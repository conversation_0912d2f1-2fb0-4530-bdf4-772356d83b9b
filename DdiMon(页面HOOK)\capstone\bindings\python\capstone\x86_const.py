# For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [x86_const.py]

# X86 registers

X86_REG_INVALID = 0
X86_REG_AH = 1
X86_REG_AL = 2
X86_REG_AX = 3
X86_REG_BH = 4
X86_REG_BL = 5
X86_REG_BP = 6
X86_REG_BPL = 7
X86_REG_BX = 8
X86_REG_CH = 9
X86_REG_CL = 10
X86_REG_CS = 11
X86_REG_CX = 12
X86_REG_DH = 13
X86_REG_DI = 14
X86_REG_DIL = 15
X86_REG_DL = 16
X86_REG_DS = 17
X86_REG_DX = 18
X86_REG_EAX = 19
X86_REG_EBP = 20
X86_REG_EBX = 21
X86_REG_ECX = 22
X86_REG_EDI = 23
X86_REG_EDX = 24
X86_REG_EFLAGS = 25
X86_REG_EIP = 26
X86_REG_EIZ = 27
X86_REG_ES = 28
X86_REG_ESI = 29
X86_REG_ESP = 30
X86_REG_FPSW = 31
X86_REG_FS = 32
X86_REG_GS = 33
X86_REG_IP = 34
X86_REG_RAX = 35
X86_REG_RBP = 36
X86_REG_RBX = 37
X86_REG_RCX = 38
X86_REG_RDI = 39
X86_REG_RDX = 40
X86_REG_RIP = 41
X86_REG_RIZ = 42
X86_REG_RSI = 43
X86_REG_RSP = 44
X86_REG_SI = 45
X86_REG_SIL = 46
X86_REG_SP = 47
X86_REG_SPL = 48
X86_REG_SS = 49
X86_REG_CR0 = 50
X86_REG_CR1 = 51
X86_REG_CR2 = 52
X86_REG_CR3 = 53
X86_REG_CR4 = 54
X86_REG_CR5 = 55
X86_REG_CR6 = 56
X86_REG_CR7 = 57
X86_REG_CR8 = 58
X86_REG_CR9 = 59
X86_REG_CR10 = 60
X86_REG_CR11 = 61
X86_REG_CR12 = 62
X86_REG_CR13 = 63
X86_REG_CR14 = 64
X86_REG_CR15 = 65
X86_REG_DR0 = 66
X86_REG_DR1 = 67
X86_REG_DR2 = 68
X86_REG_DR3 = 69
X86_REG_DR4 = 70
X86_REG_DR5 = 71
X86_REG_DR6 = 72
X86_REG_DR7 = 73
X86_REG_FP0 = 74
X86_REG_FP1 = 75
X86_REG_FP2 = 76
X86_REG_FP3 = 77
X86_REG_FP4 = 78
X86_REG_FP5 = 79
X86_REG_FP6 = 80
X86_REG_FP7 = 81
X86_REG_K0 = 82
X86_REG_K1 = 83
X86_REG_K2 = 84
X86_REG_K3 = 85
X86_REG_K4 = 86
X86_REG_K5 = 87
X86_REG_K6 = 88
X86_REG_K7 = 89
X86_REG_MM0 = 90
X86_REG_MM1 = 91
X86_REG_MM2 = 92
X86_REG_MM3 = 93
X86_REG_MM4 = 94
X86_REG_MM5 = 95
X86_REG_MM6 = 96
X86_REG_MM7 = 97
X86_REG_R8 = 98
X86_REG_R9 = 99
X86_REG_R10 = 100
X86_REG_R11 = 101
X86_REG_R12 = 102
X86_REG_R13 = 103
X86_REG_R14 = 104
X86_REG_R15 = 105
X86_REG_ST0 = 106
X86_REG_ST1 = 107
X86_REG_ST2 = 108
X86_REG_ST3 = 109
X86_REG_ST4 = 110
X86_REG_ST5 = 111
X86_REG_ST6 = 112
X86_REG_ST7 = 113
X86_REG_XMM0 = 114
X86_REG_XMM1 = 115
X86_REG_XMM2 = 116
X86_REG_XMM3 = 117
X86_REG_XMM4 = 118
X86_REG_XMM5 = 119
X86_REG_XMM6 = 120
X86_REG_XMM7 = 121
X86_REG_XMM8 = 122
X86_REG_XMM9 = 123
X86_REG_XMM10 = 124
X86_REG_XMM11 = 125
X86_REG_XMM12 = 126
X86_REG_XMM13 = 127
X86_REG_XMM14 = 128
X86_REG_XMM15 = 129
X86_REG_XMM16 = 130
X86_REG_XMM17 = 131
X86_REG_XMM18 = 132
X86_REG_XMM19 = 133
X86_REG_XMM20 = 134
X86_REG_XMM21 = 135
X86_REG_XMM22 = 136
X86_REG_XMM23 = 137
X86_REG_XMM24 = 138
X86_REG_XMM25 = 139
X86_REG_XMM26 = 140
X86_REG_XMM27 = 141
X86_REG_XMM28 = 142
X86_REG_XMM29 = 143
X86_REG_XMM30 = 144
X86_REG_XMM31 = 145
X86_REG_YMM0 = 146
X86_REG_YMM1 = 147
X86_REG_YMM2 = 148
X86_REG_YMM3 = 149
X86_REG_YMM4 = 150
X86_REG_YMM5 = 151
X86_REG_YMM6 = 152
X86_REG_YMM7 = 153
X86_REG_YMM8 = 154
X86_REG_YMM9 = 155
X86_REG_YMM10 = 156
X86_REG_YMM11 = 157
X86_REG_YMM12 = 158
X86_REG_YMM13 = 159
X86_REG_YMM14 = 160
X86_REG_YMM15 = 161
X86_REG_YMM16 = 162
X86_REG_YMM17 = 163
X86_REG_YMM18 = 164
X86_REG_YMM19 = 165
X86_REG_YMM20 = 166
X86_REG_YMM21 = 167
X86_REG_YMM22 = 168
X86_REG_YMM23 = 169
X86_REG_YMM24 = 170
X86_REG_YMM25 = 171
X86_REG_YMM26 = 172
X86_REG_YMM27 = 173
X86_REG_YMM28 = 174
X86_REG_YMM29 = 175
X86_REG_YMM30 = 176
X86_REG_YMM31 = 177
X86_REG_ZMM0 = 178
X86_REG_ZMM1 = 179
X86_REG_ZMM2 = 180
X86_REG_ZMM3 = 181
X86_REG_ZMM4 = 182
X86_REG_ZMM5 = 183
X86_REG_ZMM6 = 184
X86_REG_ZMM7 = 185
X86_REG_ZMM8 = 186
X86_REG_ZMM9 = 187
X86_REG_ZMM10 = 188
X86_REG_ZMM11 = 189
X86_REG_ZMM12 = 190
X86_REG_ZMM13 = 191
X86_REG_ZMM14 = 192
X86_REG_ZMM15 = 193
X86_REG_ZMM16 = 194
X86_REG_ZMM17 = 195
X86_REG_ZMM18 = 196
X86_REG_ZMM19 = 197
X86_REG_ZMM20 = 198
X86_REG_ZMM21 = 199
X86_REG_ZMM22 = 200
X86_REG_ZMM23 = 201
X86_REG_ZMM24 = 202
X86_REG_ZMM25 = 203
X86_REG_ZMM26 = 204
X86_REG_ZMM27 = 205
X86_REG_ZMM28 = 206
X86_REG_ZMM29 = 207
X86_REG_ZMM30 = 208
X86_REG_ZMM31 = 209
X86_REG_R8B = 210
X86_REG_R9B = 211
X86_REG_R10B = 212
X86_REG_R11B = 213
X86_REG_R12B = 214
X86_REG_R13B = 215
X86_REG_R14B = 216
X86_REG_R15B = 217
X86_REG_R8D = 218
X86_REG_R9D = 219
X86_REG_R10D = 220
X86_REG_R11D = 221
X86_REG_R12D = 222
X86_REG_R13D = 223
X86_REG_R14D = 224
X86_REG_R15D = 225
X86_REG_R8W = 226
X86_REG_R9W = 227
X86_REG_R10W = 228
X86_REG_R11W = 229
X86_REG_R12W = 230
X86_REG_R13W = 231
X86_REG_R14W = 232
X86_REG_R15W = 233
X86_REG_ENDING = 234

# Operand type for instruction's operands

X86_OP_INVALID = 0
X86_OP_REG = 1
X86_OP_IMM = 2
X86_OP_MEM = 3
X86_OP_FP = 4

# AVX broadcast type

X86_AVX_BCAST_INVALID = 0
X86_AVX_BCAST_2 = 1
X86_AVX_BCAST_4 = 2
X86_AVX_BCAST_8 = 3
X86_AVX_BCAST_16 = 4

# SSE Code Condition type

X86_SSE_CC_INVALID = 0
X86_SSE_CC_EQ = 1
X86_SSE_CC_LT = 2
X86_SSE_CC_LE = 3
X86_SSE_CC_UNORD = 4
X86_SSE_CC_NEQ = 5
X86_SSE_CC_NLT = 6
X86_SSE_CC_NLE = 7
X86_SSE_CC_ORD = 8
X86_SSE_CC_EQ_UQ = 9
X86_SSE_CC_NGE = 10
X86_SSE_CC_NGT = 11
X86_SSE_CC_FALSE = 12
X86_SSE_CC_NEQ_OQ = 13
X86_SSE_CC_GE = 14
X86_SSE_CC_GT = 15
X86_SSE_CC_TRUE = 16

# AVX Code Condition type

X86_AVX_CC_INVALID = 0
X86_AVX_CC_EQ = 1
X86_AVX_CC_LT = 2
X86_AVX_CC_LE = 3
X86_AVX_CC_UNORD = 4
X86_AVX_CC_NEQ = 5
X86_AVX_CC_NLT = 6
X86_AVX_CC_NLE = 7
X86_AVX_CC_ORD = 8
X86_AVX_CC_EQ_UQ = 9
X86_AVX_CC_NGE = 10
X86_AVX_CC_NGT = 11
X86_AVX_CC_FALSE = 12
X86_AVX_CC_NEQ_OQ = 13
X86_AVX_CC_GE = 14
X86_AVX_CC_GT = 15
X86_AVX_CC_TRUE = 16
X86_AVX_CC_EQ_OS = 17
X86_AVX_CC_LT_OQ = 18
X86_AVX_CC_LE_OQ = 19
X86_AVX_CC_UNORD_S = 20
X86_AVX_CC_NEQ_US = 21
X86_AVX_CC_NLT_UQ = 22
X86_AVX_CC_NLE_UQ = 23
X86_AVX_CC_ORD_S = 24
X86_AVX_CC_EQ_US = 25
X86_AVX_CC_NGE_UQ = 26
X86_AVX_CC_NGT_UQ = 27
X86_AVX_CC_FALSE_OS = 28
X86_AVX_CC_NEQ_OS = 29
X86_AVX_CC_GE_OQ = 30
X86_AVX_CC_GT_OQ = 31
X86_AVX_CC_TRUE_US = 32

# AVX static rounding mode type

X86_AVX_RM_INVALID = 0
X86_AVX_RM_RN = 1
X86_AVX_RM_RD = 2
X86_AVX_RM_RU = 3
X86_AVX_RM_RZ = 4

# Instruction prefixes - to be used in cs_x86.prefix[]
X86_PREFIX_LOCK = 0xf0
X86_PREFIX_REP = 0xf3
X86_PREFIX_REPNE = 0xf2
X86_PREFIX_CS = 0x2e
X86_PREFIX_SS = 0x36
X86_PREFIX_DS = 0x3e
X86_PREFIX_ES = 0x26
X86_PREFIX_FS = 0x64
X86_PREFIX_GS = 0x65
X86_PREFIX_OPSIZE = 0x66
X86_PREFIX_ADDRSIZE = 0x67

# X86 instructions

X86_INS_INVALID = 0
X86_INS_AAA = 1
X86_INS_AAD = 2
X86_INS_AAM = 3
X86_INS_AAS = 4
X86_INS_FABS = 5
X86_INS_ADC = 6
X86_INS_ADCX = 7
X86_INS_ADD = 8
X86_INS_ADDPD = 9
X86_INS_ADDPS = 10
X86_INS_ADDSD = 11
X86_INS_ADDSS = 12
X86_INS_ADDSUBPD = 13
X86_INS_ADDSUBPS = 14
X86_INS_FADD = 15
X86_INS_FIADD = 16
X86_INS_FADDP = 17
X86_INS_ADOX = 18
X86_INS_AESDECLAST = 19
X86_INS_AESDEC = 20
X86_INS_AESENCLAST = 21
X86_INS_AESENC = 22
X86_INS_AESIMC = 23
X86_INS_AESKEYGENASSIST = 24
X86_INS_AND = 25
X86_INS_ANDN = 26
X86_INS_ANDNPD = 27
X86_INS_ANDNPS = 28
X86_INS_ANDPD = 29
X86_INS_ANDPS = 30
X86_INS_ARPL = 31
X86_INS_BEXTR = 32
X86_INS_BLCFILL = 33
X86_INS_BLCI = 34
X86_INS_BLCIC = 35
X86_INS_BLCMSK = 36
X86_INS_BLCS = 37
X86_INS_BLENDPD = 38
X86_INS_BLENDPS = 39
X86_INS_BLENDVPD = 40
X86_INS_BLENDVPS = 41
X86_INS_BLSFILL = 42
X86_INS_BLSI = 43
X86_INS_BLSIC = 44
X86_INS_BLSMSK = 45
X86_INS_BLSR = 46
X86_INS_BOUND = 47
X86_INS_BSF = 48
X86_INS_BSR = 49
X86_INS_BSWAP = 50
X86_INS_BT = 51
X86_INS_BTC = 52
X86_INS_BTR = 53
X86_INS_BTS = 54
X86_INS_BZHI = 55
X86_INS_CALL = 56
X86_INS_CBW = 57
X86_INS_CDQ = 58
X86_INS_CDQE = 59
X86_INS_FCHS = 60
X86_INS_CLAC = 61
X86_INS_CLC = 62
X86_INS_CLD = 63
X86_INS_CLFLUSH = 64
X86_INS_CLGI = 65
X86_INS_CLI = 66
X86_INS_CLTS = 67
X86_INS_CMC = 68
X86_INS_CMOVA = 69
X86_INS_CMOVAE = 70
X86_INS_CMOVB = 71
X86_INS_CMOVBE = 72
X86_INS_FCMOVBE = 73
X86_INS_FCMOVB = 74
X86_INS_CMOVE = 75
X86_INS_FCMOVE = 76
X86_INS_CMOVG = 77
X86_INS_CMOVGE = 78
X86_INS_CMOVL = 79
X86_INS_CMOVLE = 80
X86_INS_FCMOVNBE = 81
X86_INS_FCMOVNB = 82
X86_INS_CMOVNE = 83
X86_INS_FCMOVNE = 84
X86_INS_CMOVNO = 85
X86_INS_CMOVNP = 86
X86_INS_FCMOVNU = 87
X86_INS_CMOVNS = 88
X86_INS_CMOVO = 89
X86_INS_CMOVP = 90
X86_INS_FCMOVU = 91
X86_INS_CMOVS = 92
X86_INS_CMP = 93
X86_INS_CMPPD = 94
X86_INS_CMPPS = 95
X86_INS_CMPSB = 96
X86_INS_CMPSD = 97
X86_INS_CMPSQ = 98
X86_INS_CMPSS = 99
X86_INS_CMPSW = 100
X86_INS_CMPXCHG16B = 101
X86_INS_CMPXCHG = 102
X86_INS_CMPXCHG8B = 103
X86_INS_COMISD = 104
X86_INS_COMISS = 105
X86_INS_FCOMP = 106
X86_INS_FCOMPI = 107
X86_INS_FCOMI = 108
X86_INS_FCOM = 109
X86_INS_FCOS = 110
X86_INS_CPUID = 111
X86_INS_CQO = 112
X86_INS_CRC32 = 113
X86_INS_CVTDQ2PD = 114
X86_INS_CVTDQ2PS = 115
X86_INS_CVTPD2DQ = 116
X86_INS_CVTPD2PS = 117
X86_INS_CVTPS2DQ = 118
X86_INS_CVTPS2PD = 119
X86_INS_CVTSD2SI = 120
X86_INS_CVTSD2SS = 121
X86_INS_CVTSI2SD = 122
X86_INS_CVTSI2SS = 123
X86_INS_CVTSS2SD = 124
X86_INS_CVTSS2SI = 125
X86_INS_CVTTPD2DQ = 126
X86_INS_CVTTPS2DQ = 127
X86_INS_CVTTSD2SI = 128
X86_INS_CVTTSS2SI = 129
X86_INS_CWD = 130
X86_INS_CWDE = 131
X86_INS_DAA = 132
X86_INS_DAS = 133
X86_INS_DATA16 = 134
X86_INS_DEC = 135
X86_INS_DIV = 136
X86_INS_DIVPD = 137
X86_INS_DIVPS = 138
X86_INS_FDIVR = 139
X86_INS_FIDIVR = 140
X86_INS_FDIVRP = 141
X86_INS_DIVSD = 142
X86_INS_DIVSS = 143
X86_INS_FDIV = 144
X86_INS_FIDIV = 145
X86_INS_FDIVP = 146
X86_INS_DPPD = 147
X86_INS_DPPS = 148
X86_INS_RET = 149
X86_INS_ENCLS = 150
X86_INS_ENCLU = 151
X86_INS_ENTER = 152
X86_INS_EXTRACTPS = 153
X86_INS_EXTRQ = 154
X86_INS_F2XM1 = 155
X86_INS_LCALL = 156
X86_INS_LJMP = 157
X86_INS_FBLD = 158
X86_INS_FBSTP = 159
X86_INS_FCOMPP = 160
X86_INS_FDECSTP = 161
X86_INS_FEMMS = 162
X86_INS_FFREE = 163
X86_INS_FICOM = 164
X86_INS_FICOMP = 165
X86_INS_FINCSTP = 166
X86_INS_FLDCW = 167
X86_INS_FLDENV = 168
X86_INS_FLDL2E = 169
X86_INS_FLDL2T = 170
X86_INS_FLDLG2 = 171
X86_INS_FLDLN2 = 172
X86_INS_FLDPI = 173
X86_INS_FNCLEX = 174
X86_INS_FNINIT = 175
X86_INS_FNOP = 176
X86_INS_FNSTCW = 177
X86_INS_FNSTSW = 178
X86_INS_FPATAN = 179
X86_INS_FPREM = 180
X86_INS_FPREM1 = 181
X86_INS_FPTAN = 182
X86_INS_FRNDINT = 183
X86_INS_FRSTOR = 184
X86_INS_FNSAVE = 185
X86_INS_FSCALE = 186
X86_INS_FSETPM = 187
X86_INS_FSINCOS = 188
X86_INS_FNSTENV = 189
X86_INS_FXAM = 190
X86_INS_FXRSTOR = 191
X86_INS_FXRSTOR64 = 192
X86_INS_FXSAVE = 193
X86_INS_FXSAVE64 = 194
X86_INS_FXTRACT = 195
X86_INS_FYL2X = 196
X86_INS_FYL2XP1 = 197
X86_INS_MOVAPD = 198
X86_INS_MOVAPS = 199
X86_INS_ORPD = 200
X86_INS_ORPS = 201
X86_INS_VMOVAPD = 202
X86_INS_VMOVAPS = 203
X86_INS_XORPD = 204
X86_INS_XORPS = 205
X86_INS_GETSEC = 206
X86_INS_HADDPD = 207
X86_INS_HADDPS = 208
X86_INS_HLT = 209
X86_INS_HSUBPD = 210
X86_INS_HSUBPS = 211
X86_INS_IDIV = 212
X86_INS_FILD = 213
X86_INS_IMUL = 214
X86_INS_IN = 215
X86_INS_INC = 216
X86_INS_INSB = 217
X86_INS_INSERTPS = 218
X86_INS_INSERTQ = 219
X86_INS_INSD = 220
X86_INS_INSW = 221
X86_INS_INT = 222
X86_INS_INT1 = 223
X86_INS_INT3 = 224
X86_INS_INTO = 225
X86_INS_INVD = 226
X86_INS_INVEPT = 227
X86_INS_INVLPG = 228
X86_INS_INVLPGA = 229
X86_INS_INVPCID = 230
X86_INS_INVVPID = 231
X86_INS_IRET = 232
X86_INS_IRETD = 233
X86_INS_IRETQ = 234
X86_INS_FISTTP = 235
X86_INS_FIST = 236
X86_INS_FISTP = 237
X86_INS_UCOMISD = 238
X86_INS_UCOMISS = 239
X86_INS_VCMP = 240
X86_INS_VCOMISD = 241
X86_INS_VCOMISS = 242
X86_INS_VCVTSD2SS = 243
X86_INS_VCVTSI2SD = 244
X86_INS_VCVTSI2SS = 245
X86_INS_VCVTSS2SD = 246
X86_INS_VCVTTSD2SI = 247
X86_INS_VCVTTSD2USI = 248
X86_INS_VCVTTSS2SI = 249
X86_INS_VCVTTSS2USI = 250
X86_INS_VCVTUSI2SD = 251
X86_INS_VCVTUSI2SS = 252
X86_INS_VUCOMISD = 253
X86_INS_VUCOMISS = 254
X86_INS_JAE = 255
X86_INS_JA = 256
X86_INS_JBE = 257
X86_INS_JB = 258
X86_INS_JCXZ = 259
X86_INS_JECXZ = 260
X86_INS_JE = 261
X86_INS_JGE = 262
X86_INS_JG = 263
X86_INS_JLE = 264
X86_INS_JL = 265
X86_INS_JMP = 266
X86_INS_JNE = 267
X86_INS_JNO = 268
X86_INS_JNP = 269
X86_INS_JNS = 270
X86_INS_JO = 271
X86_INS_JP = 272
X86_INS_JRCXZ = 273
X86_INS_JS = 274
X86_INS_KANDB = 275
X86_INS_KANDD = 276
X86_INS_KANDNB = 277
X86_INS_KANDND = 278
X86_INS_KANDNQ = 279
X86_INS_KANDNW = 280
X86_INS_KANDQ = 281
X86_INS_KANDW = 282
X86_INS_KMOVB = 283
X86_INS_KMOVD = 284
X86_INS_KMOVQ = 285
X86_INS_KMOVW = 286
X86_INS_KNOTB = 287
X86_INS_KNOTD = 288
X86_INS_KNOTQ = 289
X86_INS_KNOTW = 290
X86_INS_KORB = 291
X86_INS_KORD = 292
X86_INS_KORQ = 293
X86_INS_KORTESTW = 294
X86_INS_KORW = 295
X86_INS_KSHIFTLW = 296
X86_INS_KSHIFTRW = 297
X86_INS_KUNPCKBW = 298
X86_INS_KXNORB = 299
X86_INS_KXNORD = 300
X86_INS_KXNORQ = 301
X86_INS_KXNORW = 302
X86_INS_KXORB = 303
X86_INS_KXORD = 304
X86_INS_KXORQ = 305
X86_INS_KXORW = 306
X86_INS_LAHF = 307
X86_INS_LAR = 308
X86_INS_LDDQU = 309
X86_INS_LDMXCSR = 310
X86_INS_LDS = 311
X86_INS_FLDZ = 312
X86_INS_FLD1 = 313
X86_INS_FLD = 314
X86_INS_LEA = 315
X86_INS_LEAVE = 316
X86_INS_LES = 317
X86_INS_LFENCE = 318
X86_INS_LFS = 319
X86_INS_LGDT = 320
X86_INS_LGS = 321
X86_INS_LIDT = 322
X86_INS_LLDT = 323
X86_INS_LMSW = 324
X86_INS_OR = 325
X86_INS_SUB = 326
X86_INS_XOR = 327
X86_INS_LODSB = 328
X86_INS_LODSD = 329
X86_INS_LODSQ = 330
X86_INS_LODSW = 331
X86_INS_LOOP = 332
X86_INS_LOOPE = 333
X86_INS_LOOPNE = 334
X86_INS_RETF = 335
X86_INS_RETFQ = 336
X86_INS_LSL = 337
X86_INS_LSS = 338
X86_INS_LTR = 339
X86_INS_XADD = 340
X86_INS_LZCNT = 341
X86_INS_MASKMOVDQU = 342
X86_INS_MAXPD = 343
X86_INS_MAXPS = 344
X86_INS_MAXSD = 345
X86_INS_MAXSS = 346
X86_INS_MFENCE = 347
X86_INS_MINPD = 348
X86_INS_MINPS = 349
X86_INS_MINSD = 350
X86_INS_MINSS = 351
X86_INS_CVTPD2PI = 352
X86_INS_CVTPI2PD = 353
X86_INS_CVTPI2PS = 354
X86_INS_CVTPS2PI = 355
X86_INS_CVTTPD2PI = 356
X86_INS_CVTTPS2PI = 357
X86_INS_EMMS = 358
X86_INS_MASKMOVQ = 359
X86_INS_MOVD = 360
X86_INS_MOVDQ2Q = 361
X86_INS_MOVNTQ = 362
X86_INS_MOVQ2DQ = 363
X86_INS_MOVQ = 364
X86_INS_PABSB = 365
X86_INS_PABSD = 366
X86_INS_PABSW = 367
X86_INS_PACKSSDW = 368
X86_INS_PACKSSWB = 369
X86_INS_PACKUSWB = 370
X86_INS_PADDB = 371
X86_INS_PADDD = 372
X86_INS_PADDQ = 373
X86_INS_PADDSB = 374
X86_INS_PADDSW = 375
X86_INS_PADDUSB = 376
X86_INS_PADDUSW = 377
X86_INS_PADDW = 378
X86_INS_PALIGNR = 379
X86_INS_PANDN = 380
X86_INS_PAND = 381
X86_INS_PAVGB = 382
X86_INS_PAVGW = 383
X86_INS_PCMPEQB = 384
X86_INS_PCMPEQD = 385
X86_INS_PCMPEQW = 386
X86_INS_PCMPGTB = 387
X86_INS_PCMPGTD = 388
X86_INS_PCMPGTW = 389
X86_INS_PEXTRW = 390
X86_INS_PHADDSW = 391
X86_INS_PHADDW = 392
X86_INS_PHADDD = 393
X86_INS_PHSUBD = 394
X86_INS_PHSUBSW = 395
X86_INS_PHSUBW = 396
X86_INS_PINSRW = 397
X86_INS_PMADDUBSW = 398
X86_INS_PMADDWD = 399
X86_INS_PMAXSW = 400
X86_INS_PMAXUB = 401
X86_INS_PMINSW = 402
X86_INS_PMINUB = 403
X86_INS_PMOVMSKB = 404
X86_INS_PMULHRSW = 405
X86_INS_PMULHUW = 406
X86_INS_PMULHW = 407
X86_INS_PMULLW = 408
X86_INS_PMULUDQ = 409
X86_INS_POR = 410
X86_INS_PSADBW = 411
X86_INS_PSHUFB = 412
X86_INS_PSHUFW = 413
X86_INS_PSIGNB = 414
X86_INS_PSIGND = 415
X86_INS_PSIGNW = 416
X86_INS_PSLLD = 417
X86_INS_PSLLQ = 418
X86_INS_PSLLW = 419
X86_INS_PSRAD = 420
X86_INS_PSRAW = 421
X86_INS_PSRLD = 422
X86_INS_PSRLQ = 423
X86_INS_PSRLW = 424
X86_INS_PSUBB = 425
X86_INS_PSUBD = 426
X86_INS_PSUBQ = 427
X86_INS_PSUBSB = 428
X86_INS_PSUBSW = 429
X86_INS_PSUBUSB = 430
X86_INS_PSUBUSW = 431
X86_INS_PSUBW = 432
X86_INS_PUNPCKHBW = 433
X86_INS_PUNPCKHDQ = 434
X86_INS_PUNPCKHWD = 435
X86_INS_PUNPCKLBW = 436
X86_INS_PUNPCKLDQ = 437
X86_INS_PUNPCKLWD = 438
X86_INS_PXOR = 439
X86_INS_MONITOR = 440
X86_INS_MONTMUL = 441
X86_INS_MOV = 442
X86_INS_MOVABS = 443
X86_INS_MOVBE = 444
X86_INS_MOVDDUP = 445
X86_INS_MOVDQA = 446
X86_INS_MOVDQU = 447
X86_INS_MOVHLPS = 448
X86_INS_MOVHPD = 449
X86_INS_MOVHPS = 450
X86_INS_MOVLHPS = 451
X86_INS_MOVLPD = 452
X86_INS_MOVLPS = 453
X86_INS_MOVMSKPD = 454
X86_INS_MOVMSKPS = 455
X86_INS_MOVNTDQA = 456
X86_INS_MOVNTDQ = 457
X86_INS_MOVNTI = 458
X86_INS_MOVNTPD = 459
X86_INS_MOVNTPS = 460
X86_INS_MOVNTSD = 461
X86_INS_MOVNTSS = 462
X86_INS_MOVSB = 463
X86_INS_MOVSD = 464
X86_INS_MOVSHDUP = 465
X86_INS_MOVSLDUP = 466
X86_INS_MOVSQ = 467
X86_INS_MOVSS = 468
X86_INS_MOVSW = 469
X86_INS_MOVSX = 470
X86_INS_MOVSXD = 471
X86_INS_MOVUPD = 472
X86_INS_MOVUPS = 473
X86_INS_MOVZX = 474
X86_INS_MPSADBW = 475
X86_INS_MUL = 476
X86_INS_MULPD = 477
X86_INS_MULPS = 478
X86_INS_MULSD = 479
X86_INS_MULSS = 480
X86_INS_MULX = 481
X86_INS_FMUL = 482
X86_INS_FIMUL = 483
X86_INS_FMULP = 484
X86_INS_MWAIT = 485
X86_INS_NEG = 486
X86_INS_NOP = 487
X86_INS_NOT = 488
X86_INS_OUT = 489
X86_INS_OUTSB = 490
X86_INS_OUTSD = 491
X86_INS_OUTSW = 492
X86_INS_PACKUSDW = 493
X86_INS_PAUSE = 494
X86_INS_PAVGUSB = 495
X86_INS_PBLENDVB = 496
X86_INS_PBLENDW = 497
X86_INS_PCLMULQDQ = 498
X86_INS_PCMPEQQ = 499
X86_INS_PCMPESTRI = 500
X86_INS_PCMPESTRM = 501
X86_INS_PCMPGTQ = 502
X86_INS_PCMPISTRI = 503
X86_INS_PCMPISTRM = 504
X86_INS_PDEP = 505
X86_INS_PEXT = 506
X86_INS_PEXTRB = 507
X86_INS_PEXTRD = 508
X86_INS_PEXTRQ = 509
X86_INS_PF2ID = 510
X86_INS_PF2IW = 511
X86_INS_PFACC = 512
X86_INS_PFADD = 513
X86_INS_PFCMPEQ = 514
X86_INS_PFCMPGE = 515
X86_INS_PFCMPGT = 516
X86_INS_PFMAX = 517
X86_INS_PFMIN = 518
X86_INS_PFMUL = 519
X86_INS_PFNACC = 520
X86_INS_PFPNACC = 521
X86_INS_PFRCPIT1 = 522
X86_INS_PFRCPIT2 = 523
X86_INS_PFRCP = 524
X86_INS_PFRSQIT1 = 525
X86_INS_PFRSQRT = 526
X86_INS_PFSUBR = 527
X86_INS_PFSUB = 528
X86_INS_PHMINPOSUW = 529
X86_INS_PI2FD = 530
X86_INS_PI2FW = 531
X86_INS_PINSRB = 532
X86_INS_PINSRD = 533
X86_INS_PINSRQ = 534
X86_INS_PMAXSB = 535
X86_INS_PMAXSD = 536
X86_INS_PMAXUD = 537
X86_INS_PMAXUW = 538
X86_INS_PMINSB = 539
X86_INS_PMINSD = 540
X86_INS_PMINUD = 541
X86_INS_PMINUW = 542
X86_INS_PMOVSXBD = 543
X86_INS_PMOVSXBQ = 544
X86_INS_PMOVSXBW = 545
X86_INS_PMOVSXDQ = 546
X86_INS_PMOVSXWD = 547
X86_INS_PMOVSXWQ = 548
X86_INS_PMOVZXBD = 549
X86_INS_PMOVZXBQ = 550
X86_INS_PMOVZXBW = 551
X86_INS_PMOVZXDQ = 552
X86_INS_PMOVZXWD = 553
X86_INS_PMOVZXWQ = 554
X86_INS_PMULDQ = 555
X86_INS_PMULHRW = 556
X86_INS_PMULLD = 557
X86_INS_POP = 558
X86_INS_POPAW = 559
X86_INS_POPAL = 560
X86_INS_POPCNT = 561
X86_INS_POPF = 562
X86_INS_POPFD = 563
X86_INS_POPFQ = 564
X86_INS_PREFETCH = 565
X86_INS_PREFETCHNTA = 566
X86_INS_PREFETCHT0 = 567
X86_INS_PREFETCHT1 = 568
X86_INS_PREFETCHT2 = 569
X86_INS_PREFETCHW = 570
X86_INS_PSHUFD = 571
X86_INS_PSHUFHW = 572
X86_INS_PSHUFLW = 573
X86_INS_PSLLDQ = 574
X86_INS_PSRLDQ = 575
X86_INS_PSWAPD = 576
X86_INS_PTEST = 577
X86_INS_PUNPCKHQDQ = 578
X86_INS_PUNPCKLQDQ = 579
X86_INS_PUSH = 580
X86_INS_PUSHAW = 581
X86_INS_PUSHAL = 582
X86_INS_PUSHF = 583
X86_INS_PUSHFD = 584
X86_INS_PUSHFQ = 585
X86_INS_RCL = 586
X86_INS_RCPPS = 587
X86_INS_RCPSS = 588
X86_INS_RCR = 589
X86_INS_RDFSBASE = 590
X86_INS_RDGSBASE = 591
X86_INS_RDMSR = 592
X86_INS_RDPMC = 593
X86_INS_RDRAND = 594
X86_INS_RDSEED = 595
X86_INS_RDTSC = 596
X86_INS_RDTSCP = 597
X86_INS_ROL = 598
X86_INS_ROR = 599
X86_INS_RORX = 600
X86_INS_ROUNDPD = 601
X86_INS_ROUNDPS = 602
X86_INS_ROUNDSD = 603
X86_INS_ROUNDSS = 604
X86_INS_RSM = 605
X86_INS_RSQRTPS = 606
X86_INS_RSQRTSS = 607
X86_INS_SAHF = 608
X86_INS_SAL = 609
X86_INS_SALC = 610
X86_INS_SAR = 611
X86_INS_SARX = 612
X86_INS_SBB = 613
X86_INS_SCASB = 614
X86_INS_SCASD = 615
X86_INS_SCASQ = 616
X86_INS_SCASW = 617
X86_INS_SETAE = 618
X86_INS_SETA = 619
X86_INS_SETBE = 620
X86_INS_SETB = 621
X86_INS_SETE = 622
X86_INS_SETGE = 623
X86_INS_SETG = 624
X86_INS_SETLE = 625
X86_INS_SETL = 626
X86_INS_SETNE = 627
X86_INS_SETNO = 628
X86_INS_SETNP = 629
X86_INS_SETNS = 630
X86_INS_SETO = 631
X86_INS_SETP = 632
X86_INS_SETS = 633
X86_INS_SFENCE = 634
X86_INS_SGDT = 635
X86_INS_SHA1MSG1 = 636
X86_INS_SHA1MSG2 = 637
X86_INS_SHA1NEXTE = 638
X86_INS_SHA1RNDS4 = 639
X86_INS_SHA256MSG1 = 640
X86_INS_SHA256MSG2 = 641
X86_INS_SHA256RNDS2 = 642
X86_INS_SHL = 643
X86_INS_SHLD = 644
X86_INS_SHLX = 645
X86_INS_SHR = 646
X86_INS_SHRD = 647
X86_INS_SHRX = 648
X86_INS_SHUFPD = 649
X86_INS_SHUFPS = 650
X86_INS_SIDT = 651
X86_INS_FSIN = 652
X86_INS_SKINIT = 653
X86_INS_SLDT = 654
X86_INS_SMSW = 655
X86_INS_SQRTPD = 656
X86_INS_SQRTPS = 657
X86_INS_SQRTSD = 658
X86_INS_SQRTSS = 659
X86_INS_FSQRT = 660
X86_INS_STAC = 661
X86_INS_STC = 662
X86_INS_STD = 663
X86_INS_STGI = 664
X86_INS_STI = 665
X86_INS_STMXCSR = 666
X86_INS_STOSB = 667
X86_INS_STOSD = 668
X86_INS_STOSQ = 669
X86_INS_STOSW = 670
X86_INS_STR = 671
X86_INS_FST = 672
X86_INS_FSTP = 673
X86_INS_FSTPNCE = 674
X86_INS_SUBPD = 675
X86_INS_SUBPS = 676
X86_INS_FSUBR = 677
X86_INS_FISUBR = 678
X86_INS_FSUBRP = 679
X86_INS_SUBSD = 680
X86_INS_SUBSS = 681
X86_INS_FSUB = 682
X86_INS_FISUB = 683
X86_INS_FSUBP = 684
X86_INS_SWAPGS = 685
X86_INS_SYSCALL = 686
X86_INS_SYSENTER = 687
X86_INS_SYSEXIT = 688
X86_INS_SYSRET = 689
X86_INS_T1MSKC = 690
X86_INS_TEST = 691
X86_INS_UD2 = 692
X86_INS_FTST = 693
X86_INS_TZCNT = 694
X86_INS_TZMSK = 695
X86_INS_FUCOMPI = 696
X86_INS_FUCOMI = 697
X86_INS_FUCOMPP = 698
X86_INS_FUCOMP = 699
X86_INS_FUCOM = 700
X86_INS_UD2B = 701
X86_INS_UNPCKHPD = 702
X86_INS_UNPCKHPS = 703
X86_INS_UNPCKLPD = 704
X86_INS_UNPCKLPS = 705
X86_INS_VADDPD = 706
X86_INS_VADDPS = 707
X86_INS_VADDSD = 708
X86_INS_VADDSS = 709
X86_INS_VADDSUBPD = 710
X86_INS_VADDSUBPS = 711
X86_INS_VAESDECLAST = 712
X86_INS_VAESDEC = 713
X86_INS_VAESENCLAST = 714
X86_INS_VAESENC = 715
X86_INS_VAESIMC = 716
X86_INS_VAESKEYGENASSIST = 717
X86_INS_VALIGND = 718
X86_INS_VALIGNQ = 719
X86_INS_VANDNPD = 720
X86_INS_VANDNPS = 721
X86_INS_VANDPD = 722
X86_INS_VANDPS = 723
X86_INS_VBLENDMPD = 724
X86_INS_VBLENDMPS = 725
X86_INS_VBLENDPD = 726
X86_INS_VBLENDPS = 727
X86_INS_VBLENDVPD = 728
X86_INS_VBLENDVPS = 729
X86_INS_VBROADCASTF128 = 730
X86_INS_VBROADCASTI128 = 731
X86_INS_VBROADCASTI32X4 = 732
X86_INS_VBROADCASTI64X4 = 733
X86_INS_VBROADCASTSD = 734
X86_INS_VBROADCASTSS = 735
X86_INS_VCMPPD = 736
X86_INS_VCMPPS = 737
X86_INS_VCMPSD = 738
X86_INS_VCMPSS = 739
X86_INS_VCVTDQ2PD = 740
X86_INS_VCVTDQ2PS = 741
X86_INS_VCVTPD2DQX = 742
X86_INS_VCVTPD2DQ = 743
X86_INS_VCVTPD2PSX = 744
X86_INS_VCVTPD2PS = 745
X86_INS_VCVTPD2UDQ = 746
X86_INS_VCVTPH2PS = 747
X86_INS_VCVTPS2DQ = 748
X86_INS_VCVTPS2PD = 749
X86_INS_VCVTPS2PH = 750
X86_INS_VCVTPS2UDQ = 751
X86_INS_VCVTSD2SI = 752
X86_INS_VCVTSD2USI = 753
X86_INS_VCVTSS2SI = 754
X86_INS_VCVTSS2USI = 755
X86_INS_VCVTTPD2DQX = 756
X86_INS_VCVTTPD2DQ = 757
X86_INS_VCVTTPD2UDQ = 758
X86_INS_VCVTTPS2DQ = 759
X86_INS_VCVTTPS2UDQ = 760
X86_INS_VCVTUDQ2PD = 761
X86_INS_VCVTUDQ2PS = 762
X86_INS_VDIVPD = 763
X86_INS_VDIVPS = 764
X86_INS_VDIVSD = 765
X86_INS_VDIVSS = 766
X86_INS_VDPPD = 767
X86_INS_VDPPS = 768
X86_INS_VERR = 769
X86_INS_VERW = 770
X86_INS_VEXTRACTF128 = 771
X86_INS_VEXTRACTF32X4 = 772
X86_INS_VEXTRACTF64X4 = 773
X86_INS_VEXTRACTI128 = 774
X86_INS_VEXTRACTI32X4 = 775
X86_INS_VEXTRACTI64X4 = 776
X86_INS_VEXTRACTPS = 777
X86_INS_VFMADD132PD = 778
X86_INS_VFMADD132PS = 779
X86_INS_VFMADD213PD = 780
X86_INS_VFMADD213PS = 781
X86_INS_VFMADDPD = 782
X86_INS_VFMADD231PD = 783
X86_INS_VFMADDPS = 784
X86_INS_VFMADD231PS = 785
X86_INS_VFMADDSD = 786
X86_INS_VFMADD213SD = 787
X86_INS_VFMADD132SD = 788
X86_INS_VFMADD231SD = 789
X86_INS_VFMADDSS = 790
X86_INS_VFMADD213SS = 791
X86_INS_VFMADD132SS = 792
X86_INS_VFMADD231SS = 793
X86_INS_VFMADDSUB132PD = 794
X86_INS_VFMADDSUB132PS = 795
X86_INS_VFMADDSUB213PD = 796
X86_INS_VFMADDSUB213PS = 797
X86_INS_VFMADDSUBPD = 798
X86_INS_VFMADDSUB231PD = 799
X86_INS_VFMADDSUBPS = 800
X86_INS_VFMADDSUB231PS = 801
X86_INS_VFMSUB132PD = 802
X86_INS_VFMSUB132PS = 803
X86_INS_VFMSUB213PD = 804
X86_INS_VFMSUB213PS = 805
X86_INS_VFMSUBADD132PD = 806
X86_INS_VFMSUBADD132PS = 807
X86_INS_VFMSUBADD213PD = 808
X86_INS_VFMSUBADD213PS = 809
X86_INS_VFMSUBADDPD = 810
X86_INS_VFMSUBADD231PD = 811
X86_INS_VFMSUBADDPS = 812
X86_INS_VFMSUBADD231PS = 813
X86_INS_VFMSUBPD = 814
X86_INS_VFMSUB231PD = 815
X86_INS_VFMSUBPS = 816
X86_INS_VFMSUB231PS = 817
X86_INS_VFMSUBSD = 818
X86_INS_VFMSUB213SD = 819
X86_INS_VFMSUB132SD = 820
X86_INS_VFMSUB231SD = 821
X86_INS_VFMSUBSS = 822
X86_INS_VFMSUB213SS = 823
X86_INS_VFMSUB132SS = 824
X86_INS_VFMSUB231SS = 825
X86_INS_VFNMADD132PD = 826
X86_INS_VFNMADD132PS = 827
X86_INS_VFNMADD213PD = 828
X86_INS_VFNMADD213PS = 829
X86_INS_VFNMADDPD = 830
X86_INS_VFNMADD231PD = 831
X86_INS_VFNMADDPS = 832
X86_INS_VFNMADD231PS = 833
X86_INS_VFNMADDSD = 834
X86_INS_VFNMADD213SD = 835
X86_INS_VFNMADD132SD = 836
X86_INS_VFNMADD231SD = 837
X86_INS_VFNMADDSS = 838
X86_INS_VFNMADD213SS = 839
X86_INS_VFNMADD132SS = 840
X86_INS_VFNMADD231SS = 841
X86_INS_VFNMSUB132PD = 842
X86_INS_VFNMSUB132PS = 843
X86_INS_VFNMSUB213PD = 844
X86_INS_VFNMSUB213PS = 845
X86_INS_VFNMSUBPD = 846
X86_INS_VFNMSUB231PD = 847
X86_INS_VFNMSUBPS = 848
X86_INS_VFNMSUB231PS = 849
X86_INS_VFNMSUBSD = 850
X86_INS_VFNMSUB213SD = 851
X86_INS_VFNMSUB132SD = 852
X86_INS_VFNMSUB231SD = 853
X86_INS_VFNMSUBSS = 854
X86_INS_VFNMSUB213SS = 855
X86_INS_VFNMSUB132SS = 856
X86_INS_VFNMSUB231SS = 857
X86_INS_VFRCZPD = 858
X86_INS_VFRCZPS = 859
X86_INS_VFRCZSD = 860
X86_INS_VFRCZSS = 861
X86_INS_VORPD = 862
X86_INS_VORPS = 863
X86_INS_VXORPD = 864
X86_INS_VXORPS = 865
X86_INS_VGATHERDPD = 866
X86_INS_VGATHERDPS = 867
X86_INS_VGATHERPF0DPD = 868
X86_INS_VGATHERPF0DPS = 869
X86_INS_VGATHERPF0QPD = 870
X86_INS_VGATHERPF0QPS = 871
X86_INS_VGATHERPF1DPD = 872
X86_INS_VGATHERPF1DPS = 873
X86_INS_VGATHERPF1QPD = 874
X86_INS_VGATHERPF1QPS = 875
X86_INS_VGATHERQPD = 876
X86_INS_VGATHERQPS = 877
X86_INS_VHADDPD = 878
X86_INS_VHADDPS = 879
X86_INS_VHSUBPD = 880
X86_INS_VHSUBPS = 881
X86_INS_VINSERTF128 = 882
X86_INS_VINSERTF32X4 = 883
X86_INS_VINSERTF64X4 = 884
X86_INS_VINSERTI128 = 885
X86_INS_VINSERTI32X4 = 886
X86_INS_VINSERTI64X4 = 887
X86_INS_VINSERTPS = 888
X86_INS_VLDDQU = 889
X86_INS_VLDMXCSR = 890
X86_INS_VMASKMOVDQU = 891
X86_INS_VMASKMOVPD = 892
X86_INS_VMASKMOVPS = 893
X86_INS_VMAXPD = 894
X86_INS_VMAXPS = 895
X86_INS_VMAXSD = 896
X86_INS_VMAXSS = 897
X86_INS_VMCALL = 898
X86_INS_VMCLEAR = 899
X86_INS_VMFUNC = 900
X86_INS_VMINPD = 901
X86_INS_VMINPS = 902
X86_INS_VMINSD = 903
X86_INS_VMINSS = 904
X86_INS_VMLAUNCH = 905
X86_INS_VMLOAD = 906
X86_INS_VMMCALL = 907
X86_INS_VMOVQ = 908
X86_INS_VMOVDDUP = 909
X86_INS_VMOVD = 910
X86_INS_VMOVDQA32 = 911
X86_INS_VMOVDQA64 = 912
X86_INS_VMOVDQA = 913
X86_INS_VMOVDQU16 = 914
X86_INS_VMOVDQU32 = 915
X86_INS_VMOVDQU64 = 916
X86_INS_VMOVDQU8 = 917
X86_INS_VMOVDQU = 918
X86_INS_VMOVHLPS = 919
X86_INS_VMOVHPD = 920
X86_INS_VMOVHPS = 921
X86_INS_VMOVLHPS = 922
X86_INS_VMOVLPD = 923
X86_INS_VMOVLPS = 924
X86_INS_VMOVMSKPD = 925
X86_INS_VMOVMSKPS = 926
X86_INS_VMOVNTDQA = 927
X86_INS_VMOVNTDQ = 928
X86_INS_VMOVNTPD = 929
X86_INS_VMOVNTPS = 930
X86_INS_VMOVSD = 931
X86_INS_VMOVSHDUP = 932
X86_INS_VMOVSLDUP = 933
X86_INS_VMOVSS = 934
X86_INS_VMOVUPD = 935
X86_INS_VMOVUPS = 936
X86_INS_VMPSADBW = 937
X86_INS_VMPTRLD = 938
X86_INS_VMPTRST = 939
X86_INS_VMREAD = 940
X86_INS_VMRESUME = 941
X86_INS_VMRUN = 942
X86_INS_VMSAVE = 943
X86_INS_VMULPD = 944
X86_INS_VMULPS = 945
X86_INS_VMULSD = 946
X86_INS_VMULSS = 947
X86_INS_VMWRITE = 948
X86_INS_VMXOFF = 949
X86_INS_VMXON = 950
X86_INS_VPABSB = 951
X86_INS_VPABSD = 952
X86_INS_VPABSQ = 953
X86_INS_VPABSW = 954
X86_INS_VPACKSSDW = 955
X86_INS_VPACKSSWB = 956
X86_INS_VPACKUSDW = 957
X86_INS_VPACKUSWB = 958
X86_INS_VPADDB = 959
X86_INS_VPADDD = 960
X86_INS_VPADDQ = 961
X86_INS_VPADDSB = 962
X86_INS_VPADDSW = 963
X86_INS_VPADDUSB = 964
X86_INS_VPADDUSW = 965
X86_INS_VPADDW = 966
X86_INS_VPALIGNR = 967
X86_INS_VPANDD = 968
X86_INS_VPANDND = 969
X86_INS_VPANDNQ = 970
X86_INS_VPANDN = 971
X86_INS_VPANDQ = 972
X86_INS_VPAND = 973
X86_INS_VPAVGB = 974
X86_INS_VPAVGW = 975
X86_INS_VPBLENDD = 976
X86_INS_VPBLENDMD = 977
X86_INS_VPBLENDMQ = 978
X86_INS_VPBLENDVB = 979
X86_INS_VPBLENDW = 980
X86_INS_VPBROADCASTB = 981
X86_INS_VPBROADCASTD = 982
X86_INS_VPBROADCASTMB2Q = 983
X86_INS_VPBROADCASTMW2D = 984
X86_INS_VPBROADCASTQ = 985
X86_INS_VPBROADCASTW = 986
X86_INS_VPCLMULQDQ = 987
X86_INS_VPCMOV = 988
X86_INS_VPCMP = 989
X86_INS_VPCMPD = 990
X86_INS_VPCMPEQB = 991
X86_INS_VPCMPEQD = 992
X86_INS_VPCMPEQQ = 993
X86_INS_VPCMPEQW = 994
X86_INS_VPCMPESTRI = 995
X86_INS_VPCMPESTRM = 996
X86_INS_VPCMPGTB = 997
X86_INS_VPCMPGTD = 998
X86_INS_VPCMPGTQ = 999
X86_INS_VPCMPGTW = 1000
X86_INS_VPCMPISTRI = 1001
X86_INS_VPCMPISTRM = 1002
X86_INS_VPCMPQ = 1003
X86_INS_VPCMPUD = 1004
X86_INS_VPCMPUQ = 1005
X86_INS_VPCOMB = 1006
X86_INS_VPCOMD = 1007
X86_INS_VPCOMQ = 1008
X86_INS_VPCOMUB = 1009
X86_INS_VPCOMUD = 1010
X86_INS_VPCOMUQ = 1011
X86_INS_VPCOMUW = 1012
X86_INS_VPCOMW = 1013
X86_INS_VPCONFLICTD = 1014
X86_INS_VPCONFLICTQ = 1015
X86_INS_VPERM2F128 = 1016
X86_INS_VPERM2I128 = 1017
X86_INS_VPERMD = 1018
X86_INS_VPERMI2D = 1019
X86_INS_VPERMI2PD = 1020
X86_INS_VPERMI2PS = 1021
X86_INS_VPERMI2Q = 1022
X86_INS_VPERMIL2PD = 1023
X86_INS_VPERMIL2PS = 1024
X86_INS_VPERMILPD = 1025
X86_INS_VPERMILPS = 1026
X86_INS_VPERMPD = 1027
X86_INS_VPERMPS = 1028
X86_INS_VPERMQ = 1029
X86_INS_VPERMT2D = 1030
X86_INS_VPERMT2PD = 1031
X86_INS_VPERMT2PS = 1032
X86_INS_VPERMT2Q = 1033
X86_INS_VPEXTRB = 1034
X86_INS_VPEXTRD = 1035
X86_INS_VPEXTRQ = 1036
X86_INS_VPEXTRW = 1037
X86_INS_VPGATHERDD = 1038
X86_INS_VPGATHERDQ = 1039
X86_INS_VPGATHERQD = 1040
X86_INS_VPGATHERQQ = 1041
X86_INS_VPHADDBD = 1042
X86_INS_VPHADDBQ = 1043
X86_INS_VPHADDBW = 1044
X86_INS_VPHADDDQ = 1045
X86_INS_VPHADDD = 1046
X86_INS_VPHADDSW = 1047
X86_INS_VPHADDUBD = 1048
X86_INS_VPHADDUBQ = 1049
X86_INS_VPHADDUBW = 1050
X86_INS_VPHADDUDQ = 1051
X86_INS_VPHADDUWD = 1052
X86_INS_VPHADDUWQ = 1053
X86_INS_VPHADDWD = 1054
X86_INS_VPHADDWQ = 1055
X86_INS_VPHADDW = 1056
X86_INS_VPHMINPOSUW = 1057
X86_INS_VPHSUBBW = 1058
X86_INS_VPHSUBDQ = 1059
X86_INS_VPHSUBD = 1060
X86_INS_VPHSUBSW = 1061
X86_INS_VPHSUBWD = 1062
X86_INS_VPHSUBW = 1063
X86_INS_VPINSRB = 1064
X86_INS_VPINSRD = 1065
X86_INS_VPINSRQ = 1066
X86_INS_VPINSRW = 1067
X86_INS_VPLZCNTD = 1068
X86_INS_VPLZCNTQ = 1069
X86_INS_VPMACSDD = 1070
X86_INS_VPMACSDQH = 1071
X86_INS_VPMACSDQL = 1072
X86_INS_VPMACSSDD = 1073
X86_INS_VPMACSSDQH = 1074
X86_INS_VPMACSSDQL = 1075
X86_INS_VPMACSSWD = 1076
X86_INS_VPMACSSWW = 1077
X86_INS_VPMACSWD = 1078
X86_INS_VPMACSWW = 1079
X86_INS_VPMADCSSWD = 1080
X86_INS_VPMADCSWD = 1081
X86_INS_VPMADDUBSW = 1082
X86_INS_VPMADDWD = 1083
X86_INS_VPMASKMOVD = 1084
X86_INS_VPMASKMOVQ = 1085
X86_INS_VPMAXSB = 1086
X86_INS_VPMAXSD = 1087
X86_INS_VPMAXSQ = 1088
X86_INS_VPMAXSW = 1089
X86_INS_VPMAXUB = 1090
X86_INS_VPMAXUD = 1091
X86_INS_VPMAXUQ = 1092
X86_INS_VPMAXUW = 1093
X86_INS_VPMINSB = 1094
X86_INS_VPMINSD = 1095
X86_INS_VPMINSQ = 1096
X86_INS_VPMINSW = 1097
X86_INS_VPMINUB = 1098
X86_INS_VPMINUD = 1099
X86_INS_VPMINUQ = 1100
X86_INS_VPMINUW = 1101
X86_INS_VPMOVDB = 1102
X86_INS_VPMOVDW = 1103
X86_INS_VPMOVMSKB = 1104
X86_INS_VPMOVQB = 1105
X86_INS_VPMOVQD = 1106
X86_INS_VPMOVQW = 1107
X86_INS_VPMOVSDB = 1108
X86_INS_VPMOVSDW = 1109
X86_INS_VPMOVSQB = 1110
X86_INS_VPMOVSQD = 1111
X86_INS_VPMOVSQW = 1112
X86_INS_VPMOVSXBD = 1113
X86_INS_VPMOVSXBQ = 1114
X86_INS_VPMOVSXBW = 1115
X86_INS_VPMOVSXDQ = 1116
X86_INS_VPMOVSXWD = 1117
X86_INS_VPMOVSXWQ = 1118
X86_INS_VPMOVUSDB = 1119
X86_INS_VPMOVUSDW = 1120
X86_INS_VPMOVUSQB = 1121
X86_INS_VPMOVUSQD = 1122
X86_INS_VPMOVUSQW = 1123
X86_INS_VPMOVZXBD = 1124
X86_INS_VPMOVZXBQ = 1125
X86_INS_VPMOVZXBW = 1126
X86_INS_VPMOVZXDQ = 1127
X86_INS_VPMOVZXWD = 1128
X86_INS_VPMOVZXWQ = 1129
X86_INS_VPMULDQ = 1130
X86_INS_VPMULHRSW = 1131
X86_INS_VPMULHUW = 1132
X86_INS_VPMULHW = 1133
X86_INS_VPMULLD = 1134
X86_INS_VPMULLW = 1135
X86_INS_VPMULUDQ = 1136
X86_INS_VPORD = 1137
X86_INS_VPORQ = 1138
X86_INS_VPOR = 1139
X86_INS_VPPERM = 1140
X86_INS_VPROTB = 1141
X86_INS_VPROTD = 1142
X86_INS_VPROTQ = 1143
X86_INS_VPROTW = 1144
X86_INS_VPSADBW = 1145
X86_INS_VPSCATTERDD = 1146
X86_INS_VPSCATTERDQ = 1147
X86_INS_VPSCATTERQD = 1148
X86_INS_VPSCATTERQQ = 1149
X86_INS_VPSHAB = 1150
X86_INS_VPSHAD = 1151
X86_INS_VPSHAQ = 1152
X86_INS_VPSHAW = 1153
X86_INS_VPSHLB = 1154
X86_INS_VPSHLD = 1155
X86_INS_VPSHLQ = 1156
X86_INS_VPSHLW = 1157
X86_INS_VPSHUFB = 1158
X86_INS_VPSHUFD = 1159
X86_INS_VPSHUFHW = 1160
X86_INS_VPSHUFLW = 1161
X86_INS_VPSIGNB = 1162
X86_INS_VPSIGND = 1163
X86_INS_VPSIGNW = 1164
X86_INS_VPSLLDQ = 1165
X86_INS_VPSLLD = 1166
X86_INS_VPSLLQ = 1167
X86_INS_VPSLLVD = 1168
X86_INS_VPSLLVQ = 1169
X86_INS_VPSLLW = 1170
X86_INS_VPSRAD = 1171
X86_INS_VPSRAQ = 1172
X86_INS_VPSRAVD = 1173
X86_INS_VPSRAVQ = 1174
X86_INS_VPSRAW = 1175
X86_INS_VPSRLDQ = 1176
X86_INS_VPSRLD = 1177
X86_INS_VPSRLQ = 1178
X86_INS_VPSRLVD = 1179
X86_INS_VPSRLVQ = 1180
X86_INS_VPSRLW = 1181
X86_INS_VPSUBB = 1182
X86_INS_VPSUBD = 1183
X86_INS_VPSUBQ = 1184
X86_INS_VPSUBSB = 1185
X86_INS_VPSUBSW = 1186
X86_INS_VPSUBUSB = 1187
X86_INS_VPSUBUSW = 1188
X86_INS_VPSUBW = 1189
X86_INS_VPTESTMD = 1190
X86_INS_VPTESTMQ = 1191
X86_INS_VPTESTNMD = 1192
X86_INS_VPTESTNMQ = 1193
X86_INS_VPTEST = 1194
X86_INS_VPUNPCKHBW = 1195
X86_INS_VPUNPCKHDQ = 1196
X86_INS_VPUNPCKHQDQ = 1197
X86_INS_VPUNPCKHWD = 1198
X86_INS_VPUNPCKLBW = 1199
X86_INS_VPUNPCKLDQ = 1200
X86_INS_VPUNPCKLQDQ = 1201
X86_INS_VPUNPCKLWD = 1202
X86_INS_VPXORD = 1203
X86_INS_VPXORQ = 1204
X86_INS_VPXOR = 1205
X86_INS_VRCP14PD = 1206
X86_INS_VRCP14PS = 1207
X86_INS_VRCP14SD = 1208
X86_INS_VRCP14SS = 1209
X86_INS_VRCP28PD = 1210
X86_INS_VRCP28PS = 1211
X86_INS_VRCP28SD = 1212
X86_INS_VRCP28SS = 1213
X86_INS_VRCPPS = 1214
X86_INS_VRCPSS = 1215
X86_INS_VRNDSCALEPD = 1216
X86_INS_VRNDSCALEPS = 1217
X86_INS_VRNDSCALESD = 1218
X86_INS_VRNDSCALESS = 1219
X86_INS_VROUNDPD = 1220
X86_INS_VROUNDPS = 1221
X86_INS_VROUNDSD = 1222
X86_INS_VROUNDSS = 1223
X86_INS_VRSQRT14PD = 1224
X86_INS_VRSQRT14PS = 1225
X86_INS_VRSQRT14SD = 1226
X86_INS_VRSQRT14SS = 1227
X86_INS_VRSQRT28PD = 1228
X86_INS_VRSQRT28PS = 1229
X86_INS_VRSQRT28SD = 1230
X86_INS_VRSQRT28SS = 1231
X86_INS_VRSQRTPS = 1232
X86_INS_VRSQRTSS = 1233
X86_INS_VSCATTERDPD = 1234
X86_INS_VSCATTERDPS = 1235
X86_INS_VSCATTERPF0DPD = 1236
X86_INS_VSCATTERPF0DPS = 1237
X86_INS_VSCATTERPF0QPD = 1238
X86_INS_VSCATTERPF0QPS = 1239
X86_INS_VSCATTERPF1DPD = 1240
X86_INS_VSCATTERPF1DPS = 1241
X86_INS_VSCATTERPF1QPD = 1242
X86_INS_VSCATTERPF1QPS = 1243
X86_INS_VSCATTERQPD = 1244
X86_INS_VSCATTERQPS = 1245
X86_INS_VSHUFPD = 1246
X86_INS_VSHUFPS = 1247
X86_INS_VSQRTPD = 1248
X86_INS_VSQRTPS = 1249
X86_INS_VSQRTSD = 1250
X86_INS_VSQRTSS = 1251
X86_INS_VSTMXCSR = 1252
X86_INS_VSUBPD = 1253
X86_INS_VSUBPS = 1254
X86_INS_VSUBSD = 1255
X86_INS_VSUBSS = 1256
X86_INS_VTESTPD = 1257
X86_INS_VTESTPS = 1258
X86_INS_VUNPCKHPD = 1259
X86_INS_VUNPCKHPS = 1260
X86_INS_VUNPCKLPD = 1261
X86_INS_VUNPCKLPS = 1262
X86_INS_VZEROALL = 1263
X86_INS_VZEROUPPER = 1264
X86_INS_WAIT = 1265
X86_INS_WBINVD = 1266
X86_INS_WRFSBASE = 1267
X86_INS_WRGSBASE = 1268
X86_INS_WRMSR = 1269
X86_INS_XABORT = 1270
X86_INS_XACQUIRE = 1271
X86_INS_XBEGIN = 1272
X86_INS_XCHG = 1273
X86_INS_FXCH = 1274
X86_INS_XCRYPTCBC = 1275
X86_INS_XCRYPTCFB = 1276
X86_INS_XCRYPTCTR = 1277
X86_INS_XCRYPTECB = 1278
X86_INS_XCRYPTOFB = 1279
X86_INS_XEND = 1280
X86_INS_XGETBV = 1281
X86_INS_XLATB = 1282
X86_INS_XRELEASE = 1283
X86_INS_XRSTOR = 1284
X86_INS_XRSTOR64 = 1285
X86_INS_XSAVE = 1286
X86_INS_XSAVE64 = 1287
X86_INS_XSAVEOPT = 1288
X86_INS_XSAVEOPT64 = 1289
X86_INS_XSETBV = 1290
X86_INS_XSHA1 = 1291
X86_INS_XSHA256 = 1292
X86_INS_XSTORE = 1293
X86_INS_XTEST = 1294
X86_INS_ENDING = 1295

# Group of X86 instructions

X86_GRP_INVALID = 0

# Generic groups
X86_GRP_JUMP = 1
X86_GRP_CALL = 2
X86_GRP_RET = 3
X86_GRP_INT = 4
X86_GRP_IRET = 5

# Architecture-specific groups
X86_GRP_VM = 128
X86_GRP_3DNOW = 129
X86_GRP_AES = 130
X86_GRP_ADX = 131
X86_GRP_AVX = 132
X86_GRP_AVX2 = 133
X86_GRP_AVX512 = 134
X86_GRP_BMI = 135
X86_GRP_BMI2 = 136
X86_GRP_CMOV = 137
X86_GRP_F16C = 138
X86_GRP_FMA = 139
X86_GRP_FMA4 = 140
X86_GRP_FSGSBASE = 141
X86_GRP_HLE = 142
X86_GRP_MMX = 143
X86_GRP_MODE32 = 144
X86_GRP_MODE64 = 145
X86_GRP_RTM = 146
X86_GRP_SHA = 147
X86_GRP_SSE1 = 148
X86_GRP_SSE2 = 149
X86_GRP_SSE3 = 150
X86_GRP_SSE41 = 151
X86_GRP_SSE42 = 152
X86_GRP_SSE4A = 153
X86_GRP_SSSE3 = 154
X86_GRP_PCLMUL = 155
X86_GRP_XOP = 156
X86_GRP_CDI = 157
X86_GRP_ERI = 158
X86_GRP_TBM = 159
X86_GRP_16BITMODE = 160
X86_GRP_NOT64BITMODE = 161
X86_GRP_SGX = 162
X86_GRP_DQI = 163
X86_GRP_BWI = 164
X86_GRP_PFI = 165
X86_GRP_VLX = 166
X86_GRP_SMAP = 167
X86_GRP_NOVLX = 168
X86_GRP_ENDING = 169
