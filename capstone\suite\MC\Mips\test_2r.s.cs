# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_BIG_ENDIAN, None
0x7b,0x00,0x4f,0x9e = fill.b $w30, $9
0x7b,0x01,0xbf,0xde = fill.h $w31, $23
0x7b,0x02,0xc4,0x1e = fill.w $w16, $24
0x7b,0x08,0x05,0x5e = nloc.b $w21, $w0
0x7b,0x09,0xfc,0x9e = nloc.h $w18, $w31
0x7b,0x0a,0xb8,0x9e = nloc.w $w2, $w23
0x7b,0x0b,0x51,0x1e = nloc.d $w4, $w10
0x7b,0x0c,0x17,0xde = nlzc.b $w31, $w2
0x7b,0x0d,0xb6,0xde = nlzc.h $w27, $w22
0x7b,0x0e,0xea,0x9e = nlzc.w $w10, $w29
0x7b,0x0f,0x4e,0x5e = nlzc.d $w25, $w9
0x7b,0x04,0x95,0x1e = pcnt.b $w20, $w18
0x7b,0x05,0x40,0x1e = pcnt.h $w0, $w8
0x7b,0x06,0x4d,0xde = pcnt.w $w23, $w9
0x7b,0x07,0xc5,0x5e = pcnt.d $w21, $w24
