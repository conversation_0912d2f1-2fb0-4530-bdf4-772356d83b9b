# CS_ARCH_ARM64, 0, None
0x20,0x90,0x81,0x5f = fmul s0, s1, v1.s[0]
0x7e,0x91,0xa1,0x5f = fmul s30, s11, v1.s[1]
0xa4,0x98,0x87,0x5f = fmul s4, s5, v7.s[2]
0xd0,0x9a,0xb0,0x5f = fmul s16, s22, v16.s[3]
0x20,0x90,0xc1,0x5f = fmul d0, d1, v1.d[0]
0x7e,0x99,0xc1,0x5f = fmul d30, d11, v1.d[1]
0x46,0x90,0x88,0x7f = fmulx s6, s2, v8.s[0]
0x67,0x90,0xad,0x7f = fmulx s7, s3, v13.s[1]
0xe9,0x98,0x89,0x7f = fmulx s9, s7, v9.s[2]
0xad,0x9a,0xaa,0x7f = fmulx s13, s21, v10.s[3]
0x2f,0x91,0xc7,0x7f = fmulx d15, d9, v7.d[0]
0x8d,0x99,0xcb,0x7f = fmulx d13, d12, v11.d[1]
