﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.27130.2026
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "HyperPlatform", "HyperPlatform\HyperPlatform.vcxproj", "{4C048BB2-7E8D-43BF-B29D-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{9D6567E8-2030-4700-805F-D44CB292A667}"
	ProjectSection(SolutionItems) = preProject
		.clang-format = .clang-format
		.editorconfig = .editorconfig
		.gitattributes = .gitattributes
		.gitignore = .gitignore
		clean.bat = clean.bat
		.github\ISSUE_TEMPLATE.md = .github\ISSUE_TEMPLATE.md
		LICENSE = LICENSE
		README.md = README.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4C048BB2-7E8D-43BF-B29D-************}.Debug|x64.ActiveCfg = Debug|x64
		{4C048BB2-7E8D-43BF-B29D-************}.Debug|x64.Build.0 = Debug|x64
		{4C048BB2-7E8D-43BF-B29D-************}.Debug|x64.Deploy.0 = Debug|x64
		{4C048BB2-7E8D-43BF-B29D-************}.Debug|x86.ActiveCfg = Debug|Win32
		{4C048BB2-7E8D-43BF-B29D-************}.Debug|x86.Build.0 = Debug|Win32
		{4C048BB2-7E8D-43BF-B29D-************}.Debug|x86.Deploy.0 = Debug|Win32
		{4C048BB2-7E8D-43BF-B29D-************}.Release|x64.ActiveCfg = Release|x64
		{4C048BB2-7E8D-43BF-B29D-************}.Release|x64.Build.0 = Release|x64
		{4C048BB2-7E8D-43BF-B29D-************}.Release|x64.Deploy.0 = Release|x64
		{4C048BB2-7E8D-43BF-B29D-************}.Release|x86.ActiveCfg = Release|Win32
		{4C048BB2-7E8D-43BF-B29D-************}.Release|x86.Build.0 = Release|Win32
		{4C048BB2-7E8D-43BF-B29D-************}.Release|x86.Deploy.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {69DC2926-4C4F-4BFF-A8E8-F98B1ED1888D}
	EndGlobalSection
EndGlobal
