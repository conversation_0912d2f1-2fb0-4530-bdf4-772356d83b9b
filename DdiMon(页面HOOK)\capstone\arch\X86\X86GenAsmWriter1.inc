/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    11119U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    11112U,	// BUNDLE
    11184U,	// LIFETIME_START
    11099U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    11199U,	// AAA
    20256U,	// AAD8i8
    22647U,	// AAM8i8
    11911U,	// AAS
    11919U,	// ABS_F
    0U,	// ABS_Fp32
    0U,	// ABS_Fp64
    0U,	// ABS_Fp80
    10814U,	// ACQUIRE_MOV16rm
    10814U,	// ACQUIRE_MOV32rm
    10814U,	// ACQUIRE_MOV64rm
    10814U,	// ACQUIRE_MOV8rm
    26287U,	// ADC16i16
    1085152U,	// ADC16mi
    1085152U,	// ADC16mi8
    1085152U,	// ADC16mr
    34655968U,	// ADC16ri
    34655968U,	// ADC16ri8
    68210400U,	// ADC16rm
    34655968U,	// ADC16rr
    34623200U,	// ADC16rr_REV
    26423U,	// ADC32i32
    1117920U,	// ADC32mi
    1117920U,	// ADC32mi8
    1117920U,	// ADC32mr
    34655968U,	// ADC32ri
    34655968U,	// ADC32ri8
    101764832U,	// ADC32rm
    34655968U,	// ADC32rr
    34623200U,	// ADC32rr_REV
    26571U,	// ADC64i32
    1134304U,	// ADC64mi32
    1134304U,	// ADC64mi8
    1134304U,	// ADC64mr
    34655968U,	// ADC64ri32
    34655968U,	// ADC64ri8
    135319264U,	// ADC64rm
    34655968U,	// ADC64rr
    34623200U,	// ADC64rr_REV
    26185U,	// ADC8i8
    1150688U,	// ADC8mi
    1150688U,	// ADC8mr
    34655968U,	// ADC8ri
    168873696U,	// ADC8rm
    34655968U,	// ADC8rr
    34623200U,	// ADC8rr_REV
    101737738U,	// ADCX32rm
    34628874U,	// ADCX32rr
    135292170U,	// ADCX64rm
    34628874U,	// ADCX64rr
    26296U,	// ADD16i16
    1085307U,	// ADD16mi
    1085307U,	// ADD16mi8
    1085307U,	// ADD16mr
    34656123U,	// ADD16ri
    34656123U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    68210555U,	// ADD16rm
    34656123U,	// ADD16rr
    0U,	// ADD16rr_DB
    34623355U,	// ADD16rr_REV
    26433U,	// ADD32i32
    1118075U,	// ADD32mi
    1118075U,	// ADD32mi8
    1118075U,	// ADD32mr
    34656123U,	// ADD32ri
    34656123U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    101764987U,	// ADD32rm
    34656123U,	// ADD32rr
    0U,	// ADD32rr_DB
    34623355U,	// ADD32rr_REV
    26581U,	// ADD64i32
    1134459U,	// ADD64mi32
    1134459U,	// ADD64mi8
    1134459U,	// ADD64mr
    34656123U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    34656123U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    135319419U,	// ADD64rm
    34656123U,	// ADD64rr
    0U,	// ADD64rr_DB
    34623355U,	// ADD64rr_REV
    26194U,	// ADD8i8
    1150843U,	// ADD8mi
    1150843U,	// ADD8mr
    34656123U,	// ADD8ri
    34656123U,	// ADD8ri8
    168873851U,	// ADD8rm
    34656123U,	// ADD8rr
    34623355U,	// ADD8rr_REV
    202396135U,	// ADDPDrm
    34623975U,	// ADDPDrr
    202399362U,	// ADDPSrm
    34627202U,	// ADDPSrr
    235951174U,	// ADDSDrm
    235951174U,	// ADDSDrm_Int
    34624582U,	// ADDSDrr
    34624582U,	// ADDSDrr_Int
    269508832U,	// ADDSSrm
    269508832U,	// ADDSSrm_Int
    34627808U,	// ADDSSrr
    34627808U,	// ADDSSrr_Int
    202396070U,	// ADDSUBPDrm
    34623910U,	// ADDSUBPDrr
    202399297U,	// ADDSUBPSrm
    34627137U,	// ADDSUBPSrr
    118650U,	// ADD_F32m
    135034U,	// ADD_F64m
    36736U,	// ADD_FI16m
    69504U,	// ADD_FI32m
    22792U,	// ADD_FPrST0
    20346U,	// ADD_FST0r
    0U,	// ADD_Fp32
    0U,	// ADD_Fp32m
    0U,	// ADD_Fp64
    0U,	// ADD_Fp64m
    0U,	// ADD_Fp64m32
    0U,	// ADD_Fp80
    0U,	// ADD_Fp80m32
    0U,	// ADD_Fp80m64
    0U,	// ADD_FpI16m32
    0U,	// ADD_FpI16m64
    0U,	// ADD_FpI16m80
    0U,	// ADD_FpI32m32
    0U,	// ADD_FpI32m64
    0U,	// ADD_FpI32m80
    2117498U,	// ADD_FrST0
    11138U,	// ADJCALLSTACKDOWN32
    11138U,	// ADJCALLSTACKDOWN64
    11156U,	// ADJCALLSTACKUP32
    11156U,	// ADJCALLSTACKUP64
    303064348U,	// ADOX32rm
    336618780U,	// ADOX32rr
    370173212U,	// ADOX64rm
    336618780U,	// ADOX64rr
    403726862U,	// AESDECLASTrm
    34628110U,	// AESDECLASTrr
    403721958U,	// AESDECrm
    34623206U,	// AESDECrr
    403726875U,	// AESENCLASTrm
    34628123U,	// AESENCLASTrr
    403721998U,	// AESENCrm
    34623246U,	// AESENCrr
    437276421U,	// AESIMCrm
    336613125U,	// AESIMCrr
    2584764987U,	// AESKEYGENASSIST128rm
    2484101691U,	// AESKEYGENASSIST128rr
    26305U,	// AND16i16
    1085511U,	// AND16mi
    1085511U,	// AND16mi8
    1085511U,	// AND16mr
    34656327U,	// AND16ri
    34656327U,	// AND16ri8
    68210759U,	// AND16rm
    34656327U,	// AND16rr
    34623559U,	// AND16rr_REV
    26443U,	// AND32i32
    1118279U,	// AND32mi
    1118279U,	// AND32mi8
    1118279U,	// AND32mr
    34656327U,	// AND32ri
    34656327U,	// AND32ri8
    101765191U,	// AND32rm
    34656327U,	// AND32rr
    34623559U,	// AND32rr_REV
    26591U,	// AND64i32
    1134663U,	// AND64mi32
    1134663U,	// AND64mi8
    1134663U,	// AND64mr
    34656327U,	// AND64ri32
    34656327U,	// AND64ri8
    135319623U,	// AND64rm
    34656327U,	// AND64rr
    34623559U,	// AND64rr_REV
    26203U,	// AND8i8
    1151047U,	// AND8mi
    1151047U,	// AND8mr
    34656327U,	// AND8ri
    34656327U,	// AND8ri8
    168874055U,	// AND8rm
    34656327U,	// AND8rr
    34623559U,	// AND8rr_REV
    2484099250U,	// ANDN32rm
    2484099250U,	// ANDN32rr
    2484099250U,	// ANDN64rm
    2484099250U,	// ANDN64rr
    202396317U,	// ANDNPDrm
    34624157U,	// ANDNPDrr
    202399556U,	// ANDNPSrm
    34627396U,	// ANDNPSrr
    202396181U,	// ANDPDrm
    34624021U,	// ANDPDrr
    202399408U,	// ANDPSrm
    34627248U,	// ANDPSrr
    1087546U,	// ARPL16mr
    336615482U,	// ARPL16rr
    0U,	// AVX2_SETALLONES
    0U,	// AVX512_512_SET0
    0U,	// AVX_SET0
    2450545836U,	// BEXTR32rm
    2484100268U,	// BEXTR32rr
    2517654700U,	// BEXTR64rm
    2484100268U,	// BEXTR64rr
    2450545836U,	// BEXTRI32mi
    2484100268U,	// BEXTRI32ri
    2517654700U,	// BEXTRI64mi
    2484100268U,	// BEXTRI64ri
    303061027U,	// BLCFILL32rm
    336615459U,	// BLCFILL32rr
    370169891U,	// BLCFILL64rm
    336615459U,	// BLCFILL64rr
    303060808U,	// BLCI32rm
    336615240U,	// BLCI32rr
    370169672U,	// BLCI64rm
    336615240U,	// BLCI64rr
    303058670U,	// BLCIC32rm
    336613102U,	// BLCIC32rr
    370167534U,	// BLCIC64rm
    336613102U,	// BLCIC64rr
    303060978U,	// BLCMSK32rm
    336615410U,	// BLCMSK32rr
    370169842U,	// BLCMSK64rm
    336615410U,	// BLCMSK64rr
    303062218U,	// BLCS32rm
    336616650U,	// BLCS32rr
    370171082U,	// BLCS64rm
    336616650U,	// BLCS64rr
    2349879837U,	// BLENDPDrmi
    2182107677U,	// BLENDPDrri
    2349883064U,	// BLENDPSrmi
    2182110904U,	// BLENDPSrri
    202396413U,	// BLENDVPDrm0
    34624253U,	// BLENDVPDrr0
    202399693U,	// BLENDVPSrm0
    34627533U,	// BLENDVPSrr0
    303061036U,	// BLSFILL32rm
    336615468U,	// BLSFILL32rr
    370169900U,	// BLSFILL64rm
    336615468U,	// BLSFILL64rr
    303060964U,	// BLSI32rm
    336615396U,	// BLSI32rr
    370169828U,	// BLSI64rm
    336615396U,	// BLSI64rr
    303058677U,	// BLSIC32rm
    336613109U,	// BLSIC32rr
    370167541U,	// BLSIC64rm
    336613109U,	// BLSIC64rr
    303060986U,	// BLSMSK32rm
    336615418U,	// BLSMSK32rr
    370169850U,	// BLSMSK64rm
    336615418U,	// BLSMSK64rr
    303062167U,	// BLSR32rm
    336616599U,	// BLSR32rr
    370171031U,	// BLSR64rm
    336616599U,	// BLSR64rr
    303059037U,	// BOUNDS16rm
    370167901U,	// BOUNDS32rm
    470832853U,	// BSF16rm
    336615125U,	// BSF16rr
    303060693U,	// BSF32rm
    336615125U,	// BSF32rr
    370169557U,	// BSF64rm
    336615125U,	// BSF64rr
    470834302U,	// BSR16rm
    336616574U,	// BSR16rr
    303062142U,	// BSR32rm
    336616574U,	// BSR32rr
    370171006U,	// BSR64rm
    336616574U,	// BSR64rr
    22771U,	// BSWAP32r
    22771U,	// BSWAP64r
    1089940U,	// BT16mi8
    1089940U,	// BT16mr
    336617876U,	// BT16ri8
    336617876U,	// BT16rr
    1122708U,	// BT32mi8
    1122708U,	// BT32mr
    336617876U,	// BT32ri8
    336617876U,	// BT32rr
    1139092U,	// BT64mi8
    1139092U,	// BT64mr
    336617876U,	// BT64ri8
    336617876U,	// BT64rr
    1085211U,	// BTC16mi8
    1085211U,	// BTC16mr
    336613147U,	// BTC16ri8
    336613147U,	// BTC16rr
    1117979U,	// BTC32mi8
    1117979U,	// BTC32mr
    336613147U,	// BTC32ri8
    336613147U,	// BTC32rr
    1134363U,	// BTC64mi8
    1134363U,	// BTC64mr
    336613147U,	// BTC64ri8
    336613147U,	// BTC64rr
    1088669U,	// BTR16mi8
    1088669U,	// BTR16mr
    336616605U,	// BTR16ri8
    336616605U,	// BTR16rr
    1121437U,	// BTR32mi8
    1121437U,	// BTR32mr
    336616605U,	// BTR32ri8
    336616605U,	// BTR32rr
    1137821U,	// BTR64mi8
    1137821U,	// BTR64mr
    336616605U,	// BTR64ri8
    336616605U,	// BTR64rr
    1089922U,	// BTS16mi8
    1089922U,	// BTS16mr
    336617858U,	// BTS16ri8
    336617858U,	// BTS16rr
    1122690U,	// BTS32mi8
    1122690U,	// BTS32mr
    336617858U,	// BTS32ri8
    336617858U,	// BTS32rr
    1139074U,	// BTS64mi8
    1139074U,	// BTS64mr
    336617858U,	// BTS64ri8
    336617858U,	// BTS64rr
    2450544462U,	// BZHI32rm
    2484098894U,	// BZHI32rr
    2517653326U,	// BZHI64rm
    2484098894U,	// BZHI64rr
    38941U,	// CALL16m
    22557U,	// CALL16r
    71709U,	// CALL32m
    22557U,	// CALL32r
    88093U,	// CALL64m
    153629U,	// CALL64pcrel32
    22557U,	// CALL64r
    153629U,	// CALLpcrel16
    153629U,	// CALLpcrel32
    12204U,	// CBW
    11812U,	// CDQ
    11455U,	// CDQE
    11999U,	// CHS_F
    0U,	// CHS_Fp32
    0U,	// CHS_Fp64
    0U,	// CHS_Fp80
    11268U,	// CLAC
    11300U,	// CLC
    11350U,	// CLD
    104249U,	// CLFLUSH
    11570U,	// CLGI
    11580U,	// CLI
    12089U,	// CLTS
    11304U,	// CMC
    68177251U,	// CMOVA16rm
    34622819U,	// CMOVA16rr
    101731683U,	// CMOVA32rm
    34622819U,	// CMOVA32rr
    135286115U,	// CMOVA64rm
    34622819U,	// CMOVA64rr
    68179448U,	// CMOVAE16rm
    34625016U,	// CMOVAE16rr
    101733880U,	// CMOVAE32rm
    34625016U,	// CMOVAE32rr
    135288312U,	// CMOVAE64rm
    34625016U,	// CMOVAE64rr
    68177579U,	// CMOVB16rm
    34623147U,	// CMOVB16rr
    101732011U,	// CMOVB32rm
    34623147U,	// CMOVB32rr
    135286443U,	// CMOVB64rm
    34623147U,	// CMOVB64rr
    68179468U,	// CMOVBE16rm
    34625036U,	// CMOVBE16rr
    101733900U,	// CMOVBE32rm
    34625036U,	// CMOVBE32rr
    135288332U,	// CMOVBE64rm
    34625036U,	// CMOVBE64rr
    26027U,	// CMOVBE_F
    0U,	// CMOVBE_Fp32
    0U,	// CMOVBE_Fp64
    0U,	// CMOVBE_Fp80
    25995U,	// CMOVB_F
    0U,	// CMOVB_Fp32
    0U,	// CMOVB_Fp64
    0U,	// CMOVB_Fp80
    68179662U,	// CMOVE16rm
    34625230U,	// CMOVE16rr
    101734094U,	// CMOVE32rm
    34625230U,	// CMOVE32rr
    135288526U,	// CMOVE64rm
    34625230U,	// CMOVE64rr
    26059U,	// CMOVE_F
    0U,	// CMOVE_Fp32
    0U,	// CMOVE_Fp64
    0U,	// CMOVE_Fp80
    68179712U,	// CMOVG16rm
    34625280U,	// CMOVG16rr
    101734144U,	// CMOVG32rm
    34625280U,	// CMOVG32rr
    135288576U,	// CMOVG64rm
    34625280U,	// CMOVG64rr
    68179513U,	// CMOVGE16rm
    34625081U,	// CMOVGE16rr
    101733945U,	// CMOVGE32rm
    34625081U,	// CMOVGE32rr
    135288377U,	// CMOVGE64rm
    34625081U,	// CMOVGE64rr
    68180080U,	// CMOVL16rm
    34625648U,	// CMOVL16rr
    101734512U,	// CMOVL32rm
    34625648U,	// CMOVL32rr
    135288944U,	// CMOVL64rm
    34625648U,	// CMOVL64rr
    68179537U,	// CMOVLE16rm
    34625105U,	// CMOVLE16rr
    101733969U,	// CMOVLE32rm
    34625105U,	// CMOVLE32rr
    135288401U,	// CMOVLE64rm
    34625105U,	// CMOVLE64rr
    26010U,	// CMOVNBE_F
    0U,	// CMOVNBE_Fp32
    0U,	// CMOVNBE_Fp64
    0U,	// CMOVNBE_Fp80
    25979U,	// CMOVNB_F
    0U,	// CMOVNB_Fp32
    0U,	// CMOVNB_Fp64
    0U,	// CMOVNB_Fp80
    68179565U,	// CMOVNE16rm
    34625133U,	// CMOVNE16rr
    101733997U,	// CMOVNE32rm
    34625133U,	// CMOVNE32rr
    135288429U,	// CMOVNE64rm
    34625133U,	// CMOVNE64rr
    26043U,	// CMOVNE_F
    0U,	// CMOVNE_Fp32
    0U,	// CMOVNE_Fp64
    0U,	// CMOVNE_Fp80
    68180190U,	// CMOVNO16rm
    34625758U,	// CMOVNO16rr
    101734622U,	// CMOVNO32rm
    34625758U,	// CMOVNO32rr
    135289054U,	// CMOVNO64rm
    34625758U,	// CMOVNO64rr
    68180302U,	// CMOVNP16rm
    34625870U,	// CMOVNP16rr
    101734734U,	// CMOVNP32rm
    34625870U,	// CMOVNP32rr
    135289166U,	// CMOVNP64rm
    34625870U,	// CMOVNP64rr
    26114U,	// CMOVNP_F
    0U,	// CMOVNP_Fp32
    0U,	// CMOVNP_Fp64
    0U,	// CMOVNP_Fp80
    68181236U,	// CMOVNS16rm
    34626804U,	// CMOVNS16rr
    101735668U,	// CMOVNS32rm
    34626804U,	// CMOVNS32rr
    135290100U,	// CMOVNS64rm
    34626804U,	// CMOVNS64rr
    68180204U,	// CMOVO16rm
    34625772U,	// CMOVO16rr
    101734636U,	// CMOVO32rm
    34625772U,	// CMOVO32rr
    135289068U,	// CMOVO64rm
    34625772U,	// CMOVO64rr
    68180431U,	// CMOVP16rm
    34625999U,	// CMOVP16rr
    101734863U,	// CMOVP32rm
    34625999U,	// CMOVP32rr
    135289295U,	// CMOVP64rm
    34625999U,	// CMOVP64rr
    26130U,	// CMOVP_F
    0U,	// CMOVP_Fp32
    0U,	// CMOVP_Fp64
    0U,	// CMOVP_Fp80
    68182413U,	// CMOVS16rm
    34627981U,	// CMOVS16rr
    101736845U,	// CMOVS32rm
    34627981U,	// CMOVS32rr
    135291277U,	// CMOVS64rm
    34627981U,	// CMOVS64rr
    10618U,	// CMOV_FR32
    10777U,	// CMOV_FR64
    10497U,	// CMOV_GR16
    10477U,	// CMOV_GR32
    10796U,	// CMOV_GR8
    10598U,	// CMOV_RFP32
    10757U,	// CMOV_RFP64
    10517U,	// CMOV_RFP80
    10557U,	// CMOV_V16F32
    10637U,	// CMOV_V2F64
    10697U,	// CMOV_V2I64
    10537U,	// CMOV_V4F32
    10657U,	// CMOV_V4F64
    10717U,	// CMOV_V4I64
    10578U,	// CMOV_V8F32
    10677U,	// CMOV_V8F64
    10737U,	// CMOV_V8I64
    26332U,	// CMP16i16
    1087776U,	// CMP16mi
    1087776U,	// CMP16mi8
    1087776U,	// CMP16mr
    336615712U,	// CMP16ri
    336615712U,	// CMP16ri8
    470833440U,	// CMP16rm
    336615712U,	// CMP16rr
    336615712U,	// CMP16rr_REV
    26497U,	// CMP32i32
    1120544U,	// CMP32mi
    1120544U,	// CMP32mi8
    1120544U,	// CMP32mr
    336615712U,	// CMP32ri
    336615712U,	// CMP32ri8
    303061280U,	// CMP32rm
    336615712U,	// CMP32rr
    336615712U,	// CMP32rr_REV
    26612U,	// CMP64i32
    1136928U,	// CMP64mi32
    1136928U,	// CMP64mi8
    1136928U,	// CMP64mr
    336615712U,	// CMP64ri32
    336615712U,	// CMP64ri8
    370170144U,	// CMP64rm
    336615712U,	// CMP64rr
    336615712U,	// CMP64rr_REV
    26220U,	// CMP8i8
    1153312U,	// CMP8mi
    1153312U,	// CMP8mr
    336615712U,	// CMP8ri
    504387872U,	// CMP8rm
    336615712U,	// CMP8rr
    336615712U,	// CMP8rr_REV
    204647927U,	// CMPPDrmi
    2349879989U,	// CMPPDrmi_alt
    36892151U,	// CMPPDrri
    2182107829U,	// CMPPDrri_alt
    205696503U,	// CMPPSrmi
    2349883236U,	// CMPPSrmi_alt
    37940727U,	// CMPPSrri
    2182111076U,	// CMPPSrri_alt
    200221U,	// CMPSB
    240299511U,	// CMPSDrm
    2383434910U,	// CMPSDrm_alt
    38989303U,	// CMPSDrr
    2182108318U,	// CMPSDrr_alt
    218270U,	// CMPSL
    236406U,	// CMPSQ
    274902519U,	// CMPSSrm
    2416992560U,	// CMPSSrm_alt
    40037879U,	// CMPSSrr
    2182111536U,	// CMPSSrr_alt
    255070U,	// CMPSW
    265578U,	// CMPXCHG16B
    1087205U,	// CMPXCHG16rm
    336615141U,	// CMPXCHG16rr
    1119973U,	// CMPXCHG32rm
    336615141U,	// CMPXCHG32rr
    1136357U,	// CMPXCHG64rm
    336615141U,	// CMPXCHG64rr
    85366U,	// CMPXCHG8B
    1152741U,	// CMPXCHG8rm
    336615141U,	// CMPXCHG8rr
    537941100U,	// COMISDrm
    336614508U,	// COMISDrr
    537944326U,	// COMISSrm
    336617734U,	// COMISSrr
    22827U,	// COMP_FST0r
    22413U,	// COM_FIPr
    22356U,	// COM_FIr
    22652U,	// COM_FST0r
    12061U,	// COS_F
    0U,	// COS_Fp32
    0U,	// COS_Fp64
    0U,	// COS_Fp80
    11344U,	// CPUID32
    11344U,	// CPUID64
    11745U,	// CQO
    68209583U,	// CRC32r32m16
    101764015U,	// CRC32r32m32
    168872879U,	// CRC32r32m8
    34655151U,	// CRC32r32r16
    34655151U,	// CRC32r32r32
    34655151U,	// CRC32r32r8
    135318447U,	// CRC32r64m64
    168872879U,	// CRC32r64m8
    34655151U,	// CRC32r64r64
    34655151U,	// CRC32r64r8
    370168103U,	// CVTDQ2PDrm
    336613671U,	// CVTDQ2PDrr
    437280213U,	// CVTDQ2PSrm
    336616917U,	// CVTDQ2PSrr
    537942567U,	// CVTPD2DQrm
    336615975U,	// CVTPD2DQrr
    537943465U,	// CVTPD2PSrm
    336616873U,	// CVTPD2PSrr
    537942599U,	// CVTPS2DQrm
    336616007U,	// CVTPS2DQrr
    571494706U,	// CVTPS2PDrm
    336613682U,	// CVTPS2PDrr
    571496387U,	// CVTSD2SI64rm
    336615363U,	// CVTSD2SI64rr
    571496387U,	// CVTSD2SIrm
    336615363U,	// CVTSD2SIrr
    571498601U,	// CVTSD2SSrm
    336617577U,	// CVTSD2SSrr
    370168775U,	// CVTSI2SD64rm
    336614343U,	// CVTSI2SD64rr
    303059911U,	// CVTSI2SDrm
    336614343U,	// CVTSI2SDrr
    370172020U,	// CVTSI2SS64rm
    336617588U,	// CVTSI2SS64rr
    303063156U,	// CVTSI2SSrm
    336617588U,	// CVTSI2SSrr
    605049822U,	// CVTSS2SDrm
    336614366U,	// CVTSS2SDrr
    605050842U,	// CVTSS2SI64rm
    336615386U,	// CVTSS2SI64rr
    605050842U,	// CVTSS2SIrm
    336615386U,	// CVTSS2SIrr
    537942555U,	// CVTTPD2DQrm
    336615963U,	// CVTTPD2DQrr
    537942587U,	// CVTTPS2DQrm
    336615995U,	// CVTTPS2DQrr
    571496375U,	// CVTTSD2SI64rm
    336615351U,	// CVTTSD2SI64rr
    571496375U,	// CVTTSD2SIrm
    336615351U,	// CVTTSD2SIrr
    605050830U,	// CVTTSS2SI64rm
    336615374U,	// CVTTSS2SI64rr
    605050830U,	// CVTTSS2SIrm
    336615374U,	// CVTTSS2SIrr
    11396U,	// CWD
    11428U,	// CWDE
    11203U,	// DAA
    11915U,	// DAS
    11084U,	// DATA16_PREFIX
    36585U,	// DEC16m
    20201U,	// DEC16r
    20201U,	// DEC32_16r
    20201U,	// DEC32_32r
    69353U,	// DEC32m
    20201U,	// DEC32r
    36585U,	// DEC64_16m
    20201U,	// DEC64_16r
    69353U,	// DEC64_32m
    20201U,	// DEC64_32r
    85737U,	// DEC64m
    20201U,	// DEC64r
    102121U,	// DEC8m
    20201U,	// DEC8r
    41599U,	// DIV16m
    25215U,	// DIV16r
    74367U,	// DIV32m
    25215U,	// DIV32r
    90751U,	// DIV64m
    25215U,	// DIV64r
    107135U,	// DIV8m
    25215U,	// DIV8r
    202396424U,	// DIVPDrm
    34624264U,	// DIVPDrr
    202399704U,	// DIVPSrm
    34627544U,	// DIVPSrr
    122035U,	// DIVR_F32m
    138419U,	// DIVR_F64m
    40122U,	// DIVR_FI16m
    72890U,	// DIVR_FI32m
    22894U,	// DIVR_FPrST0
    23731U,	// DIVR_FST0r
    0U,	// DIVR_Fp32m
    0U,	// DIVR_Fp64m
    0U,	// DIVR_Fp64m32
    0U,	// DIVR_Fp80m32
    0U,	// DIVR_Fp80m64
    0U,	// DIVR_FpI16m32
    0U,	// DIVR_FpI16m64
    0U,	// DIVR_FpI16m80
    0U,	// DIVR_FpI32m32
    0U,	// DIVR_FpI32m64
    0U,	// DIVR_FpI32m80
    2120883U,	// DIVR_FrST0
    235951302U,	// DIVSDrm
    235951302U,	// DIVSDrm_Int
    34624710U,	// DIVSDrr
    34624710U,	// DIVSDrr_Int
    269508962U,	// DIVSSrm
    269508962U,	// DIVSSrm_Int
    34627938U,	// DIVSSrr
    34627938U,	// DIVSSrr_Int
    123518U,	// DIV_F32m
    139902U,	// DIV_F64m
    41604U,	// DIV_FI16m
    74372U,	// DIV_FI32m
    22984U,	// DIV_FPrST0
    25214U,	// DIV_FST0r
    0U,	// DIV_Fp32
    0U,	// DIV_Fp32m
    0U,	// DIV_Fp64
    0U,	// DIV_Fp64m
    0U,	// DIV_Fp64m32
    0U,	// DIV_Fp80
    0U,	// DIV_Fp80m32
    0U,	// DIV_Fp80m64
    0U,	// DIV_FpI16m32
    0U,	// DIV_FpI16m64
    0U,	// DIV_FpI16m80
    0U,	// DIV_FpI32m32
    0U,	// DIV_FpI32m64
    0U,	// DIV_FpI32m80
    2122366U,	// DIV_FrST0
    2349879982U,	// DPPDrmi
    2182107822U,	// DPPDrri
    2349883229U,	// DPPSrmi
    2182111069U,	// DPPSrri
    26724U,	// EH_RETURN
    26724U,	// EH_RETURN64
    10893U,	// EH_SjLj_LongJmp32
    10997U,	// EH_SjLj_LongJmp64
    10912U,	// EH_SjLj_SetJmp32
    11016U,	// EH_SjLj_SetJmp64
    154040U,	// EH_SjLj_Setup
    12049U,	// ENCLS
    12171U,	// ENCLU
    336616511U,	// ENTER
    2148654983U,	// EXTRACTPSmr
    2484100999U,	// EXTRACTPSrr
    34659176U,	// EXTRQ
    2182142824U,	// EXTRQI
    10874U,	// F2XM1
    7395356U,	// FARCALL16i
    284700U,	// FARCALL16m
    7395356U,	// FARCALL32i
    284700U,	// FARCALL32m
    284700U,	// FARCALL64
    7395621U,	// FARJMP16i
    284965U,	// FARJMP16m
    7395621U,	// FARJMP32i
    284965U,	// FARJMP32m
    284965U,	// FARJMP64
    118778U,	// FBLDm
    121212U,	// FBSTPm
    120956U,	// FCOM32m
    137340U,	// FCOM64m
    121131U,	// FCOMP32m
    137515U,	// FCOMP64m
    11781U,	// FCOMPP
    11796U,	// FDECSTP
    12055U,	// FEMMS
    22045U,	// FFREE
    39042U,	// FICOM16m
    71810U,	// FICOM32m
    39218U,	// FICOMP16m
    71986U,	// FICOMP32m
    11804U,	// FINCSTP
    41765U,	// FLDCW16m
    123531U,	// FLDENVm
    11400U,	// FLDL2E
    12094U,	// FLDL2T
    10978U,	// FLDLG2
    10985U,	// FLDLN2
    11584U,	// FLDPI
    12407U,	// FNCLEX
    12127U,	// FNINIT
    11776U,	// FNOP
    41772U,	// FNSTCW16m
    12234U,	// FNSTSW16r
    124016U,	// FNSTSWm
    0U,	// FP32_TO_INT16_IN_MEM
    0U,	// FP32_TO_INT32_IN_MEM
    0U,	// FP32_TO_INT64_IN_MEM
    0U,	// FP64_TO_INT16_IN_MEM
    0U,	// FP64_TO_INT32_IN_MEM
    0U,	// FP64_TO_INT64_IN_MEM
    0U,	// FP80_TO_INT16_IN_MEM
    0U,	// FP80_TO_INT32_IN_MEM
    0U,	// FP80_TO_INT64_IN_MEM
    11700U,	// FPATAN
    11683U,	// FPREM
    10867U,	// FPREM1
    11707U,	// FPTAN
    12146U,	// FRNDINT
    121952U,	// FRSTORm
    120510U,	// FSAVEm
    11433U,	// FSCALE
    11689U,	// FSETPM
    12066U,	// FSINCOS
    123539U,	// FSTENVm
    11678U,	// FXAM
    285800U,	// FXRSTOR
    281615U,	// FXRSTOR64
    284358U,	// FXSAVE
    281605U,	// FXSAVE64
    12101U,	// FXTRACT
    12228U,	// FYL2X
    10880U,	// FYL2XP1
    202396317U,	// FsANDNPDrm
    34624157U,	// FsANDNPDrr
    202399556U,	// FsANDNPSrm
    34627396U,	// FsANDNPSrr
    202396181U,	// FsANDPDrm
    34624021U,	// FsANDPDrr
    202399408U,	// FsANDPSrm
    34627248U,	// FsANDPSrr
    0U,	// FsFLD0SD
    0U,	// FsFLD0SS
    537940371U,	// FsMOVAPDrm
    537943606U,	// FsMOVAPSrm
    202396361U,	// FsORPDrm
    34624201U,	// FsORPDrr
    202399608U,	// FsORPSrm
    34627448U,	// FsORPSrr
    537940370U,	// FsVMOVAPDrm
    537943605U,	// FsVMOVAPSrm
    202396368U,	// FsXORPDrm
    34624208U,	// FsXORPDrr
    202399615U,	// FsXORPSrm
    34627455U,	// FsXORPSrr
    11288U,	// GETSEC
    202396143U,	// HADDPDrm
    34623983U,	// HADDPDrr
    202399370U,	// HADDPSrm
    34627210U,	// HADDPSrr
    12142U,	// HLT
    202396092U,	// HSUBPDrm
    34623932U,	// HSUBPDrr
    202399319U,	// HSUBPSrm
    34627159U,	// HSUBPSrr
    41605U,	// IDIV16m
    25221U,	// IDIV16r
    74373U,	// IDIV32m
    25221U,	// IDIV32r
    90757U,	// IDIV64m
    25221U,	// IDIV64r
    107141U,	// IDIV8m
    25221U,	// IDIV8r
    36877U,	// ILD_F16m
    69645U,	// ILD_F32m
    86029U,	// ILD_F64m
    0U,	// ILD_Fp16m32
    0U,	// ILD_Fp16m64
    0U,	// ILD_Fp16m80
    0U,	// ILD_Fp32m32
    0U,	// ILD_Fp32m64
    0U,	// ILD_Fp32m80
    0U,	// ILD_Fp64m32
    0U,	// ILD_Fp64m64
    0U,	// ILD_Fp64m80
    39018U,	// IMUL16m
    22634U,	// IMUL16r
    68180074U,	// IMUL16rm
    2618316906U,	// IMUL16rmi
    2618316906U,	// IMUL16rmi8
    34625642U,	// IMUL16rr
    2484099178U,	// IMUL16rri
    2484099178U,	// IMUL16rri8
    71786U,	// IMUL32m
    22634U,	// IMUL32r
    101734506U,	// IMUL32rm
    2450544746U,	// IMUL32rmi
    2450544746U,	// IMUL32rmi8
    34625642U,	// IMUL32rr
    2484099178U,	// IMUL32rri
    2484099178U,	// IMUL32rri8
    88170U,	// IMUL64m
    22634U,	// IMUL64r
    135288938U,	// IMUL64rm
    2517653610U,	// IMUL64rmi32
    2517653610U,	// IMUL64rmi8
    34625642U,	// IMUL64rr
    2484099178U,	// IMUL64rri32
    2484099178U,	// IMUL64rri8
    104554U,	// IMUL8m
    22634U,	// IMUL8r
    26324U,	// IN16ri
    12386U,	// IN16rr
    26488U,	// IN32ri
    12396U,	// IN32rr
    26212U,	// IN8ri
    12376U,	// IN8rr
    36630U,	// INC16m
    20246U,	// INC16r
    20246U,	// INC32_16r
    20246U,	// INC32_32r
    69398U,	// INC32m
    20246U,	// INC32r
    36630U,	// INC64_16m
    20246U,	// INC64_16r
    69398U,	// INC64_32m
    20246U,	// INC64_32r
    85782U,	// INC64m
    20246U,	// INC64r
    102166U,	// INC8m
    20246U,	// INC8r
    8687120U,	// INSB
    2416992157U,	// INSERTPSrm
    2182111133U,	// INSERTPSrr
    34659230U,	// INSERTQ
    2182142878U,	// INSERTQI
    8705160U,	// INSL
    8725585U,	// INSW
    25056U,	// INT
    10888U,	// INT1
    10992U,	// INT3
    11749U,	// INTO
    11391U,	// INVD
    437281258U,	// INVEPT32
    437281258U,	// INVEPT64
    104178U,	// INVLPG
    12342U,	// INVLPGA32
    12359U,	// INVLPGA64
    437276648U,	// INVPCID32
    437276648U,	// INVPCID64
    437276657U,	// INVVPID32
    437276657U,	// INVVPID64
    12109U,	// IRET16
    11379U,	// IRET32
    11855U,	// IRET64
    39312U,	// ISTT_FP16m
    72080U,	// ISTT_FP32m
    88464U,	// ISTT_FP64m
    0U,	// ISTT_Fp16m32
    0U,	// ISTT_Fp16m64
    0U,	// ISTT_Fp16m80
    0U,	// ISTT_Fp32m32
    0U,	// ISTT_Fp32m64
    0U,	// ISTT_Fp32m80
    0U,	// ISTT_Fp64m32
    0U,	// ISTT_Fp64m64
    0U,	// ISTT_Fp64m80
    41524U,	// IST_F16m
    74292U,	// IST_F32m
    39305U,	// IST_FP16m
    72073U,	// IST_FP32m
    88457U,	// IST_FP64m
    0U,	// IST_Fp16m32
    0U,	// IST_Fp16m64
    0U,	// IST_Fp16m80
    0U,	// IST_Fp32m32
    0U,	// IST_Fp32m64
    0U,	// IST_Fp32m80
    0U,	// IST_Fp64m32
    0U,	// IST_Fp64m64
    0U,	// IST_Fp64m80
    240299511U,	// Int_CMPSDrm
    38989303U,	// Int_CMPSDrr
    274902519U,	// Int_CMPSSrm
    40037879U,	// Int_CMPSSrr
    537941100U,	// Int_COMISDrm
    336614508U,	// Int_COMISDrr
    537944326U,	// Int_COMISSrm
    336617734U,	// Int_COMISSrr
    235954281U,	// Int_CVTSD2SSrm
    34627689U,	// Int_CVTSD2SSrr
    135287751U,	// Int_CVTSI2SD64rm
    34624455U,	// Int_CVTSI2SD64rr
    101733319U,	// Int_CVTSI2SDrm
    34624455U,	// Int_CVTSI2SDrr
    135290996U,	// Int_CVTSI2SS64rm
    34627700U,	// Int_CVTSI2SS64rr
    101736564U,	// Int_CVTSI2SSrm
    34627700U,	// Int_CVTSI2SSrr
    269505502U,	// Int_CVTSS2SDrm
    34624478U,	// Int_CVTSS2SDrr
    571496375U,	// Int_CVTTSD2SI64rm
    336615351U,	// Int_CVTTSD2SI64rr
    571496375U,	// Int_CVTTSD2SIrm
    336615351U,	// Int_CVTTSD2SIrr
    605050830U,	// Int_CVTTSS2SI64rm
    336615374U,	// Int_CVTTSS2SI64rr
    605050830U,	// Int_CVTTSS2SIrm
    336615374U,	// Int_CVTTSS2SIrr
    11172U,	// Int_MemBarrier
    537941099U,	// Int_UCOMISDrm
    336614507U,	// Int_UCOMISDrr
    537944325U,	// Int_UCOMISSrm
    336617733U,	// Int_UCOMISSrr
    2488626683U,	// Int_VCMPSDrm
    2488643067U,	// Int_VCMPSDrr
    2489675259U,	// Int_VCMPSSrm
    2489691643U,	// Int_VCMPSSrr
    537941108U,	// Int_VCOMISDZrm
    336614516U,	// Int_VCOMISDZrr
    537941108U,	// Int_VCOMISDrm
    336614516U,	// Int_VCOMISDrr
    537944334U,	// Int_VCOMISSZrm
    336617742U,	// Int_VCOMISSZrr
    537944334U,	// Int_VCOMISSrm
    336617742U,	// Int_VCOMISSrr
    2484101224U,	// Int_VCVTSD2SSrm
    2484101224U,	// Int_VCVTSD2SSrr
    2484097990U,	// Int_VCVTSI2SD64Zrm
    2484097990U,	// Int_VCVTSI2SD64Zrr
    2484097990U,	// Int_VCVTSI2SD64rm
    2484097990U,	// Int_VCVTSI2SD64rr
    2484097990U,	// Int_VCVTSI2SDZrm
    2484097990U,	// Int_VCVTSI2SDZrr
    2484097990U,	// Int_VCVTSI2SDrm
    2484097990U,	// Int_VCVTSI2SDrr
    2484101235U,	// Int_VCVTSI2SS64Zrm
    2484101235U,	// Int_VCVTSI2SS64Zrr
    2484101235U,	// Int_VCVTSI2SS64rm
    2484101235U,	// Int_VCVTSI2SS64rr
    2484101235U,	// Int_VCVTSI2SSZrm
    2484101235U,	// Int_VCVTSI2SSZrr
    2484101235U,	// Int_VCVTSI2SSrm
    2484101235U,	// Int_VCVTSI2SSrr
    2484098013U,	// Int_VCVTSS2SDrm
    2484098013U,	// Int_VCVTSS2SDrr
    571491619U,	// Int_VCVTTSD2SI64Zrm
    336610595U,	// Int_VCVTTSD2SI64Zrr
    571496374U,	// Int_VCVTTSD2SI64rm
    336615350U,	// Int_VCVTTSD2SI64rr
    571491619U,	// Int_VCVTTSD2SIZrm
    336610595U,	// Int_VCVTTSD2SIZrr
    571496374U,	// Int_VCVTTSD2SIrm
    336615350U,	// Int_VCVTTSD2SIrr
    571491669U,	// Int_VCVTTSD2USI64Zrm
    336610645U,	// Int_VCVTTSD2USI64Zrr
    571491669U,	// Int_VCVTTSD2USIZrm
    336610645U,	// Int_VCVTTSD2USIZrr
    605046076U,	// Int_VCVTTSS2SI64Zrm
    336610620U,	// Int_VCVTTSS2SI64Zrr
    605050829U,	// Int_VCVTTSS2SI64rm
    336615373U,	// Int_VCVTTSS2SI64rr
    605046076U,	// Int_VCVTTSS2SIZrm
    336610620U,	// Int_VCVTTSS2SIZrr
    605050829U,	// Int_VCVTTSS2SIrm
    336615373U,	// Int_VCVTTSS2SIrr
    605046128U,	// Int_VCVTTSS2USI64Zrm
    336610672U,	// Int_VCVTTSS2USI64Zrr
    605046128U,	// Int_VCVTTSS2USIZrm
    336610672U,	// Int_VCVTTSS2USIZrr
    2484098001U,	// Int_VCVTUSI2SD64Zrm
    2484098001U,	// Int_VCVTUSI2SD64Zrr
    2484098001U,	// Int_VCVTUSI2SDZrm
    2484098001U,	// Int_VCVTUSI2SDZrr
    2484101246U,	// Int_VCVTUSI2SS64Zrm
    2484101246U,	// Int_VCVTUSI2SS64Zrr
    2484101246U,	// Int_VCVTUSI2SSZrm
    2484101246U,	// Int_VCVTUSI2SSZrr
    537941098U,	// Int_VUCOMISDZrm
    336614506U,	// Int_VUCOMISDZrr
    537941098U,	// Int_VUCOMISDrm
    336614506U,	// Int_VUCOMISDrr
    537944324U,	// Int_VUCOMISSZrm
    336617732U,	// Int_VUCOMISSZrr
    537944324U,	// Int_VUCOMISSrm
    336617732U,	// Int_VUCOMISSrr
    153068U,	// JAE_1
    153068U,	// JAE_2
    153068U,	// JAE_4
    150840U,	// JA_1
    150840U,	// JA_2
    150840U,	// JA_4
    153088U,	// JBE_1
    153088U,	// JBE_2
    153088U,	// JBE_4
    150959U,	// JB_1
    150959U,	// JB_2
    150959U,	// JB_4
    157038U,	// JCXZ
    157031U,	// JECXZ_32
    157031U,	// JECXZ_64
    153153U,	// JE_1
    153153U,	// JE_2
    153153U,	// JE_4
    153124U,	// JGE_1
    153124U,	// JGE_2
    153124U,	// JGE_4
    153326U,	// JG_1
    153326U,	// JG_2
    153326U,	// JG_4
    153157U,	// JLE_1
    153157U,	// JLE_2
    153157U,	// JLE_4
    153624U,	// JL_1
    153624U,	// JL_2
    153624U,	// JL_4
    39206U,	// JMP16m
    22822U,	// JMP16r
    71974U,	// JMP32m
    22822U,	// JMP32r
    88358U,	// JMP64m
    22822U,	// JMP64r
    153894U,	// JMP_1
    153894U,	// JMP_2
    153894U,	// JMP_4
    153177U,	// JNE_1
    153177U,	// JNE_2
    153177U,	// JNE_4
    153810U,	// JNO_1
    153810U,	// JNO_2
    153810U,	// JNO_4
    153922U,	// JNP_1
    153922U,	// JNP_2
    153922U,	// JNP_4
    154856U,	// JNS_1
    154856U,	// JNS_2
    154856U,	// JNS_4
    153806U,	// JO_1
    153806U,	// JO_2
    153806U,	// JO_4
    153877U,	// JP_1
    153877U,	// JP_2
    153877U,	// JP_4
    157044U,	// JRCXZ
    154852U,	// JS_1
    154852U,	// JS_2
    154852U,	// JS_4
    2484092965U,	// KANDBrr
    2484093179U,	// KANDDrr
    2484093006U,	// KANDNBrr
    2484093305U,	// KANDNDrr
    2484094782U,	// KANDNQrr
    2484095738U,	// KANDNWrr
    2484094559U,	// KANDQrr
    2484095686U,	// KANDWrr
    336609432U,	// KMOVBkk
    504381592U,	// KMOVBkm
    336609432U,	// KMOVBkr
    1147032U,	// KMOVBmk
    336609432U,	// KMOVBrk
    336610551U,	// KMOVDkk
    303056119U,	// KMOVDkm
    336610551U,	// KMOVDkr
    1115383U,	// KMOVDmk
    336610551U,	// KMOVDrk
    336611358U,	// KMOVQkk
    370165790U,	// KMOVQkm
    336611358U,	// KMOVQkr
    1132574U,	// KMOVQmk
    336611358U,	// KMOVQrk
    336612186U,	// KMOVWkk
    470829914U,	// KMOVWkm
    336612186U,	// KMOVWkr
    1084250U,	// KMOVWmk
    336612186U,	// KMOVWrk
    336609424U,	// KNOTBrr
    336610478U,	// KNOTDrr
    336611285U,	// KNOTQrr
    336612167U,	// KNOTWrr
    2484093048U,	// KORBrr
    2484093862U,	// KORDrr
    2484094838U,	// KORQrr
    336612175U,	// KORTESTWrr
    2484095780U,	// KORWrr
    0U,	// KSET0B
    0U,	// KSET0W
    0U,	// KSET1B
    0U,	// KSET1W
    2484095727U,	// KSHIFTLWri
    2484095804U,	// KSHIFTRWri
    2484095675U,	// KUNPCKBWrr
    2484093055U,	// KXNORBrr
    2484093869U,	// KXNORDrr
    2484094845U,	// KXNORQrr
    2484095787U,	// KXNORWrr
    2484093064U,	// KXORBrr
    2484093886U,	// KXORDrr
    2484094862U,	// KXORQrr
    2484095796U,	// KXORWrr
    11535U,	// LAHF
    470834208U,	// LAR16rm
    336616480U,	// LAR16rr
    470834208U,	// LAR32rm
    336616480U,	// LAR32rr
    470834208U,	// LAR64rm
    336616480U,	// LAR64rr
    1087205U,	// LCMPXCHG16
    265578U,	// LCMPXCHG16B
    1119973U,	// LCMPXCHG32
    1136357U,	// LCMPXCHG64
    1152741U,	// LCMPXCHG8
    85366U,	// LCMPXCHG8B
    437281377U,	// LDDQUrm
    72836U,	// LDMXCSR
    638606544U,	// LDS16rm
    638606544U,	// LDS32rm
    12414U,	// LD_F0
    10862U,	// LD_F1
    118784U,	// LD_F32m
    135168U,	// LD_F64m
    380928U,	// LD_F80m
    0U,	// LD_Fp032
    0U,	// LD_Fp064
    0U,	// LD_Fp080
    0U,	// LD_Fp132
    0U,	// LD_Fp164
    0U,	// LD_Fp180
    0U,	// LD_Fp32m
    0U,	// LD_Fp32m64
    0U,	// LD_Fp32m80
    0U,	// LD_Fp64m
    0U,	// LD_Fp64m80
    0U,	// LD_Fp80m
    20480U,	// LD_Frr
    470830387U,	// LEA16r
    303058227U,	// LEA32r
    303058227U,	// LEA64_32r
    370167091U,	// LEA64r
    11522U,	// LEAVE
    11522U,	// LEAVE64
    638606549U,	// LES16rm
    638606549U,	// LES32rm
    11407U,	// LFENCE
    638606554U,	// LFS16rm
    638606554U,	// LFS32rm
    638606554U,	// LFS64rm
    287128U,	// LGDT16m
    287128U,	// LGDT32m
    287128U,	// LGDT64m
    638606559U,	// LGS16rm
    638606559U,	// LGS32rm
    638606559U,	// LGS64rm
    287140U,	// LIDT16m
    287140U,	// LIDT32m
    287140U,	// LIDT64m
    41392U,	// LLDT16m
    25008U,	// LLDT16r
    42050U,	// LMSW16m
    25666U,	// LMSW16r
    1085307U,	// LOCK_ADD16mi
    1085307U,	// LOCK_ADD16mi8
    1085307U,	// LOCK_ADD16mr
    1118075U,	// LOCK_ADD32mi
    1118075U,	// LOCK_ADD32mi8
    1118075U,	// LOCK_ADD32mr
    1134459U,	// LOCK_ADD64mi32
    1134459U,	// LOCK_ADD64mi8
    1134459U,	// LOCK_ADD64mr
    1150843U,	// LOCK_ADD8mi
    1150843U,	// LOCK_ADD8mr
    1085511U,	// LOCK_AND16mi
    1085511U,	// LOCK_AND16mi8
    1085511U,	// LOCK_AND16mr
    1118279U,	// LOCK_AND32mi
    1118279U,	// LOCK_AND32mi8
    1118279U,	// LOCK_AND32mr
    1134663U,	// LOCK_AND64mi32
    1134663U,	// LOCK_AND64mi8
    1134663U,	// LOCK_AND64mr
    1151047U,	// LOCK_AND8mi
    1151047U,	// LOCK_AND8mr
    36585U,	// LOCK_DEC16m
    69353U,	// LOCK_DEC32m
    85737U,	// LOCK_DEC64m
    102121U,	// LOCK_DEC8m
    36630U,	// LOCK_INC16m
    69398U,	// LOCK_INC32m
    85782U,	// LOCK_INC64m
    102166U,	// LOCK_INC8m
    1088599U,	// LOCK_OR16mi
    1088599U,	// LOCK_OR16mi8
    1088599U,	// LOCK_OR16mr
    1121367U,	// LOCK_OR32mi
    1121367U,	// LOCK_OR32mi8
    1121367U,	// LOCK_OR32mr
    1137751U,	// LOCK_OR64mi32
    1137751U,	// LOCK_OR64mi8
    1137751U,	// LOCK_OR64mr
    1154135U,	// LOCK_OR8mi
    1154135U,	// LOCK_OR8mr
    11609U,	// LOCK_PREFIX
    1085067U,	// LOCK_SUB16mi
    1085067U,	// LOCK_SUB16mi8
    1085067U,	// LOCK_SUB16mr
    1117835U,	// LOCK_SUB32mi
    1117835U,	// LOCK_SUB32mi8
    1117835U,	// LOCK_SUB32mr
    1134219U,	// LOCK_SUB64mi32
    1134219U,	// LOCK_SUB64mi8
    1134219U,	// LOCK_SUB64mr
    1150603U,	// LOCK_SUB8mi
    1150603U,	// LOCK_SUB8mr
    1088627U,	// LOCK_XOR16mi
    1088627U,	// LOCK_XOR16mi8
    1088627U,	// LOCK_XOR16mr
    1121395U,	// LOCK_XOR32mi
    1121395U,	// LOCK_XOR32mi8
    1121395U,	// LOCK_XOR32mr
    1137779U,	// LOCK_XOR64mi32
    1137779U,	// LOCK_XOR64mi8
    1137779U,	// LOCK_XOR64mr
    1154163U,	// LOCK_XOR8mi
    1154163U,	// LOCK_XOR8mr
    402997U,	// LODSB
    419681U,	// LODSL
    436234U,	// LODSQ
    452376U,	// LODSW
    153947U,	// LOOP
    153205U,	// LOOPE
    153182U,	// LOOPNE
    22234U,	// LRETIL
    23312U,	// LRETIQ
    22234U,	// LRETIW
    11556U,	// LRETL
    11829U,	// LRETQ
    11556U,	// LRETW
    470833239U,	// LSL16rm
    336615511U,	// LSL16rr
    303061079U,	// LSL32rm
    336615511U,	// LSL32rr
    370169943U,	// LSL64rm
    336615511U,	// LSL64rr
    638607642U,	// LSS16rm
    638607642U,	// LSS32rm
    638607642U,	// LSS64rm
    40098U,	// LTRm
    23714U,	// LTRr
    462727U,	// LXADD16
    479111U,	// LXADD32
    495495U,	// LXADD64
    511879U,	// LXADD8
    470835666U,	// LZCNT16rm
    336617938U,	// LZCNT16rr
    303063506U,	// LZCNT32rm
    336617938U,	// LZCNT32rr
    370172370U,	// LZCNT64rm
    336617938U,	// LZCNT64rr
    336618089U,	// MASKMOVDQU
    336618089U,	// MASKMOVDQU64
    202396444U,	// MAXCPDrm
    34624284U,	// MAXCPDrr
    202399724U,	// MAXCPSrm
    34627564U,	// MAXCPSrr
    235951319U,	// MAXCSDrm
    34624727U,	// MAXCSDrr
    269508978U,	// MAXCSSrm
    34627954U,	// MAXCSSrr
    202396444U,	// MAXPDrm
    34624284U,	// MAXPDrr
    202399724U,	// MAXPSrm
    34627564U,	// MAXPSrr
    235951319U,	// MAXSDrm
    235951319U,	// MAXSDrm_Int
    34624727U,	// MAXSDrr
    34624727U,	// MAXSDrr_Int
    269508978U,	// MAXSSrm
    269508978U,	// MAXSSrm_Int
    34627954U,	// MAXSSrr
    34627954U,	// MAXSSrr_Int
    11414U,	// MFENCE
    202396326U,	// MINCPDrm
    34624166U,	// MINCPDrr
    202399565U,	// MINCPSrm
    34627405U,	// MINCPSrr
    235951239U,	// MINCSDrm
    34624647U,	// MINCSDrr
    269508896U,	// MINCSSrm
    34627872U,	// MINCSSrr
    202396326U,	// MINPDrm
    34624166U,	// MINPDrr
    202399565U,	// MINPSrm
    34627405U,	// MINPSrr
    235951239U,	// MINSDrm
    235951239U,	// MINSDrm_Int
    34624647U,	// MINSDrr
    34624647U,	// MINSDrr_Int
    269508896U,	// MINSSrm
    269508896U,	// MINSSrm_Int
    34627872U,	// MINSSrr
    34627872U,	// MINSSrr_Int
    537941870U,	// MMX_CVTPD2PIirm
    336615278U,	// MMX_CVTPD2PIirr
    370168080U,	// MMX_CVTPI2PDirm
    336613648U,	// MMX_CVTPI2PDirr
    135290302U,	// MMX_CVTPI2PSirm
    34627006U,	// MMX_CVTPI2PSirr
    571496323U,	// MMX_CVTPS2PIirm
    336615299U,	// MMX_CVTPS2PIirr
    537941859U,	// MMX_CVTTPD2PIirm
    336615267U,	// MMX_CVTTPD2PIirr
    571496312U,	// MMX_CVTTPS2PIirm
    336615288U,	// MMX_CVTTPS2PIirr
    12056U,	// MMX_EMMS
    336616411U,	// MMX_MASKMOVQ
    336616411U,	// MMX_MASKMOVQ64
    336614740U,	// MMX_MOVD64from64rr
    336614740U,	// MMX_MOVD64grr
    1119572U,	// MMX_MOVD64mr
    303060308U,	// MMX_MOVD64rm
    336614740U,	// MMX_MOVD64rr
    336614740U,	// MMX_MOVD64to64rr
    336615894U,	// MMX_MOVDQ2Qrr
    336615894U,	// MMX_MOVFR642Qrr
    1137550U,	// MMX_MOVNTQmr
    336615985U,	// MMX_MOVQ2DQrr
    336615985U,	// MMX_MOVQ2FR64rr
    1137631U,	// MMX_MOVQ64mr
    370170847U,	// MMX_MOVQ64rm
    336616415U,	// MMX_MOVQ64rr
    336616415U,	// MMX_MOVQ64rr_REV
    370167284U,	// MMX_PABSBrm64
    336612852U,	// MMX_PABSBrr64
    370168863U,	// MMX_PABSDrm64
    336614431U,	// MMX_PABSDrr64
    370172937U,	// MMX_PABSWrm64
    336618505U,	// MMX_PABSWrr64
    135291728U,	// MMX_PACKSSDWirm
    34628432U,	// MMX_PACKSSDWirr
    135286451U,	// MMX_PACKSSWBirm
    34623155U,	// MMX_PACKSSWBirr
    135286462U,	// MMX_PACKUSWBirm
    34623166U,	// MMX_PACKUSWBirr
    135286167U,	// MMX_PADDBirm
    34622871U,	// MMX_PADDBirr
    135286679U,	// MMX_PADDDirm
    34623383U,	// MMX_PADDDirr
    135289436U,	// MMX_PADDQirm
    34626140U,	// MMX_PADDQirr
    135286277U,	// MMX_PADDSBirm
    34622981U,	// MMX_PADDSBirr
    135291962U,	// MMX_PADDSWirm
    34628666U,	// MMX_PADDSWirr
    135286319U,	// MMX_PADDUSBirm
    34623023U,	// MMX_PADDUSBirr
    135292035U,	// MMX_PADDUSWirm
    34628739U,	// MMX_PADDUSWirr
    135291710U,	// MMX_PADDWirm
    34628414U,	// MMX_PADDWirr
    2282773580U,	// MMX_PALIGNR64irm
    2182110284U,	// MMX_PALIGNR64irr
    135289009U,	// MMX_PANDNirm
    34625713U,	// MMX_PANDNirr
    135286854U,	// MMX_PANDirm
    34623558U,	// MMX_PANDirr
    135286184U,	// MMX_PAVGBirm
    34622888U,	// MMX_PAVGBirr
    135291765U,	// MMX_PAVGWirm
    34628469U,	// MMX_PAVGWirr
    135286232U,	// MMX_PCMPEQBirm
    34622936U,	// MMX_PCMPEQBirr
    135287597U,	// MMX_PCMPEQDirm
    34624301U,	// MMX_PCMPEQDirr
    135291870U,	// MMX_PCMPEQWirm
    34628574U,	// MMX_PCMPEQWirr
    135286360U,	// MMX_PCMPGTBirm
    34623064U,	// MMX_PCMPGTBirr
    135288048U,	// MMX_PCMPGTDirm
    34624752U,	// MMX_PCMPGTDirr
    135292061U,	// MMX_PCMPGTWirm
    34628765U,	// MMX_PCMPGTWirr
    2484102144U,	// MMX_PEXTRWirri
    135291952U,	// MMX_PHADDSWrm64
    34628656U,	// MMX_PHADDSWrr64
    135291701U,	// MMX_PHADDWrm64
    34628405U,	// MMX_PHADDWrr64
    135286670U,	// MMX_PHADDrm64
    34623374U,	// MMX_PHADDrr64
    135286611U,	// MMX_PHSUBDrm64
    34623315U,	// MMX_PHSUBDrr64
    135291933U,	// MMX_PHSUBSWrm64
    34628637U,	// MMX_PHSUBSWrr64
    135291647U,	// MMX_PHSUBWrm64
    34628351U,	// MMX_PHSUBWrr64
    2215666679U,	// MMX_PINSRWirmi
    2182112247U,	// MMX_PINSRWirri
    135291921U,	// MMX_PMADDUBSWrm64
    34628625U,	// MMX_PMADDUBSWrr64
    135288182U,	// MMX_PMADDWDirm
    34624886U,	// MMX_PMADDWDirr
    135292052U,	// MMX_PMAXSWirm
    34628756U,	// MMX_PMAXSWirr
    135286424U,	// MMX_PMAXUBirm
    34623128U,	// MMX_PMAXUBirr
    135291983U,	// MMX_PMINSWirm
    34628687U,	// MMX_PMINSWirr
    135286401U,	// MMX_PMINUBirm
    34623105U,	// MMX_PMINUBirr
    336612788U,	// MMX_PMOVMSKBrr
    135292006U,	// MMX_PMULHRSWrm64
    34628710U,	// MMX_PMULHRSWrr64
    135292093U,	// MMX_PMULHUWirm
    34628797U,	// MMX_PMULHUWirr
    135291794U,	// MMX_PMULHWirm
    34628498U,	// MMX_PMULHWirr
    135291836U,	// MMX_PMULLWirm
    34628540U,	// MMX_PMULLWirr
    135289576U,	// MMX_PMULUDQirm
    34626280U,	// MMX_PMULUDQirr
    135289942U,	// MMX_PORirm
    34626646U,	// MMX_PORirr
    135291584U,	// MMX_PSADBWirm
    34628288U,	// MMX_PSADBWirr
    135286175U,	// MMX_PSHUFBrm64
    34622879U,	// MMX_PSHUFBrr64
    2517656428U,	// MMX_PSHUFWmi
    2484101996U,	// MMX_PSHUFWri
    135286223U,	// MMX_PSIGNBrm64
    34622927U,	// MMX_PSIGNBrr64
    135286869U,	// MMX_PSIGNDrm64
    34623573U,	// MMX_PSIGNDrr64
    135291861U,	// MMX_PSIGNWrm64
    34628565U,	// MMX_PSIGNWrr64
    34623508U,	// MMX_PSLLDri
    135286804U,	// MMX_PSLLDrm
    34623508U,	// MMX_PSLLDrr
    34626336U,	// MMX_PSLLQri
    135289632U,	// MMX_PSLLQrm
    34626336U,	// MMX_PSLLQrr
    34628532U,	// MMX_PSLLWri
    135291828U,	// MMX_PSLLWrm
    34628532U,	// MMX_PSLLWrr
    34623286U,	// MMX_PSRADri
    135286582U,	// MMX_PSRADrm
    34623286U,	// MMX_PSRADrr
    34628269U,	// MMX_PSRAWri
    135291565U,	// MMX_PSRAWrm
    34628269U,	// MMX_PSRAWrr
    34623525U,	// MMX_PSRLDri
    135286821U,	// MMX_PSRLDrm
    34623525U,	// MMX_PSRLDrr
    34626344U,	// MMX_PSRLQri
    135289640U,	// MMX_PSRLQrm
    34626344U,	// MMX_PSRLQrr
    34628549U,	// MMX_PSRLWri
    135291845U,	// MMX_PSRLWrm
    34628549U,	// MMX_PSRLWrr
    135286159U,	// MMX_PSUBBirm
    34622863U,	// MMX_PSUBBirr
    135286620U,	// MMX_PSUBDirm
    34623324U,	// MMX_PSUBDirr
    135289341U,	// MMX_PSUBQirm
    34626045U,	// MMX_PSUBQirr
    135286268U,	// MMX_PSUBSBirm
    34622972U,	// MMX_PSUBSBirr
    135291943U,	// MMX_PSUBSWirm
    34628647U,	// MMX_PSUBSWirr
    135286309U,	// MMX_PSUBUSBirm
    34623013U,	// MMX_PSUBUSBirr
    135292025U,	// MMX_PSUBUSWirm
    34628729U,	// MMX_PSUBUSWirr
    135291656U,	// MMX_PSUBWirm
    34628360U,	// MMX_PSUBWirr
    135291612U,	// MMX_PUNPCKHBWirm
    34628316U,	// MMX_PUNPCKHBWirr
    135289454U,	// MMX_PUNPCKHDQirm
    34626158U,	// MMX_PUNPCKHDQirr
    135288192U,	// MMX_PUNPCKHWDirm
    34624896U,	// MMX_PUNPCKHWDirr
    135291624U,	// MMX_PUNPCKLBWirm
    34628328U,	// MMX_PUNPCKLBWirr
    135289466U,	// MMX_PUNPCKLDQirm
    34626170U,	// MMX_PUNPCKLDQirr
    135288204U,	// MMX_PUNPCKLWDirm
    34624908U,	// MMX_PUNPCKLWDirr
    135289970U,	// MMX_PXORirm
    34626674U,	// MMX_PXORirr
    0U,	// MONITOR
    11881U,	// MONITORrrr
    11670U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    9970335U,	// MOV16ao16
    9970335U,	// MOV16ao16_16
    1090207U,	// MOV16mi
    1090207U,	// MOV16mr
    1090207U,	// MOV16ms
    534276U,	// MOV16o16a
    534276U,	// MOV16o16a_16
    336618143U,	// MOV16ri
    336618143U,	// MOV16ri_alt
    470835871U,	// MOV16rm
    336618143U,	// MOV16rr
    336618143U,	// MOV16rr_REV
    336618143U,	// MOV16rs
    470835871U,	// MOV16sm
    336618143U,	// MOV16sr
    11035295U,	// MOV32ao32
    11035295U,	// MOV32ao32_16
    336618143U,	// MOV32cr
    336618143U,	// MOV32dr
    1122975U,	// MOV32mi
    1122975U,	// MOV32mr
    1090207U,	// MOV32ms
    550829U,	// MOV32o32a
    550829U,	// MOV32o32a_16
    0U,	// MOV32r0
    336618143U,	// MOV32rc
    336618143U,	// MOV32rd
    336618143U,	// MOV32ri
    0U,	// MOV32ri64
    336618143U,	// MOV32ri_alt
    303063711U,	// MOV32rm
    336618143U,	// MOV32rr
    336618143U,	// MOV32rr_REV
    336618143U,	// MOV32rs
    470835871U,	// MOV32sm
    336618143U,	// MOV32sr
    9968834U,	// MOV64ao16
    11033794U,	// MOV64ao32
    12098754U,	// MOV64ao64
    13163714U,	// MOV64ao8
    336618143U,	// MOV64cr
    336618143U,	// MOV64dr
    1139359U,	// MOV64mi32
    1139359U,	// MOV64mr
    1090207U,	// MOV64ms
    534254U,	// MOV64o16a
    550805U,	// MOV64o32a
    567328U,	// MOV64o64a
    583294U,	// MOV64o8a
    336618143U,	// MOV64rc
    336618143U,	// MOV64rd
    336616642U,	// MOV64ri
    336618143U,	// MOV64ri32
    370172575U,	// MOV64rm
    336618143U,	// MOV64rr
    336618143U,	// MOV64rr_REV
    336618143U,	// MOV64rs
    470835871U,	// MOV64sm
    336618143U,	// MOV64sr
    336616415U,	// MOV64toPQIrr
    370170847U,	// MOV64toSDrm
    336616415U,	// MOV64toSDrr
    13165215U,	// MOV8ao8
    13165215U,	// MOV8ao8_16
    1155743U,	// MOV8mi
    1155743U,	// MOV8mr
    1155743U,	// MOV8mr_NOREX
    583316U,	// MOV8o8a
    583316U,	// MOV8o8a_16
    336618143U,	// MOV8ri
    336618143U,	// MOV8ri_alt
    504390303U,	// MOV8rm
    504390303U,	// MOV8rm_NOREX
    336618143U,	// MOV8rr
    336618143U,	// MOV8rr_NOREX
    336618143U,	// MOV8rr_REV
    1642899U,	// MOVAPDmr
    537940371U,	// MOVAPDrm
    336613779U,	// MOVAPDrr
    336613779U,	// MOVAPDrr_REV
    1646134U,	// MOVAPSmr
    537943606U,	// MOVAPSrm
    336617014U,	// MOVAPSrr
    336617014U,	// MOVAPSrr_REV
    1086989U,	// MOVBE16mr
    470832653U,	// MOVBE16rm
    1119757U,	// MOVBE32mr
    303060493U,	// MOVBE32rm
    1136141U,	// MOVBE64mr
    370169357U,	// MOVBE64rm
    571496857U,	// MOVDDUPrm
    336615833U,	// MOVDDUPrr
    303060308U,	// MOVDI2PDIrm
    336614740U,	// MOVDI2PDIrr
    303060308U,	// MOVDI2SSrm
    336614740U,	// MOVDI2SSrr
    1314120U,	// MOVDQAmr
    437275976U,	// MOVDQArm
    336612680U,	// MOVDQArr
    336612680U,	// MOVDQArr_REV
    1319533U,	// MOVDQUmr
    437281389U,	// MOVDQUrm
    336618093U,	// MOVDQUrr
    336618093U,	// MOVDQUrr_REV
    34627338U,	// MOVHLPSrr
    1184337U,	// MOVHPDmr
    235950673U,	// MOVHPDrm
    1187574U,	// MOVHPSmr
    235953910U,	// MOVHPSrm
    34627308U,	// MOVLHPSrr
    1184387U,	// MOVLPDmr
    235950723U,	// MOVLPDrm
    1187634U,	// MOVLPSmr
    235953970U,	// MOVLPSrm
    336613978U,	// MOVMSKPDrr
    336617215U,	// MOVMSKPSrr
    437275965U,	// MOVNTDQArm
    1645267U,	// MOVNTDQmr
    1136618U,	// MOVNTI_64mr
    1120234U,	// MOVNTImr
    1643224U,	// MOVNTPDmr
    1646483U,	// MOVNTPSmr
    1184933U,	// MOVNTSD
    1171767U,	// MOVNTSS
    0U,	// MOVPC32r
    1119572U,	// MOVPDI2DImr
    336614740U,	// MOVPDI2DIrr
    1137631U,	// MOVPQI2QImr
    336616415U,	// MOVPQI2QIrr
    336616415U,	// MOVPQIto64rr
    370170847U,	// MOVQI2PQIrm
    672435777U,	// MOVSB
    1184974U,	// MOVSDmr
    571495630U,	// MOVSDrm
    34624718U,	// MOVSDrr
    34624718U,	// MOVSDrr_REV
    1137631U,	// MOVSDto64mr
    336616415U,	// MOVSDto64rr
    537942435U,	// MOVSHDUPrm
    336615843U,	// MOVSHDUPrr
    706008270U,	// MOVSL
    537942446U,	// MOVSLDUPrm
    336615854U,	// MOVSLDUPrr
    739859325U,	// MOVSQ
    1119572U,	// MOVSS2DImr
    336614740U,	// MOVSS2DIrr
    1171818U,	// MOVSSmr
    605053290U,	// MOVSSrm
    34627946U,	// MOVSSrr
    34627946U,	// MOVSSrr_REV
    773137548U,	// MOVSW
    504391001U,	// MOVSX16rm8
    336618841U,	// MOVSX16rr8
    470836569U,	// MOVSX32rm16
    504391001U,	// MOVSX32rm8
    336618841U,	// MOVSX32rr16
    336618841U,	// MOVSX32rr8
    336614884U,	// MOVSX64_NOREXrr32
    470836569U,	// MOVSX64rm16
    303060452U,	// MOVSX64rm32
    504391001U,	// MOVSX64rm8
    336618841U,	// MOVSX64rr16
    336614884U,	// MOVSX64rr32
    336618841U,	// MOVSX64rr8
    1643252U,	// MOVUPDmr
    537940724U,	// MOVUPDrm
    336614132U,	// MOVUPDrr
    336614132U,	// MOVUPDrr_REV
    1646532U,	// MOVUPSmr
    537944004U,	// MOVUPSrm
    336617412U,	// MOVUPSrr
    336617412U,	// MOVUPSrr_REV
    437279711U,	// MOVZPQILo2PQIrm
    336616415U,	// MOVZPQILo2PQIrr
    370170847U,	// MOVZQI2PQIrm
    336616415U,	// MOVZQI2PQIrr
    504391008U,	// MOVZX16rm8
    336618848U,	// MOVZX16rr8
    504391008U,	// MOVZX32_NOREXrm8
    336618848U,	// MOVZX32_NOREXrr8
    470836576U,	// MOVZX32rm16
    504391008U,	// MOVZX32rm8
    336618848U,	// MOVZX32rr16
    336618848U,	// MOVZX32rr8
    470836576U,	// MOVZX64rm16_Q
    504391008U,	// MOVZX64rm8_Q
    336618848U,	// MOVZX64rr16_Q
    336618848U,	// MOVZX64rr8_Q
    2551210687U,	// MPSADBWrmi
    2182111935U,	// MPSADBWrri
    39012U,	// MUL16m
    22628U,	// MUL16r
    71780U,	// MUL32m
    22628U,	// MUL32r
    88164U,	// MUL64m
    22628U,	// MUL64r
    104548U,	// MUL8m
    22628U,	// MUL8r
    202396283U,	// MULPDrm
    34624123U,	// MULPDrr
    202399530U,	// MULPSrm
    34627370U,	// MULPSrr
    235951230U,	// MULSDrm
    235951230U,	// MULSDrm_Int
    34624638U,	// MULSDrr
    34624638U,	// MULSDrr_Int
    269508888U,	// MULSSrm
    269508888U,	// MULSSrm_Int
    34627864U,	// MULSSrr
    34627864U,	// MULSSrr_Int
    2484102422U,	// MULX32rm
    2484102422U,	// MULX32rr
    2484102422U,	// MULX64rm
    2484102422U,	// MULX64rr
    120931U,	// MUL_F32m
    137315U,	// MUL_F64m
    39017U,	// MUL_FI16m
    71785U,	// MUL_FI32m
    22809U,	// MUL_FPrST0
    22627U,	// MUL_FST0r
    0U,	// MUL_Fp32
    0U,	// MUL_Fp32m
    0U,	// MUL_Fp64
    0U,	// MUL_Fp64m
    0U,	// MUL_Fp64m32
    0U,	// MUL_Fp80
    0U,	// MUL_Fp80m32
    0U,	// MUL_Fp80m64
    0U,	// MUL_FpI16m32
    0U,	// MUL_FpI16m64
    0U,	// MUL_FpI16m80
    0U,	// MUL_FpI32m32
    0U,	// MUL_FpI32m64
    0U,	// MUL_FpI32m80
    2119779U,	// MUL_FrST0
    12121U,	// MWAITrr
    38624U,	// NEG16m
    22240U,	// NEG16r
    71392U,	// NEG32m
    22240U,	// NEG32r
    87776U,	// NEG64m
    22240U,	// NEG64r
    104160U,	// NEG8m
    22240U,	// NEG8r
    11777U,	// NOOP
    39254U,	// NOOP18_16m4
    39254U,	// NOOP18_16m5
    39254U,	// NOOP18_16m6
    39254U,	// NOOP18_16m7
    22870U,	// NOOP18_16r4
    22870U,	// NOOP18_16r5
    22870U,	// NOOP18_16r6
    22870U,	// NOOP18_16r7
    72022U,	// NOOP18_m4
    72022U,	// NOOP18_m5
    72022U,	// NOOP18_m6
    72022U,	// NOOP18_m7
    22870U,	// NOOP18_r4
    22870U,	// NOOP18_r5
    22870U,	// NOOP18_r6
    22870U,	// NOOP18_r7
    806410582U,	// NOOP19rr
    72022U,	// NOOPL
    72022U,	// NOOPL_19
    72022U,	// NOOPL_1a
    72022U,	// NOOPL_1b
    72022U,	// NOOPL_1c
    72022U,	// NOOPL_1d
    72022U,	// NOOPL_1e
    39254U,	// NOOPW
    39254U,	// NOOPW_19
    39254U,	// NOOPW_1a
    39254U,	// NOOPW_1b
    39254U,	// NOOPW_1c
    39254U,	// NOOPW_1d
    39254U,	// NOOPW_1e
    41445U,	// NOT16m
    25061U,	// NOT16r
    74213U,	// NOT32m
    25061U,	// NOT32r
    90597U,	// NOT64m
    25061U,	// NOT64r
    106981U,	// NOT8m
    25061U,	// NOT8r
    26342U,	// OR16i16
    1088599U,	// OR16mi
    1088599U,	// OR16mi8
    1088599U,	// OR16mr
    34659415U,	// OR16ri
    34659415U,	// OR16ri8
    68213847U,	// OR16rm
    34659415U,	// OR16rr
    34626647U,	// OR16rr_REV
    26508U,	// OR32i32
    1121367U,	// OR32mi
    1121367U,	// OR32mi8
    1121367U,	// OR32mr
    1121367U,	// OR32mrLocked
    34659415U,	// OR32ri
    34659415U,	// OR32ri8
    101768279U,	// OR32rm
    34659415U,	// OR32rr
    34626647U,	// OR32rr_REV
    26647U,	// OR64i32
    1137751U,	// OR64mi32
    1137751U,	// OR64mi8
    1137751U,	// OR64mr
    34659415U,	// OR64ri32
    34659415U,	// OR64ri8
    135322711U,	// OR64rm
    34659415U,	// OR64rr
    34626647U,	// OR64rr_REV
    26230U,	// OR8i8
    1154135U,	// OR8mi
    1154135U,	// OR8mr
    34659415U,	// OR8ri
    34659415U,	// OR8ri8
    168877143U,	// OR8rm
    34659415U,	// OR8rr
    34626647U,	// OR8rr_REV
    202396361U,	// ORPDrm
    34624201U,	// ORPDrr
    202399608U,	// ORPSrm
    34627448U,	// ORPSrr
    9462357U,	// OUT16ir
    12244U,	// OUT16rr
    10510933U,	// OUT32ir
    12298U,	// OUT32rr
    12608085U,	// OUT8ir
    11614U,	// OUT8rr
    403512U,	// OUTSB
    419907U,	// OUTSL
    452686U,	// OUTSW
    437276148U,	// PABSBrm128
    336612852U,	// PABSBrr128
    437277727U,	// PABSDrm128
    336614431U,	// PABSDrr128
    437281801U,	// PABSWrm128
    336618505U,	// PABSWrr128
    403727184U,	// PACKSSDWrm
    34628432U,	// PACKSSDWrr
    403721907U,	// PACKSSWBrm
    34623155U,	// PACKSSWBrr
    403727195U,	// PACKUSDWrm
    34628443U,	// PACKUSDWrr
    403721918U,	// PACKUSWBrm
    34623166U,	// PACKUSWBrr
    403721623U,	// PADDBrm
    34622871U,	// PADDBrr
    403722135U,	// PADDDrm
    34623383U,	// PADDDrr
    403724892U,	// PADDQrm
    34626140U,	// PADDQrr
    403721733U,	// PADDSBrm
    34622981U,	// PADDSBrr
    403727418U,	// PADDSWrm
    34628666U,	// PADDSWrr
    403721775U,	// PADDUSBrm
    34623023U,	// PADDUSBrr
    403727491U,	// PADDUSWrm
    34628739U,	// PADDUSWrr
    403727166U,	// PADDWrm
    34628414U,	// PADDWrr
    2551209036U,	// PALIGNR128rm
    2182110284U,	// PALIGNR128rr
    403724465U,	// PANDNrm
    34625713U,	// PANDNrr
    403722310U,	// PANDrm
    34623558U,	// PANDrr
    11485U,	// PAUSE
    403721640U,	// PAVGBrm
    34622888U,	// PAVGBrr
    135286328U,	// PAVGUSBrm
    34623032U,	// PAVGUSBrr
    403727221U,	// PAVGWrm
    34628469U,	// PAVGWrr
    403721889U,	// PBLENDVBrm0
    34623137U,	// PBLENDVBrr0
    2551210822U,	// PBLENDWrmi
    2182112070U,	// PBLENDWrri
    2551208635U,	// PCLMULQDQrm
    2182109883U,	// PCLMULQDQrr
    403721688U,	// PCMPEQBrm
    34622936U,	// PCMPEQBrr
    403723053U,	// PCMPEQDrm
    34624301U,	// PCMPEQDrr
    403725128U,	// PCMPEQQrm
    34626376U,	// PCMPEQQrr
    403727326U,	// PCMPEQWrm
    34628574U,	// PCMPEQWrr
    0U,	// PCMPESTRIMEM
    0U,	// PCMPESTRIREG
    2584762271U,	// PCMPESTRIrm
    2484098975U,	// PCMPESTRIrr
    0U,	// PCMPESTRM128MEM
    0U,	// PCMPESTRM128REG
    2584762521U,	// PCMPESTRM128rm
    2484099225U,	// PCMPESTRM128rr
    403721816U,	// PCMPGTBrm
    34623064U,	// PCMPGTBrr
    403723504U,	// PCMPGTDrm
    34624752U,	// PCMPGTDrr
    403725189U,	// PCMPGTQrm
    34626437U,	// PCMPGTQrr
    403727517U,	// PCMPGTWrm
    34628765U,	// PCMPGTWrr
    0U,	// PCMPISTRIMEM
    0U,	// PCMPISTRIREG
    2584762283U,	// PCMPISTRIrm
    2484098987U,	// PCMPISTRIrr
    0U,	// PCMPISTRM128MEM
    0U,	// PCMPISTRM128REG
    2584762533U,	// PCMPISTRM128rm
    2484099237U,	// PCMPISTRM128rr
    2484099343U,	// PDEP32rm
    2484099343U,	// PDEP32rr
    2484099343U,	// PDEP64rm
    2484099343U,	// PDEP64rr
    2484101722U,	// PEXT32rm
    2484101722U,	// PEXT32rr
    2484101722U,	// PEXT64rm
    2484101722U,	// PEXT64rr
    2148634091U,	// PEXTRBmr
    2484096491U,	// PEXTRBrr
    2148602706U,	// PEXTRDmr
    2484097874U,	// PEXTRDrr
    2148621159U,	// PEXTRQmr
    2484099943U,	// PEXTRQrr
    2148574208U,	// PEXTRWmr
    2484102144U,	// PEXTRWri
    2484102144U,	// PEXTRWrr_REV
    370167777U,	// PF2IDrm
    336613345U,	// PF2IDrr
    370172826U,	// PF2IWrm
    336618394U,	// PF2IWrr
    135286472U,	// PFACCrm
    34623176U,	// PFACCrr
    135286649U,	// PFADDrm
    34623353U,	// PFADDrr
    135289607U,	// PFCMPEQrm
    34626311U,	// PFCMPEQrr
    135288361U,	// PFCMPGErm
    34625065U,	// PFCMPGErr
    135291329U,	// PFCMPGTrm
    34628033U,	// PFCMPGTrr
    135292163U,	// PFMAXrm
    34628867U,	// PFMAXrr
    135289024U,	// PFMINrm
    34625728U,	// PFMINrr
    135288930U,	// PFMULrm
    34625634U,	// PFMULrr
    135286479U,	// PFNACCrm
    34623183U,	// PFNACCrr
    135286487U,	// PFPNACCrm
    34623191U,	// PFPNACCrr
    135285648U,	// PFRCPIT1rm
    34622352U,	// PFRCPIT1rr
    135285744U,	// PFRCPIT2rm
    34622448U,	// PFRCPIT2rr
    370170113U,	// PFRCPrm
    336615681U,	// PFRCPrr
    135285658U,	// PFRSQIT1rm
    34622362U,	// PFRSQIT1rr
    370172420U,	// PFRSQRTrm
    336617988U,	// PFRSQRTrr
    135289898U,	// PFSUBRrm
    34626602U,	// PFSUBRrr
    135286409U,	// PFSUBrm
    34623113U,	// PFSUBrr
    403722126U,	// PHADDDrm
    34623374U,	// PHADDDrr
    403727408U,	// PHADDSWrm128
    34628656U,	// PHADDSWrr128
    403727157U,	// PHADDWrm
    34628405U,	// PHADDWrr
    437282009U,	// PHMINPOSUWrm128
    336618713U,	// PHMINPOSUWrr128
    403722067U,	// PHSUBDrm
    34623315U,	// PHSUBDrr
    403727389U,	// PHSUBSWrm128
    34628637U,	// PHSUBSWrr128
    403727103U,	// PHSUBWrm
    34628351U,	// PHSUBWrr
    370167761U,	// PI2FDrm
    336613329U,	// PI2FDrr
    370172773U,	// PI2FWrm
    336618341U,	// PI2FWrr
    2316324322U,	// PINSRBrm
    2182106594U,	// PINSRBrr
    2249216841U,	// PINSRDrm
    2182107977U,	// PINSRDrr
    2282773342U,	// PINSRQrm
    2182110046U,	// PINSRQrr
    2215666679U,	// PINSRWrmi
    2182112247U,	// PINSRWrri
    403727377U,	// PMADDUBSWrm128
    34628625U,	// PMADDUBSWrr128
    403723638U,	// PMADDWDrm
    34624886U,	// PMADDWDrr
    403721801U,	// PMAXSBrm
    34623049U,	// PMAXSBrr
    403723478U,	// PMAXSDrm
    34624726U,	// PMAXSDrr
    403727508U,	// PMAXSWrm
    34628756U,	// PMAXSWrr
    403721880U,	// PMAXUBrm
    34623128U,	// PMAXUBrr
    403723563U,	// PMAXUDrm
    34624811U,	// PMAXUDrr
    403727590U,	// PMAXUWrm
    34628838U,	// PMAXUWrr
    403721742U,	// PMINSBrm
    34622990U,	// PMINSBrr
    403723398U,	// PMINSDrm
    34624646U,	// PMINSDrr
    403727439U,	// PMINSWrm
    34628687U,	// PMINSWrr
    403721857U,	// PMINUBrm
    34623105U,	// PMINUBrr
    403723545U,	// PMINUDrm
    34624793U,	// PMINUDrr
    403727568U,	// PMINUWrm
    34628816U,	// PMINUWrr
    336612788U,	// PMOVMSKBrr
    303058788U,	// PMOVSXBDrm
    336613220U,	// PMOVSXBDrr
    470833669U,	// PMOVSXBQrm
    336615941U,	// PMOVSXBQrr
    370172688U,	// PMOVSXBWrm
    336618256U,	// PMOVSXBWrr
    370170610U,	// PMOVSXDQrm
    336616178U,	// PMOVSXDQrr
    370169295U,	// PMOVSXWDrm
    336614863U,	// PMOVSXWDrr
    303062018U,	// PMOVSXWQrm
    336616450U,	// PMOVSXWQrr
    303058799U,	// PMOVZXBDrm
    336613231U,	// PMOVZXBDrr
    470833680U,	// PMOVZXBQrm
    336615952U,	// PMOVZXBQrr
    370172699U,	// PMOVZXBWrm
    336618267U,	// PMOVZXBWrr
    370170621U,	// PMOVZXDQrm
    336616189U,	// PMOVZXDQrr
    370169306U,	// PMOVZXWDrm
    336614874U,	// PMOVZXWDrr
    303062029U,	// PMOVZXWQrm
    336616461U,	// PMOVZXWQrr
    403724952U,	// PMULDQrm
    34626200U,	// PMULDQrr
    403727462U,	// PMULHRSWrm128
    34628710U,	// PMULHRSWrr128
    135291885U,	// PMULHRWrm
    34628589U,	// PMULHRWrr
    403727549U,	// PMULHUWrm
    34628797U,	// PMULHUWrr
    403727250U,	// PMULHWrm
    34628498U,	// PMULHWrr
    403722268U,	// PMULLDrm
    34623516U,	// PMULLDrr
    403727292U,	// PMULLWrm
    34628540U,	// PMULLWrr
    403725032U,	// PMULUDQrm
    34626280U,	// PMULUDQrr
    22881U,	// POP16r
    39265U,	// POP16rmm
    22881U,	// POP16rmr
    22881U,	// POP32r
    72033U,	// POP32rmm
    22881U,	// POP32rmr
    22881U,	// POP64r
    88417U,	// POP64rmm
    22881U,	// POP64rmr
    12198U,	// POPA16
    11632U,	// POPA32
    470835658U,	// POPCNT16rm
    336617930U,	// POPCNT16rr
    303063498U,	// POPCNT32rm
    336617930U,	// POPCNT32rr
    370172362U,	// POPCNT64rm
    336617930U,	// POPCNT64rr
    11940U,	// POPDS16
    11940U,	// POPDS32
    11955U,	// POPES16
    11955U,	// POPES32
    11551U,	// POPF16
    11338U,	// POPF32
    11823U,	// POPF64
    11970U,	// POPFS16
    11970U,	// POPFS32
    11970U,	// POPFS64
    11985U,	// POPGS16
    11985U,	// POPGS32
    11985U,	// POPGS64
    12082U,	// POPSS16
    12082U,	// POPSS32
    403725398U,	// PORrm
    34626646U,	// PORrr
    104199U,	// PREFETCH
    101718U,	// PREFETCHNTA
    101218U,	// PREFETCHT0
    101252U,	// PREFETCHT1
    101348U,	// PREFETCHT2
    107388U,	// PREFETCHW
    403727040U,	// PSADBWrm
    34628288U,	// PSADBWrr
    403721631U,	// PSHUFBrm
    34622879U,	// PSHUFBrr
    2584760281U,	// PSHUFDmi
    2484096985U,	// PSHUFDri
    2584765320U,	// PSHUFHWmi
    2484102024U,	// PSHUFHWri
    2584765346U,	// PSHUFLWmi
    2484102050U,	// PSHUFLWri
    403721679U,	// PSIGNBrm
    34622927U,	// PSIGNBrr
    403722325U,	// PSIGNDrm
    34623573U,	// PSIGNDrr
    403727317U,	// PSIGNWrm
    34628565U,	// PSIGNWrr
    34626182U,	// PSLLDQri
    34623508U,	// PSLLDri
    403722260U,	// PSLLDrm
    34623508U,	// PSLLDrr
    34626336U,	// PSLLQri
    403725088U,	// PSLLQrm
    34626336U,	// PSLLQrr
    34628532U,	// PSLLWri
    403727284U,	// PSLLWrm
    34628532U,	// PSLLWrr
    34623286U,	// PSRADri
    403722038U,	// PSRADrm
    34623286U,	// PSRADrr
    34628269U,	// PSRAWri
    403727021U,	// PSRAWrm
    34628269U,	// PSRAWrr
    34626191U,	// PSRLDQri
    34623525U,	// PSRLDri
    403722277U,	// PSRLDrm
    34623525U,	// PSRLDrr
    34626344U,	// PSRLQri
    403725096U,	// PSRLQrm
    34626344U,	// PSRLQrr
    34628549U,	// PSRLWri
    403727301U,	// PSRLWrm
    34628549U,	// PSRLWrr
    403721615U,	// PSUBBrm
    34622863U,	// PSUBBrr
    403722076U,	// PSUBDrm
    34623324U,	// PSUBDrr
    403724797U,	// PSUBQrm
    34626045U,	// PSUBQrr
    403721724U,	// PSUBSBrm
    34622972U,	// PSUBSBrr
    403727399U,	// PSUBSWrm
    34628647U,	// PSUBSWrr
    403721765U,	// PSUBUSBrm
    34623013U,	// PSUBUSBrr
    403727481U,	// PSUBUSWrm
    34628729U,	// PSUBUSWrr
    403727112U,	// PSUBWrm
    34628360U,	// PSUBWrr
    370168219U,	// PSWAPDrm
    336613787U,	// PSWAPDrr
    537944616U,	// PTESTrm
    336618024U,	// PTESTrr
    403727068U,	// PUNPCKHBWrm
    34628316U,	// PUNPCKHBWrr
    403724910U,	// PUNPCKHDQrm
    34626158U,	// PUNPCKHDQrr
    403724961U,	// PUNPCKHQDQrm
    34626209U,	// PUNPCKHQDQrr
    403723648U,	// PUNPCKHWDrm
    34624896U,	// PUNPCKHWDrr
    403727080U,	// PUNPCKLBWrm
    34628328U,	// PUNPCKLBWrr
    403724922U,	// PUNPCKLDQrm
    34626170U,	// PUNPCKLDQrr
    403724974U,	// PUNPCKLQDQrm
    34626222U,	// PUNPCKLQDQrr
    403723660U,	// PUNPCKLWDrm
    34624908U,	// PUNPCKLWDrr
    22338U,	// PUSH16i8
    22338U,	// PUSH16r
    38722U,	// PUSH16rmm
    22338U,	// PUSH16rmr
    22338U,	// PUSH32i8
    22338U,	// PUSH32r
    71490U,	// PUSH32rmm
    22338U,	// PUSH32rmr
    22338U,	// PUSH64i16
    22338U,	// PUSH64i32
    22338U,	// PUSH64i8
    22338U,	// PUSH64r
    87874U,	// PUSH64rmm
    22338U,	// PUSH64rmr
    12191U,	// PUSHA16
    11625U,	// PUSHA32
    11924U,	// PUSHCS16
    11924U,	// PUSHCS32
    11932U,	// PUSHDS16
    11932U,	// PUSHDS32
    11947U,	// PUSHES16
    11947U,	// PUSHES32
    11545U,	// PUSHF16
    11331U,	// PUSHF32
    11816U,	// PUSHF64
    11962U,	// PUSHFS16
    11962U,	// PUSHFS32
    11962U,	// PUSHFS64
    11977U,	// PUSHGS16
    11977U,	// PUSHGS32
    11977U,	// PUSHGS64
    12074U,	// PUSHSS16
    12074U,	// PUSHSS32
    22338U,	// PUSHi16
    22338U,	// PUSHi32
    403725426U,	// PXORrm
    34626674U,	// PXORrr
    13670414U,	// RCL16m1
    14718990U,	// RCL16mCL
    1087502U,	// RCL16mi
    13654030U,	// RCL16r1
    14702606U,	// RCL16rCL
    34625550U,	// RCL16ri
    13703182U,	// RCL32m1
    14751758U,	// RCL32mCL
    1120270U,	// RCL32mi
    13654030U,	// RCL32r1
    14702606U,	// RCL32rCL
    34625550U,	// RCL32ri
    13719566U,	// RCL64m1
    14768142U,	// RCL64mCL
    1136654U,	// RCL64mi
    13654030U,	// RCL64r1
    14702606U,	// RCL64rCL
    34625550U,	// RCL64ri
    13735950U,	// RCL8m1
    14784526U,	// RCL8mCL
    1153038U,	// RCL8mi
    13654030U,	// RCL8r1
    14702606U,	// RCL8rCL
    34625550U,	// RCL8ri
    537943893U,	// RCPPSm
    537943893U,	// RCPPSm_Int
    336617301U,	// RCPPSr
    336617301U,	// RCPPSr_Int
    605053224U,	// RCPSSm
    269508904U,	// RCPSSm_Int
    336617768U,	// RCPSSr
    34627880U,	// RCPSSr_Int
    13671482U,	// RCR16m1
    14720058U,	// RCR16mCL
    1088570U,	// RCR16mi
    13655098U,	// RCR16r1
    14703674U,	// RCR16rCL
    34626618U,	// RCR16ri
    13704250U,	// RCR32m1
    14752826U,	// RCR32mCL
    1121338U,	// RCR32mi
    13655098U,	// RCR32r1
    14703674U,	// RCR32rCL
    34626618U,	// RCR32ri
    13720634U,	// RCR64m1
    14769210U,	// RCR64mCL
    1137722U,	// RCR64mi
    13655098U,	// RCR64r1
    14703674U,	// RCR64rCL
    34626618U,	// RCR64ri
    13737018U,	// RCR8m1
    14785594U,	// RCR8mCL
    1154106U,	// RCR8mi
    13655098U,	// RCR8r1
    14703674U,	// RCR8rCL
    34626618U,	// RCR8ri
    22140U,	// RDFSBASE
    22140U,	// RDFSBASE64
    22160U,	// RDGSBASE
    22160U,	// RDGSBASE64
    11889U,	// RDMSR
    11308U,	// RDPMC
    20556U,	// RDRAND16r
    20556U,	// RDRAND32r
    20556U,	// RDRAND64r
    20425U,	// RDSEED16r
    20425U,	// RDSEED32r
    20425U,	// RDSEED64r
    11321U,	// RDTSC
    11754U,	// RDTSCP
    10835U,	// RELEASE_MOV16mr
    10835U,	// RELEASE_MOV32mr
    10835U,	// RELEASE_MOV64mr
    10835U,	// RELEASE_MOV8mr
    11449U,	// REPNE_PREFIX
    11252U,	// REP_MOVSB_32
    11252U,	// REP_MOVSB_64
    11369U,	// REP_MOVSD_32
    11369U,	// REP_MOVSD_64
    11845U,	// REP_MOVSQ_64
    12218U,	// REP_MOVSW_32
    12218U,	// REP_MOVSW_64
    11761U,	// REP_PREFIX
    11242U,	// REP_STOSB_32
    11242U,	// REP_STOSB_64
    11359U,	// REP_STOSD_32
    11359U,	// REP_STOSD_64
    11835U,	// REP_STOSQ_64
    12208U,	// REP_STOSW_32
    12208U,	// REP_STOSW_64
    25020U,	// RETIL
    25020U,	// RETIQ
    25020U,	// RETIW
    12110U,	// RETL
    12110U,	// RETQ
    12110U,	// RETW
    11078U,	// REX64_PREFIX
    13670453U,	// ROL16m1
    14719029U,	// ROL16mCL
    1087541U,	// ROL16mi
    13654069U,	// ROL16r1
    14702645U,	// ROL16rCL
    34625589U,	// ROL16ri
    13703221U,	// ROL32m1
    14751797U,	// ROL32mCL
    1120309U,	// ROL32mi
    13654069U,	// ROL32r1
    14702645U,	// ROL32rCL
    34625589U,	// ROL32ri
    13719605U,	// ROL64m1
    14768181U,	// ROL64mCL
    1136693U,	// ROL64mi
    13654069U,	// ROL64r1
    14702645U,	// ROL64rCL
    34625589U,	// ROL64ri
    13735989U,	// ROL8m1
    14784565U,	// ROL8mCL
    1153077U,	// ROL8mi
    13654069U,	// ROL8r1
    14702645U,	// ROL8rCL
    34625589U,	// ROL8ri
    13671515U,	// ROR16m1
    14720091U,	// ROR16mCL
    1088603U,	// ROR16mi
    13655131U,	// ROR16r1
    14703707U,	// ROR16rCL
    34626651U,	// ROR16ri
    13704283U,	// ROR32m1
    14752859U,	// ROR32mCL
    1121371U,	// ROR32mi
    13655131U,	// ROR32r1
    14703707U,	// ROR32rCL
    34626651U,	// ROR32ri
    13720667U,	// ROR64m1
    14769243U,	// ROR64mCL
    1137755U,	// ROR64mi
    13655131U,	// ROR64r1
    14703707U,	// ROR64rCL
    34626651U,	// ROR64ri
    13737051U,	// ROR8m1
    14785627U,	// ROR8mCL
    1154139U,	// ROR8mi
    13655131U,	// ROR8r1
    14703707U,	// ROR8rCL
    34626651U,	// ROR8ri
    2450548039U,	// RORX32mi
    2484102471U,	// RORX32ri
    2517656903U,	// RORX64mi
    2484102471U,	// RORX64ri
    2685424167U,	// ROUNDPDm
    2484097575U,	// ROUNDPDr
    2685427394U,	// ROUNDPSm
    2484100802U,	// ROUNDPSr
    2383434849U,	// ROUNDSDm
    2182108257U,	// ROUNDSDr
    2182108257U,	// ROUNDSDr_Int
    2416992507U,	// ROUNDSSm
    2182111483U,	// ROUNDSSr
    2182111483U,	// ROUNDSSr_Int
    11696U,	// RSM
    537943976U,	// RSQRTPSm
    537943976U,	// RSQRTPSm_Int
    336617384U,	// RSQRTPSr
    336617384U,	// RSQRTPSr_Int
    605053249U,	// RSQRTSSm
    269508929U,	// RSQRTSSm_Int
    336617793U,	// RSQRTSSr
    34627905U,	// RSQRTSSr_Int
    11540U,	// SAHF
    13670409U,	// SAL16m1
    14718985U,	// SAL16mCL
    1087497U,	// SAL16mi
    13654025U,	// SAL16r1
    14702601U,	// SAL16rCL
    34625545U,	// SAL16ri
    13703177U,	// SAL32m1
    14751753U,	// SAL32mCL
    1120265U,	// SAL32mi
    13654025U,	// SAL32r1
    14702601U,	// SAL32rCL
    34625545U,	// SAL32ri
    13719561U,	// SAL64m1
    14768137U,	// SAL64mCL
    1136649U,	// SAL64mi
    13654025U,	// SAL64r1
    14702601U,	// SAL64rCL
    34625545U,	// SAL64ri
    13735945U,	// SAL8m1
    14784521U,	// SAL8mCL
    1153033U,	// SAL8mi
    13654025U,	// SAL8r1
    14702601U,	// SAL8rCL
    34625545U,	// SAL8ri
    11295U,	// SALC
    13671461U,	// SAR16m1
    14720037U,	// SAR16mCL
    1088549U,	// SAR16mi
    13655077U,	// SAR16r1
    14703653U,	// SAR16rCL
    34626597U,	// SAR16ri
    13704229U,	// SAR32m1
    14752805U,	// SAR32mCL
    1121317U,	// SAR32mi
    13655077U,	// SAR32r1
    14703653U,	// SAR32rCL
    34626597U,	// SAR32ri
    13720613U,	// SAR64m1
    14769189U,	// SAR64mCL
    1137701U,	// SAR64mi
    13655077U,	// SAR64r1
    14703653U,	// SAR64rCL
    34626597U,	// SAR64ri
    13736997U,	// SAR8m1
    14785573U,	// SAR8mCL
    1154085U,	// SAR8mi
    13655077U,	// SAR8r1
    14703653U,	// SAR8rCL
    34626597U,	// SAR8ri
    2450548027U,	// SARX32rm
    2484102459U,	// SARX32rr
    2517656891U,	// SARX64rm
    2484102459U,	// SARX64rr
    26269U,	// SBB16i16
    1084809U,	// SBB16mi
    1084809U,	// SBB16mi8
    1084809U,	// SBB16mr
    34655625U,	// SBB16ri
    34655625U,	// SBB16ri8
    68210057U,	// SBB16rm
    34655625U,	// SBB16rr
    34622857U,	// SBB16rr_REV
    26403U,	// SBB32i32
    1117577U,	// SBB32mi
    1117577U,	// SBB32mi8
    1117577U,	// SBB32mr
    34655625U,	// SBB32ri
    34655625U,	// SBB32ri8
    101764489U,	// SBB32rm
    34655625U,	// SBB32rr
    34622857U,	// SBB32rr_REV
    26551U,	// SBB64i32
    1133961U,	// SBB64mi32
    1133961U,	// SBB64mi8
    1133961U,	// SBB64mr
    34655625U,	// SBB64ri32
    34655625U,	// SBB64ri8
    135318921U,	// SBB64rm
    34655625U,	// SBB64rr
    34622857U,	// SBB64rr_REV
    26145U,	// SBB8i8
    1150345U,	// SBB8mi
    1150345U,	// SBB8mr
    34655625U,	// SBB8ri
    168873353U,	// SBB8rm
    34655625U,	// SBB8rr
    34622857U,	// SBB8rr_REV
    304682U,	// SCASB
    321365U,	// SCASL
    616446U,	// SCASQ
    337677U,	// SCASW
    12004U,	// SEG_ALLOCA_32
    12004U,	// SEG_ALLOCA_64
    11505U,	// SEH_EndPrologue
    11491U,	// SEH_Epilogue
    26802U,	// SEH_PushFrame
    26847U,	// SEH_PushReg
    336619729U,	// SEH_SaveReg
    336619643U,	// SEH_SaveXMM
    336619714U,	// SEH_SetFrame
    26785U,	// SEH_StackAlloc
    103921U,	// SETAEm
    22001U,	// SETAEr
    101712U,	// SETAm
    19792U,	// SETAr
    103941U,	// SETBEm
    22021U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    101969U,	// SETBm
    20049U,	// SETBr
    104100U,	// SETEm
    22180U,	// SETEr
    103986U,	// SETGEm
    22066U,	// SETGEr
    104186U,	// SETGm
    22266U,	// SETGr
    104010U,	// SETLEm
    22090U,	// SETLEr
    104540U,	// SETLm
    22620U,	// SETLr
    104038U,	// SETNEm
    22118U,	// SETNEr
    104663U,	// SETNOm
    22743U,	// SETNOr
    104775U,	// SETNPm
    22855U,	// SETNPr
    105709U,	// SETNSm
    23789U,	// SETNSr
    104678U,	// SETOm
    22758U,	// SETOr
    104822U,	// SETPm
    22902U,	// SETPr
    106887U,	// SETSm
    24967U,	// SETSr
    11421U,	// SFENCE
    287134U,	// SGDT16m
    287134U,	// SGDT32m
    287134U,	// SGDT64m
    403721070U,	// SHA1MSG1rm
    34622318U,	// SHA1MSG1rr
    403721153U,	// SHA1MSG2rm
    34622401U,	// SHA1MSG2rr
    403723955U,	// SHA1NEXTErm
    34625203U,	// SHA1NEXTErr
    2551204913U,	// SHA1RNDS4rmi
    2182106161U,	// SHA1RNDS4rri
    403721080U,	// SHA256MSG1rm
    34622328U,	// SHA256MSG1rr
    403721163U,	// SHA256MSG2rm
    34622411U,	// SHA256MSG2rr
    403721175U,	// SHA256RNDS2rm
    34622423U,	// SHA256RNDS2rr
    13670419U,	// SHL16m1
    14718995U,	// SHL16mCL
    1087507U,	// SHL16mi
    13654035U,	// SHL16r1
    14702611U,	// SHL16rCL
    34625555U,	// SHL16ri
    13703187U,	// SHL32m1
    14751763U,	// SHL32mCL
    1120275U,	// SHL32mi
    13654035U,	// SHL32r1
    14702611U,	// SHL32rCL
    34625555U,	// SHL32ri
    13719571U,	// SHL64m1
    14768147U,	// SHL64mCL
    1136659U,	// SHL64mi
    13654035U,	// SHL64r1
    14702611U,	// SHL64rCL
    34625555U,	// SHL64ri
    13735955U,	// SHL8m1
    14784531U,	// SHL8mCL
    1153043U,	// SHL8mi
    13654035U,	// SHL8r1
    14702611U,	// SHL8rCL
    34625555U,	// SHL8ri
    2148569095U,	// SHLD16mrCL
    2148569095U,	// SHLD16mri8
    2182107143U,	// SHLD16rrCL
    2182107143U,	// SHLD16rri8
    2148601863U,	// SHLD32mrCL
    2148601863U,	// SHLD32mri8
    2182107143U,	// SHLD32rrCL
    2182107143U,	// SHLD32rri8
    2148618247U,	// SHLD64mrCL
    2148618247U,	// SHLD64mri8
    2182107143U,	// SHLD64rrCL
    2182107143U,	// SHLD64rri8
    2450547984U,	// SHLX32rm
    2484102416U,	// SHLX32rr
    2517656848U,	// SHLX64rm
    2484102416U,	// SHLX64rr
    13671494U,	// SHR16m1
    14720070U,	// SHR16mCL
    1088582U,	// SHR16mi
    13655110U,	// SHR16r1
    14703686U,	// SHR16rCL
    34626630U,	// SHR16ri
    13704262U,	// SHR32m1
    14752838U,	// SHR32mCL
    1121350U,	// SHR32mi
    13655110U,	// SHR32r1
    14703686U,	// SHR32rCL
    34626630U,	// SHR32ri
    13720646U,	// SHR64m1
    14769222U,	// SHR64mCL
    1137734U,	// SHR64mi
    13655110U,	// SHR64r1
    14703686U,	// SHR64rCL
    34626630U,	// SHR64ri
    13737030U,	// SHR8m1
    14785606U,	// SHR8mCL
    1154118U,	// SHR8mi
    13655110U,	// SHR8r1
    14703686U,	// SHR8rCL
    34626630U,	// SHR8ri
    2148569922U,	// SHRD16mrCL
    2148569922U,	// SHRD16mri8
    2182107970U,	// SHRD16rrCL
    2182107970U,	// SHRD16rri8
    2148602690U,	// SHRD32mrCL
    2148602690U,	// SHRD32mri8
    2182107970U,	// SHRD32rrCL
    2182107970U,	// SHRD32rri8
    2148619074U,	// SHRD64mrCL
    2148619074U,	// SHRD64mri8
    2182107970U,	// SHRD64rrCL
    2182107970U,	// SHRD64rri8
    2450548033U,	// SHRX32rm
    2484102465U,	// SHRX32rr
    2517656897U,	// SHRX64rm
    2484102465U,	// SHRX64rr
    2349879869U,	// SHUFPDrmi
    2182107709U,	// SHUFPDrri
    2349883096U,	// SHUFPSrmi
    2182110936U,	// SHUFPSrri
    287146U,	// SIDT16m
    287146U,	// SIDT32m
    287146U,	// SIDT64m
    11713U,	// SIN_F
    0U,	// SIN_Fp32
    0U,	// SIN_Fp64
    0U,	// SIN_Fp80
    12287U,	// SKINIT
    41398U,	// SLDT16m
    25014U,	// SLDT16r
    25014U,	// SLDT32r
    41398U,	// SLDT64m
    25014U,	// SLDT64r
    42056U,	// SMSW16m
    25672U,	// SMSW16r
    25672U,	// SMSW32r
    25672U,	// SMSW64r
    537940706U,	// SQRTPDm
    336614114U,	// SQRTPDr
    537943977U,	// SQRTPSm
    336617385U,	// SQRTPSr
    571495599U,	// SQRTSDm
    571495599U,	// SQRTSDm_Int
    336614575U,	// SQRTSDr
    336614575U,	// SQRTSDr_Int
    605053250U,	// SQRTSSm
    605053250U,	// SQRTSSm_Int
    336617794U,	// SQRTSSr
    336617794U,	// SQRTSSr_Int
    12154U,	// SQRT_F
    0U,	// SQRT_Fp32
    0U,	// SQRT_Fp64
    0U,	// SQRT_Fp80
    11273U,	// STAC
    11327U,	// STC
    11385U,	// STD
    11575U,	// STGI
    11590U,	// STI
    72846U,	// STMXCSR
    12881430U,	// STOSB
    10802326U,	// STOSL
    12147567U,	// STOSQ
    9774167U,	// STOSW
    23719U,	// STR16r
    23719U,	// STR32r
    23719U,	// STR64r
    40103U,	// STRm
    123439U,	// ST_F32m
    139823U,	// ST_F64m
    26100U,	// ST_FCOMPST0r
    26100U,	// ST_FCOMPST0r_alt
    26087U,	// ST_FCOMST0r
    121219U,	// ST_FP32m
    137603U,	// ST_FP64m
    383363U,	// ST_FP80m
    2119188U,	// ST_FPNCEST0r
    2120067U,	// ST_FPST0r
    2120067U,	// ST_FPST0r_alt
    22915U,	// ST_FPrr
    26074U,	// ST_FXCHST0r
    26074U,	// ST_FXCHST0r_alt
    0U,	// ST_Fp32m
    0U,	// ST_Fp64m
    0U,	// ST_Fp64m32
    0U,	// ST_Fp80m32
    0U,	// ST_Fp80m64
    0U,	// ST_FpP32m
    0U,	// ST_FpP64m
    0U,	// ST_FpP64m32
    0U,	// ST_FpP80m
    0U,	// ST_FpP80m32
    0U,	// ST_FpP80m64
    25135U,	// ST_Frr
    26278U,	// SUB16i16
    1085067U,	// SUB16mi
    1085067U,	// SUB16mi8
    1085067U,	// SUB16mr
    34655883U,	// SUB16ri
    34655883U,	// SUB16ri8
    68210315U,	// SUB16rm
    34655883U,	// SUB16rr
    34623115U,	// SUB16rr_REV
    26413U,	// SUB32i32
    1117835U,	// SUB32mi
    1117835U,	// SUB32mi8
    1117835U,	// SUB32mr
    34655883U,	// SUB32ri
    34655883U,	// SUB32ri8
    101764747U,	// SUB32rm
    34655883U,	// SUB32rr
    34623115U,	// SUB32rr_REV
    26561U,	// SUB64i32
    1134219U,	// SUB64mi32
    1134219U,	// SUB64mi8
    1134219U,	// SUB64mr
    34655883U,	// SUB64ri32
    34655883U,	// SUB64ri8
    135319179U,	// SUB64rm
    34655883U,	// SUB64rr
    34623115U,	// SUB64rr_REV
    26176U,	// SUB8i8
    1150603U,	// SUB8mi
    1150603U,	// SUB8mr
    34655883U,	// SUB8ri
    34655883U,	// SUB8ri8
    168873611U,	// SUB8rm
    34655883U,	// SUB8rr
    34623115U,	// SUB8rr_REV
    202396073U,	// SUBPDrm
    34623913U,	// SUBPDrr
    202399300U,	// SUBPSrm
    34627140U,	// SUBPSrr
    121899U,	// SUBR_F32m
    138283U,	// SUBR_F64m
    39986U,	// SUBR_FI16m
    72754U,	// SUBR_FI32m
    22886U,	// SUBR_FPrST0
    23595U,	// SUBR_FST0r
    0U,	// SUBR_Fp32m
    0U,	// SUBR_Fp64m
    0U,	// SUBR_Fp64m32
    0U,	// SUBR_Fp80m32
    0U,	// SUBR_Fp80m64
    0U,	// SUBR_FpI16m32
    0U,	// SUBR_FpI16m64
    0U,	// SUBR_FpI16m80
    0U,	// SUBR_FpI32m32
    0U,	// SUBR_FpI32m64
    0U,	// SUBR_FpI32m80
    2120747U,	// SUBR_FrST0
    235951145U,	// SUBSDrm
    235951145U,	// SUBSDrm_Int
    34624553U,	// SUBSDrr
    34624553U,	// SUBSDrr_Int
    269508803U,	// SUBSSrm
    269508803U,	// SUBSSrm_Int
    34627779U,	// SUBSSrr
    34627779U,	// SUBSSrr_Int
    118410U,	// SUB_F32m
    134794U,	// SUB_F64m
    36496U,	// SUB_FI16m
    69264U,	// SUB_FI32m
    22778U,	// SUB_FPrST0
    20106U,	// SUB_FST0r
    0U,	// SUB_Fp32
    0U,	// SUB_Fp32m
    0U,	// SUB_Fp64
    0U,	// SUB_Fp64m
    0U,	// SUB_Fp64m32
    0U,	// SUB_Fp80
    0U,	// SUB_Fp80m32
    0U,	// SUB_Fp80m64
    0U,	// SUB_FpI16m32
    0U,	// SUB_FpI16m64
    0U,	// SUB_FpI16m80
    0U,	// SUB_FpI32m32
    0U,	// SUB_FpI32m64
    0U,	// SUB_FpI32m80
    2117258U,	// SUB_FrST0
    11992U,	// SWAPGS
    11653U,	// SYSCALL
    11872U,	// SYSENTER
    12134U,	// SYSEXIT
    12134U,	// SYSEXIT64
    12114U,	// SYSRET
    12114U,	// SYSRET64
    303058684U,	// T1MSKC32rm
    336613116U,	// T1MSKC32rr
    370167548U,	// T1MSKC64rm
    336613116U,	// T1MSKC64rr
    15882534U,	// TAILJMPd
    15882534U,	// TAILJMPd64
    15800614U,	// TAILJMPm
    15816998U,	// TAILJMPm64
    0U,	// TAILJMPr
    15751462U,	// TAILJMPr64
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    26362U,	// TEST16i16
    1090089U,	// TEST16mi
    1090089U,	// TEST16mi_alt
    336618025U,	// TEST16ri
    336618025U,	// TEST16ri_alt
    1090089U,	// TEST16rm
    336618025U,	// TEST16rr
    26530U,	// TEST32i32
    1122857U,	// TEST32mi
    1122857U,	// TEST32mi_alt
    336618025U,	// TEST32ri
    336618025U,	// TEST32ri_alt
    1122857U,	// TEST32rm
    336618025U,	// TEST32rr
    26669U,	// TEST64i32
    1139241U,	// TEST64mi32
    1139241U,	// TEST64mi32_alt
    336618025U,	// TEST64ri32
    336618025U,	// TEST64ri32_alt
    1139241U,	// TEST64rm
    336618025U,	// TEST64rr
    26250U,	// TEST8i8
    1155625U,	// TEST8mi
    1155625U,	// TEST8mi_alt
    336618025U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    336618025U,	// TEST8ri_alt
    1155625U,	// TEST8rm
    336618025U,	// TEST8rr
    10930U,	// TLSCall_32
    11034U,	// TLSCall_64
    10943U,	// TLS_addr32
    11047U,	// TLS_addr64
    10956U,	// TLS_base_addr32
    11060U,	// TLS_base_addr64
    10974U,	// TRAP
    12166U,	// TST_F
    0U,	// TST_Fp32
    0U,	// TST_Fp64
    0U,	// TST_Fp80
    470835673U,	// TZCNT16rm
    336617945U,	// TZCNT16rr
    303063513U,	// TZCNT32rm
    336617945U,	// TZCNT32rr
    370172377U,	// TZCNT64rm
    336617945U,	// TZCNT64rr
    303060994U,	// TZMSK32rm
    336615426U,	// TZMSK32rr
    370169858U,	// TZMSK64rm
    336615426U,	// TZMSK64rr
    571495531U,	// UCOMISDrm
    336614507U,	// UCOMISDrr
    605053189U,	// UCOMISSrm
    336617733U,	// UCOMISSrr
    22421U,	// UCOM_FIPr
    22363U,	// UCOM_FIr
    11788U,	// UCOM_FPPr
    22842U,	// UCOM_FPr
    0U,	// UCOM_FpIr32
    0U,	// UCOM_FpIr64
    0U,	// UCOM_FpIr80
    0U,	// UCOM_Fpr32
    0U,	// UCOM_Fpr64
    0U,	// UCOM_Fpr80
    22665U,	// UCOM_Fr
    11207U,	// UD2B
    202396230U,	// UNPCKHPDrm
    34624070U,	// UNPCKHPDrr
    202399457U,	// UNPCKHPSrm
    34627297U,	// UNPCKHPSrr
    202396272U,	// UNPCKLPDrm
    34624112U,	// UNPCKLPDrr
    202399519U,	// UNPCKLPSrm
    34627359U,	// UNPCKLPSrr
    2651875417U,	// VAARG_64
    2484097548U,	// VADDPDYrm
    2484097548U,	// VADDPDYrr
    2484093636U,	// VADDPDZrm
    2484093636U,	// VADDPDZrmb
    352338628U,	// VADDPDZrmbk
    2499822276U,	// VADDPDZrmbkz
    352342540U,	// VADDPDZrmk
    2499826188U,	// VADDPDZrmkz
    2484093636U,	// VADDPDZrr
    352338628U,	// VADDPDZrrk
    2499822276U,	// VADDPDZrrkz
    2484097548U,	// VADDPDrm
    2484097548U,	// VADDPDrr
    2484100775U,	// VADDPSYrm
    2484100775U,	// VADDPSYrr
    2484095340U,	// VADDPSZrm
    2484095340U,	// VADDPSZrmb
    352340332U,	// VADDPSZrmbk
    2499823980U,	// VADDPSZrmbkz
    352345767U,	// VADDPSZrmk
    2499829415U,	// VADDPSZrmkz
    2484095340U,	// VADDPSZrr
    352340332U,	// VADDPSZrrk
    2499823980U,	// VADDPSZrrkz
    2484100775U,	// VADDPSrm
    2484100775U,	// VADDPSrr
    2484098136U,	// VADDSDZrm
    2484098136U,	// VADDSDZrr
    2484098136U,	// VADDSDrm
    2484098136U,	// VADDSDrm_Int
    2484098136U,	// VADDSDrr
    2484098136U,	// VADDSDrr_Int
    2484101362U,	// VADDSSZrm
    2484101362U,	// VADDSSZrr
    2484101362U,	// VADDSSrm
    2484101362U,	// VADDSSrm_Int
    2484101362U,	// VADDSSrr
    2484101362U,	// VADDSSrr_Int
    2484097456U,	// VADDSUBPDYrm
    2484097456U,	// VADDSUBPDYrr
    2484097456U,	// VADDSUBPDrm
    2484097456U,	// VADDSUBPDrr
    2484100683U,	// VADDSUBPSYrm
    2484100683U,	// VADDSUBPSYrr
    2484100683U,	// VADDSUBPSrm
    2484100683U,	// VADDSUBPSrr
    2484101645U,	// VAESDECLASTrm
    2484101645U,	// VAESDECLASTrr
    2484096741U,	// VAESDECrm
    2484096741U,	// VAESDECrr
    2484101658U,	// VAESENCLASTrm
    2484101658U,	// VAESENCLASTrr
    2484096781U,	// VAESENCrm
    2484096781U,	// VAESENCrr
    437276420U,	// VAESIMCrm
    336613124U,	// VAESIMCrr
    2584764986U,	// VAESKEYGENASSIST128rm
    2484101690U,	// VAESKEYGENASSIST128rr
    2484093324U,	// VALIGNDrmi
    2484093324U,	// VALIGNDrri
    50348428U,	// VALIGNDrrik
    2499821964U,	// VALIGNDrrikz
    2484094801U,	// VALIGNQrmi
    2484094801U,	// VALIGNQrri
    50349905U,	// VALIGNQrrik
    2499823441U,	// VALIGNQrrikz
    2484097692U,	// VANDNPDYrm
    2484097692U,	// VANDNPDYrr
    2484097692U,	// VANDNPDrm
    2484097692U,	// VANDNPDrr
    2484100931U,	// VANDNPSYrm
    2484100931U,	// VANDNPSYrr
    2484100931U,	// VANDNPSrm
    2484100931U,	// VANDNPSrr
    2484097556U,	// VANDPDYrm
    2484097556U,	// VANDPDYrr
    2484097556U,	// VANDPDrm
    2484097556U,	// VANDPDrr
    2484100783U,	// VANDPSYrm
    2484100783U,	// VANDPSYrr
    2484100783U,	// VANDPSrm
    2484100783U,	// VANDPSrr
    2484103305U,	// VASTART_SAVE_XMM_REGS
    352338709U,	// VBLENDMPDZrm
    352338709U,	// VBLENDMPDZrr
    352340413U,	// VBLENDMPSZrm
    352340413U,	// VBLENDMPSZrr
    2484097564U,	// VBLENDPDYrmi
    2484097564U,	// VBLENDPDYrri
    2484097564U,	// VBLENDPDrmi
    2484097564U,	// VBLENDPDrri
    2484100791U,	// VBLENDPSYrmi
    2484100791U,	// VBLENDPSYrri
    2484100791U,	// VBLENDPSrmi
    2484100791U,	// VBLENDPSrri
    2484097788U,	// VBLENDVPDYrm
    2484097788U,	// VBLENDVPDYrr
    2484097788U,	// VBLENDVPDrm
    2484097788U,	// VBLENDVPDrr
    2484101068U,	// VBLENDVPSYrm
    2484101068U,	// VBLENDVPSYrr
    2484101068U,	// VBLENDVPSrm
    2484101068U,	// VBLENDVPSrr
    537939170U,	// VBROADCASTF128
    437275929U,	// VBROADCASTI128
    2499821569U,	// VBROADCASTI32X4krm
    437272577U,	// VBROADCASTI32X4rm
    2499821587U,	// VBROADCASTI64X4krm
    839925779U,	// VBROADCASTI64X4rm
    571495607U,	// VBROADCASTSDYrm
    336614583U,	// VBROADCASTSDYrr
    571491443U,	// VBROADCASTSDZrm
    336610419U,	// VBROADCASTSDZrr
    605053267U,	// VBROADCASTSSYrm
    336617811U,	// VBROADCASTSSYrr
    605047459U,	// VBROADCASTSSZrm
    336612003U,	// VBROADCASTSSZrr
    605053267U,	// VBROADCASTSSrm
    336617811U,	// VBROADCASTSSrr
    2486529531U,	// VCMPPDYrmi
    2484097716U,	// VCMPPDYrmi_alt
    2486545915U,	// VCMPPDYrri
    2484097716U,	// VCMPPDYrri_alt
    890596859U,	// VCMPPDZrmi
    2484093748U,	// VCMPPDZrmi_alt
    51752443U,	// VCMPPDZrri
    2484093748U,	// VCMPPDZrri_alt
    51752443U,	// VCMPPDZrrib
    2486529531U,	// VCMPPDrmi
    2484097716U,	// VCMPPDrmi_alt
    2486545915U,	// VCMPPDrri
    2484097716U,	// VCMPPDrri_alt
    2487578107U,	// VCMPPSYrmi
    2484100963U,	// VCMPPSYrmi_alt
    2487594491U,	// VCMPPSYrri
    2484100963U,	// VCMPPSYrri_alt
    891645435U,	// VCMPPSZrmi
    2484095452U,	// VCMPPSZrmi_alt
    52801019U,	// VCMPPSZrri
    2484095452U,	// VCMPPSZrri_alt
    52801019U,	// VCMPPSZrrib
    2487578107U,	// VCMPPSrmi
    2484100963U,	// VCMPPSrmi_alt
    2487594491U,	// VCMPPSrri
    2484100963U,	// VCMPPSrri_alt
    2488626683U,	// VCMPSDZrm
    2484098205U,	// VCMPSDZrmi_alt
    2488643067U,	// VCMPSDZrr
    2484098205U,	// VCMPSDZrri_alt
    2488626683U,	// VCMPSDrm
    2484098205U,	// VCMPSDrm_alt
    2488643067U,	// VCMPSDrr
    2484098205U,	// VCMPSDrr_alt
    2489675259U,	// VCMPSSZrm
    2484101423U,	// VCMPSSZrmi_alt
    2489691643U,	// VCMPSSZrr
    2484101423U,	// VCMPSSZrri_alt
    2489675259U,	// VCMPSSrm
    2484101423U,	// VCMPSSrm_alt
    2489691643U,	// VCMPSSrr
    2484101423U,	// VCMPSSrr_alt
    537941108U,	// VCOMISDZrm
    336614516U,	// VCOMISDZrr
    537941108U,	// VCOMISDrm
    336614516U,	// VCOMISDrr
    537944334U,	// VCOMISSZrm
    336617742U,	// VCOMISSZrr
    537944334U,	// VCOMISSrm
    336617742U,	// VCOMISSrr
    437276966U,	// VCVTDQ2PDYrm
    336613670U,	// VCVTDQ2PDYrr
    839926270U,	// VCVTDQ2PDZrm
    336609790U,	// VCVTDQ2PDZrr
    370168102U,	// VCVTDQ2PDrm
    336613670U,	// VCVTDQ2PDrr
    839933396U,	// VCVTDQ2PSYrm
    336616916U,	// VCVTDQ2PSYrr
    907036850U,	// VCVTDQ2PSZrm
    336611506U,	// VCVTDQ2PSZrr
    2484095154U,	// VCVTDQ2PSZrrb
    437280212U,	// VCVTDQ2PSrm
    336616916U,	// VCVTDQ2PSrr
    537945391U,	// VCVTPD2DQXrm
    940595750U,	// VCVTPD2DQYrm
    336615974U,	// VCVTPD2DQYrr
    974145037U,	// VCVTPD2DQZrm
    336610829U,	// VCVTPD2DQZrr
    2484094477U,	// VCVTPD2DQZrrb
    336615974U,	// VCVTPD2DQrr
    537945421U,	// VCVTPD2PSXrm
    940596648U,	// VCVTPD2PSYrm
    336616872U,	// VCVTPD2PSYrr
    974145690U,	// VCVTPD2PSZrm
    336611482U,	// VCVTPD2PSZrr
    2484095130U,	// VCVTPD2PSZrrb
    336616872U,	// VCVTPD2PSrr
    974145205U,	// VCVTPD2UDQZrm
    336610997U,	// VCVTPD2UDQZrr
    2484094645U,	// VCVTPD2UDQZrrb
    537943475U,	// VCVTPH2PSYrm
    336616883U,	// VCVTPH2PSYrr
    940596659U,	// VCVTPH2PSZrm
    336616883U,	// VCVTPH2PSZrr
    571497907U,	// VCVTPH2PSrm
    336616883U,	// VCVTPH2PSrr
    940595782U,	// VCVTPS2DQYrm
    336616006U,	// VCVTPS2DQYrr
    974145062U,	// VCVTPS2DQZrm
    336610854U,	// VCVTPS2DQZrr
    2484094502U,	// VCVTPS2DQZrrb
    537942598U,	// VCVTPS2DQrm
    336616006U,	// VCVTPS2DQrr
    537940273U,	// VCVTPS2PDYrm
    336613681U,	// VCVTPS2PDYrr
    940589591U,	// VCVTPS2PDZrm
    336609815U,	// VCVTPS2PDZrr
    571494705U,	// VCVTPS2PDrm
    336613681U,	// VCVTPS2PDrr
    2149127959U,	// VCVTPS2PHYmr
    2484098839U,	// VCVTPS2PHYrr
    2149156119U,	// VCVTPS2PHZmr
    2484094231U,	// VCVTPS2PHZrr
    2148669207U,	// VCVTPS2PHmr
    2484098839U,	// VCVTPS2PHrr
    974145232U,	// VCVTPS2UDQZrm
    336611024U,	// VCVTPS2UDQZrr
    2484094672U,	// VCVTPS2UDQZrrb
    571491632U,	// VCVTSD2SI64Zrm
    336610608U,	// VCVTSD2SI64Zrr
    571496386U,	// VCVTSD2SI64rm
    336615362U,	// VCVTSD2SI64rr
    571491632U,	// VCVTSD2SIZrm
    336610608U,	// VCVTSD2SIZrr
    571496386U,	// VCVTSD2SIrm
    336615362U,	// VCVTSD2SIrr
    2484101224U,	// VCVTSD2SSZrm
    2484101224U,	// VCVTSD2SSZrr
    2484101224U,	// VCVTSD2SSrm
    2484101224U,	// VCVTSD2SSrr
    571491683U,	// VCVTSD2USI64Zrm
    336610659U,	// VCVTSD2USI64Zrr
    571491683U,	// VCVTSD2USIZrm
    336610659U,	// VCVTSD2USIZrr
    2484097990U,	// VCVTSI2SD64rm
    2484097990U,	// VCVTSI2SD64rr
    2484093903U,	// VCVTSI2SDZrm
    2484093903U,	// VCVTSI2SDZrr
    2484097990U,	// VCVTSI2SDrm
    2484097990U,	// VCVTSI2SDrr
    2484101235U,	// VCVTSI2SS64rm
    2484101235U,	// VCVTSI2SS64rr
    2484095506U,	// VCVTSI2SSZrm
    2484095506U,	// VCVTSI2SSZrr
    2484101235U,	// VCVTSI2SSrm
    2484101235U,	// VCVTSI2SSrr
    2484093903U,	// VCVTSI642SDZrm
    2484093903U,	// VCVTSI642SDZrr
    2484095506U,	// VCVTSI642SSZrm
    2484095506U,	// VCVTSI642SSZrr
    2484098013U,	// VCVTSS2SDZrm
    2484098013U,	// VCVTSS2SDZrr
    2484098013U,	// VCVTSS2SDrm
    2484098013U,	// VCVTSS2SDrr
    605046089U,	// VCVTSS2SI64Zrm
    336610633U,	// VCVTSS2SI64Zrr
    605050841U,	// VCVTSS2SI64rm
    336615385U,	// VCVTSS2SI64rr
    605046089U,	// VCVTSS2SIZrm
    336610633U,	// VCVTSS2SIZrr
    605050841U,	// VCVTSS2SIrm
    336615385U,	// VCVTSS2SIrr
    605046142U,	// VCVTSS2USI64Zrm
    336610686U,	// VCVTSS2USI64Zrr
    605046142U,	// VCVTSS2USIZrm
    336610686U,	// VCVTSS2USIZrr
    537945378U,	// VCVTTPD2DQXrm
    940595738U,	// VCVTTPD2DQYrm
    336615962U,	// VCVTTPD2DQYrr
    974145024U,	// VCVTTPD2DQZrm
    336610816U,	// VCVTTPD2DQZrr
    336615962U,	// VCVTTPD2DQrr
    974145191U,	// VCVTTPD2UDQZrm
    336610983U,	// VCVTTPD2UDQZrr
    940595770U,	// VCVTTPS2DQYrm
    336615994U,	// VCVTTPS2DQYrr
    974145049U,	// VCVTTPS2DQZrm
    336610841U,	// VCVTTPS2DQZrr
    537942586U,	// VCVTTPS2DQrm
    336615994U,	// VCVTTPS2DQrr
    974145218U,	// VCVTTPS2UDQZrm
    336611010U,	// VCVTTPS2UDQZrr
    571491619U,	// VCVTTSD2SI64Zrm
    336610595U,	// VCVTTSD2SI64Zrr
    571496374U,	// VCVTTSD2SI64rm
    336615350U,	// VCVTTSD2SI64rr
    571491619U,	// VCVTTSD2SIZrm
    336610595U,	// VCVTTSD2SIZrr
    571496374U,	// VCVTTSD2SIrm
    336615350U,	// VCVTTSD2SIrr
    571491669U,	// VCVTTSD2USI64Zrm
    336610645U,	// VCVTTSD2USI64Zrr
    571491669U,	// VCVTTSD2USIZrm
    336610645U,	// VCVTTSD2USIZrr
    605046076U,	// VCVTTSS2SI64Zrm
    336610620U,	// VCVTTSS2SI64Zrr
    605050829U,	// VCVTTSS2SI64rm
    336615373U,	// VCVTTSS2SI64rr
    605046076U,	// VCVTTSS2SIZrm
    336610620U,	// VCVTTSS2SIZrr
    605050829U,	// VCVTTSS2SIrm
    336615373U,	// VCVTTSS2SIrr
    605046128U,	// VCVTTSS2USI64Zrm
    336610672U,	// VCVTTSS2USI64Zrr
    605046128U,	// VCVTTSS2USIZrm
    336610672U,	// VCVTTSS2USIZrr
    940589578U,	// VCVTUDQ2PDZrm
    336609802U,	// VCVTUDQ2PDZrr
    974145726U,	// VCVTUDQ2PSZrm
    336611518U,	// VCVTUDQ2PSZrr
    2484095166U,	// VCVTUDQ2PSZrrb
    2484093915U,	// VCVTUSI2SDZrm
    2484093915U,	// VCVTUSI2SDZrr
    2484095518U,	// VCVTUSI2SSZrm
    2484095518U,	// VCVTUSI2SSZrr
    2484093915U,	// VCVTUSI642SDZrm
    2484093915U,	// VCVTUSI642SDZrr
    2484095518U,	// VCVTUSI642SSZrm
    2484095518U,	// VCVTUSI642SSZrr
    2484097799U,	// VDIVPDYrm
    2484097799U,	// VDIVPDYrr
    2484093784U,	// VDIVPDZrm
    2484093784U,	// VDIVPDZrmb
    352338776U,	// VDIVPDZrmbk
    2499822424U,	// VDIVPDZrmbkz
    352342791U,	// VDIVPDZrmk
    2499826439U,	// VDIVPDZrmkz
    2484093784U,	// VDIVPDZrr
    352338776U,	// VDIVPDZrrk
    2499822424U,	// VDIVPDZrrkz
    2484097799U,	// VDIVPDrm
    2484097799U,	// VDIVPDrr
    2484101079U,	// VDIVPSYrm
    2484101079U,	// VDIVPSYrr
    2484095488U,	// VDIVPSZrm
    2484095488U,	// VDIVPSZrmb
    352340480U,	// VDIVPSZrmbk
    2499824128U,	// VDIVPSZrmbkz
    352346071U,	// VDIVPSZrmk
    2499829719U,	// VDIVPSZrmkz
    2484095488U,	// VDIVPSZrr
    352340480U,	// VDIVPSZrrk
    2499824128U,	// VDIVPSZrrkz
    2484101079U,	// VDIVPSrm
    2484101079U,	// VDIVPSrr
    2484098245U,	// VDIVSDZrm
    2484098245U,	// VDIVSDZrr
    2484098245U,	// VDIVSDrm
    2484098245U,	// VDIVSDrm_Int
    2484098245U,	// VDIVSDrr
    2484098245U,	// VDIVSDrr_Int
    2484101473U,	// VDIVSSZrm
    2484101473U,	// VDIVSSZrr
    2484101473U,	// VDIVSSrm
    2484101473U,	// VDIVSSrm_Int
    2484101473U,	// VDIVSSrr
    2484101473U,	// VDIVSSrr_Int
    2484097709U,	// VDPPDrmi
    2484097709U,	// VDPPDrri
    2484100956U,	// VDPPSYrmi
    2484100956U,	// VDPPSYrri
    2484100956U,	// VDPPSrmi
    2484100956U,	// VDPPSrri
    40056U,	// VERRm
    23672U,	// VERRr
    41959U,	// VERWm
    25575U,	// VERWr
    2149125319U,	// VEXTRACTF128mr
    2484096199U,	// VEXTRACTF128rr
    2149125180U,	// VEXTRACTF32x4mr
    2484096060U,	// VEXTRACTF32x4rr
    2149158006U,	// VEXTRACTF64x4mr
    2484096118U,	// VEXTRACTF64x4rr
    2148797694U,	// VEXTRACTI128mr
    2484096254U,	// VEXTRACTI128rr
    2148797529U,	// VEXTRACTI32x4mr
    2484096089U,	// VEXTRACTI32x4rr
    2149174419U,	// VEXTRACTI64x4mr
    2484096147U,	// VEXTRACTI64x4rr
    2148654982U,	// VEXTRACTPSmr
    2484100998U,	// VEXTRACTPSrr
    2148654982U,	// VEXTRACTPSzmr
    2484100998U,	// VEXTRACTPSzrr
    2182103509U,	// VFMADD132PDZm
    2182103509U,	// VFMADD132PDZmb
    2182105213U,	// VFMADD132PSZm
    2182105213U,	// VFMADD132PSZmb
    2182103662U,	// VFMADD213PDZm
    2182103662U,	// VFMADD213PDZmb
    2182103662U,	// VFMADD213PDZr
    50348654U,	// VFMADD213PDZrk
    2197832302U,	// VFMADD213PDZrkz
    2182105366U,	// VFMADD213PSZm
    2182105366U,	// VFMADD213PSZmb
    2182105366U,	// VFMADD213PSZr
    50350358U,	// VFMADD213PSZrk
    2197834006U,	// VFMADD213PSZrkz
    2484097527U,	// VFMADDPD4mr
    2484097527U,	// VFMADDPD4mrY
    2484097527U,	// VFMADDPD4rm
    2484097527U,	// VFMADDPD4rmY
    2484097527U,	// VFMADDPD4rr
    2484097527U,	// VFMADDPD4rrY
    2484097527U,	// VFMADDPD4rrY_REV
    2484097527U,	// VFMADDPD4rr_REV
    2182107381U,	// VFMADDPDr132m
    2182107381U,	// VFMADDPDr132mY
    2182107381U,	// VFMADDPDr132r
    2182107381U,	// VFMADDPDr132rY
    2182107511U,	// VFMADDPDr213m
    2182107511U,	// VFMADDPDr213mY
    2182107511U,	// VFMADDPDr213r
    2182107511U,	// VFMADDPDr213rY
    2182107295U,	// VFMADDPDr231m
    2182107295U,	// VFMADDPDr231mY
    2182107295U,	// VFMADDPDr231r
    2182107295U,	// VFMADDPDr231rY
    2484100754U,	// VFMADDPS4mr
    2484100754U,	// VFMADDPS4mrY
    2484100754U,	// VFMADDPS4rm
    2484100754U,	// VFMADDPS4rmY
    2484100754U,	// VFMADDPS4rr
    2484100754U,	// VFMADDPS4rrY
    2484100754U,	// VFMADDPS4rrY_REV
    2484100754U,	// VFMADDPS4rr_REV
    2182110605U,	// VFMADDPSr132m
    2182110605U,	// VFMADDPSr132mY
    2182110605U,	// VFMADDPSr132r
    2182110605U,	// VFMADDPSr132rY
    2182110746U,	// VFMADDPSr213m
    2182110746U,	// VFMADDPSr213mY
    2182110746U,	// VFMADDPSr213r
    2182110746U,	// VFMADDPSr213rY
    2182110519U,	// VFMADDPSr231m
    2182110519U,	// VFMADDPSr231mY
    2182110519U,	// VFMADDPSr231r
    2182110519U,	// VFMADDPSr231rY
    2484098115U,	// VFMADDSD4mr
    2484098115U,	// VFMADDSD4mr_Int
    2484098115U,	// VFMADDSD4rm
    2484098115U,	// VFMADDSD4rm_Int
    2484098115U,	// VFMADDSD4rr
    2484098115U,	// VFMADDSD4rr_Int
    2484098115U,	// VFMADDSD4rr_REV
    2182104069U,	// VFMADDSDZm
    2182104069U,	// VFMADDSDZr
    2182108075U,	// VFMADDSDr132m
    2182108075U,	// VFMADDSDr132r
    2182108163U,	// VFMADDSDr213m
    2182108163U,	// VFMADDSDr213r
    2182108021U,	// VFMADDSDr231m
    2182108021U,	// VFMADDSDr231r
    2484101341U,	// VFMADDSS4mr
    2484101341U,	// VFMADDSS4mr_Int
    2484101341U,	// VFMADDSS4rm
    2484101341U,	// VFMADDSS4rm_Int
    2484101341U,	// VFMADDSS4rr
    2484101341U,	// VFMADDSS4rr_Int
    2484101341U,	// VFMADDSS4rr_REV
    2182105672U,	// VFMADDSSZm
    2182105672U,	// VFMADDSSZr
    2182111309U,	// VFMADDSSr132m
    2182111309U,	// VFMADDSSr132r
    2182111397U,	// VFMADDSSr213m
    2182111397U,	// VFMADDSSr213r
    2182111255U,	// VFMADDSSr231m
    2182111255U,	// VFMADDSSr231r
    2182103446U,	// VFMADDSUB132PDZm
    2182103446U,	// VFMADDSUB132PDZmb
    2182105150U,	// VFMADDSUB132PSZm
    2182105150U,	// VFMADDSUB132PSZmb
    2182103599U,	// VFMADDSUB213PDZm
    2182103599U,	// VFMADDSUB213PDZmb
    2182103599U,	// VFMADDSUB213PDZr
    50348591U,	// VFMADDSUB213PDZrk
    2197832239U,	// VFMADDSUB213PDZrkz
    2182105303U,	// VFMADDSUB213PSZm
    2182105303U,	// VFMADDSUB213PSZmb
    2182105303U,	// VFMADDSUB213PSZr
    50350295U,	// VFMADDSUB213PSZrk
    2197833943U,	// VFMADDSUB213PSZrkz
    2484097443U,	// VFMADDSUBPD4mr
    2484097443U,	// VFMADDSUBPD4mrY
    2484097443U,	// VFMADDSUBPD4rm
    2484097443U,	// VFMADDSUBPD4rmY
    2484097443U,	// VFMADDSUBPD4rr
    2484097443U,	// VFMADDSUBPD4rrY
    2484097443U,	// VFMADDSUBPD4rrY_REV
    2484097443U,	// VFMADDSUBPD4rr_REV
    2182107322U,	// VFMADDSUBPDr132m
    2182107322U,	// VFMADDSUBPDr132mY
    2182107322U,	// VFMADDSUBPDr132r
    2182107322U,	// VFMADDSUBPDr132rY
    2182107452U,	// VFMADDSUBPDr213m
    2182107452U,	// VFMADDSUBPDr213mY
    2182107452U,	// VFMADDSUBPDr213r
    2182107452U,	// VFMADDSUBPDr213rY
    2182107236U,	// VFMADDSUBPDr231m
    2182107236U,	// VFMADDSUBPDr231mY
    2182107236U,	// VFMADDSUBPDr231r
    2182107236U,	// VFMADDSUBPDr231rY
    2484100670U,	// VFMADDSUBPS4mr
    2484100670U,	// VFMADDSUBPS4mrY
    2484100670U,	// VFMADDSUBPS4rm
    2484100670U,	// VFMADDSUBPS4rmY
    2484100670U,	// VFMADDSUBPS4rr
    2484100670U,	// VFMADDSUBPS4rrY
    2484100670U,	// VFMADDSUBPS4rrY_REV
    2484100670U,	// VFMADDSUBPS4rr_REV
    2182110546U,	// VFMADDSUBPSr132m
    2182110546U,	// VFMADDSUBPSr132mY
    2182110546U,	// VFMADDSUBPSr132r
    2182110546U,	// VFMADDSUBPSr132rY
    2182110687U,	// VFMADDSUBPSr213m
    2182110687U,	// VFMADDSUBPSr213mY
    2182110687U,	// VFMADDSUBPSr213r
    2182110687U,	// VFMADDSUBPSr213rY
    2182110460U,	// VFMADDSUBPSr231m
    2182110460U,	// VFMADDSUBPSr231mY
    2182110460U,	// VFMADDSUBPSr231r
    2182110460U,	// VFMADDSUBPSr231rY
    2182103463U,	// VFMSUB132PDZm
    2182103463U,	// VFMSUB132PDZmb
    2182105167U,	// VFMSUB132PSZm
    2182105167U,	// VFMSUB132PSZmb
    2182103616U,	// VFMSUB213PDZm
    2182103616U,	// VFMSUB213PDZmb
    2182103616U,	// VFMSUB213PDZr
    50348608U,	// VFMSUB213PDZrk
    2197832256U,	// VFMSUB213PDZrkz
    2182105320U,	// VFMSUB213PSZm
    2182105320U,	// VFMSUB213PSZmb
    2182105320U,	// VFMSUB213PSZr
    50350312U,	// VFMSUB213PSZrk
    2197833960U,	// VFMSUB213PSZrkz
    2182103492U,	// VFMSUBADD132PDZm
    2182103492U,	// VFMSUBADD132PDZmb
    2182105196U,	// VFMSUBADD132PSZm
    2182105196U,	// VFMSUBADD132PSZmb
    2182103645U,	// VFMSUBADD213PDZm
    2182103645U,	// VFMSUBADD213PDZmb
    2182103645U,	// VFMSUBADD213PDZr
    50348637U,	// VFMSUBADD213PDZrk
    2197832285U,	// VFMSUBADD213PDZrkz
    2182105349U,	// VFMSUBADD213PSZm
    2182105349U,	// VFMSUBADD213PSZmb
    2182105349U,	// VFMSUBADD213PSZr
    50350341U,	// VFMSUBADD213PSZrk
    2197833989U,	// VFMSUBADD213PSZrkz
    2484097505U,	// VFMSUBADDPD4mr
    2484097505U,	// VFMSUBADDPD4mrY
    2484097505U,	// VFMSUBADDPD4rm
    2484097505U,	// VFMSUBADDPD4rmY
    2484097505U,	// VFMSUBADDPD4rr
    2484097505U,	// VFMSUBADDPD4rrY
    2484097505U,	// VFMSUBADDPD4rrY_REV
    2484097505U,	// VFMSUBADDPD4rr_REV
    2182107365U,	// VFMSUBADDPDr132m
    2182107365U,	// VFMSUBADDPDr132mY
    2182107365U,	// VFMSUBADDPDr132r
    2182107365U,	// VFMSUBADDPDr132rY
    2182107495U,	// VFMSUBADDPDr213m
    2182107495U,	// VFMSUBADDPDr213mY
    2182107495U,	// VFMSUBADDPDr213r
    2182107495U,	// VFMSUBADDPDr213rY
    2182107279U,	// VFMSUBADDPDr231m
    2182107279U,	// VFMSUBADDPDr231mY
    2182107279U,	// VFMSUBADDPDr231r
    2182107279U,	// VFMSUBADDPDr231rY
    2484100732U,	// VFMSUBADDPS4mr
    2484100732U,	// VFMSUBADDPS4mrY
    2484100732U,	// VFMSUBADDPS4rm
    2484100732U,	// VFMSUBADDPS4rmY
    2484100732U,	// VFMSUBADDPS4rr
    2484100732U,	// VFMSUBADDPS4rrY
    2484100732U,	// VFMSUBADDPS4rrY_REV
    2484100732U,	// VFMSUBADDPS4rr_REV
    2182110589U,	// VFMSUBADDPSr132m
    2182110589U,	// VFMSUBADDPSr132mY
    2182110589U,	// VFMSUBADDPSr132r
    2182110589U,	// VFMSUBADDPSr132rY
    2182110730U,	// VFMSUBADDPSr213m
    2182110730U,	// VFMSUBADDPSr213mY
    2182110730U,	// VFMSUBADDPSr213r
    2182110730U,	// VFMSUBADDPSr213rY
    2182110503U,	// VFMSUBADDPSr231m
    2182110503U,	// VFMSUBADDPSr231mY
    2182110503U,	// VFMSUBADDPSr231r
    2182110503U,	// VFMSUBADDPSr231rY
    2484097476U,	// VFMSUBPD4mr
    2484097476U,	// VFMSUBPD4mrY
    2484097476U,	// VFMSUBPD4rm
    2484097476U,	// VFMSUBPD4rmY
    2484097476U,	// VFMSUBPD4rr
    2484097476U,	// VFMSUBPD4rrY
    2484097476U,	// VFMSUBPD4rrY_REV
    2484097476U,	// VFMSUBPD4rr_REV
    2182107338U,	// VFMSUBPDr132m
    2182107338U,	// VFMSUBPDr132mY
    2182107338U,	// VFMSUBPDr132r
    2182107338U,	// VFMSUBPDr132rY
    2182107468U,	// VFMSUBPDr213m
    2182107468U,	// VFMSUBPDr213mY
    2182107468U,	// VFMSUBPDr213r
    2182107468U,	// VFMSUBPDr213rY
    2182107252U,	// VFMSUBPDr231m
    2182107252U,	// VFMSUBPDr231mY
    2182107252U,	// VFMSUBPDr231r
    2182107252U,	// VFMSUBPDr231rY
    2484100703U,	// VFMSUBPS4mr
    2484100703U,	// VFMSUBPS4mrY
    2484100703U,	// VFMSUBPS4rm
    2484100703U,	// VFMSUBPS4rmY
    2484100703U,	// VFMSUBPS4rr
    2484100703U,	// VFMSUBPS4rrY
    2484100703U,	// VFMSUBPS4rrY_REV
    2484100703U,	// VFMSUBPS4rr_REV
    2182110562U,	// VFMSUBPSr132m
    2182110562U,	// VFMSUBPSr132mY
    2182110562U,	// VFMSUBPSr132r
    2182110562U,	// VFMSUBPSr132rY
    2182110703U,	// VFMSUBPSr213m
    2182110703U,	// VFMSUBPSr213mY
    2182110703U,	// VFMSUBPSr213r
    2182110703U,	// VFMSUBPSr213rY
    2182110476U,	// VFMSUBPSr231m
    2182110476U,	// VFMSUBPSr231mY
    2182110476U,	// VFMSUBPSr231r
    2182110476U,	// VFMSUBPSr231rY
    2484098086U,	// VFMSUBSD4mr
    2484098086U,	// VFMSUBSD4mr_Int
    2484098086U,	// VFMSUBSD4rm
    2484098086U,	// VFMSUBSD4rm_Int
    2484098086U,	// VFMSUBSD4rr
    2484098086U,	// VFMSUBSD4rr_Int
    2484098086U,	// VFMSUBSD4rr_REV
    2182104040U,	// VFMSUBSDZm
    2182104040U,	// VFMSUBSDZr
    2182108048U,	// VFMSUBSDr132m
    2182108048U,	// VFMSUBSDr132r
    2182108136U,	// VFMSUBSDr213m
    2182108136U,	// VFMSUBSDr213r
    2182107994U,	// VFMSUBSDr231m
    2182107994U,	// VFMSUBSDr231r
    2484101312U,	// VFMSUBSS4mr
    2484101312U,	// VFMSUBSS4mr_Int
    2484101312U,	// VFMSUBSS4rm
    2484101312U,	// VFMSUBSS4rm_Int
    2484101312U,	// VFMSUBSS4rr
    2484101312U,	// VFMSUBSS4rr_Int
    2484101312U,	// VFMSUBSS4rr_REV
    2182105643U,	// VFMSUBSSZm
    2182105643U,	// VFMSUBSSZr
    2182111282U,	// VFMSUBSSr132m
    2182111282U,	// VFMSUBSSr132r
    2182111370U,	// VFMSUBSSr213m
    2182111370U,	// VFMSUBSSr213r
    2182111228U,	// VFMSUBSSr231m
    2182111228U,	// VFMSUBSSr231r
    2182103523U,	// VFNMADD132PDZm
    2182103523U,	// VFNMADD132PDZmb
    2182105227U,	// VFNMADD132PSZm
    2182105227U,	// VFNMADD132PSZmb
    2182103676U,	// VFNMADD213PDZm
    2182103676U,	// VFNMADD213PDZmb
    2182103676U,	// VFNMADD213PDZr
    50348668U,	// VFNMADD213PDZrk
    2197832316U,	// VFNMADD213PDZrkz
    2182105380U,	// VFNMADD213PSZm
    2182105380U,	// VFNMADD213PSZmb
    2182105380U,	// VFNMADD213PSZr
    50350372U,	// VFNMADD213PSZrk
    2197834020U,	// VFNMADD213PSZrkz
    2484097537U,	// VFNMADDPD4mr
    2484097537U,	// VFNMADDPD4mrY
    2484097537U,	// VFNMADDPD4rm
    2484097537U,	// VFNMADDPD4rmY
    2484097537U,	// VFNMADDPD4rr
    2484097537U,	// VFNMADDPD4rrY
    2484097537U,	// VFNMADDPD4rrY_REV
    2484097537U,	// VFNMADDPD4rr_REV
    2182107394U,	// VFNMADDPDr132m
    2182107394U,	// VFNMADDPDr132mY
    2182107394U,	// VFNMADDPDr132r
    2182107394U,	// VFNMADDPDr132rY
    2182107524U,	// VFNMADDPDr213m
    2182107524U,	// VFNMADDPDr213mY
    2182107524U,	// VFNMADDPDr213r
    2182107524U,	// VFNMADDPDr213rY
    2182107308U,	// VFNMADDPDr231m
    2182107308U,	// VFNMADDPDr231mY
    2182107308U,	// VFNMADDPDr231r
    2182107308U,	// VFNMADDPDr231rY
    2484100764U,	// VFNMADDPS4mr
    2484100764U,	// VFNMADDPS4mrY
    2484100764U,	// VFNMADDPS4rm
    2484100764U,	// VFNMADDPS4rmY
    2484100764U,	// VFNMADDPS4rr
    2484100764U,	// VFNMADDPS4rrY
    2484100764U,	// VFNMADDPS4rrY_REV
    2484100764U,	// VFNMADDPS4rr_REV
    2182110618U,	// VFNMADDPSr132m
    2182110618U,	// VFNMADDPSr132mY
    2182110618U,	// VFNMADDPSr132r
    2182110618U,	// VFNMADDPSr132rY
    2182110759U,	// VFNMADDPSr213m
    2182110759U,	// VFNMADDPSr213mY
    2182110759U,	// VFNMADDPSr213r
    2182110759U,	// VFNMADDPSr213rY
    2182110532U,	// VFNMADDPSr231m
    2182110532U,	// VFNMADDPSr231mY
    2182110532U,	// VFNMADDPSr231r
    2182110532U,	// VFNMADDPSr231rY
    2484098125U,	// VFNMADDSD4mr
    2484098125U,	// VFNMADDSD4mr_Int
    2484098125U,	// VFNMADDSD4rm
    2484098125U,	// VFNMADDSD4rm_Int
    2484098125U,	// VFNMADDSD4rr
    2484098125U,	// VFNMADDSD4rr_Int
    2484098125U,	// VFNMADDSD4rr_REV
    2182104083U,	// VFNMADDSDZm
    2182104083U,	// VFNMADDSDZr
    2182108088U,	// VFNMADDSDr132m
    2182108088U,	// VFNMADDSDr132r
    2182108176U,	// VFNMADDSDr213m
    2182108176U,	// VFNMADDSDr213r
    2182108034U,	// VFNMADDSDr231m
    2182108034U,	// VFNMADDSDr231r
    2484101351U,	// VFNMADDSS4mr
    2484101351U,	// VFNMADDSS4mr_Int
    2484101351U,	// VFNMADDSS4rm
    2484101351U,	// VFNMADDSS4rm_Int
    2484101351U,	// VFNMADDSS4rr
    2484101351U,	// VFNMADDSS4rr_Int
    2484101351U,	// VFNMADDSS4rr_REV
    2182105686U,	// VFNMADDSSZm
    2182105686U,	// VFNMADDSSZr
    2182111322U,	// VFNMADDSSr132m
    2182111322U,	// VFNMADDSSr132r
    2182111410U,	// VFNMADDSSr213m
    2182111410U,	// VFNMADDSSr213r
    2182111268U,	// VFNMADDSSr231m
    2182111268U,	// VFNMADDSSr231r
    2182103477U,	// VFNMSUB132PDZm
    2182103477U,	// VFNMSUB132PDZmb
    2182105181U,	// VFNMSUB132PSZm
    2182105181U,	// VFNMSUB132PSZmb
    2182103630U,	// VFNMSUB213PDZm
    2182103630U,	// VFNMSUB213PDZmb
    2182103630U,	// VFNMSUB213PDZr
    50348622U,	// VFNMSUB213PDZrk
    2197832270U,	// VFNMSUB213PDZrkz
    2182105334U,	// VFNMSUB213PSZm
    2182105334U,	// VFNMSUB213PSZmb
    2182105334U,	// VFNMSUB213PSZr
    50350326U,	// VFNMSUB213PSZrk
    2197833974U,	// VFNMSUB213PSZrkz
    2484097486U,	// VFNMSUBPD4mr
    2484097486U,	// VFNMSUBPD4mrY
    2484097486U,	// VFNMSUBPD4rm
    2484097486U,	// VFNMSUBPD4rmY
    2484097486U,	// VFNMSUBPD4rr
    2484097486U,	// VFNMSUBPD4rrY
    2484097486U,	// VFNMSUBPD4rrY_REV
    2484097486U,	// VFNMSUBPD4rr_REV
    2182107351U,	// VFNMSUBPDr132m
    2182107351U,	// VFNMSUBPDr132mY
    2182107351U,	// VFNMSUBPDr132r
    2182107351U,	// VFNMSUBPDr132rY
    2182107481U,	// VFNMSUBPDr213m
    2182107481U,	// VFNMSUBPDr213mY
    2182107481U,	// VFNMSUBPDr213r
    2182107481U,	// VFNMSUBPDr213rY
    2182107265U,	// VFNMSUBPDr231m
    2182107265U,	// VFNMSUBPDr231mY
    2182107265U,	// VFNMSUBPDr231r
    2182107265U,	// VFNMSUBPDr231rY
    2484100713U,	// VFNMSUBPS4mr
    2484100713U,	// VFNMSUBPS4mrY
    2484100713U,	// VFNMSUBPS4rm
    2484100713U,	// VFNMSUBPS4rmY
    2484100713U,	// VFNMSUBPS4rr
    2484100713U,	// VFNMSUBPS4rrY
    2484100713U,	// VFNMSUBPS4rrY_REV
    2484100713U,	// VFNMSUBPS4rr_REV
    2182110575U,	// VFNMSUBPSr132m
    2182110575U,	// VFNMSUBPSr132mY
    2182110575U,	// VFNMSUBPSr132r
    2182110575U,	// VFNMSUBPSr132rY
    2182110716U,	// VFNMSUBPSr213m
    2182110716U,	// VFNMSUBPSr213mY
    2182110716U,	// VFNMSUBPSr213r
    2182110716U,	// VFNMSUBPSr213rY
    2182110489U,	// VFNMSUBPSr231m
    2182110489U,	// VFNMSUBPSr231mY
    2182110489U,	// VFNMSUBPSr231r
    2182110489U,	// VFNMSUBPSr231rY
    2484098096U,	// VFNMSUBSD4mr
    2484098096U,	// VFNMSUBSD4mr_Int
    2484098096U,	// VFNMSUBSD4rm
    2484098096U,	// VFNMSUBSD4rm_Int
    2484098096U,	// VFNMSUBSD4rr
    2484098096U,	// VFNMSUBSD4rr_Int
    2484098096U,	// VFNMSUBSD4rr_REV
    2182104054U,	// VFNMSUBSDZm
    2182104054U,	// VFNMSUBSDZr
    2182108061U,	// VFNMSUBSDr132m
    2182108061U,	// VFNMSUBSDr132r
    2182108149U,	// VFNMSUBSDr213m
    2182108149U,	// VFNMSUBSDr213r
    2182108007U,	// VFNMSUBSDr231m
    2182108007U,	// VFNMSUBSDr231r
    2484101322U,	// VFNMSUBSS4mr
    2484101322U,	// VFNMSUBSS4mr_Int
    2484101322U,	// VFNMSUBSS4rm
    2484101322U,	// VFNMSUBSS4rm_Int
    2484101322U,	// VFNMSUBSS4rr
    2484101322U,	// VFNMSUBSS4rr_Int
    2484101322U,	// VFNMSUBSS4rr_REV
    2182105657U,	// VFNMSUBSSZm
    2182105657U,	// VFNMSUBSSZr
    2182111295U,	// VFNMSUBSSr132m
    2182111295U,	// VFNMSUBSSr132r
    2182111383U,	// VFNMSUBSSr213m
    2182111383U,	// VFNMSUBSSr213r
    2182111241U,	// VFNMSUBSSr231m
    2182111241U,	// VFNMSUBSSr231r
    537940771U,	// VFRCZPDrm
    940593955U,	// VFRCZPDrmY
    336614179U,	// VFRCZPDrr
    336614179U,	// VFRCZPDrrY
    537944051U,	// VFRCZPSrm
    940597235U,	// VFRCZPSrmY
    336617459U,	// VFRCZPSrr
    336617459U,	// VFRCZPSrrY
    571495654U,	// VFRCZSDrm
    336614630U,	// VFRCZSDrr
    605053305U,	// VFRCZSSrm
    336617849U,	// VFRCZSSrr
    2484097692U,	// VFsANDNPDrm
    2484097692U,	// VFsANDNPDrr
    2484100931U,	// VFsANDNPSrm
    2484100931U,	// VFsANDNPSrr
    2484097556U,	// VFsANDPDrm
    2484097556U,	// VFsANDPDrr
    2484100783U,	// VFsANDPSrm
    2484100783U,	// VFsANDPSrr
    2484097736U,	// VFsORPDrm
    2484097736U,	// VFsORPDrr
    2484100983U,	// VFsORPSrm
    2484100983U,	// VFsORPSrr
    2484097743U,	// VFsXORPDrm
    2484097743U,	// VFsXORPDrr
    2484100990U,	// VFsXORPSrm
    2484100990U,	// VFsXORPSrr
    1007702576U,	// VGATHERDPDYrm
    3204465357U,	// VGATHERDPDZrm
    1007702576U,	// VGATHERDPDrm
    1074814667U,	// VGATHERDPSYrm
    1056983413U,	// VGATHERDPSZrm
    1074814667U,	// VGATHERDPSrm
    321941635U,	// VGATHERPF0DPDm
    321941775U,	// VGATHERPF0DPSm
    389050569U,	// VGATHERPF0QPDm
    389050709U,	// VGATHERPF0QPSm
    321941670U,	// VGATHERPF1DPDm
    321941810U,	// VGATHERPF1DPSm
    389050604U,	// VGATHERPF1QPDm
    389050744U,	// VGATHERPF1QPSm
    1007702716U,	// VGATHERQPDYrm
    3204465469U,	// VGATHERQPDZrm
    1007702716U,	// VGATHERQPDrm
    1074814827U,	// VGATHERQPSYrm
    3204467173U,	// VGATHERQPSZrm
    1074814827U,	// VGATHERQPSrm
    2484097518U,	// VHADDPDYrm
    2484097518U,	// VHADDPDYrr
    2484097518U,	// VHADDPDrm
    2484097518U,	// VHADDPDrr
    2484100745U,	// VHADDPSYrm
    2484100745U,	// VHADDPSYrr
    2484100745U,	// VHADDPSrm
    2484100745U,	// VHADDPSrr
    2484097467U,	// VHSUBPDYrm
    2484097467U,	// VHSUBPDYrr
    2484097467U,	// VHSUBPDrm
    2484097467U,	// VHSUBPDrr
    2484100694U,	// VHSUBPSYrm
    2484100694U,	// VHSUBPSYrr
    2484100694U,	// VHSUBPSrm
    2484100694U,	// VHSUBPSrr
    2484096213U,	// VINSERTF128rm
    2484096213U,	// VINSERTF128rr
    2484096075U,	// VINSERTF32x4rm
    2484096075U,	// VINSERTF32x4rr
    2484096133U,	// VINSERTF64x4rm
    2484096133U,	// VINSERTF64x4rr
    2484096268U,	// VINSERTI128rm
    2484096268U,	// VINSERTI128rr
    2484096104U,	// VINSERTI32x4rm
    2484096104U,	// VINSERTI32x4rr
    2484096162U,	// VINSERTI64x4rm
    2484096162U,	// VINSERTI64x4rr
    2484101020U,	// VINSERTPSrm
    2484101020U,	// VINSERTPSrr
    2484101020U,	// VINSERTPSzrm
    2484101020U,	// VINSERTPSzrr
    839934560U,	// VLDDQUYrm
    437281376U,	// VLDDQUrm
    72835U,	// VLDMXCSR
    336618088U,	// VMASKMOVDQU
    336618088U,	// VMASKMOVDQU64
    2149159695U,	// VMASKMOVPDYmr
    2484097807U,	// VMASKMOVPDYrm
    2149126927U,	// VMASKMOVPDmr
    2484097807U,	// VMASKMOVPDrm
    2149162975U,	// VMASKMOVPSYmr
    2484101087U,	// VMASKMOVPSYrm
    2149130207U,	// VMASKMOVPSmr
    2484101087U,	// VMASKMOVPSrm
    2484097819U,	// VMAXCPDYrm
    2484097819U,	// VMAXCPDYrr
    2484097819U,	// VMAXCPDrm
    2484097819U,	// VMAXCPDrr
    2484101099U,	// VMAXCPSYrm
    2484101099U,	// VMAXCPSYrr
    2484101099U,	// VMAXCPSrm
    2484101099U,	// VMAXCPSrr
    2484098270U,	// VMAXCSDrm
    2484098270U,	// VMAXCSDrr
    2484101489U,	// VMAXCSSrm
    2484101489U,	// VMAXCSSrr
    2484097819U,	// VMAXPDYrm
    2484097819U,	// VMAXPDYrr
    2484093793U,	// VMAXPDZrm
    2484093793U,	// VMAXPDZrmb
    352338785U,	// VMAXPDZrmbk
    2499822433U,	// VMAXPDZrmbkz
    352342811U,	// VMAXPDZrmk
    2499826459U,	// VMAXPDZrmkz
    2484093793U,	// VMAXPDZrr
    352338785U,	// VMAXPDZrrk
    2499822433U,	// VMAXPDZrrkz
    2484097819U,	// VMAXPDrm
    2484097819U,	// VMAXPDrr
    2484101099U,	// VMAXPSYrm
    2484101099U,	// VMAXPSYrr
    2484095497U,	// VMAXPSZrm
    2484095497U,	// VMAXPSZrmb
    352340489U,	// VMAXPSZrmbk
    2499824137U,	// VMAXPSZrmbkz
    352346091U,	// VMAXPSZrmk
    2499829739U,	// VMAXPSZrmkz
    2484095497U,	// VMAXPSZrr
    352340489U,	// VMAXPSZrrk
    2499824137U,	// VMAXPSZrrkz
    2484101099U,	// VMAXPSrm
    2484101099U,	// VMAXPSrr
    2484098270U,	// VMAXSDZrm
    2484098270U,	// VMAXSDZrr
    2484098270U,	// VMAXSDrm
    2484098270U,	// VMAXSDrm_Int
    2484098270U,	// VMAXSDrr
    2484098270U,	// VMAXSDrr_Int
    2484101489U,	// VMAXSSZrm
    2484101489U,	// VMAXSSZrr
    2484101489U,	// VMAXSSrm
    2484101489U,	// VMAXSSrm_Int
    2484101489U,	// VMAXSSrr
    2484101489U,	// VMAXSSrr_Int
    11646U,	// VMCALL
    89111U,	// VMCLEARm
    11314U,	// VMFUNC
    2484097701U,	// VMINCPDYrm
    2484097701U,	// VMINCPDYrr
    2484097701U,	// VMINCPDrm
    2484097701U,	// VMINCPDrr
    2484100940U,	// VMINCPSYrm
    2484100940U,	// VMINCPSYrr
    2484100940U,	// VMINCPSrm
    2484100940U,	// VMINCPSrr
    2484098190U,	// VMINCSDrm
    2484098190U,	// VMINCSDrr
    2484101407U,	// VMINCSSrm
    2484101407U,	// VMINCSSrr
    2484097701U,	// VMINPDYrm
    2484097701U,	// VMINPDYrr
    2484093739U,	// VMINPDZrm
    2484093739U,	// VMINPDZrmb
    352338731U,	// VMINPDZrmbk
    2499822379U,	// VMINPDZrmbkz
    352342693U,	// VMINPDZrmk
    2499826341U,	// VMINPDZrmkz
    2484093739U,	// VMINPDZrr
    352338731U,	// VMINPDZrrk
    2499822379U,	// VMINPDZrrkz
    2484097701U,	// VMINPDrm
    2484097701U,	// VMINPDrr
    2484100940U,	// VMINPSYrm
    2484100940U,	// VMINPSYrr
    2484095443U,	// VMINPSZrm
    2484095443U,	// VMINPSZrmb
    352340435U,	// VMINPSZrmbk
    2499824083U,	// VMINPSZrmbkz
    352345932U,	// VMINPSZrmk
    2499829580U,	// VMINPSZrmkz
    2484095443U,	// VMINPSZrr
    352340435U,	// VMINPSZrrk
    2499824083U,	// VMINPSZrrkz
    2484100940U,	// VMINPSrm
    2484100940U,	// VMINPSrr
    2484098190U,	// VMINSDZrm
    2484098190U,	// VMINSDZrr
    2484098190U,	// VMINSDrm
    2484098190U,	// VMINSDrm_Int
    2484098190U,	// VMINSDrr
    2484098190U,	// VMINSDrr_Int
    2484101407U,	// VMINSSZrm
    2484101407U,	// VMINSSZrr
    2484101407U,	// VMINSSrm
    2484101407U,	// VMINSSrm_Int
    2484101407U,	// VMINSSrr
    2484101407U,	// VMINSSrr_Int
    11561U,	// VMLAUNCH
    12255U,	// VMLOAD32
    12310U,	// VMLOAD64
    11638U,	// VMMCALL
    336616421U,	// VMOV64toPQIZrr
    336616421U,	// VMOV64toPQIrr
    336616421U,	// VMOV64toSDZrr
    370170853U,	// VMOV64toSDrm
    336616421U,	// VMOV64toSDrr
    1675666U,	// VMOVAPDYmr
    940593554U,	// VMOVAPDYrm
    336613778U,	// VMOVAPDYrr
    336613778U,	// VMOVAPDYrr_REV
    1642898U,	// VMOVAPDZ128mr
    17371538U,	// VMOVAPDZ128mrk
    537940370U,	// VMOVAPDZ128rm
    50352530U,	// VMOVAPDZ128rmk
    2499826066U,	// VMOVAPDZ128rmkz
    336613778U,	// VMOVAPDZ128rr
    336613778U,	// VMOVAPDZ128rr_alt
    50352530U,	// VMOVAPDZ128rrk
    50352530U,	// VMOVAPDZ128rrk_alt
    2499826066U,	// VMOVAPDZ128rrkz
    2499826066U,	// VMOVAPDZ128rrkz_alt
    1675666U,	// VMOVAPDZ256mr
    17404306U,	// VMOVAPDZ256mrk
    940593554U,	// VMOVAPDZ256rm
    50352530U,	// VMOVAPDZ256rmk
    2499826066U,	// VMOVAPDZ256rmkz
    336613778U,	// VMOVAPDZ256rr
    336613778U,	// VMOVAPDZ256rr_alt
    50352530U,	// VMOVAPDZ256rrk
    50352530U,	// VMOVAPDZ256rrk_alt
    2499826066U,	// VMOVAPDZ256rrkz
    2499826066U,	// VMOVAPDZ256rrkz_alt
    1708434U,	// VMOVAPDZmr
    17437074U,	// VMOVAPDZmrk
    974147986U,	// VMOVAPDZrm
    50352530U,	// VMOVAPDZrmk
    2499826066U,	// VMOVAPDZrmkz
    336613778U,	// VMOVAPDZrr
    336613778U,	// VMOVAPDZrr_alt
    50352530U,	// VMOVAPDZrrk
    50352530U,	// VMOVAPDZrrk_alt
    2499826066U,	// VMOVAPDZrrkz
    2499826066U,	// VMOVAPDZrrkz_alt
    1642898U,	// VMOVAPDmr
    537940370U,	// VMOVAPDrm
    336613778U,	// VMOVAPDrr
    336613778U,	// VMOVAPDrr_REV
    1678901U,	// VMOVAPSYmr
    940596789U,	// VMOVAPSYrm
    336617013U,	// VMOVAPSYrr
    336617013U,	// VMOVAPSYrr_REV
    1646133U,	// VMOVAPSZ128mr
    17374773U,	// VMOVAPSZ128mrk
    537943605U,	// VMOVAPSZ128rm
    50355765U,	// VMOVAPSZ128rmk
    2499829301U,	// VMOVAPSZ128rmkz
    336617013U,	// VMOVAPSZ128rr
    336617013U,	// VMOVAPSZ128rr_alt
    50355765U,	// VMOVAPSZ128rrk
    50355765U,	// VMOVAPSZ128rrk_alt
    2499829301U,	// VMOVAPSZ128rrkz
    2499829301U,	// VMOVAPSZ128rrkz_alt
    1678901U,	// VMOVAPSZ256mr
    17407541U,	// VMOVAPSZ256mrk
    940596789U,	// VMOVAPSZ256rm
    50355765U,	// VMOVAPSZ256rmk
    2499829301U,	// VMOVAPSZ256rmkz
    336617013U,	// VMOVAPSZ256rr
    336617013U,	// VMOVAPSZ256rr_alt
    50355765U,	// VMOVAPSZ256rrk
    50355765U,	// VMOVAPSZ256rrk_alt
    2499829301U,	// VMOVAPSZ256rrkz
    2499829301U,	// VMOVAPSZ256rrkz_alt
    1711669U,	// VMOVAPSZmr
    17440309U,	// VMOVAPSZmrk
    974151221U,	// VMOVAPSZrm
    50355765U,	// VMOVAPSZrmk
    2499829301U,	// VMOVAPSZrmkz
    336617013U,	// VMOVAPSZrr
    336617013U,	// VMOVAPSZrr_alt
    50355765U,	// VMOVAPSZrrk
    50355765U,	// VMOVAPSZrrk_alt
    2499829301U,	// VMOVAPSZrrkz
    2499829301U,	// VMOVAPSZrrkz_alt
    1646133U,	// VMOVAPSmr
    537943605U,	// VMOVAPSrm
    336617013U,	// VMOVAPSrr
    336617013U,	// VMOVAPSrr_REV
    940595608U,	// VMOVDDUPYrm
    336615832U,	// VMOVDDUPYrr
    974144907U,	// VMOVDDUPZrm
    336610699U,	// VMOVDDUPZrr
    571496856U,	// VMOVDDUPrm
    336615832U,	// VMOVDDUPrr
    303060314U,	// VMOVDI2PDIZrm
    336614746U,	// VMOVDI2PDIZrr
    303060314U,	// VMOVDI2PDIrm
    336614746U,	// VMOVDI2PDIrr
    303060314U,	// VMOVDI2SSZrm
    336614746U,	// VMOVDI2SSZrr
    303060314U,	// VMOVDI2SSrm
    336614746U,	// VMOVDI2SSrr
    1313700U,	// VMOVDQA32Z128mr
    17042340U,	// VMOVDQA32Z128mrk
    437275556U,	// VMOVDQA32Z128rm
    50351012U,	// VMOVDQA32Z128rmk
    2499824548U,	// VMOVDQA32Z128rmkz
    336612260U,	// VMOVDQA32Z128rr
    336612260U,	// VMOVDQA32Z128rr_alt
    50351012U,	// VMOVDQA32Z128rrk
    50351012U,	// VMOVDQA32Z128rrk_alt
    2499824548U,	// VMOVDQA32Z128rrkz
    2499824548U,	// VMOVDQA32Z128rrkz_alt
    1690532U,	// VMOVDQA32Z256mr
    17419172U,	// VMOVDQA32Z256mrk
    839928740U,	// VMOVDQA32Z256rm
    50351012U,	// VMOVDQA32Z256rmk
    2499824548U,	// VMOVDQA32Z256rmkz
    336612260U,	// VMOVDQA32Z256rr
    336612260U,	// VMOVDQA32Z256rr_alt
    50351012U,	// VMOVDQA32Z256rrk
    50351012U,	// VMOVDQA32Z256rrk_alt
    2499824548U,	// VMOVDQA32Z256rrkz
    2499824548U,	// VMOVDQA32Z256rrkz_alt
    1723300U,	// VMOVDQA32Zmr
    17451940U,	// VMOVDQA32Zmrk
    907037604U,	// VMOVDQA32Zrm
    50351012U,	// VMOVDQA32Zrmk
    2499824548U,	// VMOVDQA32Zrmkz
    336612260U,	// VMOVDQA32Zrr
    336612260U,	// VMOVDQA32Zrr_alt
    50351012U,	// VMOVDQA32Zrrk
    50351012U,	// VMOVDQA32Zrrk_alt
    2499824548U,	// VMOVDQA32Zrrkz
    2499824548U,	// VMOVDQA32Zrrkz_alt
    1313786U,	// VMOVDQA64Z128mr
    17042426U,	// VMOVDQA64Z128mrk
    437275642U,	// VMOVDQA64Z128rm
    50351098U,	// VMOVDQA64Z128rmk
    2499824634U,	// VMOVDQA64Z128rmkz
    336612346U,	// VMOVDQA64Z128rr
    336612346U,	// VMOVDQA64Z128rr_alt
    50351098U,	// VMOVDQA64Z128rrk
    50351098U,	// VMOVDQA64Z128rrk_alt
    2499824634U,	// VMOVDQA64Z128rrkz
    2499824634U,	// VMOVDQA64Z128rrkz_alt
    1690618U,	// VMOVDQA64Z256mr
    17419258U,	// VMOVDQA64Z256mrk
    839928826U,	// VMOVDQA64Z256rm
    50351098U,	// VMOVDQA64Z256rmk
    2499824634U,	// VMOVDQA64Z256rmkz
    336612346U,	// VMOVDQA64Z256rr
    336612346U,	// VMOVDQA64Z256rr_alt
    50351098U,	// VMOVDQA64Z256rrk
    50351098U,	// VMOVDQA64Z256rrk_alt
    2499824634U,	// VMOVDQA64Z256rrkz
    2499824634U,	// VMOVDQA64Z256rrkz_alt
    1723386U,	// VMOVDQA64Zmr
    17452026U,	// VMOVDQA64Zmrk
    907037690U,	// VMOVDQA64Zrm
    50351098U,	// VMOVDQA64Zrmk
    2499824634U,	// VMOVDQA64Zrmkz
    336612346U,	// VMOVDQA64Zrr
    336612346U,	// VMOVDQA64Zrr_alt
    50351098U,	// VMOVDQA64Zrrk
    50351098U,	// VMOVDQA64Zrrk_alt
    2499824634U,	// VMOVDQA64Zrrkz
    2499824634U,	// VMOVDQA64Zrrkz_alt
    1690951U,	// VMOVDQAYmr
    839929159U,	// VMOVDQAYrm
    336612679U,	// VMOVDQAYrr
    336612679U,	// VMOVDQAYrr_REV
    1314119U,	// VMOVDQAmr
    437275975U,	// VMOVDQArm
    336612679U,	// VMOVDQArr
    336612679U,	// VMOVDQArr_REV
    1313968U,	// VMOVDQU16Z128mr
    17042608U,	// VMOVDQU16Z128mrk
    437275824U,	// VMOVDQU16Z128rm
    50351280U,	// VMOVDQU16Z128rmk
    2499824816U,	// VMOVDQU16Z128rmkz
    336612528U,	// VMOVDQU16Z128rr
    336612528U,	// VMOVDQU16Z128rr_alt
    50351280U,	// VMOVDQU16Z128rrk
    50351280U,	// VMOVDQU16Z128rrk_alt
    2499824816U,	// VMOVDQU16Z128rrkz
    2499824816U,	// VMOVDQU16Z128rrkz_alt
    1690800U,	// VMOVDQU16Z256mr
    17419440U,	// VMOVDQU16Z256mrk
    839929008U,	// VMOVDQU16Z256rm
    50351280U,	// VMOVDQU16Z256rmk
    2499824816U,	// VMOVDQU16Z256rmkz
    336612528U,	// VMOVDQU16Z256rr
    336612528U,	// VMOVDQU16Z256rr_alt
    50351280U,	// VMOVDQU16Z256rrk
    50351280U,	// VMOVDQU16Z256rrk_alt
    2499824816U,	// VMOVDQU16Z256rrkz
    2499824816U,	// VMOVDQU16Z256rrkz_alt
    1723568U,	// VMOVDQU16Zmr
    17452208U,	// VMOVDQU16Zmrk
    907037872U,	// VMOVDQU16Zrm
    50351280U,	// VMOVDQU16Zrmk
    2499824816U,	// VMOVDQU16Zrmkz
    336612528U,	// VMOVDQU16Zrr
    336612528U,	// VMOVDQU16Zrr_alt
    50351280U,	// VMOVDQU16Zrrk
    50351280U,	// VMOVDQU16Zrrk_alt
    2499824816U,	// VMOVDQU16Zrrkz
    2499824816U,	// VMOVDQU16Zrrkz_alt
    1313718U,	// VMOVDQU32Z128mr
    17042358U,	// VMOVDQU32Z128mrk
    437275574U,	// VMOVDQU32Z128rm
    50351030U,	// VMOVDQU32Z128rmk
    2499824566U,	// VMOVDQU32Z128rmkz
    336612278U,	// VMOVDQU32Z128rr
    336612278U,	// VMOVDQU32Z128rr_alt
    50351030U,	// VMOVDQU32Z128rrk
    50351030U,	// VMOVDQU32Z128rrk_alt
    2499824566U,	// VMOVDQU32Z128rrkz
    2499824566U,	// VMOVDQU32Z128rrkz_alt
    1690550U,	// VMOVDQU32Z256mr
    17419190U,	// VMOVDQU32Z256mrk
    839928758U,	// VMOVDQU32Z256rm
    50351030U,	// VMOVDQU32Z256rmk
    2499824566U,	// VMOVDQU32Z256rmkz
    336612278U,	// VMOVDQU32Z256rr
    336612278U,	// VMOVDQU32Z256rr_alt
    50351030U,	// VMOVDQU32Z256rrk
    50351030U,	// VMOVDQU32Z256rrk_alt
    2499824566U,	// VMOVDQU32Z256rrkz
    2499824566U,	// VMOVDQU32Z256rrkz_alt
    1723318U,	// VMOVDQU32Zmr
    17451958U,	// VMOVDQU32Zmrk
    907037622U,	// VMOVDQU32Zrm
    50351030U,	// VMOVDQU32Zrmk
    2499824566U,	// VMOVDQU32Zrmkz
    336612278U,	// VMOVDQU32Zrr
    336612278U,	// VMOVDQU32Zrr_alt
    50351030U,	// VMOVDQU32Zrrk
    50351030U,	// VMOVDQU32Zrrk_alt
    2499824566U,	// VMOVDQU32Zrrkz
    2499824566U,	// VMOVDQU32Zrrkz_alt
    1313830U,	// VMOVDQU64Z128mr
    17042470U,	// VMOVDQU64Z128mrk
    437275686U,	// VMOVDQU64Z128rm
    50351142U,	// VMOVDQU64Z128rmk
    2499824678U,	// VMOVDQU64Z128rmkz
    336612390U,	// VMOVDQU64Z128rr
    336612390U,	// VMOVDQU64Z128rr_alt
    50351142U,	// VMOVDQU64Z128rrk
    50351142U,	// VMOVDQU64Z128rrk_alt
    2499824678U,	// VMOVDQU64Z128rrkz
    2499824678U,	// VMOVDQU64Z128rrkz_alt
    1690662U,	// VMOVDQU64Z256mr
    17419302U,	// VMOVDQU64Z256mrk
    839928870U,	// VMOVDQU64Z256rm
    50351142U,	// VMOVDQU64Z256rmk
    2499824678U,	// VMOVDQU64Z256rmkz
    336612390U,	// VMOVDQU64Z256rr
    336612390U,	// VMOVDQU64Z256rr_alt
    50351142U,	// VMOVDQU64Z256rrk
    50351142U,	// VMOVDQU64Z256rrk_alt
    2499824678U,	// VMOVDQU64Z256rrkz
    2499824678U,	// VMOVDQU64Z256rrkz_alt
    1723430U,	// VMOVDQU64Zmr
    17452070U,	// VMOVDQU64Zmrk
    907037734U,	// VMOVDQU64Zrm
    50351142U,	// VMOVDQU64Zrmk
    2499824678U,	// VMOVDQU64Zrmkz
    336612390U,	// VMOVDQU64Zrr
    336612390U,	// VMOVDQU64Zrr_alt
    50351142U,	// VMOVDQU64Zrrk
    50351142U,	// VMOVDQU64Zrrk_alt
    2499824678U,	// VMOVDQU64Zrrkz
    2499824678U,	// VMOVDQU64Zrrkz_alt
    1314089U,	// VMOVDQU8Z128mr
    17042729U,	// VMOVDQU8Z128mrk
    437275945U,	// VMOVDQU8Z128rm
    50351401U,	// VMOVDQU8Z128rmk
    2499824937U,	// VMOVDQU8Z128rmkz
    336612649U,	// VMOVDQU8Z128rr
    336612649U,	// VMOVDQU8Z128rr_alt
    50351401U,	// VMOVDQU8Z128rrk
    50351401U,	// VMOVDQU8Z128rrk_alt
    2499824937U,	// VMOVDQU8Z128rrkz
    2499824937U,	// VMOVDQU8Z128rrkz_alt
    1690921U,	// VMOVDQU8Z256mr
    17419561U,	// VMOVDQU8Z256mrk
    839929129U,	// VMOVDQU8Z256rm
    50351401U,	// VMOVDQU8Z256rmk
    2499824937U,	// VMOVDQU8Z256rmkz
    336612649U,	// VMOVDQU8Z256rr
    336612649U,	// VMOVDQU8Z256rr_alt
    50351401U,	// VMOVDQU8Z256rrk
    50351401U,	// VMOVDQU8Z256rrk_alt
    2499824937U,	// VMOVDQU8Z256rrkz
    2499824937U,	// VMOVDQU8Z256rrkz_alt
    1723689U,	// VMOVDQU8Zmr
    17452329U,	// VMOVDQU8Zmrk
    907037993U,	// VMOVDQU8Zrm
    50351401U,	// VMOVDQU8Zrmk
    2499824937U,	// VMOVDQU8Zrmkz
    336612649U,	// VMOVDQU8Zrr
    336612649U,	// VMOVDQU8Zrr_alt
    50351401U,	// VMOVDQU8Zrrk
    50351401U,	// VMOVDQU8Zrrk_alt
    2499824937U,	// VMOVDQU8Zrrkz
    2499824937U,	// VMOVDQU8Zrrkz_alt
    1696373U,	// VMOVDQUYmr
    839934581U,	// VMOVDQUYrm
    336618101U,	// VMOVDQUYrr
    336618101U,	// VMOVDQUYrr_REV
    1319541U,	// VMOVDQUmr
    437281397U,	// VMOVDQUrm
    336618101U,	// VMOVDQUrr
    336618101U,	// VMOVDQUrr_REV
    2484100873U,	// VMOVHLPSZrr
    2484100873U,	// VMOVHLPSrr
    1184336U,	// VMOVHPDmr
    2484097616U,	// VMOVHPDrm
    1187573U,	// VMOVHPSmr
    2484100853U,	// VMOVHPSrm
    2484100843U,	// VMOVLHPSZrr
    2484100843U,	// VMOVLHPSrr
    1184386U,	// VMOVLPDmr
    2484097666U,	// VMOVLPDrm
    1187633U,	// VMOVLPSmr
    2484100913U,	// VMOVLPSrm
    336613977U,	// VMOVMSKPDYrr
    336613977U,	// VMOVMSKPDrr
    336617214U,	// VMOVMSKPSYrr
    336617214U,	// VMOVMSKPSrr
    839929148U,	// VMOVNTDQAYrm
    437275964U,	// VMOVNTDQAZ128rm
    839929148U,	// VMOVNTDQAZ256rm
    907038012U,	// VMOVNTDQAZrm
    437275964U,	// VMOVNTDQArm
    1678034U,	// VMOVNTDQYmr
    1317586U,	// VMOVNTDQZ128mr
    1694418U,	// VMOVNTDQZ256mr
    1727186U,	// VMOVNTDQZmr
    1645266U,	// VMOVNTDQmr
    1675991U,	// VMOVNTPDYmr
    1643223U,	// VMOVNTPDZ128mr
    1675991U,	// VMOVNTPDZ256mr
    1708759U,	// VMOVNTPDZmr
    1643223U,	// VMOVNTPDmr
    1679250U,	// VMOVNTPSYmr
    1646482U,	// VMOVNTPSZ128mr
    1679250U,	// VMOVNTPSZ256mr
    1712018U,	// VMOVNTPSZmr
    1646482U,	// VMOVNTPSmr
    1119578U,	// VMOVPDI2DIZmr
    336614746U,	// VMOVPDI2DIZrr
    1119578U,	// VMOVPDI2DImr
    336614746U,	// VMOVPDI2DIrr
    1137637U,	// VMOVPQI2QImr
    336616421U,	// VMOVPQI2QIrr
    1137637U,	// VMOVPQIto64Zmr
    336616421U,	// VMOVPQIto64Zrr
    336616421U,	// VMOVPQIto64rr
    370170853U,	// VMOVQI2PQIZrm
    370170853U,	// VMOVQI2PQIrm
    1180802U,	// VMOVSDZmr
    571491458U,	// VMOVSDZrm
    2484094082U,	// VMOVSDZrr
    2484098253U,	// VMOVSDZrr_REV
    50349186U,	// VMOVSDZrrk
    1184973U,	// VMOVSDmr
    571495629U,	// VMOVSDrm
    2484098253U,	// VMOVSDrr
    2484098253U,	// VMOVSDrr_REV
    1137637U,	// VMOVSDto64Zmr
    336616421U,	// VMOVSDto64Zrr
    1137637U,	// VMOVSDto64mr
    336616421U,	// VMOVSDto64rr
    940595618U,	// VMOVSHDUPYrm
    336615842U,	// VMOVSHDUPYrr
    974144918U,	// VMOVSHDUPZrm
    336610710U,	// VMOVSHDUPZrr
    537942434U,	// VMOVSHDUPrm
    336615842U,	// VMOVSHDUPrr
    940595629U,	// VMOVSLDUPYrm
    336615853U,	// VMOVSLDUPYrr
    974144930U,	// VMOVSLDUPZrm
    336610722U,	// VMOVSLDUPZrr
    537942445U,	// VMOVSLDUPrm
    336615853U,	// VMOVSLDUPrr
    1119578U,	// VMOVSS2DIZmr
    336614746U,	// VMOVSS2DIZrr
    1119578U,	// VMOVSS2DImr
    336614746U,	// VMOVSS2DIrr
    1166002U,	// VMOVSSZmr
    605047474U,	// VMOVSSZrm
    2484095666U,	// VMOVSSZrr
    2484101481U,	// VMOVSSZrr_REV
    50350770U,	// VMOVSSZrrk
    1171817U,	// VMOVSSmr
    605053289U,	// VMOVSSrm
    2484101481U,	// VMOVSSrr
    2484101481U,	// VMOVSSrr_REV
    1676019U,	// VMOVUPDYmr
    940593907U,	// VMOVUPDYrm
    336614131U,	// VMOVUPDYrr
    336614131U,	// VMOVUPDYrr_REV
    1643251U,	// VMOVUPDZ128mr
    17371891U,	// VMOVUPDZ128mrk
    537940723U,	// VMOVUPDZ128rm
    50352883U,	// VMOVUPDZ128rmk
    2499826419U,	// VMOVUPDZ128rmkz
    336614131U,	// VMOVUPDZ128rr
    336614131U,	// VMOVUPDZ128rr_alt
    50352883U,	// VMOVUPDZ128rrk
    50352883U,	// VMOVUPDZ128rrk_alt
    2499826419U,	// VMOVUPDZ128rrkz
    2499826419U,	// VMOVUPDZ128rrkz_alt
    1676019U,	// VMOVUPDZ256mr
    17404659U,	// VMOVUPDZ256mrk
    940593907U,	// VMOVUPDZ256rm
    50352883U,	// VMOVUPDZ256rmk
    2499826419U,	// VMOVUPDZ256rmkz
    336614131U,	// VMOVUPDZ256rr
    336614131U,	// VMOVUPDZ256rr_alt
    50352883U,	// VMOVUPDZ256rrk
    50352883U,	// VMOVUPDZ256rrk_alt
    2499826419U,	// VMOVUPDZ256rrkz
    2499826419U,	// VMOVUPDZ256rrkz_alt
    1708787U,	// VMOVUPDZmr
    17437427U,	// VMOVUPDZmrk
    974148339U,	// VMOVUPDZrm
    50352883U,	// VMOVUPDZrmk
    2499826419U,	// VMOVUPDZrmkz
    336614131U,	// VMOVUPDZrr
    336614131U,	// VMOVUPDZrr_alt
    50352883U,	// VMOVUPDZrrk
    50352883U,	// VMOVUPDZrrk_alt
    2499826419U,	// VMOVUPDZrrkz
    2499826419U,	// VMOVUPDZrrkz_alt
    1643251U,	// VMOVUPDmr
    537940723U,	// VMOVUPDrm
    336614131U,	// VMOVUPDrr
    336614131U,	// VMOVUPDrr_REV
    1679299U,	// VMOVUPSYmr
    940597187U,	// VMOVUPSYrm
    336617411U,	// VMOVUPSYrr
    336617411U,	// VMOVUPSYrr_REV
    1646531U,	// VMOVUPSZ128mr
    17375171U,	// VMOVUPSZ128mrk
    537944003U,	// VMOVUPSZ128rm
    50356163U,	// VMOVUPSZ128rmk
    2499829699U,	// VMOVUPSZ128rmkz
    336617411U,	// VMOVUPSZ128rr
    336617411U,	// VMOVUPSZ128rr_alt
    50356163U,	// VMOVUPSZ128rrk
    50356163U,	// VMOVUPSZ128rrk_alt
    2499829699U,	// VMOVUPSZ128rrkz
    2499829699U,	// VMOVUPSZ128rrkz_alt
    1679299U,	// VMOVUPSZ256mr
    17407939U,	// VMOVUPSZ256mrk
    940597187U,	// VMOVUPSZ256rm
    50356163U,	// VMOVUPSZ256rmk
    2499829699U,	// VMOVUPSZ256rmkz
    336617411U,	// VMOVUPSZ256rr
    336617411U,	// VMOVUPSZ256rr_alt
    50356163U,	// VMOVUPSZ256rrk
    50356163U,	// VMOVUPSZ256rrk_alt
    2499829699U,	// VMOVUPSZ256rrkz
    2499829699U,	// VMOVUPSZ256rrkz_alt
    1712067U,	// VMOVUPSZmr
    17440707U,	// VMOVUPSZmrk
    974151619U,	// VMOVUPSZrm
    50356163U,	// VMOVUPSZrmk
    2499829699U,	// VMOVUPSZrmkz
    336617411U,	// VMOVUPSZrr
    336617411U,	// VMOVUPSZrr_alt
    50356163U,	// VMOVUPSZrrk
    50356163U,	// VMOVUPSZrrk_alt
    2499829699U,	// VMOVUPSZrrkz
    2499829699U,	// VMOVUPSZrrkz_alt
    1646531U,	// VMOVUPSmr
    537944003U,	// VMOVUPSrm
    336617411U,	// VMOVUPSrr
    336617411U,	// VMOVUPSrr_REV
    437279717U,	// VMOVZPQILo2PQIZrm
    336616421U,	// VMOVZPQILo2PQIZrr
    437279717U,	// VMOVZPQILo2PQIrm
    336616421U,	// VMOVZPQILo2PQIrr
    370170853U,	// VMOVZQI2PQIrm
    336616421U,	// VMOVZQI2PQIrr
    2484101822U,	// VMPSADBWYrmi
    2484101822U,	// VMPSADBWYrri
    2484101822U,	// VMPSADBWrmi
    2484101822U,	// VMPSADBWrri
    86060U,	// VMPTRLDm
    90700U,	// VMPTRSTm
    1117989U,	// VMREAD32rm
    336613157U,	// VMREAD32rr
    1134373U,	// VMREAD64rm
    336613157U,	// VMREAD64rr
    11440U,	// VMRESUME
    12277U,	// VMRUN32
    12332U,	// VMRUN64
    12266U,	// VMSAVE32
    12321U,	// VMSAVE64
    2484097658U,	// VMULPDYrm
    2484097658U,	// VMULPDYrr
    2484093708U,	// VMULPDZrm
    2484093708U,	// VMULPDZrmb
    352338700U,	// VMULPDZrmbk
    2499822348U,	// VMULPDZrmbkz
    352342650U,	// VMULPDZrmk
    2499826298U,	// VMULPDZrmkz
    2484093708U,	// VMULPDZrr
    352338700U,	// VMULPDZrrk
    2499822348U,	// VMULPDZrrkz
    2484097658U,	// VMULPDrm
    2484097658U,	// VMULPDrr
    2484100905U,	// VMULPSYrm
    2484100905U,	// VMULPSYrr
    2484095412U,	// VMULPSZrm
    2484095412U,	// VMULPSZrmb
    352340404U,	// VMULPSZrmbk
    2499824052U,	// VMULPSZrmbkz
    352345897U,	// VMULPSZrmk
    2499829545U,	// VMULPSZrmkz
    2484095412U,	// VMULPSZrr
    352340404U,	// VMULPSZrrk
    2499824052U,	// VMULPSZrrkz
    2484100905U,	// VMULPSrm
    2484100905U,	// VMULPSrr
    2484098173U,	// VMULSDZrm
    2484098173U,	// VMULSDZrr
    2484098173U,	// VMULSDrm
    2484098173U,	// VMULSDrm_Int
    2484098173U,	// VMULSDrr
    2484098173U,	// VMULSDrr_Int
    2484101399U,	// VMULSSZrm
    2484101399U,	// VMULSSZrr
    2484101399U,	// VMULSSrm
    2484101399U,	// VMULSSrm_Int
    2484101399U,	// VMULSSrr
    2484101399U,	// VMULSSrr_Int
    303060650U,	// VMWRITE32rm
    336615082U,	// VMWRITE32rr
    370169514U,	// VMWRITE64rm
    336615082U,	// VMWRITE64rr
    11528U,	// VMXOFF
    88263U,	// VMXON
    2484097736U,	// VORPDYrm
    2484097736U,	// VORPDYrr
    2484097736U,	// VORPDrm
    2484097736U,	// VORPDrr
    2484100983U,	// VORPSYrm
    2484100983U,	// VORPSYrr
    2484100983U,	// VORPSrm
    2484100983U,	// VORPSrr
    437276147U,	// VPABSBrm128
    839929331U,	// VPABSBrm256
    336612851U,	// VPABSBrr128
    336612851U,	// VPABSBrr256
    907035730U,	// VPABSDZrm
    2450539602U,	// VPABSDZrmb
    352339026U,	// VPABSDZrmbk
    2499822674U,	// VPABSDZrmbkz
    352339026U,	// VPABSDZrmk
    2499822674U,	// VPABSDZrmkz
    336610386U,	// VPABSDZrr
    352339026U,	// VPABSDZrrk
    2499822674U,	// VPABSDZrrkz
    437277726U,	// VPABSDrm128
    839930910U,	// VPABSDrm256
    336614430U,	// VPABSDrr128
    336614430U,	// VPABSDrr256
    907036575U,	// VPABSQZrm
    370165663U,	// VPABSQZrmb
    352339871U,	// VPABSQZrmbk
    2499823519U,	// VPABSQZrmbkz
    352339871U,	// VPABSQZrmk
    2499823519U,	// VPABSQZrmkz
    336611231U,	// VPABSQZrr
    352339871U,	// VPABSQZrrk
    2499823519U,	// VPABSQZrrkz
    437281800U,	// VPABSWrm128
    839934984U,	// VPABSWrm256
    336618504U,	// VPABSWrr128
    336618504U,	// VPABSWrr256
    2484101967U,	// VPACKSSDWYrm
    2484101967U,	// VPACKSSDWYrr
    2484101967U,	// VPACKSSDWrm
    2484101967U,	// VPACKSSDWrr
    2484096690U,	// VPACKSSWBYrm
    2484096690U,	// VPACKSSWBYrr
    2484096690U,	// VPACKSSWBrm
    2484096690U,	// VPACKSSWBrr
    2484101978U,	// VPACKUSDWYrm
    2484101978U,	// VPACKUSDWYrr
    2484101978U,	// VPACKUSDWrm
    2484101978U,	// VPACKUSDWrr
    2484096701U,	// VPACKUSWBYrm
    2484096701U,	// VPACKUSWBYrr
    2484096701U,	// VPACKUSWBrm
    2484096701U,	// VPACKUSWBrr
    2484096406U,	// VPADDBYrm
    2484096406U,	// VPADDBYrr
    2484096406U,	// VPADDBrm
    2484096406U,	// VPADDBrr
    2484096918U,	// VPADDDYrm
    2484096918U,	// VPADDDYrr
    2484093170U,	// VPADDDZrm
    2484093170U,	// VPADDDZrmb
    50348274U,	// VPADDDZrmbk
    2499821810U,	// VPADDDZrmbkz
    50348274U,	// VPADDDZrmk
    2499821810U,	// VPADDDZrmkz
    2484093170U,	// VPADDDZrr
    50348274U,	// VPADDDZrrk
    2499821810U,	// VPADDDZrrkz
    2484096918U,	// VPADDDrm
    2484096918U,	// VPADDDrr
    2484099675U,	// VPADDQYrm
    2484099675U,	// VPADDQYrr
    2484094514U,	// VPADDQZrm
    2484094514U,	// VPADDQZrmb
    50349618U,	// VPADDQZrmbk
    2499823154U,	// VPADDQZrmbkz
    50349618U,	// VPADDQZrmk
    2499823154U,	// VPADDQZrmkz
    2484094514U,	// VPADDQZrr
    50349618U,	// VPADDQZrrk
    2499823154U,	// VPADDQZrrkz
    2484099675U,	// VPADDQrm
    2484099675U,	// VPADDQrr
    2484096516U,	// VPADDSBYrm
    2484096516U,	// VPADDSBYrr
    2484096516U,	// VPADDSBrm
    2484096516U,	// VPADDSBrr
    2484102201U,	// VPADDSWYrm
    2484102201U,	// VPADDSWYrr
    2484102201U,	// VPADDSWrm
    2484102201U,	// VPADDSWrr
    2484096558U,	// VPADDUSBYrm
    2484096558U,	// VPADDUSBYrr
    2484096558U,	// VPADDUSBrm
    2484096558U,	// VPADDUSBrr
    2484102274U,	// VPADDUSWYrm
    2484102274U,	// VPADDUSWYrr
    2484102274U,	// VPADDUSWrm
    2484102274U,	// VPADDUSWrr
    2484101949U,	// VPADDWYrm
    2484101949U,	// VPADDWYrr
    2484101949U,	// VPADDWrm
    2484101949U,	// VPADDWrr
    2484100171U,	// VPALIGNR128rm
    2484100171U,	// VPALIGNR128rr
    2484100171U,	// VPALIGNR256rm
    2484100171U,	// VPALIGNR256rr
    2484093187U,	// VPANDDZrm
    2484093187U,	// VPANDDZrmb
    50348291U,	// VPANDDZrmbk
    2499821827U,	// VPANDDZrmbkz
    50348291U,	// VPANDDZrmk
    2499821827U,	// VPANDDZrmkz
    2484093187U,	// VPANDDZrr
    50348291U,	// VPANDDZrrk
    2499821827U,	// VPANDDZrrkz
    2484093314U,	// VPANDNDZrm
    2484093314U,	// VPANDNDZrmb
    50348418U,	// VPANDNDZrmbk
    2499821954U,	// VPANDNDZrmbkz
    50348418U,	// VPANDNDZrmk
    2499821954U,	// VPANDNDZrmkz
    2484093314U,	// VPANDNDZrr
    50348418U,	// VPANDNDZrrk
    2499821954U,	// VPANDNDZrrkz
    2484094791U,	// VPANDNQZrm
    2484094791U,	// VPANDNQZrmb
    50349895U,	// VPANDNQZrmbk
    2499823431U,	// VPANDNQZrmbkz
    50349895U,	// VPANDNQZrmk
    2499823431U,	// VPANDNQZrmkz
    2484094791U,	// VPANDNQZrr
    50349895U,	// VPANDNQZrrk
    2499823431U,	// VPANDNQZrrkz
    2484099248U,	// VPANDNYrm
    2484099248U,	// VPANDNYrr
    2484099248U,	// VPANDNrm
    2484099248U,	// VPANDNrr
    2484094567U,	// VPANDQZrm
    2484094567U,	// VPANDQZrmb
    50349671U,	// VPANDQZrmbk
    2499823207U,	// VPANDQZrmbkz
    50349671U,	// VPANDQZrmk
    2499823207U,	// VPANDQZrmkz
    2484094567U,	// VPANDQZrr
    50349671U,	// VPANDQZrrk
    2499823207U,	// VPANDQZrrkz
    2484097093U,	// VPANDYrm
    2484097093U,	// VPANDYrr
    2484097093U,	// VPANDrm
    2484097093U,	// VPANDrr
    2484096423U,	// VPAVGBYrm
    2484096423U,	// VPAVGBYrr
    2484096423U,	// VPAVGBrm
    2484096423U,	// VPAVGBrr
    2484102004U,	// VPAVGWYrm
    2484102004U,	// VPAVGWYrr
    2484102004U,	// VPAVGWrm
    2484102004U,	// VPAVGWrr
    2484096926U,	// VPBLENDDYrmi
    2484096926U,	// VPBLENDDYrri
    2484096926U,	// VPBLENDDrmi
    2484096926U,	// VPBLENDDrri
    352338253U,	// VPBLENDMDZrm
    352338253U,	// VPBLENDMDZrr
    352339730U,	// VPBLENDMQZrm
    352339730U,	// VPBLENDMQZrr
    2484096672U,	// VPBLENDVBYrm
    2484096672U,	// VPBLENDVBYrr
    2484096672U,	// VPBLENDVBrm
    2484096672U,	// VPBLENDVBrr
    2484101957U,	// VPBLENDWYrmi
    2484101957U,	// VPBLENDWYrri
    2484101957U,	// VPBLENDWrmi
    2484101957U,	// VPBLENDWrri
    504385129U,	// VPBROADCASTBYrm
    336612969U,	// VPBROADCASTBYrr
    504385129U,	// VPBROADCASTBrm
    336612969U,	// VPBROADCASTBrr
    303060225U,	// VPBROADCASTDYrm
    336614657U,	// VPBROADCASTDYrr
    2499822774U,	// VPBROADCASTDZkrm
    2499822774U,	// VPBROADCASTDZkrr
    303056054U,	// VPBROADCASTDZrm
    336610486U,	// VPBROADCASTDZrr
    2499822774U,	// VPBROADCASTDrZkrr
    336610486U,	// VPBROADCASTDrZrr
    303060225U,	// VPBROADCASTDrm
    336614657U,	// VPBROADCASTDrr
    336610734U,	// VPBROADCASTMB2Qrr
    336609462U,	// VPBROADCASTMW2Drr
    370170791U,	// VPBROADCASTQYrm
    336616359U,	// VPBROADCASTQYrr
    2499823581U,	// VPBROADCASTQZkrm
    2499823581U,	// VPBROADCASTQZkrr
    370165725U,	// VPBROADCASTQZrm
    336611293U,	// VPBROADCASTQZrr
    2499823581U,	// VPBROADCASTQrZkrr
    336611293U,	// VPBROADCASTQrZrr
    370170791U,	// VPBROADCASTQrm
    336616359U,	// VPBROADCASTQrr
    470836398U,	// VPBROADCASTWYrm
    336618670U,	// VPBROADCASTWYrr
    470836398U,	// VPBROADCASTWrm
    336618670U,	// VPBROADCASTWrr
    2484099770U,	// VPCLMULQDQrm
    2484099770U,	// VPCLMULQDQrr
    2484101788U,	// VPCMOVmr
    2484101788U,	// VPCMOVmrY
    2484101788U,	// VPCMOVrm
    2484101788U,	// VPCMOVrmY
    2484101788U,	// VPCMOVrr
    2484101788U,	// VPCMOVrrY
    1128623605U,	// VPCMPDZrmi
    2484097675U,	// VPCMPDZrmi_alt
    352342667U,	// VPCMPDZrmik_alt
    54898165U,	// VPCMPDZrri
    2484097675U,	// VPCMPDZrri_alt
    352342667U,	// VPCMPDZrrik_alt
    2484096471U,	// VPCMPEQBYrm
    2484096471U,	// VPCMPEQBYrr
    2484096471U,	// VPCMPEQBZ128rm
    352341463U,	// VPCMPEQBZ128rmk
    2484096471U,	// VPCMPEQBZ128rr
    352341463U,	// VPCMPEQBZ128rrk
    2484096471U,	// VPCMPEQBZ256rm
    352341463U,	// VPCMPEQBZ256rmk
    2484096471U,	// VPCMPEQBZ256rr
    352341463U,	// VPCMPEQBZ256rrk
    2484096471U,	// VPCMPEQBZrm
    352341463U,	// VPCMPEQBZrmk
    2484096471U,	// VPCMPEQBZrr
    352341463U,	// VPCMPEQBZrrk
    2484096471U,	// VPCMPEQBrm
    2484096471U,	// VPCMPEQBrr
    2484097836U,	// VPCMPEQDYrm
    2484097836U,	// VPCMPEQDYrr
    2484097836U,	// VPCMPEQDZ128rm
    2484097836U,	// VPCMPEQDZ128rmb
    352342828U,	// VPCMPEQDZ128rmbk
    352342828U,	// VPCMPEQDZ128rmk
    2484097836U,	// VPCMPEQDZ128rr
    352342828U,	// VPCMPEQDZ128rrk
    2484097836U,	// VPCMPEQDZ256rm
    2484097836U,	// VPCMPEQDZ256rmb
    352342828U,	// VPCMPEQDZ256rmbk
    352342828U,	// VPCMPEQDZ256rmk
    2484097836U,	// VPCMPEQDZ256rr
    352342828U,	// VPCMPEQDZ256rrk
    2484097836U,	// VPCMPEQDZrm
    2484097836U,	// VPCMPEQDZrmb
    352342828U,	// VPCMPEQDZrmbk
    352342828U,	// VPCMPEQDZrmk
    2484097836U,	// VPCMPEQDZrr
    352342828U,	// VPCMPEQDZrrk
    2484097836U,	// VPCMPEQDrm
    2484097836U,	// VPCMPEQDrr
    2484099911U,	// VPCMPEQQYrm
    2484099911U,	// VPCMPEQQYrr
    2484099911U,	// VPCMPEQQZ128rm
    2484099911U,	// VPCMPEQQZ128rmb
    352344903U,	// VPCMPEQQZ128rmbk
    352344903U,	// VPCMPEQQZ128rmk
    2484099911U,	// VPCMPEQQZ128rr
    352344903U,	// VPCMPEQQZ128rrk
    2484099911U,	// VPCMPEQQZ256rm
    2484099911U,	// VPCMPEQQZ256rmb
    352344903U,	// VPCMPEQQZ256rmbk
    352344903U,	// VPCMPEQQZ256rmk
    2484099911U,	// VPCMPEQQZ256rr
    352344903U,	// VPCMPEQQZ256rrk
    2484099911U,	// VPCMPEQQZrm
    2484099911U,	// VPCMPEQQZrmb
    352344903U,	// VPCMPEQQZrmbk
    352344903U,	// VPCMPEQQZrmk
    2484099911U,	// VPCMPEQQZrr
    352344903U,	// VPCMPEQQZrrk
    2484099911U,	// VPCMPEQQrm
    2484099911U,	// VPCMPEQQrr
    2484102109U,	// VPCMPEQWYrm
    2484102109U,	// VPCMPEQWYrr
    2484102109U,	// VPCMPEQWZ128rm
    352347101U,	// VPCMPEQWZ128rmk
    2484102109U,	// VPCMPEQWZ128rr
    352347101U,	// VPCMPEQWZ128rrk
    2484102109U,	// VPCMPEQWZ256rm
    352347101U,	// VPCMPEQWZ256rmk
    2484102109U,	// VPCMPEQWZ256rr
    352347101U,	// VPCMPEQWZ256rrk
    2484102109U,	// VPCMPEQWZrm
    352347101U,	// VPCMPEQWZrmk
    2484102109U,	// VPCMPEQWZrr
    352347101U,	// VPCMPEQWZrrk
    2484102109U,	// VPCMPEQWrm
    2484102109U,	// VPCMPEQWrr
    0U,	// VPCMPESTRIMEM
    0U,	// VPCMPESTRIREG
    2584762270U,	// VPCMPESTRIrm
    2484098974U,	// VPCMPESTRIrr
    0U,	// VPCMPESTRM128MEM
    0U,	// VPCMPESTRM128REG
    2584762520U,	// VPCMPESTRM128rm
    2484099224U,	// VPCMPESTRM128rr
    2484096599U,	// VPCMPGTBYrm
    2484096599U,	// VPCMPGTBYrr
    2484096599U,	// VPCMPGTBZ128rm
    352341591U,	// VPCMPGTBZ128rmk
    2484096599U,	// VPCMPGTBZ128rr
    352341591U,	// VPCMPGTBZ128rrk
    2484096599U,	// VPCMPGTBZ256rm
    352341591U,	// VPCMPGTBZ256rmk
    2484096599U,	// VPCMPGTBZ256rr
    352341591U,	// VPCMPGTBZ256rrk
    2484096599U,	// VPCMPGTBZrm
    352341591U,	// VPCMPGTBZrmk
    2484096599U,	// VPCMPGTBZrr
    352341591U,	// VPCMPGTBZrrk
    2484096599U,	// VPCMPGTBrm
    2484096599U,	// VPCMPGTBrr
    2484098287U,	// VPCMPGTDYrm
    2484098287U,	// VPCMPGTDYrr
    2484098287U,	// VPCMPGTDZ128rm
    2484098287U,	// VPCMPGTDZ128rmb
    352343279U,	// VPCMPGTDZ128rmbk
    352343279U,	// VPCMPGTDZ128rmk
    2484098287U,	// VPCMPGTDZ128rr
    352343279U,	// VPCMPGTDZ128rrk
    2484098287U,	// VPCMPGTDZ256rm
    2484098287U,	// VPCMPGTDZ256rmb
    352343279U,	// VPCMPGTDZ256rmbk
    352343279U,	// VPCMPGTDZ256rmk
    2484098287U,	// VPCMPGTDZ256rr
    352343279U,	// VPCMPGTDZ256rrk
    2484098287U,	// VPCMPGTDZrm
    2484098287U,	// VPCMPGTDZrmb
    352343279U,	// VPCMPGTDZrmbk
    352343279U,	// VPCMPGTDZrmk
    2484098287U,	// VPCMPGTDZrr
    352343279U,	// VPCMPGTDZrrk
    2484098287U,	// VPCMPGTDrm
    2484098287U,	// VPCMPGTDrr
    2484099972U,	// VPCMPGTQYrm
    2484099972U,	// VPCMPGTQYrr
    2484099972U,	// VPCMPGTQZ128rm
    2484099972U,	// VPCMPGTQZ128rmb
    352344964U,	// VPCMPGTQZ128rmbk
    352344964U,	// VPCMPGTQZ128rmk
    2484099972U,	// VPCMPGTQZ128rr
    352344964U,	// VPCMPGTQZ128rrk
    2484099972U,	// VPCMPGTQZ256rm
    2484099972U,	// VPCMPGTQZ256rmb
    352344964U,	// VPCMPGTQZ256rmbk
    352344964U,	// VPCMPGTQZ256rmk
    2484099972U,	// VPCMPGTQZ256rr
    352344964U,	// VPCMPGTQZ256rrk
    2484099972U,	// VPCMPGTQZrm
    2484099972U,	// VPCMPGTQZrmb
    352344964U,	// VPCMPGTQZrmbk
    352344964U,	// VPCMPGTQZrmk
    2484099972U,	// VPCMPGTQZrr
    352344964U,	// VPCMPGTQZrrk
    2484099972U,	// VPCMPGTQrm
    2484099972U,	// VPCMPGTQrr
    2484102300U,	// VPCMPGTWYrm
    2484102300U,	// VPCMPGTWYrr
    2484102300U,	// VPCMPGTWZ128rm
    352347292U,	// VPCMPGTWZ128rmk
    2484102300U,	// VPCMPGTWZ128rr
    352347292U,	// VPCMPGTWZ128rrk
    2484102300U,	// VPCMPGTWZ256rm
    352347292U,	// VPCMPGTWZ256rmk
    2484102300U,	// VPCMPGTWZ256rr
    352347292U,	// VPCMPGTWZ256rrk
    2484102300U,	// VPCMPGTWZrm
    352347292U,	// VPCMPGTWZrmk
    2484102300U,	// VPCMPGTWZrr
    352347292U,	// VPCMPGTWZrrk
    2484102300U,	// VPCMPGTWrm
    2484102300U,	// VPCMPGTWrr
    0U,	// VPCMPISTRIMEM
    0U,	// VPCMPISTRIREG
    2584762282U,	// VPCMPISTRIrm
    2484098986U,	// VPCMPISTRIrr
    0U,	// VPCMPISTRM128MEM
    0U,	// VPCMPISTRM128REG
    2584762532U,	// VPCMPISTRM128rm
    2484099236U,	// VPCMPISTRM128rr
    1129672181U,	// VPCMPQZrmi
    2484099903U,	// VPCMPQZrmi_alt
    352344895U,	// VPCMPQZrmik_alt
    55946741U,	// VPCMPQZrri
    2484099903U,	// VPCMPQZrri_alt
    352344895U,	// VPCMPQZrrik_alt
    1130720757U,	// VPCMPUDZrmi
    2484098337U,	// VPCMPUDZrmi_alt
    352343329U,	// VPCMPUDZrmik_alt
    56995317U,	// VPCMPUDZrri
    2484098337U,	// VPCMPUDZrri_alt
    352343329U,	// VPCMPUDZrrik_alt
    1131769333U,	// VPCMPUQZrmi
    2484100030U,	// VPCMPUQZrmi_alt
    352345022U,	// VPCMPUQZrmik_alt
    58043893U,	// VPCMPUQZrri
    2484100030U,	// VPCMPUQZrri_alt
    352345022U,	// VPCMPUQZrrik_alt
    2484096454U,	// VPCOMBmi
    2484096454U,	// VPCOMBri
    2484097077U,	// VPCOMDmi
    2484097077U,	// VPCOMDri
    2484099887U,	// VPCOMQmi
    2484099887U,	// VPCOMQri
    2484096631U,	// VPCOMUBmi
    2484096631U,	// VPCOMUBri
    2484098319U,	// VPCOMUDmi
    2484098319U,	// VPCOMUDri
    2484100021U,	// VPCOMUQmi
    2484100021U,	// VPCOMUQri
    2484102342U,	// VPCOMUWmi
    2484102342U,	// VPCOMUWri
    2484102092U,	// VPCOMWmi
    2484102092U,	// VPCOMWri
    907035797U,	// VPCONFLICTDrm
    2450539669U,	// VPCONFLICTDrmb
    50349205U,	// VPCONFLICTDrmbk
    2499822741U,	// VPCONFLICTDrmbkz
    50349205U,	// VPCONFLICTDrmk
    2499822741U,	// VPCONFLICTDrmkz
    336610453U,	// VPCONFLICTDrr
    50349205U,	// VPCONFLICTDrrk
    2499822741U,	// VPCONFLICTDrrkz
    907036604U,	// VPCONFLICTQrm
    370165692U,	// VPCONFLICTQrmb
    50350012U,	// VPCONFLICTQrmbk
    2499823548U,	// VPCONFLICTQrmbkz
    50350012U,	// VPCONFLICTQrmk
    2499823548U,	// VPCONFLICTQrmkz
    336611260U,	// VPCONFLICTQrr
    50350012U,	// VPCONFLICTQrrk
    2499823548U,	// VPCONFLICTQrrkz
    2484096187U,	// VPERM2F128rm
    2484096187U,	// VPERM2F128rr
    2484096242U,	// VPERM2I128rm
    2484096242U,	// VPERM2I128rr
    2484097085U,	// VPERMDYrm
    2484097085U,	// VPERMDYrr
    2484093285U,	// VPERMDZrm
    2484093285U,	// VPERMDZrr
    2182103200U,	// VPERMI2Drm
    50348192U,	// VPERMI2Drmk
    2197831840U,	// VPERMI2Drmkz
    2182103200U,	// VPERMI2Drr
    50348192U,	// VPERMI2Drrk
    2197831840U,	// VPERMI2Drrkz
    2182103538U,	// VPERMI2PDrm
    50348530U,	// VPERMI2PDrmk
    2197832178U,	// VPERMI2PDrmkz
    2182103538U,	// VPERMI2PDrr
    50348530U,	// VPERMI2PDrrk
    2197832178U,	// VPERMI2PDrrkz
    2182105254U,	// VPERMI2PSrm
    50350246U,	// VPERMI2PSrmk
    2197833894U,	// VPERMI2PSrmkz
    2182105254U,	// VPERMI2PSrr
    50350246U,	// VPERMI2PSrrk
    2197833894U,	// VPERMI2PSrrkz
    2182104512U,	// VPERMI2Qrm
    50349504U,	// VPERMI2Qrmk
    2197833152U,	// VPERMI2Qrmkz
    2182104512U,	// VPERMI2Qrr
    50349504U,	// VPERMI2Qrrk
    2197833152U,	// VPERMI2Qrrkz
    2484097306U,	// VPERMIL2PDmr
    2484097306U,	// VPERMIL2PDmrY
    2484097306U,	// VPERMIL2PDrm
    2484097306U,	// VPERMIL2PDrmY
    2484097306U,	// VPERMIL2PDrr
    2484097306U,	// VPERMIL2PDrrY
    2484100552U,	// VPERMIL2PSmr
    2484100552U,	// VPERMIL2PSmrY
    2484100552U,	// VPERMIL2PSrm
    2484100552U,	// VPERMIL2PSrmY
    2484100552U,	// VPERMIL2PSrr
    2484100552U,	// VPERMIL2PSrrY
    3088077412U,	// VPERMILPDYmi
    2484097636U,	// VPERMILPDYri
    2484097636U,	// VPERMILPDYrm
    2484097636U,	// VPERMILPDYrr
    3054519040U,	// VPERMILPDZmi
    2484093696U,	// VPERMILPDZri
    2685424228U,	// VPERMILPDmi
    2484097636U,	// VPERMILPDri
    2484097636U,	// VPERMILPDrm
    2484097636U,	// VPERMILPDrr
    3088080659U,	// VPERMILPSYmi
    2484100883U,	// VPERMILPSYri
    2484100883U,	// VPERMILPSYrm
    2484100883U,	// VPERMILPSYrr
    3054520744U,	// VPERMILPSZmi
    2484095400U,	// VPERMILPSZri
    2685427475U,	// VPERMILPSmi
    2484100883U,	// VPERMILPSri
    2484100883U,	// VPERMILPSrm
    2484100883U,	// VPERMILPSrr
    2987414163U,	// VPERMPDYmi
    2484097683U,	// VPERMPDYri
    3121627937U,	// VPERMPDZmi
    2484093729U,	// VPERMPDZri
    2484093729U,	// VPERMPDZrm
    2484093729U,	// VPERMPDZrr
    2484100922U,	// VPERMPSYrm
    2484100922U,	// VPERMPSYrr
    2484095433U,	// VPERMPSZrm
    2484095433U,	// VPERMPSZrr
    2987416375U,	// VPERMQYmi
    2484099895U,	// VPERMQYri
    3054520106U,	// VPERMQZmi
    2484094762U,	// VPERMQZri
    2484094762U,	// VPERMQZrm
    2484094762U,	// VPERMQZrr
    2182103211U,	// VPERMT2Drm
    50348203U,	// VPERMT2Drmk
    2197831851U,	// VPERMT2Drmkz
    2182103211U,	// VPERMT2Drr
    50348203U,	// VPERMT2Drrk
    2197831851U,	// VPERMT2Drrkz
    2182103587U,	// VPERMT2PDrm
    50348579U,	// VPERMT2PDrmk
    2197832227U,	// VPERMT2PDrmkz
    2182103587U,	// VPERMT2PDrr
    50348579U,	// VPERMT2PDrrk
    2197832227U,	// VPERMT2PDrrkz
    2182105291U,	// VPERMT2PSrm
    50350283U,	// VPERMT2PSrmk
    2197833931U,	// VPERMT2PSrmkz
    2182105291U,	// VPERMT2PSrr
    50350283U,	// VPERMT2PSrrk
    2197833931U,	// VPERMT2PSrrkz
    2182104523U,	// VPERMT2Qrm
    50349515U,	// VPERMT2Qrmk
    2197833163U,	// VPERMT2Qrmkz
    2182104523U,	// VPERMT2Qrr
    50349515U,	// VPERMT2Qrrk
    2197833163U,	// VPERMT2Qrrkz
    2148634090U,	// VPEXTRBmr
    2484096490U,	// VPEXTRBrr
    2148602705U,	// VPEXTRDmr
    2484097873U,	// VPEXTRDrr
    2148621158U,	// VPEXTRQmr
    2484099942U,	// VPEXTRQrr
    2148574207U,	// VPEXTRWmr
    2484102143U,	// VPEXTRWri
    2484102143U,	// VPEXTRWrr_REV
    1074810792U,	// VPGATHERDDYrm
    1056981260U,	// VPGATHERDDZrm
    1074810792U,	// VPGATHERDDrm
    1007704774U,	// VPGATHERDQYrm
    3204466316U,	// VPGATHERDQZrm
    1007704774U,	// VPGATHERDQrm
    1074811702U,	// VPGATHERQDYrm
    3204465514U,	// VPGATHERQDZrm
    1074811702U,	// VPGATHERQDrm
    1007704913U,	// VPGATHERQQYrm
    3204466523U,	// VPGATHERQQZrm
    1007704913U,	// VPGATHERQQrm
    437276477U,	// VPHADDBDrm
    336613181U,	// VPHADDBDrr
    437279207U,	// VPHADDBQrm
    336615911U,	// VPHADDBQrr
    437281489U,	// VPHADDBWrm
    336618193U,	// VPHADDBWrr
    437279331U,	// VPHADDDQrm
    336616035U,	// VPHADDDQrr
    2484096909U,	// VPHADDDYrm
    2484096909U,	// VPHADDDYrr
    2484096909U,	// VPHADDDrm
    2484096909U,	// VPHADDDrr
    2484102191U,	// VPHADDSWrm128
    2484102191U,	// VPHADDSWrm256
    2484102191U,	// VPHADDSWrr128
    2484102191U,	// VPHADDSWrr256
    437276487U,	// VPHADDUBDrm
    336613191U,	// VPHADDUBDrr
    437279217U,	// VPHADDUBQrm
    336615921U,	// VPHADDUBQrr
    437281523U,	// VPHADDUBWrm
    336618227U,	// VPHADDUBWrr
    437279452U,	// VPHADDUDQrm
    336616156U,	// VPHADDUDQrr
    437278147U,	// VPHADDUWDrm
    336614851U,	// VPHADDUWDrr
    437279734U,	// VPHADDUWQrm
    336616438U,	// VPHADDUWQrr
    437278059U,	// VPHADDWDrm
    336614763U,	// VPHADDWDrr
    437279724U,	// VPHADDWQrm
    336616428U,	// VPHADDWQrr
    2484101940U,	// VPHADDWYrm
    2484101940U,	// VPHADDWYrr
    2484101940U,	// VPHADDWrm
    2484101940U,	// VPHADDWrr
    437282008U,	// VPHMINPOSUWrm128
    336618712U,	// VPHMINPOSUWrr128
    437281460U,	// VPHSUBBWrm
    336618164U,	// VPHSUBBWrr
    437279313U,	// VPHSUBDQrm
    336616017U,	// VPHSUBDQrr
    2484096850U,	// VPHSUBDYrm
    2484096850U,	// VPHSUBDYrr
    2484096850U,	// VPHSUBDrm
    2484096850U,	// VPHSUBDrr
    2484102172U,	// VPHSUBSWrm128
    2484102172U,	// VPHSUBSWrm256
    2484102172U,	// VPHSUBSWrr128
    2484102172U,	// VPHSUBSWrr256
    437278049U,	// VPHSUBWDrm
    336614753U,	// VPHSUBWDrr
    2484101886U,	// VPHSUBWYrm
    2484101886U,	// VPHSUBWYrr
    2484101886U,	// VPHSUBWrm
    2484101886U,	// VPHSUBWrr
    2484096481U,	// VPINSRBrm
    2484096481U,	// VPINSRBrr
    2484097864U,	// VPINSRDrm
    2484097864U,	// VPINSRDrr
    2484099933U,	// VPINSRQrm
    2484099933U,	// VPINSRQrr
    2484102134U,	// VPINSRWrmi
    2484102134U,	// VPINSRWrri
    907035811U,	// VPLZCNTDrm
    2450539683U,	// VPLZCNTDrmb
    50349219U,	// VPLZCNTDrmbk
    2499822755U,	// VPLZCNTDrmbkz
    50349219U,	// VPLZCNTDrmk
    2499822755U,	// VPLZCNTDrmkz
    336610467U,	// VPLZCNTDrr
    50349219U,	// VPLZCNTDrrk
    2499822755U,	// VPLZCNTDrrkz
    907036618U,	// VPLZCNTQrm
    370165706U,	// VPLZCNTQrmb
    50350026U,	// VPLZCNTQrmbk
    2499823562U,	// VPLZCNTQrmbkz
    50350026U,	// VPLZCNTQrmk
    2499823562U,	// VPLZCNTQrmkz
    336611274U,	// VPLZCNTQrr
    50350026U,	// VPLZCNTQrrk
    2499823562U,	// VPLZCNTQrrkz
    2484096948U,	// VPMACSDDrm
    2484096948U,	// VPMACSDDrr
    2484098850U,	// VPMACSDQHrm
    2484098850U,	// VPMACSDQHrr
    2484099136U,	// VPMACSDQLrm
    2484099136U,	// VPMACSDQLrr
    2484096958U,	// VPMACSSDDrm
    2484096958U,	// VPMACSSDDrr
    2484098861U,	// VPMACSSDQHrm
    2484098861U,	// VPMACSSDQHrr
    2484099147U,	// VPMACSSDQLrm
    2484099147U,	// VPMACSSDQLrr
    2484098476U,	// VPMACSSWDrm
    2484098476U,	// VPMACSSWDrr
    2484102392U,	// VPMACSSWWrm
    2484102392U,	// VPMACSSWWrr
    2484098455U,	// VPMACSWDrm
    2484098455U,	// VPMACSWDrr
    2484102382U,	// VPMACSWWrm
    2484102382U,	// VPMACSWWrr
    2484098487U,	// VPMADCSSWDrm
    2484098487U,	// VPMADCSSWDrr
    2484098465U,	// VPMADCSWDrm
    2484098465U,	// VPMADCSWDrr
    2484102160U,	// VPMADDUBSWrm128
    2484102160U,	// VPMADDUBSWrm256
    2484102160U,	// VPMADDUBSWrr128
    2484102160U,	// VPMADDUBSWrr256
    2484098421U,	// VPMADDWDYrm
    2484098421U,	// VPMADDWDYrr
    2484098421U,	// VPMADDWDrm
    2484098421U,	// VPMADDWDrr
    2149176654U,	// VPMASKMOVDYmr
    2484098382U,	// VPMASKMOVDYrm
    2148799822U,	// VPMASKMOVDmr
    2484098382U,	// VPMASKMOVDrm
    2149178329U,	// VPMASKMOVQYmr
    2484100057U,	// VPMASKMOVQYrm
    2148801497U,	// VPMASKMOVQmr
    2484100057U,	// VPMASKMOVQrm
    2484096584U,	// VPMAXSBYrm
    2484096584U,	// VPMAXSBYrr
    2484096584U,	// VPMAXSBrm
    2484096584U,	// VPMAXSBrr
    2484098261U,	// VPMAXSDYrm
    2484098261U,	// VPMAXSDYrr
    2484094091U,	// VPMAXSDZrm
    2484094091U,	// VPMAXSDZrmb
    50349195U,	// VPMAXSDZrmbk
    2499822731U,	// VPMAXSDZrmbkz
    50349195U,	// VPMAXSDZrmk
    2499822731U,	// VPMAXSDZrmkz
    2484094091U,	// VPMAXSDZrr
    50349195U,	// VPMAXSDZrrk
    2499822731U,	// VPMAXSDZrrkz
    2484098261U,	// VPMAXSDrm
    2484098261U,	// VPMAXSDrr
    2484094898U,	// VPMAXSQZrm
    2484094898U,	// VPMAXSQZrmb
    50350002U,	// VPMAXSQZrmbk
    2499823538U,	// VPMAXSQZrmbkz
    50350002U,	// VPMAXSQZrmk
    2499823538U,	// VPMAXSQZrmkz
    2484094898U,	// VPMAXSQZrr
    50350002U,	// VPMAXSQZrrk
    2499823538U,	// VPMAXSQZrrkz
    2484102291U,	// VPMAXSWYrm
    2484102291U,	// VPMAXSWYrr
    2484102291U,	// VPMAXSWrm
    2484102291U,	// VPMAXSWrr
    2484096663U,	// VPMAXUBYrm
    2484096663U,	// VPMAXUBYrr
    2484096663U,	// VPMAXUBrm
    2484096663U,	// VPMAXUBrr
    2484098346U,	// VPMAXUDYrm
    2484098346U,	// VPMAXUDYrr
    2484094159U,	// VPMAXUDZrm
    2484094159U,	// VPMAXUDZrmb
    50349263U,	// VPMAXUDZrmbk
    2499822799U,	// VPMAXUDZrmbkz
    50349263U,	// VPMAXUDZrmk
    2499822799U,	// VPMAXUDZrmkz
    2484094159U,	// VPMAXUDZrr
    50349263U,	// VPMAXUDZrrk
    2499822799U,	// VPMAXUDZrrkz
    2484098346U,	// VPMAXUDrm
    2484098346U,	// VPMAXUDrr
    2484094966U,	// VPMAXUQZrm
    2484094966U,	// VPMAXUQZrmb
    50350070U,	// VPMAXUQZrmbk
    2499823606U,	// VPMAXUQZrmbkz
    50350070U,	// VPMAXUQZrmk
    2499823606U,	// VPMAXUQZrmkz
    2484094966U,	// VPMAXUQZrr
    50350070U,	// VPMAXUQZrrk
    2499823606U,	// VPMAXUQZrrkz
    2484102373U,	// VPMAXUWYrm
    2484102373U,	// VPMAXUWYrr
    2484102373U,	// VPMAXUWrm
    2484102373U,	// VPMAXUWrr
    2484096525U,	// VPMINSBYrm
    2484096525U,	// VPMINSBYrr
    2484096525U,	// VPMINSBrm
    2484096525U,	// VPMINSBrr
    2484098181U,	// VPMINSDYrm
    2484098181U,	// VPMINSDYrr
    2484094057U,	// VPMINSDZrm
    2484094057U,	// VPMINSDZrmb
    50349161U,	// VPMINSDZrmbk
    2499822697U,	// VPMINSDZrmbkz
    50349161U,	// VPMINSDZrmk
    2499822697U,	// VPMINSDZrmkz
    2484094057U,	// VPMINSDZrr
    50349161U,	// VPMINSDZrrk
    2499822697U,	// VPMINSDZrrkz
    2484098181U,	// VPMINSDrm
    2484098181U,	// VPMINSDrr
    2484094888U,	// VPMINSQZrm
    2484094888U,	// VPMINSQZrmb
    50349992U,	// VPMINSQZrmbk
    2499823528U,	// VPMINSQZrmbkz
    50349992U,	// VPMINSQZrmk
    2499823528U,	// VPMINSQZrmkz
    2484094888U,	// VPMINSQZrr
    50349992U,	// VPMINSQZrrk
    2499823528U,	// VPMINSQZrrkz
    2484102222U,	// VPMINSWYrm
    2484102222U,	// VPMINSWYrr
    2484102222U,	// VPMINSWrm
    2484102222U,	// VPMINSWrr
    2484096640U,	// VPMINUBYrm
    2484096640U,	// VPMINUBYrr
    2484096640U,	// VPMINUBrm
    2484096640U,	// VPMINUBrr
    2484098328U,	// VPMINUDYrm
    2484098328U,	// VPMINUDYrr
    2484094149U,	// VPMINUDZrm
    2484094149U,	// VPMINUDZrmb
    50349253U,	// VPMINUDZrmbk
    2499822789U,	// VPMINUDZrmbkz
    50349253U,	// VPMINUDZrmk
    2499822789U,	// VPMINUDZrmkz
    2484094149U,	// VPMINUDZrr
    50349253U,	// VPMINUDZrrk
    2499822789U,	// VPMINUDZrrkz
    2484098328U,	// VPMINUDrm
    2484098328U,	// VPMINUDrr
    2484094956U,	// VPMINUQZrm
    2484094956U,	// VPMINUQZrmb
    50350060U,	// VPMINUQZrmbk
    2499823596U,	// VPMINUQZrmbkz
    50350060U,	// VPMINUQZrmk
    2499823596U,	// VPMINUQZrmkz
    2484094956U,	// VPMINUQZrr
    50350060U,	// VPMINUQZrrk
    2499823596U,	// VPMINUQZrrkz
    2484102351U,	// VPMINUWYrm
    2484102351U,	// VPMINUWYrr
    2484102351U,	// VPMINUWrm
    2484102351U,	// VPMINUWrr
    1310788U,	// VPMOVDBmr
    17039428U,	// VPMOVDBmrk
    336609348U,	// VPMOVDBrr
    352337988U,	// VPMOVDBrrk
    2499821636U,	// VPMOVDBrrkz
    1690341U,	// VPMOVDWmr
    17418981U,	// VPMOVDWmrk
    336612069U,	// VPMOVDWrr
    352340709U,	// VPMOVDWrrk
    2499824357U,	// VPMOVDWrrkz
    336612787U,	// VPMOVMSKBYrr
    336612787U,	// VPMOVMSKBrr
    1310830U,	// VPMOVQBmr
    17039470U,	// VPMOVQBmrk
    336609390U,	// VPMOVQBrr
    352338030U,	// VPMOVQBrrk
    2499821678U,	// VPMOVQBrrkz
    1688476U,	// VPMOVQDmr
    17417116U,	// VPMOVQDmrk
    336610204U,	// VPMOVQDrr
    352338844U,	// VPMOVQDrrk
    2499822492U,	// VPMOVQDrrkz
    1313562U,	// VPMOVQWmr
    17042202U,	// VPMOVQWmrk
    336612122U,	// VPMOVQWrr
    352340762U,	// VPMOVQWrrk
    2499824410U,	// VPMOVQWrrkz
    1310777U,	// VPMOVSDBmr
    17039417U,	// VPMOVSDBmrk
    336609337U,	// VPMOVSDBrr
    352337977U,	// VPMOVSDBrrk
    2499821625U,	// VPMOVSDBrrkz
    1690330U,	// VPMOVSDWmr
    17418970U,	// VPMOVSDWmrk
    336612058U,	// VPMOVSDWrr
    352340698U,	// VPMOVSDWrrk
    2499824346U,	// VPMOVSDWrrkz
    1310819U,	// VPMOVSQBmr
    17039459U,	// VPMOVSQBmrk
    336609379U,	// VPMOVSQBrr
    352338019U,	// VPMOVSQBrrk
    2499821667U,	// VPMOVSQBrrkz
    1688465U,	// VPMOVSQDmr
    17417105U,	// VPMOVSQDmrk
    336610193U,	// VPMOVSQDrr
    352338833U,	// VPMOVSQDrrk
    2499822481U,	// VPMOVSQDrrkz
    1313551U,	// VPMOVSQWmr
    17042191U,	// VPMOVSQWmrk
    336612111U,	// VPMOVSQWrr
    352340751U,	// VPMOVSQWrrk
    2499824399U,	// VPMOVSQWrrkz
    303058787U,	// VPMOVSXBDYrm
    336613219U,	// VPMOVSXBDYrr
    437272794U,	// VPMOVSXBDZrm
    352338138U,	// VPMOVSXBDZrmk
    2499821786U,	// VPMOVSXBDZrmkz
    336609498U,	// VPMOVSXBDZrr
    352338138U,	// VPMOVSXBDZrrk
    2499821786U,	// VPMOVSXBDZrrkz
    303058787U,	// VPMOVSXBDrm
    336613219U,	// VPMOVSXBDrr
    470833668U,	// VPMOVSXBQYrm
    336615940U,	// VPMOVSXBQYrr
    437274088U,	// VPMOVSXBQZrm
    352339432U,	// VPMOVSXBQZrmk
    2499823080U,	// VPMOVSXBQZrmkz
    336610792U,	// VPMOVSXBQZrr
    352339432U,	// VPMOVSXBQZrrk
    2499823080U,	// VPMOVSXBQZrrkz
    470833668U,	// VPMOVSXBQrm
    336615940U,	// VPMOVSXBQrr
    437281551U,	// VPMOVSXBWYrm
    336618255U,	// VPMOVSXBWYrr
    370172687U,	// VPMOVSXBWrm
    336618255U,	// VPMOVSXBWrr
    437279473U,	// VPMOVSXDQYrm
    336616177U,	// VPMOVSXDQYrr
    839927528U,	// VPMOVSXDQZrm
    352339688U,	// VPMOVSXDQZrmk
    2499823336U,	// VPMOVSXDQZrmkz
    336611048U,	// VPMOVSXDQZrr
    352339688U,	// VPMOVSXDQZrrk
    2499823336U,	// VPMOVSXDQZrrkz
    370170609U,	// VPMOVSXDQrm
    336616177U,	// VPMOVSXDQrr
    437278158U,	// VPMOVSXWDYrm
    336614862U,	// VPMOVSXWDYrr
    839927039U,	// VPMOVSXWDZrm
    352339199U,	// VPMOVSXWDZrmk
    2499822847U,	// VPMOVSXWDZrmkz
    336610559U,	// VPMOVSXWDZrr
    352339199U,	// VPMOVSXWDZrrk
    2499822847U,	// VPMOVSXWDZrrkz
    370169294U,	// VPMOVSXWDrm
    336614862U,	// VPMOVSXWDrr
    303062017U,	// VPMOVSXWQYrm
    336616449U,	// VPMOVSXWQYrr
    437274662U,	// VPMOVSXWQZrm
    352340006U,	// VPMOVSXWQZrmk
    2499823654U,	// VPMOVSXWQZrmkz
    336611366U,	// VPMOVSXWQZrr
    352340006U,	// VPMOVSXWQZrrk
    2499823654U,	// VPMOVSXWQZrrkz
    303062017U,	// VPMOVSXWQrm
    336616449U,	// VPMOVSXWQrr
    1310765U,	// VPMOVUSDBmr
    17039405U,	// VPMOVUSDBmrk
    336609325U,	// VPMOVUSDBrr
    352337965U,	// VPMOVUSDBrrk
    2499821613U,	// VPMOVUSDBrrkz
    1690318U,	// VPMOVUSDWmr
    17418958U,	// VPMOVUSDWmrk
    336612046U,	// VPMOVUSDWrr
    352340686U,	// VPMOVUSDWrrk
    2499824334U,	// VPMOVUSDWrrkz
    1310807U,	// VPMOVUSQBmr
    17039447U,	// VPMOVUSQBmrk
    336609367U,	// VPMOVUSQBrr
    352338007U,	// VPMOVUSQBrrk
    2499821655U,	// VPMOVUSQBrrkz
    1688453U,	// VPMOVUSQDmr
    17417093U,	// VPMOVUSQDmrk
    336610181U,	// VPMOVUSQDrr
    352338821U,	// VPMOVUSQDrrk
    2499822469U,	// VPMOVUSQDrrkz
    1313539U,	// VPMOVUSQWmr
    17042179U,	// VPMOVUSQWmrk
    336612099U,	// VPMOVUSQWrr
    352340739U,	// VPMOVUSQWrrk
    2499824387U,	// VPMOVUSQWrrkz
    303058798U,	// VPMOVZXBDYrm
    336613230U,	// VPMOVZXBDYrr
    437272806U,	// VPMOVZXBDZrm
    352338150U,	// VPMOVZXBDZrmk
    2499821798U,	// VPMOVZXBDZrmkz
    336609510U,	// VPMOVZXBDZrr
    352338150U,	// VPMOVZXBDZrrk
    2499821798U,	// VPMOVZXBDZrrkz
    303058798U,	// VPMOVZXBDrm
    336613230U,	// VPMOVZXBDrr
    470833679U,	// VPMOVZXBQYrm
    336615951U,	// VPMOVZXBQYrr
    437274100U,	// VPMOVZXBQZrm
    352339444U,	// VPMOVZXBQZrmk
    2499823092U,	// VPMOVZXBQZrmkz
    336610804U,	// VPMOVZXBQZrr
    352339444U,	// VPMOVZXBQZrrk
    2499823092U,	// VPMOVZXBQZrrkz
    470833679U,	// VPMOVZXBQrm
    336615951U,	// VPMOVZXBQrr
    437281562U,	// VPMOVZXBWYrm
    336618266U,	// VPMOVZXBWYrr
    370172698U,	// VPMOVZXBWrm
    336618266U,	// VPMOVZXBWrr
    437279484U,	// VPMOVZXDQYrm
    336616188U,	// VPMOVZXDQYrr
    839927540U,	// VPMOVZXDQZrm
    352339700U,	// VPMOVZXDQZrmk
    2499823348U,	// VPMOVZXDQZrmkz
    336611060U,	// VPMOVZXDQZrr
    352339700U,	// VPMOVZXDQZrrk
    2499823348U,	// VPMOVZXDQZrrkz
    370170620U,	// VPMOVZXDQrm
    336616188U,	// VPMOVZXDQrr
    437278169U,	// VPMOVZXWDYrm
    336614873U,	// VPMOVZXWDYrr
    839927051U,	// VPMOVZXWDZrm
    352339211U,	// VPMOVZXWDZrmk
    2499822859U,	// VPMOVZXWDZrmkz
    336610571U,	// VPMOVZXWDZrr
    352339211U,	// VPMOVZXWDZrrk
    2499822859U,	// VPMOVZXWDZrrkz
    370169305U,	// VPMOVZXWDrm
    336614873U,	// VPMOVZXWDrr
    303062028U,	// VPMOVZXWQYrm
    336616460U,	// VPMOVZXWQYrr
    437274674U,	// VPMOVZXWQZrm
    352340018U,	// VPMOVZXWQZrmk
    2499823666U,	// VPMOVZXWQZrmkz
    336611378U,	// VPMOVZXWQZrr
    352340018U,	// VPMOVZXWQZrrk
    2499823666U,	// VPMOVZXWQZrrkz
    303062028U,	// VPMOVZXWQrm
    336616460U,	// VPMOVZXWQrr
    2484099735U,	// VPMULDQYrm
    2484099735U,	// VPMULDQYrr
    2484094549U,	// VPMULDQZrm
    2484094549U,	// VPMULDQZrmb
    352339541U,	// VPMULDQZrmbk
    2499823189U,	// VPMULDQZrmbkz
    352339541U,	// VPMULDQZrmk
    2499823189U,	// VPMULDQZrmkz
    2484094549U,	// VPMULDQZrr
    352339541U,	// VPMULDQZrrk
    2499823189U,	// VPMULDQZrrkz
    2484099735U,	// VPMULDQrm
    2484099735U,	// VPMULDQrr
    2484102245U,	// VPMULHRSWrm128
    2484102245U,	// VPMULHRSWrm256
    2484102245U,	// VPMULHRSWrr128
    2484102245U,	// VPMULHRSWrr256
    2484102332U,	// VPMULHUWYrm
    2484102332U,	// VPMULHUWYrr
    2484102332U,	// VPMULHUWrm
    2484102332U,	// VPMULHUWrr
    2484102033U,	// VPMULHWYrm
    2484102033U,	// VPMULHWYrr
    2484102033U,	// VPMULHWrm
    2484102033U,	// VPMULHWrr
    2484097051U,	// VPMULLDYrm
    2484097051U,	// VPMULLDYrr
    2484093242U,	// VPMULLDZrm
    2484093242U,	// VPMULLDZrmb
    50348346U,	// VPMULLDZrmbk
    2499821882U,	// VPMULLDZrmbkz
    50348346U,	// VPMULLDZrmk
    2499821882U,	// VPMULLDZrmkz
    2484093242U,	// VPMULLDZrr
    50348346U,	// VPMULLDZrrk
    2499821882U,	// VPMULLDZrrkz
    2484097051U,	// VPMULLDrm
    2484097051U,	// VPMULLDrr
    2484102075U,	// VPMULLWYrm
    2484102075U,	// VPMULLWYrr
    2484102075U,	// VPMULLWrm
    2484102075U,	// VPMULLWrr
    2484099815U,	// VPMULUDQYrm
    2484099815U,	// VPMULUDQYrr
    2484094685U,	// VPMULUDQZrm
    2484094685U,	// VPMULUDQZrmb
    352339677U,	// VPMULUDQZrmbk
    2499823325U,	// VPMULUDQZrmbkz
    352339677U,	// VPMULUDQZrmk
    2499823325U,	// VPMULUDQZrmkz
    2484094685U,	// VPMULUDQZrr
    352339677U,	// VPMULUDQZrrk
    2499823325U,	// VPMULUDQZrrkz
    2484099815U,	// VPMULUDQrm
    2484099815U,	// VPMULUDQrr
    2484093878U,	// VPORDZrm
    2484093878U,	// VPORDZrmb
    50348982U,	// VPORDZrmbk
    2499822518U,	// VPORDZrmbkz
    50348982U,	// VPORDZrmk
    2499822518U,	// VPORDZrmkz
    2484093878U,	// VPORDZrr
    50348982U,	// VPORDZrrk
    2499822518U,	// VPORDZrrkz
    2484094854U,	// VPORQZrm
    2484094854U,	// VPORQZrmb
    50349958U,	// VPORQZrmbk
    2499823494U,	// VPORQZrmbkz
    50349958U,	// VPORQZrmk
    2499823494U,	// VPORQZrmkz
    2484094854U,	// VPORQZrr
    50349958U,	// VPORQZrrk
    2499823494U,	// VPORQZrrkz
    2484100181U,	// VPORYrm
    2484100181U,	// VPORYrr
    2484100181U,	// VPORrm
    2484100181U,	// VPORrr
    2484099216U,	// VPPERMmr
    2484099216U,	// VPPERMrm
    2484099216U,	// VPPERMrr
    2584759905U,	// VPROTBmi
    2584759905U,	// VPROTBmr
    2484096609U,	// VPROTBri
    2484096609U,	// VPROTBrm
    2484096609U,	// VPROTBrr
    2584761593U,	// VPROTDmi
    2584761593U,	// VPROTDmr
    2484098297U,	// VPROTDri
    2484098297U,	// VPROTDrm
    2484098297U,	// VPROTDrr
    2584763286U,	// VPROTQmi
    2584763286U,	// VPROTQmr
    2484099990U,	// VPROTQri
    2484099990U,	// VPROTQrm
    2484099990U,	// VPROTQrr
    2584765606U,	// VPROTWmi
    2584765606U,	// VPROTWmr
    2484102310U,	// VPROTWri
    2484102310U,	// VPROTWrm
    2484102310U,	// VPROTWrr
    2484101832U,	// VPSADBWYrm
    2484101832U,	// VPSADBWYrr
    2484101832U,	// VPSADBWrm
    2484101832U,	// VPSADBWrr
    688409U,	// VPSCATTERDDZmr
    706201U,	// VPSCATTERDQZmr
    705399U,	// VPSCATTERQDZmr
    706408U,	// VPSCATTERQQZmr
    2584759681U,	// VPSHABmr
    2484096385U,	// VPSHABrm
    2484096385U,	// VPSHABrr
    2584760109U,	// VPSHADmr
    2484096813U,	// VPSHADrm
    2484096813U,	// VPSHADrr
    2584762847U,	// VPSHAQmr
    2484099551U,	// VPSHAQrm
    2484099551U,	// VPSHAQrr
    2584765092U,	// VPSHAWmr
    2484101796U,	// VPSHAWrm
    2484101796U,	// VPSHAWrr
    2584759742U,	// VPSHLBmr
    2484096446U,	// VPSHLBrm
    2484096446U,	// VPSHLBrr
    2584760325U,	// VPSHLDmr
    2484097029U,	// VPSHLDrm
    2484097029U,	// VPSHLDrr
    2584763159U,	// VPSHLQmr
    2484099863U,	// VPSHLQrm
    2484099863U,	// VPSHLQrr
    2584765355U,	// VPSHLWmr
    2484102059U,	// VPSHLWrm
    2484102059U,	// VPSHLWrr
    2484096414U,	// VPSHUFBYrm
    2484096414U,	// VPSHUFBYrr
    2484096414U,	// VPSHUFBrm
    2484096414U,	// VPSHUFBrr
    2987413464U,	// VPSHUFDYmi
    2484096984U,	// VPSHUFDYri
    3054518567U,	// VPSHUFDZmi
    2484093223U,	// VPSHUFDZri
    2584760280U,	// VPSHUFDmi
    2484096984U,	// VPSHUFDri
    2987418503U,	// VPSHUFHWYmi
    2484102023U,	// VPSHUFHWYri
    2584765319U,	// VPSHUFHWmi
    2484102023U,	// VPSHUFHWri
    2987418529U,	// VPSHUFLWYmi
    2484102049U,	// VPSHUFLWYri
    2584765345U,	// VPSHUFLWmi
    2484102049U,	// VPSHUFLWri
    2484096462U,	// VPSIGNBYrm
    2484096462U,	// VPSIGNBYrr
    2484096462U,	// VPSIGNBrm
    2484096462U,	// VPSIGNBrr
    2484097108U,	// VPSIGNDYrm
    2484097108U,	// VPSIGNDYrr
    2484097108U,	// VPSIGNDrm
    2484097108U,	// VPSIGNDrr
    2484102100U,	// VPSIGNWYrm
    2484102100U,	// VPSIGNWYrr
    2484102100U,	// VPSIGNWrm
    2484102100U,	// VPSIGNWrr
    2484099717U,	// VPSLLDQYri
    2484099717U,	// VPSLLDQri
    2484097043U,	// VPSLLDYri
    2484097043U,	// VPSLLDYrm
    2484097043U,	// VPSLLDYrr
    3054518577U,	// VPSLLDZmi
    352338225U,	// VPSLLDZmik
    2484093233U,	// VPSLLDZri
    352338225U,	// VPSLLDZrik
    2484093233U,	// VPSLLDZrm
    352338225U,	// VPSLLDZrmk
    2484093233U,	// VPSLLDZrr
    352338225U,	// VPSLLDZrrk
    2484097043U,	// VPSLLDri
    2484097043U,	// VPSLLDrm
    2484097043U,	// VPSLLDrr
    2484099871U,	// VPSLLQYri
    2484099871U,	// VPSLLQYrm
    2484099871U,	// VPSLLQYrr
    3054520064U,	// VPSLLQZmi
    352339712U,	// VPSLLQZmik
    2484094720U,	// VPSLLQZri
    352339712U,	// VPSLLQZrik
    2484094720U,	// VPSLLQZrm
    352339712U,	// VPSLLQZrmk
    2484094720U,	// VPSLLQZrr
    352339712U,	// VPSLLQZrrk
    2484099871U,	// VPSLLQri
    2484099871U,	// VPSLLQrm
    2484099871U,	// VPSLLQrr
    2484098364U,	// VPSLLVDYrm
    2484098364U,	// VPSLLVDYrr
    2484094179U,	// VPSLLVDZrm
    2484094179U,	// VPSLLVDZrr
    2484098364U,	// VPSLLVDrm
    2484098364U,	// VPSLLVDrr
    2484100039U,	// VPSLLVQYrm
    2484100039U,	// VPSLLVQYrr
    2484094986U,	// VPSLLVQZrm
    2484094986U,	// VPSLLVQZrr
    2484100039U,	// VPSLLVQrm
    2484100039U,	// VPSLLVQrr
    2484102067U,	// VPSLLWYri
    2484102067U,	// VPSLLWYrm
    2484102067U,	// VPSLLWYrr
    2484102067U,	// VPSLLWri
    2484102067U,	// VPSLLWrm
    2484102067U,	// VPSLLWrr
    2484096821U,	// VPSRADYri
    2484096821U,	// VPSRADYrm
    2484096821U,	// VPSRADYrr
    3054518472U,	// VPSRADZmi
    352338120U,	// VPSRADZmik
    2484093128U,	// VPSRADZri
    352338120U,	// VPSRADZrik
    2484093128U,	// VPSRADZrm
    352338120U,	// VPSRADZrmk
    2484093128U,	// VPSRADZrr
    352338120U,	// VPSRADZrrk
    2484096821U,	// VPSRADri
    2484096821U,	// VPSRADrm
    2484096821U,	// VPSRADrr
    3054519766U,	// VPSRAQZmi
    352339414U,	// VPSRAQZmik
    2484094422U,	// VPSRAQZri
    352339414U,	// VPSRAQZrik
    2484094422U,	// VPSRAQZrm
    352339414U,	// VPSRAQZrmk
    2484094422U,	// VPSRAQZrr
    352339414U,	// VPSRAQZrrk
    2484098355U,	// VPSRAVDYrm
    2484098355U,	// VPSRAVDYrr
    2484094169U,	// VPSRAVDZrm
    2484094169U,	// VPSRAVDZrr
    2484098355U,	// VPSRAVDrm
    2484098355U,	// VPSRAVDrr
    2484094976U,	// VPSRAVQZrm
    2484094976U,	// VPSRAVQZrr
    2484101804U,	// VPSRAWYri
    2484101804U,	// VPSRAWYrm
    2484101804U,	// VPSRAWYrr
    2484101804U,	// VPSRAWri
    2484101804U,	// VPSRAWrm
    2484101804U,	// VPSRAWrr
    2484099726U,	// VPSRLDQYri
    2484099726U,	// VPSRLDQri
    2484097060U,	// VPSRLDYri
    2484097060U,	// VPSRLDYrm
    2484097060U,	// VPSRLDYrr
    3054518596U,	// VPSRLDZmi
    352338244U,	// VPSRLDZmik
    2484093252U,	// VPSRLDZri
    352338244U,	// VPSRLDZrik
    2484093252U,	// VPSRLDZrm
    352338244U,	// VPSRLDZrmk
    2484093252U,	// VPSRLDZrr
    352338244U,	// VPSRLDZrrk
    2484097060U,	// VPSRLDri
    2484097060U,	// VPSRLDrm
    2484097060U,	// VPSRLDrr
    2484099879U,	// VPSRLQYri
    2484099879U,	// VPSRLQYrm
    2484099879U,	// VPSRLQYrr
    3054520073U,	// VPSRLQZmi
    352339721U,	// VPSRLQZmik
    2484094729U,	// VPSRLQZri
    352339721U,	// VPSRLQZrik
    2484094729U,	// VPSRLQZrm
    352339721U,	// VPSRLQZrmk
    2484094729U,	// VPSRLQZrr
    352339721U,	// VPSRLQZrrk
    2484099879U,	// VPSRLQri
    2484099879U,	// VPSRLQrm
    2484099879U,	// VPSRLQrr
    2484098373U,	// VPSRLVDYrm
    2484098373U,	// VPSRLVDYrr
    2484094189U,	// VPSRLVDZrm
    2484094189U,	// VPSRLVDZrr
    2484098373U,	// VPSRLVDrm
    2484098373U,	// VPSRLVDrr
    2484100048U,	// VPSRLVQYrm
    2484100048U,	// VPSRLVQYrr
    2484094996U,	// VPSRLVQZrm
    2484094996U,	// VPSRLVQZrr
    2484100048U,	// VPSRLVQrm
    2484100048U,	// VPSRLVQrr
    2484102084U,	// VPSRLWYri
    2484102084U,	// VPSRLWYrm
    2484102084U,	// VPSRLWYrr
    2484102084U,	// VPSRLWri
    2484102084U,	// VPSRLWrm
    2484102084U,	// VPSRLWrr
    2484096398U,	// VPSUBBYrm
    2484096398U,	// VPSUBBYrr
    2484096398U,	// VPSUBBrm
    2484096398U,	// VPSUBBrr
    2484096859U,	// VPSUBDYrm
    2484096859U,	// VPSUBDYrr
    2484093137U,	// VPSUBDZrm
    2484093137U,	// VPSUBDZrmb
    50348241U,	// VPSUBDZrmbk
    2499821777U,	// VPSUBDZrmbkz
    50348241U,	// VPSUBDZrmk
    2499821777U,	// VPSUBDZrmkz
    2484093137U,	// VPSUBDZrr
    50348241U,	// VPSUBDZrrk
    2499821777U,	// VPSUBDZrrkz
    2484096859U,	// VPSUBDrm
    2484096859U,	// VPSUBDrr
    2484099580U,	// VPSUBQYrm
    2484099580U,	// VPSUBQYrr
    2484094431U,	// VPSUBQZrm
    2484094431U,	// VPSUBQZrmb
    50349535U,	// VPSUBQZrmbk
    2499823071U,	// VPSUBQZrmbkz
    50349535U,	// VPSUBQZrmk
    2499823071U,	// VPSUBQZrmkz
    2484094431U,	// VPSUBQZrr
    50349535U,	// VPSUBQZrrk
    2499823071U,	// VPSUBQZrrkz
    2484099580U,	// VPSUBQrm
    2484099580U,	// VPSUBQrr
    2484096507U,	// VPSUBSBYrm
    2484096507U,	// VPSUBSBYrr
    2484096507U,	// VPSUBSBrm
    2484096507U,	// VPSUBSBrr
    2484102182U,	// VPSUBSWYrm
    2484102182U,	// VPSUBSWYrr
    2484102182U,	// VPSUBSWrm
    2484102182U,	// VPSUBSWrr
    2484096548U,	// VPSUBUSBYrm
    2484096548U,	// VPSUBUSBYrr
    2484096548U,	// VPSUBUSBrm
    2484096548U,	// VPSUBUSBrr
    2484102264U,	// VPSUBUSWYrm
    2484102264U,	// VPSUBUSWYrr
    2484102264U,	// VPSUBUSWrm
    2484102264U,	// VPSUBUSWrr
    2484101895U,	// VPSUBWYrm
    2484101895U,	// VPSUBWYrr
    2484101895U,	// VPSUBWrm
    2484101895U,	// VPSUBWrr
    2484093294U,	// VPTESTMDZrm
    2484093294U,	// VPTESTMDZrr
    2484094771U,	// VPTESTMQZrm
    2484094771U,	// VPTESTMQZrr
    2484093273U,	// VPTESTNMDZrm
    2484093273U,	// VPTESTNMDZrr
    2484094750U,	// VPTESTNMQZrm
    2484094750U,	// VPTESTNMQZrr
    839934503U,	// VPTESTYrm
    336618023U,	// VPTESTYrr
    537944615U,	// VPTESTrm
    336618023U,	// VPTESTrr
    2484101851U,	// VPUNPCKHBWYrm
    2484101851U,	// VPUNPCKHBWYrr
    2484101851U,	// VPUNPCKHBWrm
    2484101851U,	// VPUNPCKHBWrr
    2484099693U,	// VPUNPCKHDQYrm
    2484099693U,	// VPUNPCKHDQYrr
    2484094523U,	// VPUNPCKHDQZrm
    2484094523U,	// VPUNPCKHDQZrr
    2484099693U,	// VPUNPCKHDQrm
    2484099693U,	// VPUNPCKHDQrr
    2484099744U,	// VPUNPCKHQDQYrm
    2484099744U,	// VPUNPCKHQDQYrr
    2484094576U,	// VPUNPCKHQDQZrm
    2484094576U,	// VPUNPCKHQDQZrr
    2484099744U,	// VPUNPCKHQDQrm
    2484099744U,	// VPUNPCKHQDQrr
    2484098431U,	// VPUNPCKHWDYrm
    2484098431U,	// VPUNPCKHWDYrr
    2484098431U,	// VPUNPCKHWDrm
    2484098431U,	// VPUNPCKHWDrr
    2484101863U,	// VPUNPCKLBWYrm
    2484101863U,	// VPUNPCKLBWYrr
    2484101863U,	// VPUNPCKLBWrm
    2484101863U,	// VPUNPCKLBWrr
    2484099705U,	// VPUNPCKLDQYrm
    2484099705U,	// VPUNPCKLDQYrr
    2484094536U,	// VPUNPCKLDQZrm
    2484094536U,	// VPUNPCKLDQZrr
    2484099705U,	// VPUNPCKLDQrm
    2484099705U,	// VPUNPCKLDQrr
    2484099757U,	// VPUNPCKLQDQYrm
    2484099757U,	// VPUNPCKLQDQYrr
    2484094590U,	// VPUNPCKLQDQZrm
    2484094590U,	// VPUNPCKLQDQZrr
    2484099757U,	// VPUNPCKLQDQrm
    2484099757U,	// VPUNPCKLQDQrr
    2484098443U,	// VPUNPCKLWDYrm
    2484098443U,	// VPUNPCKLWDYrr
    2484098443U,	// VPUNPCKLWDrm
    2484098443U,	// VPUNPCKLWDrr
    2484093894U,	// VPXORDZrm
    2484093894U,	// VPXORDZrmb
    50348998U,	// VPXORDZrmbk
    2499822534U,	// VPXORDZrmbkz
    50348998U,	// VPXORDZrmk
    2499822534U,	// VPXORDZrmkz
    2484093894U,	// VPXORDZrr
    50348998U,	// VPXORDZrrk
    2499822534U,	// VPXORDZrrkz
    2484094870U,	// VPXORQZrm
    2484094870U,	// VPXORQZrmb
    50349974U,	// VPXORQZrmbk
    2499823510U,	// VPXORQZrmbkz
    50349974U,	// VPXORQZrmk
    2499823510U,	// VPXORQZrmkz
    2484094870U,	// VPXORQZrr
    50349974U,	// VPXORQZrrk
    2499823510U,	// VPXORQZrrkz
    2484100209U,	// VPXORYrm
    2484100209U,	// VPXORYrr
    2484100209U,	// VPXORrm
    2484100209U,	// VPXORrr
    974144139U,	// VRCP14PDZm
    336609931U,	// VRCP14PDZr
    974145843U,	// VRCP14PSZm
    336611635U,	// VRCP14PSZr
    2484093986U,	// VRCP14SDrm
    2484093986U,	// VRCP14SDrr
    2484095589U,	// VRCP14SSrm
    2484095589U,	// VRCP14SSrr
    974144163U,	// VRCP28PDZm
    336609955U,	// VRCP28PDZr
    336609955U,	// VRCP28PDZrb
    974145867U,	// VRCP28PSZm
    336611659U,	// VRCP28PSZr
    336611659U,	// VRCP28PSZrb
    2484094010U,	// VRCP28SDrm
    2484094010U,	// VRCP28SDrr
    2484094010U,	// VRCP28SDrrb
    2484095613U,	// VRCP28SSrm
    2484095613U,	// VRCP28SSrr
    2484095613U,	// VRCP28SSrrb
    940597076U,	// VRCPPSYm
    940597076U,	// VRCPPSYm_Int
    336617300U,	// VRCPPSYr
    336617300U,	// VRCPPSYr_Int
    537943892U,	// VRCPPSm
    537943892U,	// VRCPPSm_Int
    336617300U,	// VRCPPSr
    336617300U,	// VRCPPSr_Int
    2484101415U,	// VRCPSSm
    2484101415U,	// VRCPSSm_Int
    2484101415U,	// VRCPSSr
    3121627880U,	// VRNDSCALEPDZm
    2484093672U,	// VRNDSCALEPDZr
    3121629584U,	// VRNDSCALEPSZm
    2484095376U,	// VRNDSCALEPSZr
    2484094043U,	// VRNDSCALESDm
    2484094043U,	// VRNDSCALESDr
    2484095637U,	// VRNDSCALESSm
    2484095637U,	// VRNDSCALESSr
    2685424166U,	// VROUNDPDm
    2484097574U,	// VROUNDPDr
    2685427393U,	// VROUNDPSm
    2484100801U,	// VROUNDPSr
    2484098144U,	// VROUNDSDm
    2484098144U,	// VROUNDSDr
    2484098144U,	// VROUNDSDr_Int
    2484101370U,	// VROUNDSSm
    2484101370U,	// VROUNDSSr
    2484101370U,	// VROUNDSSr_Int
    3088077350U,	// VROUNDYPDm
    2484097574U,	// VROUNDYPDr
    3088080577U,	// VROUNDYPSm
    2484100801U,	// VROUNDYPSr
    974144150U,	// VRSQRT14PDZm
    336609942U,	// VRSQRT14PDZr
    974145854U,	// VRSQRT14PSZm
    336611646U,	// VRSQRT14PSZr
    2484093997U,	// VRSQRT14SDrm
    2484093997U,	// VRSQRT14SDrr
    2484095600U,	// VRSQRT14SSrm
    2484095600U,	// VRSQRT14SSrr
    974144174U,	// VRSQRT28PDZm
    336609966U,	// VRSQRT28PDZr
    336609966U,	// VRSQRT28PDZrb
    974145878U,	// VRSQRT28PSZm
    336611670U,	// VRSQRT28PSZr
    336611670U,	// VRSQRT28PSZrb
    2484094021U,	// VRSQRT28SDrm
    2484094021U,	// VRSQRT28SDrr
    2484094021U,	// VRSQRT28SDrrb
    2484095624U,	// VRSQRT28SSrm
    2484095624U,	// VRSQRT28SSrr
    2484095624U,	// VRSQRT28SSrrb
    940597159U,	// VRSQRTPSYm
    940597159U,	// VRSQRTPSYm_Int
    336617383U,	// VRSQRTPSYr
    336617383U,	// VRSQRTPSYr_Int
    537943975U,	// VRSQRTPSm
    537943975U,	// VRSQRTPSm_Int
    336617383U,	// VRSQRTPSr
    336617383U,	// VRSQRTPSr_Int
    2484101440U,	// VRSQRTSSm
    2484101440U,	// VRSQRTSSm_Int
    2484101440U,	// VRSQRTSSr
    705242U,	// VSCATTERDPDZmr
    690562U,	// VSCATTERDPSZmr
    321941652U,	// VSCATTERPF0DPDm
    321941792U,	// VSCATTERPF0DPSm
    389050586U,	// VSCATTERPF0QPDm
    389050726U,	// VSCATTERPF0QPSm
    321941687U,	// VSCATTERPF1DPDm
    321941827U,	// VSCATTERPF1DPSm
    389050621U,	// VSCATTERPF1QPDm
    389050761U,	// VSCATTERPF1QPSm
    705354U,	// VSCATTERQPDZmr
    707058U,	// VSCATTERQPSZmr
    2484097596U,	// VSHUFPDYrmi
    2484097596U,	// VSHUFPDYrri
    2484093686U,	// VSHUFPDZrmi
    2484093686U,	// VSHUFPDZrri
    2484097596U,	// VSHUFPDrmi
    2484097596U,	// VSHUFPDrri
    2484100823U,	// VSHUFPSYrmi
    2484100823U,	// VSHUFPSYrri
    2484095390U,	// VSHUFPSZrmi
    2484095390U,	// VSHUFPSZrri
    2484100823U,	// VSHUFPSrmi
    2484100823U,	// VSHUFPSrri
    940593889U,	// VSQRTPDYm
    336614113U,	// VSQRTPDYr
    974148321U,	// VSQRTPDZrm
    336614113U,	// VSQRTPDZrr
    537940705U,	// VSQRTPDm
    336614113U,	// VSQRTPDr
    940597169U,	// VSQRTPSYm
    336617393U,	// VSQRTPSYr
    974151601U,	// VSQRTPSZrm
    336617393U,	// VSQRTPSZrr
    537943985U,	// VSQRTPSm
    336617393U,	// VSQRTPSr
    2484098222U,	// VSQRTSDZm
    2484098222U,	// VSQRTSDZm_Int
    2484098222U,	// VSQRTSDZr
    2484098222U,	// VSQRTSDZr_Int
    2484098222U,	// VSQRTSDm
    2484098222U,	// VSQRTSDm_Int
    2484098222U,	// VSQRTSDr
    2484101450U,	// VSQRTSSZm
    2484101450U,	// VSQRTSSZm_Int
    2484101450U,	// VSQRTSSZr
    2484101450U,	// VSQRTSSZr_Int
    2484101450U,	// VSQRTSSm
    2484101450U,	// VSQRTSSm_Int
    2484101450U,	// VSQRTSSr
    72845U,	// VSTMXCSR
    2484097497U,	// VSUBPDYrm
    2484097497U,	// VSUBPDYrr
    2484093627U,	// VSUBPDZrm
    2484093627U,	// VSUBPDZrmb
    352338619U,	// VSUBPDZrmbk
    2499822267U,	// VSUBPDZrmbkz
    352342489U,	// VSUBPDZrmk
    2499826137U,	// VSUBPDZrmkz
    2484093627U,	// VSUBPDZrr
    352338619U,	// VSUBPDZrrk
    2499822267U,	// VSUBPDZrrkz
    2484097497U,	// VSUBPDrm
    2484097497U,	// VSUBPDrr
    2484100724U,	// VSUBPSYrm
    2484100724U,	// VSUBPSYrr
    2484095331U,	// VSUBPSZrm
    2484095331U,	// VSUBPSZrmb
    352340323U,	// VSUBPSZrmbk
    2499823971U,	// VSUBPSZrmbkz
    352345716U,	// VSUBPSZrmk
    2499829364U,	// VSUBPSZrmkz
    2484095331U,	// VSUBPSZrr
    352340323U,	// VSUBPSZrrk
    2499823971U,	// VSUBPSZrrkz
    2484100724U,	// VSUBPSrm
    2484100724U,	// VSUBPSrr
    2484098107U,	// VSUBSDZrm
    2484098107U,	// VSUBSDZrr
    2484098107U,	// VSUBSDrm
    2484098107U,	// VSUBSDrm_Int
    2484098107U,	// VSUBSDrr
    2484098107U,	// VSUBSDrr_Int
    2484101333U,	// VSUBSSZrm
    2484101333U,	// VSUBSSZrr
    2484101333U,	// VSUBSSrm
    2484101333U,	// VSUBSSrm_Int
    2484101333U,	// VSUBSSrr
    2484101333U,	// VSUBSSrr_Int
    940593898U,	// VTESTPDYrm
    336614122U,	// VTESTPDYrr
    537940714U,	// VTESTPDrm
    336614122U,	// VTESTPDrr
    940597178U,	// VTESTPSYrm
    336617402U,	// VTESTPSYrr
    537943994U,	// VTESTPSrm
    336617402U,	// VTESTPSrr
    571495530U,	// VUCOMISDZrm
    336614506U,	// VUCOMISDZrr
    571495530U,	// VUCOMISDrm
    336614506U,	// VUCOMISDrr
    605053188U,	// VUCOMISSZrm
    336617732U,	// VUCOMISSZrr
    605053188U,	// VUCOMISSrm
    336617732U,	// VUCOMISSrr
    2484097605U,	// VUNPCKHPDYrm
    2484097605U,	// VUNPCKHPDYrr
    2484097605U,	// VUNPCKHPDZrm
    2484097605U,	// VUNPCKHPDZrr
    2484097605U,	// VUNPCKHPDrm
    2484097605U,	// VUNPCKHPDrr
    2484100832U,	// VUNPCKHPSYrm
    2484100832U,	// VUNPCKHPSYrr
    2484100832U,	// VUNPCKHPSZrm
    2484100832U,	// VUNPCKHPSZrr
    2484100832U,	// VUNPCKHPSrm
    2484100832U,	// VUNPCKHPSrr
    2484097647U,	// VUNPCKLPDYrm
    2484097647U,	// VUNPCKLPDYrr
    2484097647U,	// VUNPCKLPDZrm
    2484097647U,	// VUNPCKLPDZrr
    2484097647U,	// VUNPCKLPDrm
    2484097647U,	// VUNPCKLPDrr
    2484100894U,	// VUNPCKLPSYrm
    2484100894U,	// VUNPCKLPSYrr
    2484100894U,	// VUNPCKLPSZrm
    2484100894U,	// VUNPCKLPSZrr
    2484100894U,	// VUNPCKLPSrm
    2484100894U,	// VUNPCKLPSrr
    2484097743U,	// VXORPDYrm
    2484097743U,	// VXORPDYrr
    2484097743U,	// VXORPDrm
    2484097743U,	// VXORPDrr
    2484100990U,	// VXORPSYrm
    2484100990U,	// VXORPSYrr
    2484100990U,	// VXORPSrm
    2484100990U,	// VXORPSrr
    11661U,	// VZEROALL
    11861U,	// VZEROUPPER
    0U,	// V_SET0
    0U,	// V_SETALLONES
    153629U,	// W64ALLOCA
    12122U,	// WAIT
    11389U,	// WBINVD
    11718U,	// WIN_ALLOCA
    11594U,	// WIN_FTOL_32
    11594U,	// WIN_FTOL_64
    22150U,	// WRFSBASE
    22150U,	// WRFSBASE64
    22170U,	// WRGSBASE
    22170U,	// WRGSBASE64
    11895U,	// WRMSR
    25084U,	// XABORT
    11460U,	// XACQUIRE_PREFIX
    1085319U,	// XADD16rm
    336613255U,	// XADD16rr
    1118087U,	// XADD32rm
    336613255U,	// XADD32rr
    1134471U,	// XADD64rm
    336613255U,	// XADD64rr
    1150855U,	// XADD8rm
    336613255U,	// XADD8rr
    11129U,	// XBEGIN
    153784U,	// XBEGIN_4
    26314U,	// XCHG16ar
    464616U,	// XCHG16rm
    726760U,	// XCHG16rr
    26477U,	// XCHG32ar
    26477U,	// XCHG32ar64
    481000U,	// XCHG32rm
    726760U,	// XCHG32rr
    26601U,	// XCHG64ar
    497384U,	// XCHG64rm
    726760U,	// XCHG64rr
    513768U,	// XCHG8rm
    726760U,	// XCHG8rr
    22289U,	// XCH_F
    11278U,	// XCRYPTCBC
    11222U,	// XCRYPTCFB
    11901U,	// XCRYPTCTR
    11212U,	// XCRYPTECB
    11232U,	// XCRYPTOFB
    11354U,	// XEND
    12177U,	// XGETBV
    11262U,	// XLAT
    26341U,	// XOR16i16
    1088627U,	// XOR16mi
    1088627U,	// XOR16mi8
    1088627U,	// XOR16mr
    34659443U,	// XOR16ri
    34659443U,	// XOR16ri8
    68213875U,	// XOR16rm
    34659443U,	// XOR16rr
    34626675U,	// XOR16rr_REV
    26507U,	// XOR32i32
    1121395U,	// XOR32mi
    1121395U,	// XOR32mi8
    1121395U,	// XOR32mr
    34659443U,	// XOR32ri
    34659443U,	// XOR32ri8
    101768307U,	// XOR32rm
    34659443U,	// XOR32rr
    34626675U,	// XOR32rr_REV
    26646U,	// XOR64i32
    1137779U,	// XOR64mi32
    1137779U,	// XOR64mi8
    1137779U,	// XOR64mr
    34659443U,	// XOR64ri32
    34659443U,	// XOR64ri8
    135322739U,	// XOR64rm
    34659443U,	// XOR64rr
    34626675U,	// XOR64rr_REV
    26229U,	// XOR8i8
    1154163U,	// XOR8mi
    1154163U,	// XOR8mr
    34659443U,	// XOR8ri
    34659443U,	// XOR8ri8
    168877171U,	// XOR8rm
    34659443U,	// XOR8rr
    34626675U,	// XOR8rr_REV
    202396368U,	// XORPDrm
    34624208U,	// XORPDrr
    202399615U,	// XORPSrm
    34627455U,	// XORPSrr
    11476U,	// XRELEASE_PREFIX
    285801U,	// XRSTOR
    281616U,	// XRSTOR64
    284359U,	// XSAVE
    281606U,	// XSAVE64
    287218U,	// XSAVEOPT
    281626U,	// XSAVEOPT64
    12184U,	// XSETBV
    10856U,	// XSHA1
    11091U,	// XSHA256
    11469U,	// XSTORE
    12160U,	// XTEST
    0U
  };

  static const uint32_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// AAA
    0U,	// AAD8i8
    0U,	// AAM8i8
    0U,	// AAS
    0U,	// ABS_F
    0U,	// ABS_Fp32
    0U,	// ABS_Fp64
    0U,	// ABS_Fp80
    0U,	// ACQUIRE_MOV16rm
    0U,	// ACQUIRE_MOV32rm
    0U,	// ACQUIRE_MOV64rm
    0U,	// ACQUIRE_MOV8rm
    0U,	// ADC16i16
    0U,	// ADC16mi
    0U,	// ADC16mi8
    0U,	// ADC16mr
    0U,	// ADC16ri
    0U,	// ADC16ri8
    0U,	// ADC16rm
    0U,	// ADC16rr
    0U,	// ADC16rr_REV
    0U,	// ADC32i32
    0U,	// ADC32mi
    0U,	// ADC32mi8
    0U,	// ADC32mr
    0U,	// ADC32ri
    0U,	// ADC32ri8
    0U,	// ADC32rm
    0U,	// ADC32rr
    0U,	// ADC32rr_REV
    0U,	// ADC64i32
    0U,	// ADC64mi32
    0U,	// ADC64mi8
    0U,	// ADC64mr
    0U,	// ADC64ri32
    0U,	// ADC64ri8
    0U,	// ADC64rm
    0U,	// ADC64rr
    0U,	// ADC64rr_REV
    0U,	// ADC8i8
    0U,	// ADC8mi
    0U,	// ADC8mr
    0U,	// ADC8ri
    0U,	// ADC8rm
    0U,	// ADC8rr
    0U,	// ADC8rr_REV
    0U,	// ADCX32rm
    0U,	// ADCX32rr
    0U,	// ADCX64rm
    0U,	// ADCX64rr
    0U,	// ADD16i16
    0U,	// ADD16mi
    0U,	// ADD16mi8
    0U,	// ADD16mr
    0U,	// ADD16ri
    0U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    0U,	// ADD16rm
    0U,	// ADD16rr
    0U,	// ADD16rr_DB
    0U,	// ADD16rr_REV
    0U,	// ADD32i32
    0U,	// ADD32mi
    0U,	// ADD32mi8
    0U,	// ADD32mr
    0U,	// ADD32ri
    0U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    0U,	// ADD32rm
    0U,	// ADD32rr
    0U,	// ADD32rr_DB
    0U,	// ADD32rr_REV
    0U,	// ADD64i32
    0U,	// ADD64mi32
    0U,	// ADD64mi8
    0U,	// ADD64mr
    0U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    0U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    0U,	// ADD64rm
    0U,	// ADD64rr
    0U,	// ADD64rr_DB
    0U,	// ADD64rr_REV
    0U,	// ADD8i8
    0U,	// ADD8mi
    0U,	// ADD8mr
    0U,	// ADD8ri
    0U,	// ADD8ri8
    0U,	// ADD8rm
    0U,	// ADD8rr
    0U,	// ADD8rr_REV
    0U,	// ADDPDrm
    0U,	// ADDPDrr
    0U,	// ADDPSrm
    0U,	// ADDPSrr
    0U,	// ADDSDrm
    0U,	// ADDSDrm_Int
    0U,	// ADDSDrr
    0U,	// ADDSDrr_Int
    0U,	// ADDSSrm
    0U,	// ADDSSrm_Int
    0U,	// ADDSSrr
    0U,	// ADDSSrr_Int
    0U,	// ADDSUBPDrm
    0U,	// ADDSUBPDrr
    0U,	// ADDSUBPSrm
    0U,	// ADDSUBPSrr
    0U,	// ADD_F32m
    0U,	// ADD_F64m
    0U,	// ADD_FI16m
    0U,	// ADD_FI32m
    0U,	// ADD_FPrST0
    0U,	// ADD_FST0r
    0U,	// ADD_Fp32
    0U,	// ADD_Fp32m
    0U,	// ADD_Fp64
    0U,	// ADD_Fp64m
    0U,	// ADD_Fp64m32
    0U,	// ADD_Fp80
    0U,	// ADD_Fp80m32
    0U,	// ADD_Fp80m64
    0U,	// ADD_FpI16m32
    0U,	// ADD_FpI16m64
    0U,	// ADD_FpI16m80
    0U,	// ADD_FpI32m32
    0U,	// ADD_FpI32m64
    0U,	// ADD_FpI32m80
    0U,	// ADD_FrST0
    0U,	// ADJCALLSTACKDOWN32
    0U,	// ADJCALLSTACKDOWN64
    0U,	// ADJCALLSTACKUP32
    0U,	// ADJCALLSTACKUP64
    0U,	// ADOX32rm
    0U,	// ADOX32rr
    0U,	// ADOX64rm
    0U,	// ADOX64rr
    0U,	// AESDECLASTrm
    0U,	// AESDECLASTrr
    0U,	// AESDECrm
    0U,	// AESDECrr
    0U,	// AESENCLASTrm
    0U,	// AESENCLASTrr
    0U,	// AESENCrm
    0U,	// AESENCrr
    0U,	// AESIMCrm
    0U,	// AESIMCrr
    0U,	// AESKEYGENASSIST128rm
    8U,	// AESKEYGENASSIST128rr
    0U,	// AND16i16
    0U,	// AND16mi
    0U,	// AND16mi8
    0U,	// AND16mr
    0U,	// AND16ri
    0U,	// AND16ri8
    0U,	// AND16rm
    0U,	// AND16rr
    0U,	// AND16rr_REV
    0U,	// AND32i32
    0U,	// AND32mi
    0U,	// AND32mi8
    0U,	// AND32mr
    0U,	// AND32ri
    0U,	// AND32ri8
    0U,	// AND32rm
    0U,	// AND32rr
    0U,	// AND32rr_REV
    0U,	// AND64i32
    0U,	// AND64mi32
    0U,	// AND64mi8
    0U,	// AND64mr
    0U,	// AND64ri32
    0U,	// AND64ri8
    0U,	// AND64rm
    0U,	// AND64rr
    0U,	// AND64rr_REV
    0U,	// AND8i8
    0U,	// AND8mi
    0U,	// AND8mr
    0U,	// AND8ri
    0U,	// AND8ri8
    0U,	// AND8rm
    0U,	// AND8rr
    0U,	// AND8rr_REV
    16U,	// ANDN32rm
    8U,	// ANDN32rr
    24U,	// ANDN64rm
    8U,	// ANDN64rr
    0U,	// ANDNPDrm
    0U,	// ANDNPDrr
    0U,	// ANDNPSrm
    0U,	// ANDNPSrr
    0U,	// ANDPDrm
    0U,	// ANDPDrr
    0U,	// ANDPSrm
    0U,	// ANDPSrr
    0U,	// ARPL16mr
    0U,	// ARPL16rr
    0U,	// AVX2_SETALLONES
    0U,	// AVX512_512_SET0
    0U,	// AVX_SET0
    0U,	// BEXTR32rm
    8U,	// BEXTR32rr
    0U,	// BEXTR64rm
    8U,	// BEXTR64rr
    0U,	// BEXTRI32mi
    8U,	// BEXTRI32ri
    0U,	// BEXTRI64mi
    8U,	// BEXTRI64ri
    0U,	// BLCFILL32rm
    0U,	// BLCFILL32rr
    0U,	// BLCFILL64rm
    0U,	// BLCFILL64rr
    0U,	// BLCI32rm
    0U,	// BLCI32rr
    0U,	// BLCI64rm
    0U,	// BLCI64rr
    0U,	// BLCIC32rm
    0U,	// BLCIC32rr
    0U,	// BLCIC64rm
    0U,	// BLCIC64rr
    0U,	// BLCMSK32rm
    0U,	// BLCMSK32rr
    0U,	// BLCMSK64rm
    0U,	// BLCMSK64rr
    0U,	// BLCS32rm
    0U,	// BLCS32rr
    0U,	// BLCS64rm
    0U,	// BLCS64rr
    32U,	// BLENDPDrmi
    40U,	// BLENDPDrri
    32U,	// BLENDPSrmi
    40U,	// BLENDPSrri
    0U,	// BLENDVPDrm0
    0U,	// BLENDVPDrr0
    0U,	// BLENDVPSrm0
    0U,	// BLENDVPSrr0
    0U,	// BLSFILL32rm
    0U,	// BLSFILL32rr
    0U,	// BLSFILL64rm
    0U,	// BLSFILL64rr
    0U,	// BLSI32rm
    0U,	// BLSI32rr
    0U,	// BLSI64rm
    0U,	// BLSI64rr
    0U,	// BLSIC32rm
    0U,	// BLSIC32rr
    0U,	// BLSIC64rm
    0U,	// BLSIC64rr
    0U,	// BLSMSK32rm
    0U,	// BLSMSK32rr
    0U,	// BLSMSK64rm
    0U,	// BLSMSK64rr
    0U,	// BLSR32rm
    0U,	// BLSR32rr
    0U,	// BLSR64rm
    0U,	// BLSR64rr
    0U,	// BOUNDS16rm
    0U,	// BOUNDS32rm
    0U,	// BSF16rm
    0U,	// BSF16rr
    0U,	// BSF32rm
    0U,	// BSF32rr
    0U,	// BSF64rm
    0U,	// BSF64rr
    0U,	// BSR16rm
    0U,	// BSR16rr
    0U,	// BSR32rm
    0U,	// BSR32rr
    0U,	// BSR64rm
    0U,	// BSR64rr
    0U,	// BSWAP32r
    0U,	// BSWAP64r
    0U,	// BT16mi8
    0U,	// BT16mr
    0U,	// BT16ri8
    0U,	// BT16rr
    0U,	// BT32mi8
    0U,	// BT32mr
    0U,	// BT32ri8
    0U,	// BT32rr
    0U,	// BT64mi8
    0U,	// BT64mr
    0U,	// BT64ri8
    0U,	// BT64rr
    0U,	// BTC16mi8
    0U,	// BTC16mr
    0U,	// BTC16ri8
    0U,	// BTC16rr
    0U,	// BTC32mi8
    0U,	// BTC32mr
    0U,	// BTC32ri8
    0U,	// BTC32rr
    0U,	// BTC64mi8
    0U,	// BTC64mr
    0U,	// BTC64ri8
    0U,	// BTC64rr
    0U,	// BTR16mi8
    0U,	// BTR16mr
    0U,	// BTR16ri8
    0U,	// BTR16rr
    0U,	// BTR32mi8
    0U,	// BTR32mr
    0U,	// BTR32ri8
    0U,	// BTR32rr
    0U,	// BTR64mi8
    0U,	// BTR64mr
    0U,	// BTR64ri8
    0U,	// BTR64rr
    0U,	// BTS16mi8
    0U,	// BTS16mr
    0U,	// BTS16ri8
    0U,	// BTS16rr
    0U,	// BTS32mi8
    0U,	// BTS32mr
    0U,	// BTS32ri8
    0U,	// BTS32rr
    0U,	// BTS64mi8
    0U,	// BTS64mr
    0U,	// BTS64ri8
    0U,	// BTS64rr
    0U,	// BZHI32rm
    8U,	// BZHI32rr
    0U,	// BZHI64rm
    8U,	// BZHI64rr
    0U,	// CALL16m
    0U,	// CALL16r
    0U,	// CALL32m
    0U,	// CALL32r
    0U,	// CALL64m
    0U,	// CALL64pcrel32
    0U,	// CALL64r
    0U,	// CALLpcrel16
    0U,	// CALLpcrel32
    0U,	// CBW
    0U,	// CDQ
    0U,	// CDQE
    0U,	// CHS_F
    0U,	// CHS_Fp32
    0U,	// CHS_Fp64
    0U,	// CHS_Fp80
    0U,	// CLAC
    0U,	// CLC
    0U,	// CLD
    0U,	// CLFLUSH
    0U,	// CLGI
    0U,	// CLI
    0U,	// CLTS
    0U,	// CMC
    0U,	// CMOVA16rm
    0U,	// CMOVA16rr
    0U,	// CMOVA32rm
    0U,	// CMOVA32rr
    0U,	// CMOVA64rm
    0U,	// CMOVA64rr
    0U,	// CMOVAE16rm
    0U,	// CMOVAE16rr
    0U,	// CMOVAE32rm
    0U,	// CMOVAE32rr
    0U,	// CMOVAE64rm
    0U,	// CMOVAE64rr
    0U,	// CMOVB16rm
    0U,	// CMOVB16rr
    0U,	// CMOVB32rm
    0U,	// CMOVB32rr
    0U,	// CMOVB64rm
    0U,	// CMOVB64rr
    0U,	// CMOVBE16rm
    0U,	// CMOVBE16rr
    0U,	// CMOVBE32rm
    0U,	// CMOVBE32rr
    0U,	// CMOVBE64rm
    0U,	// CMOVBE64rr
    0U,	// CMOVBE_F
    0U,	// CMOVBE_Fp32
    0U,	// CMOVBE_Fp64
    0U,	// CMOVBE_Fp80
    0U,	// CMOVB_F
    0U,	// CMOVB_Fp32
    0U,	// CMOVB_Fp64
    0U,	// CMOVB_Fp80
    0U,	// CMOVE16rm
    0U,	// CMOVE16rr
    0U,	// CMOVE32rm
    0U,	// CMOVE32rr
    0U,	// CMOVE64rm
    0U,	// CMOVE64rr
    0U,	// CMOVE_F
    0U,	// CMOVE_Fp32
    0U,	// CMOVE_Fp64
    0U,	// CMOVE_Fp80
    0U,	// CMOVG16rm
    0U,	// CMOVG16rr
    0U,	// CMOVG32rm
    0U,	// CMOVG32rr
    0U,	// CMOVG64rm
    0U,	// CMOVG64rr
    0U,	// CMOVGE16rm
    0U,	// CMOVGE16rr
    0U,	// CMOVGE32rm
    0U,	// CMOVGE32rr
    0U,	// CMOVGE64rm
    0U,	// CMOVGE64rr
    0U,	// CMOVL16rm
    0U,	// CMOVL16rr
    0U,	// CMOVL32rm
    0U,	// CMOVL32rr
    0U,	// CMOVL64rm
    0U,	// CMOVL64rr
    0U,	// CMOVLE16rm
    0U,	// CMOVLE16rr
    0U,	// CMOVLE32rm
    0U,	// CMOVLE32rr
    0U,	// CMOVLE64rm
    0U,	// CMOVLE64rr
    0U,	// CMOVNBE_F
    0U,	// CMOVNBE_Fp32
    0U,	// CMOVNBE_Fp64
    0U,	// CMOVNBE_Fp80
    0U,	// CMOVNB_F
    0U,	// CMOVNB_Fp32
    0U,	// CMOVNB_Fp64
    0U,	// CMOVNB_Fp80
    0U,	// CMOVNE16rm
    0U,	// CMOVNE16rr
    0U,	// CMOVNE32rm
    0U,	// CMOVNE32rr
    0U,	// CMOVNE64rm
    0U,	// CMOVNE64rr
    0U,	// CMOVNE_F
    0U,	// CMOVNE_Fp32
    0U,	// CMOVNE_Fp64
    0U,	// CMOVNE_Fp80
    0U,	// CMOVNO16rm
    0U,	// CMOVNO16rr
    0U,	// CMOVNO32rm
    0U,	// CMOVNO32rr
    0U,	// CMOVNO64rm
    0U,	// CMOVNO64rr
    0U,	// CMOVNP16rm
    0U,	// CMOVNP16rr
    0U,	// CMOVNP32rm
    0U,	// CMOVNP32rr
    0U,	// CMOVNP64rm
    0U,	// CMOVNP64rr
    0U,	// CMOVNP_F
    0U,	// CMOVNP_Fp32
    0U,	// CMOVNP_Fp64
    0U,	// CMOVNP_Fp80
    0U,	// CMOVNS16rm
    0U,	// CMOVNS16rr
    0U,	// CMOVNS32rm
    0U,	// CMOVNS32rr
    0U,	// CMOVNS64rm
    0U,	// CMOVNS64rr
    0U,	// CMOVO16rm
    0U,	// CMOVO16rr
    0U,	// CMOVO32rm
    0U,	// CMOVO32rr
    0U,	// CMOVO64rm
    0U,	// CMOVO64rr
    0U,	// CMOVP16rm
    0U,	// CMOVP16rr
    0U,	// CMOVP32rm
    0U,	// CMOVP32rr
    0U,	// CMOVP64rm
    0U,	// CMOVP64rr
    0U,	// CMOVP_F
    0U,	// CMOVP_Fp32
    0U,	// CMOVP_Fp64
    0U,	// CMOVP_Fp80
    0U,	// CMOVS16rm
    0U,	// CMOVS16rr
    0U,	// CMOVS32rm
    0U,	// CMOVS32rr
    0U,	// CMOVS64rm
    0U,	// CMOVS64rr
    0U,	// CMOV_FR32
    0U,	// CMOV_FR64
    0U,	// CMOV_GR16
    0U,	// CMOV_GR32
    0U,	// CMOV_GR8
    0U,	// CMOV_RFP32
    0U,	// CMOV_RFP64
    0U,	// CMOV_RFP80
    0U,	// CMOV_V16F32
    0U,	// CMOV_V2F64
    0U,	// CMOV_V2I64
    0U,	// CMOV_V4F32
    0U,	// CMOV_V4F64
    0U,	// CMOV_V4I64
    0U,	// CMOV_V8F32
    0U,	// CMOV_V8F64
    0U,	// CMOV_V8I64
    0U,	// CMP16i16
    0U,	// CMP16mi
    0U,	// CMP16mi8
    0U,	// CMP16mr
    0U,	// CMP16ri
    0U,	// CMP16ri8
    0U,	// CMP16rm
    0U,	// CMP16rr
    0U,	// CMP16rr_REV
    0U,	// CMP32i32
    0U,	// CMP32mi
    0U,	// CMP32mi8
    0U,	// CMP32mr
    0U,	// CMP32ri
    0U,	// CMP32ri8
    0U,	// CMP32rm
    0U,	// CMP32rr
    0U,	// CMP32rr_REV
    0U,	// CMP64i32
    0U,	// CMP64mi32
    0U,	// CMP64mi8
    0U,	// CMP64mr
    0U,	// CMP64ri32
    0U,	// CMP64ri8
    0U,	// CMP64rm
    0U,	// CMP64rr
    0U,	// CMP64rr_REV
    0U,	// CMP8i8
    0U,	// CMP8mi
    0U,	// CMP8mr
    0U,	// CMP8ri
    0U,	// CMP8rm
    0U,	// CMP8rr
    0U,	// CMP8rr_REV
    0U,	// CMPPDrmi
    32U,	// CMPPDrmi_alt
    0U,	// CMPPDrri
    40U,	// CMPPDrri_alt
    0U,	// CMPPSrmi
    32U,	// CMPPSrmi_alt
    0U,	// CMPPSrri
    40U,	// CMPPSrri_alt
    0U,	// CMPSB
    0U,	// CMPSDrm
    32U,	// CMPSDrm_alt
    0U,	// CMPSDrr
    40U,	// CMPSDrr_alt
    0U,	// CMPSL
    0U,	// CMPSQ
    0U,	// CMPSSrm
    32U,	// CMPSSrm_alt
    0U,	// CMPSSrr
    40U,	// CMPSSrr_alt
    0U,	// CMPSW
    0U,	// CMPXCHG16B
    0U,	// CMPXCHG16rm
    0U,	// CMPXCHG16rr
    0U,	// CMPXCHG32rm
    0U,	// CMPXCHG32rr
    0U,	// CMPXCHG64rm
    0U,	// CMPXCHG64rr
    0U,	// CMPXCHG8B
    0U,	// CMPXCHG8rm
    0U,	// CMPXCHG8rr
    0U,	// COMISDrm
    0U,	// COMISDrr
    0U,	// COMISSrm
    0U,	// COMISSrr
    0U,	// COMP_FST0r
    0U,	// COM_FIPr
    0U,	// COM_FIr
    0U,	// COM_FST0r
    0U,	// COS_F
    0U,	// COS_Fp32
    0U,	// COS_Fp64
    0U,	// COS_Fp80
    0U,	// CPUID32
    0U,	// CPUID64
    0U,	// CQO
    0U,	// CRC32r32m16
    0U,	// CRC32r32m32
    0U,	// CRC32r32m8
    0U,	// CRC32r32r16
    0U,	// CRC32r32r32
    0U,	// CRC32r32r8
    0U,	// CRC32r64m64
    0U,	// CRC32r64m8
    0U,	// CRC32r64r64
    0U,	// CRC32r64r8
    0U,	// CVTDQ2PDrm
    0U,	// CVTDQ2PDrr
    0U,	// CVTDQ2PSrm
    0U,	// CVTDQ2PSrr
    0U,	// CVTPD2DQrm
    0U,	// CVTPD2DQrr
    0U,	// CVTPD2PSrm
    0U,	// CVTPD2PSrr
    0U,	// CVTPS2DQrm
    0U,	// CVTPS2DQrr
    0U,	// CVTPS2PDrm
    0U,	// CVTPS2PDrr
    0U,	// CVTSD2SI64rm
    0U,	// CVTSD2SI64rr
    0U,	// CVTSD2SIrm
    0U,	// CVTSD2SIrr
    0U,	// CVTSD2SSrm
    0U,	// CVTSD2SSrr
    0U,	// CVTSI2SD64rm
    0U,	// CVTSI2SD64rr
    0U,	// CVTSI2SDrm
    0U,	// CVTSI2SDrr
    0U,	// CVTSI2SS64rm
    0U,	// CVTSI2SS64rr
    0U,	// CVTSI2SSrm
    0U,	// CVTSI2SSrr
    0U,	// CVTSS2SDrm
    0U,	// CVTSS2SDrr
    0U,	// CVTSS2SI64rm
    0U,	// CVTSS2SI64rr
    0U,	// CVTSS2SIrm
    0U,	// CVTSS2SIrr
    0U,	// CVTTPD2DQrm
    0U,	// CVTTPD2DQrr
    0U,	// CVTTPS2DQrm
    0U,	// CVTTPS2DQrr
    0U,	// CVTTSD2SI64rm
    0U,	// CVTTSD2SI64rr
    0U,	// CVTTSD2SIrm
    0U,	// CVTTSD2SIrr
    0U,	// CVTTSS2SI64rm
    0U,	// CVTTSS2SI64rr
    0U,	// CVTTSS2SIrm
    0U,	// CVTTSS2SIrr
    0U,	// CWD
    0U,	// CWDE
    0U,	// DAA
    0U,	// DAS
    0U,	// DATA16_PREFIX
    0U,	// DEC16m
    0U,	// DEC16r
    0U,	// DEC32_16r
    0U,	// DEC32_32r
    0U,	// DEC32m
    0U,	// DEC32r
    0U,	// DEC64_16m
    0U,	// DEC64_16r
    0U,	// DEC64_32m
    0U,	// DEC64_32r
    0U,	// DEC64m
    0U,	// DEC64r
    0U,	// DEC8m
    0U,	// DEC8r
    0U,	// DIV16m
    0U,	// DIV16r
    0U,	// DIV32m
    0U,	// DIV32r
    0U,	// DIV64m
    0U,	// DIV64r
    0U,	// DIV8m
    0U,	// DIV8r
    0U,	// DIVPDrm
    0U,	// DIVPDrr
    0U,	// DIVPSrm
    0U,	// DIVPSrr
    0U,	// DIVR_F32m
    0U,	// DIVR_F64m
    0U,	// DIVR_FI16m
    0U,	// DIVR_FI32m
    0U,	// DIVR_FPrST0
    0U,	// DIVR_FST0r
    0U,	// DIVR_Fp32m
    0U,	// DIVR_Fp64m
    0U,	// DIVR_Fp64m32
    0U,	// DIVR_Fp80m32
    0U,	// DIVR_Fp80m64
    0U,	// DIVR_FpI16m32
    0U,	// DIVR_FpI16m64
    0U,	// DIVR_FpI16m80
    0U,	// DIVR_FpI32m32
    0U,	// DIVR_FpI32m64
    0U,	// DIVR_FpI32m80
    0U,	// DIVR_FrST0
    0U,	// DIVSDrm
    0U,	// DIVSDrm_Int
    0U,	// DIVSDrr
    0U,	// DIVSDrr_Int
    0U,	// DIVSSrm
    0U,	// DIVSSrm_Int
    0U,	// DIVSSrr
    0U,	// DIVSSrr_Int
    0U,	// DIV_F32m
    0U,	// DIV_F64m
    0U,	// DIV_FI16m
    0U,	// DIV_FI32m
    0U,	// DIV_FPrST0
    0U,	// DIV_FST0r
    0U,	// DIV_Fp32
    0U,	// DIV_Fp32m
    0U,	// DIV_Fp64
    0U,	// DIV_Fp64m
    0U,	// DIV_Fp64m32
    0U,	// DIV_Fp80
    0U,	// DIV_Fp80m32
    0U,	// DIV_Fp80m64
    0U,	// DIV_FpI16m32
    0U,	// DIV_FpI16m64
    0U,	// DIV_FpI16m80
    0U,	// DIV_FpI32m32
    0U,	// DIV_FpI32m64
    0U,	// DIV_FpI32m80
    0U,	// DIV_FrST0
    32U,	// DPPDrmi
    40U,	// DPPDrri
    32U,	// DPPSrmi
    40U,	// DPPSrri
    0U,	// EH_RETURN
    0U,	// EH_RETURN64
    0U,	// EH_SjLj_LongJmp32
    0U,	// EH_SjLj_LongJmp64
    0U,	// EH_SjLj_SetJmp32
    0U,	// EH_SjLj_SetJmp64
    0U,	// EH_SjLj_Setup
    0U,	// ENCLS
    0U,	// ENCLU
    0U,	// ENTER
    0U,	// EXTRACTPSmr
    8U,	// EXTRACTPSrr
    0U,	// EXTRQ
    40U,	// EXTRQI
    0U,	// F2XM1
    0U,	// FARCALL16i
    0U,	// FARCALL16m
    0U,	// FARCALL32i
    0U,	// FARCALL32m
    0U,	// FARCALL64
    0U,	// FARJMP16i
    0U,	// FARJMP16m
    0U,	// FARJMP32i
    0U,	// FARJMP32m
    0U,	// FARJMP64
    0U,	// FBLDm
    0U,	// FBSTPm
    0U,	// FCOM32m
    0U,	// FCOM64m
    0U,	// FCOMP32m
    0U,	// FCOMP64m
    0U,	// FCOMPP
    0U,	// FDECSTP
    0U,	// FEMMS
    0U,	// FFREE
    0U,	// FICOM16m
    0U,	// FICOM32m
    0U,	// FICOMP16m
    0U,	// FICOMP32m
    0U,	// FINCSTP
    0U,	// FLDCW16m
    0U,	// FLDENVm
    0U,	// FLDL2E
    0U,	// FLDL2T
    0U,	// FLDLG2
    0U,	// FLDLN2
    0U,	// FLDPI
    0U,	// FNCLEX
    0U,	// FNINIT
    0U,	// FNOP
    0U,	// FNSTCW16m
    0U,	// FNSTSW16r
    0U,	// FNSTSWm
    0U,	// FP32_TO_INT16_IN_MEM
    0U,	// FP32_TO_INT32_IN_MEM
    0U,	// FP32_TO_INT64_IN_MEM
    0U,	// FP64_TO_INT16_IN_MEM
    0U,	// FP64_TO_INT32_IN_MEM
    0U,	// FP64_TO_INT64_IN_MEM
    0U,	// FP80_TO_INT16_IN_MEM
    0U,	// FP80_TO_INT32_IN_MEM
    0U,	// FP80_TO_INT64_IN_MEM
    0U,	// FPATAN
    0U,	// FPREM
    0U,	// FPREM1
    0U,	// FPTAN
    0U,	// FRNDINT
    0U,	// FRSTORm
    0U,	// FSAVEm
    0U,	// FSCALE
    0U,	// FSETPM
    0U,	// FSINCOS
    0U,	// FSTENVm
    0U,	// FXAM
    0U,	// FXRSTOR
    0U,	// FXRSTOR64
    0U,	// FXSAVE
    0U,	// FXSAVE64
    0U,	// FXTRACT
    0U,	// FYL2X
    0U,	// FYL2XP1
    0U,	// FsANDNPDrm
    0U,	// FsANDNPDrr
    0U,	// FsANDNPSrm
    0U,	// FsANDNPSrr
    0U,	// FsANDPDrm
    0U,	// FsANDPDrr
    0U,	// FsANDPSrm
    0U,	// FsANDPSrr
    0U,	// FsFLD0SD
    0U,	// FsFLD0SS
    0U,	// FsMOVAPDrm
    0U,	// FsMOVAPSrm
    0U,	// FsORPDrm
    0U,	// FsORPDrr
    0U,	// FsORPSrm
    0U,	// FsORPSrr
    0U,	// FsVMOVAPDrm
    0U,	// FsVMOVAPSrm
    0U,	// FsXORPDrm
    0U,	// FsXORPDrr
    0U,	// FsXORPSrm
    0U,	// FsXORPSrr
    0U,	// GETSEC
    0U,	// HADDPDrm
    0U,	// HADDPDrr
    0U,	// HADDPSrm
    0U,	// HADDPSrr
    0U,	// HLT
    0U,	// HSUBPDrm
    0U,	// HSUBPDrr
    0U,	// HSUBPSrm
    0U,	// HSUBPSrr
    0U,	// IDIV16m
    0U,	// IDIV16r
    0U,	// IDIV32m
    0U,	// IDIV32r
    0U,	// IDIV64m
    0U,	// IDIV64r
    0U,	// IDIV8m
    0U,	// IDIV8r
    0U,	// ILD_F16m
    0U,	// ILD_F32m
    0U,	// ILD_F64m
    0U,	// ILD_Fp16m32
    0U,	// ILD_Fp16m64
    0U,	// ILD_Fp16m80
    0U,	// ILD_Fp32m32
    0U,	// ILD_Fp32m64
    0U,	// ILD_Fp32m80
    0U,	// ILD_Fp64m32
    0U,	// ILD_Fp64m64
    0U,	// ILD_Fp64m80
    0U,	// IMUL16m
    0U,	// IMUL16r
    0U,	// IMUL16rm
    0U,	// IMUL16rmi
    0U,	// IMUL16rmi8
    0U,	// IMUL16rr
    8U,	// IMUL16rri
    8U,	// IMUL16rri8
    0U,	// IMUL32m
    0U,	// IMUL32r
    0U,	// IMUL32rm
    0U,	// IMUL32rmi
    0U,	// IMUL32rmi8
    0U,	// IMUL32rr
    8U,	// IMUL32rri
    8U,	// IMUL32rri8
    0U,	// IMUL64m
    0U,	// IMUL64r
    0U,	// IMUL64rm
    0U,	// IMUL64rmi32
    0U,	// IMUL64rmi8
    0U,	// IMUL64rr
    8U,	// IMUL64rri32
    8U,	// IMUL64rri8
    0U,	// IMUL8m
    0U,	// IMUL8r
    0U,	// IN16ri
    0U,	// IN16rr
    0U,	// IN32ri
    0U,	// IN32rr
    0U,	// IN8ri
    0U,	// IN8rr
    0U,	// INC16m
    0U,	// INC16r
    0U,	// INC32_16r
    0U,	// INC32_32r
    0U,	// INC32m
    0U,	// INC32r
    0U,	// INC64_16m
    0U,	// INC64_16r
    0U,	// INC64_32m
    0U,	// INC64_32r
    0U,	// INC64m
    0U,	// INC64r
    0U,	// INC8m
    0U,	// INC8r
    0U,	// INSB
    32U,	// INSERTPSrm
    40U,	// INSERTPSrr
    0U,	// INSERTQ
    296U,	// INSERTQI
    0U,	// INSL
    0U,	// INSW
    0U,	// INT
    0U,	// INT1
    0U,	// INT3
    0U,	// INTO
    0U,	// INVD
    0U,	// INVEPT32
    0U,	// INVEPT64
    0U,	// INVLPG
    0U,	// INVLPGA32
    0U,	// INVLPGA64
    0U,	// INVPCID32
    0U,	// INVPCID64
    0U,	// INVVPID32
    0U,	// INVVPID64
    0U,	// IRET16
    0U,	// IRET32
    0U,	// IRET64
    0U,	// ISTT_FP16m
    0U,	// ISTT_FP32m
    0U,	// ISTT_FP64m
    0U,	// ISTT_Fp16m32
    0U,	// ISTT_Fp16m64
    0U,	// ISTT_Fp16m80
    0U,	// ISTT_Fp32m32
    0U,	// ISTT_Fp32m64
    0U,	// ISTT_Fp32m80
    0U,	// ISTT_Fp64m32
    0U,	// ISTT_Fp64m64
    0U,	// ISTT_Fp64m80
    0U,	// IST_F16m
    0U,	// IST_F32m
    0U,	// IST_FP16m
    0U,	// IST_FP32m
    0U,	// IST_FP64m
    0U,	// IST_Fp16m32
    0U,	// IST_Fp16m64
    0U,	// IST_Fp16m80
    0U,	// IST_Fp32m32
    0U,	// IST_Fp32m64
    0U,	// IST_Fp32m80
    0U,	// IST_Fp64m32
    0U,	// IST_Fp64m64
    0U,	// IST_Fp64m80
    0U,	// Int_CMPSDrm
    0U,	// Int_CMPSDrr
    0U,	// Int_CMPSSrm
    0U,	// Int_CMPSSrr
    0U,	// Int_COMISDrm
    0U,	// Int_COMISDrr
    0U,	// Int_COMISSrm
    0U,	// Int_COMISSrr
    0U,	// Int_CVTSD2SSrm
    0U,	// Int_CVTSD2SSrr
    0U,	// Int_CVTSI2SD64rm
    0U,	// Int_CVTSI2SD64rr
    0U,	// Int_CVTSI2SDrm
    0U,	// Int_CVTSI2SDrr
    0U,	// Int_CVTSI2SS64rm
    0U,	// Int_CVTSI2SS64rr
    0U,	// Int_CVTSI2SSrm
    0U,	// Int_CVTSI2SSrr
    0U,	// Int_CVTSS2SDrm
    0U,	// Int_CVTSS2SDrr
    0U,	// Int_CVTTSD2SI64rm
    0U,	// Int_CVTTSD2SI64rr
    0U,	// Int_CVTTSD2SIrm
    0U,	// Int_CVTTSD2SIrr
    0U,	// Int_CVTTSS2SI64rm
    0U,	// Int_CVTTSS2SI64rr
    0U,	// Int_CVTTSS2SIrm
    0U,	// Int_CVTTSS2SIrr
    0U,	// Int_MemBarrier
    0U,	// Int_UCOMISDrm
    0U,	// Int_UCOMISDrr
    0U,	// Int_UCOMISSrm
    0U,	// Int_UCOMISSrr
    48U,	// Int_VCMPSDrm
    8U,	// Int_VCMPSDrr
    56U,	// Int_VCMPSSrm
    8U,	// Int_VCMPSSrr
    0U,	// Int_VCOMISDZrm
    0U,	// Int_VCOMISDZrr
    0U,	// Int_VCOMISDrm
    0U,	// Int_VCOMISDrr
    0U,	// Int_VCOMISSZrm
    0U,	// Int_VCOMISSZrr
    0U,	// Int_VCOMISSrm
    0U,	// Int_VCOMISSrr
    48U,	// Int_VCVTSD2SSrm
    8U,	// Int_VCVTSD2SSrr
    24U,	// Int_VCVTSI2SD64Zrm
    8U,	// Int_VCVTSI2SD64Zrr
    24U,	// Int_VCVTSI2SD64rm
    8U,	// Int_VCVTSI2SD64rr
    16U,	// Int_VCVTSI2SDZrm
    8U,	// Int_VCVTSI2SDZrr
    16U,	// Int_VCVTSI2SDrm
    8U,	// Int_VCVTSI2SDrr
    24U,	// Int_VCVTSI2SS64Zrm
    8U,	// Int_VCVTSI2SS64Zrr
    24U,	// Int_VCVTSI2SS64rm
    8U,	// Int_VCVTSI2SS64rr
    16U,	// Int_VCVTSI2SSZrm
    8U,	// Int_VCVTSI2SSZrr
    16U,	// Int_VCVTSI2SSrm
    8U,	// Int_VCVTSI2SSrr
    56U,	// Int_VCVTSS2SDrm
    8U,	// Int_VCVTSS2SDrr
    0U,	// Int_VCVTTSD2SI64Zrm
    0U,	// Int_VCVTTSD2SI64Zrr
    0U,	// Int_VCVTTSD2SI64rm
    0U,	// Int_VCVTTSD2SI64rr
    0U,	// Int_VCVTTSD2SIZrm
    0U,	// Int_VCVTTSD2SIZrr
    0U,	// Int_VCVTTSD2SIrm
    0U,	// Int_VCVTTSD2SIrr
    0U,	// Int_VCVTTSD2USI64Zrm
    0U,	// Int_VCVTTSD2USI64Zrr
    0U,	// Int_VCVTTSD2USIZrm
    0U,	// Int_VCVTTSD2USIZrr
    0U,	// Int_VCVTTSS2SI64Zrm
    0U,	// Int_VCVTTSS2SI64Zrr
    0U,	// Int_VCVTTSS2SI64rm
    0U,	// Int_VCVTTSS2SI64rr
    0U,	// Int_VCVTTSS2SIZrm
    0U,	// Int_VCVTTSS2SIZrr
    0U,	// Int_VCVTTSS2SIrm
    0U,	// Int_VCVTTSS2SIrr
    0U,	// Int_VCVTTSS2USI64Zrm
    0U,	// Int_VCVTTSS2USI64Zrr
    0U,	// Int_VCVTTSS2USIZrm
    0U,	// Int_VCVTTSS2USIZrr
    24U,	// Int_VCVTUSI2SD64Zrm
    8U,	// Int_VCVTUSI2SD64Zrr
    16U,	// Int_VCVTUSI2SDZrm
    8U,	// Int_VCVTUSI2SDZrr
    24U,	// Int_VCVTUSI2SS64Zrm
    8U,	// Int_VCVTUSI2SS64Zrr
    16U,	// Int_VCVTUSI2SSZrm
    8U,	// Int_VCVTUSI2SSZrr
    0U,	// Int_VUCOMISDZrm
    0U,	// Int_VUCOMISDZrr
    0U,	// Int_VUCOMISDrm
    0U,	// Int_VUCOMISDrr
    0U,	// Int_VUCOMISSZrm
    0U,	// Int_VUCOMISSZrr
    0U,	// Int_VUCOMISSrm
    0U,	// Int_VUCOMISSrr
    0U,	// JAE_1
    0U,	// JAE_2
    0U,	// JAE_4
    0U,	// JA_1
    0U,	// JA_2
    0U,	// JA_4
    0U,	// JBE_1
    0U,	// JBE_2
    0U,	// JBE_4
    0U,	// JB_1
    0U,	// JB_2
    0U,	// JB_4
    0U,	// JCXZ
    0U,	// JECXZ_32
    0U,	// JECXZ_64
    0U,	// JE_1
    0U,	// JE_2
    0U,	// JE_4
    0U,	// JGE_1
    0U,	// JGE_2
    0U,	// JGE_4
    0U,	// JG_1
    0U,	// JG_2
    0U,	// JG_4
    0U,	// JLE_1
    0U,	// JLE_2
    0U,	// JLE_4
    0U,	// JL_1
    0U,	// JL_2
    0U,	// JL_4
    0U,	// JMP16m
    0U,	// JMP16r
    0U,	// JMP32m
    0U,	// JMP32r
    0U,	// JMP64m
    0U,	// JMP64r
    0U,	// JMP_1
    0U,	// JMP_2
    0U,	// JMP_4
    0U,	// JNE_1
    0U,	// JNE_2
    0U,	// JNE_4
    0U,	// JNO_1
    0U,	// JNO_2
    0U,	// JNO_4
    0U,	// JNP_1
    0U,	// JNP_2
    0U,	// JNP_4
    0U,	// JNS_1
    0U,	// JNS_2
    0U,	// JNS_4
    0U,	// JO_1
    0U,	// JO_2
    0U,	// JO_4
    0U,	// JP_1
    0U,	// JP_2
    0U,	// JP_4
    0U,	// JRCXZ
    0U,	// JS_1
    0U,	// JS_2
    0U,	// JS_4
    8U,	// KANDBrr
    8U,	// KANDDrr
    8U,	// KANDNBrr
    8U,	// KANDNDrr
    8U,	// KANDNQrr
    8U,	// KANDNWrr
    8U,	// KANDQrr
    8U,	// KANDWrr
    0U,	// KMOVBkk
    0U,	// KMOVBkm
    0U,	// KMOVBkr
    0U,	// KMOVBmk
    0U,	// KMOVBrk
    0U,	// KMOVDkk
    0U,	// KMOVDkm
    0U,	// KMOVDkr
    0U,	// KMOVDmk
    0U,	// KMOVDrk
    0U,	// KMOVQkk
    0U,	// KMOVQkm
    0U,	// KMOVQkr
    0U,	// KMOVQmk
    0U,	// KMOVQrk
    0U,	// KMOVWkk
    0U,	// KMOVWkm
    0U,	// KMOVWkr
    0U,	// KMOVWmk
    0U,	// KMOVWrk
    0U,	// KNOTBrr
    0U,	// KNOTDrr
    0U,	// KNOTQrr
    0U,	// KNOTWrr
    8U,	// KORBrr
    8U,	// KORDrr
    8U,	// KORQrr
    0U,	// KORTESTWrr
    8U,	// KORWrr
    0U,	// KSET0B
    0U,	// KSET0W
    0U,	// KSET1B
    0U,	// KSET1W
    8U,	// KSHIFTLWri
    8U,	// KSHIFTRWri
    8U,	// KUNPCKBWrr
    8U,	// KXNORBrr
    8U,	// KXNORDrr
    8U,	// KXNORQrr
    8U,	// KXNORWrr
    8U,	// KXORBrr
    8U,	// KXORDrr
    8U,	// KXORQrr
    8U,	// KXORWrr
    0U,	// LAHF
    0U,	// LAR16rm
    0U,	// LAR16rr
    0U,	// LAR32rm
    0U,	// LAR32rr
    0U,	// LAR64rm
    0U,	// LAR64rr
    0U,	// LCMPXCHG16
    0U,	// LCMPXCHG16B
    0U,	// LCMPXCHG32
    0U,	// LCMPXCHG64
    0U,	// LCMPXCHG8
    0U,	// LCMPXCHG8B
    0U,	// LDDQUrm
    0U,	// LDMXCSR
    0U,	// LDS16rm
    0U,	// LDS32rm
    0U,	// LD_F0
    0U,	// LD_F1
    0U,	// LD_F32m
    0U,	// LD_F64m
    0U,	// LD_F80m
    0U,	// LD_Fp032
    0U,	// LD_Fp064
    0U,	// LD_Fp080
    0U,	// LD_Fp132
    0U,	// LD_Fp164
    0U,	// LD_Fp180
    0U,	// LD_Fp32m
    0U,	// LD_Fp32m64
    0U,	// LD_Fp32m80
    0U,	// LD_Fp64m
    0U,	// LD_Fp64m80
    0U,	// LD_Fp80m
    0U,	// LD_Frr
    0U,	// LEA16r
    0U,	// LEA32r
    0U,	// LEA64_32r
    0U,	// LEA64r
    0U,	// LEAVE
    0U,	// LEAVE64
    0U,	// LES16rm
    0U,	// LES32rm
    0U,	// LFENCE
    0U,	// LFS16rm
    0U,	// LFS32rm
    0U,	// LFS64rm
    0U,	// LGDT16m
    0U,	// LGDT32m
    0U,	// LGDT64m
    0U,	// LGS16rm
    0U,	// LGS32rm
    0U,	// LGS64rm
    0U,	// LIDT16m
    0U,	// LIDT32m
    0U,	// LIDT64m
    0U,	// LLDT16m
    0U,	// LLDT16r
    0U,	// LMSW16m
    0U,	// LMSW16r
    0U,	// LOCK_ADD16mi
    0U,	// LOCK_ADD16mi8
    0U,	// LOCK_ADD16mr
    0U,	// LOCK_ADD32mi
    0U,	// LOCK_ADD32mi8
    0U,	// LOCK_ADD32mr
    0U,	// LOCK_ADD64mi32
    0U,	// LOCK_ADD64mi8
    0U,	// LOCK_ADD64mr
    0U,	// LOCK_ADD8mi
    0U,	// LOCK_ADD8mr
    0U,	// LOCK_AND16mi
    0U,	// LOCK_AND16mi8
    0U,	// LOCK_AND16mr
    0U,	// LOCK_AND32mi
    0U,	// LOCK_AND32mi8
    0U,	// LOCK_AND32mr
    0U,	// LOCK_AND64mi32
    0U,	// LOCK_AND64mi8
    0U,	// LOCK_AND64mr
    0U,	// LOCK_AND8mi
    0U,	// LOCK_AND8mr
    0U,	// LOCK_DEC16m
    0U,	// LOCK_DEC32m
    0U,	// LOCK_DEC64m
    0U,	// LOCK_DEC8m
    0U,	// LOCK_INC16m
    0U,	// LOCK_INC32m
    0U,	// LOCK_INC64m
    0U,	// LOCK_INC8m
    0U,	// LOCK_OR16mi
    0U,	// LOCK_OR16mi8
    0U,	// LOCK_OR16mr
    0U,	// LOCK_OR32mi
    0U,	// LOCK_OR32mi8
    0U,	// LOCK_OR32mr
    0U,	// LOCK_OR64mi32
    0U,	// LOCK_OR64mi8
    0U,	// LOCK_OR64mr
    0U,	// LOCK_OR8mi
    0U,	// LOCK_OR8mr
    0U,	// LOCK_PREFIX
    0U,	// LOCK_SUB16mi
    0U,	// LOCK_SUB16mi8
    0U,	// LOCK_SUB16mr
    0U,	// LOCK_SUB32mi
    0U,	// LOCK_SUB32mi8
    0U,	// LOCK_SUB32mr
    0U,	// LOCK_SUB64mi32
    0U,	// LOCK_SUB64mi8
    0U,	// LOCK_SUB64mr
    0U,	// LOCK_SUB8mi
    0U,	// LOCK_SUB8mr
    0U,	// LOCK_XOR16mi
    0U,	// LOCK_XOR16mi8
    0U,	// LOCK_XOR16mr
    0U,	// LOCK_XOR32mi
    0U,	// LOCK_XOR32mi8
    0U,	// LOCK_XOR32mr
    0U,	// LOCK_XOR64mi32
    0U,	// LOCK_XOR64mi8
    0U,	// LOCK_XOR64mr
    0U,	// LOCK_XOR8mi
    0U,	// LOCK_XOR8mr
    0U,	// LODSB
    0U,	// LODSL
    0U,	// LODSQ
    0U,	// LODSW
    0U,	// LOOP
    0U,	// LOOPE
    0U,	// LOOPNE
    0U,	// LRETIL
    0U,	// LRETIQ
    0U,	// LRETIW
    0U,	// LRETL
    0U,	// LRETQ
    0U,	// LRETW
    0U,	// LSL16rm
    0U,	// LSL16rr
    0U,	// LSL32rm
    0U,	// LSL32rr
    0U,	// LSL64rm
    0U,	// LSL64rr
    0U,	// LSS16rm
    0U,	// LSS32rm
    0U,	// LSS64rm
    0U,	// LTRm
    0U,	// LTRr
    0U,	// LXADD16
    0U,	// LXADD32
    0U,	// LXADD64
    0U,	// LXADD8
    0U,	// LZCNT16rm
    0U,	// LZCNT16rr
    0U,	// LZCNT32rm
    0U,	// LZCNT32rr
    0U,	// LZCNT64rm
    0U,	// LZCNT64rr
    0U,	// MASKMOVDQU
    0U,	// MASKMOVDQU64
    0U,	// MAXCPDrm
    0U,	// MAXCPDrr
    0U,	// MAXCPSrm
    0U,	// MAXCPSrr
    0U,	// MAXCSDrm
    0U,	// MAXCSDrr
    0U,	// MAXCSSrm
    0U,	// MAXCSSrr
    0U,	// MAXPDrm
    0U,	// MAXPDrr
    0U,	// MAXPSrm
    0U,	// MAXPSrr
    0U,	// MAXSDrm
    0U,	// MAXSDrm_Int
    0U,	// MAXSDrr
    0U,	// MAXSDrr_Int
    0U,	// MAXSSrm
    0U,	// MAXSSrm_Int
    0U,	// MAXSSrr
    0U,	// MAXSSrr_Int
    0U,	// MFENCE
    0U,	// MINCPDrm
    0U,	// MINCPDrr
    0U,	// MINCPSrm
    0U,	// MINCPSrr
    0U,	// MINCSDrm
    0U,	// MINCSDrr
    0U,	// MINCSSrm
    0U,	// MINCSSrr
    0U,	// MINPDrm
    0U,	// MINPDrr
    0U,	// MINPSrm
    0U,	// MINPSrr
    0U,	// MINSDrm
    0U,	// MINSDrm_Int
    0U,	// MINSDrr
    0U,	// MINSDrr_Int
    0U,	// MINSSrm
    0U,	// MINSSrm_Int
    0U,	// MINSSrr
    0U,	// MINSSrr_Int
    0U,	// MMX_CVTPD2PIirm
    0U,	// MMX_CVTPD2PIirr
    0U,	// MMX_CVTPI2PDirm
    0U,	// MMX_CVTPI2PDirr
    0U,	// MMX_CVTPI2PSirm
    0U,	// MMX_CVTPI2PSirr
    0U,	// MMX_CVTPS2PIirm
    0U,	// MMX_CVTPS2PIirr
    0U,	// MMX_CVTTPD2PIirm
    0U,	// MMX_CVTTPD2PIirr
    0U,	// MMX_CVTTPS2PIirm
    0U,	// MMX_CVTTPS2PIirr
    0U,	// MMX_EMMS
    0U,	// MMX_MASKMOVQ
    0U,	// MMX_MASKMOVQ64
    0U,	// MMX_MOVD64from64rr
    0U,	// MMX_MOVD64grr
    0U,	// MMX_MOVD64mr
    0U,	// MMX_MOVD64rm
    0U,	// MMX_MOVD64rr
    0U,	// MMX_MOVD64to64rr
    0U,	// MMX_MOVDQ2Qrr
    0U,	// MMX_MOVFR642Qrr
    0U,	// MMX_MOVNTQmr
    0U,	// MMX_MOVQ2DQrr
    0U,	// MMX_MOVQ2FR64rr
    0U,	// MMX_MOVQ64mr
    0U,	// MMX_MOVQ64rm
    0U,	// MMX_MOVQ64rr
    0U,	// MMX_MOVQ64rr_REV
    0U,	// MMX_PABSBrm64
    0U,	// MMX_PABSBrr64
    0U,	// MMX_PABSDrm64
    0U,	// MMX_PABSDrr64
    0U,	// MMX_PABSWrm64
    0U,	// MMX_PABSWrr64
    0U,	// MMX_PACKSSDWirm
    0U,	// MMX_PACKSSDWirr
    0U,	// MMX_PACKSSWBirm
    0U,	// MMX_PACKSSWBirr
    0U,	// MMX_PACKUSWBirm
    0U,	// MMX_PACKUSWBirr
    0U,	// MMX_PADDBirm
    0U,	// MMX_PADDBirr
    0U,	// MMX_PADDDirm
    0U,	// MMX_PADDDirr
    0U,	// MMX_PADDQirm
    0U,	// MMX_PADDQirr
    0U,	// MMX_PADDSBirm
    0U,	// MMX_PADDSBirr
    0U,	// MMX_PADDSWirm
    0U,	// MMX_PADDSWirr
    0U,	// MMX_PADDUSBirm
    0U,	// MMX_PADDUSBirr
    0U,	// MMX_PADDUSWirm
    0U,	// MMX_PADDUSWirr
    0U,	// MMX_PADDWirm
    0U,	// MMX_PADDWirr
    32U,	// MMX_PALIGNR64irm
    40U,	// MMX_PALIGNR64irr
    0U,	// MMX_PANDNirm
    0U,	// MMX_PANDNirr
    0U,	// MMX_PANDirm
    0U,	// MMX_PANDirr
    0U,	// MMX_PAVGBirm
    0U,	// MMX_PAVGBirr
    0U,	// MMX_PAVGWirm
    0U,	// MMX_PAVGWirr
    0U,	// MMX_PCMPEQBirm
    0U,	// MMX_PCMPEQBirr
    0U,	// MMX_PCMPEQDirm
    0U,	// MMX_PCMPEQDirr
    0U,	// MMX_PCMPEQWirm
    0U,	// MMX_PCMPEQWirr
    0U,	// MMX_PCMPGTBirm
    0U,	// MMX_PCMPGTBirr
    0U,	// MMX_PCMPGTDirm
    0U,	// MMX_PCMPGTDirr
    0U,	// MMX_PCMPGTWirm
    0U,	// MMX_PCMPGTWirr
    8U,	// MMX_PEXTRWirri
    0U,	// MMX_PHADDSWrm64
    0U,	// MMX_PHADDSWrr64
    0U,	// MMX_PHADDWrm64
    0U,	// MMX_PHADDWrr64
    0U,	// MMX_PHADDrm64
    0U,	// MMX_PHADDrr64
    0U,	// MMX_PHSUBDrm64
    0U,	// MMX_PHSUBDrr64
    0U,	// MMX_PHSUBSWrm64
    0U,	// MMX_PHSUBSWrr64
    0U,	// MMX_PHSUBWrm64
    0U,	// MMX_PHSUBWrr64
    32U,	// MMX_PINSRWirmi
    40U,	// MMX_PINSRWirri
    0U,	// MMX_PMADDUBSWrm64
    0U,	// MMX_PMADDUBSWrr64
    0U,	// MMX_PMADDWDirm
    0U,	// MMX_PMADDWDirr
    0U,	// MMX_PMAXSWirm
    0U,	// MMX_PMAXSWirr
    0U,	// MMX_PMAXUBirm
    0U,	// MMX_PMAXUBirr
    0U,	// MMX_PMINSWirm
    0U,	// MMX_PMINSWirr
    0U,	// MMX_PMINUBirm
    0U,	// MMX_PMINUBirr
    0U,	// MMX_PMOVMSKBrr
    0U,	// MMX_PMULHRSWrm64
    0U,	// MMX_PMULHRSWrr64
    0U,	// MMX_PMULHUWirm
    0U,	// MMX_PMULHUWirr
    0U,	// MMX_PMULHWirm
    0U,	// MMX_PMULHWirr
    0U,	// MMX_PMULLWirm
    0U,	// MMX_PMULLWirr
    0U,	// MMX_PMULUDQirm
    0U,	// MMX_PMULUDQirr
    0U,	// MMX_PORirm
    0U,	// MMX_PORirr
    0U,	// MMX_PSADBWirm
    0U,	// MMX_PSADBWirr
    0U,	// MMX_PSHUFBrm64
    0U,	// MMX_PSHUFBrr64
    0U,	// MMX_PSHUFWmi
    8U,	// MMX_PSHUFWri
    0U,	// MMX_PSIGNBrm64
    0U,	// MMX_PSIGNBrr64
    0U,	// MMX_PSIGNDrm64
    0U,	// MMX_PSIGNDrr64
    0U,	// MMX_PSIGNWrm64
    0U,	// MMX_PSIGNWrr64
    0U,	// MMX_PSLLDri
    0U,	// MMX_PSLLDrm
    0U,	// MMX_PSLLDrr
    0U,	// MMX_PSLLQri
    0U,	// MMX_PSLLQrm
    0U,	// MMX_PSLLQrr
    0U,	// MMX_PSLLWri
    0U,	// MMX_PSLLWrm
    0U,	// MMX_PSLLWrr
    0U,	// MMX_PSRADri
    0U,	// MMX_PSRADrm
    0U,	// MMX_PSRADrr
    0U,	// MMX_PSRAWri
    0U,	// MMX_PSRAWrm
    0U,	// MMX_PSRAWrr
    0U,	// MMX_PSRLDri
    0U,	// MMX_PSRLDrm
    0U,	// MMX_PSRLDrr
    0U,	// MMX_PSRLQri
    0U,	// MMX_PSRLQrm
    0U,	// MMX_PSRLQrr
    0U,	// MMX_PSRLWri
    0U,	// MMX_PSRLWrm
    0U,	// MMX_PSRLWrr
    0U,	// MMX_PSUBBirm
    0U,	// MMX_PSUBBirr
    0U,	// MMX_PSUBDirm
    0U,	// MMX_PSUBDirr
    0U,	// MMX_PSUBQirm
    0U,	// MMX_PSUBQirr
    0U,	// MMX_PSUBSBirm
    0U,	// MMX_PSUBSBirr
    0U,	// MMX_PSUBSWirm
    0U,	// MMX_PSUBSWirr
    0U,	// MMX_PSUBUSBirm
    0U,	// MMX_PSUBUSBirr
    0U,	// MMX_PSUBUSWirm
    0U,	// MMX_PSUBUSWirr
    0U,	// MMX_PSUBWirm
    0U,	// MMX_PSUBWirr
    0U,	// MMX_PUNPCKHBWirm
    0U,	// MMX_PUNPCKHBWirr
    0U,	// MMX_PUNPCKHDQirm
    0U,	// MMX_PUNPCKHDQirr
    0U,	// MMX_PUNPCKHWDirm
    0U,	// MMX_PUNPCKHWDirr
    0U,	// MMX_PUNPCKLBWirm
    0U,	// MMX_PUNPCKLBWirr
    0U,	// MMX_PUNPCKLDQirm
    0U,	// MMX_PUNPCKLDQirr
    0U,	// MMX_PUNPCKLWDirm
    0U,	// MMX_PUNPCKLWDirr
    0U,	// MMX_PXORirm
    0U,	// MMX_PXORirr
    0U,	// MONITOR
    0U,	// MONITORrrr
    0U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    0U,	// MOV16ao16
    0U,	// MOV16ao16_16
    0U,	// MOV16mi
    0U,	// MOV16mr
    0U,	// MOV16ms
    0U,	// MOV16o16a
    0U,	// MOV16o16a_16
    0U,	// MOV16ri
    0U,	// MOV16ri_alt
    0U,	// MOV16rm
    0U,	// MOV16rr
    0U,	// MOV16rr_REV
    0U,	// MOV16rs
    0U,	// MOV16sm
    0U,	// MOV16sr
    0U,	// MOV32ao32
    0U,	// MOV32ao32_16
    0U,	// MOV32cr
    0U,	// MOV32dr
    0U,	// MOV32mi
    0U,	// MOV32mr
    0U,	// MOV32ms
    0U,	// MOV32o32a
    0U,	// MOV32o32a_16
    0U,	// MOV32r0
    0U,	// MOV32rc
    0U,	// MOV32rd
    0U,	// MOV32ri
    0U,	// MOV32ri64
    0U,	// MOV32ri_alt
    0U,	// MOV32rm
    0U,	// MOV32rr
    0U,	// MOV32rr_REV
    0U,	// MOV32rs
    0U,	// MOV32sm
    0U,	// MOV32sr
    0U,	// MOV64ao16
    0U,	// MOV64ao32
    0U,	// MOV64ao64
    0U,	// MOV64ao8
    0U,	// MOV64cr
    0U,	// MOV64dr
    0U,	// MOV64mi32
    0U,	// MOV64mr
    0U,	// MOV64ms
    0U,	// MOV64o16a
    0U,	// MOV64o32a
    0U,	// MOV64o64a
    0U,	// MOV64o8a
    0U,	// MOV64rc
    0U,	// MOV64rd
    0U,	// MOV64ri
    0U,	// MOV64ri32
    0U,	// MOV64rm
    0U,	// MOV64rr
    0U,	// MOV64rr_REV
    0U,	// MOV64rs
    0U,	// MOV64sm
    0U,	// MOV64sr
    0U,	// MOV64toPQIrr
    0U,	// MOV64toSDrm
    0U,	// MOV64toSDrr
    0U,	// MOV8ao8
    0U,	// MOV8ao8_16
    0U,	// MOV8mi
    0U,	// MOV8mr
    1U,	// MOV8mr_NOREX
    0U,	// MOV8o8a
    0U,	// MOV8o8a_16
    0U,	// MOV8ri
    0U,	// MOV8ri_alt
    0U,	// MOV8rm
    1U,	// MOV8rm_NOREX
    0U,	// MOV8rr
    1U,	// MOV8rr_NOREX
    0U,	// MOV8rr_REV
    0U,	// MOVAPDmr
    0U,	// MOVAPDrm
    0U,	// MOVAPDrr
    0U,	// MOVAPDrr_REV
    0U,	// MOVAPSmr
    0U,	// MOVAPSrm
    0U,	// MOVAPSrr
    0U,	// MOVAPSrr_REV
    0U,	// MOVBE16mr
    0U,	// MOVBE16rm
    0U,	// MOVBE32mr
    0U,	// MOVBE32rm
    0U,	// MOVBE64mr
    0U,	// MOVBE64rm
    0U,	// MOVDDUPrm
    0U,	// MOVDDUPrr
    0U,	// MOVDI2PDIrm
    0U,	// MOVDI2PDIrr
    0U,	// MOVDI2SSrm
    0U,	// MOVDI2SSrr
    0U,	// MOVDQAmr
    0U,	// MOVDQArm
    0U,	// MOVDQArr
    0U,	// MOVDQArr_REV
    0U,	// MOVDQUmr
    0U,	// MOVDQUrm
    0U,	// MOVDQUrr
    0U,	// MOVDQUrr_REV
    0U,	// MOVHLPSrr
    0U,	// MOVHPDmr
    0U,	// MOVHPDrm
    0U,	// MOVHPSmr
    0U,	// MOVHPSrm
    0U,	// MOVLHPSrr
    0U,	// MOVLPDmr
    0U,	// MOVLPDrm
    0U,	// MOVLPSmr
    0U,	// MOVLPSrm
    0U,	// MOVMSKPDrr
    0U,	// MOVMSKPSrr
    0U,	// MOVNTDQArm
    0U,	// MOVNTDQmr
    0U,	// MOVNTI_64mr
    0U,	// MOVNTImr
    0U,	// MOVNTPDmr
    0U,	// MOVNTPSmr
    0U,	// MOVNTSD
    0U,	// MOVNTSS
    0U,	// MOVPC32r
    0U,	// MOVPDI2DImr
    0U,	// MOVPDI2DIrr
    0U,	// MOVPQI2QImr
    0U,	// MOVPQI2QIrr
    0U,	// MOVPQIto64rr
    0U,	// MOVQI2PQIrm
    0U,	// MOVSB
    0U,	// MOVSDmr
    0U,	// MOVSDrm
    0U,	// MOVSDrr
    0U,	// MOVSDrr_REV
    0U,	// MOVSDto64mr
    0U,	// MOVSDto64rr
    0U,	// MOVSHDUPrm
    0U,	// MOVSHDUPrr
    0U,	// MOVSL
    0U,	// MOVSLDUPrm
    0U,	// MOVSLDUPrr
    0U,	// MOVSQ
    0U,	// MOVSS2DImr
    0U,	// MOVSS2DIrr
    0U,	// MOVSSmr
    0U,	// MOVSSrm
    0U,	// MOVSSrr
    0U,	// MOVSSrr_REV
    0U,	// MOVSW
    0U,	// MOVSX16rm8
    0U,	// MOVSX16rr8
    0U,	// MOVSX32rm16
    0U,	// MOVSX32rm8
    0U,	// MOVSX32rr16
    0U,	// MOVSX32rr8
    0U,	// MOVSX64_NOREXrr32
    0U,	// MOVSX64rm16
    0U,	// MOVSX64rm32
    0U,	// MOVSX64rm8
    0U,	// MOVSX64rr16
    0U,	// MOVSX64rr32
    0U,	// MOVSX64rr8
    0U,	// MOVUPDmr
    0U,	// MOVUPDrm
    0U,	// MOVUPDrr
    0U,	// MOVUPDrr_REV
    0U,	// MOVUPSmr
    0U,	// MOVUPSrm
    0U,	// MOVUPSrr
    0U,	// MOVUPSrr_REV
    0U,	// MOVZPQILo2PQIrm
    0U,	// MOVZPQILo2PQIrr
    0U,	// MOVZQI2PQIrm
    0U,	// MOVZQI2PQIrr
    0U,	// MOVZX16rm8
    0U,	// MOVZX16rr8
    0U,	// MOVZX32_NOREXrm8
    0U,	// MOVZX32_NOREXrr8
    0U,	// MOVZX32rm16
    0U,	// MOVZX32rm8
    0U,	// MOVZX32rr16
    0U,	// MOVZX32rr8
    0U,	// MOVZX64rm16_Q
    0U,	// MOVZX64rm8_Q
    0U,	// MOVZX64rr16_Q
    0U,	// MOVZX64rr8_Q
    32U,	// MPSADBWrmi
    40U,	// MPSADBWrri
    0U,	// MUL16m
    0U,	// MUL16r
    0U,	// MUL32m
    0U,	// MUL32r
    0U,	// MUL64m
    0U,	// MUL64r
    0U,	// MUL8m
    0U,	// MUL8r
    0U,	// MULPDrm
    0U,	// MULPDrr
    0U,	// MULPSrm
    0U,	// MULPSrr
    0U,	// MULSDrm
    0U,	// MULSDrm_Int
    0U,	// MULSDrr
    0U,	// MULSDrr_Int
    0U,	// MULSSrm
    0U,	// MULSSrm_Int
    0U,	// MULSSrr
    0U,	// MULSSrr_Int
    16U,	// MULX32rm
    8U,	// MULX32rr
    24U,	// MULX64rm
    8U,	// MULX64rr
    0U,	// MUL_F32m
    0U,	// MUL_F64m
    0U,	// MUL_FI16m
    0U,	// MUL_FI32m
    0U,	// MUL_FPrST0
    0U,	// MUL_FST0r
    0U,	// MUL_Fp32
    0U,	// MUL_Fp32m
    0U,	// MUL_Fp64
    0U,	// MUL_Fp64m
    0U,	// MUL_Fp64m32
    0U,	// MUL_Fp80
    0U,	// MUL_Fp80m32
    0U,	// MUL_Fp80m64
    0U,	// MUL_FpI16m32
    0U,	// MUL_FpI16m64
    0U,	// MUL_FpI16m80
    0U,	// MUL_FpI32m32
    0U,	// MUL_FpI32m64
    0U,	// MUL_FpI32m80
    0U,	// MUL_FrST0
    0U,	// MWAITrr
    0U,	// NEG16m
    0U,	// NEG16r
    0U,	// NEG32m
    0U,	// NEG32r
    0U,	// NEG64m
    0U,	// NEG64r
    0U,	// NEG8m
    0U,	// NEG8r
    0U,	// NOOP
    0U,	// NOOP18_16m4
    0U,	// NOOP18_16m5
    0U,	// NOOP18_16m6
    0U,	// NOOP18_16m7
    0U,	// NOOP18_16r4
    0U,	// NOOP18_16r5
    0U,	// NOOP18_16r6
    0U,	// NOOP18_16r7
    0U,	// NOOP18_m4
    0U,	// NOOP18_m5
    0U,	// NOOP18_m6
    0U,	// NOOP18_m7
    0U,	// NOOP18_r4
    0U,	// NOOP18_r5
    0U,	// NOOP18_r6
    0U,	// NOOP18_r7
    0U,	// NOOP19rr
    0U,	// NOOPL
    0U,	// NOOPL_19
    0U,	// NOOPL_1a
    0U,	// NOOPL_1b
    0U,	// NOOPL_1c
    0U,	// NOOPL_1d
    0U,	// NOOPL_1e
    0U,	// NOOPW
    0U,	// NOOPW_19
    0U,	// NOOPW_1a
    0U,	// NOOPW_1b
    0U,	// NOOPW_1c
    0U,	// NOOPW_1d
    0U,	// NOOPW_1e
    0U,	// NOT16m
    0U,	// NOT16r
    0U,	// NOT32m
    0U,	// NOT32r
    0U,	// NOT64m
    0U,	// NOT64r
    0U,	// NOT8m
    0U,	// NOT8r
    0U,	// OR16i16
    0U,	// OR16mi
    0U,	// OR16mi8
    0U,	// OR16mr
    0U,	// OR16ri
    0U,	// OR16ri8
    0U,	// OR16rm
    0U,	// OR16rr
    0U,	// OR16rr_REV
    0U,	// OR32i32
    0U,	// OR32mi
    0U,	// OR32mi8
    0U,	// OR32mr
    0U,	// OR32mrLocked
    0U,	// OR32ri
    0U,	// OR32ri8
    0U,	// OR32rm
    0U,	// OR32rr
    0U,	// OR32rr_REV
    0U,	// OR64i32
    0U,	// OR64mi32
    0U,	// OR64mi8
    0U,	// OR64mr
    0U,	// OR64ri32
    0U,	// OR64ri8
    0U,	// OR64rm
    0U,	// OR64rr
    0U,	// OR64rr_REV
    0U,	// OR8i8
    0U,	// OR8mi
    0U,	// OR8mr
    0U,	// OR8ri
    0U,	// OR8ri8
    0U,	// OR8rm
    0U,	// OR8rr
    0U,	// OR8rr_REV
    0U,	// ORPDrm
    0U,	// ORPDrr
    0U,	// ORPSrm
    0U,	// ORPSrr
    0U,	// OUT16ir
    0U,	// OUT16rr
    0U,	// OUT32ir
    0U,	// OUT32rr
    0U,	// OUT8ir
    0U,	// OUT8rr
    0U,	// OUTSB
    0U,	// OUTSL
    0U,	// OUTSW
    0U,	// PABSBrm128
    0U,	// PABSBrr128
    0U,	// PABSDrm128
    0U,	// PABSDrr128
    0U,	// PABSWrm128
    0U,	// PABSWrr128
    0U,	// PACKSSDWrm
    0U,	// PACKSSDWrr
    0U,	// PACKSSWBrm
    0U,	// PACKSSWBrr
    0U,	// PACKUSDWrm
    0U,	// PACKUSDWrr
    0U,	// PACKUSWBrm
    0U,	// PACKUSWBrr
    0U,	// PADDBrm
    0U,	// PADDBrr
    0U,	// PADDDrm
    0U,	// PADDDrr
    0U,	// PADDQrm
    0U,	// PADDQrr
    0U,	// PADDSBrm
    0U,	// PADDSBrr
    0U,	// PADDSWrm
    0U,	// PADDSWrr
    0U,	// PADDUSBrm
    0U,	// PADDUSBrr
    0U,	// PADDUSWrm
    0U,	// PADDUSWrr
    0U,	// PADDWrm
    0U,	// PADDWrr
    32U,	// PALIGNR128rm
    40U,	// PALIGNR128rr
    0U,	// PANDNrm
    0U,	// PANDNrr
    0U,	// PANDrm
    0U,	// PANDrr
    0U,	// PAUSE
    0U,	// PAVGBrm
    0U,	// PAVGBrr
    0U,	// PAVGUSBrm
    0U,	// PAVGUSBrr
    0U,	// PAVGWrm
    0U,	// PAVGWrr
    0U,	// PBLENDVBrm0
    0U,	// PBLENDVBrr0
    32U,	// PBLENDWrmi
    40U,	// PBLENDWrri
    32U,	// PCLMULQDQrm
    40U,	// PCLMULQDQrr
    0U,	// PCMPEQBrm
    0U,	// PCMPEQBrr
    0U,	// PCMPEQDrm
    0U,	// PCMPEQDrr
    0U,	// PCMPEQQrm
    0U,	// PCMPEQQrr
    0U,	// PCMPEQWrm
    0U,	// PCMPEQWrr
    0U,	// PCMPESTRIMEM
    0U,	// PCMPESTRIREG
    0U,	// PCMPESTRIrm
    8U,	// PCMPESTRIrr
    0U,	// PCMPESTRM128MEM
    0U,	// PCMPESTRM128REG
    0U,	// PCMPESTRM128rm
    8U,	// PCMPESTRM128rr
    0U,	// PCMPGTBrm
    0U,	// PCMPGTBrr
    0U,	// PCMPGTDrm
    0U,	// PCMPGTDrr
    0U,	// PCMPGTQrm
    0U,	// PCMPGTQrr
    0U,	// PCMPGTWrm
    0U,	// PCMPGTWrr
    0U,	// PCMPISTRIMEM
    0U,	// PCMPISTRIREG
    0U,	// PCMPISTRIrm
    8U,	// PCMPISTRIrr
    0U,	// PCMPISTRM128MEM
    0U,	// PCMPISTRM128REG
    0U,	// PCMPISTRM128rm
    8U,	// PCMPISTRM128rr
    16U,	// PDEP32rm
    8U,	// PDEP32rr
    24U,	// PDEP64rm
    8U,	// PDEP64rr
    16U,	// PEXT32rm
    8U,	// PEXT32rr
    24U,	// PEXT64rm
    8U,	// PEXT64rr
    0U,	// PEXTRBmr
    8U,	// PEXTRBrr
    0U,	// PEXTRDmr
    8U,	// PEXTRDrr
    0U,	// PEXTRQmr
    8U,	// PEXTRQrr
    0U,	// PEXTRWmr
    8U,	// PEXTRWri
    8U,	// PEXTRWrr_REV
    0U,	// PF2IDrm
    0U,	// PF2IDrr
    0U,	// PF2IWrm
    0U,	// PF2IWrr
    0U,	// PFACCrm
    0U,	// PFACCrr
    0U,	// PFADDrm
    0U,	// PFADDrr
    0U,	// PFCMPEQrm
    0U,	// PFCMPEQrr
    0U,	// PFCMPGErm
    0U,	// PFCMPGErr
    0U,	// PFCMPGTrm
    0U,	// PFCMPGTrr
    0U,	// PFMAXrm
    0U,	// PFMAXrr
    0U,	// PFMINrm
    0U,	// PFMINrr
    0U,	// PFMULrm
    0U,	// PFMULrr
    0U,	// PFNACCrm
    0U,	// PFNACCrr
    0U,	// PFPNACCrm
    0U,	// PFPNACCrr
    0U,	// PFRCPIT1rm
    0U,	// PFRCPIT1rr
    0U,	// PFRCPIT2rm
    0U,	// PFRCPIT2rr
    0U,	// PFRCPrm
    0U,	// PFRCPrr
    0U,	// PFRSQIT1rm
    0U,	// PFRSQIT1rr
    0U,	// PFRSQRTrm
    0U,	// PFRSQRTrr
    0U,	// PFSUBRrm
    0U,	// PFSUBRrr
    0U,	// PFSUBrm
    0U,	// PFSUBrr
    0U,	// PHADDDrm
    0U,	// PHADDDrr
    0U,	// PHADDSWrm128
    0U,	// PHADDSWrr128
    0U,	// PHADDWrm
    0U,	// PHADDWrr
    0U,	// PHMINPOSUWrm128
    0U,	// PHMINPOSUWrr128
    0U,	// PHSUBDrm
    0U,	// PHSUBDrr
    0U,	// PHSUBSWrm128
    0U,	// PHSUBSWrr128
    0U,	// PHSUBWrm
    0U,	// PHSUBWrr
    0U,	// PI2FDrm
    0U,	// PI2FDrr
    0U,	// PI2FWrm
    0U,	// PI2FWrr
    32U,	// PINSRBrm
    40U,	// PINSRBrr
    32U,	// PINSRDrm
    40U,	// PINSRDrr
    32U,	// PINSRQrm
    40U,	// PINSRQrr
    32U,	// PINSRWrmi
    40U,	// PINSRWrri
    0U,	// PMADDUBSWrm128
    0U,	// PMADDUBSWrr128
    0U,	// PMADDWDrm
    0U,	// PMADDWDrr
    0U,	// PMAXSBrm
    0U,	// PMAXSBrr
    0U,	// PMAXSDrm
    0U,	// PMAXSDrr
    0U,	// PMAXSWrm
    0U,	// PMAXSWrr
    0U,	// PMAXUBrm
    0U,	// PMAXUBrr
    0U,	// PMAXUDrm
    0U,	// PMAXUDrr
    0U,	// PMAXUWrm
    0U,	// PMAXUWrr
    0U,	// PMINSBrm
    0U,	// PMINSBrr
    0U,	// PMINSDrm
    0U,	// PMINSDrr
    0U,	// PMINSWrm
    0U,	// PMINSWrr
    0U,	// PMINUBrm
    0U,	// PMINUBrr
    0U,	// PMINUDrm
    0U,	// PMINUDrr
    0U,	// PMINUWrm
    0U,	// PMINUWrr
    0U,	// PMOVMSKBrr
    0U,	// PMOVSXBDrm
    0U,	// PMOVSXBDrr
    0U,	// PMOVSXBQrm
    0U,	// PMOVSXBQrr
    0U,	// PMOVSXBWrm
    0U,	// PMOVSXBWrr
    0U,	// PMOVSXDQrm
    0U,	// PMOVSXDQrr
    0U,	// PMOVSXWDrm
    0U,	// PMOVSXWDrr
    0U,	// PMOVSXWQrm
    0U,	// PMOVSXWQrr
    0U,	// PMOVZXBDrm
    0U,	// PMOVZXBDrr
    0U,	// PMOVZXBQrm
    0U,	// PMOVZXBQrr
    0U,	// PMOVZXBWrm
    0U,	// PMOVZXBWrr
    0U,	// PMOVZXDQrm
    0U,	// PMOVZXDQrr
    0U,	// PMOVZXWDrm
    0U,	// PMOVZXWDrr
    0U,	// PMOVZXWQrm
    0U,	// PMOVZXWQrr
    0U,	// PMULDQrm
    0U,	// PMULDQrr
    0U,	// PMULHRSWrm128
    0U,	// PMULHRSWrr128
    0U,	// PMULHRWrm
    0U,	// PMULHRWrr
    0U,	// PMULHUWrm
    0U,	// PMULHUWrr
    0U,	// PMULHWrm
    0U,	// PMULHWrr
    0U,	// PMULLDrm
    0U,	// PMULLDrr
    0U,	// PMULLWrm
    0U,	// PMULLWrr
    0U,	// PMULUDQrm
    0U,	// PMULUDQrr
    0U,	// POP16r
    0U,	// POP16rmm
    0U,	// POP16rmr
    0U,	// POP32r
    0U,	// POP32rmm
    0U,	// POP32rmr
    0U,	// POP64r
    0U,	// POP64rmm
    0U,	// POP64rmr
    0U,	// POPA16
    0U,	// POPA32
    0U,	// POPCNT16rm
    0U,	// POPCNT16rr
    0U,	// POPCNT32rm
    0U,	// POPCNT32rr
    0U,	// POPCNT64rm
    0U,	// POPCNT64rr
    0U,	// POPDS16
    0U,	// POPDS32
    0U,	// POPES16
    0U,	// POPES32
    0U,	// POPF16
    0U,	// POPF32
    0U,	// POPF64
    0U,	// POPFS16
    0U,	// POPFS32
    0U,	// POPFS64
    0U,	// POPGS16
    0U,	// POPGS32
    0U,	// POPGS64
    0U,	// POPSS16
    0U,	// POPSS32
    0U,	// PORrm
    0U,	// PORrr
    0U,	// PREFETCH
    0U,	// PREFETCHNTA
    0U,	// PREFETCHT0
    0U,	// PREFETCHT1
    0U,	// PREFETCHT2
    0U,	// PREFETCHW
    0U,	// PSADBWrm
    0U,	// PSADBWrr
    0U,	// PSHUFBrm
    0U,	// PSHUFBrr
    0U,	// PSHUFDmi
    8U,	// PSHUFDri
    0U,	// PSHUFHWmi
    8U,	// PSHUFHWri
    0U,	// PSHUFLWmi
    8U,	// PSHUFLWri
    0U,	// PSIGNBrm
    0U,	// PSIGNBrr
    0U,	// PSIGNDrm
    0U,	// PSIGNDrr
    0U,	// PSIGNWrm
    0U,	// PSIGNWrr
    0U,	// PSLLDQri
    0U,	// PSLLDri
    0U,	// PSLLDrm
    0U,	// PSLLDrr
    0U,	// PSLLQri
    0U,	// PSLLQrm
    0U,	// PSLLQrr
    0U,	// PSLLWri
    0U,	// PSLLWrm
    0U,	// PSLLWrr
    0U,	// PSRADri
    0U,	// PSRADrm
    0U,	// PSRADrr
    0U,	// PSRAWri
    0U,	// PSRAWrm
    0U,	// PSRAWrr
    0U,	// PSRLDQri
    0U,	// PSRLDri
    0U,	// PSRLDrm
    0U,	// PSRLDrr
    0U,	// PSRLQri
    0U,	// PSRLQrm
    0U,	// PSRLQrr
    0U,	// PSRLWri
    0U,	// PSRLWrm
    0U,	// PSRLWrr
    0U,	// PSUBBrm
    0U,	// PSUBBrr
    0U,	// PSUBDrm
    0U,	// PSUBDrr
    0U,	// PSUBQrm
    0U,	// PSUBQrr
    0U,	// PSUBSBrm
    0U,	// PSUBSBrr
    0U,	// PSUBSWrm
    0U,	// PSUBSWrr
    0U,	// PSUBUSBrm
    0U,	// PSUBUSBrr
    0U,	// PSUBUSWrm
    0U,	// PSUBUSWrr
    0U,	// PSUBWrm
    0U,	// PSUBWrr
    0U,	// PSWAPDrm
    0U,	// PSWAPDrr
    0U,	// PTESTrm
    0U,	// PTESTrr
    0U,	// PUNPCKHBWrm
    0U,	// PUNPCKHBWrr
    0U,	// PUNPCKHDQrm
    0U,	// PUNPCKHDQrr
    0U,	// PUNPCKHQDQrm
    0U,	// PUNPCKHQDQrr
    0U,	// PUNPCKHWDrm
    0U,	// PUNPCKHWDrr
    0U,	// PUNPCKLBWrm
    0U,	// PUNPCKLBWrr
    0U,	// PUNPCKLDQrm
    0U,	// PUNPCKLDQrr
    0U,	// PUNPCKLQDQrm
    0U,	// PUNPCKLQDQrr
    0U,	// PUNPCKLWDrm
    0U,	// PUNPCKLWDrr
    0U,	// PUSH16i8
    0U,	// PUSH16r
    0U,	// PUSH16rmm
    0U,	// PUSH16rmr
    0U,	// PUSH32i8
    0U,	// PUSH32r
    0U,	// PUSH32rmm
    0U,	// PUSH32rmr
    0U,	// PUSH64i16
    0U,	// PUSH64i32
    0U,	// PUSH64i8
    0U,	// PUSH64r
    0U,	// PUSH64rmm
    0U,	// PUSH64rmr
    0U,	// PUSHA16
    0U,	// PUSHA32
    0U,	// PUSHCS16
    0U,	// PUSHCS32
    0U,	// PUSHDS16
    0U,	// PUSHDS32
    0U,	// PUSHES16
    0U,	// PUSHES32
    0U,	// PUSHF16
    0U,	// PUSHF32
    0U,	// PUSHF64
    0U,	// PUSHFS16
    0U,	// PUSHFS32
    0U,	// PUSHFS64
    0U,	// PUSHGS16
    0U,	// PUSHGS32
    0U,	// PUSHGS64
    0U,	// PUSHSS16
    0U,	// PUSHSS32
    0U,	// PUSHi16
    0U,	// PUSHi32
    0U,	// PXORrm
    0U,	// PXORrr
    0U,	// RCL16m1
    0U,	// RCL16mCL
    0U,	// RCL16mi
    0U,	// RCL16r1
    0U,	// RCL16rCL
    0U,	// RCL16ri
    0U,	// RCL32m1
    0U,	// RCL32mCL
    0U,	// RCL32mi
    0U,	// RCL32r1
    0U,	// RCL32rCL
    0U,	// RCL32ri
    0U,	// RCL64m1
    0U,	// RCL64mCL
    0U,	// RCL64mi
    0U,	// RCL64r1
    0U,	// RCL64rCL
    0U,	// RCL64ri
    0U,	// RCL8m1
    0U,	// RCL8mCL
    0U,	// RCL8mi
    0U,	// RCL8r1
    0U,	// RCL8rCL
    0U,	// RCL8ri
    0U,	// RCPPSm
    0U,	// RCPPSm_Int
    0U,	// RCPPSr
    0U,	// RCPPSr_Int
    0U,	// RCPSSm
    0U,	// RCPSSm_Int
    0U,	// RCPSSr
    0U,	// RCPSSr_Int
    0U,	// RCR16m1
    0U,	// RCR16mCL
    0U,	// RCR16mi
    0U,	// RCR16r1
    0U,	// RCR16rCL
    0U,	// RCR16ri
    0U,	// RCR32m1
    0U,	// RCR32mCL
    0U,	// RCR32mi
    0U,	// RCR32r1
    0U,	// RCR32rCL
    0U,	// RCR32ri
    0U,	// RCR64m1
    0U,	// RCR64mCL
    0U,	// RCR64mi
    0U,	// RCR64r1
    0U,	// RCR64rCL
    0U,	// RCR64ri
    0U,	// RCR8m1
    0U,	// RCR8mCL
    0U,	// RCR8mi
    0U,	// RCR8r1
    0U,	// RCR8rCL
    0U,	// RCR8ri
    0U,	// RDFSBASE
    0U,	// RDFSBASE64
    0U,	// RDGSBASE
    0U,	// RDGSBASE64
    0U,	// RDMSR
    0U,	// RDPMC
    0U,	// RDRAND16r
    0U,	// RDRAND32r
    0U,	// RDRAND64r
    0U,	// RDSEED16r
    0U,	// RDSEED32r
    0U,	// RDSEED64r
    0U,	// RDTSC
    0U,	// RDTSCP
    0U,	// RELEASE_MOV16mr
    0U,	// RELEASE_MOV32mr
    0U,	// RELEASE_MOV64mr
    0U,	// RELEASE_MOV8mr
    0U,	// REPNE_PREFIX
    0U,	// REP_MOVSB_32
    0U,	// REP_MOVSB_64
    0U,	// REP_MOVSD_32
    0U,	// REP_MOVSD_64
    0U,	// REP_MOVSQ_64
    0U,	// REP_MOVSW_32
    0U,	// REP_MOVSW_64
    0U,	// REP_PREFIX
    0U,	// REP_STOSB_32
    0U,	// REP_STOSB_64
    0U,	// REP_STOSD_32
    0U,	// REP_STOSD_64
    0U,	// REP_STOSQ_64
    0U,	// REP_STOSW_32
    0U,	// REP_STOSW_64
    0U,	// RETIL
    0U,	// RETIQ
    0U,	// RETIW
    0U,	// RETL
    0U,	// RETQ
    0U,	// RETW
    0U,	// REX64_PREFIX
    0U,	// ROL16m1
    0U,	// ROL16mCL
    0U,	// ROL16mi
    0U,	// ROL16r1
    0U,	// ROL16rCL
    0U,	// ROL16ri
    0U,	// ROL32m1
    0U,	// ROL32mCL
    0U,	// ROL32mi
    0U,	// ROL32r1
    0U,	// ROL32rCL
    0U,	// ROL32ri
    0U,	// ROL64m1
    0U,	// ROL64mCL
    0U,	// ROL64mi
    0U,	// ROL64r1
    0U,	// ROL64rCL
    0U,	// ROL64ri
    0U,	// ROL8m1
    0U,	// ROL8mCL
    0U,	// ROL8mi
    0U,	// ROL8r1
    0U,	// ROL8rCL
    0U,	// ROL8ri
    0U,	// ROR16m1
    0U,	// ROR16mCL
    0U,	// ROR16mi
    0U,	// ROR16r1
    0U,	// ROR16rCL
    0U,	// ROR16ri
    0U,	// ROR32m1
    0U,	// ROR32mCL
    0U,	// ROR32mi
    0U,	// ROR32r1
    0U,	// ROR32rCL
    0U,	// ROR32ri
    0U,	// ROR64m1
    0U,	// ROR64mCL
    0U,	// ROR64mi
    0U,	// ROR64r1
    0U,	// ROR64rCL
    0U,	// ROR64ri
    0U,	// ROR8m1
    0U,	// ROR8mCL
    0U,	// ROR8mi
    0U,	// ROR8r1
    0U,	// ROR8rCL
    0U,	// ROR8ri
    0U,	// RORX32mi
    8U,	// RORX32ri
    0U,	// RORX64mi
    8U,	// RORX64ri
    0U,	// ROUNDPDm
    8U,	// ROUNDPDr
    0U,	// ROUNDPSm
    8U,	// ROUNDPSr
    32U,	// ROUNDSDm
    40U,	// ROUNDSDr
    40U,	// ROUNDSDr_Int
    32U,	// ROUNDSSm
    40U,	// ROUNDSSr
    40U,	// ROUNDSSr_Int
    0U,	// RSM
    0U,	// RSQRTPSm
    0U,	// RSQRTPSm_Int
    0U,	// RSQRTPSr
    0U,	// RSQRTPSr_Int
    0U,	// RSQRTSSm
    0U,	// RSQRTSSm_Int
    0U,	// RSQRTSSr
    0U,	// RSQRTSSr_Int
    0U,	// SAHF
    0U,	// SAL16m1
    0U,	// SAL16mCL
    0U,	// SAL16mi
    0U,	// SAL16r1
    0U,	// SAL16rCL
    0U,	// SAL16ri
    0U,	// SAL32m1
    0U,	// SAL32mCL
    0U,	// SAL32mi
    0U,	// SAL32r1
    0U,	// SAL32rCL
    0U,	// SAL32ri
    0U,	// SAL64m1
    0U,	// SAL64mCL
    0U,	// SAL64mi
    0U,	// SAL64r1
    0U,	// SAL64rCL
    0U,	// SAL64ri
    0U,	// SAL8m1
    0U,	// SAL8mCL
    0U,	// SAL8mi
    0U,	// SAL8r1
    0U,	// SAL8rCL
    0U,	// SAL8ri
    0U,	// SALC
    0U,	// SAR16m1
    0U,	// SAR16mCL
    0U,	// SAR16mi
    0U,	// SAR16r1
    0U,	// SAR16rCL
    0U,	// SAR16ri
    0U,	// SAR32m1
    0U,	// SAR32mCL
    0U,	// SAR32mi
    0U,	// SAR32r1
    0U,	// SAR32rCL
    0U,	// SAR32ri
    0U,	// SAR64m1
    0U,	// SAR64mCL
    0U,	// SAR64mi
    0U,	// SAR64r1
    0U,	// SAR64rCL
    0U,	// SAR64ri
    0U,	// SAR8m1
    0U,	// SAR8mCL
    0U,	// SAR8mi
    0U,	// SAR8r1
    0U,	// SAR8rCL
    0U,	// SAR8ri
    0U,	// SARX32rm
    8U,	// SARX32rr
    0U,	// SARX64rm
    8U,	// SARX64rr
    0U,	// SBB16i16
    0U,	// SBB16mi
    0U,	// SBB16mi8
    0U,	// SBB16mr
    0U,	// SBB16ri
    0U,	// SBB16ri8
    0U,	// SBB16rm
    0U,	// SBB16rr
    0U,	// SBB16rr_REV
    0U,	// SBB32i32
    0U,	// SBB32mi
    0U,	// SBB32mi8
    0U,	// SBB32mr
    0U,	// SBB32ri
    0U,	// SBB32ri8
    0U,	// SBB32rm
    0U,	// SBB32rr
    0U,	// SBB32rr_REV
    0U,	// SBB64i32
    0U,	// SBB64mi32
    0U,	// SBB64mi8
    0U,	// SBB64mr
    0U,	// SBB64ri32
    0U,	// SBB64ri8
    0U,	// SBB64rm
    0U,	// SBB64rr
    0U,	// SBB64rr_REV
    0U,	// SBB8i8
    0U,	// SBB8mi
    0U,	// SBB8mr
    0U,	// SBB8ri
    0U,	// SBB8rm
    0U,	// SBB8rr
    0U,	// SBB8rr_REV
    0U,	// SCASB
    0U,	// SCASL
    0U,	// SCASQ
    0U,	// SCASW
    0U,	// SEG_ALLOCA_32
    0U,	// SEG_ALLOCA_64
    0U,	// SEH_EndPrologue
    0U,	// SEH_Epilogue
    0U,	// SEH_PushFrame
    0U,	// SEH_PushReg
    0U,	// SEH_SaveReg
    0U,	// SEH_SaveXMM
    0U,	// SEH_SetFrame
    0U,	// SEH_StackAlloc
    0U,	// SETAEm
    0U,	// SETAEr
    0U,	// SETAm
    0U,	// SETAr
    0U,	// SETBEm
    0U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    0U,	// SETBm
    0U,	// SETBr
    0U,	// SETEm
    0U,	// SETEr
    0U,	// SETGEm
    0U,	// SETGEr
    0U,	// SETGm
    0U,	// SETGr
    0U,	// SETLEm
    0U,	// SETLEr
    0U,	// SETLm
    0U,	// SETLr
    0U,	// SETNEm
    0U,	// SETNEr
    0U,	// SETNOm
    0U,	// SETNOr
    0U,	// SETNPm
    0U,	// SETNPr
    0U,	// SETNSm
    0U,	// SETNSr
    0U,	// SETOm
    0U,	// SETOr
    0U,	// SETPm
    0U,	// SETPr
    0U,	// SETSm
    0U,	// SETSr
    0U,	// SFENCE
    0U,	// SGDT16m
    0U,	// SGDT32m
    0U,	// SGDT64m
    0U,	// SHA1MSG1rm
    0U,	// SHA1MSG1rr
    0U,	// SHA1MSG2rm
    0U,	// SHA1MSG2rr
    0U,	// SHA1NEXTErm
    0U,	// SHA1NEXTErr
    32U,	// SHA1RNDS4rmi
    40U,	// SHA1RNDS4rri
    0U,	// SHA256MSG1rm
    0U,	// SHA256MSG1rr
    0U,	// SHA256MSG2rm
    0U,	// SHA256MSG2rr
    0U,	// SHA256RNDS2rm
    0U,	// SHA256RNDS2rr
    0U,	// SHL16m1
    0U,	// SHL16mCL
    0U,	// SHL16mi
    0U,	// SHL16r1
    0U,	// SHL16rCL
    0U,	// SHL16ri
    0U,	// SHL32m1
    0U,	// SHL32mCL
    0U,	// SHL32mi
    0U,	// SHL32r1
    0U,	// SHL32rCL
    0U,	// SHL32ri
    0U,	// SHL64m1
    0U,	// SHL64mCL
    0U,	// SHL64mi
    0U,	// SHL64r1
    0U,	// SHL64rCL
    0U,	// SHL64ri
    0U,	// SHL8m1
    0U,	// SHL8mCL
    0U,	// SHL8mi
    0U,	// SHL8r1
    0U,	// SHL8rCL
    0U,	// SHL8ri
    1U,	// SHLD16mrCL
    0U,	// SHLD16mri8
    1U,	// SHLD16rrCL
    40U,	// SHLD16rri8
    1U,	// SHLD32mrCL
    0U,	// SHLD32mri8
    1U,	// SHLD32rrCL
    40U,	// SHLD32rri8
    1U,	// SHLD64mrCL
    0U,	// SHLD64mri8
    1U,	// SHLD64rrCL
    40U,	// SHLD64rri8
    0U,	// SHLX32rm
    8U,	// SHLX32rr
    0U,	// SHLX64rm
    8U,	// SHLX64rr
    0U,	// SHR16m1
    0U,	// SHR16mCL
    0U,	// SHR16mi
    0U,	// SHR16r1
    0U,	// SHR16rCL
    0U,	// SHR16ri
    0U,	// SHR32m1
    0U,	// SHR32mCL
    0U,	// SHR32mi
    0U,	// SHR32r1
    0U,	// SHR32rCL
    0U,	// SHR32ri
    0U,	// SHR64m1
    0U,	// SHR64mCL
    0U,	// SHR64mi
    0U,	// SHR64r1
    0U,	// SHR64rCL
    0U,	// SHR64ri
    0U,	// SHR8m1
    0U,	// SHR8mCL
    0U,	// SHR8mi
    0U,	// SHR8r1
    0U,	// SHR8rCL
    0U,	// SHR8ri
    1U,	// SHRD16mrCL
    0U,	// SHRD16mri8
    1U,	// SHRD16rrCL
    40U,	// SHRD16rri8
    1U,	// SHRD32mrCL
    0U,	// SHRD32mri8
    1U,	// SHRD32rrCL
    40U,	// SHRD32rri8
    1U,	// SHRD64mrCL
    0U,	// SHRD64mri8
    1U,	// SHRD64rrCL
    40U,	// SHRD64rri8
    0U,	// SHRX32rm
    8U,	// SHRX32rr
    0U,	// SHRX64rm
    8U,	// SHRX64rr
    32U,	// SHUFPDrmi
    40U,	// SHUFPDrri
    32U,	// SHUFPSrmi
    40U,	// SHUFPSrri
    0U,	// SIDT16m
    0U,	// SIDT32m
    0U,	// SIDT64m
    0U,	// SIN_F
    0U,	// SIN_Fp32
    0U,	// SIN_Fp64
    0U,	// SIN_Fp80
    0U,	// SKINIT
    0U,	// SLDT16m
    0U,	// SLDT16r
    0U,	// SLDT32r
    0U,	// SLDT64m
    0U,	// SLDT64r
    0U,	// SMSW16m
    0U,	// SMSW16r
    0U,	// SMSW32r
    0U,	// SMSW64r
    0U,	// SQRTPDm
    0U,	// SQRTPDr
    0U,	// SQRTPSm
    0U,	// SQRTPSr
    0U,	// SQRTSDm
    0U,	// SQRTSDm_Int
    0U,	// SQRTSDr
    0U,	// SQRTSDr_Int
    0U,	// SQRTSSm
    0U,	// SQRTSSm_Int
    0U,	// SQRTSSr
    0U,	// SQRTSSr_Int
    0U,	// SQRT_F
    0U,	// SQRT_Fp32
    0U,	// SQRT_Fp64
    0U,	// SQRT_Fp80
    0U,	// STAC
    0U,	// STC
    0U,	// STD
    0U,	// STGI
    0U,	// STI
    0U,	// STMXCSR
    0U,	// STOSB
    0U,	// STOSL
    0U,	// STOSQ
    0U,	// STOSW
    0U,	// STR16r
    0U,	// STR32r
    0U,	// STR64r
    0U,	// STRm
    0U,	// ST_F32m
    0U,	// ST_F64m
    0U,	// ST_FCOMPST0r
    0U,	// ST_FCOMPST0r_alt
    0U,	// ST_FCOMST0r
    0U,	// ST_FP32m
    0U,	// ST_FP64m
    0U,	// ST_FP80m
    0U,	// ST_FPNCEST0r
    0U,	// ST_FPST0r
    0U,	// ST_FPST0r_alt
    0U,	// ST_FPrr
    0U,	// ST_FXCHST0r
    0U,	// ST_FXCHST0r_alt
    0U,	// ST_Fp32m
    0U,	// ST_Fp64m
    0U,	// ST_Fp64m32
    0U,	// ST_Fp80m32
    0U,	// ST_Fp80m64
    0U,	// ST_FpP32m
    0U,	// ST_FpP64m
    0U,	// ST_FpP64m32
    0U,	// ST_FpP80m
    0U,	// ST_FpP80m32
    0U,	// ST_FpP80m64
    0U,	// ST_Frr
    0U,	// SUB16i16
    0U,	// SUB16mi
    0U,	// SUB16mi8
    0U,	// SUB16mr
    0U,	// SUB16ri
    0U,	// SUB16ri8
    0U,	// SUB16rm
    0U,	// SUB16rr
    0U,	// SUB16rr_REV
    0U,	// SUB32i32
    0U,	// SUB32mi
    0U,	// SUB32mi8
    0U,	// SUB32mr
    0U,	// SUB32ri
    0U,	// SUB32ri8
    0U,	// SUB32rm
    0U,	// SUB32rr
    0U,	// SUB32rr_REV
    0U,	// SUB64i32
    0U,	// SUB64mi32
    0U,	// SUB64mi8
    0U,	// SUB64mr
    0U,	// SUB64ri32
    0U,	// SUB64ri8
    0U,	// SUB64rm
    0U,	// SUB64rr
    0U,	// SUB64rr_REV
    0U,	// SUB8i8
    0U,	// SUB8mi
    0U,	// SUB8mr
    0U,	// SUB8ri
    0U,	// SUB8ri8
    0U,	// SUB8rm
    0U,	// SUB8rr
    0U,	// SUB8rr_REV
    0U,	// SUBPDrm
    0U,	// SUBPDrr
    0U,	// SUBPSrm
    0U,	// SUBPSrr
    0U,	// SUBR_F32m
    0U,	// SUBR_F64m
    0U,	// SUBR_FI16m
    0U,	// SUBR_FI32m
    0U,	// SUBR_FPrST0
    0U,	// SUBR_FST0r
    0U,	// SUBR_Fp32m
    0U,	// SUBR_Fp64m
    0U,	// SUBR_Fp64m32
    0U,	// SUBR_Fp80m32
    0U,	// SUBR_Fp80m64
    0U,	// SUBR_FpI16m32
    0U,	// SUBR_FpI16m64
    0U,	// SUBR_FpI16m80
    0U,	// SUBR_FpI32m32
    0U,	// SUBR_FpI32m64
    0U,	// SUBR_FpI32m80
    0U,	// SUBR_FrST0
    0U,	// SUBSDrm
    0U,	// SUBSDrm_Int
    0U,	// SUBSDrr
    0U,	// SUBSDrr_Int
    0U,	// SUBSSrm
    0U,	// SUBSSrm_Int
    0U,	// SUBSSrr
    0U,	// SUBSSrr_Int
    0U,	// SUB_F32m
    0U,	// SUB_F64m
    0U,	// SUB_FI16m
    0U,	// SUB_FI32m
    0U,	// SUB_FPrST0
    0U,	// SUB_FST0r
    0U,	// SUB_Fp32
    0U,	// SUB_Fp32m
    0U,	// SUB_Fp64
    0U,	// SUB_Fp64m
    0U,	// SUB_Fp64m32
    0U,	// SUB_Fp80
    0U,	// SUB_Fp80m32
    0U,	// SUB_Fp80m64
    0U,	// SUB_FpI16m32
    0U,	// SUB_FpI16m64
    0U,	// SUB_FpI16m80
    0U,	// SUB_FpI32m32
    0U,	// SUB_FpI32m64
    0U,	// SUB_FpI32m80
    0U,	// SUB_FrST0
    0U,	// SWAPGS
    0U,	// SYSCALL
    0U,	// SYSENTER
    0U,	// SYSEXIT
    0U,	// SYSEXIT64
    0U,	// SYSRET
    0U,	// SYSRET64
    0U,	// T1MSKC32rm
    0U,	// T1MSKC32rr
    0U,	// T1MSKC64rm
    0U,	// T1MSKC64rr
    0U,	// TAILJMPd
    0U,	// TAILJMPd64
    0U,	// TAILJMPm
    0U,	// TAILJMPm64
    0U,	// TAILJMPr
    0U,	// TAILJMPr64
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    0U,	// TEST16i16
    0U,	// TEST16mi
    0U,	// TEST16mi_alt
    0U,	// TEST16ri
    0U,	// TEST16ri_alt
    0U,	// TEST16rm
    0U,	// TEST16rr
    0U,	// TEST32i32
    0U,	// TEST32mi
    0U,	// TEST32mi_alt
    0U,	// TEST32ri
    0U,	// TEST32ri_alt
    0U,	// TEST32rm
    0U,	// TEST32rr
    0U,	// TEST64i32
    0U,	// TEST64mi32
    0U,	// TEST64mi32_alt
    0U,	// TEST64ri32
    0U,	// TEST64ri32_alt
    0U,	// TEST64rm
    0U,	// TEST64rr
    0U,	// TEST8i8
    0U,	// TEST8mi
    0U,	// TEST8mi_alt
    0U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    0U,	// TEST8ri_alt
    0U,	// TEST8rm
    0U,	// TEST8rr
    0U,	// TLSCall_32
    0U,	// TLSCall_64
    0U,	// TLS_addr32
    0U,	// TLS_addr64
    0U,	// TLS_base_addr32
    0U,	// TLS_base_addr64
    0U,	// TRAP
    0U,	// TST_F
    0U,	// TST_Fp32
    0U,	// TST_Fp64
    0U,	// TST_Fp80
    0U,	// TZCNT16rm
    0U,	// TZCNT16rr
    0U,	// TZCNT32rm
    0U,	// TZCNT32rr
    0U,	// TZCNT64rm
    0U,	// TZCNT64rr
    0U,	// TZMSK32rm
    0U,	// TZMSK32rr
    0U,	// TZMSK64rm
    0U,	// TZMSK64rr
    0U,	// UCOMISDrm
    0U,	// UCOMISDrr
    0U,	// UCOMISSrm
    0U,	// UCOMISSrr
    0U,	// UCOM_FIPr
    0U,	// UCOM_FIr
    0U,	// UCOM_FPPr
    0U,	// UCOM_FPr
    0U,	// UCOM_FpIr32
    0U,	// UCOM_FpIr64
    0U,	// UCOM_FpIr80
    0U,	// UCOM_Fpr32
    0U,	// UCOM_Fpr64
    0U,	// UCOM_Fpr80
    0U,	// UCOM_Fr
    0U,	// UD2B
    0U,	// UNPCKHPDrm
    0U,	// UNPCKHPDrr
    0U,	// UNPCKHPSrm
    0U,	// UNPCKHPSrr
    0U,	// UNPCKLPDrm
    0U,	// UNPCKLPDrr
    0U,	// UNPCKLPSrm
    0U,	// UNPCKLPSrr
    35072U,	// VAARG_64
    64U,	// VADDPDYrm
    8U,	// VADDPDYrr
    72U,	// VADDPDZrm
    560U,	// VADDPDZrmb
    69898U,	// VADDPDZrmbk
    69898U,	// VADDPDZrmbkz
    6410U,	// VADDPDZrmk
    6410U,	// VADDPDZrmkz
    8U,	// VADDPDZrr
    8458U,	// VADDPDZrrk
    8458U,	// VADDPDZrrkz
    80U,	// VADDPDrm
    8U,	// VADDPDrr
    64U,	// VADDPSYrm
    8U,	// VADDPSYrr
    72U,	// VADDPSZrm
    824U,	// VADDPSZrmb
    108810U,	// VADDPSZrmbk
    108810U,	// VADDPSZrmbkz
    6410U,	// VADDPSZrmk
    6410U,	// VADDPSZrmkz
    8U,	// VADDPSZrr
    8458U,	// VADDPSZrrk
    8458U,	// VADDPSZrrkz
    80U,	// VADDPSrm
    8U,	// VADDPSrr
    48U,	// VADDSDZrm
    8U,	// VADDSDZrr
    48U,	// VADDSDrm
    48U,	// VADDSDrm_Int
    8U,	// VADDSDrr
    8U,	// VADDSDrr_Int
    56U,	// VADDSSZrm
    8U,	// VADDSSZrr
    56U,	// VADDSSrm
    56U,	// VADDSSrm_Int
    8U,	// VADDSSrr
    8U,	// VADDSSrr_Int
    64U,	// VADDSUBPDYrm
    8U,	// VADDSUBPDYrr
    80U,	// VADDSUBPDrm
    8U,	// VADDSUBPDrr
    64U,	// VADDSUBPSYrm
    8U,	// VADDSUBPSYrr
    80U,	// VADDSUBPSrm
    8U,	// VADDSUBPSrr
    88U,	// VAESDECLASTrm
    8U,	// VAESDECLASTrr
    88U,	// VAESDECrm
    8U,	// VAESDECrr
    88U,	// VAESENCLASTrm
    8U,	// VAESENCLASTrr
    88U,	// VAESENCrm
    8U,	// VAESENCrr
    0U,	// VAESIMCrm
    0U,	// VAESIMCrr
    0U,	// VAESKEYGENASSIST128rm
    8U,	// VAESKEYGENASSIST128rr
    2400U,	// VALIGNDrmi
    8456U,	// VALIGNDrri
    295210U,	// VALIGNDrrik
    565514U,	// VALIGNDrrikz
    2400U,	// VALIGNQrmi
    8456U,	// VALIGNQrri
    295210U,	// VALIGNQrrik
    565514U,	// VALIGNQrrikz
    64U,	// VANDNPDYrm
    8U,	// VANDNPDYrr
    80U,	// VANDNPDrm
    8U,	// VANDNPDrr
    64U,	// VANDNPSYrm
    8U,	// VANDNPSYrr
    80U,	// VANDNPSrm
    8U,	// VANDNPSrr
    64U,	// VANDPDYrm
    8U,	// VANDPDYrr
    80U,	// VANDPDrm
    8U,	// VANDPDrr
    64U,	// VANDPSYrm
    8U,	// VANDPSYrr
    80U,	// VANDPSrm
    8U,	// VANDPSrr
    8U,	// VASTART_SAVE_XMM_REGS
    6410U,	// VBLENDMPDZrm
    8458U,	// VBLENDMPDZrr
    6410U,	// VBLENDMPSZrm
    8458U,	// VBLENDMPSZrr
    2368U,	// VBLENDPDYrmi
    8456U,	// VBLENDPDYrri
    2384U,	// VBLENDPDrmi
    8456U,	// VBLENDPDrri
    2368U,	// VBLENDPSYrmi
    8456U,	// VBLENDPSYrri
    2384U,	// VBLENDPSrmi
    8456U,	// VBLENDPSrri
    2368U,	// VBLENDVPDYrm
    8456U,	// VBLENDVPDYrr
    2384U,	// VBLENDVPDrm
    8456U,	// VBLENDVPDrr
    2368U,	// VBLENDVPSYrm
    8456U,	// VBLENDVPSYrr
    2384U,	// VBLENDVPSrm
    8456U,	// VBLENDVPSrr
    0U,	// VBROADCASTF128
    0U,	// VBROADCASTI128
    90U,	// VBROADCASTI32X4krm
    0U,	// VBROADCASTI32X4rm
    106U,	// VBROADCASTI64X4krm
    0U,	// VBROADCASTI64X4rm
    0U,	// VBROADCASTSDYrm
    0U,	// VBROADCASTSDYrr
    0U,	// VBROADCASTSDZrm
    0U,	// VBROADCASTSDZrr
    0U,	// VBROADCASTSSYrm
    0U,	// VBROADCASTSSYrr
    0U,	// VBROADCASTSSZrm
    0U,	// VBROADCASTSSZrr
    0U,	// VBROADCASTSSrm
    0U,	// VBROADCASTSSrr
    64U,	// VCMPPDYrmi
    2368U,	// VCMPPDYrmi_alt
    8U,	// VCMPPDYrri
    8456U,	// VCMPPDYrri_alt
    0U,	// VCMPPDZrmi
    2376U,	// VCMPPDZrmi_alt
    0U,	// VCMPPDZrri
    8456U,	// VCMPPDZrri_alt
    3U,	// VCMPPDZrrib
    80U,	// VCMPPDrmi
    2384U,	// VCMPPDrmi_alt
    8U,	// VCMPPDrri
    8456U,	// VCMPPDrri_alt
    64U,	// VCMPPSYrmi
    2368U,	// VCMPPSYrmi_alt
    8U,	// VCMPPSYrri
    8456U,	// VCMPPSYrri_alt
    0U,	// VCMPPSZrmi
    2376U,	// VCMPPSZrmi_alt
    0U,	// VCMPPSZrri
    8456U,	// VCMPPSZrri_alt
    3U,	// VCMPPSZrrib
    80U,	// VCMPPSrmi
    2384U,	// VCMPPSrmi_alt
    8U,	// VCMPPSrri
    8456U,	// VCMPPSrri_alt
    48U,	// VCMPSDZrm
    2352U,	// VCMPSDZrmi_alt
    8U,	// VCMPSDZrr
    8456U,	// VCMPSDZrri_alt
    48U,	// VCMPSDrm
    2352U,	// VCMPSDrm_alt
    8U,	// VCMPSDrr
    8456U,	// VCMPSDrr_alt
    56U,	// VCMPSSZrm
    2360U,	// VCMPSSZrmi_alt
    8U,	// VCMPSSZrr
    8456U,	// VCMPSSZrri_alt
    56U,	// VCMPSSrm
    2360U,	// VCMPSSrm_alt
    8U,	// VCMPSSrr
    8456U,	// VCMPSSrr_alt
    0U,	// VCOMISDZrm
    0U,	// VCOMISDZrr
    0U,	// VCOMISDrm
    0U,	// VCOMISDrr
    0U,	// VCOMISSZrm
    0U,	// VCOMISSZrr
    0U,	// VCOMISSrm
    0U,	// VCOMISSrr
    0U,	// VCVTDQ2PDYrm
    0U,	// VCVTDQ2PDYrr
    0U,	// VCVTDQ2PDZrm
    0U,	// VCVTDQ2PDZrr
    0U,	// VCVTDQ2PDrm
    0U,	// VCVTDQ2PDrr
    0U,	// VCVTDQ2PSYrm
    0U,	// VCVTDQ2PSYrr
    0U,	// VCVTDQ2PSZrm
    0U,	// VCVTDQ2PSZrr
    112U,	// VCVTDQ2PSZrrb
    0U,	// VCVTDQ2PSrm
    0U,	// VCVTDQ2PSrr
    0U,	// VCVTPD2DQXrm
    0U,	// VCVTPD2DQYrm
    0U,	// VCVTPD2DQYrr
    0U,	// VCVTPD2DQZrm
    0U,	// VCVTPD2DQZrr
    112U,	// VCVTPD2DQZrrb
    0U,	// VCVTPD2DQrr
    0U,	// VCVTPD2PSXrm
    0U,	// VCVTPD2PSYrm
    0U,	// VCVTPD2PSYrr
    0U,	// VCVTPD2PSZrm
    0U,	// VCVTPD2PSZrr
    112U,	// VCVTPD2PSZrrb
    0U,	// VCVTPD2PSrr
    0U,	// VCVTPD2UDQZrm
    0U,	// VCVTPD2UDQZrr
    112U,	// VCVTPD2UDQZrrb
    0U,	// VCVTPH2PSYrm
    0U,	// VCVTPH2PSYrr
    0U,	// VCVTPH2PSZrm
    0U,	// VCVTPH2PSZrr
    0U,	// VCVTPH2PSrm
    0U,	// VCVTPH2PSrr
    0U,	// VCVTPS2DQYrm
    0U,	// VCVTPS2DQYrr
    0U,	// VCVTPS2DQZrm
    0U,	// VCVTPS2DQZrr
    112U,	// VCVTPS2DQZrrb
    0U,	// VCVTPS2DQrm
    0U,	// VCVTPS2DQrr
    0U,	// VCVTPS2PDYrm
    0U,	// VCVTPS2PDYrr
    0U,	// VCVTPS2PDZrm
    0U,	// VCVTPS2PDZrr
    0U,	// VCVTPS2PDrm
    0U,	// VCVTPS2PDrr
    0U,	// VCVTPS2PHYmr
    8U,	// VCVTPS2PHYrr
    0U,	// VCVTPS2PHZmr
    8U,	// VCVTPS2PHZrr
    0U,	// VCVTPS2PHmr
    8U,	// VCVTPS2PHrr
    0U,	// VCVTPS2UDQZrm
    0U,	// VCVTPS2UDQZrr
    112U,	// VCVTPS2UDQZrrb
    0U,	// VCVTSD2SI64Zrm
    0U,	// VCVTSD2SI64Zrr
    0U,	// VCVTSD2SI64rm
    0U,	// VCVTSD2SI64rr
    0U,	// VCVTSD2SIZrm
    0U,	// VCVTSD2SIZrr
    0U,	// VCVTSD2SIrm
    0U,	// VCVTSD2SIrr
    48U,	// VCVTSD2SSZrm
    8U,	// VCVTSD2SSZrr
    48U,	// VCVTSD2SSrm
    8U,	// VCVTSD2SSrr
    0U,	// VCVTSD2USI64Zrm
    0U,	// VCVTSD2USI64Zrr
    0U,	// VCVTSD2USIZrm
    0U,	// VCVTSD2USIZrr
    24U,	// VCVTSI2SD64rm
    8U,	// VCVTSI2SD64rr
    16U,	// VCVTSI2SDZrm
    8U,	// VCVTSI2SDZrr
    16U,	// VCVTSI2SDrm
    8U,	// VCVTSI2SDrr
    24U,	// VCVTSI2SS64rm
    8U,	// VCVTSI2SS64rr
    16U,	// VCVTSI2SSZrm
    8U,	// VCVTSI2SSZrr
    16U,	// VCVTSI2SSrm
    8U,	// VCVTSI2SSrr
    24U,	// VCVTSI642SDZrm
    8U,	// VCVTSI642SDZrr
    24U,	// VCVTSI642SSZrm
    8U,	// VCVTSI642SSZrr
    56U,	// VCVTSS2SDZrm
    8U,	// VCVTSS2SDZrr
    56U,	// VCVTSS2SDrm
    8U,	// VCVTSS2SDrr
    0U,	// VCVTSS2SI64Zrm
    0U,	// VCVTSS2SI64Zrr
    0U,	// VCVTSS2SI64rm
    0U,	// VCVTSS2SI64rr
    0U,	// VCVTSS2SIZrm
    0U,	// VCVTSS2SIZrr
    0U,	// VCVTSS2SIrm
    0U,	// VCVTSS2SIrr
    0U,	// VCVTSS2USI64Zrm
    0U,	// VCVTSS2USI64Zrr
    0U,	// VCVTSS2USIZrm
    0U,	// VCVTSS2USIZrr
    0U,	// VCVTTPD2DQXrm
    0U,	// VCVTTPD2DQYrm
    0U,	// VCVTTPD2DQYrr
    0U,	// VCVTTPD2DQZrm
    0U,	// VCVTTPD2DQZrr
    0U,	// VCVTTPD2DQrr
    0U,	// VCVTTPD2UDQZrm
    0U,	// VCVTTPD2UDQZrr
    0U,	// VCVTTPS2DQYrm
    0U,	// VCVTTPS2DQYrr
    0U,	// VCVTTPS2DQZrm
    0U,	// VCVTTPS2DQZrr
    0U,	// VCVTTPS2DQrm
    0U,	// VCVTTPS2DQrr
    0U,	// VCVTTPS2UDQZrm
    0U,	// VCVTTPS2UDQZrr
    0U,	// VCVTTSD2SI64Zrm
    0U,	// VCVTTSD2SI64Zrr
    0U,	// VCVTTSD2SI64rm
    0U,	// VCVTTSD2SI64rr
    0U,	// VCVTTSD2SIZrm
    0U,	// VCVTTSD2SIZrr
    0U,	// VCVTTSD2SIrm
    0U,	// VCVTTSD2SIrr
    0U,	// VCVTTSD2USI64Zrm
    0U,	// VCVTTSD2USI64Zrr
    0U,	// VCVTTSD2USIZrm
    0U,	// VCVTTSD2USIZrr
    0U,	// VCVTTSS2SI64Zrm
    0U,	// VCVTTSS2SI64Zrr
    0U,	// VCVTTSS2SI64rm
    0U,	// VCVTTSS2SI64rr
    0U,	// VCVTTSS2SIZrm
    0U,	// VCVTTSS2SIZrr
    0U,	// VCVTTSS2SIrm
    0U,	// VCVTTSS2SIrr
    0U,	// VCVTTSS2USI64Zrm
    0U,	// VCVTTSS2USI64Zrr
    0U,	// VCVTTSS2USIZrm
    0U,	// VCVTTSS2USIZrr
    0U,	// VCVTUDQ2PDZrm
    0U,	// VCVTUDQ2PDZrr
    0U,	// VCVTUDQ2PSZrm
    0U,	// VCVTUDQ2PSZrr
    112U,	// VCVTUDQ2PSZrrb
    16U,	// VCVTUSI2SDZrm
    8U,	// VCVTUSI2SDZrr
    16U,	// VCVTUSI2SSZrm
    8U,	// VCVTUSI2SSZrr
    24U,	// VCVTUSI642SDZrm
    8U,	// VCVTUSI642SDZrr
    24U,	// VCVTUSI642SSZrm
    8U,	// VCVTUSI642SSZrr
    64U,	// VDIVPDYrm
    8U,	// VDIVPDYrr
    72U,	// VDIVPDZrm
    560U,	// VDIVPDZrmb
    69898U,	// VDIVPDZrmbk
    69898U,	// VDIVPDZrmbkz
    6410U,	// VDIVPDZrmk
    6410U,	// VDIVPDZrmkz
    8U,	// VDIVPDZrr
    8458U,	// VDIVPDZrrk
    8458U,	// VDIVPDZrrkz
    80U,	// VDIVPDrm
    8U,	// VDIVPDrr
    64U,	// VDIVPSYrm
    8U,	// VDIVPSYrr
    72U,	// VDIVPSZrm
    824U,	// VDIVPSZrmb
    108810U,	// VDIVPSZrmbk
    108810U,	// VDIVPSZrmbkz
    6410U,	// VDIVPSZrmk
    6410U,	// VDIVPSZrmkz
    8U,	// VDIVPSZrr
    8458U,	// VDIVPSZrrk
    8458U,	// VDIVPSZrrkz
    80U,	// VDIVPSrm
    8U,	// VDIVPSrr
    48U,	// VDIVSDZrm
    8U,	// VDIVSDZrr
    48U,	// VDIVSDrm
    48U,	// VDIVSDrm_Int
    8U,	// VDIVSDrr
    8U,	// VDIVSDrr_Int
    56U,	// VDIVSSZrm
    8U,	// VDIVSSZrr
    56U,	// VDIVSSrm
    56U,	// VDIVSSrm_Int
    8U,	// VDIVSSrr
    8U,	// VDIVSSrr_Int
    2384U,	// VDPPDrmi
    8456U,	// VDPPDrri
    2408U,	// VDPPSYrmi
    8456U,	// VDPPSYrri
    2384U,	// VDPPSrmi
    8456U,	// VDPPSrri
    0U,	// VERRm
    0U,	// VERRr
    0U,	// VERWm
    0U,	// VERWr
    0U,	// VEXTRACTF128mr
    8U,	// VEXTRACTF128rr
    0U,	// VEXTRACTF32x4mr
    8U,	// VEXTRACTF32x4rr
    0U,	// VEXTRACTF64x4mr
    8U,	// VEXTRACTF64x4rr
    0U,	// VEXTRACTI128mr
    8U,	// VEXTRACTI128rr
    0U,	// VEXTRACTI32x4mr
    8U,	// VEXTRACTI32x4rr
    0U,	// VEXTRACTI64x4mr
    8U,	// VEXTRACTI64x4rr
    0U,	// VEXTRACTPSmr
    8U,	// VEXTRACTPSrr
    0U,	// VEXTRACTPSzmr
    8U,	// VEXTRACTPSzrr
    120U,	// VFMADD132PDZm
    640U,	// VFMADD132PDZmb
    120U,	// VFMADD132PSZm
    904U,	// VFMADD132PSZmb
    120U,	// VFMADD213PDZm
    640U,	// VFMADD213PDZmb
    40U,	// VFMADD213PDZr
    298U,	// VFMADD213PDZrk
    298U,	// VFMADD213PDZrkz
    120U,	// VFMADD213PSZm
    904U,	// VFMADD213PSZmb
    40U,	// VFMADD213PSZr
    298U,	// VFMADD213PSZrk
    298U,	// VFMADD213PSZrkz
    2384U,	// VFMADDPD4mr
    2368U,	// VFMADDPD4mrY
    12552U,	// VFMADDPD4rm
    14600U,	// VFMADDPD4rmY
    8456U,	// VFMADDPD4rr
    8456U,	// VFMADDPD4rrY
    8456U,	// VFMADDPD4rrY_REV
    8456U,	// VFMADDPD4rr_REV
    144U,	// VFMADDPDr132m
    152U,	// VFMADDPDr132mY
    40U,	// VFMADDPDr132r
    40U,	// VFMADDPDr132rY
    144U,	// VFMADDPDr213m
    152U,	// VFMADDPDr213mY
    40U,	// VFMADDPDr213r
    40U,	// VFMADDPDr213rY
    144U,	// VFMADDPDr231m
    152U,	// VFMADDPDr231mY
    40U,	// VFMADDPDr231r
    40U,	// VFMADDPDr231rY
    2384U,	// VFMADDPS4mr
    2368U,	// VFMADDPS4mrY
    12552U,	// VFMADDPS4rm
    14600U,	// VFMADDPS4rmY
    8456U,	// VFMADDPS4rr
    8456U,	// VFMADDPS4rrY
    8456U,	// VFMADDPS4rrY_REV
    8456U,	// VFMADDPS4rr_REV
    144U,	// VFMADDPSr132m
    152U,	// VFMADDPSr132mY
    40U,	// VFMADDPSr132r
    40U,	// VFMADDPSr132rY
    144U,	// VFMADDPSr213m
    152U,	// VFMADDPSr213mY
    40U,	// VFMADDPSr213r
    40U,	// VFMADDPSr213rY
    144U,	// VFMADDPSr231m
    152U,	// VFMADDPSr231mY
    40U,	// VFMADDPSr231r
    40U,	// VFMADDPSr231rY
    2352U,	// VFMADDSD4mr
    2352U,	// VFMADDSD4mr_Int
    4360U,	// VFMADDSD4rm
    4360U,	// VFMADDSD4rm_Int
    8456U,	// VFMADDSD4rr
    8456U,	// VFMADDSD4rr_Int
    8456U,	// VFMADDSD4rr_REV
    144U,	// VFMADDSDZm
    40U,	// VFMADDSDZr
    128U,	// VFMADDSDr132m
    40U,	// VFMADDSDr132r
    128U,	// VFMADDSDr213m
    40U,	// VFMADDSDr213r
    128U,	// VFMADDSDr231m
    40U,	// VFMADDSDr231r
    2360U,	// VFMADDSS4mr
    2360U,	// VFMADDSS4mr_Int
    10504U,	// VFMADDSS4rm
    10504U,	// VFMADDSS4rm_Int
    8456U,	// VFMADDSS4rr
    8456U,	// VFMADDSS4rr_Int
    8456U,	// VFMADDSS4rr_REV
    144U,	// VFMADDSSZm
    40U,	// VFMADDSSZr
    136U,	// VFMADDSSr132m
    40U,	// VFMADDSSr132r
    136U,	// VFMADDSSr213m
    40U,	// VFMADDSSr213r
    136U,	// VFMADDSSr231m
    40U,	// VFMADDSSr231r
    120U,	// VFMADDSUB132PDZm
    640U,	// VFMADDSUB132PDZmb
    120U,	// VFMADDSUB132PSZm
    904U,	// VFMADDSUB132PSZmb
    120U,	// VFMADDSUB213PDZm
    640U,	// VFMADDSUB213PDZmb
    40U,	// VFMADDSUB213PDZr
    298U,	// VFMADDSUB213PDZrk
    298U,	// VFMADDSUB213PDZrkz
    120U,	// VFMADDSUB213PSZm
    904U,	// VFMADDSUB213PSZmb
    40U,	// VFMADDSUB213PSZr
    298U,	// VFMADDSUB213PSZrk
    298U,	// VFMADDSUB213PSZrkz
    2384U,	// VFMADDSUBPD4mr
    2368U,	// VFMADDSUBPD4mrY
    12552U,	// VFMADDSUBPD4rm
    14600U,	// VFMADDSUBPD4rmY
    8456U,	// VFMADDSUBPD4rr
    8456U,	// VFMADDSUBPD4rrY
    8456U,	// VFMADDSUBPD4rrY_REV
    8456U,	// VFMADDSUBPD4rr_REV
    144U,	// VFMADDSUBPDr132m
    152U,	// VFMADDSUBPDr132mY
    40U,	// VFMADDSUBPDr132r
    40U,	// VFMADDSUBPDr132rY
    144U,	// VFMADDSUBPDr213m
    152U,	// VFMADDSUBPDr213mY
    40U,	// VFMADDSUBPDr213r
    40U,	// VFMADDSUBPDr213rY
    144U,	// VFMADDSUBPDr231m
    152U,	// VFMADDSUBPDr231mY
    40U,	// VFMADDSUBPDr231r
    40U,	// VFMADDSUBPDr231rY
    2384U,	// VFMADDSUBPS4mr
    2368U,	// VFMADDSUBPS4mrY
    12552U,	// VFMADDSUBPS4rm
    14600U,	// VFMADDSUBPS4rmY
    8456U,	// VFMADDSUBPS4rr
    8456U,	// VFMADDSUBPS4rrY
    8456U,	// VFMADDSUBPS4rrY_REV
    8456U,	// VFMADDSUBPS4rr_REV
    144U,	// VFMADDSUBPSr132m
    152U,	// VFMADDSUBPSr132mY
    40U,	// VFMADDSUBPSr132r
    40U,	// VFMADDSUBPSr132rY
    144U,	// VFMADDSUBPSr213m
    152U,	// VFMADDSUBPSr213mY
    40U,	// VFMADDSUBPSr213r
    40U,	// VFMADDSUBPSr213rY
    144U,	// VFMADDSUBPSr231m
    152U,	// VFMADDSUBPSr231mY
    40U,	// VFMADDSUBPSr231r
    40U,	// VFMADDSUBPSr231rY
    120U,	// VFMSUB132PDZm
    640U,	// VFMSUB132PDZmb
    120U,	// VFMSUB132PSZm
    904U,	// VFMSUB132PSZmb
    120U,	// VFMSUB213PDZm
    640U,	// VFMSUB213PDZmb
    40U,	// VFMSUB213PDZr
    298U,	// VFMSUB213PDZrk
    298U,	// VFMSUB213PDZrkz
    120U,	// VFMSUB213PSZm
    904U,	// VFMSUB213PSZmb
    40U,	// VFMSUB213PSZr
    298U,	// VFMSUB213PSZrk
    298U,	// VFMSUB213PSZrkz
    120U,	// VFMSUBADD132PDZm
    640U,	// VFMSUBADD132PDZmb
    120U,	// VFMSUBADD132PSZm
    904U,	// VFMSUBADD132PSZmb
    120U,	// VFMSUBADD213PDZm
    640U,	// VFMSUBADD213PDZmb
    40U,	// VFMSUBADD213PDZr
    298U,	// VFMSUBADD213PDZrk
    298U,	// VFMSUBADD213PDZrkz
    120U,	// VFMSUBADD213PSZm
    904U,	// VFMSUBADD213PSZmb
    40U,	// VFMSUBADD213PSZr
    298U,	// VFMSUBADD213PSZrk
    298U,	// VFMSUBADD213PSZrkz
    2384U,	// VFMSUBADDPD4mr
    2368U,	// VFMSUBADDPD4mrY
    12552U,	// VFMSUBADDPD4rm
    14600U,	// VFMSUBADDPD4rmY
    8456U,	// VFMSUBADDPD4rr
    8456U,	// VFMSUBADDPD4rrY
    8456U,	// VFMSUBADDPD4rrY_REV
    8456U,	// VFMSUBADDPD4rr_REV
    144U,	// VFMSUBADDPDr132m
    152U,	// VFMSUBADDPDr132mY
    40U,	// VFMSUBADDPDr132r
    40U,	// VFMSUBADDPDr132rY
    144U,	// VFMSUBADDPDr213m
    152U,	// VFMSUBADDPDr213mY
    40U,	// VFMSUBADDPDr213r
    40U,	// VFMSUBADDPDr213rY
    144U,	// VFMSUBADDPDr231m
    152U,	// VFMSUBADDPDr231mY
    40U,	// VFMSUBADDPDr231r
    40U,	// VFMSUBADDPDr231rY
    2384U,	// VFMSUBADDPS4mr
    2368U,	// VFMSUBADDPS4mrY
    12552U,	// VFMSUBADDPS4rm
    14600U,	// VFMSUBADDPS4rmY
    8456U,	// VFMSUBADDPS4rr
    8456U,	// VFMSUBADDPS4rrY
    8456U,	// VFMSUBADDPS4rrY_REV
    8456U,	// VFMSUBADDPS4rr_REV
    144U,	// VFMSUBADDPSr132m
    152U,	// VFMSUBADDPSr132mY
    40U,	// VFMSUBADDPSr132r
    40U,	// VFMSUBADDPSr132rY
    144U,	// VFMSUBADDPSr213m
    152U,	// VFMSUBADDPSr213mY
    40U,	// VFMSUBADDPSr213r
    40U,	// VFMSUBADDPSr213rY
    144U,	// VFMSUBADDPSr231m
    152U,	// VFMSUBADDPSr231mY
    40U,	// VFMSUBADDPSr231r
    40U,	// VFMSUBADDPSr231rY
    2384U,	// VFMSUBPD4mr
    2368U,	// VFMSUBPD4mrY
    12552U,	// VFMSUBPD4rm
    14600U,	// VFMSUBPD4rmY
    8456U,	// VFMSUBPD4rr
    8456U,	// VFMSUBPD4rrY
    8456U,	// VFMSUBPD4rrY_REV
    8456U,	// VFMSUBPD4rr_REV
    144U,	// VFMSUBPDr132m
    152U,	// VFMSUBPDr132mY
    40U,	// VFMSUBPDr132r
    40U,	// VFMSUBPDr132rY
    144U,	// VFMSUBPDr213m
    152U,	// VFMSUBPDr213mY
    40U,	// VFMSUBPDr213r
    40U,	// VFMSUBPDr213rY
    144U,	// VFMSUBPDr231m
    152U,	// VFMSUBPDr231mY
    40U,	// VFMSUBPDr231r
    40U,	// VFMSUBPDr231rY
    2384U,	// VFMSUBPS4mr
    2368U,	// VFMSUBPS4mrY
    12552U,	// VFMSUBPS4rm
    14600U,	// VFMSUBPS4rmY
    8456U,	// VFMSUBPS4rr
    8456U,	// VFMSUBPS4rrY
    8456U,	// VFMSUBPS4rrY_REV
    8456U,	// VFMSUBPS4rr_REV
    144U,	// VFMSUBPSr132m
    152U,	// VFMSUBPSr132mY
    40U,	// VFMSUBPSr132r
    40U,	// VFMSUBPSr132rY
    144U,	// VFMSUBPSr213m
    152U,	// VFMSUBPSr213mY
    40U,	// VFMSUBPSr213r
    40U,	// VFMSUBPSr213rY
    144U,	// VFMSUBPSr231m
    152U,	// VFMSUBPSr231mY
    40U,	// VFMSUBPSr231r
    40U,	// VFMSUBPSr231rY
    2352U,	// VFMSUBSD4mr
    2352U,	// VFMSUBSD4mr_Int
    4360U,	// VFMSUBSD4rm
    4360U,	// VFMSUBSD4rm_Int
    8456U,	// VFMSUBSD4rr
    8456U,	// VFMSUBSD4rr_Int
    8456U,	// VFMSUBSD4rr_REV
    144U,	// VFMSUBSDZm
    40U,	// VFMSUBSDZr
    128U,	// VFMSUBSDr132m
    40U,	// VFMSUBSDr132r
    128U,	// VFMSUBSDr213m
    40U,	// VFMSUBSDr213r
    128U,	// VFMSUBSDr231m
    40U,	// VFMSUBSDr231r
    2360U,	// VFMSUBSS4mr
    2360U,	// VFMSUBSS4mr_Int
    10504U,	// VFMSUBSS4rm
    10504U,	// VFMSUBSS4rm_Int
    8456U,	// VFMSUBSS4rr
    8456U,	// VFMSUBSS4rr_Int
    8456U,	// VFMSUBSS4rr_REV
    144U,	// VFMSUBSSZm
    40U,	// VFMSUBSSZr
    136U,	// VFMSUBSSr132m
    40U,	// VFMSUBSSr132r
    136U,	// VFMSUBSSr213m
    40U,	// VFMSUBSSr213r
    136U,	// VFMSUBSSr231m
    40U,	// VFMSUBSSr231r
    120U,	// VFNMADD132PDZm
    640U,	// VFNMADD132PDZmb
    120U,	// VFNMADD132PSZm
    904U,	// VFNMADD132PSZmb
    120U,	// VFNMADD213PDZm
    640U,	// VFNMADD213PDZmb
    40U,	// VFNMADD213PDZr
    298U,	// VFNMADD213PDZrk
    298U,	// VFNMADD213PDZrkz
    120U,	// VFNMADD213PSZm
    904U,	// VFNMADD213PSZmb
    40U,	// VFNMADD213PSZr
    298U,	// VFNMADD213PSZrk
    298U,	// VFNMADD213PSZrkz
    2384U,	// VFNMADDPD4mr
    2368U,	// VFNMADDPD4mrY
    12552U,	// VFNMADDPD4rm
    14600U,	// VFNMADDPD4rmY
    8456U,	// VFNMADDPD4rr
    8456U,	// VFNMADDPD4rrY
    8456U,	// VFNMADDPD4rrY_REV
    8456U,	// VFNMADDPD4rr_REV
    144U,	// VFNMADDPDr132m
    152U,	// VFNMADDPDr132mY
    40U,	// VFNMADDPDr132r
    40U,	// VFNMADDPDr132rY
    144U,	// VFNMADDPDr213m
    152U,	// VFNMADDPDr213mY
    40U,	// VFNMADDPDr213r
    40U,	// VFNMADDPDr213rY
    144U,	// VFNMADDPDr231m
    152U,	// VFNMADDPDr231mY
    40U,	// VFNMADDPDr231r
    40U,	// VFNMADDPDr231rY
    2384U,	// VFNMADDPS4mr
    2368U,	// VFNMADDPS4mrY
    12552U,	// VFNMADDPS4rm
    14600U,	// VFNMADDPS4rmY
    8456U,	// VFNMADDPS4rr
    8456U,	// VFNMADDPS4rrY
    8456U,	// VFNMADDPS4rrY_REV
    8456U,	// VFNMADDPS4rr_REV
    144U,	// VFNMADDPSr132m
    152U,	// VFNMADDPSr132mY
    40U,	// VFNMADDPSr132r
    40U,	// VFNMADDPSr132rY
    144U,	// VFNMADDPSr213m
    152U,	// VFNMADDPSr213mY
    40U,	// VFNMADDPSr213r
    40U,	// VFNMADDPSr213rY
    144U,	// VFNMADDPSr231m
    152U,	// VFNMADDPSr231mY
    40U,	// VFNMADDPSr231r
    40U,	// VFNMADDPSr231rY
    2352U,	// VFNMADDSD4mr
    2352U,	// VFNMADDSD4mr_Int
    4360U,	// VFNMADDSD4rm
    4360U,	// VFNMADDSD4rm_Int
    8456U,	// VFNMADDSD4rr
    8456U,	// VFNMADDSD4rr_Int
    8456U,	// VFNMADDSD4rr_REV
    144U,	// VFNMADDSDZm
    40U,	// VFNMADDSDZr
    128U,	// VFNMADDSDr132m
    40U,	// VFNMADDSDr132r
    128U,	// VFNMADDSDr213m
    40U,	// VFNMADDSDr213r
    128U,	// VFNMADDSDr231m
    40U,	// VFNMADDSDr231r
    2360U,	// VFNMADDSS4mr
    2360U,	// VFNMADDSS4mr_Int
    10504U,	// VFNMADDSS4rm
    10504U,	// VFNMADDSS4rm_Int
    8456U,	// VFNMADDSS4rr
    8456U,	// VFNMADDSS4rr_Int
    8456U,	// VFNMADDSS4rr_REV
    144U,	// VFNMADDSSZm
    40U,	// VFNMADDSSZr
    136U,	// VFNMADDSSr132m
    40U,	// VFNMADDSSr132r
    136U,	// VFNMADDSSr213m
    40U,	// VFNMADDSSr213r
    136U,	// VFNMADDSSr231m
    40U,	// VFNMADDSSr231r
    120U,	// VFNMSUB132PDZm
    640U,	// VFNMSUB132PDZmb
    120U,	// VFNMSUB132PSZm
    904U,	// VFNMSUB132PSZmb
    120U,	// VFNMSUB213PDZm
    640U,	// VFNMSUB213PDZmb
    40U,	// VFNMSUB213PDZr
    298U,	// VFNMSUB213PDZrk
    298U,	// VFNMSUB213PDZrkz
    120U,	// VFNMSUB213PSZm
    904U,	// VFNMSUB213PSZmb
    40U,	// VFNMSUB213PSZr
    298U,	// VFNMSUB213PSZrk
    298U,	// VFNMSUB213PSZrkz
    2384U,	// VFNMSUBPD4mr
    2368U,	// VFNMSUBPD4mrY
    12552U,	// VFNMSUBPD4rm
    14600U,	// VFNMSUBPD4rmY
    8456U,	// VFNMSUBPD4rr
    8456U,	// VFNMSUBPD4rrY
    8456U,	// VFNMSUBPD4rrY_REV
    8456U,	// VFNMSUBPD4rr_REV
    144U,	// VFNMSUBPDr132m
    152U,	// VFNMSUBPDr132mY
    40U,	// VFNMSUBPDr132r
    40U,	// VFNMSUBPDr132rY
    144U,	// VFNMSUBPDr213m
    152U,	// VFNMSUBPDr213mY
    40U,	// VFNMSUBPDr213r
    40U,	// VFNMSUBPDr213rY
    144U,	// VFNMSUBPDr231m
    152U,	// VFNMSUBPDr231mY
    40U,	// VFNMSUBPDr231r
    40U,	// VFNMSUBPDr231rY
    2384U,	// VFNMSUBPS4mr
    2368U,	// VFNMSUBPS4mrY
    12552U,	// VFNMSUBPS4rm
    14600U,	// VFNMSUBPS4rmY
    8456U,	// VFNMSUBPS4rr
    8456U,	// VFNMSUBPS4rrY
    8456U,	// VFNMSUBPS4rrY_REV
    8456U,	// VFNMSUBPS4rr_REV
    144U,	// VFNMSUBPSr132m
    152U,	// VFNMSUBPSr132mY
    40U,	// VFNMSUBPSr132r
    40U,	// VFNMSUBPSr132rY
    144U,	// VFNMSUBPSr213m
    152U,	// VFNMSUBPSr213mY
    40U,	// VFNMSUBPSr213r
    40U,	// VFNMSUBPSr213rY
    144U,	// VFNMSUBPSr231m
    152U,	// VFNMSUBPSr231mY
    40U,	// VFNMSUBPSr231r
    40U,	// VFNMSUBPSr231rY
    2352U,	// VFNMSUBSD4mr
    2352U,	// VFNMSUBSD4mr_Int
    4360U,	// VFNMSUBSD4rm
    4360U,	// VFNMSUBSD4rm_Int
    8456U,	// VFNMSUBSD4rr
    8456U,	// VFNMSUBSD4rr_Int
    8456U,	// VFNMSUBSD4rr_REV
    144U,	// VFNMSUBSDZm
    40U,	// VFNMSUBSDZr
    128U,	// VFNMSUBSDr132m
    40U,	// VFNMSUBSDr132r
    128U,	// VFNMSUBSDr213m
    40U,	// VFNMSUBSDr213r
    128U,	// VFNMSUBSDr231m
    40U,	// VFNMSUBSDr231r
    2360U,	// VFNMSUBSS4mr
    2360U,	// VFNMSUBSS4mr_Int
    10504U,	// VFNMSUBSS4rm
    10504U,	// VFNMSUBSS4rm_Int
    8456U,	// VFNMSUBSS4rr
    8456U,	// VFNMSUBSS4rr_Int
    8456U,	// VFNMSUBSS4rr_REV
    144U,	// VFNMSUBSSZm
    40U,	// VFNMSUBSSZr
    136U,	// VFNMSUBSSr132m
    40U,	// VFNMSUBSSr132r
    136U,	// VFNMSUBSSr213m
    40U,	// VFNMSUBSSr213r
    136U,	// VFNMSUBSSr231m
    40U,	// VFNMSUBSSr231r
    0U,	// VFRCZPDrm
    0U,	// VFRCZPDrmY
    0U,	// VFRCZPDrr
    0U,	// VFRCZPDrrY
    0U,	// VFRCZPSrm
    0U,	// VFRCZPSrmY
    0U,	// VFRCZPSrr
    0U,	// VFRCZPSrrY
    0U,	// VFRCZSDrm
    0U,	// VFRCZSDrr
    0U,	// VFRCZSSrm
    0U,	// VFRCZSSrr
    80U,	// VFsANDNPDrm
    8U,	// VFsANDNPDrr
    80U,	// VFsANDNPSrm
    8U,	// VFsANDNPSrr
    80U,	// VFsANDPDrm
    8U,	// VFsANDPDrr
    80U,	// VFsANDPSrm
    8U,	// VFsANDPSrr
    80U,	// VFsORPDrm
    8U,	// VFsORPDrr
    80U,	// VFsORPSrm
    8U,	// VFsORPSrr
    80U,	// VFsXORPDrm
    8U,	// VFsXORPDrr
    80U,	// VFsXORPSrm
    8U,	// VFsXORPSrr
    0U,	// VGATHERDPDYrm
    3U,	// VGATHERDPDZrm
    0U,	// VGATHERDPDrm
    0U,	// VGATHERDPSYrm
    4U,	// VGATHERDPSZrm
    0U,	// VGATHERDPSrm
    0U,	// VGATHERPF0DPDm
    0U,	// VGATHERPF0DPSm
    0U,	// VGATHERPF0QPDm
    0U,	// VGATHERPF0QPSm
    0U,	// VGATHERPF1DPDm
    0U,	// VGATHERPF1DPSm
    0U,	// VGATHERPF1QPDm
    0U,	// VGATHERPF1QPSm
    0U,	// VGATHERQPDYrm
    3U,	// VGATHERQPDZrm
    0U,	// VGATHERQPDrm
    0U,	// VGATHERQPSYrm
    3U,	// VGATHERQPSZrm
    0U,	// VGATHERQPSrm
    64U,	// VHADDPDYrm
    8U,	// VHADDPDYrr
    80U,	// VHADDPDrm
    8U,	// VHADDPDrr
    64U,	// VHADDPSYrm
    8U,	// VHADDPSYrr
    80U,	// VHADDPSrm
    8U,	// VHADDPSrr
    64U,	// VHSUBPDYrm
    8U,	// VHSUBPDYrr
    80U,	// VHSUBPDrm
    8U,	// VHSUBPDrr
    64U,	// VHSUBPSYrm
    8U,	// VHSUBPSYrr
    80U,	// VHSUBPSrm
    8U,	// VHSUBPSrr
    2384U,	// VINSERTF128rm
    8456U,	// VINSERTF128rr
    2384U,	// VINSERTF32x4rm
    8456U,	// VINSERTF32x4rr
    2408U,	// VINSERTF64x4rm
    8456U,	// VINSERTF64x4rr
    2392U,	// VINSERTI128rm
    8456U,	// VINSERTI128rr
    2392U,	// VINSERTI32x4rm
    8456U,	// VINSERTI32x4rr
    2408U,	// VINSERTI64x4rm
    8456U,	// VINSERTI64x4rr
    2360U,	// VINSERTPSrm
    8456U,	// VINSERTPSrr
    2360U,	// VINSERTPSzrm
    8456U,	// VINSERTPSzrr
    0U,	// VLDDQUYrm
    0U,	// VLDDQUrm
    0U,	// VLDMXCSR
    0U,	// VMASKMOVDQU
    0U,	// VMASKMOVDQU64
    0U,	// VMASKMOVPDYmr
    64U,	// VMASKMOVPDYrm
    0U,	// VMASKMOVPDmr
    80U,	// VMASKMOVPDrm
    0U,	// VMASKMOVPSYmr
    64U,	// VMASKMOVPSYrm
    0U,	// VMASKMOVPSmr
    80U,	// VMASKMOVPSrm
    64U,	// VMAXCPDYrm
    8U,	// VMAXCPDYrr
    80U,	// VMAXCPDrm
    8U,	// VMAXCPDrr
    64U,	// VMAXCPSYrm
    8U,	// VMAXCPSYrr
    80U,	// VMAXCPSrm
    8U,	// VMAXCPSrr
    48U,	// VMAXCSDrm
    8U,	// VMAXCSDrr
    56U,	// VMAXCSSrm
    8U,	// VMAXCSSrr
    64U,	// VMAXPDYrm
    8U,	// VMAXPDYrr
    72U,	// VMAXPDZrm
    560U,	// VMAXPDZrmb
    69898U,	// VMAXPDZrmbk
    69898U,	// VMAXPDZrmbkz
    6410U,	// VMAXPDZrmk
    6410U,	// VMAXPDZrmkz
    8U,	// VMAXPDZrr
    8458U,	// VMAXPDZrrk
    8458U,	// VMAXPDZrrkz
    80U,	// VMAXPDrm
    8U,	// VMAXPDrr
    64U,	// VMAXPSYrm
    8U,	// VMAXPSYrr
    72U,	// VMAXPSZrm
    824U,	// VMAXPSZrmb
    108810U,	// VMAXPSZrmbk
    108810U,	// VMAXPSZrmbkz
    6410U,	// VMAXPSZrmk
    6410U,	// VMAXPSZrmkz
    8U,	// VMAXPSZrr
    8458U,	// VMAXPSZrrk
    8458U,	// VMAXPSZrrkz
    80U,	// VMAXPSrm
    8U,	// VMAXPSrr
    48U,	// VMAXSDZrm
    8U,	// VMAXSDZrr
    48U,	// VMAXSDrm
    48U,	// VMAXSDrm_Int
    8U,	// VMAXSDrr
    8U,	// VMAXSDrr_Int
    56U,	// VMAXSSZrm
    8U,	// VMAXSSZrr
    56U,	// VMAXSSrm
    56U,	// VMAXSSrm_Int
    8U,	// VMAXSSrr
    8U,	// VMAXSSrr_Int
    0U,	// VMCALL
    0U,	// VMCLEARm
    0U,	// VMFUNC
    64U,	// VMINCPDYrm
    8U,	// VMINCPDYrr
    80U,	// VMINCPDrm
    8U,	// VMINCPDrr
    64U,	// VMINCPSYrm
    8U,	// VMINCPSYrr
    80U,	// VMINCPSrm
    8U,	// VMINCPSrr
    48U,	// VMINCSDrm
    8U,	// VMINCSDrr
    56U,	// VMINCSSrm
    8U,	// VMINCSSrr
    64U,	// VMINPDYrm
    8U,	// VMINPDYrr
    72U,	// VMINPDZrm
    560U,	// VMINPDZrmb
    69898U,	// VMINPDZrmbk
    69898U,	// VMINPDZrmbkz
    6410U,	// VMINPDZrmk
    6410U,	// VMINPDZrmkz
    8U,	// VMINPDZrr
    8458U,	// VMINPDZrrk
    8458U,	// VMINPDZrrkz
    80U,	// VMINPDrm
    8U,	// VMINPDrr
    64U,	// VMINPSYrm
    8U,	// VMINPSYrr
    72U,	// VMINPSZrm
    824U,	// VMINPSZrmb
    108810U,	// VMINPSZrmbk
    108810U,	// VMINPSZrmbkz
    6410U,	// VMINPSZrmk
    6410U,	// VMINPSZrmkz
    8U,	// VMINPSZrr
    8458U,	// VMINPSZrrk
    8458U,	// VMINPSZrrkz
    80U,	// VMINPSrm
    8U,	// VMINPSrr
    48U,	// VMINSDZrm
    8U,	// VMINSDZrr
    48U,	// VMINSDrm
    48U,	// VMINSDrm_Int
    8U,	// VMINSDrr
    8U,	// VMINSDrr_Int
    56U,	// VMINSSZrm
    8U,	// VMINSSZrr
    56U,	// VMINSSrm
    56U,	// VMINSSrm_Int
    8U,	// VMINSSrr
    8U,	// VMINSSrr_Int
    0U,	// VMLAUNCH
    0U,	// VMLOAD32
    0U,	// VMLOAD64
    0U,	// VMMCALL
    0U,	// VMOV64toPQIZrr
    0U,	// VMOV64toPQIrr
    0U,	// VMOV64toSDZrr
    0U,	// VMOV64toSDrm
    0U,	// VMOV64toSDrr
    0U,	// VMOVAPDYmr
    0U,	// VMOVAPDYrm
    0U,	// VMOVAPDYrr
    0U,	// VMOVAPDYrr_REV
    0U,	// VMOVAPDZ128mr
    2U,	// VMOVAPDZ128mrk
    0U,	// VMOVAPDZ128rm
    146U,	// VMOVAPDZ128rmk
    82U,	// VMOVAPDZ128rmkz
    0U,	// VMOVAPDZ128rr
    0U,	// VMOVAPDZ128rr_alt
    42U,	// VMOVAPDZ128rrk
    42U,	// VMOVAPDZ128rrk_alt
    10U,	// VMOVAPDZ128rrkz
    10U,	// VMOVAPDZ128rrkz_alt
    0U,	// VMOVAPDZ256mr
    2U,	// VMOVAPDZ256mrk
    0U,	// VMOVAPDZ256rm
    154U,	// VMOVAPDZ256rmk
    66U,	// VMOVAPDZ256rmkz
    0U,	// VMOVAPDZ256rr
    0U,	// VMOVAPDZ256rr_alt
    42U,	// VMOVAPDZ256rrk
    42U,	// VMOVAPDZ256rrk_alt
    10U,	// VMOVAPDZ256rrkz
    10U,	// VMOVAPDZ256rrkz_alt
    0U,	// VMOVAPDZmr
    2U,	// VMOVAPDZmrk
    0U,	// VMOVAPDZrm
    122U,	// VMOVAPDZrmk
    74U,	// VMOVAPDZrmkz
    0U,	// VMOVAPDZrr
    0U,	// VMOVAPDZrr_alt
    42U,	// VMOVAPDZrrk
    42U,	// VMOVAPDZrrk_alt
    10U,	// VMOVAPDZrrkz
    10U,	// VMOVAPDZrrkz_alt
    0U,	// VMOVAPDmr
    0U,	// VMOVAPDrm
    0U,	// VMOVAPDrr
    0U,	// VMOVAPDrr_REV
    0U,	// VMOVAPSYmr
    0U,	// VMOVAPSYrm
    0U,	// VMOVAPSYrr
    0U,	// VMOVAPSYrr_REV
    0U,	// VMOVAPSZ128mr
    2U,	// VMOVAPSZ128mrk
    0U,	// VMOVAPSZ128rm
    146U,	// VMOVAPSZ128rmk
    82U,	// VMOVAPSZ128rmkz
    0U,	// VMOVAPSZ128rr
    0U,	// VMOVAPSZ128rr_alt
    42U,	// VMOVAPSZ128rrk
    42U,	// VMOVAPSZ128rrk_alt
    10U,	// VMOVAPSZ128rrkz
    10U,	// VMOVAPSZ128rrkz_alt
    0U,	// VMOVAPSZ256mr
    2U,	// VMOVAPSZ256mrk
    0U,	// VMOVAPSZ256rm
    154U,	// VMOVAPSZ256rmk
    66U,	// VMOVAPSZ256rmkz
    0U,	// VMOVAPSZ256rr
    0U,	// VMOVAPSZ256rr_alt
    42U,	// VMOVAPSZ256rrk
    42U,	// VMOVAPSZ256rrk_alt
    10U,	// VMOVAPSZ256rrkz
    10U,	// VMOVAPSZ256rrkz_alt
    0U,	// VMOVAPSZmr
    2U,	// VMOVAPSZmrk
    0U,	// VMOVAPSZrm
    122U,	// VMOVAPSZrmk
    74U,	// VMOVAPSZrmkz
    0U,	// VMOVAPSZrr
    0U,	// VMOVAPSZrr_alt
    42U,	// VMOVAPSZrrk
    42U,	// VMOVAPSZrrk_alt
    10U,	// VMOVAPSZrrkz
    10U,	// VMOVAPSZrrkz_alt
    0U,	// VMOVAPSmr
    0U,	// VMOVAPSrm
    0U,	// VMOVAPSrr
    0U,	// VMOVAPSrr_REV
    0U,	// VMOVDDUPYrm
    0U,	// VMOVDDUPYrr
    0U,	// VMOVDDUPZrm
    0U,	// VMOVDDUPZrr
    0U,	// VMOVDDUPrm
    0U,	// VMOVDDUPrr
    0U,	// VMOVDI2PDIZrm
    0U,	// VMOVDI2PDIZrr
    0U,	// VMOVDI2PDIrm
    0U,	// VMOVDI2PDIrr
    0U,	// VMOVDI2SSZrm
    0U,	// VMOVDI2SSZrr
    0U,	// VMOVDI2SSrm
    0U,	// VMOVDI2SSrr
    0U,	// VMOVDQA32Z128mr
    2U,	// VMOVDQA32Z128mrk
    0U,	// VMOVDQA32Z128rm
    162U,	// VMOVDQA32Z128rmk
    90U,	// VMOVDQA32Z128rmkz
    0U,	// VMOVDQA32Z128rr
    0U,	// VMOVDQA32Z128rr_alt
    42U,	// VMOVDQA32Z128rrk
    42U,	// VMOVDQA32Z128rrk_alt
    10U,	// VMOVDQA32Z128rrkz
    10U,	// VMOVDQA32Z128rrkz_alt
    0U,	// VMOVDQA32Z256mr
    2U,	// VMOVDQA32Z256mrk
    0U,	// VMOVDQA32Z256rm
    170U,	// VMOVDQA32Z256rmk
    106U,	// VMOVDQA32Z256rmkz
    0U,	// VMOVDQA32Z256rr
    0U,	// VMOVDQA32Z256rr_alt
    42U,	// VMOVDQA32Z256rrk
    42U,	// VMOVDQA32Z256rrk_alt
    10U,	// VMOVDQA32Z256rrkz
    10U,	// VMOVDQA32Z256rrkz_alt
    0U,	// VMOVDQA32Zmr
    2U,	// VMOVDQA32Zmrk
    0U,	// VMOVDQA32Zrm
    178U,	// VMOVDQA32Zrmk
    98U,	// VMOVDQA32Zrmkz
    0U,	// VMOVDQA32Zrr
    0U,	// VMOVDQA32Zrr_alt
    42U,	// VMOVDQA32Zrrk
    42U,	// VMOVDQA32Zrrk_alt
    10U,	// VMOVDQA32Zrrkz
    10U,	// VMOVDQA32Zrrkz_alt
    0U,	// VMOVDQA64Z128mr
    2U,	// VMOVDQA64Z128mrk
    0U,	// VMOVDQA64Z128rm
    162U,	// VMOVDQA64Z128rmk
    90U,	// VMOVDQA64Z128rmkz
    0U,	// VMOVDQA64Z128rr
    0U,	// VMOVDQA64Z128rr_alt
    42U,	// VMOVDQA64Z128rrk
    42U,	// VMOVDQA64Z128rrk_alt
    10U,	// VMOVDQA64Z128rrkz
    10U,	// VMOVDQA64Z128rrkz_alt
    0U,	// VMOVDQA64Z256mr
    2U,	// VMOVDQA64Z256mrk
    0U,	// VMOVDQA64Z256rm
    170U,	// VMOVDQA64Z256rmk
    106U,	// VMOVDQA64Z256rmkz
    0U,	// VMOVDQA64Z256rr
    0U,	// VMOVDQA64Z256rr_alt
    42U,	// VMOVDQA64Z256rrk
    42U,	// VMOVDQA64Z256rrk_alt
    10U,	// VMOVDQA64Z256rrkz
    10U,	// VMOVDQA64Z256rrkz_alt
    0U,	// VMOVDQA64Zmr
    2U,	// VMOVDQA64Zmrk
    0U,	// VMOVDQA64Zrm
    178U,	// VMOVDQA64Zrmk
    98U,	// VMOVDQA64Zrmkz
    0U,	// VMOVDQA64Zrr
    0U,	// VMOVDQA64Zrr_alt
    42U,	// VMOVDQA64Zrrk
    42U,	// VMOVDQA64Zrrk_alt
    10U,	// VMOVDQA64Zrrkz
    10U,	// VMOVDQA64Zrrkz_alt
    0U,	// VMOVDQAYmr
    0U,	// VMOVDQAYrm
    0U,	// VMOVDQAYrr
    0U,	// VMOVDQAYrr_REV
    0U,	// VMOVDQAmr
    0U,	// VMOVDQArm
    0U,	// VMOVDQArr
    0U,	// VMOVDQArr_REV
    0U,	// VMOVDQU16Z128mr
    2U,	// VMOVDQU16Z128mrk
    0U,	// VMOVDQU16Z128rm
    162U,	// VMOVDQU16Z128rmk
    90U,	// VMOVDQU16Z128rmkz
    0U,	// VMOVDQU16Z128rr
    0U,	// VMOVDQU16Z128rr_alt
    42U,	// VMOVDQU16Z128rrk
    42U,	// VMOVDQU16Z128rrk_alt
    10U,	// VMOVDQU16Z128rrkz
    10U,	// VMOVDQU16Z128rrkz_alt
    0U,	// VMOVDQU16Z256mr
    2U,	// VMOVDQU16Z256mrk
    0U,	// VMOVDQU16Z256rm
    170U,	// VMOVDQU16Z256rmk
    106U,	// VMOVDQU16Z256rmkz
    0U,	// VMOVDQU16Z256rr
    0U,	// VMOVDQU16Z256rr_alt
    42U,	// VMOVDQU16Z256rrk
    42U,	// VMOVDQU16Z256rrk_alt
    10U,	// VMOVDQU16Z256rrkz
    10U,	// VMOVDQU16Z256rrkz_alt
    0U,	// VMOVDQU16Zmr
    2U,	// VMOVDQU16Zmrk
    0U,	// VMOVDQU16Zrm
    178U,	// VMOVDQU16Zrmk
    98U,	// VMOVDQU16Zrmkz
    0U,	// VMOVDQU16Zrr
    0U,	// VMOVDQU16Zrr_alt
    42U,	// VMOVDQU16Zrrk
    42U,	// VMOVDQU16Zrrk_alt
    10U,	// VMOVDQU16Zrrkz
    10U,	// VMOVDQU16Zrrkz_alt
    0U,	// VMOVDQU32Z128mr
    2U,	// VMOVDQU32Z128mrk
    0U,	// VMOVDQU32Z128rm
    162U,	// VMOVDQU32Z128rmk
    90U,	// VMOVDQU32Z128rmkz
    0U,	// VMOVDQU32Z128rr
    0U,	// VMOVDQU32Z128rr_alt
    42U,	// VMOVDQU32Z128rrk
    42U,	// VMOVDQU32Z128rrk_alt
    10U,	// VMOVDQU32Z128rrkz
    10U,	// VMOVDQU32Z128rrkz_alt
    0U,	// VMOVDQU32Z256mr
    2U,	// VMOVDQU32Z256mrk
    0U,	// VMOVDQU32Z256rm
    170U,	// VMOVDQU32Z256rmk
    106U,	// VMOVDQU32Z256rmkz
    0U,	// VMOVDQU32Z256rr
    0U,	// VMOVDQU32Z256rr_alt
    42U,	// VMOVDQU32Z256rrk
    42U,	// VMOVDQU32Z256rrk_alt
    10U,	// VMOVDQU32Z256rrkz
    10U,	// VMOVDQU32Z256rrkz_alt
    0U,	// VMOVDQU32Zmr
    2U,	// VMOVDQU32Zmrk
    0U,	// VMOVDQU32Zrm
    178U,	// VMOVDQU32Zrmk
    98U,	// VMOVDQU32Zrmkz
    0U,	// VMOVDQU32Zrr
    0U,	// VMOVDQU32Zrr_alt
    42U,	// VMOVDQU32Zrrk
    42U,	// VMOVDQU32Zrrk_alt
    10U,	// VMOVDQU32Zrrkz
    10U,	// VMOVDQU32Zrrkz_alt
    0U,	// VMOVDQU64Z128mr
    2U,	// VMOVDQU64Z128mrk
    0U,	// VMOVDQU64Z128rm
    162U,	// VMOVDQU64Z128rmk
    90U,	// VMOVDQU64Z128rmkz
    0U,	// VMOVDQU64Z128rr
    0U,	// VMOVDQU64Z128rr_alt
    42U,	// VMOVDQU64Z128rrk
    42U,	// VMOVDQU64Z128rrk_alt
    10U,	// VMOVDQU64Z128rrkz
    10U,	// VMOVDQU64Z128rrkz_alt
    0U,	// VMOVDQU64Z256mr
    2U,	// VMOVDQU64Z256mrk
    0U,	// VMOVDQU64Z256rm
    170U,	// VMOVDQU64Z256rmk
    106U,	// VMOVDQU64Z256rmkz
    0U,	// VMOVDQU64Z256rr
    0U,	// VMOVDQU64Z256rr_alt
    42U,	// VMOVDQU64Z256rrk
    42U,	// VMOVDQU64Z256rrk_alt
    10U,	// VMOVDQU64Z256rrkz
    10U,	// VMOVDQU64Z256rrkz_alt
    0U,	// VMOVDQU64Zmr
    2U,	// VMOVDQU64Zmrk
    0U,	// VMOVDQU64Zrm
    178U,	// VMOVDQU64Zrmk
    98U,	// VMOVDQU64Zrmkz
    0U,	// VMOVDQU64Zrr
    0U,	// VMOVDQU64Zrr_alt
    42U,	// VMOVDQU64Zrrk
    42U,	// VMOVDQU64Zrrk_alt
    10U,	// VMOVDQU64Zrrkz
    10U,	// VMOVDQU64Zrrkz_alt
    0U,	// VMOVDQU8Z128mr
    2U,	// VMOVDQU8Z128mrk
    0U,	// VMOVDQU8Z128rm
    162U,	// VMOVDQU8Z128rmk
    90U,	// VMOVDQU8Z128rmkz
    0U,	// VMOVDQU8Z128rr
    0U,	// VMOVDQU8Z128rr_alt
    42U,	// VMOVDQU8Z128rrk
    42U,	// VMOVDQU8Z128rrk_alt
    10U,	// VMOVDQU8Z128rrkz
    10U,	// VMOVDQU8Z128rrkz_alt
    0U,	// VMOVDQU8Z256mr
    2U,	// VMOVDQU8Z256mrk
    0U,	// VMOVDQU8Z256rm
    170U,	// VMOVDQU8Z256rmk
    106U,	// VMOVDQU8Z256rmkz
    0U,	// VMOVDQU8Z256rr
    0U,	// VMOVDQU8Z256rr_alt
    42U,	// VMOVDQU8Z256rrk
    42U,	// VMOVDQU8Z256rrk_alt
    10U,	// VMOVDQU8Z256rrkz
    10U,	// VMOVDQU8Z256rrkz_alt
    0U,	// VMOVDQU8Zmr
    2U,	// VMOVDQU8Zmrk
    0U,	// VMOVDQU8Zrm
    178U,	// VMOVDQU8Zrmk
    98U,	// VMOVDQU8Zrmkz
    0U,	// VMOVDQU8Zrr
    0U,	// VMOVDQU8Zrr_alt
    42U,	// VMOVDQU8Zrrk
    42U,	// VMOVDQU8Zrrk_alt
    10U,	// VMOVDQU8Zrrkz
    10U,	// VMOVDQU8Zrrkz_alt
    0U,	// VMOVDQUYmr
    0U,	// VMOVDQUYrm
    0U,	// VMOVDQUYrr
    0U,	// VMOVDQUYrr_REV
    0U,	// VMOVDQUmr
    0U,	// VMOVDQUrm
    0U,	// VMOVDQUrr
    0U,	// VMOVDQUrr_REV
    8U,	// VMOVHLPSZrr
    8U,	// VMOVHLPSrr
    0U,	// VMOVHPDmr
    48U,	// VMOVHPDrm
    0U,	// VMOVHPSmr
    48U,	// VMOVHPSrm
    8U,	// VMOVLHPSZrr
    8U,	// VMOVLHPSrr
    0U,	// VMOVLPDmr
    48U,	// VMOVLPDrm
    0U,	// VMOVLPSmr
    48U,	// VMOVLPSrm
    0U,	// VMOVMSKPDYrr
    0U,	// VMOVMSKPDrr
    0U,	// VMOVMSKPSYrr
    0U,	// VMOVMSKPSrr
    0U,	// VMOVNTDQAYrm
    0U,	// VMOVNTDQAZ128rm
    0U,	// VMOVNTDQAZ256rm
    0U,	// VMOVNTDQAZrm
    0U,	// VMOVNTDQArm
    0U,	// VMOVNTDQYmr
    0U,	// VMOVNTDQZ128mr
    0U,	// VMOVNTDQZ256mr
    0U,	// VMOVNTDQZmr
    0U,	// VMOVNTDQmr
    0U,	// VMOVNTPDYmr
    0U,	// VMOVNTPDZ128mr
    0U,	// VMOVNTPDZ256mr
    0U,	// VMOVNTPDZmr
    0U,	// VMOVNTPDmr
    0U,	// VMOVNTPSYmr
    0U,	// VMOVNTPSZ128mr
    0U,	// VMOVNTPSZ256mr
    0U,	// VMOVNTPSZmr
    0U,	// VMOVNTPSmr
    0U,	// VMOVPDI2DIZmr
    0U,	// VMOVPDI2DIZrr
    0U,	// VMOVPDI2DImr
    0U,	// VMOVPDI2DIrr
    0U,	// VMOVPQI2QImr
    0U,	// VMOVPQI2QIrr
    0U,	// VMOVPQIto64Zmr
    0U,	// VMOVPQIto64Zrr
    0U,	// VMOVPQIto64rr
    0U,	// VMOVQI2PQIZrm
    0U,	// VMOVQI2PQIrm
    0U,	// VMOVSDZmr
    0U,	// VMOVSDZrm
    8U,	// VMOVSDZrr
    8U,	// VMOVSDZrr_REV
    298U,	// VMOVSDZrrk
    0U,	// VMOVSDmr
    0U,	// VMOVSDrm
    8U,	// VMOVSDrr
    8U,	// VMOVSDrr_REV
    0U,	// VMOVSDto64Zmr
    0U,	// VMOVSDto64Zrr
    0U,	// VMOVSDto64mr
    0U,	// VMOVSDto64rr
    0U,	// VMOVSHDUPYrm
    0U,	// VMOVSHDUPYrr
    0U,	// VMOVSHDUPZrm
    0U,	// VMOVSHDUPZrr
    0U,	// VMOVSHDUPrm
    0U,	// VMOVSHDUPrr
    0U,	// VMOVSLDUPYrm
    0U,	// VMOVSLDUPYrr
    0U,	// VMOVSLDUPZrm
    0U,	// VMOVSLDUPZrr
    0U,	// VMOVSLDUPrm
    0U,	// VMOVSLDUPrr
    0U,	// VMOVSS2DIZmr
    0U,	// VMOVSS2DIZrr
    0U,	// VMOVSS2DImr
    0U,	// VMOVSS2DIrr
    0U,	// VMOVSSZmr
    0U,	// VMOVSSZrm
    8U,	// VMOVSSZrr
    8U,	// VMOVSSZrr_REV
    298U,	// VMOVSSZrrk
    0U,	// VMOVSSmr
    0U,	// VMOVSSrm
    8U,	// VMOVSSrr
    8U,	// VMOVSSrr_REV
    0U,	// VMOVUPDYmr
    0U,	// VMOVUPDYrm
    0U,	// VMOVUPDYrr
    0U,	// VMOVUPDYrr_REV
    0U,	// VMOVUPDZ128mr
    2U,	// VMOVUPDZ128mrk
    0U,	// VMOVUPDZ128rm
    146U,	// VMOVUPDZ128rmk
    82U,	// VMOVUPDZ128rmkz
    0U,	// VMOVUPDZ128rr
    0U,	// VMOVUPDZ128rr_alt
    42U,	// VMOVUPDZ128rrk
    42U,	// VMOVUPDZ128rrk_alt
    10U,	// VMOVUPDZ128rrkz
    10U,	// VMOVUPDZ128rrkz_alt
    0U,	// VMOVUPDZ256mr
    2U,	// VMOVUPDZ256mrk
    0U,	// VMOVUPDZ256rm
    154U,	// VMOVUPDZ256rmk
    66U,	// VMOVUPDZ256rmkz
    0U,	// VMOVUPDZ256rr
    0U,	// VMOVUPDZ256rr_alt
    42U,	// VMOVUPDZ256rrk
    42U,	// VMOVUPDZ256rrk_alt
    10U,	// VMOVUPDZ256rrkz
    10U,	// VMOVUPDZ256rrkz_alt
    0U,	// VMOVUPDZmr
    2U,	// VMOVUPDZmrk
    0U,	// VMOVUPDZrm
    122U,	// VMOVUPDZrmk
    74U,	// VMOVUPDZrmkz
    0U,	// VMOVUPDZrr
    0U,	// VMOVUPDZrr_alt
    42U,	// VMOVUPDZrrk
    42U,	// VMOVUPDZrrk_alt
    10U,	// VMOVUPDZrrkz
    10U,	// VMOVUPDZrrkz_alt
    0U,	// VMOVUPDmr
    0U,	// VMOVUPDrm
    0U,	// VMOVUPDrr
    0U,	// VMOVUPDrr_REV
    0U,	// VMOVUPSYmr
    0U,	// VMOVUPSYrm
    0U,	// VMOVUPSYrr
    0U,	// VMOVUPSYrr_REV
    0U,	// VMOVUPSZ128mr
    2U,	// VMOVUPSZ128mrk
    0U,	// VMOVUPSZ128rm
    146U,	// VMOVUPSZ128rmk
    82U,	// VMOVUPSZ128rmkz
    0U,	// VMOVUPSZ128rr
    0U,	// VMOVUPSZ128rr_alt
    42U,	// VMOVUPSZ128rrk
    42U,	// VMOVUPSZ128rrk_alt
    10U,	// VMOVUPSZ128rrkz
    10U,	// VMOVUPSZ128rrkz_alt
    0U,	// VMOVUPSZ256mr
    2U,	// VMOVUPSZ256mrk
    0U,	// VMOVUPSZ256rm
    154U,	// VMOVUPSZ256rmk
    66U,	// VMOVUPSZ256rmkz
    0U,	// VMOVUPSZ256rr
    0U,	// VMOVUPSZ256rr_alt
    42U,	// VMOVUPSZ256rrk
    42U,	// VMOVUPSZ256rrk_alt
    10U,	// VMOVUPSZ256rrkz
    10U,	// VMOVUPSZ256rrkz_alt
    0U,	// VMOVUPSZmr
    2U,	// VMOVUPSZmrk
    0U,	// VMOVUPSZrm
    122U,	// VMOVUPSZrmk
    74U,	// VMOVUPSZrmkz
    0U,	// VMOVUPSZrr
    0U,	// VMOVUPSZrr_alt
    42U,	// VMOVUPSZrrk
    42U,	// VMOVUPSZrrk_alt
    10U,	// VMOVUPSZrrkz
    10U,	// VMOVUPSZrrkz_alt
    0U,	// VMOVUPSmr
    0U,	// VMOVUPSrm
    0U,	// VMOVUPSrr
    0U,	// VMOVUPSrr_REV
    0U,	// VMOVZPQILo2PQIZrm
    0U,	// VMOVZPQILo2PQIZrr
    0U,	// VMOVZPQILo2PQIrm
    0U,	// VMOVZPQILo2PQIrr
    0U,	// VMOVZQI2PQIrm
    0U,	// VMOVZQI2PQIrr
    2408U,	// VMPSADBWYrmi
    8456U,	// VMPSADBWYrri
    2392U,	// VMPSADBWrmi
    8456U,	// VMPSADBWrri
    0U,	// VMPTRLDm
    0U,	// VMPTRSTm
    0U,	// VMREAD32rm
    0U,	// VMREAD32rr
    0U,	// VMREAD64rm
    0U,	// VMREAD64rr
    0U,	// VMRESUME
    0U,	// VMRUN32
    0U,	// VMRUN64
    0U,	// VMSAVE32
    0U,	// VMSAVE64
    64U,	// VMULPDYrm
    8U,	// VMULPDYrr
    72U,	// VMULPDZrm
    560U,	// VMULPDZrmb
    69898U,	// VMULPDZrmbk
    69898U,	// VMULPDZrmbkz
    6410U,	// VMULPDZrmk
    6410U,	// VMULPDZrmkz
    8U,	// VMULPDZrr
    8458U,	// VMULPDZrrk
    8458U,	// VMULPDZrrkz
    80U,	// VMULPDrm
    8U,	// VMULPDrr
    64U,	// VMULPSYrm
    8U,	// VMULPSYrr
    72U,	// VMULPSZrm
    824U,	// VMULPSZrmb
    108810U,	// VMULPSZrmbk
    108810U,	// VMULPSZrmbkz
    6410U,	// VMULPSZrmk
    6410U,	// VMULPSZrmkz
    8U,	// VMULPSZrr
    8458U,	// VMULPSZrrk
    8458U,	// VMULPSZrrkz
    80U,	// VMULPSrm
    8U,	// VMULPSrr
    48U,	// VMULSDZrm
    8U,	// VMULSDZrr
    48U,	// VMULSDrm
    48U,	// VMULSDrm_Int
    8U,	// VMULSDrr
    8U,	// VMULSDrr_Int
    56U,	// VMULSSZrm
    8U,	// VMULSSZrr
    56U,	// VMULSSrm
    56U,	// VMULSSrm_Int
    8U,	// VMULSSrr
    8U,	// VMULSSrr_Int
    0U,	// VMWRITE32rm
    0U,	// VMWRITE32rr
    0U,	// VMWRITE64rm
    0U,	// VMWRITE64rr
    0U,	// VMXOFF
    0U,	// VMXON
    64U,	// VORPDYrm
    8U,	// VORPDYrr
    80U,	// VORPDrm
    8U,	// VORPDrr
    64U,	// VORPSYrm
    8U,	// VORPSYrr
    80U,	// VORPSrm
    8U,	// VORPSrr
    0U,	// VPABSBrm128
    0U,	// VPABSBrm256
    0U,	// VPABSBrr128
    0U,	// VPABSBrr256
    0U,	// VPABSDZrm
    4U,	// VPABSDZrmb
    786U,	// VPABSDZrmbk
    786U,	// VPABSDZrmbkz
    98U,	// VPABSDZrmk
    98U,	// VPABSDZrmkz
    0U,	// VPABSDZrr
    10U,	// VPABSDZrrk
    10U,	// VPABSDZrrkz
    0U,	// VPABSDrm128
    0U,	// VPABSDrm256
    0U,	// VPABSDrr128
    0U,	// VPABSDrr256
    0U,	// VPABSQZrm
    5U,	// VPABSQZrmb
    538U,	// VPABSQZrmbk
    538U,	// VPABSQZrmbkz
    98U,	// VPABSQZrmk
    98U,	// VPABSQZrmkz
    0U,	// VPABSQZrr
    10U,	// VPABSQZrrk
    10U,	// VPABSQZrrkz
    0U,	// VPABSWrm128
    0U,	// VPABSWrm256
    0U,	// VPABSWrr128
    0U,	// VPABSWrr256
    104U,	// VPACKSSDWYrm
    8U,	// VPACKSSDWYrr
    88U,	// VPACKSSDWrm
    8U,	// VPACKSSDWrr
    104U,	// VPACKSSWBYrm
    8U,	// VPACKSSWBYrr
    88U,	// VPACKSSWBrm
    8U,	// VPACKSSWBrr
    104U,	// VPACKUSDWYrm
    8U,	// VPACKUSDWYrr
    88U,	// VPACKUSDWrm
    8U,	// VPACKUSDWrr
    104U,	// VPACKUSWBYrm
    8U,	// VPACKUSWBYrr
    88U,	// VPACKUSWBrm
    8U,	// VPACKUSWBrr
    104U,	// VPADDBYrm
    8U,	// VPADDBYrr
    88U,	// VPADDBrm
    8U,	// VPADDBrr
    104U,	// VPADDDYrm
    8U,	// VPADDDYrr
    96U,	// VPADDDZrm
    784U,	// VPADDDZrmb
    16682U,	// VPADDDZrmbk
    117002U,	// VPADDDZrmbkz
    20778U,	// VPADDDZrmk
    22794U,	// VPADDDZrmkz
    8U,	// VPADDDZrr
    298U,	// VPADDDZrrk
    8458U,	// VPADDDZrrkz
    88U,	// VPADDDrm
    8U,	// VPADDDrr
    104U,	// VPADDQYrm
    8U,	// VPADDQYrr
    96U,	// VPADDQZrm
    536U,	// VPADDQZrmb
    24874U,	// VPADDQZrmbk
    92426U,	// VPADDQZrmbkz
    20778U,	// VPADDQZrmk
    22794U,	// VPADDQZrmkz
    8U,	// VPADDQZrr
    298U,	// VPADDQZrrk
    8458U,	// VPADDQZrrkz
    88U,	// VPADDQrm
    8U,	// VPADDQrr
    104U,	// VPADDSBYrm
    8U,	// VPADDSBYrr
    88U,	// VPADDSBrm
    8U,	// VPADDSBrr
    104U,	// VPADDSWYrm
    8U,	// VPADDSWYrr
    88U,	// VPADDSWrm
    8U,	// VPADDSWrr
    104U,	// VPADDUSBYrm
    8U,	// VPADDUSBYrr
    88U,	// VPADDUSBrm
    8U,	// VPADDUSBrr
    104U,	// VPADDUSWYrm
    8U,	// VPADDUSWYrr
    88U,	// VPADDUSWrm
    8U,	// VPADDUSWrr
    104U,	// VPADDWYrm
    8U,	// VPADDWYrr
    88U,	// VPADDWrm
    8U,	// VPADDWrr
    2392U,	// VPALIGNR128rm
    8456U,	// VPALIGNR128rr
    2408U,	// VPALIGNR256rm
    8456U,	// VPALIGNR256rr
    96U,	// VPANDDZrm
    784U,	// VPANDDZrmb
    16682U,	// VPANDDZrmbk
    117002U,	// VPANDDZrmbkz
    20778U,	// VPANDDZrmk
    22794U,	// VPANDDZrmkz
    8U,	// VPANDDZrr
    298U,	// VPANDDZrrk
    8458U,	// VPANDDZrrkz
    96U,	// VPANDNDZrm
    784U,	// VPANDNDZrmb
    16682U,	// VPANDNDZrmbk
    117002U,	// VPANDNDZrmbkz
    20778U,	// VPANDNDZrmk
    22794U,	// VPANDNDZrmkz
    8U,	// VPANDNDZrr
    298U,	// VPANDNDZrrk
    8458U,	// VPANDNDZrrkz
    96U,	// VPANDNQZrm
    536U,	// VPANDNQZrmb
    24874U,	// VPANDNQZrmbk
    92426U,	// VPANDNQZrmbkz
    20778U,	// VPANDNQZrmk
    22794U,	// VPANDNQZrmkz
    8U,	// VPANDNQZrr
    298U,	// VPANDNQZrrk
    8458U,	// VPANDNQZrrkz
    104U,	// VPANDNYrm
    8U,	// VPANDNYrr
    88U,	// VPANDNrm
    8U,	// VPANDNrr
    96U,	// VPANDQZrm
    536U,	// VPANDQZrmb
    24874U,	// VPANDQZrmbk
    92426U,	// VPANDQZrmbkz
    20778U,	// VPANDQZrmk
    22794U,	// VPANDQZrmkz
    8U,	// VPANDQZrr
    298U,	// VPANDQZrrk
    8458U,	// VPANDQZrrkz
    104U,	// VPANDYrm
    8U,	// VPANDYrr
    88U,	// VPANDrm
    8U,	// VPANDrr
    104U,	// VPAVGBYrm
    8U,	// VPAVGBYrr
    88U,	// VPAVGBrm
    8U,	// VPAVGBrr
    104U,	// VPAVGWYrm
    8U,	// VPAVGWYrr
    88U,	// VPAVGWrm
    8U,	// VPAVGWrr
    2408U,	// VPBLENDDYrmi
    8456U,	// VPBLENDDYrri
    2392U,	// VPBLENDDrmi
    8456U,	// VPBLENDDrri
    6410U,	// VPBLENDMDZrm
    8458U,	// VPBLENDMDZrr
    6410U,	// VPBLENDMQZrm
    8458U,	// VPBLENDMQZrr
    2408U,	// VPBLENDVBYrm
    8456U,	// VPBLENDVBYrr
    2392U,	// VPBLENDVBrm
    8456U,	// VPBLENDVBrr
    2408U,	// VPBLENDWYrmi
    8456U,	// VPBLENDWYrri
    2392U,	// VPBLENDWrmi
    8456U,	// VPBLENDWrri
    0U,	// VPBROADCASTBYrm
    0U,	// VPBROADCASTBYrr
    0U,	// VPBROADCASTBrm
    0U,	// VPBROADCASTBrr
    0U,	// VPBROADCASTDYrm
    0U,	// VPBROADCASTDYrr
    18U,	// VPBROADCASTDZkrm
    10U,	// VPBROADCASTDZkrr
    0U,	// VPBROADCASTDZrm
    0U,	// VPBROADCASTDZrr
    10U,	// VPBROADCASTDrZkrr
    0U,	// VPBROADCASTDrZrr
    0U,	// VPBROADCASTDrm
    0U,	// VPBROADCASTDrr
    0U,	// VPBROADCASTMB2Qrr
    0U,	// VPBROADCASTMW2Drr
    0U,	// VPBROADCASTQYrm
    0U,	// VPBROADCASTQYrr
    26U,	// VPBROADCASTQZkrm
    10U,	// VPBROADCASTQZkrr
    0U,	// VPBROADCASTQZrm
    0U,	// VPBROADCASTQZrr
    10U,	// VPBROADCASTQrZkrr
    0U,	// VPBROADCASTQrZrr
    0U,	// VPBROADCASTQrm
    0U,	// VPBROADCASTQrr
    0U,	// VPBROADCASTWYrm
    0U,	// VPBROADCASTWYrr
    0U,	// VPBROADCASTWrm
    0U,	// VPBROADCASTWrr
    2392U,	// VPCLMULQDQrm
    8456U,	// VPCLMULQDQrr
    2392U,	// VPCMOVmr
    2368U,	// VPCMOVmrY
    28936U,	// VPCMOVrm
    30984U,	// VPCMOVrmY
    8456U,	// VPCMOVrr
    8456U,	// VPCMOVrrY
    0U,	// VPCMPDZrmi
    2400U,	// VPCMPDZrmi_alt
    55562U,	// VPCMPDZrmik_alt
    0U,	// VPCMPDZrri
    8456U,	// VPCMPDZrri_alt
    565514U,	// VPCMPDZrrik_alt
    104U,	// VPCMPEQBYrm
    8U,	// VPCMPEQBYrr
    88U,	// VPCMPEQBZ128rm
    28938U,	// VPCMPEQBZ128rmk
    8U,	// VPCMPEQBZ128rr
    8458U,	// VPCMPEQBZ128rrk
    104U,	// VPCMPEQBZ256rm
    30986U,	// VPCMPEQBZ256rmk
    8U,	// VPCMPEQBZ256rr
    8458U,	// VPCMPEQBZ256rrk
    96U,	// VPCMPEQBZrm
    22794U,	// VPCMPEQBZrmk
    8U,	// VPCMPEQBZrr
    8458U,	// VPCMPEQBZrrk
    88U,	// VPCMPEQBrm
    8U,	// VPCMPEQBrr
    104U,	// VPCMPEQDYrm
    8U,	// VPCMPEQDYrr
    88U,	// VPCMPEQDZ128rm
    1040U,	// VPCMPEQDZ128rmb
    149770U,	// VPCMPEQDZ128rmbk
    28938U,	// VPCMPEQDZ128rmk
    8U,	// VPCMPEQDZ128rr
    8458U,	// VPCMPEQDZ128rrk
    104U,	// VPCMPEQDZ256rm
    528U,	// VPCMPEQDZ256rmb
    84234U,	// VPCMPEQDZ256rmbk
    30986U,	// VPCMPEQDZ256rmk
    8U,	// VPCMPEQDZ256rr
    8458U,	// VPCMPEQDZ256rrk
    96U,	// VPCMPEQDZrm
    784U,	// VPCMPEQDZrmb
    117002U,	// VPCMPEQDZrmbk
    22794U,	// VPCMPEQDZrmk
    8U,	// VPCMPEQDZrr
    8458U,	// VPCMPEQDZrrk
    88U,	// VPCMPEQDrm
    8U,	// VPCMPEQDrr
    104U,	// VPCMPEQQYrm
    8U,	// VPCMPEQQYrr
    88U,	// VPCMPEQQZ128rm
    1304U,	// VPCMPEQQZ128rmb
    190730U,	// VPCMPEQQZ128rmbk
    28938U,	// VPCMPEQQZ128rmk
    8U,	// VPCMPEQQZ128rr
    8458U,	// VPCMPEQQZ128rrk
    104U,	// VPCMPEQQZ256rm
    1048U,	// VPCMPEQQZ256rmb
    157962U,	// VPCMPEQQZ256rmbk
    30986U,	// VPCMPEQQZ256rmk
    8U,	// VPCMPEQQZ256rr
    8458U,	// VPCMPEQQZ256rrk
    96U,	// VPCMPEQQZrm
    536U,	// VPCMPEQQZrmb
    92426U,	// VPCMPEQQZrmbk
    22794U,	// VPCMPEQQZrmk
    8U,	// VPCMPEQQZrr
    8458U,	// VPCMPEQQZrrk
    88U,	// VPCMPEQQrm
    8U,	// VPCMPEQQrr
    104U,	// VPCMPEQWYrm
    8U,	// VPCMPEQWYrr
    88U,	// VPCMPEQWZ128rm
    28938U,	// VPCMPEQWZ128rmk
    8U,	// VPCMPEQWZ128rr
    8458U,	// VPCMPEQWZ128rrk
    104U,	// VPCMPEQWZ256rm
    30986U,	// VPCMPEQWZ256rmk
    8U,	// VPCMPEQWZ256rr
    8458U,	// VPCMPEQWZ256rrk
    96U,	// VPCMPEQWZrm
    22794U,	// VPCMPEQWZrmk
    8U,	// VPCMPEQWZrr
    8458U,	// VPCMPEQWZrrk
    88U,	// VPCMPEQWrm
    8U,	// VPCMPEQWrr
    0U,	// VPCMPESTRIMEM
    0U,	// VPCMPESTRIREG
    0U,	// VPCMPESTRIrm
    8U,	// VPCMPESTRIrr
    0U,	// VPCMPESTRM128MEM
    0U,	// VPCMPESTRM128REG
    0U,	// VPCMPESTRM128rm
    8U,	// VPCMPESTRM128rr
    104U,	// VPCMPGTBYrm
    8U,	// VPCMPGTBYrr
    88U,	// VPCMPGTBZ128rm
    28938U,	// VPCMPGTBZ128rmk
    8U,	// VPCMPGTBZ128rr
    8458U,	// VPCMPGTBZ128rrk
    104U,	// VPCMPGTBZ256rm
    30986U,	// VPCMPGTBZ256rmk
    8U,	// VPCMPGTBZ256rr
    8458U,	// VPCMPGTBZ256rrk
    96U,	// VPCMPGTBZrm
    22794U,	// VPCMPGTBZrmk
    8U,	// VPCMPGTBZrr
    8458U,	// VPCMPGTBZrrk
    88U,	// VPCMPGTBrm
    8U,	// VPCMPGTBrr
    104U,	// VPCMPGTDYrm
    8U,	// VPCMPGTDYrr
    88U,	// VPCMPGTDZ128rm
    1040U,	// VPCMPGTDZ128rmb
    149770U,	// VPCMPGTDZ128rmbk
    28938U,	// VPCMPGTDZ128rmk
    8U,	// VPCMPGTDZ128rr
    8458U,	// VPCMPGTDZ128rrk
    104U,	// VPCMPGTDZ256rm
    528U,	// VPCMPGTDZ256rmb
    84234U,	// VPCMPGTDZ256rmbk
    30986U,	// VPCMPGTDZ256rmk
    8U,	// VPCMPGTDZ256rr
    8458U,	// VPCMPGTDZ256rrk
    96U,	// VPCMPGTDZrm
    784U,	// VPCMPGTDZrmb
    117002U,	// VPCMPGTDZrmbk
    22794U,	// VPCMPGTDZrmk
    8U,	// VPCMPGTDZrr
    8458U,	// VPCMPGTDZrrk
    88U,	// VPCMPGTDrm
    8U,	// VPCMPGTDrr
    104U,	// VPCMPGTQYrm
    8U,	// VPCMPGTQYrr
    88U,	// VPCMPGTQZ128rm
    1304U,	// VPCMPGTQZ128rmb
    190730U,	// VPCMPGTQZ128rmbk
    28938U,	// VPCMPGTQZ128rmk
    8U,	// VPCMPGTQZ128rr
    8458U,	// VPCMPGTQZ128rrk
    104U,	// VPCMPGTQZ256rm
    1048U,	// VPCMPGTQZ256rmb
    157962U,	// VPCMPGTQZ256rmbk
    30986U,	// VPCMPGTQZ256rmk
    8U,	// VPCMPGTQZ256rr
    8458U,	// VPCMPGTQZ256rrk
    96U,	// VPCMPGTQZrm
    536U,	// VPCMPGTQZrmb
    92426U,	// VPCMPGTQZrmbk
    22794U,	// VPCMPGTQZrmk
    8U,	// VPCMPGTQZrr
    8458U,	// VPCMPGTQZrrk
    88U,	// VPCMPGTQrm
    8U,	// VPCMPGTQrr
    104U,	// VPCMPGTWYrm
    8U,	// VPCMPGTWYrr
    88U,	// VPCMPGTWZ128rm
    28938U,	// VPCMPGTWZ128rmk
    8U,	// VPCMPGTWZ128rr
    8458U,	// VPCMPGTWZ128rrk
    104U,	// VPCMPGTWZ256rm
    30986U,	// VPCMPGTWZ256rmk
    8U,	// VPCMPGTWZ256rr
    8458U,	// VPCMPGTWZ256rrk
    96U,	// VPCMPGTWZrm
    22794U,	// VPCMPGTWZrmk
    8U,	// VPCMPGTWZrr
    8458U,	// VPCMPGTWZrrk
    88U,	// VPCMPGTWrm
    8U,	// VPCMPGTWrr
    0U,	// VPCMPISTRIMEM
    0U,	// VPCMPISTRIREG
    0U,	// VPCMPISTRIrm
    8U,	// VPCMPISTRIrr
    0U,	// VPCMPISTRM128MEM
    0U,	// VPCMPISTRM128REG
    0U,	// VPCMPISTRM128rm
    8U,	// VPCMPISTRM128rr
    0U,	// VPCMPQZrmi
    2400U,	// VPCMPQZrmi_alt
    55562U,	// VPCMPQZrmik_alt
    0U,	// VPCMPQZrri
    8456U,	// VPCMPQZrri_alt
    565514U,	// VPCMPQZrrik_alt
    0U,	// VPCMPUDZrmi
    2400U,	// VPCMPUDZrmi_alt
    55562U,	// VPCMPUDZrmik_alt
    0U,	// VPCMPUDZrri
    8456U,	// VPCMPUDZrri_alt
    565514U,	// VPCMPUDZrrik_alt
    0U,	// VPCMPUQZrmi
    2400U,	// VPCMPUQZrmi_alt
    55562U,	// VPCMPUQZrmik_alt
    0U,	// VPCMPUQZrri
    8456U,	// VPCMPUQZrri_alt
    565514U,	// VPCMPUQZrrik_alt
    2392U,	// VPCOMBmi
    8456U,	// VPCOMBri
    2392U,	// VPCOMDmi
    8456U,	// VPCOMDri
    2392U,	// VPCOMQmi
    8456U,	// VPCOMQri
    2392U,	// VPCOMUBmi
    8456U,	// VPCOMUBri
    2392U,	// VPCOMUDmi
    8456U,	// VPCOMUDri
    2392U,	// VPCOMUQmi
    8456U,	// VPCOMUQri
    2392U,	// VPCOMUWmi
    8456U,	// VPCOMUWri
    2392U,	// VPCOMWmi
    8456U,	// VPCOMWri
    0U,	// VPCONFLICTDrm
    4U,	// VPCONFLICTDrmb
    186U,	// VPCONFLICTDrmbk
    786U,	// VPCONFLICTDrmbkz
    178U,	// VPCONFLICTDrmk
    98U,	// VPCONFLICTDrmkz
    0U,	// VPCONFLICTDrr
    42U,	// VPCONFLICTDrrk
    10U,	// VPCONFLICTDrrkz
    0U,	// VPCONFLICTQrm
    5U,	// VPCONFLICTQrmb
    194U,	// VPCONFLICTQrmbk
    538U,	// VPCONFLICTQrmbkz
    178U,	// VPCONFLICTQrmk
    98U,	// VPCONFLICTQrmkz
    0U,	// VPCONFLICTQrr
    42U,	// VPCONFLICTQrrk
    10U,	// VPCONFLICTQrrkz
    2368U,	// VPERM2F128rm
    8456U,	// VPERM2F128rr
    2368U,	// VPERM2I128rm
    8456U,	// VPERM2I128rr
    104U,	// VPERMDYrm
    8U,	// VPERMDYrr
    96U,	// VPERMDZrm
    8U,	// VPERMDZrr
    176U,	// VPERMI2Drm
    20778U,	// VPERMI2Drmk
    20778U,	// VPERMI2Drmkz
    40U,	// VPERMI2Drr
    298U,	// VPERMI2Drrk
    298U,	// VPERMI2Drrkz
    176U,	// VPERMI2PDrm
    20778U,	// VPERMI2PDrmk
    20778U,	// VPERMI2PDrmkz
    40U,	// VPERMI2PDrr
    298U,	// VPERMI2PDrrk
    298U,	// VPERMI2PDrrkz
    176U,	// VPERMI2PSrm
    20778U,	// VPERMI2PSrmk
    20778U,	// VPERMI2PSrmkz
    40U,	// VPERMI2PSrr
    298U,	// VPERMI2PSrrk
    298U,	// VPERMI2PSrrkz
    176U,	// VPERMI2Qrm
    20778U,	// VPERMI2Qrmk
    20778U,	// VPERMI2Qrmkz
    40U,	// VPERMI2Qrr
    298U,	// VPERMI2Qrrk
    298U,	// VPERMI2Qrrkz
    35152U,	// VPERMIL2PDmr
    35136U,	// VPERMIL2PDmrY
    45320U,	// VPERMIL2PDrm
    47368U,	// VPERMIL2PDrmY
    565512U,	// VPERMIL2PDrr
    565512U,	// VPERMIL2PDrrY
    35152U,	// VPERMIL2PSmr
    35136U,	// VPERMIL2PSmrY
    45320U,	// VPERMIL2PSrm
    47368U,	// VPERMIL2PSrmY
    565512U,	// VPERMIL2PSrr
    565512U,	// VPERMIL2PSrrY
    0U,	// VPERMILPDYmi
    8U,	// VPERMILPDYri
    104U,	// VPERMILPDYrm
    8U,	// VPERMILPDYrr
    0U,	// VPERMILPDZmi
    8U,	// VPERMILPDZri
    0U,	// VPERMILPDmi
    8U,	// VPERMILPDri
    88U,	// VPERMILPDrm
    8U,	// VPERMILPDrr
    0U,	// VPERMILPSYmi
    8U,	// VPERMILPSYri
    104U,	// VPERMILPSYrm
    8U,	// VPERMILPSYrr
    0U,	// VPERMILPSZmi
    8U,	// VPERMILPSZri
    0U,	// VPERMILPSmi
    8U,	// VPERMILPSri
    88U,	// VPERMILPSrm
    8U,	// VPERMILPSrr
    0U,	// VPERMPDYmi
    8U,	// VPERMPDYri
    0U,	// VPERMPDZmi
    8U,	// VPERMPDZri
    72U,	// VPERMPDZrm
    8U,	// VPERMPDZrr
    104U,	// VPERMPSYrm
    8U,	// VPERMPSYrr
    72U,	// VPERMPSZrm
    8U,	// VPERMPSZrr
    0U,	// VPERMQYmi
    8U,	// VPERMQYri
    0U,	// VPERMQZmi
    8U,	// VPERMQZri
    96U,	// VPERMQZrm
    8U,	// VPERMQZrr
    176U,	// VPERMT2Drm
    20778U,	// VPERMT2Drmk
    20778U,	// VPERMT2Drmkz
    40U,	// VPERMT2Drr
    298U,	// VPERMT2Drrk
    298U,	// VPERMT2Drrkz
    176U,	// VPERMT2PDrm
    20778U,	// VPERMT2PDrmk
    20778U,	// VPERMT2PDrmkz
    40U,	// VPERMT2PDrr
    298U,	// VPERMT2PDrrk
    298U,	// VPERMT2PDrrkz
    176U,	// VPERMT2PSrm
    20778U,	// VPERMT2PSrmk
    20778U,	// VPERMT2PSrmkz
    40U,	// VPERMT2PSrr
    298U,	// VPERMT2PSrrk
    298U,	// VPERMT2PSrrkz
    176U,	// VPERMT2Qrm
    20778U,	// VPERMT2Qrmk
    20778U,	// VPERMT2Qrmkz
    40U,	// VPERMT2Qrr
    298U,	// VPERMT2Qrrk
    298U,	// VPERMT2Qrrkz
    0U,	// VPEXTRBmr
    8U,	// VPEXTRBrr
    0U,	// VPEXTRDmr
    8U,	// VPEXTRDrr
    0U,	// VPEXTRQmr
    8U,	// VPEXTRQrr
    0U,	// VPEXTRWmr
    8U,	// VPEXTRWri
    8U,	// VPEXTRWrr_REV
    0U,	// VPGATHERDDYrm
    4U,	// VPGATHERDDZrm
    0U,	// VPGATHERDDrm
    0U,	// VPGATHERDQYrm
    3U,	// VPGATHERDQZrm
    0U,	// VPGATHERDQrm
    0U,	// VPGATHERQDYrm
    3U,	// VPGATHERQDZrm
    0U,	// VPGATHERQDrm
    0U,	// VPGATHERQQYrm
    3U,	// VPGATHERQQZrm
    0U,	// VPGATHERQQrm
    0U,	// VPHADDBDrm
    0U,	// VPHADDBDrr
    0U,	// VPHADDBQrm
    0U,	// VPHADDBQrr
    0U,	// VPHADDBWrm
    0U,	// VPHADDBWrr
    0U,	// VPHADDDQrm
    0U,	// VPHADDDQrr
    104U,	// VPHADDDYrm
    8U,	// VPHADDDYrr
    88U,	// VPHADDDrm
    8U,	// VPHADDDrr
    88U,	// VPHADDSWrm128
    104U,	// VPHADDSWrm256
    8U,	// VPHADDSWrr128
    8U,	// VPHADDSWrr256
    0U,	// VPHADDUBDrm
    0U,	// VPHADDUBDrr
    0U,	// VPHADDUBQrm
    0U,	// VPHADDUBQrr
    0U,	// VPHADDUBWrm
    0U,	// VPHADDUBWrr
    0U,	// VPHADDUDQrm
    0U,	// VPHADDUDQrr
    0U,	// VPHADDUWDrm
    0U,	// VPHADDUWDrr
    0U,	// VPHADDUWQrm
    0U,	// VPHADDUWQrr
    0U,	// VPHADDWDrm
    0U,	// VPHADDWDrr
    0U,	// VPHADDWQrm
    0U,	// VPHADDWQrr
    104U,	// VPHADDWYrm
    8U,	// VPHADDWYrr
    88U,	// VPHADDWrm
    8U,	// VPHADDWrr
    0U,	// VPHMINPOSUWrm128
    0U,	// VPHMINPOSUWrr128
    0U,	// VPHSUBBWrm
    0U,	// VPHSUBBWrr
    0U,	// VPHSUBDQrm
    0U,	// VPHSUBDQrr
    104U,	// VPHSUBDYrm
    8U,	// VPHSUBDYrr
    88U,	// VPHSUBDrm
    8U,	// VPHSUBDrr
    88U,	// VPHSUBSWrm128
    104U,	// VPHSUBSWrm256
    8U,	// VPHSUBSWrr128
    8U,	// VPHSUBSWrr256
    0U,	// VPHSUBWDrm
    0U,	// VPHSUBWDrr
    104U,	// VPHSUBWYrm
    8U,	// VPHSUBWYrr
    88U,	// VPHSUBWrm
    8U,	// VPHSUBWrr
    200U,	// VPINSRBrm
    8456U,	// VPINSRBrr
    2320U,	// VPINSRDrm
    8456U,	// VPINSRDrr
    2328U,	// VPINSRQrm
    8456U,	// VPINSRQrr
    208U,	// VPINSRWrmi
    8456U,	// VPINSRWrri
    0U,	// VPLZCNTDrm
    4U,	// VPLZCNTDrmb
    186U,	// VPLZCNTDrmbk
    786U,	// VPLZCNTDrmbkz
    178U,	// VPLZCNTDrmk
    98U,	// VPLZCNTDrmkz
    0U,	// VPLZCNTDrr
    42U,	// VPLZCNTDrrk
    10U,	// VPLZCNTDrrkz
    0U,	// VPLZCNTQrm
    5U,	// VPLZCNTQrmb
    194U,	// VPLZCNTQrmbk
    538U,	// VPLZCNTQrmbkz
    178U,	// VPLZCNTQrmk
    98U,	// VPLZCNTQrmkz
    0U,	// VPLZCNTQrr
    42U,	// VPLZCNTQrrk
    10U,	// VPLZCNTQrrkz
    2392U,	// VPMACSDDrm
    8456U,	// VPMACSDDrr
    2392U,	// VPMACSDQHrm
    8456U,	// VPMACSDQHrr
    2392U,	// VPMACSDQLrm
    8456U,	// VPMACSDQLrr
    2392U,	// VPMACSSDDrm
    8456U,	// VPMACSSDDrr
    2392U,	// VPMACSSDQHrm
    8456U,	// VPMACSSDQHrr
    2392U,	// VPMACSSDQLrm
    8456U,	// VPMACSSDQLrr
    2392U,	// VPMACSSWDrm
    8456U,	// VPMACSSWDrr
    2392U,	// VPMACSSWWrm
    8456U,	// VPMACSSWWrr
    2392U,	// VPMACSWDrm
    8456U,	// VPMACSWDrr
    2392U,	// VPMACSWWrm
    8456U,	// VPMACSWWrr
    2392U,	// VPMADCSSWDrm
    8456U,	// VPMADCSSWDrr
    2392U,	// VPMADCSWDrm
    8456U,	// VPMADCSWDrr
    88U,	// VPMADDUBSWrm128
    104U,	// VPMADDUBSWrm256
    8U,	// VPMADDUBSWrr128
    8U,	// VPMADDUBSWrr256
    104U,	// VPMADDWDYrm
    8U,	// VPMADDWDYrr
    88U,	// VPMADDWDrm
    8U,	// VPMADDWDrr
    0U,	// VPMASKMOVDYmr
    104U,	// VPMASKMOVDYrm
    0U,	// VPMASKMOVDmr
    88U,	// VPMASKMOVDrm
    0U,	// VPMASKMOVQYmr
    104U,	// VPMASKMOVQYrm
    0U,	// VPMASKMOVQmr
    88U,	// VPMASKMOVQrm
    104U,	// VPMAXSBYrm
    8U,	// VPMAXSBYrr
    88U,	// VPMAXSBrm
    8U,	// VPMAXSBrr
    104U,	// VPMAXSDYrm
    8U,	// VPMAXSDYrr
    96U,	// VPMAXSDZrm
    784U,	// VPMAXSDZrmb
    16682U,	// VPMAXSDZrmbk
    117002U,	// VPMAXSDZrmbkz
    20778U,	// VPMAXSDZrmk
    22794U,	// VPMAXSDZrmkz
    8U,	// VPMAXSDZrr
    298U,	// VPMAXSDZrrk
    8458U,	// VPMAXSDZrrkz
    88U,	// VPMAXSDrm
    8U,	// VPMAXSDrr
    96U,	// VPMAXSQZrm
    536U,	// VPMAXSQZrmb
    24874U,	// VPMAXSQZrmbk
    92426U,	// VPMAXSQZrmbkz
    20778U,	// VPMAXSQZrmk
    22794U,	// VPMAXSQZrmkz
    8U,	// VPMAXSQZrr
    298U,	// VPMAXSQZrrk
    8458U,	// VPMAXSQZrrkz
    104U,	// VPMAXSWYrm
    8U,	// VPMAXSWYrr
    88U,	// VPMAXSWrm
    8U,	// VPMAXSWrr
    104U,	// VPMAXUBYrm
    8U,	// VPMAXUBYrr
    88U,	// VPMAXUBrm
    8U,	// VPMAXUBrr
    104U,	// VPMAXUDYrm
    8U,	// VPMAXUDYrr
    96U,	// VPMAXUDZrm
    784U,	// VPMAXUDZrmb
    16682U,	// VPMAXUDZrmbk
    117002U,	// VPMAXUDZrmbkz
    20778U,	// VPMAXUDZrmk
    22794U,	// VPMAXUDZrmkz
    8U,	// VPMAXUDZrr
    298U,	// VPMAXUDZrrk
    8458U,	// VPMAXUDZrrkz
    88U,	// VPMAXUDrm
    8U,	// VPMAXUDrr
    96U,	// VPMAXUQZrm
    536U,	// VPMAXUQZrmb
    24874U,	// VPMAXUQZrmbk
    92426U,	// VPMAXUQZrmbkz
    20778U,	// VPMAXUQZrmk
    22794U,	// VPMAXUQZrmkz
    8U,	// VPMAXUQZrr
    298U,	// VPMAXUQZrrk
    8458U,	// VPMAXUQZrrkz
    104U,	// VPMAXUWYrm
    8U,	// VPMAXUWYrr
    88U,	// VPMAXUWrm
    8U,	// VPMAXUWrr
    104U,	// VPMINSBYrm
    8U,	// VPMINSBYrr
    88U,	// VPMINSBrm
    8U,	// VPMINSBrr
    104U,	// VPMINSDYrm
    8U,	// VPMINSDYrr
    96U,	// VPMINSDZrm
    784U,	// VPMINSDZrmb
    16682U,	// VPMINSDZrmbk
    117002U,	// VPMINSDZrmbkz
    20778U,	// VPMINSDZrmk
    22794U,	// VPMINSDZrmkz
    8U,	// VPMINSDZrr
    298U,	// VPMINSDZrrk
    8458U,	// VPMINSDZrrkz
    88U,	// VPMINSDrm
    8U,	// VPMINSDrr
    96U,	// VPMINSQZrm
    536U,	// VPMINSQZrmb
    24874U,	// VPMINSQZrmbk
    92426U,	// VPMINSQZrmbkz
    20778U,	// VPMINSQZrmk
    22794U,	// VPMINSQZrmkz
    8U,	// VPMINSQZrr
    298U,	// VPMINSQZrrk
    8458U,	// VPMINSQZrrkz
    104U,	// VPMINSWYrm
    8U,	// VPMINSWYrr
    88U,	// VPMINSWrm
    8U,	// VPMINSWrr
    104U,	// VPMINUBYrm
    8U,	// VPMINUBYrr
    88U,	// VPMINUBrm
    8U,	// VPMINUBrr
    104U,	// VPMINUDYrm
    8U,	// VPMINUDYrr
    96U,	// VPMINUDZrm
    784U,	// VPMINUDZrmb
    16682U,	// VPMINUDZrmbk
    117002U,	// VPMINUDZrmbkz
    20778U,	// VPMINUDZrmk
    22794U,	// VPMINUDZrmkz
    8U,	// VPMINUDZrr
    298U,	// VPMINUDZrrk
    8458U,	// VPMINUDZrrkz
    88U,	// VPMINUDrm
    8U,	// VPMINUDrr
    96U,	// VPMINUQZrm
    536U,	// VPMINUQZrmb
    24874U,	// VPMINUQZrmbk
    92426U,	// VPMINUQZrmbkz
    20778U,	// VPMINUQZrmk
    22794U,	// VPMINUQZrmkz
    8U,	// VPMINUQZrr
    298U,	// VPMINUQZrrk
    8458U,	// VPMINUQZrrkz
    104U,	// VPMINUWYrm
    8U,	// VPMINUWYrr
    88U,	// VPMINUWrm
    8U,	// VPMINUWrr
    0U,	// VPMOVDBmr
    2U,	// VPMOVDBmrk
    0U,	// VPMOVDBrr
    10U,	// VPMOVDBrrk
    10U,	// VPMOVDBrrkz
    0U,	// VPMOVDWmr
    2U,	// VPMOVDWmrk
    0U,	// VPMOVDWrr
    10U,	// VPMOVDWrrk
    10U,	// VPMOVDWrrkz
    0U,	// VPMOVMSKBYrr
    0U,	// VPMOVMSKBrr
    0U,	// VPMOVQBmr
    2U,	// VPMOVQBmrk
    0U,	// VPMOVQBrr
    10U,	// VPMOVQBrrk
    10U,	// VPMOVQBrrkz
    0U,	// VPMOVQDmr
    2U,	// VPMOVQDmrk
    0U,	// VPMOVQDrr
    10U,	// VPMOVQDrrk
    10U,	// VPMOVQDrrkz
    0U,	// VPMOVQWmr
    2U,	// VPMOVQWmrk
    0U,	// VPMOVQWrr
    10U,	// VPMOVQWrrk
    10U,	// VPMOVQWrrkz
    0U,	// VPMOVSDBmr
    2U,	// VPMOVSDBmrk
    0U,	// VPMOVSDBrr
    10U,	// VPMOVSDBrrk
    10U,	// VPMOVSDBrrkz
    0U,	// VPMOVSDWmr
    2U,	// VPMOVSDWmrk
    0U,	// VPMOVSDWrr
    10U,	// VPMOVSDWrrk
    10U,	// VPMOVSDWrrkz
    0U,	// VPMOVSQBmr
    2U,	// VPMOVSQBmrk
    0U,	// VPMOVSQBrr
    10U,	// VPMOVSQBrrk
    10U,	// VPMOVSQBrrkz
    0U,	// VPMOVSQDmr
    2U,	// VPMOVSQDmrk
    0U,	// VPMOVSQDrr
    10U,	// VPMOVSQDrrk
    10U,	// VPMOVSQDrrkz
    0U,	// VPMOVSQWmr
    2U,	// VPMOVSQWmrk
    0U,	// VPMOVSQWrr
    10U,	// VPMOVSQWrrk
    10U,	// VPMOVSQWrrkz
    0U,	// VPMOVSXBDYrm
    0U,	// VPMOVSXBDYrr
    0U,	// VPMOVSXBDZrm
    90U,	// VPMOVSXBDZrmk
    90U,	// VPMOVSXBDZrmkz
    0U,	// VPMOVSXBDZrr
    10U,	// VPMOVSXBDZrrk
    10U,	// VPMOVSXBDZrrkz
    0U,	// VPMOVSXBDrm
    0U,	// VPMOVSXBDrr
    0U,	// VPMOVSXBQYrm
    0U,	// VPMOVSXBQYrr
    0U,	// VPMOVSXBQZrm
    90U,	// VPMOVSXBQZrmk
    90U,	// VPMOVSXBQZrmkz
    0U,	// VPMOVSXBQZrr
    10U,	// VPMOVSXBQZrrk
    10U,	// VPMOVSXBQZrrkz
    0U,	// VPMOVSXBQrm
    0U,	// VPMOVSXBQrr
    0U,	// VPMOVSXBWYrm
    0U,	// VPMOVSXBWYrr
    0U,	// VPMOVSXBWrm
    0U,	// VPMOVSXBWrr
    0U,	// VPMOVSXDQYrm
    0U,	// VPMOVSXDQYrr
    0U,	// VPMOVSXDQZrm
    106U,	// VPMOVSXDQZrmk
    106U,	// VPMOVSXDQZrmkz
    0U,	// VPMOVSXDQZrr
    10U,	// VPMOVSXDQZrrk
    10U,	// VPMOVSXDQZrrkz
    0U,	// VPMOVSXDQrm
    0U,	// VPMOVSXDQrr
    0U,	// VPMOVSXWDYrm
    0U,	// VPMOVSXWDYrr
    0U,	// VPMOVSXWDZrm
    106U,	// VPMOVSXWDZrmk
    106U,	// VPMOVSXWDZrmkz
    0U,	// VPMOVSXWDZrr
    10U,	// VPMOVSXWDZrrk
    10U,	// VPMOVSXWDZrrkz
    0U,	// VPMOVSXWDrm
    0U,	// VPMOVSXWDrr
    0U,	// VPMOVSXWQYrm
    0U,	// VPMOVSXWQYrr
    0U,	// VPMOVSXWQZrm
    90U,	// VPMOVSXWQZrmk
    90U,	// VPMOVSXWQZrmkz
    0U,	// VPMOVSXWQZrr
    10U,	// VPMOVSXWQZrrk
    10U,	// VPMOVSXWQZrrkz
    0U,	// VPMOVSXWQrm
    0U,	// VPMOVSXWQrr
    0U,	// VPMOVUSDBmr
    2U,	// VPMOVUSDBmrk
    0U,	// VPMOVUSDBrr
    10U,	// VPMOVUSDBrrk
    10U,	// VPMOVUSDBrrkz
    0U,	// VPMOVUSDWmr
    2U,	// VPMOVUSDWmrk
    0U,	// VPMOVUSDWrr
    10U,	// VPMOVUSDWrrk
    10U,	// VPMOVUSDWrrkz
    0U,	// VPMOVUSQBmr
    2U,	// VPMOVUSQBmrk
    0U,	// VPMOVUSQBrr
    10U,	// VPMOVUSQBrrk
    10U,	// VPMOVUSQBrrkz
    0U,	// VPMOVUSQDmr
    2U,	// VPMOVUSQDmrk
    0U,	// VPMOVUSQDrr
    10U,	// VPMOVUSQDrrk
    10U,	// VPMOVUSQDrrkz
    0U,	// VPMOVUSQWmr
    2U,	// VPMOVUSQWmrk
    0U,	// VPMOVUSQWrr
    10U,	// VPMOVUSQWrrk
    10U,	// VPMOVUSQWrrkz
    0U,	// VPMOVZXBDYrm
    0U,	// VPMOVZXBDYrr
    0U,	// VPMOVZXBDZrm
    90U,	// VPMOVZXBDZrmk
    90U,	// VPMOVZXBDZrmkz
    0U,	// VPMOVZXBDZrr
    10U,	// VPMOVZXBDZrrk
    10U,	// VPMOVZXBDZrrkz
    0U,	// VPMOVZXBDrm
    0U,	// VPMOVZXBDrr
    0U,	// VPMOVZXBQYrm
    0U,	// VPMOVZXBQYrr
    0U,	// VPMOVZXBQZrm
    90U,	// VPMOVZXBQZrmk
    90U,	// VPMOVZXBQZrmkz
    0U,	// VPMOVZXBQZrr
    10U,	// VPMOVZXBQZrrk
    10U,	// VPMOVZXBQZrrkz
    0U,	// VPMOVZXBQrm
    0U,	// VPMOVZXBQrr
    0U,	// VPMOVZXBWYrm
    0U,	// VPMOVZXBWYrr
    0U,	// VPMOVZXBWrm
    0U,	// VPMOVZXBWrr
    0U,	// VPMOVZXDQYrm
    0U,	// VPMOVZXDQYrr
    0U,	// VPMOVZXDQZrm
    106U,	// VPMOVZXDQZrmk
    106U,	// VPMOVZXDQZrmkz
    0U,	// VPMOVZXDQZrr
    10U,	// VPMOVZXDQZrrk
    10U,	// VPMOVZXDQZrrkz
    0U,	// VPMOVZXDQrm
    0U,	// VPMOVZXDQrr
    0U,	// VPMOVZXWDYrm
    0U,	// VPMOVZXWDYrr
    0U,	// VPMOVZXWDZrm
    106U,	// VPMOVZXWDZrmk
    106U,	// VPMOVZXWDZrmkz
    0U,	// VPMOVZXWDZrr
    10U,	// VPMOVZXWDZrrk
    10U,	// VPMOVZXWDZrrkz
    0U,	// VPMOVZXWDrm
    0U,	// VPMOVZXWDrr
    0U,	// VPMOVZXWQYrm
    0U,	// VPMOVZXWQYrr
    0U,	// VPMOVZXWQZrm
    90U,	// VPMOVZXWQZrmk
    90U,	// VPMOVZXWQZrmkz
    0U,	// VPMOVZXWQZrr
    10U,	// VPMOVZXWQZrrk
    10U,	// VPMOVZXWQZrrkz
    0U,	// VPMOVZXWQrm
    0U,	// VPMOVZXWQrr
    104U,	// VPMULDQYrm
    8U,	// VPMULDQYrr
    96U,	// VPMULDQZrm
    536U,	// VPMULDQZrmb
    92426U,	// VPMULDQZrmbk
    92426U,	// VPMULDQZrmbkz
    22794U,	// VPMULDQZrmk
    22794U,	// VPMULDQZrmkz
    8U,	// VPMULDQZrr
    8458U,	// VPMULDQZrrk
    8458U,	// VPMULDQZrrkz
    88U,	// VPMULDQrm
    8U,	// VPMULDQrr
    88U,	// VPMULHRSWrm128
    104U,	// VPMULHRSWrm256
    8U,	// VPMULHRSWrr128
    8U,	// VPMULHRSWrr256
    104U,	// VPMULHUWYrm
    8U,	// VPMULHUWYrr
    88U,	// VPMULHUWrm
    8U,	// VPMULHUWrr
    104U,	// VPMULHWYrm
    8U,	// VPMULHWYrr
    88U,	// VPMULHWrm
    8U,	// VPMULHWrr
    104U,	// VPMULLDYrm
    8U,	// VPMULLDYrr
    96U,	// VPMULLDZrm
    784U,	// VPMULLDZrmb
    16682U,	// VPMULLDZrmbk
    117002U,	// VPMULLDZrmbkz
    20778U,	// VPMULLDZrmk
    22794U,	// VPMULLDZrmkz
    8U,	// VPMULLDZrr
    298U,	// VPMULLDZrrk
    8458U,	// VPMULLDZrrkz
    88U,	// VPMULLDrm
    8U,	// VPMULLDrr
    104U,	// VPMULLWYrm
    8U,	// VPMULLWYrr
    88U,	// VPMULLWrm
    8U,	// VPMULLWrr
    104U,	// VPMULUDQYrm
    8U,	// VPMULUDQYrr
    96U,	// VPMULUDQZrm
    536U,	// VPMULUDQZrmb
    92426U,	// VPMULUDQZrmbk
    92426U,	// VPMULUDQZrmbkz
    22794U,	// VPMULUDQZrmk
    22794U,	// VPMULUDQZrmkz
    8U,	// VPMULUDQZrr
    8458U,	// VPMULUDQZrrk
    8458U,	// VPMULUDQZrrkz
    88U,	// VPMULUDQrm
    8U,	// VPMULUDQrr
    96U,	// VPORDZrm
    784U,	// VPORDZrmb
    16682U,	// VPORDZrmbk
    117002U,	// VPORDZrmbkz
    20778U,	// VPORDZrmk
    22794U,	// VPORDZrmkz
    8U,	// VPORDZrr
    298U,	// VPORDZrrk
    8458U,	// VPORDZrrkz
    96U,	// VPORQZrm
    536U,	// VPORQZrmb
    24874U,	// VPORQZrmbk
    92426U,	// VPORQZrmbkz
    20778U,	// VPORQZrmk
    22794U,	// VPORQZrmkz
    8U,	// VPORQZrr
    298U,	// VPORQZrrk
    8458U,	// VPORQZrrkz
    104U,	// VPORYrm
    8U,	// VPORYrr
    88U,	// VPORrm
    8U,	// VPORrr
    2392U,	// VPPERMmr
    28936U,	// VPPERMrm
    8456U,	// VPPERMrr
    0U,	// VPROTBmi
    0U,	// VPROTBmr
    8U,	// VPROTBri
    88U,	// VPROTBrm
    8U,	// VPROTBrr
    0U,	// VPROTDmi
    0U,	// VPROTDmr
    8U,	// VPROTDri
    88U,	// VPROTDrm
    8U,	// VPROTDrr
    0U,	// VPROTQmi
    0U,	// VPROTQmr
    8U,	// VPROTQri
    88U,	// VPROTQrm
    8U,	// VPROTQrr
    0U,	// VPROTWmi
    0U,	// VPROTWmr
    8U,	// VPROTWri
    88U,	// VPROTWrm
    8U,	// VPROTWrr
    104U,	// VPSADBWYrm
    8U,	// VPSADBWYrr
    88U,	// VPSADBWrm
    8U,	// VPSADBWrr
    0U,	// VPSCATTERDDZmr
    0U,	// VPSCATTERDQZmr
    0U,	// VPSCATTERQDZmr
    0U,	// VPSCATTERQQZmr
    0U,	// VPSHABmr
    88U,	// VPSHABrm
    8U,	// VPSHABrr
    0U,	// VPSHADmr
    88U,	// VPSHADrm
    8U,	// VPSHADrr
    0U,	// VPSHAQmr
    88U,	// VPSHAQrm
    8U,	// VPSHAQrr
    0U,	// VPSHAWmr
    88U,	// VPSHAWrm
    8U,	// VPSHAWrr
    0U,	// VPSHLBmr
    88U,	// VPSHLBrm
    8U,	// VPSHLBrr
    0U,	// VPSHLDmr
    88U,	// VPSHLDrm
    8U,	// VPSHLDrr
    0U,	// VPSHLQmr
    88U,	// VPSHLQrm
    8U,	// VPSHLQrr
    0U,	// VPSHLWmr
    88U,	// VPSHLWrm
    8U,	// VPSHLWrr
    104U,	// VPSHUFBYrm
    8U,	// VPSHUFBYrr
    88U,	// VPSHUFBrm
    8U,	// VPSHUFBrr
    0U,	// VPSHUFDYmi
    8U,	// VPSHUFDYri
    0U,	// VPSHUFDZmi
    8U,	// VPSHUFDZri
    0U,	// VPSHUFDmi
    8U,	// VPSHUFDri
    0U,	// VPSHUFHWYmi
    8U,	// VPSHUFHWYri
    0U,	// VPSHUFHWmi
    8U,	// VPSHUFHWri
    0U,	// VPSHUFLWYmi
    8U,	// VPSHUFLWYri
    0U,	// VPSHUFLWmi
    8U,	// VPSHUFLWri
    104U,	// VPSIGNBYrm
    8U,	// VPSIGNBYrr
    88U,	// VPSIGNBrm
    8U,	// VPSIGNBrr
    104U,	// VPSIGNDYrm
    8U,	// VPSIGNDYrr
    88U,	// VPSIGNDrm
    8U,	// VPSIGNDrr
    104U,	// VPSIGNWYrm
    8U,	// VPSIGNWYrr
    88U,	// VPSIGNWrm
    8U,	// VPSIGNWrr
    8U,	// VPSLLDQYri
    8U,	// VPSLLDQri
    8U,	// VPSLLDYri
    88U,	// VPSLLDYrm
    8U,	// VPSLLDYrr
    0U,	// VPSLLDZmi
    2402U,	// VPSLLDZmik
    8U,	// VPSLLDZri
    8458U,	// VPSLLDZrik
    88U,	// VPSLLDZrm
    28938U,	// VPSLLDZrmk
    8U,	// VPSLLDZrr
    8458U,	// VPSLLDZrrk
    8U,	// VPSLLDri
    88U,	// VPSLLDrm
    8U,	// VPSLLDrr
    8U,	// VPSLLQYri
    88U,	// VPSLLQYrm
    8U,	// VPSLLQYrr
    0U,	// VPSLLQZmi
    2402U,	// VPSLLQZmik
    8U,	// VPSLLQZri
    8458U,	// VPSLLQZrik
    88U,	// VPSLLQZrm
    28938U,	// VPSLLQZrmk
    8U,	// VPSLLQZrr
    8458U,	// VPSLLQZrrk
    8U,	// VPSLLQri
    88U,	// VPSLLQrm
    8U,	// VPSLLQrr
    104U,	// VPSLLVDYrm
    8U,	// VPSLLVDYrr
    96U,	// VPSLLVDZrm
    8U,	// VPSLLVDZrr
    88U,	// VPSLLVDrm
    8U,	// VPSLLVDrr
    104U,	// VPSLLVQYrm
    8U,	// VPSLLVQYrr
    96U,	// VPSLLVQZrm
    8U,	// VPSLLVQZrr
    88U,	// VPSLLVQrm
    8U,	// VPSLLVQrr
    8U,	// VPSLLWYri
    88U,	// VPSLLWYrm
    8U,	// VPSLLWYrr
    8U,	// VPSLLWri
    88U,	// VPSLLWrm
    8U,	// VPSLLWrr
    8U,	// VPSRADYri
    88U,	// VPSRADYrm
    8U,	// VPSRADYrr
    0U,	// VPSRADZmi
    2402U,	// VPSRADZmik
    8U,	// VPSRADZri
    8458U,	// VPSRADZrik
    88U,	// VPSRADZrm
    28938U,	// VPSRADZrmk
    8U,	// VPSRADZrr
    8458U,	// VPSRADZrrk
    8U,	// VPSRADri
    88U,	// VPSRADrm
    8U,	// VPSRADrr
    0U,	// VPSRAQZmi
    2402U,	// VPSRAQZmik
    8U,	// VPSRAQZri
    8458U,	// VPSRAQZrik
    88U,	// VPSRAQZrm
    28938U,	// VPSRAQZrmk
    8U,	// VPSRAQZrr
    8458U,	// VPSRAQZrrk
    104U,	// VPSRAVDYrm
    8U,	// VPSRAVDYrr
    96U,	// VPSRAVDZrm
    8U,	// VPSRAVDZrr
    88U,	// VPSRAVDrm
    8U,	// VPSRAVDrr
    96U,	// VPSRAVQZrm
    8U,	// VPSRAVQZrr
    8U,	// VPSRAWYri
    88U,	// VPSRAWYrm
    8U,	// VPSRAWYrr
    8U,	// VPSRAWri
    88U,	// VPSRAWrm
    8U,	// VPSRAWrr
    8U,	// VPSRLDQYri
    8U,	// VPSRLDQri
    8U,	// VPSRLDYri
    88U,	// VPSRLDYrm
    8U,	// VPSRLDYrr
    0U,	// VPSRLDZmi
    2402U,	// VPSRLDZmik
    8U,	// VPSRLDZri
    8458U,	// VPSRLDZrik
    88U,	// VPSRLDZrm
    28938U,	// VPSRLDZrmk
    8U,	// VPSRLDZrr
    8458U,	// VPSRLDZrrk
    8U,	// VPSRLDri
    88U,	// VPSRLDrm
    8U,	// VPSRLDrr
    8U,	// VPSRLQYri
    88U,	// VPSRLQYrm
    8U,	// VPSRLQYrr
    0U,	// VPSRLQZmi
    2402U,	// VPSRLQZmik
    8U,	// VPSRLQZri
    8458U,	// VPSRLQZrik
    88U,	// VPSRLQZrm
    28938U,	// VPSRLQZrmk
    8U,	// VPSRLQZrr
    8458U,	// VPSRLQZrrk
    8U,	// VPSRLQri
    88U,	// VPSRLQrm
    8U,	// VPSRLQrr
    104U,	// VPSRLVDYrm
    8U,	// VPSRLVDYrr
    96U,	// VPSRLVDZrm
    8U,	// VPSRLVDZrr
    88U,	// VPSRLVDrm
    8U,	// VPSRLVDrr
    104U,	// VPSRLVQYrm
    8U,	// VPSRLVQYrr
    96U,	// VPSRLVQZrm
    8U,	// VPSRLVQZrr
    88U,	// VPSRLVQrm
    8U,	// VPSRLVQrr
    8U,	// VPSRLWYri
    88U,	// VPSRLWYrm
    8U,	// VPSRLWYrr
    8U,	// VPSRLWri
    88U,	// VPSRLWrm
    8U,	// VPSRLWrr
    104U,	// VPSUBBYrm
    8U,	// VPSUBBYrr
    88U,	// VPSUBBrm
    8U,	// VPSUBBrr
    104U,	// VPSUBDYrm
    8U,	// VPSUBDYrr
    96U,	// VPSUBDZrm
    784U,	// VPSUBDZrmb
    16682U,	// VPSUBDZrmbk
    117002U,	// VPSUBDZrmbkz
    20778U,	// VPSUBDZrmk
    22794U,	// VPSUBDZrmkz
    8U,	// VPSUBDZrr
    298U,	// VPSUBDZrrk
    8458U,	// VPSUBDZrrkz
    88U,	// VPSUBDrm
    8U,	// VPSUBDrr
    104U,	// VPSUBQYrm
    8U,	// VPSUBQYrr
    96U,	// VPSUBQZrm
    536U,	// VPSUBQZrmb
    24874U,	// VPSUBQZrmbk
    92426U,	// VPSUBQZrmbkz
    20778U,	// VPSUBQZrmk
    22794U,	// VPSUBQZrmkz
    8U,	// VPSUBQZrr
    298U,	// VPSUBQZrrk
    8458U,	// VPSUBQZrrkz
    88U,	// VPSUBQrm
    8U,	// VPSUBQrr
    104U,	// VPSUBSBYrm
    8U,	// VPSUBSBYrr
    88U,	// VPSUBSBrm
    8U,	// VPSUBSBrr
    104U,	// VPSUBSWYrm
    8U,	// VPSUBSWYrr
    88U,	// VPSUBSWrm
    8U,	// VPSUBSWrr
    104U,	// VPSUBUSBYrm
    8U,	// VPSUBUSBYrr
    88U,	// VPSUBUSBrm
    8U,	// VPSUBUSBrr
    104U,	// VPSUBUSWYrm
    8U,	// VPSUBUSWYrr
    88U,	// VPSUBUSWrm
    8U,	// VPSUBUSWrr
    104U,	// VPSUBWYrm
    8U,	// VPSUBWYrr
    88U,	// VPSUBWrm
    8U,	// VPSUBWrr
    72U,	// VPTESTMDZrm
    8U,	// VPTESTMDZrr
    72U,	// VPTESTMQZrm
    8U,	// VPTESTMQZrr
    72U,	// VPTESTNMDZrm
    8U,	// VPTESTNMDZrr
    72U,	// VPTESTNMQZrm
    8U,	// VPTESTNMQZrr
    0U,	// VPTESTYrm
    0U,	// VPTESTYrr
    0U,	// VPTESTrm
    0U,	// VPTESTrr
    104U,	// VPUNPCKHBWYrm
    8U,	// VPUNPCKHBWYrr
    88U,	// VPUNPCKHBWrm
    8U,	// VPUNPCKHBWrr
    104U,	// VPUNPCKHDQYrm
    8U,	// VPUNPCKHDQYrr
    96U,	// VPUNPCKHDQZrm
    8U,	// VPUNPCKHDQZrr
    88U,	// VPUNPCKHDQrm
    8U,	// VPUNPCKHDQrr
    104U,	// VPUNPCKHQDQYrm
    8U,	// VPUNPCKHQDQYrr
    96U,	// VPUNPCKHQDQZrm
    8U,	// VPUNPCKHQDQZrr
    88U,	// VPUNPCKHQDQrm
    8U,	// VPUNPCKHQDQrr
    104U,	// VPUNPCKHWDYrm
    8U,	// VPUNPCKHWDYrr
    88U,	// VPUNPCKHWDrm
    8U,	// VPUNPCKHWDrr
    104U,	// VPUNPCKLBWYrm
    8U,	// VPUNPCKLBWYrr
    88U,	// VPUNPCKLBWrm
    8U,	// VPUNPCKLBWrr
    104U,	// VPUNPCKLDQYrm
    8U,	// VPUNPCKLDQYrr
    96U,	// VPUNPCKLDQZrm
    8U,	// VPUNPCKLDQZrr
    88U,	// VPUNPCKLDQrm
    8U,	// VPUNPCKLDQrr
    104U,	// VPUNPCKLQDQYrm
    8U,	// VPUNPCKLQDQYrr
    96U,	// VPUNPCKLQDQZrm
    8U,	// VPUNPCKLQDQZrr
    88U,	// VPUNPCKLQDQrm
    8U,	// VPUNPCKLQDQrr
    104U,	// VPUNPCKLWDYrm
    8U,	// VPUNPCKLWDYrr
    88U,	// VPUNPCKLWDrm
    8U,	// VPUNPCKLWDrr
    96U,	// VPXORDZrm
    784U,	// VPXORDZrmb
    16682U,	// VPXORDZrmbk
    117002U,	// VPXORDZrmbkz
    20778U,	// VPXORDZrmk
    22794U,	// VPXORDZrmkz
    8U,	// VPXORDZrr
    298U,	// VPXORDZrrk
    8458U,	// VPXORDZrrkz
    96U,	// VPXORQZrm
    536U,	// VPXORQZrmb
    24874U,	// VPXORQZrmbk
    92426U,	// VPXORQZrmbkz
    20778U,	// VPXORQZrmk
    22794U,	// VPXORQZrmkz
    8U,	// VPXORQZrr
    298U,	// VPXORQZrrk
    8458U,	// VPXORQZrrkz
    104U,	// VPXORYrm
    8U,	// VPXORYrr
    88U,	// VPXORrm
    8U,	// VPXORrr
    0U,	// VRCP14PDZm
    0U,	// VRCP14PDZr
    0U,	// VRCP14PSZm
    0U,	// VRCP14PSZr
    48U,	// VRCP14SDrm
    8U,	// VRCP14SDrr
    56U,	// VRCP14SSrm
    8U,	// VRCP14SSrr
    0U,	// VRCP28PDZm
    0U,	// VRCP28PDZr
    3U,	// VRCP28PDZrb
    0U,	// VRCP28PSZm
    0U,	// VRCP28PSZr
    3U,	// VRCP28PSZrb
    48U,	// VRCP28SDrm
    8U,	// VRCP28SDrr
    1544U,	// VRCP28SDrrb
    56U,	// VRCP28SSrm
    8U,	// VRCP28SSrr
    1544U,	// VRCP28SSrrb
    0U,	// VRCPPSYm
    0U,	// VRCPPSYm_Int
    0U,	// VRCPPSYr
    0U,	// VRCPPSYr_Int
    0U,	// VRCPPSm
    0U,	// VRCPPSm_Int
    0U,	// VRCPPSr
    0U,	// VRCPPSr_Int
    56U,	// VRCPSSm
    56U,	// VRCPSSm_Int
    8U,	// VRCPSSr
    0U,	// VRNDSCALEPDZm
    8U,	// VRNDSCALEPDZr
    0U,	// VRNDSCALEPSZm
    8U,	// VRNDSCALEPSZr
    48U,	// VRNDSCALESDm
    8U,	// VRNDSCALESDr
    56U,	// VRNDSCALESSm
    8U,	// VRNDSCALESSr
    0U,	// VROUNDPDm
    8U,	// VROUNDPDr
    0U,	// VROUNDPSm
    8U,	// VROUNDPSr
    2352U,	// VROUNDSDm
    8456U,	// VROUNDSDr
    8456U,	// VROUNDSDr_Int
    2360U,	// VROUNDSSm
    8456U,	// VROUNDSSr
    8456U,	// VROUNDSSr_Int
    0U,	// VROUNDYPDm
    8U,	// VROUNDYPDr
    0U,	// VROUNDYPSm
    8U,	// VROUNDYPSr
    0U,	// VRSQRT14PDZm
    0U,	// VRSQRT14PDZr
    0U,	// VRSQRT14PSZm
    0U,	// VRSQRT14PSZr
    48U,	// VRSQRT14SDrm
    8U,	// VRSQRT14SDrr
    56U,	// VRSQRT14SSrm
    8U,	// VRSQRT14SSrr
    0U,	// VRSQRT28PDZm
    0U,	// VRSQRT28PDZr
    3U,	// VRSQRT28PDZrb
    0U,	// VRSQRT28PSZm
    0U,	// VRSQRT28PSZr
    3U,	// VRSQRT28PSZrb
    48U,	// VRSQRT28SDrm
    8U,	// VRSQRT28SDrr
    1544U,	// VRSQRT28SDrrb
    56U,	// VRSQRT28SSrm
    8U,	// VRSQRT28SSrr
    1544U,	// VRSQRT28SSrrb
    0U,	// VRSQRTPSYm
    0U,	// VRSQRTPSYm_Int
    0U,	// VRSQRTPSYr
    0U,	// VRSQRTPSYr_Int
    0U,	// VRSQRTPSm
    0U,	// VRSQRTPSm_Int
    0U,	// VRSQRTPSr
    0U,	// VRSQRTPSr_Int
    56U,	// VRSQRTSSm
    56U,	// VRSQRTSSm_Int
    8U,	// VRSQRTSSr
    0U,	// VSCATTERDPDZmr
    0U,	// VSCATTERDPSZmr
    0U,	// VSCATTERPF0DPDm
    0U,	// VSCATTERPF0DPSm
    0U,	// VSCATTERPF0QPDm
    0U,	// VSCATTERPF0QPSm
    0U,	// VSCATTERPF1DPDm
    0U,	// VSCATTERPF1DPSm
    0U,	// VSCATTERPF1QPDm
    0U,	// VSCATTERPF1QPSm
    0U,	// VSCATTERQPDZmr
    0U,	// VSCATTERQPSZmr
    2368U,	// VSHUFPDYrmi
    8456U,	// VSHUFPDYrri
    2376U,	// VSHUFPDZrmi
    8456U,	// VSHUFPDZrri
    2384U,	// VSHUFPDrmi
    8456U,	// VSHUFPDrri
    2368U,	// VSHUFPSYrmi
    8456U,	// VSHUFPSYrri
    2376U,	// VSHUFPSZrmi
    8456U,	// VSHUFPSZrri
    2384U,	// VSHUFPSrmi
    8456U,	// VSHUFPSrri
    0U,	// VSQRTPDYm
    0U,	// VSQRTPDYr
    0U,	// VSQRTPDZrm
    0U,	// VSQRTPDZrr
    0U,	// VSQRTPDm
    0U,	// VSQRTPDr
    0U,	// VSQRTPSYm
    0U,	// VSQRTPSYr
    0U,	// VSQRTPSZrm
    0U,	// VSQRTPSZrr
    0U,	// VSQRTPSm
    0U,	// VSQRTPSr
    48U,	// VSQRTSDZm
    48U,	// VSQRTSDZm_Int
    8U,	// VSQRTSDZr
    8U,	// VSQRTSDZr_Int
    48U,	// VSQRTSDm
    48U,	// VSQRTSDm_Int
    8U,	// VSQRTSDr
    56U,	// VSQRTSSZm
    56U,	// VSQRTSSZm_Int
    8U,	// VSQRTSSZr
    8U,	// VSQRTSSZr_Int
    56U,	// VSQRTSSm
    56U,	// VSQRTSSm_Int
    8U,	// VSQRTSSr
    0U,	// VSTMXCSR
    64U,	// VSUBPDYrm
    8U,	// VSUBPDYrr
    72U,	// VSUBPDZrm
    560U,	// VSUBPDZrmb
    69898U,	// VSUBPDZrmbk
    69898U,	// VSUBPDZrmbkz
    6410U,	// VSUBPDZrmk
    6410U,	// VSUBPDZrmkz
    8U,	// VSUBPDZrr
    8458U,	// VSUBPDZrrk
    8458U,	// VSUBPDZrrkz
    80U,	// VSUBPDrm
    8U,	// VSUBPDrr
    64U,	// VSUBPSYrm
    8U,	// VSUBPSYrr
    72U,	// VSUBPSZrm
    824U,	// VSUBPSZrmb
    108810U,	// VSUBPSZrmbk
    108810U,	// VSUBPSZrmbkz
    6410U,	// VSUBPSZrmk
    6410U,	// VSUBPSZrmkz
    8U,	// VSUBPSZrr
    8458U,	// VSUBPSZrrk
    8458U,	// VSUBPSZrrkz
    80U,	// VSUBPSrm
    8U,	// VSUBPSrr
    48U,	// VSUBSDZrm
    8U,	// VSUBSDZrr
    48U,	// VSUBSDrm
    48U,	// VSUBSDrm_Int
    8U,	// VSUBSDrr
    8U,	// VSUBSDrr_Int
    56U,	// VSUBSSZrm
    8U,	// VSUBSSZrr
    56U,	// VSUBSSrm
    56U,	// VSUBSSrm_Int
    8U,	// VSUBSSrr
    8U,	// VSUBSSrr_Int
    0U,	// VTESTPDYrm
    0U,	// VTESTPDYrr
    0U,	// VTESTPDrm
    0U,	// VTESTPDrr
    0U,	// VTESTPSYrm
    0U,	// VTESTPSYrr
    0U,	// VTESTPSrm
    0U,	// VTESTPSrr
    0U,	// VUCOMISDZrm
    0U,	// VUCOMISDZrr
    0U,	// VUCOMISDrm
    0U,	// VUCOMISDrr
    0U,	// VUCOMISSZrm
    0U,	// VUCOMISSZrr
    0U,	// VUCOMISSrm
    0U,	// VUCOMISSrr
    64U,	// VUNPCKHPDYrm
    8U,	// VUNPCKHPDYrr
    72U,	// VUNPCKHPDZrm
    8U,	// VUNPCKHPDZrr
    80U,	// VUNPCKHPDrm
    8U,	// VUNPCKHPDrr
    64U,	// VUNPCKHPSYrm
    8U,	// VUNPCKHPSYrr
    72U,	// VUNPCKHPSZrm
    8U,	// VUNPCKHPSZrr
    80U,	// VUNPCKHPSrm
    8U,	// VUNPCKHPSrr
    64U,	// VUNPCKLPDYrm
    8U,	// VUNPCKLPDYrr
    72U,	// VUNPCKLPDZrm
    8U,	// VUNPCKLPDZrr
    80U,	// VUNPCKLPDrm
    8U,	// VUNPCKLPDrr
    64U,	// VUNPCKLPSYrm
    8U,	// VUNPCKLPSYrr
    72U,	// VUNPCKLPSZrm
    8U,	// VUNPCKLPSZrr
    80U,	// VUNPCKLPSrm
    8U,	// VUNPCKLPSrr
    64U,	// VXORPDYrm
    8U,	// VXORPDYrr
    80U,	// VXORPDrm
    8U,	// VXORPDrr
    64U,	// VXORPSYrm
    8U,	// VXORPSYrr
    80U,	// VXORPSrm
    8U,	// VXORPSrr
    0U,	// VZEROALL
    0U,	// VZEROUPPER
    0U,	// V_SET0
    0U,	// V_SETALLONES
    0U,	// W64ALLOCA
    0U,	// WAIT
    0U,	// WBINVD
    0U,	// WIN_ALLOCA
    0U,	// WIN_FTOL_32
    0U,	// WIN_FTOL_64
    0U,	// WRFSBASE
    0U,	// WRFSBASE64
    0U,	// WRGSBASE
    0U,	// WRGSBASE64
    0U,	// WRMSR
    0U,	// XABORT
    0U,	// XACQUIRE_PREFIX
    0U,	// XADD16rm
    0U,	// XADD16rr
    0U,	// XADD32rm
    0U,	// XADD32rr
    0U,	// XADD64rm
    0U,	// XADD64rr
    0U,	// XADD8rm
    0U,	// XADD8rr
    0U,	// XBEGIN
    0U,	// XBEGIN_4
    0U,	// XCHG16ar
    0U,	// XCHG16rm
    0U,	// XCHG16rr
    0U,	// XCHG32ar
    0U,	// XCHG32ar64
    0U,	// XCHG32rm
    0U,	// XCHG32rr
    0U,	// XCHG64ar
    0U,	// XCHG64rm
    0U,	// XCHG64rr
    0U,	// XCHG8rm
    0U,	// XCHG8rr
    0U,	// XCH_F
    0U,	// XCRYPTCBC
    0U,	// XCRYPTCFB
    0U,	// XCRYPTCTR
    0U,	// XCRYPTECB
    0U,	// XCRYPTOFB
    0U,	// XEND
    0U,	// XGETBV
    0U,	// XLAT
    0U,	// XOR16i16
    0U,	// XOR16mi
    0U,	// XOR16mi8
    0U,	// XOR16mr
    0U,	// XOR16ri
    0U,	// XOR16ri8
    0U,	// XOR16rm
    0U,	// XOR16rr
    0U,	// XOR16rr_REV
    0U,	// XOR32i32
    0U,	// XOR32mi
    0U,	// XOR32mi8
    0U,	// XOR32mr
    0U,	// XOR32ri
    0U,	// XOR32ri8
    0U,	// XOR32rm
    0U,	// XOR32rr
    0U,	// XOR32rr_REV
    0U,	// XOR64i32
    0U,	// XOR64mi32
    0U,	// XOR64mi8
    0U,	// XOR64mr
    0U,	// XOR64ri32
    0U,	// XOR64ri8
    0U,	// XOR64rm
    0U,	// XOR64rr
    0U,	// XOR64rr_REV
    0U,	// XOR8i8
    0U,	// XOR8mi
    0U,	// XOR8mr
    0U,	// XOR8ri
    0U,	// XOR8ri8
    0U,	// XOR8rm
    0U,	// XOR8rr
    0U,	// XOR8rr_REV
    0U,	// XORPDrm
    0U,	// XORPDrr
    0U,	// XORPSrm
    0U,	// XORPSrr
    0U,	// XRELEASE_PREFIX
    0U,	// XRSTOR
    0U,	// XRSTOR64
    0U,	// XSAVE
    0U,	// XSAVE64
    0U,	// XSAVEOPT
    0U,	// XSAVEOPT64
    0U,	// XSETBV
    0U,	// XSHA1
    0U,	// XSHA256
    0U,	// XSTORE
    0U,	// XTEST
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'i', '3', '2', 'x', '4', 32, 9, 0,
  /* 18 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'i', '6', '4', 'x', '4', 32, 9, 0,
  /* 36 */ 'k', 'a', 'n', 'd', 'b', 32, 9, 0,
  /* 44 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'd', 'b', 32, 9, 0,
  /* 56 */ 'v', 'p', 'm', 'o', 'v', 's', 'd', 'b', 32, 9, 0,
  /* 67 */ 'v', 'p', 'm', 'o', 'v', 'd', 'b', 32, 9, 0,
  /* 77 */ 'k', 'a', 'n', 'd', 'n', 'b', 32, 9, 0,
  /* 86 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'q', 'b', 32, 9, 0,
  /* 98 */ 'v', 'p', 'm', 'o', 'v', 's', 'q', 'b', 32, 9, 0,
  /* 109 */ 'v', 'p', 'm', 'o', 'v', 'q', 'b', 32, 9, 0,
  /* 119 */ 'k', 'o', 'r', 'b', 32, 9, 0,
  /* 126 */ 'k', 'x', 'n', 'o', 'r', 'b', 32, 9, 0,
  /* 135 */ 'k', 'x', 'o', 'r', 'b', 32, 9, 0,
  /* 143 */ 'k', 'n', 'o', 't', 'b', 32, 9, 0,
  /* 151 */ 'k', 'm', 'o', 'v', 'b', 32, 9, 0,
  /* 159 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'd', 32, 9, 0,
  /* 170 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'd', 32, 9, 0,
  /* 181 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'm', 'w', '2', 'd', 32, 9, 0,
  /* 199 */ 'v', 'p', 's', 'r', 'a', 'd', 32, 9, 0,
  /* 208 */ 'v', 'p', 's', 'u', 'b', 'd', 32, 9, 0,
  /* 217 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'd', 32, 9, 0,
  /* 229 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'd', 32, 9, 0,
  /* 241 */ 'v', 'p', 'a', 'd', 'd', 'd', 32, 9, 0,
  /* 250 */ 'k', 'a', 'n', 'd', 'd', 32, 9, 0,
  /* 258 */ 'v', 'p', 'a', 'n', 'd', 'd', 32, 9, 0,
  /* 267 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'd', 32, 9, 0,
  /* 280 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'd', 32, 9, 0,
  /* 294 */ 'v', 'p', 's', 'h', 'u', 'f', 'd', 32, 9, 0,
  /* 304 */ 'v', 'p', 's', 'l', 'l', 'd', 32, 9, 0,
  /* 313 */ 'v', 'p', 'm', 'u', 'l', 'l', 'd', 32, 9, 0,
  /* 323 */ 'v', 'p', 's', 'r', 'l', 'd', 32, 9, 0,
  /* 332 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'm', 'd', 32, 9, 0,
  /* 344 */ 'v', 'p', 't', 'e', 's', 't', 'n', 'm', 'd', 32, 9, 0,
  /* 356 */ 'v', 'p', 'e', 'r', 'm', 'd', 32, 9, 0,
  /* 365 */ 'v', 'p', 't', 'e', 's', 't', 'm', 'd', 32, 9, 0,
  /* 376 */ 'k', 'a', 'n', 'd', 'n', 'd', 32, 9, 0,
  /* 385 */ 'v', 'p', 'a', 'n', 'd', 'n', 'd', 32, 9, 0,
  /* 395 */ 'v', 'a', 'l', 'i', 'g', 'n', 'd', 32, 9, 0,
  /* 405 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 422 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 436 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 451 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 468 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 482 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 497 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'p', 'd', 32, 9, 0,
  /* 509 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 'd', 32, 9, 0,
  /* 521 */ 'v', 'c', 'v', 't', 'u', 'd', 'q', '2', 'p', 'd', 32, 9, 0,
  /* 534 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'd', 32, 9, 0,
  /* 546 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'p', 'd', 32, 9, 0,
  /* 558 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 575 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 589 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 604 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 621 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 635 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 650 */ 'v', 'r', 'c', 'p', '1', '4', 'p', 'd', 32, 9, 0,
  /* 661 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 'p', 'd', 32, 9, 0,
  /* 674 */ 'v', 'r', 'c', 'p', '2', '8', 'p', 'd', 32, 9, 0,
  /* 685 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 'p', 'd', 32, 9, 0,
  /* 698 */ 'v', 's', 'u', 'b', 'p', 'd', 32, 9, 0,
  /* 707 */ 'v', 'a', 'd', 'd', 'p', 'd', 32, 9, 0,
  /* 716 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 'd', 32, 9, 0,
  /* 729 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'p', 'd', 32, 9, 0,
  /* 743 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 'p', 'd', 32, 9, 0,
  /* 757 */ 'v', 's', 'h', 'u', 'f', 'p', 'd', 32, 9, 0,
  /* 767 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 'd', 32, 9, 0,
  /* 779 */ 'v', 'm', 'u', 'l', 'p', 'd', 32, 9, 0,
  /* 788 */ 'v', 'b', 'l', 'e', 'n', 'd', 'm', 'p', 'd', 32, 9, 0,
  /* 800 */ 'v', 'p', 'e', 'r', 'm', 'p', 'd', 32, 9, 0,
  /* 810 */ 'v', 'm', 'i', 'n', 'p', 'd', 32, 9, 0,
  /* 819 */ 'v', 'c', 'm', 'p', 'p', 'd', 32, 9, 0,
  /* 828 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 'd', 32, 9, 0,
  /* 841 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'p', 'd', 32, 9, 0,
  /* 855 */ 'v', 'd', 'i', 'v', 'p', 'd', 32, 9, 0,
  /* 864 */ 'v', 'm', 'a', 'x', 'p', 'd', 32, 9, 0,
  /* 873 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'd', 32, 9, 0,
  /* 886 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'd', 32, 9, 0,
  /* 900 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'q', 'd', 32, 9, 0,
  /* 912 */ 'v', 'p', 'm', 'o', 'v', 's', 'q', 'd', 32, 9, 0,
  /* 923 */ 'v', 'p', 'm', 'o', 'v', 'q', 'd', 32, 9, 0,
  /* 933 */ 'k', 'o', 'r', 'd', 32, 9, 0,
  /* 940 */ 'k', 'x', 'n', 'o', 'r', 'd', 32, 9, 0,
  /* 949 */ 'v', 'p', 'o', 'r', 'd', 32, 9, 0,
  /* 957 */ 'k', 'x', 'o', 'r', 'd', 32, 9, 0,
  /* 965 */ 'v', 'p', 'x', 'o', 'r', 'd', 32, 9, 0,
  /* 974 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 'd', 32, 9, 0,
  /* 986 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 'd', 32, 9, 0,
  /* 999 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1013 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1028 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1042 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1057 */ 'v', 'r', 'c', 'p', '1', '4', 's', 'd', 32, 9, 0,
  /* 1068 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 's', 'd', 32, 9, 0,
  /* 1081 */ 'v', 'r', 'c', 'p', '2', '8', 's', 'd', 32, 9, 0,
  /* 1092 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 's', 'd', 32, 9, 0,
  /* 1105 */ 'v', 'p', 'a', 'b', 's', 'd', 32, 9, 0,
  /* 1114 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 's', 'd', 32, 9, 0,
  /* 1128 */ 'v', 'p', 'm', 'i', 'n', 's', 'd', 32, 9, 0,
  /* 1138 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 'd', 32, 9, 0,
  /* 1153 */ 'v', 'm', 'o', 'v', 's', 'd', 32, 9, 0,
  /* 1162 */ 'v', 'p', 'm', 'a', 'x', 's', 'd', 32, 9, 0,
  /* 1172 */ 'v', 'p', 'c', 'o', 'n', 'f', 'l', 'i', 'c', 't', 'd', 32, 9, 0,
  /* 1186 */ 'v', 'p', 'l', 'z', 'c', 'n', 't', 'd', 32, 9, 0,
  /* 1197 */ 'k', 'n', 'o', 't', 'd', 32, 9, 0,
  /* 1205 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'd', 32, 9, 0,
  /* 1220 */ 'v', 'p', 'm', 'i', 'n', 'u', 'd', 32, 9, 0,
  /* 1230 */ 'v', 'p', 'm', 'a', 'x', 'u', 'd', 32, 9, 0,
  /* 1240 */ 'v', 'p', 's', 'r', 'a', 'v', 'd', 32, 9, 0,
  /* 1250 */ 'v', 'p', 's', 'l', 'l', 'v', 'd', 32, 9, 0,
  /* 1260 */ 'v', 'p', 's', 'r', 'l', 'v', 'd', 32, 9, 0,
  /* 1270 */ 'k', 'm', 'o', 'v', 'd', 32, 9, 0,
  /* 1278 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'd', 32, 9, 0,
  /* 1290 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'd', 32, 9, 0,
  /* 1302 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'h', 32, 9, 0,
  /* 1314 */ 'v', 'c', 'v', 't', 't', 's', 'd', '2', 's', 'i', 32, 9, 0,
  /* 1327 */ 'v', 'c', 'v', 't', 's', 'd', '2', 's', 'i', 32, 9, 0,
  /* 1339 */ 'v', 'c', 'v', 't', 't', 's', 's', '2', 's', 'i', 32, 9, 0,
  /* 1352 */ 'v', 'c', 'v', 't', 's', 's', '2', 's', 'i', 32, 9, 0,
  /* 1364 */ 'v', 'c', 'v', 't', 't', 's', 'd', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1378 */ 'v', 'c', 'v', 't', 's', 'd', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1391 */ 'v', 'c', 'v', 't', 't', 's', 's', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1405 */ 'v', 'c', 'v', 't', 's', 's', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1418 */ 'v', 'm', 'o', 'v', 'd', 'd', 'u', 'p', 32, 9, 0,
  /* 1429 */ 'v', 'm', 'o', 'v', 's', 'h', 'd', 'u', 'p', 32, 9, 0,
  /* 1441 */ 'v', 'm', 'o', 'v', 's', 'l', 'd', 'u', 'p', 32, 9, 0,
  /* 1453 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'm', 'b', '2', 'q', 32, 9, 0,
  /* 1471 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'q', 32, 9, 0,
  /* 1482 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'q', 32, 9, 0,
  /* 1493 */ 'v', 'p', 's', 'r', 'a', 'q', 32, 9, 0,
  /* 1502 */ 'v', 'p', 's', 'u', 'b', 'q', 32, 9, 0,
  /* 1511 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'q', 32, 9, 0,
  /* 1523 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'q', 32, 9, 0,
  /* 1535 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 32, 9, 0,
  /* 1548 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 32, 9, 0,
  /* 1560 */ 'v', 'c', 'v', 't', 't', 'p', 's', '2', 'd', 'q', 32, 9, 0,
  /* 1573 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'd', 'q', 32, 9, 0,
  /* 1585 */ 'v', 'p', 'a', 'd', 'd', 'q', 32, 9, 0,
  /* 1594 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'd', 'q', 32, 9, 0,
  /* 1607 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'd', 'q', 32, 9, 0,
  /* 1620 */ 'v', 'p', 'm', 'u', 'l', 'd', 'q', 32, 9, 0,
  /* 1630 */ 'k', 'a', 'n', 'd', 'q', 32, 9, 0,
  /* 1638 */ 'v', 'p', 'a', 'n', 'd', 'q', 32, 9, 0,
  /* 1647 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'q', 'd', 'q', 32, 9, 0,
  /* 1661 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'q', 'd', 'q', 32, 9, 0,
  /* 1675 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'q', 32, 9, 0,
  /* 1688 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'q', 32, 9, 0,
  /* 1702 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1716 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1729 */ 'v', 'c', 'v', 't', 't', 'p', 's', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1743 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1756 */ 'v', 'p', 'm', 'u', 'l', 'u', 'd', 'q', 32, 9, 0,
  /* 1767 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'd', 'q', 32, 9, 0,
  /* 1779 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'd', 'q', 32, 9, 0,
  /* 1791 */ 'v', 'p', 's', 'l', 'l', 'q', 32, 9, 0,
  /* 1800 */ 'v', 'p', 's', 'r', 'l', 'q', 32, 9, 0,
  /* 1809 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'm', 'q', 32, 9, 0,
  /* 1821 */ 'v', 'p', 't', 'e', 's', 't', 'n', 'm', 'q', 32, 9, 0,
  /* 1833 */ 'v', 'p', 'e', 'r', 'm', 'q', 32, 9, 0,
  /* 1842 */ 'v', 'p', 't', 'e', 's', 't', 'm', 'q', 32, 9, 0,
  /* 1853 */ 'k', 'a', 'n', 'd', 'n', 'q', 32, 9, 0,
  /* 1862 */ 'v', 'p', 'a', 'n', 'd', 'n', 'q', 32, 9, 0,
  /* 1872 */ 'v', 'a', 'l', 'i', 'g', 'n', 'q', 32, 9, 0,
  /* 1882 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'q', 32, 9, 0,
  /* 1895 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'q', 32, 9, 0,
  /* 1909 */ 'k', 'o', 'r', 'q', 32, 9, 0,
  /* 1916 */ 'k', 'x', 'n', 'o', 'r', 'q', 32, 9, 0,
  /* 1925 */ 'v', 'p', 'o', 'r', 'q', 32, 9, 0,
  /* 1933 */ 'k', 'x', 'o', 'r', 'q', 32, 9, 0,
  /* 1941 */ 'v', 'p', 'x', 'o', 'r', 'q', 32, 9, 0,
  /* 1950 */ 'v', 'p', 'a', 'b', 's', 'q', 32, 9, 0,
  /* 1959 */ 'v', 'p', 'm', 'i', 'n', 's', 'q', 32, 9, 0,
  /* 1969 */ 'v', 'p', 'm', 'a', 'x', 's', 'q', 32, 9, 0,
  /* 1979 */ 'v', 'p', 'c', 'o', 'n', 'f', 'l', 'i', 'c', 't', 'q', 32, 9, 0,
  /* 1993 */ 'v', 'p', 'l', 'z', 'c', 'n', 't', 'q', 32, 9, 0,
  /* 2004 */ 'k', 'n', 'o', 't', 'q', 32, 9, 0,
  /* 2012 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'q', 32, 9, 0,
  /* 2027 */ 'v', 'p', 'm', 'i', 'n', 'u', 'q', 32, 9, 0,
  /* 2037 */ 'v', 'p', 'm', 'a', 'x', 'u', 'q', 32, 9, 0,
  /* 2047 */ 'v', 'p', 's', 'r', 'a', 'v', 'q', 32, 9, 0,
  /* 2057 */ 'v', 'p', 's', 'l', 'l', 'v', 'q', 32, 9, 0,
  /* 2067 */ 'v', 'p', 's', 'r', 'l', 'v', 'q', 32, 9, 0,
  /* 2077 */ 'k', 'm', 'o', 'v', 'q', 32, 9, 0,
  /* 2085 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'q', 32, 9, 0,
  /* 2097 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'q', 32, 9, 0,
  /* 2109 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2126 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2140 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2155 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2172 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2186 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2201 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 32, 9, 0,
  /* 2213 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'p', 's', 32, 9, 0,
  /* 2225 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 's', 32, 9, 0,
  /* 2237 */ 'v', 'c', 'v', 't', 'u', 'd', 'q', '2', 'p', 's', 32, 9, 0,
  /* 2250 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'p', 's', 32, 9, 0,
  /* 2262 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2279 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2293 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2308 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2325 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2339 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2354 */ 'v', 'r', 'c', 'p', '1', '4', 'p', 's', 32, 9, 0,
  /* 2365 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 'p', 's', 32, 9, 0,
  /* 2378 */ 'v', 'r', 'c', 'p', '2', '8', 'p', 's', 32, 9, 0,
  /* 2389 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 'p', 's', 32, 9, 0,
  /* 2402 */ 'v', 's', 'u', 'b', 'p', 's', 32, 9, 0,
  /* 2411 */ 'v', 'a', 'd', 'd', 'p', 's', 32, 9, 0,
  /* 2420 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 's', 32, 9, 0,
  /* 2433 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'p', 's', 32, 9, 0,
  /* 2447 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 'p', 's', 32, 9, 0,
  /* 2461 */ 'v', 's', 'h', 'u', 'f', 'p', 's', 32, 9, 0,
  /* 2471 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 's', 32, 9, 0,
  /* 2483 */ 'v', 'm', 'u', 'l', 'p', 's', 32, 9, 0,
  /* 2492 */ 'v', 'b', 'l', 'e', 'n', 'd', 'm', 'p', 's', 32, 9, 0,
  /* 2504 */ 'v', 'p', 'e', 'r', 'm', 'p', 's', 32, 9, 0,
  /* 2514 */ 'v', 'm', 'i', 'n', 'p', 's', 32, 9, 0,
  /* 2523 */ 'v', 'c', 'm', 'p', 'p', 's', 32, 9, 0,
  /* 2532 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 's', 32, 9, 0,
  /* 2545 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'p', 's', 32, 9, 0,
  /* 2559 */ 'v', 'd', 'i', 'v', 'p', 's', 32, 9, 0,
  /* 2568 */ 'v', 'm', 'a', 'x', 'p', 's', 32, 9, 0,
  /* 2577 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 's', 32, 9, 0,
  /* 2589 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 's', 32, 9, 0,
  /* 2602 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2616 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2631 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2645 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2660 */ 'v', 'r', 'c', 'p', '1', '4', 's', 's', 32, 9, 0,
  /* 2671 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 's', 's', 32, 9, 0,
  /* 2684 */ 'v', 'r', 'c', 'p', '2', '8', 's', 's', 32, 9, 0,
  /* 2695 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 's', 's', 32, 9, 0,
  /* 2708 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 's', 's', 32, 9, 0,
  /* 2722 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 's', 32, 9, 0,
  /* 2737 */ 'v', 'm', 'o', 'v', 's', 's', 32, 9, 0,
  /* 2746 */ 'k', 'u', 'n', 'p', 'c', 'k', 'b', 'w', 32, 9, 0,
  /* 2757 */ 'k', 'a', 'n', 'd', 'w', 32, 9, 0,
  /* 2765 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'd', 'w', 32, 9, 0,
  /* 2777 */ 'v', 'p', 'm', 'o', 'v', 's', 'd', 'w', 32, 9, 0,
  /* 2788 */ 'v', 'p', 'm', 'o', 'v', 'd', 'w', 32, 9, 0,
  /* 2798 */ 'k', 's', 'h', 'i', 'f', 't', 'l', 'w', 32, 9, 0,
  /* 2809 */ 'k', 'a', 'n', 'd', 'n', 'w', 32, 9, 0,
  /* 2818 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'q', 'w', 32, 9, 0,
  /* 2830 */ 'v', 'p', 'm', 'o', 'v', 's', 'q', 'w', 32, 9, 0,
  /* 2841 */ 'v', 'p', 'm', 'o', 'v', 'q', 'w', 32, 9, 0,
  /* 2851 */ 'k', 'o', 'r', 'w', 32, 9, 0,
  /* 2858 */ 'k', 'x', 'n', 'o', 'r', 'w', 32, 9, 0,
  /* 2867 */ 'k', 'x', 'o', 'r', 'w', 32, 9, 0,
  /* 2875 */ 'k', 's', 'h', 'i', 'f', 't', 'r', 'w', 32, 9, 0,
  /* 2886 */ 'k', 'n', 'o', 't', 'w', 32, 9, 0,
  /* 2894 */ 'k', 'o', 'r', 't', 'e', 's', 't', 'w', 32, 9, 0,
  /* 2905 */ 'k', 'm', 'o', 'v', 'w', 32, 9, 0,
  /* 2913 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 't', '0', 9, 0,
  /* 2925 */ 's', 'h', 'a', '1', 'm', 's', 'g', '1', 9, 0,
  /* 2935 */ 's', 'h', 'a', '2', '5', '6', 'm', 's', 'g', '1', 9, 0,
  /* 2947 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 't', '1', 9, 0,
  /* 2959 */ 'p', 'f', 'r', 'c', 'p', 'i', 't', '1', 9, 0,
  /* 2969 */ 'p', 'f', 'r', 's', 'q', 'i', 't', '1', 9, 0,
  /* 2979 */ 'v', 'm', 'o', 'v', 'd', 'q', 'a', '3', '2', 9, 0,
  /* 2990 */ 'c', 'r', 'c', '3', '2', 9, 0,
  /* 2997 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '3', '2', 9, 0,
  /* 3008 */ 's', 'h', 'a', '1', 'm', 's', 'g', '2', 9, 0,
  /* 3018 */ 's', 'h', 'a', '2', '5', '6', 'm', 's', 'g', '2', 9, 0,
  /* 3030 */ 's', 'h', 'a', '2', '5', '6', 'r', 'n', 'd', 's', '2', 9, 0,
  /* 3043 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 't', '2', 9, 0,
  /* 3055 */ 'p', 'f', 'r', 'c', 'p', 'i', 't', '2', 9, 0,
  /* 3065 */ 'v', 'm', 'o', 'v', 'd', 'q', 'a', '6', '4', 9, 0,
  /* 3076 */ 'f', 'x', 's', 'a', 'v', 'e', '6', '4', 9, 0,
  /* 3086 */ 'f', 'x', 'r', 's', 't', 'o', 'r', '6', '4', 9, 0,
  /* 3097 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', '6', '4', 9, 0,
  /* 3109 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '6', '4', 9, 0,
  /* 3120 */ 's', 'h', 'a', '1', 'r', 'n', 'd', 's', '4', 9, 0,
  /* 3131 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'f', '3', '2', 'x', '4', 9, 0,
  /* 3146 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'f', '3', '2', 'x', '4', 9, 0,
  /* 3160 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'i', '3', '2', 'x', '4', 9, 0,
  /* 3175 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'i', '3', '2', 'x', '4', 9, 0,
  /* 3189 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'f', '6', '4', 'x', '4', 9, 0,
  /* 3204 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'f', '6', '4', 'x', '4', 9, 0,
  /* 3218 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'i', '6', '4', 'x', '4', 9, 0,
  /* 3233 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'i', '6', '4', 'x', '4', 9, 0,
  /* 3247 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '1', '6', 9, 0,
  /* 3258 */ 'v', 'p', 'e', 'r', 'm', '2', 'f', '1', '2', '8', 9, 0,
  /* 3270 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'f', '1', '2', '8', 9, 0,
  /* 3284 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'f', '1', '2', '8', 9, 0,
  /* 3297 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'f', '1', '2', '8', 9, 0,
  /* 3313 */ 'v', 'p', 'e', 'r', 'm', '2', 'i', '1', '2', '8', 9, 0,
  /* 3325 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'i', '1', '2', '8', 9, 0,
  /* 3339 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'i', '1', '2', '8', 9, 0,
  /* 3352 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'i', '1', '2', '8', 9, 0,
  /* 3368 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '8', 9, 0,
  /* 3378 */ 'l', 'e', 'a', 9, 0,
  /* 3383 */ 'j', 'a', 9, 0,
  /* 3387 */ 'v', 'm', 'o', 'v', 'n', 't', 'd', 'q', 'a', 9, 0,
  /* 3398 */ 'v', 'm', 'o', 'v', 'd', 'q', 'a', 9, 0,
  /* 3407 */ 's', 'e', 't', 'a', 9, 0,
  /* 3413 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 'n', 't', 'a', 9, 0,
  /* 3426 */ 'c', 'm', 'o', 'v', 'a', 9, 0,
  /* 3433 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '1', '6', 'b', 9, 0,
  /* 3445 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '8', 'b', 9, 0,
  /* 3456 */ 'v', 'p', 's', 'h', 'a', 'b', 9, 0,
  /* 3464 */ 's', 'b', 'b', 9, 0,
  /* 3469 */ 'v', 'p', 's', 'u', 'b', 'b', 9, 0,
  /* 3477 */ 'v', 'p', 'a', 'd', 'd', 'b', 9, 0,
  /* 3485 */ 'v', 'p', 's', 'h', 'u', 'f', 'b', 9, 0,
  /* 3494 */ 'v', 'p', 'a', 'v', 'g', 'b', 9, 0,
  /* 3502 */ 'j', 'b', 9, 0,
  /* 3506 */ 'v', 'p', 'm', 'o', 'v', 'm', 's', 'k', 'b', 9, 0,
  /* 3517 */ 'v', 'p', 's', 'h', 'l', 'b', 9, 0,
  /* 3525 */ 'v', 'p', 'c', 'o', 'm', 'b', 9, 0,
  /* 3533 */ 'v', 'p', 's', 'i', 'g', 'n', 'b', 9, 0,
  /* 3542 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'b', 9, 0,
  /* 3552 */ 'v', 'p', 'i', 'n', 's', 'r', 'b', 9, 0,
  /* 3561 */ 'v', 'p', 'e', 'x', 't', 'r', 'b', 9, 0,
  /* 3570 */ 'v', 'p', 'a', 'b', 's', 'b', 9, 0,
  /* 3578 */ 'v', 'p', 's', 'u', 'b', 's', 'b', 9, 0,
  /* 3587 */ 'v', 'p', 'a', 'd', 'd', 's', 'b', 9, 0,
  /* 3596 */ 'v', 'p', 'm', 'i', 'n', 's', 'b', 9, 0,
  /* 3605 */ 's', 't', 'o', 's', 'b', 9, 0,
  /* 3612 */ 'c', 'm', 'p', 's', 'b', 9, 0,
  /* 3619 */ 'v', 'p', 's', 'u', 'b', 'u', 's', 'b', 9, 0,
  /* 3629 */ 'v', 'p', 'a', 'd', 'd', 'u', 's', 'b', 9, 0,
  /* 3639 */ 'p', 'a', 'v', 'g', 'u', 's', 'b', 9, 0,
  /* 3648 */ 'm', 'o', 'v', 's', 'b', 9, 0,
  /* 3655 */ 'v', 'p', 'm', 'a', 'x', 's', 'b', 9, 0,
  /* 3664 */ 's', 'e', 't', 'b', 9, 0,
  /* 3670 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'b', 9, 0,
  /* 3680 */ 'v', 'p', 'r', 'o', 't', 'b', 9, 0,
  /* 3688 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'b', 9, 0,
  /* 3702 */ 'v', 'p', 'c', 'o', 'm', 'u', 'b', 9, 0,
  /* 3711 */ 'v', 'p', 'm', 'i', 'n', 'u', 'b', 9, 0,
  /* 3720 */ 'p', 'f', 's', 'u', 'b', 9, 0,
  /* 3727 */ 'f', 'i', 's', 'u', 'b', 9, 0,
  /* 3734 */ 'v', 'p', 'm', 'a', 'x', 'u', 'b', 9, 0,
  /* 3743 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'v', 'b', 9, 0,
  /* 3754 */ 'c', 'm', 'o', 'v', 'b', 9, 0,
  /* 3761 */ 'v', 'p', 'a', 'c', 'k', 's', 's', 'w', 'b', 9, 0,
  /* 3772 */ 'v', 'p', 'a', 'c', 'k', 'u', 's', 'w', 'b', 9, 0,
  /* 3783 */ 'p', 'f', 'a', 'c', 'c', 9, 0,
  /* 3790 */ 'p', 'f', 'n', 'a', 'c', 'c', 9, 0,
  /* 3798 */ 'p', 'f', 'p', 'n', 'a', 'c', 'c', 9, 0,
  /* 3807 */ 'a', 'd', 'c', 9, 0,
  /* 3812 */ 'v', 'a', 'e', 's', 'd', 'e', 'c', 9, 0,
  /* 3821 */ 'b', 'l', 'c', 'i', 'c', 9, 0,
  /* 3828 */ 'b', 'l', 's', 'i', 'c', 9, 0,
  /* 3835 */ 't', '1', 'm', 's', 'k', 'c', 9, 0,
  /* 3843 */ 'v', 'a', 'e', 's', 'i', 'm', 'c', 9, 0,
  /* 3852 */ 'v', 'a', 'e', 's', 'e', 'n', 'c', 9, 0,
  /* 3861 */ 'i', 'n', 'c', 9, 0,
  /* 3866 */ 'b', 't', 'c', 9, 0,
  /* 3871 */ 'a', 'a', 'd', 9, 0,
  /* 3876 */ 'v', 'm', 'r', 'e', 'a', 'd', 9, 0,
  /* 3884 */ 'v', 'p', 's', 'h', 'a', 'd', 9, 0,
  /* 3892 */ 'v', 'p', 's', 'r', 'a', 'd', 9, 0,
  /* 3900 */ 'v', 'p', 'h', 'a', 'd', 'd', 'b', 'd', 9, 0,
  /* 3910 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'b', 'd', 9, 0,
  /* 3921 */ 'v', 'p', 'h', 's', 'u', 'b', 'd', 9, 0,
  /* 3930 */ 'v', 'p', 's', 'u', 'b', 'd', 9, 0,
  /* 3938 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'd', 9, 0,
  /* 3949 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'd', 9, 0,
  /* 3960 */ 'p', 'f', 'a', 'd', 'd', 9, 0,
  /* 3967 */ 'f', 'i', 'a', 'd', 'd', 9, 0,
  /* 3974 */ 'x', 'a', 'd', 'd', 9, 0,
  /* 3980 */ 'v', 'p', 'h', 'a', 'd', 'd', 'd', 9, 0,
  /* 3989 */ 'v', 'p', 'a', 'd', 'd', 'd', 9, 0,
  /* 3997 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'd', 9, 0,
  /* 4007 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'd', 9, 0,
  /* 4019 */ 'v', 'p', 'm', 'a', 'c', 's', 'd', 'd', 9, 0,
  /* 4029 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'd', 'd', 9, 0,
  /* 4040 */ 'r', 'd', 's', 'e', 'e', 'd', 9, 0,
  /* 4048 */ 'p', 'i', '2', 'f', 'd', 9, 0,
  /* 4055 */ 'v', 'p', 's', 'h', 'u', 'f', 'd', 9, 0,
  /* 4064 */ 'p', 'f', '2', 'i', 'd', 9, 0,
  /* 4071 */ 'i', 'n', 'v', 'p', 'c', 'i', 'd', 9, 0,
  /* 4080 */ 'i', 'n', 'v', 'v', 'p', 'i', 'd', 9, 0,
  /* 4089 */ 'f', 'b', 'l', 'd', 9, 0,
  /* 4095 */ 'f', 'l', 'd', 9, 0,
  /* 4100 */ 'v', 'p', 's', 'h', 'l', 'd', 9, 0,
  /* 4108 */ 'f', 'i', 'l', 'd', 9, 0,
  /* 4114 */ 'v', 'p', 's', 'l', 'l', 'd', 9, 0,
  /* 4122 */ 'v', 'p', 'm', 'u', 'l', 'l', 'd', 9, 0,
  /* 4131 */ 'v', 'p', 's', 'r', 'l', 'd', 9, 0,
  /* 4139 */ 'v', 'm', 'p', 't', 'r', 'l', 'd', 9, 0,
  /* 4148 */ 'v', 'p', 'c', 'o', 'm', 'd', 9, 0,
  /* 4156 */ 'v', 'p', 'e', 'r', 'm', 'd', 9, 0,
  /* 4164 */ 'v', 'p', 'a', 'n', 'd', 9, 0,
  /* 4171 */ 'r', 'd', 'r', 'a', 'n', 'd', 9, 0,
  /* 4179 */ 'v', 'p', 's', 'i', 'g', 'n', 'd', 9, 0,
  /* 4188 */ 'b', 'o', 'u', 'n', 'd', 9, 0,
  /* 4195 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4211 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4224 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4238 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4254 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4267 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4281 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4297 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4310 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4324 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4340 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4353 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4367 */ 'c', 'v', 't', 'p', 'i', '2', 'p', 'd', 9, 0,
  /* 4377 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', '2', 'p', 'd', 9, 0,
  /* 4389 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 'd', 9, 0,
  /* 4400 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'd', 9, 0,
  /* 4411 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4427 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4440 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4454 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4470 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4483 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4497 */ 'v', 'm', 'o', 'v', 'a', 'p', 'd', 9, 0,
  /* 4506 */ 'p', 's', 'w', 'a', 'p', 'd', 9, 0,
  /* 4514 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4527 */ 'v', 'a', 'd', 'd', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4538 */ 'v', 'h', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4547 */ 'v', 'f', 'm', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4557 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4568 */ 'v', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4576 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4589 */ 'v', 'h', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4598 */ 'v', 'f', 'm', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4608 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4619 */ 'v', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4627 */ 'v', 'a', 'n', 'd', 'p', 'd', 9, 0,
  /* 4635 */ 'v', 'b', 'l', 'e', 'n', 'd', 'p', 'd', 9, 0,
  /* 4645 */ 'v', 'r', 'o', 'u', 'n', 'd', 'p', 'd', 9, 0,
  /* 4655 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 'd', 9, 0,
  /* 4667 */ 'v', 's', 'h', 'u', 'f', 'p', 'd', 9, 0,
  /* 4676 */ 'v', 'u', 'n', 'p', 'c', 'k', 'h', 'p', 'd', 9, 0,
  /* 4687 */ 'v', 'm', 'o', 'v', 'h', 'p', 'd', 9, 0,
  /* 4696 */ 'v', 'm', 'o', 'v', 'm', 's', 'k', 'p', 'd', 9, 0,
  /* 4707 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 'd', 9, 0,
  /* 4718 */ 'v', 'u', 'n', 'p', 'c', 'k', 'l', 'p', 'd', 9, 0,
  /* 4729 */ 'v', 'm', 'u', 'l', 'p', 'd', 9, 0,
  /* 4737 */ 'v', 'm', 'o', 'v', 'l', 'p', 'd', 9, 0,
  /* 4746 */ 'v', 'p', 'c', 'm', 'p', 'd', 9, 0,
  /* 4754 */ 'v', 'p', 'e', 'r', 'm', 'p', 'd', 9, 0,
  /* 4763 */ 'v', 'a', 'n', 'd', 'n', 'p', 'd', 9, 0,
  /* 4772 */ 'v', 'm', 'i', 'n', 'p', 'd', 9, 0,
  /* 4780 */ 'v', 'd', 'p', 'p', 'd', 9, 0,
  /* 4787 */ 'v', 'c', 'm', 'p', 'p', 'd', 9, 0,
  /* 4795 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 'd', 9, 0,
  /* 4807 */ 'v', 'o', 'r', 'p', 'd', 9, 0,
  /* 4814 */ 'v', 'x', 'o', 'r', 'p', 'd', 9, 0,
  /* 4822 */ 'v', 'm', 'o', 'v', 'n', 't', 'p', 'd', 9, 0,
  /* 4832 */ 'v', 's', 'q', 'r', 't', 'p', 'd', 9, 0,
  /* 4841 */ 'v', 't', 'e', 's', 't', 'p', 'd', 9, 0,
  /* 4850 */ 'v', 'm', 'o', 'v', 'u', 'p', 'd', 9, 0,
  /* 4859 */ 'v', 'b', 'l', 'e', 'n', 'd', 'v', 'p', 'd', 9, 0,
  /* 4870 */ 'v', 'd', 'i', 'v', 'p', 'd', 9, 0,
  /* 4878 */ 'v', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'p', 'd', 9, 0,
  /* 4890 */ 'v', 'm', 'a', 'x', 'p', 'd', 9, 0,
  /* 4898 */ 'v', 'f', 'r', 'c', 'z', 'p', 'd', 9, 0,
  /* 4907 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'd', 9, 0,
  /* 4917 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'd', 9, 0,
  /* 4929 */ 's', 'h', 'r', 'd', 9, 0,
  /* 4935 */ 'v', 'p', 'i', 'n', 's', 'r', 'd', 9, 0,
  /* 4944 */ 'v', 'p', 'e', 'x', 't', 'r', 'd', 9, 0,
  /* 4953 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 's', 'd', 9, 0,
  /* 4966 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 's', 'd', 9, 0,
  /* 4980 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 'd', 9, 0,
  /* 4993 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 'd', 9, 0,
  /* 5007 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 's', 'd', 9, 0,
  /* 5020 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 's', 'd', 9, 0,
  /* 5034 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 'd', 9, 0,
  /* 5047 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 'd', 9, 0,
  /* 5061 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 'd', 9, 0,
  /* 5072 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 'd', 9, 0,
  /* 5084 */ 'v', 'c', 'v', 't', 's', 's', '2', 's', 'd', 9, 0,
  /* 5095 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 9, 0,
  /* 5108 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 9, 0,
  /* 5122 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 9, 0,
  /* 5135 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 9, 0,
  /* 5149 */ 'v', 'p', 'a', 'b', 's', 'd', 9, 0,
  /* 5157 */ 'v', 'f', 'm', 's', 'u', 'b', 's', 'd', 9, 0,
  /* 5167 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 's', 'd', 9, 0,
  /* 5178 */ 'v', 's', 'u', 'b', 's', 'd', 9, 0,
  /* 5186 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'd', 9, 0,
  /* 5196 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 's', 'd', 9, 0,
  /* 5207 */ 'v', 'a', 'd', 'd', 's', 'd', 9, 0,
  /* 5215 */ 'v', 'r', 'o', 'u', 'n', 'd', 's', 'd', 9, 0,
  /* 5225 */ 'v', 'u', 'c', 'o', 'm', 'i', 's', 'd', 9, 0,
  /* 5235 */ 'v', 'c', 'o', 'm', 'i', 's', 'd', 9, 0,
  /* 5244 */ 'v', 'm', 'u', 'l', 's', 'd', 9, 0,
  /* 5252 */ 'v', 'p', 'm', 'i', 'n', 's', 'd', 9, 0,
  /* 5261 */ 'v', 'm', 'i', 'n', 's', 'd', 9, 0,
  /* 5269 */ 's', 't', 'o', 's', 'd', 9, 0,
  /* 5276 */ 'v', 'c', 'm', 'p', 's', 'd', 9, 0,
  /* 5284 */ 'm', 'o', 'v', 'n', 't', 's', 'd', 9, 0,
  /* 5293 */ 'v', 's', 'q', 'r', 't', 's', 'd', 9, 0,
  /* 5302 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 'd', 9, 0,
  /* 5316 */ 'v', 'd', 'i', 'v', 's', 'd', 9, 0,
  /* 5324 */ 'v', 'm', 'o', 'v', 's', 'd', 9, 0,
  /* 5332 */ 'v', 'p', 'm', 'a', 'x', 's', 'd', 9, 0,
  /* 5341 */ 'v', 'm', 'a', 'x', 's', 'd', 9, 0,
  /* 5349 */ 'v', 'f', 'r', 'c', 'z', 's', 'd', 9, 0,
  /* 5358 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'd', 9, 0,
  /* 5368 */ 'v', 'p', 'r', 'o', 't', 'd', 9, 0,
  /* 5376 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'd', 9, 0,
  /* 5390 */ 'v', 'p', 'c', 'o', 'm', 'u', 'd', 9, 0,
  /* 5399 */ 'v', 'p', 'm', 'i', 'n', 'u', 'd', 9, 0,
  /* 5408 */ 'v', 'p', 'c', 'm', 'p', 'u', 'd', 9, 0,
  /* 5417 */ 'v', 'p', 'm', 'a', 'x', 'u', 'd', 9, 0,
  /* 5426 */ 'v', 'p', 's', 'r', 'a', 'v', 'd', 9, 0,
  /* 5435 */ 'v', 'p', 's', 'l', 'l', 'v', 'd', 9, 0,
  /* 5444 */ 'v', 'p', 's', 'r', 'l', 'v', 'd', 9, 0,
  /* 5453 */ 'v', 'p', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'd', 9, 0,
  /* 5465 */ 'v', 'm', 'o', 'v', 'd', 9, 0,
  /* 5472 */ 'v', 'p', 'h', 's', 'u', 'b', 'w', 'd', 9, 0,
  /* 5482 */ 'v', 'p', 'h', 'a', 'd', 'd', 'w', 'd', 9, 0,
  /* 5492 */ 'v', 'p', 'm', 'a', 'd', 'd', 'w', 'd', 9, 0,
  /* 5502 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'w', 'd', 9, 0,
  /* 5514 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'w', 'd', 9, 0,
  /* 5526 */ 'v', 'p', 'm', 'a', 'c', 's', 'w', 'd', 9, 0,
  /* 5536 */ 'v', 'p', 'm', 'a', 'd', 'c', 's', 'w', 'd', 9, 0,
  /* 5547 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'w', 'd', 9, 0,
  /* 5558 */ 'v', 'p', 'm', 'a', 'd', 'c', 's', 's', 'w', 'd', 9, 0,
  /* 5570 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'w', 'd', 9, 0,
  /* 5581 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'd', 9, 0,
  /* 5592 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'd', 9, 0,
  /* 5603 */ 'm', 'o', 'v', 's', 'x', 'd', 9, 0,
  /* 5611 */ 'j', 'a', 'e', 9, 0,
  /* 5616 */ 's', 'e', 't', 'a', 'e', 9, 0,
  /* 5623 */ 'c', 'm', 'o', 'v', 'a', 'e', 9, 0,
  /* 5631 */ 'j', 'b', 'e', 9, 0,
  /* 5636 */ 's', 'e', 't', 'b', 'e', 9, 0,
  /* 5643 */ 'c', 'm', 'o', 'v', 'b', 'e', 9, 0,
  /* 5651 */ 'f', 's', 't', 'p', 'n', 'c', 'e', 9, 0,
  /* 5660 */ 'f', 'f', 'r', 'e', 'e', 9, 0,
  /* 5667 */ 'j', 'g', 'e', 9, 0,
  /* 5672 */ 'p', 'f', 'c', 'm', 'p', 'g', 'e', 9, 0,
  /* 5681 */ 's', 'e', 't', 'g', 'e', 9, 0,
  /* 5688 */ 'c', 'm', 'o', 'v', 'g', 'e', 9, 0,
  /* 5696 */ 'j', 'e', 9, 0,
  /* 5700 */ 'j', 'l', 'e', 9, 0,
  /* 5705 */ 's', 'e', 't', 'l', 'e', 9, 0,
  /* 5712 */ 'c', 'm', 'o', 'v', 'l', 'e', 9, 0,
  /* 5720 */ 'j', 'n', 'e', 9, 0,
  /* 5725 */ 'l', 'o', 'o', 'p', 'n', 'e', 9, 0,
  /* 5733 */ 's', 'e', 't', 'n', 'e', 9, 0,
  /* 5740 */ 'c', 'm', 'o', 'v', 'n', 'e', 9, 0,
  /* 5748 */ 'l', 'o', 'o', 'p', 'e', 9, 0,
  /* 5755 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 5765 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 5775 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 5785 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 5795 */ 's', 'e', 't', 'e', 9, 0,
  /* 5801 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 9, 0,
  /* 5810 */ 's', 'h', 'a', '1', 'n', 'e', 'x', 't', 'e', 9, 0,
  /* 5821 */ 'f', 'n', 's', 'a', 'v', 'e', 9, 0,
  /* 5829 */ 'f', 'x', 's', 'a', 'v', 'e', 9, 0,
  /* 5837 */ 'c', 'm', 'o', 'v', 'e', 9, 0,
  /* 5844 */ 'b', 's', 'f', 9, 0,
  /* 5849 */ 'r', 'e', 't', 'f', 9, 0,
  /* 5855 */ 'n', 'e', 'g', 9, 0,
  /* 5860 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 9, 0,
  /* 5869 */ 'j', 'g', 9, 0,
  /* 5873 */ 'i', 'n', 'v', 'l', 'p', 'g', 9, 0,
  /* 5881 */ 's', 'e', 't', 'g', 9, 0,
  /* 5887 */ 'c', 'm', 'o', 'v', 'g', 9, 0,
  /* 5894 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 9, 0,
  /* 5904 */ 'f', 'x', 'c', 'h', 9, 0,
  /* 5910 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'h', 9, 0,
  /* 5921 */ 'v', 'p', 'm', 'a', 'c', 's', 'd', 'q', 'h', 9, 0,
  /* 5932 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'd', 'q', 'h', 9, 0,
  /* 5944 */ 'c', 'l', 'f', 'l', 'u', 's', 'h', 9, 0,
  /* 5953 */ 'p', 'u', 's', 'h', 9, 0,
  /* 5959 */ 'b', 'l', 'c', 'i', 9, 0,
  /* 5965 */ 'b', 'z', 'h', 'i', 9, 0,
  /* 5971 */ 'f', 'c', 'o', 'm', 'i', 9, 0,
  /* 5978 */ 'f', 'u', 'c', 'o', 'm', 'i', 9, 0,
  /* 5986 */ 'c', 'v', 't', 't', 'p', 'd', '2', 'p', 'i', 9, 0,
  /* 5997 */ 'c', 'v', 't', 'p', 'd', '2', 'p', 'i', 9, 0,
  /* 6007 */ 'c', 'v', 't', 't', 'p', 's', '2', 'p', 'i', 9, 0,
  /* 6018 */ 'c', 'v', 't', 'p', 's', '2', 'p', 'i', 9, 0,
  /* 6028 */ 'f', 'c', 'o', 'm', 'p', 'i', 9, 0,
  /* 6036 */ 'f', 'u', 'c', 'o', 'm', 'p', 'i', 9, 0,
  /* 6045 */ 'v', 'p', 'c', 'm', 'p', 'e', 's', 't', 'r', 'i', 9, 0,
  /* 6057 */ 'v', 'p', 'c', 'm', 'p', 'i', 's', 't', 'r', 'i', 9, 0,
  /* 6069 */ 'v', 'c', 'v', 't', 't', 's', 'd', '2', 's', 'i', 9, 0,
  /* 6081 */ 'v', 'c', 'v', 't', 's', 'd', '2', 's', 'i', 9, 0,
  /* 6092 */ 'v', 'c', 'v', 't', 't', 's', 's', '2', 's', 'i', 9, 0,
  /* 6104 */ 'v', 'c', 'v', 't', 's', 's', '2', 's', 'i', 9, 0,
  /* 6115 */ 'b', 'l', 's', 'i', 9, 0,
  /* 6121 */ 'm', 'o', 'v', 'n', 't', 'i', 9, 0,
  /* 6129 */ 'b', 'l', 'c', 'm', 's', 'k', 9, 0,
  /* 6137 */ 'b', 'l', 's', 'm', 's', 'k', 9, 0,
  /* 6145 */ 't', 'z', 'm', 's', 'k', 9, 0,
  /* 6152 */ 's', 'a', 'l', 9, 0,
  /* 6157 */ 'r', 'c', 'l', 9, 0,
  /* 6162 */ 's', 'h', 'l', 9, 0,
  /* 6167 */ 'j', 'l', 9, 0,
  /* 6171 */ 'l', 'c', 'a', 'l', 'l', 9, 0,
  /* 6178 */ 'b', 'l', 'c', 'f', 'i', 'l', 'l', 9, 0,
  /* 6187 */ 'b', 'l', 's', 'f', 'i', 'l', 'l', 9, 0,
  /* 6196 */ 'r', 'o', 'l', 9, 0,
  /* 6201 */ 'a', 'r', 'p', 'l', 9, 0,
  /* 6207 */ 'v', 'p', 'm', 'a', 'c', 's', 'd', 'q', 'l', 9, 0,
  /* 6218 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'd', 'q', 'l', 9, 0,
  /* 6230 */ 'l', 's', 'l', 9, 0,
  /* 6235 */ 's', 'e', 't', 'l', 9, 0,
  /* 6241 */ 'p', 'f', 'm', 'u', 'l', 9, 0,
  /* 6248 */ 'f', 'i', 'm', 'u', 'l', 9, 0,
  /* 6255 */ 'c', 'm', 'o', 'v', 'l', 9, 0,
  /* 6262 */ 'a', 'a', 'm', 9, 0,
  /* 6267 */ 'f', 'c', 'o', 'm', 9, 0,
  /* 6273 */ 'f', 'i', 'c', 'o', 'm', 9, 0,
  /* 6280 */ 'f', 'u', 'c', 'o', 'm', 9, 0,
  /* 6287 */ 'v', 'p', 'p', 'e', 'r', 'm', 9, 0,
  /* 6295 */ 'v', 'p', 'c', 'm', 'p', 'e', 's', 't', 'r', 'm', 9, 0,
  /* 6307 */ 'v', 'p', 'c', 'm', 'p', 'i', 's', 't', 'r', 'm', 9, 0,
  /* 6319 */ 'v', 'p', 'a', 'n', 'd', 'n', 9, 0,
  /* 6327 */ 'x', 'b', 'e', 'g', 'i', 'n', 9, 0,
  /* 6335 */ 'p', 'f', 'm', 'i', 'n', 9, 0,
  /* 6342 */ 'v', 'm', 'x', 'o', 'n', 9, 0,
  /* 6349 */ 'j', 'o', 9, 0,
  /* 6353 */ 'j', 'n', 'o', 9, 0,
  /* 6358 */ 's', 'e', 't', 'n', 'o', 9, 0,
  /* 6365 */ 'c', 'm', 'o', 'v', 'n', 'o', 9, 0,
  /* 6373 */ 's', 'e', 't', 'o', 9, 0,
  /* 6379 */ 'c', 'm', 'o', 'v', 'o', 9, 0,
  /* 6386 */ 'b', 's', 'w', 'a', 'p', 9, 0,
  /* 6393 */ 'f', 's', 'u', 'b', 'p', 9, 0,
  /* 6400 */ 'p', 'f', 'r', 'c', 'p', 9, 0,
  /* 6407 */ 'f', 'a', 'd', 'd', 'p', 9, 0,
  /* 6414 */ 'p', 'd', 'e', 'p', 9, 0,
  /* 6420 */ 'j', 'p', 9, 0,
  /* 6424 */ 'f', 'm', 'u', 'l', 'p', 9, 0,
  /* 6431 */ 'c', 'm', 'p', 9, 0,
  /* 6436 */ 'l', 'j', 'm', 'p', 9, 0,
  /* 6442 */ 'f', 'c', 'o', 'm', 'p', 9, 0,
  /* 6449 */ 'f', 'i', 'c', 'o', 'm', 'p', 9, 0,
  /* 6457 */ 'f', 'u', 'c', 'o', 'm', 'p', 9, 0,
  /* 6465 */ 'j', 'n', 'p', 9, 0,
  /* 6470 */ 's', 'e', 't', 'n', 'p', 9, 0,
  /* 6477 */ 'c', 'm', 'o', 'v', 'n', 'p', 9, 0,
  /* 6485 */ 'n', 'o', 'p', 9, 0,
  /* 6490 */ 'l', 'o', 'o', 'p', 9, 0,
  /* 6496 */ 'p', 'o', 'p', 9, 0,
  /* 6501 */ 'f', 's', 'u', 'b', 'r', 'p', 9, 0,
  /* 6509 */ 'f', 'd', 'i', 'v', 'r', 'p', 9, 0,
  /* 6517 */ 's', 'e', 't', 'p', 9, 0,
  /* 6523 */ 'f', 'b', 's', 't', 'p', 9, 0,
  /* 6530 */ 'f', 's', 't', 'p', 9, 0,
  /* 6536 */ 'f', 'i', 's', 't', 'p', 9, 0,
  /* 6543 */ 'f', 'i', 's', 't', 't', 'p', 9, 0,
  /* 6551 */ 'v', 'm', 'o', 'v', 'd', 'd', 'u', 'p', 9, 0,
  /* 6561 */ 'v', 'm', 'o', 'v', 's', 'h', 'd', 'u', 'p', 9, 0,
  /* 6572 */ 'v', 'm', 'o', 'v', 's', 'l', 'd', 'u', 'p', 9, 0,
  /* 6583 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 6599 */ 'f', 'd', 'i', 'v', 'p', 9, 0,
  /* 6606 */ 'c', 'm', 'o', 'v', 'p', 9, 0,
  /* 6613 */ 'm', 'o', 'v', 'd', 'q', '2', 'q', 9, 0,
  /* 6622 */ 'v', 'p', 's', 'h', 'a', 'q', 9, 0,
  /* 6630 */ 'v', 'p', 'h', 'a', 'd', 'd', 'b', 'q', 9, 0,
  /* 6640 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'b', 'q', 9, 0,
  /* 6651 */ 'v', 'p', 's', 'u', 'b', 'q', 9, 0,
  /* 6659 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'q', 9, 0,
  /* 6670 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'q', 9, 0,
  /* 6681 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 9, 0,
  /* 6693 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 9, 0,
  /* 6704 */ 'm', 'o', 'v', 'q', '2', 'd', 'q', 9, 0,
  /* 6713 */ 'v', 'c', 'v', 't', 't', 'p', 's', '2', 'd', 'q', 9, 0,
  /* 6725 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'd', 'q', 9, 0,
  /* 6736 */ 'v', 'p', 'h', 's', 'u', 'b', 'd', 'q', 9, 0,
  /* 6746 */ 'v', 'p', 'a', 'd', 'd', 'q', 9, 0,
  /* 6754 */ 'v', 'p', 'h', 'a', 'd', 'd', 'd', 'q', 9, 0,
  /* 6764 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'd', 'q', 9, 0,
  /* 6776 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'd', 'q', 9, 0,
  /* 6788 */ 'v', 'p', 's', 'l', 'l', 'd', 'q', 9, 0,
  /* 6797 */ 'v', 'p', 's', 'r', 'l', 'd', 'q', 9, 0,
  /* 6806 */ 'v', 'p', 'm', 'u', 'l', 'd', 'q', 9, 0,
  /* 6815 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'q', 'd', 'q', 9, 0,
  /* 6828 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'q', 'd', 'q', 9, 0,
  /* 6841 */ 'v', 'p', 'c', 'l', 'm', 'u', 'l', 'q', 'd', 'q', 9, 0,
  /* 6853 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'q', 9, 0,
  /* 6865 */ 'v', 'm', 'o', 'v', 'n', 't', 'd', 'q', 9, 0,
  /* 6875 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'd', 'q', 9, 0,
  /* 6886 */ 'v', 'p', 'm', 'u', 'l', 'u', 'd', 'q', 9, 0,
  /* 6896 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'd', 'q', 9, 0,
  /* 6907 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'd', 'q', 9, 0,
  /* 6918 */ 'p', 'f', 'c', 'm', 'p', 'e', 'q', 9, 0,
  /* 6927 */ 'r', 'e', 't', 'f', 'q', 9, 0,
  /* 6934 */ 'v', 'p', 's', 'h', 'l', 'q', 9, 0,
  /* 6942 */ 'v', 'p', 's', 'l', 'l', 'q', 9, 0,
  /* 6950 */ 'v', 'p', 's', 'r', 'l', 'q', 9, 0,
  /* 6958 */ 'v', 'p', 'c', 'o', 'm', 'q', 9, 0,
  /* 6966 */ 'v', 'p', 'e', 'r', 'm', 'q', 9, 0,
  /* 6974 */ 'v', 'p', 'c', 'm', 'p', 'q', 9, 0,
  /* 6982 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'q', 9, 0,
  /* 6992 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'q', 9, 0,
  /* 7004 */ 'v', 'p', 'i', 'n', 's', 'r', 'q', 9, 0,
  /* 7013 */ 'v', 'p', 'e', 'x', 't', 'r', 'q', 9, 0,
  /* 7022 */ 's', 't', 'o', 's', 'q', 9, 0,
  /* 7029 */ 'c', 'm', 'p', 's', 'q', 9, 0,
  /* 7036 */ 'm', 'o', 'v', 's', 'q', 9, 0,
  /* 7043 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'q', 9, 0,
  /* 7053 */ 'm', 'o', 'v', 'n', 't', 'q', 9, 0,
  /* 7061 */ 'v', 'p', 'r', 'o', 't', 'q', 9, 0,
  /* 7069 */ 'i', 'n', 's', 'e', 'r', 't', 'q', 9, 0,
  /* 7078 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'q', 9, 0,
  /* 7092 */ 'v', 'p', 'c', 'o', 'm', 'u', 'q', 9, 0,
  /* 7101 */ 'v', 'p', 'c', 'm', 'p', 'u', 'q', 9, 0,
  /* 7110 */ 'v', 'p', 's', 'l', 'l', 'v', 'q', 9, 0,
  /* 7119 */ 'v', 'p', 's', 'r', 'l', 'v', 'q', 9, 0,
  /* 7128 */ 'v', 'p', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'q', 9, 0,
  /* 7140 */ 'v', 'm', 'o', 'v', 'q', 9, 0,
  /* 7147 */ 'v', 'p', 'h', 'a', 'd', 'd', 'w', 'q', 9, 0,
  /* 7157 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'w', 'q', 9, 0,
  /* 7168 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'q', 9, 0,
  /* 7179 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'q', 9, 0,
  /* 7190 */ 'v', 'm', 'c', 'l', 'e', 'a', 'r', 9, 0,
  /* 7199 */ 'l', 'a', 'r', 9, 0,
  /* 7204 */ 's', 'a', 'r', 9, 0,
  /* 7209 */ 'p', 'f', 's', 'u', 'b', 'r', 9, 0,
  /* 7217 */ 'f', 'i', 's', 'u', 'b', 'r', 9, 0,
  /* 7225 */ 'r', 'c', 'r', 9, 0,
  /* 7230 */ 'e', 'n', 't', 'e', 'r', 9, 0,
  /* 7237 */ 's', 'h', 'r', 9, 0,
  /* 7242 */ 'v', 'p', 'a', 'l', 'i', 'g', 'n', 'r', 9, 0,
  /* 7252 */ 'v', 'p', 'o', 'r', 9, 0,
  /* 7258 */ 'r', 'o', 'r', 9, 0,
  /* 7263 */ 'f', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 7271 */ 'f', 'x', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 7280 */ 'v', 'p', 'x', 'o', 'r', 9, 0,
  /* 7287 */ 'v', 'e', 'r', 'r', 9, 0,
  /* 7293 */ 'b', 's', 'r', 9, 0,
  /* 7298 */ 'v', 'l', 'd', 'm', 'x', 'c', 's', 'r', 9, 0,
  /* 7308 */ 'v', 's', 't', 'm', 'x', 'c', 's', 'r', 9, 0,
  /* 7318 */ 'b', 'l', 's', 'r', 9, 0,
  /* 7324 */ 'b', 't', 'r', 9, 0,
  /* 7329 */ 'l', 't', 'r', 9, 0,
  /* 7334 */ 's', 't', 'r', 9, 0,
  /* 7339 */ 'b', 'e', 'x', 't', 'r', 9, 0,
  /* 7346 */ 'f', 'd', 'i', 'v', 'r', 9, 0,
  /* 7353 */ 'f', 'i', 'd', 'i', 'v', 'r', 9, 0,
  /* 7361 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 0,
  /* 7369 */ 'b', 'l', 'c', 's', 9, 0,
  /* 7375 */ 'l', 'd', 's', 9, 0,
  /* 7380 */ 'l', 'e', 's', 9, 0,
  /* 7385 */ 'l', 'f', 's', 9, 0,
  /* 7390 */ 'l', 'g', 's', 9, 0,
  /* 7395 */ 'j', 's', 9, 0,
  /* 7399 */ 'j', 'n', 's', 9, 0,
  /* 7404 */ 's', 'e', 't', 'n', 's', 9, 0,
  /* 7411 */ 'c', 'm', 'o', 'v', 'n', 's', 9, 0,
  /* 7419 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '3', '1', 'p', 's', 9, 0,
  /* 7435 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 's', 9, 0,
  /* 7448 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 's', 9, 0,
  /* 7462 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '3', '1', 'p', 's', 9, 0,
  /* 7478 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 's', 9, 0,
  /* 7491 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 's', 9, 0,
  /* 7505 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 's', 9, 0,
  /* 7521 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 9, 0,
  /* 7534 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 9, 0,
  /* 7548 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 9, 0,
  /* 7564 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 9, 0,
  /* 7577 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 9, 0,
  /* 7591 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 9, 0,
  /* 7602 */ 'v', 'c', 'v', 't', 'p', 'h', '2', 'p', 's', 9, 0,
  /* 7613 */ 'c', 'v', 't', 'p', 'i', '2', 'p', 's', 9, 0,
  /* 7623 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', '2', 'p', 's', 9, 0,
  /* 7635 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 's', 9, 0,
  /* 7646 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 's', 9, 0,
  /* 7662 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 9, 0,
  /* 7675 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 9, 0,
  /* 7689 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 9, 0,
  /* 7705 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 9, 0,
  /* 7718 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 9, 0,
  /* 7732 */ 'v', 'm', 'o', 'v', 'a', 'p', 's', 9, 0,
  /* 7741 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 7754 */ 'v', 'a', 'd', 'd', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 7765 */ 'v', 'h', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 7774 */ 'v', 'f', 'm', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 7784 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 7795 */ 'v', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 7803 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 7816 */ 'v', 'h', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 7825 */ 'v', 'f', 'm', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 7835 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 7846 */ 'v', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 7854 */ 'v', 'a', 'n', 'd', 'p', 's', 9, 0,
  /* 7862 */ 'v', 'b', 'l', 'e', 'n', 'd', 'p', 's', 9, 0,
  /* 7872 */ 'v', 'r', 'o', 'u', 'n', 'd', 'p', 's', 9, 0,
  /* 7882 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 's', 9, 0,
  /* 7894 */ 'v', 's', 'h', 'u', 'f', 'p', 's', 9, 0,
  /* 7903 */ 'v', 'u', 'n', 'p', 'c', 'k', 'h', 'p', 's', 9, 0,
  /* 7914 */ 'v', 'm', 'o', 'v', 'l', 'h', 'p', 's', 9, 0,
  /* 7924 */ 'v', 'm', 'o', 'v', 'h', 'p', 's', 9, 0,
  /* 7933 */ 'v', 'm', 'o', 'v', 'm', 's', 'k', 'p', 's', 9, 0,
  /* 7944 */ 'v', 'm', 'o', 'v', 'h', 'l', 'p', 's', 9, 0,
  /* 7954 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 's', 9, 0,
  /* 7965 */ 'v', 'u', 'n', 'p', 'c', 'k', 'l', 'p', 's', 9, 0,
  /* 7976 */ 'v', 'm', 'u', 'l', 'p', 's', 9, 0,
  /* 7984 */ 'v', 'm', 'o', 'v', 'l', 'p', 's', 9, 0,
  /* 7993 */ 'v', 'p', 'e', 'r', 'm', 'p', 's', 9, 0,
  /* 8002 */ 'v', 'a', 'n', 'd', 'n', 'p', 's', 9, 0,
  /* 8011 */ 'v', 'm', 'i', 'n', 'p', 's', 9, 0,
  /* 8019 */ 'v', 'r', 'c', 'p', 'p', 's', 9, 0,
  /* 8027 */ 'v', 'd', 'p', 'p', 's', 9, 0,
  /* 8034 */ 'v', 'c', 'm', 'p', 'p', 's', 9, 0,
  /* 8042 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 's', 9, 0,
  /* 8054 */ 'v', 'o', 'r', 'p', 's', 9, 0,
  /* 8061 */ 'v', 'x', 'o', 'r', 'p', 's', 9, 0,
  /* 8069 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'p', 's', 9, 0,
  /* 8081 */ 'v', 'm', 'o', 'v', 'n', 't', 'p', 's', 9, 0,
  /* 8091 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'p', 's', 9, 0,
  /* 8102 */ 'v', 'r', 's', 'q', 'r', 't', 'p', 's', 9, 0,
  /* 8112 */ 'v', 's', 'q', 'r', 't', 'p', 's', 9, 0,
  /* 8121 */ 'v', 't', 'e', 's', 't', 'p', 's', 9, 0,
  /* 8130 */ 'v', 'm', 'o', 'v', 'u', 'p', 's', 9, 0,
  /* 8139 */ 'v', 'b', 'l', 'e', 'n', 'd', 'v', 'p', 's', 9, 0,
  /* 8150 */ 'v', 'd', 'i', 'v', 'p', 's', 9, 0,
  /* 8158 */ 'v', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'p', 's', 9, 0,
  /* 8170 */ 'v', 'm', 'a', 'x', 'p', 's', 9, 0,
  /* 8178 */ 'v', 'f', 'r', 'c', 'z', 'p', 's', 9, 0,
  /* 8187 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 's', 's', 9, 0,
  /* 8200 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 's', 's', 9, 0,
  /* 8214 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 's', 9, 0,
  /* 8227 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 's', 9, 0,
  /* 8241 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 's', 's', 9, 0,
  /* 8254 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 's', 's', 9, 0,
  /* 8268 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 's', 9, 0,
  /* 8281 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 's', 9, 0,
  /* 8295 */ 'v', 'c', 'v', 't', 's', 'd', '2', 's', 's', 9, 0,
  /* 8306 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 's', 9, 0,
  /* 8317 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 's', 9, 0,
  /* 8329 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 9, 0,
  /* 8342 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 9, 0,
  /* 8356 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 9, 0,
  /* 8369 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 9, 0,
  /* 8383 */ 'v', 'f', 'm', 's', 'u', 'b', 's', 's', 9, 0,
  /* 8393 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 's', 's', 9, 0,
  /* 8404 */ 'v', 's', 'u', 'b', 's', 's', 9, 0,
  /* 8412 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 's', 9, 0,
  /* 8422 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 's', 's', 9, 0,
  /* 8433 */ 'v', 'a', 'd', 'd', 's', 's', 9, 0,
  /* 8441 */ 'v', 'r', 'o', 'u', 'n', 'd', 's', 's', 9, 0,
  /* 8451 */ 'v', 'u', 'c', 'o', 'm', 'i', 's', 's', 9, 0,
  /* 8461 */ 'v', 'c', 'o', 'm', 'i', 's', 's', 9, 0,
  /* 8470 */ 'v', 'm', 'u', 'l', 's', 's', 9, 0,
  /* 8478 */ 'v', 'm', 'i', 'n', 's', 's', 9, 0,
  /* 8486 */ 'v', 'r', 'c', 'p', 's', 's', 9, 0,
  /* 8494 */ 'v', 'c', 'm', 'p', 's', 's', 9, 0,
  /* 8502 */ 'm', 'o', 'v', 'n', 't', 's', 's', 9, 0,
  /* 8511 */ 'v', 'r', 's', 'q', 'r', 't', 's', 's', 9, 0,
  /* 8521 */ 'v', 's', 'q', 'r', 't', 's', 's', 9, 0,
  /* 8530 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 's', 9, 0,
  /* 8544 */ 'v', 'd', 'i', 'v', 's', 's', 9, 0,
  /* 8552 */ 'v', 'm', 'o', 'v', 's', 's', 9, 0,
  /* 8560 */ 'v', 'm', 'a', 'x', 's', 's', 9, 0,
  /* 8568 */ 'v', 'f', 'r', 'c', 'z', 's', 's', 9, 0,
  /* 8577 */ 'b', 't', 's', 9, 0,
  /* 8582 */ 's', 'e', 't', 's', 9, 0,
  /* 8588 */ 'c', 'm', 'o', 'v', 's', 9, 0,
  /* 8595 */ 'b', 't', 9, 0,
  /* 8599 */ 'l', 'g', 'd', 't', 9, 0,
  /* 8605 */ 's', 'g', 'd', 't', 9, 0,
  /* 8611 */ 'l', 'i', 'd', 't', 9, 0,
  /* 8617 */ 's', 'i', 'd', 't', 9, 0,
  /* 8623 */ 'l', 'l', 'd', 't', 9, 0,
  /* 8629 */ 's', 'l', 'd', 't', 9, 0,
  /* 8635 */ 'r', 'e', 't', 9, 0,
  /* 8640 */ 'p', 'f', 'c', 'm', 'p', 'g', 't', 9, 0,
  /* 8649 */ 'p', 'o', 'p', 'c', 'n', 't', 9, 0,
  /* 8657 */ 'l', 'z', 'c', 'n', 't', 9, 0,
  /* 8664 */ 't', 'z', 'c', 'n', 't', 9, 0,
  /* 8671 */ 'i', 'n', 't', 9, 0,
  /* 8676 */ 'n', 'o', 't', 9, 0,
  /* 8681 */ 'i', 'n', 'v', 'e', 'p', 't', 9, 0,
  /* 8689 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 9, 0,
  /* 8699 */ 'x', 'a', 'b', 'o', 'r', 't', 9, 0,
  /* 8707 */ 'p', 'f', 'r', 's', 'q', 'r', 't', 9, 0,
  /* 8716 */ 'v', 'a', 'e', 's', 'd', 'e', 'c', 'l', 'a', 's', 't', 9, 0,
  /* 8729 */ 'v', 'a', 'e', 's', 'e', 'n', 'c', 'l', 'a', 's', 't', 9, 0,
  /* 8742 */ 'v', 'p', 't', 'e', 's', 't', 9, 0,
  /* 8750 */ 'f', 's', 't', 9, 0,
  /* 8755 */ 'f', 'i', 's', 't', 9, 0,
  /* 8761 */ 'v', 'a', 'e', 's', 'k', 'e', 'y', 'g', 'e', 'n', 'a', 's', 's', 'i', 's', 't', 9, 0,
  /* 8779 */ 'v', 'm', 'p', 't', 'r', 's', 't', 9, 0,
  /* 8788 */ 'o', 'u', 't', 9, 0,
  /* 8793 */ 'p', 'e', 'x', 't', 9, 0,
  /* 8799 */ 'v', 'l', 'd', 'd', 'q', 'u', 9, 0,
  /* 8807 */ 'v', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'd', 'q', 'u', 9, 0,
  /* 8820 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', 9, 0,
  /* 8829 */ 'f', 'd', 'i', 'v', 9, 0,
  /* 8835 */ 'f', 'i', 'd', 'i', 'v', 9, 0,
  /* 8842 */ 'f', 'l', 'd', 'e', 'n', 'v', 9, 0,
  /* 8850 */ 'f', 'n', 's', 't', 'e', 'n', 'v', 9, 0,
  /* 8859 */ 'v', 'p', 'c', 'm', 'o', 'v', 9, 0,
  /* 8867 */ 'v', 'p', 's', 'h', 'a', 'w', 9, 0,
  /* 8875 */ 'v', 'p', 's', 'r', 'a', 'w', 9, 0,
  /* 8883 */ 'v', 'p', 'h', 's', 'u', 'b', 'b', 'w', 9, 0,
  /* 8893 */ 'v', 'm', 'p', 's', 'a', 'd', 'b', 'w', 9, 0,
  /* 8903 */ 'v', 'p', 's', 'a', 'd', 'b', 'w', 9, 0,
  /* 8912 */ 'v', 'p', 'h', 'a', 'd', 'd', 'b', 'w', 9, 0,
  /* 8922 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'b', 'w', 9, 0,
  /* 8934 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'b', 'w', 9, 0,
  /* 8946 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'b', 'w', 9, 0,
  /* 8957 */ 'v', 'p', 'h', 's', 'u', 'b', 'w', 9, 0,
  /* 8966 */ 'v', 'p', 's', 'u', 'b', 'w', 9, 0,
  /* 8974 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'w', 9, 0,
  /* 8985 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'w', 9, 0,
  /* 8996 */ 'f', 'l', 'd', 'c', 'w', 9, 0,
  /* 9003 */ 'f', 'n', 's', 't', 'c', 'w', 9, 0,
  /* 9011 */ 'v', 'p', 'h', 'a', 'd', 'd', 'w', 9, 0,
  /* 9020 */ 'v', 'p', 'a', 'd', 'd', 'w', 9, 0,
  /* 9028 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'w', 9, 0,
  /* 9038 */ 'v', 'p', 'a', 'c', 'k', 's', 's', 'd', 'w', 9, 0,
  /* 9049 */ 'v', 'p', 'a', 'c', 'k', 'u', 's', 'd', 'w', 9, 0,
  /* 9060 */ 'p', 'i', '2', 'f', 'w', 9, 0,
  /* 9067 */ 'p', 's', 'h', 'u', 'f', 'w', 9, 0,
  /* 9075 */ 'v', 'p', 'a', 'v', 'g', 'w', 9, 0,
  /* 9083 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 'w', 9, 0,
  /* 9094 */ 'v', 'p', 's', 'h', 'u', 'f', 'h', 'w', 9, 0,
  /* 9104 */ 'v', 'p', 'm', 'u', 'l', 'h', 'w', 9, 0,
  /* 9113 */ 'p', 'f', '2', 'i', 'w', 9, 0,
  /* 9120 */ 'v', 'p', 's', 'h', 'u', 'f', 'l', 'w', 9, 0,
  /* 9130 */ 'v', 'p', 's', 'h', 'l', 'w', 9, 0,
  /* 9138 */ 'v', 'p', 's', 'l', 'l', 'w', 9, 0,
  /* 9146 */ 'v', 'p', 'm', 'u', 'l', 'l', 'w', 9, 0,
  /* 9155 */ 'v', 'p', 's', 'r', 'l', 'w', 9, 0,
  /* 9163 */ 'v', 'p', 'c', 'o', 'm', 'w', 9, 0,
  /* 9171 */ 'v', 'p', 's', 'i', 'g', 'n', 'w', 9, 0,
  /* 9180 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'w', 9, 0,
  /* 9190 */ 'v', 'e', 'r', 'w', 9, 0,
  /* 9196 */ 'p', 'm', 'u', 'l', 'h', 'r', 'w', 9, 0,
  /* 9205 */ 'v', 'p', 'i', 'n', 's', 'r', 'w', 9, 0,
  /* 9214 */ 'v', 'p', 'e', 'x', 't', 'r', 'w', 9, 0,
  /* 9223 */ 'v', 'p', 'a', 'b', 's', 'w', 9, 0,
  /* 9231 */ 'v', 'p', 'm', 'a', 'd', 'd', 'u', 'b', 's', 'w', 9, 0,
  /* 9243 */ 'v', 'p', 'h', 's', 'u', 'b', 's', 'w', 9, 0,
  /* 9253 */ 'v', 'p', 's', 'u', 'b', 's', 'w', 9, 0,
  /* 9262 */ 'v', 'p', 'h', 'a', 'd', 'd', 's', 'w', 9, 0,
  /* 9272 */ 'v', 'p', 'a', 'd', 'd', 's', 'w', 9, 0,
  /* 9281 */ 'l', 'm', 's', 'w', 9, 0,
  /* 9287 */ 's', 'm', 's', 'w', 9, 0,
  /* 9293 */ 'v', 'p', 'm', 'i', 'n', 's', 'w', 9, 0,
  /* 9302 */ 's', 't', 'o', 's', 'w', 9, 0,
  /* 9309 */ 'c', 'm', 'p', 's', 'w', 9, 0,
  /* 9316 */ 'v', 'p', 'm', 'u', 'l', 'h', 'r', 's', 'w', 9, 0,
  /* 9327 */ 'f', 'n', 's', 't', 's', 'w', 9, 0,
  /* 9335 */ 'v', 'p', 's', 'u', 'b', 'u', 's', 'w', 9, 0,
  /* 9345 */ 'v', 'p', 'a', 'd', 'd', 'u', 's', 'w', 9, 0,
  /* 9355 */ 'm', 'o', 'v', 's', 'w', 9, 0,
  /* 9362 */ 'v', 'p', 'm', 'a', 'x', 's', 'w', 9, 0,
  /* 9371 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'w', 9, 0,
  /* 9381 */ 'v', 'p', 'r', 'o', 't', 'w', 9, 0,
  /* 9389 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'w', 9, 0,
  /* 9403 */ 'v', 'p', 'm', 'u', 'l', 'h', 'u', 'w', 9, 0,
  /* 9413 */ 'v', 'p', 'c', 'o', 'm', 'u', 'w', 9, 0,
  /* 9422 */ 'v', 'p', 'm', 'i', 'n', 'u', 'w', 9, 0,
  /* 9431 */ 'v', 'p', 'h', 'm', 'i', 'n', 'p', 'o', 's', 'u', 'w', 9, 0,
  /* 9444 */ 'v', 'p', 'm', 'a', 'x', 'u', 'w', 9, 0,
  /* 9453 */ 'v', 'p', 'm', 'a', 'c', 's', 'w', 'w', 9, 0,
  /* 9463 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'w', 'w', 9, 0,
  /* 9474 */ 'p', 'f', 'm', 'a', 'x', 9, 0,
  /* 9481 */ 'a', 'd', 'c', 'x', 9, 0,
  /* 9487 */ 's', 'h', 'l', 'x', 9, 0,
  /* 9493 */ 'm', 'u', 'l', 'x', 9, 0,
  /* 9499 */ 'a', 'd', 'o', 'x', 9, 0,
  /* 9505 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 'x', 9, 0,
  /* 9518 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 'x', 9, 0,
  /* 9530 */ 's', 'a', 'r', 'x', 9, 0,
  /* 9536 */ 's', 'h', 'r', 'x', 9, 0,
  /* 9542 */ 'r', 'o', 'r', 'x', 9, 0,
  /* 9548 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 'x', 9, 0,
  /* 9560 */ 'm', 'o', 'v', 's', 'x', 9, 0,
  /* 9567 */ 'm', 'o', 'v', 'z', 'x', 9, 0,
  /* 9574 */ 'j', 'e', 'c', 'x', 'z', 9, 0,
  /* 9581 */ 'j', 'c', 'x', 'z', 9, 0,
  /* 9587 */ 'j', 'r', 'c', 'x', 'z', 9, 0,
  /* 9594 */ 'f', 'c', 'm', 'o', 'v', 'n', 'b', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9610 */ 'f', 'c', 'm', 'o', 'v', 'b', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9625 */ 'f', 'c', 'm', 'o', 'v', 'n', 'b', 'e', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9642 */ 'f', 'c', 'm', 'o', 'v', 'b', 'e', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9658 */ 'f', 'c', 'm', 'o', 'v', 'n', 'e', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9674 */ 'f', 'c', 'm', 'o', 'v', 'e', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9689 */ 'f', 'x', 'c', 'h', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9702 */ 'f', 'c', 'o', 'm', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9715 */ 'f', 'c', 'o', 'm', 'p', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9729 */ 'f', 'c', 'm', 'o', 'v', 'n', 'u', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9745 */ 'f', 'c', 'm', 'o', 'v', 'u', 9, 's', 't', '(', '0', ')', ',', 32, 0,
  /* 9760 */ 's', 'b', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 9769 */ 's', 'c', 'a', 's', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 9780 */ 'l', 'o', 'd', 's', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 9791 */ 's', 'u', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 9800 */ 'a', 'd', 'c', 9, 'a', 'l', ',', 32, 0,
  /* 9809 */ 'a', 'd', 'd', 9, 'a', 'l', ',', 32, 0,
  /* 9818 */ 'a', 'n', 'd', 9, 'a', 'l', ',', 32, 0,
  /* 9827 */ 'i', 'n', 9, 'a', 'l', ',', 32, 0,
  /* 9835 */ 'c', 'm', 'p', 9, 'a', 'l', ',', 32, 0,
  /* 9844 */ 'x', 'o', 'r', 9, 'a', 'l', ',', 32, 0,
  /* 9853 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'a', 'l', ',', 32, 0,
  /* 9865 */ 't', 'e', 's', 't', 9, 'a', 'l', ',', 32, 0,
  /* 9875 */ 'm', 'o', 'v', 9, 'a', 'l', ',', 32, 0,
  /* 9884 */ 's', 'b', 'b', 9, 'a', 'x', ',', 32, 0,
  /* 9893 */ 's', 'u', 'b', 9, 'a', 'x', ',', 32, 0,
  /* 9902 */ 'a', 'd', 'c', 9, 'a', 'x', ',', 32, 0,
  /* 9911 */ 'a', 'd', 'd', 9, 'a', 'x', ',', 32, 0,
  /* 9920 */ 'a', 'n', 'd', 9, 'a', 'x', ',', 32, 0,
  /* 9929 */ 'x', 'c', 'h', 'g', 9, 'a', 'x', ',', 32, 0,
  /* 9939 */ 'i', 'n', 9, 'a', 'x', ',', 32, 0,
  /* 9947 */ 'c', 'm', 'p', 9, 'a', 'x', ',', 32, 0,
  /* 9956 */ 'x', 'o', 'r', 9, 'a', 'x', ',', 32, 0,
  /* 9965 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'a', 'x', ',', 32, 0,
  /* 9977 */ 't', 'e', 's', 't', 9, 'a', 'x', ',', 32, 0,
  /* 9987 */ 'm', 'o', 'v', 9, 'a', 'x', ',', 32, 0,
  /* 9996 */ 's', 'c', 'a', 's', 'w', 9, 'a', 'x', ',', 32, 0,
  /* 10007 */ 'l', 'o', 'd', 's', 'w', 9, 'a', 'x', ',', 32, 0,
  /* 10018 */ 's', 'b', 'b', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10028 */ 's', 'u', 'b', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10038 */ 'a', 'd', 'c', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10048 */ 'a', 'd', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10058 */ 'a', 'n', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10068 */ 's', 'c', 'a', 's', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10080 */ 'l', 'o', 'd', 's', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10092 */ 'x', 'c', 'h', 'g', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10103 */ 'i', 'n', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10112 */ 'c', 'm', 'p', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10122 */ 'x', 'o', 'r', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10132 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10145 */ 't', 'e', 's', 't', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10156 */ 'm', 'o', 'v', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 10166 */ 's', 'b', 'b', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10176 */ 's', 'u', 'b', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10186 */ 'a', 'd', 'c', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10196 */ 'a', 'd', 'd', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10206 */ 'a', 'n', 'd', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10216 */ 'x', 'c', 'h', 'g', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10227 */ 'c', 'm', 'p', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10237 */ 's', 'c', 'a', 's', 'q', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10249 */ 'l', 'o', 'd', 's', 'q', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10261 */ 'x', 'o', 'r', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10271 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10284 */ 't', 'e', 's', 't', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 10295 */ 'o', 'u', 't', 's', 'b', 9, 'd', 'x', ',', 32, 0,
  /* 10306 */ 'o', 'u', 't', 's', 'd', 9, 'd', 'x', ',', 32, 0,
  /* 10317 */ 'o', 'u', 't', 's', 'w', 9, 'd', 'x', ',', 32, 0,
  /* 10328 */ '#', 'V', 'A', 'A', 'R', 'G', '_', '6', '4', 32, 0,
  /* 10339 */ 'r', 'e', 't', 9, '#', 'e', 'h', '_', 'r', 'e', 't', 'u', 'r', 'n', ',', 32, 'a', 'd', 'd', 'r', ':', 32, 0,
  /* 10362 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'X', 'M', 'M', 32, 0,
  /* 10376 */ '#', 'V', 'A', 'S', 'T', 'A', 'R', 'T', '_', 'S', 'A', 'V', 'E', '_', 'X', 'M', 'M', '_', 'R', 'E', 'G', 'S', 32, 0,
  /* 10400 */ '#', 'S', 'E', 'H', '_', 'S', 't', 'a', 'c', 'k', 'A', 'l', 'l', 'o', 'c', 32, 0,
  /* 10417 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 10433 */ '#', 'S', 'E', 'H', '_', 'S', 'e', 't', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 10448 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'R', 'e', 'g', 32, 0,
  /* 10462 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'R', 'e', 'g', 32, 0,
  /* 10476 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '3', '2', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10496 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '1', '6', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10516 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '8', '0', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10536 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10556 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '1', '6', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10577 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10597 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10617 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10636 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10656 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10676 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10696 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10716 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10736 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10756 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10776 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10795 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '8', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10813 */ '#', 'A', 'C', 'Q', 'U', 'I', 'R', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10834 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 10855 */ 'x', 's', 'h', 'a', '1', 0,
  /* 10861 */ 'f', 'l', 'd', '1', 0,
  /* 10866 */ 'f', 'p', 'r', 'e', 'm', '1', 0,
  /* 10873 */ 'f', '2', 'x', 'm', '1', 0,
  /* 10879 */ 'f', 'y', 'l', '2', 'x', 'p', '1', 0,
  /* 10887 */ 'i', 'n', 't', '1', 0,
  /* 10892 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 10911 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 10929 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '3', '2', 0,
  /* 10942 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 10955 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 10973 */ 'u', 'd', '2', 0,
  /* 10977 */ 'f', 'l', 'd', 'l', 'g', '2', 0,
  /* 10984 */ 'f', 'l', 'd', 'l', 'n', '2', 0,
  /* 10991 */ 'i', 'n', 't', '3', 0,
  /* 10996 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 11015 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 11033 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '6', '4', 0,
  /* 11046 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 11059 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 11077 */ 'r', 'e', 'x', '6', '4', 0,
  /* 11083 */ 'd', 'a', 't', 'a', '1', '6', 0,
  /* 11090 */ 'x', 's', 'h', 'a', '2', '5', '6', 0,
  /* 11098 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 11111 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 11118 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 11128 */ '#', 32, 'X', 'B', 'E', 'G', 'I', 'N', 0,
  /* 11137 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 0,
  /* 11155 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 0,
  /* 11171 */ '#', 'M', 'E', 'M', 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 11183 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 11198 */ 'a', 'a', 'a', 0,
  /* 11202 */ 'd', 'a', 'a', 0,
  /* 11206 */ 'u', 'd', '2', 'b', 0,
  /* 11211 */ 'x', 'c', 'r', 'y', 'p', 't', 'e', 'c', 'b', 0,
  /* 11221 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'f', 'b', 0,
  /* 11231 */ 'x', 'c', 'r', 'y', 'p', 't', 'o', 'f', 'b', 0,
  /* 11241 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'b', 0,
  /* 11251 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'b', 0,
  /* 11261 */ 'x', 'l', 'a', 't', 'b', 0,
  /* 11267 */ 'c', 'l', 'a', 'c', 0,
  /* 11272 */ 's', 't', 'a', 'c', 0,
  /* 11277 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'b', 'c', 0,
  /* 11287 */ 'g', 'e', 't', 's', 'e', 'c', 0,
  /* 11294 */ 's', 'a', 'l', 'c', 0,
  /* 11299 */ 'c', 'l', 'c', 0,
  /* 11303 */ 'c', 'm', 'c', 0,
  /* 11307 */ 'r', 'd', 'p', 'm', 'c', 0,
  /* 11313 */ 'v', 'm', 'f', 'u', 'n', 'c', 0,
  /* 11320 */ 'r', 'd', 't', 's', 'c', 0,
  /* 11326 */ 's', 't', 'c', 0,
  /* 11330 */ 'p', 'u', 's', 'h', 'f', 'd', 0,
  /* 11337 */ 'p', 'o', 'p', 'f', 'd', 0,
  /* 11343 */ 'c', 'p', 'u', 'i', 'd', 0,
  /* 11349 */ 'c', 'l', 'd', 0,
  /* 11353 */ 'x', 'e', 'n', 'd', 0,
  /* 11358 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'd', 0,
  /* 11368 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'd', 0,
  /* 11378 */ 'i', 'r', 'e', 't', 'd', 0,
  /* 11384 */ 's', 't', 'd', 0,
  /* 11388 */ 'w', 'b', 'i', 'n', 'v', 'd', 0,
  /* 11395 */ 'c', 'w', 'd', 0,
  /* 11399 */ 'f', 'l', 'd', 'l', '2', 'e', 0,
  /* 11406 */ 'l', 'f', 'e', 'n', 'c', 'e', 0,
  /* 11413 */ 'm', 'f', 'e', 'n', 'c', 'e', 0,
  /* 11420 */ 's', 'f', 'e', 'n', 'c', 'e', 0,
  /* 11427 */ 'c', 'w', 'd', 'e', 0,
  /* 11432 */ 'f', 's', 'c', 'a', 'l', 'e', 0,
  /* 11439 */ 'v', 'm', 'r', 'e', 's', 'u', 'm', 'e', 0,
  /* 11448 */ 'r', 'e', 'p', 'n', 'e', 0,
  /* 11454 */ 'c', 'd', 'q', 'e', 0,
  /* 11459 */ 'x', 'a', 'c', 'q', 'u', 'i', 'r', 'e', 0,
  /* 11468 */ 'x', 's', 't', 'o', 'r', 'e', 0,
  /* 11475 */ 'x', 'r', 'e', 'l', 'e', 'a', 's', 'e', 0,
  /* 11484 */ 'p', 'a', 'u', 's', 'e', 0,
  /* 11490 */ '#', 'S', 'E', 'H', '_', 'E', 'p', 'i', 'l', 'o', 'g', 'u', 'e', 0,
  /* 11504 */ '#', 'S', 'E', 'H', '_', 'E', 'n', 'd', 'P', 'r', 'o', 'l', 'o', 'g', 'u', 'e', 0,
  /* 11521 */ 'l', 'e', 'a', 'v', 'e', 0,
  /* 11527 */ 'v', 'm', 'x', 'o', 'f', 'f', 0,
  /* 11534 */ 'l', 'a', 'h', 'f', 0,
  /* 11539 */ 's', 'a', 'h', 'f', 0,
  /* 11544 */ 'p', 'u', 's', 'h', 'f', 0,
  /* 11550 */ 'p', 'o', 'p', 'f', 0,
  /* 11555 */ 'r', 'e', 't', 'f', 0,
  /* 11560 */ 'v', 'm', 'l', 'a', 'u', 'n', 'c', 'h', 0,
  /* 11569 */ 'c', 'l', 'g', 'i', 0,
  /* 11574 */ 's', 't', 'g', 'i', 0,
  /* 11579 */ 'c', 'l', 'i', 0,
  /* 11583 */ 'f', 'l', 'd', 'p', 'i', 0,
  /* 11589 */ 's', 't', 'i', 0,
  /* 11593 */ '#', 32, 'w', 'i', 'n', '3', '2', 32, 'f', 'p', 't', 'o', 'u', 'i', 0,
  /* 11608 */ 'l', 'o', 'c', 'k', 0,
  /* 11613 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'a', 'l', 0,
  /* 11624 */ 'p', 'u', 's', 'h', 'a', 'l', 0,
  /* 11631 */ 'p', 'o', 'p', 'a', 'l', 0,
  /* 11637 */ 'v', 'm', 'm', 'c', 'a', 'l', 'l', 0,
  /* 11645 */ 'v', 'm', 'c', 'a', 'l', 'l', 0,
  /* 11652 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 0,
  /* 11660 */ 'v', 'z', 'e', 'r', 'o', 'a', 'l', 'l', 0,
  /* 11669 */ 'm', 'o', 'n', 't', 'm', 'u', 'l', 0,
  /* 11677 */ 'f', 'x', 'a', 'm', 0,
  /* 11682 */ 'f', 'p', 'r', 'e', 'm', 0,
  /* 11688 */ 'f', 's', 'e', 't', 'p', 'm', 0,
  /* 11695 */ 'r', 's', 'm', 0,
  /* 11699 */ 'f', 'p', 'a', 't', 'a', 'n', 0,
  /* 11706 */ 'f', 'p', 't', 'a', 'n', 0,
  /* 11712 */ 'f', 's', 'i', 'n', 0,
  /* 11717 */ '#', 32, 'd', 'y', 'n', 'a', 'm', 'i', 'c', 32, 's', 't', 'a', 'c', 'k', 32, 'a', 'l', 'l', 'o', 'c', 'a', 't', 'i', 'o', 'n', 0,
  /* 11744 */ 'c', 'q', 'o', 0,
  /* 11748 */ 'i', 'n', 't', 'o', 0,
  /* 11753 */ 'r', 'd', 't', 's', 'c', 'p', 0,
  /* 11760 */ 'r', 'e', 'p', 0,
  /* 11764 */ 'v', 'p', 'c', 'm', 'p', 0,
  /* 11770 */ 'v', 'c', 'm', 'p', 0,
  /* 11775 */ 'f', 'n', 'o', 'p', 0,
  /* 11780 */ 'f', 'c', 'o', 'm', 'p', 'p', 0,
  /* 11787 */ 'f', 'u', 'c', 'o', 'm', 'p', 'p', 0,
  /* 11795 */ 'f', 'd', 'e', 'c', 's', 't', 'p', 0,
  /* 11803 */ 'f', 'i', 'n', 'c', 's', 't', 'p', 0,
  /* 11811 */ 'c', 'd', 'q', 0,
  /* 11815 */ 'p', 'u', 's', 'h', 'f', 'q', 0,
  /* 11822 */ 'p', 'o', 'p', 'f', 'q', 0,
  /* 11828 */ 'r', 'e', 't', 'f', 'q', 0,
  /* 11834 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'q', 0,
  /* 11844 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'q', 0,
  /* 11854 */ 'i', 'r', 'e', 't', 'q', 0,
  /* 11860 */ 'v', 'z', 'e', 'r', 'o', 'u', 'p', 'p', 'e', 'r', 0,
  /* 11871 */ 's', 'y', 's', 'e', 'n', 't', 'e', 'r', 0,
  /* 11880 */ 'm', 'o', 'n', 'i', 't', 'o', 'r', 0,
  /* 11888 */ 'r', 'd', 'm', 's', 'r', 0,
  /* 11894 */ 'w', 'r', 'm', 's', 'r', 0,
  /* 11900 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 't', 'r', 0,
  /* 11910 */ 'a', 'a', 's', 0,
  /* 11914 */ 'd', 'a', 's', 0,
  /* 11918 */ 'f', 'a', 'b', 's', 0,
  /* 11923 */ 'p', 'u', 's', 'h', 9, 'c', 's', 0,
  /* 11931 */ 'p', 'u', 's', 'h', 9, 'd', 's', 0,
  /* 11939 */ 'p', 'o', 'p', 9, 'd', 's', 0,
  /* 11946 */ 'p', 'u', 's', 'h', 9, 'e', 's', 0,
  /* 11954 */ 'p', 'o', 'p', 9, 'e', 's', 0,
  /* 11961 */ 'p', 'u', 's', 'h', 9, 'f', 's', 0,
  /* 11969 */ 'p', 'o', 'p', 9, 'f', 's', 0,
  /* 11976 */ 'p', 'u', 's', 'h', 9, 'g', 's', 0,
  /* 11984 */ 'p', 'o', 'p', 9, 'g', 's', 0,
  /* 11991 */ 's', 'w', 'a', 'p', 'g', 's', 0,
  /* 11998 */ 'f', 'c', 'h', 's', 0,
  /* 12003 */ '#', 32, 'v', 'a', 'r', 'i', 'a', 'b', 'l', 'e', 32, 's', 'i', 'z', 'e', 'd', 32, 'a', 'l', 'l', 'o', 'c', 'a', 32, 'f', 'o', 'r', 32, 's', 'e', 'g', 'm', 'e', 'n', 't', 'e', 'd', 32, 's', 't', 'a', 'c', 'k', 's', 0,
  /* 12048 */ 'e', 'n', 'c', 'l', 's', 0,
  /* 12054 */ 'f', 'e', 'm', 'm', 's', 0,
  /* 12060 */ 'f', 'c', 'o', 's', 0,
  /* 12065 */ 'f', 's', 'i', 'n', 'c', 'o', 's', 0,
  /* 12073 */ 'p', 'u', 's', 'h', 9, 's', 's', 0,
  /* 12081 */ 'p', 'o', 'p', 9, 's', 's', 0,
  /* 12088 */ 'c', 'l', 't', 's', 0,
  /* 12093 */ 'f', 'l', 'd', 'l', '2', 't', 0,
  /* 12100 */ 'f', 'x', 't', 'r', 'a', 'c', 't', 0,
  /* 12108 */ 'i', 'r', 'e', 't', 0,
  /* 12113 */ 's', 'y', 's', 'r', 'e', 't', 0,
  /* 12120 */ 'm', 'w', 'a', 'i', 't', 0,
  /* 12126 */ 'f', 'n', 'i', 'n', 'i', 't', 0,
  /* 12133 */ 's', 'y', 's', 'e', 'x', 'i', 't', 0,
  /* 12141 */ 'h', 'l', 't', 0,
  /* 12145 */ 'f', 'r', 'n', 'd', 'i', 'n', 't', 0,
  /* 12153 */ 'f', 's', 'q', 'r', 't', 0,
  /* 12159 */ 'x', 't', 'e', 's', 't', 0,
  /* 12165 */ 'f', 't', 's', 't', 0,
  /* 12170 */ 'e', 'n', 'c', 'l', 'u', 0,
  /* 12176 */ 'x', 'g', 'e', 't', 'b', 'v', 0,
  /* 12183 */ 'x', 's', 'e', 't', 'b', 'v', 0,
  /* 12190 */ 'p', 'u', 's', 'h', 'a', 'w', 0,
  /* 12197 */ 'p', 'o', 'p', 'a', 'w', 0,
  /* 12203 */ 'c', 'b', 'w', 0,
  /* 12207 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'w', 0,
  /* 12217 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'w', 0,
  /* 12227 */ 'f', 'y', 'l', '2', 'x', 0,
  /* 12233 */ 'f', 'n', 's', 't', 's', 'w', 9, 'a', 'x', 0,
  /* 12243 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'a', 'x', 0,
  /* 12254 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, 'e', 'a', 'x', 0,
  /* 12265 */ 'v', 'm', 's', 'a', 'v', 'e', 9, 'e', 'a', 'x', 0,
  /* 12276 */ 'v', 'm', 'r', 'u', 'n', 9, 'e', 'a', 'x', 0,
  /* 12286 */ 's', 'k', 'i', 'n', 'i', 't', 9, 'e', 'a', 'x', 0,
  /* 12297 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'e', 'a', 'x', 0,
  /* 12309 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, 'r', 'a', 'x', 0,
  /* 12320 */ 'v', 'm', 's', 'a', 'v', 'e', 9, 'r', 'a', 'x', 0,
  /* 12331 */ 'v', 'm', 'r', 'u', 'n', 9, 'r', 'a', 'x', 0,
  /* 12341 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, 'e', 'a', 'x', ',', 32, 'e', 'c', 'x', 0,
  /* 12358 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, 'r', 'a', 'x', ',', 32, 'e', 'c', 'x', 0,
  /* 12375 */ 'i', 'n', 9, 'a', 'l', ',', 32, 'd', 'x', 0,
  /* 12385 */ 'i', 'n', 9, 'a', 'x', ',', 32, 'd', 'x', 0,
  /* 12395 */ 'i', 'n', 9, 'e', 'a', 'x', ',', 32, 'd', 'x', 0,
  /* 12406 */ 'f', 'n', 'c', 'l', 'e', 'x', 0,
  /* 12413 */ 'f', 'l', 'd', 'z', 0,
  /* 12418 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'd', 'p', 'd', 32, 9, '{', 0,
  /* 12435 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'd', 'p', 'd', 32, 9, '{', 0,
  /* 12453 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'd', 'p', 'd', 32, 9, '{', 0,
  /* 12470 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'd', 'p', 'd', 32, 9, '{', 0,
  /* 12488 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'q', 'p', 'd', 32, 9, '{', 0,
  /* 12505 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'q', 'p', 'd', 32, 9, '{', 0,
  /* 12523 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'q', 'p', 'd', 32, 9, '{', 0,
  /* 12540 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'q', 'p', 'd', 32, 9, '{', 0,
  /* 12558 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'd', 'p', 's', 32, 9, '{', 0,
  /* 12575 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'd', 'p', 's', 32, 9, '{', 0,
  /* 12593 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'd', 'p', 's', 32, 9, '{', 0,
  /* 12610 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'd', 'p', 's', 32, 9, '{', 0,
  /* 12628 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'q', 'p', 's', 32, 9, '{', 0,
  /* 12645 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'q', 'p', 's', 32, 9, '{', 0,
  /* 12663 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'q', 'p', 's', 32, 9, '{', 0,
  /* 12680 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'q', 'p', 's', 32, 9, '{', 0,
  };
#endif

  // Emit the opcode for the instruction.
  unsigned int opcode = MCInst_getOpcode(MI);
  uint64_t Bits1 = OpInfo[opcode];
  uint64_t Bits2 = OpInfo2[opcode];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
  if (!X86_lockrep(MI, O))
#ifndef CAPSTONE_DIET
    SStream_concat0(O, AsmStrs+(Bits & 16383)-1);
#else
	;
#endif


  // Fragment 0 encoded into 6 bits for 45 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 14) & 63);
  switch ((Bits >> 14) & 63) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, AAA, AAS, ABS_F, ACQU...
    return;
    break;
  case 1:
    // AAD8i8, AAM8i8, ADC16i16, ADC16rr_REV, ADC32i32, ADC32rr_REV, ADC64i32...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADD16mi, ADD16mi8, ADD16mr, ADD_FI16m, AND...
    printi16mem(MI, 0, O); 
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // ADC32mi, ADC32mi8, ADC32mr, ADD32mi, ADD32mi8, ADD32mr, ADD_FI32m, AND...
    printi32mem(MI, 0, O); 
    break;
  case 5:
    // ADC64mi32, ADC64mi8, ADC64mr, ADD64mi32, ADD64mi8, ADD64mr, AND64mi32,...
    printi64mem(MI, 0, O); 
    break;
  case 6:
    // ADC8mi, ADC8mr, ADD8mi, ADD8mr, AND8mi, AND8mr, CLFLUSH, CMP8mi, CMP8m...
    printi8mem(MI, 0, O); 
    break;
  case 7:
    // ADD_F32m, DIVR_F32m, DIV_F32m, EXTRACTPSmr, FBLDm, FBSTPm, FCOM32m, FC...
    printf32mem(MI, 0, O); 
    break;
  case 8:
    // ADD_F64m, DIVR_F64m, DIV_F64m, FCOM64m, FCOMP64m, LD_F64m, MOVHPDmr, M...
    printf64mem(MI, 0, O); 
    break;
  case 9:
    // CALL64pcrel32, CALLpcrel16, CALLpcrel32, EH_SjLj_Setup, JAE_1, JAE_2, ...
    printPCRelImm(MI, 0, O); 
    break;
  case 10:
    // CMPPDrmi, CMPPSrmi, CMPSDrm, CMPSSrm, Int_CMPSDrm, Int_CMPSSrm
    printSSECC(MI, 7, O); 
    break;
  case 11:
    // CMPPDrri, CMPPSrri, CMPSDrr, CMPSSrr, Int_CMPSDrr, Int_CMPSSrr
    printSSECC(MI, 3, O); 
    break;
  case 12:
    // CMPSB
    printSrcIdx8(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx8(MI, 0, O); 
    return;
    break;
  case 13:
    // CMPSL
    printSrcIdx32(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx32(MI, 0, O); 
    return;
    break;
  case 14:
    // CMPSQ
    printSrcIdx64(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx64(MI, 0, O); 
    return;
    break;
  case 15:
    // CMPSW
    printSrcIdx16(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx16(MI, 0, O); 
    return;
    break;
  case 16:
    // CMPXCHG16B, LCMPXCHG16B, MOVDQAmr, MOVDQUmr, VEXTRACTI128mr, VEXTRACTI...
    printi128mem(MI, 0, O); 
    break;
  case 17:
    // FARCALL16m, FARCALL32m, FARCALL64, FARJMP16m, FARJMP32m, FARJMP64, FXR...
    printopaquemem(MI, 0, O); 
    return;
    break;
  case 18:
    // INSB, MOVSB, SCASB, STOSB
    printDstIdx8(MI, 0, O); 
    break;
  case 19:
    // INSL, MOVSL, SCASL, STOSL
    printDstIdx32(MI, 0, O); 
    break;
  case 20:
    // INSW, MOVSW, SCASW, STOSW
    printDstIdx16(MI, 0, O); 
    break;
  case 21:
    // Int_VCMPSDrm, Int_VCMPSSrm, VCMPPDYrmi, VCMPPDZrmi, VCMPPDrmi, VCMPPSY...
    printAVXCC(MI, 7, O); 
    break;
  case 22:
    // Int_VCMPSDrr, Int_VCMPSSrr, VCMPPDYrri, VCMPPDZrri, VCMPPDZrrib, VCMPP...
    printAVXCC(MI, 3, O); 
    break;
  case 23:
    // LD_F80m, ST_FP80m
    printf80mem(MI, 0, O); 
    return;
    break;
  case 24:
    // LODSB, OUTSB
    printSrcIdx8(MI, 0, O); 
    return;
    break;
  case 25:
    // LODSL, OUTSL
    printSrcIdx32(MI, 0, O); 
    return;
    break;
  case 26:
    // LODSQ
    printSrcIdx64(MI, 0, O); 
    return;
    break;
  case 27:
    // LODSW, OUTSW
    printSrcIdx16(MI, 0, O); 
    return;
    break;
  case 28:
    // LXADD16, XCHG16rm
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 29:
    // LXADD32, XCHG32rm
    printi32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 30:
    // LXADD64, XCHG64rm
    printi64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 31:
    // LXADD8, XCHG8rm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 32:
    // MOV16ao16, MOV16ao16_16, MOV16o16a, MOV16o16a_16, MOV64ao16, MOV64o16a
    printMemOffs16(MI, 0, O); 
    break;
  case 33:
    // MOV32ao32, MOV32ao32_16, MOV32o32a, MOV32o32a_16, MOV64ao32, MOV64o32a
    printMemOffs32(MI, 0, O); 
    break;
  case 34:
    // MOV64ao64, MOV64o64a
    printMemOffs64(MI, 0, O); 
    break;
  case 35:
    // MOV64ao8, MOV64o8a, MOV8ao8, MOV8ao8_16, MOV8o8a, MOV8o8a_16
    printMemOffs8(MI, 0, O); 
    break;
  case 36:
    // MOVAPDmr, MOVAPSmr, MOVNTDQmr, MOVNTPDmr, MOVNTPSmr, MOVUPDmr, MOVUPSm...
    printf128mem(MI, 0, O); 
    break;
  case 37:
    // MOVSQ, SCASQ, STOSQ
    printDstIdx64(MI, 0, O); 
    break;
  case 38:
    // VCVTPS2PHZmr, VEXTRACTF64x4mr, VMASKMOVPDYmr, VMASKMOVPSYmr, VMOVAPDYm...
    printf256mem(MI, 0, O); 
    break;
  case 39:
    // VEXTRACTI64x4mr, VMOVDQA32Z256mr, VMOVDQA32Z256mrk, VMOVDQA64Z256mr, V...
    printi256mem(MI, 0, O); 
    break;
  case 40:
    // VMOVAPDZmr, VMOVAPDZmrk, VMOVAPSZmr, VMOVAPSZmrk, VMOVNTPDZmr, VMOVNTP...
    printf512mem(MI, 0, O); 
    break;
  case 41:
    // VMOVDQA32Zmr, VMOVDQA32Zmrk, VMOVDQA64Zmr, VMOVDQA64Zmrk, VMOVDQU16Zmr...
    printi512mem(MI, 0, O); 
    break;
  case 42:
    // VPSCATTERDDZmr, VSCATTERDPSZmr
    printi32mem(MI, 1, O); 
    SStream_concat0(O, " {"); 
    printOperand(MI, 6, O); 
    SStream_concat0(O, "}, "); 
    printOperand(MI, 7, O); 
    return;
    break;
  case 43:
    // VPSCATTERDQZmr, VPSCATTERQDZmr, VPSCATTERQQZmr, VSCATTERDPDZmr, VSCATT...
    printi64mem(MI, 1, O); 
    SStream_concat0(O, " {"); 
    printOperand(MI, 6, O); 
    SStream_concat0(O, "}, "); 
    printOperand(MI, 7, O); 
    return;
    break;
  case 44:
    // XCHG16rr, XCHG32rr, XCHG64rr, XCHG8rr
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 5 bits for 24 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 20) & 31);
  switch ((Bits >> 20) & 31) {
  default:   // unreachable.
  case 0:
    // AAD8i8, AAM8i8, ADC16i16, ADC32i32, ADC64i32, ADC8i8, ADD16i16, ADD32i...
    return;
    break;
  case 1:
    // ADC16mi, ADC16mi8, ADC16mr, ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC16...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // ADD_FrST0, DIVR_FrST0, DIV_FrST0, MUL_FrST0, ST_FPNCEST0r, ST_FPST0r, ...
    SStream_concat0(O, ", st(0)"); 
    op_addReg(MI, X86_REG_ST0);
    return;
    break;
  case 3:
    // CMPPDrmi, CMPPDrri, VCMPPDYrmi, VCMPPDYrri, VCMPPDrmi, VCMPPDrri
    SStream_concat0(O, "pd\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // CMPPSrmi, CMPPSrri, VCMPPSYrmi, VCMPPSYrri, VCMPPSrmi, VCMPPSrri
    SStream_concat0(O, "ps\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // CMPSDrm, CMPSDrr, Int_CMPSDrm, Int_CMPSDrr, Int_VCMPSDrm, Int_VCMPSDrr...
    SStream_concat0(O, "sd\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // CMPSSrm, CMPSSrr, Int_CMPSSrm, Int_CMPSSrr, Int_VCMPSSrm, Int_VCMPSSrr...
    SStream_concat0(O, "ss\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 7:
    // FARCALL16i, FARCALL32i, FARJMP16i, FARJMP32i
    SStream_concat0(O, ":"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 8:
    // INSB, INSL, INSW
    SStream_concat0(O, ", dx"); 
    op_addReg(MI, X86_REG_DX);
    return;
    break;
  case 9:
    // MOV16ao16, MOV16ao16_16, MOV64ao16, OUT16ir, STOSW
    SStream_concat0(O, ", ax"); 
    op_addReg(MI, X86_REG_AX);
    return;
    break;
  case 10:
    // MOV32ao32, MOV32ao32_16, MOV64ao32, OUT32ir, STOSL
    SStream_concat0(O, ", eax"); 
    op_addReg(MI, X86_REG_EAX);
    return;
    break;
  case 11:
    // MOV64ao64, STOSQ
    SStream_concat0(O, ", rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 12:
    // MOV64ao8, MOV8ao8, MOV8ao8_16, OUT8ir, STOSB
    SStream_concat0(O, ", al"); 
    op_addReg(MI, X86_REG_AL);
    return;
    break;
  case 13:
    // RCL16m1, RCL16r1, RCL32m1, RCL32r1, RCL64m1, RCL64r1, RCL8m1, RCL8r1, ...
    SStream_concat0(O, ", 1"); 
    op_addImm(MI, 1);
    return;
    break;
  case 14:
    // RCL16mCL, RCL16rCL, RCL32mCL, RCL32rCL, RCL64mCL, RCL64rCL, RCL8mCL, R...
    SStream_concat0(O, ", cl"); 
    op_addReg(MI, X86_REG_CL);
    return;
    break;
  case 15:
    // TAILJMPd, TAILJMPd64, TAILJMPm, TAILJMPm64, TAILJMPr64
    return;
    break;
  case 16:
    // VADDPDZrmbk, VADDPDZrmbkz, VADDPDZrmk, VADDPDZrmkz, VADDPDZrrk, VADDPD...
    SStream_concat0(O, " {"); 
    break;
  case 17:
    // VCMPPDZrmi, VCMPPDZrri, VCMPPDZrrib
    SStream_concat0(O, "pd \t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 18:
    // VCMPPSZrmi, VCMPPSZrri, VCMPPSZrrib
    SStream_concat0(O, "ps \t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // VGATHERPF0DPDm, VGATHERPF0DPSm, VGATHERPF0QPDm, VGATHERPF0QPSm, VGATHE...
    SStream_concat0(O, "}, "); 
    break;
  case 20:
    // VPCMPDZrmi, VPCMPDZrri
    SStream_concat0(O, "d\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 21:
    // VPCMPQZrmi, VPCMPQZrri
    SStream_concat0(O, "q\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // VPCMPUDZrmi, VPCMPUDZrri
    SStream_concat0(O, "ud\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 23:
    // VPCMPUQZrmi, VPCMPUQZrri
    SStream_concat0(O, "uq\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  }


  // Fragment 2 encoded into 6 bits for 34 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 25) & 63);
  switch ((Bits >> 25) & 63) {
  default:   // unreachable.
  case 0:
    // ADC16mi, ADC16mi8, ADC16mr, ADC32mi, ADC32mi8, ADC32mr, ADC64mi32, ADC...
    printOperand(MI, 5, O); 
    break;
  case 1:
    // ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, ADC32rr, A...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // ADC16rm, ADD16rm, AND16rm, CMOVA16rm, CMOVAE16rm, CMOVB16rm, CMOVBE16r...
    printi16mem(MI, 2, O); 
    break;
  case 3:
    // ADC32rm, ADCX32rm, ADD32rm, AND32rm, CMOVA32rm, CMOVAE32rm, CMOVB32rm,...
    printi32mem(MI, 2, O); 
    break;
  case 4:
    // ADC64rm, ADCX64rm, ADD64rm, AND64rm, CMOVA64rm, CMOVAE64rm, CMOVB64rm,...
    printi64mem(MI, 2, O); 
    break;
  case 5:
    // ADC8rm, ADD8rm, AND8rm, CRC32r32m8, CRC32r64m8, OR8rm, PINSRBrm, SBB8r...
    printi8mem(MI, 2, O); 
    break;
  case 6:
    // ADDPDrm, ADDPSrm, ADDSUBPDrm, ADDSUBPSrm, ANDNPDrm, ANDNPSrm, ANDPDrm,...
    printf128mem(MI, 2, O); 
    break;
  case 7:
    // ADDSDrm, ADDSDrm_Int, CMPSDrm, CMPSDrm_alt, DIVSDrm, DIVSDrm_Int, Int_...
    printf64mem(MI, 2, O); 
    break;
  case 8:
    // ADDSSrm, ADDSSrm_Int, CMPSSrm, CMPSSrm_alt, DIVSSrm, DIVSSrm_Int, INSE...
    printf32mem(MI, 2, O); 
    break;
  case 9:
    // ADOX32rm, BEXTR32rm, BEXTRI32mi, BLCFILL32rm, BLCI32rm, BLCIC32rm, BLC...
    printi32mem(MI, 1, O); 
    break;
  case 10:
    // ADOX32rr, ADOX64rr, AESIMCrr, AESKEYGENASSIST128rr, ANDN32rm, ANDN32rr...
    printOperand(MI, 1, O); 
    break;
  case 11:
    // ADOX64rm, BEXTR64rm, BEXTRI64mi, BLCFILL64rm, BLCI64rm, BLCIC64rm, BLC...
    printi64mem(MI, 1, O); 
    break;
  case 12:
    // AESDECLASTrm, AESDECrm, AESENCLASTrm, AESENCrm, MPSADBWrmi, PACKSSDWrm...
    printi128mem(MI, 2, O); 
    break;
  case 13:
    // AESIMCrm, AESKEYGENASSIST128rm, CVTDQ2PSrm, INVEPT32, INVEPT64, INVPCI...
    printi128mem(MI, 1, O); 
    break;
  case 14:
    // BSF16rm, BSR16rm, CMP16rm, IMUL16rmi, IMUL16rmi8, KMOVWkm, LAR16rm, LA...
    printi16mem(MI, 1, O); 
    break;
  case 15:
    // CMP8rm, KMOVBkm, MOV8rm, MOV8rm_NOREX, MOVSX16rm8, MOVSX32rm8, MOVSX64...
    printi8mem(MI, 1, O); 
    break;
  case 16:
    // COMISDrm, COMISSrm, CVTPD2DQrm, CVTPD2PSrm, CVTPS2DQrm, CVTTPD2DQrm, C...
    printf128mem(MI, 1, O); 
    break;
  case 17:
    // CVTPS2PDrm, CVTSD2SI64rm, CVTSD2SIrm, CVTSD2SSrm, CVTTSD2SI64rm, CVTTS...
    printf64mem(MI, 1, O); 
    return;
    break;
  case 18:
    // CVTSS2SDrm, CVTSS2SI64rm, CVTSS2SIrm, CVTTSS2SI64rm, CVTTSS2SIrm, Int_...
    printf32mem(MI, 1, O); 
    return;
    break;
  case 19:
    // LDS16rm, LDS32rm, LES16rm, LES32rm, LFS16rm, LFS32rm, LFS64rm, LGS16rm...
    printopaquemem(MI, 1, O); 
    return;
    break;
  case 20:
    // MOVSB
    printSrcIdx8(MI, 1, O); 
    return;
    break;
  case 21:
    // MOVSL
    printSrcIdx32(MI, 1, O); 
    return;
    break;
  case 22:
    // MOVSQ
    printSrcIdx64(MI, 1, O); 
    return;
    break;
  case 23:
    // MOVSW
    printSrcIdx16(MI, 1, O); 
    return;
    break;
  case 24:
    // NOOP19rr
    printOperand(MI, 0, O); 
    return;
    break;
  case 25:
    // VBROADCASTI64X4rm, VCVTDQ2PDZrm, VCVTDQ2PSYrm, VLDDQUYrm, VMOVDQA32Z25...
    printi256mem(MI, 1, O); 
    break;
  case 26:
    // VCMPPDZrmi, VCMPPSZrmi
    printf512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAVXCC(MI, 7, O); 
    return;
    break;
  case 27:
    // VCVTDQ2PSZrm, VMOVDQA32Zrm, VMOVDQA64Zrm, VMOVDQU16Zrm, VMOVDQU32Zrm, ...
    printi512mem(MI, 1, O); 
    break;
  case 28:
    // VCVTPD2DQYrm, VCVTPD2PSYrm, VCVTPH2PSZrm, VCVTPS2DQYrm, VCVTPS2PDZrm, ...
    printf256mem(MI, 1, O); 
    break;
  case 29:
    // VCVTPD2DQZrm, VCVTPD2PSZrm, VCVTPD2UDQZrm, VCVTPS2DQZrm, VCVTPS2UDQZrm...
    printf512mem(MI, 1, O); 
    break;
  case 30:
    // VGATHERDPDYrm, VGATHERDPDrm, VGATHERQPDYrm, VGATHERQPDrm, VPGATHERDQYr...
    printi64mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
    break;
  case 31:
    // VGATHERDPDZrm, VGATHERDPSZrm, VGATHERQPDZrm, VGATHERQPSZrm, VPGATHERDD...
    printOperand(MI, 3, O); 
    SStream_concat0(O, "}, "); 
    break;
  case 32:
    // VGATHERDPSYrm, VGATHERDPSrm, VGATHERQPSYrm, VGATHERQPSrm, VPGATHERDDYr...
    printi32mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
    break;
  case 33:
    // VPCMPDZrmi, VPCMPQZrmi, VPCMPUDZrmi, VPCMPUQZrmi
    printi512mem(MI, 2, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 4 bits for 11 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 31) & 15);
  switch ((Bits >> 31) & 15) {
  default:   // unreachable.
  case 0:
    // ADC16mi, ADC16mi8, ADC16mr, ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC16...
    return;
    break;
  case 1:
    // AESKEYGENASSIST128rm, AESKEYGENASSIST128rr, ANDN32rm, ANDN32rr, ANDN64...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // MOV8mr_NOREX, MOV8rm_NOREX, MOV8rr_NOREX
    return;
    break;
  case 3:
    // SHLD16mrCL, SHLD16rrCL, SHLD32mrCL, SHLD32rrCL, SHLD64mrCL, SHLD64rrCL...
    SStream_concat0(O, ", cl"); 
    op_addReg(MI, X86_REG_CL);
    return;
    break;
  case 4:
    // VADDPDZrmbk, VADDPDZrmk, VADDPDZrrk, VADDPSZrmbk, VADDPSZrmk, VADDPSZr...
    SStream_concat0(O, "}, "); 
    break;
  case 5:
    // VADDPDZrmbkz, VADDPDZrmkz, VADDPDZrrkz, VADDPSZrmbkz, VADDPSZrmkz, VAD...
    SStream_concat0(O, "} {z}, "); 
    op_addAvxZeroOpmask(MI);
    break;
  case 6:
    // VCMPPDZrrib, VCMPPSZrrib, VRCP28PDZrb, VRCP28PSZrb, VRSQRT28PDZrb, VRS...
    SStream_concat0(O, ", {sae}"); 
    op_addAvxSae(MI);
    return;
    break;
  case 7:
    // VGATHERDPDZrm, VGATHERQPDZrm, VGATHERQPSZrm, VPGATHERDQZrm, VPGATHERQD...
    printi64mem(MI, 4, O); 
    return;
    break;
  case 8:
    // VGATHERDPSZrm, VPGATHERDDZrm
    printi32mem(MI, 4, O); 
    return;
    break;
  case 9:
    // VPABSDZrmb, VPCONFLICTDrmb, VPLZCNTDrmb
    SStream_concat0(O, "{1to16}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_16);
    return;
    break;
  case 10:
    // VPABSQZrmb, VPCONFLICTQrmb, VPLZCNTQrmb
    SStream_concat0(O, "{1to8}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_8);
    return;
    break;
  }


  // Fragment 4 encoded into 5 bits for 27 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 35) & 31);
  switch ((Bits >> 35) & 31) {
  default:   // unreachable.
  case 0:
    // AESKEYGENASSIST128rm, BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZ...
    printOperand(MI, 6, O); 
    break;
  case 1:
    // AESKEYGENASSIST128rr, ANDN32rr, ANDN64rr, BEXTR32rr, BEXTR64rr, BEXTRI...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // ANDN32rm, Int_VCVTSI2SDZrm, Int_VCVTSI2SDrm, Int_VCVTSI2SSZrm, Int_VCV...
    printi32mem(MI, 2, O); 
    break;
  case 3:
    // ANDN64rm, Int_VCVTSI2SD64Zrm, Int_VCVTSI2SD64rm, Int_VCVTSI2SS64Zrm, I...
    printi64mem(MI, 2, O); 
    break;
  case 4:
    // BLENDPDrmi, BLENDPSrmi, CMPPDrmi_alt, CMPPSrmi_alt, CMPSDrm_alt, CMPSS...
    printOperand(MI, 7, O); 
    return;
    break;
  case 5:
    // BLENDPDrri, BLENDPSrri, CMPPDrri_alt, CMPPSrri_alt, CMPSDrr_alt, CMPSS...
    printOperand(MI, 3, O); 
    break;
  case 6:
    // Int_VCMPSDrm, Int_VCVTSD2SSrm, VADDPDZrmb, VADDSDZrm, VADDSDrm, VADDSD...
    printf64mem(MI, 2, O); 
    break;
  case 7:
    // Int_VCMPSSrm, Int_VCVTSS2SDrm, VADDPSZrmb, VADDSSZrm, VADDSSrm, VADDSS...
    printf32mem(MI, 2, O); 
    break;
  case 8:
    // VADDPDYrm, VADDPSYrm, VADDSUBPDYrm, VADDSUBPSYrm, VANDNPDYrm, VANDNPSY...
    printf256mem(MI, 2, O); 
    break;
  case 9:
    // VADDPDZrm, VADDPSZrm, VCMPPDZrmi_alt, VCMPPSZrmi_alt, VDIVPDZrm, VDIVP...
    printf512mem(MI, 2, O); 
    break;
  case 10:
    // VADDPDrm, VADDPSrm, VADDSUBPDrm, VADDSUBPSrm, VANDNPDrm, VANDNPSrm, VA...
    printf128mem(MI, 2, O); 
    break;
  case 11:
    // VAESDECLASTrm, VAESDECrm, VAESENCLASTrm, VAESENCrm, VBROADCASTI32X4krm...
    printi128mem(MI, 2, O); 
    break;
  case 12:
    // VALIGNDrmi, VALIGNQrmi, VMOVDQA32Zrmkz, VMOVDQA64Zrmkz, VMOVDQU16Zrmkz...
    printi512mem(MI, 2, O); 
    break;
  case 13:
    // VBROADCASTI64X4krm, VDPPSYrmi, VINSERTF64x4rm, VINSERTI64x4rm, VMOVDQA...
    printi256mem(MI, 2, O); 
    break;
  case 14:
    // VCVTDQ2PSZrrb, VCVTPD2DQZrrb, VCVTPD2PSZrrb, VCVTPD2UDQZrrb, VCVTPS2DQ...
    printRoundingControl(MI, 2, O); 
    return;
    break;
  case 15:
    // VFMADD132PDZm, VFMADD132PSZm, VFMADD213PDZm, VFMADD213PSZm, VFMADDSUB1...
    printf512mem(MI, 3, O); 
    return;
    break;
  case 16:
    // VFMADD132PDZmb, VFMADD213PDZmb, VFMADDSDr132m, VFMADDSDr213m, VFMADDSD...
    printf64mem(MI, 3, O); 
    break;
  case 17:
    // VFMADD132PSZmb, VFMADD213PSZmb, VFMADDSSr132m, VFMADDSSr213m, VFMADDSS...
    printf32mem(MI, 3, O); 
    break;
  case 18:
    // VFMADDPDr132m, VFMADDPDr213m, VFMADDPDr231m, VFMADDPSr132m, VFMADDPSr2...
    printf128mem(MI, 3, O); 
    return;
    break;
  case 19:
    // VFMADDPDr132mY, VFMADDPDr213mY, VFMADDPDr231mY, VFMADDPSr132mY, VFMADD...
    printf256mem(MI, 3, O); 
    return;
    break;
  case 20:
    // VMOVDQA32Z128rmk, VMOVDQA64Z128rmk, VMOVDQU16Z128rmk, VMOVDQU32Z128rmk...
    printi128mem(MI, 3, O); 
    return;
    break;
  case 21:
    // VMOVDQA32Z256rmk, VMOVDQA64Z256rmk, VMOVDQU16Z256rmk, VMOVDQU32Z256rmk...
    printi256mem(MI, 3, O); 
    return;
    break;
  case 22:
    // VMOVDQA32Zrmk, VMOVDQA64Zrmk, VMOVDQU16Zrmk, VMOVDQU32Zrmk, VMOVDQU64Z...
    printi512mem(MI, 3, O); 
    return;
    break;
  case 23:
    // VPCONFLICTDrmbk, VPLZCNTDrmbk
    printi32mem(MI, 3, O); 
    SStream_concat0(O, "{1to16}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_16);
    return;
    break;
  case 24:
    // VPCONFLICTQrmbk, VPLZCNTQrmbk
    printi64mem(MI, 3, O); 
    SStream_concat0(O, "{1to8}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_8);
    return;
    break;
  case 25:
    // VPINSRBrm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    return;
    break;
  case 26:
    // VPINSRWrmi
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 3 bits for 7 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 40) & 7);
  switch ((Bits >> 40) & 7) {
  default:   // unreachable.
  case 0:
    // AESKEYGENASSIST128rm, AESKEYGENASSIST128rr, ANDN32rm, ANDN32rr, ANDN64...
    return;
    break;
  case 1:
    // INSERTQI, VAARG_64, VADDPDZrmbk, VADDPDZrmbkz, VADDPDZrmk, VADDPDZrmkz...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // VADDPDZrmb, VDIVPDZrmb, VFMADD132PDZmb, VFMADD213PDZmb, VFMADDSUB132PD...
    SStream_concat0(O, "{1to8}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_8);
    return;
    break;
  case 3:
    // VADDPSZrmb, VDIVPSZrmb, VFMADD132PSZmb, VFMADD213PSZmb, VFMADDSUB132PS...
    SStream_concat0(O, "{1to16}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_16);
    return;
    break;
  case 4:
    // VPCMPEQDZ128rmb, VPCMPEQQZ256rmb, VPCMPGTDZ128rmb, VPCMPGTQZ256rmb
    SStream_concat0(O, "{1to4}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_4);
    return;
    break;
  case 5:
    // VPCMPEQQZ128rmb, VPCMPGTQZ128rmb
    SStream_concat0(O, "{1to2}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_2);
    return;
    break;
  case 6:
    // VRCP28SDrrb, VRCP28SSrrb, VRSQRT28SDrrb, VRSQRT28SSrrb
    SStream_concat0(O, ", {sae}"); 
    op_addAvxSae(MI);
    return;
    break;
  }


  // Fragment 6 encoded into 4 bits for 16 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 43) & 15);
  switch ((Bits >> 43) & 15) {
  default:   // unreachable.
  case 0:
    // INSERTQI, VALIGNDrrik, VALIGNQrrik, VFMADD213PDZrk, VFMADD213PDZrkz, V...
    printOperand(MI, 4, O); 
    break;
  case 1:
    // VAARG_64, VALIGNDrmi, VALIGNQrmi, VBLENDPDYrmi, VBLENDPDrmi, VBLENDPSY...
    printOperand(MI, 7, O); 
    break;
  case 2:
    // VADDPDZrmbk, VADDPDZrmbkz, VDIVPDZrmbk, VDIVPDZrmbkz, VFMADDSD4rm, VFM...
    printf64mem(MI, 3, O); 
    break;
  case 3:
    // VADDPDZrmk, VADDPDZrmkz, VADDPSZrmk, VADDPSZrmkz, VBLENDMPDZrm, VBLEND...
    printf512mem(MI, 3, O); 
    return;
    break;
  case 4:
    // VADDPDZrrk, VADDPDZrrkz, VADDPSZrrk, VADDPSZrrkz, VALIGNDrri, VALIGNDr...
    printOperand(MI, 3, O); 
    break;
  case 5:
    // VADDPSZrmbk, VADDPSZrmbkz, VDIVPSZrmbk, VDIVPSZrmbkz, VFMADDSS4rm, VFM...
    printf32mem(MI, 3, O); 
    break;
  case 6:
    // VFMADDPD4rm, VFMADDPS4rm, VFMADDSUBPD4rm, VFMADDSUBPS4rm, VFMSUBADDPD4...
    printf128mem(MI, 3, O); 
    break;
  case 7:
    // VFMADDPD4rmY, VFMADDPS4rmY, VFMADDSUBPD4rmY, VFMADDSUBPS4rmY, VFMSUBAD...
    printf256mem(MI, 3, O); 
    break;
  case 8:
    // VPADDDZrmbk, VPANDDZrmbk, VPANDNDZrmbk, VPMAXSDZrmbk, VPMAXUDZrmbk, VP...
    printi32mem(MI, 4, O); 
    SStream_concat0(O, "{1to16}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_16);
    return;
    break;
  case 9:
    // VPADDDZrmbkz, VPANDDZrmbkz, VPANDNDZrmbkz, VPCMPEQDZ128rmbk, VPCMPEQDZ...
    printi32mem(MI, 3, O); 
    break;
  case 10:
    // VPADDDZrmk, VPADDQZrmk, VPANDDZrmk, VPANDNDZrmk, VPANDNQZrmk, VPANDQZr...
    printi512mem(MI, 4, O); 
    return;
    break;
  case 11:
    // VPADDDZrmkz, VPADDQZrmkz, VPANDDZrmkz, VPANDNDZrmkz, VPANDNQZrmkz, VPA...
    printi512mem(MI, 3, O); 
    break;
  case 12:
    // VPADDQZrmbk, VPANDNQZrmbk, VPANDQZrmbk, VPMAXSQZrmbk, VPMAXUQZrmbk, VP...
    printi64mem(MI, 4, O); 
    SStream_concat0(O, "{1to8}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_8);
    return;
    break;
  case 13:
    // VPADDQZrmbkz, VPANDNQZrmbkz, VPANDQZrmbkz, VPCMPEQQZ128rmbk, VPCMPEQQZ...
    printi64mem(MI, 3, O); 
    break;
  case 14:
    // VPCMOVrm, VPCMPEQBZ128rmk, VPCMPEQDZ128rmk, VPCMPEQQZ128rmk, VPCMPEQWZ...
    printi128mem(MI, 3, O); 
    return;
    break;
  case 15:
    // VPCMOVrmY, VPCMPEQBZ256rmk, VPCMPEQDZ256rmk, VPCMPEQQZ256rmk, VPCMPEQW...
    printi256mem(MI, 3, O); 
    return;
    break;
  }


  // Fragment 7 encoded into 3 bits for 6 unique commands.
  //printf("Frag-7: %"PRIu64"\n", (Bits >> 47) & 7);
  switch ((Bits >> 47) & 7) {
  default:   // unreachable.
  case 0:
    // INSERTQI, VADDPDZrrk, VADDPDZrrkz, VADDPSZrrk, VADDPSZrrkz, VALIGNDrmi...
    return;
    break;
  case 1:
    // VAARG_64, VALIGNDrrik, VALIGNDrrikz, VALIGNQrrik, VALIGNQrrikz, VPCMPD...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // VADDPDZrmbk, VADDPDZrmbkz, VDIVPDZrmbk, VDIVPDZrmbkz, VMAXPDZrmbk, VMA...
    SStream_concat0(O, "{1to8}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_8);
    return;
    break;
  case 3:
    // VADDPSZrmbk, VADDPSZrmbkz, VDIVPSZrmbk, VDIVPSZrmbkz, VMAXPSZrmbk, VMA...
    SStream_concat0(O, "{1to16}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_16);
    return;
    break;
  case 4:
    // VPCMPEQDZ128rmbk, VPCMPEQQZ256rmbk, VPCMPGTDZ128rmbk, VPCMPGTQZ256rmbk
    SStream_concat0(O, "{1to4}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_4);
    return;
    break;
  case 5:
    // VPCMPEQQZ128rmbk, VPCMPGTQZ128rmbk
    SStream_concat0(O, "{1to2}"); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_2);
    return;
    break;
  }


  // Fragment 8 encoded into 2 bits for 3 unique commands.
  //printf("Frag-8: %"PRIu64"\n", (Bits >> 50) & 3);
  switch ((Bits >> 50) & 3) {
  default:   // unreachable.
  case 0:
    // VAARG_64, VPCMPDZrmik_alt, VPCMPQZrmik_alt, VPCMPUDZrmik_alt, VPCMPUQZ...
    printOperand(MI, 8, O); 
    return;
    break;
  case 1:
    // VALIGNDrrik, VALIGNQrrik
    printOperand(MI, 5, O); 
    return;
    break;
  case 2:
    // VALIGNDrrikz, VALIGNQrrikz, VPCMPDZrrik_alt, VPCMPQZrrik_alt, VPCMPUDZ...
    printOperand(MI, 4, O); 
    return;
    break;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 234 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 's', 't', '(', '0', ')', 0,
  /* 6 */ 's', 't', '(', '1', ')', 0,
  /* 12 */ 's', 't', '(', '2', ')', 0,
  /* 18 */ 's', 't', '(', '3', ')', 0,
  /* 24 */ 's', 't', '(', '4', ')', 0,
  /* 30 */ 's', 't', '(', '5', ')', 0,
  /* 36 */ 's', 't', '(', '6', ')', 0,
  /* 42 */ 's', 't', '(', '7', ')', 0,
  /* 48 */ 'x', 'm', 'm', '1', '0', 0,
  /* 54 */ 'y', 'm', 'm', '1', '0', 0,
  /* 60 */ 'z', 'm', 'm', '1', '0', 0,
  /* 66 */ 'c', 'r', '1', '0', 0,
  /* 71 */ 'x', 'm', 'm', '2', '0', 0,
  /* 77 */ 'y', 'm', 'm', '2', '0', 0,
  /* 83 */ 'z', 'm', 'm', '2', '0', 0,
  /* 89 */ 'x', 'm', 'm', '3', '0', 0,
  /* 95 */ 'y', 'm', 'm', '3', '0', 0,
  /* 101 */ 'z', 'm', 'm', '3', '0', 0,
  /* 107 */ 'k', '0', 0,
  /* 110 */ 'x', 'm', 'm', '0', 0,
  /* 115 */ 'y', 'm', 'm', '0', 0,
  /* 120 */ 'z', 'm', 'm', '0', 0,
  /* 125 */ 'f', 'p', '0', 0,
  /* 129 */ 'c', 'r', '0', 0,
  /* 133 */ 'd', 'r', '0', 0,
  /* 137 */ 'x', 'm', 'm', '1', '1', 0,
  /* 143 */ 'y', 'm', 'm', '1', '1', 0,
  /* 149 */ 'z', 'm', 'm', '1', '1', 0,
  /* 155 */ 'c', 'r', '1', '1', 0,
  /* 160 */ 'x', 'm', 'm', '2', '1', 0,
  /* 166 */ 'y', 'm', 'm', '2', '1', 0,
  /* 172 */ 'z', 'm', 'm', '2', '1', 0,
  /* 178 */ 'x', 'm', 'm', '3', '1', 0,
  /* 184 */ 'y', 'm', 'm', '3', '1', 0,
  /* 190 */ 'z', 'm', 'm', '3', '1', 0,
  /* 196 */ 'k', '1', 0,
  /* 199 */ 'x', 'm', 'm', '1', 0,
  /* 204 */ 'y', 'm', 'm', '1', 0,
  /* 209 */ 'z', 'm', 'm', '1', 0,
  /* 214 */ 'f', 'p', '1', 0,
  /* 218 */ 'c', 'r', '1', 0,
  /* 222 */ 'd', 'r', '1', 0,
  /* 226 */ 'x', 'm', 'm', '1', '2', 0,
  /* 232 */ 'y', 'm', 'm', '1', '2', 0,
  /* 238 */ 'z', 'm', 'm', '1', '2', 0,
  /* 244 */ 'c', 'r', '1', '2', 0,
  /* 249 */ 'x', 'm', 'm', '2', '2', 0,
  /* 255 */ 'y', 'm', 'm', '2', '2', 0,
  /* 261 */ 'z', 'm', 'm', '2', '2', 0,
  /* 267 */ 'k', '2', 0,
  /* 270 */ 'x', 'm', 'm', '2', 0,
  /* 275 */ 'y', 'm', 'm', '2', 0,
  /* 280 */ 'z', 'm', 'm', '2', 0,
  /* 285 */ 'f', 'p', '2', 0,
  /* 289 */ 'c', 'r', '2', 0,
  /* 293 */ 'd', 'r', '2', 0,
  /* 297 */ 'x', 'm', 'm', '1', '3', 0,
  /* 303 */ 'y', 'm', 'm', '1', '3', 0,
  /* 309 */ 'z', 'm', 'm', '1', '3', 0,
  /* 315 */ 'c', 'r', '1', '3', 0,
  /* 320 */ 'x', 'm', 'm', '2', '3', 0,
  /* 326 */ 'y', 'm', 'm', '2', '3', 0,
  /* 332 */ 'z', 'm', 'm', '2', '3', 0,
  /* 338 */ 'k', '3', 0,
  /* 341 */ 'x', 'm', 'm', '3', 0,
  /* 346 */ 'y', 'm', 'm', '3', 0,
  /* 351 */ 'z', 'm', 'm', '3', 0,
  /* 356 */ 'f', 'p', '3', 0,
  /* 360 */ 'c', 'r', '3', 0,
  /* 364 */ 'd', 'r', '3', 0,
  /* 368 */ 'x', 'm', 'm', '1', '4', 0,
  /* 374 */ 'y', 'm', 'm', '1', '4', 0,
  /* 380 */ 'z', 'm', 'm', '1', '4', 0,
  /* 386 */ 'c', 'r', '1', '4', 0,
  /* 391 */ 'x', 'm', 'm', '2', '4', 0,
  /* 397 */ 'y', 'm', 'm', '2', '4', 0,
  /* 403 */ 'z', 'm', 'm', '2', '4', 0,
  /* 409 */ 'k', '4', 0,
  /* 412 */ 'x', 'm', 'm', '4', 0,
  /* 417 */ 'y', 'm', 'm', '4', 0,
  /* 422 */ 'z', 'm', 'm', '4', 0,
  /* 427 */ 'f', 'p', '4', 0,
  /* 431 */ 'c', 'r', '4', 0,
  /* 435 */ 'd', 'r', '4', 0,
  /* 439 */ 'x', 'm', 'm', '1', '5', 0,
  /* 445 */ 'y', 'm', 'm', '1', '5', 0,
  /* 451 */ 'z', 'm', 'm', '1', '5', 0,
  /* 457 */ 'c', 'r', '1', '5', 0,
  /* 462 */ 'x', 'm', 'm', '2', '5', 0,
  /* 468 */ 'y', 'm', 'm', '2', '5', 0,
  /* 474 */ 'z', 'm', 'm', '2', '5', 0,
  /* 480 */ 'k', '5', 0,
  /* 483 */ 'x', 'm', 'm', '5', 0,
  /* 488 */ 'y', 'm', 'm', '5', 0,
  /* 493 */ 'z', 'm', 'm', '5', 0,
  /* 498 */ 'f', 'p', '5', 0,
  /* 502 */ 'c', 'r', '5', 0,
  /* 506 */ 'd', 'r', '5', 0,
  /* 510 */ 'x', 'm', 'm', '1', '6', 0,
  /* 516 */ 'y', 'm', 'm', '1', '6', 0,
  /* 522 */ 'z', 'm', 'm', '1', '6', 0,
  /* 528 */ 'x', 'm', 'm', '2', '6', 0,
  /* 534 */ 'y', 'm', 'm', '2', '6', 0,
  /* 540 */ 'z', 'm', 'm', '2', '6', 0,
  /* 546 */ 'k', '6', 0,
  /* 549 */ 'x', 'm', 'm', '6', 0,
  /* 554 */ 'y', 'm', 'm', '6', 0,
  /* 559 */ 'z', 'm', 'm', '6', 0,
  /* 564 */ 'f', 'p', '6', 0,
  /* 568 */ 'c', 'r', '6', 0,
  /* 572 */ 'd', 'r', '6', 0,
  /* 576 */ 'x', 'm', 'm', '1', '7', 0,
  /* 582 */ 'y', 'm', 'm', '1', '7', 0,
  /* 588 */ 'z', 'm', 'm', '1', '7', 0,
  /* 594 */ 'x', 'm', 'm', '2', '7', 0,
  /* 600 */ 'y', 'm', 'm', '2', '7', 0,
  /* 606 */ 'z', 'm', 'm', '2', '7', 0,
  /* 612 */ 'k', '7', 0,
  /* 615 */ 'x', 'm', 'm', '7', 0,
  /* 620 */ 'y', 'm', 'm', '7', 0,
  /* 625 */ 'z', 'm', 'm', '7', 0,
  /* 630 */ 'f', 'p', '7', 0,
  /* 634 */ 'c', 'r', '7', 0,
  /* 638 */ 'd', 'r', '7', 0,
  /* 642 */ 'x', 'm', 'm', '1', '8', 0,
  /* 648 */ 'y', 'm', 'm', '1', '8', 0,
  /* 654 */ 'z', 'm', 'm', '1', '8', 0,
  /* 660 */ 'x', 'm', 'm', '2', '8', 0,
  /* 666 */ 'y', 'm', 'm', '2', '8', 0,
  /* 672 */ 'z', 'm', 'm', '2', '8', 0,
  /* 678 */ 'x', 'm', 'm', '8', 0,
  /* 683 */ 'y', 'm', 'm', '8', 0,
  /* 688 */ 'z', 'm', 'm', '8', 0,
  /* 693 */ 'c', 'r', '8', 0,
  /* 697 */ 'x', 'm', 'm', '1', '9', 0,
  /* 703 */ 'y', 'm', 'm', '1', '9', 0,
  /* 709 */ 'z', 'm', 'm', '1', '9', 0,
  /* 715 */ 'x', 'm', 'm', '2', '9', 0,
  /* 721 */ 'y', 'm', 'm', '2', '9', 0,
  /* 727 */ 'z', 'm', 'm', '2', '9', 0,
  /* 733 */ 'x', 'm', 'm', '9', 0,
  /* 738 */ 'y', 'm', 'm', '9', 0,
  /* 743 */ 'z', 'm', 'm', '9', 0,
  /* 748 */ 'c', 'r', '9', 0,
  /* 752 */ 'r', '1', '0', 'b', 0,
  /* 757 */ 'r', '1', '1', 'b', 0,
  /* 762 */ 'r', '1', '2', 'b', 0,
  /* 767 */ 'r', '1', '3', 'b', 0,
  /* 772 */ 'r', '1', '4', 'b', 0,
  /* 777 */ 'r', '1', '5', 'b', 0,
  /* 782 */ 'r', '8', 'b', 0,
  /* 786 */ 'r', '9', 'b', 0,
  /* 790 */ 'r', '1', '0', 'd', 0,
  /* 795 */ 'r', '1', '1', 'd', 0,
  /* 800 */ 'r', '1', '2', 'd', 0,
  /* 805 */ 'r', '1', '3', 'd', 0,
  /* 810 */ 'r', '1', '4', 'd', 0,
  /* 815 */ 'r', '1', '5', 'd', 0,
  /* 820 */ 'r', '8', 'd', 0,
  /* 824 */ 'r', '9', 'd', 0,
  /* 828 */ 'a', 'h', 0,
  /* 831 */ 'b', 'h', 0,
  /* 834 */ 'c', 'h', 0,
  /* 837 */ 'd', 'h', 0,
  /* 840 */ 'e', 'd', 'i', 0,
  /* 844 */ 'r', 'd', 'i', 0,
  /* 848 */ 'e', 's', 'i', 0,
  /* 852 */ 'r', 's', 'i', 0,
  /* 856 */ 'a', 'l', 0,
  /* 859 */ 'b', 'l', 0,
  /* 862 */ 'c', 'l', 0,
  /* 865 */ 'd', 'l', 0,
  /* 868 */ 'd', 'i', 'l', 0,
  /* 872 */ 's', 'i', 'l', 0,
  /* 876 */ 'b', 'p', 'l', 0,
  /* 880 */ 's', 'p', 'l', 0,
  /* 884 */ 'e', 'b', 'p', 0,
  /* 888 */ 'r', 'b', 'p', 0,
  /* 892 */ 'e', 'i', 'p', 0,
  /* 896 */ 'r', 'i', 'p', 0,
  /* 900 */ 'e', 's', 'p', 0,
  /* 904 */ 'r', 's', 'p', 0,
  /* 908 */ 'c', 's', 0,
  /* 911 */ 'd', 's', 0,
  /* 914 */ 'e', 's', 0,
  /* 917 */ 'f', 's', 0,
  /* 920 */ 'f', 'l', 'a', 'g', 's', 0,
  /* 926 */ 's', 's', 0,
  /* 929 */ 'r', '1', '0', 'w', 0,
  /* 934 */ 'r', '1', '1', 'w', 0,
  /* 939 */ 'r', '1', '2', 'w', 0,
  /* 944 */ 'r', '1', '3', 'w', 0,
  /* 949 */ 'r', '1', '4', 'w', 0,
  /* 954 */ 'r', '1', '5', 'w', 0,
  /* 959 */ 'r', '8', 'w', 0,
  /* 963 */ 'r', '9', 'w', 0,
  /* 967 */ 'f', 'p', 's', 'w', 0,
  /* 972 */ 'e', 'a', 'x', 0,
  /* 976 */ 'r', 'a', 'x', 0,
  /* 980 */ 'e', 'b', 'x', 0,
  /* 984 */ 'r', 'b', 'x', 0,
  /* 988 */ 'e', 'c', 'x', 0,
  /* 992 */ 'r', 'c', 'x', 0,
  /* 996 */ 'e', 'd', 'x', 0,
  /* 1000 */ 'r', 'd', 'x', 0,
  /* 1004 */ 'e', 'i', 'z', 0,
  /* 1008 */ 'r', 'i', 'z', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    828, 856, 973, 831, 859, 885, 876, 981, 834, 862, 908, 989, 837, 841, 
    868, 865, 911, 997, 972, 884, 980, 988, 840, 996, 920, 892, 1004, 914, 
    848, 900, 967, 917, 923, 893, 976, 888, 984, 992, 844, 1000, 896, 1008, 
    852, 904, 849, 872, 901, 880, 926, 129, 218, 289, 360, 431, 502, 568, 
    634, 693, 748, 66, 155, 244, 315, 386, 457, 133, 222, 293, 364, 435, 
    506, 572, 638, 125, 214, 285, 356, 427, 498, 564, 630, 107, 196, 267, 
    338, 409, 480, 546, 612, 111, 200, 271, 342, 413, 484, 550, 616, 694, 
    749, 67, 156, 245, 316, 387, 458, 0, 6, 12, 18, 24, 30, 36, 
    42, 110, 199, 270, 341, 412, 483, 549, 615, 678, 733, 48, 137, 226, 
    297, 368, 439, 510, 576, 642, 697, 71, 160, 249, 320, 391, 462, 528, 
    594, 660, 715, 89, 178, 115, 204, 275, 346, 417, 488, 554, 620, 683, 
    738, 54, 143, 232, 303, 374, 445, 516, 582, 648, 703, 77, 166, 255, 
    326, 397, 468, 534, 600, 666, 721, 95, 184, 120, 209, 280, 351, 422, 
    493, 559, 625, 688, 743, 60, 149, 238, 309, 380, 451, 522, 588, 654, 
    709, 83, 172, 261, 332, 403, 474, 540, 606, 672, 727, 101, 190, 782, 
    786, 752, 757, 762, 767, 772, 777, 820, 824, 790, 795, 800, 805, 810, 
    815, 959, 963, 929, 934, 939, 944, 949, 954, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printf64mem(MI, OpIdx, OS);
    break;
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case X86_AAD8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAD8i8 10)
      AsmString = "aad";
      break;
    }
    return NULL;
  case X86_AAM8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAM8i8 10)
      AsmString = "aam";
      break;
    }
    return NULL;
  case X86_CVTSD2SI64rm:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(X86_GR64RegClassID, 0)) {
      // (CVTSD2SI64rm GR64:$dst, sdmem:$src)
      AsmString = "cvtsd2si	$\x01, $\xFF\x02\x01";
      break;
    }
    return NULL;
  case X86_XSTORE:
    if (MCInst_getNumOperands(MI) == 0) {
      // (XSTORE)
      AsmString = "xstorerng";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
