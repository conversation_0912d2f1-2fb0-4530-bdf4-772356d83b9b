// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Declares IOCTL interfaces for DdiMon EPT memory operations.

#ifndef DDIMON_IOCTL_H_
#define DDIMON_IOCTL_H_

#include <ntddk.h>

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL Codes
//

// Device type for DdiMon
#define DDIMON_DEVICE_TYPE 0x8000

// IOCTL codes for EPT memory operations (only read/write memory supported)
#define IOCTL_DDIMON_EPT_READ_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_EPT_WRITE_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x801, ME<PERSON><PERSON>_BUFFERED, FILE_ANY_ACCESS)

// Removed: Unused IOCTL codes for manual hook management
// - IOCTL_DDIMON_CHECK_EPT_HOOK
// - IOCTL_DDIMON_CREATE_EPT_HOOK
// - IOCTL_DDIMON_CREATE_EPT_HOOK_EX
// - IOCTL_DDIMON_REMOVE_EPT_HOOK
// - IOCTL_DDIMON_LIST_EPT_HOOKS
// - IOCTL_DDIMON_REMOVE_ALL_HOOKS

////////////////////////////////////////////////////////////////////////////////
//
// Data Structures
//

// EPT memory read request
#pragma pack(push, 1)
typedef struct _EPT_READ_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to read from
    ULONG Size;                     // Number of bytes to read
} EPT_READ_MEMORY_REQUEST, *PEPT_READ_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory read response
typedef struct _EPT_READ_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesRead;                // Actual bytes read
    UCHAR Data[1];                  // Variable length data buffer
} EPT_READ_MEMORY_RESPONSE, *PEPT_READ_MEMORY_RESPONSE;

// EPT memory write request
#pragma pack(push, 1)
typedef struct _EPT_WRITE_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to write to
    ULONG Size;                     // Number of bytes to write
    UCHAR Data[1];                  // Variable length data buffer
} EPT_WRITE_MEMORY_REQUEST, *PEPT_WRITE_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory write response
typedef struct _EPT_WRITE_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesWritten;             // Actual bytes written
} EPT_WRITE_MEMORY_RESPONSE, *PEPT_WRITE_MEMORY_RESPONSE;

// Removed: Unused EPT hook check structures
// - EPT_HOOK_CHECK_REQUEST
// - EPT_HOOK_CHECK_RESPONSE

// Removed: Unused EPT hook creation structures
// - EPT_HOOK_CREATE_REQUEST
// - EPT_HOOK_CREATE_REQUEST_EX
// - EPT_HOOK_CREATE_RESPONSE

// Removed: Unused EPT hook management structures
// - EPT_HOOK_REMOVE_REQUEST
// - EPT_HOOK_REMOVE_RESPONSE
// - EPT_HOOK_INFO
// - EPT_HOOK_LIST_REQUEST
// - EPT_HOOK_LIST_RESPONSE

////////////////////////////////////////////////////////////////////////////////
//
// Function Prototypes
//

#ifdef __cplusplus
extern "C" {
#endif

// IOCTL handler functions
_IRQL_requires_max_(PASSIVE_LEVEL) NTSTATUS
DdimonHandleEptReadMemory(_In_ PVOID InputBuffer, _In_ ULONG InputBufferLength,
                         _Out_ PVOID OutputBuffer, _In_ ULONG OutputBufferLength,
                         _Out_ PULONG BytesReturned);

_IRQL_requires_max_(PASSIVE_LEVEL) NTSTATUS
DdimonHandleEptWriteMemory(_In_ PVOID InputBuffer, _In_ ULONG InputBufferLength,
                          _Out_ PVOID OutputBuffer, _In_ ULONG OutputBufferLength,
                          _Out_ PULONG BytesReturned);

// REMOVED: All manual hook management handlers
// Per ultimate simplification: Only read/write memory IOCTLs are supported
// Hooks are automatically managed during read/write operations

// REMOVED: Individual EPT hook remove handler
// Per new design: User manages hook points via EptWriteMemory API

// Removed: Unused function declaration
// - DdimonHandleEptHookRemoveAll

// Dynamic EPT hook violation handler
BOOLEAN DdimonpHandleDynamicEptViolation(struct _EptData* ept_data, void* fault_va);

// Dynamic EPT hooks cleanup function
VOID DdimonpCleanupDynamicHooks();

// REMOVED: Individual page hook removal function
// Per new design: User manages hook points via EptWriteMemory API

_IRQL_requires_max_(PASSIVE_LEVEL) EXTERN_C NTSTATUS
    DdimonpRemoveAllPageHooks(void);

#ifdef __cplusplus
}
#endif

#endif  // DDIMON_IOCTL_H_
