// Copyright (c) 2015-2017, <PERSON><PERSON>. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

/// @file
/// Implements DdiMon IOCTL handlers - CLEANED VERSION
/// Focused on page-level EPT HOOK functionality only

#include "ddi_mon.h"
#include "ddi_mon_ioctl.h"
#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "../HyperPlatform/HyperPlatform/util.h"
#include "../HyperPlatform/HyperPlatform/ept.h"
#include <intrin.h>   // For __vmx_vmcall intrinsic

// Ensure PAGE_SIZE is correctly defined
#ifndef PAGE_SIZE
#define PAGE_SIZE 0x1000
#endif

extern "C" {

// External reference to shared shadow hook data
extern SharedShadowHookData* g_shared_sh_data;

// SIMPLIFIED: Page-level hook structure - user manages hook points via write memory
typedef struct _PAGE_HOOK_INFO {
    ULONG64 HookId;                 // Unique page hook identifier
    ULONG64 ProcessId;              // Target process ID
    ULONG64 TargetAddress;          // Target address (page-aligned)
    ULONG64 PageSize;               // Page size (usually 4KB)
    PVOID ShadowPage;               // Shadow page virtual address (single page)
    ULONG64 ShadowPagePA;           // Shadow page physical address
    ULONG64 OriginalPagePA;         // Original page physical address
    BOOLEAN IsActive;               // Hook activation status
    LARGE_INTEGER CreateTime;       // Hook creation timestamp
    struct _PAGE_HOOK_INFO* Next;   // Next hook in linked list
} PAGE_HOOK_INFO, *PPAGE_HOOK_INFO;

// Global page hook management
static volatile PPAGE_HOOK_INFO g_PageHooks = nullptr;
static volatile BOOLEAN g_PageHooksInitialized = FALSE;

// Hook ID generation
static volatile ULONG64 g_NextHookId = 1;

// Function declarations
static NTSTATUS DdimonpInitializePageHooks(void);
static PPAGE_HOOK_INFO DdimonpFindPageHookByAddress(ULONG64 Address, ULONG ProcessId);
static PPAGE_HOOK_INFO DdimonpFindPageHookById(ULONG64 HookId);
static VOID DdimonpAddPageHook(PPAGE_HOOK_INFO Hook);
static ULONG64 DdimonpGenerateHookId(void);
static NTSTATUS DdimonpCreatePageHook(ULONG64 TargetAddress, ULONG ProcessId, PULONG64 HookId);
static NTSTATUS DdimonpRemoveAllPageHooks(void);

// Process attachment helpers
static KAPC_STATE DdimonpAttachToProcess(PEPROCESS Process);
static VOID DdimonpDetachFromProcess(PKAPC_STATE ApcState);

////////////////////////////////////////////////////////////////////////////////
//
// implementations
//

// Initialize page hook management
static NTSTATUS DdimonpInitializePageHooks(void) {
    if (InterlockedCompareExchange((LONG*)&g_PageHooksInitialized, TRUE, FALSE) != FALSE) {
        return STATUS_SUCCESS;  // Already initialized
    }

    // Initialize using atomic operations
    InterlockedExchangePointer((PVOID*)&g_PageHooks, nullptr);
    InterlockedExchange64((LONG64*)&g_NextHookId, 1);

    HYPERPLATFORM_LOG_INFO("Page hooks initialized");
    return STATUS_SUCCESS;
}

// Generate unique hook ID
static ULONG64 DdimonpGenerateHookId(void) {
    return InterlockedIncrement64((LONG64*)&g_NextHookId);
}

// Find page hook by address and process ID
static PPAGE_HOOK_INFO DdimonpFindPageHookByAddress(ULONG64 Address, ULONG ProcessId) {
    if (!g_PageHooksInitialized) {
        return nullptr;
    }

    PPAGE_HOOK_INFO current = g_PageHooks;
    while (current) {
        if (current->TargetAddress == (Address & ~0xFFF) && current->ProcessId == ProcessId) {
            return current;
        }
        current = current->Next;
    }
    return nullptr;
}

// Find page hook by ID
static PPAGE_HOOK_INFO DdimonpFindPageHookById(ULONG64 HookId) {
    if (!g_PageHooksInitialized) {
        return nullptr;
    }

    PPAGE_HOOK_INFO current = g_PageHooks;
    while (current) {
        if (current->HookId == HookId) {
            return current;
        }
        current = current->Next;
    }
    return nullptr;
}

// Add page hook to global list
static VOID DdimonpAddPageHook(PPAGE_HOOK_INFO Hook) {
    Hook->Next = (PPAGE_HOOK_INFO)InterlockedExchangePointer((PVOID*)&g_PageHooks, Hook);
}

// Process attachment helpers
static KAPC_STATE DdimonpAttachToProcess(PEPROCESS Process) {
    KAPC_STATE apcState;
    KeStackAttachProcess(Process, &apcState);
    return apcState;
}

static VOID DdimonpDetachFromProcess(PKAPC_STATE ApcState) {
    KeUnstackDetachProcess(ApcState);
}

// Create page-level hook
static NTSTATUS DdimonpCreatePageHook(ULONG64 TargetAddress, ULONG ProcessId, PULONG64 HookId) {
    HYPERPLATFORM_LOG_INFO("Creating page-level hook: Address=0x%llX, ProcessId=%d", TargetAddress, ProcessId);

    // Page-align the target address
    ULONG64 PageAddress = TargetAddress & ~0xFFF;

    // Check if hook already exists for this page
    PPAGE_HOOK_INFO existingHook = DdimonpFindPageHookByAddress(PageAddress, ProcessId);
    if (existingHook && existingHook->IsActive) {
        HYPERPLATFORM_LOG_INFO("Page hook already exists and active: HookId=%llu, Page=0x%llX",
                               existingHook->HookId, PageAddress);
        *HookId = existingHook->HookId;
        return STATUS_SUCCESS;
    }

    // Initialize page hooks if not already done
    DdimonpInitializePageHooks();

    // Allocate shadow page
    PVOID ShadowPage = ExAllocatePoolWithTag(NonPagedPool, PAGE_SIZE, 'Hook');
    if (!ShadowPage) {
        HYPERPLATFORM_LOG_ERROR("Failed to allocate shadow page");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    PHYSICAL_ADDRESS ShadowPagePhys = MmGetPhysicalAddress(ShadowPage);
    if (ShadowPagePhys.QuadPart == 0) {
        ExFreePoolWithTag(ShadowPage, 'Hook');
        HYPERPLATFORM_LOG_ERROR("Failed to get shadow page physical address");
        return STATUS_UNSUCCESSFUL;
    }

    // Copy original page content to shadow page
    NTSTATUS status = STATUS_UNSUCCESSFUL;
    PEPROCESS TargetProcess;
    status = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &TargetProcess);
    if (NT_SUCCESS(status)) {
        KAPC_STATE ApcState;
        KeStackAttachProcess(TargetProcess, &ApcState);

        // Try to copy original page content
        BOOLEAN addressAccessible = FALSE;
        __try {
            volatile UCHAR testByte = *(volatile UCHAR*)PageAddress;
            UNREFERENCED_PARAMETER(testByte);
            addressAccessible = TRUE;
        } __except(EXCEPTION_EXECUTE_HANDLER) {
            addressAccessible = MmIsAddressValid((PVOID)PageAddress);
        }

        if (addressAccessible) {
            RtlCopyMemory(ShadowPage, (PVOID)PageAddress, PAGE_SIZE);
            HYPERPLATFORM_LOG_INFO("Shadow page created with original content: PA=0x%llX, Source VA=0x%llX",
                                  ShadowPagePhys.QuadPart, PageAddress);
            status = STATUS_SUCCESS;
        } else {
            HYPERPLATFORM_LOG_ERROR("Virtual address not accessible: 0x%llX in process %lu", PageAddress, ProcessId);
            status = STATUS_ACCESS_VIOLATION;
        }

        KeUnstackDetachProcess(&ApcState);
        ObDereferenceObject(TargetProcess);
    } else {
        HYPERPLATFORM_LOG_ERROR("Failed to lookup target process: ProcessId=%d, Status=0x%08X", ProcessId, status);
    }

    if (!NT_SUCCESS(status)) {
        ExFreePoolWithTag(ShadowPage, 'Hook');
        return status;
    }

    // Create hook info structure
    PPAGE_HOOK_INFO hook = static_cast<PPAGE_HOOK_INFO>(
        ExAllocatePoolWithTag(NonPagedPool, sizeof(PAGE_HOOK_INFO), 'Hook'));
    if (!hook) {
        ExFreePoolWithTag(ShadowPage, 'Hook');
        HYPERPLATFORM_LOG_ERROR("Failed to allocate hook structure");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    // Initialize hook structure
    RtlZeroMemory(hook, sizeof(PAGE_HOOK_INFO));
    hook->HookId = DdimonpGenerateHookId();
    hook->ProcessId = ProcessId;
    hook->TargetAddress = PageAddress;
    hook->PageSize = PAGE_SIZE;
    hook->ShadowPage = ShadowPage;
    hook->ShadowPagePA = ShadowPagePhys.QuadPart;
    hook->OriginalPagePA = 0;  // Will be set below
    hook->IsActive = FALSE;  // Will be activated after EPT setup
    KeQuerySystemTime(&hook->CreateTime);

    // Add to hook list
    DdimonpAddPageHook(hook);

    *HookId = hook->HookId;
    return STATUS_SUCCESS;
}

// Remove all page hooks
static NTSTATUS DdimonpRemoveAllPageHooks(void) {
    if (!g_PageHooksInitialized) {
        return STATUS_SUCCESS;
    }

    PPAGE_HOOK_INFO current = (PPAGE_HOOK_INFO)InterlockedExchangePointer((PVOID*)&g_PageHooks, nullptr);
    ULONG removedCount = 0;

    while (current) {
        PPAGE_HOOK_INFO next = current->Next;

        // Free shadow page
        if (current->ShadowPage) {
            ExFreePoolWithTag(current->ShadowPage, 'Hook');
        }

        // Free hook structure
        ExFreePoolWithTag(current, 'Hook');
        removedCount++;

        current = next;
    }

    InterlockedExchange((LONG*)&g_PageHooksInitialized, FALSE);
    HYPERPLATFORM_LOG_INFO("Removed %lu page hooks", removedCount);
    return STATUS_SUCCESS;
}

// IOCTL handler: Create EPT hook
NTSTATUS DdimonHandleEptHookCreate(PEPT_HOOK_CREATE_REQUEST Request, PEPT_HOOK_CREATE_RESPONSE Response) {
    if (!Request || !Response) {
        return STATUS_INVALID_PARAMETER;
    }

    HYPERPLATFORM_LOG_INFO("DdimonHandleEptHookCreate: Address=0x%llX, ProcessId=%lu",
                           Request->TargetAddress, Request->ProcessId);

    ULONG64 HookId = 0;
    NTSTATUS status = DdimonpCreatePageHook(Request->TargetAddress, Request->ProcessId, &HookId);

    Response->HookId = HookId;
    Response->Status = status;

    HYPERPLATFORM_LOG_INFO("DdimonHandleEptHookCreate completed: Status=0x%08X, HookId=%llu", status, HookId);
    return status;
}

// IOCTL handler: Remove all EPT hooks
NTSTATUS DdimonHandleEptHookRemoveAll(PEPT_HOOK_REMOVE_ALL_REQUEST Request, PEPT_HOOK_REMOVE_ALL_RESPONSE Response) {
    UNREFERENCED_PARAMETER(Request);

    HYPERPLATFORM_LOG_INFO("DdimonHandleEptHookRemoveAll called");

    NTSTATUS status = DdimonpRemoveAllPageHooks();

    if (Response) {
        Response->Status = status;
    }

    HYPERPLATFORM_LOG_INFO("DdimonHandleEptHookRemoveAll completed: Status=0x%08X", status);
    return status;
}

// EPT violation handler for dynamic hooks
BOOLEAN DdimonpHandleDynamicEptViolation(EptData* ept_data, EptViolationQualification exit_qualification,
                                         ULONG64 guest_physical_address, ULONG64 guest_linear_address, ULONG64 guest_rip) {
    UNREFERENCED_PARAMETER(ept_data);
    UNREFERENCED_PARAMETER(exit_qualification);
    UNREFERENCED_PARAMETER(guest_linear_address);

    if (!g_PageHooksInitialized) {
        return FALSE;
    }

    // Find hook by physical address
    PPAGE_HOOK_INFO current = g_PageHooks;
    while (current) {
        if (current->IsActive && current->OriginalPagePA == (guest_physical_address & ~0xFFF)) {
            HYPERPLATFORM_LOG_INFO("EPT violation handled: PA=0x%llX, RIP=0x%llX, HookId=%llu",
                                   guest_physical_address, guest_rip, current->HookId);
            return TRUE;  // Handled by our hook
        }
        current = current->Next;
    }

    return FALSE;  // Not our hook
}

// Cleanup function called during driver unload
VOID DdimonpCleanupDynamicHooks(void) {
    DdimonpRemoveAllPageHooks();
    HYPERPLATFORM_LOG_INFO("Dynamic hooks cleanup completed");
}

}  // extern "C"
