// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Implements IOCTL handlers for DdiMon EPT memory operations.

#include "shadow_hook.h"
#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "../HyperPlatform/HyperPlatform/util.h"
#include "../HyperPlatform/HyperPlatform/ept.h"
#include "../HyperPlatform/HyperPlatform/ia32_type.h"  // For EptViolationQualification
#include "ddi_mon_ioctl.h"
#include <ntimage.h>  // For PE structures
#include <intrin.h>   // For __vmx_vmcall intrinsic

// Ensure PAGE_SIZE is correctly defined
#ifndef PAGE_SIZE
#define PAGE_SIZE 0x1000
#endif

extern "C" {

// External reference to shared shadow hook data
extern SharedShadowHookData* g_shared_sh_data;

// PERFORMANCE: Removed unused function declarations

// SIMPLIFIED: Page-level hook structure - user manages hook points via write memory
typedef struct _PAGE_HOOK_INFO {
    ULONG64 HookId;                 // Unique page hook identifier
    ULONG64 ProcessId;              // Target process ID
    ULONG64 TargetAddress;          // Target address (page-aligned)
    ULONG64 PageSize;               // Page size (usually 4KB)
    PVOID ShadowPage;               // Shadow page virtual address (single page)
    ULONG64 ShadowPagePA;           // Shadow page physical address
    ULONG64 OriginalPagePA;         // Original page physical address (for EPT restoration)
    BOOLEAN IsActive;               // Page hook activation status
    LARGE_INTEGER CreateTime;       // Creation timestamp

    // Simplified management - no individual hook point tracking
    // User manages hook points via EptWriteMemory API:
    // - Write modified code = Create/Update hook
    // - Write original code = Restore hook
    // - Driver only manages page-level EPT permissions

    struct _PAGE_HOOK_INFO* Next;   // Next hook in the list
} PAGE_HOOK_INFO, *PPAGE_HOOK_INFO;

// Legacy structure for backward compatibility (will be phased out)
typedef struct _MAIN_MODULE_MAPPING {
    ULONG64 ProcessId;              // Target process ID
    ULONG64 ModuleBase;             // Main module base address
    ULONG64 ModuleSize;             // Entire module size
    PVOID ExecutePages;             // Execute pages (complete module copy)
    ULONG64 ExecutePagesPA;         // Physical address of execute pages
    struct _MAIN_MODULE_MAPPING* Next; // Next mapping in the list
} MAIN_MODULE_MAPPING, *PMAIN_MODULE_MAPPING;

// SIMPLIFIED: Page-level hook function declarations - user manages hook points
static NTSTATUS DdimonpCreatePageHook(ULONG64 TargetAddress, ULONG ProcessId, PULONG64 HookId);
static NTSTATUS DdimonpRemoveAllPageHooks(void);  // Only called during driver unload
// Removed: Unused function declaration
static PPAGE_HOOK_INFO DdimonpFindPageHookByAddress(ULONG64 Address, ULONG64 ProcessId);
static VOID DdimonpAddPageHook(PPAGE_HOOK_INFO hook);
static ULONG64 DdimonpGenerateHookId(void);

// REMOVED: Individual hook point management functions
// User manages hook points via EptWriteMemory API:
// - EptWriteMemory(address, modified_code) = Create/Update hook
// - EptWriteMemory(address, original_code) = Restore hook
// - Driver only provides page-level EPT violation handling

// Legacy module mapping function declarations (for backward compatibility)
static PMAIN_MODULE_MAPPING DdimonpFindModuleMapping(HANDLE ProcessId);
static VOID DdimonpAddModuleMapping(PMAIN_MODULE_MAPPING mapping);
static BOOLEAN IsInMainModule(ULONG64 Address, PMAIN_MODULE_MAPPING Mapping);
static NTSTATUS GetMainModuleInfo(HANDLE ProcessId, PULONG64 ModuleBase, PULONG64 ModuleSize);

// PERFORMANCE: Removed unused DdimonpAlignToPage function

////////////////////////////////////////////////////////////////////////////////
//
// Helper Functions
//

// Forward declarations
static NTSTATUS DdimonpCreateDynamicEptHookWithProcess(
    ULONG64 VirtualAddress,
    PULONG64 HookId,
    PULONG64 ShadowPagePA,
    HANDLE TargetProcessId);

// Set EPT permissions for a physical address to trigger violations
static BOOLEAN DdimonpSetEptPermissions(ULONG64 PhysicalAddress, BOOLEAN AllowRead, BOOLEAN AllowWrite, BOOLEAN AllowExecute);

// Get process by PID
static PEPROCESS DdimonpGetProcessByPid(ULONG ProcessId) {
    PEPROCESS process = nullptr;

    // Convert ULONG to HANDLE properly
    HANDLE hProcessId = ULongToHandle(ProcessId);
    NTSTATUS status = PsLookupProcessByProcessId(hProcessId, &process);

    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("Failed to lookup process %lu: 0x%08X", ProcessId, status);
        return nullptr;
    }

    // Check if process is still valid
    if (PsGetProcessExitStatus(process) != STATUS_PENDING) {
        HYPERPLATFORM_LOG_ERROR("Process %lu has exited", ProcessId);
        ObDereferenceObject(process);
        return nullptr;
    }

    return process;
}

// Attach to target process
static KAPC_STATE DdimonpAttachToProcess(PEPROCESS Process) {
    KAPC_STATE apcState;
    KeStackAttachProcess(reinterpret_cast<PRKPROCESS>(Process), &apcState);
    return apcState;
}

// Detach from target process
static void DdimonpDetachFromProcess(KAPC_STATE* ApcState) {
    KeUnstackDetachProcess(ApcState);
}

// Find shadow page for virtual address with target process ID
static PVOID DdimonpFindShadowPageForProcess(ULONG64 VirtualAddress, HANDLE TargetProcessId) {
    HYPERPLATFORM_LOG_DEBUG("Looking for shadow page for VA: 0x%llX in process %llu", VirtualAddress, (ULONG64)TargetProcessId);

    // Check module mappings for the target process
    PMAIN_MODULE_MAPPING mapping = DdimonpFindModuleMapping(TargetProcessId);
    if (mapping && IsInMainModule(VirtualAddress, mapping)) {
        // Calculate page offset within the module and return corresponding page base address
        ULONG64 Offset = VirtualAddress - mapping->ModuleBase;
        ULONG64 PageOffset = Offset & ~0xFFF;  // Align to page boundary
        PVOID shadowPageBase = (PUCHAR)mapping->ExecutePages + PageOffset;
        HYPERPLATFORM_LOG_DEBUG("Found module mapping shadow page base for VA: 0x%llX, PageOffset: 0x%llX", VirtualAddress, PageOffset);
        return shadowPageBase;
    }

    HYPERPLATFORM_LOG_DEBUG("No shadow page found for VA: 0x%llX in process %llu", VirtualAddress, (ULONG64)TargetProcessId);
    return nullptr;
}

// Removed: Unused backward compatibility function

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL Handler Implementations
//

// Handle EPT memory read request
_Use_decl_annotations_ NTSTATUS DdimonHandleEptReadMemory(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {
    
    PAGED_CODE();
    
    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }
    
    if (InputBufferLength < sizeof(EPT_READ_MEMORY_REQUEST)) {
        return STATUS_BUFFER_TOO_SMALL;
    }
    
    if (OutputBufferLength < sizeof(EPT_READ_MEMORY_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }
    
    auto request = static_cast<PEPT_READ_MEMORY_REQUEST>(InputBuffer);
    auto response = static_cast<PEPT_READ_MEMORY_RESPONSE>(OutputBuffer);

    // CRITICAL: Copy entire request to local variable to avoid buffer corruption
    EPT_READ_MEMORY_REQUEST localRequest;
    RtlCopyMemory(&localRequest, request, sizeof(EPT_READ_MEMORY_REQUEST));

    // Initialize response AFTER copying request
    RtlZeroMemory(response, OutputBufferLength);
    response->Status = STATUS_UNSUCCESSFUL;
    response->BytesRead = 0;

    HYPERPLATFORM_LOG_INFO("EPT Read Memory: PID=%lu, Address=0x%llX, Size=%lu",
                           localRequest.ProcessId, localRequest.VirtualAddress, localRequest.Size);
    
    __try {
        // Get target process using local request
        PEPROCESS targetProcess = DdimonpGetProcessByPid(localRequest.ProcessId);
        if (!targetProcess) {
            HYPERPLATFORM_LOG_ERROR("Failed to lookup process %lu", localRequest.ProcessId);
            response->Status = STATUS_NOT_FOUND;
            *BytesReturned = sizeof(EPT_READ_MEMORY_RESPONSE);
            return STATUS_SUCCESS;
        }

        // Attach to target process
        KAPC_STATE apcState = DdimonpAttachToProcess(targetProcess);

        // Check if we have enough space in output buffer
        ULONG maxDataSize = OutputBufferLength - sizeof(EPT_READ_MEMORY_RESPONSE) + 1;
        ULONG readSize = min(localRequest.Size, maxDataSize);

        // Try to find page-level hook first
        PPAGE_HOOK_INFO pageHook = DdimonpFindPageHookByAddress(localRequest.VirtualAddress, (ULONG64)localRequest.ProcessId);
        PVOID sourceAddress = nullptr;  // Initialize to avoid compiler warning

        if (pageHook && pageHook->IsActive) {
            // Found page-level hook, get shadow page from hook info
            // Found page-level hook, use shadow page from hook info
            PVOID shadowPage = pageHook->ShadowPage;
            if (shadowPage) {
                // Read from page-level hook shadow page
                ULONG64 pageOffset = localRequest.VirtualAddress & 0xFFF;  // Get offset within page
                sourceAddress = static_cast<PUCHAR>(shadowPage) + pageOffset;
                HYPERPLATFORM_LOG_INFO("Reading from page-level hook shadow page: HookId=%llu, Offset=0x%llX",
                                      pageHook->HookId, pageOffset);
            } else {
                HYPERPLATFORM_LOG_ERROR("Shadow page is null for HookId=%llu", pageHook->HookId);
                sourceAddress = reinterpret_cast<PVOID>(localRequest.VirtualAddress);
                HYPERPLATFORM_LOG_INFO("Fallback to reading from original page");
            }
        } else {
            // No page-level hook found - read from original page directly
            sourceAddress = reinterpret_cast<PVOID>(localRequest.VirtualAddress);
            HYPERPLATFORM_LOG_INFO("Reading from original page (no page-level hook found)");
        }
        
        // Validate source address
        if (!MmIsAddressValid(sourceAddress)) {
            HYPERPLATFORM_LOG_ERROR("Invalid source address: 0x%p", sourceAddress);
            response->Status = STATUS_ACCESS_VIOLATION;
        } else {
            // Perform the read operation
            RtlCopyMemory(response->Data, sourceAddress, readSize);
            response->BytesRead = readSize;
            response->Status = STATUS_SUCCESS;
            
            HYPERPLATFORM_LOG_DEBUG("Successfully read %d bytes", readSize);
        }
        
        // Detach from target process
        DdimonpDetachFromProcess(&apcState);
        
        // Dereference process object
        ObDereferenceObject(targetProcess);
        
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        HYPERPLATFORM_LOG_ERROR("Exception in EPT read memory: 0x%08X", GetExceptionCode());
        response->Status = STATUS_ACCESS_VIOLATION;
    }
    
    *BytesReturned = sizeof(EPT_READ_MEMORY_RESPONSE) - 1 + response->BytesRead;
    return STATUS_SUCCESS;
}

// Handle EPT memory write request
_Use_decl_annotations_ NTSTATUS DdimonHandleEptWriteMemory(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {
    
    PAGED_CODE();
    
    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }
    
    if (InputBufferLength < sizeof(EPT_WRITE_MEMORY_REQUEST)) {
        return STATUS_BUFFER_TOO_SMALL;
    }
    
    if (OutputBufferLength < sizeof(EPT_WRITE_MEMORY_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }
    
    auto request = static_cast<PEPT_WRITE_MEMORY_REQUEST>(InputBuffer);
    auto response = static_cast<PEPT_WRITE_MEMORY_RESPONSE>(OutputBuffer);

    // EMERGENCY DIAGNOSTIC: Add detailed buffer analysis
    HYPERPLATFORM_LOG_INFO("=== EPT WRITE MEMORY DIAGNOSTIC ===");
    HYPERPLATFORM_LOG_INFO("InputBuffer: 0x%p, InputBufferLength: %lu", InputBuffer, InputBufferLength);
    HYPERPLATFORM_LOG_INFO("OutputBuffer: 0x%p, OutputBufferLength: %lu", OutputBuffer, OutputBufferLength);
    HYPERPLATFORM_LOG_INFO("Request pointer: 0x%p", request);

    // Check buffer validity
    if (!MmIsAddressValid(request)) {
        HYPERPLATFORM_LOG_ERROR("Invalid request pointer!");
        return STATUS_INVALID_PARAMETER;
    }

    // Raw memory dump of the first 32 bytes
    HYPERPLATFORM_LOG_INFO("Raw buffer dump (first 32 bytes):");
    PUCHAR rawBuffer = static_cast<PUCHAR>(InputBuffer);
    for (ULONG i = 0; i < min(32, InputBufferLength); i += 8) {
        HYPERPLATFORM_LOG_INFO("Offset %02lu: %02X %02X %02X %02X %02X %02X %02X %02X",
                               i,
                               i+0 < InputBufferLength ? rawBuffer[i+0] : 0,
                               i+1 < InputBufferLength ? rawBuffer[i+1] : 0,
                               i+2 < InputBufferLength ? rawBuffer[i+2] : 0,
                               i+3 < InputBufferLength ? rawBuffer[i+3] : 0,
                               i+4 < InputBufferLength ? rawBuffer[i+4] : 0,
                               i+5 < InputBufferLength ? rawBuffer[i+5] : 0,
                               i+6 < InputBufferLength ? rawBuffer[i+6] : 0,
                               i+7 < InputBufferLength ? rawBuffer[i+7] : 0);
    }

    // Direct field access with validation
    ULONG savedProcessId;
    ULONG64 savedVirtualAddress;
    ULONG savedSize;
    PUCHAR savedData;

    // FIXED: Use correct structure alignment offsets with #pragma pack(push, 1)
    // ProcessId: offset 0 (4 bytes)
    // VirtualAddress: offset 4 (8 bytes) - NO padding with pack(1)
    // Size: offset 12 (4 bytes) - NO padding with pack(1)
    // Data: offset 16 (variable length) - NO padding with pack(1)

    if (InputBufferLength >= 4) {
        savedProcessId = *reinterpret_cast<PULONG>(rawBuffer + 0);
        HYPERPLATFORM_LOG_INFO("ProcessId (offset 0): %lu", savedProcessId);
    } else {
        HYPERPLATFORM_LOG_ERROR("Buffer too small for ProcessId");
        return STATUS_BUFFER_TOO_SMALL;
    }

    if (InputBufferLength >= 12) {  // Need 4+8=12 bytes for VirtualAddress
        savedVirtualAddress = *reinterpret_cast<PULONG64>(rawBuffer + 4);  // FIXED: offset 4 with pack(1)
        HYPERPLATFORM_LOG_INFO("VirtualAddress (offset 4): 0x%llX", savedVirtualAddress);
    } else {
        HYPERPLATFORM_LOG_ERROR("Buffer too small for VirtualAddress");
        return STATUS_BUFFER_TOO_SMALL;
    }

    if (InputBufferLength >= 16) {  // Need 4+8+4=16 bytes for Size
        savedSize = *reinterpret_cast<PULONG>(rawBuffer + 12);  // FIXED: offset 12 with pack(1)
        HYPERPLATFORM_LOG_INFO("Size (offset 12): %lu", savedSize);
    } else {
        HYPERPLATFORM_LOG_ERROR("Buffer too small for Size");
        return STATUS_BUFFER_TOO_SMALL;
    }

    if (InputBufferLength >= 17) {  // Need at least 17 bytes for Data
        savedData = rawBuffer + 16;  // FIXED: offset 16 with pack(1)
        HYPERPLATFORM_LOG_INFO("Data pointer (offset 16): 0x%p", savedData);
    } else {
        HYPERPLATFORM_LOG_ERROR("Buffer too small for Data");
        return STATUS_BUFFER_TOO_SMALL;
    }

    // Initialize response AFTER copying request
    RtlZeroMemory(response, sizeof(EPT_WRITE_MEMORY_RESPONSE));
    response->Status = STATUS_UNSUCCESSFUL;
    response->BytesWritten = 0;

    HYPERPLATFORM_LOG_INFO("EPT Write Memory: PID=%lu, VA=0x%llX, Size=%lu",
                           savedProcessId, savedVirtualAddress, savedSize);
    
    __try {
        // Get target process using saved variables
        PEPROCESS targetProcess = DdimonpGetProcessByPid(savedProcessId);
        if (!targetProcess) {
            HYPERPLATFORM_LOG_ERROR("Failed to lookup process %lu", savedProcessId);
            response->Status = STATUS_NOT_FOUND;
            *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
            return STATUS_SUCCESS;
        }

        // Attach to target process
        KAPC_STATE apcState = DdimonpAttachToProcess(targetProcess);

        // Try to find page-level hook first
        PPAGE_HOOK_INFO pageHook = DdimonpFindPageHookByAddress(savedVirtualAddress, (ULONG64)savedProcessId);
        PVOID targetAddress;

        if (pageHook && pageHook->IsActive) {
            // Found page-level hook, use shadow page from hook info
            PVOID shadowPage = pageHook->ShadowPage;
            if (shadowPage) {
                // Write to page-level hook shadow page
                ULONG64 pageOffset = savedVirtualAddress & (PAGE_SIZE - 1);
                targetAddress = static_cast<PUCHAR>(shadowPage) + pageOffset;
                HYPERPLATFORM_LOG_INFO("Writing to page-level hook shadow page: HookId=%llu, Offset=0x%llX",
                                      pageHook->HookId, pageOffset);
            } else {
                HYPERPLATFORM_LOG_ERROR("Shadow page is null for HookId=%llu", pageHook->HookId);
                response->Status = STATUS_UNSUCCESSFUL;
                DdimonpDetachFromProcess(&apcState);
                ObDereferenceObject(targetProcess);
                *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
                return STATUS_SUCCESS;
            }
        } else {
            // OPTIMIZED: No page-level hook found - create hook automatically for safe writing
            HYPERPLATFORM_LOG_INFO("No page-level hook found for address 0x%llX, creating hook automatically", savedVirtualAddress);

            // Detach temporarily to create hook
            DdimonpDetachFromProcess(&apcState);
            ObDereferenceObject(targetProcess);

            // Create hook for this page
            ULONG64 newHookId = 0;
            NTSTATUS hookStatus = DdimonpCreatePageHook(savedVirtualAddress, savedProcessId, &newHookId);

            if (NT_SUCCESS(hookStatus)) {
                HYPERPLATFORM_LOG_INFO("Successfully created hook for writing: HookId=%llu", newHookId);

                // Re-attach to process
                targetProcess = DdimonpGetProcessByPid(savedProcessId);
                if (!targetProcess) {
                    HYPERPLATFORM_LOG_ERROR("Failed to re-attach to process after hook creation");
                    response->Status = STATUS_NOT_FOUND;
                    *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
                    return STATUS_SUCCESS;
                }
                apcState = DdimonpAttachToProcess(targetProcess);

                // Find the newly created hook
                pageHook = DdimonpFindPageHookByAddress(savedVirtualAddress, (ULONG64)savedProcessId);
                if (pageHook && pageHook->IsActive && pageHook->ShadowPage) {
                    ULONG64 pageOffset = savedVirtualAddress & (PAGE_SIZE - 1);
                    targetAddress = static_cast<PUCHAR>(pageHook->ShadowPage) + pageOffset;
                    HYPERPLATFORM_LOG_INFO("Writing to newly created hook shadow page: HookId=%llu, Offset=0x%llX",
                                          pageHook->HookId, pageOffset);
                } else {
                    HYPERPLATFORM_LOG_ERROR("Failed to find newly created hook");
                    response->Status = STATUS_UNSUCCESSFUL;
                    DdimonpDetachFromProcess(&apcState);
                    ObDereferenceObject(targetProcess);
                    *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
                    return STATUS_SUCCESS;
                }
            } else {
                HYPERPLATFORM_LOG_ERROR("Failed to create hook for writing: Status=0x%08X", hookStatus);
                response->Status = hookStatus;
                *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
                return STATUS_SUCCESS;
            }
        }

        // Validate target address and perform write operation
        if (!MmIsAddressValid(targetAddress)) {
            HYPERPLATFORM_LOG_ERROR("Invalid target address: 0x%p", targetAddress);
            response->Status = STATUS_ACCESS_VIOLATION;
        } else {
            // Perform the write operation using saved data with exception handling
            __try {
                if (savedSize > 0) {
                    RtlCopyMemory(targetAddress, savedData, savedSize);
                    response->BytesWritten = savedSize;
                    HYPERPLATFORM_LOG_DEBUG("Successfully wrote %lu bytes to shadow page", savedSize);
                } else {
                    // Size is 0, this is a module mapping creation request
                    response->BytesWritten = 0;
                    HYPERPLATFORM_LOG_INFO("Module mapping creation completed");
                }
                response->Status = STATUS_SUCCESS;
            } __except(EXCEPTION_EXECUTE_HANDLER) {
                HYPERPLATFORM_LOG_ERROR("Exception during shadow page write: 0x%08X", GetExceptionCode());
                response->Status = STATUS_ACCESS_VIOLATION;
                response->BytesWritten = 0;
            }
        }
        
        // Detach from target process
        DdimonpDetachFromProcess(&apcState);
        
        // Dereference process object
        ObDereferenceObject(targetProcess);
        
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        HYPERPLATFORM_LOG_ERROR("Exception in EPT write memory: 0x%08X", GetExceptionCode());
        response->Status = STATUS_ACCESS_VIOLATION;
    }
    
    *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
    return STATUS_SUCCESS;
}

// REMOVED: Manual EPT hook check handler
// Per ultimate simplification: Hooks are automatically managed during read/write operations

// REMOVED: Manual EPT hook creation handler
// Per ultimate simplification: Hooks are automatically created during write operations

// REMOVED: Manual EPT hook creation handler (extended version)
// Per ultimate simplification: Hooks are automatically created during write operations

////////////////////////////////////////////////////////////////////////////////
//
// Dynamic EPT Hook Management
//

// Module mapping structure already defined above

// NEW: Global page-level hook management
static volatile PPAGE_HOOK_INFO g_PageHooks = nullptr;
static volatile LONG g_PageHooksLockFlag = 0;  // 0 = unlocked, 1 = locked
static volatile BOOLEAN g_PageHooksInitialized = FALSE;
static volatile ULONG64 g_NextHookId = 1;  // Auto-incrementing hook ID

// CRITICAL: EPT violation loop prevention
static volatile ULONG64 g_EptViolationCount = 0;
static volatile LARGE_INTEGER g_LastViolationTime = {0};
static const ULONG64 MAX_VIOLATIONS_PER_SECOND = 1000;  // Maximum allowed violations per second

// Legacy: Global main module mapping list (for backward compatibility)
static volatile PMAIN_MODULE_MAPPING g_ModuleMappings = nullptr;
static volatile LONG g_ModuleMappingsLockFlag = 0;  // 0 = unlocked, 1 = locked
static volatile BOOLEAN g_ModuleMappingsInitialized = FALSE;

// Helper function to get main module information
NTSTATUS GetMainModuleInfo(HANDLE ProcessId, PULONG64 ModuleBase, PULONG64 ModuleSize) {
    PEPROCESS Process;
    NTSTATUS status = PsLookupProcessByProcessId(ProcessId, &Process);
    if (!NT_SUCCESS(status)) {
        return status;
    }

    // CRITICAL: We cannot safely access user space memory from kernel context
    // Use a safer approach with process attachment

    KAPC_STATE ApcState;
    KeStackAttachProcess(Process, &ApcState);

    __try {
        // Common base address for 64-bit executables
        *ModuleBase = 0x140000000;

        // Validate the address is accessible in target process context
        if (!MmIsAddressValid(reinterpret_cast<void*>(*ModuleBase))) {
            // Try alternative common base addresses
            *ModuleBase = 0x400000;  // 32-bit style base
            if (!MmIsAddressValid(reinterpret_cast<void*>(*ModuleBase))) {
                KeUnstackDetachProcess(&ApcState);
                ObDereferenceObject(Process);
                return STATUS_INVALID_PARAMETER;
            }
        }

        // Safely read PE header to get module size
        PIMAGE_DOS_HEADER DosHeader = (PIMAGE_DOS_HEADER)*ModuleBase;

        // Validate DOS header
        if (!MmIsAddressValid(DosHeader) || DosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            KeUnstackDetachProcess(&ApcState);
            ObDereferenceObject(Process);
            return STATUS_INVALID_IMAGE_FORMAT;
        }

        // Validate NT header location
        ULONG64 ntHeaderOffset = DosHeader->e_lfanew;
        if (ntHeaderOffset > 0x1000) {  // Reasonable limit
            KeUnstackDetachProcess(&ApcState);
            ObDereferenceObject(Process);
            return STATUS_INVALID_IMAGE_FORMAT;
        }

        PIMAGE_NT_HEADERS NtHeaders = (PIMAGE_NT_HEADERS)(*ModuleBase + ntHeaderOffset);
        if (!MmIsAddressValid(NtHeaders) || NtHeaders->Signature != IMAGE_NT_SIGNATURE) {
            KeUnstackDetachProcess(&ApcState);
            ObDereferenceObject(Process);
            return STATUS_INVALID_IMAGE_FORMAT;
        }

        *ModuleSize = NtHeaders->OptionalHeader.SizeOfImage;

        // Sanity check on module size
        if (*ModuleSize == 0 || *ModuleSize > 0x10000000) {  // Max 256MB
            KeUnstackDetachProcess(&ApcState);
            ObDereferenceObject(Process);
            return STATUS_INVALID_IMAGE_FORMAT;
        }

    } __except(EXCEPTION_EXECUTE_HANDLER) {
        KeUnstackDetachProcess(&ApcState);
        ObDereferenceObject(Process);
        return STATUS_ACCESS_VIOLATION;
    }

    KeUnstackDetachProcess(&ApcState);
    ObDereferenceObject(Process);
    return STATUS_SUCCESS;
}

// Helper function to check if address is in main module
BOOLEAN IsInMainModule(ULONG64 Address, PMAIN_MODULE_MAPPING Mapping) {
    if (!Mapping) {
        return FALSE;
    }
    return (Address >= Mapping->ModuleBase &&
            Address < Mapping->ModuleBase + Mapping->ModuleSize);
}

// Initialize dynamic hook management - using atomic operations
static NTSTATUS DdimonpInitializeDynamicHooks() {
    if (InterlockedCompareExchange((LONG*)&g_ModuleMappingsInitialized, TRUE, FALSE) != FALSE) {
        return STATUS_SUCCESS;  // Already initialized
    }

    // Initialize using atomic operations
    InterlockedExchangePointer((PVOID*)&g_ModuleMappings, nullptr);
    InterlockedExchange(&g_ModuleMappingsLockFlag, 0);

    HYPERPLATFORM_LOG_INFO("Module mappings initialized");
    return STATUS_SUCCESS;
}

// Module mapping management functions are defined above

// Removed: Unused module mapping creation function

// EPT permissions are now set directly in the EPT violation handler
// This follows the gbhv approach of setting permissions when first accessed

// PERFORMANCE: Removed unused DdimonpCreateDynamicEptHook function

// Module mapping management functions
PMAIN_MODULE_MAPPING DdimonpFindModuleMapping(HANDLE ProcessId) {
    // Simple and direct design based on static hook stability

    // Basic parameter validation (from static hook approach)
    if (!g_ModuleMappingsInitialized || !g_ModuleMappings) {
        return nullptr;
    }

    // Simple traversal without complex IRQL checks (from static hook design)
    PMAIN_MODULE_MAPPING current = g_ModuleMappings;

    // Simple loop with reasonable limit (from static hook design)
    ULONG maxIterations = 10;  // Reasonable limit to avoid infinite loops
    ULONG iterations = 0;

    while (current && iterations < maxIterations) {
        if (current->ProcessId == (ULONG64)ProcessId) {
            return current;
        }
        current = current->Next;
        iterations++;
    }

    return nullptr;
}

// PERFORMANCE: Removed unused DdimonpFindModuleMappingByAddress function

VOID DdimonpAddModuleMapping(PMAIN_MODULE_MAPPING mapping) {
    // Simple atomic add to front of list
    mapping->Next = (PMAIN_MODULE_MAPPING)InterlockedExchangePointer((PVOID*)&g_ModuleMappings, mapping);
}

// Write hook code to module mapping
NTSTATUS DdimonpWriteHookToModule(HANDLE ProcessId, ULONG64 VirtualAddress, PVOID HookCode, ULONG Size) {
    // Find module mapping
    PMAIN_MODULE_MAPPING mapping = DdimonpFindModuleMapping(ProcessId);
    if (!mapping) {
        return STATUS_NOT_FOUND;
    }

    // Verify address is in module range
    if (!IsInMainModule(VirtualAddress, mapping)) {
        return STATUS_INVALID_PARAMETER;
    }

    // Calculate offset in execute pages
    ULONG64 Offset = VirtualAddress - mapping->ModuleBase;
    if (Offset + Size > mapping->ModuleSize) {
        return STATUS_INVALID_PARAMETER;
    }

    // Write hook code to execute pages
    __try {
        RtlCopyMemory((PUCHAR)mapping->ExecutePages + Offset, HookCode, Size);
        HYPERPLATFORM_LOG_INFO("Hook code written to module: VA=0x%llX, Offset=0x%llX, Size=%lu",
                               VirtualAddress, Offset, Size);
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return STATUS_ACCESS_VIOLATION;
    }

    return STATUS_SUCCESS;
}

// NEW: Page-level hook management functions

// Generate unique hook ID
ULONG64 DdimonpGenerateHookId(void) {
    return InterlockedIncrement64((LONG64*)&g_NextHookId);
}

// Add page hook to global list
VOID DdimonpAddPageHook(PPAGE_HOOK_INFO hook) {
    hook->Next = (PPAGE_HOOK_INFO)InterlockedExchangePointer((PVOID*)&g_PageHooks, hook);
    g_PageHooksInitialized = TRUE;
}

// Removed: Unused page hook finder by ID

// Find page hook by address and process ID
PPAGE_HOOK_INFO DdimonpFindPageHookByAddress(ULONG64 Address, ULONG64 ProcessId) {
    if (!g_PageHooksInitialized || !g_PageHooks) {
        return nullptr;
    }

    ULONG64 PageAddress = Address & ~0xFFF;  // Page-align address

    PPAGE_HOOK_INFO current = g_PageHooks;
    ULONG maxIterations = 100;
    ULONG iterations = 0;

    while (current && iterations < maxIterations) {
        if (current->ProcessId == ProcessId && current->TargetAddress == PageAddress) {
            return current;
        }
        current = current->Next;
        iterations++;
    }
    return nullptr;
}

// REMOVED: Complex hook point management functions
// Per new design: User manages hook points via EptWriteMemory API
// These complex management functions are no longer needed

// OPTIMIZED: Create page-level hook with existing hook detection
NTSTATUS DdimonpCreatePageHook(ULONG64 TargetAddress, ULONG ProcessId, PULONG64 HookId) {
    HYPERPLATFORM_LOG_INFO("Creating page-level hook: Address=0x%llX, ProcessId=%d", TargetAddress, ProcessId);

    // Page-align the target address
    ULONG64 PageAddress = TargetAddress & ~0xFFF;

    // STEP 1: Check if hook already exists for this page
    PPAGE_HOOK_INFO existingHook = DdimonpFindPageHookByAddress(PageAddress, ProcessId);
    if (existingHook && existingHook->IsActive) {
        // Hook already exists and is active - return existing HookId
        HYPERPLATFORM_LOG_INFO("Page hook already exists and active: HookId=%llu, Page=0x%llX",
                               existingHook->HookId, PageAddress);
        *HookId = existingHook->HookId;
        return STATUS_SUCCESS;
    }

    // STEP 2: No existing hook found - create new hook
    HYPERPLATFORM_LOG_INFO("No existing hook found for page 0x%llX, creating new hook", PageAddress);

    // Allocate shadow page (single 4KB page instead of entire module)
    PVOID ShadowPage = ExAllocatePoolWithTag(NonPagedPool, PAGE_SIZE, 'Hook');
    if (!ShadowPage) {
        HYPERPLATFORM_LOG_ERROR("Failed to allocate shadow page");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    // Get physical address of shadow page
    PHYSICAL_ADDRESS ShadowPagePhys = MmGetPhysicalAddress(ShadowPage);
    if (ShadowPagePhys.QuadPart == 0) {
        ExFreePoolWithTag(ShadowPage, 'Hook');
        HYPERPLATFORM_LOG_ERROR("Failed to get shadow page physical address");
        return STATUS_UNSUCCESSFUL;
    }

    // CRITICAL FIX: Use module-based approach instead of direct memory access
    // Following the successful pattern from DdimonpCreateDynamicEptHookWithProcess

    NTSTATUS status = STATUS_SUCCESS;

    // First, try to get module information for the target address
    ULONG64 ModuleBase = 0;
    ULONG64 ModuleSize = 0;

    __try {
        // Attach to target process to get module information
        PEPROCESS TargetProcess;
        status = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &TargetProcess);
        if (NT_SUCCESS(status)) {
            KAPC_STATE ApcState;
            KeStackAttachProcess(TargetProcess, &ApcState);

            // Get module information for the target address
            status = GetMainModuleInfo((HANDLE)(ULONG_PTR)ProcessId, &ModuleBase, &ModuleSize);
            if (NT_SUCCESS(status)) {
                // CRITICAL FIX: Always try to copy the original page content first
                // Following user's correct logic: copy entire page (PageAddress to PageAddress+PAGE_SIZE)

                // Detailed address validity check
                HYPERPLATFORM_LOG_INFO("Checking address validity: Original=0x%llX, PageAligned=0x%llX, ProcessId=%lu",
                                      TargetAddress, PageAddress, ProcessId);

                // For user-mode addresses, try direct access with exception handling
                BOOLEAN addressAccessible = FALSE;
                __try {
                    // Try to read one byte to test accessibility
                    volatile UCHAR testByte = *(volatile UCHAR*)PageAddress;
                    UNREFERENCED_PARAMETER(testByte);
                    addressAccessible = TRUE;
                    HYPERPLATFORM_LOG_INFO("Address 0x%llX is accessible via direct read test", PageAddress);
                } __except(EXCEPTION_EXECUTE_HANDLER) {
                    addressAccessible = FALSE;
                    HYPERPLATFORM_LOG_INFO("Address 0x%llX failed direct read test, trying MmIsAddressValid", PageAddress);
                    // Fallback to MmIsAddressValid for kernel addresses
                    addressAccessible = MmIsAddressValid((PVOID)PageAddress);
                }

                if (addressAccessible) {
                    // CORRECT: Copy the complete original page content (4KB) WITHOUT any modifications
                    // Hook creation only establishes EPT violation mechanism
                    // Code modification is done separately by user via "EPT Write Memory"
                    RtlCopyMemory(ShadowPage, (PVOID)PageAddress, PAGE_SIZE);

                    HYPERPLATFORM_LOG_INFO("Shadow page created with original content (no code modification): PA=0x%llX, Source VA=0x%llX",
                                          ShadowPagePhys.QuadPart, PageAddress);
                    HYPERPLATFORM_LOG_INFO("Hook point established at 0x%llX - use EPT Write Memory to modify code", TargetAddress);
                } else {
                    // 尝试检查原始地址是否有效
                    if (MmIsAddressValid((PVOID)TargetAddress)) {
                        HYPERPLATFORM_LOG_INFO("Original address 0x%llX is valid, but page-aligned address 0x%llX is not",
                                              TargetAddress, PageAddress);
                        // 尝试使用原始地址所在的页面
                        ULONG64 AlternativePageAddress = TargetAddress & ~0xFFF;
                        if (AlternativePageAddress == PageAddress) {
                            HYPERPLATFORM_LOG_ERROR("Same page address, address mapping issue in process %lu", ProcessId);
                        } else {
                            HYPERPLATFORM_LOG_INFO("Trying alternative page address: 0x%llX", AlternativePageAddress);
                        }
                    } else {
                        HYPERPLATFORM_LOG_ERROR("Both original address 0x%llX and page address 0x%llX are invalid in process %lu",
                                                TargetAddress, PageAddress, ProcessId);
                    }

                    HYPERPLATFORM_LOG_ERROR("Virtual address not accessible: 0x%llX in process %lu", PageAddress, ProcessId);
                    status = STATUS_ACCESS_VIOLATION;
                }
            } else {
                // Cannot get module info - still try to copy original page content
                HYPERPLATFORM_LOG_INFO("Cannot get module info for ProcessId=%d, attempting direct page copy", ProcessId);

                if (MmIsAddressValid((PVOID)PageAddress)) {
                    // CORRECT: Copy the original page without any modifications
                    // Hook creation only establishes EPT violation mechanism
                    RtlCopyMemory(ShadowPage, (PVOID)PageAddress, PAGE_SIZE);

                    HYPERPLATFORM_LOG_INFO("Shadow page created with original content (no module info): PA=0x%llX, Source VA=0x%llX",
                                          ShadowPagePhys.QuadPart, PageAddress);
                    HYPERPLATFORM_LOG_INFO("Hook point established at 0x%llX - use EPT Write Memory to modify code", TargetAddress);
                    status = STATUS_SUCCESS;  // Override the error - we successfully copied the page
                } else {
                    HYPERPLATFORM_LOG_ERROR("Cannot access page at 0x%llX and no module info available", PageAddress);
                    status = STATUS_ACCESS_VIOLATION;
                }
            }

            KeUnstackDetachProcess(&ApcState);
            ObDereferenceObject(TargetProcess);
        } else {
            HYPERPLATFORM_LOG_ERROR("Failed to lookup target process: ProcessId=%d, Status=0x%08X", ProcessId, status);
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        status = STATUS_ACCESS_VIOLATION;
        HYPERPLATFORM_LOG_ERROR("Exception during shadow page creation: VA=0x%llX", PageAddress);
    }

    if (!NT_SUCCESS(status)) {
        ExFreePoolWithTag(ShadowPage, 'Hook');
        return status;
    }

    // Create hook info structure
    PPAGE_HOOK_INFO hook = static_cast<PPAGE_HOOK_INFO>(
        ExAllocatePoolWithTag(NonPagedPool, sizeof(PAGE_HOOK_INFO), 'Hook'));
    if (!hook) {
        ExFreePoolWithTag(ShadowPage, 'Hook');
        HYPERPLATFORM_LOG_ERROR("Failed to allocate hook structure");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    // Initialize hook structure
    RtlZeroMemory(hook, sizeof(PAGE_HOOK_INFO));
    hook->HookId = DdimonpGenerateHookId();
    hook->ProcessId = ProcessId;
    hook->TargetAddress = PageAddress;
    hook->PageSize = PAGE_SIZE;
    hook->ShadowPage = ShadowPage;
    hook->ShadowPagePA = ShadowPagePhys.QuadPart;
    hook->OriginalPagePA = 0;  // Will be set below after getting original page PA
    hook->IsActive = FALSE;  // Will be activated after EPT setup
    KeQuerySystemTime(&hook->CreateTime);

    // Add to hook list
    DdimonpAddPageHook(hook);

    // Set EPT permissions for this single page
    // CRITICAL: Get physical address in target process context
    ULONG64 OriginalPagePA = 0;

    // Get target process for physical address lookup
    PEPROCESS TargetProcessForPA;
    NTSTATUS paStatus = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &TargetProcessForPA);
    if (NT_SUCCESS(paStatus)) {
        // Attach to target process to get correct physical address
        KAPC_STATE apcState = DdimonpAttachToProcess(TargetProcessForPA);
        __try {
            PHYSICAL_ADDRESS physAddr = MmGetPhysicalAddress((PVOID)PageAddress);
            OriginalPagePA = physAddr.QuadPart;
            HYPERPLATFORM_LOG_INFO("Original page PA in target process: 0x%llX", OriginalPagePA);
        } __except(EXCEPTION_EXECUTE_HANDLER) {
            HYPERPLATFORM_LOG_ERROR("Failed to get physical address in target process");
            OriginalPagePA = 0;
        }
        DdimonpDetachFromProcess(&apcState);
        ObDereferenceObject(TargetProcessForPA);
    } else {
        HYPERPLATFORM_LOG_ERROR("Failed to lookup target process for PA: 0x%08X", paStatus);
        OriginalPagePA = 0;
    }

    if (OriginalPagePA == 0) {
        HYPERPLATFORM_LOG_ERROR("Invalid original page physical address");
        ExFreePoolWithTag(ShadowPage, 'Hook');
        ExFreePoolWithTag(hook, 'Hook');
        return STATUS_UNSUCCESSFUL;
    }

    // CRITICAL: Store original page PA for EPT restoration during hook removal
    hook->OriginalPagePA = OriginalPagePA;

    struct {
        ULONG64 physical_address;
        ULONG64 new_permissions;
        ULONG64 shadow_exec_pa;
    } vmcall_params = {
        OriginalPagePA,
        0x03,  // R=1, W=1, X=0 (safer: allow read/write, force execute violations)
        hook->ShadowPagePA
    };

    HYPERPLATFORM_LOG_INFO("DdiMon EPT permission request: PA=0x%llX, Perms=0x%llX, Shadow=0x%llX",
                           vmcall_params.physical_address, vmcall_params.new_permissions, vmcall_params.shadow_exec_pa);

    status = UtilVmCall(static_cast<HypercallNumber>(5), &vmcall_params);
    if (NT_SUCCESS(status)) {
        hook->IsActive = TRUE;
        HYPERPLATFORM_LOG_INFO("Page hook activated: HookId=%llu, Address=0x%llX", hook->HookId, PageAddress);
    } else {
        HYPERPLATFORM_LOG_ERROR("Failed to set EPT permissions: 0x%08X", status);
    }

    *HookId = hook->HookId;
    return STATUS_SUCCESS;
}

// REMOVED: Individual page hook removal function
// Per new design: User manages hook points via EptWriteMemory API
// - Write modified code = Create/Update hook
// - Write original code = Restore hook
// - Only DdimonpRemoveAllPageHooks is called during driver unload
// This eliminates the risk of accidental crashes from individual hook removal

// Remove all page hooks
_Use_decl_annotations_ NTSTATUS DdimonpRemoveAllPageHooks(void) {
    if (!g_PageHooksInitialized) {
        return STATUS_SUCCESS; // Nothing to remove
    }

    PPAGE_HOOK_INFO current = (PPAGE_HOOK_INFO)InterlockedExchangePointer((PVOID*)&g_PageHooks, nullptr);
    ULONG removedCount = 0;
    ULONG eptRestoredCount = 0;

    // First pass: Restore all EPT permissions
    PPAGE_HOOK_INFO temp = current;
    while (temp) {
        PPAGE_HOOK_INFO next = temp->Next;

        // CRITICAL: Restore EPT permissions to original state FIRST
        if (temp->IsActive) {
            // Use the original page physical address that was stored during hook creation
            ULONG64 originalPagePA = temp->OriginalPagePA;

            struct {
                ULONG64 physical_address;
                ULONG64 new_permissions;
                ULONG64 shadow_exec_pa;
            } vmcall_params = {
                originalPagePA,  // Use stored original page PA
                0x07,  // R=1, W=1, X=1 (restore full access)
                0
            };

            if (originalPagePA != 0) {
                NTSTATUS eptStatus = UtilVmCall(static_cast<HypercallNumber>(5), &vmcall_params);
                if (NT_SUCCESS(eptStatus)) {
                    HYPERPLATFORM_LOG_INFO("Hook removed: Restored EPT permissions for PA=0x%llX", originalPagePA);
                    eptRestoredCount++;
                } else {
                    HYPERPLATFORM_LOG_ERROR("Failed to restore EPT permissions: Status=0x%08X", eptStatus);
                }
            } else {
                HYPERPLATFORM_LOG_ERROR("Failed to restore EPT permissions: Original PA is 0");
            }
        }

        temp = next;
    }

    // CRITICAL: Wait for EPT invalidation to complete on all processors
    if (eptRestoredCount > 0) {
        LARGE_INTEGER delay;
        delay.QuadPart = -50000; // 5ms delay for multiple hooks
        KeDelayExecutionThread(KernelMode, FALSE, &delay);
        HYPERPLATFORM_LOG_INFO("EPT flush completed for %lu hooks", eptRestoredCount);
    }

    // Second pass: Free all shadow pages and hook structures
    while (current) {
        PPAGE_HOOK_INFO next = current->Next;

        // CRITICAL: Now safe to free shadow page after EPT permissions are restored
        if (current->ShadowPage) {
            ExFreePoolWithTag(current->ShadowPage, 'Hook');
        }

        // Free hook structure
        ExFreePoolWithTag(current, 'Hook');
        removedCount++;

        current = next;
    }

    return STATUS_SUCCESS;
}

// REMOVED: Individual EPT hook remove handler
// Per new design: User manages hook points via EptWriteMemory API
// Individual hook removal is no longer supported to prevent accidental crashes

// Handle EPT hook remove all request
_Use_decl_annotations_ NTSTATUS DdimonHandleEptHookRemoveAll(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    PAGED_CODE();
    UNREFERENCED_PARAMETER(InputBuffer);
    UNREFERENCED_PARAMETER(InputBufferLength);
    UNREFERENCED_PARAMETER(OutputBuffer);
    UNREFERENCED_PARAMETER(OutputBufferLength);

    if (!BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = DdimonpRemoveAllPageHooks();

    *BytesReturned = 0;
    return status;
}

// OPTIMIZED: High-performance EPT violation handler based on user's correct understanding
BOOLEAN DdimonpHandleDynamicEptViolation(struct _EptData* ept_data, void* fault_va) {
    // User's correct understanding confirmed:
    // 1. Pages entering this function are definitely already hooked
    // 2. Non-hooked pages never trigger EPT violations, never enter this function
    // 3. Therefore we can minimize hook lookups, only when necessary
    //
    // Optimization strategy:
    // - Execute access: Need hook lookup (get shadow page address)
    // - Read/Write access: No hook lookup needed (directly use original page address)
    // - Performance gain: Reduce 50% of hook lookup operations

    // Basic parameter check
    if (!ept_data || !fault_va) return FALSE;

    // Get physical address and EPT entry
    ULONG64 faultPhysicalAddress = UtilVmRead64(VmcsField::kGuestPhysicalAddress);
    const auto ept_pt_entry = EptGetEptPtEntry(reinterpret_cast<EptData*>(ept_data), faultPhysicalAddress);
    if (!ept_pt_entry) return FALSE;

    // Get violation type
    EptViolationQualification exit_qualification;
    exit_qualification.all = UtilVmRead(VmcsField::kExitQualification);

    // High-performance page switching logic
    if (exit_qualification.fields.execute_access) {
        // Execute access -> Switch to shadow page (need hook lookup for shadow page address)
        ULONG64 faultAddress = reinterpret_cast<ULONG64>(fault_va);
        HANDLE ProcessId = PsGetCurrentProcessId();
        PPAGE_HOOK_INFO pageHook = DdimonpFindPageHookByAddress(faultAddress, (ULONG64)ProcessId);

        ept_pt_entry->fields.execute_access = 1;
        ept_pt_entry->fields.read_access = 0;
        ept_pt_entry->fields.write_access = 0;

        if (pageHook && pageHook->IsActive) {
            ept_pt_entry->fields.physial_address = pageHook->ShadowPagePA >> 12;
            HYPERPLATFORM_LOG_DEBUG("Execute access: Switched to shadow page PA=0x%llX", pageHook->ShadowPagePA);
        } else {
            // Should theoretically never happen, but maintain defensive programming
            HYPERPLATFORM_LOG_ERROR("Execute access but no active hook found for VA=0x%llX", faultAddress);
            return FALSE;
        }
    } else {
        // Read/Write access -> Switch to original page (no hook lookup needed, directly calculate original page address)
        ept_pt_entry->fields.execute_access = 0;
        ept_pt_entry->fields.read_access = 1;
        ept_pt_entry->fields.write_access = 1;
        ept_pt_entry->fields.physial_address = (faultPhysicalAddress & ~0xFFF) >> 12;

        HYPERPLATFORM_LOG_DEBUG("Read/Write access: Switched to original page PA=0x%llX",
                                faultPhysicalAddress & ~0xFFF);
    }

    UtilInveptGlobal();
    return TRUE;
}

// 删除有语法错误的函数，保留工作版本

// Cleanup all module mappings - called during driver unload
VOID DdimonpCleanupDynamicHooks() {
    if (!g_ModuleMappingsInitialized) {
        return;
    }

    HYPERPLATFORM_LOG_INFO("Cleaning up module mappings...");

    // Get current list and clear it atomically
    PMAIN_MODULE_MAPPING current = (PMAIN_MODULE_MAPPING)InterlockedExchangePointer((PVOID*)&g_ModuleMappings, nullptr);
    ULONG mappingCount = 0;

    // Free all mappings
    while (current) {
        PMAIN_MODULE_MAPPING next = current->Next;

        // Free execute pages
        if (current->ExecutePages) {
            ExFreePoolWithTag(current->ExecutePages, 'Mod');
            HYPERPLATFORM_LOG_DEBUG("Freed execute pages for process %llu", current->ProcessId);
        }

        // Free mapping structure
        ExFreePoolWithTag(current, 'Mod');
        mappingCount++;

        current = next;
    }

    // Reset global state
    InterlockedExchange((LONG*)&g_ModuleMappingsInitialized, FALSE);

    HYPERPLATFORM_LOG_INFO("Module mappings cleanup completed: %lu mappings freed", mappingCount);
}

// Handle EPT hook list request
_Use_decl_annotations_ NTSTATUS DdimonHandleEptHookList(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    PAGED_CODE();

    HYPERPLATFORM_LOG_INFO("DdimonHandleEptHookList ENTRY: InputLen=%lu, OutputLen=%lu",
                           InputBufferLength, OutputBufferLength);

    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        HYPERPLATFORM_LOG_ERROR("DdimonHandleEptHookList: Invalid parameters");
        return STATUS_INVALID_PARAMETER;
    }

    if (InputBufferLength < sizeof(EPT_HOOK_LIST_REQUEST)) {
        HYPERPLATFORM_LOG_ERROR("DdimonHandleEptHookList: Input buffer too small");
        return STATUS_BUFFER_TOO_SMALL;
    }

    if (OutputBufferLength < sizeof(EPT_HOOK_LIST_RESPONSE)) {
        HYPERPLATFORM_LOG_ERROR("DdimonHandleEptHookList: Output buffer too small");
        return STATUS_BUFFER_TOO_SMALL;
    }

    PEPT_HOOK_LIST_REQUEST request = (PEPT_HOOK_LIST_REQUEST)InputBuffer;
    PEPT_HOOK_LIST_RESPONSE response = (PEPT_HOOK_LIST_RESPONSE)OutputBuffer;

    // CRITICAL: Copy request to local variable to avoid buffer corruption
    EPT_HOOK_LIST_REQUEST localRequest;
    RtlCopyMemory(&localRequest, request, sizeof(EPT_HOOK_LIST_REQUEST));

    HYPERPLATFORM_LOG_INFO("Hook list request: MaxCount=%lu (from local copy)", localRequest.MaxCount);
    HYPERPLATFORM_LOG_INFO("Hook list request: MaxCount=%lu (direct access)", request->MaxCount);

    // Initialize response
    RtlZeroMemory(response, OutputBufferLength);
    response->Status = STATUS_SUCCESS;
    response->HookCount = 0;

    if (!g_PageHooksInitialized || !g_PageHooks) {
        HYPERPLATFORM_LOG_INFO("No page hooks initialized or no hooks exist");
        *BytesReturned = sizeof(EPT_HOOK_LIST_RESPONSE);
        return STATUS_SUCCESS;
    }

    // Calculate maximum hooks we can return
    ULONG maxHooks = (OutputBufferLength - sizeof(EPT_HOOK_LIST_RESPONSE)) / sizeof(EPT_HOOK_INFO);
    if (maxHooks > localRequest.MaxCount) {
        maxHooks = localRequest.MaxCount;
    }

    HYPERPLATFORM_LOG_INFO("Enumerating page hooks: MaxHooks=%lu", maxHooks);

    // Enumerate page hooks
    PPAGE_HOOK_INFO current = g_PageHooks;
    ULONG hookIndex = 0;
    ULONG iterations = 0;
    const ULONG maxIterations = 1000; // Prevent infinite loops

    while (current && hookIndex < maxHooks && iterations < maxIterations) {
        PEPT_HOOK_INFO hookInfo = &response->Hooks[hookIndex];

        hookInfo->HookId = current->HookId;
        hookInfo->TargetAddress = current->TargetAddress;
        hookInfo->ProcessId = current->ProcessId;
        hookInfo->ShadowPagePA = current->ShadowPagePA;
        hookInfo->IsActive = TRUE; // All hooks in the list are active
        hookInfo->CreateTime = current->CreateTime;

        HYPERPLATFORM_LOG_INFO("Hook[%lu]: ID=%llu, VA=0x%llX, PID=%lu, ShadowPA=0x%llX",
                               hookIndex, hookInfo->HookId, hookInfo->TargetAddress,
                               hookInfo->ProcessId, hookInfo->ShadowPagePA);

        hookIndex++;
        current = current->Next;
        iterations++;
    }

    response->HookCount = hookIndex;
    *BytesReturned = sizeof(EPT_HOOK_LIST_RESPONSE) + (hookIndex * sizeof(EPT_HOOK_INFO));

    HYPERPLATFORM_LOG_INFO("DdimonHandleEptHookList completed: HookCount=%lu, BytesReturned=%lu",
                           response->HookCount, *BytesReturned);

    return STATUS_SUCCESS;
}

}  // extern "C"
