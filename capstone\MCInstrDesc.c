/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */

#include "MCInstrDesc.h"

/// isPredicate - Set if this is one of the operands that made up of
/// the predicate operand that controls an isPredicable() instruction.
bool MCOperandInfo_isPredicate(MCOperandInfo *m)
{
	return m->Flags & (1 << MCOI_Predicate);
}

/// isOptionalDef - Set if this operand is a optional def.
///
bool MCOperandInfo_isOptionalDef(MCOperandInfo *m)
{
	return m->Flags & (1 << MCOI_OptionalDef);
}
