// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Implements IOCTL handlers for DdiMon EPT memory operations.

#include "shadow_hook.h"
#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "../HyperPlatform/HyperPlatform/util.h"
#include "../HyperPlatform/HyperPlatform/ept.h"
#include "../HyperPlatform/HyperPlatform/ia32_type.h"  // For EptViolationQualification
#include "ddi_mon_ioctl.h"
#include <ntimage.h>  // For PE structures
#include <intrin.h>   // For __vmx_vmcall intrinsic

// Ensure PAGE_SIZE is correctly defined
#ifndef PAGE_SIZE
#define PAGE_SIZE 0x1000
#endif

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding management structures and globals
//

// Hidden process entry structure
typedef struct _HIDDEN_PROCESS_ENTRY {
    ULONG ProcessId;                // Process ID
    PEPROCESS ProcessObject;        // Process object reference
    LARGE_INTEGER HideTime;         // Time when hidden
    WCHAR ProcessName[16];          // Process name (truncated)
    struct _HIDDEN_PROCESS_ENTRY* Next; // Next entry in list
} HIDDEN_PROCESS_ENTRY, *PHIDDEN_PROCESS_ENTRY;

// Global hidden process management
static volatile PHIDDEN_PROCESS_ENTRY g_HiddenProcesses = nullptr;
static volatile BOOLEAN g_ProcessHidingInitialized = FALSE;
static KSPIN_LOCK g_HiddenProcessesLock;

////////////////////////////////////////////////////////////////////////////////
//
// Forward declarations
//

static NTSTATUS DdimonpInitializeProcessHiding(void);
static NTSTATUS DdimonpAddHiddenProcess(ULONG ProcessId);
static NTSTATUS DdimonpRemoveHiddenProcess(ULONG ProcessId);
static PHIDDEN_PROCESS_ENTRY DdimonpFindHiddenProcess(ULONG ProcessId);
static VOID DdimonpCleanupHiddenProcesses(void);

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding implementation
//

// Initialize process hiding system
static NTSTATUS DdimonpInitializeProcessHiding(void) {
    HYPERPLATFORM_LOG_INFO("DdimonpInitializeProcessHiding: Entry");
    
    if (InterlockedCompareExchange((LONG*)&g_ProcessHidingInitialized, TRUE, FALSE) != FALSE) {
        HYPERPLATFORM_LOG_INFO("DdimonpInitializeProcessHiding: Already initialized");
        return STATUS_SUCCESS;  // Already initialized
    }

    HYPERPLATFORM_LOG_INFO("DdimonpInitializeProcessHiding: Initializing spin lock");
    // Initialize spin lock
    KeInitializeSpinLock(&g_HiddenProcessesLock);
    
    HYPERPLATFORM_LOG_INFO("DdimonpInitializeProcessHiding: Initializing list pointer");
    // Initialize list
    InterlockedExchangePointer((PVOID*)&g_HiddenProcesses, nullptr);

    HYPERPLATFORM_LOG_INFO("DdimonpInitializeProcessHiding: Process hiding system initialized successfully");
    return STATUS_SUCCESS;
}

// Add process to hidden list
static NTSTATUS DdimonpAddHiddenProcess(ULONG ProcessId) {
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Entry - PID=%lu", ProcessId);

    // Validate process ID
    if (ProcessId == 0 || ProcessId == 4) {  // Don't hide System Idle Process or System Process
        HYPERPLATFORM_LOG_ERROR("DdimonpAddHiddenProcess: Invalid process ID: %lu", ProcessId);
        return STATUS_INVALID_PARAMETER;
    }

    // Initialize if needed
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Initializing process hiding system");
    NTSTATUS initStatus = DdimonpInitializeProcessHiding();
    if (!NT_SUCCESS(initStatus)) {
        HYPERPLATFORM_LOG_ERROR("DdimonpAddHiddenProcess: Failed to initialize: 0x%08X", initStatus);
        return initStatus;
    }

    // Check if already hidden
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Checking if process already hidden");
    if (DdimonpIsProcessHidden(ProcessId)) {
        HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Process already hidden: PID=%lu", ProcessId);
        return STATUS_SUCCESS;
    }

    // Get process object
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Looking up process object for PID=%lu", ProcessId);
    PEPROCESS ProcessObject = nullptr;
    NTSTATUS status = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &ProcessObject);
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("DdimonpAddHiddenProcess: Failed to lookup process: PID=%lu, Status=0x%08X", ProcessId, status);
        // 返回更具体的错误代码
        if (status == STATUS_INVALID_PARAMETER) {
            return STATUS_INVALID_PARAMETER;
        } else {
            return STATUS_NOT_FOUND;  // 进程不存在
        }
    }

    if (!ProcessObject) {
        HYPERPLATFORM_LOG_ERROR("DdimonpAddHiddenProcess: Process object is NULL for PID=%lu", ProcessId);
        return STATUS_NOT_FOUND;
    }

    // Allocate hidden process entry
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Allocating memory for hidden process entry");
    PHIDDEN_PROCESS_ENTRY entry = static_cast<PHIDDEN_PROCESS_ENTRY>(
        ExAllocatePoolWithTag(NonPagedPool, sizeof(HIDDEN_PROCESS_ENTRY), 'Hide'));
    if (!entry) {
        HYPERPLATFORM_LOG_ERROR("DdimonpAddHiddenProcess: Failed to allocate hidden process entry");
        ObDereferenceObject(ProcessObject);
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Initializing hidden process entry");
    // Initialize entry
    RtlZeroMemory(entry, sizeof(HIDDEN_PROCESS_ENTRY));
    entry->ProcessId = ProcessId;
    entry->ProcessObject = ProcessObject;  // Keep reference
    KeQuerySystemTime(&entry->HideTime);

    // Get process name (safe approach)
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Setting process name");
    // Use a safer approach - just set a generic name for now
    // This avoids potential crashes from accessing unknown memory offsets
    wcscpy_s(entry->ProcessName, 16, L"Hidden");
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Process name set successfully");

    // Add to list with lock
    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Adding to hidden process list");
    KIRQL oldIrql;
    KeAcquireSpinLock(&g_HiddenProcessesLock, &oldIrql);
    entry->Next = (PHIDDEN_PROCESS_ENTRY)g_HiddenProcesses;
    InterlockedExchangePointer((PVOID*)&g_HiddenProcesses, entry);
    KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);

    HYPERPLATFORM_LOG_INFO("DdimonpAddHiddenProcess: Process added to hidden list successfully: PID=%lu, Name=%S", ProcessId, entry->ProcessName);
    return STATUS_SUCCESS;
}

// Remove process from hidden list
static NTSTATUS DdimonpRemoveHiddenProcess(ULONG ProcessId) {
    HYPERPLATFORM_LOG_INFO("Removing process from hidden list: PID=%lu", ProcessId);

    if (!g_ProcessHidingInitialized) {
        return STATUS_NOT_FOUND;
    }

    KIRQL oldIrql;
    KeAcquireSpinLock(&g_HiddenProcessesLock, &oldIrql);

    PHIDDEN_PROCESS_ENTRY* current = (PHIDDEN_PROCESS_ENTRY*)&g_HiddenProcesses;
    while (*current) {
        if ((*current)->ProcessId == ProcessId) {
            PHIDDEN_PROCESS_ENTRY toRemove = *current;
            *current = toRemove->Next;
            KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);

            // Release process object reference
            if (toRemove->ProcessObject) {
                ObDereferenceObject(toRemove->ProcessObject);
            }

            ExFreePoolWithTag(toRemove, 'Hide');
            HYPERPLATFORM_LOG_INFO("Process removed from hidden list: PID=%lu", ProcessId);
            return STATUS_SUCCESS;
        }
        current = &((*current)->Next);
    }

    KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);
    HYPERPLATFORM_LOG_INFO("Process not found in hidden list: PID=%lu", ProcessId);
    return STATUS_NOT_FOUND;
}

// Find hidden process entry
static PHIDDEN_PROCESS_ENTRY DdimonpFindHiddenProcess(ULONG ProcessId) {
    if (!g_ProcessHidingInitialized) {
        return nullptr;
    }

    if (ProcessId == 0) {
        return nullptr;
    }

    KIRQL oldIrql;
    KeAcquireSpinLock(&g_HiddenProcessesLock, &oldIrql);

    PHIDDEN_PROCESS_ENTRY current = (PHIDDEN_PROCESS_ENTRY)g_HiddenProcesses;
    while (current) {
        if (!MmIsAddressValid(current)) {
            HYPERPLATFORM_LOG_ERROR("DdimonpFindHiddenProcess: Invalid current entry address");
            KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);
            return nullptr;
        }
        
        if (current->ProcessId == ProcessId) {
            KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);
            return current;
        }
        current = current->Next;
    }

    KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);
    return nullptr;
}

// Check if process is hidden (extern "C" for use by process hiding hooks)
extern "C" BOOLEAN DdimonpIsProcessHidden(ULONG ProcessId) {
    if (!g_ProcessHidingInitialized) {
        return FALSE;
    }
    
    if (ProcessId == 0) {
        return FALSE;
    }
    
    return DdimonpFindHiddenProcess(ProcessId) != nullptr;
}

// Cleanup all hidden processes
static VOID DdimonpCleanupHiddenProcesses(void) {
    if (!g_ProcessHidingInitialized) {
        return;
    }

    HYPERPLATFORM_LOG_INFO("Cleaning up hidden processes...");

    KIRQL oldIrql;
    KeAcquireSpinLock(&g_HiddenProcessesLock, &oldIrql);

    PHIDDEN_PROCESS_ENTRY current = (PHIDDEN_PROCESS_ENTRY)g_HiddenProcesses;
    InterlockedExchangePointer((PVOID*)&g_HiddenProcesses, nullptr);
    KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);

    ULONG count = 0;
    while (current) {
        PHIDDEN_PROCESS_ENTRY next = current->Next;

        // Release process object reference
        if (current->ProcessObject) {
            ObDereferenceObject(current->ProcessObject);
        }

        ExFreePoolWithTag(current, 'Hide');
        count++;
        current = next;
    }

    InterlockedExchange((LONG*)&g_ProcessHidingInitialized, FALSE);
    HYPERPLATFORM_LOG_INFO("Hidden processes cleanup completed: %lu processes freed", count);
}

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL handlers for process hiding
//

// Handle hide process request
_Use_decl_annotations_ NTSTATUS DdimonHandleHideProcess(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    HYPERPLATFORM_LOG_INFO("DdimonHandleHideProcess: Entry");

    PAGED_CODE();

    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        HYPERPLATFORM_LOG_ERROR("DdimonHandleHideProcess: Invalid parameters");
        return STATUS_INVALID_PARAMETER;
    }

    if (InputBufferLength < sizeof(PROCESS_HIDE_REQUEST) ||
        OutputBufferLength < sizeof(PROCESS_HIDE_RESPONSE)) {
        HYPERPLATFORM_LOG_ERROR("DdimonHandleHideProcess: Buffer too small - Input:%lu, Output:%lu",
                               InputBufferLength, OutputBufferLength);
        return STATUS_BUFFER_TOO_SMALL;
    }

    auto request = static_cast<PPROCESS_HIDE_REQUEST>(InputBuffer);
    auto response = static_cast<PPROCESS_HIDE_RESPONSE>(OutputBuffer);

    HYPERPLATFORM_LOG_INFO("DdimonHandleHideProcess: Process hide request - PID=%lu, Hide=%s",
                           request->ProcessId, request->Hide ? "TRUE" : "FALSE");

    // Initialize response
    HYPERPLATFORM_LOG_INFO("DdimonHandleHideProcess: Initializing response");
    RtlZeroMemory(response, sizeof(PROCESS_HIDE_RESPONSE));
    response->PreviousState = DdimonpIsProcessHidden(request->ProcessId);

    if (request->Hide) {
        // Hide the process
        HYPERPLATFORM_LOG_INFO("DdimonHandleHideProcess: Hiding process PID=%lu", request->ProcessId);
        response->Status = DdimonpAddHiddenProcess(request->ProcessId);
    } else {
        // Show the process
        HYPERPLATFORM_LOG_INFO("DdimonHandleHideProcess: Showing process PID=%lu", request->ProcessId);
        response->Status = DdimonpRemoveHiddenProcess(request->ProcessId);
    }

    *BytesReturned = sizeof(PROCESS_HIDE_RESPONSE);
    HYPERPLATFORM_LOG_INFO("DdimonHandleHideProcess: Completed with status 0x%08X", response->Status);
    return STATUS_SUCCESS;
}

// Handle show process request
_Use_decl_annotations_ NTSTATUS DdimonHandleShowProcess(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    PAGED_CODE();

    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }

    if (InputBufferLength < sizeof(PROCESS_HIDE_REQUEST) ||
        OutputBufferLength < sizeof(PROCESS_HIDE_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    // Convert hide request to show request
    auto request = static_cast<PPROCESS_HIDE_REQUEST>(InputBuffer);
    PROCESS_HIDE_REQUEST hideRequest = { request->ProcessId, FALSE };

    return DdimonHandleHideProcess(&hideRequest, sizeof(hideRequest),
                                  OutputBuffer, OutputBufferLength,
                                  BytesReturned);
}

// Handle list hidden processes request
_Use_decl_annotations_ NTSTATUS DdimonHandleListHiddenProcesses(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    PAGED_CODE();

    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }

    if (InputBufferLength < sizeof(LIST_HIDDEN_PROCESSES_REQUEST) ||
        OutputBufferLength < sizeof(LIST_HIDDEN_PROCESSES_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    auto request = static_cast<PLIST_HIDDEN_PROCESSES_REQUEST>(InputBuffer);
    auto response = static_cast<PLIST_HIDDEN_PROCESSES_RESPONSE>(OutputBuffer);

    // Initialize response
    RtlZeroMemory(response, sizeof(LIST_HIDDEN_PROCESSES_RESPONSE));
    response->Status = STATUS_SUCCESS;
    response->ProcessCount = 0;

    if (!g_ProcessHidingInitialized) {
        *BytesReturned = sizeof(LIST_HIDDEN_PROCESSES_RESPONSE);
        return STATUS_SUCCESS;
    }

    // Calculate maximum processes that can fit in output buffer
    ULONG maxProcesses = (OutputBufferLength - sizeof(LIST_HIDDEN_PROCESSES_RESPONSE)) / sizeof(HIDDEN_PROCESS_INFO);
    if (maxProcesses > request->MaxCount) {
        maxProcesses = request->MaxCount;
    }

    KIRQL oldIrql;
    KeAcquireSpinLock(&g_HiddenProcessesLock, &oldIrql);

    PHIDDEN_PROCESS_ENTRY current = (PHIDDEN_PROCESS_ENTRY)g_HiddenProcesses;
    ULONG processIndex = 0;

    while (current && processIndex < maxProcesses) {
        PHIDDEN_PROCESS_INFO processInfo = &response->Processes[processIndex];
        processInfo->ProcessId = current->ProcessId;
        processInfo->HideTime = current->HideTime;
        wcscpy_s(processInfo->ProcessName, 16, current->ProcessName);

        processIndex++;
        current = current->Next;
    }

    KeReleaseSpinLock(&g_HiddenProcessesLock, oldIrql);

    response->ProcessCount = processIndex;
    *BytesReturned = sizeof(LIST_HIDDEN_PROCESSES_RESPONSE) + (processIndex * sizeof(HIDDEN_PROCESS_INFO));

    return STATUS_SUCCESS;
}

////////////////////////////////////////////////////////////////////////////////
//
// Simplified EPT memory operations (basic implementation)
//

// Handle EPT read memory request
_Use_decl_annotations_ NTSTATUS DdimonHandleEptReadMemory(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    PAGED_CODE();

    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }

    if (InputBufferLength < sizeof(EPT_READ_MEMORY_REQUEST)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    if (OutputBufferLength < sizeof(EPT_READ_MEMORY_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    auto request = static_cast<PEPT_READ_MEMORY_REQUEST>(InputBuffer);
    auto response = static_cast<PEPT_READ_MEMORY_RESPONSE>(OutputBuffer);

    // Initialize response
    RtlZeroMemory(response, sizeof(EPT_READ_MEMORY_RESPONSE));
    response->Status = STATUS_NOT_IMPLEMENTED;
    response->BytesRead = 0;

    *BytesReturned = sizeof(EPT_READ_MEMORY_RESPONSE);
    return STATUS_SUCCESS;
}

// Handle EPT write memory request
_Use_decl_annotations_ NTSTATUS DdimonHandleEptWriteMemory(
    PVOID InputBuffer, ULONG InputBufferLength,
    PVOID OutputBuffer, ULONG OutputBufferLength,
    PULONG BytesReturned) {

    PAGED_CODE();

    if (!InputBuffer || !OutputBuffer || !BytesReturned) {
        return STATUS_INVALID_PARAMETER;
    }

    if (InputBufferLength < sizeof(EPT_WRITE_MEMORY_REQUEST)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    if (OutputBufferLength < sizeof(EPT_WRITE_MEMORY_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    auto response = static_cast<PEPT_WRITE_MEMORY_RESPONSE>(OutputBuffer);

    // Initialize response
    RtlZeroMemory(response, sizeof(EPT_WRITE_MEMORY_RESPONSE));
    response->Status = STATUS_NOT_IMPLEMENTED;
    response->BytesWritten = 0;

    *BytesReturned = sizeof(EPT_WRITE_MEMORY_RESPONSE);
    return STATUS_SUCCESS;
}

////////////////////////////////////////////////////////////////////////////////
//
// Cleanup function for external access
//

// Cleanup all hidden processes (extern "C" function for cross-module access)
extern "C" VOID DdimonpCleanupHiddenProcessesExternal(void) {
    DdimonpCleanupHiddenProcesses();
}
