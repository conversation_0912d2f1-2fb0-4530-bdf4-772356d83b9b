﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Driver Files">
      <UniqueIdentifier>{8E41214B-6785-4CFE-B992-037D68949A14}</UniqueIdentifier>
      <Extensions>inf;inv;inx;mof;mc;</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="driver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ept.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="log.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="performance.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="util.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="kernel_stl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="global_object.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="power_callback.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hotplug_callback.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="asm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ept.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ia32_type.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="perf_counter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="performance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="driver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="util_page_constants.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="global_object.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hotplug_callback.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="power_callback.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <MASM Include="Arch\x64\x64.asm">
      <Filter>Source Files</Filter>
    </MASM>
    <MASM Include="Arch\x86\x86.asm">
      <Filter>Source Files</Filter>
    </MASM>
  </ItemGroup>
  <ItemGroup>
    <None Include="HyperPlatform.ruleset" />
  </ItemGroup>
</Project>