This file credits all the contributors of the Capstone engine project.

Key developers
==============
1. <PERSON><PERSON><PERSON> <aquynh -at- gmail.com>
	- Core engine
	- Bindings: Python, Ruby, OCaml, Java, C#

2. <PERSON> <shengdi -at- coseinc.com>
	- Bindings: Ruby

3. <PERSON> <ben -at- coseinc.com>
	- Bindings: <PERSON>, Go

4. <PERSON><PERSON> <dang.hvu -at- gmail.com>
	- Bindings: Java


Beta testers (in random order)
==============================
Pancake
Van <PERSON>user
FX of Phenoelit
The Grugq, The Grugq   <-- our hero for submitting the first ever patch!
<PERSON>, Veracode Inc
Patroklos Argyroudis, Census Inc. (http://census-labs.com)
Attila Suszter
Le <PERSON>, Winsider Seminars & Solutions Inc.
Snare
<PERSON>-Lopez
<PERSON>. <PERSON>-<PERSON>g
<PERSON>


Contributors (in no particular order)
=====================================
(Please let us know if you want to have your name here)

<PERSON>nås (author of the 100th Pull-Request in our Github repo, thanks!)
Axel "0vercl0k" Souchet (@0vercl0k) & Alex Ionescu: port to MSVC.
Daniel Pistelli: Cmake support.
Peter Hlavaty: integrate Capstone for Windows kernel drivers.
Guillaume Jeanne: Ocaml binding.
Martin Tofall, Obsidium Software: Optimize X86 performance & size.
David Martínez Moreno & Hilko Bengen: Debian package.
Félix Cloutier: Xcode project.
Benoit Lecocq: OpenBSD package.
Christophe Avoinne (Hlide): Improve memory management for better performance.
Michael Cohen & Nguyen Tan Cong: Python module installer.
Adel Gadllah, Francisco Alonso & Stefan Cornelius: RPM package.
Felix Gröbert (Google): fuzz testing harness.
Xipiter LLC: Capstone logo redesigned.
Satoshi Tanda: Support Windows kernel driver.
