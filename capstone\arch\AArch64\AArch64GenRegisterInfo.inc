/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Register Enum Values                                                 *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_REGINFO_ENUM
#undef GET_REGINFO_ENUM

enum {
  AArch64_NoRegister,
  AArch64_FP = 1,
  AArch64_LR = 2,
  AArch64_NZCV = 3,
  AArch64_SP = 4,
  AArch64_WSP = 5,
  AArch64_WZR = 6,
  AArch64_XZR = 7,
  AArch64_B0 = 8,
  AArch64_B1 = 9,
  AArch64_B2 = 10,
  AArch64_B3 = 11,
  AArch64_B4 = 12,
  AArch64_B5 = 13,
  AArch64_B6 = 14,
  AArch64_B7 = 15,
  AArch64_B8 = 16,
  AArch64_B9 = 17,
  AArch64_B10 = 18,
  AArch64_B11 = 19,
  AArch64_B12 = 20,
  AArch64_B13 = 21,
  AArch64_B14 = 22,
  AArch64_B15 = 23,
  AArch64_B16 = 24,
  AArch64_B17 = 25,
  AArch64_B18 = 26,
  AArch64_B19 = 27,
  AArch64_B20 = 28,
  AArch64_B21 = 29,
  AArch64_B22 = 30,
  AArch64_B23 = 31,
  AArch64_B24 = 32,
  AArch64_B25 = 33,
  AArch64_B26 = 34,
  AArch64_B27 = 35,
  AArch64_B28 = 36,
  AArch64_B29 = 37,
  AArch64_B30 = 38,
  AArch64_B31 = 39,
  AArch64_D0 = 40,
  AArch64_D1 = 41,
  AArch64_D2 = 42,
  AArch64_D3 = 43,
  AArch64_D4 = 44,
  AArch64_D5 = 45,
  AArch64_D6 = 46,
  AArch64_D7 = 47,
  AArch64_D8 = 48,
  AArch64_D9 = 49,
  AArch64_D10 = 50,
  AArch64_D11 = 51,
  AArch64_D12 = 52,
  AArch64_D13 = 53,
  AArch64_D14 = 54,
  AArch64_D15 = 55,
  AArch64_D16 = 56,
  AArch64_D17 = 57,
  AArch64_D18 = 58,
  AArch64_D19 = 59,
  AArch64_D20 = 60,
  AArch64_D21 = 61,
  AArch64_D22 = 62,
  AArch64_D23 = 63,
  AArch64_D24 = 64,
  AArch64_D25 = 65,
  AArch64_D26 = 66,
  AArch64_D27 = 67,
  AArch64_D28 = 68,
  AArch64_D29 = 69,
  AArch64_D30 = 70,
  AArch64_D31 = 71,
  AArch64_H0 = 72,
  AArch64_H1 = 73,
  AArch64_H2 = 74,
  AArch64_H3 = 75,
  AArch64_H4 = 76,
  AArch64_H5 = 77,
  AArch64_H6 = 78,
  AArch64_H7 = 79,
  AArch64_H8 = 80,
  AArch64_H9 = 81,
  AArch64_H10 = 82,
  AArch64_H11 = 83,
  AArch64_H12 = 84,
  AArch64_H13 = 85,
  AArch64_H14 = 86,
  AArch64_H15 = 87,
  AArch64_H16 = 88,
  AArch64_H17 = 89,
  AArch64_H18 = 90,
  AArch64_H19 = 91,
  AArch64_H20 = 92,
  AArch64_H21 = 93,
  AArch64_H22 = 94,
  AArch64_H23 = 95,
  AArch64_H24 = 96,
  AArch64_H25 = 97,
  AArch64_H26 = 98,
  AArch64_H27 = 99,
  AArch64_H28 = 100,
  AArch64_H29 = 101,
  AArch64_H30 = 102,
  AArch64_H31 = 103,
  AArch64_Q0 = 104,
  AArch64_Q1 = 105,
  AArch64_Q2 = 106,
  AArch64_Q3 = 107,
  AArch64_Q4 = 108,
  AArch64_Q5 = 109,
  AArch64_Q6 = 110,
  AArch64_Q7 = 111,
  AArch64_Q8 = 112,
  AArch64_Q9 = 113,
  AArch64_Q10 = 114,
  AArch64_Q11 = 115,
  AArch64_Q12 = 116,
  AArch64_Q13 = 117,
  AArch64_Q14 = 118,
  AArch64_Q15 = 119,
  AArch64_Q16 = 120,
  AArch64_Q17 = 121,
  AArch64_Q18 = 122,
  AArch64_Q19 = 123,
  AArch64_Q20 = 124,
  AArch64_Q21 = 125,
  AArch64_Q22 = 126,
  AArch64_Q23 = 127,
  AArch64_Q24 = 128,
  AArch64_Q25 = 129,
  AArch64_Q26 = 130,
  AArch64_Q27 = 131,
  AArch64_Q28 = 132,
  AArch64_Q29 = 133,
  AArch64_Q30 = 134,
  AArch64_Q31 = 135,
  AArch64_S0 = 136,
  AArch64_S1 = 137,
  AArch64_S2 = 138,
  AArch64_S3 = 139,
  AArch64_S4 = 140,
  AArch64_S5 = 141,
  AArch64_S6 = 142,
  AArch64_S7 = 143,
  AArch64_S8 = 144,
  AArch64_S9 = 145,
  AArch64_S10 = 146,
  AArch64_S11 = 147,
  AArch64_S12 = 148,
  AArch64_S13 = 149,
  AArch64_S14 = 150,
  AArch64_S15 = 151,
  AArch64_S16 = 152,
  AArch64_S17 = 153,
  AArch64_S18 = 154,
  AArch64_S19 = 155,
  AArch64_S20 = 156,
  AArch64_S21 = 157,
  AArch64_S22 = 158,
  AArch64_S23 = 159,
  AArch64_S24 = 160,
  AArch64_S25 = 161,
  AArch64_S26 = 162,
  AArch64_S27 = 163,
  AArch64_S28 = 164,
  AArch64_S29 = 165,
  AArch64_S30 = 166,
  AArch64_S31 = 167,
  AArch64_W0 = 168,
  AArch64_W1 = 169,
  AArch64_W2 = 170,
  AArch64_W3 = 171,
  AArch64_W4 = 172,
  AArch64_W5 = 173,
  AArch64_W6 = 174,
  AArch64_W7 = 175,
  AArch64_W8 = 176,
  AArch64_W9 = 177,
  AArch64_W10 = 178,
  AArch64_W11 = 179,
  AArch64_W12 = 180,
  AArch64_W13 = 181,
  AArch64_W14 = 182,
  AArch64_W15 = 183,
  AArch64_W16 = 184,
  AArch64_W17 = 185,
  AArch64_W18 = 186,
  AArch64_W19 = 187,
  AArch64_W20 = 188,
  AArch64_W21 = 189,
  AArch64_W22 = 190,
  AArch64_W23 = 191,
  AArch64_W24 = 192,
  AArch64_W25 = 193,
  AArch64_W26 = 194,
  AArch64_W27 = 195,
  AArch64_W28 = 196,
  AArch64_W29 = 197,
  AArch64_W30 = 198,
  AArch64_X0 = 199,
  AArch64_X1 = 200,
  AArch64_X2 = 201,
  AArch64_X3 = 202,
  AArch64_X4 = 203,
  AArch64_X5 = 204,
  AArch64_X6 = 205,
  AArch64_X7 = 206,
  AArch64_X8 = 207,
  AArch64_X9 = 208,
  AArch64_X10 = 209,
  AArch64_X11 = 210,
  AArch64_X12 = 211,
  AArch64_X13 = 212,
  AArch64_X14 = 213,
  AArch64_X15 = 214,
  AArch64_X16 = 215,
  AArch64_X17 = 216,
  AArch64_X18 = 217,
  AArch64_X19 = 218,
  AArch64_X20 = 219,
  AArch64_X21 = 220,
  AArch64_X22 = 221,
  AArch64_X23 = 222,
  AArch64_X24 = 223,
  AArch64_X25 = 224,
  AArch64_X26 = 225,
  AArch64_X27 = 226,
  AArch64_X28 = 227,
  AArch64_D0_D1 = 228,
  AArch64_D1_D2 = 229,
  AArch64_D2_D3 = 230,
  AArch64_D3_D4 = 231,
  AArch64_D4_D5 = 232,
  AArch64_D5_D6 = 233,
  AArch64_D6_D7 = 234,
  AArch64_D7_D8 = 235,
  AArch64_D8_D9 = 236,
  AArch64_D9_D10 = 237,
  AArch64_D10_D11 = 238,
  AArch64_D11_D12 = 239,
  AArch64_D12_D13 = 240,
  AArch64_D13_D14 = 241,
  AArch64_D14_D15 = 242,
  AArch64_D15_D16 = 243,
  AArch64_D16_D17 = 244,
  AArch64_D17_D18 = 245,
  AArch64_D18_D19 = 246,
  AArch64_D19_D20 = 247,
  AArch64_D20_D21 = 248,
  AArch64_D21_D22 = 249,
  AArch64_D22_D23 = 250,
  AArch64_D23_D24 = 251,
  AArch64_D24_D25 = 252,
  AArch64_D25_D26 = 253,
  AArch64_D26_D27 = 254,
  AArch64_D27_D28 = 255,
  AArch64_D28_D29 = 256,
  AArch64_D29_D30 = 257,
  AArch64_D30_D31 = 258,
  AArch64_D31_D0 = 259,
  AArch64_D0_D1_D2_D3 = 260,
  AArch64_D1_D2_D3_D4 = 261,
  AArch64_D2_D3_D4_D5 = 262,
  AArch64_D3_D4_D5_D6 = 263,
  AArch64_D4_D5_D6_D7 = 264,
  AArch64_D5_D6_D7_D8 = 265,
  AArch64_D6_D7_D8_D9 = 266,
  AArch64_D7_D8_D9_D10 = 267,
  AArch64_D8_D9_D10_D11 = 268,
  AArch64_D9_D10_D11_D12 = 269,
  AArch64_D10_D11_D12_D13 = 270,
  AArch64_D11_D12_D13_D14 = 271,
  AArch64_D12_D13_D14_D15 = 272,
  AArch64_D13_D14_D15_D16 = 273,
  AArch64_D14_D15_D16_D17 = 274,
  AArch64_D15_D16_D17_D18 = 275,
  AArch64_D16_D17_D18_D19 = 276,
  AArch64_D17_D18_D19_D20 = 277,
  AArch64_D18_D19_D20_D21 = 278,
  AArch64_D19_D20_D21_D22 = 279,
  AArch64_D20_D21_D22_D23 = 280,
  AArch64_D21_D22_D23_D24 = 281,
  AArch64_D22_D23_D24_D25 = 282,
  AArch64_D23_D24_D25_D26 = 283,
  AArch64_D24_D25_D26_D27 = 284,
  AArch64_D25_D26_D27_D28 = 285,
  AArch64_D26_D27_D28_D29 = 286,
  AArch64_D27_D28_D29_D30 = 287,
  AArch64_D28_D29_D30_D31 = 288,
  AArch64_D29_D30_D31_D0 = 289,
  AArch64_D30_D31_D0_D1 = 290,
  AArch64_D31_D0_D1_D2 = 291,
  AArch64_D0_D1_D2 = 292,
  AArch64_D1_D2_D3 = 293,
  AArch64_D2_D3_D4 = 294,
  AArch64_D3_D4_D5 = 295,
  AArch64_D4_D5_D6 = 296,
  AArch64_D5_D6_D7 = 297,
  AArch64_D6_D7_D8 = 298,
  AArch64_D7_D8_D9 = 299,
  AArch64_D8_D9_D10 = 300,
  AArch64_D9_D10_D11 = 301,
  AArch64_D10_D11_D12 = 302,
  AArch64_D11_D12_D13 = 303,
  AArch64_D12_D13_D14 = 304,
  AArch64_D13_D14_D15 = 305,
  AArch64_D14_D15_D16 = 306,
  AArch64_D15_D16_D17 = 307,
  AArch64_D16_D17_D18 = 308,
  AArch64_D17_D18_D19 = 309,
  AArch64_D18_D19_D20 = 310,
  AArch64_D19_D20_D21 = 311,
  AArch64_D20_D21_D22 = 312,
  AArch64_D21_D22_D23 = 313,
  AArch64_D22_D23_D24 = 314,
  AArch64_D23_D24_D25 = 315,
  AArch64_D24_D25_D26 = 316,
  AArch64_D25_D26_D27 = 317,
  AArch64_D26_D27_D28 = 318,
  AArch64_D27_D28_D29 = 319,
  AArch64_D28_D29_D30 = 320,
  AArch64_D29_D30_D31 = 321,
  AArch64_D30_D31_D0 = 322,
  AArch64_D31_D0_D1 = 323,
  AArch64_Q0_Q1 = 324,
  AArch64_Q1_Q2 = 325,
  AArch64_Q2_Q3 = 326,
  AArch64_Q3_Q4 = 327,
  AArch64_Q4_Q5 = 328,
  AArch64_Q5_Q6 = 329,
  AArch64_Q6_Q7 = 330,
  AArch64_Q7_Q8 = 331,
  AArch64_Q8_Q9 = 332,
  AArch64_Q9_Q10 = 333,
  AArch64_Q10_Q11 = 334,
  AArch64_Q11_Q12 = 335,
  AArch64_Q12_Q13 = 336,
  AArch64_Q13_Q14 = 337,
  AArch64_Q14_Q15 = 338,
  AArch64_Q15_Q16 = 339,
  AArch64_Q16_Q17 = 340,
  AArch64_Q17_Q18 = 341,
  AArch64_Q18_Q19 = 342,
  AArch64_Q19_Q20 = 343,
  AArch64_Q20_Q21 = 344,
  AArch64_Q21_Q22 = 345,
  AArch64_Q22_Q23 = 346,
  AArch64_Q23_Q24 = 347,
  AArch64_Q24_Q25 = 348,
  AArch64_Q25_Q26 = 349,
  AArch64_Q26_Q27 = 350,
  AArch64_Q27_Q28 = 351,
  AArch64_Q28_Q29 = 352,
  AArch64_Q29_Q30 = 353,
  AArch64_Q30_Q31 = 354,
  AArch64_Q31_Q0 = 355,
  AArch64_Q0_Q1_Q2_Q3 = 356,
  AArch64_Q1_Q2_Q3_Q4 = 357,
  AArch64_Q2_Q3_Q4_Q5 = 358,
  AArch64_Q3_Q4_Q5_Q6 = 359,
  AArch64_Q4_Q5_Q6_Q7 = 360,
  AArch64_Q5_Q6_Q7_Q8 = 361,
  AArch64_Q6_Q7_Q8_Q9 = 362,
  AArch64_Q7_Q8_Q9_Q10 = 363,
  AArch64_Q8_Q9_Q10_Q11 = 364,
  AArch64_Q9_Q10_Q11_Q12 = 365,
  AArch64_Q10_Q11_Q12_Q13 = 366,
  AArch64_Q11_Q12_Q13_Q14 = 367,
  AArch64_Q12_Q13_Q14_Q15 = 368,
  AArch64_Q13_Q14_Q15_Q16 = 369,
  AArch64_Q14_Q15_Q16_Q17 = 370,
  AArch64_Q15_Q16_Q17_Q18 = 371,
  AArch64_Q16_Q17_Q18_Q19 = 372,
  AArch64_Q17_Q18_Q19_Q20 = 373,
  AArch64_Q18_Q19_Q20_Q21 = 374,
  AArch64_Q19_Q20_Q21_Q22 = 375,
  AArch64_Q20_Q21_Q22_Q23 = 376,
  AArch64_Q21_Q22_Q23_Q24 = 377,
  AArch64_Q22_Q23_Q24_Q25 = 378,
  AArch64_Q23_Q24_Q25_Q26 = 379,
  AArch64_Q24_Q25_Q26_Q27 = 380,
  AArch64_Q25_Q26_Q27_Q28 = 381,
  AArch64_Q26_Q27_Q28_Q29 = 382,
  AArch64_Q27_Q28_Q29_Q30 = 383,
  AArch64_Q28_Q29_Q30_Q31 = 384,
  AArch64_Q29_Q30_Q31_Q0 = 385,
  AArch64_Q30_Q31_Q0_Q1 = 386,
  AArch64_Q31_Q0_Q1_Q2 = 387,
  AArch64_Q0_Q1_Q2 = 388,
  AArch64_Q1_Q2_Q3 = 389,
  AArch64_Q2_Q3_Q4 = 390,
  AArch64_Q3_Q4_Q5 = 391,
  AArch64_Q4_Q5_Q6 = 392,
  AArch64_Q5_Q6_Q7 = 393,
  AArch64_Q6_Q7_Q8 = 394,
  AArch64_Q7_Q8_Q9 = 395,
  AArch64_Q8_Q9_Q10 = 396,
  AArch64_Q9_Q10_Q11 = 397,
  AArch64_Q10_Q11_Q12 = 398,
  AArch64_Q11_Q12_Q13 = 399,
  AArch64_Q12_Q13_Q14 = 400,
  AArch64_Q13_Q14_Q15 = 401,
  AArch64_Q14_Q15_Q16 = 402,
  AArch64_Q15_Q16_Q17 = 403,
  AArch64_Q16_Q17_Q18 = 404,
  AArch64_Q17_Q18_Q19 = 405,
  AArch64_Q18_Q19_Q20 = 406,
  AArch64_Q19_Q20_Q21 = 407,
  AArch64_Q20_Q21_Q22 = 408,
  AArch64_Q21_Q22_Q23 = 409,
  AArch64_Q22_Q23_Q24 = 410,
  AArch64_Q23_Q24_Q25 = 411,
  AArch64_Q24_Q25_Q26 = 412,
  AArch64_Q25_Q26_Q27 = 413,
  AArch64_Q26_Q27_Q28 = 414,
  AArch64_Q27_Q28_Q29 = 415,
  AArch64_Q28_Q29_Q30 = 416,
  AArch64_Q29_Q30_Q31 = 417,
  AArch64_Q30_Q31_Q0 = 418,
  AArch64_Q31_Q0_Q1 = 419,
  AArch64_NUM_TARGET_REGS 	// 420
};

// Register classes
enum {
  AArch64_FPR8RegClassID = 0,
  AArch64_FPR16RegClassID = 1,
  AArch64_GPR32allRegClassID = 2,
  AArch64_FPR32RegClassID = 3,
  AArch64_GPR32RegClassID = 4,
  AArch64_GPR32spRegClassID = 5,
  AArch64_GPR32commonRegClassID = 6,
  AArch64_CCRRegClassID = 7,
  AArch64_GPR32sponlyRegClassID = 8,
  AArch64_GPR64allRegClassID = 9,
  AArch64_FPR64RegClassID = 10,
  AArch64_GPR64RegClassID = 11,
  AArch64_GPR64spRegClassID = 12,
  AArch64_GPR64commonRegClassID = 13,
  AArch64_tcGPR64RegClassID = 14,
  AArch64_GPR64sponlyRegClassID = 15,
  AArch64_DDRegClassID = 16,
  AArch64_FPR128RegClassID = 17,
  AArch64_FPR128_loRegClassID = 18,
  AArch64_DDDRegClassID = 19,
  AArch64_DDDDRegClassID = 20,
  AArch64_QQRegClassID = 21,
  AArch64_QQ_with_qsub0_in_FPR128_loRegClassID = 22,
  AArch64_QQ_with_qsub1_in_FPR128_loRegClassID = 23,
  AArch64_QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_loRegClassID = 24,
  AArch64_QQQRegClassID = 25,
  AArch64_QQQ_with_qsub0_in_FPR128_loRegClassID = 26,
  AArch64_QQQ_with_qsub1_in_FPR128_loRegClassID = 27,
  AArch64_QQQ_with_qsub2_in_FPR128_loRegClassID = 28,
  AArch64_QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_loRegClassID = 29,
  AArch64_QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loRegClassID = 30,
  AArch64_QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loRegClassID = 31,
  AArch64_QQQQRegClassID = 32,
  AArch64_QQQQ_with_qsub0_in_FPR128_loRegClassID = 33,
  AArch64_QQQQ_with_qsub1_in_FPR128_loRegClassID = 34,
  AArch64_QQQQ_with_qsub2_in_FPR128_loRegClassID = 35,
  AArch64_QQQQ_with_qsub3_in_FPR128_loRegClassID = 36,
  AArch64_QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_loRegClassID = 37,
  AArch64_QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loRegClassID = 38,
  AArch64_QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loRegClassID = 39,
  AArch64_QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loRegClassID = 40,
  AArch64_QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loRegClassID = 41,
  AArch64_QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loRegClassID = 42
};

// Register alternate name indices
enum {
	AArch64_NoRegAltName, // 0
	AArch64_vlist1,       // 1
	AArch64_vreg, // 2
	AArch64_NUM_TARGET_REG_ALT_NAMES = 3
};

// Subregister indices
enum {
  AArch64_NoSubRegister,
  AArch64_bsub,	// 1
  AArch64_dsub,	// 2
  AArch64_dsub0,	// 3
  AArch64_dsub1,	// 4
  AArch64_dsub2,	// 5
  AArch64_dsub3,	// 6
  AArch64_hsub,	// 7
  AArch64_qhisub,	// 8
  AArch64_qsub,	// 9
  AArch64_qsub0,	// 10
  AArch64_qsub1,	// 11
  AArch64_qsub2,	// 12
  AArch64_qsub3,	// 13
  AArch64_ssub,	// 14
  AArch64_sub_32,	// 15
  AArch64_dsub1_then_bsub,	// 16
  AArch64_dsub1_then_hsub,	// 17
  AArch64_dsub1_then_ssub,	// 18
  AArch64_dsub3_then_bsub,	// 19
  AArch64_dsub3_then_hsub,	// 20
  AArch64_dsub3_then_ssub,	// 21
  AArch64_dsub2_then_bsub,	// 22
  AArch64_dsub2_then_hsub,	// 23
  AArch64_dsub2_then_ssub,	// 24
  AArch64_qsub1_then_bsub,	// 25
  AArch64_qsub1_then_dsub,	// 26
  AArch64_qsub1_then_hsub,	// 27
  AArch64_qsub1_then_ssub,	// 28
  AArch64_qsub3_then_bsub,	// 29
  AArch64_qsub3_then_dsub,	// 30
  AArch64_qsub3_then_hsub,	// 31
  AArch64_qsub3_then_ssub,	// 32
  AArch64_qsub2_then_bsub,	// 33
  AArch64_qsub2_then_dsub,	// 34
  AArch64_qsub2_then_hsub,	// 35
  AArch64_qsub2_then_ssub,	// 36
  AArch64_dsub0_dsub1,	// 37
  AArch64_dsub0_dsub1_dsub2,	// 38
  AArch64_dsub1_dsub2,	// 39
  AArch64_dsub1_dsub2_dsub3,	// 40
  AArch64_dsub2_dsub3,	// 41
  AArch64_dsub_qsub1_then_dsub,	// 42
  AArch64_dsub_qsub1_then_dsub_qsub2_then_dsub_qsub3_then_dsub,	// 43
  AArch64_dsub_qsub1_then_dsub_qsub2_then_dsub,	// 44
  AArch64_qsub0_qsub1,	// 45
  AArch64_qsub0_qsub1_qsub2,	// 46
  AArch64_qsub1_qsub2,	// 47
  AArch64_qsub1_qsub2_qsub3,	// 48
  AArch64_qsub2_qsub3,	// 49
  AArch64_qsub1_then_dsub_qsub2_then_dsub,	// 50
  AArch64_qsub1_then_dsub_qsub2_then_dsub_qsub3_then_dsub,	// 51
  AArch64_qsub2_then_dsub_qsub3_then_dsub,	// 52
  AArch64_NUM_TARGET_SUBREGS
};

#endif // GET_REGINFO_ENUM

/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*MC Register Information                                                     *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By Nguyen Anh Quynh <<EMAIL>>, 2013-2014 */


#ifdef GET_REGINFO_MC_DESC
#undef GET_REGINFO_MC_DESC

static MCPhysReg AArch64RegDiffLists[] = {
  /* 0 */ 65185, 1, 1, 1, 0,
  /* 5 */ 65281, 1, 1, 1, 0,
  /* 10 */ 5, 29, 1, 1, 0,
  /* 15 */ 65153, 1, 1, 0,
  /* 19 */ 65249, 1, 1, 0,
  /* 23 */ 5, 1, 29, 1, 0,
  /* 28 */ 5, 30, 1, 0,
  /* 32 */ 65284, 96, 65472, 65472, 33, 96, 65472, 65472, 33, 96, 65472, 65472, 218, 1, 0,
  /* 47 */ 65284, 96, 65472, 65472, 33, 96, 65472, 65472, 1, 96, 65472, 65472, 250, 1, 0,
  /* 62 */ 65217, 1, 0,
  /* 65 */ 65313, 1, 0,
  /* 68 */ 64, 64, 65440, 64, 123, 1, 62, 65503, 34, 65503, 34, 65503, 1, 63, 1, 62, 65503, 34, 65503, 34, 65503, 1, 0,
  /* 91 */ 219, 1, 62, 65503, 34, 65503, 34, 65503, 1, 0,
  /* 101 */ 64, 64, 65440, 64, 124, 31, 33, 65504, 62, 65503, 34, 65503, 1, 33, 31, 33, 65504, 62, 65503, 34, 65503, 1, 0,
  /* 124 */ 220, 31, 33, 65504, 62, 65503, 34, 65503, 1, 0,
  /* 134 */ 63, 65503, 34, 65503, 1, 64, 63, 65503, 34, 65503, 1, 0,
  /* 146 */ 64, 64, 65440, 64, 123, 1, 63, 1, 65503, 1, 62, 65503, 1, 33, 1, 63, 1, 65503, 1, 62, 65503, 1, 0,
  /* 169 */ 219, 1, 63, 1, 65503, 1, 62, 65503, 1, 0,
  /* 179 */ 64, 65504, 63, 65503, 1, 33, 64, 65504, 63, 65503, 1, 0,
  /* 191 */ 65503, 1, 128, 65503, 1, 0,
  /* 197 */ 3, 0,
  /* 199 */ 4, 0,
  /* 201 */ 5, 1, 1, 29, 0,
  /* 206 */ 64, 64, 65440, 64, 123, 1, 62, 1, 65503, 34, 65503, 1, 29, 34, 1, 62, 1, 65503, 34, 65503, 1, 29, 0,
  /* 229 */ 219, 1, 62, 1, 65503, 34, 65503, 1, 29, 0,
  /* 239 */ 5, 1, 30, 0,
  /* 243 */ 63, 1, 65503, 1, 30, 34, 63, 1, 65503, 1, 30, 0,
  /* 255 */ 5, 31, 0,
  /* 258 */ 65504, 31, 97, 65504, 31, 0,
  /* 264 */ 96, 0,
  /* 266 */ 196, 0,
  /* 268 */ 65316, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 219, 0,
  /* 280 */ 65316, 65472, 96, 65472, 65472, 65, 65472, 96, 65472, 65472, 251, 0,
  /* 292 */ 65339, 0,
  /* 294 */ 65340, 0,
  /* 296 */ 65374, 0,
  /* 298 */ 65405, 0,
  /* 300 */ 65437, 0,
  /* 302 */ 65252, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 218, 64, 32, 1, 65440, 0,
  /* 323 */ 65252, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 65, 65472, 96, 65472, 65472, 250, 64, 32, 1, 65440, 0,
  /* 344 */ 65252, 65472, 96, 65472, 65472, 65, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 250, 64, 32, 65505, 65440, 0,
  /* 365 */ 65284, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 65, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 249, 32, 32, 32, 64, 65473, 64, 65441, 65471, 64, 65441, 0,
  /* 397 */ 65316, 96, 65472, 65472, 33, 96, 65472, 65472, 1, 96, 65472, 65472, 33, 96, 65472, 65472, 249, 64, 65473, 64, 65441, 0,
  /* 419 */ 65469, 0,
  /* 421 */ 65348, 96, 65472, 65472, 1, 96, 65472, 65472, 0,
  /* 430 */ 65348, 96, 65472, 65472, 33, 96, 65472, 65472, 0,
  /* 439 */ 65472, 96, 65472, 65472, 0,
  /* 444 */ 65284, 65472, 96, 65472, 65472, 65, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 249, 32, 32, 32, 64, 65441, 64, 65473, 65439, 64, 65473, 0,
  /* 476 */ 65284, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 217, 32, 32, 32, 64, 65473, 64, 65473, 65439, 64, 65473, 0,
  /* 508 */ 65284, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 97, 65472, 96, 65472, 65472, 65, 65472, 96, 65472, 65472, 249, 32, 32, 32, 64, 65473, 64, 65473, 65439, 64, 65473, 0,
  /* 540 */ 65316, 96, 65472, 65472, 1, 96, 65472, 65472, 33, 96, 65472, 65472, 33, 96, 65472, 65472, 249, 64, 65441, 64, 65473, 0,
  /* 562 */ 65316, 96, 65472, 65472, 33, 96, 65472, 65472, 33, 96, 65472, 65472, 33, 96, 65472, 65472, 217, 64, 65473, 64, 65473, 0,
  /* 584 */ 65316, 96, 65472, 65472, 33, 96, 65472, 65472, 33, 96, 65472, 65472, 1, 96, 65472, 65472, 249, 64, 65473, 64, 65473, 0,
  /* 606 */ 65501, 0,
  /* 608 */ 65284, 96, 65472, 65472, 1, 96, 65472, 65472, 33, 96, 65472, 65472, 250, 65505, 0,
  /* 623 */ 65533, 0,
  /* 625 */ 65535, 0,
};

static uint16_t AArch64SubRegIdxLists[] = {
  /* 0 */ 2, 14, 7, 1, 0,
  /* 5 */ 15, 0,
  /* 7 */ 3, 14, 7, 1, 4, 18, 17, 16, 0,
  /* 16 */ 3, 14, 7, 1, 4, 18, 17, 16, 5, 24, 23, 22, 37, 39, 0,
  /* 31 */ 3, 14, 7, 1, 4, 18, 17, 16, 5, 24, 23, 22, 6, 21, 20, 19, 37, 38, 39, 40, 41, 0,
  /* 53 */ 10, 2, 14, 7, 1, 11, 26, 28, 27, 25, 42, 0,
  /* 65 */ 10, 2, 14, 7, 1, 11, 26, 28, 27, 25, 12, 34, 36, 35, 33, 42, 44, 45, 47, 50, 0,
  /* 86 */ 10, 2, 14, 7, 1, 11, 26, 28, 27, 25, 12, 34, 36, 35, 33, 13, 30, 32, 31, 29, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 0,
};

static MCRegisterDesc AArch64RegDesc[] = { // Descriptors
  { 3, 0, 0, 0, 0 },
  { 1518, 266, 4, 5, 10001 },
  { 1525, 266, 4, 5, 10001 },
  { 1536, 4, 4, 4, 10001 },
  { 1522, 3, 4, 5, 3152 },
  { 1521, 4, 625, 4, 3152 },
  { 1528, 4, 3, 4, 3184 },
  { 1532, 625, 4, 5, 3184 },
  { 146, 4, 101, 4, 9969 },
  { 335, 4, 146, 4, 9969 },
  { 480, 4, 206, 4, 9969 },
  { 625, 4, 68, 4, 9969 },
  { 768, 4, 68, 4, 9969 },
  { 911, 4, 68, 4, 9969 },
  { 1054, 4, 68, 4, 9969 },
  { 1197, 4, 68, 4, 9969 },
  { 1340, 4, 68, 4, 9969 },
  { 1479, 4, 68, 4, 9969 },
  { 0, 4, 68, 4, 9969 },
  { 191, 4, 68, 4, 9969 },
  { 378, 4, 68, 4, 9969 },
  { 521, 4, 68, 4, 9969 },
  { 664, 4, 68, 4, 9969 },
  { 807, 4, 68, 4, 9969 },
  { 950, 4, 68, 4, 9969 },
  { 1093, 4, 68, 4, 9969 },
  { 1236, 4, 68, 4, 9969 },
  { 1379, 4, 68, 4, 9969 },
  { 46, 4, 68, 4, 9969 },
  { 239, 4, 68, 4, 9969 },
  { 428, 4, 68, 4, 9969 },
  { 573, 4, 68, 4, 9969 },
  { 716, 4, 68, 4, 9969 },
  { 859, 4, 68, 4, 9969 },
  { 1002, 4, 68, 4, 9969 },
  { 1145, 4, 68, 4, 9969 },
  { 1288, 4, 68, 4, 9969 },
  { 1431, 4, 68, 4, 9969 },
  { 98, 4, 68, 4, 9969 },
  { 291, 4, 68, 4, 9969 },
  { 161, 426, 104, 1, 9697 },
  { 349, 426, 149, 1, 9697 },
  { 493, 426, 209, 1, 9697 },
  { 637, 426, 71, 1, 9697 },
  { 780, 426, 71, 1, 9697 },
  { 923, 426, 71, 1, 9697 },
  { 1066, 426, 71, 1, 9697 },
  { 1209, 426, 71, 1, 9697 },
  { 1352, 426, 71, 1, 9697 },
  { 1491, 426, 71, 1, 9697 },
  { 13, 426, 71, 1, 9697 },
  { 205, 426, 71, 1, 9697 },
  { 393, 426, 71, 1, 9697 },
  { 537, 426, 71, 1, 9697 },
  { 680, 426, 71, 1, 9697 },
  { 823, 426, 71, 1, 9697 },
  { 966, 426, 71, 1, 9697 },
  { 1109, 426, 71, 1, 9697 },
  { 1252, 426, 71, 1, 9697 },
  { 1395, 426, 71, 1, 9697 },
  { 62, 426, 71, 1, 9697 },
  { 255, 426, 71, 1, 9697 },
  { 444, 426, 71, 1, 9697 },
  { 589, 426, 71, 1, 9697 },
  { 732, 426, 71, 1, 9697 },
  { 875, 426, 71, 1, 9697 },
  { 1018, 426, 71, 1, 9697 },
  { 1161, 426, 71, 1, 9697 },
  { 1304, 426, 71, 1, 9697 },
  { 1447, 426, 71, 1, 9697 },
  { 114, 426, 71, 1, 9697 },
  { 307, 426, 71, 1, 9697 },
  { 164, 428, 102, 3, 6705 },
  { 352, 428, 147, 3, 6705 },
  { 496, 428, 207, 3, 6705 },
  { 640, 428, 69, 3, 6705 },
  { 783, 428, 69, 3, 6705 },
  { 926, 428, 69, 3, 6705 },
  { 1069, 428, 69, 3, 6705 },
  { 1212, 428, 69, 3, 6705 },
  { 1355, 428, 69, 3, 6705 },
  { 1494, 428, 69, 3, 6705 },
  { 17, 428, 69, 3, 6705 },
  { 209, 428, 69, 3, 6705 },
  { 397, 428, 69, 3, 6705 },
  { 541, 428, 69, 3, 6705 },
  { 684, 428, 69, 3, 6705 },
  { 827, 428, 69, 3, 6705 },
  { 970, 428, 69, 3, 6705 },
  { 1113, 428, 69, 3, 6705 },
  { 1256, 428, 69, 3, 6705 },
  { 1399, 428, 69, 3, 6705 },
  { 66, 428, 69, 3, 6705 },
  { 259, 428, 69, 3, 6705 },
  { 448, 428, 69, 3, 6705 },
  { 593, 428, 69, 3, 6705 },
  { 736, 428, 69, 3, 6705 },
  { 879, 428, 69, 3, 6705 },
  { 1022, 428, 69, 3, 6705 },
  { 1165, 428, 69, 3, 6705 },
  { 1308, 428, 69, 3, 6705 },
  { 1451, 428, 69, 3, 6705 },
  { 118, 428, 69, 3, 6705 },
  { 311, 428, 69, 3, 6705 },
  { 179, 439, 124, 0, 4801 },
  { 366, 439, 169, 0, 4801 },
  { 509, 439, 229, 0, 4801 },
  { 652, 439, 91, 0, 4801 },
  { 795, 439, 91, 0, 4801 },
  { 938, 439, 91, 0, 4801 },
  { 1081, 439, 91, 0, 4801 },
  { 1224, 439, 91, 0, 4801 },
  { 1367, 439, 91, 0, 4801 },
  { 1506, 439, 91, 0, 4801 },
  { 30, 439, 91, 0, 4801 },
  { 223, 439, 91, 0, 4801 },
  { 412, 439, 91, 0, 4801 },
  { 557, 439, 91, 0, 4801 },
  { 700, 439, 91, 0, 4801 },
  { 843, 439, 91, 0, 4801 },
  { 986, 439, 91, 0, 4801 },
  { 1129, 439, 91, 0, 4801 },
  { 1272, 439, 91, 0, 4801 },
  { 1415, 439, 91, 0, 4801 },
  { 82, 439, 91, 0, 4801 },
  { 275, 439, 91, 0, 4801 },
  { 464, 439, 91, 0, 4801 },
  { 609, 439, 91, 0, 4801 },
  { 752, 439, 91, 0, 4801 },
  { 895, 439, 91, 0, 4801 },
  { 1038, 439, 91, 0, 4801 },
  { 1181, 439, 91, 0, 4801 },
  { 1324, 439, 91, 0, 4801 },
  { 1467, 439, 91, 0, 4801 },
  { 134, 439, 91, 0, 4801 },
  { 327, 439, 91, 0, 4801 },
  { 182, 427, 103, 2, 4769 },
  { 369, 427, 148, 2, 4769 },
  { 512, 427, 208, 2, 4769 },
  { 655, 427, 70, 2, 4769 },
  { 798, 427, 70, 2, 4769 },
  { 941, 427, 70, 2, 4769 },
  { 1084, 427, 70, 2, 4769 },
  { 1227, 427, 70, 2, 4769 },
  { 1370, 427, 70, 2, 4769 },
  { 1509, 427, 70, 2, 4769 },
  { 34, 427, 70, 2, 4769 },
  { 227, 427, 70, 2, 4769 },
  { 416, 427, 70, 2, 4769 },
  { 561, 427, 70, 2, 4769 },
  { 704, 427, 70, 2, 4769 },
  { 847, 427, 70, 2, 4769 },
  { 990, 427, 70, 2, 4769 },
  { 1133, 427, 70, 2, 4769 },
  { 1276, 427, 70, 2, 4769 },
  { 1419, 427, 70, 2, 4769 },
  { 86, 427, 70, 2, 4769 },
  { 279, 427, 70, 2, 4769 },
  { 468, 427, 70, 2, 4769 },
  { 613, 427, 70, 2, 4769 },
  { 756, 427, 70, 2, 4769 },
  { 899, 427, 70, 2, 4769 },
  { 1042, 427, 70, 2, 4769 },
  { 1185, 427, 70, 2, 4769 },
  { 1328, 427, 70, 2, 4769 },
  { 1471, 427, 70, 2, 4769 },
  { 138, 427, 70, 2, 4769 },
  { 331, 427, 70, 2, 4769 },
  { 185, 4, 256, 4, 4769 },
  { 372, 4, 256, 4, 4769 },
  { 515, 4, 256, 4, 4769 },
  { 658, 4, 256, 4, 4769 },
  { 801, 4, 256, 4, 4769 },
  { 944, 4, 256, 4, 4769 },
  { 1087, 4, 256, 4, 4769 },
  { 1230, 4, 256, 4, 4769 },
  { 1373, 4, 256, 4, 4769 },
  { 1512, 4, 256, 4, 4769 },
  { 38, 4, 256, 4, 4769 },
  { 231, 4, 256, 4, 4769 },
  { 420, 4, 256, 4, 4769 },
  { 565, 4, 256, 4, 4769 },
  { 708, 4, 256, 4, 4769 },
  { 851, 4, 256, 4, 4769 },
  { 994, 4, 256, 4, 4769 },
  { 1137, 4, 256, 4, 4769 },
  { 1280, 4, 256, 4, 4769 },
  { 1423, 4, 256, 4, 4769 },
  { 90, 4, 256, 4, 4769 },
  { 283, 4, 256, 4, 4769 },
  { 472, 4, 256, 4, 4769 },
  { 617, 4, 256, 4, 4769 },
  { 760, 4, 256, 4, 4769 },
  { 903, 4, 256, 4, 4769 },
  { 1046, 4, 256, 4, 4769 },
  { 1189, 4, 256, 4, 4769 },
  { 1332, 4, 256, 4, 4769 },
  { 1475, 4, 294, 4, 4673 },
  { 142, 4, 294, 4, 4673 },
  { 188, 621, 4, 5, 4737 },
  { 375, 621, 4, 5, 4737 },
  { 518, 621, 4, 5, 4737 },
  { 661, 621, 4, 5, 4737 },
  { 804, 621, 4, 5, 4737 },
  { 947, 621, 4, 5, 4737 },
  { 1090, 621, 4, 5, 4737 },
  { 1233, 621, 4, 5, 4737 },
  { 1376, 621, 4, 5, 4737 },
  { 1515, 621, 4, 5, 4737 },
  { 42, 621, 4, 5, 4737 },
  { 235, 621, 4, 5, 4737 },
  { 424, 621, 4, 5, 4737 },
  { 569, 621, 4, 5, 4737 },
  { 712, 621, 4, 5, 4737 },
  { 855, 621, 4, 5, 4737 },
  { 998, 621, 4, 5, 4737 },
  { 1141, 621, 4, 5, 4737 },
  { 1284, 621, 4, 5, 4737 },
  { 1427, 621, 4, 5, 4737 },
  { 94, 621, 4, 5, 4737 },
  { 287, 621, 4, 5, 4737 },
  { 476, 621, 4, 5, 4737 },
  { 621, 621, 4, 5, 4737 },
  { 764, 621, 4, 5, 4737 },
  { 907, 621, 4, 5, 4737 },
  { 1050, 621, 4, 5, 4737 },
  { 1193, 621, 4, 5, 4737 },
  { 1336, 621, 4, 5, 4737 },
  { 346, 430, 179, 7, 1041 },
  { 490, 430, 243, 7, 1041 },
  { 634, 430, 134, 7, 1041 },
  { 777, 430, 134, 7, 1041 },
  { 920, 430, 134, 7, 1041 },
  { 1063, 430, 134, 7, 1041 },
  { 1206, 430, 134, 7, 1041 },
  { 1349, 430, 134, 7, 1041 },
  { 1488, 430, 134, 7, 1041 },
  { 10, 430, 134, 7, 1041 },
  { 201, 430, 134, 7, 1041 },
  { 389, 430, 134, 7, 1041 },
  { 533, 430, 134, 7, 1041 },
  { 676, 430, 134, 7, 1041 },
  { 819, 430, 134, 7, 1041 },
  { 962, 430, 134, 7, 1041 },
  { 1105, 430, 134, 7, 1041 },
  { 1248, 430, 134, 7, 1041 },
  { 1391, 430, 134, 7, 1041 },
  { 58, 430, 134, 7, 1041 },
  { 251, 430, 134, 7, 1041 },
  { 440, 430, 134, 7, 1041 },
  { 585, 430, 134, 7, 1041 },
  { 728, 430, 134, 7, 1041 },
  { 871, 430, 134, 7, 1041 },
  { 1014, 430, 134, 7, 1041 },
  { 1157, 430, 134, 7, 1041 },
  { 1300, 430, 134, 7, 1041 },
  { 1443, 430, 134, 7, 1041 },
  { 110, 430, 134, 7, 1041 },
  { 303, 430, 134, 7, 1041 },
  { 157, 421, 134, 7, 4080 },
  { 628, 562, 264, 31, 81 },
  { 771, 562, 264, 31, 81 },
  { 914, 562, 264, 31, 81 },
  { 1057, 562, 264, 31, 81 },
  { 1200, 562, 264, 31, 81 },
  { 1343, 562, 264, 31, 81 },
  { 1482, 562, 264, 31, 81 },
  { 4, 562, 264, 31, 81 },
  { 195, 562, 264, 31, 81 },
  { 382, 562, 264, 31, 81 },
  { 525, 562, 264, 31, 81 },
  { 668, 562, 264, 31, 81 },
  { 811, 562, 264, 31, 81 },
  { 954, 562, 264, 31, 81 },
  { 1097, 562, 264, 31, 81 },
  { 1240, 562, 264, 31, 81 },
  { 1383, 562, 264, 31, 81 },
  { 50, 562, 264, 31, 81 },
  { 243, 562, 264, 31, 81 },
  { 432, 562, 264, 31, 81 },
  { 577, 562, 264, 31, 81 },
  { 720, 562, 264, 31, 81 },
  { 863, 562, 264, 31, 81 },
  { 1006, 562, 264, 31, 81 },
  { 1149, 562, 264, 31, 81 },
  { 1292, 562, 264, 31, 81 },
  { 1435, 562, 264, 31, 81 },
  { 102, 562, 264, 31, 81 },
  { 295, 562, 264, 31, 81 },
  { 149, 584, 264, 31, 160 },
  { 338, 397, 264, 31, 368 },
  { 483, 540, 264, 31, 3216 },
  { 487, 32, 258, 16, 305 },
  { 631, 32, 191, 16, 305 },
  { 774, 32, 191, 16, 305 },
  { 917, 32, 191, 16, 305 },
  { 1060, 32, 191, 16, 305 },
  { 1203, 32, 191, 16, 305 },
  { 1346, 32, 191, 16, 305 },
  { 1485, 32, 191, 16, 305 },
  { 7, 32, 191, 16, 305 },
  { 198, 32, 191, 16, 305 },
  { 385, 32, 191, 16, 305 },
  { 529, 32, 191, 16, 305 },
  { 672, 32, 191, 16, 305 },
  { 815, 32, 191, 16, 305 },
  { 958, 32, 191, 16, 305 },
  { 1101, 32, 191, 16, 305 },
  { 1244, 32, 191, 16, 305 },
  { 1387, 32, 191, 16, 305 },
  { 54, 32, 191, 16, 305 },
  { 247, 32, 191, 16, 305 },
  { 436, 32, 191, 16, 305 },
  { 581, 32, 191, 16, 305 },
  { 724, 32, 191, 16, 305 },
  { 867, 32, 191, 16, 305 },
  { 1010, 32, 191, 16, 305 },
  { 1153, 32, 191, 16, 305 },
  { 1296, 32, 191, 16, 305 },
  { 1439, 32, 191, 16, 305 },
  { 106, 32, 191, 16, 305 },
  { 299, 32, 191, 16, 305 },
  { 153, 47, 191, 16, 448 },
  { 342, 608, 191, 16, 3824 },
  { 363, 268, 185, 53, 993 },
  { 506, 268, 249, 53, 993 },
  { 649, 268, 140, 53, 993 },
  { 792, 268, 140, 53, 993 },
  { 935, 268, 140, 53, 993 },
  { 1078, 268, 140, 53, 993 },
  { 1221, 268, 140, 53, 993 },
  { 1364, 268, 140, 53, 993 },
  { 1503, 268, 140, 53, 993 },
  { 27, 268, 140, 53, 993 },
  { 219, 268, 140, 53, 993 },
  { 408, 268, 140, 53, 993 },
  { 553, 268, 140, 53, 993 },
  { 696, 268, 140, 53, 993 },
  { 839, 268, 140, 53, 993 },
  { 982, 268, 140, 53, 993 },
  { 1125, 268, 140, 53, 993 },
  { 1268, 268, 140, 53, 993 },
  { 1411, 268, 140, 53, 993 },
  { 78, 268, 140, 53, 993 },
  { 271, 268, 140, 53, 993 },
  { 460, 268, 140, 53, 993 },
  { 605, 268, 140, 53, 993 },
  { 748, 268, 140, 53, 993 },
  { 891, 268, 140, 53, 993 },
  { 1034, 268, 140, 53, 993 },
  { 1177, 268, 140, 53, 993 },
  { 1320, 268, 140, 53, 993 },
  { 1463, 268, 140, 53, 993 },
  { 130, 268, 140, 53, 993 },
  { 323, 268, 140, 53, 993 },
  { 175, 280, 140, 53, 4080 },
  { 643, 476, 4, 86, 1 },
  { 786, 476, 4, 86, 1 },
  { 929, 476, 4, 86, 1 },
  { 1072, 476, 4, 86, 1 },
  { 1215, 476, 4, 86, 1 },
  { 1358, 476, 4, 86, 1 },
  { 1497, 476, 4, 86, 1 },
  { 21, 476, 4, 86, 1 },
  { 213, 476, 4, 86, 1 },
  { 401, 476, 4, 86, 1 },
  { 545, 476, 4, 86, 1 },
  { 688, 476, 4, 86, 1 },
  { 831, 476, 4, 86, 1 },
  { 974, 476, 4, 86, 1 },
  { 1117, 476, 4, 86, 1 },
  { 1260, 476, 4, 86, 1 },
  { 1403, 476, 4, 86, 1 },
  { 70, 476, 4, 86, 1 },
  { 263, 476, 4, 86, 1 },
  { 452, 476, 4, 86, 1 },
  { 597, 476, 4, 86, 1 },
  { 740, 476, 4, 86, 1 },
  { 883, 476, 4, 86, 1 },
  { 1026, 476, 4, 86, 1 },
  { 1169, 476, 4, 86, 1 },
  { 1312, 476, 4, 86, 1 },
  { 1455, 476, 4, 86, 1 },
  { 122, 476, 4, 86, 1 },
  { 315, 476, 4, 86, 1 },
  { 167, 508, 4, 86, 160 },
  { 355, 365, 4, 86, 368 },
  { 499, 444, 4, 86, 3216 },
  { 503, 302, 261, 65, 241 },
  { 646, 302, 88, 65, 241 },
  { 789, 302, 88, 65, 241 },
  { 932, 302, 88, 65, 241 },
  { 1075, 302, 88, 65, 241 },
  { 1218, 302, 88, 65, 241 },
  { 1361, 302, 88, 65, 241 },
  { 1500, 302, 88, 65, 241 },
  { 24, 302, 88, 65, 241 },
  { 216, 302, 88, 65, 241 },
  { 404, 302, 88, 65, 241 },
  { 549, 302, 88, 65, 241 },
  { 692, 302, 88, 65, 241 },
  { 835, 302, 88, 65, 241 },
  { 978, 302, 88, 65, 241 },
  { 1121, 302, 88, 65, 241 },
  { 1264, 302, 88, 65, 241 },
  { 1407, 302, 88, 65, 241 },
  { 74, 302, 88, 65, 241 },
  { 267, 302, 88, 65, 241 },
  { 456, 302, 88, 65, 241 },
  { 601, 302, 88, 65, 241 },
  { 744, 302, 88, 65, 241 },
  { 887, 302, 88, 65, 241 },
  { 1030, 302, 88, 65, 241 },
  { 1173, 302, 88, 65, 241 },
  { 1316, 302, 88, 65, 241 },
  { 1459, 302, 88, 65, 241 },
  { 126, 302, 88, 65, 241 },
  { 319, 302, 88, 65, 241 },
  { 171, 323, 88, 65, 448 },
  { 359, 344, 88, 65, 3824 },
};

  // FPR8 Register Class...
  static MCPhysReg FPR8[] = {
    AArch64_B0, AArch64_B1, AArch64_B2, AArch64_B3, AArch64_B4, AArch64_B5, AArch64_B6, AArch64_B7, AArch64_B8, AArch64_B9, AArch64_B10, AArch64_B11, AArch64_B12, AArch64_B13, AArch64_B14, AArch64_B15, AArch64_B16, AArch64_B17, AArch64_B18, AArch64_B19, AArch64_B20, AArch64_B21, AArch64_B22, AArch64_B23, AArch64_B24, AArch64_B25, AArch64_B26, AArch64_B27, AArch64_B28, AArch64_B29, AArch64_B30, AArch64_B31, 
  };

  // FPR8 Bit set.
  static uint8_t FPR8Bits[] = {
    0x00, 0xff, 0xff, 0xff, 0xff, 
  };

  // FPR16 Register Class...
  static MCPhysReg FPR16[] = {
    AArch64_H0, AArch64_H1, AArch64_H2, AArch64_H3, AArch64_H4, AArch64_H5, AArch64_H6, AArch64_H7, AArch64_H8, AArch64_H9, AArch64_H10, AArch64_H11, AArch64_H12, AArch64_H13, AArch64_H14, AArch64_H15, AArch64_H16, AArch64_H17, AArch64_H18, AArch64_H19, AArch64_H20, AArch64_H21, AArch64_H22, AArch64_H23, AArch64_H24, AArch64_H25, AArch64_H26, AArch64_H27, AArch64_H28, AArch64_H29, AArch64_H30, AArch64_H31, 
  };

  // FPR16 Bit set.
  static uint8_t FPR16Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 
  };

  // GPR32all Register Class...
  static MCPhysReg GPR32all[] = {
    AArch64_W0, AArch64_W1, AArch64_W2, AArch64_W3, AArch64_W4, AArch64_W5, AArch64_W6, AArch64_W7, AArch64_W8, AArch64_W9, AArch64_W10, AArch64_W11, AArch64_W12, AArch64_W13, AArch64_W14, AArch64_W15, AArch64_W16, AArch64_W17, AArch64_W18, AArch64_W19, AArch64_W20, AArch64_W21, AArch64_W22, AArch64_W23, AArch64_W24, AArch64_W25, AArch64_W26, AArch64_W27, AArch64_W28, AArch64_W29, AArch64_W30, AArch64_WZR, AArch64_WSP, 
  };

  // GPR32all Bit set.
  static uint8_t GPR32allBits[] = {
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x7f, 
  };

  // FPR32 Register Class...
  static MCPhysReg FPR32[] = {
    AArch64_S0, AArch64_S1, AArch64_S2, AArch64_S3, AArch64_S4, AArch64_S5, AArch64_S6, AArch64_S7, AArch64_S8, AArch64_S9, AArch64_S10, AArch64_S11, AArch64_S12, AArch64_S13, AArch64_S14, AArch64_S15, AArch64_S16, AArch64_S17, AArch64_S18, AArch64_S19, AArch64_S20, AArch64_S21, AArch64_S22, AArch64_S23, AArch64_S24, AArch64_S25, AArch64_S26, AArch64_S27, AArch64_S28, AArch64_S29, AArch64_S30, AArch64_S31, 
  };

  // FPR32 Bit set.
  static uint8_t FPR32Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 
  };

  // GPR32 Register Class...
  static MCPhysReg GPR32[] = {
    AArch64_W0, AArch64_W1, AArch64_W2, AArch64_W3, AArch64_W4, AArch64_W5, AArch64_W6, AArch64_W7, AArch64_W8, AArch64_W9, AArch64_W10, AArch64_W11, AArch64_W12, AArch64_W13, AArch64_W14, AArch64_W15, AArch64_W16, AArch64_W17, AArch64_W18, AArch64_W19, AArch64_W20, AArch64_W21, AArch64_W22, AArch64_W23, AArch64_W24, AArch64_W25, AArch64_W26, AArch64_W27, AArch64_W28, AArch64_W29, AArch64_W30, AArch64_WZR, 
  };

  // GPR32 Bit set.
  static uint8_t GPR32Bits[] = {
    0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x7f, 
  };

  // GPR32sp Register Class...
  static MCPhysReg GPR32sp[] = {
    AArch64_W0, AArch64_W1, AArch64_W2, AArch64_W3, AArch64_W4, AArch64_W5, AArch64_W6, AArch64_W7, AArch64_W8, AArch64_W9, AArch64_W10, AArch64_W11, AArch64_W12, AArch64_W13, AArch64_W14, AArch64_W15, AArch64_W16, AArch64_W17, AArch64_W18, AArch64_W19, AArch64_W20, AArch64_W21, AArch64_W22, AArch64_W23, AArch64_W24, AArch64_W25, AArch64_W26, AArch64_W27, AArch64_W28, AArch64_W29, AArch64_W30, AArch64_WSP, 
  };

  // GPR32sp Bit set.
  static uint8_t GPR32spBits[] = {
    0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x7f, 
  };

  // GPR32common Register Class...
  static MCPhysReg GPR32common[] = {
    AArch64_W0, AArch64_W1, AArch64_W2, AArch64_W3, AArch64_W4, AArch64_W5, AArch64_W6, AArch64_W7, AArch64_W8, AArch64_W9, AArch64_W10, AArch64_W11, AArch64_W12, AArch64_W13, AArch64_W14, AArch64_W15, AArch64_W16, AArch64_W17, AArch64_W18, AArch64_W19, AArch64_W20, AArch64_W21, AArch64_W22, AArch64_W23, AArch64_W24, AArch64_W25, AArch64_W26, AArch64_W27, AArch64_W28, AArch64_W29, AArch64_W30, 
  };

  // GPR32common Bit set.
  static uint8_t GPR32commonBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x7f, 
  };

  // CCR Register Class...
  static MCPhysReg CCR[] = {
    AArch64_NZCV, 
  };

  // CCR Bit set.
  static uint8_t CCRBits[] = {
    0x08, 
  };

  // GPR32sponly Register Class...
  static MCPhysReg GPR32sponly[] = {
    AArch64_WSP, 
  };

  // GPR32sponly Bit set.
  static uint8_t GPR32sponlyBits[] = {
    0x20, 
  };

  // GPR64all Register Class...
  static MCPhysReg GPR64all[] = {
    AArch64_X0, AArch64_X1, AArch64_X2, AArch64_X3, AArch64_X4, AArch64_X5, AArch64_X6, AArch64_X7, AArch64_X8, AArch64_X9, AArch64_X10, AArch64_X11, AArch64_X12, AArch64_X13, AArch64_X14, AArch64_X15, AArch64_X16, AArch64_X17, AArch64_X18, AArch64_X19, AArch64_X20, AArch64_X21, AArch64_X22, AArch64_X23, AArch64_X24, AArch64_X25, AArch64_X26, AArch64_X27, AArch64_X28, AArch64_FP, AArch64_LR, AArch64_XZR, AArch64_SP, 
  };

  // GPR64all Bit set.
  static uint8_t GPR64allBits[] = {
    0x96, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x0f, 
  };

  // FPR64 Register Class...
  static MCPhysReg FPR64[] = {
    AArch64_D0, AArch64_D1, AArch64_D2, AArch64_D3, AArch64_D4, AArch64_D5, AArch64_D6, AArch64_D7, AArch64_D8, AArch64_D9, AArch64_D10, AArch64_D11, AArch64_D12, AArch64_D13, AArch64_D14, AArch64_D15, AArch64_D16, AArch64_D17, AArch64_D18, AArch64_D19, AArch64_D20, AArch64_D21, AArch64_D22, AArch64_D23, AArch64_D24, AArch64_D25, AArch64_D26, AArch64_D27, AArch64_D28, AArch64_D29, AArch64_D30, AArch64_D31, 
  };

  // FPR64 Bit set.
  static uint8_t FPR64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 
  };

  // GPR64 Register Class...
  static MCPhysReg GPR64[] = {
    AArch64_X0, AArch64_X1, AArch64_X2, AArch64_X3, AArch64_X4, AArch64_X5, AArch64_X6, AArch64_X7, AArch64_X8, AArch64_X9, AArch64_X10, AArch64_X11, AArch64_X12, AArch64_X13, AArch64_X14, AArch64_X15, AArch64_X16, AArch64_X17, AArch64_X18, AArch64_X19, AArch64_X20, AArch64_X21, AArch64_X22, AArch64_X23, AArch64_X24, AArch64_X25, AArch64_X26, AArch64_X27, AArch64_X28, AArch64_FP, AArch64_LR, AArch64_XZR, 
  };

  // GPR64 Bit set.
  static uint8_t GPR64Bits[] = {
    0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x0f, 
  };

  // GPR64sp Register Class...
  static MCPhysReg GPR64sp[] = {
    AArch64_X0, AArch64_X1, AArch64_X2, AArch64_X3, AArch64_X4, AArch64_X5, AArch64_X6, AArch64_X7, AArch64_X8, AArch64_X9, AArch64_X10, AArch64_X11, AArch64_X12, AArch64_X13, AArch64_X14, AArch64_X15, AArch64_X16, AArch64_X17, AArch64_X18, AArch64_X19, AArch64_X20, AArch64_X21, AArch64_X22, AArch64_X23, AArch64_X24, AArch64_X25, AArch64_X26, AArch64_X27, AArch64_X28, AArch64_FP, AArch64_LR, AArch64_SP, 
  };

  // GPR64sp Bit set.
  static uint8_t GPR64spBits[] = {
    0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x0f, 
  };

  // GPR64common Register Class...
  static MCPhysReg GPR64common[] = {
    AArch64_X0, AArch64_X1, AArch64_X2, AArch64_X3, AArch64_X4, AArch64_X5, AArch64_X6, AArch64_X7, AArch64_X8, AArch64_X9, AArch64_X10, AArch64_X11, AArch64_X12, AArch64_X13, AArch64_X14, AArch64_X15, AArch64_X16, AArch64_X17, AArch64_X18, AArch64_X19, AArch64_X20, AArch64_X21, AArch64_X22, AArch64_X23, AArch64_X24, AArch64_X25, AArch64_X26, AArch64_X27, AArch64_X28, AArch64_FP, AArch64_LR, 
  };

  // GPR64common Bit set.
  static uint8_t GPR64commonBits[] = {
    0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x0f, 
  };

  // tcGPR64 Register Class...
  static MCPhysReg tcGPR64[] = {
    AArch64_X0, AArch64_X1, AArch64_X2, AArch64_X3, AArch64_X4, AArch64_X5, AArch64_X6, AArch64_X7, AArch64_X8, AArch64_X9, AArch64_X10, AArch64_X11, AArch64_X12, AArch64_X13, AArch64_X14, AArch64_X15, AArch64_X16, AArch64_X17, AArch64_X18, 
  };

  // tcGPR64 Bit set.
  static uint8_t tcGPR64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0x03, 
  };

  // GPR64sponly Register Class...
  static MCPhysReg GPR64sponly[] = {
    AArch64_SP, 
  };

  // GPR64sponly Bit set.
  static uint8_t GPR64sponlyBits[] = {
    0x10, 
  };

  // DD Register Class...
  static MCPhysReg DD[] = {
    AArch64_D0_D1, AArch64_D1_D2, AArch64_D2_D3, AArch64_D3_D4, AArch64_D4_D5, AArch64_D5_D6, AArch64_D6_D7, AArch64_D7_D8, AArch64_D8_D9, AArch64_D9_D10, AArch64_D10_D11, AArch64_D11_D12, AArch64_D12_D13, AArch64_D13_D14, AArch64_D14_D15, AArch64_D15_D16, AArch64_D16_D17, AArch64_D17_D18, AArch64_D18_D19, AArch64_D19_D20, AArch64_D20_D21, AArch64_D21_D22, AArch64_D22_D23, AArch64_D23_D24, AArch64_D24_D25, AArch64_D25_D26, AArch64_D26_D27, AArch64_D27_D28, AArch64_D28_D29, AArch64_D29_D30, AArch64_D30_D31, AArch64_D31_D0, 
  };

  // DD Bit set.
  static uint8_t DDBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0xff, 0x0f, 
  };

  // FPR128 Register Class...
  static MCPhysReg FPR128[] = {
    AArch64_Q0, AArch64_Q1, AArch64_Q2, AArch64_Q3, AArch64_Q4, AArch64_Q5, AArch64_Q6, AArch64_Q7, AArch64_Q8, AArch64_Q9, AArch64_Q10, AArch64_Q11, AArch64_Q12, AArch64_Q13, AArch64_Q14, AArch64_Q15, AArch64_Q16, AArch64_Q17, AArch64_Q18, AArch64_Q19, AArch64_Q20, AArch64_Q21, AArch64_Q22, AArch64_Q23, AArch64_Q24, AArch64_Q25, AArch64_Q26, AArch64_Q27, AArch64_Q28, AArch64_Q29, AArch64_Q30, AArch64_Q31, 
  };

  // FPR128 Bit set.
  static uint8_t FPR128Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 
  };

  // FPR128_lo Register Class...
  static MCPhysReg FPR128_lo[] = {
    AArch64_Q0, AArch64_Q1, AArch64_Q2, AArch64_Q3, AArch64_Q4, AArch64_Q5, AArch64_Q6, AArch64_Q7, AArch64_Q8, AArch64_Q9, AArch64_Q10, AArch64_Q11, AArch64_Q12, AArch64_Q13, AArch64_Q14, AArch64_Q15, 
  };

  // FPR128_lo Bit set.
  static uint8_t FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 
  };

  // DDD Register Class...
  static MCPhysReg DDD[] = {
    AArch64_D0_D1_D2, AArch64_D1_D2_D3, AArch64_D2_D3_D4, AArch64_D3_D4_D5, AArch64_D4_D5_D6, AArch64_D5_D6_D7, AArch64_D6_D7_D8, AArch64_D7_D8_D9, AArch64_D8_D9_D10, AArch64_D9_D10_D11, AArch64_D10_D11_D12, AArch64_D11_D12_D13, AArch64_D12_D13_D14, AArch64_D13_D14_D15, AArch64_D14_D15_D16, AArch64_D15_D16_D17, AArch64_D16_D17_D18, AArch64_D17_D18_D19, AArch64_D18_D19_D20, AArch64_D19_D20_D21, AArch64_D20_D21_D22, AArch64_D21_D22_D23, AArch64_D22_D23_D24, AArch64_D23_D24_D25, AArch64_D24_D25_D26, AArch64_D25_D26_D27, AArch64_D26_D27_D28, AArch64_D27_D28_D29, AArch64_D28_D29_D30, AArch64_D29_D30_D31, AArch64_D30_D31_D0, AArch64_D31_D0_D1, 
  };

  // DDD Bit set.
  static uint8_t DDDBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0xff, 0x0f, 
  };

  // DDDD Register Class...
  static MCPhysReg DDDD[] = {
    AArch64_D0_D1_D2_D3, AArch64_D1_D2_D3_D4, AArch64_D2_D3_D4_D5, AArch64_D3_D4_D5_D6, AArch64_D4_D5_D6_D7, AArch64_D5_D6_D7_D8, AArch64_D6_D7_D8_D9, AArch64_D7_D8_D9_D10, AArch64_D8_D9_D10_D11, AArch64_D9_D10_D11_D12, AArch64_D10_D11_D12_D13, AArch64_D11_D12_D13_D14, AArch64_D12_D13_D14_D15, AArch64_D13_D14_D15_D16, AArch64_D14_D15_D16_D17, AArch64_D15_D16_D17_D18, AArch64_D16_D17_D18_D19, AArch64_D17_D18_D19_D20, AArch64_D18_D19_D20_D21, AArch64_D19_D20_D21_D22, AArch64_D20_D21_D22_D23, AArch64_D21_D22_D23_D24, AArch64_D22_D23_D24_D25, AArch64_D23_D24_D25_D26, AArch64_D24_D25_D26_D27, AArch64_D25_D26_D27_D28, AArch64_D26_D27_D28_D29, AArch64_D27_D28_D29_D30, AArch64_D28_D29_D30_D31, AArch64_D29_D30_D31_D0, AArch64_D30_D31_D0_D1, AArch64_D31_D0_D1_D2, 
  };

  // DDDD Bit set.
  static uint8_t DDDDBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0xff, 0x0f, 
  };

  // QQ Register Class...
  static MCPhysReg QQ[] = {
    AArch64_Q0_Q1, AArch64_Q1_Q2, AArch64_Q2_Q3, AArch64_Q3_Q4, AArch64_Q4_Q5, AArch64_Q5_Q6, AArch64_Q6_Q7, AArch64_Q7_Q8, AArch64_Q8_Q9, AArch64_Q9_Q10, AArch64_Q10_Q11, AArch64_Q11_Q12, AArch64_Q12_Q13, AArch64_Q13_Q14, AArch64_Q14_Q15, AArch64_Q15_Q16, AArch64_Q16_Q17, AArch64_Q17_Q18, AArch64_Q18_Q19, AArch64_Q19_Q20, AArch64_Q20_Q21, AArch64_Q21_Q22, AArch64_Q22_Q23, AArch64_Q23_Q24, AArch64_Q24_Q25, AArch64_Q25_Q26, AArch64_Q26_Q27, AArch64_Q27_Q28, AArch64_Q28_Q29, AArch64_Q29_Q30, AArch64_Q30_Q31, AArch64_Q31_Q0, 
  };

  // QQ Bit set.
  static uint8_t QQBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0xff, 0x0f, 
  };

  // QQ_with_qsub0_in_FPR128_lo Register Class...
  static MCPhysReg QQ_with_qsub0_in_FPR128_lo[] = {
    AArch64_Q0_Q1, AArch64_Q1_Q2, AArch64_Q2_Q3, AArch64_Q3_Q4, AArch64_Q4_Q5, AArch64_Q5_Q6, AArch64_Q6_Q7, AArch64_Q7_Q8, AArch64_Q8_Q9, AArch64_Q9_Q10, AArch64_Q10_Q11, AArch64_Q11_Q12, AArch64_Q12_Q13, AArch64_Q13_Q14, AArch64_Q14_Q15, AArch64_Q15_Q16, 
  };

  // QQ_with_qsub0_in_FPR128_lo Bit set.
  static uint8_t QQ_with_qsub0_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x0f, 
  };

  // QQ_with_qsub1_in_FPR128_lo Register Class...
  static MCPhysReg QQ_with_qsub1_in_FPR128_lo[] = {
    AArch64_Q0_Q1, AArch64_Q1_Q2, AArch64_Q2_Q3, AArch64_Q3_Q4, AArch64_Q4_Q5, AArch64_Q5_Q6, AArch64_Q6_Q7, AArch64_Q7_Q8, AArch64_Q8_Q9, AArch64_Q9_Q10, AArch64_Q10_Q11, AArch64_Q11_Q12, AArch64_Q12_Q13, AArch64_Q13_Q14, AArch64_Q14_Q15, AArch64_Q31_Q0, 
  };

  // QQ_with_qsub1_in_FPR128_lo Bit set.
  static uint8_t QQ_with_qsub1_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 0x00, 0x08, 
  };

  // QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_lo Register Class...
  static MCPhysReg QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_lo[] = {
    AArch64_Q0_Q1, AArch64_Q1_Q2, AArch64_Q2_Q3, AArch64_Q3_Q4, AArch64_Q4_Q5, AArch64_Q5_Q6, AArch64_Q6_Q7, AArch64_Q7_Q8, AArch64_Q8_Q9, AArch64_Q9_Q10, AArch64_Q10_Q11, AArch64_Q11_Q12, AArch64_Q12_Q13, AArch64_Q13_Q14, AArch64_Q14_Q15, 
  };

  // QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_lo Bit set.
  static uint8_t QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 
  };

  // QQQ Register Class...
  static MCPhysReg QQQ[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, AArch64_Q14_Q15_Q16, AArch64_Q15_Q16_Q17, AArch64_Q16_Q17_Q18, AArch64_Q17_Q18_Q19, AArch64_Q18_Q19_Q20, AArch64_Q19_Q20_Q21, AArch64_Q20_Q21_Q22, AArch64_Q21_Q22_Q23, AArch64_Q22_Q23_Q24, AArch64_Q23_Q24_Q25, AArch64_Q24_Q25_Q26, AArch64_Q25_Q26_Q27, AArch64_Q26_Q27_Q28, AArch64_Q27_Q28_Q29, AArch64_Q28_Q29_Q30, AArch64_Q29_Q30_Q31, AArch64_Q30_Q31_Q0, AArch64_Q31_Q0_Q1, 
  };

  // QQQ Bit set.
  static uint8_t QQQBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0xff, 0x0f, 
  };

  // QQQ_with_qsub0_in_FPR128_lo Register Class...
  static MCPhysReg QQQ_with_qsub0_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, AArch64_Q14_Q15_Q16, AArch64_Q15_Q16_Q17, 
  };

  // QQQ_with_qsub0_in_FPR128_lo Bit set.
  static uint8_t QQQ_with_qsub0_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x0f, 
  };

  // QQQ_with_qsub1_in_FPR128_lo Register Class...
  static MCPhysReg QQQ_with_qsub1_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, AArch64_Q14_Q15_Q16, AArch64_Q31_Q0_Q1, 
  };

  // QQQ_with_qsub1_in_FPR128_lo Bit set.
  static uint8_t QQQ_with_qsub1_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 0x00, 0x08, 
  };

  // QQQ_with_qsub2_in_FPR128_lo Register Class...
  static MCPhysReg QQQ_with_qsub2_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, AArch64_Q30_Q31_Q0, AArch64_Q31_Q0_Q1, 
  };

  // QQQ_with_qsub2_in_FPR128_lo Bit set.
  static uint8_t QQQ_with_qsub2_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x03, 0x00, 0x0c, 
  };

  // QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_lo Register Class...
  static MCPhysReg QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, AArch64_Q14_Q15_Q16, 
  };

  // QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_lo Bit set.
  static uint8_t QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 
  };

  // QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo Register Class...
  static MCPhysReg QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, AArch64_Q31_Q0_Q1, 
  };

  // QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo Bit set.
  static uint8_t QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x03, 0x00, 0x08, 
  };

  // QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo Register Class...
  static MCPhysReg QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2, AArch64_Q1_Q2_Q3, AArch64_Q2_Q3_Q4, AArch64_Q3_Q4_Q5, AArch64_Q4_Q5_Q6, AArch64_Q5_Q6_Q7, AArch64_Q6_Q7_Q8, AArch64_Q7_Q8_Q9, AArch64_Q8_Q9_Q10, AArch64_Q9_Q10_Q11, AArch64_Q10_Q11_Q12, AArch64_Q11_Q12_Q13, AArch64_Q12_Q13_Q14, AArch64_Q13_Q14_Q15, 
  };

  // QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo Bit set.
  static uint8_t QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x03, 
  };

  // QQQQ Register Class...
  static MCPhysReg QQQQ[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, AArch64_Q14_Q15_Q16_Q17, AArch64_Q15_Q16_Q17_Q18, AArch64_Q16_Q17_Q18_Q19, AArch64_Q17_Q18_Q19_Q20, AArch64_Q18_Q19_Q20_Q21, AArch64_Q19_Q20_Q21_Q22, AArch64_Q20_Q21_Q22_Q23, AArch64_Q21_Q22_Q23_Q24, AArch64_Q22_Q23_Q24_Q25, AArch64_Q23_Q24_Q25_Q26, AArch64_Q24_Q25_Q26_Q27, AArch64_Q25_Q26_Q27_Q28, AArch64_Q26_Q27_Q28_Q29, AArch64_Q27_Q28_Q29_Q30, AArch64_Q28_Q29_Q30_Q31, AArch64_Q29_Q30_Q31_Q0, AArch64_Q30_Q31_Q0_Q1, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ Bit set.
  static uint8_t QQQQBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0xff, 0x0f, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub0_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, AArch64_Q14_Q15_Q16_Q17, AArch64_Q15_Q16_Q17_Q18, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub0_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x0f, 
  };

  // QQQQ_with_qsub1_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub1_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, AArch64_Q14_Q15_Q16_Q17, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ_with_qsub1_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub1_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 0x00, 0x08, 
  };

  // QQQQ_with_qsub2_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub2_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, AArch64_Q30_Q31_Q0_Q1, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ_with_qsub2_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub2_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x03, 0x00, 0x0c, 
  };

  // QQQQ_with_qsub3_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub3_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q29_Q30_Q31_Q0, AArch64_Q30_Q31_Q0_Q1, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ_with_qsub3_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub3_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x01, 0x00, 0x0e, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, AArch64_Q14_Q15_Q16_Q17, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 
  };

  // QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x03, 0x00, 0x08, 
  };

  // QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q30_Q31_Q0_Q1, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x01, 0x00, 0x0c, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q13_Q14_Q15_Q16, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x03, 
  };

  // QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, AArch64_Q31_Q0_Q1_Q2, 
  };

  // QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x01, 0x00, 0x08, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo Register Class...
  static MCPhysReg QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo[] = {
    AArch64_Q0_Q1_Q2_Q3, AArch64_Q1_Q2_Q3_Q4, AArch64_Q2_Q3_Q4_Q5, AArch64_Q3_Q4_Q5_Q6, AArch64_Q4_Q5_Q6_Q7, AArch64_Q5_Q6_Q7_Q8, AArch64_Q6_Q7_Q8_Q9, AArch64_Q7_Q8_Q9_Q10, AArch64_Q8_Q9_Q10_Q11, AArch64_Q9_Q10_Q11_Q12, AArch64_Q10_Q11_Q12_Q13, AArch64_Q11_Q12_Q13_Q14, AArch64_Q12_Q13_Q14_Q15, 
  };

  // QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo Bit set.
  static uint8_t QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x01, 
  };

static MCRegisterClass AArch64MCRegisterClasses[] = {
  { "FPR8", FPR8, FPR8Bits, 32, sizeof(FPR8Bits), AArch64_FPR8RegClassID, 1, 1, 1, 1 },
  { "FPR16", FPR16, FPR16Bits, 32, sizeof(FPR16Bits), AArch64_FPR16RegClassID, 2, 2, 1, 1 },
  { "GPR32all", GPR32all, GPR32allBits, 33, sizeof(GPR32allBits), AArch64_GPR32allRegClassID, 4, 4, 1, 1 },
  { "FPR32", FPR32, FPR32Bits, 32, sizeof(FPR32Bits), AArch64_FPR32RegClassID, 4, 4, 1, 1 },
  { "GPR32", GPR32, GPR32Bits, 32, sizeof(GPR32Bits), AArch64_GPR32RegClassID, 4, 4, 1, 1 },
  { "GPR32sp", GPR32sp, GPR32spBits, 32, sizeof(GPR32spBits), AArch64_GPR32spRegClassID, 4, 4, 1, 1 },
  { "GPR32common", GPR32common, GPR32commonBits, 31, sizeof(GPR32commonBits), AArch64_GPR32commonRegClassID, 4, 4, 1, 1 },
  { "CCR", CCR, CCRBits, 1, sizeof(CCRBits), AArch64_CCRRegClassID, 4, 4, -1, 0 },
  { "GPR32sponly", GPR32sponly, GPR32sponlyBits, 1, sizeof(GPR32sponlyBits), AArch64_GPR32sponlyRegClassID, 4, 4, 1, 1 },
  { "GPR64all", GPR64all, GPR64allBits, 33, sizeof(GPR64allBits), AArch64_GPR64allRegClassID, 8, 8, 1, 1 },
  { "FPR64", FPR64, FPR64Bits, 32, sizeof(FPR64Bits), AArch64_FPR64RegClassID, 8, 8, 1, 1 },
  { "GPR64", GPR64, GPR64Bits, 32, sizeof(GPR64Bits), AArch64_GPR64RegClassID, 8, 8, 1, 1 },
  { "GPR64sp", GPR64sp, GPR64spBits, 32, sizeof(GPR64spBits), AArch64_GPR64spRegClassID, 8, 8, 1, 1 },
  { "GPR64common", GPR64common, GPR64commonBits, 31, sizeof(GPR64commonBits), AArch64_GPR64commonRegClassID, 8, 8, 1, 1 },
  { "tcGPR64", tcGPR64, tcGPR64Bits, 19, sizeof(tcGPR64Bits), AArch64_tcGPR64RegClassID, 8, 8, 1, 1 },
  { "GPR64sponly", GPR64sponly, GPR64sponlyBits, 1, sizeof(GPR64sponlyBits), AArch64_GPR64sponlyRegClassID, 8, 8, 1, 1 },
  { "DD", DD, DDBits, 32, sizeof(DDBits), AArch64_DDRegClassID, 16, 8, 1, 1 },
  { "FPR128", FPR128, FPR128Bits, 32, sizeof(FPR128Bits), AArch64_FPR128RegClassID, 16, 16, 1, 1 },
  { "FPR128_lo", FPR128_lo, FPR128_loBits, 16, sizeof(FPR128_loBits), AArch64_FPR128_loRegClassID, 16, 16, 1, 1 },
  { "DDD", DDD, DDDBits, 32, sizeof(DDDBits), AArch64_DDDRegClassID, 24, 8, 1, 1 },
  { "DDDD", DDDD, DDDDBits, 32, sizeof(DDDDBits), AArch64_DDDDRegClassID, 32, 8, 1, 1 },
  { "QQ", QQ, QQBits, 32, sizeof(QQBits), AArch64_QQRegClassID, 32, 16, 1, 1 },
  { "QQ_with_qsub0_in_FPR128_lo", QQ_with_qsub0_in_FPR128_lo, QQ_with_qsub0_in_FPR128_loBits, 16, sizeof(QQ_with_qsub0_in_FPR128_loBits), AArch64_QQ_with_qsub0_in_FPR128_loRegClassID, 32, 16, 1, 1 },
  { "QQ_with_qsub1_in_FPR128_lo", QQ_with_qsub1_in_FPR128_lo, QQ_with_qsub1_in_FPR128_loBits, 16, sizeof(QQ_with_qsub1_in_FPR128_loBits), AArch64_QQ_with_qsub1_in_FPR128_loRegClassID, 32, 16, 1, 1 },
  { "QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_lo", QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_lo, QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_loBits, 15, sizeof(QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_loBits), AArch64_QQ_with_qsub0_in_FPR128_lo_and_QQ_with_qsub1_in_FPR128_loRegClassID, 32, 16, 1, 1 },
  { "QQQ", QQQ, QQQBits, 32, sizeof(QQQBits), AArch64_QQQRegClassID, 48, 16, 1, 1 },
  { "QQQ_with_qsub0_in_FPR128_lo", QQQ_with_qsub0_in_FPR128_lo, QQQ_with_qsub0_in_FPR128_loBits, 16, sizeof(QQQ_with_qsub0_in_FPR128_loBits), AArch64_QQQ_with_qsub0_in_FPR128_loRegClassID, 48, 16, 1, 1 },
  { "QQQ_with_qsub1_in_FPR128_lo", QQQ_with_qsub1_in_FPR128_lo, QQQ_with_qsub1_in_FPR128_loBits, 16, sizeof(QQQ_with_qsub1_in_FPR128_loBits), AArch64_QQQ_with_qsub1_in_FPR128_loRegClassID, 48, 16, 1, 1 },
  { "QQQ_with_qsub2_in_FPR128_lo", QQQ_with_qsub2_in_FPR128_lo, QQQ_with_qsub2_in_FPR128_loBits, 16, sizeof(QQQ_with_qsub2_in_FPR128_loBits), AArch64_QQQ_with_qsub2_in_FPR128_loRegClassID, 48, 16, 1, 1 },
  { "QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_lo", QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_lo, QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_loBits, 15, sizeof(QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_loBits), AArch64_QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub1_in_FPR128_loRegClassID, 48, 16, 1, 1 },
  { "QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo", QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo, QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loBits, 15, sizeof(QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loBits), AArch64_QQQ_with_qsub1_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loRegClassID, 48, 16, 1, 1 },
  { "QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo", QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_lo, QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loBits, 14, sizeof(QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loBits), AArch64_QQQ_with_qsub0_in_FPR128_lo_and_QQQ_with_qsub2_in_FPR128_loRegClassID, 48, 16, 1, 1 },
  { "QQQQ", QQQQ, QQQQBits, 32, sizeof(QQQQBits), AArch64_QQQQRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub0_in_FPR128_lo", QQQQ_with_qsub0_in_FPR128_lo, QQQQ_with_qsub0_in_FPR128_loBits, 16, sizeof(QQQQ_with_qsub0_in_FPR128_loBits), AArch64_QQQQ_with_qsub0_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub1_in_FPR128_lo", QQQQ_with_qsub1_in_FPR128_lo, QQQQ_with_qsub1_in_FPR128_loBits, 16, sizeof(QQQQ_with_qsub1_in_FPR128_loBits), AArch64_QQQQ_with_qsub1_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub2_in_FPR128_lo", QQQQ_with_qsub2_in_FPR128_lo, QQQQ_with_qsub2_in_FPR128_loBits, 16, sizeof(QQQQ_with_qsub2_in_FPR128_loBits), AArch64_QQQQ_with_qsub2_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub3_in_FPR128_lo", QQQQ_with_qsub3_in_FPR128_lo, QQQQ_with_qsub3_in_FPR128_loBits, 16, sizeof(QQQQ_with_qsub3_in_FPR128_loBits), AArch64_QQQQ_with_qsub3_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_lo", QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_lo, QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_loBits, 15, sizeof(QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_loBits), AArch64_QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub1_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo", QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo, QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loBits, 15, sizeof(QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loBits), AArch64_QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo", QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo, QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits, 15, sizeof(QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits), AArch64_QQQQ_with_qsub2_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo", QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_lo, QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loBits, 14, sizeof(QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loBits), AArch64_QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub2_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo", QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo, QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits, 14, sizeof(QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits), AArch64_QQQQ_with_qsub1_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loRegClassID, 64, 16, 1, 1 },
  { "QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo", QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_lo, QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits, 13, sizeof(QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loBits), AArch64_QQQQ_with_qsub0_in_FPR128_lo_and_QQQQ_with_qsub3_in_FPR128_loRegClassID, 64, 16, 1, 1 },
};

#endif // GET_REGINFO_MC_DESC
