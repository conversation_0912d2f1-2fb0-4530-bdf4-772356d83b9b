/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    14036U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    14029U,	// BUNDLE
    14101U,	// LIFETIME_START
    14016U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    14116U,	// AAA
    20679U,	// AAD8i8
    23816U,	// AAM8i8
    14857U,	// AAS
    14865U,	// ABS_F
    0U,	// ABS_Fp32
    0U,	// ABS_Fp64
    0U,	// ABS_Fp80
    13680U,	// ACQUIRE_MOV16rm
    13680U,	// ACQUIRE_MOV32rm
    13680U,	// ACQUIRE_MOV64rm
    13680U,	// ACQUIRE_MOV8rm
    2124421U,	// ADC16i16
    4237957U,	// ADC16mi
    4237957U,	// ADC16mi8
    4237957U,	// ADC16mr
    6351493U,	// ADC16ri
    6351493U,	// ADC16ri8
    6367877U,	// ADC16rm
    6351493U,	// ADC16rr
    8448645U,	// ADC16rr_REV
    10508565U,	// ADC32i32
    12622101U,	// ADC32mi
    12622101U,	// ADC32mi8
    12622101U,	// ADC32mr
    6347029U,	// ADC32ri
    6347029U,	// ADC32ri8
    283203861U,	// ADC32rm
    6347029U,	// ADC32rr
    8444181U,	// ADC32rr_REV
    16801419U,	// ADC64i32
    18914955U,	// ADC64mi32
    18914955U,	// ADC64mi8
    18914955U,	// ADC64mr
    6348427U,	// ADC64ri32
    6348427U,	// ADC64ri8
    283221643U,	// ADC64rm
    6348427U,	// ADC64rr
    8445579U,	// ADC64rr_REV
    20991661U,	// ADC8i8
    23105197U,	// ADC8mi
    23105197U,	// ADC8mr
    6344365U,	// ADC8ri
    118445U,	// ADC8rm
    6344365U,	// ADC8rr
    8441517U,	// ADC8rr_REV
    551640279U,	// ADCX32rm
    8445143U,	// ADCX32rr
    551658286U,	// ADCX64rm
    8446766U,	// ADCX64rr
    2124463U,	// ADD16i16
    4237999U,	// ADD16mi
    4237999U,	// ADD16mi8
    4237999U,	// ADD16mr
    6351535U,	// ADD16ri
    6351535U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    6367919U,	// ADD16rm
    6351535U,	// ADD16rr
    0U,	// ADD16rr_DB
    8448687U,	// ADD16rr_REV
    10508599U,	// ADD32i32
    12622135U,	// ADD32mi
    12622135U,	// ADD32mi8
    12622135U,	// ADD32mr
    6347063U,	// ADD32ri
    6347063U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    283203895U,	// ADD32rm
    6347063U,	// ADD32rr
    0U,	// ADD32rr_DB
    8444215U,	// ADD32rr_REV
    16801519U,	// ADD64i32
    18915055U,	// ADD64mi32
    18915055U,	// ADD64mi8
    18915055U,	// ADD64mr
    6348527U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    6348527U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    283221743U,	// ADD64rm
    6348527U,	// ADD64rr
    0U,	// ADD64rr_DB
    8445679U,	// ADD64rr_REV
    20991681U,	// ADD8i8
    23105217U,	// ADD8mi
    23105217U,	// ADD8mr
    6344385U,	// ADD8ri
    6344385U,	// ADD8ri8
    118465U,	// ADD8rm
    6344385U,	// ADD8rr
    8441537U,	// ADD8rr_REV
    8524643U,	// ADDPDrm
    8442723U,	// ADDPDrr
    8529324U,	// ADDPSrm
    8447404U,	// ADDPSrr
    551703973U,	// ADDSDrm
    551703973U,	// ADDSDrm_Int
    8443301U,	// ADDSDrr
    8443301U,	// ADDSDrr_Int
    551725118U,	// ADDSSrm
    551725118U,	// ADDSSrm_Int
    8448062U,	// ADDSSrr
    8448062U,	// ADDSSrr_Int
    8524578U,	// ADDSUBPDrm
    8442658U,	// ADDSUBPDrr
    8529259U,	// ADDSUBPSrm
    8447339U,	// ADDSUBPSrr
    189404U,	// ADD_F32m
    203062U,	// ADD_F64m
    222179U,	// ADD_FI16m
    235837U,	// ADD_FI32m
    23932U,	// ADD_FPrST0
    20761U,	// ADD_FST0r
    0U,	// ADD_Fp32
    0U,	// ADD_Fp32m
    0U,	// ADD_Fp64
    0U,	// ADD_Fp64m
    0U,	// ADD_Fp64m32
    0U,	// ADD_Fp80
    0U,	// ADD_Fp80m32
    0U,	// ADD_Fp80m64
    0U,	// ADD_FpI16m32
    0U,	// ADD_FpI16m64
    0U,	// ADD_FpI16m80
    0U,	// ADD_FpI32m32
    0U,	// ADD_FpI32m64
    0U,	// ADD_FpI32m80
    28362U,	// ADD_FrST0
    14055U,	// ADJCALLSTACKDOWN32
    14055U,	// ADJCALLSTACKDOWN64
    14073U,	// ADJCALLSTACKUP32
    14073U,	// ADJCALLSTACKUP64
    551804140U,	// ADOX32rm
    551820524U,	// ADOX32rr
    551838531U,	// ADOX64rm
    551822147U,	// ADOX64rr
    8694081U,	// AESDECLASTrm
    8448321U,	// AESDECLASTrr
    8687767U,	// AESDECrm
    8442007U,	// AESDECrr
    8694094U,	// AESENCLASTrm
    8448334U,	// AESENCLASTrr
    8687807U,	// AESENCrm
    8442047U,	// AESENCrr
    315574U,	// AESIMCrm
    551817398U,	// AESIMCrr
    25504104U,	// AESKEYGENASSIST128rm
    811657576U,	// AESKEYGENASSIST128rr
    2124503U,	// AND16i16
    4238039U,	// AND16mi
    4238039U,	// AND16mi8
    4238039U,	// AND16mr
    6351575U,	// AND16ri
    6351575U,	// AND16ri8
    6367959U,	// AND16rm
    6351575U,	// AND16rr
    8448727U,	// AND16rr_REV
    10508652U,	// AND32i32
    12622188U,	// AND32mi
    12622188U,	// AND32mi8
    12622188U,	// AND32mr
    6347116U,	// AND32ri
    6347116U,	// AND32ri8
    283203948U,	// AND32rm
    6347116U,	// AND32rr
    8444268U,	// AND32rr_REV
    16801612U,	// AND64i32
    18915148U,	// AND64mi32
    18915148U,	// AND64mi8
    18915148U,	// AND64mr
    6348620U,	// AND64ri32
    6348620U,	// AND64ri8
    283221836U,	// AND64rm
    6348620U,	// AND64rr
    8445772U,	// AND64rr_REV
    20991694U,	// AND8i8
    23105230U,	// AND8mi
    23105230U,	// AND8mr
    6344398U,	// AND8ri
    6344398U,	// AND8ri8
    118478U,	// AND8rm
    6344398U,	// AND8rr
    8441550U,	// AND8rr_REV
    283204287U,	// ANDN32rm
    811653823U,	// ANDN32rr
    283222265U,	// ANDN64rm
    811655417U,	// ANDN64rr
    8524825U,	// ANDNPDrm
    8442905U,	// ANDNPDrr
    8529535U,	// ANDNPSrm
    8447615U,	// ANDNPSrr
    8524689U,	// ANDPDrm
    8442769U,	// ANDPDrr
    8529370U,	// ANDPSrm
    8447450U,	// ANDPSrr
    4234014U,	// ARPL16mr
    551820062U,	// ARPL16rr
    0U,	// AVX2_SETALLONES
    0U,	// AVX512_512_SET0
    0U,	// AVX_SET0
    832904105U,	// BEXTR32rm
    811654057U,	// BEXTR32rr
    835002789U,	// BEXTR64rm
    811655589U,	// BEXTR64rr
    832906169U,	// BEXTRI32mi
    811656121U,	// BEXTRI32ri
    835003321U,	// BEXTRI64mi
    811656121U,	// BEXTRI64ri
    551803488U,	// BLCFILL32rm
    551819872U,	// BLCFILL32rr
    551836256U,	// BLCFILL64rm
    551819872U,	// BLCFILL64rr
    551802925U,	// BLCI32rm
    551819309U,	// BLCI32rr
    551835693U,	// BLCI64rm
    551819309U,	// BLCI64rr
    551800991U,	// BLCIC32rm
    551817375U,	// BLCIC32rr
    551833759U,	// BLCIC64rm
    551817375U,	// BLCIC64rr
    551803075U,	// BLCMSK32rm
    551819459U,	// BLCMSK32rr
    551835843U,	// BLCMSK64rm
    551819459U,	// BLCMSK64rr
    551805910U,	// BLCS32rm
    551822294U,	// BLCS32rr
    551838678U,	// BLCS64rm
    551822294U,	// BLCS64rr
    568677273U,	// BLENDPDrmi
    570790809U,	// BLENDPDrri
    568681954U,	// BLENDPSrmi
    570795490U,	// BLENDPSrri
    8524921U,	// BLENDVPDrm0
    8443001U,	// BLENDVPDrr0
    8529696U,	// BLENDVPSrm0
    8447776U,	// BLENDVPSrr0
    551803497U,	// BLSFILL32rm
    551819881U,	// BLSFILL32rr
    551836265U,	// BLSFILL64rm
    551819881U,	// BLSFILL64rr
    551803433U,	// BLSI32rm
    551819817U,	// BLSI32rr
    551837826U,	// BLSI64rm
    551821442U,	// BLSI64rr
    551800998U,	// BLSIC32rm
    551817382U,	// BLSIC32rr
    551833766U,	// BLSIC64rm
    551817382U,	// BLSIC64rr
    551803453U,	// BLSMSK32rm
    551819837U,	// BLSMSK32rr
    551837842U,	// BLSMSK64rm
    551821458U,	// BLSMSK64rr
    551803798U,	// BLSR32rm
    551820182U,	// BLSR32rr
    551838089U,	// BLSR64rm
    551821705U,	// BLSR64rr
    551801305U,	// BOUNDS16rm
    551834073U,	// BOUNDS32rm
    387904U,	// BSF16rm
    551824192U,	// BSF16rr
    551803389U,	// BSF32rm
    551819773U,	// BSF32rr
    551837782U,	// BSF64rm
    551821398U,	// BSF64rr
    388226U,	// BSR16rm
    551824514U,	// BSR16rr
    551803792U,	// BSR32rm
    551820176U,	// BSR32rr
    551838083U,	// BSR64rm
    551821699U,	// BSR64rr
    23260U,	// BSWAP32r
    24849U,	// BSWAP64r
    4238713U,	// BT16mi8
    4238713U,	// BT16mr
    551824761U,	// BT16ri8
    551824761U,	// BT16rr
    12622901U,	// BT32mi8
    12622901U,	// BT32mr
    551820341U,	// BT32ri8
    551820341U,	// BT32rr
    18915862U,	// BT64mi8
    18915862U,	// BT64mr
    551821846U,	// BT64ri8
    551821846U,	// BT64rr
    4237982U,	// BTC16mi8
    4237982U,	// BTC16mr
    551824030U,	// BTC16ri8
    551824030U,	// BTC16rr
    12622119U,	// BTC32mi8
    12622119U,	// BTC32mr
    551819559U,	// BTC32ri8
    551819559U,	// BTC32rr
    18914973U,	// BTC64mi8
    18914973U,	// BTC64mr
    551820957U,	// BTC64ri8
    551820957U,	// BTC64rr
    4238481U,	// BTR16mi8
    4238481U,	// BTR16mr
    551824529U,	// BTR16ri8
    551824529U,	// BTR16rr
    12622749U,	// BTR32mi8
    12622749U,	// BTR32mr
    551820189U,	// BTR32ri8
    551820189U,	// BTR32rr
    18915737U,	// BTR64mi8
    18915737U,	// BTR64mr
    551821721U,	// BTR64ri8
    551821721U,	// BTR64rr
    4238655U,	// BTS16mi8
    4238655U,	// BTS16mr
    551824703U,	// BTS16ri8
    551824703U,	// BTS16rr
    12622880U,	// BTS32mi8
    12622880U,	// BTS32mr
    551820320U,	// BTS32ri8
    551820320U,	// BTS32rr
    18915848U,	// BTS64mi8
    18915848U,	// BTS64mr
    551821832U,	// BTS64ri8
    551821832U,	// BTS64rr
    832903714U,	// BZHI32rm
    811653666U,	// BZHI32rr
    835002491U,	// BZHI64rm
    811655291U,	// BZHI64rr
    226749U,	// CALL16m
    30141U,	// CALL16r
    243099U,	// CALL32m
    30107U,	// CALL32r
    406956U,	// CALL64m
    417967U,	// CALL64pcrel32
    30124U,	// CALL64r
    420793U,	// CALLpcrel16
    416371U,	// CALLpcrel32
    15328U,	// CBW
    14263U,	// CDQ
    14802U,	// CDQE
    15087U,	// CHS_F
    0U,	// CHS_Fp32
    0U,	// CHS_Fp64
    0U,	// CHS_Fp80
    14185U,	// CLAC
    14217U,	// CLC
    14254U,	// CLD
    432164U,	// CLFLUSH
    14428U,	// CLGI
    14438U,	// CLI
    15200U,	// CLTS
    14221U,	// CMC
    8464878U,	// CMOVA16rm
    8448494U,	// CMOVA16rr
    551639264U,	// CMOVA32rm
    8444128U,	// CMOVA32rr
    551657010U,	// CMOVA64rm
    8445490U,	// CMOVA64rr
    8465156U,	// CMOVAE16rm
    8448772U,	// CMOVAE16rr
    551639442U,	// CMOVAE32rm
    8444306U,	// CMOVAE32rr
    551657433U,	// CMOVAE64rm
    8445913U,	// CMOVAE64rr
    8464991U,	// CMOVB16rm
    8448607U,	// CMOVB16rr
    551639301U,	// CMOVB32rm
    8444165U,	// CMOVB32rr
    551657061U,	// CMOVB64rm
    8445541U,	// CMOVB64rr
    8465165U,	// CMOVBE16rm
    8448781U,	// CMOVBE16rr
    551639451U,	// CMOVBE32rm
    8444315U,	// CMOVBE32rr
    551657442U,	// CMOVBE64rm
    8445922U,	// CMOVBE64rr
    35673950U,	// CMOVBE_F
    0U,	// CMOVBE_Fp32
    0U,	// CMOVBE_Fp64
    0U,	// CMOVBE_Fp80
    35672160U,	// CMOVB_F
    0U,	// CMOVB_Fp32
    0U,	// CMOVB_Fp64
    0U,	// CMOVB_Fp80
    8465201U,	// CMOVE16rm
    8448817U,	// CMOVE16rr
    551639541U,	// CMOVE32rm
    8444405U,	// CMOVE32rr
    551657550U,	// CMOVE64rm
    8446030U,	// CMOVE64rr
    35674072U,	// CMOVE_F
    0U,	// CMOVE_Fp32
    0U,	// CMOVE_Fp64
    0U,	// CMOVE_Fp80
    8465254U,	// CMOVG16rm
    8448870U,	// CMOVG16rr
    551639571U,	// CMOVG32rm
    8444435U,	// CMOVG32rr
    551657580U,	// CMOVG64rm
    8446060U,	// CMOVG64rr
    8465174U,	// CMOVGE16rm
    8448790U,	// CMOVGE16rr
    551639460U,	// CMOVGE32rm
    8444324U,	// CMOVGE32rr
    551657451U,	// CMOVGE64rm
    8445931U,	// CMOVGE64rr
    8465388U,	// CMOVL16rm
    8449004U,	// CMOVL16rr
    551639720U,	// CMOVL32rm
    8444584U,	// CMOVL32rr
    551657697U,	// CMOVL64rm
    8446177U,	// CMOVL64rr
    8465183U,	// CMOVLE16rm
    8448799U,	// CMOVLE16rr
    551639469U,	// CMOVLE32rm
    8444333U,	// CMOVLE32rr
    551657460U,	// CMOVLE64rm
    8445940U,	// CMOVLE64rr
    35673933U,	// CMOVNBE_F
    0U,	// CMOVNBE_Fp32
    0U,	// CMOVNBE_Fp64
    0U,	// CMOVNBE_Fp80
    35671867U,	// CMOVNB_F
    0U,	// CMOVNB_Fp32
    0U,	// CMOVNB_Fp64
    0U,	// CMOVNB_Fp80
    8465192U,	// CMOVNE16rm
    8448808U,	// CMOVNE16rr
    551639478U,	// CMOVNE32rm
    8444342U,	// CMOVNE32rr
    551657469U,	// CMOVNE64rm
    8445949U,	// CMOVNE64rr
    35674023U,	// CMOVNE_F
    0U,	// CMOVNE_Fp32
    0U,	// CMOVNE_Fp64
    0U,	// CMOVNE_Fp80
    8465418U,	// CMOVNO16rm
    8449034U,	// CMOVNO16rr
    551639755U,	// CMOVNO32rm
    8444619U,	// CMOVNO32rr
    551657728U,	// CMOVNO64rm
    8446208U,	// CMOVNO64rr
    8465448U,	// CMOVNP16rm
    8449064U,	// CMOVNP16rr
    551639817U,	// CMOVNP32rm
    8444681U,	// CMOVNP32rr
    551657768U,	// CMOVNP64rm
    8446248U,	// CMOVNP64rr
    35678594U,	// CMOVNP_F
    0U,	// CMOVNP_Fp32
    0U,	// CMOVNP_Fp64
    0U,	// CMOVNP_Fp80
    8465694U,	// CMOVNS16rm
    8449310U,	// CMOVNS16rr
    551640049U,	// CMOVNS32rm
    8444913U,	// CMOVNS32rr
    551657945U,	// CMOVNS64rm
    8446425U,	// CMOVNS64rr
    8465427U,	// CMOVO16rm
    8449043U,	// CMOVO16rr
    551639764U,	// CMOVO32rm
    8444628U,	// CMOVO32rr
    551657737U,	// CMOVO64rm
    8446217U,	// CMOVO64rr
    8465469U,	// CMOVP16rm
    8449085U,	// CMOVP16rr
    551639868U,	// CMOVP32rm
    8444732U,	// CMOVP32rr
    551657783U,	// CMOVP64rm
    8446263U,	// CMOVP64rr
    35678633U,	// CMOVP_F
    0U,	// CMOVP_Fp32
    0U,	// CMOVP_Fp64
    0U,	// CMOVP_Fp80
    8465768U,	// CMOVS16rm
    8449384U,	// CMOVS16rr
    551640109U,	// CMOVS32rm
    8444973U,	// CMOVS32rr
    551657998U,	// CMOVS64rm
    8446478U,	// CMOVS64rr
    13484U,	// CMOV_FR32
    13643U,	// CMOV_FR64
    13363U,	// CMOV_GR16
    13343U,	// CMOV_GR32
    13662U,	// CMOV_GR8
    13464U,	// CMOV_RFP32
    13623U,	// CMOV_RFP64
    13383U,	// CMOV_RFP80
    13423U,	// CMOV_V16F32
    13503U,	// CMOV_V2F64
    13563U,	// CMOV_V2I64
    13403U,	// CMOV_V4F32
    13523U,	// CMOV_V4F64
    13583U,	// CMOV_V4I64
    13444U,	// CMOV_V8F32
    13543U,	// CMOV_V8F64
    13603U,	// CMOV_V8I64
    2124827U,	// CMP16i16
    4238363U,	// CMP16mi
    4238363U,	// CMP16mi8
    4238363U,	// CMP16mr
    551824411U,	// CMP16ri
    551824411U,	// CMP16ri8
    388123U,	// CMP16rm
    551824411U,	// CMP16rr
    551824411U,	// CMP16rr_REV
    10509035U,	// CMP32i32
    12622571U,	// CMP32mi
    12622571U,	// CMP32mi8
    12622571U,	// CMP32mr
    551820011U,	// CMP32ri
    551820011U,	// CMP32ri8
    551803627U,	// CMP32rm
    551820011U,	// CMP32rr
    551820011U,	// CMP32rr_REV
    16802082U,	// CMP64i32
    18915618U,	// CMP64mi32
    18915618U,	// CMP64mi8
    18915618U,	// CMP64mr
    551821602U,	// CMP64ri32
    551821602U,	// CMP64ri8
    551837986U,	// CMP64rm
    551821602U,	// CMP64rr
    551821602U,	// CMP64rr_REV
    20991812U,	// CMP8i8
    23105348U,	// CMP8mi
    23105348U,	// CMP8mr
    551817028U,	// CMP8ri
    446276U,	// CMP8rm
    551817028U,	// CMP8rr
    551817028U,	// CMP8rr_REV
    1111964007U,	// CMPPDrmi
    568677425U,	// CMPPDrmi_alt
    1380415847U,	// CMPPDrri
    570790961U,	// CMPPDrri_alt
    1114061159U,	// CMPPSrmi
    568682143U,	// CMPPSrmi_alt
    1382512999U,	// CMPPSrri
    570795679U,	// CMPPSrri_alt
    1625788350U,	// CMPSB
    1921464679U,	// CMPSDrm
    581260790U,	// CMPSDrm_alt
    1384610151U,	// CMPSDrr
    570791414U,	// CMPSDrr_alt
    2162678778U,	// CMPSL
    2431132130U,	// CMPSQ
    2730965351U,	// CMPSSrm
    585459854U,	// CMPSSrm_alt
    1388804455U,	// CMPSSrr
    570796174U,	// CMPSSrr_alt
    2968022311U,	// CMPSW
    560768U,	// CMPXCHG16B
    4238164U,	// CMPXCHG16rm
    551824212U,	// CMPXCHG16rr
    12622345U,	// CMPXCHG32rm
    551819785U,	// CMPXCHG32rr
    18915426U,	// CMPXCHG64rm
    551821410U,	// CMPXCHG64rr
    396940U,	// CMPXCHG8B
    23105251U,	// CMPXCHG8rm
    551816931U,	// CMPXCHG8rr
    579019U,	// COMISDrm
    551818699U,	// COMISDrr
    583780U,	// COMISSrm
    551823460U,	// COMISSrr
    23955U,	// COMP_FST0r
    22636U,	// COM_FIPr
    22579U,	// COM_FIr
    23821U,	// COM_FST0r
    15149U,	// COS_F
    0U,	// COS_Fp32
    0U,	// COS_Fp64
    0U,	// COS_Fp80
    14248U,	// CPUID32
    14248U,	// CPUID64
    14677U,	// CQO
    6367696U,	// CRC32r32m16
    283203794U,	// CRC32r32m32
    118392U,	// CRC32r32m8
    6351312U,	// CRC32r32r16
    6346962U,	// CRC32r32r32
    6344312U,	// CRC32r32r8
    283221523U,	// CRC32r64m64
    118392U,	// CRC32r64m8
    6348307U,	// CRC32r64r64
    6344312U,	// CRC32r64r8
    551834275U,	// CVTDQ2PDrm
    551817891U,	// CVTDQ2PDrr
    320767U,	// CVTDQ2PSrm
    551822591U,	// CVTDQ2PSrr
    581296U,	// CVTPD2DQrm
    551820976U,	// CVTPD2DQrr
    582867U,	// CVTPD2PSrm
    551822547U,	// CVTPD2PSrr
    581328U,	// CVTPS2DQrm
    551821008U,	// CVTPS2DQrr
    594606U,	// CVTPS2PDrm
    551817902U,	// CVTPS2PDrr
    596130U,	// CVTSD2SI64rm
    551819426U,	// CVTSD2SI64rr
    596130U,	// CVTSD2SIrm
    551819426U,	// CVTSD2SIrr
    600030U,	// CVTSD2SSrm
    551823326U,	// CVTSD2SSrr
    551837580U,	// CVTSI2SD64rm
    551821196U,	// CVTSI2SD64rr
    551803258U,	// CVTSI2SDrm
    551819642U,	// CVTSI2SDrr
    551838186U,	// CVTSI2SS64rm
    551821802U,	// CVTSI2SS64rr
    551803906U,	// CVTSI2SSrm
    551820290U,	// CVTSI2SSrr
    611645U,	// CVTSS2SDrm
    551818557U,	// CVTSS2SDrr
    612537U,	// CVTSS2SI64rm
    551819449U,	// CVTSS2SI64rr
    612537U,	// CVTSS2SIrm
    551819449U,	// CVTSS2SIrr
    581284U,	// CVTTPD2DQrm
    551820964U,	// CVTTPD2DQrr
    581316U,	// CVTTPS2DQrm
    551820996U,	// CVTTPS2DQrr
    596118U,	// CVTTSD2SI64rm
    551819414U,	// CVTTSD2SI64rr
    596118U,	// CVTTSD2SIrm
    551819414U,	// CVTTSD2SIrr
    612525U,	// CVTTSS2SI64rm
    551819437U,	// CVTTSS2SI64rr
    612525U,	// CVTTSS2SIrm
    551819437U,	// CVTTSS2SIrr
    14272U,	// CWD
    14592U,	// CWDE
    14120U,	// DAA
    14861U,	// DAS
    14001U,	// DATA16_PREFIX
    223890U,	// DEC16m
    27282U,	// DEC16r
    27282U,	// DEC32_16r
    22811U,	// DEC32_32r
    235803U,	// DEC32m
    22811U,	// DEC32r
    223890U,	// DEC64_16m
    27282U,	// DEC64_16r
    235803U,	// DEC64_32m
    22811U,	// DEC64_32r
    401041U,	// DEC64m
    24209U,	// DEC64r
    429747U,	// DEC8m
    20147U,	// DEC8r
    224808U,	// DIV16m
    28200U,	// DIV16r
    236716U,	// DIV32m
    23724U,	// DIV32r
    402113U,	// DIV64m
    25281U,	// DIV64r
    430170U,	// DIV8m
    20570U,	// DIV8r
    8524932U,	// DIVPDrm
    8443012U,	// DIVPDrr
    8529707U,	// DIVPSrm
    8447787U,	// DIVPSrr
    190304U,	// DIVR_F32m
    203697U,	// DIVR_F64m
    223080U,	// DIVR_FI16m
    236473U,	// DIVR_FI32m
    24076U,	// DIVR_FPrST0
    25536U,	// DIVR_FST0r
    0U,	// DIVR_Fp32m
    0U,	// DIVR_Fp64m
    0U,	// DIVR_Fp64m32
    0U,	// DIVR_Fp80m32
    0U,	// DIVR_Fp80m64
    0U,	// DIVR_FpI16m32
    0U,	// DIVR_FpI16m64
    0U,	// DIVR_FpI16m80
    0U,	// DIVR_FpI32m32
    0U,	// DIVR_FpI32m64
    0U,	// DIVR_FpI32m80
    28451U,	// DIVR_FrST0
    551704094U,	// DIVSDrm
    551704094U,	// DIVSDrm_Int
    8443422U,	// DIVSDrr
    8443422U,	// DIVSDrr_Int
    551725248U,	// DIVSSrm
    551725248U,	// DIVSSrm_Int
    8448192U,	// DIVSSrr
    8448192U,	// DIVSSrr_Int
    190707U,	// DIV_F32m
    203947U,	// DIV_F64m
    223482U,	// DIV_FI16m
    236722U,	// DIV_FI32m
    24001U,	// DIV_FPrST0
    27057U,	// DIV_FST0r
    0U,	// DIV_Fp32
    0U,	// DIV_Fp32m
    0U,	// DIV_Fp64
    0U,	// DIV_Fp64m
    0U,	// DIV_Fp64m32
    0U,	// DIV_Fp80
    0U,	// DIV_Fp80m32
    0U,	// DIV_Fp80m64
    0U,	// DIV_FpI16m32
    0U,	// DIV_FpI16m64
    0U,	// DIV_FpI16m80
    0U,	// DIV_FpI32m32
    0U,	// DIV_FpI32m64
    0U,	// DIV_FpI32m80
    28436U,	// DIV_FrST0
    568677418U,	// DPPDrmi
    570790954U,	// DPPDrri
    568682136U,	// DPPSrmi
    570795672U,	// DPPSrri
    29590U,	// EH_RETURN
    29590U,	// EH_RETURN64
    13810U,	// EH_SjLj_LongJmp32
    13914U,	// EH_SjLj_LongJmp64
    13829U,	// EH_SjLj_SetJmp32
    13933U,	// EH_SjLj_SetJmp64
    417276U,	// EH_SjLj_Setup
    15137U,	// ENCLS
    15262U,	// ENCLU
    283140976U,	// ENTER
    3271894722U,	// EXTRACTPSmr
    811656898U,	// EXTRACTPSrr
    6349222U,	// EXTRQ
    302358950U,	// EXTRQI
    13791U,	// F2XM1
    52702136U,	// FARCALL16i
    636348U,	// FARCALL16m
    52697714U,	// FARCALL32i
    636314U,	// FARCALL32m
    636331U,	// FARCALL64
    52702241U,	// FARJMP16i
    636357U,	// FARJMP16m
    52697841U,	// FARJMP32i
    636323U,	// FARJMP32m
    636340U,	// FARJMP64
    184708U,	// FBLDm
    187855U,	// FBSTPm
    189451U,	// FCOM32m
    203440U,	// FCOM64m
    190052U,	// FCOMP32m
    203512U,	// FCOMP64m
    14709U,	// FCOMPP
    14724U,	// FDECSTP
    15143U,	// FEMMS
    22375U,	// FFREE
    222226U,	// FICOM16m
    236215U,	// FICOM32m
    222828U,	// FICOMP16m
    236288U,	// FICOMP32m
    14732U,	// FINCSTP
    223883U,	// FLDCW16m
    190903U,	// FLDENVm
    14284U,	// FLDL2E
    15205U,	// FLDL2T
    13895U,	// FLDLG2
    13902U,	// FLDLN2
    14442U,	// FLDPI
    15552U,	// FNCLEX
    15226U,	// FNINIT
    14704U,	// FNOP
    223908U,	// FNSTCW16m
    15351U,	// FNSTSW16r
    191813U,	// FNSTSWm
    0U,	// FP32_TO_INT16_IN_MEM
    0U,	// FP32_TO_INT32_IN_MEM
    0U,	// FP32_TO_INT64_IN_MEM
    0U,	// FP64_TO_INT16_IN_MEM
    0U,	// FP64_TO_INT32_IN_MEM
    0U,	// FP64_TO_INT64_IN_MEM
    0U,	// FP80_TO_INT16_IN_MEM
    0U,	// FP80_TO_INT32_IN_MEM
    0U,	// FP80_TO_INT64_IN_MEM
    14627U,	// FPATAN
    14610U,	// FPREM
    13784U,	// FPREM1
    14634U,	// FPTAN
    15237U,	// FRNDINT
    189319U,	// FRSTORm
    186312U,	// FSAVEm
    14312U,	// FSCALE
    14616U,	// FSETPM
    15154U,	// FSINCOS
    190911U,	// FSTENVm
    14605U,	// FXAM
    631695U,	// FXRSTOR
    631155U,	// FXRSTOR64
    628688U,	// FXSAVE
    630853U,	// FXSAVE64
    15212U,	// FXTRACT
    15345U,	// FYL2X
    13797U,	// FYL2XP1
    8524825U,	// FsANDNPDrm
    8442905U,	// FsANDNPDrr
    8529535U,	// FsANDNPSrm
    8447615U,	// FsANDNPSrr
    8524689U,	// FsANDPDrm
    8442769U,	// FsANDPDrr
    8529370U,	// FsANDPSrm
    8447450U,	// FsANDPSrr
    0U,	// FsFLD0SD
    0U,	// FsFLD0SS
    578319U,	// FsMOVAPDrm
    583008U,	// FsMOVAPSrm
    8524869U,	// FsORPDrm
    8442949U,	// FsORPDrr
    8529587U,	// FsORPSrm
    8447667U,	// FsORPSrr
    578318U,	// FsVMOVAPDrm
    583007U,	// FsVMOVAPSrm
    8524876U,	// FsXORPDrm
    8442956U,	// FsXORPDrr
    8529594U,	// FsXORPSrm
    8447674U,	// FsXORPSrr
    14205U,	// GETSEC
    8524651U,	// HADDPDrm
    8442731U,	// HADDPDrr
    8529332U,	// HADDPSrm
    8447412U,	// HADDPSrr
    15233U,	// HLT
    8524600U,	// HSUBPDrm
    8442680U,	// HSUBPDrr
    8529281U,	// HSUBPSrm
    8447361U,	// HSUBPSrr
    224807U,	// IDIV16m
    28199U,	// IDIV16r
    236723U,	// IDIV32m
    23731U,	// IDIV32r
    402112U,	// IDIV64m
    25280U,	// IDIV64r
    430169U,	// IDIV8m
    20569U,	// IDIV8r
    222193U,	// ILD_F16m
    235874U,	// ILD_F32m
    399954U,	// ILD_F64m
    0U,	// ILD_Fp16m32
    0U,	// ILD_Fp16m64
    0U,	// ILD_Fp16m80
    0U,	// ILD_Fp32m32
    0U,	// ILD_Fp32m64
    0U,	// ILD_Fp32m80
    0U,	// ILD_Fp64m32
    0U,	// ILD_Fp64m64
    0U,	// ILD_Fp64m80
    224229U,	// IMUL16m
    27621U,	// IMUL16r
    8465381U,	// IMUL16rm
    54864869U,	// IMUL16rmi
    54864869U,	// IMUL16rmi8
    8448997U,	// IMUL16rr
    811658213U,	// IMUL16rri
    811658213U,	// IMUL16rri8
    236193U,	// IMUL32m
    23201U,	// IMUL32r
    551639713U,	// IMUL32rm
    832903841U,	// IMUL32rmi
    832903841U,	// IMUL32rmi8
    8444577U,	// IMUL32rr
    811653793U,	// IMUL32rri
    811653793U,	// IMUL32rri8
    401626U,	// IMUL64m
    24794U,	// IMUL64r
    551657690U,	// IMUL64rm
    835002586U,	// IMUL64rmi32
    835002586U,	// IMUL64rmi8
    8446170U,	// IMUL64rr
    811655386U,	// IMUL64rri32
    811655386U,	// IMUL64rri8
    429854U,	// IMUL8m
    20254U,	// IMUL8r
    2124805U,	// IN16ri
    15362U,	// IN16rr
    10508998U,	// IN32ri
    15441U,	// IN32rr
    20991798U,	// IN8ri
    14472U,	// IN8rr
    223896U,	// INC16m
    27288U,	// INC16r
    27288U,	// INC32_16r
    22817U,	// INC32_32r
    235809U,	// INC32m
    22817U,	// INC32r
    223896U,	// INC64_16m
    27288U,	// INC64_16r
    235809U,	// INC64_32m
    22817U,	// INC64_32r
    401047U,	// INC64m
    24215U,	// INC64r
    429753U,	// INC8m
    20153U,	// INC8r
    504521U,	// INSB
    585459416U,	// INSERTPSrm
    570795736U,	// INSERTPSrr
    6349449U,	// INSERTQ
    302637705U,	// INSERTQI
    520916U,	// INSL
    553695U,	// INSW
    26897U,	// INT
    13805U,	// INT1
    13909U,	// INT3
    14672U,	// INTO
    14279U,	// INVD
    321814U,	// INVEPT32
    321814U,	// INVEPT64
    432100U,	// INVLPG
    15422U,	// INVLPGA32
    15490U,	// INVLPGA64
    315762U,	// INVPCID32
    315762U,	// INVPCID64
    315771U,	// INVVPID32
    315771U,	// INVVPID64
    15333U,	// IRET16
    14563U,	// IRET32
    14773U,	// IRET64
    222989U,	// ISTT_FP16m
    236339U,	// ISTT_FP32m
    400009U,	// ISTT_FP64m
    0U,	// ISTT_Fp16m32
    0U,	// ISTT_Fp16m64
    0U,	// ISTT_Fp16m80
    0U,	// ISTT_Fp32m32
    0U,	// ISTT_Fp32m64
    0U,	// ISTT_Fp32m80
    0U,	// ISTT_Fp64m32
    0U,	// ISTT_Fp64m64
    0U,	// ISTT_Fp64m80
    223468U,	// IST_F16m
    236694U,	// IST_F32m
    222981U,	// IST_FP16m
    236331U,	// IST_FP32m
    400000U,	// IST_FP64m
    0U,	// IST_Fp16m32
    0U,	// IST_Fp16m64
    0U,	// IST_Fp16m80
    0U,	// IST_Fp32m32
    0U,	// IST_Fp32m64
    0U,	// IST_Fp32m80
    0U,	// IST_Fp64m32
    0U,	// IST_Fp64m64
    0U,	// IST_Fp64m80
    1921464679U,	// Int_CMPSDrm
    1384610151U,	// Int_CMPSDrr
    2730965351U,	// Int_CMPSSrm
    1388804455U,	// Int_CMPSSrr
    579019U,	// Int_COMISDrm
    551818699U,	// Int_COMISDrr
    583780U,	// Int_COMISSrm
    551823460U,	// Int_COMISSrr
    551708638U,	// Int_CVTSD2SSrm
    8447966U,	// Int_CVTSD2SSrr
    551657356U,	// Int_CVTSI2SD64rm
    8445836U,	// Int_CVTSI2SD64rr
    551639418U,	// Int_CVTSI2SDrm
    8444282U,	// Int_CVTSI2SDrr
    551657962U,	// Int_CVTSI2SS64rm
    8446442U,	// Int_CVTSI2SS64rr
    551640066U,	// Int_CVTSI2SSrm
    8444930U,	// Int_CVTSI2SSrr
    551720253U,	// Int_CVTSS2SDrm
    8443197U,	// Int_CVTSS2SDrr
    596118U,	// Int_CVTTSD2SI64rm
    551819414U,	// Int_CVTTSD2SI64rr
    596118U,	// Int_CVTTSD2SIrm
    551819414U,	// Int_CVTTSD2SIrr
    612525U,	// Int_CVTTSS2SI64rm
    551819437U,	// Int_CVTTSS2SI64rr
    612525U,	// Int_CVTTSS2SIrm
    551819437U,	// Int_CVTTSS2SIrr
    14089U,	// Int_MemBarrier
    579018U,	// Int_UCOMISDrm
    551818698U,	// Int_UCOMISDrr
    583779U,	// Int_UCOMISSrm
    551823459U,	// Int_UCOMISSrr
    1921661291U,	// Int_VCMPSDrm
    1384806763U,	// Int_VCMPSDrr
    2731161963U,	// Int_VCMPSSrm
    1389001067U,	// Int_VCMPSSrr
    579027U,	// Int_VCOMISDZrm
    551818707U,	// Int_VCOMISDZrr
    579027U,	// Int_VCOMISDrm
    551818707U,	// Int_VCOMISDrr
    583788U,	// Int_VCOMISSZrm
    551823468U,	// Int_VCOMISSZrr
    583788U,	// Int_VCOMISSrm
    551823468U,	// Int_VCOMISSrr
    283273181U,	// Int_VCVTSD2SSrm
    811657181U,	// Int_VCVTSD2SSrr
    283221899U,	// Int_VCVTSI2SD64Zrm
    811655051U,	// Int_VCVTSI2SD64Zrr
    283221899U,	// Int_VCVTSI2SD64rm
    811655051U,	// Int_VCVTSI2SD64rr
    283203961U,	// Int_VCVTSI2SDZrm
    811653497U,	// Int_VCVTSI2SDZrr
    283203961U,	// Int_VCVTSI2SDrm
    811653497U,	// Int_VCVTSI2SDrr
    283222505U,	// Int_VCVTSI2SS64Zrm
    811655657U,	// Int_VCVTSI2SS64Zrr
    283222505U,	// Int_VCVTSI2SS64rm
    811655657U,	// Int_VCVTSI2SS64rr
    283204609U,	// Int_VCVTSI2SSZrm
    811654145U,	// Int_VCVTSI2SSZrr
    283204609U,	// Int_VCVTSI2SSrm
    811654145U,	// Int_VCVTSI2SSrr
    283284796U,	// Int_VCVTSS2SDrm
    811652412U,	// Int_VCVTSS2SDrr
    591246U,	// Int_VCVTTSD2SI64Zrm
    551814542U,	// Int_VCVTTSD2SI64Zrr
    596117U,	// Int_VCVTTSD2SI64rm
    551819413U,	// Int_VCVTTSD2SI64rr
    591246U,	// Int_VCVTTSD2SIZrm
    551814542U,	// Int_VCVTTSD2SIZrr
    596117U,	// Int_VCVTTSD2SIrm
    551819413U,	// Int_VCVTTSD2SIrr
    591296U,	// Int_VCVTTSD2USI64Zrm
    551814592U,	// Int_VCVTTSD2USI64Zrr
    591296U,	// Int_VCVTTSD2USIZrm
    551814592U,	// Int_VCVTTSD2USIZrr
    607655U,	// Int_VCVTTSS2SI64Zrm
    551814567U,	// Int_VCVTTSS2SI64Zrr
    612524U,	// Int_VCVTTSS2SI64rm
    551819436U,	// Int_VCVTTSS2SI64rr
    607655U,	// Int_VCVTTSS2SIZrm
    551814567U,	// Int_VCVTTSS2SIZrr
    612524U,	// Int_VCVTTSS2SIrm
    551819436U,	// Int_VCVTTSS2SIrr
    607707U,	// Int_VCVTTSS2USI64Zrm
    551814619U,	// Int_VCVTTSS2USI64Zrr
    607707U,	// Int_VCVTTSS2USIZrm
    551814619U,	// Int_VCVTTSS2USIZrr
    283221911U,	// Int_VCVTUSI2SD64Zrm
    811655063U,	// Int_VCVTUSI2SD64Zrr
    283203973U,	// Int_VCVTUSI2SDZrm
    811653509U,	// Int_VCVTUSI2SDZrr
    283222517U,	// Int_VCVTUSI2SS64Zrm
    811655669U,	// Int_VCVTUSI2SS64Zrr
    283204621U,	// Int_VCVTUSI2SSZrm
    811654157U,	// Int_VCVTUSI2SSZrr
    579017U,	// Int_VUCOMISDZrm
    551818697U,	// Int_VUCOMISDZrr
    579017U,	// Int_VUCOMISDrm
    551818697U,	// Int_VUCOMISDrr
    583778U,	// Int_VUCOMISSZrm
    551823458U,	// Int_VUCOMISSZrr
    583778U,	// Int_VUCOMISSrm
    551823458U,	// Int_VUCOMISSrr
    415548U,	// JAE_1
    415548U,	// JAE_2
    415548U,	// JAE_4
    413261U,	// JA_1
    413261U,	// JA_2
    413261U,	// JA_4
    415560U,	// JBE_1
    415560U,	// JBE_2
    415560U,	// JBE_4
    413429U,	// JB_1
    413429U,	// JB_2
    413429U,	// JB_4
    421551U,	// JCXZ
    421544U,	// JECXZ_32
    421544U,	// JECXZ_64
    415619U,	// JE_1
    415619U,	// JE_2
    415619U,	// JE_4
    415598U,	// JGE_1
    415598U,	// JGE_2
    415598U,	// JGE_4
    415712U,	// JG_1
    415712U,	// JG_2
    415712U,	// JG_4
    415623U,	// JLE_1
    415623U,	// JLE_2
    415623U,	// JLE_4
    416313U,	// JL_1
    416313U,	// JL_2
    416313U,	// JL_4
    226758U,	// JMP16m
    30150U,	// JMP16r
    243108U,	// JMP32m
    30116U,	// JMP32r
    406965U,	// JMP64m
    30133U,	// JMP64r
    417166U,	// JMP_1
    417166U,	// JMP_2
    417166U,	// JMP_4
    415635U,	// JNE_1
    415635U,	// JNE_2
    415635U,	// JNE_4
    417116U,	// JNO_1
    417116U,	// JNO_2
    417116U,	// JNO_4
    417186U,	// JNP_1
    417186U,	// JNP_2
    417186U,	// JNP_4
    418842U,	// JNS_1
    418842U,	// JNS_2
    418842U,	// JNS_4
    417112U,	// JO_1
    417112U,	// JO_2
    417112U,	// JO_4
    417155U,	// JP_1
    417155U,	// JP_2
    417155U,	// JP_4
    421557U,	// JRCXZ
    418808U,	// JS_1
    418808U,	// JS_2
    418808U,	// JS_4
    811647013U,	// KANDBrr
    811647227U,	// KANDDrr
    811647054U,	// KANDNBrr
    811647353U,	// KANDNDrr
    811649018U,	// KANDNQrr
    811650108U,	// KANDNWrr
    811648768U,	// KANDQrr
    811650056U,	// KANDWrr
    551813272U,	// KMOVBkk
    442520U,	// KMOVBkm
    551813272U,	// KMOVBkr
    23101592U,	// KMOVBmk
    551813272U,	// KMOVBrk
    551814498U,	// KMOVDkk
    551798114U,	// KMOVDkm
    551814498U,	// KMOVDkr
    12617058U,	// KMOVDmk
    551814498U,	// KMOVDrk
    551815413U,	// KMOVQkk
    551831797U,	// KMOVQkm
    551815413U,	// KMOVQkr
    18909429U,	// KMOVQmk
    551815413U,	// KMOVQrk
    551816348U,	// KMOVWkk
    380060U,	// KMOVWkm
    551816348U,	// KMOVWkr
    4230300U,	// KMOVWmk
    551816348U,	// KMOVWrk
    551813264U,	// KNOTBrr
    551814425U,	// KNOTDrr
    551815340U,	// KNOTQrr
    551816329U,	// KNOTWrr
    811647096U,	// KORBrr
    811648042U,	// KORDrr
    811649074U,	// KORQrr
    551816337U,	// KORTESTWrr
    811650150U,	// KORWrr
    0U,	// KSET0B
    0U,	// KSET0W
    0U,	// KSET1B
    0U,	// KSET1W
    811650097U,	// KSHIFTLWri
    811650174U,	// KSHIFTRWri
    811650045U,	// KUNPCKBWrr
    811647103U,	// KXNORBrr
    811648049U,	// KXNORDrr
    811649081U,	// KXNORQrr
    811650157U,	// KXNORWrr
    811647112U,	// KXORBrr
    811648066U,	// KXORDrr
    811649098U,	// KXORQrr
    811650166U,	// KXORWrr
    14409U,	// LAHF
    388175U,	// LAR16rm
    551824463U,	// LAR16rr
    383835U,	// LAR32rm
    551820123U,	// LAR32rr
    385365U,	// LAR64rm
    551821653U,	// LAR64rr
    4238164U,	// LCMPXCHG16
    560768U,	// LCMPXCHG16B
    12622345U,	// LCMPXCHG32
    18915426U,	// LCMPXCHG64
    23105251U,	// LCMPXCHG8
    396940U,	// LCMPXCHG8B
    321932U,	// LDDQUrm
    238502U,	// LDMXCSR
    699638U,	// LDS16rm
    695250U,	// LDS32rm
    15559U,	// LD_F0
    13779U,	// LD_F1
    189419U,	// LD_F32m
    203093U,	// LD_F64m
    715010U,	// LD_F80m
    0U,	// LD_Fp032
    0U,	// LD_Fp064
    0U,	// LD_Fp080
    0U,	// LD_Fp132
    0U,	// LD_Fp164
    0U,	// LD_Fp180
    0U,	// LD_Fp32m
    0U,	// LD_Fp32m64
    0U,	// LD_Fp32m80
    0U,	// LD_Fp64m
    0U,	// LD_Fp64m80
    0U,	// LD_Fp80m
    20874U,	// LD_Frr
    387544U,	// LEA16r
    551803098U,	// LEA32r
    551803098U,	// LEA64_32r
    551837220U,	// LEA64r
    14396U,	// LEAVE
    14396U,	// LEAVE64
    699651U,	// LES16rm
    695263U,	// LES32rm
    14291U,	// LFENCE
    699657U,	// LFS16rm
    695269U,	// LFS32rm
    696781U,	// LFS64rm
    634238U,	// LGDT16m
    629818U,	// LGDT32m
    631323U,	// LGDT64m
    699663U,	// LGS16rm
    695275U,	// LGS32rm
    696787U,	// LGS64rm
    634252U,	// LIDT16m
    629832U,	// LIDT32m
    631337U,	// LIDT64m
    224666U,	// LLDT16m
    28058U,	// LLDT16r
    224830U,	// LMSW16m
    28222U,	// LMSW16r
    4237999U,	// LOCK_ADD16mi
    4237999U,	// LOCK_ADD16mi8
    4237999U,	// LOCK_ADD16mr
    12622135U,	// LOCK_ADD32mi
    12622135U,	// LOCK_ADD32mi8
    12622135U,	// LOCK_ADD32mr
    18915055U,	// LOCK_ADD64mi32
    18915055U,	// LOCK_ADD64mi8
    18915055U,	// LOCK_ADD64mr
    23105217U,	// LOCK_ADD8mi
    23105217U,	// LOCK_ADD8mr
    4238039U,	// LOCK_AND16mi
    4238039U,	// LOCK_AND16mi8
    4238039U,	// LOCK_AND16mr
    12622188U,	// LOCK_AND32mi
    12622188U,	// LOCK_AND32mi8
    12622188U,	// LOCK_AND32mr
    18915148U,	// LOCK_AND64mi32
    18915148U,	// LOCK_AND64mi8
    18915148U,	// LOCK_AND64mr
    23105230U,	// LOCK_AND8mi
    23105230U,	// LOCK_AND8mr
    223890U,	// LOCK_DEC16m
    235803U,	// LOCK_DEC32m
    401041U,	// LOCK_DEC64m
    429747U,	// LOCK_DEC8m
    223896U,	// LOCK_INC16m
    235809U,	// LOCK_INC32m
    401047U,	// LOCK_INC64m
    429753U,	// LOCK_INC8m
    4238455U,	// LOCK_OR16mi
    4238455U,	// LOCK_OR16mi8
    4238455U,	// LOCK_OR16mr
    12622725U,	// LOCK_OR32mi
    12622725U,	// LOCK_OR32mi8
    12622725U,	// LOCK_OR32mr
    18915694U,	// LOCK_OR64mi32
    18915694U,	// LOCK_OR64mi8
    18915694U,	// LOCK_OR64mr
    23105383U,	// LOCK_OR8mi
    23105383U,	// LOCK_OR8mr
    14467U,	// LOCK_PREFIX
    4237905U,	// LOCK_SUB16mi
    4237905U,	// LOCK_SUB16mi8
    4237905U,	// LOCK_SUB16mr
    12622071U,	// LOCK_SUB32mi
    12622071U,	// LOCK_SUB32mi8
    12622071U,	// LOCK_SUB32mr
    18914911U,	// LOCK_SUB64mi32
    18914911U,	// LOCK_SUB64mi8
    18914911U,	// LOCK_SUB64mr
    23105191U,	// LOCK_SUB8mi
    23105191U,	// LOCK_SUB8mr
    4238460U,	// LOCK_XOR16mi
    4238460U,	// LOCK_XOR16mi8
    4238460U,	// LOCK_XOR16mr
    12622730U,	// LOCK_XOR32mi
    12622730U,	// LOCK_XOR32mi8
    12622730U,	// LOCK_XOR32mr
    18915709U,	// LOCK_XOR64mi32
    18915709U,	// LOCK_XOR64mi8
    18915709U,	// LOCK_XOR64mr
    23105388U,	// LOCK_XOR8mi
    23105388U,	// LOCK_XOR8mr
    21696430U,	// LODSB
    11230168U,	// LODSL
    762310U,	// LODSQ
    2878716U,	// LODSW
    417203U,	// LOOP
    415664U,	// LOOPE
    415640U,	// LOOPNE
    23645U,	// LRETIL
    25150U,	// LRETIQ
    28072U,	// LRETIW
    14569U,	// LRETL
    14779U,	// LRETQ
    15339U,	// LRETW
    388063U,	// LSL16rm
    551824351U,	// LSL16rr
    551803539U,	// LSL32rm
    551819923U,	// LSL32rr
    551837900U,	// LSL64rm
    551821516U,	// LSL64rr
    699705U,	// LSS16rm
    695322U,	// LSS32rm
    696834U,	// LSS64rm
    224407U,	// LTRm
    27799U,	// LTRr
    3504614077U,	// LXADD16
    3773045061U,	// LXADD32
    4041481973U,	// LXADD64
    14945991U,	// LXADD8
    388546U,	// LZCNT16rm
    551824834U,	// LZCNT16rr
    551804019U,	// LZCNT32rm
    551820403U,	// LZCNT32rr
    551838296U,	// LZCNT64rm
    551821912U,	// LZCNT64rr
    551823764U,	// MASKMOVDQU
    551823764U,	// MASKMOVDQU64
    8524952U,	// MAXCPDrm
    8443032U,	// MAXCPDrr
    8529727U,	// MAXCPSrm
    8447807U,	// MAXCPSrr
    551704111U,	// MAXCSDrm
    8443439U,	// MAXCSDrr
    551725264U,	// MAXCSSrm
    8448208U,	// MAXCSSrr
    8524952U,	// MAXPDrm
    8443032U,	// MAXPDrr
    8529727U,	// MAXPSrm
    8447807U,	// MAXPSrr
    551704111U,	// MAXSDrm
    551704111U,	// MAXSDrm_Int
    8443439U,	// MAXSDrr
    8443439U,	// MAXSDrr_Int
    551725264U,	// MAXSSrm
    551725264U,	// MAXSSrm_Int
    8448208U,	// MAXSSrr
    8448208U,	// MAXSSrr_Int
    14298U,	// MFENCE
    8524834U,	// MINCPDrm
    8442914U,	// MINCPDrr
    8529544U,	// MINCPSrm
    8447624U,	// MINCPSrr
    551704038U,	// MINCSDrm
    8443366U,	// MINCSDrr
    551725182U,	// MINCSSrm
    8448126U,	// MINCSSrr
    8524834U,	// MINPDrm
    8442914U,	// MINPDrr
    8529544U,	// MINPSrm
    8447624U,	// MINPSrr
    551704038U,	// MINSDrm
    551704038U,	// MINSDrm_Int
    8443366U,	// MINSDrr
    8443366U,	// MINSDrr_Int
    551725182U,	// MINSSrm
    551725182U,	// MINSSrm_Int
    8448126U,	// MINSSrr
    8448126U,	// MINSSrr_Int
    579661U,	// MMX_CVTPD2PIirm
    551819341U,	// MMX_CVTPD2PIirr
    551834252U,	// MMX_CVTPI2PDirm
    551817868U,	// MMX_CVTPI2PDirr
    551658728U,	// MMX_CVTPI2PSirm
    8447208U,	// MMX_CVTPI2PSirr
    596066U,	// MMX_CVTPS2PIirm
    551819362U,	// MMX_CVTPS2PIirr
    579650U,	// MMX_CVTTPD2PIirm
    551819330U,	// MMX_CVTTPD2PIirr
    596055U,	// MMX_CVTTPS2PIirm
    551819351U,	// MMX_CVTTPS2PIirr
    15144U,	// MMX_EMMS
    551822043U,	// MMX_MASKMOVQ
    551822043U,	// MMX_MASKMOVQ64
    551818924U,	// MMX_MOVD64from64rr
    551818924U,	// MMX_MOVD64grr
    12621484U,	// MMX_MOVD64mr
    551802540U,	// MMX_MOVD64rm
    551818924U,	// MMX_MOVD64rr
    551818924U,	// MMX_MOVD64to64rr
    551820827U,	// MMX_MOVDQ2Qrr
    551820827U,	// MMX_MOVFR642Qrr
    18915944U,	// MMX_MOVNTQmr
    551820986U,	// MMX_MOVQ2DQrr
    551820986U,	// MMX_MOVQ2FR64rr
    18916063U,	// MMX_MOVQ64mr
    551838431U,	// MMX_MOVQ64rm
    551822047U,	// MMX_MOVQ64rr
    551822047U,	// MMX_MOVQ64rr_REV
    551833484U,	// MMX_PABSBrm64
    551817100U,	// MMX_PABSBrr64
    551835006U,	// MMX_PABSDrm64
    551818622U,	// MMX_PABSDrr64
    551840948U,	// MMX_PABSWrm64
    551824564U,	// MMX_PABSWrr64
    551660271U,	// MMX_PACKSSDWirm
    8448751U,	// MMX_PACKSSDWirr
    551653481U,	// MMX_PACKSSWBirm
    8441961U,	// MMX_PACKSSWBirr
    551653492U,	// MMX_PACKUSWBirm
    8441972U,	// MMX_PACKUSWBirr
    551653056U,	// MMX_PADDBirm
    8441536U,	// MMX_PADDBirr
    551653673U,	// MMX_PADDDirm
    8442153U,	// MMX_PADDDirr
    551657198U,	// MMX_PADDQirm
    8445678U,	// MMX_PADDQirr
    551653286U,	// MMX_PADDSBirm
    8441766U,	// MMX_PADDSBirr
    551660782U,	// MMX_PADDSWirm
    8449262U,	// MMX_PADDSWirr
    551653335U,	// MMX_PADDUSBirm
    8441815U,	// MMX_PADDUSBirr
    551660895U,	// MMX_PADDUSWirm
    8449375U,	// MMX_PADDUSWirr
    551660214U,	// MMX_PADDWirm
    8448694U,	// MMX_PADDWirr
    593847160U,	// MMX_PALIGNR64irm
    570794872U,	// MMX_PALIGNR64irr
    551656763U,	// MMX_PANDNirm
    8445243U,	// MMX_PANDNirr
    551653834U,	// MMX_PANDirm
    8442314U,	// MMX_PANDirr
    551653102U,	// MMX_PAVGBirm
    8441582U,	// MMX_PAVGBirr
    551660383U,	// MMX_PAVGWirm
    8448863U,	// MMX_PAVGWirr
    551653195U,	// MMX_PCMPEQBirm
    8441675U,	// MMX_PCMPEQBirr
    551654569U,	// MMX_PCMPEQDirm
    8443049U,	// MMX_PCMPEQDirr
    551660614U,	// MMX_PCMPEQWirm
    8449094U,	// MMX_PCMPEQWirr
    551653376U,	// MMX_PCMPGTBirm
    8441856U,	// MMX_PCMPGTBirr
    551654984U,	// MMX_PCMPGTDirm
    8443464U,	// MMX_PCMPGTDirr
    551660976U,	// MMX_PCMPGTWirm
    8449456U,	// MMX_PCMPGTWirr
    811658404U,	// MMX_PEXTRWirri
    551660772U,	// MMX_PHADDSWrm64
    8449252U,	// MMX_PHADDSWrr64
    551660205U,	// MMX_PHADDWrm64
    8448685U,	// MMX_PHADDWrr64
    551653664U,	// MMX_PHADDrm64
    8442144U,	// MMX_PHADDrr64
    551653618U,	// MMX_PHSUBDrm64
    8442098U,	// MMX_PHSUBDrr64
    551660753U,	// MMX_PHSUBSWrm64
    8449233U,	// MMX_PHSUBSWrr64
    551660111U,	// MMX_PHSUBWrm64
    8448591U,	// MMX_PHSUBWrr64
    595946633U,	// MMX_PINSRWirmi
    570797193U,	// MMX_PINSRWirri
    551660741U,	// MMX_PMADDUBSWrm64
    8449221U,	// MMX_PMADDUBSWrr64
    551655118U,	// MMX_PMADDWDirm
    8443598U,	// MMX_PMADDWDirr
    551660913U,	// MMX_PMAXSWirm
    8449393U,	// MMX_PMAXSWirr
    551653446U,	// MMX_PMAXUBirm
    8441926U,	// MMX_PMAXUBirr
    551660822U,	// MMX_PMINSWirm
    8449302U,	// MMX_PMINSWirr
    551653430U,	// MMX_PMINUBirm
    8441910U,	// MMX_PMINUBirr
    551816954U,	// MMX_PMOVMSKBrr
    551660847U,	// MMX_PMULHRSWrm64
    8449327U,	// MMX_PMULHRSWrr64
    551661046U,	// MMX_PMULHUWirm
    8449526U,	// MMX_PMULHUWirr
    551660420U,	// MMX_PMULHWirm
    8448900U,	// MMX_PMULHWirr
    551660489U,	// MMX_PMULLWirm
    8448969U,	// MMX_PMULLWirr
    551657402U,	// MMX_PMULUDQirm
    8445882U,	// MMX_PMULUDQirr
    551658370U,	// MMX_PORirm
    8446850U,	// MMX_PORirr
    551660040U,	// MMX_PSADBWirm
    8448520U,	// MMX_PSADBWirr
    551653077U,	// MMX_PSHUFBrm64
    8441557U,	// MMX_PSHUFBrr64
    835005254U,	// MMX_PSHUFWmi
    811658054U,	// MMX_PSHUFWri
    551653166U,	// MMX_PSIGNBrm64
    8441646U,	// MMX_PSIGNBrr64
    551653841U,	// MMX_PSIGNDrm64
    8442321U,	// MMX_PSIGNDrr64
    551660541U,	// MMX_PSIGNWrm64
    8449021U,	// MMX_PSIGNWrr64
    8442264U,	// MMX_PSLLDri
    551653784U,	// MMX_PSLLDrm
    8442264U,	// MMX_PSLLDrr
    8446135U,	// MMX_PSLLQri
    551657655U,	// MMX_PSLLQrm
    8446135U,	// MMX_PSLLQrr
    8448961U,	// MMX_PSLLWri
    551660481U,	// MMX_PSLLWrm
    8448961U,	// MMX_PSLLWrr
    8442069U,	// MMX_PSRADri
    551653589U,	// MMX_PSRADrm
    8442069U,	// MMX_PSRADrr
    8448487U,	// MMX_PSRAWri
    551660007U,	// MMX_PSRAWrm
    8448487U,	// MMX_PSRAWrr
    8442281U,	// MMX_PSRLDri
    551653801U,	// MMX_PSRLDrm
    8442281U,	// MMX_PSRLDrr
    8446149U,	// MMX_PSRLQri
    551657669U,	// MMX_PSRLQrm
    8446149U,	// MMX_PSRLQrr
    8448984U,	// MMX_PSRLWri
    551660504U,	// MMX_PSRLWrm
    8448984U,	// MMX_PSRLWrr
    551653030U,	// MMX_PSUBBirm
    8441510U,	// MMX_PSUBBirr
    551653627U,	// MMX_PSUBDirm
    8442107U,	// MMX_PSUBDirr
    551657054U,	// MMX_PSUBQirm
    8445534U,	// MMX_PSUBQirr
    551653277U,	// MMX_PSUBSBirm
    8441757U,	// MMX_PSUBSBirr
    551660763U,	// MMX_PSUBSWirm
    8449243U,	// MMX_PSUBSWirr
    551653325U,	// MMX_PSUBUSBirm
    8441805U,	// MMX_PSUBUSBirr
    551660885U,	// MMX_PSUBUSWirm
    8449365U,	// MMX_PSUBUSWirr
    551660120U,	// MMX_PSUBWirm
    8448600U,	// MMX_PSUBWirr
    551660068U,	// MMX_PUNPCKHBWirm
    8448548U,	// MMX_PUNPCKHBWirr
    551657232U,	// MMX_PUNPCKHDQirm
    8445712U,	// MMX_PUNPCKHDQirr
    551655128U,	// MMX_PUNPCKHWDirm
    8443608U,	// MMX_PUNPCKHWDirr
    551660080U,	// MMX_PUNPCKLBWirm
    8448560U,	// MMX_PUNPCKLBWirr
    551657251U,	// MMX_PUNPCKLDQirm
    8445731U,	// MMX_PUNPCKLDQirr
    551655140U,	// MMX_PUNPCKLWDirm
    8443620U,	// MMX_PUNPCKLWDirr
    551658393U,	// MMX_PXORirm
    8446873U,	// MMX_PXORirr
    0U,	// MONITOR
    14827U,	// MONITORrrr
    14597U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    799342U,	// MOV16ao16
    799342U,	// MOV16ao16_16
    4238894U,	// MOV16mi
    4238894U,	// MOV16mr
    4238894U,	// MOV16ms
    2895406U,	// MOV16o16a
    2895406U,	// MOV16o16a_16
    551824942U,	// MOV16ri
    551824942U,	// MOV16ri_alt
    388654U,	// MOV16rm
    551824942U,	// MOV16rr
    551824942U,	// MOV16rr_REV
    551824942U,	// MOV16rs
    388654U,	// MOV16sm
    551824942U,	// MOV16sr
    815777U,	// MOV32ao32
    815777U,	// MOV32ao32_16
    551820474U,	// MOV32cr
    551820474U,	// MOV32dr
    12623034U,	// MOV32mi
    12623034U,	// MOV32mr
    4234426U,	// MOV32ms
    11295930U,	// MOV32o32a
    11295930U,	// MOV32o32a_16
    0U,	// MOV32r0
    551820474U,	// MOV32rc
    551820474U,	// MOV32rd
    551820474U,	// MOV32ri
    0U,	// MOV32ri64
    551820474U,	// MOV32ri_alt
    551804090U,	// MOV32rm
    551820474U,	// MOV32rr
    551820474U,	// MOV32rr_REV
    551820474U,	// MOV32rs
    384186U,	// MOV32sm
    551820474U,	// MOV32sr
    799305U,	// MOV64ao16
    815737U,	// MOV64ao32
    832173U,	// MOV64ao64
    847985U,	// MOV64ao8
    551822047U,	// MOV64cr
    551822047U,	// MOV64dr
    18916063U,	// MOV64mi32
    18916063U,	// MOV64mr
    4235999U,	// MOV64ms
    2895035U,	// MOV64o16a
    11295689U,	// MOV64o32a
    17605053U,	// MOV64o64a
    21811091U,	// MOV64o8a
    551822047U,	// MOV64rc
    551822047U,	// MOV64rd
    551821757U,	// MOV64ri
    551822047U,	// MOV64ri32
    551838431U,	// MOV64rm
    551822047U,	// MOV64rr
    551822047U,	// MOV64rr_REV
    551822047U,	// MOV64rs
    385759U,	// MOV64sm
    551822047U,	// MOV64sr
    551818924U,	// MOV64toPQIrr
    551838431U,	// MOV64toSDrm
    551818924U,	// MOV64toSDrr
    848022U,	// MOV8ao8
    848022U,	// MOV8ao8_16
    23105634U,	// MOV8mi
    23105634U,	// MOV8mr
    291541090U,	// MOV8mr_NOREX
    21811298U,	// MOV8o8a
    21811298U,	// MOV8o8a_16
    551817314U,	// MOV8ri
    551817314U,	// MOV8ri_alt
    446562U,	// MOV8rm
    61263970U,	// MOV8rm_NOREX
    551817314U,	// MOV8rr
    551817314U,	// MOV8rr_NOREX
    551817314U,	// MOV8rr_REV
    62952207U,	// MOVAPDmr
    578319U,	// MOVAPDrm
    551817999U,	// MOVAPDrr
    551817999U,	// MOVAPDrr_REV
    62956896U,	// MOVAPSmr
    583008U,	// MOVAPSrm
    551822688U,	// MOVAPSrr
    551822688U,	// MOVAPSrr_REV
    4238094U,	// MOVBE16mr
    387854U,	// MOVBE16rm
    12622236U,	// MOVBE32mr
    551803292U,	// MOVBE32rm
    18915299U,	// MOVBE64mr
    551837667U,	// MOVBE64rm
    597469U,	// MOVDDUPrm
    551820765U,	// MOVDDUPrr
    551802540U,	// MOVDI2PDIrm
    551818924U,	// MOVDI2PDIrr
    551802540U,	// MOVDI2SSrm
    551818924U,	// MOVDI2SSrr
    65048157U,	// MOVDQAmr
    314973U,	// MOVDQArm
    551816797U,	// MOVDQArr
    551816797U,	// MOVDQArr_REV
    65055128U,	// MOVDQUmr
    321944U,	// MOVDQUrm
    551823768U,	// MOVDQUrr
    551823768U,	// MOVDQUrr_REV
    8447540U,	// MOVHLPSrr
    67146701U,	// MOVHPDmr
    551703501U,	// MOVHPDrm
    67151392U,	// MOVHPSmr
    551708192U,	// MOVHPSrm
    8447510U,	// MOVLHPSrr
    67146751U,	// MOVLPDmr
    551703551U,	// MOVLPDrm
    67151452U,	// MOVLPSmr
    551708252U,	// MOVLPSrm
    551818198U,	// MOVMSKPDrr
    551822889U,	// MOVMSKPSrr
    314962U,	// MOVNTDQArm
    62955429U,	// MOVNTDQmr
    18915465U,	// MOVNTI_64mr
    12622384U,	// MOVNTImr
    62952532U,	// MOVNTPDmr
    62957262U,	// MOVNTPSmr
    67147261U,	// MOVNTSD
    69249173U,	// MOVNTSS
    0U,	// MOVPC32r
    12621484U,	// MOVPDI2DImr
    551818924U,	// MOVPDI2DIrr
    18916063U,	// MOVPQI2QImr
    551822047U,	// MOVPQI2QIrr
    551818924U,	// MOVPQIto64rr
    551838431U,	// MOVQI2PQIrm
    856041U,	// MOVSB
    67147302U,	// MOVSDmr
    595494U,	// MOVSDrm
    8443430U,	// MOVSDrr
    8443430U,	// MOVSDrr_REV
    18916063U,	// MOVSDto64mr
    551818924U,	// MOVSDto64rr
    581095U,	// MOVSHDUPrm
    551820775U,	// MOVSHDUPrr
    875566U,	// MOVSL
    581106U,	// MOVSLDUPrm
    551820786U,	// MOVSLDUPrr
    893455U,	// MOVSQ
    12621484U,	// MOVSS2DImr
    551818924U,	// MOVSS2DIrr
    69249224U,	// MOVSSmr
    616648U,	// MOVSSrm
    8448200U,	// MOVSSrr
    8448200U,	// MOVSSrr_REV
    912745U,	// MOVSW
    453179U,	// MOVSX16rm8
    551823931U,	// MOVSX16rr8
    384199U,	// MOVSX32rm16
    448750U,	// MOVSX32rm8
    551820487U,	// MOVSX32rr16
    551819502U,	// MOVSX32rr8
    551821522U,	// MOVSX64_NOREXrr32
    385789U,	// MOVSX64rm16
    551805138U,	// MOVSX64rm32
    450122U,	// MOVSX64rm8
    551822077U,	// MOVSX64rr16
    551821522U,	// MOVSX64rr32
    551820874U,	// MOVSX64rr8
    62952560U,	// MOVUPDmr
    578672U,	// MOVUPDrm
    551818352U,	// MOVUPDrr
    551818352U,	// MOVUPDrr_REV
    62957335U,	// MOVUPSmr
    583447U,	// MOVUPSrm
    551823127U,	// MOVUPSrr
    551823127U,	// MOVUPSrr_REV
    320223U,	// MOVZPQILo2PQIrm
    551822047U,	// MOVZPQILo2PQIrr
    551838431U,	// MOVZQI2PQIrm
    551818924U,	// MOVZQI2PQIrr
    453245U,	// MOVZX16rm8
    551823997U,	// MOVZX16rr8
    448781U,	// MOVZX32_NOREXrm8
    551819533U,	// MOVZX32_NOREXrr8
    384207U,	// MOVZX32rm16
    448781U,	// MOVZX32rm8
    551820495U,	// MOVZX32rr16
    551819533U,	// MOVZX32rr8
    385830U,	// MOVZX64rm16_Q
    450179U,	// MOVZX64rm8_Q
    551822118U,	// MOVZX64rr16_Q
    551820931U,	// MOVZX64rr8_Q
    608528903U,	// MPSADBWrmi
    570796551U,	// MPSADBWrri
    224230U,	// MUL16m
    27622U,	// MUL16r
    236186U,	// MUL32m
    23194U,	// MUL32r
    401627U,	// MUL64m
    24795U,	// MUL64r
    429855U,	// MUL8m
    20255U,	// MUL8r
    8524791U,	// MULPDrm
    8442871U,	// MULPDrr
    8529492U,	// MULPSrm
    8447572U,	// MULPSrr
    551704029U,	// MULSDrm
    551704029U,	// MULSDrm_Int
    8443357U,	// MULSDrr
    8443357U,	// MULSDrr_Int
    551725174U,	// MULSSrm
    551725174U,	// MULSSrm_Int
    8448118U,	// MULSSrr
    8448118U,	// MULSSrr_Int
    283204837U,	// MULX32rm
    811654373U,	// MULX32rr
    283222844U,	// MULX64rm
    811655996U,	// MULX64rr
    189436U,	// MUL_F32m
    203417U,	// MUL_F64m
    222211U,	// MUL_FI16m
    236192U,	// MUL_FI32m
    23943U,	// MUL_FPrST0
    23717U,	// MUL_FST0r
    0U,	// MUL_Fp32
    0U,	// MUL_Fp32m
    0U,	// MUL_Fp64
    0U,	// MUL_Fp64m
    0U,	// MUL_Fp64m32
    0U,	// MUL_Fp80
    0U,	// MUL_Fp80m32
    0U,	// MUL_Fp80m64
    0U,	// MUL_FpI16m32
    0U,	// MUL_FpI16m64
    0U,	// MUL_FpI16m80
    0U,	// MUL_FpI32m32
    0U,	// MUL_FpI32m64
    0U,	// MUL_FpI32m80
    28393U,	// MUL_FrST0
    15220U,	// MWAITrr
    224078U,	// NEG16m
    27470U,	// NEG16r
    236035U,	// NEG32m
    23043U,	// NEG32r
    401500U,	// NEG64m
    24668U,	// NEG64r
    429789U,	// NEG8m
    20189U,	// NEG8r
    14705U,	// NOOP
    224305U,	// NOOP18_16m4
    224305U,	// NOOP18_16m5
    224305U,	// NOOP18_16m6
    224305U,	// NOOP18_16m7
    27697U,	// NOOP18_16r4
    27697U,	// NOOP18_16r5
    27697U,	// NOOP18_16r6
    27697U,	// NOOP18_16r7
    236306U,	// NOOP18_m4
    236306U,	// NOOP18_m5
    236306U,	// NOOP18_m6
    236306U,	// NOOP18_m7
    23314U,	// NOOP18_r4
    23314U,	// NOOP18_r5
    23314U,	// NOOP18_r6
    23314U,	// NOOP18_r7
    283139502U,	// NOOP19rr
    236306U,	// NOOPL
    236306U,	// NOOPL_19
    236306U,	// NOOPL_1a
    236306U,	// NOOPL_1b
    236306U,	// NOOPL_1c
    236306U,	// NOOPL_1d
    236306U,	// NOOPL_1e
    224305U,	// NOOPW
    224305U,	// NOOPW_19
    224305U,	// NOOPW_1a
    224305U,	// NOOPW_1b
    224305U,	// NOOPW_1c
    224305U,	// NOOPW_1d
    224305U,	// NOOPW_1e
    224722U,	// NOT16m
    28114U,	// NOT16r
    236675U,	// NOT32m
    23683U,	// NOT32r
    402032U,	// NOT64m
    25200U,	// NOT64r
    430089U,	// NOT8m
    20489U,	// NOT8r
    2124919U,	// OR16i16
    4238455U,	// OR16mi
    4238455U,	// OR16mi8
    4238455U,	// OR16mr
    6351991U,	// OR16ri
    6351991U,	// OR16ri8
    6368375U,	// OR16rm
    6351991U,	// OR16rr
    8449143U,	// OR16rr_REV
    10509189U,	// OR32i32
    12622725U,	// OR32mi
    12622725U,	// OR32mi8
    12622725U,	// OR32mr
    12622725U,	// OR32mrLocked
    6347653U,	// OR32ri
    6347653U,	// OR32ri8
    283204485U,	// OR32rm
    6347653U,	// OR32rr
    8444805U,	// OR32rr_REV
    16802158U,	// OR64i32
    18915694U,	// OR64mi32
    18915694U,	// OR64mi8
    18915694U,	// OR64mr
    6349166U,	// OR64ri32
    6349166U,	// OR64ri8
    283222382U,	// OR64rm
    6349166U,	// OR64rr
    8446318U,	// OR64rr_REV
    20991847U,	// OR8i8
    23105383U,	// OR8mi
    23105383U,	// OR8mr
    6344551U,	// OR8ri
    6344551U,	// OR8ri8
    118631U,	// OR8rm
    6344551U,	// OR8rr
    8441703U,	// OR8rr_REV
    8524869U,	// ORPDrm
    8442949U,	// ORPDrr
    8529587U,	// ORPSrm
    8447667U,	// ORPSrr
    29283U,	// OUT16ir
    15523U,	// OUT16rr
    29333U,	// OUT32ir
    15537U,	// OUT32rr
    28811U,	// OUT8ir
    15509U,	// OUT8rr
    74125253U,	// OUTSB
    74144806U,	// OUTSL
    74181965U,	// OUTSW
    315276U,	// PABSBrm128
    551817100U,	// PABSBrr128
    316798U,	// PABSDrm128
    551818622U,	// PABSDrr128
    322740U,	// PABSWrm128
    551824564U,	// PABSWrr128
    8694511U,	// PACKSSDWrm
    8448751U,	// PACKSSDWrr
    8687721U,	// PACKSSWBrm
    8441961U,	// PACKSSWBrr
    8694522U,	// PACKUSDWrm
    8448762U,	// PACKUSDWrr
    8687732U,	// PACKUSWBrm
    8441972U,	// PACKUSWBrr
    8687296U,	// PADDBrm
    8441536U,	// PADDBrr
    8687913U,	// PADDDrm
    8442153U,	// PADDDrr
    8691438U,	// PADDQrm
    8445678U,	// PADDQrr
    8687526U,	// PADDSBrm
    8441766U,	// PADDSBrr
    8695022U,	// PADDSWrm
    8449262U,	// PADDSWrr
    8687575U,	// PADDUSBrm
    8441815U,	// PADDUSBrr
    8695135U,	// PADDUSWrm
    8449375U,	// PADDUSWrr
    8694454U,	// PADDWrm
    8448694U,	// PADDWrr
    608527224U,	// PALIGNR128rm
    570794872U,	// PALIGNR128rr
    8691003U,	// PANDNrm
    8445243U,	// PANDNrr
    8688074U,	// PANDrm
    8442314U,	// PANDrr
    14359U,	// PAUSE
    8687342U,	// PAVGBrm
    8441582U,	// PAVGBrr
    551653344U,	// PAVGUSBrm
    8441824U,	// PAVGUSBrr
    8694623U,	// PAVGWrm
    8448863U,	// PAVGWrr
    8687695U,	// PBLENDVBrm0
    8441935U,	// PBLENDVBrr0
    608529118U,	// PBLENDWrmi
    570796766U,	// PBLENDWrri
    608526189U,	// PCLMULQDQrm
    570793837U,	// PCLMULQDQrr
    8687435U,	// PCMPEQBrm
    8441675U,	// PCMPEQBrr
    8688809U,	// PCMPEQDrm
    8443049U,	// PCMPEQDrr
    8692032U,	// PCMPEQQrm
    8446272U,	// PCMPEQQrr
    8694854U,	// PCMPEQWrm
    8449094U,	// PCMPEQWrr
    0U,	// PCMPESTRIMEM
    0U,	// PCMPESTRIREG
    25499774U,	// PCMPESTRIrm
    811653246U,	// PCMPESTRIrr
    0U,	// PCMPESTRM128MEM
    0U,	// PCMPESTRM128REG
    25500963U,	// PCMPESTRM128rm
    811654435U,	// PCMPESTRM128rr
    8687616U,	// PCMPGTBrm
    8441856U,	// PCMPGTBrr
    8689224U,	// PCMPGTDrm
    8443464U,	// PCMPGTDrr
    8692294U,	// PCMPGTQrm
    8446534U,	// PCMPGTQrr
    8695216U,	// PCMPGTWrm
    8449456U,	// PCMPGTWrr
    0U,	// PCMPISTRIMEM
    0U,	// PCMPISTRIREG
    25499786U,	// PCMPISTRIrm
    811653258U,	// PCMPISTRIrr
    0U,	// PCMPISTRM128MEM
    0U,	// PCMPISTRM128REG
    25500975U,	// PCMPISTRM128rm
    811654447U,	// PCMPISTRM128rr
    283204324U,	// PDEP32rm
    811653860U,	// PDEP32rr
    283222297U,	// PDEP64rm
    811655449U,	// PDEP64rr
    283204765U,	// PEXT32rm
    811654301U,	// PEXT32rr
    283222695U,	// PEXT64rm
    811655847U,	// PEXT64rr
    587534204U,	// PEXTRBmr
    811650940U,	// PEXTRBrr
    855971016U,	// PEXTRDmr
    811652296U,	// PEXTRDrr
    1124409774U,	// PEXTRQmr
    811655598U,	// PEXTRQrr
    1392848036U,	// PEXTRWmr
    811658404U,	// PEXTRWri
    811658404U,	// PEXTRWrr_REV
    551833963U,	// PF2IDrm
    551817579U,	// PF2IDrr
    551840659U,	// PF2IWrm
    551824275U,	// PF2IWrr
    551653502U,	// PFACCrm
    8441982U,	// PFACCrr
    551653656U,	// PFADDrm
    8442136U,	// PFADDrr
    551657478U,	// PFCMPEQrm
    8445958U,	// PFCMPEQrr
    551655283U,	// PFCMPGErm
    8443763U,	// PFCMPGErr
    551659784U,	// PFCMPGTrm
    8448264U,	// PFCMPGTrr
    551661143U,	// PFMAXrm
    8449623U,	// PFMAXrr
    551656778U,	// PFMINrm
    8445258U,	// PFMINrr
    551656612U,	// PFMULrm
    8445092U,	// PFMULrr
    551653509U,	// PFNACCrm
    8441989U,	// PFNACCrr
    551653517U,	// PFPNACCrm
    8441997U,	// PFPNACCrr
    551652562U,	// PFRCPIT1rm
    8441042U,	// PFRCPIT1rr
    551652651U,	// PFRCPIT2rm
    8441131U,	// PFRCPIT2rr
    551837045U,	// PFRCPrm
    551820661U,	// PFRCPrr
    551652572U,	// PFRSQIT1rm
    8441052U,	// PFRSQIT1rr
    551840055U,	// PFRSQRTrm
    551823671U,	// PFRSQRTrr
    551658344U,	// PFSUBRrm
    8446824U,	// PFSUBRrr
    551653438U,	// PFSUBrm
    8441918U,	// PFSUBrr
    8687904U,	// PHADDDrm
    8442144U,	// PHADDDrr
    8695012U,	// PHADDSWrm128
    8449252U,	// PHADDSWrr128
    8694445U,	// PHADDWrm
    8448685U,	// PHADDWrr
    323090U,	// PHMINPOSUWrm128
    551824914U,	// PHMINPOSUWrr128
    8687858U,	// PHSUBDrm
    8442098U,	// PHSUBDrr
    8694993U,	// PHSUBSWrm128
    8449233U,	// PHSUBSWrr128
    8694351U,	// PHSUBWrm
    8448591U,	// PHSUBWrr
    551833947U,	// PI2FDrm
    551817563U,	// PI2FDrr
    551840569U,	// PI2FWrm
    551824185U,	// PI2FWrr
    612716403U,	// PINSRBrm
    570789747U,	// PINSRBrr
    614814911U,	// PINSRDrm
    570791103U,	// PINSRDrr
    593846673U,	// PINSRQrm
    570794385U,	// PINSRQrr
    595946633U,	// PINSRWrmi
    570797193U,	// PINSRWrri
    8694981U,	// PMADDUBSWrm128
    8449221U,	// PMADDUBSWrr128
    8689358U,	// PMADDWDrm
    8443598U,	// PMADDWDrr
    8687601U,	// PMAXSBrm
    8441841U,	// PMAXSBrr
    8689198U,	// PMAXSDrm
    8443438U,	// PMAXSDrr
    8695153U,	// PMAXSWrm
    8449393U,	// PMAXSWrr
    8687686U,	// PMAXUBrm
    8441926U,	// PMAXUBrr
    8689283U,	// PMAXUDrm
    8443523U,	// PMAXUDrr
    8695327U,	// PMAXUWrm
    8449567U,	// PMAXUWrr
    8687542U,	// PMINSBrm
    8441782U,	// PMINSBrr
    8689125U,	// PMINSDrm
    8443365U,	// PMINSDrr
    8695062U,	// PMINSWrm
    8449302U,	// PMINSWrr
    8687670U,	// PMINUBrm
    8441910U,	// PMINUBrr
    8689265U,	// PMINUDrm
    8443505U,	// PMINUDrr
    8695305U,	// PMINUWrm
    8449545U,	// PMINUWrr
    551816954U,	// PMOVMSKBrr
    551801091U,	// PMOVSXBDrm
    551817475U,	// PMOVSXBDrr
    384622U,	// PMOVSXBQrm
    551820910U,	// PMOVSXBQrr
    551840360U,	// PMOVSXBWrm
    551823976U,	// PMOVSXBWrr
    551837636U,	// PMOVSXDQrm
    551821252U,	// PMOVSXDQrr
    551835431U,	// PMOVSXWDrm
    551819047U,	// PMOVSXWDrr
    551805713U,	// PMOVSXWQrm
    551822097U,	// PMOVSXWQrr
    551801102U,	// PMOVZXBDrm
    551817486U,	// PMOVZXBDrr
    384633U,	// PMOVZXBQrm
    551820921U,	// PMOVZXBQrr
    551840371U,	// PMOVZXBWrm
    551823987U,	// PMOVZXBWrr
    551837647U,	// PMOVZXDQrm
    551821263U,	// PMOVZXDQrr
    551835442U,	// PMOVZXWDrm
    551819058U,	// PMOVZXWDrr
    551805724U,	// PMOVZXWQrm
    551822108U,	// PMOVZXWQrr
    8691521U,	// PMULDQrm
    8445761U,	// PMULDQrr
    8695087U,	// PMULHRSWrm128
    8449327U,	// PMULHRSWrr128
    551660647U,	// PMULHRWrm
    8449127U,	// PMULHRWrr
    8695286U,	// PMULHUWrm
    8449526U,	// PMULHUWrr
    8694660U,	// PMULHWrm
    8448900U,	// PMULHWrr
    8688032U,	// PMULLDrm
    8442272U,	// PMULLDrr
    8694729U,	// PMULLWrm
    8448969U,	// PMULLWrr
    8691642U,	// PMULUDQrm
    8445882U,	// PMULUDQrr
    27703U,	// POP16r
    224311U,	// POP16rmm
    27703U,	// POP16rmr
    23320U,	// POP32r
    236312U,	// POP32rmm
    23320U,	// POP32rmr
    24881U,	// POP64r
    401713U,	// POP64rmm
    24881U,	// POP64rmr
    15289U,	// POPA16
    14492U,	// POPA32
    388537U,	// POPCNT16rm
    551824825U,	// POPCNT16rr
    551804010U,	// POPCNT32rm
    551820394U,	// POPCNT32rr
    551838287U,	// POPCNT64rm
    551821903U,	// POPCNT64rr
    14919U,	// POPDS16
    14900U,	// POPDS32
    14957U,	// POPES16
    14938U,	// POPES32
    15302U,	// POPF16
    14505U,	// POPF32
    14747U,	// POPF64
    15014U,	// POPFS16
    14976U,	// POPFS32
    14995U,	// POPFS64
    15071U,	// POPGS16
    15033U,	// POPGS32
    15052U,	// POPGS64
    15191U,	// POPSS16
    15172U,	// POPSS32
    8692610U,	// PORrm
    8446850U,	// PORrr
    432114U,	// PREFETCH
    429675U,	// PREFETCHNTA
    429220U,	// PREFETCHT0
    429254U,	// PREFETCHT1
    429343U,	// PREFETCHT2
    437102U,	// PREFETCHW
    8694280U,	// PSADBWrm
    8448520U,	// PSADBWrr
    8687317U,	// PSHUFBrm
    8441557U,	// PSHUFBrr
    25497955U,	// PSHUFDmi
    811651427U,	// PSHUFDri
    25504634U,	// PSHUFHWmi
    811658106U,	// PSHUFHWri
    25504679U,	// PSHUFLWmi
    811658151U,	// PSHUFLWri
    8687406U,	// PSIGNBrm
    8441646U,	// PSIGNBrr
    8688081U,	// PSIGNDrm
    8442321U,	// PSIGNDrr
    8694781U,	// PSIGNWrm
    8449021U,	// PSIGNWrr
    8445743U,	// PSLLDQri
    8442264U,	// PSLLDri
    8688024U,	// PSLLDrm
    8442264U,	// PSLLDrr
    8446135U,	// PSLLQri
    8691895U,	// PSLLQrm
    8446135U,	// PSLLQrr
    8448961U,	// PSLLWri
    8694721U,	// PSLLWrm
    8448961U,	// PSLLWrr
    8442069U,	// PSRADri
    8687829U,	// PSRADrm
    8442069U,	// PSRADrr
    8448487U,	// PSRAWri
    8694247U,	// PSRAWrm
    8448487U,	// PSRAWrr
    8445752U,	// PSRLDQri
    8442281U,	// PSRLDri
    8688041U,	// PSRLDrm
    8442281U,	// PSRLDrr
    8446149U,	// PSRLQri
    8691909U,	// PSRLQrm
    8446149U,	// PSRLQrr
    8448984U,	// PSRLWri
    8694744U,	// PSRLWrm
    8448984U,	// PSRLWrr
    8687270U,	// PSUBBrm
    8441510U,	// PSUBBrr
    8687867U,	// PSUBDrm
    8442107U,	// PSUBDrr
    8691294U,	// PSUBQrm
    8445534U,	// PSUBQrr
    8687517U,	// PSUBSBrm
    8441757U,	// PSUBSBrr
    8695003U,	// PSUBSWrm
    8449243U,	// PSUBSWrr
    8687565U,	// PSUBUSBrm
    8441805U,	// PSUBUSBrr
    8695125U,	// PSUBUSWrm
    8449365U,	// PSUBUSWrr
    8694360U,	// PSUBWrm
    8448600U,	// PSUBWrr
    551834391U,	// PSWAPDrm
    551818007U,	// PSWAPDrr
    584027U,	// PTESTrm
    551823707U,	// PTESTrr
    8694308U,	// PUNPCKHBWrm
    8448548U,	// PUNPCKHBWrr
    8691472U,	// PUNPCKHDQrm
    8445712U,	// PUNPCKHDQrr
    8691539U,	// PUNPCKHQDQrm
    8445779U,	// PUNPCKHQDQrr
    8689368U,	// PUNPCKHWDrm
    8443608U,	// PUNPCKHWDrr
    8694320U,	// PUNPCKLBWrm
    8448560U,	// PUNPCKLBWrr
    8691491U,	// PUNPCKLDQrm
    8445731U,	// PUNPCKLDQrr
    8691552U,	// PUNPCKLQDQrm
    8445792U,	// PUNPCKLQDQrr
    8689380U,	// PUNPCKLWDrm
    8443620U,	// PUNPCKLWDrr
    27532U,	// PUSH16i8
    27532U,	// PUSH16r
    224140U,	// PUSH16rmm
    27532U,	// PUSH16rmr
    23067U,	// PUSH32i8
    23067U,	// PUSH32r
    236059U,	// PUSH32rmm
    23067U,	// PUSH32rmr
    27532U,	// PUSH64i16
    24692U,	// PUSH64i32
    24692U,	// PUSH64i8
    24692U,	// PUSH64r
    401524U,	// PUSH64rmm
    24692U,	// PUSH64rmr
    15282U,	// PUSHA16
    14485U,	// PUSHA32
    14880U,	// PUSHCS16
    14870U,	// PUSHCS32
    14909U,	// PUSHDS16
    14890U,	// PUSHDS32
    14947U,	// PUSHES16
    14928U,	// PUSHES32
    15295U,	// PUSHF16
    14498U,	// PUSHF32
    14740U,	// PUSHF64
    15004U,	// PUSHFS16
    14966U,	// PUSHFS32
    14985U,	// PUSHFS64
    15061U,	// PUSHGS16
    15023U,	// PUSHGS32
    15042U,	// PUSHGS64
    15181U,	// PUSHSS16
    15162U,	// PUSHSS32
    27532U,	// PUSHi16
    23067U,	// PUSHi32
    8692633U,	// PXORrm
    8446873U,	// PXORrr
    225323U,	// RCL16m1
    225788U,	// RCL16mCL
    4238240U,	// RCL16mi
    28715U,	// RCL16r1
    29180U,	// RCL16rCL
    8448928U,	// RCL16ri
    242562U,	// RCL32m1
    241948U,	// RCL32mCL
    12622412U,	// RCL32mi
    28555U,	// RCL32r1
    28956U,	// RCL32rCL
    8444492U,	// RCL32ri
    405467U,	// RCL64m1
    405900U,	// RCL64mCL
    18915489U,	// RCL64mi
    28635U,	// RCL64r1
    29068U,	// RCL64rCL
    8446113U,	// RCL64ri
    438075U,	// RCL8m1
    438444U,	// RCL8mCL
    23105290U,	// RCL8mi
    28475U,	// RCL8r1
    28844U,	// RCL8rCL
    8441610U,	// RCL8ri
    583312U,	// RCPPSm
    583312U,	// RCPPSm_Int
    551822992U,	// RCPPSr
    551822992U,	// RCPPSr_Int
    616582U,	// RCPSSm
    551725190U,	// RCPSSm_Int
    551823494U,	// RCPSSr
    8448134U,	// RCPSSr_Int
    225363U,	// RCR16m1
    225832U,	// RCR16mCL
    4238427U,	// RCR16mi
    28755U,	// RCR16r1
    29224U,	// RCR16rCL
    8449115U,	// RCR16ri
    241587U,	// RCR32m1
    241992U,	// RCR32mCL
    12622712U,	// RCR32mi
    28595U,	// RCR32r1
    29000U,	// RCR32rCL
    8444792U,	// RCR32ri
    405507U,	// RCR64m1
    405944U,	// RCR64mCL
    18915681U,	// RCR64mi
    28675U,	// RCR64r1
    29112U,	// RCR64rCL
    8446305U,	// RCR64ri
    438115U,	// RCR8m1
    438488U,	// RCR8mCL
    23105370U,	// RCR8mi
    28515U,	// RCR8r1
    28888U,	// RCR8rCL
    8441690U,	// RCR8ri
    22975U,	// RDFSBASE
    24591U,	// RDFSBASE64
    22997U,	// RDGSBASE
    24613U,	// RDGSBASE64
    14835U,	// RDMSR
    14225U,	// RDPMC
    27348U,	// RDRAND16r
    22889U,	// RDRAND32r
    24393U,	// RDRAND64r
    27332U,	// RDSEED16r
    22860U,	// RDSEED32r
    24326U,	// RDSEED64r
    14238U,	// RDTSC
    14682U,	// RDTSCP
    13701U,	// RELEASE_MOV16mr
    13701U,	// RELEASE_MOV32mr
    13701U,	// RELEASE_MOV64mr
    13701U,	// RELEASE_MOV8mr
    14328U,	// REPNE_PREFIX
    14169U,	// REP_MOVSB_32
    14169U,	// REP_MOVSB_64
    14553U,	// REP_MOVSD_32
    14553U,	// REP_MOVSD_64
    14763U,	// REP_MOVSQ_64
    15318U,	// REP_MOVSW_32
    15318U,	// REP_MOVSW_64
    14689U,	// REP_PREFIX
    14159U,	// REP_STOSB_32
    14159U,	// REP_STOSB_64
    14543U,	// REP_STOSD_32
    14543U,	// REP_STOSD_64
    14753U,	// REP_STOSQ_64
    15308U,	// REP_STOSW_32
    15308U,	// REP_STOSW_64
    23646U,	// RETIL
    25151U,	// RETIQ
    28073U,	// RETIW
    14564U,	// RETL
    14774U,	// RETQ
    15334U,	// RETW
    13995U,	// REX64_PREFIX
    225343U,	// ROL16m1
    225810U,	// ROL16mCL
    4238289U,	// ROL16mi
    28735U,	// ROL16r1
    29202U,	// ROL16rCL
    8448977U,	// ROL16ri
    241567U,	// ROL32m1
    241970U,	// ROL32mCL
    12622458U,	// ROL32mi
    28575U,	// ROL32r1
    28978U,	// ROL32rCL
    8444538U,	// ROL32ri
    405487U,	// ROL64m1
    405922U,	// ROL64mCL
    18915518U,	// ROL64mi
    28655U,	// ROL64r1
    29090U,	// ROL64rCL
    8446142U,	// ROL64ri
    438095U,	// ROL8m1
    438466U,	// ROL8mCL
    23105304U,	// ROL8mi
    28495U,	// ROL8r1
    28866U,	// ROL8rCL
    8441624U,	// ROL8ri
    225383U,	// ROR16m1
    225854U,	// ROR16mCL
    4238454U,	// ROR16mi
    28775U,	// ROR16r1
    29246U,	// ROR16rCL
    8449142U,	// ROR16ri
    241607U,	// ROR32m1
    242014U,	// ROR32mCL
    12622724U,	// ROR32mi
    28615U,	// ROR32r1
    29022U,	// ROR32rCL
    8444804U,	// ROR32ri
    405527U,	// ROR64m1
    405966U,	// ROR64mCL
    18915693U,	// ROR64mi
    28695U,	// ROR64r1
    29134U,	// ROR64rCL
    8446317U,	// ROR64ri
    438135U,	// ROR8m1
    438510U,	// ROR8mCL
    23105382U,	// ROR8mi
    28535U,	// ROR8r1
    28910U,	// ROR8rCL
    8441702U,	// ROR8ri
    832904449U,	// RORX32mi
    811654401U,	// RORX32ri
    835003224U,	// RORX64mi
    811656024U,	// RORX64ri
    80024483U,	// ROUNDPDm
    811652003U,	// ROUNDPDr
    80029164U,	// ROUNDPSm
    811656684U,	// ROUNDPSr
    581260736U,	// ROUNDSDm
    570791360U,	// ROUNDSDr
    570791360U,	// ROUNDSDr_Int
    585459801U,	// ROUNDSSm
    570796121U,	// ROUNDSSr
    570796121U,	// ROUNDSSr_Int
    14623U,	// RSM
    583395U,	// RSQRTPSm
    583395U,	// RSQRTPSm_Int
    551823075U,	// RSQRTPSr
    551823075U,	// RSQRTPSr_Int
    616607U,	// RSQRTSSm
    551725215U,	// RSQRTSSm_Int
    551823519U,	// RSQRTSSr
    8448159U,	// RSQRTSSr_Int
    14414U,	// SAHF
    225313U,	// SAL16m1
    225777U,	// SAL16mCL
    4238234U,	// SAL16mi
    28705U,	// SAL16r1
    29169U,	// SAL16rCL
    8448922U,	// SAL16ri
    241537U,	// SAL32m1
    241937U,	// SAL32mCL
    12622406U,	// SAL32mi
    28545U,	// SAL32r1
    28945U,	// SAL32rCL
    8444486U,	// SAL32ri
    405457U,	// SAL64m1
    405889U,	// SAL64mCL
    18915483U,	// SAL64mi
    28625U,	// SAL64r1
    29057U,	// SAL64rCL
    8446107U,	// SAL64ri
    438065U,	// SAL8m1
    438433U,	// SAL8mCL
    23105284U,	// SAL8mi
    28465U,	// SAL8r1
    28833U,	// SAL8rCL
    8441604U,	// SAL8ri
    14212U,	// SALC
    225353U,	// SAR16m1
    225821U,	// SAR16mCL
    4238421U,	// SAR16mi
    28745U,	// SAR16r1
    29213U,	// SAR16rCL
    8449109U,	// SAR16ri
    241577U,	// SAR32m1
    241981U,	// SAR32mCL
    12622689U,	// SAR32mi
    28585U,	// SAR32r1
    28989U,	// SAR32rCL
    8444769U,	// SAR32ri
    405497U,	// SAR64m1
    405933U,	// SAR64mCL
    18915675U,	// SAR64mi
    28665U,	// SAR64r1
    29101U,	// SAR64rCL
    8446299U,	// SAR64ri
    438105U,	// SAR8m1
    438477U,	// SAR8mCL
    23105364U,	// SAR8mi
    28505U,	// SAR8r1
    28877U,	// SAR8rCL
    8441684U,	// SAR8ri
    832904435U,	// SARX32rm
    811654387U,	// SARX32rr
    835003210U,	// SARX64rm
    811656010U,	// SARX64rr
    2124278U,	// SBB16i16
    4237814U,	// SBB16mi
    4237814U,	// SBB16mi8
    4237814U,	// SBB16mr
    6351350U,	// SBB16ri
    6351350U,	// SBB16ri8
    6367734U,	// SBB16rm
    6351350U,	// SBB16rr
    8448502U,	// SBB16rr_REV
    10508520U,	// SBB32i32
    12622056U,	// SBB32mi
    12622056U,	// SBB32mi8
    12622056U,	// SBB32mr
    6346984U,	// SBB32ri
    6346984U,	// SBB32ri8
    283203816U,	// SBB32rm
    6346984U,	// SBB32rr
    8444136U,	// SBB32rr_REV
    16801338U,	// SBB64i32
    18914874U,	// SBB64mi32
    18914874U,	// SBB64mi8
    18914874U,	// SBB64mr
    6348346U,	// SBB64ri32
    6348346U,	// SBB64ri8
    283221562U,	// SBB64rm
    6348346U,	// SBB64rr
    8445498U,	// SBB64rr_REV
    20991647U,	// SBB8i8
    23105183U,	// SBB8mi
    23105183U,	// SBB8mr
    6344351U,	// SBB8ri
    118431U,	// SBB8rm
    6344351U,	// SBB8rr
    8441503U,	// SBB8rr_REV
    21467012U,	// SCASB
    11000770U,	// SCASL
    17310134U,	// SCASQ
    2649260U,	// SCASW
    15092U,	// SEG_ALLOCA_32
    15092U,	// SEG_ALLOCA_64
    14379U,	// SEH_EndPrologue
    14365U,	// SEH_Epilogue
    29668U,	// SEH_PushFrame
    29713U,	// SEH_PushReg
    283145219U,	// SEH_SaveReg
    283145133U,	// SEH_SaveXMM
    283145204U,	// SEH_SetFrame
    29651U,	// SEH_StackAlloc
    431937U,	// SETAEm
    22337U,	// SETAEr
    429669U,	// SETAm
    20069U,	// SETAr
    431959U,	// SETBEm
    22359U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    430073U,	// SETBm
    20473U,	// SETBr
    432055U,	// SETEm
    22455U,	// SETEr
    431996U,	// SETGEm
    22396U,	// SETGEr
    432108U,	// SETGm
    22508U,	// SETGr
    432012U,	// SETLEm
    22412U,	// SETLEr
    433252U,	// SETLm
    23652U,	// SETLr
    432032U,	// SETNEm
    22432U,	// SETNEr
    433505U,	// SETNOm
    23905U,	// SETNOr
    433575U,	// SETNPm
    23975U,	// SETNPr
    435231U,	// SETNSm
    25631U,	// SETNSr
    433512U,	// SETOm
    23912U,	// SETOr
    433609U,	// SETPm
    24009U,	// SETPr
    436448U,	// SETSm
    26848U,	// SETSr
    14305U,	// SFENCE
    634245U,	// SGDT16m
    629825U,	// SGDT32m
    631330U,	// SGDT64m
    8686768U,	// SHA1MSG1rm
    8441008U,	// SHA1MSG1rr
    8686844U,	// SHA1MSG2rm
    8441084U,	// SHA1MSG2rr
    8689597U,	// SHA1NEXTErm
    8443837U,	// SHA1NEXTErr
    608521547U,	// SHA1RNDS4rmi
    570789195U,	// SHA1RNDS4rri
    8686778U,	// SHA256MSG1rm
    8441018U,	// SHA256MSG1rr
    8686854U,	// SHA256MSG2rm
    8441094U,	// SHA256MSG2rr
    8686866U,	// SHA256RNDS2rm
    8441106U,	// SHA256RNDS2rr
    225333U,	// SHL16m1
    225799U,	// SHL16mCL
    4238258U,	// SHL16mi
    28725U,	// SHL16r1
    29191U,	// SHL16rCL
    8448946U,	// SHL16ri
    241557U,	// SHL32m1
    241959U,	// SHL32mCL
    12622426U,	// SHL32mi
    28565U,	// SHL32r1
    28967U,	// SHL32rCL
    8444506U,	// SHL32ri
    405477U,	// SHL64m1
    405911U,	// SHL64mCL
    18915497U,	// SHL64mi
    28645U,	// SHL64r1
    29079U,	// SHL64rCL
    8446121U,	// SHL64ri
    438085U,	// SHL8m1
    438455U,	// SHL8mCL
    23105298U,	// SHL8mi
    28485U,	// SHL8r1
    28855U,	// SHL8rCL
    8441618U,	// SHL8ri
    4239833U,	// SHLD16mrCL
    1392847565U,	// SHLD16mri8
    8450521U,	// SHLD16rrCL
    570796749U,	// SHLD16rri8
    12628217U,	// SHLD32mrCL
    855972187U,	// SHLD32mri8
    8450297U,	// SHLD32rrCL
    570792283U,	// SHLD32rri8
    18919785U,	// SHLD64mrCL
    1124409115U,	// SHLD64mri8
    8450409U,	// SHLD64rrCL
    570793755U,	// SHLD64rri8
    832904414U,	// SHLX32rm
    811654366U,	// SHLX32rr
    835003189U,	// SHLX64rm
    811655989U,	// SHLX64rr
    225373U,	// SHR16m1
    225843U,	// SHR16mCL
    4238448U,	// SHR16mi
    28765U,	// SHR16r1
    29235U,	// SHR16rCL
    8449136U,	// SHR16ri
    241597U,	// SHR32m1
    242003U,	// SHR32mCL
    12622718U,	// SHR32mi
    28605U,	// SHR32r1
    29011U,	// SHR32rCL
    8444798U,	// SHR32ri
    405517U,	// SHR64m1
    405955U,	// SHR64mCL
    18915687U,	// SHR64mi
    28685U,	// SHR64r1
    29123U,	// SHR64rCL
    8446311U,	// SHR64ri
    438125U,	// SHR8m1
    438499U,	// SHR8mCL
    23105376U,	// SHR8mi
    28525U,	// SHR8r1
    28899U,	// SHR8rCL
    8441696U,	// SHR8ri
    4239845U,	// SHRD16mrCL
    1392847591U,	// SHRD16mri8
    8450533U,	// SHRD16rrCL
    570796775U,	// SHRD16rri8
    12628229U,	// SHRD32mrCL
    855972210U,	// SHRD32mri8
    8450309U,	// SHRD32rrCL
    570792306U,	// SHRD32rri8
    18919797U,	// SHRD64mrCL
    1124409220U,	// SHRD64mri8
    8450421U,	// SHRD64rrCL
    570793860U,	// SHRD64rri8
    832904442U,	// SHRX32rm
    811654394U,	// SHRX32rr
    835003217U,	// SHRX64rm
    811656017U,	// SHRX64rr
    568677305U,	// SHUFPDrmi
    570790841U,	// SHUFPDrri
    568681986U,	// SHUFPSrmi
    570795522U,	// SHUFPSrri
    634259U,	// SIDT16m
    629839U,	// SIDT32m
    631344U,	// SIDT64m
    14640U,	// SIN_F
    0U,	// SIN_Fp32
    0U,	// SIN_Fp64
    0U,	// SIN_Fp80
    15410U,	// SKINIT
    224673U,	// SLDT16m
    28065U,	// SLDT16r
    23638U,	// SLDT32r
    221751U,	// SLDT64m
    25143U,	// SLDT64r
    224837U,	// SMSW16m
    28229U,	// SMSW16r
    23744U,	// SMSW32r
    25334U,	// SMSW64r
    578654U,	// SQRTPDm
    551818334U,	// SQRTPDr
    583396U,	// SQRTPSm
    551823076U,	// SQRTPSr
    595463U,	// SQRTSDm
    595463U,	// SQRTSDm_Int
    551818759U,	// SQRTSDr
    551818759U,	// SQRTSDr_Int
    616608U,	// SQRTSSm
    616608U,	// SQRTSSm_Int
    551823520U,	// SQRTSSr
    551823520U,	// SQRTSSr_Int
    15245U,	// SQRT_F
    0U,	// SQRT_Fp32
    0U,	// SQRT_Fp64
    0U,	// SQRT_Fp80
    14190U,	// STAC
    14244U,	// STC
    14268U,	// STD
    14433U,	// STGI
    14448U,	// STI
    238512U,	// STMXCSR
    503935U,	// STOSB
    520840U,	// STOSL
    537276U,	// STOSQ
    553559U,	// STOSW
    27805U,	// STR16r
    23459U,	// STR32r
    24991U,	// STR64r
    224413U,	// STRm
    190694U,	// ST_F32m
    203920U,	// ST_F64m
    35675539U,	// ST_FCOMPST0r
    35675539U,	// ST_FCOMPST0r_alt
    35675405U,	// ST_FCOMST0r
    190206U,	// ST_FP32m
    203556U,	// ST_FP64m
    715048U,	// ST_FP80m
    28376U,	// ST_FPNCEST0r
    28407U,	// ST_FPST0r
    28407U,	// ST_FPST0r_alt
    24022U,	// ST_FPrr
    35674108U,	// ST_FXCHST0r
    35674108U,	// ST_FXCHST0r_alt
    0U,	// ST_Fp32m
    0U,	// ST_Fp64m
    0U,	// ST_Fp64m32
    0U,	// ST_Fp80m32
    0U,	// ST_Fp80m64
    0U,	// ST_FpP32m
    0U,	// ST_FpP64m
    0U,	// ST_FpP64m32
    0U,	// ST_FpP80m
    0U,	// ST_FpP80m32
    0U,	// ST_FpP80m64
    26978U,	// ST_Frr
    2124369U,	// SUB16i16
    4237905U,	// SUB16mi
    4237905U,	// SUB16mi8
    4237905U,	// SUB16mr
    6351441U,	// SUB16ri
    6351441U,	// SUB16ri8
    6367825U,	// SUB16rm
    6351441U,	// SUB16rr
    8448593U,	// SUB16rr_REV
    10508535U,	// SUB32i32
    12622071U,	// SUB32mi
    12622071U,	// SUB32mi8
    12622071U,	// SUB32mr
    6346999U,	// SUB32ri
    6346999U,	// SUB32ri8
    283203831U,	// SUB32rm
    6346999U,	// SUB32rr
    8444151U,	// SUB32rr_REV
    16801375U,	// SUB64i32
    18914911U,	// SUB64mi32
    18914911U,	// SUB64mi8
    18914911U,	// SUB64mr
    6348383U,	// SUB64ri32
    6348383U,	// SUB64ri8
    283221599U,	// SUB64rm
    6348383U,	// SUB64rr
    8445535U,	// SUB64rr_REV
    20991655U,	// SUB8i8
    23105191U,	// SUB8mi
    23105191U,	// SUB8mr
    6344359U,	// SUB8ri
    6344359U,	// SUB8ri8
    118439U,	// SUB8rm
    6344359U,	// SUB8rr
    8441511U,	// SUB8rr_REV
    8524581U,	// SUBPDrm
    8442661U,	// SUBPDrr
    8529262U,	// SUBPSrm
    8447342U,	// SUBPSrr
    190287U,	// SUBR_F32m
    203623U,	// SUBR_F64m
    223063U,	// SUBR_FI16m
    236399U,	// SUBR_FI32m
    23918U,	// SUBR_FPrST0
    25449U,	// SUBR_FST0r
    0U,	// SUBR_Fp32m
    0U,	// SUBR_Fp64m
    0U,	// SUBR_Fp64m32
    0U,	// SUBR_Fp80m32
    0U,	// SUBR_Fp80m64
    0U,	// SUBR_FpI16m32
    0U,	// SUBR_FpI16m64
    0U,	// SUBR_FpI16m80
    0U,	// SUBR_FpI32m32
    0U,	// SUBR_FpI32m64
    0U,	// SUBR_FpI32m80
    28348U,	// SUBR_FrST0
    551703944U,	// SUBSDrm
    551703944U,	// SUBSDrm_Int
    8443272U,	// SUBSDrr
    8443272U,	// SUBSDrr_Int
    551725089U,	// SUBSSrm
    551725089U,	// SUBSSrm_Int
    8448033U,	// SUBSSrr
    8448033U,	// SUBSSrr_Int
    189383U,	// SUB_F32m
    202998U,	// SUB_F64m
    222158U,	// SUB_FI16m
    235773U,	// SUB_FI32m
    23993U,	// SUB_FPrST0
    20543U,	// SUB_FST0r
    0U,	// SUB_Fp32
    0U,	// SUB_Fp32m
    0U,	// SUB_Fp64
    0U,	// SUB_Fp64m
    0U,	// SUB_Fp64m32
    0U,	// SUB_Fp80
    0U,	// SUB_Fp80m32
    0U,	// SUB_Fp80m64
    0U,	// SUB_FpI16m32
    0U,	// SUB_FpI16m64
    0U,	// SUB_FpI16m80
    0U,	// SUB_FpI32m32
    0U,	// SUB_FpI32m64
    0U,	// SUB_FpI32m80
    28421U,	// SUB_FrST0
    15080U,	// SWAPGS
    14526U,	// SYSCALL
    14818U,	// SYSENTER
    14583U,	// SYSEXIT
    14793U,	// SYSEXIT64
    14575U,	// SYSRET
    14785U,	// SYSRET64
    551801005U,	// T1MSKC32rm
    551817389U,	// T1MSKC32rr
    551833773U,	// T1MSKC64rm
    551817389U,	// T1MSKC64rr
    82206094U,	// TAILJMPd
    82206094U,	// TAILJMPd64
    82032036U,	// TAILJMPm
    82195893U,	// TAILJMPm64
    0U,	// TAILJMPr
    81819061U,	// TAILJMPr64
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    2125294U,	// TEST16i16
    4238830U,	// TEST16mi
    4238830U,	// TEST16mi_alt
    551824878U,	// TEST16ri
    551824878U,	// TEST16ri_alt
    4238830U,	// TEST16rm
    551824878U,	// TEST16rr
    10509449U,	// TEST32i32
    12622985U,	// TEST32mi
    12622985U,	// TEST32mi_alt
    551820425U,	// TEST32ri
    551820425U,	// TEST32ri_alt
    12622985U,	// TEST32rm
    551820425U,	// TEST32rr
    16802464U,	// TEST64i32
    18916000U,	// TEST64mi32
    18916000U,	// TEST64mi32_alt
    551821984U,	// TEST64ri32
    551821984U,	// TEST64ri32_alt
    18916000U,	// TEST64rm
    551821984U,	// TEST64rr
    20992037U,	// TEST8i8
    23105573U,	// TEST8mi
    23105573U,	// TEST8mi_alt
    551817253U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    551817253U,	// TEST8ri_alt
    23105573U,	// TEST8rm
    551817253U,	// TEST8rr
    13847U,	// TLSCall_32
    13951U,	// TLSCall_64
    13860U,	// TLS_addr32
    13964U,	// TLS_addr64
    13873U,	// TLS_base_addr32
    13977U,	// TLS_base_addr64
    13891U,	// TRAP
    15257U,	// TST_F
    0U,	// TST_Fp32
    0U,	// TST_Fp64
    0U,	// TST_Fp80
    388554U,	// TZCNT16rm
    551824842U,	// TZCNT16rr
    551804027U,	// TZCNT32rm
    551820411U,	// TZCNT32rr
    551838304U,	// TZCNT64rm
    551821920U,	// TZCNT64rr
    551803083U,	// TZMSK32rm
    551819467U,	// TZMSK32rr
    551835851U,	// TZMSK64rm
    551819467U,	// TZMSK64rr
    595402U,	// UCOMISDrm
    551818698U,	// UCOMISDrr
    616547U,	// UCOMISSrm
    551823459U,	// UCOMISSrr
    22644U,	// UCOM_FIPr
    22586U,	// UCOM_FIr
    14716U,	// UCOM_FPPr
    23962U,	// UCOM_FPr
    0U,	// UCOM_FpIr32
    0U,	// UCOM_FpIr64
    0U,	// UCOM_FpIr80
    0U,	// UCOM_Fpr32
    0U,	// UCOM_Fpr64
    0U,	// UCOM_Fpr80
    23827U,	// UCOM_Fr
    14124U,	// UD2B
    8524738U,	// UNPCKHPDrm
    8442818U,	// UNPCKHPDrr
    8529419U,	// UNPCKHPSrm
    8447499U,	// UNPCKHPSrr
    8524780U,	// UNPCKLPDrm
    8442860U,	// UNPCKLPDrr
    8529481U,	// UNPCKLPSrm
    8447561U,	// UNPCKLPSrr
    1625322379U,	// VAARG_64
    812520328U,	// VADDPDYrm
    811651976U,	// VADDPDYrr
    812532486U,	// VADDPDZrm
    352469766U,	// VADDPDZrmb
    1427014406U,	// VADDPDZrmbk
    1427014406U,	// VADDPDZrmbkz
    571397000U,	// VADDPDZrmk
    571397000U,	// VADDPDZrmkz
    811647750U,	// VADDPDZrr
    570786566U,	// VADDPDZrrk
    570786566U,	// VADDPDZrrkz
    811733896U,	// VADDPDrm
    811651976U,	// VADDPDrr
    812525009U,	// VADDPSYrm
    811656657U,	// VADDPSYrr
    812534405U,	// VADDPSZrm
    354585221U,	// VADDPSZrmb
    1429146245U,	// VADDPSZrmbk
    1429146245U,	// VADDPSZrmbkz
    571401681U,	// VADDPSZrmk
    571401681U,	// VADDPSZrmkz
    811649669U,	// VADDPSZrr
    570788485U,	// VADDPSZrrk
    570788485U,	// VADDPSZrrkz
    811738577U,	// VADDPSrm
    811656657U,	// VADDPSrr
    283268535U,	// VADDSDZrm
    811652535U,	// VADDSDZrr
    283268535U,	// VADDSDrm
    283268535U,	// VADDSDrm_Int
    811652535U,	// VADDSDrr
    811652535U,	// VADDSDrr_Int
    283289680U,	// VADDSSZrm
    811657296U,	// VADDSSZrr
    283289680U,	// VADDSSrm
    283289680U,	// VADDSSrm_Int
    811657296U,	// VADDSSrr
    811657296U,	// VADDSSrr_Int
    812520236U,	// VADDSUBPDYrm
    811651884U,	// VADDSUBPDYrr
    811733804U,	// VADDSUBPDrm
    811651884U,	// VADDSUBPDrr
    812524917U,	// VADDSUBPSYrm
    811656565U,	// VADDSUBPSYrr
    811738485U,	// VADDSUBPSrm
    811656565U,	// VADDSUBPSrr
    811903296U,	// VAESDECLASTrm
    811657536U,	// VAESDECLASTrr
    811896982U,	// VAESDECrm
    811651222U,	// VAESDECrr
    811903309U,	// VAESENCLASTrm
    811657549U,	// VAESENCLASTrr
    811897022U,	// VAESENCrm
    811651262U,	// VAESENCrr
    315573U,	// VAESIMCrm
    551817397U,	// VAESIMCrr
    25504103U,	// VAESKEYGENASSIST128rm
    811657575U,	// VAESKEYGENASSIST128rr
    356860300U,	// VALIGNDrmi
    302350732U,	// VALIGNDrri
    90210700U,	// VALIGNDrrik
    571064716U,	// VALIGNDrrikz
    356861965U,	// VALIGNQrmi
    302352397U,	// VALIGNQrri
    90212365U,	// VALIGNQrrik
    571066381U,	// VALIGNQrrikz
    812520472U,	// VANDNPDYrm
    811652120U,	// VANDNPDYrr
    811734040U,	// VANDNPDrm
    811652120U,	// VANDNPDrr
    812525182U,	// VANDNPSYrm
    811656830U,	// VANDNPSYrr
    811738750U,	// VANDNPSrm
    811656830U,	// VANDNPSrr
    812520336U,	// VANDPDYrm
    811651984U,	// VANDPDYrr
    811733904U,	// VANDPDrm
    811651984U,	// VANDPDrr
    812525017U,	// VANDPSYrm
    811656665U,	// VANDPSYrr
    811738585U,	// VANDPSrm
    811656665U,	// VANDPSrr
    283145147U,	// VASTART_SAVE_XMM_REGS
    571392855U,	// VBLENDMPDZrm
    570786647U,	// VBLENDMPDZrr
    571394774U,	// VBLENDMPSZrm
    570788566U,	// VBLENDMPSZrr
    92623768U,	// VBLENDPDYrmi
    302355352U,	// VBLENDPDYrri
    300241816U,	// VBLENDPDrmi
    302355352U,	// VBLENDPDrri
    92628449U,	// VBLENDPSYrmi
    302360033U,	// VBLENDPSYrri
    300246497U,	// VBLENDPSrmi
    302360033U,	// VBLENDPSrri
    92623992U,	// VBLENDVPDYrm
    302355576U,	// VBLENDVPDYrr
    300242040U,	// VBLENDVPDrm
    302355576U,	// VBLENDVPDrr
    92628767U,	// VBLENDVPSYrm
    302360351U,	// VBLENDVPSYrr
    300246815U,	// VBLENDVPSrm
    302360351U,	// VBLENDVPSrr
    577020U,	// VBROADCASTF128
    314931U,	// VBROADCASTI128
    1887731713U,	// VBROADCASTI32X4krm
    311297U,	// VBROADCASTI32X4rm
    1888436243U,	// VBROADCASTI64X4krm
    1015827U,	// VBROADCASTI64X4rm
    595471U,	// VBROADCASTSDYrm
    551818767U,	// VBROADCASTSDYrr
    591070U,	// VBROADCASTSDZrm
    551814366U,	// VBROADCASTSDZrr
    616625U,	// VBROADCASTSSYrm
    551823537U,	// VBROADCASTSSYrr
    609253U,	// VBROADCASTSSZrm
    551816165U,	// VBROADCASTSSZrr
    616625U,	// VBROADCASTSSrm
    551823537U,	// VBROADCASTSSrr
    2185902443U,	// VCMPPDYrmi
    92623920U,	// VCMPPDYrmi_alt
    1380612459U,	// VCMPPDYrri
    302355504U,	// VCMPPDYrri_alt
    2510961003U,	// VCMPPDZrmi
    96813942U,	// VCMPPDZrmi_alt
    1437235563U,	// VCMPPDZrri
    302351222U,	// VCMPPDZrri_alt
    99252587U,	// VCMPPDZrrib
    1112160619U,	// VCMPPDrmi
    300241968U,	// VCMPPDrmi_alt
    1380612459U,	// VCMPPDrri
    302355504U,	// VCMPPDrri_alt
    2187999595U,	// VCMPPSYrmi
    92628638U,	// VCMPPSYrmi_alt
    1382709611U,	// VCMPPSYrri
    302360222U,	// VCMPPSYrri_alt
    2517252459U,	// VCMPPSZrmi
    96815861U,	// VCMPPSZrmi_alt
    1443527019U,	// VCMPPSZrri
    302353141U,	// VCMPPSZrri_alt
    103446891U,	// VCMPPSZrrib
    1114257771U,	// VCMPPSrmi
    300246686U,	// VCMPPSrmi_alt
    1382709611U,	// VCMPPSrri
    302360222U,	// VCMPPSrri_alt
    1921661291U,	// VCMPSDZrm
    312825333U,	// VCMPSDZrmi_alt
    1384806763U,	// VCMPSDZrr
    302355957U,	// VCMPSDZrri_alt
    1921661291U,	// VCMPSDrm
    312825333U,	// VCMPSDrm_alt
    1384806763U,	// VCMPSDrr
    302355957U,	// VCMPSDrr_alt
    2731161963U,	// VCMPSSZrm
    317024397U,	// VCMPSSZrmi_alt
    1389001067U,	// VCMPSSZrr
    302360717U,	// VCMPSSZrri_alt
    2731161963U,	// VCMPSSrm
    317024397U,	// VCMPSSrm_alt
    1389001067U,	// VCMPSSrr
    302360717U,	// VCMPSSrr_alt
    579027U,	// VCOMISDZrm
    551818707U,	// VCOMISDZrr
    579027U,	// VCOMISDrm
    551818707U,	// VCOMISDrr
    583788U,	// VCOMISSZrm
    551823468U,	// VCOMISSZrr
    583788U,	// VCOMISSrm
    551823468U,	// VCOMISSrr
    316066U,	// VCVTDQ2PDYrm
    551817890U,	// VCVTDQ2PDYrr
    1016318U,	// VCVTDQ2PDZrm
    551813630U,	// VCVTDQ2PDZrr
    551834274U,	// VCVTDQ2PDrm
    551817890U,	// VCVTDQ2PDrr
    1025278U,	// VCVTDQ2PSYrm
    551822590U,	// VCVTDQ2PSYrr
    1034633U,	// VCVTDQ2PSZrm
    551815561U,	// VCVTDQ2PSZrr
    1051017U,	// VCVTDQ2PSZrrb
    320766U,	// VCVTDQ2PSrm
    551822590U,	// VCVTDQ2PSrr
    585323U,	// VCVTPD2DQXrm
    1076880U,	// VCVTPD2DQYrm
    551825040U,	// VCVTPD2DQYrr
    1083054U,	// VCVTPD2DQZrm
    551814830U,	// VCVTPD2DQZrr
    1050286U,	// VCVTPD2DQZrrb
    551820975U,	// VCVTPD2DQrr
    585335U,	// VCVTPD2PSXrm
    1076892U,	// VCVTPD2PSYrm
    551825052U,	// VCVTPD2PSYrr
    1083761U,	// VCVTPD2PSZrm
    551815537U,	// VCVTPD2PSZrr
    1050993U,	// VCVTPD2PSZrrb
    551822546U,	// VCVTPD2PSrr
    1083249U,	// VCVTPD2UDQZrm
    551815025U,	// VCVTPD2UDQZrr
    1050481U,	// VCVTPD2UDQZrrb
    582877U,	// VCVTPH2PSYrm
    551822557U,	// VCVTPH2PSYrr
    1074397U,	// VCVTPH2PSZrm
    551822557U,	// VCVTPH2PSZrr
    599261U,	// VCVTPH2PSrm
    551822557U,	// VCVTPH2PSrr
    1072847U,	// VCVTPS2DQYrm
    551821007U,	// VCVTPS2DQYrr
    1083079U,	// VCVTPS2DQZrm
    551814855U,	// VCVTPS2DQZrr
    1050311U,	// VCVTPS2DQZrrb
    581327U,	// VCVTPS2DQrm
    551821007U,	// VCVTPS2DQrr
    578221U,	// VCVTPS2PDYrm
    551817901U,	// VCVTPS2PDYrr
    1065495U,	// VCVTPS2PDZrm
    551813655U,	// VCVTPS2PDZrr
    594605U,	// VCVTPS2PDrm
    551817901U,	// VCVTPS2PDrr
    2735020034U,	// VCVTPS2PHYmr
    811653122U,	// VCVTPS2PHYrr
    3003450754U,	// VCVTPS2PHZmr
    811648386U,	// VCVTPS2PHZrr
    3271890946U,	// VCVTPS2PHmr
    811653122U,	// VCVTPS2PHrr
    1083276U,	// VCVTPS2UDQZrm
    551815052U,	// VCVTPS2UDQZrr
    1050508U,	// VCVTPS2UDQZrrb
    591259U,	// VCVTSD2SI64Zrm
    551814555U,	// VCVTSD2SI64Zrr
    596129U,	// VCVTSD2SI64rm
    551819425U,	// VCVTSD2SI64rr
    591259U,	// VCVTSD2SIZrm
    551814555U,	// VCVTSD2SIZrr
    596129U,	// VCVTSD2SIrm
    551819425U,	// VCVTSD2SIrr
    283273181U,	// VCVTSD2SSZrm
    811657181U,	// VCVTSD2SSZrr
    283273181U,	// VCVTSD2SSrm
    811657181U,	// VCVTSD2SSrr
    591310U,	// VCVTSD2USI64Zrm
    551814606U,	// VCVTSD2USI64Zrr
    591310U,	// VCVTSD2USIZrm
    551814606U,	// VCVTSD2USIZrr
    283221899U,	// VCVTSI2SD64rm
    811655051U,	// VCVTSI2SD64rr
    283198966U,	// VCVTSI2SDZrm
    811648502U,	// VCVTSI2SDZrr
    283203961U,	// VCVTSI2SDrm
    811653497U,	// VCVTSI2SDrr
    283222505U,	// VCVTSI2SS64rm
    811655657U,	// VCVTSI2SS64rr
    283198993U,	// VCVTSI2SSZrm
    811648529U,	// VCVTSI2SSZrr
    283204609U,	// VCVTSI2SSrm
    811654145U,	// VCVTSI2SSrr
    283215688U,	// VCVTSI642SDZrm
    811648840U,	// VCVTSI642SDZrr
    283215982U,	// VCVTSI642SSZrm
    811649134U,	// VCVTSI642SSZrr
    283284796U,	// VCVTSS2SDZrm
    811652412U,	// VCVTSS2SDZrr
    283284796U,	// VCVTSS2SDrm
    811652412U,	// VCVTSS2SDrr
    607668U,	// VCVTSS2SI64Zrm
    551814580U,	// VCVTSS2SI64Zrr
    612536U,	// VCVTSS2SI64rm
    551819448U,	// VCVTSS2SI64rr
    607668U,	// VCVTSS2SIZrm
    551814580U,	// VCVTSS2SIZrr
    612536U,	// VCVTSS2SIrm
    551819448U,	// VCVTSS2SIrr
    607721U,	// VCVTSS2USI64Zrm
    551814633U,	// VCVTSS2USI64Zrr
    607721U,	// VCVTSS2USIZrm
    551814633U,	// VCVTSS2USIZrr
    585310U,	// VCVTTPD2DQXrm
    1076867U,	// VCVTTPD2DQYrm
    551825027U,	// VCVTTPD2DQYrr
    1083041U,	// VCVTTPD2DQZrm
    551814817U,	// VCVTTPD2DQZrr
    551820963U,	// VCVTTPD2DQrr
    1083235U,	// VCVTTPD2UDQZrm
    551815011U,	// VCVTTPD2UDQZrr
    1072835U,	// VCVTTPS2DQYrm
    551820995U,	// VCVTTPS2DQYrr
    1083066U,	// VCVTTPS2DQZrm
    551814842U,	// VCVTTPS2DQZrr
    581315U,	// VCVTTPS2DQrm
    551820995U,	// VCVTTPS2DQrr
    1083262U,	// VCVTTPS2UDQZrm
    551815038U,	// VCVTTPS2UDQZrr
    591246U,	// VCVTTSD2SI64Zrm
    551814542U,	// VCVTTSD2SI64Zrr
    596117U,	// VCVTTSD2SI64rm
    551819413U,	// VCVTTSD2SI64rr
    591246U,	// VCVTTSD2SIZrm
    551814542U,	// VCVTTSD2SIZrr
    596117U,	// VCVTTSD2SIrm
    551819413U,	// VCVTTSD2SIrr
    591296U,	// VCVTTSD2USI64Zrm
    551814592U,	// VCVTTSD2USI64Zrr
    591296U,	// VCVTTSD2USIZrm
    551814592U,	// VCVTTSD2USIZrr
    607655U,	// VCVTTSS2SI64Zrm
    551814567U,	// VCVTTSS2SI64Zrr
    612524U,	// VCVTTSS2SI64rm
    551819436U,	// VCVTTSS2SI64rr
    607655U,	// VCVTTSS2SIZrm
    551814567U,	// VCVTTSS2SIZrr
    612524U,	// VCVTTSS2SIrm
    551819436U,	// VCVTTSS2SIrr
    607707U,	// VCVTTSS2USI64Zrm
    551814619U,	// VCVTTSS2USI64Zrr
    607707U,	// VCVTTSS2USIZrm
    551814619U,	// VCVTTSS2USIZrr
    1065482U,	// VCVTUDQ2PDZrm
    551813642U,	// VCVTUDQ2PDZrr
    1083797U,	// VCVTUDQ2PSZrm
    551815573U,	// VCVTUDQ2PSZrr
    1051029U,	// VCVTUDQ2PSZrrb
    283198979U,	// VCVTUSI2SDZrm
    811648515U,	// VCVTUSI2SDZrr
    283199006U,	// VCVTUSI2SSZrm
    811648542U,	// VCVTUSI2SSZrr
    283215701U,	// VCVTUSI642SDZrm
    811648853U,	// VCVTUSI642SDZrr
    283215995U,	// VCVTUSI642SSZrm
    811649147U,	// VCVTUSI642SSZrr
    812520579U,	// VDIVPDYrm
    811652227U,	// VDIVPDYrr
    812532700U,	// VDIVPDZrm
    352469980U,	// VDIVPDZrmb
    1427014620U,	// VDIVPDZrmbk
    1427014620U,	// VDIVPDZrmbkz
    571397251U,	// VDIVPDZrmk
    571397251U,	// VDIVPDZrmkz
    811647964U,	// VDIVPDZrr
    570786780U,	// VDIVPDZrrk
    570786780U,	// VDIVPDZrrkz
    811734147U,	// VDIVPDrm
    811652227U,	// VDIVPDrr
    812525354U,	// VDIVPSYrm
    811657002U,	// VDIVPSYrr
    812534619U,	// VDIVPSZrm
    354585435U,	// VDIVPSZrmb
    1429146459U,	// VDIVPSZrmbk
    1429146459U,	// VDIVPSZrmbkz
    571402026U,	// VDIVPSZrmk
    571402026U,	// VDIVPSZrmkz
    811649883U,	// VDIVPSZrr
    570788699U,	// VDIVPSZrrk
    570788699U,	// VDIVPSZrrkz
    811738922U,	// VDIVPSrm
    811657002U,	// VDIVPSrr
    283268637U,	// VDIVSDZrm
    811652637U,	// VDIVSDZrr
    283268637U,	// VDIVSDrm
    283268637U,	// VDIVSDrm_Int
    811652637U,	// VDIVSDrr
    811652637U,	// VDIVSDrr_Int
    283289791U,	// VDIVSSZrm
    811657407U,	// VDIVSSZrr
    283289791U,	// VDIVSSrm
    283289791U,	// VDIVSSrm_Int
    811657407U,	// VDIVSSrr
    811657407U,	// VDIVSSrr_Int
    300241961U,	// VDPPDrmi
    302355497U,	// VDPPDrri
    105211543U,	// VDPPSYrmi
    302360215U,	// VDPPSYrri
    300246679U,	// VDPPSrmi
    302360215U,	// VDPPSrri
    222111U,	// VERRm
    25503U,	// VERRr
    224353U,	// VERWm
    27745U,	// VERWr
    2735017441U,	// VEXTRACTF128mr
    811650529U,	// VEXTRACTF128rr
    2735017302U,	// VEXTRACTF32x4mr
    811650390U,	// VEXTRACTF32x4rr
    3003452816U,	// VEXTRACTF64x4mr
    811650448U,	// VEXTRACTF64x4rr
    3540323864U,	// VEXTRACTI128mr
    811650584U,	// VEXTRACTI128rr
    3540323699U,	// VEXTRACTI32x4mr
    811650419U,	// VEXTRACTI32x4rr
    3808759213U,	// VEXTRACTI64x4mr
    811650477U,	// VEXTRACTI64x4rr
    3271894721U,	// VEXTRACTPSmr
    811656897U,	// VEXTRACTPSrr
    3271894721U,	// VEXTRACTPSzmr
    811656897U,	// VEXTRACTPSzrr
    571392469U,	// VFMADD132PDZm
    1427014101U,	// VFMADD132PDZmb
    571394388U,	// VFMADD132PSZm
    1429145940U,	// VFMADD132PSZmb
    571392622U,	// VFMADD213PDZm
    1427014254U,	// VFMADD213PDZmb
    570786414U,	// VFMADD213PDZr
    1888076398U,	// VFMADD213PDZrk
    1888076398U,	// VFMADD213PDZrkz
    571394541U,	// VFMADD213PSZm
    1429146093U,	// VFMADD213PSZmb
    570788333U,	// VFMADD213PSZr
    1888078317U,	// VFMADD213PSZrk
    1888078317U,	// VFMADD213PSZrkz
    300241779U,	// VFMADDPD4mr
    92623731U,	// VFMADDPD4mrY
    303092595U,	// VFMADDPD4rm
    303108979U,	// VFMADDPD4rmY
    302355315U,	// VFMADDPD4rr
    302355315U,	// VFMADDPD4rrY
    302355315U,	// VFMADDPD4rrY_REV
    302355315U,	// VFMADDPD4rr_REV
    571527793U,	// VFMADDPDr132m
    571544177U,	// VFMADDPDr132mY
    570790513U,	// VFMADDPDr132r
    570790513U,	// VFMADDPDr132rY
    571527923U,	// VFMADDPDr213m
    571544307U,	// VFMADDPDr213mY
    570790643U,	// VFMADDPDr213r
    570790643U,	// VFMADDPDr213rY
    571527707U,	// VFMADDPDr231m
    571544091U,	// VFMADDPDr231mY
    570790427U,	// VFMADDPDr231r
    570790427U,	// VFMADDPDr231rY
    300246460U,	// VFMADDPS4mr
    92628412U,	// VFMADDPS4mrY
    303097276U,	// VFMADDPS4rm
    303113660U,	// VFMADDPS4rmY
    302359996U,	// VFMADDPS4rr
    302359996U,	// VFMADDPS4rrY
    302359996U,	// VFMADDPS4rrY_REV
    302359996U,	// VFMADDPS4rr_REV
    571532471U,	// VFMADDPSr132m
    571548855U,	// VFMADDPSr132mY
    570795191U,	// VFMADDPSr132r
    570795191U,	// VFMADDPSr132rY
    571532612U,	// VFMADDPSr213m
    571548996U,	// VFMADDPSr213mY
    570795332U,	// VFMADDPSr213r
    570795332U,	// VFMADDPSr213rY
    571532385U,	// VFMADDPSr231m
    571548769U,	// VFMADDPSr231mY
    570795105U,	// VFMADDPSr231r
    570795105U,	// VFMADDPSr231rY
    312825250U,	// VFMADDSD4mr
    312825250U,	// VFMADDSD4mr_Int
    1357813154U,	// VFMADDSD4rm
    1357813154U,	// VFMADDSD4rm_Int
    302355874U,	// VFMADDSD4rr
    302355874U,	// VFMADDSD4rr_Int
    302355874U,	// VFMADDSD4rr_REV
    571524208U,	// VFMADDSDZm
    570786928U,	// VFMADDSDZr
    1357813025U,	// VFMADDSDr132m
    570791201U,	// VFMADDSDr132r
    1357813090U,	// VFMADDSDr213m
    570791266U,	// VFMADDSDr213r
    1357812971U,	// VFMADDSDr231m
    570791147U,	// VFMADDSDr231r
    317024315U,	// VFMADDSS4mr
    317024315U,	// VFMADDSS4mr_Int
    1357850683U,	// VFMADDSS4rm
    1357850683U,	// VFMADDSS4rm_Int
    302360635U,	// VFMADDSS4rr
    302360635U,	// VFMADDSS4rr_Int
    302360635U,	// VFMADDSS4rr_REV
    571526026U,	// VFMADDSSZm
    570788746U,	// VFMADDSSZr
    1357850562U,	// VFMADDSSr132m
    570795970U,	// VFMADDSSr132r
    1357850627U,	// VFMADDSSr213m
    570796035U,	// VFMADDSSr213r
    1357850508U,	// VFMADDSSr231m
    570795916U,	// VFMADDSSr231r
    571392406U,	// VFMADDSUB132PDZm
    1427014038U,	// VFMADDSUB132PDZmb
    571394325U,	// VFMADDSUB132PSZm
    1429145877U,	// VFMADDSUB132PSZmb
    571392559U,	// VFMADDSUB213PDZm
    1427014191U,	// VFMADDSUB213PDZmb
    570786351U,	// VFMADDSUB213PDZr
    1888076335U,	// VFMADDSUB213PDZrk
    1888076335U,	// VFMADDSUB213PDZrkz
    571394478U,	// VFMADDSUB213PSZm
    1429146030U,	// VFMADDSUB213PSZmb
    570788270U,	// VFMADDSUB213PSZr
    1888078254U,	// VFMADDSUB213PSZrk
    1888078254U,	// VFMADDSUB213PSZrkz
    300241695U,	// VFMADDSUBPD4mr
    92623647U,	// VFMADDSUBPD4mrY
    303092511U,	// VFMADDSUBPD4rm
    303108895U,	// VFMADDSUBPD4rmY
    302355231U,	// VFMADDSUBPD4rr
    302355231U,	// VFMADDSUBPD4rrY
    302355231U,	// VFMADDSUBPD4rrY_REV
    302355231U,	// VFMADDSUBPD4rr_REV
    571527734U,	// VFMADDSUBPDr132m
    571544118U,	// VFMADDSUBPDr132mY
    570790454U,	// VFMADDSUBPDr132r
    570790454U,	// VFMADDSUBPDr132rY
    571527864U,	// VFMADDSUBPDr213m
    571544248U,	// VFMADDSUBPDr213mY
    570790584U,	// VFMADDSUBPDr213r
    570790584U,	// VFMADDSUBPDr213rY
    571527648U,	// VFMADDSUBPDr231m
    571544032U,	// VFMADDSUBPDr231mY
    570790368U,	// VFMADDSUBPDr231r
    570790368U,	// VFMADDSUBPDr231rY
    300246376U,	// VFMADDSUBPS4mr
    92628328U,	// VFMADDSUBPS4mrY
    303097192U,	// VFMADDSUBPS4rm
    303113576U,	// VFMADDSUBPS4rmY
    302359912U,	// VFMADDSUBPS4rr
    302359912U,	// VFMADDSUBPS4rrY
    302359912U,	// VFMADDSUBPS4rrY_REV
    302359912U,	// VFMADDSUBPS4rr_REV
    571532412U,	// VFMADDSUBPSr132m
    571548796U,	// VFMADDSUBPSr132mY
    570795132U,	// VFMADDSUBPSr132r
    570795132U,	// VFMADDSUBPSr132rY
    571532553U,	// VFMADDSUBPSr213m
    571548937U,	// VFMADDSUBPSr213mY
    570795273U,	// VFMADDSUBPSr213r
    570795273U,	// VFMADDSUBPSr213rY
    571532326U,	// VFMADDSUBPSr231m
    571548710U,	// VFMADDSUBPSr231mY
    570795046U,	// VFMADDSUBPSr231r
    570795046U,	// VFMADDSUBPSr231rY
    571392423U,	// VFMSUB132PDZm
    1427014055U,	// VFMSUB132PDZmb
    571394342U,	// VFMSUB132PSZm
    1429145894U,	// VFMSUB132PSZmb
    571392576U,	// VFMSUB213PDZm
    1427014208U,	// VFMSUB213PDZmb
    570786368U,	// VFMSUB213PDZr
    1888076352U,	// VFMSUB213PDZrk
    1888076352U,	// VFMSUB213PDZrkz
    571394495U,	// VFMSUB213PSZm
    1429146047U,	// VFMSUB213PSZmb
    570788287U,	// VFMSUB213PSZr
    1888078271U,	// VFMSUB213PSZrk
    1888078271U,	// VFMSUB213PSZrkz
    571392452U,	// VFMSUBADD132PDZm
    1427014084U,	// VFMSUBADD132PDZmb
    571394371U,	// VFMSUBADD132PSZm
    1429145923U,	// VFMSUBADD132PSZmb
    571392605U,	// VFMSUBADD213PDZm
    1427014237U,	// VFMSUBADD213PDZmb
    570786397U,	// VFMSUBADD213PDZr
    1888076381U,	// VFMSUBADD213PDZrk
    1888076381U,	// VFMSUBADD213PDZrkz
    571394524U,	// VFMSUBADD213PSZm
    1429146076U,	// VFMSUBADD213PSZmb
    570788316U,	// VFMSUBADD213PSZr
    1888078300U,	// VFMSUBADD213PSZrk
    1888078300U,	// VFMSUBADD213PSZrkz
    300241757U,	// VFMSUBADDPD4mr
    92623709U,	// VFMSUBADDPD4mrY
    303092573U,	// VFMSUBADDPD4rm
    303108957U,	// VFMSUBADDPD4rmY
    302355293U,	// VFMSUBADDPD4rr
    302355293U,	// VFMSUBADDPD4rrY
    302355293U,	// VFMSUBADDPD4rrY_REV
    302355293U,	// VFMSUBADDPD4rr_REV
    571527777U,	// VFMSUBADDPDr132m
    571544161U,	// VFMSUBADDPDr132mY
    570790497U,	// VFMSUBADDPDr132r
    570790497U,	// VFMSUBADDPDr132rY
    571527907U,	// VFMSUBADDPDr213m
    571544291U,	// VFMSUBADDPDr213mY
    570790627U,	// VFMSUBADDPDr213r
    570790627U,	// VFMSUBADDPDr213rY
    571527691U,	// VFMSUBADDPDr231m
    571544075U,	// VFMSUBADDPDr231mY
    570790411U,	// VFMSUBADDPDr231r
    570790411U,	// VFMSUBADDPDr231rY
    300246438U,	// VFMSUBADDPS4mr
    92628390U,	// VFMSUBADDPS4mrY
    303097254U,	// VFMSUBADDPS4rm
    303113638U,	// VFMSUBADDPS4rmY
    302359974U,	// VFMSUBADDPS4rr
    302359974U,	// VFMSUBADDPS4rrY
    302359974U,	// VFMSUBADDPS4rrY_REV
    302359974U,	// VFMSUBADDPS4rr_REV
    571532455U,	// VFMSUBADDPSr132m
    571548839U,	// VFMSUBADDPSr132mY
    570795175U,	// VFMSUBADDPSr132r
    570795175U,	// VFMSUBADDPSr132rY
    571532596U,	// VFMSUBADDPSr213m
    571548980U,	// VFMSUBADDPSr213mY
    570795316U,	// VFMSUBADDPSr213r
    570795316U,	// VFMSUBADDPSr213rY
    571532369U,	// VFMSUBADDPSr231m
    571548753U,	// VFMSUBADDPSr231mY
    570795089U,	// VFMSUBADDPSr231r
    570795089U,	// VFMSUBADDPSr231rY
    300241728U,	// VFMSUBPD4mr
    92623680U,	// VFMSUBPD4mrY
    303092544U,	// VFMSUBPD4rm
    303108928U,	// VFMSUBPD4rmY
    302355264U,	// VFMSUBPD4rr
    302355264U,	// VFMSUBPD4rrY
    302355264U,	// VFMSUBPD4rrY_REV
    302355264U,	// VFMSUBPD4rr_REV
    571527750U,	// VFMSUBPDr132m
    571544134U,	// VFMSUBPDr132mY
    570790470U,	// VFMSUBPDr132r
    570790470U,	// VFMSUBPDr132rY
    571527880U,	// VFMSUBPDr213m
    571544264U,	// VFMSUBPDr213mY
    570790600U,	// VFMSUBPDr213r
    570790600U,	// VFMSUBPDr213rY
    571527664U,	// VFMSUBPDr231m
    571544048U,	// VFMSUBPDr231mY
    570790384U,	// VFMSUBPDr231r
    570790384U,	// VFMSUBPDr231rY
    300246409U,	// VFMSUBPS4mr
    92628361U,	// VFMSUBPS4mrY
    303097225U,	// VFMSUBPS4rm
    303113609U,	// VFMSUBPS4rmY
    302359945U,	// VFMSUBPS4rr
    302359945U,	// VFMSUBPS4rrY
    302359945U,	// VFMSUBPS4rrY_REV
    302359945U,	// VFMSUBPS4rr_REV
    571532428U,	// VFMSUBPSr132m
    571548812U,	// VFMSUBPSr132mY
    570795148U,	// VFMSUBPSr132r
    570795148U,	// VFMSUBPSr132rY
    571532569U,	// VFMSUBPSr213m
    571548953U,	// VFMSUBPSr213mY
    570795289U,	// VFMSUBPSr213r
    570795289U,	// VFMSUBPSr213rY
    571532342U,	// VFMSUBPSr231m
    571548726U,	// VFMSUBPSr231mY
    570795062U,	// VFMSUBPSr231r
    570795062U,	// VFMSUBPSr231rY
    312825221U,	// VFMSUBSD4mr
    312825221U,	// VFMSUBSD4mr_Int
    1357813125U,	// VFMSUBSD4rm
    1357813125U,	// VFMSUBSD4rm_Int
    302355845U,	// VFMSUBSD4rr
    302355845U,	// VFMSUBSD4rr_Int
    302355845U,	// VFMSUBSD4rr_REV
    571524179U,	// VFMSUBSDZm
    570786899U,	// VFMSUBSDZr
    1357812998U,	// VFMSUBSDr132m
    570791174U,	// VFMSUBSDr132r
    1357813063U,	// VFMSUBSDr213m
    570791239U,	// VFMSUBSDr213r
    1357812944U,	// VFMSUBSDr231m
    570791120U,	// VFMSUBSDr231r
    317024286U,	// VFMSUBSS4mr
    317024286U,	// VFMSUBSS4mr_Int
    1357850654U,	// VFMSUBSS4rm
    1357850654U,	// VFMSUBSS4rm_Int
    302360606U,	// VFMSUBSS4rr
    302360606U,	// VFMSUBSS4rr_Int
    302360606U,	// VFMSUBSS4rr_REV
    571525997U,	// VFMSUBSSZm
    570788717U,	// VFMSUBSSZr
    1357850535U,	// VFMSUBSSr132m
    570795943U,	// VFMSUBSSr132r
    1357850600U,	// VFMSUBSSr213m
    570796008U,	// VFMSUBSSr213r
    1357850481U,	// VFMSUBSSr231m
    570795889U,	// VFMSUBSSr231r
    571392483U,	// VFNMADD132PDZm
    1427014115U,	// VFNMADD132PDZmb
    571394402U,	// VFNMADD132PSZm
    1429145954U,	// VFNMADD132PSZmb
    571392636U,	// VFNMADD213PDZm
    1427014268U,	// VFNMADD213PDZmb
    570786428U,	// VFNMADD213PDZr
    1888076412U,	// VFNMADD213PDZrk
    1888076412U,	// VFNMADD213PDZrkz
    571394555U,	// VFNMADD213PSZm
    1429146107U,	// VFNMADD213PSZmb
    570788347U,	// VFNMADD213PSZr
    1888078331U,	// VFNMADD213PSZrk
    1888078331U,	// VFNMADD213PSZrkz
    300241789U,	// VFNMADDPD4mr
    92623741U,	// VFNMADDPD4mrY
    303092605U,	// VFNMADDPD4rm
    303108989U,	// VFNMADDPD4rmY
    302355325U,	// VFNMADDPD4rr
    302355325U,	// VFNMADDPD4rrY
    302355325U,	// VFNMADDPD4rrY_REV
    302355325U,	// VFNMADDPD4rr_REV
    571527806U,	// VFNMADDPDr132m
    571544190U,	// VFNMADDPDr132mY
    570790526U,	// VFNMADDPDr132r
    570790526U,	// VFNMADDPDr132rY
    571527936U,	// VFNMADDPDr213m
    571544320U,	// VFNMADDPDr213mY
    570790656U,	// VFNMADDPDr213r
    570790656U,	// VFNMADDPDr213rY
    571527720U,	// VFNMADDPDr231m
    571544104U,	// VFNMADDPDr231mY
    570790440U,	// VFNMADDPDr231r
    570790440U,	// VFNMADDPDr231rY
    300246470U,	// VFNMADDPS4mr
    92628422U,	// VFNMADDPS4mrY
    303097286U,	// VFNMADDPS4rm
    303113670U,	// VFNMADDPS4rmY
    302360006U,	// VFNMADDPS4rr
    302360006U,	// VFNMADDPS4rrY
    302360006U,	// VFNMADDPS4rrY_REV
    302360006U,	// VFNMADDPS4rr_REV
    571532484U,	// VFNMADDPSr132m
    571548868U,	// VFNMADDPSr132mY
    570795204U,	// VFNMADDPSr132r
    570795204U,	// VFNMADDPSr132rY
    571532625U,	// VFNMADDPSr213m
    571549009U,	// VFNMADDPSr213mY
    570795345U,	// VFNMADDPSr213r
    570795345U,	// VFNMADDPSr213rY
    571532398U,	// VFNMADDPSr231m
    571548782U,	// VFNMADDPSr231mY
    570795118U,	// VFNMADDPSr231r
    570795118U,	// VFNMADDPSr231rY
    312825260U,	// VFNMADDSD4mr
    312825260U,	// VFNMADDSD4mr_Int
    1357813164U,	// VFNMADDSD4rm
    1357813164U,	// VFNMADDSD4rm_Int
    302355884U,	// VFNMADDSD4rr
    302355884U,	// VFNMADDSD4rr_Int
    302355884U,	// VFNMADDSD4rr_REV
    571524222U,	// VFNMADDSDZm
    570786942U,	// VFNMADDSDZr
    1357813038U,	// VFNMADDSDr132m
    570791214U,	// VFNMADDSDr132r
    1357813103U,	// VFNMADDSDr213m
    570791279U,	// VFNMADDSDr213r
    1357812984U,	// VFNMADDSDr231m
    570791160U,	// VFNMADDSDr231r
    317024325U,	// VFNMADDSS4mr
    317024325U,	// VFNMADDSS4mr_Int
    1357850693U,	// VFNMADDSS4rm
    1357850693U,	// VFNMADDSS4rm_Int
    302360645U,	// VFNMADDSS4rr
    302360645U,	// VFNMADDSS4rr_Int
    302360645U,	// VFNMADDSS4rr_REV
    571526040U,	// VFNMADDSSZm
    570788760U,	// VFNMADDSSZr
    1357850575U,	// VFNMADDSSr132m
    570795983U,	// VFNMADDSSr132r
    1357850640U,	// VFNMADDSSr213m
    570796048U,	// VFNMADDSSr213r
    1357850521U,	// VFNMADDSSr231m
    570795929U,	// VFNMADDSSr231r
    571392437U,	// VFNMSUB132PDZm
    1427014069U,	// VFNMSUB132PDZmb
    571394356U,	// VFNMSUB132PSZm
    1429145908U,	// VFNMSUB132PSZmb
    571392590U,	// VFNMSUB213PDZm
    1427014222U,	// VFNMSUB213PDZmb
    570786382U,	// VFNMSUB213PDZr
    1888076366U,	// VFNMSUB213PDZrk
    1888076366U,	// VFNMSUB213PDZrkz
    571394509U,	// VFNMSUB213PSZm
    1429146061U,	// VFNMSUB213PSZmb
    570788301U,	// VFNMSUB213PSZr
    1888078285U,	// VFNMSUB213PSZrk
    1888078285U,	// VFNMSUB213PSZrkz
    300241738U,	// VFNMSUBPD4mr
    92623690U,	// VFNMSUBPD4mrY
    303092554U,	// VFNMSUBPD4rm
    303108938U,	// VFNMSUBPD4rmY
    302355274U,	// VFNMSUBPD4rr
    302355274U,	// VFNMSUBPD4rrY
    302355274U,	// VFNMSUBPD4rrY_REV
    302355274U,	// VFNMSUBPD4rr_REV
    571527763U,	// VFNMSUBPDr132m
    571544147U,	// VFNMSUBPDr132mY
    570790483U,	// VFNMSUBPDr132r
    570790483U,	// VFNMSUBPDr132rY
    571527893U,	// VFNMSUBPDr213m
    571544277U,	// VFNMSUBPDr213mY
    570790613U,	// VFNMSUBPDr213r
    570790613U,	// VFNMSUBPDr213rY
    571527677U,	// VFNMSUBPDr231m
    571544061U,	// VFNMSUBPDr231mY
    570790397U,	// VFNMSUBPDr231r
    570790397U,	// VFNMSUBPDr231rY
    300246419U,	// VFNMSUBPS4mr
    92628371U,	// VFNMSUBPS4mrY
    303097235U,	// VFNMSUBPS4rm
    303113619U,	// VFNMSUBPS4rmY
    302359955U,	// VFNMSUBPS4rr
    302359955U,	// VFNMSUBPS4rrY
    302359955U,	// VFNMSUBPS4rrY_REV
    302359955U,	// VFNMSUBPS4rr_REV
    571532441U,	// VFNMSUBPSr132m
    571548825U,	// VFNMSUBPSr132mY
    570795161U,	// VFNMSUBPSr132r
    570795161U,	// VFNMSUBPSr132rY
    571532582U,	// VFNMSUBPSr213m
    571548966U,	// VFNMSUBPSr213mY
    570795302U,	// VFNMSUBPSr213r
    570795302U,	// VFNMSUBPSr213rY
    571532355U,	// VFNMSUBPSr231m
    571548739U,	// VFNMSUBPSr231mY
    570795075U,	// VFNMSUBPSr231r
    570795075U,	// VFNMSUBPSr231rY
    312825231U,	// VFNMSUBSD4mr
    312825231U,	// VFNMSUBSD4mr_Int
    1357813135U,	// VFNMSUBSD4rm
    1357813135U,	// VFNMSUBSD4rm_Int
    302355855U,	// VFNMSUBSD4rr
    302355855U,	// VFNMSUBSD4rr_Int
    302355855U,	// VFNMSUBSD4rr_REV
    571524193U,	// VFNMSUBSDZm
    570786913U,	// VFNMSUBSDZr
    1357813011U,	// VFNMSUBSDr132m
    570791187U,	// VFNMSUBSDr132r
    1357813076U,	// VFNMSUBSDr213m
    570791252U,	// VFNMSUBSDr213r
    1357812957U,	// VFNMSUBSDr231m
    570791133U,	// VFNMSUBSDr231r
    317024296U,	// VFNMSUBSS4mr
    317024296U,	// VFNMSUBSS4mr_Int
    1357850664U,	// VFNMSUBSS4rm
    1357850664U,	// VFNMSUBSS4rm_Int
    302360616U,	// VFNMSUBSS4rr
    302360616U,	// VFNMSUBSS4rr_Int
    302360616U,	// VFNMSUBSS4rr_REV
    571526011U,	// VFNMSUBSSZm
    570788731U,	// VFNMSUBSSZr
    1357850548U,	// VFNMSUBSSr132m
    570795956U,	// VFNMSUBSSr132r
    1357850613U,	// VFNMSUBSSr213m
    570796021U,	// VFNMSUBSSr213r
    1357850494U,	// VFNMSUBSSr231m
    570795902U,	// VFNMSUBSSr231r
    578719U,	// VFRCZPDrm
    1070239U,	// VFRCZPDrmY
    551818399U,	// VFRCZPDrr
    551818399U,	// VFRCZPDrrY
    583494U,	// VFRCZPSrm
    1075014U,	// VFRCZPSrmY
    551823174U,	// VFRCZPSrr
    551823174U,	// VFRCZPSrrY
    595518U,	// VFRCZSDrm
    551818814U,	// VFRCZSDrr
    616663U,	// VFRCZSSrm
    551823575U,	// VFRCZSSrr
    811734040U,	// VFsANDNPDrm
    811652120U,	// VFsANDNPDrr
    811738750U,	// VFsANDNPSrm
    811656830U,	// VFsANDNPSrr
    811733904U,	// VFsANDPDrm
    811651984U,	// VFsANDPDrr
    811738585U,	// VFsANDPSrm
    811656665U,	// VFsANDPSrr
    811734084U,	// VFsORPDrm
    811652164U,	// VFsORPDrr
    811738802U,	// VFsORPSrm
    811656882U,	// VFsORPSrr
    811734091U,	// VFsXORPDrm
    811652171U,	// VFsXORPDrr
    811738809U,	// VFsXORPSrm
    811656889U,	// VFsXORPSrr
    108090284U,	// VGATHERDPDYrm
    552698639U,	// VGATHERDPDZrm
    108090284U,	// VGATHERDPDrm
    110192117U,	// VGATHERDPSYrm
    552716942U,	// VGATHERDPSZrm
    110192117U,	// VGATHERDPSrm
    111395524U,	// VGATHERPF0DPDm
    111397443U,	// VGATHERPF0DPSm
    111428479U,	// VGATHERPF0QPDm
    111430398U,	// VGATHERPF0QPSm
    111395557U,	// VGATHERPF1DPDm
    111397476U,	// VGATHERPF1DPSm
    111428512U,	// VGATHERPF1QPDm
    111430431U,	// VGATHERPF1QPSm
    108090424U,	// VGATHERQPDYrm
    552698817U,	// VGATHERQPDZrm
    108090424U,	// VGATHERQPDrm
    110192294U,	// VGATHERQPSYrm
    552700736U,	// VGATHERQPSZrm
    110192294U,	// VGATHERQPSrm
    812520298U,	// VHADDPDYrm
    811651946U,	// VHADDPDYrr
    811733866U,	// VHADDPDrm
    811651946U,	// VHADDPDrr
    812524979U,	// VHADDPSYrm
    811656627U,	// VHADDPSYrr
    811738547U,	// VHADDPSrm
    811656627U,	// VHADDPSrr
    812520247U,	// VHSUBPDYrm
    811651895U,	// VHSUBPDYrr
    811733815U,	// VHSUBPDrm
    811651895U,	// VHSUBPDrr
    812524928U,	// VHSUBPSYrm
    811656576U,	// VHSUBPSYrr
    811738496U,	// VHSUBPSrm
    811656576U,	// VHSUBPSrr
    300240367U,	// VINSERTF128rm
    302353903U,	// VINSERTF128rr
    300240229U,	// VINSERTF32x4rm
    302353765U,	// VINSERTF32x4rr
    105205151U,	// VINSERTF64x4rm
    302353823U,	// VINSERTF64x4rr
    340086310U,	// VINSERTI128rm
    302353958U,	// VINSERTI128rr
    340086146U,	// VINSERTI32x4rm
    302353794U,	// VINSERTI32x4rr
    105205180U,	// VINSERTI64x4rm
    302353852U,	// VINSERTI64x4rr
    317023959U,	// VINSERTPSrm
    302360279U,	// VINSERTPSrr
    317023959U,	// VINSERTPSzrm
    302360279U,	// VINSERTPSzrr
    1026443U,	// VLDDQUYrm
    321931U,	// VLDDQUrm
    238501U,	// VLDMXCSR
    551823763U,	// VMASKMOVDQU
    551823763U,	// VMASKMOVDQU64
    3003454603U,	// VMASKMOVPDYmr
    812520587U,	// VMASKMOVPDYrm
    2735019147U,	// VMASKMOVPDmr
    811734155U,	// VMASKMOVPDrm
    3003459378U,	// VMASKMOVPSYmr
    812525362U,	// VMASKMOVPSYrm
    2735023922U,	// VMASKMOVPSmr
    811738930U,	// VMASKMOVPSrm
    812520599U,	// VMAXCPDYrm
    811652247U,	// VMAXCPDYrr
    811734167U,	// VMAXCPDrm
    811652247U,	// VMAXCPDrr
    812525374U,	// VMAXCPSYrm
    811657022U,	// VMAXCPSYrr
    811738942U,	// VMAXCPSrm
    811657022U,	// VMAXCPSrr
    283268662U,	// VMAXCSDrm
    811652662U,	// VMAXCSDrr
    283289807U,	// VMAXCSSrm
    811657423U,	// VMAXCSSrr
    812520599U,	// VMAXPDYrm
    811652247U,	// VMAXPDYrr
    812532709U,	// VMAXPDZrm
    352469989U,	// VMAXPDZrmb
    1427014629U,	// VMAXPDZrmbk
    1427014629U,	// VMAXPDZrmbkz
    571397271U,	// VMAXPDZrmk
    571397271U,	// VMAXPDZrmkz
    811647973U,	// VMAXPDZrr
    570786789U,	// VMAXPDZrrk
    570786789U,	// VMAXPDZrrkz
    811734167U,	// VMAXPDrm
    811652247U,	// VMAXPDrr
    812525374U,	// VMAXPSYrm
    811657022U,	// VMAXPSYrr
    812534628U,	// VMAXPSZrm
    354585444U,	// VMAXPSZrmb
    1429146468U,	// VMAXPSZrmbk
    1429146468U,	// VMAXPSZrmbkz
    571402046U,	// VMAXPSZrmk
    571402046U,	// VMAXPSZrmkz
    811649892U,	// VMAXPSZrr
    570788708U,	// VMAXPSZrrk
    570788708U,	// VMAXPSZrrkz
    811738942U,	// VMAXPSrm
    811657022U,	// VMAXPSrr
    283268662U,	// VMAXSDZrm
    811652662U,	// VMAXSDZrr
    283268662U,	// VMAXSDrm
    283268662U,	// VMAXSDrm_Int
    811652662U,	// VMAXSDrr
    811652662U,	// VMAXSDrr_Int
    283289807U,	// VMAXSSZrm
    811657423U,	// VMAXSSZrr
    283289807U,	// VMAXSSrm
    283289807U,	// VMAXSSrm_Int
    811657423U,	// VMAXSSrr
    811657423U,	// VMAXSSrr_Int
    14519U,	// VMCALL
    402271U,	// VMCLEARm
    14231U,	// VMFUNC
    812520481U,	// VMINCPDYrm
    811652129U,	// VMINCPDYrr
    811734049U,	// VMINCPDrm
    811652129U,	// VMINCPDrr
    812525191U,	// VMINCPSYrm
    811656839U,	// VMINCPSYrr
    811738759U,	// VMINCPSrm
    811656839U,	// VMINCPSrr
    283268589U,	// VMINCSDrm
    811652589U,	// VMINCSDrr
    283289725U,	// VMINCSSrm
    811657341U,	// VMINCSSrr
    812520481U,	// VMINPDYrm
    811652129U,	// VMINPDYrr
    812532589U,	// VMINPDZrm
    352469869U,	// VMINPDZrmb
    1427014509U,	// VMINPDZrmbk
    1427014509U,	// VMINPDZrmbkz
    571397153U,	// VMINPDZrmk
    571397153U,	// VMINPDZrmkz
    811647853U,	// VMINPDZrr
    570786669U,	// VMINPDZrrk
    570786669U,	// VMINPDZrrkz
    811734049U,	// VMINPDrm
    811652129U,	// VMINPDrr
    812525191U,	// VMINPSYrm
    811656839U,	// VMINPSYrr
    812534508U,	// VMINPSZrm
    354585324U,	// VMINPSZrmb
    1429146348U,	// VMINPSZrmbk
    1429146348U,	// VMINPSZrmbkz
    571401863U,	// VMINPSZrmk
    571401863U,	// VMINPSZrmkz
    811649772U,	// VMINPSZrr
    570788588U,	// VMINPSZrrk
    570788588U,	// VMINPSZrrkz
    811738759U,	// VMINPSrm
    811656839U,	// VMINPSrr
    283268589U,	// VMINSDZrm
    811652589U,	// VMINSDZrr
    283268589U,	// VMINSDrm
    283268589U,	// VMINSDrm_Int
    811652589U,	// VMINSDrr
    811652589U,	// VMINSDrr_Int
    283289725U,	// VMINSSZrm
    811657341U,	// VMINSSZrr
    283289725U,	// VMINSSrm
    283289725U,	// VMINSSrm_Int
    811657341U,	// VMINSSrr
    811657341U,	// VMINSSrr_Int
    14419U,	// VMLAUNCH
    15375U,	// VMLOAD32
    15455U,	// VMLOAD64
    14511U,	// VMMCALL
    551822053U,	// VMOV64toPQIZrr
    551822053U,	// VMOV64toPQIrr
    551822053U,	// VMOV64toSDZrr
    551838437U,	// VMOV64toSDrm
    551822053U,	// VMOV64toSDrr
    113283854U,	// VMOVAPDYmr
    1069838U,	// VMOVAPDYrm
    551817998U,	// VMOVAPDYrr
    551817998U,	// VMOVAPDYrr_REV
    62952206U,	// VMOVAPDZ128mr
    1942295310U,	// VMOVAPDZ128mrk
    578318U,	// VMOVAPDZ128rm
    1888539406U,	// VMOVAPDZ128rmk
    1887572750U,	// VMOVAPDZ128rmkz
    551817998U,	// VMOVAPDZ128rr
    551817998U,	// VMOVAPDZ128rr_alt
    1887802126U,	// VMOVAPDZ128rrk
    1887802126U,	// VMOVAPDZ128rrk_alt
    1887490830U,	// VMOVAPDZ128rrkz
    1887490830U,	// VMOVAPDZ128rrkz_alt
    113283854U,	// VMOVAPDZ256mr
    1992626958U,	// VMOVAPDZ256mrk
    1069838U,	// VMOVAPDZ256rm
    1888555790U,	// VMOVAPDZ256rmk
    1888359182U,	// VMOVAPDZ256rmkz
    551817998U,	// VMOVAPDZ256rr
    551817998U,	// VMOVAPDZ256rr_alt
    1887802126U,	// VMOVAPDZ256rrk
    1887802126U,	// VMOVAPDZ256rrk_alt
    1887490830U,	// VMOVAPDZ256rrkz
    1887490830U,	// VMOVAPDZ256rrkz_alt
    115381006U,	// VMOVAPDZmr
    1994724110U,	// VMOVAPDZmrk
    1086222U,	// VMOVAPDZrm
    1888408334U,	// VMOVAPDZrmk
    1888375566U,	// VMOVAPDZrmkz
    551817998U,	// VMOVAPDZrr
    551817998U,	// VMOVAPDZrr_alt
    1887802126U,	// VMOVAPDZrrk
    1887802126U,	// VMOVAPDZrrk_alt
    1887490830U,	// VMOVAPDZrrkz
    1887490830U,	// VMOVAPDZrrkz_alt
    62952206U,	// VMOVAPDmr
    578318U,	// VMOVAPDrm
    551817998U,	// VMOVAPDrr
    551817998U,	// VMOVAPDrr_REV
    113288543U,	// VMOVAPSYmr
    1074527U,	// VMOVAPSYrm
    551822687U,	// VMOVAPSYrr
    551822687U,	// VMOVAPSYrr_REV
    62956895U,	// VMOVAPSZ128mr
    1942299999U,	// VMOVAPSZ128mrk
    583007U,	// VMOVAPSZ128rm
    1888544095U,	// VMOVAPSZ128rmk
    1887577439U,	// VMOVAPSZ128rmkz
    551822687U,	// VMOVAPSZ128rr
    551822687U,	// VMOVAPSZ128rr_alt
    1887806815U,	// VMOVAPSZ128rrk
    1887806815U,	// VMOVAPSZ128rrk_alt
    1887495519U,	// VMOVAPSZ128rrkz
    1887495519U,	// VMOVAPSZ128rrkz_alt
    113288543U,	// VMOVAPSZ256mr
    1992631647U,	// VMOVAPSZ256mrk
    1074527U,	// VMOVAPSZ256rm
    1888560479U,	// VMOVAPSZ256rmk
    1888363871U,	// VMOVAPSZ256rmkz
    551822687U,	// VMOVAPSZ256rr
    551822687U,	// VMOVAPSZ256rr_alt
    1887806815U,	// VMOVAPSZ256rrk
    1887806815U,	// VMOVAPSZ256rrk_alt
    1887495519U,	// VMOVAPSZ256rrkz
    1887495519U,	// VMOVAPSZ256rrkz_alt
    115385695U,	// VMOVAPSZmr
    1994728799U,	// VMOVAPSZmrk
    1090911U,	// VMOVAPSZrm
    1888413023U,	// VMOVAPSZrmk
    1888380255U,	// VMOVAPSZrmkz
    551822687U,	// VMOVAPSZrr
    551822687U,	// VMOVAPSZrr_alt
    1887806815U,	// VMOVAPSZrrk
    1887806815U,	// VMOVAPSZrrk_alt
    1887495519U,	// VMOVAPSZrrkz
    1887495519U,	// VMOVAPSZrrkz_alt
    62956895U,	// VMOVAPSmr
    583007U,	// VMOVAPSrm
    551822687U,	// VMOVAPSrr
    551822687U,	// VMOVAPSrr_REV
    1072604U,	// VMOVDDUPYrm
    551820764U,	// VMOVDDUPYrr
    1082924U,	// VMOVDDUPZrm
    551814700U,	// VMOVDDUPZrr
    597468U,	// VMOVDDUPrm
    551820764U,	// VMOVDDUPrr
    551802546U,	// VMOVDI2PDIZrm
    551818930U,	// VMOVDI2PDIZrr
    551802546U,	// VMOVDI2PDIrm
    551818930U,	// VMOVDI2PDIrr
    551802546U,	// VMOVDI2SSZrm
    551818930U,	// VMOVDI2SSZrr
    551802546U,	// VMOVDI2SSrm
    551818930U,	// VMOVDI2SSrr
    65047782U,	// VMOVDQA32Z128mr
    1944390886U,	// VMOVDQA32Z128mrk
    314598U,	// VMOVDQA32Z128rm
    1888619750U,	// VMOVDQA32Z128rmk
    1887735014U,	// VMOVDQA32Z128rmkz
    551816422U,	// VMOVDQA32Z128rr
    551816422U,	// VMOVDQA32Z128rr_alt
    1887800550U,	// VMOVDQA32Z128rrk
    1887800550U,	// VMOVDQA32Z128rrk_alt
    1887489254U,	// VMOVDQA32Z128rrkz
    1887489254U,	// VMOVDQA32Z128rrkz_alt
    117476582U,	// VMOVDQA32Z256mr
    1996819686U,	// VMOVDQA32Z256mrk
    1019110U,	// VMOVDQA32Z256rm
    1888636134U,	// VMOVDQA32Z256rmk
    1888439526U,	// VMOVDQA32Z256rmkz
    551816422U,	// VMOVDQA32Z256rr
    551816422U,	// VMOVDQA32Z256rr_alt
    1887800550U,	// VMOVDQA32Z256rrk
    1887800550U,	// VMOVDQA32Z256rrk_alt
    1887489254U,	// VMOVDQA32Z256rrkz
    1887489254U,	// VMOVDQA32Z256rrkz_alt
    119573734U,	// VMOVDQA32Zmr
    1998916838U,	// VMOVDQA32Zmrk
    1035494U,	// VMOVDQA32Zrm
    1888652518U,	// VMOVDQA32Zrmk
    1888668902U,	// VMOVDQA32Zrmkz
    551816422U,	// VMOVDQA32Zrr
    551816422U,	// VMOVDQA32Zrr_alt
    1887800550U,	// VMOVDQA32Zrrk
    1887800550U,	// VMOVDQA32Zrrk_alt
    1887489254U,	// VMOVDQA32Zrrkz
    1887489254U,	// VMOVDQA32Zrrkz_alt
    65047861U,	// VMOVDQA64Z128mr
    1944390965U,	// VMOVDQA64Z128mrk
    314677U,	// VMOVDQA64Z128rm
    1888619829U,	// VMOVDQA64Z128rmk
    1887735093U,	// VMOVDQA64Z128rmkz
    551816501U,	// VMOVDQA64Z128rr
    551816501U,	// VMOVDQA64Z128rr_alt
    1887800629U,	// VMOVDQA64Z128rrk
    1887800629U,	// VMOVDQA64Z128rrk_alt
    1887489333U,	// VMOVDQA64Z128rrkz
    1887489333U,	// VMOVDQA64Z128rrkz_alt
    117476661U,	// VMOVDQA64Z256mr
    1996819765U,	// VMOVDQA64Z256mrk
    1019189U,	// VMOVDQA64Z256rm
    1888636213U,	// VMOVDQA64Z256rmk
    1888439605U,	// VMOVDQA64Z256rmkz
    551816501U,	// VMOVDQA64Z256rr
    551816501U,	// VMOVDQA64Z256rr_alt
    1887800629U,	// VMOVDQA64Z256rrk
    1887800629U,	// VMOVDQA64Z256rrk_alt
    1887489333U,	// VMOVDQA64Z256rrkz
    1887489333U,	// VMOVDQA64Z256rrkz_alt
    119573813U,	// VMOVDQA64Zmr
    1998916917U,	// VMOVDQA64Zmrk
    1035573U,	// VMOVDQA64Zrm
    1888652597U,	// VMOVDQA64Zrmk
    1888668981U,	// VMOVDQA64Zrmkz
    551816501U,	// VMOVDQA64Zrr
    551816501U,	// VMOVDQA64Zrr_alt
    1887800629U,	// VMOVDQA64Zrrk
    1887800629U,	// VMOVDQA64Zrrk_alt
    1887489333U,	// VMOVDQA64Zrrkz
    1887489333U,	// VMOVDQA64Zrrkz_alt
    117476956U,	// VMOVDQAYmr
    1019484U,	// VMOVDQAYrm
    551816796U,	// VMOVDQAYrr
    551816796U,	// VMOVDQAYrr_REV
    65048156U,	// VMOVDQAmr
    314972U,	// VMOVDQArm
    551816796U,	// VMOVDQArr
    551816796U,	// VMOVDQArr_REV
    65048010U,	// VMOVDQU16Z128mr
    1944391114U,	// VMOVDQU16Z128mrk
    314826U,	// VMOVDQU16Z128rm
    1888619978U,	// VMOVDQU16Z128rmk
    1887735242U,	// VMOVDQU16Z128rmkz
    551816650U,	// VMOVDQU16Z128rr
    551816650U,	// VMOVDQU16Z128rr_alt
    1887800778U,	// VMOVDQU16Z128rrk
    1887800778U,	// VMOVDQU16Z128rrk_alt
    1887489482U,	// VMOVDQU16Z128rrkz
    1887489482U,	// VMOVDQU16Z128rrkz_alt
    117476810U,	// VMOVDQU16Z256mr
    1996819914U,	// VMOVDQU16Z256mrk
    1019338U,	// VMOVDQU16Z256rm
    1888636362U,	// VMOVDQU16Z256rmk
    1888439754U,	// VMOVDQU16Z256rmkz
    551816650U,	// VMOVDQU16Z256rr
    551816650U,	// VMOVDQU16Z256rr_alt
    1887800778U,	// VMOVDQU16Z256rrk
    1887800778U,	// VMOVDQU16Z256rrk_alt
    1887489482U,	// VMOVDQU16Z256rrkz
    1887489482U,	// VMOVDQU16Z256rrkz_alt
    119573962U,	// VMOVDQU16Zmr
    1998917066U,	// VMOVDQU16Zmrk
    1035722U,	// VMOVDQU16Zrm
    1888652746U,	// VMOVDQU16Zrmk
    1888669130U,	// VMOVDQU16Zrmkz
    551816650U,	// VMOVDQU16Zrr
    551816650U,	// VMOVDQU16Zrr_alt
    1887800778U,	// VMOVDQU16Zrrk
    1887800778U,	// VMOVDQU16Zrrk_alt
    1887489482U,	// VMOVDQU16Zrrkz
    1887489482U,	// VMOVDQU16Zrrkz_alt
    65047793U,	// VMOVDQU32Z128mr
    1944390897U,	// VMOVDQU32Z128mrk
    314609U,	// VMOVDQU32Z128rm
    1888619761U,	// VMOVDQU32Z128rmk
    1887735025U,	// VMOVDQU32Z128rmkz
    551816433U,	// VMOVDQU32Z128rr
    551816433U,	// VMOVDQU32Z128rr_alt
    1887800561U,	// VMOVDQU32Z128rrk
    1887800561U,	// VMOVDQU32Z128rrk_alt
    1887489265U,	// VMOVDQU32Z128rrkz
    1887489265U,	// VMOVDQU32Z128rrkz_alt
    117476593U,	// VMOVDQU32Z256mr
    1996819697U,	// VMOVDQU32Z256mrk
    1019121U,	// VMOVDQU32Z256rm
    1888636145U,	// VMOVDQU32Z256rmk
    1888439537U,	// VMOVDQU32Z256rmkz
    551816433U,	// VMOVDQU32Z256rr
    551816433U,	// VMOVDQU32Z256rr_alt
    1887800561U,	// VMOVDQU32Z256rrk
    1887800561U,	// VMOVDQU32Z256rrk_alt
    1887489265U,	// VMOVDQU32Z256rrkz
    1887489265U,	// VMOVDQU32Z256rrkz_alt
    119573745U,	// VMOVDQU32Zmr
    1998916849U,	// VMOVDQU32Zmrk
    1035505U,	// VMOVDQU32Zrm
    1888652529U,	// VMOVDQU32Zrmk
    1888668913U,	// VMOVDQU32Zrmkz
    551816433U,	// VMOVDQU32Zrr
    551816433U,	// VMOVDQU32Zrr_alt
    1887800561U,	// VMOVDQU32Zrrk
    1887800561U,	// VMOVDQU32Zrrk_alt
    1887489265U,	// VMOVDQU32Zrrkz
    1887489265U,	// VMOVDQU32Zrrkz_alt
    65047872U,	// VMOVDQU64Z128mr
    1944390976U,	// VMOVDQU64Z128mrk
    314688U,	// VMOVDQU64Z128rm
    1888619840U,	// VMOVDQU64Z128rmk
    1887735104U,	// VMOVDQU64Z128rmkz
    551816512U,	// VMOVDQU64Z128rr
    551816512U,	// VMOVDQU64Z128rr_alt
    1887800640U,	// VMOVDQU64Z128rrk
    1887800640U,	// VMOVDQU64Z128rrk_alt
    1887489344U,	// VMOVDQU64Z128rrkz
    1887489344U,	// VMOVDQU64Z128rrkz_alt
    117476672U,	// VMOVDQU64Z256mr
    1996819776U,	// VMOVDQU64Z256mrk
    1019200U,	// VMOVDQU64Z256rm
    1888636224U,	// VMOVDQU64Z256rmk
    1888439616U,	// VMOVDQU64Z256rmkz
    551816512U,	// VMOVDQU64Z256rr
    551816512U,	// VMOVDQU64Z256rr_alt
    1887800640U,	// VMOVDQU64Z256rrk
    1887800640U,	// VMOVDQU64Z256rrk_alt
    1887489344U,	// VMOVDQU64Z256rrkz
    1887489344U,	// VMOVDQU64Z256rrkz_alt
    119573824U,	// VMOVDQU64Zmr
    1998916928U,	// VMOVDQU64Zmrk
    1035584U,	// VMOVDQU64Zrm
    1888652608U,	// VMOVDQU64Zrmk
    1888668992U,	// VMOVDQU64Zrmkz
    551816512U,	// VMOVDQU64Zrr
    551816512U,	// VMOVDQU64Zrr_alt
    1887800640U,	// VMOVDQU64Zrrk
    1887800640U,	// VMOVDQU64Zrrk_alt
    1887489344U,	// VMOVDQU64Zrrkz
    1887489344U,	// VMOVDQU64Zrrkz_alt
    65048131U,	// VMOVDQU8Z128mr
    1944391235U,	// VMOVDQU8Z128mrk
    314947U,	// VMOVDQU8Z128rm
    1888620099U,	// VMOVDQU8Z128rmk
    1887735363U,	// VMOVDQU8Z128rmkz
    551816771U,	// VMOVDQU8Z128rr
    551816771U,	// VMOVDQU8Z128rr_alt
    1887800899U,	// VMOVDQU8Z128rrk
    1887800899U,	// VMOVDQU8Z128rrk_alt
    1887489603U,	// VMOVDQU8Z128rrkz
    1887489603U,	// VMOVDQU8Z128rrkz_alt
    117476931U,	// VMOVDQU8Z256mr
    1996820035U,	// VMOVDQU8Z256mrk
    1019459U,	// VMOVDQU8Z256rm
    1888636483U,	// VMOVDQU8Z256rmk
    1888439875U,	// VMOVDQU8Z256rmkz
    551816771U,	// VMOVDQU8Z256rr
    551816771U,	// VMOVDQU8Z256rr_alt
    1887800899U,	// VMOVDQU8Z256rrk
    1887800899U,	// VMOVDQU8Z256rrk_alt
    1887489603U,	// VMOVDQU8Z256rrkz
    1887489603U,	// VMOVDQU8Z256rrkz_alt
    119574083U,	// VMOVDQU8Zmr
    1998917187U,	// VMOVDQU8Zmrk
    1035843U,	// VMOVDQU8Zrm
    1888652867U,	// VMOVDQU8Zrmk
    1888669251U,	// VMOVDQU8Zrmkz
    551816771U,	// VMOVDQU8Zrr
    551816771U,	// VMOVDQU8Zrr_alt
    1887800899U,	// VMOVDQU8Zrrk
    1887800899U,	// VMOVDQU8Zrrk_alt
    1887489603U,	// VMOVDQU8Zrrkz
    1887489603U,	// VMOVDQU8Zrrkz_alt
    117483936U,	// VMOVDQUYmr
    1026464U,	// VMOVDQUYrm
    551823776U,	// VMOVDQUYrr
    551823776U,	// VMOVDQUYrr_REV
    65055136U,	// VMOVDQUmr
    321952U,	// VMOVDQUrm
    551823776U,	// VMOVDQUrr
    551823776U,	// VMOVDQUrr_REV
    811656755U,	// VMOVHLPSZrr
    811656755U,	// VMOVHLPSrr
    67146700U,	// VMOVHPDmr
    283268044U,	// VMOVHPDrm
    67151391U,	// VMOVHPSmr
    283272735U,	// VMOVHPSrm
    811656725U,	// VMOVLHPSZrr
    811656725U,	// VMOVLHPSrr
    67146750U,	// VMOVLPDmr
    283268094U,	// VMOVLPDrm
    67151451U,	// VMOVLPSmr
    283272795U,	// VMOVLPSrm
    551818197U,	// VMOVMSKPDYrr
    551818197U,	// VMOVMSKPDrr
    551822888U,	// VMOVMSKPSYrr
    551822888U,	// VMOVMSKPSrr
    1019473U,	// VMOVNTDQAYrm
    314961U,	// VMOVNTDQAZ128rm
    1019473U,	// VMOVNTDQAZ256rm
    1035857U,	// VMOVNTDQAZrm
    314961U,	// VMOVNTDQArm
    113287076U,	// VMOVNTDQYmr
    65052580U,	// VMOVNTDQZ128mr
    117481380U,	// VMOVNTDQZ256mr
    119578532U,	// VMOVNTDQZmr
    62955428U,	// VMOVNTDQmr
    113284179U,	// VMOVNTPDYmr
    62952531U,	// VMOVNTPDZ128mr
    113284179U,	// VMOVNTPDZ256mr
    115381331U,	// VMOVNTPDZmr
    62952531U,	// VMOVNTPDmr
    113288909U,	// VMOVNTPSYmr
    62957261U,	// VMOVNTPSZ128mr
    113288909U,	// VMOVNTPSZ256mr
    115386061U,	// VMOVNTPSZmr
    62957261U,	// VMOVNTPSmr
    12621490U,	// VMOVPDI2DIZmr
    551818930U,	// VMOVPDI2DIZrr
    12621490U,	// VMOVPDI2DImr
    551818930U,	// VMOVPDI2DIrr
    18916069U,	// VMOVPQI2QImr
    551822053U,	// VMOVPQI2QIrr
    18916069U,	// VMOVPQIto64Zmr
    551822053U,	// VMOVPQIto64Zrr
    551822053U,	// VMOVPQIto64rr
    551838437U,	// VMOVQI2PQIZrm
    551838437U,	// VMOVQI2PQIrm
    67142893U,	// VMOVSDZmr
    591085U,	// VMOVSDZrm
    811648237U,	// VMOVSDZrr
    811652645U,	// VMOVSDZrr_REV
    1888077037U,	// VMOVSDZrrk
    67147301U,	// VMOVSDmr
    595493U,	// VMOVSDrm
    811652645U,	// VMOVSDrr
    811652645U,	// VMOVSDrr_REV
    18916069U,	// VMOVSDto64Zmr
    551822053U,	// VMOVSDto64Zrr
    18916069U,	// VMOVSDto64mr
    551822053U,	// VMOVSDto64rr
    1072614U,	// VMOVSHDUPYrm
    551820774U,	// VMOVSHDUPYrr
    1082935U,	// VMOVSHDUPZrm
    551814711U,	// VMOVSHDUPZrr
    581094U,	// VMOVSHDUPrm
    551820774U,	// VMOVSHDUPrr
    1072625U,	// VMOVSLDUPYrm
    551820785U,	// VMOVSLDUPYrr
    1082947U,	// VMOVSLDUPZrm
    551814723U,	// VMOVSLDUPZrr
    581105U,	// VMOVSLDUPrm
    551820785U,	// VMOVSLDUPrr
    12621490U,	// VMOVSS2DIZmr
    551818930U,	// VMOVSS2DIZrr
    12621490U,	// VMOVSS2DImr
    551818930U,	// VMOVSS2DIrr
    69241844U,	// VMOVSSZmr
    609268U,	// VMOVSSZrm
    811650036U,	// VMOVSSZrr
    811657415U,	// VMOVSSZrr_REV
    1888078836U,	// VMOVSSZrrk
    69249223U,	// VMOVSSmr
    616647U,	// VMOVSSrm
    811657415U,	// VMOVSSrr
    811657415U,	// VMOVSSrr_REV
    113284207U,	// VMOVUPDYmr
    1070191U,	// VMOVUPDYrm
    551818351U,	// VMOVUPDYrr
    551818351U,	// VMOVUPDYrr_REV
    62952559U,	// VMOVUPDZ128mr
    1942295663U,	// VMOVUPDZ128mrk
    578671U,	// VMOVUPDZ128rm
    1888539759U,	// VMOVUPDZ128rmk
    1887573103U,	// VMOVUPDZ128rmkz
    551818351U,	// VMOVUPDZ128rr
    551818351U,	// VMOVUPDZ128rr_alt
    1887802479U,	// VMOVUPDZ128rrk
    1887802479U,	// VMOVUPDZ128rrk_alt
    1887491183U,	// VMOVUPDZ128rrkz
    1887491183U,	// VMOVUPDZ128rrkz_alt
    113284207U,	// VMOVUPDZ256mr
    1992627311U,	// VMOVUPDZ256mrk
    1070191U,	// VMOVUPDZ256rm
    1888556143U,	// VMOVUPDZ256rmk
    1888359535U,	// VMOVUPDZ256rmkz
    551818351U,	// VMOVUPDZ256rr
    551818351U,	// VMOVUPDZ256rr_alt
    1887802479U,	// VMOVUPDZ256rrk
    1887802479U,	// VMOVUPDZ256rrk_alt
    1887491183U,	// VMOVUPDZ256rrkz
    1887491183U,	// VMOVUPDZ256rrkz_alt
    115381359U,	// VMOVUPDZmr
    1994724463U,	// VMOVUPDZmrk
    1086575U,	// VMOVUPDZrm
    1888408687U,	// VMOVUPDZrmk
    1888375919U,	// VMOVUPDZrmkz
    551818351U,	// VMOVUPDZrr
    551818351U,	// VMOVUPDZrr_alt
    1887802479U,	// VMOVUPDZrrk
    1887802479U,	// VMOVUPDZrrk_alt
    1887491183U,	// VMOVUPDZrrkz
    1887491183U,	// VMOVUPDZrrkz_alt
    62952559U,	// VMOVUPDmr
    578671U,	// VMOVUPDrm
    551818351U,	// VMOVUPDrr
    551818351U,	// VMOVUPDrr_REV
    113288982U,	// VMOVUPSYmr
    1074966U,	// VMOVUPSYrm
    551823126U,	// VMOVUPSYrr
    551823126U,	// VMOVUPSYrr_REV
    62957334U,	// VMOVUPSZ128mr
    1942300438U,	// VMOVUPSZ128mrk
    583446U,	// VMOVUPSZ128rm
    1888544534U,	// VMOVUPSZ128rmk
    1887577878U,	// VMOVUPSZ128rmkz
    551823126U,	// VMOVUPSZ128rr
    551823126U,	// VMOVUPSZ128rr_alt
    1887807254U,	// VMOVUPSZ128rrk
    1887807254U,	// VMOVUPSZ128rrk_alt
    1887495958U,	// VMOVUPSZ128rrkz
    1887495958U,	// VMOVUPSZ128rrkz_alt
    113288982U,	// VMOVUPSZ256mr
    1992632086U,	// VMOVUPSZ256mrk
    1074966U,	// VMOVUPSZ256rm
    1888560918U,	// VMOVUPSZ256rmk
    1888364310U,	// VMOVUPSZ256rmkz
    551823126U,	// VMOVUPSZ256rr
    551823126U,	// VMOVUPSZ256rr_alt
    1887807254U,	// VMOVUPSZ256rrk
    1887807254U,	// VMOVUPSZ256rrk_alt
    1887495958U,	// VMOVUPSZ256rrkz
    1887495958U,	// VMOVUPSZ256rrkz_alt
    115386134U,	// VMOVUPSZmr
    1994729238U,	// VMOVUPSZmrk
    1091350U,	// VMOVUPSZrm
    1888413462U,	// VMOVUPSZrmk
    1888380694U,	// VMOVUPSZrmkz
    551823126U,	// VMOVUPSZrr
    551823126U,	// VMOVUPSZrr_alt
    1887807254U,	// VMOVUPSZrrk
    1887807254U,	// VMOVUPSZrrk_alt
    1887495958U,	// VMOVUPSZrrkz
    1887495958U,	// VMOVUPSZrrkz_alt
    62957334U,	// VMOVUPSmr
    583446U,	// VMOVUPSrm
    551823126U,	// VMOVUPSrr
    551823126U,	// VMOVUPSrr_REV
    320229U,	// VMOVZPQILo2PQIZrm
    551822053U,	// VMOVZPQILo2PQIZrr
    320229U,	// VMOVZPQILo2PQIrm
    551822053U,	// VMOVZPQILo2PQIrr
    551838437U,	// VMOVZQI2PQIrm
    551822053U,	// VMOVZQI2PQIrr
    105212422U,	// VMPSADBWYrmi
    302361094U,	// VMPSADBWYrri
    340093446U,	// VMPSADBWrmi
    302361094U,	// VMPSADBWrri
    397744U,	// VMPTRLDm
    403833U,	// VMPTRSTm
    12622125U,	// VMREAD32rm
    551819565U,	// VMREAD32rr
    18915034U,	// VMREAD64rm
    551821018U,	// VMREAD64rr
    14319U,	// VMRESUME
    15399U,	// VMRUN32
    15479U,	// VMRUN64
    15387U,	// VMSAVE32
    15467U,	// VMSAVE64
    812520438U,	// VMULPDYrm
    811652086U,	// VMULPDYrr
    812532558U,	// VMULPDZrm
    352469838U,	// VMULPDZrmb
    1427014478U,	// VMULPDZrmbk
    1427014478U,	// VMULPDZrmbkz
    571397110U,	// VMULPDZrmk
    571397110U,	// VMULPDZrmkz
    811647822U,	// VMULPDZrr
    570786638U,	// VMULPDZrrk
    570786638U,	// VMULPDZrrkz
    811734006U,	// VMULPDrm
    811652086U,	// VMULPDrr
    812525139U,	// VMULPSYrm
    811656787U,	// VMULPSYrr
    812534477U,	// VMULPSZrm
    354585293U,	// VMULPSZrmb
    1429146317U,	// VMULPSZrmbk
    1429146317U,	// VMULPSZrmbkz
    571401811U,	// VMULPSZrmk
    571401811U,	// VMULPSZrmkz
    811649741U,	// VMULPSZrr
    570788557U,	// VMULPSZrrk
    570788557U,	// VMULPSZrrkz
    811738707U,	// VMULPSrm
    811656787U,	// VMULPSrr
    283268572U,	// VMULSDZrm
    811652572U,	// VMULSDZrr
    283268572U,	// VMULSDrm
    283268572U,	// VMULSDrm_Int
    811652572U,	// VMULSDrr
    811652572U,	// VMULSDrr_Int
    283289717U,	// VMULSSZrm
    811657333U,	// VMULSSZrr
    283289717U,	// VMULSSrm
    283289717U,	// VMULSSrm_Int
    811657333U,	// VMULSSrr
    811657333U,	// VMULSSrr_Int
    551803371U,	// VMWRITE32rm
    551819755U,	// VMWRITE32rr
    551837755U,	// VMWRITE64rm
    551821371U,	// VMWRITE64rr
    14402U,	// VMXOFF
    400721U,	// VMXON
    812520516U,	// VORPDYrm
    811652164U,	// VORPDYrr
    811734084U,	// VORPDrm
    811652164U,	// VORPDrr
    812525234U,	// VORPSYrm
    811656882U,	// VORPSYrr
    811738802U,	// VORPSrm
    811656882U,	// VORPSrr
    315275U,	// VPABSBrm128
    1019787U,	// VPABSBrm256
    551817099U,	// VPABSBrr128
    551817099U,	// VPABSBrr256
    1033405U,	// VPABSDZrm
    623101117U,	// VPABSDZrmb
    622937277U,	// VPABSDZrmbk
    622937277U,	// VPABSDZrmbkz
    1888666813U,	// VPABSDZrmk
    1888666813U,	// VPABSDZrmkz
    551814333U,	// VPABSDZrr
    1887487165U,	// VPABSDZrrk
    1887487165U,	// VPABSDZrrkz
    316797U,	// VPABSDrm128
    1021309U,	// VPABSDrm256
    551818621U,	// VPABSDrr128
    551818621U,	// VPABSDrr256
    1034331U,	// VPABSQZrm
    621037659U,	// VPABSQZrmb
    620857435U,	// VPABSQZrmbk
    620857435U,	// VPABSQZrmbkz
    1888667739U,	// VPABSQZrmk
    1888667739U,	// VPABSQZrmkz
    551815259U,	// VPABSQZrr
    1887488091U,	// VPABSQZrrk
    1887488091U,	// VPABSQZrrkz
    322739U,	// VPABSWrm128
    1027251U,	// VPABSWrm256
    551824563U,	// VPABSWrr128
    551824563U,	// VPABSWrr256
    812608238U,	// VPACKSSDWYrm
    811657966U,	// VPACKSSDWYrr
    811903726U,	// VPACKSSDWrm
    811657966U,	// VPACKSSDWrr
    812601448U,	// VPACKSSWBYrm
    811651176U,	// VPACKSSWBYrr
    811896936U,	// VPACKSSWBrm
    811651176U,	// VPACKSSWBrr
    812608249U,	// VPACKUSDWYrm
    811657977U,	// VPACKUSDWYrr
    811903737U,	// VPACKUSDWrm
    811657977U,	// VPACKUSDWrr
    812601459U,	// VPACKUSWBYrm
    811651187U,	// VPACKUSWBYrr
    811896947U,	// VPACKUSWBrm
    811651187U,	// VPACKUSWBrr
    812601023U,	// VPADDBYrm
    811650751U,	// VPADDBYrr
    811896511U,	// VPADDBrm
    811650751U,	// VPADDBrr
    812601640U,	// VPADDDYrm
    811651368U,	// VPADDDYrr
    812826866U,	// VPADDDZrm
    354500850U,	// VPADDDZrmb
    4113678578U,	// VPADDDZrmbk
    1429405938U,	// VPADDDZrmbkz
    122896626U,	// VPADDDZrmk
    571638002U,	// VPADDDZrmkz
    811647218U,	// VPADDDZrr
    1888076018U,	// VPADDDZrrk
    570786034U,	// VPADDDZrrkz
    811897128U,	// VPADDDrm
    811651368U,	// VPADDDrr
    812605165U,	// VPADDQYrm
    811654893U,	// VPADDQYrr
    812828371U,	// VPADDQZrm
    352421587U,	// VPADDQZrmb
    4111566547U,	// VPADDQZrmbk
    1427343059U,	// VPADDQZrmbkz
    122898131U,	// VPADDQZrmk
    571639507U,	// VPADDQZrmkz
    811648723U,	// VPADDQZrr
    1888077523U,	// VPADDQZrrk
    570787539U,	// VPADDQZrrkz
    811900653U,	// VPADDQrm
    811654893U,	// VPADDQrr
    812601253U,	// VPADDSBYrm
    811650981U,	// VPADDSBYrr
    811896741U,	// VPADDSBrm
    811650981U,	// VPADDSBrr
    812608749U,	// VPADDSWYrm
    811658477U,	// VPADDSWYrr
    811904237U,	// VPADDSWrm
    811658477U,	// VPADDSWrr
    812601302U,	// VPADDUSBYrm
    811651030U,	// VPADDUSBYrr
    811896790U,	// VPADDUSBrm
    811651030U,	// VPADDUSBrr
    812608862U,	// VPADDUSWYrm
    811658590U,	// VPADDUSWYrr
    811904350U,	// VPADDUSWrm
    811658590U,	// VPADDUSWrr
    812608181U,	// VPADDWYrm
    811657909U,	// VPADDWYrr
    811903669U,	// VPADDWrm
    811657909U,	// VPADDWrr
    340091767U,	// VPALIGNR128rm
    302359415U,	// VPALIGNR128rr
    105210743U,	// VPALIGNR256rm
    302359415U,	// VPALIGNR256rr
    812826883U,	// VPANDDZrm
    354500867U,	// VPANDDZrmb
    4113678595U,	// VPANDDZrmbk
    1429405955U,	// VPANDDZrmbkz
    122896643U,	// VPANDDZrmk
    571638019U,	// VPANDDZrmkz
    811647235U,	// VPANDDZrr
    1888076035U,	// VPANDDZrrk
    570786051U,	// VPANDDZrrkz
    812827010U,	// VPANDNDZrm
    354500994U,	// VPANDNDZrmb
    4113678722U,	// VPANDNDZrmbk
    1429406082U,	// VPANDNDZrmbkz
    122896770U,	// VPANDNDZrmk
    571638146U,	// VPANDNDZrmkz
    811647362U,	// VPANDNDZrr
    1888076162U,	// VPANDNDZrrk
    570786178U,	// VPANDNDZrrkz
    812828675U,	// VPANDNQZrm
    352421891U,	// VPANDNQZrmb
    4111566851U,	// VPANDNQZrmbk
    1427343363U,	// VPANDNQZrmbkz
    122898435U,	// VPANDNQZrmk
    571639811U,	// VPANDNQZrmkz
    811649027U,	// VPANDNQZrr
    1888077827U,	// VPANDNQZrrk
    570787843U,	// VPANDNQZrrkz
    812604730U,	// VPANDNYrm
    811654458U,	// VPANDNYrr
    811900218U,	// VPANDNrm
    811654458U,	// VPANDNrr
    812828424U,	// VPANDQZrm
    352421640U,	// VPANDQZrmb
    4111566600U,	// VPANDQZrmbk
    1427343112U,	// VPANDQZrmbkz
    122898184U,	// VPANDQZrmk
    571639560U,	// VPANDQZrmkz
    811648776U,	// VPANDQZrr
    1888077576U,	// VPANDQZrrk
    570787592U,	// VPANDQZrrkz
    812601801U,	// VPANDYrm
    811651529U,	// VPANDYrr
    811897289U,	// VPANDrm
    811651529U,	// VPANDrr
    812601069U,	// VPAVGBYrm
    811650797U,	// VPAVGBYrr
    811896557U,	// VPAVGBrm
    811650797U,	// VPAVGBrr
    812608350U,	// VPAVGWYrm
    811658078U,	// VPAVGWYrr
    811903838U,	// VPAVGWrm
    811658078U,	// VPAVGWrr
    105206064U,	// VPBLENDDYrmi
    302354736U,	// VPBLENDDYrri
    340087088U,	// VPBLENDDrmi
    302354736U,	// VPBLENDDrri
    571392333U,	// VPBLENDMDZrm
    570786125U,	// VPBLENDMDZrr
    571393998U,	// VPBLENDMQZrm
    570787790U,	// VPBLENDMQZrr
    105205838U,	// VPBLENDVBYrm
    302354510U,	// VPBLENDVBYrr
    340086862U,	// VPBLENDVBrm
    302354510U,	// VPBLENDVBrr
    105212637U,	// VPBLENDWYrmi
    302361309U,	// VPBLENDWYrri
    340093661U,	// VPBLENDWrmi
    302361309U,	// VPBLENDWrri
    446487U,	// VPBROADCASTBYrm
    551817239U,	// VPBROADCASTBYrr
    446487U,	// VPBROADCASTBrm
    551817239U,	// VPBROADCASTBrr
    551802457U,	// VPBROADCASTDYrm
    551818841U,	// VPBROADCASTDYrr
    551634209U,	// VPBROADCASTDZkrm
    1887487265U,	// VPBROADCASTDZkrr
    551798049U,	// VPBROADCASTDZrm
    551814433U,	// VPBROADCASTDZrr
    1887487265U,	// VPBROADCASTDrZkrr
    551814433U,	// VPBROADCASTDrZrr
    551802457U,	// VPBROADCASTDrm
    551818841U,	// VPBROADCASTDrr
    551814735U,	// VPBROADCASTMB2Qrr
    551813302U,	// VPBROADCASTMW2Drr
    551838354U,	// VPBROADCASTQYrm
    551821970U,	// VPBROADCASTQYrr
    551651508U,	// VPBROADCASTQZkrm
    1887488180U,	// VPBROADCASTQZkrr
    551831732U,	// VPBROADCASTQZrm
    551815348U,	// VPBROADCASTQZrr
    1887488180U,	// VPBROADCASTQrZkrr
    551815348U,	// VPBROADCASTQrZrr
    551838354U,	// VPBROADCASTQrm
    551821970U,	// VPBROADCASTQrr
    388576U,	// VPBROADCASTWYrm
    551824864U,	// VPBROADCASTWYrr
    388576U,	// VPBROADCASTWrm
    551824864U,	// VPBROADCASTWrr
    340090732U,	// VPCLMULQDQrm
    302358380U,	// VPCLMULQDQrr
    340093384U,	// VPCMOVmr
    92629448U,	// VPCMOVmrY
    303180232U,	// VPCMOVrm
    303196616U,	// VPCMOVrmY
    302361032U,	// VPCMOVrr
    302361032U,	// VPCMOVrrY
    124402021U,	// VPCMPDZrmi
    356865031U,	// VPCMPDZrmi_alt
    126964743U,	// VPCMPDZrmik_alt
    1466595685U,	// VPCMPDZrri
    302355463U,	// VPCMPDZrri_alt
    571069447U,	// VPCMPDZrrik_alt
    812601162U,	// VPCMPEQBYrm
    811650890U,	// VPCMPEQBYrr
    811896650U,	// VPCMPEQBZ128rm
    571608906U,	// VPCMPEQBZ128rmk
    811650890U,	// VPCMPEQBZ128rr
    570789706U,	// VPCMPEQBZ128rrk
    812601162U,	// VPCMPEQBZ256rm
    571625290U,	// VPCMPEQBZ256rmk
    811650890U,	// VPCMPEQBZ256rr
    570789706U,	// VPCMPEQBZ256rrk
    812830538U,	// VPCMPEQBZrm
    571641674U,	// VPCMPEQBZrmk
    811650890U,	// VPCMPEQBZrr
    570789706U,	// VPCMPEQBZrrk
    811896650U,	// VPCMPEQBrm
    811650890U,	// VPCMPEQBrr
    812602536U,	// VPCMPEQDYrm
    811652264U,	// VPCMPEQDYrr
    811898024U,	// VPCMPEQDZ128rm
    396448936U,	// VPCMPEQDZ128rmb
    1471354024U,	// VPCMPEQDZ128rmbk
    571610280U,	// VPCMPEQDZ128rmk
    811652264U,	// VPCMPEQDZ128rr
    570791080U,	// VPCMPEQDZ128rrk
    812602536U,	// VPCMPEQDZ256rm
    352408744U,	// VPCMPEQDZ256rmb
    1427313832U,	// VPCMPEQDZ256rmbk
    571626664U,	// VPCMPEQDZ256rmk
    811652264U,	// VPCMPEQDZ256rr
    570791080U,	// VPCMPEQDZ256rrk
    812831912U,	// VPCMPEQDZrm
    354505896U,	// VPCMPEQDZrmb
    1429410984U,	// VPCMPEQDZrmbk
    571643048U,	// VPCMPEQDZrmk
    811652264U,	// VPCMPEQDZrr
    570791080U,	// VPCMPEQDZrrk
    811898024U,	// VPCMPEQDrm
    811652264U,	// VPCMPEQDrr
    812605759U,	// VPCMPEQQYrm
    811655487U,	// VPCMPEQQYrr
    811901247U,	// VPCMPEQQZ128rm
    398565695U,	// VPCMPEQQZ128rmb
    1473487167U,	// VPCMPEQQZ128rmbk
    571613503U,	// VPCMPEQQZ128rmk
    811655487U,	// VPCMPEQQZ128rr
    570794303U,	// VPCMPEQQZ128rrk
    812605759U,	// VPCMPEQQZ256rm
    396468543U,	// VPCMPEQQZ256rmb
    1471390015U,	// VPCMPEQQZ256rmbk
    571629887U,	// VPCMPEQQZ256rmk
    811655487U,	// VPCMPEQQZ256rr
    570794303U,	// VPCMPEQQZ256rrk
    812835135U,	// VPCMPEQQZrm
    352428351U,	// VPCMPEQQZrmb
    1427349823U,	// VPCMPEQQZrmbk
    571646271U,	// VPCMPEQQZrmk
    811655487U,	// VPCMPEQQZrr
    570794303U,	// VPCMPEQQZrrk
    811901247U,	// VPCMPEQQrm
    811655487U,	// VPCMPEQQrr
    812608581U,	// VPCMPEQWYrm
    811658309U,	// VPCMPEQWYrr
    811904069U,	// VPCMPEQWZ128rm
    571616325U,	// VPCMPEQWZ128rmk
    811658309U,	// VPCMPEQWZ128rr
    570797125U,	// VPCMPEQWZ128rrk
    812608581U,	// VPCMPEQWZ256rm
    571632709U,	// VPCMPEQWZ256rmk
    811658309U,	// VPCMPEQWZ256rr
    570797125U,	// VPCMPEQWZ256rrk
    812837957U,	// VPCMPEQWZrm
    571649093U,	// VPCMPEQWZrmk
    811658309U,	// VPCMPEQWZrr
    570797125U,	// VPCMPEQWZrrk
    811904069U,	// VPCMPEQWrm
    811658309U,	// VPCMPEQWrr
    0U,	// VPCMPESTRIMEM
    0U,	// VPCMPESTRIREG
    25499773U,	// VPCMPESTRIrm
    811653245U,	// VPCMPESTRIrr
    0U,	// VPCMPESTRM128MEM
    0U,	// VPCMPESTRM128REG
    25500962U,	// VPCMPESTRM128rm
    811654434U,	// VPCMPESTRM128rr
    812601343U,	// VPCMPGTBYrm
    811651071U,	// VPCMPGTBYrr
    811896831U,	// VPCMPGTBZ128rm
    571609087U,	// VPCMPGTBZ128rmk
    811651071U,	// VPCMPGTBZ128rr
    570789887U,	// VPCMPGTBZ128rrk
    812601343U,	// VPCMPGTBZ256rm
    571625471U,	// VPCMPGTBZ256rmk
    811651071U,	// VPCMPGTBZ256rr
    570789887U,	// VPCMPGTBZ256rrk
    812830719U,	// VPCMPGTBZrm
    571641855U,	// VPCMPGTBZrmk
    811651071U,	// VPCMPGTBZrr
    570789887U,	// VPCMPGTBZrrk
    811896831U,	// VPCMPGTBrm
    811651071U,	// VPCMPGTBrr
    812602951U,	// VPCMPGTDYrm
    811652679U,	// VPCMPGTDYrr
    811898439U,	// VPCMPGTDZ128rm
    396449351U,	// VPCMPGTDZ128rmb
    1471354439U,	// VPCMPGTDZ128rmbk
    571610695U,	// VPCMPGTDZ128rmk
    811652679U,	// VPCMPGTDZ128rr
    570791495U,	// VPCMPGTDZ128rrk
    812602951U,	// VPCMPGTDZ256rm
    352409159U,	// VPCMPGTDZ256rmb
    1427314247U,	// VPCMPGTDZ256rmbk
    571627079U,	// VPCMPGTDZ256rmk
    811652679U,	// VPCMPGTDZ256rr
    570791495U,	// VPCMPGTDZ256rrk
    812832327U,	// VPCMPGTDZrm
    354506311U,	// VPCMPGTDZrmb
    1429411399U,	// VPCMPGTDZrmbk
    571643463U,	// VPCMPGTDZrmk
    811652679U,	// VPCMPGTDZrr
    570791495U,	// VPCMPGTDZrrk
    811898439U,	// VPCMPGTDrm
    811652679U,	// VPCMPGTDrr
    812606021U,	// VPCMPGTQYrm
    811655749U,	// VPCMPGTQYrr
    811901509U,	// VPCMPGTQZ128rm
    398565957U,	// VPCMPGTQZ128rmb
    1473487429U,	// VPCMPGTQZ128rmbk
    571613765U,	// VPCMPGTQZ128rmk
    811655749U,	// VPCMPGTQZ128rr
    570794565U,	// VPCMPGTQZ128rrk
    812606021U,	// VPCMPGTQZ256rm
    396468805U,	// VPCMPGTQZ256rmb
    1471390277U,	// VPCMPGTQZ256rmbk
    571630149U,	// VPCMPGTQZ256rmk
    811655749U,	// VPCMPGTQZ256rr
    570794565U,	// VPCMPGTQZ256rrk
    812835397U,	// VPCMPGTQZrm
    352428613U,	// VPCMPGTQZrmb
    1427350085U,	// VPCMPGTQZrmbk
    571646533U,	// VPCMPGTQZrmk
    811655749U,	// VPCMPGTQZrr
    570794565U,	// VPCMPGTQZrrk
    811901509U,	// VPCMPGTQrm
    811655749U,	// VPCMPGTQrr
    812608943U,	// VPCMPGTWYrm
    811658671U,	// VPCMPGTWYrr
    811904431U,	// VPCMPGTWZ128rm
    571616687U,	// VPCMPGTWZ128rmk
    811658671U,	// VPCMPGTWZ128rr
    570797487U,	// VPCMPGTWZ128rrk
    812608943U,	// VPCMPGTWZ256rm
    571633071U,	// VPCMPGTWZ256rmk
    811658671U,	// VPCMPGTWZ256rr
    570797487U,	// VPCMPGTWZ256rrk
    812838319U,	// VPCMPGTWZrm
    571649455U,	// VPCMPGTWZrmk
    811658671U,	// VPCMPGTWZrr
    570797487U,	// VPCMPGTWZrrk
    811904431U,	// VPCMPGTWrm
    811658671U,	// VPCMPGTWrr
    0U,	// VPCMPISTRIMEM
    0U,	// VPCMPISTRIREG
    25499785U,	// VPCMPISTRIrm
    811653257U,	// VPCMPISTRIrr
    0U,	// VPCMPISTRM128MEM
    0U,	// VPCMPISTRM128REG
    25500974U,	// VPCMPISTRM128rm
    811654446U,	// VPCMPISTRM128rr
    132790629U,	// VPCMPQZrmi
    356868384U,	// VPCMPQZrmi_alt
    126968096U,	// VPCMPQZrmik_alt
    1474984293U,	// VPCMPQZrri
    302358816U,	// VPCMPQZrri_alt
    571072800U,	// VPCMPQZrrik_alt
    134887781U,	// VPCMPUDZrmi
    356865657U,	// VPCMPUDZrmi_alt
    126965369U,	// VPCMPUDZrmik_alt
    1477081445U,	// VPCMPUDZrri
    302356089U,	// VPCMPUDZrri_alt
    571070073U,	// VPCMPUDZrrik_alt
    136984933U,	// VPCMPUQZrmi
    356868791U,	// VPCMPUQZrmi_alt
    126968503U,	// VPCMPUQZrmik_alt
    1479178597U,	// VPCMPUQZrri
    302359223U,	// VPCMPUQZrri_alt
    571073207U,	// VPCMPUQZrrik_alt
    340086565U,	// VPCOMBmi
    302354213U,	// VPCOMBri
    340087225U,	// VPCOMDmi
    302354873U,	// VPCOMDri
    340091113U,	// VPCOMQmi
    302358761U,	// VPCOMQri
    340086828U,	// VPCOMUBmi
    302354476U,	// VPCOMUBri
    340088423U,	// VPCOMUDmi
    302356071U,	// VPCOMUDri
    340091566U,	// VPCOMUQmi
    302359214U,	// VPCOMUQri
    340094463U,	// VPCOMUWmi
    302362111U,	// VPCOMUWri
    340093940U,	// VPCOMWmi
    302361588U,	// VPCOMWri
    1033472U,	// VPCONFLICTDrm
    623101184U,	// VPCONFLICTDrmb
    624100608U,	// VPCONFLICTDrmbk
    622937344U,	// VPCONFLICTDrmbkz
    1888650496U,	// VPCONFLICTDrmk
    1888666880U,	// VPCONFLICTDrmkz
    551814400U,	// VPCONFLICTDrr
    1887798528U,	// VPCONFLICTDrrk
    1887487232U,	// VPCONFLICTDrrkz
    1034387U,	// VPCONFLICTQrm
    621037715U,	// VPCONFLICTQrmb
    622037139U,	// VPCONFLICTQrmbk
    620857491U,	// VPCONFLICTQrmbkz
    1888651411U,	// VPCONFLICTQrmk
    1888667795U,	// VPCONFLICTQrmkz
    551815315U,	// VPCONFLICTQrr
    1887799443U,	// VPCONFLICTQrrk
    1887488147U,	// VPCONFLICTQrrkz
    92622293U,	// VPERM2F128rm
    302353877U,	// VPERM2F128rr
    92622348U,	// VPERM2I128rm
    302353932U,	// VPERM2I128rr
    812601793U,	// VPERMDYrm
    811651521U,	// VPERMDYrr
    812826981U,	// VPERMDZrm
    811647333U,	// VPERMDZrr
    571637920U,	// VPERMI2Drm
    122896544U,	// VPERMI2Drmk
    139673760U,	// VPERMI2Drmkz
    570785952U,	// VPERMI2Drr
    1888075936U,	// VPERMI2Drrk
    1888075936U,	// VPERMI2Drrkz
    571638258U,	// VPERMI2PDrm
    122896882U,	// VPERMI2PDrmk
    139674098U,	// VPERMI2PDrmkz
    570786290U,	// VPERMI2PDrr
    1888076274U,	// VPERMI2PDrrk
    1888076274U,	// VPERMI2PDrrkz
    571640189U,	// VPERMI2PSrm
    122898813U,	// VPERMI2PSrmk
    139676029U,	// VPERMI2PSrmkz
    570788221U,	// VPERMI2PSrr
    1888078205U,	// VPERMI2PSrrk
    1888078205U,	// VPERMI2PSrrkz
    571639393U,	// VPERMI2Qrm
    122898017U,	// VPERMI2Qrmk
    139675233U,	// VPERMI2Qrmkz
    570787425U,	// VPERMI2Qrr
    1888077409U,	// VPERMI2Qrrk
    1888077409U,	// VPERMI2Qrrkz
    1215386262U,	// VPERMIL2PDmr
    2289128086U,	// VPERMIL2PDmrY
    143741590U,	// VPERMIL2PDrm
    145838742U,	// VPERMIL2PDrmY
    302633622U,	// VPERMIL2PDrr
    302633622U,	// VPERMIL2PDrrY
    1215390962U,	// VPERMIL2PSmr
    2289132786U,	// VPERMIL2PSmrY
    143746290U,	// VPERMIL2PSrm
    145843442U,	// VPERMIL2PSrmY
    302638322U,	// VPERMIL2PSrr
    302638322U,	// VPERMIL2PSrrY
    147133408U,	// VPERMILPDYmi
    811652064U,	// VPERMILPDYri
    812602336U,	// VPERMILPDYrm
    811652064U,	// VPERMILPDYrr
    149226306U,	// VPERMILPDZmi
    811647810U,	// VPERMILPDZri
    80024544U,	// VPERMILPDmi
    811652064U,	// VPERMILPDri
    811897824U,	// VPERMILPDrm
    811652064U,	// VPERMILPDrr
    147138109U,	// VPERMILPSYmi
    811656765U,	// VPERMILPSYri
    812607037U,	// VPERMILPSYrm
    811656765U,	// VPERMILPSYrr
    149228225U,	// VPERMILPSZmi
    811649729U,	// VPERMILPSZri
    80029245U,	// VPERMILPSmi
    811656765U,	// VPERMILPSri
    811902525U,	// VPERMILPSrm
    811656765U,	// VPERMILPSrr
    151327759U,	// VPERMPDYmi
    811652111U,	// VPERMPDYri
    153420643U,	// VPERMPDZmi
    811647843U,	// VPERMPDZri
    812532579U,	// VPERMPDZrm
    811647843U,	// VPERMPDZrr
    812607093U,	// VPERMPSYrm
    811656821U,	// VPERMPSYrr
    812534498U,	// VPERMPSZrm
    811649762U,	// VPERMPSZrr
    151331057U,	// VPERMQYmi
    811655409U,	// VPERMQYri
    149227494U,	// VPERMQZmi
    811648998U,	// VPERMQZri
    812828646U,	// VPERMQZrm
    811648998U,	// VPERMQZrr
    571637931U,	// VPERMT2Drm
    122896555U,	// VPERMT2Drmk
    139673771U,	// VPERMT2Drmkz
    570785963U,	// VPERMT2Drr
    1888075947U,	// VPERMT2Drrk
    1888075947U,	// VPERMT2Drrkz
    571638307U,	// VPERMT2PDrm
    122896931U,	// VPERMT2PDrmk
    139674147U,	// VPERMT2PDrmkz
    570786339U,	// VPERMT2PDrr
    1888076323U,	// VPERMT2PDrrk
    1888076323U,	// VPERMT2PDrrkz
    571640226U,	// VPERMT2PSrm
    122898850U,	// VPERMT2PSrmk
    139676066U,	// VPERMT2PSrmkz
    570788258U,	// VPERMT2PSrr
    1888078242U,	// VPERMT2PSrrk
    1888078242U,	// VPERMT2PSrrkz
    571639404U,	// VPERMT2Qrm
    122898028U,	// VPERMT2Qrmk
    139675244U,	// VPERMT2Qrmkz
    570787436U,	// VPERMT2Qrr
    1888077420U,	// VPERMT2Qrrk
    1888077420U,	// VPERMT2Qrrkz
    587534203U,	// VPEXTRBmr
    811650939U,	// VPEXTRBrr
    855971015U,	// VPEXTRDmr
    811652295U,	// VPEXTRDrr
    1124409773U,	// VPEXTRQmr
    811655597U,	// VPEXTRQrr
    1392848035U,	// VPEXTRWmr
    811658403U,	// VPEXTRWri
    811658403U,	// VPEXTRWrr_REV
    110186810U,	// VPGATHERDDYrm
    552714508U,	// VPGATHERDDZrm
    110186810U,	// VPGATHERDDrm
    108093304U,	// VPGATHERDQYrm
    552699693U,	// VPGATHERDQZrm
    108093304U,	// VPGATHERDQrm
    110187698U,	// VPGATHERQDYrm
    552698862U,	// VPGATHERQDZrm
    110187698U,	// VPGATHERQDrm
    108093769U,	// VPGATHERQQYrm
    552699927U,	// VPGATHERQQZrm
    108093769U,	// VPGATHERQQrm
    315612U,	// VPHADDBDrm
    551817436U,	// VPHADDBDrr
    319040U,	// VPHADDBQrm
    551820864U,	// VPHADDBQrr
    322073U,	// VPHADDBWrm
    551823897U,	// VPHADDBWrr
    319228U,	// VPHADDDQrm
    551821052U,	// VPHADDDQrr
    812601631U,	// VPHADDDYrm
    811651359U,	// VPHADDDYrr
    811897119U,	// VPHADDDrm
    811651359U,	// VPHADDDrr
    811904227U,	// VPHADDSWrm128
    812608739U,	// VPHADDSWrm256
    811658467U,	// VPHADDSWrr128
    811658467U,	// VPHADDSWrr256
    315622U,	// VPHADDUBDrm
    551817446U,	// VPHADDUBDrr
    319058U,	// VPHADDUBQrm
    551820882U,	// VPHADDUBQrr
    322115U,	// VPHADDUBWrm
    551823939U,	// VPHADDUBWrr
    319406U,	// VPHADDUDQrm
    551821230U,	// VPHADDUDQrr
    317211U,	// VPHADDUWDrm
    551819035U,	// VPHADDUWDrr
    320261U,	// VPHADDUWQrm
    551822085U,	// VPHADDUWQrr
    317123U,	// VPHADDWDrm
    551818947U,	// VPHADDWDrr
    320236U,	// VPHADDWQrm
    551822060U,	// VPHADDWQrr
    812608172U,	// VPHADDWYrm
    811657900U,	// VPHADDWYrr
    811903660U,	// VPHADDWrm
    811657900U,	// VPHADDWrr
    323089U,	// VPHMINPOSUWrm128
    551824913U,	// VPHMINPOSUWrr128
    322044U,	// VPHSUBBWrm
    551823868U,	// VPHSUBBWrr
    319203U,	// VPHSUBDQrm
    551821027U,	// VPHSUBDQrr
    812601585U,	// VPHSUBDYrm
    811651313U,	// VPHSUBDYrr
    811897073U,	// VPHSUBDrm
    811651313U,	// VPHSUBDrr
    811904208U,	// VPHSUBSWrm128
    812608720U,	// VPHSUBSWrm256
    811658448U,	// VPHSUBSWrr128
    811658448U,	// VPHSUBSWrr256
    317113U,	// VPHSUBWDrm
    551818937U,	// VPHSUBWDrr
    812608078U,	// VPHSUBWYrm
    811657806U,	// VPHSUBWYrr
    811903566U,	// VPHSUBWrm
    811657806U,	// VPHSUBWrr
    344280946U,	// VPINSRBrm
    302354290U,	// VPINSRBrr
    346379454U,	// VPINSRDrm
    302355646U,	// VPINSRDrr
    325411216U,	// VPINSRQrm
    302358928U,	// VPINSRQrr
    327511176U,	// VPINSRWrmi
    302361736U,	// VPINSRWrri
    1033486U,	// VPLZCNTDrm
    623101198U,	// VPLZCNTDrmb
    624100622U,	// VPLZCNTDrmbk
    622937358U,	// VPLZCNTDrmbkz
    1888650510U,	// VPLZCNTDrmk
    1888666894U,	// VPLZCNTDrmkz
    551814414U,	// VPLZCNTDrr
    1887798542U,	// VPLZCNTDrrk
    1887487246U,	// VPLZCNTDrrkz
    1034401U,	// VPLZCNTQrm
    621037729U,	// VPLZCNTQrmb
    622037153U,	// VPLZCNTQrmbk
    620857505U,	// VPLZCNTQrmbkz
    1888651425U,	// VPLZCNTQrmk
    1888667809U,	// VPLZCNTQrmkz
    551815329U,	// VPLZCNTQrr
    1887799457U,	// VPLZCNTQrrk
    1887488161U,	// VPLZCNTQrrkz
    340087110U,	// VPMACSDDrm
    302354758U,	// VPMACSDDrr
    340088845U,	// VPMACSDQHrm
    302356493U,	// VPMACSDQHrr
    340089668U,	// VPMACSDQLrm
    302357316U,	// VPMACSDQLrr
    340087120U,	// VPMACSSDDrm
    302354768U,	// VPMACSSDDrr
    340088856U,	// VPMACSSDQHrm
    302356504U,	// VPMACSSDQHrr
    340089679U,	// VPMACSSDQLrm
    302357327U,	// VPMACSSDQLrr
    340088580U,	// VPMACSSWDrm
    302356228U,	// VPMACSSWDrr
    340094540U,	// VPMACSSWWrm
    302362188U,	// VPMACSSWWrr
    340088559U,	// VPMACSWDrm
    302356207U,	// VPMACSWDrr
    340094516U,	// VPMACSWWrm
    302362164U,	// VPMACSWWrr
    340088591U,	// VPMADCSSWDrm
    302356239U,	// VPMADCSSWDrr
    340088569U,	// VPMADCSWDrm
    302356217U,	// VPMADCSWDrr
    811904196U,	// VPMADDUBSWrm128
    812608708U,	// VPMADDUBSWrm256
    811658436U,	// VPMADDUBSWrr128
    811658436U,	// VPMADDUBSWrr256
    812603085U,	// VPMADDWDYrm
    811652813U,	// VPMADDWDYrr
    811898573U,	// VPMADDWDrm
    811652813U,	// VPMADDWDrr
    3808761510U,	// VPMASKMOVDYmr
    812603046U,	// VPMASKMOVDYrm
    3540326054U,	// VPMASKMOVDmr
    811898534U,	// VPMASKMOVDrm
    3808764633U,	// VPMASKMOVQYmr
    812606169U,	// VPMASKMOVQYrm
    3540329177U,	// VPMASKMOVQmr
    811901657U,	// VPMASKMOVQrm
    812601328U,	// VPMAXSBYrm
    811651056U,	// VPMAXSBYrr
    811896816U,	// VPMAXSBrm
    811651056U,	// VPMAXSBrr
    812602925U,	// VPMAXSDYrm
    811652653U,	// VPMAXSDYrr
    812827894U,	// VPMAXSDZrm
    354501878U,	// VPMAXSDZrmb
    4113679606U,	// VPMAXSDZrmbk
    1429406966U,	// VPMAXSDZrmbkz
    122897654U,	// VPMAXSDZrmk
    571639030U,	// VPMAXSDZrmkz
    811648246U,	// VPMAXSDZrr
    1888077046U,	// VPMAXSDZrrk
    570787062U,	// VPMAXSDZrrkz
    811898413U,	// VPMAXSDrm
    811652653U,	// VPMAXSDrr
    812828809U,	// VPMAXSQZrm
    352422025U,	// VPMAXSQZrmb
    4111566985U,	// VPMAXSQZrmbk
    1427343497U,	// VPMAXSQZrmbkz
    122898569U,	// VPMAXSQZrmk
    571639945U,	// VPMAXSQZrmkz
    811649161U,	// VPMAXSQZrr
    1888077961U,	// VPMAXSQZrrk
    570787977U,	// VPMAXSQZrrkz
    812608880U,	// VPMAXSWYrm
    811658608U,	// VPMAXSWYrr
    811904368U,	// VPMAXSWrm
    811658608U,	// VPMAXSWrr
    812601413U,	// VPMAXUBYrm
    811651141U,	// VPMAXUBYrr
    811896901U,	// VPMAXUBrm
    811651141U,	// VPMAXUBrr
    812603010U,	// VPMAXUDYrm
    811652738U,	// VPMAXUDYrr
    812827962U,	// VPMAXUDZrm
    354501946U,	// VPMAXUDZrmb
    4113679674U,	// VPMAXUDZrmbk
    1429407034U,	// VPMAXUDZrmbkz
    122897722U,	// VPMAXUDZrmk
    571639098U,	// VPMAXUDZrmkz
    811648314U,	// VPMAXUDZrr
    1888077114U,	// VPMAXUDZrrk
    570787130U,	// VPMAXUDZrrkz
    811898498U,	// VPMAXUDrm
    811652738U,	// VPMAXUDrr
    812828877U,	// VPMAXUQZrm
    352422093U,	// VPMAXUQZrmb
    4111567053U,	// VPMAXUQZrmbk
    1427343565U,	// VPMAXUQZrmbkz
    122898637U,	// VPMAXUQZrmk
    571640013U,	// VPMAXUQZrmkz
    811649229U,	// VPMAXUQZrr
    1888078029U,	// VPMAXUQZrrk
    570788045U,	// VPMAXUQZrrkz
    812609054U,	// VPMAXUWYrm
    811658782U,	// VPMAXUWYrr
    811904542U,	// VPMAXUWrm
    811658782U,	// VPMAXUWrr
    812601269U,	// VPMINSBYrm
    811650997U,	// VPMINSBYrr
    811896757U,	// VPMINSBrm
    811650997U,	// VPMINSBrr
    812602852U,	// VPMINSDYrm
    811652580U,	// VPMINSDYrr
    812827860U,	// VPMINSDZrm
    354501844U,	// VPMINSDZrmb
    4113679572U,	// VPMINSDZrmbk
    1429406932U,	// VPMINSDZrmbkz
    122897620U,	// VPMINSDZrmk
    571638996U,	// VPMINSDZrmkz
    811648212U,	// VPMINSDZrr
    1888077012U,	// VPMINSDZrrk
    570787028U,	// VPMINSDZrrkz
    811898340U,	// VPMINSDrm
    811652580U,	// VPMINSDrr
    812828772U,	// VPMINSQZrm
    352421988U,	// VPMINSQZrmb
    4111566948U,	// VPMINSQZrmbk
    1427343460U,	// VPMINSQZrmbkz
    122898532U,	// VPMINSQZrmk
    571639908U,	// VPMINSQZrmkz
    811649124U,	// VPMINSQZrr
    1888077924U,	// VPMINSQZrrk
    570787940U,	// VPMINSQZrrkz
    812608789U,	// VPMINSWYrm
    811658517U,	// VPMINSWYrr
    811904277U,	// VPMINSWrm
    811658517U,	// VPMINSWrr
    812601397U,	// VPMINUBYrm
    811651125U,	// VPMINUBYrr
    811896885U,	// VPMINUBrm
    811651125U,	// VPMINUBrr
    812602992U,	// VPMINUDYrm
    811652720U,	// VPMINUDYrr
    812827952U,	// VPMINUDZrm
    354501936U,	// VPMINUDZrmb
    4113679664U,	// VPMINUDZrmbk
    1429407024U,	// VPMINUDZrmbkz
    122897712U,	// VPMINUDZrmk
    571639088U,	// VPMINUDZrmkz
    811648304U,	// VPMINUDZrr
    1888077104U,	// VPMINUDZrrk
    570787120U,	// VPMINUDZrrkz
    811898480U,	// VPMINUDrm
    811652720U,	// VPMINUDrr
    812828867U,	// VPMINUQZrm
    352422083U,	// VPMINUQZrmb
    4111567043U,	// VPMINUQZrmbk
    1427343555U,	// VPMINUQZrmbkz
    122898627U,	// VPMINUQZrmk
    571640003U,	// VPMINUQZrmkz
    811649219U,	// VPMINUQZrr
    1888078019U,	// VPMINUQZrrk
    570788035U,	// VPMINUQZrrkz
    812609032U,	// VPMINUWYrm
    811658760U,	// VPMINUWYrr
    811904520U,	// VPMINUWrm
    811658760U,	// VPMINUWrr
    65044548U,	// VPMOVDBmr
    1944387652U,	// VPMOVDBmrk
    551813188U,	// VPMOVDBrr
    1887486020U,	// VPMOVDBrrk
    1887486020U,	// VPMOVDBrrkz
    117476391U,	// VPMOVDWmr
    1996819495U,	// VPMOVDWmrk
    551816231U,	// VPMOVDWrr
    1887489063U,	// VPMOVDWrrk
    1887489063U,	// VPMOVDWrrkz
    551816953U,	// VPMOVMSKBYrr
    551816953U,	// VPMOVMSKBrr
    65044590U,	// VPMOVQBmr
    1944387694U,	// VPMOVQBmrk
    551813230U,	// VPMOVQBrr
    1887486062U,	// VPMOVQBrrk
    1887486062U,	// VPMOVQBrrkz
    117474336U,	// VPMOVQDmr
    1996817440U,	// VPMOVQDmrk
    551814176U,	// VPMOVQDrr
    1887487008U,	// VPMOVQDrrk
    1887487008U,	// VPMOVQDrrkz
    65047644U,	// VPMOVQWmr
    1944390748U,	// VPMOVQWmrk
    551816284U,	// VPMOVQWrr
    1887489116U,	// VPMOVQWrrk
    1887489116U,	// VPMOVQWrrkz
    65044537U,	// VPMOVSDBmr
    1944387641U,	// VPMOVSDBmrk
    551813177U,	// VPMOVSDBrr
    1887486009U,	// VPMOVSDBrrk
    1887486009U,	// VPMOVSDBrrkz
    117476380U,	// VPMOVSDWmr
    1996819484U,	// VPMOVSDWmrk
    551816220U,	// VPMOVSDWrr
    1887489052U,	// VPMOVSDWrrk
    1887489052U,	// VPMOVSDWrrkz
    65044579U,	// VPMOVSQBmr
    1944387683U,	// VPMOVSQBmrk
    551813219U,	// VPMOVSQBrr
    1887486051U,	// VPMOVSQBrrk
    1887486051U,	// VPMOVSQBrrkz
    117474325U,	// VPMOVSQDmr
    1996817429U,	// VPMOVSQDmrk
    551814165U,	// VPMOVSQDrr
    1887486997U,	// VPMOVSQDrrk
    1887486997U,	// VPMOVSQDrrkz
    65047633U,	// VPMOVSQWmr
    1944390737U,	// VPMOVSQWmrk
    551816273U,	// VPMOVSQWrr
    1887489105U,	// VPMOVSQWrrk
    1887489105U,	// VPMOVSQWrrkz
    551801090U,	// VPMOVSXBDYrm
    551817474U,	// VPMOVSXBDYrr
    311514U,	// VPMOVSXBDZrm
    1887731930U,	// VPMOVSXBDZrmk
    1887731930U,	// VPMOVSXBDZrmkz
    551813338U,	// VPMOVSXBDZrr
    1887486170U,	// VPMOVSXBDZrrk
    1887486170U,	// VPMOVSXBDZrrkz
    551801090U,	// VPMOVSXBDrm
    551817474U,	// VPMOVSXBDrr
    384621U,	// VPMOVSXBQYrm
    551820909U,	// VPMOVSXBQYrr
    312969U,	// VPMOVSXBQZrm
    1887733385U,	// VPMOVSXBQZrmk
    1887733385U,	// VPMOVSXBQZrmkz
    551814793U,	// VPMOVSXBQZrr
    1887487625U,	// VPMOVSXBQZrrk
    1887487625U,	// VPMOVSXBQZrrkz
    384621U,	// VPMOVSXBQrm
    551820909U,	// VPMOVSXBQrr
    322151U,	// VPMOVSXBWYrm
    551823975U,	// VPMOVSXBWYrr
    551840359U,	// VPMOVSXBWrm
    551823975U,	// VPMOVSXBWrr
    319427U,	// VPMOVSXDQYrm
    551821251U,	// VPMOVSXDQYrr
    1017764U,	// VPMOVSXDQZrm
    1888438180U,	// VPMOVSXDQZrmk
    1888438180U,	// VPMOVSXDQZrmkz
    551815076U,	// VPMOVSXDQZrr
    1887487908U,	// VPMOVSXDQZrrk
    1887487908U,	// VPMOVSXDQZrrkz
    551837635U,	// VPMOVSXDQrm
    551821251U,	// VPMOVSXDQrr
    317222U,	// VPMOVSXWDYrm
    551819046U,	// VPMOVSXWDYrr
    1017194U,	// VPMOVSXWDZrm
    1888437610U,	// VPMOVSXWDZrmk
    1888437610U,	// VPMOVSXWDZrmkz
    551814506U,	// VPMOVSXWDZrr
    1887487338U,	// VPMOVSXWDZrrk
    1887487338U,	// VPMOVSXWDZrrkz
    551835430U,	// VPMOVSXWDrm
    551819046U,	// VPMOVSXWDrr
    551805712U,	// VPMOVSXWQYrm
    551822096U,	// VPMOVSXWQYrr
    313597U,	// VPMOVSXWQZrm
    1887734013U,	// VPMOVSXWQZrmk
    1887734013U,	// VPMOVSXWQZrmkz
    551815421U,	// VPMOVSXWQZrr
    1887488253U,	// VPMOVSXWQZrrk
    1887488253U,	// VPMOVSXWQZrrkz
    551805712U,	// VPMOVSXWQrm
    551822096U,	// VPMOVSXWQrr
    65044525U,	// VPMOVUSDBmr
    1944387629U,	// VPMOVUSDBmrk
    551813165U,	// VPMOVUSDBrr
    1887485997U,	// VPMOVUSDBrrk
    1887485997U,	// VPMOVUSDBrrkz
    117476368U,	// VPMOVUSDWmr
    1996819472U,	// VPMOVUSDWmrk
    551816208U,	// VPMOVUSDWrr
    1887489040U,	// VPMOVUSDWrrk
    1887489040U,	// VPMOVUSDWrrkz
    65044567U,	// VPMOVUSQBmr
    1944387671U,	// VPMOVUSQBmrk
    551813207U,	// VPMOVUSQBrr
    1887486039U,	// VPMOVUSQBrrk
    1887486039U,	// VPMOVUSQBrrkz
    117474313U,	// VPMOVUSQDmr
    1996817417U,	// VPMOVUSQDmrk
    551814153U,	// VPMOVUSQDrr
    1887486985U,	// VPMOVUSQDrrk
    1887486985U,	// VPMOVUSQDrrkz
    65047621U,	// VPMOVUSQWmr
    1944390725U,	// VPMOVUSQWmrk
    551816261U,	// VPMOVUSQWrr
    1887489093U,	// VPMOVUSQWrrk
    1887489093U,	// VPMOVUSQWrrkz
    551801101U,	// VPMOVZXBDYrm
    551817485U,	// VPMOVZXBDYrr
    311526U,	// VPMOVZXBDZrm
    1887731942U,	// VPMOVZXBDZrmk
    1887731942U,	// VPMOVZXBDZrmkz
    551813350U,	// VPMOVZXBDZrr
    1887486182U,	// VPMOVZXBDZrrk
    1887486182U,	// VPMOVZXBDZrrkz
    551801101U,	// VPMOVZXBDrm
    551817485U,	// VPMOVZXBDrr
    384632U,	// VPMOVZXBQYrm
    551820920U,	// VPMOVZXBQYrr
    312981U,	// VPMOVZXBQZrm
    1887733397U,	// VPMOVZXBQZrmk
    1887733397U,	// VPMOVZXBQZrmkz
    551814805U,	// VPMOVZXBQZrr
    1887487637U,	// VPMOVZXBQZrrk
    1887487637U,	// VPMOVZXBQZrrkz
    384632U,	// VPMOVZXBQrm
    551820920U,	// VPMOVZXBQrr
    322162U,	// VPMOVZXBWYrm
    551823986U,	// VPMOVZXBWYrr
    551840370U,	// VPMOVZXBWrm
    551823986U,	// VPMOVZXBWrr
    319438U,	// VPMOVZXDQYrm
    551821262U,	// VPMOVZXDQYrr
    1017776U,	// VPMOVZXDQZrm
    1888438192U,	// VPMOVZXDQZrmk
    1888438192U,	// VPMOVZXDQZrmkz
    551815088U,	// VPMOVZXDQZrr
    1887487920U,	// VPMOVZXDQZrrk
    1887487920U,	// VPMOVZXDQZrrkz
    551837646U,	// VPMOVZXDQrm
    551821262U,	// VPMOVZXDQrr
    317233U,	// VPMOVZXWDYrm
    551819057U,	// VPMOVZXWDYrr
    1017206U,	// VPMOVZXWDZrm
    1888437622U,	// VPMOVZXWDZrmk
    1888437622U,	// VPMOVZXWDZrmkz
    551814518U,	// VPMOVZXWDZrr
    1887487350U,	// VPMOVZXWDZrrk
    1887487350U,	// VPMOVZXWDZrrkz
    551835441U,	// VPMOVZXWDrm
    551819057U,	// VPMOVZXWDrr
    551805723U,	// VPMOVZXWQYrm
    551822107U,	// VPMOVZXWQYrr
    313609U,	// VPMOVZXWQZrm
    1887734025U,	// VPMOVZXWQZrmk
    1887734025U,	// VPMOVZXWQZrmkz
    551815433U,	// VPMOVZXWQZrr
    1887488265U,	// VPMOVZXWQZrrk
    1887488265U,	// VPMOVZXWQZrrkz
    551805723U,	// VPMOVZXWQrm
    551822107U,	// VPMOVZXWQrr
    812605248U,	// VPMULDQYrm
    811654976U,	// VPMULDQYrr
    812828406U,	// VPMULDQZrm
    352421622U,	// VPMULDQZrmb
    1427343094U,	// VPMULDQZrmbk
    1427343094U,	// VPMULDQZrmbkz
    571639542U,	// VPMULDQZrmk
    571639542U,	// VPMULDQZrmkz
    811648758U,	// VPMULDQZrr
    570787574U,	// VPMULDQZrrk
    570787574U,	// VPMULDQZrrkz
    811900736U,	// VPMULDQrm
    811654976U,	// VPMULDQrr
    811904302U,	// VPMULHRSWrm128
    812608814U,	// VPMULHRSWrm256
    811658542U,	// VPMULHRSWrr128
    811658542U,	// VPMULHRSWrr256
    812609013U,	// VPMULHUWYrm
    811658741U,	// VPMULHUWYrr
    811904501U,	// VPMULHUWrm
    811658741U,	// VPMULHUWrr
    812608387U,	// VPMULHWYrm
    811658115U,	// VPMULHWYrr
    811903875U,	// VPMULHWrm
    811658115U,	// VPMULHWrr
    812601759U,	// VPMULLDYrm
    811651487U,	// VPMULLDYrr
    812826938U,	// VPMULLDZrm
    354500922U,	// VPMULLDZrmb
    4113678650U,	// VPMULLDZrmbk
    1429406010U,	// VPMULLDZrmbkz
    122896698U,	// VPMULLDZrmk
    571638074U,	// VPMULLDZrmkz
    811647290U,	// VPMULLDZrr
    1888076090U,	// VPMULLDZrrk
    570786106U,	// VPMULLDZrrkz
    811897247U,	// VPMULLDrm
    811651487U,	// VPMULLDrr
    812608456U,	// VPMULLWYrm
    811658184U,	// VPMULLWYrr
    811903944U,	// VPMULLWrm
    811658184U,	// VPMULLWrr
    812605369U,	// VPMULUDQYrm
    811655097U,	// VPMULUDQYrr
    812828569U,	// VPMULUDQZrm
    352421785U,	// VPMULUDQZrmb
    1427343257U,	// VPMULUDQZrmbk
    1427343257U,	// VPMULUDQZrmbkz
    571639705U,	// VPMULUDQZrmk
    571639705U,	// VPMULUDQZrmkz
    811648921U,	// VPMULUDQZrr
    570787737U,	// VPMULUDQZrrk
    570787737U,	// VPMULUDQZrrkz
    811900857U,	// VPMULUDQrm
    811655097U,	// VPMULUDQrr
    812827706U,	// VPORDZrm
    354501690U,	// VPORDZrmb
    4113679418U,	// VPORDZrmbk
    1429406778U,	// VPORDZrmbkz
    122897466U,	// VPORDZrmk
    571638842U,	// VPORDZrmkz
    811648058U,	// VPORDZrr
    1888076858U,	// VPORDZrrk
    570786874U,	// VPORDZrrkz
    812828738U,	// VPORQZrm
    352421954U,	// VPORQZrmb
    4111566914U,	// VPORQZrmbk
    1427343426U,	// VPORQZrmbkz
    122898498U,	// VPORQZrmk
    571639874U,	// VPORQZrmkz
    811649090U,	// VPORQZrr
    1888077890U,	// VPORQZrrk
    570787906U,	// VPORQZrrkz
    812606337U,	// VPORYrm
    811656065U,	// VPORYrr
    811901825U,	// VPORrm
    811656065U,	// VPORrr
    340090138U,	// VPPERMmr
    303176986U,	// VPPERMrm
    302357786U,	// VPPERMrr
    25497615U,	// VPROTBmi
    25497615U,	// VPROTBmr
    811651087U,	// VPROTBri
    811896847U,	// VPROTBrm
    811651087U,	// VPROTBrr
    25499217U,	// VPROTDmi
    25499217U,	// VPROTDmr
    811652689U,	// VPROTDri
    811898449U,	// VPROTDrm
    811652689U,	// VPROTDrr
    25502326U,	// VPROTQmi
    25502326U,	// VPROTQmr
    811655798U,	// VPROTQri
    811901558U,	// VPROTQrm
    811655798U,	// VPROTQrr
    25505240U,	// VPROTWmi
    25505240U,	// VPROTWmr
    811658712U,	// VPROTWri
    811904472U,	// VPROTWrm
    811658712U,	// VPROTWrr
    812608016U,	// VPSADBWYrm
    811657744U,	// VPSADBWYrr
    811903504U,	// VPSADBWrm
    811657744U,	// VPSADBWrr
    1906655513U,	// VPSCATTERDDZmr
    1908754234U,	// VPSCATTERDQZmr
    1908753403U,	// VPSCATTERQDZmr
    1908754468U,	// VPSCATTERQQZmr
    25497239U,	// VPSHABmr
    811896471U,	// VPSHABrm
    811650711U,	// VPSHABrr
    25497804U,	// VPSHADmr
    811897036U,	// VPSHADrm
    811651276U,	// VPSHADrr
    25501226U,	// VPSHAQmr
    811900458U,	// VPSHAQrm
    811654698U,	// VPSHAQrr
    25504222U,	// VPSHAWmr
    811903454U,	// VPSHAWrm
    811657694U,	// VPSHAWrr
    25497360U,	// VPSHLBmr
    811896592U,	// VPSHLBrm
    811650832U,	// VPSHLBrr
    25497999U,	// VPSHLDmr
    811897231U,	// VPSHLDrm
    811651471U,	// VPSHLDrr
    25501863U,	// VPSHLQmr
    811901095U,	// VPSHLQrm
    811655335U,	// VPSHLQrr
    25504688U,	// VPSHLWmr
    811903920U,	// VPSHLWrm
    811658160U,	// VPSHLWrr
    812601044U,	// VPSHUFBYrm
    811650772U,	// VPSHUFBYrr
    811896532U,	// VPSHUFBrm
    811650772U,	// VPSHUFBrr
    151327074U,	// VPSHUFDYmi
    811651426U,	// VPSHUFDYri
    149225767U,	// VPSHUFDZmi
    811647271U,	// VPSHUFDZri
    25497954U,	// VPSHUFDmi
    811651426U,	// VPSHUFDri
    151333753U,	// VPSHUFHWYmi
    811658105U,	// VPSHUFHWYri
    25504633U,	// VPSHUFHWmi
    811658105U,	// VPSHUFHWri
    151333798U,	// VPSHUFLWYmi
    811658150U,	// VPSHUFLWYri
    25504678U,	// VPSHUFLWmi
    811658150U,	// VPSHUFLWri
    812601133U,	// VPSIGNBYrm
    811650861U,	// VPSIGNBYrr
    811896621U,	// VPSIGNBrm
    811650861U,	// VPSIGNBrr
    812601808U,	// VPSIGNDYrm
    811651536U,	// VPSIGNDYrr
    811897296U,	// VPSIGNDrm
    811651536U,	// VPSIGNDrr
    812608508U,	// VPSIGNWYrm
    811658236U,	// VPSIGNWYrr
    811903996U,	// VPSIGNWrm
    811658236U,	// VPSIGNWrr
    811654958U,	// VPSLLDQYri
    811654958U,	// VPSLLDQri
    811651479U,	// VPSLLDYri
    811897239U,	// VPSLLDYrm
    811651479U,	// VPSLLDYrr
    149225777U,	// VPSLLDZmi
    625295665U,	// VPSLLDZmik
    811647281U,	// VPSLLDZri
    570786097U,	// VPSLLDZrik
    811893041U,	// VPSLLDZrm
    571605297U,	// VPSLLDZrmk
    811647281U,	// VPSLLDZrr
    570786097U,	// VPSLLDZrrk
    811651479U,	// VPSLLDri
    811897239U,	// VPSLLDrm
    811651479U,	// VPSLLDrr
    811655350U,	// VPSLLQYri
    811901110U,	// VPSLLQYrm
    811655350U,	// VPSLLQYrr
    149227452U,	// VPSLLQZmi
    625297340U,	// VPSLLQZmik
    811648956U,	// VPSLLQZri
    570787772U,	// VPSLLQZrik
    811894716U,	// VPSLLQZrm
    571606972U,	// VPSLLQZrmk
    811648956U,	// VPSLLQZrr
    570787772U,	// VPSLLQZrrk
    811655350U,	// VPSLLQri
    811901110U,	// VPSLLQrm
    811655350U,	// VPSLLQrr
    812603028U,	// VPSLLVDYrm
    811652756U,	// VPSLLVDYrr
    812827982U,	// VPSLLVDZrm
    811648334U,	// VPSLLVDZrr
    811898516U,	// VPSLLVDrm
    811652756U,	// VPSLLVDrr
    812606151U,	// VPSLLVQYrm
    811655879U,	// VPSLLVQYrr
    812828897U,	// VPSLLVQZrm
    811649249U,	// VPSLLVQZrr
    811901639U,	// VPSLLVQrm
    811655879U,	// VPSLLVQrr
    811658176U,	// VPSLLWYri
    811903936U,	// VPSLLWYrm
    811658176U,	// VPSLLWYrr
    811658176U,	// VPSLLWri
    811903936U,	// VPSLLWrm
    811658176U,	// VPSLLWrr
    811651284U,	// VPSRADYri
    811897044U,	// VPSRADYrm
    811651284U,	// VPSRADYrr
    149225672U,	// VPSRADZmi
    625295560U,	// VPSRADZmik
    811647176U,	// VPSRADZri
    570785992U,	// VPSRADZrik
    811892936U,	// VPSRADZrm
    571605192U,	// VPSRADZrmk
    811647176U,	// VPSRADZrr
    570785992U,	// VPSRADZrrk
    811651284U,	// VPSRADri
    811897044U,	// VPSRADrm
    811651284U,	// VPSRADrr
    149227127U,	// VPSRAQZmi
    625297015U,	// VPSRAQZmik
    811648631U,	// VPSRAQZri
    570787447U,	// VPSRAQZrik
    811894391U,	// VPSRAQZrm
    571606647U,	// VPSRAQZrmk
    811648631U,	// VPSRAQZrr
    570787447U,	// VPSRAQZrrk
    812603019U,	// VPSRAVDYrm
    811652747U,	// VPSRAVDYrr
    812827972U,	// VPSRAVDZrm
    811648324U,	// VPSRAVDZrr
    811898507U,	// VPSRAVDrm
    811652747U,	// VPSRAVDrr
    812828887U,	// VPSRAVQZrm
    811649239U,	// VPSRAVQZrr
    811657702U,	// VPSRAWYri
    811903462U,	// VPSRAWYrm
    811657702U,	// VPSRAWYrr
    811657702U,	// VPSRAWri
    811903462U,	// VPSRAWrm
    811657702U,	// VPSRAWrr
    811654967U,	// VPSRLDQYri
    811654967U,	// VPSRLDQri
    811651496U,	// VPSRLDYri
    811897256U,	// VPSRLDYrm
    811651496U,	// VPSRLDYrr
    149225796U,	// VPSRLDZmi
    625295684U,	// VPSRLDZmik
    811647300U,	// VPSRLDZri
    570786116U,	// VPSRLDZrik
    811893060U,	// VPSRLDZrm
    571605316U,	// VPSRLDZrmk
    811647300U,	// VPSRLDZrr
    570786116U,	// VPSRLDZrrk
    811651496U,	// VPSRLDri
    811897256U,	// VPSRLDrm
    811651496U,	// VPSRLDrr
    811655364U,	// VPSRLQYri
    811901124U,	// VPSRLQYrm
    811655364U,	// VPSRLQYrr
    149227461U,	// VPSRLQZmi
    625297349U,	// VPSRLQZmik
    811648965U,	// VPSRLQZri
    570787781U,	// VPSRLQZrik
    811894725U,	// VPSRLQZrm
    571606981U,	// VPSRLQZrmk
    811648965U,	// VPSRLQZrr
    570787781U,	// VPSRLQZrrk
    811655364U,	// VPSRLQri
    811901124U,	// VPSRLQrm
    811655364U,	// VPSRLQrr
    812603037U,	// VPSRLVDYrm
    811652765U,	// VPSRLVDYrr
    812827992U,	// VPSRLVDZrm
    811648344U,	// VPSRLVDZrr
    811898525U,	// VPSRLVDrm
    811652765U,	// VPSRLVDrr
    812606160U,	// VPSRLVQYrm
    811655888U,	// VPSRLVQYrr
    812828907U,	// VPSRLVQZrm
    811649259U,	// VPSRLVQZrr
    811901648U,	// VPSRLVQrm
    811655888U,	// VPSRLVQrr
    811658199U,	// VPSRLWYri
    811903959U,	// VPSRLWYrm
    811658199U,	// VPSRLWYrr
    811658199U,	// VPSRLWri
    811903959U,	// VPSRLWrm
    811658199U,	// VPSRLWrr
    812600997U,	// VPSUBBYrm
    811650725U,	// VPSUBBYrr
    811896485U,	// VPSUBBrm
    811650725U,	// VPSUBBrr
    812601594U,	// VPSUBDYrm
    811651322U,	// VPSUBDYrr
    812826833U,	// VPSUBDZrm
    354500817U,	// VPSUBDZrmb
    4113678545U,	// VPSUBDZrmbk
    1429405905U,	// VPSUBDZrmbkz
    122896593U,	// VPSUBDZrmk
    571637969U,	// VPSUBDZrmkz
    811647185U,	// VPSUBDZrr
    1888075985U,	// VPSUBDZrrk
    570786001U,	// VPSUBDZrrkz
    811897082U,	// VPSUBDrm
    811651322U,	// VPSUBDrr
    812605021U,	// VPSUBQYrm
    811654749U,	// VPSUBQYrr
    812828288U,	// VPSUBQZrm
    352421504U,	// VPSUBQZrmb
    4111566464U,	// VPSUBQZrmbk
    1427342976U,	// VPSUBQZrmbkz
    122898048U,	// VPSUBQZrmk
    571639424U,	// VPSUBQZrmkz
    811648640U,	// VPSUBQZrr
    1888077440U,	// VPSUBQZrrk
    570787456U,	// VPSUBQZrrkz
    811900509U,	// VPSUBQrm
    811654749U,	// VPSUBQrr
    812601244U,	// VPSUBSBYrm
    811650972U,	// VPSUBSBYrr
    811896732U,	// VPSUBSBrm
    811650972U,	// VPSUBSBrr
    812608730U,	// VPSUBSWYrm
    811658458U,	// VPSUBSWYrr
    811904218U,	// VPSUBSWrm
    811658458U,	// VPSUBSWrr
    812601292U,	// VPSUBUSBYrm
    811651020U,	// VPSUBUSBYrr
    811896780U,	// VPSUBUSBrm
    811651020U,	// VPSUBUSBrr
    812608852U,	// VPSUBUSWYrm
    811658580U,	// VPSUBUSWYrr
    811904340U,	// VPSUBUSWrm
    811658580U,	// VPSUBUSWrr
    812608087U,	// VPSUBWYrm
    811657815U,	// VPSUBWYrr
    811903575U,	// VPSUBWrm
    811657815U,	// VPSUBWrr
    812532078U,	// VPTESTMDZrm
    811647342U,	// VPTESTMDZrr
    812533743U,	// VPTESTMQZrm
    811649007U,	// VPTESTMQZrr
    812532057U,	// VPTESTNMDZrm
    811647321U,	// VPTESTNMDZrr
    812533722U,	// VPTESTNMQZrm
    811648986U,	// VPTESTNMQZrr
    1026394U,	// VPTESTYrm
    551823706U,	// VPTESTYrr
    584026U,	// VPTESTrm
    551823706U,	// VPTESTrr
    812608035U,	// VPUNPCKHBWYrm
    811657763U,	// VPUNPCKHBWYrr
    811903523U,	// VPUNPCKHBWrm
    811657763U,	// VPUNPCKHBWrr
    812605199U,	// VPUNPCKHDQYrm
    811654927U,	// VPUNPCKHDQYrr
    812828380U,	// VPUNPCKHDQZrm
    811648732U,	// VPUNPCKHDQZrr
    811900687U,	// VPUNPCKHDQrm
    811654927U,	// VPUNPCKHDQrr
    812605266U,	// VPUNPCKHQDQYrm
    811654994U,	// VPUNPCKHQDQYrr
    812828433U,	// VPUNPCKHQDQZrm
    811648785U,	// VPUNPCKHQDQZrr
    811900754U,	// VPUNPCKHQDQrm
    811654994U,	// VPUNPCKHQDQrr
    812603095U,	// VPUNPCKHWDYrm
    811652823U,	// VPUNPCKHWDYrr
    811898583U,	// VPUNPCKHWDrm
    811652823U,	// VPUNPCKHWDrr
    812608047U,	// VPUNPCKLBWYrm
    811657775U,	// VPUNPCKLBWYrr
    811903535U,	// VPUNPCKLBWrm
    811657775U,	// VPUNPCKLBWrr
    812605218U,	// VPUNPCKLDQYrm
    811654946U,	// VPUNPCKLDQYrr
    812828393U,	// VPUNPCKLDQZrm
    811648745U,	// VPUNPCKLDQZrr
    811900706U,	// VPUNPCKLDQrm
    811654946U,	// VPUNPCKLDQrr
    812605279U,	// VPUNPCKLQDQYrm
    811655007U,	// VPUNPCKLQDQYrr
    812828447U,	// VPUNPCKLQDQZrm
    811648799U,	// VPUNPCKLQDQZrr
    811900767U,	// VPUNPCKLQDQrm
    811655007U,	// VPUNPCKLQDQrr
    812603107U,	// VPUNPCKLWDYrm
    811652835U,	// VPUNPCKLWDYrr
    811898595U,	// VPUNPCKLWDrm
    811652835U,	// VPUNPCKLWDrr
    812827722U,	// VPXORDZrm
    354501706U,	// VPXORDZrmb
    4113679434U,	// VPXORDZrmbk
    1429406794U,	// VPXORDZrmbkz
    122897482U,	// VPXORDZrmk
    571638858U,	// VPXORDZrmkz
    811648074U,	// VPXORDZrr
    1888076874U,	// VPXORDZrrk
    570786890U,	// VPXORDZrrkz
    812828754U,	// VPXORQZrm
    352421970U,	// VPXORQZrmb
    4111566930U,	// VPXORQZrmbk
    1427343442U,	// VPXORQZrmbkz
    122898514U,	// VPXORQZrmk
    571639890U,	// VPXORQZrmkz
    811649106U,	// VPXORQZrr
    1888077906U,	// VPXORQZrrk
    570787922U,	// VPXORQZrrkz
    812606360U,	// VPXORYrm
    811656088U,	// VPXORYrr
    811901848U,	// VPXORrm
    811656088U,	// VPXORrr
    1081995U,	// VRCP14PDZm
    551813771U,	// VRCP14PDZr
    1083914U,	// VRCP14PSZm
    551815690U,	// VRCP14PSZr
    283264141U,	// VRCP14SDrm
    811648141U,	// VRCP14SDrr
    283282343U,	// VRCP14SSrm
    811649959U,	// VRCP14SSrr
    1082019U,	// VRCP28PDZm
    551813795U,	// VRCP28PDZr
    551826154U,	// VRCP28PDZrb
    1083938U,	// VRCP28PSZm
    551815714U,	// VRCP28PSZr
    551826230U,	// VRCP28PSZrb
    283264165U,	// VRCP28SDrm
    811648165U,	// VRCP28SDrr
    811660048U,	// VRCP28SDrrb
    283282367U,	// VRCP28SSrm
    811649983U,	// VRCP28SSrr
    811660124U,	// VRCP28SSrrb
    1074831U,	// VRCPPSYm
    1074831U,	// VRCPPSYm_Int
    551822991U,	// VRCPPSYr
    551822991U,	// VRCPPSYr_Int
    583311U,	// VRCPPSm
    583311U,	// VRCPPSm_Int
    551822991U,	// VRCPPSr
    551822991U,	// VRCPPSr_Int
    283289733U,	// VRCPSSm
    283289733U,	// VRCPSSm_Int
    811657349U,	// VRCPSSr
    153420586U,	// VRNDSCALEPDZm
    811647786U,	// VRNDSCALEPDZr
    153422505U,	// VRNDSCALEPSZm
    811649705U,	// VRNDSCALEPSZr
    283264198U,	// VRNDSCALESDm
    811648198U,	// VRNDSCALESDr
    283282391U,	// VRNDSCALESSm
    811650007U,	// VRNDSCALESSr
    80024482U,	// VROUNDPDm
    811652002U,	// VROUNDPDr
    80029163U,	// VROUNDPSm
    811656683U,	// VROUNDPSr
    312825279U,	// VROUNDSDm
    302355903U,	// VROUNDSDr
    302355903U,	// VROUNDSDr_Int
    317024344U,	// VROUNDSSm
    302360664U,	// VROUNDSSr
    302360664U,	// VROUNDSSr_Int
    147133346U,	// VROUNDYPDm
    811652002U,	// VROUNDYPDr
    147138027U,	// VROUNDYPSm
    811656683U,	// VROUNDYPSr
    1082006U,	// VRSQRT14PDZm
    551813782U,	// VRSQRT14PDZr
    1083925U,	// VRSQRT14PSZm
    551815701U,	// VRSQRT14PSZr
    283264152U,	// VRSQRT14SDrm
    811648152U,	// VRSQRT14SDrr
    283282354U,	// VRSQRT14SSrm
    811649970U,	// VRSQRT14SSrr
    1082030U,	// VRSQRT28PDZm
    551813806U,	// VRSQRT28PDZr
    551826172U,	// VRSQRT28PDZrb
    1083949U,	// VRSQRT28PSZm
    551815725U,	// VRSQRT28PSZr
    551826248U,	// VRSQRT28PSZrb
    283264176U,	// VRSQRT28SDrm
    811648176U,	// VRSQRT28SDrr
    811660066U,	// VRSQRT28SDrrb
    283282378U,	// VRSQRT28SSrm
    811649994U,	// VRSQRT28SSrr
    811660142U,	// VRSQRT28SSrrb
    1074914U,	// VRSQRTPSYm
    1074914U,	// VRSQRTPSYm_Int
    551823074U,	// VRSQRTPSYr
    551823074U,	// VRSQRTPSYr_Int
    583394U,	// VRSQRTPSm
    583394U,	// VRSQRTPSm_Int
    551823074U,	// VRSQRTPSr
    551823074U,	// VRSQRTPSr_Int
    283289758U,	// VRSQRTSSm
    283289758U,	// VRSQRTSSm_Int
    811657374U,	// VRSQRTSSr
    1908753180U,	// VSCATTERDPDZmr
    1906657947U,	// VSCATTERDPSZmr
    111395540U,	// VSCATTERPF0DPDm
    111397459U,	// VSCATTERPF0DPSm
    111428495U,	// VSCATTERPF0QPDm
    111430414U,	// VSCATTERPF0QPSm
    111395573U,	// VSCATTERPF1DPDm
    111397492U,	// VSCATTERPF1DPSm
    111428528U,	// VSCATTERPF1QPDm
    111430447U,	// VSCATTERPF1QPSm
    1908753358U,	// VSCATTERQPDZmr
    1908755277U,	// VSCATTERQPSZmr
    92623800U,	// VSHUFPDYrmi
    302355384U,	// VSHUFPDYrri
    96813880U,	// VSHUFPDZrmi
    302351160U,	// VSHUFPDZrri
    300241848U,	// VSHUFPDrmi
    302355384U,	// VSHUFPDrri
    92628481U,	// VSHUFPSYrmi
    302360065U,	// VSHUFPSYrri
    96815799U,	// VSHUFPSZrmi
    302353079U,	// VSHUFPSZrri
    300246529U,	// VSHUFPSrmi
    302360065U,	// VSHUFPSrri
    1070173U,	// VSQRTPDYm
    551818333U,	// VSQRTPDYr
    1086557U,	// VSQRTPDZrm
    551818333U,	// VSQRTPDZrr
    578653U,	// VSQRTPDm
    551818333U,	// VSQRTPDr
    1074924U,	// VSQRTPSYm
    551823084U,	// VSQRTPSYr
    1091308U,	// VSQRTPSZrm
    551823084U,	// VSQRTPSZrr
    583404U,	// VSQRTPSm
    551823084U,	// VSQRTPSr
    283268614U,	// VSQRTSDZm
    283268614U,	// VSQRTSDZm_Int
    811652614U,	// VSQRTSDZr
    811652614U,	// VSQRTSDZr_Int
    283268614U,	// VSQRTSDm
    283268614U,	// VSQRTSDm_Int
    811652614U,	// VSQRTSDr
    283289768U,	// VSQRTSSZm
    283289768U,	// VSQRTSSZm_Int
    811657384U,	// VSQRTSSZr
    811657384U,	// VSQRTSSZr_Int
    283289768U,	// VSQRTSSm
    283289768U,	// VSQRTSSm_Int
    811657384U,	// VSQRTSSr
    238511U,	// VSTMXCSR
    812520277U,	// VSUBPDYrm
    811651925U,	// VSUBPDYrr
    812532411U,	// VSUBPDZrm
    352469691U,	// VSUBPDZrmb
    1427014331U,	// VSUBPDZrmbk
    1427014331U,	// VSUBPDZrmbkz
    571396949U,	// VSUBPDZrmk
    571396949U,	// VSUBPDZrmkz
    811647675U,	// VSUBPDZrr
    570786491U,	// VSUBPDZrrk
    570786491U,	// VSUBPDZrrkz
    811733845U,	// VSUBPDrm
    811651925U,	// VSUBPDrr
    812524958U,	// VSUBPSYrm
    811656606U,	// VSUBPSYrr
    812534330U,	// VSUBPSZrm
    354585146U,	// VSUBPSZrmb
    1429146170U,	// VSUBPSZrmbk
    1429146170U,	// VSUBPSZrmbkz
    571401630U,	// VSUBPSZrmk
    571401630U,	// VSUBPSZrmkz
    811649594U,	// VSUBPSZrr
    570788410U,	// VSUBPSZrrk
    570788410U,	// VSUBPSZrrkz
    811738526U,	// VSUBPSrm
    811656606U,	// VSUBPSrr
    283268506U,	// VSUBSDZrm
    811652506U,	// VSUBSDZrr
    283268506U,	// VSUBSDrm
    283268506U,	// VSUBSDrm_Int
    811652506U,	// VSUBSDrr
    811652506U,	// VSUBSDrr_Int
    283289651U,	// VSUBSSZrm
    811657267U,	// VSUBSSZrr
    283289651U,	// VSUBSSrm
    283289651U,	// VSUBSSrm_Int
    811657267U,	// VSUBSSrr
    811657267U,	// VSUBSSrr_Int
    1070182U,	// VTESTPDYrm
    551818342U,	// VTESTPDYrr
    578662U,	// VTESTPDrm
    551818342U,	// VTESTPDrr
    1074933U,	// VTESTPSYrm
    551823093U,	// VTESTPSYrr
    583413U,	// VTESTPSrm
    551823093U,	// VTESTPSrr
    595401U,	// VUCOMISDZrm
    551818697U,	// VUCOMISDZrr
    595401U,	// VUCOMISDrm
    551818697U,	// VUCOMISDrr
    616546U,	// VUCOMISSZrm
    551823458U,	// VUCOMISSZrr
    616546U,	// VUCOMISSrm
    551823458U,	// VUCOMISSrr
    812520385U,	// VUNPCKHPDYrm
    811652033U,	// VUNPCKHPDYrr
    812536769U,	// VUNPCKHPDZrm
    811652033U,	// VUNPCKHPDZrr
    811733953U,	// VUNPCKHPDrm
    811652033U,	// VUNPCKHPDrr
    812525066U,	// VUNPCKHPSYrm
    811656714U,	// VUNPCKHPSYrr
    812541450U,	// VUNPCKHPSZrm
    811656714U,	// VUNPCKHPSZrr
    811738634U,	// VUNPCKHPSrm
    811656714U,	// VUNPCKHPSrr
    812520427U,	// VUNPCKLPDYrm
    811652075U,	// VUNPCKLPDYrr
    812536811U,	// VUNPCKLPDZrm
    811652075U,	// VUNPCKLPDZrr
    811733995U,	// VUNPCKLPDrm
    811652075U,	// VUNPCKLPDrr
    812525128U,	// VUNPCKLPSYrm
    811656776U,	// VUNPCKLPSYrr
    812541512U,	// VUNPCKLPSZrm
    811656776U,	// VUNPCKLPSZrr
    811738696U,	// VUNPCKLPSrm
    811656776U,	// VUNPCKLPSrr
    812520523U,	// VXORPDYrm
    811652171U,	// VXORPDYrr
    811734091U,	// VXORPDrm
    811652171U,	// VXORPDrr
    812525241U,	// VXORPSYrm
    811656889U,	// VXORPSYrr
    811738809U,	// VXORPSrm
    811656889U,	// VXORPSrr
    14534U,	// VZEROALL
    14807U,	// VZEROUPPER
    0U,	// V_SET0
    0U,	// V_SETALLONES
    417967U,	// W64ALLOCA
    15221U,	// WAIT
    14277U,	// WBINVD
    14645U,	// WIN_ALLOCA
    14452U,	// WIN_FTOL_32
    14452U,	// WIN_FTOL_64
    22986U,	// WRFSBASE
    24602U,	// WRFSBASE64
    23008U,	// WRGSBASE
    24624U,	// WRGSBASE64
    14841U,	// WRMSR
    26927U,	// XABORT
    14334U,	// XACQUIRE_PREFIX
    4238013U,	// XADD16rm
    551824061U,	// XADD16rr
    12622149U,	// XADD32rm
    551819589U,	// XADD32rr
    18915061U,	// XADD64rm
    551821045U,	// XADD64rr
    23105223U,	// XADD8rm
    551816903U,	// XADD8rr
    14046U,	// XBEGIN
    417090U,	// XBEGIN_4
    2124631U,	// XCHG16ar
    3504614231U,	// XCHG16rm
    1357130583U,	// XCHG16rr
    10508812U,	// XCHG32ar
    10508812U,	// XCHG32ar64
    3773045260U,	// XCHG32rm
    1357126156U,	// XCHG32rr
    16801893U,	// XCHG64ar
    4041482341U,	// XCHG64rm
    1357127781U,	// XCHG64rr
    14946022U,	// XCHG8rm
    1357123302U,	// XCHG8rr
    22524U,	// XCH_F
    14195U,	// XCRYPTCBC
    14139U,	// XCRYPTCFB
    14847U,	// XCRYPTCTR
    14129U,	// XCRYPTECB
    14149U,	// XCRYPTOFB
    14258U,	// XEND
    15268U,	// XGETBV
    14179U,	// XLAT
    2124924U,	// XOR16i16
    4238460U,	// XOR16mi
    4238460U,	// XOR16mi8
    4238460U,	// XOR16mr
    6351996U,	// XOR16ri
    6351996U,	// XOR16ri8
    6368380U,	// XOR16rm
    6351996U,	// XOR16rr
    8449148U,	// XOR16rr_REV
    10509194U,	// XOR32i32
    12622730U,	// XOR32mi
    12622730U,	// XOR32mi8
    12622730U,	// XOR32mr
    6347658U,	// XOR32ri
    6347658U,	// XOR32ri8
    283204490U,	// XOR32rm
    6347658U,	// XOR32rr
    8444810U,	// XOR32rr_REV
    16802173U,	// XOR64i32
    18915709U,	// XOR64mi32
    18915709U,	// XOR64mi8
    18915709U,	// XOR64mr
    6349181U,	// XOR64ri32
    6349181U,	// XOR64ri8
    283222397U,	// XOR64rm
    6349181U,	// XOR64rr
    8446333U,	// XOR64rr_REV
    20991852U,	// XOR8i8
    23105388U,	// XOR8mi
    23105388U,	// XOR8mr
    6344556U,	// XOR8ri
    6344556U,	// XOR8ri8
    118636U,	// XOR8rm
    6344556U,	// XOR8rr
    8441708U,	// XOR8rr_REV
    8524876U,	// XORPDrm
    8442956U,	// XORPDrr
    8529594U,	// XORPSrm
    8447674U,	// XORPSrr
    14350U,	// XRELEASE_PREFIX
    631696U,	// XRSTOR
    631156U,	// XRSTOR64
    628689U,	// XSAVE
    630854U,	// XSAVE64
    633118U,	// XSAVEOPT
    631422U,	// XSAVEOPT64
    15275U,	// XSETBV
    13773U,	// XSHA1
    14008U,	// XSHA256
    14343U,	// XSTORE
    15251U,	// XTEST
    0U
  };

  static const uint16_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// AAA
    0U,	// AAD8i8
    0U,	// AAM8i8
    0U,	// AAS
    0U,	// ABS_F
    0U,	// ABS_Fp32
    0U,	// ABS_Fp64
    0U,	// ABS_Fp80
    0U,	// ACQUIRE_MOV16rm
    0U,	// ACQUIRE_MOV32rm
    0U,	// ACQUIRE_MOV64rm
    0U,	// ACQUIRE_MOV8rm
    0U,	// ADC16i16
    0U,	// ADC16mi
    0U,	// ADC16mi8
    0U,	// ADC16mr
    0U,	// ADC16ri
    0U,	// ADC16ri8
    0U,	// ADC16rm
    0U,	// ADC16rr
    0U,	// ADC16rr_REV
    0U,	// ADC32i32
    0U,	// ADC32mi
    0U,	// ADC32mi8
    0U,	// ADC32mr
    0U,	// ADC32ri
    0U,	// ADC32ri8
    0U,	// ADC32rm
    0U,	// ADC32rr
    0U,	// ADC32rr_REV
    0U,	// ADC64i32
    0U,	// ADC64mi32
    0U,	// ADC64mi8
    0U,	// ADC64mr
    0U,	// ADC64ri32
    0U,	// ADC64ri8
    0U,	// ADC64rm
    0U,	// ADC64rr
    0U,	// ADC64rr_REV
    0U,	// ADC8i8
    0U,	// ADC8mi
    0U,	// ADC8mr
    0U,	// ADC8ri
    0U,	// ADC8rm
    0U,	// ADC8rr
    0U,	// ADC8rr_REV
    0U,	// ADCX32rm
    0U,	// ADCX32rr
    0U,	// ADCX64rm
    0U,	// ADCX64rr
    0U,	// ADD16i16
    0U,	// ADD16mi
    0U,	// ADD16mi8
    0U,	// ADD16mr
    0U,	// ADD16ri
    0U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    0U,	// ADD16rm
    0U,	// ADD16rr
    0U,	// ADD16rr_DB
    0U,	// ADD16rr_REV
    0U,	// ADD32i32
    0U,	// ADD32mi
    0U,	// ADD32mi8
    0U,	// ADD32mr
    0U,	// ADD32ri
    0U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    0U,	// ADD32rm
    0U,	// ADD32rr
    0U,	// ADD32rr_DB
    0U,	// ADD32rr_REV
    0U,	// ADD64i32
    0U,	// ADD64mi32
    0U,	// ADD64mi8
    0U,	// ADD64mr
    0U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    0U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    0U,	// ADD64rm
    0U,	// ADD64rr
    0U,	// ADD64rr_DB
    0U,	// ADD64rr_REV
    0U,	// ADD8i8
    0U,	// ADD8mi
    0U,	// ADD8mr
    0U,	// ADD8ri
    0U,	// ADD8ri8
    0U,	// ADD8rm
    0U,	// ADD8rr
    0U,	// ADD8rr_REV
    0U,	// ADDPDrm
    0U,	// ADDPDrr
    0U,	// ADDPSrm
    0U,	// ADDPSrr
    0U,	// ADDSDrm
    0U,	// ADDSDrm_Int
    0U,	// ADDSDrr
    0U,	// ADDSDrr_Int
    0U,	// ADDSSrm
    0U,	// ADDSSrm_Int
    0U,	// ADDSSrr
    0U,	// ADDSSrr_Int
    0U,	// ADDSUBPDrm
    0U,	// ADDSUBPDrr
    0U,	// ADDSUBPSrm
    0U,	// ADDSUBPSrr
    0U,	// ADD_F32m
    0U,	// ADD_F64m
    0U,	// ADD_FI16m
    0U,	// ADD_FI32m
    0U,	// ADD_FPrST0
    0U,	// ADD_FST0r
    0U,	// ADD_Fp32
    0U,	// ADD_Fp32m
    0U,	// ADD_Fp64
    0U,	// ADD_Fp64m
    0U,	// ADD_Fp64m32
    0U,	// ADD_Fp80
    0U,	// ADD_Fp80m32
    0U,	// ADD_Fp80m64
    0U,	// ADD_FpI16m32
    0U,	// ADD_FpI16m64
    0U,	// ADD_FpI16m80
    0U,	// ADD_FpI32m32
    0U,	// ADD_FpI32m64
    0U,	// ADD_FpI32m80
    0U,	// ADD_FrST0
    0U,	// ADJCALLSTACKDOWN32
    0U,	// ADJCALLSTACKDOWN64
    0U,	// ADJCALLSTACKUP32
    0U,	// ADJCALLSTACKUP64
    0U,	// ADOX32rm
    0U,	// ADOX32rr
    0U,	// ADOX64rm
    0U,	// ADOX64rr
    0U,	// AESDECLASTrm
    0U,	// AESDECLASTrr
    0U,	// AESDECrm
    0U,	// AESDECrr
    0U,	// AESENCLASTrm
    0U,	// AESENCLASTrr
    0U,	// AESENCrm
    0U,	// AESENCrr
    0U,	// AESIMCrm
    0U,	// AESIMCrr
    0U,	// AESKEYGENASSIST128rm
    0U,	// AESKEYGENASSIST128rr
    0U,	// AND16i16
    0U,	// AND16mi
    0U,	// AND16mi8
    0U,	// AND16mr
    0U,	// AND16ri
    0U,	// AND16ri8
    0U,	// AND16rm
    0U,	// AND16rr
    0U,	// AND16rr_REV
    0U,	// AND32i32
    0U,	// AND32mi
    0U,	// AND32mi8
    0U,	// AND32mr
    0U,	// AND32ri
    0U,	// AND32ri8
    0U,	// AND32rm
    0U,	// AND32rr
    0U,	// AND32rr_REV
    0U,	// AND64i32
    0U,	// AND64mi32
    0U,	// AND64mi8
    0U,	// AND64mr
    0U,	// AND64ri32
    0U,	// AND64ri8
    0U,	// AND64rm
    0U,	// AND64rr
    0U,	// AND64rr_REV
    0U,	// AND8i8
    0U,	// AND8mi
    0U,	// AND8mr
    0U,	// AND8ri
    0U,	// AND8ri8
    0U,	// AND8rm
    0U,	// AND8rr
    0U,	// AND8rr_REV
    4U,	// ANDN32rm
    0U,	// ANDN32rr
    4U,	// ANDN64rm
    0U,	// ANDN64rr
    0U,	// ANDNPDrm
    0U,	// ANDNPDrr
    0U,	// ANDNPSrm
    0U,	// ANDNPSrr
    0U,	// ANDPDrm
    0U,	// ANDPDrr
    0U,	// ANDPSrm
    0U,	// ANDPSrr
    0U,	// ARPL16mr
    0U,	// ARPL16rr
    0U,	// AVX2_SETALLONES
    0U,	// AVX512_512_SET0
    0U,	// AVX_SET0
    0U,	// BEXTR32rm
    0U,	// BEXTR32rr
    0U,	// BEXTR64rm
    0U,	// BEXTR64rr
    0U,	// BEXTRI32mi
    0U,	// BEXTRI32ri
    0U,	// BEXTRI64mi
    0U,	// BEXTRI64ri
    0U,	// BLCFILL32rm
    0U,	// BLCFILL32rr
    0U,	// BLCFILL64rm
    0U,	// BLCFILL64rr
    0U,	// BLCI32rm
    0U,	// BLCI32rr
    0U,	// BLCI64rm
    0U,	// BLCI64rr
    0U,	// BLCIC32rm
    0U,	// BLCIC32rr
    0U,	// BLCIC64rm
    0U,	// BLCIC64rr
    0U,	// BLCMSK32rm
    0U,	// BLCMSK32rr
    0U,	// BLCMSK64rm
    0U,	// BLCMSK64rr
    0U,	// BLCS32rm
    0U,	// BLCS32rr
    0U,	// BLCS64rm
    0U,	// BLCS64rr
    0U,	// BLENDPDrmi
    0U,	// BLENDPDrri
    0U,	// BLENDPSrmi
    0U,	// BLENDPSrri
    0U,	// BLENDVPDrm0
    0U,	// BLENDVPDrr0
    0U,	// BLENDVPSrm0
    0U,	// BLENDVPSrr0
    0U,	// BLSFILL32rm
    0U,	// BLSFILL32rr
    0U,	// BLSFILL64rm
    0U,	// BLSFILL64rr
    0U,	// BLSI32rm
    0U,	// BLSI32rr
    0U,	// BLSI64rm
    0U,	// BLSI64rr
    0U,	// BLSIC32rm
    0U,	// BLSIC32rr
    0U,	// BLSIC64rm
    0U,	// BLSIC64rr
    0U,	// BLSMSK32rm
    0U,	// BLSMSK32rr
    0U,	// BLSMSK64rm
    0U,	// BLSMSK64rr
    0U,	// BLSR32rm
    0U,	// BLSR32rr
    0U,	// BLSR64rm
    0U,	// BLSR64rr
    0U,	// BOUNDS16rm
    0U,	// BOUNDS32rm
    0U,	// BSF16rm
    0U,	// BSF16rr
    0U,	// BSF32rm
    0U,	// BSF32rr
    0U,	// BSF64rm
    0U,	// BSF64rr
    0U,	// BSR16rm
    0U,	// BSR16rr
    0U,	// BSR32rm
    0U,	// BSR32rr
    0U,	// BSR64rm
    0U,	// BSR64rr
    0U,	// BSWAP32r
    0U,	// BSWAP64r
    0U,	// BT16mi8
    0U,	// BT16mr
    0U,	// BT16ri8
    0U,	// BT16rr
    0U,	// BT32mi8
    0U,	// BT32mr
    0U,	// BT32ri8
    0U,	// BT32rr
    0U,	// BT64mi8
    0U,	// BT64mr
    0U,	// BT64ri8
    0U,	// BT64rr
    0U,	// BTC16mi8
    0U,	// BTC16mr
    0U,	// BTC16ri8
    0U,	// BTC16rr
    0U,	// BTC32mi8
    0U,	// BTC32mr
    0U,	// BTC32ri8
    0U,	// BTC32rr
    0U,	// BTC64mi8
    0U,	// BTC64mr
    0U,	// BTC64ri8
    0U,	// BTC64rr
    0U,	// BTR16mi8
    0U,	// BTR16mr
    0U,	// BTR16ri8
    0U,	// BTR16rr
    0U,	// BTR32mi8
    0U,	// BTR32mr
    0U,	// BTR32ri8
    0U,	// BTR32rr
    0U,	// BTR64mi8
    0U,	// BTR64mr
    0U,	// BTR64ri8
    0U,	// BTR64rr
    0U,	// BTS16mi8
    0U,	// BTS16mr
    0U,	// BTS16ri8
    0U,	// BTS16rr
    0U,	// BTS32mi8
    0U,	// BTS32mr
    0U,	// BTS32ri8
    0U,	// BTS32rr
    0U,	// BTS64mi8
    0U,	// BTS64mr
    0U,	// BTS64ri8
    0U,	// BTS64rr
    0U,	// BZHI32rm
    0U,	// BZHI32rr
    0U,	// BZHI64rm
    0U,	// BZHI64rr
    0U,	// CALL16m
    0U,	// CALL16r
    0U,	// CALL32m
    0U,	// CALL32r
    0U,	// CALL64m
    0U,	// CALL64pcrel32
    0U,	// CALL64r
    0U,	// CALLpcrel16
    0U,	// CALLpcrel32
    0U,	// CBW
    0U,	// CDQ
    0U,	// CDQE
    0U,	// CHS_F
    0U,	// CHS_Fp32
    0U,	// CHS_Fp64
    0U,	// CHS_Fp80
    0U,	// CLAC
    0U,	// CLC
    0U,	// CLD
    0U,	// CLFLUSH
    0U,	// CLGI
    0U,	// CLI
    0U,	// CLTS
    0U,	// CMC
    0U,	// CMOVA16rm
    0U,	// CMOVA16rr
    0U,	// CMOVA32rm
    0U,	// CMOVA32rr
    0U,	// CMOVA64rm
    0U,	// CMOVA64rr
    0U,	// CMOVAE16rm
    0U,	// CMOVAE16rr
    0U,	// CMOVAE32rm
    0U,	// CMOVAE32rr
    0U,	// CMOVAE64rm
    0U,	// CMOVAE64rr
    0U,	// CMOVB16rm
    0U,	// CMOVB16rr
    0U,	// CMOVB32rm
    0U,	// CMOVB32rr
    0U,	// CMOVB64rm
    0U,	// CMOVB64rr
    0U,	// CMOVBE16rm
    0U,	// CMOVBE16rr
    0U,	// CMOVBE32rm
    0U,	// CMOVBE32rr
    0U,	// CMOVBE64rm
    0U,	// CMOVBE64rr
    0U,	// CMOVBE_F
    0U,	// CMOVBE_Fp32
    0U,	// CMOVBE_Fp64
    0U,	// CMOVBE_Fp80
    0U,	// CMOVB_F
    0U,	// CMOVB_Fp32
    0U,	// CMOVB_Fp64
    0U,	// CMOVB_Fp80
    0U,	// CMOVE16rm
    0U,	// CMOVE16rr
    0U,	// CMOVE32rm
    0U,	// CMOVE32rr
    0U,	// CMOVE64rm
    0U,	// CMOVE64rr
    0U,	// CMOVE_F
    0U,	// CMOVE_Fp32
    0U,	// CMOVE_Fp64
    0U,	// CMOVE_Fp80
    0U,	// CMOVG16rm
    0U,	// CMOVG16rr
    0U,	// CMOVG32rm
    0U,	// CMOVG32rr
    0U,	// CMOVG64rm
    0U,	// CMOVG64rr
    0U,	// CMOVGE16rm
    0U,	// CMOVGE16rr
    0U,	// CMOVGE32rm
    0U,	// CMOVGE32rr
    0U,	// CMOVGE64rm
    0U,	// CMOVGE64rr
    0U,	// CMOVL16rm
    0U,	// CMOVL16rr
    0U,	// CMOVL32rm
    0U,	// CMOVL32rr
    0U,	// CMOVL64rm
    0U,	// CMOVL64rr
    0U,	// CMOVLE16rm
    0U,	// CMOVLE16rr
    0U,	// CMOVLE32rm
    0U,	// CMOVLE32rr
    0U,	// CMOVLE64rm
    0U,	// CMOVLE64rr
    0U,	// CMOVNBE_F
    0U,	// CMOVNBE_Fp32
    0U,	// CMOVNBE_Fp64
    0U,	// CMOVNBE_Fp80
    0U,	// CMOVNB_F
    0U,	// CMOVNB_Fp32
    0U,	// CMOVNB_Fp64
    0U,	// CMOVNB_Fp80
    0U,	// CMOVNE16rm
    0U,	// CMOVNE16rr
    0U,	// CMOVNE32rm
    0U,	// CMOVNE32rr
    0U,	// CMOVNE64rm
    0U,	// CMOVNE64rr
    0U,	// CMOVNE_F
    0U,	// CMOVNE_Fp32
    0U,	// CMOVNE_Fp64
    0U,	// CMOVNE_Fp80
    0U,	// CMOVNO16rm
    0U,	// CMOVNO16rr
    0U,	// CMOVNO32rm
    0U,	// CMOVNO32rr
    0U,	// CMOVNO64rm
    0U,	// CMOVNO64rr
    0U,	// CMOVNP16rm
    0U,	// CMOVNP16rr
    0U,	// CMOVNP32rm
    0U,	// CMOVNP32rr
    0U,	// CMOVNP64rm
    0U,	// CMOVNP64rr
    0U,	// CMOVNP_F
    0U,	// CMOVNP_Fp32
    0U,	// CMOVNP_Fp64
    0U,	// CMOVNP_Fp80
    0U,	// CMOVNS16rm
    0U,	// CMOVNS16rr
    0U,	// CMOVNS32rm
    0U,	// CMOVNS32rr
    0U,	// CMOVNS64rm
    0U,	// CMOVNS64rr
    0U,	// CMOVO16rm
    0U,	// CMOVO16rr
    0U,	// CMOVO32rm
    0U,	// CMOVO32rr
    0U,	// CMOVO64rm
    0U,	// CMOVO64rr
    0U,	// CMOVP16rm
    0U,	// CMOVP16rr
    0U,	// CMOVP32rm
    0U,	// CMOVP32rr
    0U,	// CMOVP64rm
    0U,	// CMOVP64rr
    0U,	// CMOVP_F
    0U,	// CMOVP_Fp32
    0U,	// CMOVP_Fp64
    0U,	// CMOVP_Fp80
    0U,	// CMOVS16rm
    0U,	// CMOVS16rr
    0U,	// CMOVS32rm
    0U,	// CMOVS32rr
    0U,	// CMOVS64rm
    0U,	// CMOVS64rr
    0U,	// CMOV_FR32
    0U,	// CMOV_FR64
    0U,	// CMOV_GR16
    0U,	// CMOV_GR32
    0U,	// CMOV_GR8
    0U,	// CMOV_RFP32
    0U,	// CMOV_RFP64
    0U,	// CMOV_RFP80
    0U,	// CMOV_V16F32
    0U,	// CMOV_V2F64
    0U,	// CMOV_V2I64
    0U,	// CMOV_V4F32
    0U,	// CMOV_V4F64
    0U,	// CMOV_V4I64
    0U,	// CMOV_V8F32
    0U,	// CMOV_V8F64
    0U,	// CMOV_V8I64
    0U,	// CMP16i16
    0U,	// CMP16mi
    0U,	// CMP16mi8
    0U,	// CMP16mr
    0U,	// CMP16ri
    0U,	// CMP16ri8
    0U,	// CMP16rm
    0U,	// CMP16rr
    0U,	// CMP16rr_REV
    0U,	// CMP32i32
    0U,	// CMP32mi
    0U,	// CMP32mi8
    0U,	// CMP32mr
    0U,	// CMP32ri
    0U,	// CMP32ri8
    0U,	// CMP32rm
    0U,	// CMP32rr
    0U,	// CMP32rr_REV
    0U,	// CMP64i32
    0U,	// CMP64mi32
    0U,	// CMP64mi8
    0U,	// CMP64mr
    0U,	// CMP64ri32
    0U,	// CMP64ri8
    0U,	// CMP64rm
    0U,	// CMP64rr
    0U,	// CMP64rr_REV
    0U,	// CMP8i8
    0U,	// CMP8mi
    0U,	// CMP8mr
    0U,	// CMP8ri
    0U,	// CMP8rm
    0U,	// CMP8rr
    0U,	// CMP8rr_REV
    8U,	// CMPPDrmi
    0U,	// CMPPDrmi_alt
    4U,	// CMPPDrri
    0U,	// CMPPDrri_alt
    8U,	// CMPPSrmi
    0U,	// CMPPSrmi_alt
    4U,	// CMPPSrri
    0U,	// CMPPSrri_alt
    0U,	// CMPSB
    8U,	// CMPSDrm
    0U,	// CMPSDrm_alt
    4U,	// CMPSDrr
    0U,	// CMPSDrr_alt
    0U,	// CMPSL
    0U,	// CMPSQ
    8U,	// CMPSSrm
    0U,	// CMPSSrm_alt
    4U,	// CMPSSrr
    0U,	// CMPSSrr_alt
    0U,	// CMPSW
    0U,	// CMPXCHG16B
    0U,	// CMPXCHG16rm
    0U,	// CMPXCHG16rr
    0U,	// CMPXCHG32rm
    0U,	// CMPXCHG32rr
    0U,	// CMPXCHG64rm
    0U,	// CMPXCHG64rr
    0U,	// CMPXCHG8B
    0U,	// CMPXCHG8rm
    0U,	// CMPXCHG8rr
    0U,	// COMISDrm
    0U,	// COMISDrr
    0U,	// COMISSrm
    0U,	// COMISSrr
    0U,	// COMP_FST0r
    0U,	// COM_FIPr
    0U,	// COM_FIr
    0U,	// COM_FST0r
    0U,	// COS_F
    0U,	// COS_Fp32
    0U,	// COS_Fp64
    0U,	// COS_Fp80
    0U,	// CPUID32
    0U,	// CPUID64
    0U,	// CQO
    0U,	// CRC32r32m16
    0U,	// CRC32r32m32
    0U,	// CRC32r32m8
    0U,	// CRC32r32r16
    0U,	// CRC32r32r32
    0U,	// CRC32r32r8
    0U,	// CRC32r64m64
    0U,	// CRC32r64m8
    0U,	// CRC32r64r64
    0U,	// CRC32r64r8
    0U,	// CVTDQ2PDrm
    0U,	// CVTDQ2PDrr
    0U,	// CVTDQ2PSrm
    0U,	// CVTDQ2PSrr
    0U,	// CVTPD2DQrm
    0U,	// CVTPD2DQrr
    0U,	// CVTPD2PSrm
    0U,	// CVTPD2PSrr
    0U,	// CVTPS2DQrm
    0U,	// CVTPS2DQrr
    0U,	// CVTPS2PDrm
    0U,	// CVTPS2PDrr
    0U,	// CVTSD2SI64rm
    0U,	// CVTSD2SI64rr
    0U,	// CVTSD2SIrm
    0U,	// CVTSD2SIrr
    0U,	// CVTSD2SSrm
    0U,	// CVTSD2SSrr
    0U,	// CVTSI2SD64rm
    0U,	// CVTSI2SD64rr
    0U,	// CVTSI2SDrm
    0U,	// CVTSI2SDrr
    0U,	// CVTSI2SS64rm
    0U,	// CVTSI2SS64rr
    0U,	// CVTSI2SSrm
    0U,	// CVTSI2SSrr
    0U,	// CVTSS2SDrm
    0U,	// CVTSS2SDrr
    0U,	// CVTSS2SI64rm
    0U,	// CVTSS2SI64rr
    0U,	// CVTSS2SIrm
    0U,	// CVTSS2SIrr
    0U,	// CVTTPD2DQrm
    0U,	// CVTTPD2DQrr
    0U,	// CVTTPS2DQrm
    0U,	// CVTTPS2DQrr
    0U,	// CVTTSD2SI64rm
    0U,	// CVTTSD2SI64rr
    0U,	// CVTTSD2SIrm
    0U,	// CVTTSD2SIrr
    0U,	// CVTTSS2SI64rm
    0U,	// CVTTSS2SI64rr
    0U,	// CVTTSS2SIrm
    0U,	// CVTTSS2SIrr
    0U,	// CWD
    0U,	// CWDE
    0U,	// DAA
    0U,	// DAS
    0U,	// DATA16_PREFIX
    0U,	// DEC16m
    0U,	// DEC16r
    0U,	// DEC32_16r
    0U,	// DEC32_32r
    0U,	// DEC32m
    0U,	// DEC32r
    0U,	// DEC64_16m
    0U,	// DEC64_16r
    0U,	// DEC64_32m
    0U,	// DEC64_32r
    0U,	// DEC64m
    0U,	// DEC64r
    0U,	// DEC8m
    0U,	// DEC8r
    0U,	// DIV16m
    0U,	// DIV16r
    0U,	// DIV32m
    0U,	// DIV32r
    0U,	// DIV64m
    0U,	// DIV64r
    0U,	// DIV8m
    0U,	// DIV8r
    0U,	// DIVPDrm
    0U,	// DIVPDrr
    0U,	// DIVPSrm
    0U,	// DIVPSrr
    0U,	// DIVR_F32m
    0U,	// DIVR_F64m
    0U,	// DIVR_FI16m
    0U,	// DIVR_FI32m
    0U,	// DIVR_FPrST0
    0U,	// DIVR_FST0r
    0U,	// DIVR_Fp32m
    0U,	// DIVR_Fp64m
    0U,	// DIVR_Fp64m32
    0U,	// DIVR_Fp80m32
    0U,	// DIVR_Fp80m64
    0U,	// DIVR_FpI16m32
    0U,	// DIVR_FpI16m64
    0U,	// DIVR_FpI16m80
    0U,	// DIVR_FpI32m32
    0U,	// DIVR_FpI32m64
    0U,	// DIVR_FpI32m80
    0U,	// DIVR_FrST0
    0U,	// DIVSDrm
    0U,	// DIVSDrm_Int
    0U,	// DIVSDrr
    0U,	// DIVSDrr_Int
    0U,	// DIVSSrm
    0U,	// DIVSSrm_Int
    0U,	// DIVSSrr
    0U,	// DIVSSrr_Int
    0U,	// DIV_F32m
    0U,	// DIV_F64m
    0U,	// DIV_FI16m
    0U,	// DIV_FI32m
    0U,	// DIV_FPrST0
    0U,	// DIV_FST0r
    0U,	// DIV_Fp32
    0U,	// DIV_Fp32m
    0U,	// DIV_Fp64
    0U,	// DIV_Fp64m
    0U,	// DIV_Fp64m32
    0U,	// DIV_Fp80
    0U,	// DIV_Fp80m32
    0U,	// DIV_Fp80m64
    0U,	// DIV_FpI16m32
    0U,	// DIV_FpI16m64
    0U,	// DIV_FpI16m80
    0U,	// DIV_FpI32m32
    0U,	// DIV_FpI32m64
    0U,	// DIV_FpI32m80
    0U,	// DIV_FrST0
    0U,	// DPPDrmi
    0U,	// DPPDrri
    0U,	// DPPSrmi
    0U,	// DPPSrri
    0U,	// EH_RETURN
    0U,	// EH_RETURN64
    0U,	// EH_SjLj_LongJmp32
    0U,	// EH_SjLj_LongJmp64
    0U,	// EH_SjLj_SetJmp32
    0U,	// EH_SjLj_SetJmp64
    0U,	// EH_SjLj_Setup
    0U,	// ENCLS
    0U,	// ENCLU
    0U,	// ENTER
    0U,	// EXTRACTPSmr
    0U,	// EXTRACTPSrr
    0U,	// EXTRQ
    0U,	// EXTRQI
    0U,	// F2XM1
    0U,	// FARCALL16i
    0U,	// FARCALL16m
    0U,	// FARCALL32i
    0U,	// FARCALL32m
    0U,	// FARCALL64
    0U,	// FARJMP16i
    0U,	// FARJMP16m
    0U,	// FARJMP32i
    0U,	// FARJMP32m
    0U,	// FARJMP64
    0U,	// FBLDm
    0U,	// FBSTPm
    0U,	// FCOM32m
    0U,	// FCOM64m
    0U,	// FCOMP32m
    0U,	// FCOMP64m
    0U,	// FCOMPP
    0U,	// FDECSTP
    0U,	// FEMMS
    0U,	// FFREE
    0U,	// FICOM16m
    0U,	// FICOM32m
    0U,	// FICOMP16m
    0U,	// FICOMP32m
    0U,	// FINCSTP
    0U,	// FLDCW16m
    0U,	// FLDENVm
    0U,	// FLDL2E
    0U,	// FLDL2T
    0U,	// FLDLG2
    0U,	// FLDLN2
    0U,	// FLDPI
    0U,	// FNCLEX
    0U,	// FNINIT
    0U,	// FNOP
    0U,	// FNSTCW16m
    0U,	// FNSTSW16r
    0U,	// FNSTSWm
    0U,	// FP32_TO_INT16_IN_MEM
    0U,	// FP32_TO_INT32_IN_MEM
    0U,	// FP32_TO_INT64_IN_MEM
    0U,	// FP64_TO_INT16_IN_MEM
    0U,	// FP64_TO_INT32_IN_MEM
    0U,	// FP64_TO_INT64_IN_MEM
    0U,	// FP80_TO_INT16_IN_MEM
    0U,	// FP80_TO_INT32_IN_MEM
    0U,	// FP80_TO_INT64_IN_MEM
    0U,	// FPATAN
    0U,	// FPREM
    0U,	// FPREM1
    0U,	// FPTAN
    0U,	// FRNDINT
    0U,	// FRSTORm
    0U,	// FSAVEm
    0U,	// FSCALE
    0U,	// FSETPM
    0U,	// FSINCOS
    0U,	// FSTENVm
    0U,	// FXAM
    0U,	// FXRSTOR
    0U,	// FXRSTOR64
    0U,	// FXSAVE
    0U,	// FXSAVE64
    0U,	// FXTRACT
    0U,	// FYL2X
    0U,	// FYL2XP1
    0U,	// FsANDNPDrm
    0U,	// FsANDNPDrr
    0U,	// FsANDNPSrm
    0U,	// FsANDNPSrr
    0U,	// FsANDPDrm
    0U,	// FsANDPDrr
    0U,	// FsANDPSrm
    0U,	// FsANDPSrr
    0U,	// FsFLD0SD
    0U,	// FsFLD0SS
    0U,	// FsMOVAPDrm
    0U,	// FsMOVAPSrm
    0U,	// FsORPDrm
    0U,	// FsORPDrr
    0U,	// FsORPSrm
    0U,	// FsORPSrr
    0U,	// FsVMOVAPDrm
    0U,	// FsVMOVAPSrm
    0U,	// FsXORPDrm
    0U,	// FsXORPDrr
    0U,	// FsXORPSrm
    0U,	// FsXORPSrr
    0U,	// GETSEC
    0U,	// HADDPDrm
    0U,	// HADDPDrr
    0U,	// HADDPSrm
    0U,	// HADDPSrr
    0U,	// HLT
    0U,	// HSUBPDrm
    0U,	// HSUBPDrr
    0U,	// HSUBPSrm
    0U,	// HSUBPSrr
    0U,	// IDIV16m
    0U,	// IDIV16r
    0U,	// IDIV32m
    0U,	// IDIV32r
    0U,	// IDIV64m
    0U,	// IDIV64r
    0U,	// IDIV8m
    0U,	// IDIV8r
    0U,	// ILD_F16m
    0U,	// ILD_F32m
    0U,	// ILD_F64m
    0U,	// ILD_Fp16m32
    0U,	// ILD_Fp16m64
    0U,	// ILD_Fp16m80
    0U,	// ILD_Fp32m32
    0U,	// ILD_Fp32m64
    0U,	// ILD_Fp32m80
    0U,	// ILD_Fp64m32
    0U,	// ILD_Fp64m64
    0U,	// ILD_Fp64m80
    0U,	// IMUL16m
    0U,	// IMUL16r
    0U,	// IMUL16rm
    0U,	// IMUL16rmi
    0U,	// IMUL16rmi8
    0U,	// IMUL16rr
    0U,	// IMUL16rri
    0U,	// IMUL16rri8
    0U,	// IMUL32m
    0U,	// IMUL32r
    0U,	// IMUL32rm
    0U,	// IMUL32rmi
    0U,	// IMUL32rmi8
    0U,	// IMUL32rr
    0U,	// IMUL32rri
    0U,	// IMUL32rri8
    0U,	// IMUL64m
    0U,	// IMUL64r
    0U,	// IMUL64rm
    0U,	// IMUL64rmi32
    0U,	// IMUL64rmi8
    0U,	// IMUL64rr
    0U,	// IMUL64rri32
    0U,	// IMUL64rri8
    0U,	// IMUL8m
    0U,	// IMUL8r
    0U,	// IN16ri
    0U,	// IN16rr
    0U,	// IN32ri
    0U,	// IN32rr
    0U,	// IN8ri
    0U,	// IN8rr
    0U,	// INC16m
    0U,	// INC16r
    0U,	// INC32_16r
    0U,	// INC32_32r
    0U,	// INC32m
    0U,	// INC32r
    0U,	// INC64_16m
    0U,	// INC64_16r
    0U,	// INC64_32m
    0U,	// INC64_32r
    0U,	// INC64m
    0U,	// INC64r
    0U,	// INC8m
    0U,	// INC8r
    0U,	// INSB
    0U,	// INSERTPSrm
    0U,	// INSERTPSrr
    0U,	// INSERTQ
    0U,	// INSERTQI
    0U,	// INSL
    0U,	// INSW
    0U,	// INT
    0U,	// INT1
    0U,	// INT3
    0U,	// INTO
    0U,	// INVD
    0U,	// INVEPT32
    0U,	// INVEPT64
    0U,	// INVLPG
    0U,	// INVLPGA32
    0U,	// INVLPGA64
    0U,	// INVPCID32
    0U,	// INVPCID64
    0U,	// INVVPID32
    0U,	// INVVPID64
    0U,	// IRET16
    0U,	// IRET32
    0U,	// IRET64
    0U,	// ISTT_FP16m
    0U,	// ISTT_FP32m
    0U,	// ISTT_FP64m
    0U,	// ISTT_Fp16m32
    0U,	// ISTT_Fp16m64
    0U,	// ISTT_Fp16m80
    0U,	// ISTT_Fp32m32
    0U,	// ISTT_Fp32m64
    0U,	// ISTT_Fp32m80
    0U,	// ISTT_Fp64m32
    0U,	// ISTT_Fp64m64
    0U,	// ISTT_Fp64m80
    0U,	// IST_F16m
    0U,	// IST_F32m
    0U,	// IST_FP16m
    0U,	// IST_FP32m
    0U,	// IST_FP64m
    0U,	// IST_Fp16m32
    0U,	// IST_Fp16m64
    0U,	// IST_Fp16m80
    0U,	// IST_Fp32m32
    0U,	// IST_Fp32m64
    0U,	// IST_Fp32m80
    0U,	// IST_Fp64m32
    0U,	// IST_Fp64m64
    0U,	// IST_Fp64m80
    8U,	// Int_CMPSDrm
    4U,	// Int_CMPSDrr
    8U,	// Int_CMPSSrm
    4U,	// Int_CMPSSrr
    0U,	// Int_COMISDrm
    0U,	// Int_COMISDrr
    0U,	// Int_COMISSrm
    0U,	// Int_COMISSrr
    0U,	// Int_CVTSD2SSrm
    0U,	// Int_CVTSD2SSrr
    0U,	// Int_CVTSI2SD64rm
    0U,	// Int_CVTSI2SD64rr
    0U,	// Int_CVTSI2SDrm
    0U,	// Int_CVTSI2SDrr
    0U,	// Int_CVTSI2SS64rm
    0U,	// Int_CVTSI2SS64rr
    0U,	// Int_CVTSI2SSrm
    0U,	// Int_CVTSI2SSrr
    0U,	// Int_CVTSS2SDrm
    0U,	// Int_CVTSS2SDrr
    0U,	// Int_CVTTSD2SI64rm
    0U,	// Int_CVTTSD2SI64rr
    0U,	// Int_CVTTSD2SIrm
    0U,	// Int_CVTTSD2SIrr
    0U,	// Int_CVTTSS2SI64rm
    0U,	// Int_CVTTSS2SI64rr
    0U,	// Int_CVTTSS2SIrm
    0U,	// Int_CVTTSS2SIrr
    0U,	// Int_MemBarrier
    0U,	// Int_UCOMISDrm
    0U,	// Int_UCOMISDrr
    0U,	// Int_UCOMISSrm
    0U,	// Int_UCOMISSrr
    76U,	// Int_VCMPSDrm
    1156U,	// Int_VCMPSDrr
    76U,	// Int_VCMPSSrm
    1156U,	// Int_VCMPSSrr
    0U,	// Int_VCOMISDZrm
    0U,	// Int_VCOMISDZrr
    0U,	// Int_VCOMISDrm
    0U,	// Int_VCOMISDrr
    0U,	// Int_VCOMISSZrm
    0U,	// Int_VCOMISSZrr
    0U,	// Int_VCOMISSrm
    0U,	// Int_VCOMISSrr
    4U,	// Int_VCVTSD2SSrm
    0U,	// Int_VCVTSD2SSrr
    4U,	// Int_VCVTSI2SD64Zrm
    0U,	// Int_VCVTSI2SD64Zrr
    4U,	// Int_VCVTSI2SD64rm
    0U,	// Int_VCVTSI2SD64rr
    4U,	// Int_VCVTSI2SDZrm
    0U,	// Int_VCVTSI2SDZrr
    4U,	// Int_VCVTSI2SDrm
    0U,	// Int_VCVTSI2SDrr
    4U,	// Int_VCVTSI2SS64Zrm
    0U,	// Int_VCVTSI2SS64Zrr
    4U,	// Int_VCVTSI2SS64rm
    0U,	// Int_VCVTSI2SS64rr
    4U,	// Int_VCVTSI2SSZrm
    0U,	// Int_VCVTSI2SSZrr
    4U,	// Int_VCVTSI2SSrm
    0U,	// Int_VCVTSI2SSrr
    4U,	// Int_VCVTSS2SDrm
    0U,	// Int_VCVTSS2SDrr
    0U,	// Int_VCVTTSD2SI64Zrm
    0U,	// Int_VCVTTSD2SI64Zrr
    0U,	// Int_VCVTTSD2SI64rm
    0U,	// Int_VCVTTSD2SI64rr
    0U,	// Int_VCVTTSD2SIZrm
    0U,	// Int_VCVTTSD2SIZrr
    0U,	// Int_VCVTTSD2SIrm
    0U,	// Int_VCVTTSD2SIrr
    0U,	// Int_VCVTTSD2USI64Zrm
    0U,	// Int_VCVTTSD2USI64Zrr
    0U,	// Int_VCVTTSD2USIZrm
    0U,	// Int_VCVTTSD2USIZrr
    0U,	// Int_VCVTTSS2SI64Zrm
    0U,	// Int_VCVTTSS2SI64Zrr
    0U,	// Int_VCVTTSS2SI64rm
    0U,	// Int_VCVTTSS2SI64rr
    0U,	// Int_VCVTTSS2SIZrm
    0U,	// Int_VCVTTSS2SIZrr
    0U,	// Int_VCVTTSS2SIrm
    0U,	// Int_VCVTTSS2SIrr
    0U,	// Int_VCVTTSS2USI64Zrm
    0U,	// Int_VCVTTSS2USI64Zrr
    0U,	// Int_VCVTTSS2USIZrm
    0U,	// Int_VCVTTSS2USIZrr
    4U,	// Int_VCVTUSI2SD64Zrm
    0U,	// Int_VCVTUSI2SD64Zrr
    4U,	// Int_VCVTUSI2SDZrm
    0U,	// Int_VCVTUSI2SDZrr
    4U,	// Int_VCVTUSI2SS64Zrm
    0U,	// Int_VCVTUSI2SS64Zrr
    4U,	// Int_VCVTUSI2SSZrm
    0U,	// Int_VCVTUSI2SSZrr
    0U,	// Int_VUCOMISDZrm
    0U,	// Int_VUCOMISDZrr
    0U,	// Int_VUCOMISDrm
    0U,	// Int_VUCOMISDrr
    0U,	// Int_VUCOMISSZrm
    0U,	// Int_VUCOMISSZrr
    0U,	// Int_VUCOMISSrm
    0U,	// Int_VUCOMISSrr
    0U,	// JAE_1
    0U,	// JAE_2
    0U,	// JAE_4
    0U,	// JA_1
    0U,	// JA_2
    0U,	// JA_4
    0U,	// JBE_1
    0U,	// JBE_2
    0U,	// JBE_4
    0U,	// JB_1
    0U,	// JB_2
    0U,	// JB_4
    0U,	// JCXZ
    0U,	// JECXZ_32
    0U,	// JECXZ_64
    0U,	// JE_1
    0U,	// JE_2
    0U,	// JE_4
    0U,	// JGE_1
    0U,	// JGE_2
    0U,	// JGE_4
    0U,	// JG_1
    0U,	// JG_2
    0U,	// JG_4
    0U,	// JLE_1
    0U,	// JLE_2
    0U,	// JLE_4
    0U,	// JL_1
    0U,	// JL_2
    0U,	// JL_4
    0U,	// JMP16m
    0U,	// JMP16r
    0U,	// JMP32m
    0U,	// JMP32r
    0U,	// JMP64m
    0U,	// JMP64r
    0U,	// JMP_1
    0U,	// JMP_2
    0U,	// JMP_4
    0U,	// JNE_1
    0U,	// JNE_2
    0U,	// JNE_4
    0U,	// JNO_1
    0U,	// JNO_2
    0U,	// JNO_4
    0U,	// JNP_1
    0U,	// JNP_2
    0U,	// JNP_4
    0U,	// JNS_1
    0U,	// JNS_2
    0U,	// JNS_4
    0U,	// JO_1
    0U,	// JO_2
    0U,	// JO_4
    0U,	// JP_1
    0U,	// JP_2
    0U,	// JP_4
    0U,	// JRCXZ
    0U,	// JS_1
    0U,	// JS_2
    0U,	// JS_4
    0U,	// KANDBrr
    0U,	// KANDDrr
    0U,	// KANDNBrr
    0U,	// KANDNDrr
    0U,	// KANDNQrr
    0U,	// KANDNWrr
    0U,	// KANDQrr
    0U,	// KANDWrr
    0U,	// KMOVBkk
    0U,	// KMOVBkm
    0U,	// KMOVBkr
    0U,	// KMOVBmk
    0U,	// KMOVBrk
    0U,	// KMOVDkk
    0U,	// KMOVDkm
    0U,	// KMOVDkr
    0U,	// KMOVDmk
    0U,	// KMOVDrk
    0U,	// KMOVQkk
    0U,	// KMOVQkm
    0U,	// KMOVQkr
    0U,	// KMOVQmk
    0U,	// KMOVQrk
    0U,	// KMOVWkk
    0U,	// KMOVWkm
    0U,	// KMOVWkr
    0U,	// KMOVWmk
    0U,	// KMOVWrk
    0U,	// KNOTBrr
    0U,	// KNOTDrr
    0U,	// KNOTQrr
    0U,	// KNOTWrr
    0U,	// KORBrr
    0U,	// KORDrr
    0U,	// KORQrr
    0U,	// KORTESTWrr
    0U,	// KORWrr
    0U,	// KSET0B
    0U,	// KSET0W
    0U,	// KSET1B
    0U,	// KSET1W
    0U,	// KSHIFTLWri
    0U,	// KSHIFTRWri
    0U,	// KUNPCKBWrr
    0U,	// KXNORBrr
    0U,	// KXNORDrr
    0U,	// KXNORQrr
    0U,	// KXNORWrr
    0U,	// KXORBrr
    0U,	// KXORDrr
    0U,	// KXORQrr
    0U,	// KXORWrr
    0U,	// LAHF
    0U,	// LAR16rm
    0U,	// LAR16rr
    0U,	// LAR32rm
    0U,	// LAR32rr
    0U,	// LAR64rm
    0U,	// LAR64rr
    0U,	// LCMPXCHG16
    0U,	// LCMPXCHG16B
    0U,	// LCMPXCHG32
    0U,	// LCMPXCHG64
    0U,	// LCMPXCHG8
    0U,	// LCMPXCHG8B
    0U,	// LDDQUrm
    0U,	// LDMXCSR
    0U,	// LDS16rm
    0U,	// LDS32rm
    0U,	// LD_F0
    0U,	// LD_F1
    0U,	// LD_F32m
    0U,	// LD_F64m
    0U,	// LD_F80m
    0U,	// LD_Fp032
    0U,	// LD_Fp064
    0U,	// LD_Fp080
    0U,	// LD_Fp132
    0U,	// LD_Fp164
    0U,	// LD_Fp180
    0U,	// LD_Fp32m
    0U,	// LD_Fp32m64
    0U,	// LD_Fp32m80
    0U,	// LD_Fp64m
    0U,	// LD_Fp64m80
    0U,	// LD_Fp80m
    0U,	// LD_Frr
    0U,	// LEA16r
    0U,	// LEA32r
    0U,	// LEA64_32r
    0U,	// LEA64r
    0U,	// LEAVE
    0U,	// LEAVE64
    0U,	// LES16rm
    0U,	// LES32rm
    0U,	// LFENCE
    0U,	// LFS16rm
    0U,	// LFS32rm
    0U,	// LFS64rm
    0U,	// LGDT16m
    0U,	// LGDT32m
    0U,	// LGDT64m
    0U,	// LGS16rm
    0U,	// LGS32rm
    0U,	// LGS64rm
    0U,	// LIDT16m
    0U,	// LIDT32m
    0U,	// LIDT64m
    0U,	// LLDT16m
    0U,	// LLDT16r
    0U,	// LMSW16m
    0U,	// LMSW16r
    0U,	// LOCK_ADD16mi
    0U,	// LOCK_ADD16mi8
    0U,	// LOCK_ADD16mr
    0U,	// LOCK_ADD32mi
    0U,	// LOCK_ADD32mi8
    0U,	// LOCK_ADD32mr
    0U,	// LOCK_ADD64mi32
    0U,	// LOCK_ADD64mi8
    0U,	// LOCK_ADD64mr
    0U,	// LOCK_ADD8mi
    0U,	// LOCK_ADD8mr
    0U,	// LOCK_AND16mi
    0U,	// LOCK_AND16mi8
    0U,	// LOCK_AND16mr
    0U,	// LOCK_AND32mi
    0U,	// LOCK_AND32mi8
    0U,	// LOCK_AND32mr
    0U,	// LOCK_AND64mi32
    0U,	// LOCK_AND64mi8
    0U,	// LOCK_AND64mr
    0U,	// LOCK_AND8mi
    0U,	// LOCK_AND8mr
    0U,	// LOCK_DEC16m
    0U,	// LOCK_DEC32m
    0U,	// LOCK_DEC64m
    0U,	// LOCK_DEC8m
    0U,	// LOCK_INC16m
    0U,	// LOCK_INC32m
    0U,	// LOCK_INC64m
    0U,	// LOCK_INC8m
    0U,	// LOCK_OR16mi
    0U,	// LOCK_OR16mi8
    0U,	// LOCK_OR16mr
    0U,	// LOCK_OR32mi
    0U,	// LOCK_OR32mi8
    0U,	// LOCK_OR32mr
    0U,	// LOCK_OR64mi32
    0U,	// LOCK_OR64mi8
    0U,	// LOCK_OR64mr
    0U,	// LOCK_OR8mi
    0U,	// LOCK_OR8mr
    0U,	// LOCK_PREFIX
    0U,	// LOCK_SUB16mi
    0U,	// LOCK_SUB16mi8
    0U,	// LOCK_SUB16mr
    0U,	// LOCK_SUB32mi
    0U,	// LOCK_SUB32mi8
    0U,	// LOCK_SUB32mr
    0U,	// LOCK_SUB64mi32
    0U,	// LOCK_SUB64mi8
    0U,	// LOCK_SUB64mr
    0U,	// LOCK_SUB8mi
    0U,	// LOCK_SUB8mr
    0U,	// LOCK_XOR16mi
    0U,	// LOCK_XOR16mi8
    0U,	// LOCK_XOR16mr
    0U,	// LOCK_XOR32mi
    0U,	// LOCK_XOR32mi8
    0U,	// LOCK_XOR32mr
    0U,	// LOCK_XOR64mi32
    0U,	// LOCK_XOR64mi8
    0U,	// LOCK_XOR64mr
    0U,	// LOCK_XOR8mi
    0U,	// LOCK_XOR8mr
    0U,	// LODSB
    0U,	// LODSL
    0U,	// LODSQ
    0U,	// LODSW
    0U,	// LOOP
    0U,	// LOOPE
    0U,	// LOOPNE
    0U,	// LRETIL
    0U,	// LRETIQ
    0U,	// LRETIW
    0U,	// LRETL
    0U,	// LRETQ
    0U,	// LRETW
    0U,	// LSL16rm
    0U,	// LSL16rr
    0U,	// LSL32rm
    0U,	// LSL32rr
    0U,	// LSL64rm
    0U,	// LSL64rr
    0U,	// LSS16rm
    0U,	// LSS32rm
    0U,	// LSS64rm
    0U,	// LTRm
    0U,	// LTRr
    0U,	// LXADD16
    0U,	// LXADD32
    0U,	// LXADD64
    1U,	// LXADD8
    0U,	// LZCNT16rm
    0U,	// LZCNT16rr
    0U,	// LZCNT32rm
    0U,	// LZCNT32rr
    0U,	// LZCNT64rm
    0U,	// LZCNT64rr
    0U,	// MASKMOVDQU
    0U,	// MASKMOVDQU64
    0U,	// MAXCPDrm
    0U,	// MAXCPDrr
    0U,	// MAXCPSrm
    0U,	// MAXCPSrr
    0U,	// MAXCSDrm
    0U,	// MAXCSDrr
    0U,	// MAXCSSrm
    0U,	// MAXCSSrr
    0U,	// MAXPDrm
    0U,	// MAXPDrr
    0U,	// MAXPSrm
    0U,	// MAXPSrr
    0U,	// MAXSDrm
    0U,	// MAXSDrm_Int
    0U,	// MAXSDrr
    0U,	// MAXSDrr_Int
    0U,	// MAXSSrm
    0U,	// MAXSSrm_Int
    0U,	// MAXSSrr
    0U,	// MAXSSrr_Int
    0U,	// MFENCE
    0U,	// MINCPDrm
    0U,	// MINCPDrr
    0U,	// MINCPSrm
    0U,	// MINCPSrr
    0U,	// MINCSDrm
    0U,	// MINCSDrr
    0U,	// MINCSSrm
    0U,	// MINCSSrr
    0U,	// MINPDrm
    0U,	// MINPDrr
    0U,	// MINPSrm
    0U,	// MINPSrr
    0U,	// MINSDrm
    0U,	// MINSDrm_Int
    0U,	// MINSDrr
    0U,	// MINSDrr_Int
    0U,	// MINSSrm
    0U,	// MINSSrm_Int
    0U,	// MINSSrr
    0U,	// MINSSrr_Int
    0U,	// MMX_CVTPD2PIirm
    0U,	// MMX_CVTPD2PIirr
    0U,	// MMX_CVTPI2PDirm
    0U,	// MMX_CVTPI2PDirr
    0U,	// MMX_CVTPI2PSirm
    0U,	// MMX_CVTPI2PSirr
    0U,	// MMX_CVTPS2PIirm
    0U,	// MMX_CVTPS2PIirr
    0U,	// MMX_CVTTPD2PIirm
    0U,	// MMX_CVTTPD2PIirr
    0U,	// MMX_CVTTPS2PIirm
    0U,	// MMX_CVTTPS2PIirr
    0U,	// MMX_EMMS
    0U,	// MMX_MASKMOVQ
    0U,	// MMX_MASKMOVQ64
    0U,	// MMX_MOVD64from64rr
    0U,	// MMX_MOVD64grr
    0U,	// MMX_MOVD64mr
    0U,	// MMX_MOVD64rm
    0U,	// MMX_MOVD64rr
    0U,	// MMX_MOVD64to64rr
    0U,	// MMX_MOVDQ2Qrr
    0U,	// MMX_MOVFR642Qrr
    0U,	// MMX_MOVNTQmr
    0U,	// MMX_MOVQ2DQrr
    0U,	// MMX_MOVQ2FR64rr
    0U,	// MMX_MOVQ64mr
    0U,	// MMX_MOVQ64rm
    0U,	// MMX_MOVQ64rr
    0U,	// MMX_MOVQ64rr_REV
    0U,	// MMX_PABSBrm64
    0U,	// MMX_PABSBrr64
    0U,	// MMX_PABSDrm64
    0U,	// MMX_PABSDrr64
    0U,	// MMX_PABSWrm64
    0U,	// MMX_PABSWrr64
    0U,	// MMX_PACKSSDWirm
    0U,	// MMX_PACKSSDWirr
    0U,	// MMX_PACKSSWBirm
    0U,	// MMX_PACKSSWBirr
    0U,	// MMX_PACKUSWBirm
    0U,	// MMX_PACKUSWBirr
    0U,	// MMX_PADDBirm
    0U,	// MMX_PADDBirr
    0U,	// MMX_PADDDirm
    0U,	// MMX_PADDDirr
    0U,	// MMX_PADDQirm
    0U,	// MMX_PADDQirr
    0U,	// MMX_PADDSBirm
    0U,	// MMX_PADDSBirr
    0U,	// MMX_PADDSWirm
    0U,	// MMX_PADDSWirr
    0U,	// MMX_PADDUSBirm
    0U,	// MMX_PADDUSBirr
    0U,	// MMX_PADDUSWirm
    0U,	// MMX_PADDUSWirr
    0U,	// MMX_PADDWirm
    0U,	// MMX_PADDWirr
    0U,	// MMX_PALIGNR64irm
    0U,	// MMX_PALIGNR64irr
    0U,	// MMX_PANDNirm
    0U,	// MMX_PANDNirr
    0U,	// MMX_PANDirm
    0U,	// MMX_PANDirr
    0U,	// MMX_PAVGBirm
    0U,	// MMX_PAVGBirr
    0U,	// MMX_PAVGWirm
    0U,	// MMX_PAVGWirr
    0U,	// MMX_PCMPEQBirm
    0U,	// MMX_PCMPEQBirr
    0U,	// MMX_PCMPEQDirm
    0U,	// MMX_PCMPEQDirr
    0U,	// MMX_PCMPEQWirm
    0U,	// MMX_PCMPEQWirr
    0U,	// MMX_PCMPGTBirm
    0U,	// MMX_PCMPGTBirr
    0U,	// MMX_PCMPGTDirm
    0U,	// MMX_PCMPGTDirr
    0U,	// MMX_PCMPGTWirm
    0U,	// MMX_PCMPGTWirr
    0U,	// MMX_PEXTRWirri
    0U,	// MMX_PHADDSWrm64
    0U,	// MMX_PHADDSWrr64
    0U,	// MMX_PHADDWrm64
    0U,	// MMX_PHADDWrr64
    0U,	// MMX_PHADDrm64
    0U,	// MMX_PHADDrr64
    0U,	// MMX_PHSUBDrm64
    0U,	// MMX_PHSUBDrr64
    0U,	// MMX_PHSUBSWrm64
    0U,	// MMX_PHSUBSWrr64
    0U,	// MMX_PHSUBWrm64
    0U,	// MMX_PHSUBWrr64
    0U,	// MMX_PINSRWirmi
    0U,	// MMX_PINSRWirri
    0U,	// MMX_PMADDUBSWrm64
    0U,	// MMX_PMADDUBSWrr64
    0U,	// MMX_PMADDWDirm
    0U,	// MMX_PMADDWDirr
    0U,	// MMX_PMAXSWirm
    0U,	// MMX_PMAXSWirr
    0U,	// MMX_PMAXUBirm
    0U,	// MMX_PMAXUBirr
    0U,	// MMX_PMINSWirm
    0U,	// MMX_PMINSWirr
    0U,	// MMX_PMINUBirm
    0U,	// MMX_PMINUBirr
    0U,	// MMX_PMOVMSKBrr
    0U,	// MMX_PMULHRSWrm64
    0U,	// MMX_PMULHRSWrr64
    0U,	// MMX_PMULHUWirm
    0U,	// MMX_PMULHUWirr
    0U,	// MMX_PMULHWirm
    0U,	// MMX_PMULHWirr
    0U,	// MMX_PMULLWirm
    0U,	// MMX_PMULLWirr
    0U,	// MMX_PMULUDQirm
    0U,	// MMX_PMULUDQirr
    0U,	// MMX_PORirm
    0U,	// MMX_PORirr
    0U,	// MMX_PSADBWirm
    0U,	// MMX_PSADBWirr
    0U,	// MMX_PSHUFBrm64
    0U,	// MMX_PSHUFBrr64
    0U,	// MMX_PSHUFWmi
    0U,	// MMX_PSHUFWri
    0U,	// MMX_PSIGNBrm64
    0U,	// MMX_PSIGNBrr64
    0U,	// MMX_PSIGNDrm64
    0U,	// MMX_PSIGNDrr64
    0U,	// MMX_PSIGNWrm64
    0U,	// MMX_PSIGNWrr64
    0U,	// MMX_PSLLDri
    0U,	// MMX_PSLLDrm
    0U,	// MMX_PSLLDrr
    0U,	// MMX_PSLLQri
    0U,	// MMX_PSLLQrm
    0U,	// MMX_PSLLQrr
    0U,	// MMX_PSLLWri
    0U,	// MMX_PSLLWrm
    0U,	// MMX_PSLLWrr
    0U,	// MMX_PSRADri
    0U,	// MMX_PSRADrm
    0U,	// MMX_PSRADrr
    0U,	// MMX_PSRAWri
    0U,	// MMX_PSRAWrm
    0U,	// MMX_PSRAWrr
    0U,	// MMX_PSRLDri
    0U,	// MMX_PSRLDrm
    0U,	// MMX_PSRLDrr
    0U,	// MMX_PSRLQri
    0U,	// MMX_PSRLQrm
    0U,	// MMX_PSRLQrr
    0U,	// MMX_PSRLWri
    0U,	// MMX_PSRLWrm
    0U,	// MMX_PSRLWrr
    0U,	// MMX_PSUBBirm
    0U,	// MMX_PSUBBirr
    0U,	// MMX_PSUBDirm
    0U,	// MMX_PSUBDirr
    0U,	// MMX_PSUBQirm
    0U,	// MMX_PSUBQirr
    0U,	// MMX_PSUBSBirm
    0U,	// MMX_PSUBSBirr
    0U,	// MMX_PSUBSWirm
    0U,	// MMX_PSUBSWirr
    0U,	// MMX_PSUBUSBirm
    0U,	// MMX_PSUBUSBirr
    0U,	// MMX_PSUBUSWirm
    0U,	// MMX_PSUBUSWirr
    0U,	// MMX_PSUBWirm
    0U,	// MMX_PSUBWirr
    0U,	// MMX_PUNPCKHBWirm
    0U,	// MMX_PUNPCKHBWirr
    0U,	// MMX_PUNPCKHDQirm
    0U,	// MMX_PUNPCKHDQirr
    0U,	// MMX_PUNPCKHWDirm
    0U,	// MMX_PUNPCKHWDirr
    0U,	// MMX_PUNPCKLBWirm
    0U,	// MMX_PUNPCKLBWirr
    0U,	// MMX_PUNPCKLDQirm
    0U,	// MMX_PUNPCKLDQirr
    0U,	// MMX_PUNPCKLWDirm
    0U,	// MMX_PUNPCKLWDirr
    0U,	// MMX_PXORirm
    0U,	// MMX_PXORirr
    0U,	// MONITOR
    0U,	// MONITORrrr
    0U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    0U,	// MOV16ao16
    0U,	// MOV16ao16_16
    0U,	// MOV16mi
    0U,	// MOV16mr
    0U,	// MOV16ms
    0U,	// MOV16o16a
    0U,	// MOV16o16a_16
    0U,	// MOV16ri
    0U,	// MOV16ri_alt
    0U,	// MOV16rm
    0U,	// MOV16rr
    0U,	// MOV16rr_REV
    0U,	// MOV16rs
    0U,	// MOV16sm
    0U,	// MOV16sr
    0U,	// MOV32ao32
    0U,	// MOV32ao32_16
    0U,	// MOV32cr
    0U,	// MOV32dr
    0U,	// MOV32mi
    0U,	// MOV32mr
    0U,	// MOV32ms
    0U,	// MOV32o32a
    0U,	// MOV32o32a_16
    0U,	// MOV32r0
    0U,	// MOV32rc
    0U,	// MOV32rd
    0U,	// MOV32ri
    0U,	// MOV32ri64
    0U,	// MOV32ri_alt
    0U,	// MOV32rm
    0U,	// MOV32rr
    0U,	// MOV32rr_REV
    0U,	// MOV32rs
    0U,	// MOV32sm
    0U,	// MOV32sr
    0U,	// MOV64ao16
    0U,	// MOV64ao32
    0U,	// MOV64ao64
    0U,	// MOV64ao8
    0U,	// MOV64cr
    0U,	// MOV64dr
    0U,	// MOV64mi32
    0U,	// MOV64mr
    0U,	// MOV64ms
    0U,	// MOV64o16a
    0U,	// MOV64o32a
    0U,	// MOV64o64a
    0U,	// MOV64o8a
    0U,	// MOV64rc
    0U,	// MOV64rd
    0U,	// MOV64ri
    0U,	// MOV64ri32
    0U,	// MOV64rm
    0U,	// MOV64rr
    0U,	// MOV64rr_REV
    0U,	// MOV64rs
    0U,	// MOV64sm
    0U,	// MOV64sr
    0U,	// MOV64toPQIrr
    0U,	// MOV64toSDrm
    0U,	// MOV64toSDrr
    0U,	// MOV8ao8
    0U,	// MOV8ao8_16
    0U,	// MOV8mi
    0U,	// MOV8mr
    1U,	// MOV8mr_NOREX
    0U,	// MOV8o8a
    0U,	// MOV8o8a_16
    0U,	// MOV8ri
    0U,	// MOV8ri_alt
    0U,	// MOV8rm
    0U,	// MOV8rm_NOREX
    0U,	// MOV8rr
    16U,	// MOV8rr_NOREX
    0U,	// MOV8rr_REV
    0U,	// MOVAPDmr
    0U,	// MOVAPDrm
    0U,	// MOVAPDrr
    0U,	// MOVAPDrr_REV
    0U,	// MOVAPSmr
    0U,	// MOVAPSrm
    0U,	// MOVAPSrr
    0U,	// MOVAPSrr_REV
    0U,	// MOVBE16mr
    0U,	// MOVBE16rm
    0U,	// MOVBE32mr
    0U,	// MOVBE32rm
    0U,	// MOVBE64mr
    0U,	// MOVBE64rm
    0U,	// MOVDDUPrm
    0U,	// MOVDDUPrr
    0U,	// MOVDI2PDIrm
    0U,	// MOVDI2PDIrr
    0U,	// MOVDI2SSrm
    0U,	// MOVDI2SSrr
    0U,	// MOVDQAmr
    0U,	// MOVDQArm
    0U,	// MOVDQArr
    0U,	// MOVDQArr_REV
    0U,	// MOVDQUmr
    0U,	// MOVDQUrm
    0U,	// MOVDQUrr
    0U,	// MOVDQUrr_REV
    0U,	// MOVHLPSrr
    0U,	// MOVHPDmr
    0U,	// MOVHPDrm
    0U,	// MOVHPSmr
    0U,	// MOVHPSrm
    0U,	// MOVLHPSrr
    0U,	// MOVLPDmr
    0U,	// MOVLPDrm
    0U,	// MOVLPSmr
    0U,	// MOVLPSrm
    0U,	// MOVMSKPDrr
    0U,	// MOVMSKPSrr
    0U,	// MOVNTDQArm
    0U,	// MOVNTDQmr
    0U,	// MOVNTI_64mr
    0U,	// MOVNTImr
    0U,	// MOVNTPDmr
    0U,	// MOVNTPSmr
    0U,	// MOVNTSD
    0U,	// MOVNTSS
    0U,	// MOVPC32r
    0U,	// MOVPDI2DImr
    0U,	// MOVPDI2DIrr
    0U,	// MOVPQI2QImr
    0U,	// MOVPQI2QIrr
    0U,	// MOVPQIto64rr
    0U,	// MOVQI2PQIrm
    0U,	// MOVSB
    0U,	// MOVSDmr
    0U,	// MOVSDrm
    0U,	// MOVSDrr
    0U,	// MOVSDrr_REV
    0U,	// MOVSDto64mr
    0U,	// MOVSDto64rr
    0U,	// MOVSHDUPrm
    0U,	// MOVSHDUPrr
    0U,	// MOVSL
    0U,	// MOVSLDUPrm
    0U,	// MOVSLDUPrr
    0U,	// MOVSQ
    0U,	// MOVSS2DImr
    0U,	// MOVSS2DIrr
    0U,	// MOVSSmr
    0U,	// MOVSSrm
    0U,	// MOVSSrr
    0U,	// MOVSSrr_REV
    0U,	// MOVSW
    0U,	// MOVSX16rm8
    0U,	// MOVSX16rr8
    0U,	// MOVSX32rm16
    0U,	// MOVSX32rm8
    0U,	// MOVSX32rr16
    0U,	// MOVSX32rr8
    0U,	// MOVSX64_NOREXrr32
    0U,	// MOVSX64rm16
    0U,	// MOVSX64rm32
    0U,	// MOVSX64rm8
    0U,	// MOVSX64rr16
    0U,	// MOVSX64rr32
    0U,	// MOVSX64rr8
    0U,	// MOVUPDmr
    0U,	// MOVUPDrm
    0U,	// MOVUPDrr
    0U,	// MOVUPDrr_REV
    0U,	// MOVUPSmr
    0U,	// MOVUPSrm
    0U,	// MOVUPSrr
    0U,	// MOVUPSrr_REV
    0U,	// MOVZPQILo2PQIrm
    0U,	// MOVZPQILo2PQIrr
    0U,	// MOVZQI2PQIrm
    0U,	// MOVZQI2PQIrr
    0U,	// MOVZX16rm8
    0U,	// MOVZX16rr8
    0U,	// MOVZX32_NOREXrm8
    0U,	// MOVZX32_NOREXrr8
    0U,	// MOVZX32rm16
    0U,	// MOVZX32rm8
    0U,	// MOVZX32rr16
    0U,	// MOVZX32rr8
    0U,	// MOVZX64rm16_Q
    0U,	// MOVZX64rm8_Q
    0U,	// MOVZX64rr16_Q
    0U,	// MOVZX64rr8_Q
    0U,	// MPSADBWrmi
    0U,	// MPSADBWrri
    0U,	// MUL16m
    0U,	// MUL16r
    0U,	// MUL32m
    0U,	// MUL32r
    0U,	// MUL64m
    0U,	// MUL64r
    0U,	// MUL8m
    0U,	// MUL8r
    0U,	// MULPDrm
    0U,	// MULPDrr
    0U,	// MULPSrm
    0U,	// MULPSrr
    0U,	// MULSDrm
    0U,	// MULSDrm_Int
    0U,	// MULSDrr
    0U,	// MULSDrr_Int
    0U,	// MULSSrm
    0U,	// MULSSrm_Int
    0U,	// MULSSrr
    0U,	// MULSSrr_Int
    4U,	// MULX32rm
    0U,	// MULX32rr
    4U,	// MULX64rm
    0U,	// MULX64rr
    0U,	// MUL_F32m
    0U,	// MUL_F64m
    0U,	// MUL_FI16m
    0U,	// MUL_FI32m
    0U,	// MUL_FPrST0
    0U,	// MUL_FST0r
    0U,	// MUL_Fp32
    0U,	// MUL_Fp32m
    0U,	// MUL_Fp64
    0U,	// MUL_Fp64m
    0U,	// MUL_Fp64m32
    0U,	// MUL_Fp80
    0U,	// MUL_Fp80m32
    0U,	// MUL_Fp80m64
    0U,	// MUL_FpI16m32
    0U,	// MUL_FpI16m64
    0U,	// MUL_FpI16m80
    0U,	// MUL_FpI32m32
    0U,	// MUL_FpI32m64
    0U,	// MUL_FpI32m80
    0U,	// MUL_FrST0
    0U,	// MWAITrr
    0U,	// NEG16m
    0U,	// NEG16r
    0U,	// NEG32m
    0U,	// NEG32r
    0U,	// NEG64m
    0U,	// NEG64r
    0U,	// NEG8m
    0U,	// NEG8r
    0U,	// NOOP
    0U,	// NOOP18_16m4
    0U,	// NOOP18_16m5
    0U,	// NOOP18_16m6
    0U,	// NOOP18_16m7
    0U,	// NOOP18_16r4
    0U,	// NOOP18_16r5
    0U,	// NOOP18_16r6
    0U,	// NOOP18_16r7
    0U,	// NOOP18_m4
    0U,	// NOOP18_m5
    0U,	// NOOP18_m6
    0U,	// NOOP18_m7
    0U,	// NOOP18_r4
    0U,	// NOOP18_r5
    0U,	// NOOP18_r6
    0U,	// NOOP18_r7
    0U,	// NOOP19rr
    0U,	// NOOPL
    0U,	// NOOPL_19
    0U,	// NOOPL_1a
    0U,	// NOOPL_1b
    0U,	// NOOPL_1c
    0U,	// NOOPL_1d
    0U,	// NOOPL_1e
    0U,	// NOOPW
    0U,	// NOOPW_19
    0U,	// NOOPW_1a
    0U,	// NOOPW_1b
    0U,	// NOOPW_1c
    0U,	// NOOPW_1d
    0U,	// NOOPW_1e
    0U,	// NOT16m
    0U,	// NOT16r
    0U,	// NOT32m
    0U,	// NOT32r
    0U,	// NOT64m
    0U,	// NOT64r
    0U,	// NOT8m
    0U,	// NOT8r
    0U,	// OR16i16
    0U,	// OR16mi
    0U,	// OR16mi8
    0U,	// OR16mr
    0U,	// OR16ri
    0U,	// OR16ri8
    0U,	// OR16rm
    0U,	// OR16rr
    0U,	// OR16rr_REV
    0U,	// OR32i32
    0U,	// OR32mi
    0U,	// OR32mi8
    0U,	// OR32mr
    0U,	// OR32mrLocked
    0U,	// OR32ri
    0U,	// OR32ri8
    0U,	// OR32rm
    0U,	// OR32rr
    0U,	// OR32rr_REV
    0U,	// OR64i32
    0U,	// OR64mi32
    0U,	// OR64mi8
    0U,	// OR64mr
    0U,	// OR64ri32
    0U,	// OR64ri8
    0U,	// OR64rm
    0U,	// OR64rr
    0U,	// OR64rr_REV
    0U,	// OR8i8
    0U,	// OR8mi
    0U,	// OR8mr
    0U,	// OR8ri
    0U,	// OR8ri8
    0U,	// OR8rm
    0U,	// OR8rr
    0U,	// OR8rr_REV
    0U,	// ORPDrm
    0U,	// ORPDrr
    0U,	// ORPSrm
    0U,	// ORPSrr
    0U,	// OUT16ir
    0U,	// OUT16rr
    0U,	// OUT32ir
    0U,	// OUT32rr
    0U,	// OUT8ir
    0U,	// OUT8rr
    0U,	// OUTSB
    0U,	// OUTSL
    0U,	// OUTSW
    0U,	// PABSBrm128
    0U,	// PABSBrr128
    0U,	// PABSDrm128
    0U,	// PABSDrr128
    0U,	// PABSWrm128
    0U,	// PABSWrr128
    0U,	// PACKSSDWrm
    0U,	// PACKSSDWrr
    0U,	// PACKSSWBrm
    0U,	// PACKSSWBrr
    0U,	// PACKUSDWrm
    0U,	// PACKUSDWrr
    0U,	// PACKUSWBrm
    0U,	// PACKUSWBrr
    0U,	// PADDBrm
    0U,	// PADDBrr
    0U,	// PADDDrm
    0U,	// PADDDrr
    0U,	// PADDQrm
    0U,	// PADDQrr
    0U,	// PADDSBrm
    0U,	// PADDSBrr
    0U,	// PADDSWrm
    0U,	// PADDSWrr
    0U,	// PADDUSBrm
    0U,	// PADDUSBrr
    0U,	// PADDUSWrm
    0U,	// PADDUSWrr
    0U,	// PADDWrm
    0U,	// PADDWrr
    0U,	// PALIGNR128rm
    0U,	// PALIGNR128rr
    0U,	// PANDNrm
    0U,	// PANDNrr
    0U,	// PANDrm
    0U,	// PANDrr
    0U,	// PAUSE
    0U,	// PAVGBrm
    0U,	// PAVGBrr
    0U,	// PAVGUSBrm
    0U,	// PAVGUSBrr
    0U,	// PAVGWrm
    0U,	// PAVGWrr
    0U,	// PBLENDVBrm0
    0U,	// PBLENDVBrr0
    0U,	// PBLENDWrmi
    0U,	// PBLENDWrri
    0U,	// PCLMULQDQrm
    0U,	// PCLMULQDQrr
    0U,	// PCMPEQBrm
    0U,	// PCMPEQBrr
    0U,	// PCMPEQDrm
    0U,	// PCMPEQDrr
    0U,	// PCMPEQQrm
    0U,	// PCMPEQQrr
    0U,	// PCMPEQWrm
    0U,	// PCMPEQWrr
    0U,	// PCMPESTRIMEM
    0U,	// PCMPESTRIREG
    0U,	// PCMPESTRIrm
    0U,	// PCMPESTRIrr
    0U,	// PCMPESTRM128MEM
    0U,	// PCMPESTRM128REG
    0U,	// PCMPESTRM128rm
    0U,	// PCMPESTRM128rr
    0U,	// PCMPGTBrm
    0U,	// PCMPGTBrr
    0U,	// PCMPGTDrm
    0U,	// PCMPGTDrr
    0U,	// PCMPGTQrm
    0U,	// PCMPGTQrr
    0U,	// PCMPGTWrm
    0U,	// PCMPGTWrr
    0U,	// PCMPISTRIMEM
    0U,	// PCMPISTRIREG
    0U,	// PCMPISTRIrm
    0U,	// PCMPISTRIrr
    0U,	// PCMPISTRM128MEM
    0U,	// PCMPISTRM128REG
    0U,	// PCMPISTRM128rm
    0U,	// PCMPISTRM128rr
    4U,	// PDEP32rm
    0U,	// PDEP32rr
    4U,	// PDEP64rm
    0U,	// PDEP64rr
    4U,	// PEXT32rm
    0U,	// PEXT32rr
    4U,	// PEXT64rm
    0U,	// PEXT64rr
    1U,	// PEXTRBmr
    0U,	// PEXTRBrr
    1U,	// PEXTRDmr
    0U,	// PEXTRDrr
    1U,	// PEXTRQmr
    0U,	// PEXTRQrr
    1U,	// PEXTRWmr
    0U,	// PEXTRWri
    0U,	// PEXTRWrr_REV
    0U,	// PF2IDrm
    0U,	// PF2IDrr
    0U,	// PF2IWrm
    0U,	// PF2IWrr
    0U,	// PFACCrm
    0U,	// PFACCrr
    0U,	// PFADDrm
    0U,	// PFADDrr
    0U,	// PFCMPEQrm
    0U,	// PFCMPEQrr
    0U,	// PFCMPGErm
    0U,	// PFCMPGErr
    0U,	// PFCMPGTrm
    0U,	// PFCMPGTrr
    0U,	// PFMAXrm
    0U,	// PFMAXrr
    0U,	// PFMINrm
    0U,	// PFMINrr
    0U,	// PFMULrm
    0U,	// PFMULrr
    0U,	// PFNACCrm
    0U,	// PFNACCrr
    0U,	// PFPNACCrm
    0U,	// PFPNACCrr
    0U,	// PFRCPIT1rm
    0U,	// PFRCPIT1rr
    0U,	// PFRCPIT2rm
    0U,	// PFRCPIT2rr
    0U,	// PFRCPrm
    0U,	// PFRCPrr
    0U,	// PFRSQIT1rm
    0U,	// PFRSQIT1rr
    0U,	// PFRSQRTrm
    0U,	// PFRSQRTrr
    0U,	// PFSUBRrm
    0U,	// PFSUBRrr
    0U,	// PFSUBrm
    0U,	// PFSUBrr
    0U,	// PHADDDrm
    0U,	// PHADDDrr
    0U,	// PHADDSWrm128
    0U,	// PHADDSWrr128
    0U,	// PHADDWrm
    0U,	// PHADDWrr
    0U,	// PHMINPOSUWrm128
    0U,	// PHMINPOSUWrr128
    0U,	// PHSUBDrm
    0U,	// PHSUBDrr
    0U,	// PHSUBSWrm128
    0U,	// PHSUBSWrr128
    0U,	// PHSUBWrm
    0U,	// PHSUBWrr
    0U,	// PI2FDrm
    0U,	// PI2FDrr
    0U,	// PI2FWrm
    0U,	// PI2FWrr
    0U,	// PINSRBrm
    0U,	// PINSRBrr
    0U,	// PINSRDrm
    0U,	// PINSRDrr
    0U,	// PINSRQrm
    0U,	// PINSRQrr
    0U,	// PINSRWrmi
    0U,	// PINSRWrri
    0U,	// PMADDUBSWrm128
    0U,	// PMADDUBSWrr128
    0U,	// PMADDWDrm
    0U,	// PMADDWDrr
    0U,	// PMAXSBrm
    0U,	// PMAXSBrr
    0U,	// PMAXSDrm
    0U,	// PMAXSDrr
    0U,	// PMAXSWrm
    0U,	// PMAXSWrr
    0U,	// PMAXUBrm
    0U,	// PMAXUBrr
    0U,	// PMAXUDrm
    0U,	// PMAXUDrr
    0U,	// PMAXUWrm
    0U,	// PMAXUWrr
    0U,	// PMINSBrm
    0U,	// PMINSBrr
    0U,	// PMINSDrm
    0U,	// PMINSDrr
    0U,	// PMINSWrm
    0U,	// PMINSWrr
    0U,	// PMINUBrm
    0U,	// PMINUBrr
    0U,	// PMINUDrm
    0U,	// PMINUDrr
    0U,	// PMINUWrm
    0U,	// PMINUWrr
    0U,	// PMOVMSKBrr
    0U,	// PMOVSXBDrm
    0U,	// PMOVSXBDrr
    0U,	// PMOVSXBQrm
    0U,	// PMOVSXBQrr
    0U,	// PMOVSXBWrm
    0U,	// PMOVSXBWrr
    0U,	// PMOVSXDQrm
    0U,	// PMOVSXDQrr
    0U,	// PMOVSXWDrm
    0U,	// PMOVSXWDrr
    0U,	// PMOVSXWQrm
    0U,	// PMOVSXWQrr
    0U,	// PMOVZXBDrm
    0U,	// PMOVZXBDrr
    0U,	// PMOVZXBQrm
    0U,	// PMOVZXBQrr
    0U,	// PMOVZXBWrm
    0U,	// PMOVZXBWrr
    0U,	// PMOVZXDQrm
    0U,	// PMOVZXDQrr
    0U,	// PMOVZXWDrm
    0U,	// PMOVZXWDrr
    0U,	// PMOVZXWQrm
    0U,	// PMOVZXWQrr
    0U,	// PMULDQrm
    0U,	// PMULDQrr
    0U,	// PMULHRSWrm128
    0U,	// PMULHRSWrr128
    0U,	// PMULHRWrm
    0U,	// PMULHRWrr
    0U,	// PMULHUWrm
    0U,	// PMULHUWrr
    0U,	// PMULHWrm
    0U,	// PMULHWrr
    0U,	// PMULLDrm
    0U,	// PMULLDrr
    0U,	// PMULLWrm
    0U,	// PMULLWrr
    0U,	// PMULUDQrm
    0U,	// PMULUDQrr
    0U,	// POP16r
    0U,	// POP16rmm
    0U,	// POP16rmr
    0U,	// POP32r
    0U,	// POP32rmm
    0U,	// POP32rmr
    0U,	// POP64r
    0U,	// POP64rmm
    0U,	// POP64rmr
    0U,	// POPA16
    0U,	// POPA32
    0U,	// POPCNT16rm
    0U,	// POPCNT16rr
    0U,	// POPCNT32rm
    0U,	// POPCNT32rr
    0U,	// POPCNT64rm
    0U,	// POPCNT64rr
    0U,	// POPDS16
    0U,	// POPDS32
    0U,	// POPES16
    0U,	// POPES32
    0U,	// POPF16
    0U,	// POPF32
    0U,	// POPF64
    0U,	// POPFS16
    0U,	// POPFS32
    0U,	// POPFS64
    0U,	// POPGS16
    0U,	// POPGS32
    0U,	// POPGS64
    0U,	// POPSS16
    0U,	// POPSS32
    0U,	// PORrm
    0U,	// PORrr
    0U,	// PREFETCH
    0U,	// PREFETCHNTA
    0U,	// PREFETCHT0
    0U,	// PREFETCHT1
    0U,	// PREFETCHT2
    0U,	// PREFETCHW
    0U,	// PSADBWrm
    0U,	// PSADBWrr
    0U,	// PSHUFBrm
    0U,	// PSHUFBrr
    0U,	// PSHUFDmi
    0U,	// PSHUFDri
    0U,	// PSHUFHWmi
    0U,	// PSHUFHWri
    0U,	// PSHUFLWmi
    0U,	// PSHUFLWri
    0U,	// PSIGNBrm
    0U,	// PSIGNBrr
    0U,	// PSIGNDrm
    0U,	// PSIGNDrr
    0U,	// PSIGNWrm
    0U,	// PSIGNWrr
    0U,	// PSLLDQri
    0U,	// PSLLDri
    0U,	// PSLLDrm
    0U,	// PSLLDrr
    0U,	// PSLLQri
    0U,	// PSLLQrm
    0U,	// PSLLQrr
    0U,	// PSLLWri
    0U,	// PSLLWrm
    0U,	// PSLLWrr
    0U,	// PSRADri
    0U,	// PSRADrm
    0U,	// PSRADrr
    0U,	// PSRAWri
    0U,	// PSRAWrm
    0U,	// PSRAWrr
    0U,	// PSRLDQri
    0U,	// PSRLDri
    0U,	// PSRLDrm
    0U,	// PSRLDrr
    0U,	// PSRLQri
    0U,	// PSRLQrm
    0U,	// PSRLQrr
    0U,	// PSRLWri
    0U,	// PSRLWrm
    0U,	// PSRLWrr
    0U,	// PSUBBrm
    0U,	// PSUBBrr
    0U,	// PSUBDrm
    0U,	// PSUBDrr
    0U,	// PSUBQrm
    0U,	// PSUBQrr
    0U,	// PSUBSBrm
    0U,	// PSUBSBrr
    0U,	// PSUBSWrm
    0U,	// PSUBSWrr
    0U,	// PSUBUSBrm
    0U,	// PSUBUSBrr
    0U,	// PSUBUSWrm
    0U,	// PSUBUSWrr
    0U,	// PSUBWrm
    0U,	// PSUBWrr
    0U,	// PSWAPDrm
    0U,	// PSWAPDrr
    0U,	// PTESTrm
    0U,	// PTESTrr
    0U,	// PUNPCKHBWrm
    0U,	// PUNPCKHBWrr
    0U,	// PUNPCKHDQrm
    0U,	// PUNPCKHDQrr
    0U,	// PUNPCKHQDQrm
    0U,	// PUNPCKHQDQrr
    0U,	// PUNPCKHWDrm
    0U,	// PUNPCKHWDrr
    0U,	// PUNPCKLBWrm
    0U,	// PUNPCKLBWrr
    0U,	// PUNPCKLDQrm
    0U,	// PUNPCKLDQrr
    0U,	// PUNPCKLQDQrm
    0U,	// PUNPCKLQDQrr
    0U,	// PUNPCKLWDrm
    0U,	// PUNPCKLWDrr
    0U,	// PUSH16i8
    0U,	// PUSH16r
    0U,	// PUSH16rmm
    0U,	// PUSH16rmr
    0U,	// PUSH32i8
    0U,	// PUSH32r
    0U,	// PUSH32rmm
    0U,	// PUSH32rmr
    0U,	// PUSH64i16
    0U,	// PUSH64i32
    0U,	// PUSH64i8
    0U,	// PUSH64r
    0U,	// PUSH64rmm
    0U,	// PUSH64rmr
    0U,	// PUSHA16
    0U,	// PUSHA32
    0U,	// PUSHCS16
    0U,	// PUSHCS32
    0U,	// PUSHDS16
    0U,	// PUSHDS32
    0U,	// PUSHES16
    0U,	// PUSHES32
    0U,	// PUSHF16
    0U,	// PUSHF32
    0U,	// PUSHF64
    0U,	// PUSHFS16
    0U,	// PUSHFS32
    0U,	// PUSHFS64
    0U,	// PUSHGS16
    0U,	// PUSHGS32
    0U,	// PUSHGS64
    0U,	// PUSHSS16
    0U,	// PUSHSS32
    0U,	// PUSHi16
    0U,	// PUSHi32
    0U,	// PXORrm
    0U,	// PXORrr
    0U,	// RCL16m1
    0U,	// RCL16mCL
    0U,	// RCL16mi
    0U,	// RCL16r1
    0U,	// RCL16rCL
    0U,	// RCL16ri
    0U,	// RCL32m1
    0U,	// RCL32mCL
    0U,	// RCL32mi
    0U,	// RCL32r1
    0U,	// RCL32rCL
    0U,	// RCL32ri
    0U,	// RCL64m1
    0U,	// RCL64mCL
    0U,	// RCL64mi
    0U,	// RCL64r1
    0U,	// RCL64rCL
    0U,	// RCL64ri
    0U,	// RCL8m1
    0U,	// RCL8mCL
    0U,	// RCL8mi
    0U,	// RCL8r1
    0U,	// RCL8rCL
    0U,	// RCL8ri
    0U,	// RCPPSm
    0U,	// RCPPSm_Int
    0U,	// RCPPSr
    0U,	// RCPPSr_Int
    0U,	// RCPSSm
    0U,	// RCPSSm_Int
    0U,	// RCPSSr
    0U,	// RCPSSr_Int
    0U,	// RCR16m1
    0U,	// RCR16mCL
    0U,	// RCR16mi
    0U,	// RCR16r1
    0U,	// RCR16rCL
    0U,	// RCR16ri
    0U,	// RCR32m1
    0U,	// RCR32mCL
    0U,	// RCR32mi
    0U,	// RCR32r1
    0U,	// RCR32rCL
    0U,	// RCR32ri
    0U,	// RCR64m1
    0U,	// RCR64mCL
    0U,	// RCR64mi
    0U,	// RCR64r1
    0U,	// RCR64rCL
    0U,	// RCR64ri
    0U,	// RCR8m1
    0U,	// RCR8mCL
    0U,	// RCR8mi
    0U,	// RCR8r1
    0U,	// RCR8rCL
    0U,	// RCR8ri
    0U,	// RDFSBASE
    0U,	// RDFSBASE64
    0U,	// RDGSBASE
    0U,	// RDGSBASE64
    0U,	// RDMSR
    0U,	// RDPMC
    0U,	// RDRAND16r
    0U,	// RDRAND32r
    0U,	// RDRAND64r
    0U,	// RDSEED16r
    0U,	// RDSEED32r
    0U,	// RDSEED64r
    0U,	// RDTSC
    0U,	// RDTSCP
    0U,	// RELEASE_MOV16mr
    0U,	// RELEASE_MOV32mr
    0U,	// RELEASE_MOV64mr
    0U,	// RELEASE_MOV8mr
    0U,	// REPNE_PREFIX
    0U,	// REP_MOVSB_32
    0U,	// REP_MOVSB_64
    0U,	// REP_MOVSD_32
    0U,	// REP_MOVSD_64
    0U,	// REP_MOVSQ_64
    0U,	// REP_MOVSW_32
    0U,	// REP_MOVSW_64
    0U,	// REP_PREFIX
    0U,	// REP_STOSB_32
    0U,	// REP_STOSB_64
    0U,	// REP_STOSD_32
    0U,	// REP_STOSD_64
    0U,	// REP_STOSQ_64
    0U,	// REP_STOSW_32
    0U,	// REP_STOSW_64
    0U,	// RETIL
    0U,	// RETIQ
    0U,	// RETIW
    0U,	// RETL
    0U,	// RETQ
    0U,	// RETW
    0U,	// REX64_PREFIX
    0U,	// ROL16m1
    0U,	// ROL16mCL
    0U,	// ROL16mi
    0U,	// ROL16r1
    0U,	// ROL16rCL
    0U,	// ROL16ri
    0U,	// ROL32m1
    0U,	// ROL32mCL
    0U,	// ROL32mi
    0U,	// ROL32r1
    0U,	// ROL32rCL
    0U,	// ROL32ri
    0U,	// ROL64m1
    0U,	// ROL64mCL
    0U,	// ROL64mi
    0U,	// ROL64r1
    0U,	// ROL64rCL
    0U,	// ROL64ri
    0U,	// ROL8m1
    0U,	// ROL8mCL
    0U,	// ROL8mi
    0U,	// ROL8r1
    0U,	// ROL8rCL
    0U,	// ROL8ri
    0U,	// ROR16m1
    0U,	// ROR16mCL
    0U,	// ROR16mi
    0U,	// ROR16r1
    0U,	// ROR16rCL
    0U,	// ROR16ri
    0U,	// ROR32m1
    0U,	// ROR32mCL
    0U,	// ROR32mi
    0U,	// ROR32r1
    0U,	// ROR32rCL
    0U,	// ROR32ri
    0U,	// ROR64m1
    0U,	// ROR64mCL
    0U,	// ROR64mi
    0U,	// ROR64r1
    0U,	// ROR64rCL
    0U,	// ROR64ri
    0U,	// ROR8m1
    0U,	// ROR8mCL
    0U,	// ROR8mi
    0U,	// ROR8r1
    0U,	// ROR8rCL
    0U,	// ROR8ri
    0U,	// RORX32mi
    0U,	// RORX32ri
    0U,	// RORX64mi
    0U,	// RORX64ri
    0U,	// ROUNDPDm
    0U,	// ROUNDPDr
    0U,	// ROUNDPSm
    0U,	// ROUNDPSr
    0U,	// ROUNDSDm
    0U,	// ROUNDSDr
    0U,	// ROUNDSDr_Int
    0U,	// ROUNDSSm
    0U,	// ROUNDSSr
    0U,	// ROUNDSSr_Int
    0U,	// RSM
    0U,	// RSQRTPSm
    0U,	// RSQRTPSm_Int
    0U,	// RSQRTPSr
    0U,	// RSQRTPSr_Int
    0U,	// RSQRTSSm
    0U,	// RSQRTSSm_Int
    0U,	// RSQRTSSr
    0U,	// RSQRTSSr_Int
    0U,	// SAHF
    0U,	// SAL16m1
    0U,	// SAL16mCL
    0U,	// SAL16mi
    0U,	// SAL16r1
    0U,	// SAL16rCL
    0U,	// SAL16ri
    0U,	// SAL32m1
    0U,	// SAL32mCL
    0U,	// SAL32mi
    0U,	// SAL32r1
    0U,	// SAL32rCL
    0U,	// SAL32ri
    0U,	// SAL64m1
    0U,	// SAL64mCL
    0U,	// SAL64mi
    0U,	// SAL64r1
    0U,	// SAL64rCL
    0U,	// SAL64ri
    0U,	// SAL8m1
    0U,	// SAL8mCL
    0U,	// SAL8mi
    0U,	// SAL8r1
    0U,	// SAL8rCL
    0U,	// SAL8ri
    0U,	// SALC
    0U,	// SAR16m1
    0U,	// SAR16mCL
    0U,	// SAR16mi
    0U,	// SAR16r1
    0U,	// SAR16rCL
    0U,	// SAR16ri
    0U,	// SAR32m1
    0U,	// SAR32mCL
    0U,	// SAR32mi
    0U,	// SAR32r1
    0U,	// SAR32rCL
    0U,	// SAR32ri
    0U,	// SAR64m1
    0U,	// SAR64mCL
    0U,	// SAR64mi
    0U,	// SAR64r1
    0U,	// SAR64rCL
    0U,	// SAR64ri
    0U,	// SAR8m1
    0U,	// SAR8mCL
    0U,	// SAR8mi
    0U,	// SAR8r1
    0U,	// SAR8rCL
    0U,	// SAR8ri
    0U,	// SARX32rm
    0U,	// SARX32rr
    0U,	// SARX64rm
    0U,	// SARX64rr
    0U,	// SBB16i16
    0U,	// SBB16mi
    0U,	// SBB16mi8
    0U,	// SBB16mr
    0U,	// SBB16ri
    0U,	// SBB16ri8
    0U,	// SBB16rm
    0U,	// SBB16rr
    0U,	// SBB16rr_REV
    0U,	// SBB32i32
    0U,	// SBB32mi
    0U,	// SBB32mi8
    0U,	// SBB32mr
    0U,	// SBB32ri
    0U,	// SBB32ri8
    0U,	// SBB32rm
    0U,	// SBB32rr
    0U,	// SBB32rr_REV
    0U,	// SBB64i32
    0U,	// SBB64mi32
    0U,	// SBB64mi8
    0U,	// SBB64mr
    0U,	// SBB64ri32
    0U,	// SBB64ri8
    0U,	// SBB64rm
    0U,	// SBB64rr
    0U,	// SBB64rr_REV
    0U,	// SBB8i8
    0U,	// SBB8mi
    0U,	// SBB8mr
    0U,	// SBB8ri
    0U,	// SBB8rm
    0U,	// SBB8rr
    0U,	// SBB8rr_REV
    0U,	// SCASB
    0U,	// SCASL
    0U,	// SCASQ
    0U,	// SCASW
    0U,	// SEG_ALLOCA_32
    0U,	// SEG_ALLOCA_64
    0U,	// SEH_EndPrologue
    0U,	// SEH_Epilogue
    0U,	// SEH_PushFrame
    0U,	// SEH_PushReg
    0U,	// SEH_SaveReg
    0U,	// SEH_SaveXMM
    0U,	// SEH_SetFrame
    0U,	// SEH_StackAlloc
    0U,	// SETAEm
    0U,	// SETAEr
    0U,	// SETAm
    0U,	// SETAr
    0U,	// SETBEm
    0U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    0U,	// SETBm
    0U,	// SETBr
    0U,	// SETEm
    0U,	// SETEr
    0U,	// SETGEm
    0U,	// SETGEr
    0U,	// SETGm
    0U,	// SETGr
    0U,	// SETLEm
    0U,	// SETLEr
    0U,	// SETLm
    0U,	// SETLr
    0U,	// SETNEm
    0U,	// SETNEr
    0U,	// SETNOm
    0U,	// SETNOr
    0U,	// SETNPm
    0U,	// SETNPr
    0U,	// SETNSm
    0U,	// SETNSr
    0U,	// SETOm
    0U,	// SETOr
    0U,	// SETPm
    0U,	// SETPr
    0U,	// SETSm
    0U,	// SETSr
    0U,	// SFENCE
    0U,	// SGDT16m
    0U,	// SGDT32m
    0U,	// SGDT64m
    0U,	// SHA1MSG1rm
    0U,	// SHA1MSG1rr
    0U,	// SHA1MSG2rm
    0U,	// SHA1MSG2rr
    0U,	// SHA1NEXTErm
    0U,	// SHA1NEXTErr
    0U,	// SHA1RNDS4rmi
    0U,	// SHA1RNDS4rri
    0U,	// SHA256MSG1rm
    0U,	// SHA256MSG1rr
    0U,	// SHA256MSG2rm
    0U,	// SHA256MSG2rr
    0U,	// SHA256RNDS2rm
    0U,	// SHA256RNDS2rr
    0U,	// SHL16m1
    0U,	// SHL16mCL
    0U,	// SHL16mi
    0U,	// SHL16r1
    0U,	// SHL16rCL
    0U,	// SHL16ri
    0U,	// SHL32m1
    0U,	// SHL32mCL
    0U,	// SHL32mi
    0U,	// SHL32r1
    0U,	// SHL32rCL
    0U,	// SHL32ri
    0U,	// SHL64m1
    0U,	// SHL64mCL
    0U,	// SHL64mi
    0U,	// SHL64r1
    0U,	// SHL64rCL
    0U,	// SHL64ri
    0U,	// SHL8m1
    0U,	// SHL8mCL
    0U,	// SHL8mi
    0U,	// SHL8r1
    0U,	// SHL8rCL
    0U,	// SHL8ri
    0U,	// SHLD16mrCL
    1U,	// SHLD16mri8
    0U,	// SHLD16rrCL
    0U,	// SHLD16rri8
    0U,	// SHLD32mrCL
    1U,	// SHLD32mri8
    0U,	// SHLD32rrCL
    0U,	// SHLD32rri8
    0U,	// SHLD64mrCL
    1U,	// SHLD64mri8
    0U,	// SHLD64rrCL
    0U,	// SHLD64rri8
    0U,	// SHLX32rm
    0U,	// SHLX32rr
    0U,	// SHLX64rm
    0U,	// SHLX64rr
    0U,	// SHR16m1
    0U,	// SHR16mCL
    0U,	// SHR16mi
    0U,	// SHR16r1
    0U,	// SHR16rCL
    0U,	// SHR16ri
    0U,	// SHR32m1
    0U,	// SHR32mCL
    0U,	// SHR32mi
    0U,	// SHR32r1
    0U,	// SHR32rCL
    0U,	// SHR32ri
    0U,	// SHR64m1
    0U,	// SHR64mCL
    0U,	// SHR64mi
    0U,	// SHR64r1
    0U,	// SHR64rCL
    0U,	// SHR64ri
    0U,	// SHR8m1
    0U,	// SHR8mCL
    0U,	// SHR8mi
    0U,	// SHR8r1
    0U,	// SHR8rCL
    0U,	// SHR8ri
    0U,	// SHRD16mrCL
    1U,	// SHRD16mri8
    0U,	// SHRD16rrCL
    0U,	// SHRD16rri8
    0U,	// SHRD32mrCL
    1U,	// SHRD32mri8
    0U,	// SHRD32rrCL
    0U,	// SHRD32rri8
    0U,	// SHRD64mrCL
    1U,	// SHRD64mri8
    0U,	// SHRD64rrCL
    0U,	// SHRD64rri8
    0U,	// SHRX32rm
    0U,	// SHRX32rr
    0U,	// SHRX64rm
    0U,	// SHRX64rr
    0U,	// SHUFPDrmi
    0U,	// SHUFPDrri
    0U,	// SHUFPSrmi
    0U,	// SHUFPSrri
    0U,	// SIDT16m
    0U,	// SIDT32m
    0U,	// SIDT64m
    0U,	// SIN_F
    0U,	// SIN_Fp32
    0U,	// SIN_Fp64
    0U,	// SIN_Fp80
    0U,	// SKINIT
    0U,	// SLDT16m
    0U,	// SLDT16r
    0U,	// SLDT32r
    0U,	// SLDT64m
    0U,	// SLDT64r
    0U,	// SMSW16m
    0U,	// SMSW16r
    0U,	// SMSW32r
    0U,	// SMSW64r
    0U,	// SQRTPDm
    0U,	// SQRTPDr
    0U,	// SQRTPSm
    0U,	// SQRTPSr
    0U,	// SQRTSDm
    0U,	// SQRTSDm_Int
    0U,	// SQRTSDr
    0U,	// SQRTSDr_Int
    0U,	// SQRTSSm
    0U,	// SQRTSSm_Int
    0U,	// SQRTSSr
    0U,	// SQRTSSr_Int
    0U,	// SQRT_F
    0U,	// SQRT_Fp32
    0U,	// SQRT_Fp64
    0U,	// SQRT_Fp80
    0U,	// STAC
    0U,	// STC
    0U,	// STD
    0U,	// STGI
    0U,	// STI
    0U,	// STMXCSR
    0U,	// STOSB
    0U,	// STOSL
    0U,	// STOSQ
    0U,	// STOSW
    0U,	// STR16r
    0U,	// STR32r
    0U,	// STR64r
    0U,	// STRm
    0U,	// ST_F32m
    0U,	// ST_F64m
    0U,	// ST_FCOMPST0r
    0U,	// ST_FCOMPST0r_alt
    0U,	// ST_FCOMST0r
    0U,	// ST_FP32m
    0U,	// ST_FP64m
    0U,	// ST_FP80m
    0U,	// ST_FPNCEST0r
    0U,	// ST_FPST0r
    0U,	// ST_FPST0r_alt
    0U,	// ST_FPrr
    0U,	// ST_FXCHST0r
    0U,	// ST_FXCHST0r_alt
    0U,	// ST_Fp32m
    0U,	// ST_Fp64m
    0U,	// ST_Fp64m32
    0U,	// ST_Fp80m32
    0U,	// ST_Fp80m64
    0U,	// ST_FpP32m
    0U,	// ST_FpP64m
    0U,	// ST_FpP64m32
    0U,	// ST_FpP80m
    0U,	// ST_FpP80m32
    0U,	// ST_FpP80m64
    0U,	// ST_Frr
    0U,	// SUB16i16
    0U,	// SUB16mi
    0U,	// SUB16mi8
    0U,	// SUB16mr
    0U,	// SUB16ri
    0U,	// SUB16ri8
    0U,	// SUB16rm
    0U,	// SUB16rr
    0U,	// SUB16rr_REV
    0U,	// SUB32i32
    0U,	// SUB32mi
    0U,	// SUB32mi8
    0U,	// SUB32mr
    0U,	// SUB32ri
    0U,	// SUB32ri8
    0U,	// SUB32rm
    0U,	// SUB32rr
    0U,	// SUB32rr_REV
    0U,	// SUB64i32
    0U,	// SUB64mi32
    0U,	// SUB64mi8
    0U,	// SUB64mr
    0U,	// SUB64ri32
    0U,	// SUB64ri8
    0U,	// SUB64rm
    0U,	// SUB64rr
    0U,	// SUB64rr_REV
    0U,	// SUB8i8
    0U,	// SUB8mi
    0U,	// SUB8mr
    0U,	// SUB8ri
    0U,	// SUB8ri8
    0U,	// SUB8rm
    0U,	// SUB8rr
    0U,	// SUB8rr_REV
    0U,	// SUBPDrm
    0U,	// SUBPDrr
    0U,	// SUBPSrm
    0U,	// SUBPSrr
    0U,	// SUBR_F32m
    0U,	// SUBR_F64m
    0U,	// SUBR_FI16m
    0U,	// SUBR_FI32m
    0U,	// SUBR_FPrST0
    0U,	// SUBR_FST0r
    0U,	// SUBR_Fp32m
    0U,	// SUBR_Fp64m
    0U,	// SUBR_Fp64m32
    0U,	// SUBR_Fp80m32
    0U,	// SUBR_Fp80m64
    0U,	// SUBR_FpI16m32
    0U,	// SUBR_FpI16m64
    0U,	// SUBR_FpI16m80
    0U,	// SUBR_FpI32m32
    0U,	// SUBR_FpI32m64
    0U,	// SUBR_FpI32m80
    0U,	// SUBR_FrST0
    0U,	// SUBSDrm
    0U,	// SUBSDrm_Int
    0U,	// SUBSDrr
    0U,	// SUBSDrr_Int
    0U,	// SUBSSrm
    0U,	// SUBSSrm_Int
    0U,	// SUBSSrr
    0U,	// SUBSSrr_Int
    0U,	// SUB_F32m
    0U,	// SUB_F64m
    0U,	// SUB_FI16m
    0U,	// SUB_FI32m
    0U,	// SUB_FPrST0
    0U,	// SUB_FST0r
    0U,	// SUB_Fp32
    0U,	// SUB_Fp32m
    0U,	// SUB_Fp64
    0U,	// SUB_Fp64m
    0U,	// SUB_Fp64m32
    0U,	// SUB_Fp80
    0U,	// SUB_Fp80m32
    0U,	// SUB_Fp80m64
    0U,	// SUB_FpI16m32
    0U,	// SUB_FpI16m64
    0U,	// SUB_FpI16m80
    0U,	// SUB_FpI32m32
    0U,	// SUB_FpI32m64
    0U,	// SUB_FpI32m80
    0U,	// SUB_FrST0
    0U,	// SWAPGS
    0U,	// SYSCALL
    0U,	// SYSENTER
    0U,	// SYSEXIT
    0U,	// SYSEXIT64
    0U,	// SYSRET
    0U,	// SYSRET64
    0U,	// T1MSKC32rm
    0U,	// T1MSKC32rr
    0U,	// T1MSKC64rm
    0U,	// T1MSKC64rr
    0U,	// TAILJMPd
    0U,	// TAILJMPd64
    0U,	// TAILJMPm
    0U,	// TAILJMPm64
    0U,	// TAILJMPr
    0U,	// TAILJMPr64
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    0U,	// TEST16i16
    0U,	// TEST16mi
    0U,	// TEST16mi_alt
    0U,	// TEST16ri
    0U,	// TEST16ri_alt
    0U,	// TEST16rm
    0U,	// TEST16rr
    0U,	// TEST32i32
    0U,	// TEST32mi
    0U,	// TEST32mi_alt
    0U,	// TEST32ri
    0U,	// TEST32ri_alt
    0U,	// TEST32rm
    0U,	// TEST32rr
    0U,	// TEST64i32
    0U,	// TEST64mi32
    0U,	// TEST64mi32_alt
    0U,	// TEST64ri32
    0U,	// TEST64ri32_alt
    0U,	// TEST64rm
    0U,	// TEST64rr
    0U,	// TEST8i8
    0U,	// TEST8mi
    0U,	// TEST8mi_alt
    0U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    0U,	// TEST8ri_alt
    0U,	// TEST8rm
    0U,	// TEST8rr
    0U,	// TLSCall_32
    0U,	// TLSCall_64
    0U,	// TLS_addr32
    0U,	// TLS_addr64
    0U,	// TLS_base_addr32
    0U,	// TLS_base_addr64
    0U,	// TRAP
    0U,	// TST_F
    0U,	// TST_Fp32
    0U,	// TST_Fp64
    0U,	// TST_Fp80
    0U,	// TZCNT16rm
    0U,	// TZCNT16rr
    0U,	// TZCNT32rm
    0U,	// TZCNT32rr
    0U,	// TZCNT64rm
    0U,	// TZCNT64rr
    0U,	// TZMSK32rm
    0U,	// TZMSK32rr
    0U,	// TZMSK64rm
    0U,	// TZMSK64rr
    0U,	// UCOMISDrm
    0U,	// UCOMISDrr
    0U,	// UCOMISSrm
    0U,	// UCOMISSrr
    0U,	// UCOM_FIPr
    0U,	// UCOM_FIr
    0U,	// UCOM_FPPr
    0U,	// UCOM_FPr
    0U,	// UCOM_FpIr32
    0U,	// UCOM_FpIr64
    0U,	// UCOM_FpIr80
    0U,	// UCOM_Fpr32
    0U,	// UCOM_Fpr64
    0U,	// UCOM_Fpr80
    0U,	// UCOM_Fr
    0U,	// UD2B
    0U,	// UNPCKHPDrm
    0U,	// UNPCKHPDrr
    0U,	// UNPCKHPSrm
    0U,	// UNPCKHPSrr
    0U,	// UNPCKLPDrm
    0U,	// UNPCKLPDrr
    0U,	// UNPCKLPSrm
    0U,	// UNPCKLPSrr
    1U,	// VAARG_64
    0U,	// VADDPDYrm
    0U,	// VADDPDYrr
    0U,	// VADDPDZrm
    4U,	// VADDPDZrmb
    2052U,	// VADDPDZrmbk
    10244U,	// VADDPDZrmbkz
    3220U,	// VADDPDZrmk
    4244U,	// VADDPDZrmkz
    0U,	// VADDPDZrr
    5268U,	// VADDPDZrrk
    4244U,	// VADDPDZrrkz
    0U,	// VADDPDrm
    0U,	// VADDPDrr
    0U,	// VADDPSYrm
    0U,	// VADDPSYrr
    0U,	// VADDPSZrm
    4U,	// VADDPSZrmb
    2052U,	// VADDPSZrmbk
    10244U,	// VADDPSZrmbkz
    3220U,	// VADDPSZrmk
    4244U,	// VADDPSZrmkz
    0U,	// VADDPSZrr
    5268U,	// VADDPSZrrk
    4244U,	// VADDPSZrrkz
    0U,	// VADDPSrm
    0U,	// VADDPSrr
    4U,	// VADDSDZrm
    0U,	// VADDSDZrr
    4U,	// VADDSDrm
    4U,	// VADDSDrm_Int
    0U,	// VADDSDrr
    0U,	// VADDSDrr_Int
    4U,	// VADDSSZrm
    0U,	// VADDSSZrr
    4U,	// VADDSSrm
    4U,	// VADDSSrm_Int
    0U,	// VADDSSrr
    0U,	// VADDSSrr_Int
    0U,	// VADDSUBPDYrm
    0U,	// VADDSUBPDYrr
    0U,	// VADDSUBPDrm
    0U,	// VADDSUBPDrr
    0U,	// VADDSUBPSYrm
    0U,	// VADDSUBPSYrr
    0U,	// VADDSUBPSrm
    0U,	// VADDSUBPSrr
    0U,	// VAESDECLASTrm
    0U,	// VAESDECLASTrr
    0U,	// VAESDECrm
    0U,	// VAESDECrr
    0U,	// VAESENCLASTrm
    0U,	// VAESENCLASTrr
    0U,	// VAESENCrm
    0U,	// VAESENCrr
    0U,	// VAESIMCrm
    0U,	// VAESIMCrr
    0U,	// VAESKEYGENASSIST128rm
    0U,	// VAESKEYGENASSIST128rr
    4U,	// VALIGNDrmi
    4U,	// VALIGNDrri
    0U,	// VALIGNDrrik
    4244U,	// VALIGNDrrikz
    4U,	// VALIGNQrmi
    4U,	// VALIGNQrri
    0U,	// VALIGNQrrik
    4244U,	// VALIGNQrrikz
    0U,	// VANDNPDYrm
    0U,	// VANDNPDYrr
    0U,	// VANDNPDrm
    0U,	// VANDNPDrr
    0U,	// VANDNPSYrm
    0U,	// VANDNPSYrr
    0U,	// VANDNPSrm
    0U,	// VANDNPSrr
    0U,	// VANDPDYrm
    0U,	// VANDPDYrr
    0U,	// VANDPDrm
    0U,	// VANDPDrr
    0U,	// VANDPSYrm
    0U,	// VANDPSYrr
    0U,	// VANDPSrm
    0U,	// VANDPSrr
    196U,	// VASTART_SAVE_XMM_REGS
    3220U,	// VBLENDMPDZrm
    3220U,	// VBLENDMPDZrr
    3220U,	// VBLENDMPSZrm
    3220U,	// VBLENDMPSZrr
    0U,	// VBLENDPDYrmi
    4U,	// VBLENDPDYrri
    4U,	// VBLENDPDrmi
    4U,	// VBLENDPDrri
    0U,	// VBLENDPSYrmi
    4U,	// VBLENDPSYrri
    4U,	// VBLENDPSrmi
    4U,	// VBLENDPSrri
    0U,	// VBLENDVPDYrm
    4U,	// VBLENDVPDYrr
    4U,	// VBLENDVPDrm
    4U,	// VBLENDVPDrr
    0U,	// VBLENDVPSYrm
    4U,	// VBLENDVPSYrr
    4U,	// VBLENDVPSrm
    4U,	// VBLENDVPSrr
    0U,	// VBROADCASTF128
    0U,	// VBROADCASTI128
    269U,	// VBROADCASTI32X4krm
    0U,	// VBROADCASTI32X4rm
    269U,	// VBROADCASTI64X4krm
    0U,	// VBROADCASTI64X4rm
    0U,	// VBROADCASTSDYrm
    0U,	// VBROADCASTSDYrr
    0U,	// VBROADCASTSDZrm
    0U,	// VBROADCASTSDZrr
    0U,	// VBROADCASTSSYrm
    0U,	// VBROADCASTSSYrr
    0U,	// VBROADCASTSSZrm
    0U,	// VBROADCASTSSZrr
    0U,	// VBROADCASTSSrm
    0U,	// VBROADCASTSSrr
    1U,	// VCMPPDYrmi
    0U,	// VCMPPDYrmi_alt
    1156U,	// VCMPPDYrri
    4U,	// VCMPPDYrri_alt
    1U,	// VCMPPDZrmi
    0U,	// VCMPPDZrmi_alt
    1156U,	// VCMPPDZrri
    4U,	// VCMPPDZrri_alt
    0U,	// VCMPPDZrrib
    76U,	// VCMPPDrmi
    4U,	// VCMPPDrmi_alt
    1156U,	// VCMPPDrri
    4U,	// VCMPPDrri_alt
    1U,	// VCMPPSYrmi
    0U,	// VCMPPSYrmi_alt
    1156U,	// VCMPPSYrri
    4U,	// VCMPPSYrri_alt
    1U,	// VCMPPSZrmi
    0U,	// VCMPPSZrmi_alt
    1156U,	// VCMPPSZrri
    4U,	// VCMPPSZrri_alt
    0U,	// VCMPPSZrrib
    76U,	// VCMPPSrmi
    4U,	// VCMPPSrmi_alt
    1156U,	// VCMPPSrri
    4U,	// VCMPPSrri_alt
    76U,	// VCMPSDZrm
    4U,	// VCMPSDZrmi_alt
    1156U,	// VCMPSDZrr
    4U,	// VCMPSDZrri_alt
    76U,	// VCMPSDrm
    4U,	// VCMPSDrm_alt
    1156U,	// VCMPSDrr
    4U,	// VCMPSDrr_alt
    76U,	// VCMPSSZrm
    4U,	// VCMPSSZrmi_alt
    1156U,	// VCMPSSZrr
    4U,	// VCMPSSZrri_alt
    76U,	// VCMPSSrm
    4U,	// VCMPSSrm_alt
    1156U,	// VCMPSSrr
    4U,	// VCMPSSrr_alt
    0U,	// VCOMISDZrm
    0U,	// VCOMISDZrr
    0U,	// VCOMISDrm
    0U,	// VCOMISDrr
    0U,	// VCOMISSZrm
    0U,	// VCOMISSZrr
    0U,	// VCOMISSrm
    0U,	// VCOMISSrr
    0U,	// VCVTDQ2PDYrm
    0U,	// VCVTDQ2PDYrr
    0U,	// VCVTDQ2PDZrm
    0U,	// VCVTDQ2PDZrr
    0U,	// VCVTDQ2PDrm
    0U,	// VCVTDQ2PDrr
    0U,	// VCVTDQ2PSYrm
    0U,	// VCVTDQ2PSYrr
    0U,	// VCVTDQ2PSZrm
    0U,	// VCVTDQ2PSZrr
    0U,	// VCVTDQ2PSZrrb
    0U,	// VCVTDQ2PSrm
    0U,	// VCVTDQ2PSrr
    0U,	// VCVTPD2DQXrm
    0U,	// VCVTPD2DQYrm
    0U,	// VCVTPD2DQYrr
    0U,	// VCVTPD2DQZrm
    0U,	// VCVTPD2DQZrr
    0U,	// VCVTPD2DQZrrb
    0U,	// VCVTPD2DQrr
    0U,	// VCVTPD2PSXrm
    0U,	// VCVTPD2PSYrm
    0U,	// VCVTPD2PSYrr
    0U,	// VCVTPD2PSZrm
    0U,	// VCVTPD2PSZrr
    0U,	// VCVTPD2PSZrrb
    0U,	// VCVTPD2PSrr
    0U,	// VCVTPD2UDQZrm
    0U,	// VCVTPD2UDQZrr
    0U,	// VCVTPD2UDQZrrb
    0U,	// VCVTPH2PSYrm
    0U,	// VCVTPH2PSYrr
    0U,	// VCVTPH2PSZrm
    0U,	// VCVTPH2PSZrr
    0U,	// VCVTPH2PSrm
    0U,	// VCVTPH2PSrr
    0U,	// VCVTPS2DQYrm
    0U,	// VCVTPS2DQYrr
    0U,	// VCVTPS2DQZrm
    0U,	// VCVTPS2DQZrr
    0U,	// VCVTPS2DQZrrb
    0U,	// VCVTPS2DQrm
    0U,	// VCVTPS2DQrr
    0U,	// VCVTPS2PDYrm
    0U,	// VCVTPS2PDYrr
    0U,	// VCVTPS2PDZrm
    0U,	// VCVTPS2PDZrr
    0U,	// VCVTPS2PDrm
    0U,	// VCVTPS2PDrr
    1U,	// VCVTPS2PHYmr
    0U,	// VCVTPS2PHYrr
    1U,	// VCVTPS2PHZmr
    0U,	// VCVTPS2PHZrr
    1U,	// VCVTPS2PHmr
    0U,	// VCVTPS2PHrr
    0U,	// VCVTPS2UDQZrm
    0U,	// VCVTPS2UDQZrr
    0U,	// VCVTPS2UDQZrrb
    0U,	// VCVTSD2SI64Zrm
    0U,	// VCVTSD2SI64Zrr
    0U,	// VCVTSD2SI64rm
    0U,	// VCVTSD2SI64rr
    0U,	// VCVTSD2SIZrm
    0U,	// VCVTSD2SIZrr
    0U,	// VCVTSD2SIrm
    0U,	// VCVTSD2SIrr
    4U,	// VCVTSD2SSZrm
    0U,	// VCVTSD2SSZrr
    4U,	// VCVTSD2SSrm
    0U,	// VCVTSD2SSrr
    0U,	// VCVTSD2USI64Zrm
    0U,	// VCVTSD2USI64Zrr
    0U,	// VCVTSD2USIZrm
    0U,	// VCVTSD2USIZrr
    4U,	// VCVTSI2SD64rm
    0U,	// VCVTSI2SD64rr
    4U,	// VCVTSI2SDZrm
    0U,	// VCVTSI2SDZrr
    4U,	// VCVTSI2SDrm
    0U,	// VCVTSI2SDrr
    4U,	// VCVTSI2SS64rm
    0U,	// VCVTSI2SS64rr
    4U,	// VCVTSI2SSZrm
    0U,	// VCVTSI2SSZrr
    4U,	// VCVTSI2SSrm
    0U,	// VCVTSI2SSrr
    4U,	// VCVTSI642SDZrm
    0U,	// VCVTSI642SDZrr
    4U,	// VCVTSI642SSZrm
    0U,	// VCVTSI642SSZrr
    4U,	// VCVTSS2SDZrm
    0U,	// VCVTSS2SDZrr
    4U,	// VCVTSS2SDrm
    0U,	// VCVTSS2SDrr
    0U,	// VCVTSS2SI64Zrm
    0U,	// VCVTSS2SI64Zrr
    0U,	// VCVTSS2SI64rm
    0U,	// VCVTSS2SI64rr
    0U,	// VCVTSS2SIZrm
    0U,	// VCVTSS2SIZrr
    0U,	// VCVTSS2SIrm
    0U,	// VCVTSS2SIrr
    0U,	// VCVTSS2USI64Zrm
    0U,	// VCVTSS2USI64Zrr
    0U,	// VCVTSS2USIZrm
    0U,	// VCVTSS2USIZrr
    0U,	// VCVTTPD2DQXrm
    0U,	// VCVTTPD2DQYrm
    0U,	// VCVTTPD2DQYrr
    0U,	// VCVTTPD2DQZrm
    0U,	// VCVTTPD2DQZrr
    0U,	// VCVTTPD2DQrr
    0U,	// VCVTTPD2UDQZrm
    0U,	// VCVTTPD2UDQZrr
    0U,	// VCVTTPS2DQYrm
    0U,	// VCVTTPS2DQYrr
    0U,	// VCVTTPS2DQZrm
    0U,	// VCVTTPS2DQZrr
    0U,	// VCVTTPS2DQrm
    0U,	// VCVTTPS2DQrr
    0U,	// VCVTTPS2UDQZrm
    0U,	// VCVTTPS2UDQZrr
    0U,	// VCVTTSD2SI64Zrm
    0U,	// VCVTTSD2SI64Zrr
    0U,	// VCVTTSD2SI64rm
    0U,	// VCVTTSD2SI64rr
    0U,	// VCVTTSD2SIZrm
    0U,	// VCVTTSD2SIZrr
    0U,	// VCVTTSD2SIrm
    0U,	// VCVTTSD2SIrr
    0U,	// VCVTTSD2USI64Zrm
    0U,	// VCVTTSD2USI64Zrr
    0U,	// VCVTTSD2USIZrm
    0U,	// VCVTTSD2USIZrr
    0U,	// VCVTTSS2SI64Zrm
    0U,	// VCVTTSS2SI64Zrr
    0U,	// VCVTTSS2SI64rm
    0U,	// VCVTTSS2SI64rr
    0U,	// VCVTTSS2SIZrm
    0U,	// VCVTTSS2SIZrr
    0U,	// VCVTTSS2SIrm
    0U,	// VCVTTSS2SIrr
    0U,	// VCVTTSS2USI64Zrm
    0U,	// VCVTTSS2USI64Zrr
    0U,	// VCVTTSS2USIZrm
    0U,	// VCVTTSS2USIZrr
    0U,	// VCVTUDQ2PDZrm
    0U,	// VCVTUDQ2PDZrr
    0U,	// VCVTUDQ2PSZrm
    0U,	// VCVTUDQ2PSZrr
    0U,	// VCVTUDQ2PSZrrb
    4U,	// VCVTUSI2SDZrm
    0U,	// VCVTUSI2SDZrr
    4U,	// VCVTUSI2SSZrm
    0U,	// VCVTUSI2SSZrr
    4U,	// VCVTUSI642SDZrm
    0U,	// VCVTUSI642SDZrr
    4U,	// VCVTUSI642SSZrm
    0U,	// VCVTUSI642SSZrr
    0U,	// VDIVPDYrm
    0U,	// VDIVPDYrr
    0U,	// VDIVPDZrm
    4U,	// VDIVPDZrmb
    2052U,	// VDIVPDZrmbk
    10244U,	// VDIVPDZrmbkz
    3220U,	// VDIVPDZrmk
    4244U,	// VDIVPDZrmkz
    0U,	// VDIVPDZrr
    5268U,	// VDIVPDZrrk
    4244U,	// VDIVPDZrrkz
    0U,	// VDIVPDrm
    0U,	// VDIVPDrr
    0U,	// VDIVPSYrm
    0U,	// VDIVPSYrr
    0U,	// VDIVPSZrm
    4U,	// VDIVPSZrmb
    2052U,	// VDIVPSZrmbk
    10244U,	// VDIVPSZrmbkz
    3220U,	// VDIVPSZrmk
    4244U,	// VDIVPSZrmkz
    0U,	// VDIVPSZrr
    5268U,	// VDIVPSZrrk
    4244U,	// VDIVPSZrrkz
    0U,	// VDIVPSrm
    0U,	// VDIVPSrr
    4U,	// VDIVSDZrm
    0U,	// VDIVSDZrr
    4U,	// VDIVSDrm
    4U,	// VDIVSDrm_Int
    0U,	// VDIVSDrr
    0U,	// VDIVSDrr_Int
    4U,	// VDIVSSZrm
    0U,	// VDIVSSZrr
    4U,	// VDIVSSrm
    4U,	// VDIVSSrm_Int
    0U,	// VDIVSSrr
    0U,	// VDIVSSrr_Int
    4U,	// VDPPDrmi
    4U,	// VDPPDrri
    0U,	// VDPPSYrmi
    4U,	// VDPPSYrri
    4U,	// VDPPSrmi
    4U,	// VDPPSrri
    0U,	// VERRm
    0U,	// VERRr
    0U,	// VERWm
    0U,	// VERWr
    1U,	// VEXTRACTF128mr
    0U,	// VEXTRACTF128rr
    1U,	// VEXTRACTF32x4mr
    0U,	// VEXTRACTF32x4rr
    1U,	// VEXTRACTF64x4mr
    0U,	// VEXTRACTF64x4rr
    1U,	// VEXTRACTI128mr
    0U,	// VEXTRACTI128rr
    1U,	// VEXTRACTI32x4mr
    0U,	// VEXTRACTI32x4rr
    1U,	// VEXTRACTI64x4mr
    0U,	// VEXTRACTI64x4rr
    0U,	// VEXTRACTPSmr
    0U,	// VEXTRACTPSrr
    0U,	// VEXTRACTPSzmr
    0U,	// VEXTRACTPSzrr
    0U,	// VFMADD132PDZm
    4U,	// VFMADD132PDZmb
    0U,	// VFMADD132PSZm
    4U,	// VFMADD132PSZmb
    0U,	// VFMADD213PDZm
    4U,	// VFMADD213PDZmb
    0U,	// VFMADD213PDZr
    345U,	// VFMADD213PDZrk
    281U,	// VFMADD213PDZrkz
    0U,	// VFMADD213PSZm
    4U,	// VFMADD213PSZmb
    0U,	// VFMADD213PSZr
    345U,	// VFMADD213PSZrk
    281U,	// VFMADD213PSZrkz
    4U,	// VFMADDPD4mr
    0U,	// VFMADDPD4mrY
    4U,	// VFMADDPD4rm
    4U,	// VFMADDPD4rmY
    4U,	// VFMADDPD4rr
    4U,	// VFMADDPD4rrY
    4U,	// VFMADDPD4rrY_REV
    4U,	// VFMADDPD4rr_REV
    0U,	// VFMADDPDr132m
    0U,	// VFMADDPDr132mY
    0U,	// VFMADDPDr132r
    0U,	// VFMADDPDr132rY
    0U,	// VFMADDPDr213m
    0U,	// VFMADDPDr213mY
    0U,	// VFMADDPDr213r
    0U,	// VFMADDPDr213rY
    0U,	// VFMADDPDr231m
    0U,	// VFMADDPDr231mY
    0U,	// VFMADDPDr231r
    0U,	// VFMADDPDr231rY
    4U,	// VFMADDPS4mr
    0U,	// VFMADDPS4mrY
    4U,	// VFMADDPS4rm
    4U,	// VFMADDPS4rmY
    4U,	// VFMADDPS4rr
    4U,	// VFMADDPS4rrY
    4U,	// VFMADDPS4rrY_REV
    4U,	// VFMADDPS4rr_REV
    0U,	// VFMADDPSr132m
    0U,	// VFMADDPSr132mY
    0U,	// VFMADDPSr132r
    0U,	// VFMADDPSr132rY
    0U,	// VFMADDPSr213m
    0U,	// VFMADDPSr213mY
    0U,	// VFMADDPSr213r
    0U,	// VFMADDPSr213rY
    0U,	// VFMADDPSr231m
    0U,	// VFMADDPSr231mY
    0U,	// VFMADDPSr231r
    0U,	// VFMADDPSr231rY
    4U,	// VFMADDSD4mr
    4U,	// VFMADDSD4mr_Int
    1156U,	// VFMADDSD4rm
    1156U,	// VFMADDSD4rm_Int
    4U,	// VFMADDSD4rr
    4U,	// VFMADDSD4rr_Int
    4U,	// VFMADDSD4rr_REV
    0U,	// VFMADDSDZm
    0U,	// VFMADDSDZr
    4U,	// VFMADDSDr132m
    0U,	// VFMADDSDr132r
    4U,	// VFMADDSDr213m
    0U,	// VFMADDSDr213r
    4U,	// VFMADDSDr231m
    0U,	// VFMADDSDr231r
    4U,	// VFMADDSS4mr
    4U,	// VFMADDSS4mr_Int
    1156U,	// VFMADDSS4rm
    1156U,	// VFMADDSS4rm_Int
    4U,	// VFMADDSS4rr
    4U,	// VFMADDSS4rr_Int
    4U,	// VFMADDSS4rr_REV
    0U,	// VFMADDSSZm
    0U,	// VFMADDSSZr
    4U,	// VFMADDSSr132m
    0U,	// VFMADDSSr132r
    4U,	// VFMADDSSr213m
    0U,	// VFMADDSSr213r
    4U,	// VFMADDSSr231m
    0U,	// VFMADDSSr231r
    0U,	// VFMADDSUB132PDZm
    4U,	// VFMADDSUB132PDZmb
    0U,	// VFMADDSUB132PSZm
    4U,	// VFMADDSUB132PSZmb
    0U,	// VFMADDSUB213PDZm
    4U,	// VFMADDSUB213PDZmb
    0U,	// VFMADDSUB213PDZr
    345U,	// VFMADDSUB213PDZrk
    281U,	// VFMADDSUB213PDZrkz
    0U,	// VFMADDSUB213PSZm
    4U,	// VFMADDSUB213PSZmb
    0U,	// VFMADDSUB213PSZr
    345U,	// VFMADDSUB213PSZrk
    281U,	// VFMADDSUB213PSZrkz
    4U,	// VFMADDSUBPD4mr
    0U,	// VFMADDSUBPD4mrY
    4U,	// VFMADDSUBPD4rm
    4U,	// VFMADDSUBPD4rmY
    4U,	// VFMADDSUBPD4rr
    4U,	// VFMADDSUBPD4rrY
    4U,	// VFMADDSUBPD4rrY_REV
    4U,	// VFMADDSUBPD4rr_REV
    0U,	// VFMADDSUBPDr132m
    0U,	// VFMADDSUBPDr132mY
    0U,	// VFMADDSUBPDr132r
    0U,	// VFMADDSUBPDr132rY
    0U,	// VFMADDSUBPDr213m
    0U,	// VFMADDSUBPDr213mY
    0U,	// VFMADDSUBPDr213r
    0U,	// VFMADDSUBPDr213rY
    0U,	// VFMADDSUBPDr231m
    0U,	// VFMADDSUBPDr231mY
    0U,	// VFMADDSUBPDr231r
    0U,	// VFMADDSUBPDr231rY
    4U,	// VFMADDSUBPS4mr
    0U,	// VFMADDSUBPS4mrY
    4U,	// VFMADDSUBPS4rm
    4U,	// VFMADDSUBPS4rmY
    4U,	// VFMADDSUBPS4rr
    4U,	// VFMADDSUBPS4rrY
    4U,	// VFMADDSUBPS4rrY_REV
    4U,	// VFMADDSUBPS4rr_REV
    0U,	// VFMADDSUBPSr132m
    0U,	// VFMADDSUBPSr132mY
    0U,	// VFMADDSUBPSr132r
    0U,	// VFMADDSUBPSr132rY
    0U,	// VFMADDSUBPSr213m
    0U,	// VFMADDSUBPSr213mY
    0U,	// VFMADDSUBPSr213r
    0U,	// VFMADDSUBPSr213rY
    0U,	// VFMADDSUBPSr231m
    0U,	// VFMADDSUBPSr231mY
    0U,	// VFMADDSUBPSr231r
    0U,	// VFMADDSUBPSr231rY
    0U,	// VFMSUB132PDZm
    4U,	// VFMSUB132PDZmb
    0U,	// VFMSUB132PSZm
    4U,	// VFMSUB132PSZmb
    0U,	// VFMSUB213PDZm
    4U,	// VFMSUB213PDZmb
    0U,	// VFMSUB213PDZr
    345U,	// VFMSUB213PDZrk
    281U,	// VFMSUB213PDZrkz
    0U,	// VFMSUB213PSZm
    4U,	// VFMSUB213PSZmb
    0U,	// VFMSUB213PSZr
    345U,	// VFMSUB213PSZrk
    281U,	// VFMSUB213PSZrkz
    0U,	// VFMSUBADD132PDZm
    4U,	// VFMSUBADD132PDZmb
    0U,	// VFMSUBADD132PSZm
    4U,	// VFMSUBADD132PSZmb
    0U,	// VFMSUBADD213PDZm
    4U,	// VFMSUBADD213PDZmb
    0U,	// VFMSUBADD213PDZr
    345U,	// VFMSUBADD213PDZrk
    281U,	// VFMSUBADD213PDZrkz
    0U,	// VFMSUBADD213PSZm
    4U,	// VFMSUBADD213PSZmb
    0U,	// VFMSUBADD213PSZr
    345U,	// VFMSUBADD213PSZrk
    281U,	// VFMSUBADD213PSZrkz
    4U,	// VFMSUBADDPD4mr
    0U,	// VFMSUBADDPD4mrY
    4U,	// VFMSUBADDPD4rm
    4U,	// VFMSUBADDPD4rmY
    4U,	// VFMSUBADDPD4rr
    4U,	// VFMSUBADDPD4rrY
    4U,	// VFMSUBADDPD4rrY_REV
    4U,	// VFMSUBADDPD4rr_REV
    0U,	// VFMSUBADDPDr132m
    0U,	// VFMSUBADDPDr132mY
    0U,	// VFMSUBADDPDr132r
    0U,	// VFMSUBADDPDr132rY
    0U,	// VFMSUBADDPDr213m
    0U,	// VFMSUBADDPDr213mY
    0U,	// VFMSUBADDPDr213r
    0U,	// VFMSUBADDPDr213rY
    0U,	// VFMSUBADDPDr231m
    0U,	// VFMSUBADDPDr231mY
    0U,	// VFMSUBADDPDr231r
    0U,	// VFMSUBADDPDr231rY
    4U,	// VFMSUBADDPS4mr
    0U,	// VFMSUBADDPS4mrY
    4U,	// VFMSUBADDPS4rm
    4U,	// VFMSUBADDPS4rmY
    4U,	// VFMSUBADDPS4rr
    4U,	// VFMSUBADDPS4rrY
    4U,	// VFMSUBADDPS4rrY_REV
    4U,	// VFMSUBADDPS4rr_REV
    0U,	// VFMSUBADDPSr132m
    0U,	// VFMSUBADDPSr132mY
    0U,	// VFMSUBADDPSr132r
    0U,	// VFMSUBADDPSr132rY
    0U,	// VFMSUBADDPSr213m
    0U,	// VFMSUBADDPSr213mY
    0U,	// VFMSUBADDPSr213r
    0U,	// VFMSUBADDPSr213rY
    0U,	// VFMSUBADDPSr231m
    0U,	// VFMSUBADDPSr231mY
    0U,	// VFMSUBADDPSr231r
    0U,	// VFMSUBADDPSr231rY
    4U,	// VFMSUBPD4mr
    0U,	// VFMSUBPD4mrY
    4U,	// VFMSUBPD4rm
    4U,	// VFMSUBPD4rmY
    4U,	// VFMSUBPD4rr
    4U,	// VFMSUBPD4rrY
    4U,	// VFMSUBPD4rrY_REV
    4U,	// VFMSUBPD4rr_REV
    0U,	// VFMSUBPDr132m
    0U,	// VFMSUBPDr132mY
    0U,	// VFMSUBPDr132r
    0U,	// VFMSUBPDr132rY
    0U,	// VFMSUBPDr213m
    0U,	// VFMSUBPDr213mY
    0U,	// VFMSUBPDr213r
    0U,	// VFMSUBPDr213rY
    0U,	// VFMSUBPDr231m
    0U,	// VFMSUBPDr231mY
    0U,	// VFMSUBPDr231r
    0U,	// VFMSUBPDr231rY
    4U,	// VFMSUBPS4mr
    0U,	// VFMSUBPS4mrY
    4U,	// VFMSUBPS4rm
    4U,	// VFMSUBPS4rmY
    4U,	// VFMSUBPS4rr
    4U,	// VFMSUBPS4rrY
    4U,	// VFMSUBPS4rrY_REV
    4U,	// VFMSUBPS4rr_REV
    0U,	// VFMSUBPSr132m
    0U,	// VFMSUBPSr132mY
    0U,	// VFMSUBPSr132r
    0U,	// VFMSUBPSr132rY
    0U,	// VFMSUBPSr213m
    0U,	// VFMSUBPSr213mY
    0U,	// VFMSUBPSr213r
    0U,	// VFMSUBPSr213rY
    0U,	// VFMSUBPSr231m
    0U,	// VFMSUBPSr231mY
    0U,	// VFMSUBPSr231r
    0U,	// VFMSUBPSr231rY
    4U,	// VFMSUBSD4mr
    4U,	// VFMSUBSD4mr_Int
    1156U,	// VFMSUBSD4rm
    1156U,	// VFMSUBSD4rm_Int
    4U,	// VFMSUBSD4rr
    4U,	// VFMSUBSD4rr_Int
    4U,	// VFMSUBSD4rr_REV
    0U,	// VFMSUBSDZm
    0U,	// VFMSUBSDZr
    4U,	// VFMSUBSDr132m
    0U,	// VFMSUBSDr132r
    4U,	// VFMSUBSDr213m
    0U,	// VFMSUBSDr213r
    4U,	// VFMSUBSDr231m
    0U,	// VFMSUBSDr231r
    4U,	// VFMSUBSS4mr
    4U,	// VFMSUBSS4mr_Int
    1156U,	// VFMSUBSS4rm
    1156U,	// VFMSUBSS4rm_Int
    4U,	// VFMSUBSS4rr
    4U,	// VFMSUBSS4rr_Int
    4U,	// VFMSUBSS4rr_REV
    0U,	// VFMSUBSSZm
    0U,	// VFMSUBSSZr
    4U,	// VFMSUBSSr132m
    0U,	// VFMSUBSSr132r
    4U,	// VFMSUBSSr213m
    0U,	// VFMSUBSSr213r
    4U,	// VFMSUBSSr231m
    0U,	// VFMSUBSSr231r
    0U,	// VFNMADD132PDZm
    4U,	// VFNMADD132PDZmb
    0U,	// VFNMADD132PSZm
    4U,	// VFNMADD132PSZmb
    0U,	// VFNMADD213PDZm
    4U,	// VFNMADD213PDZmb
    0U,	// VFNMADD213PDZr
    345U,	// VFNMADD213PDZrk
    281U,	// VFNMADD213PDZrkz
    0U,	// VFNMADD213PSZm
    4U,	// VFNMADD213PSZmb
    0U,	// VFNMADD213PSZr
    345U,	// VFNMADD213PSZrk
    281U,	// VFNMADD213PSZrkz
    4U,	// VFNMADDPD4mr
    0U,	// VFNMADDPD4mrY
    4U,	// VFNMADDPD4rm
    4U,	// VFNMADDPD4rmY
    4U,	// VFNMADDPD4rr
    4U,	// VFNMADDPD4rrY
    4U,	// VFNMADDPD4rrY_REV
    4U,	// VFNMADDPD4rr_REV
    0U,	// VFNMADDPDr132m
    0U,	// VFNMADDPDr132mY
    0U,	// VFNMADDPDr132r
    0U,	// VFNMADDPDr132rY
    0U,	// VFNMADDPDr213m
    0U,	// VFNMADDPDr213mY
    0U,	// VFNMADDPDr213r
    0U,	// VFNMADDPDr213rY
    0U,	// VFNMADDPDr231m
    0U,	// VFNMADDPDr231mY
    0U,	// VFNMADDPDr231r
    0U,	// VFNMADDPDr231rY
    4U,	// VFNMADDPS4mr
    0U,	// VFNMADDPS4mrY
    4U,	// VFNMADDPS4rm
    4U,	// VFNMADDPS4rmY
    4U,	// VFNMADDPS4rr
    4U,	// VFNMADDPS4rrY
    4U,	// VFNMADDPS4rrY_REV
    4U,	// VFNMADDPS4rr_REV
    0U,	// VFNMADDPSr132m
    0U,	// VFNMADDPSr132mY
    0U,	// VFNMADDPSr132r
    0U,	// VFNMADDPSr132rY
    0U,	// VFNMADDPSr213m
    0U,	// VFNMADDPSr213mY
    0U,	// VFNMADDPSr213r
    0U,	// VFNMADDPSr213rY
    0U,	// VFNMADDPSr231m
    0U,	// VFNMADDPSr231mY
    0U,	// VFNMADDPSr231r
    0U,	// VFNMADDPSr231rY
    4U,	// VFNMADDSD4mr
    4U,	// VFNMADDSD4mr_Int
    1156U,	// VFNMADDSD4rm
    1156U,	// VFNMADDSD4rm_Int
    4U,	// VFNMADDSD4rr
    4U,	// VFNMADDSD4rr_Int
    4U,	// VFNMADDSD4rr_REV
    0U,	// VFNMADDSDZm
    0U,	// VFNMADDSDZr
    4U,	// VFNMADDSDr132m
    0U,	// VFNMADDSDr132r
    4U,	// VFNMADDSDr213m
    0U,	// VFNMADDSDr213r
    4U,	// VFNMADDSDr231m
    0U,	// VFNMADDSDr231r
    4U,	// VFNMADDSS4mr
    4U,	// VFNMADDSS4mr_Int
    1156U,	// VFNMADDSS4rm
    1156U,	// VFNMADDSS4rm_Int
    4U,	// VFNMADDSS4rr
    4U,	// VFNMADDSS4rr_Int
    4U,	// VFNMADDSS4rr_REV
    0U,	// VFNMADDSSZm
    0U,	// VFNMADDSSZr
    4U,	// VFNMADDSSr132m
    0U,	// VFNMADDSSr132r
    4U,	// VFNMADDSSr213m
    0U,	// VFNMADDSSr213r
    4U,	// VFNMADDSSr231m
    0U,	// VFNMADDSSr231r
    0U,	// VFNMSUB132PDZm
    4U,	// VFNMSUB132PDZmb
    0U,	// VFNMSUB132PSZm
    4U,	// VFNMSUB132PSZmb
    0U,	// VFNMSUB213PDZm
    4U,	// VFNMSUB213PDZmb
    0U,	// VFNMSUB213PDZr
    345U,	// VFNMSUB213PDZrk
    281U,	// VFNMSUB213PDZrkz
    0U,	// VFNMSUB213PSZm
    4U,	// VFNMSUB213PSZmb
    0U,	// VFNMSUB213PSZr
    345U,	// VFNMSUB213PSZrk
    281U,	// VFNMSUB213PSZrkz
    4U,	// VFNMSUBPD4mr
    0U,	// VFNMSUBPD4mrY
    4U,	// VFNMSUBPD4rm
    4U,	// VFNMSUBPD4rmY
    4U,	// VFNMSUBPD4rr
    4U,	// VFNMSUBPD4rrY
    4U,	// VFNMSUBPD4rrY_REV
    4U,	// VFNMSUBPD4rr_REV
    0U,	// VFNMSUBPDr132m
    0U,	// VFNMSUBPDr132mY
    0U,	// VFNMSUBPDr132r
    0U,	// VFNMSUBPDr132rY
    0U,	// VFNMSUBPDr213m
    0U,	// VFNMSUBPDr213mY
    0U,	// VFNMSUBPDr213r
    0U,	// VFNMSUBPDr213rY
    0U,	// VFNMSUBPDr231m
    0U,	// VFNMSUBPDr231mY
    0U,	// VFNMSUBPDr231r
    0U,	// VFNMSUBPDr231rY
    4U,	// VFNMSUBPS4mr
    0U,	// VFNMSUBPS4mrY
    4U,	// VFNMSUBPS4rm
    4U,	// VFNMSUBPS4rmY
    4U,	// VFNMSUBPS4rr
    4U,	// VFNMSUBPS4rrY
    4U,	// VFNMSUBPS4rrY_REV
    4U,	// VFNMSUBPS4rr_REV
    0U,	// VFNMSUBPSr132m
    0U,	// VFNMSUBPSr132mY
    0U,	// VFNMSUBPSr132r
    0U,	// VFNMSUBPSr132rY
    0U,	// VFNMSUBPSr213m
    0U,	// VFNMSUBPSr213mY
    0U,	// VFNMSUBPSr213r
    0U,	// VFNMSUBPSr213rY
    0U,	// VFNMSUBPSr231m
    0U,	// VFNMSUBPSr231mY
    0U,	// VFNMSUBPSr231r
    0U,	// VFNMSUBPSr231rY
    4U,	// VFNMSUBSD4mr
    4U,	// VFNMSUBSD4mr_Int
    1156U,	// VFNMSUBSD4rm
    1156U,	// VFNMSUBSD4rm_Int
    4U,	// VFNMSUBSD4rr
    4U,	// VFNMSUBSD4rr_Int
    4U,	// VFNMSUBSD4rr_REV
    0U,	// VFNMSUBSDZm
    0U,	// VFNMSUBSDZr
    4U,	// VFNMSUBSDr132m
    0U,	// VFNMSUBSDr132r
    4U,	// VFNMSUBSDr213m
    0U,	// VFNMSUBSDr213r
    4U,	// VFNMSUBSDr231m
    0U,	// VFNMSUBSDr231r
    4U,	// VFNMSUBSS4mr
    4U,	// VFNMSUBSS4mr_Int
    1156U,	// VFNMSUBSS4rm
    1156U,	// VFNMSUBSS4rm_Int
    4U,	// VFNMSUBSS4rr
    4U,	// VFNMSUBSS4rr_Int
    4U,	// VFNMSUBSS4rr_REV
    0U,	// VFNMSUBSSZm
    0U,	// VFNMSUBSSZr
    4U,	// VFNMSUBSSr132m
    0U,	// VFNMSUBSSr132r
    4U,	// VFNMSUBSSr213m
    0U,	// VFNMSUBSSr213r
    4U,	// VFNMSUBSSr231m
    0U,	// VFNMSUBSSr231r
    0U,	// VFRCZPDrm
    0U,	// VFRCZPDrmY
    0U,	// VFRCZPDrr
    0U,	// VFRCZPDrrY
    0U,	// VFRCZPSrm
    0U,	// VFRCZPSrmY
    0U,	// VFRCZPSrr
    0U,	// VFRCZPSrrY
    0U,	// VFRCZSDrm
    0U,	// VFRCZSDrr
    0U,	// VFRCZSSrm
    0U,	// VFRCZSSrr
    0U,	// VFsANDNPDrm
    0U,	// VFsANDNPDrr
    0U,	// VFsANDNPSrm
    0U,	// VFsANDNPSrr
    0U,	// VFsANDPDrm
    0U,	// VFsANDPDrr
    0U,	// VFsANDPSrm
    0U,	// VFsANDPSrr
    0U,	// VFsORPDrm
    0U,	// VFsORPDrr
    0U,	// VFsORPSrm
    0U,	// VFsORPSrr
    0U,	// VFsXORPDrm
    0U,	// VFsXORPDrr
    0U,	// VFsXORPSrm
    0U,	// VFsXORPSrr
    0U,	// VGATHERDPDYrm
    404U,	// VGATHERDPDZrm
    0U,	// VGATHERDPDrm
    0U,	// VGATHERDPSYrm
    404U,	// VGATHERDPSZrm
    0U,	// VGATHERDPSrm
    0U,	// VGATHERPF0DPDm
    0U,	// VGATHERPF0DPSm
    0U,	// VGATHERPF0QPDm
    0U,	// VGATHERPF0QPSm
    0U,	// VGATHERPF1DPDm
    0U,	// VGATHERPF1DPSm
    0U,	// VGATHERPF1QPDm
    0U,	// VGATHERPF1QPSm
    0U,	// VGATHERQPDYrm
    404U,	// VGATHERQPDZrm
    0U,	// VGATHERQPDrm
    0U,	// VGATHERQPSYrm
    404U,	// VGATHERQPSZrm
    0U,	// VGATHERQPSrm
    0U,	// VHADDPDYrm
    0U,	// VHADDPDYrr
    0U,	// VHADDPDrm
    0U,	// VHADDPDrr
    0U,	// VHADDPSYrm
    0U,	// VHADDPSYrr
    0U,	// VHADDPSrm
    0U,	// VHADDPSrr
    0U,	// VHSUBPDYrm
    0U,	// VHSUBPDYrr
    0U,	// VHSUBPDrm
    0U,	// VHSUBPDrr
    0U,	// VHSUBPSYrm
    0U,	// VHSUBPSYrr
    0U,	// VHSUBPSrm
    0U,	// VHSUBPSrr
    4U,	// VINSERTF128rm
    4U,	// VINSERTF128rr
    4U,	// VINSERTF32x4rm
    4U,	// VINSERTF32x4rr
    0U,	// VINSERTF64x4rm
    4U,	// VINSERTF64x4rr
    4U,	// VINSERTI128rm
    4U,	// VINSERTI128rr
    4U,	// VINSERTI32x4rm
    4U,	// VINSERTI32x4rr
    0U,	// VINSERTI64x4rm
    4U,	// VINSERTI64x4rr
    4U,	// VINSERTPSrm
    4U,	// VINSERTPSrr
    4U,	// VINSERTPSzrm
    4U,	// VINSERTPSzrr
    0U,	// VLDDQUYrm
    0U,	// VLDDQUrm
    0U,	// VLDMXCSR
    0U,	// VMASKMOVDQU
    0U,	// VMASKMOVDQU64
    1U,	// VMASKMOVPDYmr
    0U,	// VMASKMOVPDYrm
    1U,	// VMASKMOVPDmr
    0U,	// VMASKMOVPDrm
    1U,	// VMASKMOVPSYmr
    0U,	// VMASKMOVPSYrm
    1U,	// VMASKMOVPSmr
    0U,	// VMASKMOVPSrm
    0U,	// VMAXCPDYrm
    0U,	// VMAXCPDYrr
    0U,	// VMAXCPDrm
    0U,	// VMAXCPDrr
    0U,	// VMAXCPSYrm
    0U,	// VMAXCPSYrr
    0U,	// VMAXCPSrm
    0U,	// VMAXCPSrr
    4U,	// VMAXCSDrm
    0U,	// VMAXCSDrr
    4U,	// VMAXCSSrm
    0U,	// VMAXCSSrr
    0U,	// VMAXPDYrm
    0U,	// VMAXPDYrr
    0U,	// VMAXPDZrm
    4U,	// VMAXPDZrmb
    2052U,	// VMAXPDZrmbk
    10244U,	// VMAXPDZrmbkz
    3220U,	// VMAXPDZrmk
    4244U,	// VMAXPDZrmkz
    0U,	// VMAXPDZrr
    5268U,	// VMAXPDZrrk
    4244U,	// VMAXPDZrrkz
    0U,	// VMAXPDrm
    0U,	// VMAXPDrr
    0U,	// VMAXPSYrm
    0U,	// VMAXPSYrr
    0U,	// VMAXPSZrm
    4U,	// VMAXPSZrmb
    2052U,	// VMAXPSZrmbk
    10244U,	// VMAXPSZrmbkz
    3220U,	// VMAXPSZrmk
    4244U,	// VMAXPSZrmkz
    0U,	// VMAXPSZrr
    5268U,	// VMAXPSZrrk
    4244U,	// VMAXPSZrrkz
    0U,	// VMAXPSrm
    0U,	// VMAXPSrr
    4U,	// VMAXSDZrm
    0U,	// VMAXSDZrr
    4U,	// VMAXSDrm
    4U,	// VMAXSDrm_Int
    0U,	// VMAXSDrr
    0U,	// VMAXSDrr_Int
    4U,	// VMAXSSZrm
    0U,	// VMAXSSZrr
    4U,	// VMAXSSrm
    4U,	// VMAXSSrm_Int
    0U,	// VMAXSSrr
    0U,	// VMAXSSrr_Int
    0U,	// VMCALL
    0U,	// VMCLEARm
    0U,	// VMFUNC
    0U,	// VMINCPDYrm
    0U,	// VMINCPDYrr
    0U,	// VMINCPDrm
    0U,	// VMINCPDrr
    0U,	// VMINCPSYrm
    0U,	// VMINCPSYrr
    0U,	// VMINCPSrm
    0U,	// VMINCPSrr
    4U,	// VMINCSDrm
    0U,	// VMINCSDrr
    4U,	// VMINCSSrm
    0U,	// VMINCSSrr
    0U,	// VMINPDYrm
    0U,	// VMINPDYrr
    0U,	// VMINPDZrm
    4U,	// VMINPDZrmb
    2052U,	// VMINPDZrmbk
    10244U,	// VMINPDZrmbkz
    3220U,	// VMINPDZrmk
    4244U,	// VMINPDZrmkz
    0U,	// VMINPDZrr
    5268U,	// VMINPDZrrk
    4244U,	// VMINPDZrrkz
    0U,	// VMINPDrm
    0U,	// VMINPDrr
    0U,	// VMINPSYrm
    0U,	// VMINPSYrr
    0U,	// VMINPSZrm
    4U,	// VMINPSZrmb
    2052U,	// VMINPSZrmbk
    10244U,	// VMINPSZrmbkz
    3220U,	// VMINPSZrmk
    4244U,	// VMINPSZrmkz
    0U,	// VMINPSZrr
    5268U,	// VMINPSZrrk
    4244U,	// VMINPSZrrkz
    0U,	// VMINPSrm
    0U,	// VMINPSrr
    4U,	// VMINSDZrm
    0U,	// VMINSDZrr
    4U,	// VMINSDrm
    4U,	// VMINSDrm_Int
    0U,	// VMINSDrr
    0U,	// VMINSDrr_Int
    4U,	// VMINSSZrm
    0U,	// VMINSSZrr
    4U,	// VMINSSrm
    4U,	// VMINSSrm_Int
    0U,	// VMINSSrr
    0U,	// VMINSSrr_Int
    0U,	// VMLAUNCH
    0U,	// VMLOAD32
    0U,	// VMLOAD64
    0U,	// VMMCALL
    0U,	// VMOV64toPQIZrr
    0U,	// VMOV64toPQIrr
    0U,	// VMOV64toSDZrr
    0U,	// VMOV64toSDrm
    0U,	// VMOV64toSDrr
    0U,	// VMOVAPDYmr
    0U,	// VMOVAPDYrm
    0U,	// VMOVAPDYrr
    0U,	// VMOVAPDYrr_REV
    0U,	// VMOVAPDZ128mr
    29U,	// VMOVAPDZ128mrk
    0U,	// VMOVAPDZ128rm
    345U,	// VMOVAPDZ128rmk
    269U,	// VMOVAPDZ128rmkz
    0U,	// VMOVAPDZ128rr
    0U,	// VMOVAPDZ128rr_alt
    345U,	// VMOVAPDZ128rrk
    345U,	// VMOVAPDZ128rrk_alt
    269U,	// VMOVAPDZ128rrkz
    269U,	// VMOVAPDZ128rrkz_alt
    0U,	// VMOVAPDZ256mr
    29U,	// VMOVAPDZ256mrk
    0U,	// VMOVAPDZ256rm
    345U,	// VMOVAPDZ256rmk
    269U,	// VMOVAPDZ256rmkz
    0U,	// VMOVAPDZ256rr
    0U,	// VMOVAPDZ256rr_alt
    345U,	// VMOVAPDZ256rrk
    345U,	// VMOVAPDZ256rrk_alt
    269U,	// VMOVAPDZ256rrkz
    269U,	// VMOVAPDZ256rrkz_alt
    0U,	// VMOVAPDZmr
    29U,	// VMOVAPDZmrk
    0U,	// VMOVAPDZrm
    345U,	// VMOVAPDZrmk
    269U,	// VMOVAPDZrmkz
    0U,	// VMOVAPDZrr
    0U,	// VMOVAPDZrr_alt
    345U,	// VMOVAPDZrrk
    345U,	// VMOVAPDZrrk_alt
    269U,	// VMOVAPDZrrkz
    269U,	// VMOVAPDZrrkz_alt
    0U,	// VMOVAPDmr
    0U,	// VMOVAPDrm
    0U,	// VMOVAPDrr
    0U,	// VMOVAPDrr_REV
    0U,	// VMOVAPSYmr
    0U,	// VMOVAPSYrm
    0U,	// VMOVAPSYrr
    0U,	// VMOVAPSYrr_REV
    0U,	// VMOVAPSZ128mr
    29U,	// VMOVAPSZ128mrk
    0U,	// VMOVAPSZ128rm
    345U,	// VMOVAPSZ128rmk
    269U,	// VMOVAPSZ128rmkz
    0U,	// VMOVAPSZ128rr
    0U,	// VMOVAPSZ128rr_alt
    345U,	// VMOVAPSZ128rrk
    345U,	// VMOVAPSZ128rrk_alt
    269U,	// VMOVAPSZ128rrkz
    269U,	// VMOVAPSZ128rrkz_alt
    0U,	// VMOVAPSZ256mr
    29U,	// VMOVAPSZ256mrk
    0U,	// VMOVAPSZ256rm
    345U,	// VMOVAPSZ256rmk
    269U,	// VMOVAPSZ256rmkz
    0U,	// VMOVAPSZ256rr
    0U,	// VMOVAPSZ256rr_alt
    345U,	// VMOVAPSZ256rrk
    345U,	// VMOVAPSZ256rrk_alt
    269U,	// VMOVAPSZ256rrkz
    269U,	// VMOVAPSZ256rrkz_alt
    0U,	// VMOVAPSZmr
    29U,	// VMOVAPSZmrk
    0U,	// VMOVAPSZrm
    345U,	// VMOVAPSZrmk
    269U,	// VMOVAPSZrmkz
    0U,	// VMOVAPSZrr
    0U,	// VMOVAPSZrr_alt
    345U,	// VMOVAPSZrrk
    345U,	// VMOVAPSZrrk_alt
    269U,	// VMOVAPSZrrkz
    269U,	// VMOVAPSZrrkz_alt
    0U,	// VMOVAPSmr
    0U,	// VMOVAPSrm
    0U,	// VMOVAPSrr
    0U,	// VMOVAPSrr_REV
    0U,	// VMOVDDUPYrm
    0U,	// VMOVDDUPYrr
    0U,	// VMOVDDUPZrm
    0U,	// VMOVDDUPZrr
    0U,	// VMOVDDUPrm
    0U,	// VMOVDDUPrr
    0U,	// VMOVDI2PDIZrm
    0U,	// VMOVDI2PDIZrr
    0U,	// VMOVDI2PDIrm
    0U,	// VMOVDI2PDIrr
    0U,	// VMOVDI2SSZrm
    0U,	// VMOVDI2SSZrr
    0U,	// VMOVDI2SSrm
    0U,	// VMOVDI2SSrr
    0U,	// VMOVDQA32Z128mr
    29U,	// VMOVDQA32Z128mrk
    0U,	// VMOVDQA32Z128rm
    345U,	// VMOVDQA32Z128rmk
    269U,	// VMOVDQA32Z128rmkz
    0U,	// VMOVDQA32Z128rr
    0U,	// VMOVDQA32Z128rr_alt
    345U,	// VMOVDQA32Z128rrk
    345U,	// VMOVDQA32Z128rrk_alt
    269U,	// VMOVDQA32Z128rrkz
    269U,	// VMOVDQA32Z128rrkz_alt
    0U,	// VMOVDQA32Z256mr
    29U,	// VMOVDQA32Z256mrk
    0U,	// VMOVDQA32Z256rm
    345U,	// VMOVDQA32Z256rmk
    269U,	// VMOVDQA32Z256rmkz
    0U,	// VMOVDQA32Z256rr
    0U,	// VMOVDQA32Z256rr_alt
    345U,	// VMOVDQA32Z256rrk
    345U,	// VMOVDQA32Z256rrk_alt
    269U,	// VMOVDQA32Z256rrkz
    269U,	// VMOVDQA32Z256rrkz_alt
    0U,	// VMOVDQA32Zmr
    29U,	// VMOVDQA32Zmrk
    0U,	// VMOVDQA32Zrm
    345U,	// VMOVDQA32Zrmk
    269U,	// VMOVDQA32Zrmkz
    0U,	// VMOVDQA32Zrr
    0U,	// VMOVDQA32Zrr_alt
    345U,	// VMOVDQA32Zrrk
    345U,	// VMOVDQA32Zrrk_alt
    269U,	// VMOVDQA32Zrrkz
    269U,	// VMOVDQA32Zrrkz_alt
    0U,	// VMOVDQA64Z128mr
    29U,	// VMOVDQA64Z128mrk
    0U,	// VMOVDQA64Z128rm
    345U,	// VMOVDQA64Z128rmk
    269U,	// VMOVDQA64Z128rmkz
    0U,	// VMOVDQA64Z128rr
    0U,	// VMOVDQA64Z128rr_alt
    345U,	// VMOVDQA64Z128rrk
    345U,	// VMOVDQA64Z128rrk_alt
    269U,	// VMOVDQA64Z128rrkz
    269U,	// VMOVDQA64Z128rrkz_alt
    0U,	// VMOVDQA64Z256mr
    29U,	// VMOVDQA64Z256mrk
    0U,	// VMOVDQA64Z256rm
    345U,	// VMOVDQA64Z256rmk
    269U,	// VMOVDQA64Z256rmkz
    0U,	// VMOVDQA64Z256rr
    0U,	// VMOVDQA64Z256rr_alt
    345U,	// VMOVDQA64Z256rrk
    345U,	// VMOVDQA64Z256rrk_alt
    269U,	// VMOVDQA64Z256rrkz
    269U,	// VMOVDQA64Z256rrkz_alt
    0U,	// VMOVDQA64Zmr
    29U,	// VMOVDQA64Zmrk
    0U,	// VMOVDQA64Zrm
    345U,	// VMOVDQA64Zrmk
    269U,	// VMOVDQA64Zrmkz
    0U,	// VMOVDQA64Zrr
    0U,	// VMOVDQA64Zrr_alt
    345U,	// VMOVDQA64Zrrk
    345U,	// VMOVDQA64Zrrk_alt
    269U,	// VMOVDQA64Zrrkz
    269U,	// VMOVDQA64Zrrkz_alt
    0U,	// VMOVDQAYmr
    0U,	// VMOVDQAYrm
    0U,	// VMOVDQAYrr
    0U,	// VMOVDQAYrr_REV
    0U,	// VMOVDQAmr
    0U,	// VMOVDQArm
    0U,	// VMOVDQArr
    0U,	// VMOVDQArr_REV
    0U,	// VMOVDQU16Z128mr
    29U,	// VMOVDQU16Z128mrk
    0U,	// VMOVDQU16Z128rm
    345U,	// VMOVDQU16Z128rmk
    269U,	// VMOVDQU16Z128rmkz
    0U,	// VMOVDQU16Z128rr
    0U,	// VMOVDQU16Z128rr_alt
    345U,	// VMOVDQU16Z128rrk
    345U,	// VMOVDQU16Z128rrk_alt
    269U,	// VMOVDQU16Z128rrkz
    269U,	// VMOVDQU16Z128rrkz_alt
    0U,	// VMOVDQU16Z256mr
    29U,	// VMOVDQU16Z256mrk
    0U,	// VMOVDQU16Z256rm
    345U,	// VMOVDQU16Z256rmk
    269U,	// VMOVDQU16Z256rmkz
    0U,	// VMOVDQU16Z256rr
    0U,	// VMOVDQU16Z256rr_alt
    345U,	// VMOVDQU16Z256rrk
    345U,	// VMOVDQU16Z256rrk_alt
    269U,	// VMOVDQU16Z256rrkz
    269U,	// VMOVDQU16Z256rrkz_alt
    0U,	// VMOVDQU16Zmr
    29U,	// VMOVDQU16Zmrk
    0U,	// VMOVDQU16Zrm
    345U,	// VMOVDQU16Zrmk
    269U,	// VMOVDQU16Zrmkz
    0U,	// VMOVDQU16Zrr
    0U,	// VMOVDQU16Zrr_alt
    345U,	// VMOVDQU16Zrrk
    345U,	// VMOVDQU16Zrrk_alt
    269U,	// VMOVDQU16Zrrkz
    269U,	// VMOVDQU16Zrrkz_alt
    0U,	// VMOVDQU32Z128mr
    29U,	// VMOVDQU32Z128mrk
    0U,	// VMOVDQU32Z128rm
    345U,	// VMOVDQU32Z128rmk
    269U,	// VMOVDQU32Z128rmkz
    0U,	// VMOVDQU32Z128rr
    0U,	// VMOVDQU32Z128rr_alt
    345U,	// VMOVDQU32Z128rrk
    345U,	// VMOVDQU32Z128rrk_alt
    269U,	// VMOVDQU32Z128rrkz
    269U,	// VMOVDQU32Z128rrkz_alt
    0U,	// VMOVDQU32Z256mr
    29U,	// VMOVDQU32Z256mrk
    0U,	// VMOVDQU32Z256rm
    345U,	// VMOVDQU32Z256rmk
    269U,	// VMOVDQU32Z256rmkz
    0U,	// VMOVDQU32Z256rr
    0U,	// VMOVDQU32Z256rr_alt
    345U,	// VMOVDQU32Z256rrk
    345U,	// VMOVDQU32Z256rrk_alt
    269U,	// VMOVDQU32Z256rrkz
    269U,	// VMOVDQU32Z256rrkz_alt
    0U,	// VMOVDQU32Zmr
    29U,	// VMOVDQU32Zmrk
    0U,	// VMOVDQU32Zrm
    345U,	// VMOVDQU32Zrmk
    269U,	// VMOVDQU32Zrmkz
    0U,	// VMOVDQU32Zrr
    0U,	// VMOVDQU32Zrr_alt
    345U,	// VMOVDQU32Zrrk
    345U,	// VMOVDQU32Zrrk_alt
    269U,	// VMOVDQU32Zrrkz
    269U,	// VMOVDQU32Zrrkz_alt
    0U,	// VMOVDQU64Z128mr
    29U,	// VMOVDQU64Z128mrk
    0U,	// VMOVDQU64Z128rm
    345U,	// VMOVDQU64Z128rmk
    269U,	// VMOVDQU64Z128rmkz
    0U,	// VMOVDQU64Z128rr
    0U,	// VMOVDQU64Z128rr_alt
    345U,	// VMOVDQU64Z128rrk
    345U,	// VMOVDQU64Z128rrk_alt
    269U,	// VMOVDQU64Z128rrkz
    269U,	// VMOVDQU64Z128rrkz_alt
    0U,	// VMOVDQU64Z256mr
    29U,	// VMOVDQU64Z256mrk
    0U,	// VMOVDQU64Z256rm
    345U,	// VMOVDQU64Z256rmk
    269U,	// VMOVDQU64Z256rmkz
    0U,	// VMOVDQU64Z256rr
    0U,	// VMOVDQU64Z256rr_alt
    345U,	// VMOVDQU64Z256rrk
    345U,	// VMOVDQU64Z256rrk_alt
    269U,	// VMOVDQU64Z256rrkz
    269U,	// VMOVDQU64Z256rrkz_alt
    0U,	// VMOVDQU64Zmr
    29U,	// VMOVDQU64Zmrk
    0U,	// VMOVDQU64Zrm
    345U,	// VMOVDQU64Zrmk
    269U,	// VMOVDQU64Zrmkz
    0U,	// VMOVDQU64Zrr
    0U,	// VMOVDQU64Zrr_alt
    345U,	// VMOVDQU64Zrrk
    345U,	// VMOVDQU64Zrrk_alt
    269U,	// VMOVDQU64Zrrkz
    269U,	// VMOVDQU64Zrrkz_alt
    0U,	// VMOVDQU8Z128mr
    29U,	// VMOVDQU8Z128mrk
    0U,	// VMOVDQU8Z128rm
    345U,	// VMOVDQU8Z128rmk
    269U,	// VMOVDQU8Z128rmkz
    0U,	// VMOVDQU8Z128rr
    0U,	// VMOVDQU8Z128rr_alt
    345U,	// VMOVDQU8Z128rrk
    345U,	// VMOVDQU8Z128rrk_alt
    269U,	// VMOVDQU8Z128rrkz
    269U,	// VMOVDQU8Z128rrkz_alt
    0U,	// VMOVDQU8Z256mr
    29U,	// VMOVDQU8Z256mrk
    0U,	// VMOVDQU8Z256rm
    345U,	// VMOVDQU8Z256rmk
    269U,	// VMOVDQU8Z256rmkz
    0U,	// VMOVDQU8Z256rr
    0U,	// VMOVDQU8Z256rr_alt
    345U,	// VMOVDQU8Z256rrk
    345U,	// VMOVDQU8Z256rrk_alt
    269U,	// VMOVDQU8Z256rrkz
    269U,	// VMOVDQU8Z256rrkz_alt
    0U,	// VMOVDQU8Zmr
    29U,	// VMOVDQU8Zmrk
    0U,	// VMOVDQU8Zrm
    345U,	// VMOVDQU8Zrmk
    269U,	// VMOVDQU8Zrmkz
    0U,	// VMOVDQU8Zrr
    0U,	// VMOVDQU8Zrr_alt
    345U,	// VMOVDQU8Zrrk
    345U,	// VMOVDQU8Zrrk_alt
    269U,	// VMOVDQU8Zrrkz
    269U,	// VMOVDQU8Zrrkz_alt
    0U,	// VMOVDQUYmr
    0U,	// VMOVDQUYrm
    0U,	// VMOVDQUYrr
    0U,	// VMOVDQUYrr_REV
    0U,	// VMOVDQUmr
    0U,	// VMOVDQUrm
    0U,	// VMOVDQUrr
    0U,	// VMOVDQUrr_REV
    0U,	// VMOVHLPSZrr
    0U,	// VMOVHLPSrr
    0U,	// VMOVHPDmr
    4U,	// VMOVHPDrm
    0U,	// VMOVHPSmr
    4U,	// VMOVHPSrm
    0U,	// VMOVLHPSZrr
    0U,	// VMOVLHPSrr
    0U,	// VMOVLPDmr
    4U,	// VMOVLPDrm
    0U,	// VMOVLPSmr
    4U,	// VMOVLPSrm
    0U,	// VMOVMSKPDYrr
    0U,	// VMOVMSKPDrr
    0U,	// VMOVMSKPSYrr
    0U,	// VMOVMSKPSrr
    0U,	// VMOVNTDQAYrm
    0U,	// VMOVNTDQAZ128rm
    0U,	// VMOVNTDQAZ256rm
    0U,	// VMOVNTDQAZrm
    0U,	// VMOVNTDQArm
    0U,	// VMOVNTDQYmr
    0U,	// VMOVNTDQZ128mr
    0U,	// VMOVNTDQZ256mr
    0U,	// VMOVNTDQZmr
    0U,	// VMOVNTDQmr
    0U,	// VMOVNTPDYmr
    0U,	// VMOVNTPDZ128mr
    0U,	// VMOVNTPDZ256mr
    0U,	// VMOVNTPDZmr
    0U,	// VMOVNTPDmr
    0U,	// VMOVNTPSYmr
    0U,	// VMOVNTPSZ128mr
    0U,	// VMOVNTPSZ256mr
    0U,	// VMOVNTPSZmr
    0U,	// VMOVNTPSmr
    0U,	// VMOVPDI2DIZmr
    0U,	// VMOVPDI2DIZrr
    0U,	// VMOVPDI2DImr
    0U,	// VMOVPDI2DIrr
    0U,	// VMOVPQI2QImr
    0U,	// VMOVPQI2QIrr
    0U,	// VMOVPQIto64Zmr
    0U,	// VMOVPQIto64Zrr
    0U,	// VMOVPQIto64rr
    0U,	// VMOVQI2PQIZrm
    0U,	// VMOVQI2PQIrm
    0U,	// VMOVSDZmr
    0U,	// VMOVSDZrm
    0U,	// VMOVSDZrr
    0U,	// VMOVSDZrr_REV
    345U,	// VMOVSDZrrk
    0U,	// VMOVSDmr
    0U,	// VMOVSDrm
    0U,	// VMOVSDrr
    0U,	// VMOVSDrr_REV
    0U,	// VMOVSDto64Zmr
    0U,	// VMOVSDto64Zrr
    0U,	// VMOVSDto64mr
    0U,	// VMOVSDto64rr
    0U,	// VMOVSHDUPYrm
    0U,	// VMOVSHDUPYrr
    0U,	// VMOVSHDUPZrm
    0U,	// VMOVSHDUPZrr
    0U,	// VMOVSHDUPrm
    0U,	// VMOVSHDUPrr
    0U,	// VMOVSLDUPYrm
    0U,	// VMOVSLDUPYrr
    0U,	// VMOVSLDUPZrm
    0U,	// VMOVSLDUPZrr
    0U,	// VMOVSLDUPrm
    0U,	// VMOVSLDUPrr
    0U,	// VMOVSS2DIZmr
    0U,	// VMOVSS2DIZrr
    0U,	// VMOVSS2DImr
    0U,	// VMOVSS2DIrr
    0U,	// VMOVSSZmr
    0U,	// VMOVSSZrm
    0U,	// VMOVSSZrr
    0U,	// VMOVSSZrr_REV
    345U,	// VMOVSSZrrk
    0U,	// VMOVSSmr
    0U,	// VMOVSSrm
    0U,	// VMOVSSrr
    0U,	// VMOVSSrr_REV
    0U,	// VMOVUPDYmr
    0U,	// VMOVUPDYrm
    0U,	// VMOVUPDYrr
    0U,	// VMOVUPDYrr_REV
    0U,	// VMOVUPDZ128mr
    29U,	// VMOVUPDZ128mrk
    0U,	// VMOVUPDZ128rm
    345U,	// VMOVUPDZ128rmk
    269U,	// VMOVUPDZ128rmkz
    0U,	// VMOVUPDZ128rr
    0U,	// VMOVUPDZ128rr_alt
    345U,	// VMOVUPDZ128rrk
    345U,	// VMOVUPDZ128rrk_alt
    269U,	// VMOVUPDZ128rrkz
    269U,	// VMOVUPDZ128rrkz_alt
    0U,	// VMOVUPDZ256mr
    29U,	// VMOVUPDZ256mrk
    0U,	// VMOVUPDZ256rm
    345U,	// VMOVUPDZ256rmk
    269U,	// VMOVUPDZ256rmkz
    0U,	// VMOVUPDZ256rr
    0U,	// VMOVUPDZ256rr_alt
    345U,	// VMOVUPDZ256rrk
    345U,	// VMOVUPDZ256rrk_alt
    269U,	// VMOVUPDZ256rrkz
    269U,	// VMOVUPDZ256rrkz_alt
    0U,	// VMOVUPDZmr
    29U,	// VMOVUPDZmrk
    0U,	// VMOVUPDZrm
    345U,	// VMOVUPDZrmk
    269U,	// VMOVUPDZrmkz
    0U,	// VMOVUPDZrr
    0U,	// VMOVUPDZrr_alt
    345U,	// VMOVUPDZrrk
    345U,	// VMOVUPDZrrk_alt
    269U,	// VMOVUPDZrrkz
    269U,	// VMOVUPDZrrkz_alt
    0U,	// VMOVUPDmr
    0U,	// VMOVUPDrm
    0U,	// VMOVUPDrr
    0U,	// VMOVUPDrr_REV
    0U,	// VMOVUPSYmr
    0U,	// VMOVUPSYrm
    0U,	// VMOVUPSYrr
    0U,	// VMOVUPSYrr_REV
    0U,	// VMOVUPSZ128mr
    29U,	// VMOVUPSZ128mrk
    0U,	// VMOVUPSZ128rm
    345U,	// VMOVUPSZ128rmk
    269U,	// VMOVUPSZ128rmkz
    0U,	// VMOVUPSZ128rr
    0U,	// VMOVUPSZ128rr_alt
    345U,	// VMOVUPSZ128rrk
    345U,	// VMOVUPSZ128rrk_alt
    269U,	// VMOVUPSZ128rrkz
    269U,	// VMOVUPSZ128rrkz_alt
    0U,	// VMOVUPSZ256mr
    29U,	// VMOVUPSZ256mrk
    0U,	// VMOVUPSZ256rm
    345U,	// VMOVUPSZ256rmk
    269U,	// VMOVUPSZ256rmkz
    0U,	// VMOVUPSZ256rr
    0U,	// VMOVUPSZ256rr_alt
    345U,	// VMOVUPSZ256rrk
    345U,	// VMOVUPSZ256rrk_alt
    269U,	// VMOVUPSZ256rrkz
    269U,	// VMOVUPSZ256rrkz_alt
    0U,	// VMOVUPSZmr
    29U,	// VMOVUPSZmrk
    0U,	// VMOVUPSZrm
    345U,	// VMOVUPSZrmk
    269U,	// VMOVUPSZrmkz
    0U,	// VMOVUPSZrr
    0U,	// VMOVUPSZrr_alt
    345U,	// VMOVUPSZrrk
    345U,	// VMOVUPSZrrk_alt
    269U,	// VMOVUPSZrrkz
    269U,	// VMOVUPSZrrkz_alt
    0U,	// VMOVUPSmr
    0U,	// VMOVUPSrm
    0U,	// VMOVUPSrr
    0U,	// VMOVUPSrr_REV
    0U,	// VMOVZPQILo2PQIZrm
    0U,	// VMOVZPQILo2PQIZrr
    0U,	// VMOVZPQILo2PQIrm
    0U,	// VMOVZPQILo2PQIrr
    0U,	// VMOVZQI2PQIrm
    0U,	// VMOVZQI2PQIrr
    0U,	// VMPSADBWYrmi
    4U,	// VMPSADBWYrri
    4U,	// VMPSADBWrmi
    4U,	// VMPSADBWrri
    0U,	// VMPTRLDm
    0U,	// VMPTRSTm
    0U,	// VMREAD32rm
    0U,	// VMREAD32rr
    0U,	// VMREAD64rm
    0U,	// VMREAD64rr
    0U,	// VMRESUME
    0U,	// VMRUN32
    0U,	// VMRUN64
    0U,	// VMSAVE32
    0U,	// VMSAVE64
    0U,	// VMULPDYrm
    0U,	// VMULPDYrr
    0U,	// VMULPDZrm
    4U,	// VMULPDZrmb
    2052U,	// VMULPDZrmbk
    10244U,	// VMULPDZrmbkz
    3220U,	// VMULPDZrmk
    4244U,	// VMULPDZrmkz
    0U,	// VMULPDZrr
    5268U,	// VMULPDZrrk
    4244U,	// VMULPDZrrkz
    0U,	// VMULPDrm
    0U,	// VMULPDrr
    0U,	// VMULPSYrm
    0U,	// VMULPSYrr
    0U,	// VMULPSZrm
    4U,	// VMULPSZrmb
    2052U,	// VMULPSZrmbk
    10244U,	// VMULPSZrmbkz
    3220U,	// VMULPSZrmk
    4244U,	// VMULPSZrmkz
    0U,	// VMULPSZrr
    5268U,	// VMULPSZrrk
    4244U,	// VMULPSZrrkz
    0U,	// VMULPSrm
    0U,	// VMULPSrr
    4U,	// VMULSDZrm
    0U,	// VMULSDZrr
    4U,	// VMULSDrm
    4U,	// VMULSDrm_Int
    0U,	// VMULSDrr
    0U,	// VMULSDrr_Int
    4U,	// VMULSSZrm
    0U,	// VMULSSZrr
    4U,	// VMULSSrm
    4U,	// VMULSSrm_Int
    0U,	// VMULSSrr
    0U,	// VMULSSrr_Int
    0U,	// VMWRITE32rm
    0U,	// VMWRITE32rr
    0U,	// VMWRITE64rm
    0U,	// VMWRITE64rr
    0U,	// VMXOFF
    0U,	// VMXON
    0U,	// VORPDYrm
    0U,	// VORPDYrr
    0U,	// VORPDrm
    0U,	// VORPDrr
    0U,	// VORPSYrm
    0U,	// VORPSYrr
    0U,	// VORPSrm
    0U,	// VORPSrr
    0U,	// VPABSBrm128
    0U,	// VPABSBrm256
    0U,	// VPABSBrr128
    0U,	// VPABSBrr256
    0U,	// VPABSDZrm
    0U,	// VPABSDZrmb
    3220U,	// VPABSDZrmbk
    4244U,	// VPABSDZrmbkz
    333U,	// VPABSDZrmk
    269U,	// VPABSDZrmkz
    0U,	// VPABSDZrr
    333U,	// VPABSDZrrk
    269U,	// VPABSDZrrkz
    0U,	// VPABSDrm128
    0U,	// VPABSDrm256
    0U,	// VPABSDrr128
    0U,	// VPABSDrr256
    0U,	// VPABSQZrm
    0U,	// VPABSQZrmb
    3220U,	// VPABSQZrmbk
    4244U,	// VPABSQZrmbkz
    333U,	// VPABSQZrmk
    269U,	// VPABSQZrmkz
    0U,	// VPABSQZrr
    333U,	// VPABSQZrrk
    269U,	// VPABSQZrrkz
    0U,	// VPABSWrm128
    0U,	// VPABSWrm256
    0U,	// VPABSWrr128
    0U,	// VPABSWrr256
    0U,	// VPACKSSDWYrm
    0U,	// VPACKSSDWYrr
    0U,	// VPACKSSDWrm
    0U,	// VPACKSSDWrr
    0U,	// VPACKSSWBYrm
    0U,	// VPACKSSWBYrr
    0U,	// VPACKSSWBrm
    0U,	// VPACKSSWBrr
    0U,	// VPACKUSDWYrm
    0U,	// VPACKUSDWYrr
    0U,	// VPACKUSDWrm
    0U,	// VPACKUSDWrr
    0U,	// VPACKUSWBYrm
    0U,	// VPACKUSWBYrr
    0U,	// VPACKUSWBrm
    0U,	// VPACKUSWBrr
    0U,	// VPADDBYrm
    0U,	// VPADDBYrr
    0U,	// VPADDBrm
    0U,	// VPADDBrr
    0U,	// VPADDDYrm
    0U,	// VPADDDYrr
    0U,	// VPADDDZrm
    4U,	// VPADDDZrmb
    1U,	// VPADDDZrmbk
    10244U,	// VPADDDZrmbkz
    0U,	// VPADDDZrmk
    4244U,	// VPADDDZrmkz
    0U,	// VPADDDZrr
    345U,	// VPADDDZrrk
    4244U,	// VPADDDZrrkz
    0U,	// VPADDDrm
    0U,	// VPADDDrr
    0U,	// VPADDQYrm
    0U,	// VPADDQYrr
    0U,	// VPADDQZrm
    4U,	// VPADDQZrmb
    1U,	// VPADDQZrmbk
    10244U,	// VPADDQZrmbkz
    0U,	// VPADDQZrmk
    4244U,	// VPADDQZrmkz
    0U,	// VPADDQZrr
    345U,	// VPADDQZrrk
    4244U,	// VPADDQZrrkz
    0U,	// VPADDQrm
    0U,	// VPADDQrr
    0U,	// VPADDSBYrm
    0U,	// VPADDSBYrr
    0U,	// VPADDSBrm
    0U,	// VPADDSBrr
    0U,	// VPADDSWYrm
    0U,	// VPADDSWYrr
    0U,	// VPADDSWrm
    0U,	// VPADDSWrr
    0U,	// VPADDUSBYrm
    0U,	// VPADDUSBYrr
    0U,	// VPADDUSBrm
    0U,	// VPADDUSBrr
    0U,	// VPADDUSWYrm
    0U,	// VPADDUSWYrr
    0U,	// VPADDUSWrm
    0U,	// VPADDUSWrr
    0U,	// VPADDWYrm
    0U,	// VPADDWYrr
    0U,	// VPADDWrm
    0U,	// VPADDWrr
    4U,	// VPALIGNR128rm
    4U,	// VPALIGNR128rr
    0U,	// VPALIGNR256rm
    4U,	// VPALIGNR256rr
    0U,	// VPANDDZrm
    4U,	// VPANDDZrmb
    1U,	// VPANDDZrmbk
    10244U,	// VPANDDZrmbkz
    0U,	// VPANDDZrmk
    4244U,	// VPANDDZrmkz
    0U,	// VPANDDZrr
    345U,	// VPANDDZrrk
    4244U,	// VPANDDZrrkz
    0U,	// VPANDNDZrm
    4U,	// VPANDNDZrmb
    1U,	// VPANDNDZrmbk
    10244U,	// VPANDNDZrmbkz
    0U,	// VPANDNDZrmk
    4244U,	// VPANDNDZrmkz
    0U,	// VPANDNDZrr
    345U,	// VPANDNDZrrk
    4244U,	// VPANDNDZrrkz
    0U,	// VPANDNQZrm
    4U,	// VPANDNQZrmb
    1U,	// VPANDNQZrmbk
    10244U,	// VPANDNQZrmbkz
    0U,	// VPANDNQZrmk
    4244U,	// VPANDNQZrmkz
    0U,	// VPANDNQZrr
    345U,	// VPANDNQZrrk
    4244U,	// VPANDNQZrrkz
    0U,	// VPANDNYrm
    0U,	// VPANDNYrr
    0U,	// VPANDNrm
    0U,	// VPANDNrr
    0U,	// VPANDQZrm
    4U,	// VPANDQZrmb
    1U,	// VPANDQZrmbk
    10244U,	// VPANDQZrmbkz
    0U,	// VPANDQZrmk
    4244U,	// VPANDQZrmkz
    0U,	// VPANDQZrr
    345U,	// VPANDQZrrk
    4244U,	// VPANDQZrrkz
    0U,	// VPANDYrm
    0U,	// VPANDYrr
    0U,	// VPANDrm
    0U,	// VPANDrr
    0U,	// VPAVGBYrm
    0U,	// VPAVGBYrr
    0U,	// VPAVGBrm
    0U,	// VPAVGBrr
    0U,	// VPAVGWYrm
    0U,	// VPAVGWYrr
    0U,	// VPAVGWrm
    0U,	// VPAVGWrr
    0U,	// VPBLENDDYrmi
    4U,	// VPBLENDDYrri
    4U,	// VPBLENDDrmi
    4U,	// VPBLENDDrri
    3220U,	// VPBLENDMDZrm
    3220U,	// VPBLENDMDZrr
    3220U,	// VPBLENDMQZrm
    3220U,	// VPBLENDMQZrr
    0U,	// VPBLENDVBYrm
    4U,	// VPBLENDVBYrr
    4U,	// VPBLENDVBrm
    4U,	// VPBLENDVBrr
    0U,	// VPBLENDWYrmi
    4U,	// VPBLENDWYrri
    4U,	// VPBLENDWrmi
    4U,	// VPBLENDWrri
    0U,	// VPBROADCASTBYrm
    0U,	// VPBROADCASTBYrr
    0U,	// VPBROADCASTBrm
    0U,	// VPBROADCASTBrr
    0U,	// VPBROADCASTDYrm
    0U,	// VPBROADCASTDYrr
    4244U,	// VPBROADCASTDZkrm
    269U,	// VPBROADCASTDZkrr
    0U,	// VPBROADCASTDZrm
    0U,	// VPBROADCASTDZrr
    269U,	// VPBROADCASTDrZkrr
    0U,	// VPBROADCASTDrZrr
    0U,	// VPBROADCASTDrm
    0U,	// VPBROADCASTDrr
    0U,	// VPBROADCASTMB2Qrr
    0U,	// VPBROADCASTMW2Drr
    0U,	// VPBROADCASTQYrm
    0U,	// VPBROADCASTQYrr
    4244U,	// VPBROADCASTQZkrm
    269U,	// VPBROADCASTQZkrr
    0U,	// VPBROADCASTQZrm
    0U,	// VPBROADCASTQZrr
    269U,	// VPBROADCASTQrZkrr
    0U,	// VPBROADCASTQrZrr
    0U,	// VPBROADCASTQrm
    0U,	// VPBROADCASTQrr
    0U,	// VPBROADCASTWYrm
    0U,	// VPBROADCASTWYrr
    0U,	// VPBROADCASTWrm
    0U,	// VPBROADCASTWrr
    4U,	// VPCLMULQDQrm
    4U,	// VPCLMULQDQrr
    4U,	// VPCMOVmr
    0U,	// VPCMOVmrY
    4U,	// VPCMOVrm
    4U,	// VPCMOVrmY
    4U,	// VPCMOVrr
    4U,	// VPCMOVrrY
    2U,	// VPCMPDZrmi
    4U,	// VPCMPDZrmi_alt
    0U,	// VPCMPDZrmik_alt
    1156U,	// VPCMPDZrri
    4U,	// VPCMPDZrri_alt
    3220U,	// VPCMPDZrrik_alt
    0U,	// VPCMPEQBYrm
    0U,	// VPCMPEQBYrr
    0U,	// VPCMPEQBZ128rm
    3220U,	// VPCMPEQBZ128rmk
    0U,	// VPCMPEQBZ128rr
    3220U,	// VPCMPEQBZ128rrk
    0U,	// VPCMPEQBZ256rm
    3220U,	// VPCMPEQBZ256rmk
    0U,	// VPCMPEQBZ256rr
    3220U,	// VPCMPEQBZ256rrk
    0U,	// VPCMPEQBZrm
    3220U,	// VPCMPEQBZrmk
    0U,	// VPCMPEQBZrr
    3220U,	// VPCMPEQBZrrk
    0U,	// VPCMPEQBrm
    0U,	// VPCMPEQBrr
    0U,	// VPCMPEQDYrm
    0U,	// VPCMPEQDYrr
    0U,	// VPCMPEQDZ128rm
    4U,	// VPCMPEQDZ128rmb
    2052U,	// VPCMPEQDZ128rmbk
    3220U,	// VPCMPEQDZ128rmk
    0U,	// VPCMPEQDZ128rr
    3220U,	// VPCMPEQDZ128rrk
    0U,	// VPCMPEQDZ256rm
    4U,	// VPCMPEQDZ256rmb
    2052U,	// VPCMPEQDZ256rmbk
    3220U,	// VPCMPEQDZ256rmk
    0U,	// VPCMPEQDZ256rr
    3220U,	// VPCMPEQDZ256rrk
    0U,	// VPCMPEQDZrm
    4U,	// VPCMPEQDZrmb
    2052U,	// VPCMPEQDZrmbk
    3220U,	// VPCMPEQDZrmk
    0U,	// VPCMPEQDZrr
    3220U,	// VPCMPEQDZrrk
    0U,	// VPCMPEQDrm
    0U,	// VPCMPEQDrr
    0U,	// VPCMPEQQYrm
    0U,	// VPCMPEQQYrr
    0U,	// VPCMPEQQZ128rm
    4U,	// VPCMPEQQZ128rmb
    2052U,	// VPCMPEQQZ128rmbk
    3220U,	// VPCMPEQQZ128rmk
    0U,	// VPCMPEQQZ128rr
    3220U,	// VPCMPEQQZ128rrk
    0U,	// VPCMPEQQZ256rm
    4U,	// VPCMPEQQZ256rmb
    2052U,	// VPCMPEQQZ256rmbk
    3220U,	// VPCMPEQQZ256rmk
    0U,	// VPCMPEQQZ256rr
    3220U,	// VPCMPEQQZ256rrk
    0U,	// VPCMPEQQZrm
    4U,	// VPCMPEQQZrmb
    2052U,	// VPCMPEQQZrmbk
    3220U,	// VPCMPEQQZrmk
    0U,	// VPCMPEQQZrr
    3220U,	// VPCMPEQQZrrk
    0U,	// VPCMPEQQrm
    0U,	// VPCMPEQQrr
    0U,	// VPCMPEQWYrm
    0U,	// VPCMPEQWYrr
    0U,	// VPCMPEQWZ128rm
    3220U,	// VPCMPEQWZ128rmk
    0U,	// VPCMPEQWZ128rr
    3220U,	// VPCMPEQWZ128rrk
    0U,	// VPCMPEQWZ256rm
    3220U,	// VPCMPEQWZ256rmk
    0U,	// VPCMPEQWZ256rr
    3220U,	// VPCMPEQWZ256rrk
    0U,	// VPCMPEQWZrm
    3220U,	// VPCMPEQWZrmk
    0U,	// VPCMPEQWZrr
    3220U,	// VPCMPEQWZrrk
    0U,	// VPCMPEQWrm
    0U,	// VPCMPEQWrr
    0U,	// VPCMPESTRIMEM
    0U,	// VPCMPESTRIREG
    0U,	// VPCMPESTRIrm
    0U,	// VPCMPESTRIrr
    0U,	// VPCMPESTRM128MEM
    0U,	// VPCMPESTRM128REG
    0U,	// VPCMPESTRM128rm
    0U,	// VPCMPESTRM128rr
    0U,	// VPCMPGTBYrm
    0U,	// VPCMPGTBYrr
    0U,	// VPCMPGTBZ128rm
    3220U,	// VPCMPGTBZ128rmk
    0U,	// VPCMPGTBZ128rr
    3220U,	// VPCMPGTBZ128rrk
    0U,	// VPCMPGTBZ256rm
    3220U,	// VPCMPGTBZ256rmk
    0U,	// VPCMPGTBZ256rr
    3220U,	// VPCMPGTBZ256rrk
    0U,	// VPCMPGTBZrm
    3220U,	// VPCMPGTBZrmk
    0U,	// VPCMPGTBZrr
    3220U,	// VPCMPGTBZrrk
    0U,	// VPCMPGTBrm
    0U,	// VPCMPGTBrr
    0U,	// VPCMPGTDYrm
    0U,	// VPCMPGTDYrr
    0U,	// VPCMPGTDZ128rm
    4U,	// VPCMPGTDZ128rmb
    2052U,	// VPCMPGTDZ128rmbk
    3220U,	// VPCMPGTDZ128rmk
    0U,	// VPCMPGTDZ128rr
    3220U,	// VPCMPGTDZ128rrk
    0U,	// VPCMPGTDZ256rm
    4U,	// VPCMPGTDZ256rmb
    2052U,	// VPCMPGTDZ256rmbk
    3220U,	// VPCMPGTDZ256rmk
    0U,	// VPCMPGTDZ256rr
    3220U,	// VPCMPGTDZ256rrk
    0U,	// VPCMPGTDZrm
    4U,	// VPCMPGTDZrmb
    2052U,	// VPCMPGTDZrmbk
    3220U,	// VPCMPGTDZrmk
    0U,	// VPCMPGTDZrr
    3220U,	// VPCMPGTDZrrk
    0U,	// VPCMPGTDrm
    0U,	// VPCMPGTDrr
    0U,	// VPCMPGTQYrm
    0U,	// VPCMPGTQYrr
    0U,	// VPCMPGTQZ128rm
    4U,	// VPCMPGTQZ128rmb
    2052U,	// VPCMPGTQZ128rmbk
    3220U,	// VPCMPGTQZ128rmk
    0U,	// VPCMPGTQZ128rr
    3220U,	// VPCMPGTQZ128rrk
    0U,	// VPCMPGTQZ256rm
    4U,	// VPCMPGTQZ256rmb
    2052U,	// VPCMPGTQZ256rmbk
    3220U,	// VPCMPGTQZ256rmk
    0U,	// VPCMPGTQZ256rr
    3220U,	// VPCMPGTQZ256rrk
    0U,	// VPCMPGTQZrm
    4U,	// VPCMPGTQZrmb
    2052U,	// VPCMPGTQZrmbk
    3220U,	// VPCMPGTQZrmk
    0U,	// VPCMPGTQZrr
    3220U,	// VPCMPGTQZrrk
    0U,	// VPCMPGTQrm
    0U,	// VPCMPGTQrr
    0U,	// VPCMPGTWYrm
    0U,	// VPCMPGTWYrr
    0U,	// VPCMPGTWZ128rm
    3220U,	// VPCMPGTWZ128rmk
    0U,	// VPCMPGTWZ128rr
    3220U,	// VPCMPGTWZ128rrk
    0U,	// VPCMPGTWZ256rm
    3220U,	// VPCMPGTWZ256rmk
    0U,	// VPCMPGTWZ256rr
    3220U,	// VPCMPGTWZ256rrk
    0U,	// VPCMPGTWZrm
    3220U,	// VPCMPGTWZrmk
    0U,	// VPCMPGTWZrr
    3220U,	// VPCMPGTWZrrk
    0U,	// VPCMPGTWrm
    0U,	// VPCMPGTWrr
    0U,	// VPCMPISTRIMEM
    0U,	// VPCMPISTRIREG
    0U,	// VPCMPISTRIrm
    0U,	// VPCMPISTRIrr
    0U,	// VPCMPISTRM128MEM
    0U,	// VPCMPISTRM128REG
    0U,	// VPCMPISTRM128rm
    0U,	// VPCMPISTRM128rr
    2U,	// VPCMPQZrmi
    4U,	// VPCMPQZrmi_alt
    0U,	// VPCMPQZrmik_alt
    1156U,	// VPCMPQZrri
    4U,	// VPCMPQZrri_alt
    3220U,	// VPCMPQZrrik_alt
    2U,	// VPCMPUDZrmi
    4U,	// VPCMPUDZrmi_alt
    0U,	// VPCMPUDZrmik_alt
    1156U,	// VPCMPUDZrri
    4U,	// VPCMPUDZrri_alt
    3220U,	// VPCMPUDZrrik_alt
    2U,	// VPCMPUQZrmi
    4U,	// VPCMPUQZrmi_alt
    0U,	// VPCMPUQZrmik_alt
    1156U,	// VPCMPUQZrri
    4U,	// VPCMPUQZrri_alt
    3220U,	// VPCMPUQZrrik_alt
    4U,	// VPCOMBmi
    4U,	// VPCOMBri
    4U,	// VPCOMDmi
    4U,	// VPCOMDri
    4U,	// VPCOMQmi
    4U,	// VPCOMQri
    4U,	// VPCOMUBmi
    4U,	// VPCOMUBri
    4U,	// VPCOMUDmi
    4U,	// VPCOMUDri
    4U,	// VPCOMUQmi
    4U,	// VPCOMUQri
    4U,	// VPCOMUWmi
    4U,	// VPCOMUWri
    4U,	// VPCOMWmi
    4U,	// VPCOMWri
    0U,	// VPCONFLICTDrm
    0U,	// VPCONFLICTDrmb
    3284U,	// VPCONFLICTDrmbk
    4244U,	// VPCONFLICTDrmbkz
    345U,	// VPCONFLICTDrmk
    269U,	// VPCONFLICTDrmkz
    32U,	// VPCONFLICTDrr
    345U,	// VPCONFLICTDrrk
    269U,	// VPCONFLICTDrrkz
    0U,	// VPCONFLICTQrm
    0U,	// VPCONFLICTQrmb
    3284U,	// VPCONFLICTQrmbk
    4244U,	// VPCONFLICTQrmbkz
    345U,	// VPCONFLICTQrmk
    269U,	// VPCONFLICTQrmkz
    32U,	// VPCONFLICTQrr
    345U,	// VPCONFLICTQrrk
    269U,	// VPCONFLICTQrrkz
    0U,	// VPERM2F128rm
    4U,	// VPERM2F128rr
    0U,	// VPERM2I128rm
    4U,	// VPERM2I128rr
    0U,	// VPERMDYrm
    0U,	// VPERMDYrr
    0U,	// VPERMDZrm
    0U,	// VPERMDZrr
    0U,	// VPERMI2Drm
    0U,	// VPERMI2Drmk
    0U,	// VPERMI2Drmkz
    0U,	// VPERMI2Drr
    345U,	// VPERMI2Drrk
    473U,	// VPERMI2Drrkz
    0U,	// VPERMI2PDrm
    0U,	// VPERMI2PDrmk
    0U,	// VPERMI2PDrmkz
    0U,	// VPERMI2PDrr
    345U,	// VPERMI2PDrrk
    473U,	// VPERMI2PDrrkz
    0U,	// VPERMI2PSrm
    0U,	// VPERMI2PSrmk
    0U,	// VPERMI2PSrmkz
    0U,	// VPERMI2PSrr
    345U,	// VPERMI2PSrrk
    473U,	// VPERMI2PSrrkz
    0U,	// VPERMI2Qrm
    0U,	// VPERMI2Qrmk
    0U,	// VPERMI2Qrmkz
    0U,	// VPERMI2Qrr
    345U,	// VPERMI2Qrrk
    473U,	// VPERMI2Qrrkz
    76U,	// VPERMIL2PDmr
    1U,	// VPERMIL2PDmrY
    0U,	// VPERMIL2PDrm
    0U,	// VPERMIL2PDrmY
    4U,	// VPERMIL2PDrr
    4U,	// VPERMIL2PDrrY
    76U,	// VPERMIL2PSmr
    1U,	// VPERMIL2PSmrY
    0U,	// VPERMIL2PSrm
    0U,	// VPERMIL2PSrmY
    4U,	// VPERMIL2PSrr
    4U,	// VPERMIL2PSrrY
    0U,	// VPERMILPDYmi
    0U,	// VPERMILPDYri
    0U,	// VPERMILPDYrm
    0U,	// VPERMILPDYrr
    0U,	// VPERMILPDZmi
    0U,	// VPERMILPDZri
    0U,	// VPERMILPDmi
    0U,	// VPERMILPDri
    0U,	// VPERMILPDrm
    0U,	// VPERMILPDrr
    0U,	// VPERMILPSYmi
    0U,	// VPERMILPSYri
    0U,	// VPERMILPSYrm
    0U,	// VPERMILPSYrr
    0U,	// VPERMILPSZmi
    0U,	// VPERMILPSZri
    0U,	// VPERMILPSmi
    0U,	// VPERMILPSri
    0U,	// VPERMILPSrm
    0U,	// VPERMILPSrr
    0U,	// VPERMPDYmi
    0U,	// VPERMPDYri
    0U,	// VPERMPDZmi
    0U,	// VPERMPDZri
    0U,	// VPERMPDZrm
    0U,	// VPERMPDZrr
    0U,	// VPERMPSYrm
    0U,	// VPERMPSYrr
    0U,	// VPERMPSZrm
    0U,	// VPERMPSZrr
    0U,	// VPERMQYmi
    0U,	// VPERMQYri
    0U,	// VPERMQZmi
    0U,	// VPERMQZri
    0U,	// VPERMQZrm
    0U,	// VPERMQZrr
    0U,	// VPERMT2Drm
    0U,	// VPERMT2Drmk
    0U,	// VPERMT2Drmkz
    0U,	// VPERMT2Drr
    345U,	// VPERMT2Drrk
    473U,	// VPERMT2Drrkz
    0U,	// VPERMT2PDrm
    0U,	// VPERMT2PDrmk
    0U,	// VPERMT2PDrmkz
    0U,	// VPERMT2PDrr
    345U,	// VPERMT2PDrrk
    473U,	// VPERMT2PDrrkz
    0U,	// VPERMT2PSrm
    0U,	// VPERMT2PSrmk
    0U,	// VPERMT2PSrmkz
    0U,	// VPERMT2PSrr
    345U,	// VPERMT2PSrrk
    473U,	// VPERMT2PSrrkz
    0U,	// VPERMT2Qrm
    0U,	// VPERMT2Qrmk
    0U,	// VPERMT2Qrmkz
    0U,	// VPERMT2Qrr
    345U,	// VPERMT2Qrrk
    473U,	// VPERMT2Qrrkz
    1U,	// VPEXTRBmr
    0U,	// VPEXTRBrr
    1U,	// VPEXTRDmr
    0U,	// VPEXTRDrr
    1U,	// VPEXTRQmr
    0U,	// VPEXTRQrr
    1U,	// VPEXTRWmr
    0U,	// VPEXTRWri
    0U,	// VPEXTRWrr_REV
    0U,	// VPGATHERDDYrm
    404U,	// VPGATHERDDZrm
    0U,	// VPGATHERDDrm
    0U,	// VPGATHERDQYrm
    404U,	// VPGATHERDQZrm
    0U,	// VPGATHERDQrm
    0U,	// VPGATHERQDYrm
    404U,	// VPGATHERQDZrm
    0U,	// VPGATHERQDrm
    0U,	// VPGATHERQQYrm
    404U,	// VPGATHERQQZrm
    0U,	// VPGATHERQQrm
    0U,	// VPHADDBDrm
    0U,	// VPHADDBDrr
    0U,	// VPHADDBQrm
    0U,	// VPHADDBQrr
    0U,	// VPHADDBWrm
    0U,	// VPHADDBWrr
    0U,	// VPHADDDQrm
    0U,	// VPHADDDQrr
    0U,	// VPHADDDYrm
    0U,	// VPHADDDYrr
    0U,	// VPHADDDrm
    0U,	// VPHADDDrr
    0U,	// VPHADDSWrm128
    0U,	// VPHADDSWrm256
    0U,	// VPHADDSWrr128
    0U,	// VPHADDSWrr256
    0U,	// VPHADDUBDrm
    0U,	// VPHADDUBDrr
    0U,	// VPHADDUBQrm
    0U,	// VPHADDUBQrr
    0U,	// VPHADDUBWrm
    0U,	// VPHADDUBWrr
    0U,	// VPHADDUDQrm
    0U,	// VPHADDUDQrr
    0U,	// VPHADDUWDrm
    0U,	// VPHADDUWDrr
    0U,	// VPHADDUWQrm
    0U,	// VPHADDUWQrr
    0U,	// VPHADDWDrm
    0U,	// VPHADDWDrr
    0U,	// VPHADDWQrm
    0U,	// VPHADDWQrr
    0U,	// VPHADDWYrm
    0U,	// VPHADDWYrr
    0U,	// VPHADDWrm
    0U,	// VPHADDWrr
    0U,	// VPHMINPOSUWrm128
    0U,	// VPHMINPOSUWrr128
    0U,	// VPHSUBBWrm
    0U,	// VPHSUBBWrr
    0U,	// VPHSUBDQrm
    0U,	// VPHSUBDQrr
    0U,	// VPHSUBDYrm
    0U,	// VPHSUBDYrr
    0U,	// VPHSUBDrm
    0U,	// VPHSUBDrr
    0U,	// VPHSUBSWrm128
    0U,	// VPHSUBSWrm256
    0U,	// VPHSUBSWrr128
    0U,	// VPHSUBSWrr256
    0U,	// VPHSUBWDrm
    0U,	// VPHSUBWDrr
    0U,	// VPHSUBWYrm
    0U,	// VPHSUBWYrr
    0U,	// VPHSUBWrm
    0U,	// VPHSUBWrr
    4U,	// VPINSRBrm
    4U,	// VPINSRBrr
    4U,	// VPINSRDrm
    4U,	// VPINSRDrr
    4U,	// VPINSRQrm
    4U,	// VPINSRQrr
    4U,	// VPINSRWrmi
    4U,	// VPINSRWrri
    0U,	// VPLZCNTDrm
    0U,	// VPLZCNTDrmb
    3284U,	// VPLZCNTDrmbk
    4244U,	// VPLZCNTDrmbkz
    345U,	// VPLZCNTDrmk
    269U,	// VPLZCNTDrmkz
    32U,	// VPLZCNTDrr
    345U,	// VPLZCNTDrrk
    269U,	// VPLZCNTDrrkz
    0U,	// VPLZCNTQrm
    0U,	// VPLZCNTQrmb
    3284U,	// VPLZCNTQrmbk
    4244U,	// VPLZCNTQrmbkz
    345U,	// VPLZCNTQrmk
    269U,	// VPLZCNTQrmkz
    32U,	// VPLZCNTQrr
    345U,	// VPLZCNTQrrk
    269U,	// VPLZCNTQrrkz
    4U,	// VPMACSDDrm
    4U,	// VPMACSDDrr
    4U,	// VPMACSDQHrm
    4U,	// VPMACSDQHrr
    4U,	// VPMACSDQLrm
    4U,	// VPMACSDQLrr
    4U,	// VPMACSSDDrm
    4U,	// VPMACSSDDrr
    4U,	// VPMACSSDQHrm
    4U,	// VPMACSSDQHrr
    4U,	// VPMACSSDQLrm
    4U,	// VPMACSSDQLrr
    4U,	// VPMACSSWDrm
    4U,	// VPMACSSWDrr
    4U,	// VPMACSSWWrm
    4U,	// VPMACSSWWrr
    4U,	// VPMACSWDrm
    4U,	// VPMACSWDrr
    4U,	// VPMACSWWrm
    4U,	// VPMACSWWrr
    4U,	// VPMADCSSWDrm
    4U,	// VPMADCSSWDrr
    4U,	// VPMADCSWDrm
    4U,	// VPMADCSWDrr
    0U,	// VPMADDUBSWrm128
    0U,	// VPMADDUBSWrm256
    0U,	// VPMADDUBSWrr128
    0U,	// VPMADDUBSWrr256
    0U,	// VPMADDWDYrm
    0U,	// VPMADDWDYrr
    0U,	// VPMADDWDrm
    0U,	// VPMADDWDrr
    1U,	// VPMASKMOVDYmr
    0U,	// VPMASKMOVDYrm
    1U,	// VPMASKMOVDmr
    0U,	// VPMASKMOVDrm
    1U,	// VPMASKMOVQYmr
    0U,	// VPMASKMOVQYrm
    1U,	// VPMASKMOVQmr
    0U,	// VPMASKMOVQrm
    0U,	// VPMAXSBYrm
    0U,	// VPMAXSBYrr
    0U,	// VPMAXSBrm
    0U,	// VPMAXSBrr
    0U,	// VPMAXSDYrm
    0U,	// VPMAXSDYrr
    0U,	// VPMAXSDZrm
    4U,	// VPMAXSDZrmb
    1U,	// VPMAXSDZrmbk
    10244U,	// VPMAXSDZrmbkz
    0U,	// VPMAXSDZrmk
    4244U,	// VPMAXSDZrmkz
    0U,	// VPMAXSDZrr
    345U,	// VPMAXSDZrrk
    4244U,	// VPMAXSDZrrkz
    0U,	// VPMAXSDrm
    0U,	// VPMAXSDrr
    0U,	// VPMAXSQZrm
    4U,	// VPMAXSQZrmb
    1U,	// VPMAXSQZrmbk
    10244U,	// VPMAXSQZrmbkz
    0U,	// VPMAXSQZrmk
    4244U,	// VPMAXSQZrmkz
    0U,	// VPMAXSQZrr
    345U,	// VPMAXSQZrrk
    4244U,	// VPMAXSQZrrkz
    0U,	// VPMAXSWYrm
    0U,	// VPMAXSWYrr
    0U,	// VPMAXSWrm
    0U,	// VPMAXSWrr
    0U,	// VPMAXUBYrm
    0U,	// VPMAXUBYrr
    0U,	// VPMAXUBrm
    0U,	// VPMAXUBrr
    0U,	// VPMAXUDYrm
    0U,	// VPMAXUDYrr
    0U,	// VPMAXUDZrm
    4U,	// VPMAXUDZrmb
    1U,	// VPMAXUDZrmbk
    10244U,	// VPMAXUDZrmbkz
    0U,	// VPMAXUDZrmk
    4244U,	// VPMAXUDZrmkz
    0U,	// VPMAXUDZrr
    345U,	// VPMAXUDZrrk
    4244U,	// VPMAXUDZrrkz
    0U,	// VPMAXUDrm
    0U,	// VPMAXUDrr
    0U,	// VPMAXUQZrm
    4U,	// VPMAXUQZrmb
    1U,	// VPMAXUQZrmbk
    10244U,	// VPMAXUQZrmbkz
    0U,	// VPMAXUQZrmk
    4244U,	// VPMAXUQZrmkz
    0U,	// VPMAXUQZrr
    345U,	// VPMAXUQZrrk
    4244U,	// VPMAXUQZrrkz
    0U,	// VPMAXUWYrm
    0U,	// VPMAXUWYrr
    0U,	// VPMAXUWrm
    0U,	// VPMAXUWrr
    0U,	// VPMINSBYrm
    0U,	// VPMINSBYrr
    0U,	// VPMINSBrm
    0U,	// VPMINSBrr
    0U,	// VPMINSDYrm
    0U,	// VPMINSDYrr
    0U,	// VPMINSDZrm
    4U,	// VPMINSDZrmb
    1U,	// VPMINSDZrmbk
    10244U,	// VPMINSDZrmbkz
    0U,	// VPMINSDZrmk
    4244U,	// VPMINSDZrmkz
    0U,	// VPMINSDZrr
    345U,	// VPMINSDZrrk
    4244U,	// VPMINSDZrrkz
    0U,	// VPMINSDrm
    0U,	// VPMINSDrr
    0U,	// VPMINSQZrm
    4U,	// VPMINSQZrmb
    1U,	// VPMINSQZrmbk
    10244U,	// VPMINSQZrmbkz
    0U,	// VPMINSQZrmk
    4244U,	// VPMINSQZrmkz
    0U,	// VPMINSQZrr
    345U,	// VPMINSQZrrk
    4244U,	// VPMINSQZrrkz
    0U,	// VPMINSWYrm
    0U,	// VPMINSWYrr
    0U,	// VPMINSWrm
    0U,	// VPMINSWrr
    0U,	// VPMINUBYrm
    0U,	// VPMINUBYrr
    0U,	// VPMINUBrm
    0U,	// VPMINUBrr
    0U,	// VPMINUDYrm
    0U,	// VPMINUDYrr
    0U,	// VPMINUDZrm
    4U,	// VPMINUDZrmb
    1U,	// VPMINUDZrmbk
    10244U,	// VPMINUDZrmbkz
    0U,	// VPMINUDZrmk
    4244U,	// VPMINUDZrmkz
    0U,	// VPMINUDZrr
    345U,	// VPMINUDZrrk
    4244U,	// VPMINUDZrrkz
    0U,	// VPMINUDrm
    0U,	// VPMINUDrr
    0U,	// VPMINUQZrm
    4U,	// VPMINUQZrmb
    1U,	// VPMINUQZrmbk
    10244U,	// VPMINUQZrmbkz
    0U,	// VPMINUQZrmk
    4244U,	// VPMINUQZrmkz
    0U,	// VPMINUQZrr
    345U,	// VPMINUQZrrk
    4244U,	// VPMINUQZrrkz
    0U,	// VPMINUWYrm
    0U,	// VPMINUWYrr
    0U,	// VPMINUWrm
    0U,	// VPMINUWrr
    0U,	// VPMOVDBmr
    29U,	// VPMOVDBmrk
    0U,	// VPMOVDBrr
    333U,	// VPMOVDBrrk
    269U,	// VPMOVDBrrkz
    0U,	// VPMOVDWmr
    29U,	// VPMOVDWmrk
    0U,	// VPMOVDWrr
    333U,	// VPMOVDWrrk
    269U,	// VPMOVDWrrkz
    0U,	// VPMOVMSKBYrr
    0U,	// VPMOVMSKBrr
    0U,	// VPMOVQBmr
    29U,	// VPMOVQBmrk
    0U,	// VPMOVQBrr
    333U,	// VPMOVQBrrk
    269U,	// VPMOVQBrrkz
    0U,	// VPMOVQDmr
    29U,	// VPMOVQDmrk
    0U,	// VPMOVQDrr
    333U,	// VPMOVQDrrk
    269U,	// VPMOVQDrrkz
    0U,	// VPMOVQWmr
    29U,	// VPMOVQWmrk
    0U,	// VPMOVQWrr
    333U,	// VPMOVQWrrk
    269U,	// VPMOVQWrrkz
    0U,	// VPMOVSDBmr
    29U,	// VPMOVSDBmrk
    0U,	// VPMOVSDBrr
    333U,	// VPMOVSDBrrk
    269U,	// VPMOVSDBrrkz
    0U,	// VPMOVSDWmr
    29U,	// VPMOVSDWmrk
    0U,	// VPMOVSDWrr
    333U,	// VPMOVSDWrrk
    269U,	// VPMOVSDWrrkz
    0U,	// VPMOVSQBmr
    29U,	// VPMOVSQBmrk
    0U,	// VPMOVSQBrr
    333U,	// VPMOVSQBrrk
    269U,	// VPMOVSQBrrkz
    0U,	// VPMOVSQDmr
    29U,	// VPMOVSQDmrk
    0U,	// VPMOVSQDrr
    333U,	// VPMOVSQDrrk
    269U,	// VPMOVSQDrrkz
    0U,	// VPMOVSQWmr
    29U,	// VPMOVSQWmrk
    0U,	// VPMOVSQWrr
    333U,	// VPMOVSQWrrk
    269U,	// VPMOVSQWrrkz
    0U,	// VPMOVSXBDYrm
    0U,	// VPMOVSXBDYrr
    0U,	// VPMOVSXBDZrm
    525U,	// VPMOVSXBDZrmk
    269U,	// VPMOVSXBDZrmkz
    0U,	// VPMOVSXBDZrr
    525U,	// VPMOVSXBDZrrk
    269U,	// VPMOVSXBDZrrkz
    0U,	// VPMOVSXBDrm
    0U,	// VPMOVSXBDrr
    0U,	// VPMOVSXBQYrm
    0U,	// VPMOVSXBQYrr
    0U,	// VPMOVSXBQZrm
    525U,	// VPMOVSXBQZrmk
    269U,	// VPMOVSXBQZrmkz
    0U,	// VPMOVSXBQZrr
    525U,	// VPMOVSXBQZrrk
    269U,	// VPMOVSXBQZrrkz
    0U,	// VPMOVSXBQrm
    0U,	// VPMOVSXBQrr
    0U,	// VPMOVSXBWYrm
    0U,	// VPMOVSXBWYrr
    0U,	// VPMOVSXBWrm
    0U,	// VPMOVSXBWrr
    0U,	// VPMOVSXDQYrm
    0U,	// VPMOVSXDQYrr
    0U,	// VPMOVSXDQZrm
    525U,	// VPMOVSXDQZrmk
    269U,	// VPMOVSXDQZrmkz
    0U,	// VPMOVSXDQZrr
    525U,	// VPMOVSXDQZrrk
    269U,	// VPMOVSXDQZrrkz
    0U,	// VPMOVSXDQrm
    0U,	// VPMOVSXDQrr
    0U,	// VPMOVSXWDYrm
    0U,	// VPMOVSXWDYrr
    0U,	// VPMOVSXWDZrm
    525U,	// VPMOVSXWDZrmk
    269U,	// VPMOVSXWDZrmkz
    0U,	// VPMOVSXWDZrr
    525U,	// VPMOVSXWDZrrk
    269U,	// VPMOVSXWDZrrkz
    0U,	// VPMOVSXWDrm
    0U,	// VPMOVSXWDrr
    0U,	// VPMOVSXWQYrm
    0U,	// VPMOVSXWQYrr
    0U,	// VPMOVSXWQZrm
    525U,	// VPMOVSXWQZrmk
    269U,	// VPMOVSXWQZrmkz
    0U,	// VPMOVSXWQZrr
    525U,	// VPMOVSXWQZrrk
    269U,	// VPMOVSXWQZrrkz
    0U,	// VPMOVSXWQrm
    0U,	// VPMOVSXWQrr
    0U,	// VPMOVUSDBmr
    29U,	// VPMOVUSDBmrk
    0U,	// VPMOVUSDBrr
    333U,	// VPMOVUSDBrrk
    269U,	// VPMOVUSDBrrkz
    0U,	// VPMOVUSDWmr
    29U,	// VPMOVUSDWmrk
    0U,	// VPMOVUSDWrr
    333U,	// VPMOVUSDWrrk
    269U,	// VPMOVUSDWrrkz
    0U,	// VPMOVUSQBmr
    29U,	// VPMOVUSQBmrk
    0U,	// VPMOVUSQBrr
    333U,	// VPMOVUSQBrrk
    269U,	// VPMOVUSQBrrkz
    0U,	// VPMOVUSQDmr
    29U,	// VPMOVUSQDmrk
    0U,	// VPMOVUSQDrr
    333U,	// VPMOVUSQDrrk
    269U,	// VPMOVUSQDrrkz
    0U,	// VPMOVUSQWmr
    29U,	// VPMOVUSQWmrk
    0U,	// VPMOVUSQWrr
    333U,	// VPMOVUSQWrrk
    269U,	// VPMOVUSQWrrkz
    0U,	// VPMOVZXBDYrm
    0U,	// VPMOVZXBDYrr
    0U,	// VPMOVZXBDZrm
    525U,	// VPMOVZXBDZrmk
    269U,	// VPMOVZXBDZrmkz
    0U,	// VPMOVZXBDZrr
    525U,	// VPMOVZXBDZrrk
    269U,	// VPMOVZXBDZrrkz
    0U,	// VPMOVZXBDrm
    0U,	// VPMOVZXBDrr
    0U,	// VPMOVZXBQYrm
    0U,	// VPMOVZXBQYrr
    0U,	// VPMOVZXBQZrm
    525U,	// VPMOVZXBQZrmk
    269U,	// VPMOVZXBQZrmkz
    0U,	// VPMOVZXBQZrr
    525U,	// VPMOVZXBQZrrk
    269U,	// VPMOVZXBQZrrkz
    0U,	// VPMOVZXBQrm
    0U,	// VPMOVZXBQrr
    0U,	// VPMOVZXBWYrm
    0U,	// VPMOVZXBWYrr
    0U,	// VPMOVZXBWrm
    0U,	// VPMOVZXBWrr
    0U,	// VPMOVZXDQYrm
    0U,	// VPMOVZXDQYrr
    0U,	// VPMOVZXDQZrm
    525U,	// VPMOVZXDQZrmk
    269U,	// VPMOVZXDQZrmkz
    0U,	// VPMOVZXDQZrr
    525U,	// VPMOVZXDQZrrk
    269U,	// VPMOVZXDQZrrkz
    0U,	// VPMOVZXDQrm
    0U,	// VPMOVZXDQrr
    0U,	// VPMOVZXWDYrm
    0U,	// VPMOVZXWDYrr
    0U,	// VPMOVZXWDZrm
    525U,	// VPMOVZXWDZrmk
    269U,	// VPMOVZXWDZrmkz
    0U,	// VPMOVZXWDZrr
    525U,	// VPMOVZXWDZrrk
    269U,	// VPMOVZXWDZrrkz
    0U,	// VPMOVZXWDrm
    0U,	// VPMOVZXWDrr
    0U,	// VPMOVZXWQYrm
    0U,	// VPMOVZXWQYrr
    0U,	// VPMOVZXWQZrm
    525U,	// VPMOVZXWQZrmk
    269U,	// VPMOVZXWQZrmkz
    0U,	// VPMOVZXWQZrr
    525U,	// VPMOVZXWQZrrk
    269U,	// VPMOVZXWQZrrkz
    0U,	// VPMOVZXWQrm
    0U,	// VPMOVZXWQrr
    0U,	// VPMULDQYrm
    0U,	// VPMULDQYrr
    0U,	// VPMULDQZrm
    4U,	// VPMULDQZrmb
    2052U,	// VPMULDQZrmbk
    10244U,	// VPMULDQZrmbkz
    3220U,	// VPMULDQZrmk
    4244U,	// VPMULDQZrmkz
    0U,	// VPMULDQZrr
    3220U,	// VPMULDQZrrk
    4244U,	// VPMULDQZrrkz
    0U,	// VPMULDQrm
    0U,	// VPMULDQrr
    0U,	// VPMULHRSWrm128
    0U,	// VPMULHRSWrm256
    0U,	// VPMULHRSWrr128
    0U,	// VPMULHRSWrr256
    0U,	// VPMULHUWYrm
    0U,	// VPMULHUWYrr
    0U,	// VPMULHUWrm
    0U,	// VPMULHUWrr
    0U,	// VPMULHWYrm
    0U,	// VPMULHWYrr
    0U,	// VPMULHWrm
    0U,	// VPMULHWrr
    0U,	// VPMULLDYrm
    0U,	// VPMULLDYrr
    0U,	// VPMULLDZrm
    4U,	// VPMULLDZrmb
    1U,	// VPMULLDZrmbk
    10244U,	// VPMULLDZrmbkz
    0U,	// VPMULLDZrmk
    4244U,	// VPMULLDZrmkz
    0U,	// VPMULLDZrr
    345U,	// VPMULLDZrrk
    4244U,	// VPMULLDZrrkz
    0U,	// VPMULLDrm
    0U,	// VPMULLDrr
    0U,	// VPMULLWYrm
    0U,	// VPMULLWYrr
    0U,	// VPMULLWrm
    0U,	// VPMULLWrr
    0U,	// VPMULUDQYrm
    0U,	// VPMULUDQYrr
    0U,	// VPMULUDQZrm
    4U,	// VPMULUDQZrmb
    2052U,	// VPMULUDQZrmbk
    10244U,	// VPMULUDQZrmbkz
    3220U,	// VPMULUDQZrmk
    4244U,	// VPMULUDQZrmkz
    0U,	// VPMULUDQZrr
    3220U,	// VPMULUDQZrrk
    4244U,	// VPMULUDQZrrkz
    0U,	// VPMULUDQrm
    0U,	// VPMULUDQrr
    0U,	// VPORDZrm
    4U,	// VPORDZrmb
    1U,	// VPORDZrmbk
    10244U,	// VPORDZrmbkz
    0U,	// VPORDZrmk
    4244U,	// VPORDZrmkz
    0U,	// VPORDZrr
    345U,	// VPORDZrrk
    4244U,	// VPORDZrrkz
    0U,	// VPORQZrm
    4U,	// VPORQZrmb
    1U,	// VPORQZrmbk
    10244U,	// VPORQZrmbkz
    0U,	// VPORQZrmk
    4244U,	// VPORQZrmkz
    0U,	// VPORQZrr
    345U,	// VPORQZrrk
    4244U,	// VPORQZrrkz
    0U,	// VPORYrm
    0U,	// VPORYrr
    0U,	// VPORrm
    0U,	// VPORrr
    4U,	// VPPERMmr
    4U,	// VPPERMrm
    4U,	// VPPERMrr
    0U,	// VPROTBmi
    0U,	// VPROTBmr
    0U,	// VPROTBri
    0U,	// VPROTBrm
    0U,	// VPROTBrr
    0U,	// VPROTDmi
    0U,	// VPROTDmr
    0U,	// VPROTDri
    0U,	// VPROTDrm
    0U,	// VPROTDrr
    0U,	// VPROTQmi
    0U,	// VPROTQmr
    0U,	// VPROTQri
    0U,	// VPROTQrm
    0U,	// VPROTQrr
    0U,	// VPROTWmi
    0U,	// VPROTWmr
    0U,	// VPROTWri
    0U,	// VPROTWrm
    0U,	// VPROTWrr
    0U,	// VPSADBWYrm
    0U,	// VPSADBWYrr
    0U,	// VPSADBWrm
    0U,	// VPSADBWrr
    37U,	// VPSCATTERDDZmr
    37U,	// VPSCATTERDQZmr
    37U,	// VPSCATTERQDZmr
    37U,	// VPSCATTERQQZmr
    0U,	// VPSHABmr
    0U,	// VPSHABrm
    0U,	// VPSHABrr
    0U,	// VPSHADmr
    0U,	// VPSHADrm
    0U,	// VPSHADrr
    0U,	// VPSHAQmr
    0U,	// VPSHAQrm
    0U,	// VPSHAQrr
    0U,	// VPSHAWmr
    0U,	// VPSHAWrm
    0U,	// VPSHAWrr
    0U,	// VPSHLBmr
    0U,	// VPSHLBrm
    0U,	// VPSHLBrr
    0U,	// VPSHLDmr
    0U,	// VPSHLDrm
    0U,	// VPSHLDrr
    0U,	// VPSHLQmr
    0U,	// VPSHLQrm
    0U,	// VPSHLQrr
    0U,	// VPSHLWmr
    0U,	// VPSHLWrm
    0U,	// VPSHLWrr
    0U,	// VPSHUFBYrm
    0U,	// VPSHUFBYrr
    0U,	// VPSHUFBrm
    0U,	// VPSHUFBrr
    0U,	// VPSHUFDYmi
    0U,	// VPSHUFDYri
    0U,	// VPSHUFDZmi
    0U,	// VPSHUFDZri
    0U,	// VPSHUFDmi
    0U,	// VPSHUFDri
    0U,	// VPSHUFHWYmi
    0U,	// VPSHUFHWYri
    0U,	// VPSHUFHWmi
    0U,	// VPSHUFHWri
    0U,	// VPSHUFLWYmi
    0U,	// VPSHUFLWYri
    0U,	// VPSHUFLWmi
    0U,	// VPSHUFLWri
    0U,	// VPSIGNBYrm
    0U,	// VPSIGNBYrr
    0U,	// VPSIGNBrm
    0U,	// VPSIGNBrr
    0U,	// VPSIGNDYrm
    0U,	// VPSIGNDYrr
    0U,	// VPSIGNDrm
    0U,	// VPSIGNDrr
    0U,	// VPSIGNWYrm
    0U,	// VPSIGNWYrr
    0U,	// VPSIGNWrm
    0U,	// VPSIGNWrr
    0U,	// VPSLLDQYri
    0U,	// VPSLLDQri
    0U,	// VPSLLDYri
    0U,	// VPSLLDYrm
    0U,	// VPSLLDYrr
    0U,	// VPSLLDZmi
    3220U,	// VPSLLDZmik
    0U,	// VPSLLDZri
    3220U,	// VPSLLDZrik
    0U,	// VPSLLDZrm
    3220U,	// VPSLLDZrmk
    0U,	// VPSLLDZrr
    3220U,	// VPSLLDZrrk
    0U,	// VPSLLDri
    0U,	// VPSLLDrm
    0U,	// VPSLLDrr
    0U,	// VPSLLQYri
    0U,	// VPSLLQYrm
    0U,	// VPSLLQYrr
    0U,	// VPSLLQZmi
    3220U,	// VPSLLQZmik
    0U,	// VPSLLQZri
    3220U,	// VPSLLQZrik
    0U,	// VPSLLQZrm
    3220U,	// VPSLLQZrmk
    0U,	// VPSLLQZrr
    3220U,	// VPSLLQZrrk
    0U,	// VPSLLQri
    0U,	// VPSLLQrm
    0U,	// VPSLLQrr
    0U,	// VPSLLVDYrm
    0U,	// VPSLLVDYrr
    0U,	// VPSLLVDZrm
    0U,	// VPSLLVDZrr
    0U,	// VPSLLVDrm
    0U,	// VPSLLVDrr
    0U,	// VPSLLVQYrm
    0U,	// VPSLLVQYrr
    0U,	// VPSLLVQZrm
    0U,	// VPSLLVQZrr
    0U,	// VPSLLVQrm
    0U,	// VPSLLVQrr
    0U,	// VPSLLWYri
    0U,	// VPSLLWYrm
    0U,	// VPSLLWYrr
    0U,	// VPSLLWri
    0U,	// VPSLLWrm
    0U,	// VPSLLWrr
    0U,	// VPSRADYri
    0U,	// VPSRADYrm
    0U,	// VPSRADYrr
    0U,	// VPSRADZmi
    3220U,	// VPSRADZmik
    0U,	// VPSRADZri
    3220U,	// VPSRADZrik
    0U,	// VPSRADZrm
    3220U,	// VPSRADZrmk
    0U,	// VPSRADZrr
    3220U,	// VPSRADZrrk
    0U,	// VPSRADri
    0U,	// VPSRADrm
    0U,	// VPSRADrr
    0U,	// VPSRAQZmi
    3220U,	// VPSRAQZmik
    0U,	// VPSRAQZri
    3220U,	// VPSRAQZrik
    0U,	// VPSRAQZrm
    3220U,	// VPSRAQZrmk
    0U,	// VPSRAQZrr
    3220U,	// VPSRAQZrrk
    0U,	// VPSRAVDYrm
    0U,	// VPSRAVDYrr
    0U,	// VPSRAVDZrm
    0U,	// VPSRAVDZrr
    0U,	// VPSRAVDrm
    0U,	// VPSRAVDrr
    0U,	// VPSRAVQZrm
    0U,	// VPSRAVQZrr
    0U,	// VPSRAWYri
    0U,	// VPSRAWYrm
    0U,	// VPSRAWYrr
    0U,	// VPSRAWri
    0U,	// VPSRAWrm
    0U,	// VPSRAWrr
    0U,	// VPSRLDQYri
    0U,	// VPSRLDQri
    0U,	// VPSRLDYri
    0U,	// VPSRLDYrm
    0U,	// VPSRLDYrr
    0U,	// VPSRLDZmi
    3220U,	// VPSRLDZmik
    0U,	// VPSRLDZri
    3220U,	// VPSRLDZrik
    0U,	// VPSRLDZrm
    3220U,	// VPSRLDZrmk
    0U,	// VPSRLDZrr
    3220U,	// VPSRLDZrrk
    0U,	// VPSRLDri
    0U,	// VPSRLDrm
    0U,	// VPSRLDrr
    0U,	// VPSRLQYri
    0U,	// VPSRLQYrm
    0U,	// VPSRLQYrr
    0U,	// VPSRLQZmi
    3220U,	// VPSRLQZmik
    0U,	// VPSRLQZri
    3220U,	// VPSRLQZrik
    0U,	// VPSRLQZrm
    3220U,	// VPSRLQZrmk
    0U,	// VPSRLQZrr
    3220U,	// VPSRLQZrrk
    0U,	// VPSRLQri
    0U,	// VPSRLQrm
    0U,	// VPSRLQrr
    0U,	// VPSRLVDYrm
    0U,	// VPSRLVDYrr
    0U,	// VPSRLVDZrm
    0U,	// VPSRLVDZrr
    0U,	// VPSRLVDrm
    0U,	// VPSRLVDrr
    0U,	// VPSRLVQYrm
    0U,	// VPSRLVQYrr
    0U,	// VPSRLVQZrm
    0U,	// VPSRLVQZrr
    0U,	// VPSRLVQrm
    0U,	// VPSRLVQrr
    0U,	// VPSRLWYri
    0U,	// VPSRLWYrm
    0U,	// VPSRLWYrr
    0U,	// VPSRLWri
    0U,	// VPSRLWrm
    0U,	// VPSRLWrr
    0U,	// VPSUBBYrm
    0U,	// VPSUBBYrr
    0U,	// VPSUBBrm
    0U,	// VPSUBBrr
    0U,	// VPSUBDYrm
    0U,	// VPSUBDYrr
    0U,	// VPSUBDZrm
    4U,	// VPSUBDZrmb
    1U,	// VPSUBDZrmbk
    10244U,	// VPSUBDZrmbkz
    0U,	// VPSUBDZrmk
    4244U,	// VPSUBDZrmkz
    0U,	// VPSUBDZrr
    345U,	// VPSUBDZrrk
    4244U,	// VPSUBDZrrkz
    0U,	// VPSUBDrm
    0U,	// VPSUBDrr
    0U,	// VPSUBQYrm
    0U,	// VPSUBQYrr
    0U,	// VPSUBQZrm
    4U,	// VPSUBQZrmb
    1U,	// VPSUBQZrmbk
    10244U,	// VPSUBQZrmbkz
    0U,	// VPSUBQZrmk
    4244U,	// VPSUBQZrmkz
    0U,	// VPSUBQZrr
    345U,	// VPSUBQZrrk
    4244U,	// VPSUBQZrrkz
    0U,	// VPSUBQrm
    0U,	// VPSUBQrr
    0U,	// VPSUBSBYrm
    0U,	// VPSUBSBYrr
    0U,	// VPSUBSBrm
    0U,	// VPSUBSBrr
    0U,	// VPSUBSWYrm
    0U,	// VPSUBSWYrr
    0U,	// VPSUBSWrm
    0U,	// VPSUBSWrr
    0U,	// VPSUBUSBYrm
    0U,	// VPSUBUSBYrr
    0U,	// VPSUBUSBrm
    0U,	// VPSUBUSBrr
    0U,	// VPSUBUSWYrm
    0U,	// VPSUBUSWYrr
    0U,	// VPSUBUSWrm
    0U,	// VPSUBUSWrr
    0U,	// VPSUBWYrm
    0U,	// VPSUBWYrr
    0U,	// VPSUBWrm
    0U,	// VPSUBWrr
    0U,	// VPTESTMDZrm
    0U,	// VPTESTMDZrr
    0U,	// VPTESTMQZrm
    0U,	// VPTESTMQZrr
    0U,	// VPTESTNMDZrm
    0U,	// VPTESTNMDZrr
    0U,	// VPTESTNMQZrm
    0U,	// VPTESTNMQZrr
    0U,	// VPTESTYrm
    0U,	// VPTESTYrr
    0U,	// VPTESTrm
    0U,	// VPTESTrr
    0U,	// VPUNPCKHBWYrm
    0U,	// VPUNPCKHBWYrr
    0U,	// VPUNPCKHBWrm
    0U,	// VPUNPCKHBWrr
    0U,	// VPUNPCKHDQYrm
    0U,	// VPUNPCKHDQYrr
    0U,	// VPUNPCKHDQZrm
    0U,	// VPUNPCKHDQZrr
    0U,	// VPUNPCKHDQrm
    0U,	// VPUNPCKHDQrr
    0U,	// VPUNPCKHQDQYrm
    0U,	// VPUNPCKHQDQYrr
    0U,	// VPUNPCKHQDQZrm
    0U,	// VPUNPCKHQDQZrr
    0U,	// VPUNPCKHQDQrm
    0U,	// VPUNPCKHQDQrr
    0U,	// VPUNPCKHWDYrm
    0U,	// VPUNPCKHWDYrr
    0U,	// VPUNPCKHWDrm
    0U,	// VPUNPCKHWDrr
    0U,	// VPUNPCKLBWYrm
    0U,	// VPUNPCKLBWYrr
    0U,	// VPUNPCKLBWrm
    0U,	// VPUNPCKLBWrr
    0U,	// VPUNPCKLDQYrm
    0U,	// VPUNPCKLDQYrr
    0U,	// VPUNPCKLDQZrm
    0U,	// VPUNPCKLDQZrr
    0U,	// VPUNPCKLDQrm
    0U,	// VPUNPCKLDQrr
    0U,	// VPUNPCKLQDQYrm
    0U,	// VPUNPCKLQDQYrr
    0U,	// VPUNPCKLQDQZrm
    0U,	// VPUNPCKLQDQZrr
    0U,	// VPUNPCKLQDQrm
    0U,	// VPUNPCKLQDQrr
    0U,	// VPUNPCKLWDYrm
    0U,	// VPUNPCKLWDYrr
    0U,	// VPUNPCKLWDrm
    0U,	// VPUNPCKLWDrr
    0U,	// VPXORDZrm
    4U,	// VPXORDZrmb
    1U,	// VPXORDZrmbk
    10244U,	// VPXORDZrmbkz
    0U,	// VPXORDZrmk
    4244U,	// VPXORDZrmkz
    0U,	// VPXORDZrr
    345U,	// VPXORDZrrk
    4244U,	// VPXORDZrrkz
    0U,	// VPXORQZrm
    4U,	// VPXORQZrmb
    1U,	// VPXORQZrmbk
    10244U,	// VPXORQZrmbkz
    0U,	// VPXORQZrmk
    4244U,	// VPXORQZrmkz
    0U,	// VPXORQZrr
    345U,	// VPXORQZrrk
    4244U,	// VPXORQZrrkz
    0U,	// VPXORYrm
    0U,	// VPXORYrr
    0U,	// VPXORrm
    0U,	// VPXORrr
    0U,	// VRCP14PDZm
    0U,	// VRCP14PDZr
    0U,	// VRCP14PSZm
    0U,	// VRCP14PSZr
    4U,	// VRCP14SDrm
    0U,	// VRCP14SDrr
    4U,	// VRCP14SSrm
    0U,	// VRCP14SSrr
    0U,	// VRCP28PDZm
    0U,	// VRCP28PDZr
    0U,	// VRCP28PDZrb
    0U,	// VRCP28PSZm
    0U,	// VRCP28PSZr
    0U,	// VRCP28PSZrb
    4U,	// VRCP28SDrm
    0U,	// VRCP28SDrr
    0U,	// VRCP28SDrrb
    4U,	// VRCP28SSrm
    0U,	// VRCP28SSrr
    0U,	// VRCP28SSrrb
    0U,	// VRCPPSYm
    0U,	// VRCPPSYm_Int
    0U,	// VRCPPSYr
    0U,	// VRCPPSYr_Int
    0U,	// VRCPPSm
    0U,	// VRCPPSm_Int
    0U,	// VRCPPSr
    0U,	// VRCPPSr_Int
    4U,	// VRCPSSm
    4U,	// VRCPSSm_Int
    0U,	// VRCPSSr
    0U,	// VRNDSCALEPDZm
    0U,	// VRNDSCALEPDZr
    0U,	// VRNDSCALEPSZm
    0U,	// VRNDSCALEPSZr
    4U,	// VRNDSCALESDm
    0U,	// VRNDSCALESDr
    4U,	// VRNDSCALESSm
    0U,	// VRNDSCALESSr
    0U,	// VROUNDPDm
    0U,	// VROUNDPDr
    0U,	// VROUNDPSm
    0U,	// VROUNDPSr
    4U,	// VROUNDSDm
    4U,	// VROUNDSDr
    4U,	// VROUNDSDr_Int
    4U,	// VROUNDSSm
    4U,	// VROUNDSSr
    4U,	// VROUNDSSr_Int
    0U,	// VROUNDYPDm
    0U,	// VROUNDYPDr
    0U,	// VROUNDYPSm
    0U,	// VROUNDYPSr
    0U,	// VRSQRT14PDZm
    0U,	// VRSQRT14PDZr
    0U,	// VRSQRT14PSZm
    0U,	// VRSQRT14PSZr
    4U,	// VRSQRT14SDrm
    0U,	// VRSQRT14SDrr
    4U,	// VRSQRT14SSrm
    0U,	// VRSQRT14SSrr
    0U,	// VRSQRT28PDZm
    0U,	// VRSQRT28PDZr
    0U,	// VRSQRT28PDZrb
    0U,	// VRSQRT28PSZm
    0U,	// VRSQRT28PSZr
    0U,	// VRSQRT28PSZrb
    4U,	// VRSQRT28SDrm
    0U,	// VRSQRT28SDrr
    0U,	// VRSQRT28SDrrb
    4U,	// VRSQRT28SSrm
    0U,	// VRSQRT28SSrr
    0U,	// VRSQRT28SSrrb
    0U,	// VRSQRTPSYm
    0U,	// VRSQRTPSYm_Int
    0U,	// VRSQRTPSYr
    0U,	// VRSQRTPSYr_Int
    0U,	// VRSQRTPSm
    0U,	// VRSQRTPSm_Int
    0U,	// VRSQRTPSr
    0U,	// VRSQRTPSr_Int
    4U,	// VRSQRTSSm
    4U,	// VRSQRTSSm_Int
    0U,	// VRSQRTSSr
    37U,	// VSCATTERDPDZmr
    37U,	// VSCATTERDPSZmr
    0U,	// VSCATTERPF0DPDm
    0U,	// VSCATTERPF0DPSm
    0U,	// VSCATTERPF0QPDm
    0U,	// VSCATTERPF0QPSm
    0U,	// VSCATTERPF1DPDm
    0U,	// VSCATTERPF1DPSm
    0U,	// VSCATTERPF1QPDm
    0U,	// VSCATTERPF1QPSm
    37U,	// VSCATTERQPDZmr
    37U,	// VSCATTERQPSZmr
    0U,	// VSHUFPDYrmi
    4U,	// VSHUFPDYrri
    0U,	// VSHUFPDZrmi
    4U,	// VSHUFPDZrri
    4U,	// VSHUFPDrmi
    4U,	// VSHUFPDrri
    0U,	// VSHUFPSYrmi
    4U,	// VSHUFPSYrri
    0U,	// VSHUFPSZrmi
    4U,	// VSHUFPSZrri
    4U,	// VSHUFPSrmi
    4U,	// VSHUFPSrri
    0U,	// VSQRTPDYm
    0U,	// VSQRTPDYr
    0U,	// VSQRTPDZrm
    0U,	// VSQRTPDZrr
    0U,	// VSQRTPDm
    0U,	// VSQRTPDr
    0U,	// VSQRTPSYm
    0U,	// VSQRTPSYr
    0U,	// VSQRTPSZrm
    0U,	// VSQRTPSZrr
    0U,	// VSQRTPSm
    0U,	// VSQRTPSr
    4U,	// VSQRTSDZm
    4U,	// VSQRTSDZm_Int
    0U,	// VSQRTSDZr
    0U,	// VSQRTSDZr_Int
    4U,	// VSQRTSDm
    4U,	// VSQRTSDm_Int
    0U,	// VSQRTSDr
    4U,	// VSQRTSSZm
    4U,	// VSQRTSSZm_Int
    0U,	// VSQRTSSZr
    0U,	// VSQRTSSZr_Int
    4U,	// VSQRTSSm
    4U,	// VSQRTSSm_Int
    0U,	// VSQRTSSr
    0U,	// VSTMXCSR
    0U,	// VSUBPDYrm
    0U,	// VSUBPDYrr
    0U,	// VSUBPDZrm
    4U,	// VSUBPDZrmb
    2052U,	// VSUBPDZrmbk
    10244U,	// VSUBPDZrmbkz
    3220U,	// VSUBPDZrmk
    4244U,	// VSUBPDZrmkz
    0U,	// VSUBPDZrr
    5268U,	// VSUBPDZrrk
    4244U,	// VSUBPDZrrkz
    0U,	// VSUBPDrm
    0U,	// VSUBPDrr
    0U,	// VSUBPSYrm
    0U,	// VSUBPSYrr
    0U,	// VSUBPSZrm
    4U,	// VSUBPSZrmb
    2052U,	// VSUBPSZrmbk
    10244U,	// VSUBPSZrmbkz
    3220U,	// VSUBPSZrmk
    4244U,	// VSUBPSZrmkz
    0U,	// VSUBPSZrr
    5268U,	// VSUBPSZrrk
    4244U,	// VSUBPSZrrkz
    0U,	// VSUBPSrm
    0U,	// VSUBPSrr
    4U,	// VSUBSDZrm
    0U,	// VSUBSDZrr
    4U,	// VSUBSDrm
    4U,	// VSUBSDrm_Int
    0U,	// VSUBSDrr
    0U,	// VSUBSDrr_Int
    4U,	// VSUBSSZrm
    0U,	// VSUBSSZrr
    4U,	// VSUBSSrm
    4U,	// VSUBSSrm_Int
    0U,	// VSUBSSrr
    0U,	// VSUBSSrr_Int
    0U,	// VTESTPDYrm
    0U,	// VTESTPDYrr
    0U,	// VTESTPDrm
    0U,	// VTESTPDrr
    0U,	// VTESTPSYrm
    0U,	// VTESTPSYrr
    0U,	// VTESTPSrm
    0U,	// VTESTPSrr
    0U,	// VUCOMISDZrm
    0U,	// VUCOMISDZrr
    0U,	// VUCOMISDrm
    0U,	// VUCOMISDrr
    0U,	// VUCOMISSZrm
    0U,	// VUCOMISSZrr
    0U,	// VUCOMISSrm
    0U,	// VUCOMISSrr
    0U,	// VUNPCKHPDYrm
    0U,	// VUNPCKHPDYrr
    0U,	// VUNPCKHPDZrm
    0U,	// VUNPCKHPDZrr
    0U,	// VUNPCKHPDrm
    0U,	// VUNPCKHPDrr
    0U,	// VUNPCKHPSYrm
    0U,	// VUNPCKHPSYrr
    0U,	// VUNPCKHPSZrm
    0U,	// VUNPCKHPSZrr
    0U,	// VUNPCKHPSrm
    0U,	// VUNPCKHPSrr
    0U,	// VUNPCKLPDYrm
    0U,	// VUNPCKLPDYrr
    0U,	// VUNPCKLPDZrm
    0U,	// VUNPCKLPDZrr
    0U,	// VUNPCKLPDrm
    0U,	// VUNPCKLPDrr
    0U,	// VUNPCKLPSYrm
    0U,	// VUNPCKLPSYrr
    0U,	// VUNPCKLPSZrm
    0U,	// VUNPCKLPSZrr
    0U,	// VUNPCKLPSrm
    0U,	// VUNPCKLPSrr
    0U,	// VXORPDYrm
    0U,	// VXORPDYrr
    0U,	// VXORPDrm
    0U,	// VXORPDrr
    0U,	// VXORPSYrm
    0U,	// VXORPSYrr
    0U,	// VXORPSrm
    0U,	// VXORPSrr
    0U,	// VZEROALL
    0U,	// VZEROUPPER
    0U,	// V_SET0
    0U,	// V_SETALLONES
    0U,	// W64ALLOCA
    0U,	// WAIT
    0U,	// WBINVD
    0U,	// WIN_ALLOCA
    0U,	// WIN_FTOL_32
    0U,	// WIN_FTOL_64
    0U,	// WRFSBASE
    0U,	// WRFSBASE64
    0U,	// WRGSBASE
    0U,	// WRGSBASE64
    0U,	// WRMSR
    0U,	// XABORT
    0U,	// XACQUIRE_PREFIX
    0U,	// XADD16rm
    0U,	// XADD16rr
    0U,	// XADD32rm
    0U,	// XADD32rr
    0U,	// XADD64rm
    0U,	// XADD64rr
    0U,	// XADD8rm
    0U,	// XADD8rr
    0U,	// XBEGIN
    0U,	// XBEGIN_4
    0U,	// XCHG16ar
    0U,	// XCHG16rm
    0U,	// XCHG16rr
    0U,	// XCHG32ar
    0U,	// XCHG32ar64
    0U,	// XCHG32rm
    0U,	// XCHG32rr
    0U,	// XCHG64ar
    0U,	// XCHG64rm
    0U,	// XCHG64rr
    1U,	// XCHG8rm
    0U,	// XCHG8rr
    0U,	// XCH_F
    0U,	// XCRYPTCBC
    0U,	// XCRYPTCFB
    0U,	// XCRYPTCTR
    0U,	// XCRYPTECB
    0U,	// XCRYPTOFB
    0U,	// XEND
    0U,	// XGETBV
    0U,	// XLAT
    0U,	// XOR16i16
    0U,	// XOR16mi
    0U,	// XOR16mi8
    0U,	// XOR16mr
    0U,	// XOR16ri
    0U,	// XOR16ri8
    0U,	// XOR16rm
    0U,	// XOR16rr
    0U,	// XOR16rr_REV
    0U,	// XOR32i32
    0U,	// XOR32mi
    0U,	// XOR32mi8
    0U,	// XOR32mr
    0U,	// XOR32ri
    0U,	// XOR32ri8
    0U,	// XOR32rm
    0U,	// XOR32rr
    0U,	// XOR32rr_REV
    0U,	// XOR64i32
    0U,	// XOR64mi32
    0U,	// XOR64mi8
    0U,	// XOR64mr
    0U,	// XOR64ri32
    0U,	// XOR64ri8
    0U,	// XOR64rm
    0U,	// XOR64rr
    0U,	// XOR64rr_REV
    0U,	// XOR8i8
    0U,	// XOR8mi
    0U,	// XOR8mr
    0U,	// XOR8ri
    0U,	// XOR8ri8
    0U,	// XOR8rm
    0U,	// XOR8rr
    0U,	// XOR8rr_REV
    0U,	// XORPDrm
    0U,	// XORPDrr
    0U,	// XORPSrm
    0U,	// XORPSrr
    0U,	// XRELEASE_PREFIX
    0U,	// XRSTOR
    0U,	// XRSTOR64
    0U,	// XSAVE
    0U,	// XSAVE64
    0U,	// XSAVEOPT
    0U,	// XSAVEOPT64
    0U,	// XSETBV
    0U,	// XSHA1
    0U,	// XSHA256
    0U,	// XSTORE
    0U,	// XTEST
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'i', '3', '2', 'x', '4', 32, 9, 0,
  /* 18 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'i', '6', '4', 'x', '4', 32, 9, 0,
  /* 36 */ 'k', 'a', 'n', 'd', 'b', 32, 9, 0,
  /* 44 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'd', 'b', 32, 9, 0,
  /* 56 */ 'v', 'p', 'm', 'o', 'v', 's', 'd', 'b', 32, 9, 0,
  /* 67 */ 'v', 'p', 'm', 'o', 'v', 'd', 'b', 32, 9, 0,
  /* 77 */ 'k', 'a', 'n', 'd', 'n', 'b', 32, 9, 0,
  /* 86 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'q', 'b', 32, 9, 0,
  /* 98 */ 'v', 'p', 'm', 'o', 'v', 's', 'q', 'b', 32, 9, 0,
  /* 109 */ 'v', 'p', 'm', 'o', 'v', 'q', 'b', 32, 9, 0,
  /* 119 */ 'k', 'o', 'r', 'b', 32, 9, 0,
  /* 126 */ 'k', 'x', 'n', 'o', 'r', 'b', 32, 9, 0,
  /* 135 */ 'k', 'x', 'o', 'r', 'b', 32, 9, 0,
  /* 143 */ 'k', 'n', 'o', 't', 'b', 32, 9, 0,
  /* 151 */ 'k', 'm', 'o', 'v', 'b', 32, 9, 0,
  /* 159 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'd', 32, 9, 0,
  /* 170 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'd', 32, 9, 0,
  /* 181 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'm', 'w', '2', 'd', 32, 9, 0,
  /* 199 */ 'v', 'p', 's', 'r', 'a', 'd', 32, 9, 0,
  /* 208 */ 'v', 'p', 's', 'u', 'b', 'd', 32, 9, 0,
  /* 217 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'd', 32, 9, 0,
  /* 229 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'd', 32, 9, 0,
  /* 241 */ 'v', 'p', 'a', 'd', 'd', 'd', 32, 9, 0,
  /* 250 */ 'k', 'a', 'n', 'd', 'd', 32, 9, 0,
  /* 258 */ 'v', 'p', 'a', 'n', 'd', 'd', 32, 9, 0,
  /* 267 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'd', 32, 9, 0,
  /* 280 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'd', 32, 9, 0,
  /* 294 */ 'v', 'p', 's', 'h', 'u', 'f', 'd', 32, 9, 0,
  /* 304 */ 'v', 'p', 's', 'l', 'l', 'd', 32, 9, 0,
  /* 313 */ 'v', 'p', 'm', 'u', 'l', 'l', 'd', 32, 9, 0,
  /* 323 */ 'v', 'p', 's', 'r', 'l', 'd', 32, 9, 0,
  /* 332 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'm', 'd', 32, 9, 0,
  /* 344 */ 'v', 'p', 't', 'e', 's', 't', 'n', 'm', 'd', 32, 9, 0,
  /* 356 */ 'v', 'p', 'e', 'r', 'm', 'd', 32, 9, 0,
  /* 365 */ 'v', 'p', 't', 'e', 's', 't', 'm', 'd', 32, 9, 0,
  /* 376 */ 'k', 'a', 'n', 'd', 'n', 'd', 32, 9, 0,
  /* 385 */ 'v', 'p', 'a', 'n', 'd', 'n', 'd', 32, 9, 0,
  /* 395 */ 'v', 'a', 'l', 'i', 'g', 'n', 'd', 32, 9, 0,
  /* 405 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 422 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 436 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 451 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 468 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 482 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 32, 9, 0,
  /* 497 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'p', 'd', 32, 9, 0,
  /* 509 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 'd', 32, 9, 0,
  /* 521 */ 'v', 'c', 'v', 't', 'u', 'd', 'q', '2', 'p', 'd', 32, 9, 0,
  /* 534 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'd', 32, 9, 0,
  /* 546 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'p', 'd', 32, 9, 0,
  /* 558 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 575 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 589 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 604 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 621 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 635 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 32, 9, 0,
  /* 650 */ 'v', 'r', 'c', 'p', '1', '4', 'p', 'd', 32, 9, 0,
  /* 661 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 'p', 'd', 32, 9, 0,
  /* 674 */ 'v', 'r', 'c', 'p', '2', '8', 'p', 'd', 32, 9, 0,
  /* 685 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 'p', 'd', 32, 9, 0,
  /* 698 */ 'v', 's', 'u', 'b', 'p', 'd', 32, 9, 0,
  /* 707 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'd', 'p', 'd', 32, 9, 0,
  /* 723 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'd', 'p', 'd', 32, 9, 0,
  /* 740 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'd', 'p', 'd', 32, 9, 0,
  /* 756 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'd', 'p', 'd', 32, 9, 0,
  /* 773 */ 'v', 'a', 'd', 'd', 'p', 'd', 32, 9, 0,
  /* 782 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 'd', 32, 9, 0,
  /* 795 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'p', 'd', 32, 9, 0,
  /* 809 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 'p', 'd', 32, 9, 0,
  /* 823 */ 'v', 's', 'h', 'u', 'f', 'p', 'd', 32, 9, 0,
  /* 833 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 'd', 32, 9, 0,
  /* 845 */ 'v', 'm', 'u', 'l', 'p', 'd', 32, 9, 0,
  /* 854 */ 'v', 'b', 'l', 'e', 'n', 'd', 'm', 'p', 'd', 32, 9, 0,
  /* 866 */ 'v', 'p', 'e', 'r', 'm', 'p', 'd', 32, 9, 0,
  /* 876 */ 'v', 'm', 'i', 'n', 'p', 'd', 32, 9, 0,
  /* 885 */ 'v', 'c', 'm', 'p', 'p', 'd', 32, 9, 0,
  /* 894 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'q', 'p', 'd', 32, 9, 0,
  /* 910 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'q', 'p', 'd', 32, 9, 0,
  /* 927 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'q', 'p', 'd', 32, 9, 0,
  /* 943 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'q', 'p', 'd', 32, 9, 0,
  /* 960 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 'd', 32, 9, 0,
  /* 973 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'p', 'd', 32, 9, 0,
  /* 987 */ 'v', 'd', 'i', 'v', 'p', 'd', 32, 9, 0,
  /* 996 */ 'v', 'm', 'a', 'x', 'p', 'd', 32, 9, 0,
  /* 1005 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'd', 32, 9, 0,
  /* 1018 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'd', 32, 9, 0,
  /* 1032 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'q', 'd', 32, 9, 0,
  /* 1044 */ 'v', 'p', 'm', 'o', 'v', 's', 'q', 'd', 32, 9, 0,
  /* 1055 */ 'v', 'p', 'm', 'o', 'v', 'q', 'd', 32, 9, 0,
  /* 1065 */ 'k', 'o', 'r', 'd', 32, 9, 0,
  /* 1072 */ 'k', 'x', 'n', 'o', 'r', 'd', 32, 9, 0,
  /* 1081 */ 'v', 'p', 'o', 'r', 'd', 32, 9, 0,
  /* 1089 */ 'k', 'x', 'o', 'r', 'd', 32, 9, 0,
  /* 1097 */ 'v', 'p', 'x', 'o', 'r', 'd', 32, 9, 0,
  /* 1106 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1120 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1135 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1149 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 32, 9, 0,
  /* 1164 */ 'v', 'r', 'c', 'p', '1', '4', 's', 'd', 32, 9, 0,
  /* 1175 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 's', 'd', 32, 9, 0,
  /* 1188 */ 'v', 'r', 'c', 'p', '2', '8', 's', 'd', 32, 9, 0,
  /* 1199 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 's', 'd', 32, 9, 0,
  /* 1212 */ 'v', 'p', 'a', 'b', 's', 'd', 32, 9, 0,
  /* 1221 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 's', 'd', 32, 9, 0,
  /* 1235 */ 'v', 'p', 'm', 'i', 'n', 's', 'd', 32, 9, 0,
  /* 1245 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 'd', 32, 9, 0,
  /* 1260 */ 'v', 'm', 'o', 'v', 's', 'd', 32, 9, 0,
  /* 1269 */ 'v', 'p', 'm', 'a', 'x', 's', 'd', 32, 9, 0,
  /* 1279 */ 'v', 'p', 'c', 'o', 'n', 'f', 'l', 'i', 'c', 't', 'd', 32, 9, 0,
  /* 1293 */ 'v', 'p', 'l', 'z', 'c', 'n', 't', 'd', 32, 9, 0,
  /* 1304 */ 'k', 'n', 'o', 't', 'd', 32, 9, 0,
  /* 1312 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'd', 32, 9, 0,
  /* 1327 */ 'v', 'p', 'm', 'i', 'n', 'u', 'd', 32, 9, 0,
  /* 1337 */ 'v', 'p', 'm', 'a', 'x', 'u', 'd', 32, 9, 0,
  /* 1347 */ 'v', 'p', 's', 'r', 'a', 'v', 'd', 32, 9, 0,
  /* 1357 */ 'v', 'p', 's', 'l', 'l', 'v', 'd', 32, 9, 0,
  /* 1367 */ 'v', 'p', 's', 'r', 'l', 'v', 'd', 32, 9, 0,
  /* 1377 */ 'k', 'm', 'o', 'v', 'd', 32, 9, 0,
  /* 1385 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'd', 32, 9, 0,
  /* 1397 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'd', 32, 9, 0,
  /* 1409 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'h', 32, 9, 0,
  /* 1421 */ 'v', 'c', 'v', 't', 't', 's', 'd', '2', 's', 'i', 32, 9, 0,
  /* 1434 */ 'v', 'c', 'v', 't', 's', 'd', '2', 's', 'i', 32, 9, 0,
  /* 1446 */ 'v', 'c', 'v', 't', 't', 's', 's', '2', 's', 'i', 32, 9, 0,
  /* 1459 */ 'v', 'c', 'v', 't', 's', 's', '2', 's', 'i', 32, 9, 0,
  /* 1471 */ 'v', 'c', 'v', 't', 't', 's', 'd', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1485 */ 'v', 'c', 'v', 't', 's', 'd', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1498 */ 'v', 'c', 'v', 't', 't', 's', 's', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1512 */ 'v', 'c', 'v', 't', 's', 's', '2', 'u', 's', 'i', 32, 9, 0,
  /* 1525 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 'd', 'l', 32, 9, 0,
  /* 1538 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 'd', 'l', 32, 9, 0,
  /* 1552 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 's', 'l', 32, 9, 0,
  /* 1565 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 's', 'l', 32, 9, 0,
  /* 1579 */ 'v', 'm', 'o', 'v', 'd', 'd', 'u', 'p', 32, 9, 0,
  /* 1590 */ 'v', 'm', 'o', 'v', 's', 'h', 'd', 'u', 'p', 32, 9, 0,
  /* 1602 */ 'v', 'm', 'o', 'v', 's', 'l', 'd', 'u', 'p', 32, 9, 0,
  /* 1614 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'm', 'b', '2', 'q', 32, 9, 0,
  /* 1632 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'q', 32, 9, 0,
  /* 1643 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'q', 32, 9, 0,
  /* 1654 */ 'v', 'p', 's', 'r', 'a', 'q', 32, 9, 0,
  /* 1663 */ 'v', 'p', 's', 'u', 'b', 'q', 32, 9, 0,
  /* 1672 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'q', 32, 9, 0,
  /* 1684 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'q', 32, 9, 0,
  /* 1696 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 32, 9, 0,
  /* 1709 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 32, 9, 0,
  /* 1721 */ 'v', 'c', 'v', 't', 't', 'p', 's', '2', 'd', 'q', 32, 9, 0,
  /* 1734 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'd', 'q', 32, 9, 0,
  /* 1746 */ 'v', 'p', 'a', 'd', 'd', 'q', 32, 9, 0,
  /* 1755 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'd', 'q', 32, 9, 0,
  /* 1768 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'd', 'q', 32, 9, 0,
  /* 1781 */ 'v', 'p', 'm', 'u', 'l', 'd', 'q', 32, 9, 0,
  /* 1791 */ 'k', 'a', 'n', 'd', 'q', 32, 9, 0,
  /* 1799 */ 'v', 'p', 'a', 'n', 'd', 'q', 32, 9, 0,
  /* 1808 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'q', 'd', 'q', 32, 9, 0,
  /* 1822 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'q', 'd', 'q', 32, 9, 0,
  /* 1836 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'q', 32, 9, 0,
  /* 1849 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'q', 32, 9, 0,
  /* 1863 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 'd', 'q', 32, 9, 0,
  /* 1876 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 'd', 'q', 32, 9, 0,
  /* 1890 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1904 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1917 */ 'v', 'c', 'v', 't', 't', 'p', 's', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1931 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'u', 'd', 'q', 32, 9, 0,
  /* 1944 */ 'v', 'p', 'm', 'u', 'l', 'u', 'd', 'q', 32, 9, 0,
  /* 1955 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'd', 'q', 32, 9, 0,
  /* 1967 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'd', 'q', 32, 9, 0,
  /* 1979 */ 'v', 'p', 's', 'l', 'l', 'q', 32, 9, 0,
  /* 1988 */ 'v', 'p', 's', 'r', 'l', 'q', 32, 9, 0,
  /* 1997 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'm', 'q', 32, 9, 0,
  /* 2009 */ 'v', 'p', 't', 'e', 's', 't', 'n', 'm', 'q', 32, 9, 0,
  /* 2021 */ 'v', 'p', 'e', 'r', 'm', 'q', 32, 9, 0,
  /* 2030 */ 'v', 'p', 't', 'e', 's', 't', 'm', 'q', 32, 9, 0,
  /* 2041 */ 'k', 'a', 'n', 'd', 'n', 'q', 32, 9, 0,
  /* 2050 */ 'v', 'p', 'a', 'n', 'd', 'n', 'q', 32, 9, 0,
  /* 2060 */ 'v', 'a', 'l', 'i', 'g', 'n', 'q', 32, 9, 0,
  /* 2070 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'q', 32, 9, 0,
  /* 2083 */ 'v', 'p', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'q', 32, 9, 0,
  /* 2097 */ 'k', 'o', 'r', 'q', 32, 9, 0,
  /* 2104 */ 'k', 'x', 'n', 'o', 'r', 'q', 32, 9, 0,
  /* 2113 */ 'v', 'p', 'o', 'r', 'q', 32, 9, 0,
  /* 2121 */ 'k', 'x', 'o', 'r', 'q', 32, 9, 0,
  /* 2129 */ 'v', 'p', 'x', 'o', 'r', 'q', 32, 9, 0,
  /* 2138 */ 'v', 'p', 'a', 'b', 's', 'q', 32, 9, 0,
  /* 2147 */ 'v', 'p', 'm', 'i', 'n', 's', 'q', 32, 9, 0,
  /* 2157 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 's', 'q', 32, 9, 0,
  /* 2170 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 's', 'q', 32, 9, 0,
  /* 2184 */ 'v', 'p', 'm', 'a', 'x', 's', 'q', 32, 9, 0,
  /* 2194 */ 'v', 'p', 'c', 'o', 'n', 'f', 'l', 'i', 'c', 't', 'q', 32, 9, 0,
  /* 2208 */ 'v', 'p', 'l', 'z', 'c', 'n', 't', 'q', 32, 9, 0,
  /* 2219 */ 'k', 'n', 'o', 't', 'q', 32, 9, 0,
  /* 2227 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'q', 32, 9, 0,
  /* 2242 */ 'v', 'p', 'm', 'i', 'n', 'u', 'q', 32, 9, 0,
  /* 2252 */ 'v', 'p', 'm', 'a', 'x', 'u', 'q', 32, 9, 0,
  /* 2262 */ 'v', 'p', 's', 'r', 'a', 'v', 'q', 32, 9, 0,
  /* 2272 */ 'v', 'p', 's', 'l', 'l', 'v', 'q', 32, 9, 0,
  /* 2282 */ 'v', 'p', 's', 'r', 'l', 'v', 'q', 32, 9, 0,
  /* 2292 */ 'k', 'm', 'o', 'v', 'q', 32, 9, 0,
  /* 2300 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'q', 32, 9, 0,
  /* 2312 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'q', 32, 9, 0,
  /* 2324 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2341 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2355 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2370 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2387 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2401 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 32, 9, 0,
  /* 2416 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 32, 9, 0,
  /* 2428 */ 'v', 'p', 'e', 'r', 'm', 'i', '2', 'p', 's', 32, 9, 0,
  /* 2440 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 's', 32, 9, 0,
  /* 2452 */ 'v', 'c', 'v', 't', 'u', 'd', 'q', '2', 'p', 's', 32, 9, 0,
  /* 2465 */ 'v', 'p', 'e', 'r', 'm', 't', '2', 'p', 's', 32, 9, 0,
  /* 2477 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2494 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2508 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2523 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2540 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2554 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 32, 9, 0,
  /* 2569 */ 'v', 'r', 'c', 'p', '1', '4', 'p', 's', 32, 9, 0,
  /* 2580 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 'p', 's', 32, 9, 0,
  /* 2593 */ 'v', 'r', 'c', 'p', '2', '8', 'p', 's', 32, 9, 0,
  /* 2604 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 'p', 's', 32, 9, 0,
  /* 2617 */ 'v', 's', 'u', 'b', 'p', 's', 32, 9, 0,
  /* 2626 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'd', 'p', 's', 32, 9, 0,
  /* 2642 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'd', 'p', 's', 32, 9, 0,
  /* 2659 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'd', 'p', 's', 32, 9, 0,
  /* 2675 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'd', 'p', 's', 32, 9, 0,
  /* 2692 */ 'v', 'a', 'd', 'd', 'p', 's', 32, 9, 0,
  /* 2701 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 's', 32, 9, 0,
  /* 2714 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'd', 'p', 's', 32, 9, 0,
  /* 2728 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 'p', 's', 32, 9, 0,
  /* 2742 */ 'v', 's', 'h', 'u', 'f', 'p', 's', 32, 9, 0,
  /* 2752 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 's', 32, 9, 0,
  /* 2764 */ 'v', 'm', 'u', 'l', 'p', 's', 32, 9, 0,
  /* 2773 */ 'v', 'b', 'l', 'e', 'n', 'd', 'm', 'p', 's', 32, 9, 0,
  /* 2785 */ 'v', 'p', 'e', 'r', 'm', 'p', 's', 32, 9, 0,
  /* 2795 */ 'v', 'm', 'i', 'n', 'p', 's', 32, 9, 0,
  /* 2804 */ 'v', 'c', 'm', 'p', 'p', 's', 32, 9, 0,
  /* 2813 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '0', 'q', 'p', 's', 32, 9, 0,
  /* 2829 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '0', 'q', 'p', 's', 32, 9, 0,
  /* 2846 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'p', 'f', '1', 'q', 'p', 's', 32, 9, 0,
  /* 2862 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'p', 'f', '1', 'q', 'p', 's', 32, 9, 0,
  /* 2879 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 's', 32, 9, 0,
  /* 2892 */ 'v', 's', 'c', 'a', 't', 't', 'e', 'r', 'q', 'p', 's', 32, 9, 0,
  /* 2906 */ 'v', 'd', 'i', 'v', 'p', 's', 32, 9, 0,
  /* 2915 */ 'v', 'm', 'a', 'x', 'p', 's', 32, 9, 0,
  /* 2924 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2938 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2953 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2967 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 32, 9, 0,
  /* 2982 */ 'v', 'r', 'c', 'p', '1', '4', 's', 's', 32, 9, 0,
  /* 2993 */ 'v', 'r', 's', 'q', 'r', 't', '1', '4', 's', 's', 32, 9, 0,
  /* 3006 */ 'v', 'r', 'c', 'p', '2', '8', 's', 's', 32, 9, 0,
  /* 3017 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 's', 's', 32, 9, 0,
  /* 3030 */ 'v', 'r', 'n', 'd', 's', 'c', 'a', 'l', 'e', 's', 's', 32, 9, 0,
  /* 3044 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 's', 32, 9, 0,
  /* 3059 */ 'v', 'm', 'o', 'v', 's', 's', 32, 9, 0,
  /* 3068 */ 'k', 'u', 'n', 'p', 'c', 'k', 'b', 'w', 32, 9, 0,
  /* 3079 */ 'k', 'a', 'n', 'd', 'w', 32, 9, 0,
  /* 3087 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'd', 'w', 32, 9, 0,
  /* 3099 */ 'v', 'p', 'm', 'o', 'v', 's', 'd', 'w', 32, 9, 0,
  /* 3110 */ 'v', 'p', 'm', 'o', 'v', 'd', 'w', 32, 9, 0,
  /* 3120 */ 'k', 's', 'h', 'i', 'f', 't', 'l', 'w', 32, 9, 0,
  /* 3131 */ 'k', 'a', 'n', 'd', 'n', 'w', 32, 9, 0,
  /* 3140 */ 'v', 'p', 'm', 'o', 'v', 'u', 's', 'q', 'w', 32, 9, 0,
  /* 3152 */ 'v', 'p', 'm', 'o', 'v', 's', 'q', 'w', 32, 9, 0,
  /* 3163 */ 'v', 'p', 'm', 'o', 'v', 'q', 'w', 32, 9, 0,
  /* 3173 */ 'k', 'o', 'r', 'w', 32, 9, 0,
  /* 3180 */ 'k', 'x', 'n', 'o', 'r', 'w', 32, 9, 0,
  /* 3189 */ 'k', 'x', 'o', 'r', 'w', 32, 9, 0,
  /* 3197 */ 'k', 's', 'h', 'i', 'f', 't', 'r', 'w', 32, 9, 0,
  /* 3208 */ 'k', 'n', 'o', 't', 'w', 32, 9, 0,
  /* 3216 */ 'k', 'o', 'r', 't', 'e', 's', 't', 'w', 32, 9, 0,
  /* 3227 */ 'k', 'm', 'o', 'v', 'w', 32, 9, 0,
  /* 3235 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 't', '0', 9, 0,
  /* 3247 */ 's', 'h', 'a', '1', 'm', 's', 'g', '1', 9, 0,
  /* 3257 */ 's', 'h', 'a', '2', '5', '6', 'm', 's', 'g', '1', 9, 0,
  /* 3269 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 't', '1', 9, 0,
  /* 3281 */ 'p', 'f', 'r', 'c', 'p', 'i', 't', '1', 9, 0,
  /* 3291 */ 'p', 'f', 'r', 's', 'q', 'i', 't', '1', 9, 0,
  /* 3301 */ 'v', 'm', 'o', 'v', 'd', 'q', 'a', '3', '2', 9, 0,
  /* 3312 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '3', '2', 9, 0,
  /* 3323 */ 's', 'h', 'a', '1', 'm', 's', 'g', '2', 9, 0,
  /* 3333 */ 's', 'h', 'a', '2', '5', '6', 'm', 's', 'g', '2', 9, 0,
  /* 3345 */ 's', 'h', 'a', '2', '5', '6', 'r', 'n', 'd', 's', '2', 9, 0,
  /* 3358 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 't', '2', 9, 0,
  /* 3370 */ 'p', 'f', 'r', 'c', 'p', 'i', 't', '2', 9, 0,
  /* 3380 */ 'v', 'm', 'o', 'v', 'd', 'q', 'a', '6', '4', 9, 0,
  /* 3391 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '6', '4', 9, 0,
  /* 3402 */ 's', 'h', 'a', '1', 'r', 'n', 'd', 's', '4', 9, 0,
  /* 3413 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'f', '3', '2', 'x', '4', 9, 0,
  /* 3428 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'f', '3', '2', 'x', '4', 9, 0,
  /* 3442 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'i', '3', '2', 'x', '4', 9, 0,
  /* 3457 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'i', '3', '2', 'x', '4', 9, 0,
  /* 3471 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'f', '6', '4', 'x', '4', 9, 0,
  /* 3486 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'f', '6', '4', 'x', '4', 9, 0,
  /* 3500 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'i', '6', '4', 'x', '4', 9, 0,
  /* 3515 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'i', '6', '4', 'x', '4', 9, 0,
  /* 3529 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '1', '6', 9, 0,
  /* 3540 */ 'v', 'p', 'e', 'r', 'm', '2', 'f', '1', '2', '8', 9, 0,
  /* 3552 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'f', '1', '2', '8', 9, 0,
  /* 3566 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'f', '1', '2', '8', 9, 0,
  /* 3579 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'f', '1', '2', '8', 9, 0,
  /* 3595 */ 'v', 'p', 'e', 'r', 'm', '2', 'i', '1', '2', '8', 9, 0,
  /* 3607 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'i', '1', '2', '8', 9, 0,
  /* 3621 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'i', '1', '2', '8', 9, 0,
  /* 3634 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'i', '1', '2', '8', 9, 0,
  /* 3650 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', '8', 9, 0,
  /* 3660 */ 'j', 'a', 9, 0,
  /* 3664 */ 'v', 'm', 'o', 'v', 'n', 't', 'd', 'q', 'a', 9, 0,
  /* 3675 */ 'v', 'm', 'o', 'v', 'd', 'q', 'a', 9, 0,
  /* 3684 */ 's', 'e', 't', 'a', 9, 0,
  /* 3690 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 'n', 't', 'a', 9, 0,
  /* 3703 */ 'c', 'r', 'c', '3', '2', 'b', 9, 0,
  /* 3711 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '1', '6', 'b', 9, 0,
  /* 3723 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '8', 'b', 9, 0,
  /* 3734 */ 'v', 'p', 's', 'h', 'a', 'b', 9, 0,
  /* 3742 */ 's', 'b', 'b', 'b', 9, 0,
  /* 3748 */ 'v', 'p', 's', 'u', 'b', 'b', 9, 0,
  /* 3756 */ 'a', 'd', 'c', 'b', 9, 0,
  /* 3762 */ 'd', 'e', 'c', 'b', 9, 0,
  /* 3768 */ 'i', 'n', 'c', 'b', 9, 0,
  /* 3774 */ 'v', 'p', 'a', 'd', 'd', 'b', 9, 0,
  /* 3782 */ 'x', 'a', 'd', 'd', 'b', 9, 0,
  /* 3789 */ 'a', 'n', 'd', 'b', 9, 0,
  /* 3795 */ 'v', 'p', 's', 'h', 'u', 'f', 'b', 9, 0,
  /* 3804 */ 'n', 'e', 'g', 'b', 9, 0,
  /* 3810 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'b', 9, 0,
  /* 3820 */ 'v', 'p', 'a', 'v', 'g', 'b', 9, 0,
  /* 3828 */ 'j', 'b', 9, 0,
  /* 3832 */ 'v', 'p', 'm', 'o', 'v', 'm', 's', 'k', 'b', 9, 0,
  /* 3843 */ 's', 'a', 'l', 'b', 9, 0,
  /* 3849 */ 'r', 'c', 'l', 'b', 9, 0,
  /* 3855 */ 'v', 'p', 's', 'h', 'l', 'b', 9, 0,
  /* 3863 */ 'r', 'o', 'l', 'b', 9, 0,
  /* 3869 */ 'i', 'm', 'u', 'l', 'b', 9, 0,
  /* 3876 */ 'v', 'p', 'c', 'o', 'm', 'b', 9, 0,
  /* 3884 */ 'v', 'p', 's', 'i', 'g', 'n', 'b', 9, 0,
  /* 3893 */ 'i', 'n', 'b', 9, 0,
  /* 3898 */ 'f', 'c', 'm', 'o', 'v', 'n', 'b', 9, 0,
  /* 3907 */ 'c', 'm', 'p', 'b', 9, 0,
  /* 3913 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'b', 9, 0,
  /* 3923 */ 's', 'a', 'r', 'b', 9, 0,
  /* 3929 */ 'r', 'c', 'r', 'b', 9, 0,
  /* 3935 */ 's', 'h', 'r', 'b', 9, 0,
  /* 3941 */ 'r', 'o', 'r', 'b', 9, 0,
  /* 3947 */ 'x', 'o', 'r', 'b', 9, 0,
  /* 3953 */ 'v', 'p', 'i', 'n', 's', 'r', 'b', 9, 0,
  /* 3962 */ 'v', 'p', 'e', 'x', 't', 'r', 'b', 9, 0,
  /* 3971 */ 's', 'c', 'a', 's', 'b', 9, 0,
  /* 3978 */ 'v', 'p', 'a', 'b', 's', 'b', 9, 0,
  /* 3986 */ 'm', 'o', 'v', 'a', 'b', 's', 'b', 9, 0,
  /* 3995 */ 'v', 'p', 's', 'u', 'b', 's', 'b', 9, 0,
  /* 4004 */ 'v', 'p', 'a', 'd', 'd', 's', 'b', 9, 0,
  /* 4013 */ 'l', 'o', 'd', 's', 'b', 9, 0,
  /* 4020 */ 'v', 'p', 'm', 'i', 'n', 's', 'b', 9, 0,
  /* 4029 */ 'c', 'm', 'p', 's', 'b', 9, 0,
  /* 4036 */ 'o', 'u', 't', 's', 'b', 9, 0,
  /* 4043 */ 'v', 'p', 's', 'u', 'b', 'u', 's', 'b', 9, 0,
  /* 4053 */ 'v', 'p', 'a', 'd', 'd', 'u', 's', 'b', 9, 0,
  /* 4063 */ 'p', 'a', 'v', 'g', 'u', 's', 'b', 9, 0,
  /* 4072 */ 'm', 'o', 'v', 's', 'b', 9, 0,
  /* 4079 */ 'v', 'p', 'm', 'a', 'x', 's', 'b', 9, 0,
  /* 4088 */ 's', 'e', 't', 'b', 9, 0,
  /* 4094 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'b', 9, 0,
  /* 4104 */ 'n', 'o', 't', 'b', 9, 0,
  /* 4110 */ 'v', 'p', 'r', 'o', 't', 'b', 9, 0,
  /* 4118 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'b', 9, 0,
  /* 4132 */ 't', 'e', 's', 't', 'b', 9, 0,
  /* 4139 */ 'v', 'p', 'c', 'o', 'm', 'u', 'b', 9, 0,
  /* 4148 */ 'v', 'p', 'm', 'i', 'n', 'u', 'b', 9, 0,
  /* 4157 */ 'p', 'f', 's', 'u', 'b', 9, 0,
  /* 4164 */ 'v', 'p', 'm', 'a', 'x', 'u', 'b', 9, 0,
  /* 4173 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'v', 'b', 9, 0,
  /* 4184 */ 'i', 'd', 'i', 'v', 'b', 9, 0,
  /* 4191 */ 'f', 'c', 'm', 'o', 'v', 'b', 9, 0,
  /* 4199 */ 'v', 'p', 'a', 'c', 'k', 's', 's', 'w', 'b', 9, 0,
  /* 4210 */ 'v', 'p', 'a', 'c', 'k', 'u', 's', 'w', 'b', 9, 0,
  /* 4221 */ 'p', 'f', 'a', 'c', 'c', 9, 0,
  /* 4228 */ 'p', 'f', 'n', 'a', 'c', 'c', 9, 0,
  /* 4236 */ 'p', 'f', 'p', 'n', 'a', 'c', 'c', 9, 0,
  /* 4245 */ 'v', 'a', 'e', 's', 'd', 'e', 'c', 9, 0,
  /* 4254 */ 'b', 'l', 'c', 'i', 'c', 9, 0,
  /* 4261 */ 'b', 'l', 's', 'i', 'c', 9, 0,
  /* 4268 */ 't', '1', 'm', 's', 'k', 'c', 9, 0,
  /* 4276 */ 'v', 'a', 'e', 's', 'i', 'm', 'c', 9, 0,
  /* 4285 */ 'v', 'a', 'e', 's', 'e', 'n', 'c', 9, 0,
  /* 4294 */ 'a', 'a', 'd', 9, 0,
  /* 4299 */ 'v', 'p', 's', 'h', 'a', 'd', 9, 0,
  /* 4307 */ 'v', 'p', 's', 'r', 'a', 'd', 9, 0,
  /* 4315 */ 'v', 'p', 'h', 'a', 'd', 'd', 'b', 'd', 9, 0,
  /* 4325 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'b', 'd', 9, 0,
  /* 4336 */ 'v', 'p', 'h', 's', 'u', 'b', 'd', 9, 0,
  /* 4345 */ 'v', 'p', 's', 'u', 'b', 'd', 9, 0,
  /* 4353 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'd', 9, 0,
  /* 4364 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'd', 9, 0,
  /* 4375 */ 'p', 'f', 'a', 'd', 'd', 9, 0,
  /* 4382 */ 'v', 'p', 'h', 'a', 'd', 'd', 'd', 9, 0,
  /* 4391 */ 'v', 'p', 'a', 'd', 'd', 'd', 9, 0,
  /* 4399 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'd', 9, 0,
  /* 4409 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'd', 9, 0,
  /* 4421 */ 'v', 'p', 'm', 'a', 'c', 's', 'd', 'd', 9, 0,
  /* 4431 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'd', 'd', 9, 0,
  /* 4442 */ 'p', 'i', '2', 'f', 'd', 9, 0,
  /* 4449 */ 'v', 'p', 's', 'h', 'u', 'f', 'd', 9, 0,
  /* 4458 */ 'p', 'f', '2', 'i', 'd', 9, 0,
  /* 4465 */ 'i', 'n', 'v', 'p', 'c', 'i', 'd', 9, 0,
  /* 4474 */ 'i', 'n', 'v', 'v', 'p', 'i', 'd', 9, 0,
  /* 4483 */ 'f', 'b', 'l', 'd', 9, 0,
  /* 4489 */ 'f', 'l', 'd', 9, 0,
  /* 4494 */ 'v', 'p', 's', 'h', 'l', 'd', 9, 0,
  /* 4502 */ 'v', 'p', 's', 'l', 'l', 'd', 9, 0,
  /* 4510 */ 'v', 'p', 'm', 'u', 'l', 'l', 'd', 9, 0,
  /* 4519 */ 'v', 'p', 's', 'r', 'l', 'd', 9, 0,
  /* 4527 */ 'v', 'm', 'p', 't', 'r', 'l', 'd', 9, 0,
  /* 4536 */ 'v', 'p', 'c', 'o', 'm', 'd', 9, 0,
  /* 4544 */ 'v', 'p', 'e', 'r', 'm', 'd', 9, 0,
  /* 4552 */ 'v', 'p', 'a', 'n', 'd', 9, 0,
  /* 4559 */ 'v', 'p', 's', 'i', 'g', 'n', 'd', 9, 0,
  /* 4568 */ 'b', 'o', 'u', 'n', 'd', 9, 0,
  /* 4575 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4591 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4604 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4618 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4634 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4647 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 'd', 9, 0,
  /* 4661 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4677 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4690 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4704 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4720 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4733 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 'd', 9, 0,
  /* 4747 */ 'c', 'v', 't', 'p', 'i', '2', 'p', 'd', 9, 0,
  /* 4757 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', '2', 'p', 'd', 9, 0,
  /* 4769 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 'd', 9, 0,
  /* 4780 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'd', 9, 0,
  /* 4791 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4807 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4820 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4834 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4850 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4863 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 'd', 9, 0,
  /* 4877 */ 'v', 'm', 'o', 'v', 'a', 'p', 'd', 9, 0,
  /* 4886 */ 'p', 's', 'w', 'a', 'p', 'd', 9, 0,
  /* 4894 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4907 */ 'v', 'a', 'd', 'd', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4918 */ 'v', 'h', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4927 */ 'v', 'f', 'm', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4937 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4948 */ 'v', 's', 'u', 'b', 'p', 'd', 9, 0,
  /* 4956 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4969 */ 'v', 'h', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4978 */ 'v', 'f', 'm', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4988 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 4999 */ 'v', 'a', 'd', 'd', 'p', 'd', 9, 0,
  /* 5007 */ 'v', 'a', 'n', 'd', 'p', 'd', 9, 0,
  /* 5015 */ 'v', 'b', 'l', 'e', 'n', 'd', 'p', 'd', 9, 0,
  /* 5025 */ 'v', 'r', 'o', 'u', 'n', 'd', 'p', 'd', 9, 0,
  /* 5035 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 'd', 9, 0,
  /* 5047 */ 'v', 's', 'h', 'u', 'f', 'p', 'd', 9, 0,
  /* 5056 */ 'v', 'u', 'n', 'p', 'c', 'k', 'h', 'p', 'd', 9, 0,
  /* 5067 */ 'v', 'm', 'o', 'v', 'h', 'p', 'd', 9, 0,
  /* 5076 */ 'v', 'm', 'o', 'v', 'm', 's', 'k', 'p', 'd', 9, 0,
  /* 5087 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 'd', 9, 0,
  /* 5098 */ 'v', 'u', 'n', 'p', 'c', 'k', 'l', 'p', 'd', 9, 0,
  /* 5109 */ 'v', 'm', 'u', 'l', 'p', 'd', 9, 0,
  /* 5117 */ 'v', 'm', 'o', 'v', 'l', 'p', 'd', 9, 0,
  /* 5126 */ 'v', 'p', 'c', 'm', 'p', 'd', 9, 0,
  /* 5134 */ 'v', 'p', 'e', 'r', 'm', 'p', 'd', 9, 0,
  /* 5143 */ 'v', 'a', 'n', 'd', 'n', 'p', 'd', 9, 0,
  /* 5152 */ 'v', 'm', 'i', 'n', 'p', 'd', 9, 0,
  /* 5160 */ 'v', 'd', 'p', 'p', 'd', 9, 0,
  /* 5167 */ 'v', 'c', 'm', 'p', 'p', 'd', 9, 0,
  /* 5175 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 'd', 9, 0,
  /* 5187 */ 'v', 'o', 'r', 'p', 'd', 9, 0,
  /* 5194 */ 'v', 'x', 'o', 'r', 'p', 'd', 9, 0,
  /* 5202 */ 'v', 'm', 'o', 'v', 'n', 't', 'p', 'd', 9, 0,
  /* 5212 */ 'v', 's', 'q', 'r', 't', 'p', 'd', 9, 0,
  /* 5221 */ 'v', 't', 'e', 's', 't', 'p', 'd', 9, 0,
  /* 5230 */ 'v', 'm', 'o', 'v', 'u', 'p', 'd', 9, 0,
  /* 5239 */ 'v', 'b', 'l', 'e', 'n', 'd', 'v', 'p', 'd', 9, 0,
  /* 5250 */ 'v', 'd', 'i', 'v', 'p', 'd', 9, 0,
  /* 5258 */ 'v', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'p', 'd', 9, 0,
  /* 5270 */ 'v', 'm', 'a', 'x', 'p', 'd', 9, 0,
  /* 5278 */ 'v', 'f', 'r', 'c', 'z', 'p', 'd', 9, 0,
  /* 5287 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'd', 9, 0,
  /* 5297 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'd', 9, 0,
  /* 5309 */ 'v', 'p', 'i', 'n', 's', 'r', 'd', 9, 0,
  /* 5318 */ 'v', 'p', 'e', 'x', 't', 'r', 'd', 9, 0,
  /* 5327 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 's', 'd', 9, 0,
  /* 5340 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 's', 'd', 9, 0,
  /* 5354 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 'd', 9, 0,
  /* 5367 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 'd', 9, 0,
  /* 5381 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 's', 'd', 9, 0,
  /* 5394 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 's', 'd', 9, 0,
  /* 5408 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 'd', 9, 0,
  /* 5421 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 'd', 9, 0,
  /* 5435 */ 'v', 'c', 'v', 't', 's', 's', '2', 's', 'd', 9, 0,
  /* 5446 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 9, 0,
  /* 5459 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 'd', 9, 0,
  /* 5473 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 9, 0,
  /* 5486 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 'd', 9, 0,
  /* 5500 */ 'v', 'p', 'a', 'b', 's', 'd', 9, 0,
  /* 5508 */ 'v', 'f', 'm', 's', 'u', 'b', 's', 'd', 9, 0,
  /* 5518 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 's', 'd', 9, 0,
  /* 5529 */ 'v', 's', 'u', 'b', 's', 'd', 9, 0,
  /* 5537 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'd', 9, 0,
  /* 5547 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 's', 'd', 9, 0,
  /* 5558 */ 'v', 'a', 'd', 'd', 's', 'd', 9, 0,
  /* 5566 */ 'v', 'r', 'o', 'u', 'n', 'd', 's', 'd', 9, 0,
  /* 5576 */ 'v', 'u', 'c', 'o', 'm', 'i', 's', 'd', 9, 0,
  /* 5586 */ 'v', 'c', 'o', 'm', 'i', 's', 'd', 9, 0,
  /* 5595 */ 'v', 'm', 'u', 'l', 's', 'd', 9, 0,
  /* 5603 */ 'v', 'p', 'm', 'i', 'n', 's', 'd', 9, 0,
  /* 5612 */ 'v', 'm', 'i', 'n', 's', 'd', 9, 0,
  /* 5620 */ 'v', 'c', 'm', 'p', 's', 'd', 9, 0,
  /* 5628 */ 'm', 'o', 'v', 'n', 't', 's', 'd', 9, 0,
  /* 5637 */ 'v', 's', 'q', 'r', 't', 's', 'd', 9, 0,
  /* 5646 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 'd', 9, 0,
  /* 5660 */ 'v', 'd', 'i', 'v', 's', 'd', 9, 0,
  /* 5668 */ 'v', 'm', 'o', 'v', 's', 'd', 9, 0,
  /* 5676 */ 'v', 'p', 'm', 'a', 'x', 's', 'd', 9, 0,
  /* 5685 */ 'v', 'm', 'a', 'x', 's', 'd', 9, 0,
  /* 5693 */ 'v', 'f', 'r', 'c', 'z', 's', 'd', 9, 0,
  /* 5702 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'd', 9, 0,
  /* 5712 */ 'v', 'p', 'r', 'o', 't', 'd', 9, 0,
  /* 5720 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'd', 9, 0,
  /* 5734 */ 'v', 'p', 'c', 'o', 'm', 'u', 'd', 9, 0,
  /* 5743 */ 'v', 'p', 'm', 'i', 'n', 'u', 'd', 9, 0,
  /* 5752 */ 'v', 'p', 'c', 'm', 'p', 'u', 'd', 9, 0,
  /* 5761 */ 'v', 'p', 'm', 'a', 'x', 'u', 'd', 9, 0,
  /* 5770 */ 'v', 'p', 's', 'r', 'a', 'v', 'd', 9, 0,
  /* 5779 */ 'v', 'p', 's', 'l', 'l', 'v', 'd', 9, 0,
  /* 5788 */ 'v', 'p', 's', 'r', 'l', 'v', 'd', 9, 0,
  /* 5797 */ 'v', 'p', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'd', 9, 0,
  /* 5809 */ 'v', 'm', 'o', 'v', 'd', 9, 0,
  /* 5816 */ 'v', 'p', 'h', 's', 'u', 'b', 'w', 'd', 9, 0,
  /* 5826 */ 'v', 'p', 'h', 'a', 'd', 'd', 'w', 'd', 9, 0,
  /* 5836 */ 'v', 'p', 'm', 'a', 'd', 'd', 'w', 'd', 9, 0,
  /* 5846 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'w', 'd', 9, 0,
  /* 5858 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'w', 'd', 9, 0,
  /* 5870 */ 'v', 'p', 'm', 'a', 'c', 's', 'w', 'd', 9, 0,
  /* 5880 */ 'v', 'p', 'm', 'a', 'd', 'c', 's', 'w', 'd', 9, 0,
  /* 5891 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'w', 'd', 9, 0,
  /* 5902 */ 'v', 'p', 'm', 'a', 'd', 'c', 's', 's', 'w', 'd', 9, 0,
  /* 5914 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'w', 'd', 9, 0,
  /* 5925 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'd', 9, 0,
  /* 5936 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'd', 9, 0,
  /* 5947 */ 'j', 'a', 'e', 9, 0,
  /* 5952 */ 's', 'e', 't', 'a', 'e', 9, 0,
  /* 5959 */ 'j', 'b', 'e', 9, 0,
  /* 5964 */ 'f', 'c', 'm', 'o', 'v', 'n', 'b', 'e', 9, 0,
  /* 5974 */ 's', 'e', 't', 'b', 'e', 9, 0,
  /* 5981 */ 'f', 'c', 'm', 'o', 'v', 'b', 'e', 9, 0,
  /* 5990 */ 'f', 'f', 'r', 'e', 'e', 9, 0,
  /* 5997 */ 'j', 'g', 'e', 9, 0,
  /* 6002 */ 'p', 'f', 'c', 'm', 'p', 'g', 'e', 9, 0,
  /* 6011 */ 's', 'e', 't', 'g', 'e', 9, 0,
  /* 6018 */ 'j', 'e', 9, 0,
  /* 6022 */ 'j', 'l', 'e', 9, 0,
  /* 6027 */ 's', 'e', 't', 'l', 'e', 9, 0,
  /* 6034 */ 'j', 'n', 'e', 9, 0,
  /* 6039 */ 'l', 'o', 'o', 'p', 'n', 'e', 9, 0,
  /* 6047 */ 's', 'e', 't', 'n', 'e', 9, 0,
  /* 6054 */ 'f', 'c', 'm', 'o', 'v', 'n', 'e', 9, 0,
  /* 6063 */ 'l', 'o', 'o', 'p', 'e', 9, 0,
  /* 6070 */ 's', 'e', 't', 'e', 9, 0,
  /* 6076 */ 's', 'h', 'a', '1', 'n', 'e', 'x', 't', 'e', 9, 0,
  /* 6087 */ 'f', 'n', 's', 'a', 'v', 'e', 9, 0,
  /* 6095 */ 'f', 'x', 's', 'a', 'v', 'e', 9, 0,
  /* 6103 */ 'f', 'c', 'm', 'o', 'v', 'e', 9, 0,
  /* 6111 */ 'j', 'g', 9, 0,
  /* 6115 */ 'i', 'n', 'v', 'l', 'p', 'g', 9, 0,
  /* 6123 */ 's', 'e', 't', 'g', 9, 0,
  /* 6129 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 9, 0,
  /* 6139 */ 'f', 'x', 'c', 'h', 9, 0,
  /* 6145 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'p', 'h', 9, 0,
  /* 6156 */ 'v', 'p', 'm', 'a', 'c', 's', 'd', 'q', 'h', 9, 0,
  /* 6167 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'd', 'q', 'h', 9, 0,
  /* 6179 */ 'c', 'l', 'f', 'l', 'u', 's', 'h', 9, 0,
  /* 6188 */ 'b', 'l', 'c', 'i', 9, 0,
  /* 6194 */ 'f', 'c', 'o', 'm', 'i', 9, 0,
  /* 6201 */ 'f', 'u', 'c', 'o', 'm', 'i', 9, 0,
  /* 6209 */ 'c', 'v', 't', 't', 'p', 'd', '2', 'p', 'i', 9, 0,
  /* 6220 */ 'c', 'v', 't', 'p', 'd', '2', 'p', 'i', 9, 0,
  /* 6230 */ 'c', 'v', 't', 't', 'p', 's', '2', 'p', 'i', 9, 0,
  /* 6241 */ 'c', 'v', 't', 'p', 's', '2', 'p', 'i', 9, 0,
  /* 6251 */ 'f', 'c', 'o', 'm', 'p', 'i', 9, 0,
  /* 6259 */ 'f', 'u', 'c', 'o', 'm', 'p', 'i', 9, 0,
  /* 6268 */ 'v', 'p', 'c', 'm', 'p', 'e', 's', 't', 'r', 'i', 9, 0,
  /* 6280 */ 'v', 'p', 'c', 'm', 'p', 'i', 's', 't', 'r', 'i', 9, 0,
  /* 6292 */ 'v', 'c', 'v', 't', 't', 's', 'd', '2', 's', 'i', 9, 0,
  /* 6304 */ 'v', 'c', 'v', 't', 's', 'd', '2', 's', 'i', 9, 0,
  /* 6315 */ 'v', 'c', 'v', 't', 't', 's', 's', '2', 's', 'i', 9, 0,
  /* 6327 */ 'v', 'c', 'v', 't', 's', 's', '2', 's', 'i', 9, 0,
  /* 6338 */ 'b', 'l', 'c', 'm', 's', 'k', 9, 0,
  /* 6346 */ 't', 'z', 'm', 's', 'k', 9, 0,
  /* 6353 */ 'c', 'r', 'c', '3', '2', 'l', 9, 0,
  /* 6361 */ 'l', 'e', 'a', 'l', 9, 0,
  /* 6367 */ 'c', 'm', 'o', 'v', 'a', 'l', 9, 0,
  /* 6375 */ 's', 'b', 'b', 'l', 9, 0,
  /* 6381 */ 'm', 'o', 'v', 's', 'b', 'l', 9, 0,
  /* 6389 */ 'f', 's', 'u', 'b', 'l', 9, 0,
  /* 6396 */ 'f', 'i', 's', 'u', 'b', 'l', 9, 0,
  /* 6404 */ 'c', 'm', 'o', 'v', 'b', 'l', 9, 0,
  /* 6412 */ 'm', 'o', 'v', 'z', 'b', 'l', 9, 0,
  /* 6420 */ 'a', 'd', 'c', 'l', 9, 0,
  /* 6426 */ 'd', 'e', 'c', 'l', 9, 0,
  /* 6432 */ 'i', 'n', 'c', 'l', 9, 0,
  /* 6438 */ 'b', 't', 'c', 'l', 9, 0,
  /* 6444 */ 'v', 'm', 'r', 'e', 'a', 'd', 'l', 9, 0,
  /* 6453 */ 'f', 'a', 'd', 'd', 'l', 9, 0,
  /* 6460 */ 'f', 'i', 'a', 'd', 'd', 'l', 9, 0,
  /* 6468 */ 'x', 'a', 'd', 'd', 'l', 9, 0,
  /* 6475 */ 'r', 'd', 's', 'e', 'e', 'd', 'l', 9, 0,
  /* 6484 */ 'f', 'l', 'd', 'l', 9, 0,
  /* 6490 */ 's', 'h', 'l', 'd', 'l', 9, 0,
  /* 6497 */ 'f', 'i', 'l', 'd', 'l', 9, 0,
  /* 6504 */ 'r', 'd', 'r', 'a', 'n', 'd', 'l', 9, 0,
  /* 6513 */ 's', 'h', 'r', 'd', 'l', 9, 0,
  /* 6520 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 'd', 'l', 9, 0,
  /* 6532 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 'd', 'l', 9, 0,
  /* 6545 */ 'c', 'm', 'o', 'v', 'a', 'e', 'l', 9, 0,
  /* 6554 */ 'c', 'm', 'o', 'v', 'b', 'e', 'l', 9, 0,
  /* 6563 */ 'c', 'm', 'o', 'v', 'g', 'e', 'l', 9, 0,
  /* 6572 */ 'c', 'm', 'o', 'v', 'l', 'e', 'l', 9, 0,
  /* 6581 */ 'c', 'm', 'o', 'v', 'n', 'e', 'l', 9, 0,
  /* 6590 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 6601 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 6612 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 6623 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 6634 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 'l', 9, 0,
  /* 6644 */ 'c', 'm', 'o', 'v', 'e', 'l', 9, 0,
  /* 6652 */ 'b', 's', 'f', 'l', 9, 0,
  /* 6658 */ 'n', 'e', 'g', 'l', 9, 0,
  /* 6664 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'l', 9, 0,
  /* 6674 */ 'c', 'm', 'o', 'v', 'g', 'l', 9, 0,
  /* 6682 */ 'p', 'u', 's', 'h', 'l', 9, 0,
  /* 6689 */ 'b', 'z', 'h', 'i', 'l', 9, 0,
  /* 6696 */ 'b', 'l', 's', 'i', 'l', 9, 0,
  /* 6703 */ 'm', 'o', 'v', 'n', 't', 'i', 'l', 9, 0,
  /* 6712 */ 'j', 'l', 9, 0,
  /* 6716 */ 'b', 'l', 's', 'm', 's', 'k', 'l', 9, 0,
  /* 6725 */ 's', 'a', 'l', 'l', 9, 0,
  /* 6731 */ 'r', 'c', 'l', 'l', 9, 0,
  /* 6737 */ 'f', 'i', 'l', 'd', 'l', 'l', 9, 0,
  /* 6745 */ 's', 'h', 'l', 'l', 9, 0,
  /* 6751 */ 'b', 'l', 'c', 'f', 'i', 'l', 'l', 9, 0,
  /* 6760 */ 'b', 'l', 's', 'f', 'i', 'l', 'l', 9, 0,
  /* 6769 */ 'l', 'c', 'a', 'l', 'l', 'l', 9, 0,
  /* 6777 */ 'r', 'o', 'l', 'l', 9, 0,
  /* 6783 */ 'f', 'i', 's', 't', 'p', 'l', 'l', 9, 0,
  /* 6792 */ 'f', 'i', 's', 't', 't', 'p', 'l', 'l', 9, 0,
  /* 6802 */ 'l', 's', 'l', 'l', 9, 0,
  /* 6808 */ 'f', 'm', 'u', 'l', 'l', 9, 0,
  /* 6815 */ 'f', 'i', 'm', 'u', 'l', 'l', 9, 0,
  /* 6823 */ 'c', 'm', 'o', 'v', 'l', 'l', 9, 0,
  /* 6831 */ 'f', 'c', 'o', 'm', 'l', 9, 0,
  /* 6838 */ 'f', 'i', 'c', 'o', 'm', 'l', 9, 0,
  /* 6846 */ 'a', 'n', 'd', 'n', 'l', 9, 0,
  /* 6853 */ 'i', 'n', 'l', 9, 0,
  /* 6858 */ 'c', 'm', 'o', 'v', 'n', 'o', 'l', 9, 0,
  /* 6867 */ 'c', 'm', 'o', 'v', 'o', 'l', 9, 0,
  /* 6875 */ 'b', 's', 'w', 'a', 'p', 'l', 9, 0,
  /* 6883 */ 'p', 'd', 'e', 'p', 'l', 9, 0,
  /* 6890 */ 'c', 'm', 'p', 'l', 9, 0,
  /* 6896 */ 'l', 'j', 'm', 'p', 'l', 9, 0,
  /* 6903 */ 'f', 'c', 'o', 'm', 'p', 'l', 9, 0,
  /* 6911 */ 'f', 'i', 'c', 'o', 'm', 'p', 'l', 9, 0,
  /* 6920 */ 'c', 'm', 'o', 'v', 'n', 'p', 'l', 9, 0,
  /* 6929 */ 'n', 'o', 'p', 'l', 9, 0,
  /* 6935 */ 'p', 'o', 'p', 'l', 9, 0,
  /* 6941 */ 'a', 'r', 'p', 'l', 9, 0,
  /* 6947 */ 'f', 's', 't', 'p', 'l', 9, 0,
  /* 6954 */ 'f', 'i', 's', 't', 'p', 'l', 9, 0,
  /* 6962 */ 'f', 'i', 's', 't', 't', 'p', 'l', 9, 0,
  /* 6971 */ 'c', 'm', 'o', 'v', 'p', 'l', 9, 0,
  /* 6979 */ 'v', 'p', 'm', 'a', 'c', 's', 'd', 'q', 'l', 9, 0,
  /* 6990 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'd', 'q', 'l', 9, 0,
  /* 7002 */ 'l', 'a', 'r', 'l', 9, 0,
  /* 7008 */ 's', 'a', 'r', 'l', 9, 0,
  /* 7014 */ 'f', 's', 'u', 'b', 'r', 'l', 9, 0,
  /* 7022 */ 'f', 'i', 's', 'u', 'b', 'r', 'l', 9, 0,
  /* 7031 */ 'r', 'c', 'r', 'l', 9, 0,
  /* 7037 */ 's', 'h', 'r', 'l', 9, 0,
  /* 7043 */ 'r', 'o', 'r', 'l', 9, 0,
  /* 7049 */ 'x', 'o', 'r', 'l', 9, 0,
  /* 7055 */ 'b', 's', 'r', 'l', 9, 0,
  /* 7061 */ 'b', 'l', 's', 'r', 'l', 9, 0,
  /* 7068 */ 'b', 't', 'r', 'l', 9, 0,
  /* 7074 */ 's', 't', 'r', 'l', 9, 0,
  /* 7080 */ 'b', 'e', 'x', 't', 'r', 'l', 9, 0,
  /* 7088 */ 'f', 'd', 'i', 'v', 'r', 'l', 9, 0,
  /* 7096 */ 'f', 'i', 'd', 'i', 'v', 'r', 'l', 9, 0,
  /* 7105 */ 's', 'c', 'a', 's', 'l', 9, 0,
  /* 7112 */ 'm', 'o', 'v', 'a', 'b', 's', 'l', 9, 0,
  /* 7121 */ 'l', 'd', 's', 'l', 9, 0,
  /* 7127 */ 'l', 'o', 'd', 's', 'l', 9, 0,
  /* 7134 */ 'l', 'e', 's', 'l', 9, 0,
  /* 7140 */ 'l', 'f', 's', 'l', 9, 0,
  /* 7146 */ 'l', 'g', 's', 'l', 9, 0,
  /* 7152 */ 'c', 'm', 'o', 'v', 'n', 's', 'l', 9, 0,
  /* 7161 */ 'c', 'm', 'p', 's', 'l', 9, 0,
  /* 7168 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 's', 'l', 9, 0,
  /* 7180 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 's', 'l', 9, 0,
  /* 7193 */ 'l', 's', 's', 'l', 9, 0,
  /* 7199 */ 'b', 't', 's', 'l', 9, 0,
  /* 7205 */ 'o', 'u', 't', 's', 'l', 9, 0,
  /* 7212 */ 'c', 'm', 'o', 'v', 's', 'l', 9, 0,
  /* 7220 */ 'b', 't', 'l', 9, 0,
  /* 7225 */ 'l', 'g', 'd', 't', 'l', 9, 0,
  /* 7232 */ 's', 'g', 'd', 't', 'l', 9, 0,
  /* 7239 */ 'l', 'i', 'd', 't', 'l', 9, 0,
  /* 7246 */ 's', 'i', 'd', 't', 'l', 9, 0,
  /* 7253 */ 's', 'l', 'd', 't', 'l', 9, 0,
  /* 7260 */ 'l', 'r', 'e', 't', 'l', 9, 0,
  /* 7267 */ 's', 'e', 't', 'l', 9, 0,
  /* 7273 */ 'p', 'o', 'p', 'c', 'n', 't', 'l', 9, 0,
  /* 7282 */ 'l', 'z', 'c', 'n', 't', 'l', 9, 0,
  /* 7290 */ 't', 'z', 'c', 'n', 't', 'l', 9, 0,
  /* 7298 */ 'n', 'o', 't', 'l', 9, 0,
  /* 7304 */ 't', 'e', 's', 't', 'l', 9, 0,
  /* 7311 */ 'f', 's', 't', 'l', 9, 0,
  /* 7317 */ 'f', 'i', 's', 't', 'l', 9, 0,
  /* 7324 */ 'p', 'e', 'x', 't', 'l', 9, 0,
  /* 7331 */ 'p', 'f', 'm', 'u', 'l', 9, 0,
  /* 7338 */ 'f', 'd', 'i', 'v', 'l', 9, 0,
  /* 7345 */ 'f', 'i', 'd', 'i', 'v', 'l', 9, 0,
  /* 7353 */ 'm', 'o', 'v', 'l', 9, 0,
  /* 7359 */ 's', 'm', 's', 'w', 'l', 9, 0,
  /* 7366 */ 'm', 'o', 'v', 's', 'w', 'l', 9, 0,
  /* 7374 */ 'm', 'o', 'v', 'z', 'w', 'l', 9, 0,
  /* 7382 */ 'a', 'd', 'c', 'x', 'l', 9, 0,
  /* 7389 */ 's', 'h', 'l', 'x', 'l', 9, 0,
  /* 7396 */ 'm', 'u', 'l', 'x', 'l', 9, 0,
  /* 7403 */ 'a', 'd', 'o', 'x', 'l', 9, 0,
  /* 7410 */ 's', 'a', 'r', 'x', 'l', 9, 0,
  /* 7417 */ 's', 'h', 'r', 'x', 'l', 9, 0,
  /* 7424 */ 'r', 'o', 'r', 'x', 'l', 9, 0,
  /* 7431 */ 'a', 'a', 'm', 9, 0,
  /* 7436 */ 'f', 'c', 'o', 'm', 9, 0,
  /* 7442 */ 'f', 'u', 'c', 'o', 'm', 9, 0,
  /* 7449 */ 'v', 'p', 'p', 'e', 'r', 'm', 9, 0,
  /* 7457 */ 'v', 'p', 'c', 'm', 'p', 'e', 's', 't', 'r', 'm', 9, 0,
  /* 7469 */ 'v', 'p', 'c', 'm', 'p', 'i', 's', 't', 'r', 'm', 9, 0,
  /* 7481 */ 'v', 'p', 'a', 'n', 'd', 'n', 9, 0,
  /* 7489 */ 'x', 'b', 'e', 'g', 'i', 'n', 9, 0,
  /* 7497 */ 'p', 'f', 'm', 'i', 'n', 9, 0,
  /* 7504 */ 'v', 'm', 'x', 'o', 'n', 9, 0,
  /* 7511 */ 'j', 'o', 9, 0,
  /* 7515 */ 'j', 'n', 'o', 9, 0,
  /* 7520 */ 's', 'e', 't', 'n', 'o', 9, 0,
  /* 7527 */ 's', 'e', 't', 'o', 9, 0,
  /* 7533 */ 'f', 's', 'u', 'b', 'p', 9, 0,
  /* 7540 */ 'p', 'f', 'r', 'c', 'p', 9, 0,
  /* 7547 */ 'f', 'a', 'd', 'd', 'p', 9, 0,
  /* 7554 */ 'j', 'p', 9, 0,
  /* 7558 */ 'f', 'm', 'u', 'l', 'p', 9, 0,
  /* 7565 */ 'j', 'm', 'p', 9, 0,
  /* 7570 */ 'f', 'c', 'o', 'm', 'p', 9, 0,
  /* 7577 */ 'f', 'u', 'c', 'o', 'm', 'p', 9, 0,
  /* 7585 */ 'j', 'n', 'p', 9, 0,
  /* 7590 */ 's', 'e', 't', 'n', 'p', 9, 0,
  /* 7597 */ 'n', 'o', 'p', 9, 0,
  /* 7602 */ 'l', 'o', 'o', 'p', 9, 0,
  /* 7608 */ 'f', 's', 'u', 'b', 'r', 'p', 9, 0,
  /* 7616 */ 'f', 'd', 'i', 'v', 'r', 'p', 9, 0,
  /* 7624 */ 's', 'e', 't', 'p', 9, 0,
  /* 7630 */ 'f', 'b', 's', 't', 'p', 9, 0,
  /* 7637 */ 'f', 's', 't', 'p', 9, 0,
  /* 7643 */ 'v', 'm', 'o', 'v', 'd', 'd', 'u', 'p', 9, 0,
  /* 7653 */ 'v', 'm', 'o', 'v', 's', 'h', 'd', 'u', 'p', 9, 0,
  /* 7664 */ 'v', 'm', 'o', 'v', 's', 'l', 'd', 'u', 'p', 9, 0,
  /* 7675 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 7691 */ 'f', 'd', 'i', 'v', 'p', 9, 0,
  /* 7698 */ 'c', 'r', 'c', '3', '2', 'q', 9, 0,
  /* 7706 */ 'm', 'o', 'v', 'd', 'q', '2', 'q', 9, 0,
  /* 7715 */ 'l', 'e', 'a', 'q', 9, 0,
  /* 7721 */ 'v', 'p', 's', 'h', 'a', 'q', 9, 0,
  /* 7729 */ 'c', 'm', 'o', 'v', 'a', 'q', 9, 0,
  /* 7737 */ 's', 'b', 'b', 'q', 9, 0,
  /* 7743 */ 'v', 'p', 'h', 'a', 'd', 'd', 'b', 'q', 9, 0,
  /* 7753 */ 'm', 'o', 'v', 's', 'b', 'q', 9, 0,
  /* 7761 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'b', 'q', 9, 0,
  /* 7772 */ 'v', 'p', 's', 'u', 'b', 'q', 9, 0,
  /* 7780 */ 'c', 'm', 'o', 'v', 'b', 'q', 9, 0,
  /* 7788 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'q', 9, 0,
  /* 7799 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'q', 9, 0,
  /* 7810 */ 'm', 'o', 'v', 'z', 'b', 'q', 9, 0,
  /* 7818 */ 'a', 'd', 'c', 'q', 9, 0,
  /* 7824 */ 'd', 'e', 'c', 'q', 9, 0,
  /* 7830 */ 'i', 'n', 'c', 'q', 9, 0,
  /* 7836 */ 'b', 't', 'c', 'q', 9, 0,
  /* 7842 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 9, 0,
  /* 7854 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 9, 0,
  /* 7865 */ 'm', 'o', 'v', 'q', '2', 'd', 'q', 9, 0,
  /* 7874 */ 'v', 'c', 'v', 't', 't', 'p', 's', '2', 'd', 'q', 9, 0,
  /* 7886 */ 'v', 'c', 'v', 't', 'p', 's', '2', 'd', 'q', 9, 0,
  /* 7897 */ 'v', 'm', 'r', 'e', 'a', 'd', 'q', 9, 0,
  /* 7906 */ 'v', 'p', 'h', 's', 'u', 'b', 'd', 'q', 9, 0,
  /* 7916 */ 'v', 'p', 'a', 'd', 'd', 'q', 9, 0,
  /* 7924 */ 'x', 'a', 'd', 'd', 'q', 9, 0,
  /* 7931 */ 'v', 'p', 'h', 'a', 'd', 'd', 'd', 'q', 9, 0,
  /* 7941 */ 'r', 'd', 's', 'e', 'e', 'd', 'q', 9, 0,
  /* 7950 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'd', 'q', 9, 0,
  /* 7962 */ 's', 'h', 'l', 'd', 'q', 9, 0,
  /* 7969 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'd', 'q', 9, 0,
  /* 7981 */ 'v', 'p', 's', 'l', 'l', 'd', 'q', 9, 0,
  /* 7990 */ 'v', 'p', 's', 'r', 'l', 'd', 'q', 9, 0,
  /* 7999 */ 'v', 'p', 'm', 'u', 'l', 'd', 'q', 9, 0,
  /* 8008 */ 'r', 'd', 'r', 'a', 'n', 'd', 'q', 9, 0,
  /* 8017 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'q', 'd', 'q', 9, 0,
  /* 8030 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'q', 'd', 'q', 9, 0,
  /* 8043 */ 'v', 'p', 'c', 'l', 'm', 'u', 'l', 'q', 'd', 'q', 9, 0,
  /* 8055 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'q', 9, 0,
  /* 8067 */ 's', 'h', 'r', 'd', 'q', 9, 0,
  /* 8074 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 'd', 'q', 9, 0,
  /* 8086 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 'd', 'q', 9, 0,
  /* 8099 */ 'v', 'm', 'o', 'v', 'n', 't', 'd', 'q', 9, 0,
  /* 8109 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'd', 'q', 9, 0,
  /* 8120 */ 'v', 'p', 'm', 'u', 'l', 'u', 'd', 'q', 9, 0,
  /* 8130 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'd', 'q', 9, 0,
  /* 8141 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'd', 'q', 9, 0,
  /* 8152 */ 'c', 'm', 'o', 'v', 'a', 'e', 'q', 9, 0,
  /* 8161 */ 'c', 'm', 'o', 'v', 'b', 'e', 'q', 9, 0,
  /* 8170 */ 'c', 'm', 'o', 'v', 'g', 'e', 'q', 9, 0,
  /* 8179 */ 'c', 'm', 'o', 'v', 'l', 'e', 'q', 9, 0,
  /* 8188 */ 'c', 'm', 'o', 'v', 'n', 'e', 'q', 9, 0,
  /* 8197 */ 'p', 'f', 'c', 'm', 'p', 'e', 'q', 9, 0,
  /* 8206 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 8217 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 8228 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 8239 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 8250 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 'q', 9, 0,
  /* 8260 */ 'f', 'x', 's', 'a', 'v', 'e', 'q', 9, 0,
  /* 8269 */ 'c', 'm', 'o', 'v', 'e', 'q', 9, 0,
  /* 8277 */ 'b', 's', 'f', 'q', 9, 0,
  /* 8283 */ 'n', 'e', 'g', 'q', 9, 0,
  /* 8289 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'q', 9, 0,
  /* 8299 */ 'c', 'm', 'o', 'v', 'g', 'q', 9, 0,
  /* 8307 */ 'p', 'u', 's', 'h', 'q', 9, 0,
  /* 8314 */ 'b', 'z', 'h', 'i', 'q', 9, 0,
  /* 8321 */ 'b', 'l', 's', 'i', 'q', 9, 0,
  /* 8328 */ 'm', 'o', 'v', 'n', 't', 'i', 'q', 9, 0,
  /* 8337 */ 'b', 'l', 's', 'm', 's', 'k', 'q', 9, 0,
  /* 8346 */ 's', 'a', 'l', 'q', 9, 0,
  /* 8352 */ 'r', 'c', 'l', 'q', 9, 0,
  /* 8358 */ 'v', 'p', 's', 'h', 'l', 'q', 9, 0,
  /* 8366 */ 'c', 'a', 'l', 'l', 'q', 9, 0,
  /* 8373 */ 'v', 'p', 's', 'l', 'l', 'q', 9, 0,
  /* 8381 */ 'r', 'o', 'l', 'q', 9, 0,
  /* 8387 */ 'v', 'p', 's', 'r', 'l', 'q', 9, 0,
  /* 8395 */ 'l', 's', 'l', 'q', 9, 0,
  /* 8401 */ 'm', 'o', 'v', 's', 'l', 'q', 9, 0,
  /* 8409 */ 'i', 'm', 'u', 'l', 'q', 9, 0,
  /* 8416 */ 'c', 'm', 'o', 'v', 'l', 'q', 9, 0,
  /* 8424 */ 'v', 'p', 'c', 'o', 'm', 'q', 9, 0,
  /* 8432 */ 'v', 'p', 'e', 'r', 'm', 'q', 9, 0,
  /* 8440 */ 'a', 'n', 'd', 'n', 'q', 9, 0,
  /* 8447 */ 'c', 'm', 'o', 'v', 'n', 'o', 'q', 9, 0,
  /* 8456 */ 'c', 'm', 'o', 'v', 'o', 'q', 9, 0,
  /* 8464 */ 'b', 's', 'w', 'a', 'p', 'q', 9, 0,
  /* 8472 */ 'p', 'd', 'e', 'p', 'q', 9, 0,
  /* 8479 */ 'v', 'p', 'c', 'm', 'p', 'q', 9, 0,
  /* 8487 */ 'c', 'm', 'o', 'v', 'n', 'p', 'q', 9, 0,
  /* 8496 */ 'p', 'o', 'p', 'q', 9, 0,
  /* 8502 */ 'c', 'm', 'o', 'v', 'p', 'q', 9, 0,
  /* 8510 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'q', 9, 0,
  /* 8520 */ 'v', 'p', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'q', 9, 0,
  /* 8532 */ 'l', 'a', 'r', 'q', 9, 0,
  /* 8538 */ 's', 'a', 'r', 'q', 9, 0,
  /* 8544 */ 'r', 'c', 'r', 'q', 9, 0,
  /* 8550 */ 's', 'h', 'r', 'q', 9, 0,
  /* 8556 */ 'r', 'o', 'r', 'q', 9, 0,
  /* 8562 */ 'f', 'x', 'r', 's', 't', 'o', 'r', 'q', 9, 0,
  /* 8572 */ 'x', 'o', 'r', 'q', 9, 0,
  /* 8578 */ 'b', 's', 'r', 'q', 9, 0,
  /* 8584 */ 'b', 'l', 's', 'r', 'q', 9, 0,
  /* 8591 */ 'v', 'p', 'i', 'n', 's', 'r', 'q', 9, 0,
  /* 8600 */ 'b', 't', 'r', 'q', 9, 0,
  /* 8606 */ 's', 't', 'r', 'q', 9, 0,
  /* 8612 */ 'b', 'e', 'x', 't', 'r', 'q', 9, 0,
  /* 8620 */ 'v', 'p', 'e', 'x', 't', 'r', 'q', 9, 0,
  /* 8629 */ 's', 'c', 'a', 's', 'q', 9, 0,
  /* 8636 */ 'm', 'o', 'v', 'a', 'b', 's', 'q', 9, 0,
  /* 8645 */ 'l', 'o', 'd', 's', 'q', 9, 0,
  /* 8652 */ 'l', 'f', 's', 'q', 9, 0,
  /* 8658 */ 'l', 'g', 's', 'q', 9, 0,
  /* 8664 */ 'c', 'm', 'o', 'v', 'n', 's', 'q', 9, 0,
  /* 8673 */ 'c', 'm', 'p', 's', 'q', 9, 0,
  /* 8680 */ 'v', 'c', 'v', 't', 's', 'i', '2', 's', 's', 'q', 9, 0,
  /* 8692 */ 'v', 'c', 'v', 't', 'u', 's', 'i', '2', 's', 's', 'q', 9, 0,
  /* 8705 */ 'l', 's', 's', 'q', 9, 0,
  /* 8711 */ 'b', 't', 's', 'q', 9, 0,
  /* 8717 */ 'c', 'm', 'o', 'v', 's', 'q', 9, 0,
  /* 8725 */ 'b', 't', 'q', 9, 0,
  /* 8730 */ 'l', 'g', 'd', 't', 'q', 9, 0,
  /* 8737 */ 's', 'g', 'd', 't', 'q', 9, 0,
  /* 8744 */ 'l', 'i', 'd', 't', 'q', 9, 0,
  /* 8751 */ 's', 'i', 'd', 't', 'q', 9, 0,
  /* 8758 */ 's', 'l', 'd', 't', 'q', 9, 0,
  /* 8765 */ 'l', 'r', 'e', 't', 'q', 9, 0,
  /* 8772 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'q', 9, 0,
  /* 8782 */ 'p', 'o', 'p', 'c', 'n', 't', 'q', 9, 0,
  /* 8791 */ 'l', 'z', 'c', 'n', 't', 'q', 9, 0,
  /* 8799 */ 't', 'z', 'c', 'n', 't', 'q', 9, 0,
  /* 8807 */ 'm', 'o', 'v', 'n', 't', 'q', 9, 0,
  /* 8815 */ 'n', 'o', 't', 'q', 9, 0,
  /* 8821 */ 'v', 'p', 'r', 'o', 't', 'q', 9, 0,
  /* 8829 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 'q', 9, 0,
  /* 8840 */ 'i', 'n', 's', 'e', 'r', 't', 'q', 9, 0,
  /* 8849 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'q', 9, 0,
  /* 8863 */ 't', 'e', 's', 't', 'q', 9, 0,
  /* 8870 */ 'p', 'e', 'x', 't', 'q', 9, 0,
  /* 8877 */ 'v', 'p', 'c', 'o', 'm', 'u', 'q', 9, 0,
  /* 8886 */ 'v', 'p', 'c', 'm', 'p', 'u', 'q', 9, 0,
  /* 8895 */ 'i', 'd', 'i', 'v', 'q', 9, 0,
  /* 8902 */ 'v', 'p', 's', 'l', 'l', 'v', 'q', 9, 0,
  /* 8911 */ 'v', 'p', 's', 'r', 'l', 'v', 'q', 9, 0,
  /* 8920 */ 'v', 'p', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'q', 9, 0,
  /* 8932 */ 'v', 'm', 'o', 'v', 'q', 9, 0,
  /* 8939 */ 'v', 'p', 'h', 'a', 'd', 'd', 'w', 'q', 9, 0,
  /* 8949 */ 's', 'm', 's', 'w', 'q', 9, 0,
  /* 8956 */ 'm', 'o', 'v', 's', 'w', 'q', 9, 0,
  /* 8964 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'w', 'q', 9, 0,
  /* 8975 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'w', 'q', 9, 0,
  /* 8986 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'w', 'q', 9, 0,
  /* 8997 */ 'm', 'o', 'v', 'z', 'w', 'q', 9, 0,
  /* 9005 */ 'a', 'd', 'c', 'x', 'q', 9, 0,
  /* 9012 */ 's', 'h', 'l', 'x', 'q', 9, 0,
  /* 9019 */ 'm', 'u', 'l', 'x', 'q', 9, 0,
  /* 9026 */ 'a', 'd', 'o', 'x', 'q', 9, 0,
  /* 9033 */ 's', 'a', 'r', 'x', 'q', 9, 0,
  /* 9040 */ 's', 'h', 'r', 'x', 'q', 9, 0,
  /* 9047 */ 'r', 'o', 'r', 'x', 'q', 9, 0,
  /* 9054 */ 'v', 'm', 'c', 'l', 'e', 'a', 'r', 9, 0,
  /* 9063 */ 'p', 'f', 's', 'u', 'b', 'r', 9, 0,
  /* 9071 */ 'e', 'n', 't', 'e', 'r', 9, 0,
  /* 9078 */ 'v', 'p', 'a', 'l', 'i', 'g', 'n', 'r', 9, 0,
  /* 9088 */ 'v', 'p', 'o', 'r', 9, 0,
  /* 9094 */ 'f', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 9102 */ 'f', 'x', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 9111 */ 'v', 'p', 'x', 'o', 'r', 9, 0,
  /* 9118 */ 'v', 'e', 'r', 'r', 9, 0,
  /* 9124 */ 'v', 'l', 'd', 'm', 'x', 'c', 's', 'r', 9, 0,
  /* 9134 */ 'v', 's', 't', 'm', 'x', 'c', 's', 'r', 9, 0,
  /* 9144 */ 'b', 'e', 'x', 't', 'r', 9, 0,
  /* 9151 */ 'f', 'd', 'i', 'v', 'r', 9, 0,
  /* 9158 */ 'f', 's', 'u', 'b', 's', 9, 0,
  /* 9165 */ 'f', 'i', 's', 'u', 'b', 's', 9, 0,
  /* 9173 */ 'b', 'l', 'c', 's', 9, 0,
  /* 9179 */ 'f', 'a', 'd', 'd', 's', 9, 0,
  /* 9186 */ 'f', 'i', 'a', 'd', 'd', 's', 9, 0,
  /* 9194 */ 'f', 'l', 'd', 's', 9, 0,
  /* 9200 */ 'f', 'i', 'l', 'd', 's', 9, 0,
  /* 9207 */ 'j', 's', 9, 0,
  /* 9211 */ 'f', 'm', 'u', 'l', 's', 9, 0,
  /* 9218 */ 'f', 'i', 'm', 'u', 'l', 's', 9, 0,
  /* 9226 */ 'f', 'c', 'o', 'm', 's', 9, 0,
  /* 9233 */ 'f', 'i', 'c', 'o', 'm', 's', 9, 0,
  /* 9241 */ 'j', 'n', 's', 9, 0,
  /* 9246 */ 's', 'e', 't', 'n', 's', 9, 0,
  /* 9253 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '3', '1', 'p', 's', 9, 0,
  /* 9269 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 's', 9, 0,
  /* 9282 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 'p', 's', 9, 0,
  /* 9296 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '3', '1', 'p', 's', 9, 0,
  /* 9312 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 's', 9, 0,
  /* 9325 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 'p', 's', 9, 0,
  /* 9339 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '1', '3', '2', 'p', 's', 9, 0,
  /* 9355 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 9, 0,
  /* 9368 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 'p', 's', 9, 0,
  /* 9382 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 9, 0,
  /* 9398 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 9, 0,
  /* 9411 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 'p', 's', 9, 0,
  /* 9425 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 9, 0,
  /* 9436 */ 'v', 'c', 'v', 't', 'p', 'h', '2', 'p', 's', 9, 0,
  /* 9447 */ 'c', 'v', 't', 'p', 'i', '2', 'p', 's', 9, 0,
  /* 9457 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', '2', 'p', 's', 9, 0,
  /* 9469 */ 'v', 'c', 'v', 't', 'd', 'q', '2', 'p', 's', 9, 0,
  /* 9480 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', '2', '1', '3', 'p', 's', 9, 0,
  /* 9496 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 9, 0,
  /* 9509 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 'p', 's', 9, 0,
  /* 9523 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 9, 0,
  /* 9539 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 9, 0,
  /* 9552 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 'p', 's', 9, 0,
  /* 9566 */ 'v', 'm', 'o', 'v', 'a', 'p', 's', 9, 0,
  /* 9575 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 9588 */ 'v', 'a', 'd', 'd', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 9599 */ 'v', 'h', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 9608 */ 'v', 'f', 'm', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 9618 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 9629 */ 'v', 's', 'u', 'b', 'p', 's', 9, 0,
  /* 9637 */ 'v', 'f', 'm', 's', 'u', 'b', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 9650 */ 'v', 'h', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 9659 */ 'v', 'f', 'm', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 9669 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 9680 */ 'v', 'a', 'd', 'd', 'p', 's', 9, 0,
  /* 9688 */ 'v', 'a', 'n', 'd', 'p', 's', 9, 0,
  /* 9696 */ 'v', 'b', 'l', 'e', 'n', 'd', 'p', 's', 9, 0,
  /* 9706 */ 'v', 'r', 'o', 'u', 'n', 'd', 'p', 's', 9, 0,
  /* 9716 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'd', 'p', 's', 9, 0,
  /* 9728 */ 'v', 's', 'h', 'u', 'f', 'p', 's', 9, 0,
  /* 9737 */ 'v', 'u', 'n', 'p', 'c', 'k', 'h', 'p', 's', 9, 0,
  /* 9748 */ 'v', 'm', 'o', 'v', 'l', 'h', 'p', 's', 9, 0,
  /* 9758 */ 'v', 'm', 'o', 'v', 'h', 'p', 's', 9, 0,
  /* 9767 */ 'v', 'm', 'o', 'v', 'm', 's', 'k', 'p', 's', 9, 0,
  /* 9778 */ 'v', 'm', 'o', 'v', 'h', 'l', 'p', 's', 9, 0,
  /* 9788 */ 'v', 'p', 'e', 'r', 'm', 'i', 'l', 'p', 's', 9, 0,
  /* 9799 */ 'v', 'u', 'n', 'p', 'c', 'k', 'l', 'p', 's', 9, 0,
  /* 9810 */ 'v', 'm', 'u', 'l', 'p', 's', 9, 0,
  /* 9818 */ 'v', 'm', 'o', 'v', 'l', 'p', 's', 9, 0,
  /* 9827 */ 'f', 'c', 'o', 'm', 'p', 's', 9, 0,
  /* 9835 */ 'f', 'i', 'c', 'o', 'm', 'p', 's', 9, 0,
  /* 9844 */ 'v', 'p', 'e', 'r', 'm', 'p', 's', 9, 0,
  /* 9853 */ 'v', 'a', 'n', 'd', 'n', 'p', 's', 9, 0,
  /* 9862 */ 'v', 'm', 'i', 'n', 'p', 's', 9, 0,
  /* 9870 */ 'v', 'r', 'c', 'p', 'p', 's', 9, 0,
  /* 9878 */ 'v', 'd', 'p', 'p', 's', 9, 0,
  /* 9885 */ 'v', 'c', 'm', 'p', 'p', 's', 9, 0,
  /* 9893 */ 'v', 'g', 'a', 't', 'h', 'e', 'r', 'q', 'p', 's', 9, 0,
  /* 9905 */ 'v', 'o', 'r', 'p', 's', 9, 0,
  /* 9912 */ 'v', 'x', 'o', 'r', 'p', 's', 9, 0,
  /* 9920 */ 'v', 'e', 'x', 't', 'r', 'a', 'c', 't', 'p', 's', 9, 0,
  /* 9932 */ 'v', 'm', 'o', 'v', 'n', 't', 'p', 's', 9, 0,
  /* 9942 */ 'v', 'i', 'n', 's', 'e', 'r', 't', 'p', 's', 9, 0,
  /* 9953 */ 'v', 'r', 's', 'q', 'r', 't', 'p', 's', 9, 0,
  /* 9963 */ 'v', 's', 'q', 'r', 't', 'p', 's', 9, 0,
  /* 9972 */ 'v', 't', 'e', 's', 't', 'p', 's', 9, 0,
  /* 9981 */ 'f', 's', 't', 'p', 's', 9, 0,
  /* 9988 */ 'f', 'i', 's', 't', 'p', 's', 9, 0,
  /* 9996 */ 'f', 'i', 's', 't', 't', 'p', 's', 9, 0,
  /* 10005 */ 'v', 'm', 'o', 'v', 'u', 'p', 's', 9, 0,
  /* 10014 */ 'v', 'b', 'l', 'e', 'n', 'd', 'v', 'p', 's', 9, 0,
  /* 10025 */ 'v', 'd', 'i', 'v', 'p', 's', 9, 0,
  /* 10033 */ 'v', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'p', 's', 9, 0,
  /* 10045 */ 'v', 'm', 'a', 'x', 'p', 's', 9, 0,
  /* 10053 */ 'v', 'f', 'r', 'c', 'z', 'p', 's', 9, 0,
  /* 10062 */ 'f', 's', 'u', 'b', 'r', 's', 9, 0,
  /* 10070 */ 'f', 'i', 's', 'u', 'b', 'r', 's', 9, 0,
  /* 10079 */ 'f', 'd', 'i', 'v', 'r', 's', 9, 0,
  /* 10087 */ 'f', 'i', 'd', 'i', 'v', 'r', 's', 9, 0,
  /* 10096 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '3', '1', 's', 's', 9, 0,
  /* 10109 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '3', '1', 's', 's', 9, 0,
  /* 10123 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 's', 9, 0,
  /* 10136 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '3', '1', 's', 's', 9, 0,
  /* 10150 */ 'v', 'f', 'm', 's', 'u', 'b', '1', '3', '2', 's', 's', 9, 0,
  /* 10163 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '1', '3', '2', 's', 's', 9, 0,
  /* 10177 */ 'v', 'f', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 's', 9, 0,
  /* 10190 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '1', '3', '2', 's', 's', 9, 0,
  /* 10204 */ 'v', 'c', 'v', 't', 's', 'd', '2', 's', 's', 9, 0,
  /* 10215 */ 'v', 'f', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 9, 0,
  /* 10228 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', '2', '1', '3', 's', 's', 9, 0,
  /* 10242 */ 'v', 'f', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 9, 0,
  /* 10255 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', '2', '1', '3', 's', 's', 9, 0,
  /* 10269 */ 'v', 'f', 'm', 's', 'u', 'b', 's', 's', 9, 0,
  /* 10279 */ 'v', 'f', 'n', 'm', 's', 'u', 'b', 's', 's', 9, 0,
  /* 10290 */ 'v', 's', 'u', 'b', 's', 's', 9, 0,
  /* 10298 */ 'v', 'f', 'm', 'a', 'd', 'd', 's', 's', 9, 0,
  /* 10308 */ 'v', 'f', 'n', 'm', 'a', 'd', 'd', 's', 's', 9, 0,
  /* 10319 */ 'v', 'a', 'd', 'd', 's', 's', 9, 0,
  /* 10327 */ 'v', 'r', 'o', 'u', 'n', 'd', 's', 's', 9, 0,
  /* 10337 */ 'v', 'u', 'c', 'o', 'm', 'i', 's', 's', 9, 0,
  /* 10347 */ 'v', 'c', 'o', 'm', 'i', 's', 's', 9, 0,
  /* 10356 */ 'v', 'm', 'u', 'l', 's', 's', 9, 0,
  /* 10364 */ 'v', 'm', 'i', 'n', 's', 's', 9, 0,
  /* 10372 */ 'v', 'r', 'c', 'p', 's', 's', 9, 0,
  /* 10380 */ 'v', 'c', 'm', 'p', 's', 's', 9, 0,
  /* 10388 */ 'm', 'o', 'v', 'n', 't', 's', 's', 9, 0,
  /* 10397 */ 'v', 'r', 's', 'q', 'r', 't', 's', 's', 9, 0,
  /* 10407 */ 'v', 's', 'q', 'r', 't', 's', 's', 9, 0,
  /* 10416 */ 'v', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 's', 's', 9, 0,
  /* 10430 */ 'v', 'd', 'i', 'v', 's', 's', 9, 0,
  /* 10438 */ 'v', 'm', 'o', 'v', 's', 's', 9, 0,
  /* 10446 */ 'v', 'm', 'a', 'x', 's', 's', 9, 0,
  /* 10454 */ 'v', 'f', 'r', 'c', 'z', 's', 's', 9, 0,
  /* 10463 */ 's', 'e', 't', 's', 9, 0,
  /* 10469 */ 'f', 's', 't', 's', 9, 0,
  /* 10475 */ 'f', 'i', 's', 't', 's', 9, 0,
  /* 10482 */ 'f', 'd', 'i', 'v', 's', 9, 0,
  /* 10489 */ 'f', 'i', 'd', 'i', 'v', 's', 9, 0,
  /* 10497 */ 'f', 'l', 'd', 't', 9, 0,
  /* 10503 */ 'p', 'f', 'c', 'm', 'p', 'g', 't', 9, 0,
  /* 10512 */ 'i', 'n', 't', 9, 0,
  /* 10517 */ 'i', 'n', 'v', 'e', 'p', 't', 9, 0,
  /* 10525 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 9, 0,
  /* 10535 */ 'f', 's', 't', 'p', 't', 9, 0,
  /* 10542 */ 'x', 'a', 'b', 'o', 'r', 't', 9, 0,
  /* 10550 */ 'p', 'f', 'r', 's', 'q', 'r', 't', 9, 0,
  /* 10559 */ 'v', 'a', 'e', 's', 'd', 'e', 'c', 'l', 'a', 's', 't', 9, 0,
  /* 10572 */ 'v', 'a', 'e', 's', 'e', 'n', 'c', 'l', 'a', 's', 't', 9, 0,
  /* 10585 */ 'v', 'p', 't', 'e', 's', 't', 9, 0,
  /* 10593 */ 'f', 's', 't', 9, 0,
  /* 10598 */ 'v', 'a', 'e', 's', 'k', 'e', 'y', 'g', 'e', 'n', 'a', 's', 's', 'i', 's', 't', 9, 0,
  /* 10616 */ 'v', 'm', 'p', 't', 'r', 's', 't', 9, 0,
  /* 10625 */ 'f', 'c', 'm', 'o', 'v', 'n', 'u', 9, 0,
  /* 10634 */ 'v', 'l', 'd', 'd', 'q', 'u', 9, 0,
  /* 10642 */ 'v', 'm', 'a', 's', 'k', 'm', 'o', 'v', 'd', 'q', 'u', 9, 0,
  /* 10655 */ 'v', 'm', 'o', 'v', 'd', 'q', 'u', 9, 0,
  /* 10664 */ 'f', 'c', 'm', 'o', 'v', 'u', 9, 0,
  /* 10672 */ 'f', 'd', 'i', 'v', 9, 0,
  /* 10678 */ 'f', 'l', 'd', 'e', 'n', 'v', 9, 0,
  /* 10686 */ 'f', 'n', 's', 't', 'e', 'n', 'v', 9, 0,
  /* 10695 */ 'v', 'p', 'c', 'm', 'o', 'v', 9, 0,
  /* 10703 */ 'c', 'r', 'c', '3', '2', 'w', 9, 0,
  /* 10711 */ 'l', 'e', 'a', 'w', 9, 0,
  /* 10717 */ 'v', 'p', 's', 'h', 'a', 'w', 9, 0,
  /* 10725 */ 'v', 'p', 's', 'r', 'a', 'w', 9, 0,
  /* 10733 */ 'c', 'm', 'o', 'v', 'a', 'w', 9, 0,
  /* 10741 */ 's', 'b', 'b', 'w', 9, 0,
  /* 10747 */ 'v', 'p', 'h', 's', 'u', 'b', 'b', 'w', 9, 0,
  /* 10757 */ 'v', 'm', 'p', 's', 'a', 'd', 'b', 'w', 9, 0,
  /* 10767 */ 'v', 'p', 's', 'a', 'd', 'b', 'w', 9, 0,
  /* 10776 */ 'v', 'p', 'h', 'a', 'd', 'd', 'b', 'w', 9, 0,
  /* 10786 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'h', 'b', 'w', 9, 0,
  /* 10798 */ 'v', 'p', 'u', 'n', 'p', 'c', 'k', 'l', 'b', 'w', 9, 0,
  /* 10810 */ 'm', 'o', 'v', 's', 'b', 'w', 9, 0,
  /* 10818 */ 'v', 'p', 'h', 'a', 'd', 'd', 'u', 'b', 'w', 9, 0,
  /* 10829 */ 'v', 'p', 'h', 's', 'u', 'b', 'w', 9, 0,
  /* 10838 */ 'v', 'p', 's', 'u', 'b', 'w', 9, 0,
  /* 10846 */ 'c', 'm', 'o', 'v', 'b', 'w', 9, 0,
  /* 10854 */ 'v', 'p', 'm', 'o', 'v', 's', 'x', 'b', 'w', 9, 0,
  /* 10865 */ 'v', 'p', 'm', 'o', 'v', 'z', 'x', 'b', 'w', 9, 0,
  /* 10876 */ 'm', 'o', 'v', 'z', 'b', 'w', 9, 0,
  /* 10884 */ 'a', 'd', 'c', 'w', 9, 0,
  /* 10890 */ 'f', 'l', 'd', 'c', 'w', 9, 0,
  /* 10897 */ 'd', 'e', 'c', 'w', 9, 0,
  /* 10903 */ 'i', 'n', 'c', 'w', 9, 0,
  /* 10909 */ 'b', 't', 'c', 'w', 9, 0,
  /* 10915 */ 'f', 'n', 's', 't', 'c', 'w', 9, 0,
  /* 10923 */ 'v', 'p', 'h', 'a', 'd', 'd', 'w', 9, 0,
  /* 10932 */ 'v', 'p', 'a', 'd', 'd', 'w', 9, 0,
  /* 10940 */ 'x', 'a', 'd', 'd', 'w', 9, 0,
  /* 10947 */ 'r', 'd', 's', 'e', 'e', 'd', 'w', 9, 0,
  /* 10956 */ 's', 'h', 'l', 'd', 'w', 9, 0,
  /* 10963 */ 'r', 'd', 'r', 'a', 'n', 'd', 'w', 9, 0,
  /* 10972 */ 'v', 'p', 'b', 'l', 'e', 'n', 'd', 'w', 9, 0,
  /* 10982 */ 's', 'h', 'r', 'd', 'w', 9, 0,
  /* 10989 */ 'v', 'p', 'a', 'c', 'k', 's', 's', 'd', 'w', 9, 0,
  /* 11000 */ 'v', 'p', 'a', 'c', 'k', 'u', 's', 'd', 'w', 9, 0,
  /* 11011 */ 'c', 'm', 'o', 'v', 'a', 'e', 'w', 9, 0,
  /* 11020 */ 'c', 'm', 'o', 'v', 'b', 'e', 'w', 9, 0,
  /* 11029 */ 'c', 'm', 'o', 'v', 'g', 'e', 'w', 9, 0,
  /* 11038 */ 'c', 'm', 'o', 'v', 'l', 'e', 'w', 9, 0,
  /* 11047 */ 'c', 'm', 'o', 'v', 'n', 'e', 'w', 9, 0,
  /* 11056 */ 'c', 'm', 'o', 'v', 'e', 'w', 9, 0,
  /* 11064 */ 'p', 'i', '2', 'f', 'w', 9, 0,
  /* 11071 */ 'b', 's', 'f', 'w', 9, 0,
  /* 11077 */ 'p', 's', 'h', 'u', 'f', 'w', 9, 0,
  /* 11085 */ 'n', 'e', 'g', 'w', 9, 0,
  /* 11091 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'w', 9, 0,
  /* 11101 */ 'v', 'p', 'a', 'v', 'g', 'w', 9, 0,
  /* 11109 */ 'c', 'm', 'o', 'v', 'g', 'w', 9, 0,
  /* 11117 */ 'p', 'r', 'e', 'f', 'e', 't', 'c', 'h', 'w', 9, 0,
  /* 11128 */ 'v', 'p', 's', 'h', 'u', 'f', 'h', 'w', 9, 0,
  /* 11138 */ 'v', 'p', 'm', 'u', 'l', 'h', 'w', 9, 0,
  /* 11147 */ 'p', 'u', 's', 'h', 'w', 9, 0,
  /* 11154 */ 'p', 'f', '2', 'i', 'w', 9, 0,
  /* 11161 */ 's', 'a', 'l', 'w', 9, 0,
  /* 11167 */ 'r', 'c', 'l', 'w', 9, 0,
  /* 11173 */ 'v', 'p', 's', 'h', 'u', 'f', 'l', 'w', 9, 0,
  /* 11183 */ 'v', 'p', 's', 'h', 'l', 'w', 9, 0,
  /* 11191 */ 'l', 'c', 'a', 'l', 'l', 'w', 9, 0,
  /* 11199 */ 'v', 'p', 's', 'l', 'l', 'w', 9, 0,
  /* 11207 */ 'v', 'p', 'm', 'u', 'l', 'l', 'w', 9, 0,
  /* 11216 */ 'r', 'o', 'l', 'w', 9, 0,
  /* 11222 */ 'v', 'p', 's', 'r', 'l', 'w', 9, 0,
  /* 11230 */ 'l', 's', 'l', 'w', 9, 0,
  /* 11236 */ 'i', 'm', 'u', 'l', 'w', 9, 0,
  /* 11243 */ 'c', 'm', 'o', 'v', 'l', 'w', 9, 0,
  /* 11251 */ 'v', 'p', 'c', 'o', 'm', 'w', 9, 0,
  /* 11259 */ 'v', 'p', 's', 'i', 'g', 'n', 'w', 9, 0,
  /* 11268 */ 'i', 'n', 'w', 9, 0,
  /* 11273 */ 'c', 'm', 'o', 'v', 'n', 'o', 'w', 9, 0,
  /* 11282 */ 'c', 'm', 'o', 'v', 'o', 'w', 9, 0,
  /* 11290 */ 'c', 'm', 'p', 'w', 9, 0,
  /* 11296 */ 'l', 'j', 'm', 'p', 'w', 9, 0,
  /* 11303 */ 'c', 'm', 'o', 'v', 'n', 'p', 'w', 9, 0,
  /* 11312 */ 'n', 'o', 'p', 'w', 9, 0,
  /* 11318 */ 'p', 'o', 'p', 'w', 9, 0,
  /* 11324 */ 'c', 'm', 'o', 'v', 'p', 'w', 9, 0,
  /* 11332 */ 'v', 'p', 'c', 'm', 'p', 'e', 'q', 'w', 9, 0,
  /* 11342 */ 'l', 'a', 'r', 'w', 9, 0,
  /* 11348 */ 's', 'a', 'r', 'w', 9, 0,
  /* 11354 */ 'r', 'c', 'r', 'w', 9, 0,
  /* 11360 */ 'v', 'e', 'r', 'w', 9, 0,
  /* 11366 */ 'p', 'm', 'u', 'l', 'h', 'r', 'w', 9, 0,
  /* 11375 */ 's', 'h', 'r', 'w', 9, 0,
  /* 11381 */ 'r', 'o', 'r', 'w', 9, 0,
  /* 11387 */ 'x', 'o', 'r', 'w', 9, 0,
  /* 11393 */ 'b', 's', 'r', 'w', 9, 0,
  /* 11399 */ 'v', 'p', 'i', 'n', 's', 'r', 'w', 9, 0,
  /* 11408 */ 'b', 't', 'r', 'w', 9, 0,
  /* 11414 */ 'l', 't', 'r', 'w', 9, 0,
  /* 11420 */ 's', 't', 'r', 'w', 9, 0,
  /* 11426 */ 'v', 'p', 'e', 'x', 't', 'r', 'w', 9, 0,
  /* 11435 */ 's', 'c', 'a', 's', 'w', 9, 0,
  /* 11442 */ 'v', 'p', 'a', 'b', 's', 'w', 9, 0,
  /* 11450 */ 'm', 'o', 'v', 'a', 'b', 's', 'w', 9, 0,
  /* 11459 */ 'v', 'p', 'm', 'a', 'd', 'd', 'u', 'b', 's', 'w', 9, 0,
  /* 11471 */ 'v', 'p', 'h', 's', 'u', 'b', 's', 'w', 9, 0,
  /* 11481 */ 'v', 'p', 's', 'u', 'b', 's', 'w', 9, 0,
  /* 11490 */ 'v', 'p', 'h', 'a', 'd', 'd', 's', 'w', 9, 0,
  /* 11500 */ 'v', 'p', 'a', 'd', 'd', 's', 'w', 9, 0,
  /* 11509 */ 'l', 'd', 's', 'w', 9, 0,
  /* 11515 */ 'l', 'o', 'd', 's', 'w', 9, 0,
  /* 11522 */ 'l', 'e', 's', 'w', 9, 0,
  /* 11528 */ 'l', 'f', 's', 'w', 9, 0,
  /* 11534 */ 'l', 'g', 's', 'w', 9, 0,
  /* 11540 */ 'v', 'p', 'm', 'i', 'n', 's', 'w', 9, 0,
  /* 11549 */ 'c', 'm', 'o', 'v', 'n', 's', 'w', 9, 0,
  /* 11558 */ 'c', 'm', 'p', 's', 'w', 9, 0,
  /* 11565 */ 'v', 'p', 'm', 'u', 'l', 'h', 'r', 's', 'w', 9, 0,
  /* 11576 */ 'l', 's', 's', 'w', 9, 0,
  /* 11582 */ 'b', 't', 's', 'w', 9, 0,
  /* 11588 */ 'f', 'n', 's', 't', 's', 'w', 9, 0,
  /* 11596 */ 'o', 'u', 't', 's', 'w', 9, 0,
  /* 11603 */ 'v', 'p', 's', 'u', 'b', 'u', 's', 'w', 9, 0,
  /* 11613 */ 'v', 'p', 'a', 'd', 'd', 'u', 's', 'w', 9, 0,
  /* 11623 */ 'c', 'm', 'o', 'v', 's', 'w', 9, 0,
  /* 11631 */ 'v', 'p', 'm', 'a', 'x', 's', 'w', 9, 0,
  /* 11640 */ 'b', 't', 'w', 9, 0,
  /* 11645 */ 'l', 'g', 'd', 't', 'w', 9, 0,
  /* 11652 */ 's', 'g', 'd', 't', 'w', 9, 0,
  /* 11659 */ 'l', 'i', 'd', 't', 'w', 9, 0,
  /* 11666 */ 's', 'i', 'd', 't', 'w', 9, 0,
  /* 11673 */ 'l', 'l', 'd', 't', 'w', 9, 0,
  /* 11680 */ 's', 'l', 'd', 't', 'w', 9, 0,
  /* 11687 */ 'l', 'r', 'e', 't', 'w', 9, 0,
  /* 11694 */ 'v', 'p', 'c', 'm', 'p', 'g', 't', 'w', 9, 0,
  /* 11704 */ 'p', 'o', 'p', 'c', 'n', 't', 'w', 9, 0,
  /* 11713 */ 'l', 'z', 'c', 'n', 't', 'w', 9, 0,
  /* 11721 */ 't', 'z', 'c', 'n', 't', 'w', 9, 0,
  /* 11729 */ 'n', 'o', 't', 'w', 9, 0,
  /* 11735 */ 'v', 'p', 'r', 'o', 't', 'w', 9, 0,
  /* 11743 */ 'v', 'p', 'b', 'r', 'o', 'a', 'd', 'c', 'a', 's', 't', 'w', 9, 0,
  /* 11757 */ 't', 'e', 's', 't', 'w', 9, 0,
  /* 11764 */ 'v', 'p', 'm', 'u', 'l', 'h', 'u', 'w', 9, 0,
  /* 11774 */ 'v', 'p', 'c', 'o', 'm', 'u', 'w', 9, 0,
  /* 11783 */ 'v', 'p', 'm', 'i', 'n', 'u', 'w', 9, 0,
  /* 11792 */ 'v', 'p', 'h', 'm', 'i', 'n', 'p', 'o', 's', 'u', 'w', 9, 0,
  /* 11805 */ 'v', 'p', 'm', 'a', 'x', 'u', 'w', 9, 0,
  /* 11814 */ 'i', 'd', 'i', 'v', 'w', 9, 0,
  /* 11821 */ 'm', 'o', 'v', 'w', 9, 0,
  /* 11827 */ 'v', 'p', 'm', 'a', 'c', 's', 'w', 'w', 9, 0,
  /* 11837 */ 'l', 'm', 's', 'w', 'w', 9, 0,
  /* 11844 */ 's', 'm', 's', 'w', 'w', 9, 0,
  /* 11851 */ 'v', 'p', 'm', 'a', 'c', 's', 's', 'w', 'w', 9, 0,
  /* 11862 */ 'p', 'f', 'm', 'a', 'x', 9, 0,
  /* 11869 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 'x', 9, 0,
  /* 11882 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 'x', 9, 0,
  /* 11894 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 'x', 9, 0,
  /* 11906 */ 'v', 'c', 'v', 't', 't', 'p', 'd', '2', 'd', 'q', 'y', 9, 0,
  /* 11919 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'd', 'q', 'y', 9, 0,
  /* 11931 */ 'v', 'c', 'v', 't', 'p', 'd', '2', 'p', 's', 'y', 9, 0,
  /* 11943 */ 'j', 'e', 'c', 'x', 'z', 9, 0,
  /* 11950 */ 'j', 'c', 'x', 'z', 9, 0,
  /* 11956 */ 'j', 'r', 'c', 'x', 'z', 9, 0,
  /* 11963 */ 'f', 's', 'u', 'b', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 11977 */ 'f', 'a', 'd', 'd', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 11991 */ 'f', 's', 't', 'p', 'n', 'c', 'e', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 12008 */ 'f', 'm', 'u', 'l', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 12022 */ 'f', 's', 't', 'p', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 12036 */ 'f', 's', 'u', 'b', 'r', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 12051 */ 'f', 'd', 'i', 'v', 'r', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 12066 */ 'f', 'd', 'i', 'v', 9, '%', 's', 't', '(', '0', ')', ',', 32, 0,
  /* 12080 */ 's', 'a', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 12090 */ 'r', 'c', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 12100 */ 's', 'h', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 12110 */ 'r', 'o', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 12120 */ 's', 'a', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 12130 */ 'r', 'c', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 12140 */ 's', 'h', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 12150 */ 'r', 'o', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 12160 */ 's', 'a', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 12170 */ 'r', 'c', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 12180 */ 's', 'h', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 12190 */ 'r', 'o', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 12200 */ 's', 'a', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 12210 */ 'r', 'c', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 12220 */ 's', 'h', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 12230 */ 'r', 'o', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 12240 */ 's', 'a', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 12250 */ 'r', 'c', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 12260 */ 's', 'h', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 12270 */ 'r', 'o', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 12280 */ 's', 'a', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 12290 */ 'r', 'c', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 12300 */ 's', 'h', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 12310 */ 'r', 'o', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 12320 */ 's', 'a', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 12330 */ 'r', 'c', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 12340 */ 's', 'h', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 12350 */ 'r', 'o', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 12360 */ 's', 'a', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 12370 */ 'r', 'c', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 12380 */ 's', 'h', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 12390 */ 'r', 'o', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 12400 */ 'm', 'o', 'v', 'a', 'b', 's', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 12414 */ 's', 't', 'o', 's', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 12426 */ 'o', 'u', 't', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 12437 */ 'm', 'o', 'v', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 12448 */ 's', 'a', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12459 */ 'r', 'c', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12470 */ 's', 'h', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12481 */ 'r', 'o', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12492 */ 's', 'a', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12503 */ 'r', 'c', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12514 */ 's', 'h', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12525 */ 'r', 'o', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12536 */ 's', 'h', 'l', 'd', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12548 */ 's', 'h', 'r', 'd', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12560 */ 's', 'a', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12571 */ 'r', 'c', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12582 */ 's', 'h', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12593 */ 'r', 'o', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12604 */ 's', 'a', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12615 */ 'r', 'c', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12626 */ 's', 'h', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12637 */ 'r', 'o', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12648 */ 's', 'h', 'l', 'd', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12660 */ 's', 'h', 'r', 'd', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12672 */ 's', 'a', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12683 */ 'r', 'c', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12694 */ 's', 'h', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12705 */ 'r', 'o', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12716 */ 's', 'a', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12727 */ 'r', 'c', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12738 */ 's', 'h', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12749 */ 'r', 'o', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12760 */ 's', 'h', 'l', 'd', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12772 */ 's', 'h', 'r', 'd', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12784 */ 's', 'a', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12795 */ 'r', 'c', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12806 */ 's', 'h', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12817 */ 'r', 'o', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12828 */ 's', 'a', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12839 */ 'r', 'c', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12850 */ 's', 'h', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12861 */ 'r', 'o', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 12872 */ 'm', 'o', 'v', 'a', 'b', 's', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 12886 */ 's', 't', 'o', 's', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 12898 */ 'o', 'u', 't', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 12909 */ 'm', 'o', 'v', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 12920 */ 'm', 'o', 'v', 'a', 'b', 's', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 12935 */ 's', 't', 'o', 's', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 12948 */ 'o', 'u', 't', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 12960 */ 'm', 'o', 'v', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 12972 */ 'm', 'o', 'v', 'a', 'b', 's', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 12987 */ 's', 't', 'o', 's', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 13000 */ 'i', 'n', 's', 'b', 9, '%', 'd', 'x', ',', 32, 0,
  /* 13011 */ 'i', 'n', 's', 'l', 9, '%', 'd', 'x', ',', 32, 0,
  /* 13022 */ 'i', 'n', 's', 'w', 9, '%', 'd', 'x', ',', 32, 0,
  /* 13033 */ 'v', 'r', 'c', 'p', '2', '8', 'p', 'd', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13051 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 'p', 'd', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13071 */ 'v', 'r', 'c', 'p', '2', '8', 's', 'd', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13089 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 's', 'd', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13109 */ 'v', 'r', 'c', 'p', '2', '8', 'p', 's', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13127 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 'p', 's', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13147 */ 'v', 'r', 'c', 'p', '2', '8', 's', 's', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13165 */ 'v', 'r', 's', 'q', 'r', 't', '2', '8', 's', 's', 32, 9, '{', 's', 'a', 'e', '}', ',', 32, 0,
  /* 13185 */ 'r', 'c', 'l', 'l', 9, '$', '1', 32, 0,
  /* 13194 */ '#', 'V', 'A', 'A', 'R', 'G', '_', '6', '4', 32, 0,
  /* 13205 */ 'r', 'e', 't', 9, '#', 'e', 'h', '_', 'r', 'e', 't', 'u', 'r', 'n', ',', 32, 'a', 'd', 'd', 'r', ':', 32, 0,
  /* 13228 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'X', 'M', 'M', 32, 0,
  /* 13242 */ '#', 'V', 'A', 'S', 'T', 'A', 'R', 'T', '_', 'S', 'A', 'V', 'E', '_', 'X', 'M', 'M', '_', 'R', 'E', 'G', 'S', 32, 0,
  /* 13266 */ '#', 'S', 'E', 'H', '_', 'S', 't', 'a', 'c', 'k', 'A', 'l', 'l', 'o', 'c', 32, 0,
  /* 13283 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 13299 */ '#', 'S', 'E', 'H', '_', 'S', 'e', 't', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 13314 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'R', 'e', 'g', 32, 0,
  /* 13328 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'R', 'e', 'g', 32, 0,
  /* 13342 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '3', '2', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13362 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '1', '6', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13382 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '8', '0', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13402 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13422 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '1', '6', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13443 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13463 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13483 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13502 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13522 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13542 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13562 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13582 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13602 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13622 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13642 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13661 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '8', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13679 */ '#', 'A', 'C', 'Q', 'U', 'I', 'R', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13700 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 13721 */ 'l', 'c', 'a', 'l', 'l', 'l', 9, '*', 0,
  /* 13730 */ 'l', 'j', 'm', 'p', 'l', 9, '*', 0,
  /* 13738 */ 'l', 'c', 'a', 'l', 'l', 'q', 9, '*', 0,
  /* 13747 */ 'l', 'j', 'm', 'p', 'q', 9, '*', 0,
  /* 13755 */ 'l', 'c', 'a', 'l', 'l', 'w', 9, '*', 0,
  /* 13764 */ 'l', 'j', 'm', 'p', 'w', 9, '*', 0,
  /* 13772 */ 'x', 's', 'h', 'a', '1', 0,
  /* 13778 */ 'f', 'l', 'd', '1', 0,
  /* 13783 */ 'f', 'p', 'r', 'e', 'm', '1', 0,
  /* 13790 */ 'f', '2', 'x', 'm', '1', 0,
  /* 13796 */ 'f', 'y', 'l', '2', 'x', 'p', '1', 0,
  /* 13804 */ 'i', 'n', 't', '1', 0,
  /* 13809 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 13828 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 13846 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '3', '2', 0,
  /* 13859 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 13872 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 13890 */ 'u', 'd', '2', 0,
  /* 13894 */ 'f', 'l', 'd', 'l', 'g', '2', 0,
  /* 13901 */ 'f', 'l', 'd', 'l', 'n', '2', 0,
  /* 13908 */ 'i', 'n', 't', '3', 0,
  /* 13913 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 13932 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 13950 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '6', '4', 0,
  /* 13963 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 13976 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 13994 */ 'r', 'e', 'x', '6', '4', 0,
  /* 14000 */ 'd', 'a', 't', 'a', '1', '6', 0,
  /* 14007 */ 'x', 's', 'h', 'a', '2', '5', '6', 0,
  /* 14015 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 14028 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 14035 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 14045 */ '#', 32, 'X', 'B', 'E', 'G', 'I', 'N', 0,
  /* 14054 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 0,
  /* 14072 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 0,
  /* 14088 */ '#', 'M', 'E', 'M', 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 14100 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 14115 */ 'a', 'a', 'a', 0,
  /* 14119 */ 'd', 'a', 'a', 0,
  /* 14123 */ 'u', 'd', '2', 'b', 0,
  /* 14128 */ 'x', 'c', 'r', 'y', 'p', 't', 'e', 'c', 'b', 0,
  /* 14138 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'f', 'b', 0,
  /* 14148 */ 'x', 'c', 'r', 'y', 'p', 't', 'o', 'f', 'b', 0,
  /* 14158 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'b', 0,
  /* 14168 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'b', 0,
  /* 14178 */ 'x', 'l', 'a', 't', 'b', 0,
  /* 14184 */ 'c', 'l', 'a', 'c', 0,
  /* 14189 */ 's', 't', 'a', 'c', 0,
  /* 14194 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'b', 'c', 0,
  /* 14204 */ 'g', 'e', 't', 's', 'e', 'c', 0,
  /* 14211 */ 's', 'a', 'l', 'c', 0,
  /* 14216 */ 'c', 'l', 'c', 0,
  /* 14220 */ 'c', 'm', 'c', 0,
  /* 14224 */ 'r', 'd', 'p', 'm', 'c', 0,
  /* 14230 */ 'v', 'm', 'f', 'u', 'n', 'c', 0,
  /* 14237 */ 'r', 'd', 't', 's', 'c', 0,
  /* 14243 */ 's', 't', 'c', 0,
  /* 14247 */ 'c', 'p', 'u', 'i', 'd', 0,
  /* 14253 */ 'c', 'l', 'd', 0,
  /* 14257 */ 'x', 'e', 'n', 'd', 0,
  /* 14262 */ 'c', 'l', 't', 'd', 0,
  /* 14267 */ 's', 't', 'd', 0,
  /* 14271 */ 'c', 'w', 't', 'd', 0,
  /* 14276 */ 'w', 'b', 'i', 'n', 'v', 'd', 0,
  /* 14283 */ 'f', 'l', 'd', 'l', '2', 'e', 0,
  /* 14290 */ 'l', 'f', 'e', 'n', 'c', 'e', 0,
  /* 14297 */ 'm', 'f', 'e', 'n', 'c', 'e', 0,
  /* 14304 */ 's', 'f', 'e', 'n', 'c', 'e', 0,
  /* 14311 */ 'f', 's', 'c', 'a', 'l', 'e', 0,
  /* 14318 */ 'v', 'm', 'r', 'e', 's', 'u', 'm', 'e', 0,
  /* 14327 */ 'r', 'e', 'p', 'n', 'e', 0,
  /* 14333 */ 'x', 'a', 'c', 'q', 'u', 'i', 'r', 'e', 0,
  /* 14342 */ 'x', 's', 't', 'o', 'r', 'e', 0,
  /* 14349 */ 'x', 'r', 'e', 'l', 'e', 'a', 's', 'e', 0,
  /* 14358 */ 'p', 'a', 'u', 's', 'e', 0,
  /* 14364 */ '#', 'S', 'E', 'H', '_', 'E', 'p', 'i', 'l', 'o', 'g', 'u', 'e', 0,
  /* 14378 */ '#', 'S', 'E', 'H', '_', 'E', 'n', 'd', 'P', 'r', 'o', 'l', 'o', 'g', 'u', 'e', 0,
  /* 14395 */ 'l', 'e', 'a', 'v', 'e', 0,
  /* 14401 */ 'v', 'm', 'x', 'o', 'f', 'f', 0,
  /* 14408 */ 'l', 'a', 'h', 'f', 0,
  /* 14413 */ 's', 'a', 'h', 'f', 0,
  /* 14418 */ 'v', 'm', 'l', 'a', 'u', 'n', 'c', 'h', 0,
  /* 14427 */ 'c', 'l', 'g', 'i', 0,
  /* 14432 */ 's', 't', 'g', 'i', 0,
  /* 14437 */ 'c', 'l', 'i', 0,
  /* 14441 */ 'f', 'l', 'd', 'p', 'i', 0,
  /* 14447 */ 's', 't', 'i', 0,
  /* 14451 */ '#', 32, 'w', 'i', 'n', '3', '2', 32, 'f', 'p', 't', 'o', 'u', 'i', 0,
  /* 14466 */ 'l', 'o', 'c', 'k', 0,
  /* 14471 */ 'i', 'n', 'b', 9, '%', 'd', 'x', ',', 32, '%', 'a', 'l', 0,
  /* 14484 */ 'p', 'u', 's', 'h', 'a', 'l', 0,
  /* 14491 */ 'p', 'o', 'p', 'a', 'l', 0,
  /* 14497 */ 'p', 'u', 's', 'h', 'f', 'l', 0,
  /* 14504 */ 'p', 'o', 'p', 'f', 'l', 0,
  /* 14510 */ 'v', 'm', 'm', 'c', 'a', 'l', 'l', 0,
  /* 14518 */ 'v', 'm', 'c', 'a', 'l', 'l', 0,
  /* 14525 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 0,
  /* 14533 */ 'v', 'z', 'e', 'r', 'o', 'a', 'l', 'l', 0,
  /* 14542 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'l', 0,
  /* 14552 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'l', 0,
  /* 14562 */ 'i', 'r', 'e', 't', 'l', 0,
  /* 14568 */ 'l', 'r', 'e', 't', 'l', 0,
  /* 14574 */ 's', 'y', 's', 'r', 'e', 't', 'l', 0,
  /* 14582 */ 's', 'y', 's', 'e', 'x', 'i', 't', 'l', 0,
  /* 14591 */ 'c', 'w', 't', 'l', 0,
  /* 14596 */ 'm', 'o', 'n', 't', 'm', 'u', 'l', 0,
  /* 14604 */ 'f', 'x', 'a', 'm', 0,
  /* 14609 */ 'f', 'p', 'r', 'e', 'm', 0,
  /* 14615 */ 'f', 's', 'e', 't', 'p', 'm', 0,
  /* 14622 */ 'r', 's', 'm', 0,
  /* 14626 */ 'f', 'p', 'a', 't', 'a', 'n', 0,
  /* 14633 */ 'f', 'p', 't', 'a', 'n', 0,
  /* 14639 */ 'f', 's', 'i', 'n', 0,
  /* 14644 */ '#', 32, 'd', 'y', 'n', 'a', 'm', 'i', 'c', 32, 's', 't', 'a', 'c', 'k', 32, 'a', 'l', 'l', 'o', 'c', 'a', 't', 'i', 'o', 'n', 0,
  /* 14671 */ 'i', 'n', 't', 'o', 0,
  /* 14676 */ 'c', 'q', 't', 'o', 0,
  /* 14681 */ 'r', 'd', 't', 's', 'c', 'p', 0,
  /* 14688 */ 'r', 'e', 'p', 0,
  /* 14692 */ 'v', 'p', 'c', 'm', 'p', 0,
  /* 14698 */ 'v', 'c', 'm', 'p', 0,
  /* 14703 */ 'f', 'n', 'o', 'p', 0,
  /* 14708 */ 'f', 'c', 'o', 'm', 'p', 'p', 0,
  /* 14715 */ 'f', 'u', 'c', 'o', 'm', 'p', 'p', 0,
  /* 14723 */ 'f', 'd', 'e', 'c', 's', 't', 'p', 0,
  /* 14731 */ 'f', 'i', 'n', 'c', 's', 't', 'p', 0,
  /* 14739 */ 'p', 'u', 's', 'h', 'f', 'q', 0,
  /* 14746 */ 'p', 'o', 'p', 'f', 'q', 0,
  /* 14752 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'q', 0,
  /* 14762 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'q', 0,
  /* 14772 */ 'i', 'r', 'e', 't', 'q', 0,
  /* 14778 */ 'l', 'r', 'e', 't', 'q', 0,
  /* 14784 */ 's', 'y', 's', 'r', 'e', 't', 'q', 0,
  /* 14792 */ 's', 'y', 's', 'e', 'x', 'i', 't', 'q', 0,
  /* 14801 */ 'c', 'l', 't', 'q', 0,
  /* 14806 */ 'v', 'z', 'e', 'r', 'o', 'u', 'p', 'p', 'e', 'r', 0,
  /* 14817 */ 's', 'y', 's', 'e', 'n', 't', 'e', 'r', 0,
  /* 14826 */ 'm', 'o', 'n', 'i', 't', 'o', 'r', 0,
  /* 14834 */ 'r', 'd', 'm', 's', 'r', 0,
  /* 14840 */ 'w', 'r', 'm', 's', 'r', 0,
  /* 14846 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 't', 'r', 0,
  /* 14856 */ 'a', 'a', 's', 0,
  /* 14860 */ 'd', 'a', 's', 0,
  /* 14864 */ 'f', 'a', 'b', 's', 0,
  /* 14869 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'c', 's', 0,
  /* 14879 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'c', 's', 0,
  /* 14889 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'd', 's', 0,
  /* 14899 */ 'p', 'o', 'p', 'l', 9, '%', 'd', 's', 0,
  /* 14908 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'd', 's', 0,
  /* 14918 */ 'p', 'o', 'p', 'w', 9, '%', 'd', 's', 0,
  /* 14927 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'e', 's', 0,
  /* 14937 */ 'p', 'o', 'p', 'l', 9, '%', 'e', 's', 0,
  /* 14946 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'e', 's', 0,
  /* 14956 */ 'p', 'o', 'p', 'w', 9, '%', 'e', 's', 0,
  /* 14965 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'f', 's', 0,
  /* 14975 */ 'p', 'o', 'p', 'l', 9, '%', 'f', 's', 0,
  /* 14984 */ 'p', 'u', 's', 'h', 'q', 9, '%', 'f', 's', 0,
  /* 14994 */ 'p', 'o', 'p', 'q', 9, '%', 'f', 's', 0,
  /* 15003 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'f', 's', 0,
  /* 15013 */ 'p', 'o', 'p', 'w', 9, '%', 'f', 's', 0,
  /* 15022 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'g', 's', 0,
  /* 15032 */ 'p', 'o', 'p', 'l', 9, '%', 'g', 's', 0,
  /* 15041 */ 'p', 'u', 's', 'h', 'q', 9, '%', 'g', 's', 0,
  /* 15051 */ 'p', 'o', 'p', 'q', 9, '%', 'g', 's', 0,
  /* 15060 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'g', 's', 0,
  /* 15070 */ 'p', 'o', 'p', 'w', 9, '%', 'g', 's', 0,
  /* 15079 */ 's', 'w', 'a', 'p', 'g', 's', 0,
  /* 15086 */ 'f', 'c', 'h', 's', 0,
  /* 15091 */ '#', 32, 'v', 'a', 'r', 'i', 'a', 'b', 'l', 'e', 32, 's', 'i', 'z', 'e', 'd', 32, 'a', 'l', 'l', 'o', 'c', 'a', 32, 'f', 'o', 'r', 32, 's', 'e', 'g', 'm', 'e', 'n', 't', 'e', 'd', 32, 's', 't', 'a', 'c', 'k', 's', 0,
  /* 15136 */ 'e', 'n', 'c', 'l', 's', 0,
  /* 15142 */ 'f', 'e', 'm', 'm', 's', 0,
  /* 15148 */ 'f', 'c', 'o', 's', 0,
  /* 15153 */ 'f', 's', 'i', 'n', 'c', 'o', 's', 0,
  /* 15161 */ 'p', 'u', 's', 'h', 'l', 9, '%', 's', 's', 0,
  /* 15171 */ 'p', 'o', 'p', 'l', 9, '%', 's', 's', 0,
  /* 15180 */ 'p', 'u', 's', 'h', 'w', 9, '%', 's', 's', 0,
  /* 15190 */ 'p', 'o', 'p', 'w', 9, '%', 's', 's', 0,
  /* 15199 */ 'c', 'l', 't', 's', 0,
  /* 15204 */ 'f', 'l', 'd', 'l', '2', 't', 0,
  /* 15211 */ 'f', 'x', 't', 'r', 'a', 'c', 't', 0,
  /* 15219 */ 'm', 'w', 'a', 'i', 't', 0,
  /* 15225 */ 'f', 'n', 'i', 'n', 'i', 't', 0,
  /* 15232 */ 'h', 'l', 't', 0,
  /* 15236 */ 'f', 'r', 'n', 'd', 'i', 'n', 't', 0,
  /* 15244 */ 'f', 's', 'q', 'r', 't', 0,
  /* 15250 */ 'x', 't', 'e', 's', 't', 0,
  /* 15256 */ 'f', 't', 's', 't', 0,
  /* 15261 */ 'e', 'n', 'c', 'l', 'u', 0,
  /* 15267 */ 'x', 'g', 'e', 't', 'b', 'v', 0,
  /* 15274 */ 'x', 's', 'e', 't', 'b', 'v', 0,
  /* 15281 */ 'p', 'u', 's', 'h', 'a', 'w', 0,
  /* 15288 */ 'p', 'o', 'p', 'a', 'w', 0,
  /* 15294 */ 'p', 'u', 's', 'h', 'f', 'w', 0,
  /* 15301 */ 'p', 'o', 'p', 'f', 'w', 0,
  /* 15307 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'w', 0,
  /* 15317 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'w', 0,
  /* 15327 */ 'c', 'b', 't', 'w', 0,
  /* 15332 */ 'i', 'r', 'e', 't', 'w', 0,
  /* 15338 */ 'l', 'r', 'e', 't', 'w', 0,
  /* 15344 */ 'f', 'y', 'l', '2', 'x', 0,
  /* 15350 */ 'f', 'n', 's', 't', 's', 'w', 9, '%', 'a', 'x', 0,
  /* 15361 */ 'i', 'n', 'w', 9, '%', 'd', 'x', ',', 32, '%', 'a', 'x', 0,
  /* 15374 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, '%', 'e', 'a', 'x', 0,
  /* 15386 */ 'v', 'm', 's', 'a', 'v', 'e', 9, '%', 'e', 'a', 'x', 0,
  /* 15398 */ 'v', 'm', 'r', 'u', 'n', 9, '%', 'e', 'a', 'x', 0,
  /* 15409 */ 's', 'k', 'i', 'n', 'i', 't', 9, '%', 'e', 'a', 'x', 0,
  /* 15421 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, '%', 'e', 'c', 'x', ',', 32, '%', 'e', 'a', 'x', 0,
  /* 15440 */ 'i', 'n', 'l', 9, '%', 'd', 'x', ',', 32, '%', 'e', 'a', 'x', 0,
  /* 15454 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, '%', 'r', 'a', 'x', 0,
  /* 15466 */ 'v', 'm', 's', 'a', 'v', 'e', 9, '%', 'r', 'a', 'x', 0,
  /* 15478 */ 'v', 'm', 'r', 'u', 'n', 9, '%', 'r', 'a', 'x', 0,
  /* 15489 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, '%', 'e', 'c', 'x', ',', 32, '%', 'r', 'a', 'x', 0,
  /* 15508 */ 'o', 'u', 't', 'b', 9, '%', 'a', 'l', ',', 32, '%', 'd', 'x', 0,
  /* 15522 */ 'o', 'u', 't', 'w', 9, '%', 'a', 'x', ',', 32, '%', 'd', 'x', 0,
  /* 15536 */ 'o', 'u', 't', 'l', 9, '%', 'e', 'a', 'x', ',', 32, '%', 'd', 'x', 0,
  /* 15551 */ 'f', 'n', 'c', 'l', 'e', 'x', 0,
  /* 15558 */ 'f', 'l', 'd', 'z', 0,
  };
#endif

  // Emit the opcode for the instruction.
  unsigned int opcode = MCInst_getOpcode(MI);
  uint64_t Bits1 = OpInfo[opcode];
  uint64_t Bits2 = OpInfo2[opcode];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
  if (!X86_lockrep(MI, O))
#ifndef CAPSTONE_DIET
    SStream_concat0(O, AsmStrs+(Bits & 16383)-1);
#else
	;
#endif


  // Fragment 0 encoded into 7 bits for 79 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 14) & 127);
  switch ((Bits >> 14) & 127) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, AAA, AAS, ABS_F, ACQU...
    return;
    break;
  case 1:
    // AAD8i8, AAM8i8, ADC16i16, ADC32i32, ADC64i32, ADC8i8, ADD16i16, ADD32i...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADC32mi, ADC32mi8, ADC32mr, ADC64mi32, ADC...
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, ADC32rr, A...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // ADC16rm, ADD16rm, AND16rm, CMOVA16rm, CMOVAE16rm, CMOVB16rm, CMOVBE16r...
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // ADC32rm, ADCX32rm, ADD32rm, AND32rm, ANDN32rm, CMOVA32rm, CMOVAE32rm, ...
    printi32mem(MI, 2, O); 
    break;
  case 6:
    // ADC64rm, ADCX64rm, ADD64rm, AND64rm, ANDN64rm, CMOVA64rm, CMOVAE64rm, ...
    printi64mem(MI, 2, O); 
    break;
  case 7:
    // ADC8rm, ADD8rm, AND8rm, CRC32r32m8, CRC32r64m8, OR8rm, SBB8rm, SUB8rm,...
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // ADDPDrm, ADDPSrm, ADDSUBPDrm, ADDSUBPSrm, ANDNPDrm, ANDNPSrm, ANDPDrm,...
    printf128mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 9:
    // ADDSDrm, ADDSDrm_Int, DIVSDrm, DIVSDrm_Int, Int_CVTSD2SSrm, Int_VCVTSD...
    printf64mem(MI, 2, O); 
    break;
  case 10:
    // ADDSSrm, ADDSSrm_Int, DIVSSrm, DIVSSrm_Int, Int_CVTSS2SDrm, Int_VCVTSS...
    printf32mem(MI, 2, O); 
    break;
  case 11:
    // ADD_F32m, DIVR_F32m, DIV_F32m, FBLDm, FBSTPm, FCOM32m, FCOMP32m, FLDEN...
    printf32mem(MI, 0, O); 
    return;
    break;
  case 12:
    // ADD_F64m, DIVR_F64m, DIV_F64m, FCOM64m, FCOMP64m, LD_F64m, MUL_F64m, S...
    printf64mem(MI, 0, O); 
    return;
    break;
  case 13:
    // ADD_FI16m, CALL16m, DEC16m, DEC64_16m, DIV16m, DIVR_FI16m, DIV_FI16m, ...
    printi16mem(MI, 0, O); 
    return;
    break;
  case 14:
    // ADD_FI32m, CALL32m, DEC32m, DEC64_32m, DIV32m, DIVR_FI32m, DIV_FI32m, ...
    printi32mem(MI, 0, O); 
    break;
  case 15:
    // ADOX32rm, BLCFILL32rm, BLCI32rm, BLCIC32rm, BLCMSK32rm, BLCS32rm, BLSF...
    printi32mem(MI, 1, O); 
    break;
  case 16:
    // ADOX32rr, ADOX64rr, AESIMCrr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI...
    printOperand(MI, 1, O); 
    break;
  case 17:
    // ADOX64rm, BLCFILL64rm, BLCI64rm, BLCIC64rm, BLCMSK64rm, BLCS64rm, BLSF...
    printi64mem(MI, 1, O); 
    break;
  case 18:
    // AESDECLASTrm, AESDECrm, AESENCLASTrm, AESENCrm, PACKSSDWrm, PACKSSWBrm...
    printi128mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // AESIMCrm, CVTDQ2PSrm, INVEPT32, INVEPT64, INVPCID32, INVPCID64, INVVPI...
    printi128mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 20:
    // AESKEYGENASSIST128rm, BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZ...
    printOperand(MI, 6, O); 
    SStream_concat0(O, ", "); 
    break;
  case 21:
    // BLENDPDrmi, BLENDPSrmi, CMPPDrmi_alt, CMPPSrmi_alt, CMPSDrm_alt, CMPSS...
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // BLENDPDrri, BLENDPSrri, CMPPDrri_alt, CMPPSrri_alt, CMPSDrr_alt, CMPSS...
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 23:
    // BSF16rm, BSR16rm, CMP16rm, KMOVWkm, LAR16rm, LAR32rm, LAR64rm, LEA16r,...
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 24:
    // CALL64m, CMPXCHG8B, DEC64m, DIV64m, IDIV64m, ILD_F64m, IMUL64m, INC64m...
    printi64mem(MI, 0, O); 
    break;
  case 25:
    // CALL64pcrel32, CALLpcrel16, CALLpcrel32, EH_SjLj_Setup, JAE_1, JAE_2, ...
    printPCRelImm(MI, 0, O); 
    break;
  case 26:
    // CLFLUSH, DEC8m, DIV8m, IDIV8m, IMUL8m, INC8m, INVLPG, LOCK_DEC8m, LOCK...
    printi8mem(MI, 0, O); 
    return;
    break;
  case 27:
    // CMP8rm, KMOVBkm, MOV8rm, MOV8rm_NOREX, MOVSX16rm8, MOVSX32rm8, MOVSX64...
    printi8mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    break;
  case 28:
    // CMPPDrmi, CMPPSrmi, CMPSDrm, CMPSSrm, Int_CMPSDrm, Int_CMPSSrm
    printSSECC(MI, 7, O); 
    break;
  case 29:
    // CMPPDrri, CMPPSrri, CMPSDrr, CMPSSrr, Int_CMPSDrr, Int_CMPSSrr
    printSSECC(MI, 3, O); 
    break;
  case 30:
    // CMPSB, INSB, SCASB, STOSB
    printDstIdx8(MI, 0, O); 
    break;
  case 31:
    // CMPSL, INSL, SCASL, STOSL
    printDstIdx32(MI, 0, O); 
    break;
  case 32:
    // CMPSQ, SCASQ, STOSQ
    printDstIdx64(MI, 0, O); 
    break;
  case 33:
    // CMPSW, INSW, SCASW, STOSW
    printDstIdx16(MI, 0, O); 
    break;
  case 34:
    // CMPXCHG16B, LCMPXCHG16B
    printi128mem(MI, 0, O); 
    return;
    break;
  case 35:
    // COMISDrm, COMISSrm, CVTPD2DQrm, CVTPD2PSrm, CVTPS2DQrm, CVTTPD2DQrm, C...
    printf128mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 36:
    // CVTPS2PDrm, CVTSD2SI64rm, CVTSD2SIrm, CVTSD2SSrm, CVTTSD2SI64rm, CVTTS...
    printf64mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 37:
    // CVTSS2SDrm, CVTSS2SI64rm, CVTSS2SIrm, CVTTSS2SI64rm, CVTTSS2SIrm, Int_...
    printf32mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 38:
    // FARCALL16m, FARCALL32m, FARCALL64, FARJMP16m, FARJMP32m, FARJMP64, FXR...
    printopaquemem(MI, 0, O); 
    return;
    break;
  case 39:
    // INSERTQI, VALIGNDrrikz, VALIGNQrrikz, VFMADD213PDZrk, VFMADD213PDZrkz,...
    printOperand(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 40:
    // Int_VCMPSDrm, Int_VCMPSSrm, VCMPPDYrmi, VCMPPDZrmi, VCMPPDrmi, VCMPPSY...
    printAVXCC(MI, 7, O); 
    break;
  case 41:
    // Int_VCMPSDrr, Int_VCMPSSrr, VCMPPDYrri, VCMPPDZrri, VCMPPDZrrib, VCMPP...
    printAVXCC(MI, 3, O); 
    break;
  case 42:
    // LDS16rm, LDS32rm, LES16rm, LES32rm, LFS16rm, LFS32rm, LFS64rm, LGS16rm...
    printopaquemem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 43:
    // LD_F80m, ST_FP80m
    printf80mem(MI, 0, O); 
    return;
    break;
  case 44:
    // LODSB, OUTSB
    printSrcIdx8(MI, 0, O); 
    break;
  case 45:
    // LODSL, OUTSL
    printSrcIdx32(MI, 0, O); 
    break;
  case 46:
    // LODSQ
    printSrcIdx64(MI, 0, O); 
    SStream_concat0(O, ", %rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 47:
    // LODSW, OUTSW
    printSrcIdx16(MI, 0, O); 
    break;
  case 48:
    // MOV16ao16, MOV16ao16_16, MOV16o16a, MOV16o16a_16, MOV64ao16, MOV64o16a
    printMemOffs16(MI, 0, O); 
    break;
  case 49:
    // MOV32ao32, MOV32ao32_16, MOV32o32a, MOV32o32a_16, MOV64ao32, MOV64o32a
    printMemOffs32(MI, 0, O); 
    break;
  case 50:
    // MOV64ao64, MOV64o64a
    printMemOffs64(MI, 0, O); 
    break;
  case 51:
    // MOV64ao8, MOV64o8a, MOV8ao8, MOV8ao8_16, MOV8o8a, MOV8o8a_16
    printMemOffs8(MI, 0, O); 
    break;
  case 52:
    // MOVSB
    printSrcIdx8(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx8(MI, 0, O); 
    return;
    break;
  case 53:
    // MOVSL
    printSrcIdx32(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx32(MI, 0, O); 
    return;
    break;
  case 54:
    // MOVSQ
    printSrcIdx64(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx64(MI, 0, O); 
    return;
    break;
  case 55:
    // MOVSW
    printSrcIdx16(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx16(MI, 0, O); 
    return;
    break;
  case 56:
    // VADDPDYrm, VADDPSYrm, VADDSUBPDYrm, VADDSUBPSYrm, VANDNPDYrm, VANDNPSY...
    printf256mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 57:
    // VADDPDZrm, VADDPSZrm, VDIVPDZrm, VDIVPSZrm, VMAXPDZrm, VMAXPSZrm, VMIN...
    printf512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 58:
    // VADDPDZrmbk, VADDPDZrmbkz, VDIVPDZrmbk, VDIVPDZrmbkz, VFMADD132PDZmb, ...
    printf64mem(MI, 3, O); 
    break;
  case 59:
    // VADDPDZrmk, VADDPDZrmkz, VADDPSZrmk, VADDPSZrmkz, VBLENDMPDZrm, VBLEND...
    printf512mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 60:
    // VADDPSZrmbk, VADDPSZrmbkz, VDIVPSZrmbk, VDIVPSZrmbkz, VFMADD132PSZmb, ...
    printf32mem(MI, 3, O); 
    break;
  case 61:
    // VBROADCASTI64X4krm, VMOVDQA32Z256rmkz, VMOVDQA64Z256rmkz, VMOVDQU16Z25...
    printi256mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 62:
    // VBROADCASTI64X4rm, VCVTDQ2PDZrm, VCVTDQ2PSYrm, VLDDQUYrm, VMOVDQA32Z25...
    printi256mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 63:
    // VCVTDQ2PSZrm, VMOVDQA32Zrm, VMOVDQA64Zrm, VMOVDQU16Zrm, VMOVDQU32Zrm, ...
    printi512mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 64:
    // VCVTDQ2PSZrrb, VCVTPD2DQZrrb, VCVTPD2PSZrrb, VCVTPD2UDQZrrb, VCVTPS2DQ...
    printRoundingControl(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 65:
    // VCVTPD2DQYrm, VCVTPD2PSYrm, VCVTPH2PSZrm, VCVTPS2DQYrm, VCVTPS2PDZrm, ...
    printf256mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 66:
    // VCVTPD2DQZrm, VCVTPD2PSZrm, VCVTPD2UDQZrm, VCVTPS2DQZrm, VCVTPS2UDQZrm...
    printf512mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 67:
    // VFMADDPD4rm, VFMADDPDr132m, VFMADDPDr213m, VFMADDPDr231m, VFMADDPS4rm,...
    printf128mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 68:
    // VFMADDPD4rmY, VFMADDPDr132mY, VFMADDPDr213mY, VFMADDPDr231mY, VFMADDPS...
    printf256mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 69:
    // VGATHERDPDYrm, VGATHERDPDrm, VGATHERDPSYrm, VGATHERDPSrm, VGATHERQPDYr...
    printOperand(MI, 8, O); 
    SStream_concat0(O, ", "); 
    break;
  case 70:
    // VGATHERDPDZrm, VGATHERQPDZrm, VGATHERQPSZrm, VPADDQZrmbk, VPANDNQZrmbk...
    printi64mem(MI, 4, O); 
    break;
  case 71:
    // VGATHERDPSZrm, VPADDDZrmbk, VPANDDZrmbk, VPANDNDZrmbk, VPGATHERDDZrm, ...
    printi32mem(MI, 4, O); 
    break;
  case 72:
    // VMOVDQA32Z128rmk, VMOVDQA64Z128rmk, VMOVDQU16Z128rmk, VMOVDQU32Z128rmk...
    printi128mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 73:
    // VMOVDQA32Z256rmk, VMOVDQA64Z256rmk, VMOVDQU16Z256rmk, VMOVDQU32Z256rmk...
    printi256mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 74:
    // VMOVDQA32Zrmk, VMOVDQA64Zrmk, VMOVDQU16Zrmk, VMOVDQU32Zrmk, VMOVDQU64Z...
    printi512mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    break;
  case 75:
    // VMOVDQA32Zrmkz, VMOVDQA64Zrmkz, VMOVDQU16Zrmkz, VMOVDQU32Zrmkz, VMOVDQ...
    printi512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 76:
    // VPADDDZrmbkz, VPANDDZrmbkz, VPANDNDZrmbkz, VPCMPEQDZ128rmbk, VPCMPEQDZ...
    printi32mem(MI, 3, O); 
    break;
  case 77:
    // VPADDDZrmk, VPADDQZrmk, VPANDDZrmk, VPANDNDZrmk, VPANDNQZrmk, VPANDQZr...
    printi512mem(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, " {"); 
    printOperand(MI, 2, O); 
    break;
  case 78:
    // VPADDQZrmbkz, VPANDNQZrmbkz, VPANDQZrmbkz, VPCMPEQQZ128rmbk, VPCMPEQQZ...
    printi64mem(MI, 3, O); 
    break;
  }


  // Fragment 1 encoded into 7 bits for 74 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 21) & 127);
  switch ((Bits >> 21) & 127) {
  default:   // unreachable.
  case 0:
    // AAD8i8, AAM8i8, ADD_FI32m, ADD_FPrST0, ADD_FST0r, ADD_FrST0, BSWAP32r,...
    return;
    break;
  case 1:
    // ADC16i16, ADD16i16, AND16i16, CMP16i16, IN16ri, LODSW, MOV16o16a, MOV1...
    SStream_concat0(O, ", %ax"); 
    op_addReg(MI, X86_REG_AX);
    return;
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADD16mi, ADD16mi8, ADD16mr, AND16mi, AND16...
    printi16mem(MI, 0, O); 
    return;
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rr, ADC64...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // ADC16rr_REV, ADC32rr_REV, ADC64rr_REV, ADC8rr_REV, ADCX32rr, ADCX64rr,...
    printOperand(MI, 0, O); 
    break;
  case 5:
    // ADC32i32, ADD32i32, AND32i32, CMP32i32, IN32ri, LODSL, MOV32o32a, MOV3...
    SStream_concat0(O, ", %eax"); 
    op_addReg(MI, X86_REG_EAX);
    return;
    break;
  case 6:
    // ADC32mi, ADC32mi8, ADC32mr, ADD32mi, ADD32mi8, ADD32mr, AND32mi, AND32...
    printi32mem(MI, 0, O); 
    return;
    break;
  case 7:
    // ADC32rm, ADC64rm, ADCX32rm, ADCX64rm, ADD32rm, ADD64rm, ADDSDrm, ADDSD...
    SStream_concat0(O, ", "); 
    break;
  case 8:
    // ADC64i32, ADD64i32, AND64i32, CMP64i32, MOV64o64a, OR64i32, SBB64i32, ...
    SStream_concat0(O, ", %rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 9:
    // ADC64mi32, ADC64mi8, ADC64mr, ADD64mi32, ADD64mi8, ADD64mr, AND64mi32,...
    printi64mem(MI, 0, O); 
    return;
    break;
  case 10:
    // ADC8i8, ADD8i8, AND8i8, CMP8i8, IN8ri, LODSB, MOV64o8a, MOV8o8a, MOV8o...
    SStream_concat0(O, ", %al"); 
    op_addReg(MI, X86_REG_AL);
    return;
    break;
  case 11:
    // ADC8mi, ADC8mr, ADD8mi, ADD8mr, AND8mi, AND8mr, CMP8mi, CMP8mr, CMPXCH...
    printi8mem(MI, 0, O); 
    break;
  case 12:
    // AESKEYGENASSIST128rm, PCMPESTRIrm, PCMPESTRM128rm, PCMPISTRIrm, PCMPIS...
    printi128mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 13:
    // BEXTR32rm, BEXTRI32mi, BZHI32rm, IMUL32rmi, IMUL32rmi8, RORX32mi, SARX...
    printi32mem(MI, 1, O); 
    break;
  case 14:
    // BEXTR64rm, BEXTRI64mi, BZHI64rm, IMUL64rmi32, IMUL64rmi8, MMX_PSHUFWmi...
    printi64mem(MI, 1, O); 
    break;
  case 15:
    // BLENDPDrmi, BLENDPSrmi, CMPPDrmi_alt, CMPPSrmi_alt, DPPDrmi, DPPSrmi, ...
    printf128mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 16:
    // BLENDPDrri, BLENDPSrri, CMPPDrri_alt, CMPPSrri_alt, CMPSDrr_alt, CMPSS...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 17:
    // CMOVBE_F, CMOVB_F, CMOVE_F, CMOVNBE_F, CMOVNB_F, CMOVNE_F, CMOVNP_F, C...
    SStream_concat0(O, ", %st(0)"); 
    op_addReg(MI, X86_REG_ST0);
    return;
    break;
  case 18:
    // CMPPDrmi, CMPPDrri, VCMPPDYrmi, VCMPPDYrri, VCMPPDrmi, VCMPPDrri
    SStream_concat0(O, "pd\t"); 
    break;
  case 19:
    // CMPPSrmi, CMPPSrri, VCMPPSYrmi, VCMPPSYrri, VCMPPSrmi, VCMPPSrri
    SStream_concat0(O, "ps\t"); 
    break;
  case 20:
    // CMPSDrm, CMPSDrr, Int_CMPSDrm, Int_CMPSDrr, Int_VCMPSDrm, Int_VCMPSDrr...
    SStream_concat0(O, "sd\t"); 
    break;
  case 21:
    // CMPSDrm_alt, ROUNDSDm, VCMPSDZrmi_alt, VCMPSDrm_alt, VFMADDSD4mr, VFMA...
    printf64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // CMPSSrm, CMPSSrr, Int_CMPSSrm, Int_CMPSSrr, Int_VCMPSSrm, Int_VCMPSSrr...
    SStream_concat0(O, "ss\t"); 
    break;
  case 23:
    // CMPSSrm_alt, INSERTPSrm, ROUNDSSm, VCMPSSZrmi_alt, VCMPSSrm_alt, VFMAD...
    printf32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 24:
    // EXTRACTPSmr, PEXTRBmr, PEXTRDmr, PEXTRQmr, PEXTRWmr, SHLD16mri8, SHLD3...
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    break;
  case 25:
    // FARCALL16i, FARCALL32i, FARJMP16i, FARJMP32i
    SStream_concat0(O, ":"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 26:
    // IMUL16rmi, IMUL16rmi8
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 27:
    // MMX_PALIGNR64irm, PINSRQrm, VPINSRQrm
    printi64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 28:
    // MMX_PINSRWirmi, PINSRWrmi, VPINSRWrmi
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 29:
    // MOV8rm_NOREX
    return;
    break;
  case 30:
    // MOVAPDmr, MOVAPSmr, MOVNTDQmr, MOVNTPDmr, MOVNTPSmr, MOVUPDmr, MOVUPSm...
    printf128mem(MI, 0, O); 
    break;
  case 31:
    // MOVDQAmr, MOVDQUmr, VMOVDQA32Z128mr, VMOVDQA32Z128mrk, VMOVDQA64Z128mr...
    printi128mem(MI, 0, O); 
    break;
  case 32:
    // MOVHPDmr, MOVHPSmr, MOVLPDmr, MOVLPSmr, MOVNTSD, MOVSDmr, VMOVHPDmr, V...
    printf64mem(MI, 0, O); 
    return;
    break;
  case 33:
    // MOVNTSS, MOVSSmr, VMOVSSZmr, VMOVSSmr
    printf32mem(MI, 0, O); 
    return;
    break;
  case 34:
    // MPSADBWrmi, PALIGNR128rm, PBLENDWrmi, PCLMULQDQrm, SHA1RNDS4rmi, VINSE...
    printi128mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 35:
    // OUTSB, OUTSL, OUTSW
    SStream_concat0(O, ", %dx"); 
    op_addReg(MI, X86_REG_DX);
    return;
    break;
  case 36:
    // PINSRBrm, VPINSRBrm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 37:
    // PINSRDrm, VPINSRDrm
    printi32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 38:
    // ROUNDPDm, ROUNDPSm, VPERMILPDmi, VPERMILPSmi, VROUNDPDm, VROUNDPSm
    printf128mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 39:
    // TAILJMPd, TAILJMPd64, TAILJMPm, TAILJMPm64, TAILJMPr64
    return;
    break;
  case 40:
    // VADDPDZrmb, VADDPDZrmbk, VADDPDZrmbkz, VDIVPDZrmb, VDIVPDZrmbk, VDIVPD...
    SStream_concat0(O, "{1to8}, "); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_8);
    break;
  case 41:
    // VADDPSZrmb, VADDPSZrmbk, VADDPSZrmbkz, VDIVPSZrmb, VDIVPSZrmbk, VDIVPS...
    SStream_concat0(O, "{1to16}, "); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_16);
    break;
  case 42:
    // VALIGNDrmi, VALIGNQrmi, VPCMPDZrmi_alt, VPCMPQZrmi_alt, VPCMPUDZrmi_al...
    printi512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 43:
    // VALIGNDrrik, VALIGNQrrik
    printOperand(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, " {"); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  case 44:
    // VBLENDPDYrmi, VBLENDPSYrmi, VBLENDVPDYrm, VBLENDVPSYrm, VCMPPDYrmi_alt...
    printf256mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 45:
    // VCMPPDZrmi, VCMPPDZrri
    SStream_concat0(O, "pd \t"); 
    break;
  case 46:
    // VCMPPDZrmi_alt, VCMPPSZrmi_alt, VSHUFPDZrmi, VSHUFPSZrmi
    printf512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 47:
    // VCMPPDZrrib
    SStream_concat0(O, "pd \t{sae}, "); 
    op_addAvxSae(MI);
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 48:
    // VCMPPSZrmi, VCMPPSZrri
    SStream_concat0(O, "ps \t"); 
    break;
  case 49:
    // VCMPPSZrrib
    SStream_concat0(O, "ps \t{sae}, "); 
    op_addAvxSae(MI);
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 50:
    // VDPPSYrmi, VINSERTF64x4rm, VINSERTI64x4rm, VMPSADBWYrmi, VPALIGNR256rm...
    printi256mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 51:
    // VGATHERDPDYrm, VGATHERDPDrm, VGATHERQPDYrm, VGATHERQPDrm, VPGATHERDQYr...
    printi64mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 52:
    // VGATHERDPSYrm, VGATHERDPSrm, VGATHERQPSYrm, VGATHERQPSrm, VPGATHERDDYr...
    printi32mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 53:
    // VGATHERPF0DPDm, VGATHERPF0DPSm, VGATHERPF0QPDm, VGATHERPF0QPSm, VGATHE...
    SStream_concat0(O, " {"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  case 54:
    // VMOVAPDYmr, VMOVAPDZ256mr, VMOVAPDZ256mrk, VMOVAPSYmr, VMOVAPSZ256mr, ...
    printf256mem(MI, 0, O); 
    break;
  case 55:
    // VMOVAPDZmr, VMOVAPDZmrk, VMOVAPSZmr, VMOVAPSZmrk, VMOVNTPDZmr, VMOVNTP...
    printf512mem(MI, 0, O); 
    break;
  case 56:
    // VMOVDQA32Z256mr, VMOVDQA32Z256mrk, VMOVDQA64Z256mr, VMOVDQA64Z256mrk, ...
    printi256mem(MI, 0, O); 
    break;
  case 57:
    // VMOVDQA32Zmr, VMOVDQA32Zmrk, VMOVDQA64Zmr, VMOVDQA64Zmrk, VMOVDQU16Zmr...
    printi512mem(MI, 0, O); 
    break;
  case 58:
    // VPADDDZrmk, VPADDQZrmk, VPANDDZrmk, VPANDNDZrmk, VPANDNQZrmk, VPANDQZr...
    SStream_concat0(O, "}"); 
    return;
    break;
  case 59:
    // VPCMPDZrmi, VPCMPDZrri
    SStream_concat0(O, "d\t"); 
    break;
  case 60:
    // VPCMPDZrmik_alt, VPCMPQZrmik_alt, VPCMPUDZrmik_alt, VPCMPUQZrmik_alt
    printi512mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, " {"); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  case 61:
    // VPCMPEQDZ128rmb, VPCMPEQDZ128rmbk, VPCMPEQQZ256rmb, VPCMPEQQZ256rmbk, ...
    SStream_concat0(O, "{1to4}, "); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_4);
    break;
  case 62:
    // VPCMPEQQZ128rmb, VPCMPEQQZ128rmbk, VPCMPGTQZ128rmb, VPCMPGTQZ128rmbk
    SStream_concat0(O, "{1to2}, "); 
    op_addAvxBroadcast(MI, X86_AVX_BCAST_2);
    break;
  case 63:
    // VPCMPQZrmi, VPCMPQZrri
    SStream_concat0(O, "q\t"); 
    break;
  case 64:
    // VPCMPUDZrmi, VPCMPUDZrri
    SStream_concat0(O, "ud\t"); 
    break;
  case 65:
    // VPCMPUQZrmi, VPCMPUQZrri
    SStream_concat0(O, "uq\t"); 
    break;
  case 66:
    // VPERMI2Drmkz, VPERMI2PDrmkz, VPERMI2PSrmkz, VPERMI2Qrmkz, VPERMT2Drmkz...
    SStream_concat0(O, "} {z}"); 
    op_addAvxZeroOpmask(MI);
    return;
    break;
  case 67:
    // VPERMIL2PDmr, VPERMIL2PDmrY, VPERMIL2PSmr, VPERMIL2PSmrY
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    break;
  case 68:
    // VPERMIL2PDrm, VPERMIL2PSrm
    printf128mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 69:
    // VPERMIL2PDrmY, VPERMIL2PSrmY
    printf256mem(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 70:
    // VPERMILPDYmi, VPERMILPSYmi, VROUNDYPDm, VROUNDYPSm
    printf256mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 71:
    // VPERMILPDZmi, VPERMILPSZmi, VPERMQZmi, VPSHUFDZmi, VPSLLDZmi, VPSLLQZm...
    printi512mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 72:
    // VPERMPDYmi, VPERMQYmi, VPSHUFDYmi, VPSHUFHWYmi, VPSHUFLWYmi
    printi256mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 73:
    // VPERMPDZmi, VRNDSCALEPDZm, VRNDSCALEPSZm
    printf512mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 2 encoded into 6 bits for 33 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 28) & 63);
  switch ((Bits >> 28) & 63) {
  default:   // unreachable.
  case 0:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, A...
    return;
    break;
  case 1:
    // ADC32rm, ADC64rm, ADD32rm, ADD64rm, AND32rm, AND64rm, ANDN32rm, ANDN64...
    printOperand(MI, 1, O); 
    break;
  case 2:
    // ADCX32rm, ADCX64rm, ADDSDrm, ADDSDrm_Int, ADDSSrm, ADDSSrm_Int, ADOX32...
    printOperand(MI, 0, O); 
    break;
  case 3:
    // AESKEYGENASSIST128rr, ANDN32rr, ANDN64rr, BEXTR32rm, BEXTR32rr, BEXTR6...
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 4:
    // CMPPDrmi, CMPPSrmi, VCMPPDrmi, VCMPPSrmi, VPERMIL2PDmr, VPERMIL2PSmr
    printf128mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // CMPPDrri, CMPPSrri, CMPSDrr, CMPSSrr, Int_CMPSDrr, Int_CMPSSrr, Int_VC...
    printOperand(MI, 2, O); 
    break;
  case 6:
    // CMPSB
    printSrcIdx8(MI, 1, O); 
    return;
    break;
  case 7:
    // CMPSDrm, Int_CMPSDrm, Int_VCMPSDrm, VCMPSDZrm, VCMPSDrm
    printf64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 8:
    // CMPSL
    printSrcIdx32(MI, 1, O); 
    return;
    break;
  case 9:
    // CMPSQ
    printSrcIdx64(MI, 1, O); 
    return;
    break;
  case 10:
    // CMPSSrm, Int_CMPSSrm, Int_VCMPSSrm, VCMPSSZrm, VCMPSSrm
    printf32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 11:
    // CMPSW
    printSrcIdx16(MI, 1, O); 
    return;
    break;
  case 12:
    // EXTRACTPSmr, VEXTRACTPSmr, VEXTRACTPSzmr
    printf32mem(MI, 0, O); 
    return;
    break;
  case 13:
    // LXADD16, XCHG16rm
    printi16mem(MI, 2, O); 
    return;
    break;
  case 14:
    // LXADD32, XCHG32rm
    printi32mem(MI, 2, O); 
    return;
    break;
  case 15:
    // LXADD64, XCHG64rm
    printi64mem(MI, 2, O); 
    return;
    break;
  case 16:
    // LXADD8, XCHG8rm
    printi8mem(MI, 2, O); 
    return;
    break;
  case 17:
    // MOV8mr_NOREX
    return;
    break;
  case 18:
    // PEXTRBmr, VPEXTRBmr
    printi8mem(MI, 0, O); 
    return;
    break;
  case 19:
    // PEXTRDmr, SHLD32mri8, SHRD32mri8, VPEXTRDmr
    printi32mem(MI, 0, O); 
    return;
    break;
  case 20:
    // PEXTRQmr, SHLD64mri8, SHRD64mri8, VPEXTRQmr
    printi64mem(MI, 0, O); 
    return;
    break;
  case 21:
    // PEXTRWmr, SHLD16mri8, SHRD16mri8, VPEXTRWmr
    printi16mem(MI, 0, O); 
    return;
    break;
  case 22:
    // VAARG_64
    printi8mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 6, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
    break;
  case 23:
    // VBROADCASTI32X4krm, VBROADCASTI64X4krm, VFMADD213PDZrk, VFMADD213PDZrk...
    SStream_concat0(O, " {"); 
    break;
  case 24:
    // VCMPPDYrmi, VCMPPSYrmi, VPERMIL2PDmrY, VPERMIL2PSmrY
    printf256mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 25:
    // VCMPPDZrmi, VCMPPSZrmi
    printf512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 26:
    // VCVTPS2PHYmr, VEXTRACTF128mr, VEXTRACTF32x4mr, VMASKMOVPDmr, VMASKMOVP...
    printf128mem(MI, 0, O); 
    return;
    break;
  case 27:
    // VCVTPS2PHZmr, VEXTRACTF64x4mr, VMASKMOVPDYmr, VMASKMOVPSYmr
    printf256mem(MI, 0, O); 
    return;
    break;
  case 28:
    // VCVTPS2PHmr
    printf64mem(MI, 0, O); 
    return;
    break;
  case 29:
    // VEXTRACTI128mr, VEXTRACTI32x4mr, VPMASKMOVDmr, VPMASKMOVQmr
    printi128mem(MI, 0, O); 
    return;
    break;
  case 30:
    // VEXTRACTI64x4mr, VPMASKMOVDYmr, VPMASKMOVQYmr
    printi256mem(MI, 0, O); 
    return;
    break;
  case 31:
    // VPADDDZrmbk, VPADDQZrmbk, VPANDDZrmbk, VPANDNDZrmbk, VPANDNQZrmbk, VPA...
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, " {"); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  case 32:
    // VPCMPDZrmi, VPCMPQZrmi, VPCMPUDZrmi, VPCMPUQZrmi
    printi512mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 4 bits for 10 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 34) & 15);
  switch ((Bits >> 34) & 15) {
  default:   // unreachable.
  case 0:
    // ADC32rm, ADC64rm, ADCX32rm, ADCX64rm, ADD32rm, ADD64rm, ADDSDrm, ADDSD...
    return;
    break;
  case 1:
    // ANDN32rm, ANDN64rm, CMPPDrri, CMPPSrri, CMPSDrr, CMPSSrr, Int_CMPSDrr,...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // CMPPDrmi, CMPPSrmi, CMPSDrm, CMPSSrm, Int_CMPSDrm, Int_CMPSSrm
    printOperand(MI, 0, O); 
    return;
    break;
  case 3:
    // Int_VCMPSDrm, Int_VCMPSSrm, VBROADCASTI32X4krm, VBROADCASTI64X4krm, VC...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // MOV8rr_NOREX
    return;
    break;
  case 5:
    // VADDPDZrmk, VADDPDZrmkz, VADDPDZrrk, VADDPDZrrkz, VADDPSZrmk, VADDPSZr...
    SStream_concat0(O, " {"); 
    break;
  case 6:
    // VFMADD213PDZrk, VFMADD213PDZrkz, VFMADD213PSZrk, VFMADD213PSZrkz, VFMA...
    printOperand(MI, 2, O); 
    break;
  case 7:
    // VMOVAPDZ128mrk, VMOVAPDZ256mrk, VMOVAPDZmrk, VMOVAPSZ128mrk, VMOVAPSZ2...
    printOperand(MI, 5, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  case 8:
    // VPCONFLICTDrr, VPCONFLICTQrr, VPLZCNTDrr, VPLZCNTQrr
    return;
    break;
  case 9:
    // VPSCATTERDDZmr, VPSCATTERDQZmr, VPSCATTERQDZmr, VPSCATTERQQZmr, VSCATT...
    printOperand(MI, 6, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  }


  // Fragment 4 encoded into 4 bits for 9 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 38) & 15);
  switch ((Bits >> 38) & 15) {
  default:   // unreachable.
  case 0:
    // ANDN32rm, ANDN64rm, CMPPDrri, CMPPSrri, CMPSDrr, CMPSSrr, Int_CMPSDrr,...
    printOperand(MI, 0, O); 
    break;
  case 1:
    // Int_VCMPSDrm, Int_VCMPSSrm, VCMPPDrmi, VCMPPSrmi, VCMPSDZrm, VCMPSDrm,...
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 2:
    // Int_VCMPSDrr, Int_VCMPSSrr, VADDPDZrmk, VADDPDZrmkz, VADDPDZrrk, VADDP...
    printOperand(MI, 1, O); 
    break;
  case 3:
    // VASTART_SAVE_XMM_REGS, VPCONFLICTDrmbk, VPCONFLICTQrmbk, VPLZCNTDrmbk,...
    printOperand(MI, 2, O); 
    break;
  case 4:
    // VBROADCASTI32X4krm, VBROADCASTI64X4krm, VFMADD213PDZrkz, VFMADD213PSZr...
    SStream_concat0(O, "} {z}"); 
    op_addAvxZeroOpmask(MI);
    return;
    break;
  case 5:
    // VFMADD213PDZrk, VFMADD213PSZrk, VFMADDSUB213PDZrk, VFMADDSUB213PSZrk, ...
    SStream_concat0(O, "}"); 
    return;
    break;
  case 6:
    // VGATHERDPDZrm, VGATHERDPSZrm, VGATHERQPDZrm, VGATHERQPSZrm, VPGATHERDD...
    printOperand(MI, 3, O); 
    SStream_concat0(O, "}"); 
    return;
    break;
  case 7:
    // VPERMI2Drrkz, VPERMI2PDrrkz, VPERMI2PSrrkz, VPERMI2Qrrkz, VPERMT2Drrkz...
    SStream_concat0(O, "} {z} "); 
    op_addAvxZeroOpmask(MI);
    return;
    break;
  case 8:
    // VPMOVSXBDZrmk, VPMOVSXBDZrrk, VPMOVSXBQZrmk, VPMOVSXBQZrrk, VPMOVSXDQZ...
    SStream_concat0(O, "} "); 
    return;
    break;
  }


  // Fragment 5 encoded into 3 bits for 6 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 42) & 7);
  switch ((Bits >> 42) & 7) {
  default:   // unreachable.
  case 0:
    // ANDN32rm, ANDN64rm, CMPPDrri, CMPPSrri, CMPSDrr, CMPSSrr, Int_CMPSDrr,...
    return;
    break;
  case 1:
    // Int_VCMPSDrr, Int_VCMPSSrr, VCMPPDYrri, VCMPPDZrri, VCMPPDrri, VCMPPSY...
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 2:
    // VADDPDZrmbk, VADDPDZrmbkz, VADDPSZrmbk, VADDPSZrmbkz, VDIVPDZrmbk, VDI...
    SStream_concat0(O, " {"); 
    printOperand(MI, 1, O); 
    break;
  case 3:
    // VADDPDZrmk, VADDPSZrmk, VBLENDMPDZrm, VBLENDMPDZrr, VBLENDMPSZrm, VBLE...
    SStream_concat0(O, "}"); 
    return;
    break;
  case 4:
    // VADDPDZrmkz, VADDPDZrrkz, VADDPSZrmkz, VADDPSZrrkz, VALIGNDrrikz, VALI...
    SStream_concat0(O, "} {z}"); 
    op_addAvxZeroOpmask(MI);
    return;
    break;
  case 5:
    // VADDPDZrrk, VADDPSZrrk, VDIVPDZrrk, VDIVPSZrrk, VMAXPDZrrk, VMAXPSZrrk...
    SStream_concat0(O, "} "); 
    return;
    break;
  }


  // Fragment 6 encoded into 1 bits for 2 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 45) & 1);
  if ((Bits >> 45) & 1) {
    // VADDPDZrmbkz, VADDPSZrmbkz, VDIVPDZrmbkz, VDIVPSZrmbkz, VMAXPDZrmbkz, ...
    SStream_concat0(O, "} {z}"); 
    op_addAvxZeroOpmask(MI);
    return;
  } else {
    // VADDPDZrmbk, VADDPSZrmbk, VDIVPDZrmbk, VDIVPSZrmbk, VMAXPDZrmbk, VMAXP...
    SStream_concat0(O, "}"); 
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 234 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 's', 't', '(', '0', ')', 0,
  /* 6 */ 's', 't', '(', '1', ')', 0,
  /* 12 */ 's', 't', '(', '2', ')', 0,
  /* 18 */ 's', 't', '(', '3', ')', 0,
  /* 24 */ 's', 't', '(', '4', ')', 0,
  /* 30 */ 's', 't', '(', '5', ')', 0,
  /* 36 */ 's', 't', '(', '6', ')', 0,
  /* 42 */ 's', 't', '(', '7', ')', 0,
  /* 48 */ 'x', 'm', 'm', '1', '0', 0,
  /* 54 */ 'y', 'm', 'm', '1', '0', 0,
  /* 60 */ 'z', 'm', 'm', '1', '0', 0,
  /* 66 */ 'c', 'r', '1', '0', 0,
  /* 71 */ 'x', 'm', 'm', '2', '0', 0,
  /* 77 */ 'y', 'm', 'm', '2', '0', 0,
  /* 83 */ 'z', 'm', 'm', '2', '0', 0,
  /* 89 */ 'x', 'm', 'm', '3', '0', 0,
  /* 95 */ 'y', 'm', 'm', '3', '0', 0,
  /* 101 */ 'z', 'm', 'm', '3', '0', 0,
  /* 107 */ 'k', '0', 0,
  /* 110 */ 'x', 'm', 'm', '0', 0,
  /* 115 */ 'y', 'm', 'm', '0', 0,
  /* 120 */ 'z', 'm', 'm', '0', 0,
  /* 125 */ 'f', 'p', '0', 0,
  /* 129 */ 'c', 'r', '0', 0,
  /* 133 */ 'd', 'r', '0', 0,
  /* 137 */ 'x', 'm', 'm', '1', '1', 0,
  /* 143 */ 'y', 'm', 'm', '1', '1', 0,
  /* 149 */ 'z', 'm', 'm', '1', '1', 0,
  /* 155 */ 'c', 'r', '1', '1', 0,
  /* 160 */ 'x', 'm', 'm', '2', '1', 0,
  /* 166 */ 'y', 'm', 'm', '2', '1', 0,
  /* 172 */ 'z', 'm', 'm', '2', '1', 0,
  /* 178 */ 'x', 'm', 'm', '3', '1', 0,
  /* 184 */ 'y', 'm', 'm', '3', '1', 0,
  /* 190 */ 'z', 'm', 'm', '3', '1', 0,
  /* 196 */ 'k', '1', 0,
  /* 199 */ 'x', 'm', 'm', '1', 0,
  /* 204 */ 'y', 'm', 'm', '1', 0,
  /* 209 */ 'z', 'm', 'm', '1', 0,
  /* 214 */ 'f', 'p', '1', 0,
  /* 218 */ 'c', 'r', '1', 0,
  /* 222 */ 'd', 'r', '1', 0,
  /* 226 */ 'x', 'm', 'm', '1', '2', 0,
  /* 232 */ 'y', 'm', 'm', '1', '2', 0,
  /* 238 */ 'z', 'm', 'm', '1', '2', 0,
  /* 244 */ 'c', 'r', '1', '2', 0,
  /* 249 */ 'x', 'm', 'm', '2', '2', 0,
  /* 255 */ 'y', 'm', 'm', '2', '2', 0,
  /* 261 */ 'z', 'm', 'm', '2', '2', 0,
  /* 267 */ 'k', '2', 0,
  /* 270 */ 'x', 'm', 'm', '2', 0,
  /* 275 */ 'y', 'm', 'm', '2', 0,
  /* 280 */ 'z', 'm', 'm', '2', 0,
  /* 285 */ 'f', 'p', '2', 0,
  /* 289 */ 'c', 'r', '2', 0,
  /* 293 */ 'd', 'r', '2', 0,
  /* 297 */ 'x', 'm', 'm', '1', '3', 0,
  /* 303 */ 'y', 'm', 'm', '1', '3', 0,
  /* 309 */ 'z', 'm', 'm', '1', '3', 0,
  /* 315 */ 'c', 'r', '1', '3', 0,
  /* 320 */ 'x', 'm', 'm', '2', '3', 0,
  /* 326 */ 'y', 'm', 'm', '2', '3', 0,
  /* 332 */ 'z', 'm', 'm', '2', '3', 0,
  /* 338 */ 'k', '3', 0,
  /* 341 */ 'x', 'm', 'm', '3', 0,
  /* 346 */ 'y', 'm', 'm', '3', 0,
  /* 351 */ 'z', 'm', 'm', '3', 0,
  /* 356 */ 'f', 'p', '3', 0,
  /* 360 */ 'c', 'r', '3', 0,
  /* 364 */ 'd', 'r', '3', 0,
  /* 368 */ 'x', 'm', 'm', '1', '4', 0,
  /* 374 */ 'y', 'm', 'm', '1', '4', 0,
  /* 380 */ 'z', 'm', 'm', '1', '4', 0,
  /* 386 */ 'c', 'r', '1', '4', 0,
  /* 391 */ 'x', 'm', 'm', '2', '4', 0,
  /* 397 */ 'y', 'm', 'm', '2', '4', 0,
  /* 403 */ 'z', 'm', 'm', '2', '4', 0,
  /* 409 */ 'k', '4', 0,
  /* 412 */ 'x', 'm', 'm', '4', 0,
  /* 417 */ 'y', 'm', 'm', '4', 0,
  /* 422 */ 'z', 'm', 'm', '4', 0,
  /* 427 */ 'f', 'p', '4', 0,
  /* 431 */ 'c', 'r', '4', 0,
  /* 435 */ 'd', 'r', '4', 0,
  /* 439 */ 'x', 'm', 'm', '1', '5', 0,
  /* 445 */ 'y', 'm', 'm', '1', '5', 0,
  /* 451 */ 'z', 'm', 'm', '1', '5', 0,
  /* 457 */ 'c', 'r', '1', '5', 0,
  /* 462 */ 'x', 'm', 'm', '2', '5', 0,
  /* 468 */ 'y', 'm', 'm', '2', '5', 0,
  /* 474 */ 'z', 'm', 'm', '2', '5', 0,
  /* 480 */ 'k', '5', 0,
  /* 483 */ 'x', 'm', 'm', '5', 0,
  /* 488 */ 'y', 'm', 'm', '5', 0,
  /* 493 */ 'z', 'm', 'm', '5', 0,
  /* 498 */ 'f', 'p', '5', 0,
  /* 502 */ 'c', 'r', '5', 0,
  /* 506 */ 'd', 'r', '5', 0,
  /* 510 */ 'x', 'm', 'm', '1', '6', 0,
  /* 516 */ 'y', 'm', 'm', '1', '6', 0,
  /* 522 */ 'z', 'm', 'm', '1', '6', 0,
  /* 528 */ 'x', 'm', 'm', '2', '6', 0,
  /* 534 */ 'y', 'm', 'm', '2', '6', 0,
  /* 540 */ 'z', 'm', 'm', '2', '6', 0,
  /* 546 */ 'k', '6', 0,
  /* 549 */ 'x', 'm', 'm', '6', 0,
  /* 554 */ 'y', 'm', 'm', '6', 0,
  /* 559 */ 'z', 'm', 'm', '6', 0,
  /* 564 */ 'f', 'p', '6', 0,
  /* 568 */ 'c', 'r', '6', 0,
  /* 572 */ 'd', 'r', '6', 0,
  /* 576 */ 'x', 'm', 'm', '1', '7', 0,
  /* 582 */ 'y', 'm', 'm', '1', '7', 0,
  /* 588 */ 'z', 'm', 'm', '1', '7', 0,
  /* 594 */ 'x', 'm', 'm', '2', '7', 0,
  /* 600 */ 'y', 'm', 'm', '2', '7', 0,
  /* 606 */ 'z', 'm', 'm', '2', '7', 0,
  /* 612 */ 'k', '7', 0,
  /* 615 */ 'x', 'm', 'm', '7', 0,
  /* 620 */ 'y', 'm', 'm', '7', 0,
  /* 625 */ 'z', 'm', 'm', '7', 0,
  /* 630 */ 'f', 'p', '7', 0,
  /* 634 */ 'c', 'r', '7', 0,
  /* 638 */ 'd', 'r', '7', 0,
  /* 642 */ 'x', 'm', 'm', '1', '8', 0,
  /* 648 */ 'y', 'm', 'm', '1', '8', 0,
  /* 654 */ 'z', 'm', 'm', '1', '8', 0,
  /* 660 */ 'x', 'm', 'm', '2', '8', 0,
  /* 666 */ 'y', 'm', 'm', '2', '8', 0,
  /* 672 */ 'z', 'm', 'm', '2', '8', 0,
  /* 678 */ 'x', 'm', 'm', '8', 0,
  /* 683 */ 'y', 'm', 'm', '8', 0,
  /* 688 */ 'z', 'm', 'm', '8', 0,
  /* 693 */ 'c', 'r', '8', 0,
  /* 697 */ 'x', 'm', 'm', '1', '9', 0,
  /* 703 */ 'y', 'm', 'm', '1', '9', 0,
  /* 709 */ 'z', 'm', 'm', '1', '9', 0,
  /* 715 */ 'x', 'm', 'm', '2', '9', 0,
  /* 721 */ 'y', 'm', 'm', '2', '9', 0,
  /* 727 */ 'z', 'm', 'm', '2', '9', 0,
  /* 733 */ 'x', 'm', 'm', '9', 0,
  /* 738 */ 'y', 'm', 'm', '9', 0,
  /* 743 */ 'z', 'm', 'm', '9', 0,
  /* 748 */ 'c', 'r', '9', 0,
  /* 752 */ 'r', '1', '0', 'b', 0,
  /* 757 */ 'r', '1', '1', 'b', 0,
  /* 762 */ 'r', '1', '2', 'b', 0,
  /* 767 */ 'r', '1', '3', 'b', 0,
  /* 772 */ 'r', '1', '4', 'b', 0,
  /* 777 */ 'r', '1', '5', 'b', 0,
  /* 782 */ 'r', '8', 'b', 0,
  /* 786 */ 'r', '9', 'b', 0,
  /* 790 */ 'r', '1', '0', 'd', 0,
  /* 795 */ 'r', '1', '1', 'd', 0,
  /* 800 */ 'r', '1', '2', 'd', 0,
  /* 805 */ 'r', '1', '3', 'd', 0,
  /* 810 */ 'r', '1', '4', 'd', 0,
  /* 815 */ 'r', '1', '5', 'd', 0,
  /* 820 */ 'r', '8', 'd', 0,
  /* 824 */ 'r', '9', 'd', 0,
  /* 828 */ 'a', 'h', 0,
  /* 831 */ 'b', 'h', 0,
  /* 834 */ 'c', 'h', 0,
  /* 837 */ 'd', 'h', 0,
  /* 840 */ 'e', 'd', 'i', 0,
  /* 844 */ 'r', 'd', 'i', 0,
  /* 848 */ 'e', 's', 'i', 0,
  /* 852 */ 'r', 's', 'i', 0,
  /* 856 */ 'a', 'l', 0,
  /* 859 */ 'b', 'l', 0,
  /* 862 */ 'c', 'l', 0,
  /* 865 */ 'd', 'l', 0,
  /* 868 */ 'd', 'i', 'l', 0,
  /* 872 */ 's', 'i', 'l', 0,
  /* 876 */ 'b', 'p', 'l', 0,
  /* 880 */ 's', 'p', 'l', 0,
  /* 884 */ 'e', 'b', 'p', 0,
  /* 888 */ 'r', 'b', 'p', 0,
  /* 892 */ 'e', 'i', 'p', 0,
  /* 896 */ 'r', 'i', 'p', 0,
  /* 900 */ 'e', 's', 'p', 0,
  /* 904 */ 'r', 's', 'p', 0,
  /* 908 */ 'c', 's', 0,
  /* 911 */ 'd', 's', 0,
  /* 914 */ 'e', 's', 0,
  /* 917 */ 'f', 's', 0,
  /* 920 */ 'f', 'l', 'a', 'g', 's', 0,
  /* 926 */ 's', 's', 0,
  /* 929 */ 'r', '1', '0', 'w', 0,
  /* 934 */ 'r', '1', '1', 'w', 0,
  /* 939 */ 'r', '1', '2', 'w', 0,
  /* 944 */ 'r', '1', '3', 'w', 0,
  /* 949 */ 'r', '1', '4', 'w', 0,
  /* 954 */ 'r', '1', '5', 'w', 0,
  /* 959 */ 'r', '8', 'w', 0,
  /* 963 */ 'r', '9', 'w', 0,
  /* 967 */ 'f', 'p', 's', 'w', 0,
  /* 972 */ 'e', 'a', 'x', 0,
  /* 976 */ 'r', 'a', 'x', 0,
  /* 980 */ 'e', 'b', 'x', 0,
  /* 984 */ 'r', 'b', 'x', 0,
  /* 988 */ 'e', 'c', 'x', 0,
  /* 992 */ 'r', 'c', 'x', 0,
  /* 996 */ 'e', 'd', 'x', 0,
  /* 1000 */ 'r', 'd', 'x', 0,
  /* 1004 */ 'e', 'i', 'z', 0,
  /* 1008 */ 'r', 'i', 'z', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    828, 856, 973, 831, 859, 885, 876, 981, 834, 862, 908, 989, 837, 841, 
    868, 865, 911, 997, 972, 884, 980, 988, 840, 996, 920, 892, 1004, 914, 
    848, 900, 967, 917, 923, 893, 976, 888, 984, 992, 844, 1000, 896, 1008, 
    852, 904, 849, 872, 901, 880, 926, 129, 218, 289, 360, 431, 502, 568, 
    634, 693, 748, 66, 155, 244, 315, 386, 457, 133, 222, 293, 364, 435, 
    506, 572, 638, 125, 214, 285, 356, 427, 498, 564, 630, 107, 196, 267, 
    338, 409, 480, 546, 612, 111, 200, 271, 342, 413, 484, 550, 616, 694, 
    749, 67, 156, 245, 316, 387, 458, 0, 6, 12, 18, 24, 30, 36, 
    42, 110, 199, 270, 341, 412, 483, 549, 615, 678, 733, 48, 137, 226, 
    297, 368, 439, 510, 576, 642, 697, 71, 160, 249, 320, 391, 462, 528, 
    594, 660, 715, 89, 178, 115, 204, 275, 346, 417, 488, 554, 620, 683, 
    738, 54, 143, 232, 303, 374, 445, 516, 582, 648, 703, 77, 166, 255, 
    326, 397, 468, 534, 600, 666, 721, 95, 184, 120, 209, 280, 351, 422, 
    493, 559, 625, 688, 743, 60, 149, 238, 309, 380, 451, 522, 588, 654, 
    709, 83, 172, 261, 332, 403, 474, 540, 606, 672, 727, 101, 190, 782, 
    786, 752, 757, 762, 767, 772, 777, 820, 824, 790, 795, 800, 805, 810, 
    815, 959, 963, 929, 934, 939, 944, 949, 954, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printf64mem(MI, OpIdx, OS);
    break;
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case X86_AAD8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAD8i8 10)
      AsmString = "aad";
      break;
    }
    return NULL;
  case X86_AAM8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAM8i8 10)
      AsmString = "aam";
      break;
    }
    return NULL;
  case X86_CVTSD2SI64rm:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(X86_GR64RegClassID, 0)) {
      // (CVTSD2SI64rm GR64:$dst, sdmem:$src)
      AsmString = "cvtsd2siq	$\xFF\x02\x01, $\x01";
      break;
    }
    return NULL;
  case X86_XSTORE:
    if (MCInst_getNumOperands(MI) == 0) {
      // (XSTORE)
      AsmString = "xstorerng";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
