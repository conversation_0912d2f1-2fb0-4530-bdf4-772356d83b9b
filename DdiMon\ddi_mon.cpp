// Copyright (c) 2015-2018, <PERSON><PERSON>. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

/// @file
/// Implements DdiMon functions.

#include "ddi_mon.h"
#include "ddi_mon_ioctl.h"
#include "process_hiding.h"
#include "shadow_hook.h"
#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "../HyperPlatform/HyperPlatform/util.h"
#include "../HyperPlatform/HyperPlatform/ept.h"

////////////////////////////////////////////////////////////////////////////////
//
// macro utilities
//

////////////////////////////////////////////////////////////////////////////////
//
// constants and macros
//

////////////////////////////////////////////////////////////////////////////////
//
// types
//

// Removed: Traditional API hook related types - not needed for EPT HOOK functionality

////////////////////////////////////////////////////////////////////////////////
//
// prototypes
//

// Removed: Traditional API hook function prototypes - not needed for EPT HOOK functionality

// Process hiding cleanup function
extern "C" VOID DdimonpCleanupHiddenProcesses(void);

// Process hiding hooks
extern "C" NTSTATUS ProcessHidingSetSharedData(SharedShadowHookData* shared_data);
extern "C" NTSTATUS ProcessHidingInitialize(void);
extern "C" VOID ProcessHidingTerminate(void);

// Hidden processes cleanup
extern "C" VOID DdimonpCleanupHiddenProcessesExternal(void);

#if defined(ALLOC_PRAGMA)
#pragma alloc_text(PAGE, DdimonInitialization)
#pragma alloc_text(PAGE, DdimonTermination)
#endif

////////////////////////////////////////////////////////////////////////////////
//
// variables
//

// Defines where to install shadow hooks and their handlers
//
// Because of simplified implementation of DdiMon, DdiMon is unable to handle
// any of following exports properly:
//  - already unmapped exports (eg, ones on the INIT section) because it no
//    longer exists on memory
//  - exported data because setting 0xcc does not make any sense in this case
//  - functions does not comply x64 calling conventions, for example Zw*
//    functions. Because contents of stack do not hold expected values leading
//    handlers to failure of parameter analysis that may result in bug check.
//
// Also the following care should be taken:
//  - Function parameters may be an user-address space pointer and not
//    trusted. Even a kernel-address space pointer should not be trusted for
//    production level security. Verity and capture all contents from user
//    supplied address to VMM, then use them.
// Removed: Traditional API hook targets - not needed for EPT HOOK functionality

////////////////////////////////////////////////////////////////////////////////
//
// implementations
//

// Initializes DdiMon
_Use_decl_annotations_ EXTERN_C NTSTATUS
DdimonInitialization(SharedShadowHookData* shared_sh_data) {
  PAGED_CODE();

  // Phase 5: Real EPT Hook implementation with detailed logging
  HYPERPLATFORM_LOG_INFO("=== DdiMon Phase 5 Initialization Starting ===");
  HYPERPLATFORM_LOG_INFO("DdiMon initialized with shared shadow hook data at 0x%p", shared_sh_data);

  if (!shared_sh_data) {
    HYPERPLATFORM_LOG_ERROR("CRITICAL: Shared shadow hook data is NULL!");
    return STATUS_INVALID_PARAMETER;
  }

  // Set shared data for process hiding
  HYPERPLATFORM_LOG_INFO("Step 1: Setting shared data for process hiding");
  NTSTATUS status = ProcessHidingSetSharedData(shared_sh_data);
  if (!NT_SUCCESS(status)) {
    HYPERPLATFORM_LOG_ERROR("FAILED Step 1: Failed to set process hiding shared data: 0x%08X", status);
    return status;
  }
  HYPERPLATFORM_LOG_INFO("SUCCESS Step 1: Shared data set successfully");

  // Initialize process hiding hooks
  HYPERPLATFORM_LOG_INFO("Step 2: Initializing process hiding hooks");
  status = ProcessHidingInitialize();
  if (!NT_SUCCESS(status)) {
    HYPERPLATFORM_LOG_ERROR("FAILED Step 2: Failed to initialize process hiding: 0x%08X", status);
    return status;
  }
  HYPERPLATFORM_LOG_INFO("SUCCESS Step 2: Process hiding hooks initialized");

  HYPERPLATFORM_LOG_INFO("=== DdiMon Phase 5 Initialization Completed Successfully ===");
  HYPERPLATFORM_LOG_INFO("DdiMon initialization completed successfully (Phase 5 - Real Hooks Active).");
  return STATUS_SUCCESS;
}

// Terminates DdiMon
_Use_decl_annotations_ EXTERN_C void DdimonTermination() {
  PAGED_CODE();

  // Clean up process hiding hooks first
  ProcessHidingTerminate();

  // Clean up dynamic EPT hooks
  DdimonpCleanupDynamicHooks();

  // Clean up hidden processes
  DdimonpCleanupHiddenProcessesExternal();

  HYPERPLATFORM_LOG_INFO("DdiMon has been terminated.");
}

// Removed: Traditional API hook related functions - not needed for EPT HOOK functionality

// Removed: Traditional API hook callback - not needed for EPT HOOK functionality

// Removed: Pool tag conversion and original function finder - not needed for EPT HOOK functionality

// Removed: All traditional API hook handlers - not needed for EPT HOOK functionality
