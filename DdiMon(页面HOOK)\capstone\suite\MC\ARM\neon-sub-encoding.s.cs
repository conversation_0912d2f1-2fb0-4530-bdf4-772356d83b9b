# CS_ARCH_ARM, CS_MODE_ARM, None
0xa0,0x08,0x41,0xf3 = vsub.i8 d16, d17, d16
0xa0,0x08,0x51,0xf3 = vsub.i16 d16, d17, d16
0xa0,0x08,0x61,0xf3 = vsub.i32 d16, d17, d16
0xa0,0x08,0x71,0xf3 = vsub.i64 d16, d17, d16
0xa1,0x0d,0x60,0xf2 = vsub.f32 d16, d16, d17
0xe2,0x08,0x40,0xf3 = vsub.i8 q8, q8, q9
0xe2,0x08,0x50,0xf3 = vsub.i16 q8, q8, q9
0xe2,0x08,0x60,0xf3 = vsub.i32 q8, q8, q9
0xe2,0x08,0x70,0xf3 = vsub.i64 q8, q8, q9
0xe2,0x0d,0x60,0xf2 = vsub.f32 q8, q8, q9
0x25,0xd8,0x0d,0xf3 = vsub.i8 d13, d13, d21
0x26,0xe8,0x1e,0xf3 = vsub.i16 d14, d14, d22
0x27,0xf8,0x2f,0xf3 = vsub.i32 d15, d15, d23
0xa8,0x08,0x70,0xf3 = vsub.i64 d16, d16, d24
0xa9,0x1d,0x61,0xf2 = vsub.f32 d17, d17, d25
0x64,0x28,0x02,0xf3 = vsub.i8 q1, q1, q10
0x62,0x48,0x14,0xf3 = vsub.i16 q2, q2, q9
0x60,0x68,0x26,0xf3 = vsub.i32 q3, q3, q8
0x4e,0x88,0x38,0xf3 = vsub.i64 q4, q4, q7
0x4c,0xad,0x2a,0xf2 = vsub.f32 q5, q5, q6
0xa0,0x02,0xc1,0xf2 = vsubl.s8 q8, d17, d16
0xa0,0x02,0xd1,0xf2 = vsubl.s16 q8, d17, d16
0xa0,0x02,0xe1,0xf2 = vsubl.s32 q8, d17, d16
0xa0,0x02,0xc1,0xf3 = vsubl.u8 q8, d17, d16
0xa0,0x02,0xd1,0xf3 = vsubl.u16 q8, d17, d16
0xa0,0x02,0xe1,0xf3 = vsubl.u32 q8, d17, d16
0xa2,0x03,0xc0,0xf2 = vsubw.s8 q8, q8, d18
0xa2,0x03,0xd0,0xf2 = vsubw.s16 q8, q8, d18
0xa2,0x03,0xe0,0xf2 = vsubw.s32 q8, q8, d18
0xa2,0x03,0xc0,0xf3 = vsubw.u8 q8, q8, d18
0xa2,0x03,0xd0,0xf3 = vsubw.u16 q8, q8, d18
0xa2,0x03,0xe0,0xf3 = vsubw.u32 q8, q8, d18
0xa1,0x02,0x40,0xf2 = vhsub.s8 d16, d16, d17
0xa1,0x02,0x50,0xf2 = vhsub.s16 d16, d16, d17
0xa1,0x02,0x60,0xf2 = vhsub.s32 d16, d16, d17
0xa1,0x02,0x40,0xf3 = vhsub.u8 d16, d16, d17
0xa1,0x02,0x50,0xf3 = vhsub.u16 d16, d16, d17
0xa1,0x02,0x60,0xf3 = vhsub.u32 d16, d16, d17
0xe2,0x02,0x40,0xf2 = vhsub.s8 q8, q8, q9
0xe2,0x02,0x50,0xf2 = vhsub.s16 q8, q8, q9
0xe2,0x02,0x60,0xf2 = vhsub.s32 q8, q8, q9
0xb1,0x02,0x40,0xf2 = vqsub.s8 d16, d16, d17
0xb1,0x02,0x50,0xf2 = vqsub.s16 d16, d16, d17
0xb1,0x02,0x60,0xf2 = vqsub.s32 d16, d16, d17
0xb1,0x02,0x70,0xf2 = vqsub.s64 d16, d16, d17
0xb1,0x02,0x40,0xf3 = vqsub.u8 d16, d16, d17
0xb1,0x02,0x50,0xf3 = vqsub.u16 d16, d16, d17
0xb1,0x02,0x60,0xf3 = vqsub.u32 d16, d16, d17
0xb1,0x02,0x70,0xf3 = vqsub.u64 d16, d16, d17
0xf2,0x02,0x40,0xf2 = vqsub.s8 q8, q8, q9
0xf2,0x02,0x50,0xf2 = vqsub.s16 q8, q8, q9
0xf2,0x02,0x60,0xf2 = vqsub.s32 q8, q8, q9
0xf2,0x02,0x70,0xf2 = vqsub.s64 q8, q8, q9
0xf2,0x02,0x40,0xf3 = vqsub.u8 q8, q8, q9
0xf2,0x02,0x50,0xf3 = vqsub.u16 q8, q8, q9
0xf2,0x02,0x60,0xf3 = vqsub.u32 q8, q8, q9
0xf2,0x02,0x70,0xf3 = vqsub.u64 q8, q8, q9
0xa2,0x06,0xc0,0xf2 = vsubhn.i16 d16, q8, q9
0xa2,0x06,0xd0,0xf2 = vsubhn.i32 d16, q8, q9
0xa2,0x06,0xe0,0xf2 = vsubhn.i64 d16, q8, q9
0xa2,0x06,0xc0,0xf3 = vrsubhn.i16 d16, q8, q9
0xa2,0x06,0xd0,0xf3 = vrsubhn.i32 d16, q8, q9
0xa2,0x06,0xe0,0xf3 = vrsubhn.i64 d16, q8, q9
0x28,0xb2,0x0b,0xf2 = vhsub.s8 d11, d11, d24
0x27,0xc2,0x1c,0xf2 = vhsub.s16 d12, d12, d23
0x26,0xd2,0x2d,0xf2 = vhsub.s32 d13, d13, d22
0x25,0xe2,0x0e,0xf3 = vhsub.u8 d14, d14, d21
0x24,0xf2,0x1f,0xf3 = vhsub.u16 d15, d15, d20
0xa3,0x02,0x60,0xf3 = vhsub.u32 d16, d16, d19
0x68,0x22,0x02,0xf2 = vhsub.s8 q1, q1, q12
0x66,0x42,0x14,0xf2 = vhsub.s16 q2, q2, q11
0x64,0x62,0x26,0xf2 = vhsub.s32 q3, q3, q10
0x62,0x82,0x08,0xf3 = vhsub.u8 q4, q4, q9
0x60,0xa2,0x1a,0xf3 = vhsub.u16 q5, q5, q8
0x4e,0xc2,0x2c,0xf3 = vhsub.u32 q6, q6, q7
0x05,0xc3,0x8c,0xf2 = vsubw.s8 q6, q6, d5
0x01,0xe3,0x9e,0xf2 = vsubw.s16 q7, q7, d1
0x82,0x03,0xe0,0xf2 = vsubw.s32 q8, q8, d2
0x05,0xc3,0x8c,0xf3 = vsubw.u8 q6, q6, d5
0x01,0xe3,0x9e,0xf3 = vsubw.u16 q7, q7, d1
0x82,0x03,0xe0,0xf3 = vsubw.u32 q8, q8, d2
