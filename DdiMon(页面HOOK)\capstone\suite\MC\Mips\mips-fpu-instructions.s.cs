# CS_ARCH_MIPS, CS_MODE_MIPS32, None
0x05,0x73,0x20,0x46 = abs.d $f12, $f14
0x85,0x39,0x00,0x46 = abs.s $f6, $f7
0x00,0x62,0x2e,0x46 = add.d $f8, $f12, $f14
0x40,0x32,0x07,0x46 = add.s $f9, $f6, $f7
0x0f,0x73,0x20,0x46 = floor.w.d $f12, $f14
0x8f,0x39,0x00,0x46 = floor.w.s $f6, $f7
0x0e,0x73,0x20,0x46 = ceil.w.d $f12, $f14
0x8e,0x39,0x00,0x46 = ceil.w.s $f6, $f7
0x02,0x62,0x2e,0x46 = mul.d $f8, $f12, $f14
0x42,0x32,0x07,0x46 = mul.s $f9, $f6, $f7
0x07,0x73,0x20,0x46 = neg.d $f12, $f14
0x87,0x39,0x00,0x46 = neg.s $f6, $f7
0x0c,0x73,0x20,0x46 = round.w.d $f12, $f14
0x8c,0x39,0x00,0x46 = round.w.s $f6, $f7
0x04,0x73,0x20,0x46 = sqrt.d $f12, $f14
0x84,0x39,0x00,0x46 = sqrt.s $f6, $f7
0x01,0x62,0x2e,0x46 = sub.d $f8, $f12, $f14
0x41,0x32,0x07,0x46 = sub.s $f9, $f6, $f7
0x0d,0x73,0x20,0x46 = trunc.w.d $f12, $f14
0x8d,0x39,0x00,0x46 = trunc.w.s $f6, $f7
0x32,0x60,0x2e,0x46 = c.eq.d $f12, $f14
0x32,0x30,0x07,0x46 = c.eq.s $f6, $f7
0x30,0x60,0x2e,0x46 = c.f.d $f12, $f14
0x30,0x30,0x07,0x46 = c.f.s $f6, $f7
0x3e,0x60,0x2e,0x46 = c.le.d $f12, $f14
0x3e,0x30,0x07,0x46 = c.le.s $f6, $f7
0x3c,0x60,0x2e,0x46 = c.lt.d $f12, $f14
0x3c,0x30,0x07,0x46 = c.lt.s $f6, $f7
0x3d,0x60,0x2e,0x46 = c.nge.d $f12, $f14
0x3d,0x30,0x07,0x46 = c.nge.s $f6, $f7
0x3b,0x60,0x2e,0x46 = c.ngl.d $f12, $f14
0x3b,0x30,0x07,0x46 = c.ngl.s $f6, $f7
0x39,0x60,0x2e,0x46 = c.ngle.d $f12, $f14
0x39,0x30,0x07,0x46 = c.ngle.s $f6, $f7
0x3f,0x60,0x2e,0x46 = c.ngt.d $f12, $f14
0x3f,0x30,0x07,0x46 = c.ngt.s $f6, $f7
0x36,0x60,0x2e,0x46 = c.ole.d $f12, $f14
0x36,0x30,0x07,0x46 = c.ole.s $f6, $f7
0x34,0x60,0x2e,0x46 = c.olt.d $f12, $f14
0x34,0x30,0x07,0x46 = c.olt.s $f6, $f7
0x3a,0x60,0x2e,0x46 = c.seq.d $f12, $f14
0x3a,0x30,0x07,0x46 = c.seq.s $f6, $f7
0x38,0x60,0x2e,0x46 = c.sf.d $f12, $f14
0x38,0x30,0x07,0x46 = c.sf.s $f6, $f7
0x33,0x60,0x2e,0x46 = c.ueq.d $f12, $f14
0x33,0xe0,0x12,0x46 = c.ueq.s $f28, $f18
0x37,0x60,0x2e,0x46 = c.ule.d $f12, $f14
0x37,0x30,0x07,0x46 = c.ule.s $f6, $f7
0x35,0x60,0x2e,0x46 = c.ult.d $f12, $f14
0x35,0x30,0x07,0x46 = c.ult.s $f6, $f7
0x31,0x60,0x2e,0x46 = c.un.d $f12, $f14
0x31,0x30,0x07,0x46 = c.un.s $f6, $f7
0xa1,0x39,0x00,0x46 = cvt.d.s $f6, $f7
0x21,0x73,0x80,0x46 = cvt.d.w $f12, $f14
0x20,0x73,0x20,0x46 = cvt.s.d $f12, $f14
0xa0,0x39,0x80,0x46 = cvt.s.w $f6, $f7
0x24,0x73,0x20,0x46 = cvt.w.d $f12, $f14
0xa4,0x39,0x00,0x46 = cvt.w.s $f6, $f7
0x00,0x00,0x46,0x44 = cfc1 $6, $0
0x00,0xf8,0xca,0x44 = ctc1 $10, $31
0x00,0x38,0x06,0x44 = mfc1 $6, $f7
0x10,0x28,0x00,0x00 = mfhi $5
0x12,0x28,0x00,0x00 = mflo $5
0x86,0x41,0x20,0x46 = mov.d $f6, $f8
0x86,0x39,0x00,0x46 = mov.s $f6, $f7
0x00,0x38,0x86,0x44 = mtc1 $6, $f7
0x11,0x00,0xe0,0x00 = mthi $7
0x13,0x00,0xe0,0x00 = mtlo $7
0xc6,0x23,0xe9,0xe4 = swc1 $f9, 9158($7)
0x00,0x38,0x06,0x40 = mfc0 $6, $7, 0
0x00,0x40,0x89,0x40 = mtc0 $9, $8, 0
0x00,0x38,0x05,0x48 = mfc2 $5, $7, 0
0x00,0x20,0x89,0x48 = mtc2 $9, $4, 0
0x02,0x38,0x06,0x40 = mfc0 $6, $7, 2
0x03,0x40,0x89,0x40 = mtc0 $9, $8, 3
0x04,0x38,0x05,0x48 = mfc2 $5, $7, 4
0x05,0x20,0x89,0x48 = mtc2 $9, $4, 5
0x01,0x10,0x20,0x00 = movf $2, $1, $fcc0
0x01,0x10,0x21,0x00 = movt $2, $1, $fcc0
0x01,0x20,0xb1,0x00 = movt $4, $5, $fcc4
0x11,0x31,0x28,0x46 = movf.d $f4, $f6, $fcc2
0x11,0x31,0x14,0x46 = movf.s $f4, $f6, $fcc5
0x05,0x00,0xa6,0x4c = luxc1 $f0, $6($5)
0x0d,0x20,0xb8,0x4c = suxc1 $f4, $24($5)
0x00,0x05,0xcc,0x4d = lwxc1 $f20, $12($14)
0x08,0xd0,0xd2,0x4e = swxc1 $f26, $18($22)
0x00,0x20,0x71,0x44 = mfhc1 $17, $f4
0x00,0x30,0xf1,0x44 = mthc1 $17, $f6
0x10,0x00,0xa4,0xeb = swc2 $4, 16($sp)
0x10,0x00,0xa4,0xfb = sdc2 $4, 16($sp)
0x0c,0x00,0xeb,0xcb = lwc2 $11, 12($ra)
0x0c,0x00,0xeb,0xdb = ldc2 $11, 12($ra)
