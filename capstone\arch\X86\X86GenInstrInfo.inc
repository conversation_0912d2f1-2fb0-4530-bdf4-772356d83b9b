/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    X86_PHI	= 0,
    X86_INLINEASM	= 1,
    X86_CFI_INSTRUCTION	= 2,
    X86_EH_LABEL	= 3,
    X86_GC_LABEL	= 4,
    X86_KILL	= 5,
    X86_EXTRACT_SUBREG	= 6,
    X86_INSERT_SUBREG	= 7,
    X86_IMPLICIT_DEF	= 8,
    X86_SUBREG_TO_REG	= 9,
    X86_COPY_TO_REGCLASS	= 10,
    X86_DBG_VALUE	= 11,
    X86_REG_SEQUENCE	= 12,
    X86_COPY	= 13,
    X86_BUNDLE	= 14,
    X86_LIFETIME_START	= 15,
    X86_LIFETIME_END	= 16,
    X86_STACKMAP	= 17,
    X86_PATCHPOINT	= 18,
    X86_LOAD_STACK_GUARD	= 19,
    X86_AAA	= 20,
    X86_AAD8i8	= 21,
    X86_AAM8i8	= 22,
    X86_AAS	= 23,
    X86_ABS_F	= 24,
    X86_ABS_Fp32	= 25,
    X86_ABS_Fp64	= 26,
    X86_ABS_Fp80	= 27,
    X86_ACQUIRE_MOV16rm	= 28,
    X86_ACQUIRE_MOV32rm	= 29,
    X86_ACQUIRE_MOV64rm	= 30,
    X86_ACQUIRE_MOV8rm	= 31,
    X86_ADC16i16	= 32,
    X86_ADC16mi	= 33,
    X86_ADC16mi8	= 34,
    X86_ADC16mr	= 35,
    X86_ADC16ri	= 36,
    X86_ADC16ri8	= 37,
    X86_ADC16rm	= 38,
    X86_ADC16rr	= 39,
    X86_ADC16rr_REV	= 40,
    X86_ADC32i32	= 41,
    X86_ADC32mi	= 42,
    X86_ADC32mi8	= 43,
    X86_ADC32mr	= 44,
    X86_ADC32ri	= 45,
    X86_ADC32ri8	= 46,
    X86_ADC32rm	= 47,
    X86_ADC32rr	= 48,
    X86_ADC32rr_REV	= 49,
    X86_ADC64i32	= 50,
    X86_ADC64mi32	= 51,
    X86_ADC64mi8	= 52,
    X86_ADC64mr	= 53,
    X86_ADC64ri32	= 54,
    X86_ADC64ri8	= 55,
    X86_ADC64rm	= 56,
    X86_ADC64rr	= 57,
    X86_ADC64rr_REV	= 58,
    X86_ADC8i8	= 59,
    X86_ADC8mi	= 60,
    X86_ADC8mr	= 61,
    X86_ADC8ri	= 62,
    X86_ADC8rm	= 63,
    X86_ADC8rr	= 64,
    X86_ADC8rr_REV	= 65,
    X86_ADCX32rm	= 66,
    X86_ADCX32rr	= 67,
    X86_ADCX64rm	= 68,
    X86_ADCX64rr	= 69,
    X86_ADD16i16	= 70,
    X86_ADD16mi	= 71,
    X86_ADD16mi8	= 72,
    X86_ADD16mr	= 73,
    X86_ADD16ri	= 74,
    X86_ADD16ri8	= 75,
    X86_ADD16ri8_DB	= 76,
    X86_ADD16ri_DB	= 77,
    X86_ADD16rm	= 78,
    X86_ADD16rr	= 79,
    X86_ADD16rr_DB	= 80,
    X86_ADD16rr_REV	= 81,
    X86_ADD32i32	= 82,
    X86_ADD32mi	= 83,
    X86_ADD32mi8	= 84,
    X86_ADD32mr	= 85,
    X86_ADD32ri	= 86,
    X86_ADD32ri8	= 87,
    X86_ADD32ri8_DB	= 88,
    X86_ADD32ri_DB	= 89,
    X86_ADD32rm	= 90,
    X86_ADD32rr	= 91,
    X86_ADD32rr_DB	= 92,
    X86_ADD32rr_REV	= 93,
    X86_ADD64i32	= 94,
    X86_ADD64mi32	= 95,
    X86_ADD64mi8	= 96,
    X86_ADD64mr	= 97,
    X86_ADD64ri32	= 98,
    X86_ADD64ri32_DB	= 99,
    X86_ADD64ri8	= 100,
    X86_ADD64ri8_DB	= 101,
    X86_ADD64rm	= 102,
    X86_ADD64rr	= 103,
    X86_ADD64rr_DB	= 104,
    X86_ADD64rr_REV	= 105,
    X86_ADD8i8	= 106,
    X86_ADD8mi	= 107,
    X86_ADD8mr	= 108,
    X86_ADD8ri	= 109,
    X86_ADD8ri8	= 110,
    X86_ADD8rm	= 111,
    X86_ADD8rr	= 112,
    X86_ADD8rr_REV	= 113,
    X86_ADDPDrm	= 114,
    X86_ADDPDrr	= 115,
    X86_ADDPSrm	= 116,
    X86_ADDPSrr	= 117,
    X86_ADDSDrm	= 118,
    X86_ADDSDrm_Int	= 119,
    X86_ADDSDrr	= 120,
    X86_ADDSDrr_Int	= 121,
    X86_ADDSSrm	= 122,
    X86_ADDSSrm_Int	= 123,
    X86_ADDSSrr	= 124,
    X86_ADDSSrr_Int	= 125,
    X86_ADDSUBPDrm	= 126,
    X86_ADDSUBPDrr	= 127,
    X86_ADDSUBPSrm	= 128,
    X86_ADDSUBPSrr	= 129,
    X86_ADD_F32m	= 130,
    X86_ADD_F64m	= 131,
    X86_ADD_FI16m	= 132,
    X86_ADD_FI32m	= 133,
    X86_ADD_FPrST0	= 134,
    X86_ADD_FST0r	= 135,
    X86_ADD_Fp32	= 136,
    X86_ADD_Fp32m	= 137,
    X86_ADD_Fp64	= 138,
    X86_ADD_Fp64m	= 139,
    X86_ADD_Fp64m32	= 140,
    X86_ADD_Fp80	= 141,
    X86_ADD_Fp80m32	= 142,
    X86_ADD_Fp80m64	= 143,
    X86_ADD_FpI16m32	= 144,
    X86_ADD_FpI16m64	= 145,
    X86_ADD_FpI16m80	= 146,
    X86_ADD_FpI32m32	= 147,
    X86_ADD_FpI32m64	= 148,
    X86_ADD_FpI32m80	= 149,
    X86_ADD_FrST0	= 150,
    X86_ADJCALLSTACKDOWN32	= 151,
    X86_ADJCALLSTACKDOWN64	= 152,
    X86_ADJCALLSTACKUP32	= 153,
    X86_ADJCALLSTACKUP64	= 154,
    X86_ADOX32rm	= 155,
    X86_ADOX32rr	= 156,
    X86_ADOX64rm	= 157,
    X86_ADOX64rr	= 158,
    X86_AESDECLASTrm	= 159,
    X86_AESDECLASTrr	= 160,
    X86_AESDECrm	= 161,
    X86_AESDECrr	= 162,
    X86_AESENCLASTrm	= 163,
    X86_AESENCLASTrr	= 164,
    X86_AESENCrm	= 165,
    X86_AESENCrr	= 166,
    X86_AESIMCrm	= 167,
    X86_AESIMCrr	= 168,
    X86_AESKEYGENASSIST128rm	= 169,
    X86_AESKEYGENASSIST128rr	= 170,
    X86_AND16i16	= 171,
    X86_AND16mi	= 172,
    X86_AND16mi8	= 173,
    X86_AND16mr	= 174,
    X86_AND16ri	= 175,
    X86_AND16ri8	= 176,
    X86_AND16rm	= 177,
    X86_AND16rr	= 178,
    X86_AND16rr_REV	= 179,
    X86_AND32i32	= 180,
    X86_AND32mi	= 181,
    X86_AND32mi8	= 182,
    X86_AND32mr	= 183,
    X86_AND32ri	= 184,
    X86_AND32ri8	= 185,
    X86_AND32rm	= 186,
    X86_AND32rr	= 187,
    X86_AND32rr_REV	= 188,
    X86_AND64i32	= 189,
    X86_AND64mi32	= 190,
    X86_AND64mi8	= 191,
    X86_AND64mr	= 192,
    X86_AND64ri32	= 193,
    X86_AND64ri8	= 194,
    X86_AND64rm	= 195,
    X86_AND64rr	= 196,
    X86_AND64rr_REV	= 197,
    X86_AND8i8	= 198,
    X86_AND8mi	= 199,
    X86_AND8mr	= 200,
    X86_AND8ri	= 201,
    X86_AND8ri8	= 202,
    X86_AND8rm	= 203,
    X86_AND8rr	= 204,
    X86_AND8rr_REV	= 205,
    X86_ANDN32rm	= 206,
    X86_ANDN32rr	= 207,
    X86_ANDN64rm	= 208,
    X86_ANDN64rr	= 209,
    X86_ANDNPDrm	= 210,
    X86_ANDNPDrr	= 211,
    X86_ANDNPSrm	= 212,
    X86_ANDNPSrr	= 213,
    X86_ANDPDrm	= 214,
    X86_ANDPDrr	= 215,
    X86_ANDPSrm	= 216,
    X86_ANDPSrr	= 217,
    X86_ARPL16mr	= 218,
    X86_ARPL16rr	= 219,
    X86_AVX2_SETALLONES	= 220,
    X86_AVX512_512_SET0	= 221,
    X86_AVX_SET0	= 222,
    X86_BEXTR32rm	= 223,
    X86_BEXTR32rr	= 224,
    X86_BEXTR64rm	= 225,
    X86_BEXTR64rr	= 226,
    X86_BEXTRI32mi	= 227,
    X86_BEXTRI32ri	= 228,
    X86_BEXTRI64mi	= 229,
    X86_BEXTRI64ri	= 230,
    X86_BLCFILL32rm	= 231,
    X86_BLCFILL32rr	= 232,
    X86_BLCFILL64rm	= 233,
    X86_BLCFILL64rr	= 234,
    X86_BLCI32rm	= 235,
    X86_BLCI32rr	= 236,
    X86_BLCI64rm	= 237,
    X86_BLCI64rr	= 238,
    X86_BLCIC32rm	= 239,
    X86_BLCIC32rr	= 240,
    X86_BLCIC64rm	= 241,
    X86_BLCIC64rr	= 242,
    X86_BLCMSK32rm	= 243,
    X86_BLCMSK32rr	= 244,
    X86_BLCMSK64rm	= 245,
    X86_BLCMSK64rr	= 246,
    X86_BLCS32rm	= 247,
    X86_BLCS32rr	= 248,
    X86_BLCS64rm	= 249,
    X86_BLCS64rr	= 250,
    X86_BLENDPDrmi	= 251,
    X86_BLENDPDrri	= 252,
    X86_BLENDPSrmi	= 253,
    X86_BLENDPSrri	= 254,
    X86_BLENDVPDrm0	= 255,
    X86_BLENDVPDrr0	= 256,
    X86_BLENDVPSrm0	= 257,
    X86_BLENDVPSrr0	= 258,
    X86_BLSFILL32rm	= 259,
    X86_BLSFILL32rr	= 260,
    X86_BLSFILL64rm	= 261,
    X86_BLSFILL64rr	= 262,
    X86_BLSI32rm	= 263,
    X86_BLSI32rr	= 264,
    X86_BLSI64rm	= 265,
    X86_BLSI64rr	= 266,
    X86_BLSIC32rm	= 267,
    X86_BLSIC32rr	= 268,
    X86_BLSIC64rm	= 269,
    X86_BLSIC64rr	= 270,
    X86_BLSMSK32rm	= 271,
    X86_BLSMSK32rr	= 272,
    X86_BLSMSK64rm	= 273,
    X86_BLSMSK64rr	= 274,
    X86_BLSR32rm	= 275,
    X86_BLSR32rr	= 276,
    X86_BLSR64rm	= 277,
    X86_BLSR64rr	= 278,
    X86_BOUNDS16rm	= 279,
    X86_BOUNDS32rm	= 280,
    X86_BSF16rm	= 281,
    X86_BSF16rr	= 282,
    X86_BSF32rm	= 283,
    X86_BSF32rr	= 284,
    X86_BSF64rm	= 285,
    X86_BSF64rr	= 286,
    X86_BSR16rm	= 287,
    X86_BSR16rr	= 288,
    X86_BSR32rm	= 289,
    X86_BSR32rr	= 290,
    X86_BSR64rm	= 291,
    X86_BSR64rr	= 292,
    X86_BSWAP32r	= 293,
    X86_BSWAP64r	= 294,
    X86_BT16mi8	= 295,
    X86_BT16mr	= 296,
    X86_BT16ri8	= 297,
    X86_BT16rr	= 298,
    X86_BT32mi8	= 299,
    X86_BT32mr	= 300,
    X86_BT32ri8	= 301,
    X86_BT32rr	= 302,
    X86_BT64mi8	= 303,
    X86_BT64mr	= 304,
    X86_BT64ri8	= 305,
    X86_BT64rr	= 306,
    X86_BTC16mi8	= 307,
    X86_BTC16mr	= 308,
    X86_BTC16ri8	= 309,
    X86_BTC16rr	= 310,
    X86_BTC32mi8	= 311,
    X86_BTC32mr	= 312,
    X86_BTC32ri8	= 313,
    X86_BTC32rr	= 314,
    X86_BTC64mi8	= 315,
    X86_BTC64mr	= 316,
    X86_BTC64ri8	= 317,
    X86_BTC64rr	= 318,
    X86_BTR16mi8	= 319,
    X86_BTR16mr	= 320,
    X86_BTR16ri8	= 321,
    X86_BTR16rr	= 322,
    X86_BTR32mi8	= 323,
    X86_BTR32mr	= 324,
    X86_BTR32ri8	= 325,
    X86_BTR32rr	= 326,
    X86_BTR64mi8	= 327,
    X86_BTR64mr	= 328,
    X86_BTR64ri8	= 329,
    X86_BTR64rr	= 330,
    X86_BTS16mi8	= 331,
    X86_BTS16mr	= 332,
    X86_BTS16ri8	= 333,
    X86_BTS16rr	= 334,
    X86_BTS32mi8	= 335,
    X86_BTS32mr	= 336,
    X86_BTS32ri8	= 337,
    X86_BTS32rr	= 338,
    X86_BTS64mi8	= 339,
    X86_BTS64mr	= 340,
    X86_BTS64ri8	= 341,
    X86_BTS64rr	= 342,
    X86_BZHI32rm	= 343,
    X86_BZHI32rr	= 344,
    X86_BZHI64rm	= 345,
    X86_BZHI64rr	= 346,
    X86_CALL16m	= 347,
    X86_CALL16r	= 348,
    X86_CALL32m	= 349,
    X86_CALL32r	= 350,
    X86_CALL64m	= 351,
    X86_CALL64pcrel32	= 352,
    X86_CALL64r	= 353,
    X86_CALLpcrel16	= 354,
    X86_CALLpcrel32	= 355,
    X86_CBW	= 356,
    X86_CDQ	= 357,
    X86_CDQE	= 358,
    X86_CHS_F	= 359,
    X86_CHS_Fp32	= 360,
    X86_CHS_Fp64	= 361,
    X86_CHS_Fp80	= 362,
    X86_CLAC	= 363,
    X86_CLC	= 364,
    X86_CLD	= 365,
    X86_CLFLUSH	= 366,
    X86_CLGI	= 367,
    X86_CLI	= 368,
    X86_CLTS	= 369,
    X86_CMC	= 370,
    X86_CMOVA16rm	= 371,
    X86_CMOVA16rr	= 372,
    X86_CMOVA32rm	= 373,
    X86_CMOVA32rr	= 374,
    X86_CMOVA64rm	= 375,
    X86_CMOVA64rr	= 376,
    X86_CMOVAE16rm	= 377,
    X86_CMOVAE16rr	= 378,
    X86_CMOVAE32rm	= 379,
    X86_CMOVAE32rr	= 380,
    X86_CMOVAE64rm	= 381,
    X86_CMOVAE64rr	= 382,
    X86_CMOVB16rm	= 383,
    X86_CMOVB16rr	= 384,
    X86_CMOVB32rm	= 385,
    X86_CMOVB32rr	= 386,
    X86_CMOVB64rm	= 387,
    X86_CMOVB64rr	= 388,
    X86_CMOVBE16rm	= 389,
    X86_CMOVBE16rr	= 390,
    X86_CMOVBE32rm	= 391,
    X86_CMOVBE32rr	= 392,
    X86_CMOVBE64rm	= 393,
    X86_CMOVBE64rr	= 394,
    X86_CMOVBE_F	= 395,
    X86_CMOVBE_Fp32	= 396,
    X86_CMOVBE_Fp64	= 397,
    X86_CMOVBE_Fp80	= 398,
    X86_CMOVB_F	= 399,
    X86_CMOVB_Fp32	= 400,
    X86_CMOVB_Fp64	= 401,
    X86_CMOVB_Fp80	= 402,
    X86_CMOVE16rm	= 403,
    X86_CMOVE16rr	= 404,
    X86_CMOVE32rm	= 405,
    X86_CMOVE32rr	= 406,
    X86_CMOVE64rm	= 407,
    X86_CMOVE64rr	= 408,
    X86_CMOVE_F	= 409,
    X86_CMOVE_Fp32	= 410,
    X86_CMOVE_Fp64	= 411,
    X86_CMOVE_Fp80	= 412,
    X86_CMOVG16rm	= 413,
    X86_CMOVG16rr	= 414,
    X86_CMOVG32rm	= 415,
    X86_CMOVG32rr	= 416,
    X86_CMOVG64rm	= 417,
    X86_CMOVG64rr	= 418,
    X86_CMOVGE16rm	= 419,
    X86_CMOVGE16rr	= 420,
    X86_CMOVGE32rm	= 421,
    X86_CMOVGE32rr	= 422,
    X86_CMOVGE64rm	= 423,
    X86_CMOVGE64rr	= 424,
    X86_CMOVL16rm	= 425,
    X86_CMOVL16rr	= 426,
    X86_CMOVL32rm	= 427,
    X86_CMOVL32rr	= 428,
    X86_CMOVL64rm	= 429,
    X86_CMOVL64rr	= 430,
    X86_CMOVLE16rm	= 431,
    X86_CMOVLE16rr	= 432,
    X86_CMOVLE32rm	= 433,
    X86_CMOVLE32rr	= 434,
    X86_CMOVLE64rm	= 435,
    X86_CMOVLE64rr	= 436,
    X86_CMOVNBE_F	= 437,
    X86_CMOVNBE_Fp32	= 438,
    X86_CMOVNBE_Fp64	= 439,
    X86_CMOVNBE_Fp80	= 440,
    X86_CMOVNB_F	= 441,
    X86_CMOVNB_Fp32	= 442,
    X86_CMOVNB_Fp64	= 443,
    X86_CMOVNB_Fp80	= 444,
    X86_CMOVNE16rm	= 445,
    X86_CMOVNE16rr	= 446,
    X86_CMOVNE32rm	= 447,
    X86_CMOVNE32rr	= 448,
    X86_CMOVNE64rm	= 449,
    X86_CMOVNE64rr	= 450,
    X86_CMOVNE_F	= 451,
    X86_CMOVNE_Fp32	= 452,
    X86_CMOVNE_Fp64	= 453,
    X86_CMOVNE_Fp80	= 454,
    X86_CMOVNO16rm	= 455,
    X86_CMOVNO16rr	= 456,
    X86_CMOVNO32rm	= 457,
    X86_CMOVNO32rr	= 458,
    X86_CMOVNO64rm	= 459,
    X86_CMOVNO64rr	= 460,
    X86_CMOVNP16rm	= 461,
    X86_CMOVNP16rr	= 462,
    X86_CMOVNP32rm	= 463,
    X86_CMOVNP32rr	= 464,
    X86_CMOVNP64rm	= 465,
    X86_CMOVNP64rr	= 466,
    X86_CMOVNP_F	= 467,
    X86_CMOVNP_Fp32	= 468,
    X86_CMOVNP_Fp64	= 469,
    X86_CMOVNP_Fp80	= 470,
    X86_CMOVNS16rm	= 471,
    X86_CMOVNS16rr	= 472,
    X86_CMOVNS32rm	= 473,
    X86_CMOVNS32rr	= 474,
    X86_CMOVNS64rm	= 475,
    X86_CMOVNS64rr	= 476,
    X86_CMOVO16rm	= 477,
    X86_CMOVO16rr	= 478,
    X86_CMOVO32rm	= 479,
    X86_CMOVO32rr	= 480,
    X86_CMOVO64rm	= 481,
    X86_CMOVO64rr	= 482,
    X86_CMOVP16rm	= 483,
    X86_CMOVP16rr	= 484,
    X86_CMOVP32rm	= 485,
    X86_CMOVP32rr	= 486,
    X86_CMOVP64rm	= 487,
    X86_CMOVP64rr	= 488,
    X86_CMOVP_F	= 489,
    X86_CMOVP_Fp32	= 490,
    X86_CMOVP_Fp64	= 491,
    X86_CMOVP_Fp80	= 492,
    X86_CMOVS16rm	= 493,
    X86_CMOVS16rr	= 494,
    X86_CMOVS32rm	= 495,
    X86_CMOVS32rr	= 496,
    X86_CMOVS64rm	= 497,
    X86_CMOVS64rr	= 498,
    X86_CMOV_FR32	= 499,
    X86_CMOV_FR64	= 500,
    X86_CMOV_GR16	= 501,
    X86_CMOV_GR32	= 502,
    X86_CMOV_GR8	= 503,
    X86_CMOV_RFP32	= 504,
    X86_CMOV_RFP64	= 505,
    X86_CMOV_RFP80	= 506,
    X86_CMOV_V16F32	= 507,
    X86_CMOV_V2F64	= 508,
    X86_CMOV_V2I64	= 509,
    X86_CMOV_V4F32	= 510,
    X86_CMOV_V4F64	= 511,
    X86_CMOV_V4I64	= 512,
    X86_CMOV_V8F32	= 513,
    X86_CMOV_V8F64	= 514,
    X86_CMOV_V8I64	= 515,
    X86_CMP16i16	= 516,
    X86_CMP16mi	= 517,
    X86_CMP16mi8	= 518,
    X86_CMP16mr	= 519,
    X86_CMP16ri	= 520,
    X86_CMP16ri8	= 521,
    X86_CMP16rm	= 522,
    X86_CMP16rr	= 523,
    X86_CMP16rr_REV	= 524,
    X86_CMP32i32	= 525,
    X86_CMP32mi	= 526,
    X86_CMP32mi8	= 527,
    X86_CMP32mr	= 528,
    X86_CMP32ri	= 529,
    X86_CMP32ri8	= 530,
    X86_CMP32rm	= 531,
    X86_CMP32rr	= 532,
    X86_CMP32rr_REV	= 533,
    X86_CMP64i32	= 534,
    X86_CMP64mi32	= 535,
    X86_CMP64mi8	= 536,
    X86_CMP64mr	= 537,
    X86_CMP64ri32	= 538,
    X86_CMP64ri8	= 539,
    X86_CMP64rm	= 540,
    X86_CMP64rr	= 541,
    X86_CMP64rr_REV	= 542,
    X86_CMP8i8	= 543,
    X86_CMP8mi	= 544,
    X86_CMP8mr	= 545,
    X86_CMP8ri	= 546,
    X86_CMP8rm	= 547,
    X86_CMP8rr	= 548,
    X86_CMP8rr_REV	= 549,
    X86_CMPPDrmi	= 550,
    X86_CMPPDrmi_alt	= 551,
    X86_CMPPDrri	= 552,
    X86_CMPPDrri_alt	= 553,
    X86_CMPPSrmi	= 554,
    X86_CMPPSrmi_alt	= 555,
    X86_CMPPSrri	= 556,
    X86_CMPPSrri_alt	= 557,
    X86_CMPSB	= 558,
    X86_CMPSDrm	= 559,
    X86_CMPSDrm_alt	= 560,
    X86_CMPSDrr	= 561,
    X86_CMPSDrr_alt	= 562,
    X86_CMPSL	= 563,
    X86_CMPSQ	= 564,
    X86_CMPSSrm	= 565,
    X86_CMPSSrm_alt	= 566,
    X86_CMPSSrr	= 567,
    X86_CMPSSrr_alt	= 568,
    X86_CMPSW	= 569,
    X86_CMPXCHG16B	= 570,
    X86_CMPXCHG16rm	= 571,
    X86_CMPXCHG16rr	= 572,
    X86_CMPXCHG32rm	= 573,
    X86_CMPXCHG32rr	= 574,
    X86_CMPXCHG64rm	= 575,
    X86_CMPXCHG64rr	= 576,
    X86_CMPXCHG8B	= 577,
    X86_CMPXCHG8rm	= 578,
    X86_CMPXCHG8rr	= 579,
    X86_COMISDrm	= 580,
    X86_COMISDrr	= 581,
    X86_COMISSrm	= 582,
    X86_COMISSrr	= 583,
    X86_COMP_FST0r	= 584,
    X86_COM_FIPr	= 585,
    X86_COM_FIr	= 586,
    X86_COM_FST0r	= 587,
    X86_COS_F	= 588,
    X86_COS_Fp32	= 589,
    X86_COS_Fp64	= 590,
    X86_COS_Fp80	= 591,
    X86_CPUID32	= 592,
    X86_CPUID64	= 593,
    X86_CQO	= 594,
    X86_CRC32r32m16	= 595,
    X86_CRC32r32m32	= 596,
    X86_CRC32r32m8	= 597,
    X86_CRC32r32r16	= 598,
    X86_CRC32r32r32	= 599,
    X86_CRC32r32r8	= 600,
    X86_CRC32r64m64	= 601,
    X86_CRC32r64m8	= 602,
    X86_CRC32r64r64	= 603,
    X86_CRC32r64r8	= 604,
    X86_CVTDQ2PDrm	= 605,
    X86_CVTDQ2PDrr	= 606,
    X86_CVTDQ2PSrm	= 607,
    X86_CVTDQ2PSrr	= 608,
    X86_CVTPD2DQrm	= 609,
    X86_CVTPD2DQrr	= 610,
    X86_CVTPD2PSrm	= 611,
    X86_CVTPD2PSrr	= 612,
    X86_CVTPS2DQrm	= 613,
    X86_CVTPS2DQrr	= 614,
    X86_CVTPS2PDrm	= 615,
    X86_CVTPS2PDrr	= 616,
    X86_CVTSD2SI64rm	= 617,
    X86_CVTSD2SI64rr	= 618,
    X86_CVTSD2SIrm	= 619,
    X86_CVTSD2SIrr	= 620,
    X86_CVTSD2SSrm	= 621,
    X86_CVTSD2SSrr	= 622,
    X86_CVTSI2SD64rm	= 623,
    X86_CVTSI2SD64rr	= 624,
    X86_CVTSI2SDrm	= 625,
    X86_CVTSI2SDrr	= 626,
    X86_CVTSI2SS64rm	= 627,
    X86_CVTSI2SS64rr	= 628,
    X86_CVTSI2SSrm	= 629,
    X86_CVTSI2SSrr	= 630,
    X86_CVTSS2SDrm	= 631,
    X86_CVTSS2SDrr	= 632,
    X86_CVTSS2SI64rm	= 633,
    X86_CVTSS2SI64rr	= 634,
    X86_CVTSS2SIrm	= 635,
    X86_CVTSS2SIrr	= 636,
    X86_CVTTPD2DQrm	= 637,
    X86_CVTTPD2DQrr	= 638,
    X86_CVTTPS2DQrm	= 639,
    X86_CVTTPS2DQrr	= 640,
    X86_CVTTSD2SI64rm	= 641,
    X86_CVTTSD2SI64rr	= 642,
    X86_CVTTSD2SIrm	= 643,
    X86_CVTTSD2SIrr	= 644,
    X86_CVTTSS2SI64rm	= 645,
    X86_CVTTSS2SI64rr	= 646,
    X86_CVTTSS2SIrm	= 647,
    X86_CVTTSS2SIrr	= 648,
    X86_CWD	= 649,
    X86_CWDE	= 650,
    X86_DAA	= 651,
    X86_DAS	= 652,
    X86_DATA16_PREFIX	= 653,
    X86_DEC16m	= 654,
    X86_DEC16r	= 655,
    X86_DEC32_16r	= 656,
    X86_DEC32_32r	= 657,
    X86_DEC32m	= 658,
    X86_DEC32r	= 659,
    X86_DEC64_16m	= 660,
    X86_DEC64_16r	= 661,
    X86_DEC64_32m	= 662,
    X86_DEC64_32r	= 663,
    X86_DEC64m	= 664,
    X86_DEC64r	= 665,
    X86_DEC8m	= 666,
    X86_DEC8r	= 667,
    X86_DIV16m	= 668,
    X86_DIV16r	= 669,
    X86_DIV32m	= 670,
    X86_DIV32r	= 671,
    X86_DIV64m	= 672,
    X86_DIV64r	= 673,
    X86_DIV8m	= 674,
    X86_DIV8r	= 675,
    X86_DIVPDrm	= 676,
    X86_DIVPDrr	= 677,
    X86_DIVPSrm	= 678,
    X86_DIVPSrr	= 679,
    X86_DIVR_F32m	= 680,
    X86_DIVR_F64m	= 681,
    X86_DIVR_FI16m	= 682,
    X86_DIVR_FI32m	= 683,
    X86_DIVR_FPrST0	= 684,
    X86_DIVR_FST0r	= 685,
    X86_DIVR_Fp32m	= 686,
    X86_DIVR_Fp64m	= 687,
    X86_DIVR_Fp64m32	= 688,
    X86_DIVR_Fp80m32	= 689,
    X86_DIVR_Fp80m64	= 690,
    X86_DIVR_FpI16m32	= 691,
    X86_DIVR_FpI16m64	= 692,
    X86_DIVR_FpI16m80	= 693,
    X86_DIVR_FpI32m32	= 694,
    X86_DIVR_FpI32m64	= 695,
    X86_DIVR_FpI32m80	= 696,
    X86_DIVR_FrST0	= 697,
    X86_DIVSDrm	= 698,
    X86_DIVSDrm_Int	= 699,
    X86_DIVSDrr	= 700,
    X86_DIVSDrr_Int	= 701,
    X86_DIVSSrm	= 702,
    X86_DIVSSrm_Int	= 703,
    X86_DIVSSrr	= 704,
    X86_DIVSSrr_Int	= 705,
    X86_DIV_F32m	= 706,
    X86_DIV_F64m	= 707,
    X86_DIV_FI16m	= 708,
    X86_DIV_FI32m	= 709,
    X86_DIV_FPrST0	= 710,
    X86_DIV_FST0r	= 711,
    X86_DIV_Fp32	= 712,
    X86_DIV_Fp32m	= 713,
    X86_DIV_Fp64	= 714,
    X86_DIV_Fp64m	= 715,
    X86_DIV_Fp64m32	= 716,
    X86_DIV_Fp80	= 717,
    X86_DIV_Fp80m32	= 718,
    X86_DIV_Fp80m64	= 719,
    X86_DIV_FpI16m32	= 720,
    X86_DIV_FpI16m64	= 721,
    X86_DIV_FpI16m80	= 722,
    X86_DIV_FpI32m32	= 723,
    X86_DIV_FpI32m64	= 724,
    X86_DIV_FpI32m80	= 725,
    X86_DIV_FrST0	= 726,
    X86_DPPDrmi	= 727,
    X86_DPPDrri	= 728,
    X86_DPPSrmi	= 729,
    X86_DPPSrri	= 730,
    X86_EH_RETURN	= 731,
    X86_EH_RETURN64	= 732,
    X86_EH_SjLj_LongJmp32	= 733,
    X86_EH_SjLj_LongJmp64	= 734,
    X86_EH_SjLj_SetJmp32	= 735,
    X86_EH_SjLj_SetJmp64	= 736,
    X86_EH_SjLj_Setup	= 737,
    X86_ENCLS	= 738,
    X86_ENCLU	= 739,
    X86_ENTER	= 740,
    X86_EXTRACTPSmr	= 741,
    X86_EXTRACTPSrr	= 742,
    X86_EXTRQ	= 743,
    X86_EXTRQI	= 744,
    X86_F2XM1	= 745,
    X86_FARCALL16i	= 746,
    X86_FARCALL16m	= 747,
    X86_FARCALL32i	= 748,
    X86_FARCALL32m	= 749,
    X86_FARCALL64	= 750,
    X86_FARJMP16i	= 751,
    X86_FARJMP16m	= 752,
    X86_FARJMP32i	= 753,
    X86_FARJMP32m	= 754,
    X86_FARJMP64	= 755,
    X86_FBLDm	= 756,
    X86_FBSTPm	= 757,
    X86_FCOM32m	= 758,
    X86_FCOM64m	= 759,
    X86_FCOMP32m	= 760,
    X86_FCOMP64m	= 761,
    X86_FCOMPP	= 762,
    X86_FDECSTP	= 763,
    X86_FEMMS	= 764,
    X86_FFREE	= 765,
    X86_FICOM16m	= 766,
    X86_FICOM32m	= 767,
    X86_FICOMP16m	= 768,
    X86_FICOMP32m	= 769,
    X86_FINCSTP	= 770,
    X86_FLDCW16m	= 771,
    X86_FLDENVm	= 772,
    X86_FLDL2E	= 773,
    X86_FLDL2T	= 774,
    X86_FLDLG2	= 775,
    X86_FLDLN2	= 776,
    X86_FLDPI	= 777,
    X86_FNCLEX	= 778,
    X86_FNINIT	= 779,
    X86_FNOP	= 780,
    X86_FNSTCW16m	= 781,
    X86_FNSTSW16r	= 782,
    X86_FNSTSWm	= 783,
    X86_FP32_TO_INT16_IN_MEM	= 784,
    X86_FP32_TO_INT32_IN_MEM	= 785,
    X86_FP32_TO_INT64_IN_MEM	= 786,
    X86_FP64_TO_INT16_IN_MEM	= 787,
    X86_FP64_TO_INT32_IN_MEM	= 788,
    X86_FP64_TO_INT64_IN_MEM	= 789,
    X86_FP80_TO_INT16_IN_MEM	= 790,
    X86_FP80_TO_INT32_IN_MEM	= 791,
    X86_FP80_TO_INT64_IN_MEM	= 792,
    X86_FPATAN	= 793,
    X86_FPREM	= 794,
    X86_FPREM1	= 795,
    X86_FPTAN	= 796,
    X86_FRNDINT	= 797,
    X86_FRSTORm	= 798,
    X86_FSAVEm	= 799,
    X86_FSCALE	= 800,
    X86_FSETPM	= 801,
    X86_FSINCOS	= 802,
    X86_FSTENVm	= 803,
    X86_FXAM	= 804,
    X86_FXRSTOR	= 805,
    X86_FXRSTOR64	= 806,
    X86_FXSAVE	= 807,
    X86_FXSAVE64	= 808,
    X86_FXTRACT	= 809,
    X86_FYL2X	= 810,
    X86_FYL2XP1	= 811,
    X86_FsANDNPDrm	= 812,
    X86_FsANDNPDrr	= 813,
    X86_FsANDNPSrm	= 814,
    X86_FsANDNPSrr	= 815,
    X86_FsANDPDrm	= 816,
    X86_FsANDPDrr	= 817,
    X86_FsANDPSrm	= 818,
    X86_FsANDPSrr	= 819,
    X86_FsFLD0SD	= 820,
    X86_FsFLD0SS	= 821,
    X86_FsMOVAPDrm	= 822,
    X86_FsMOVAPSrm	= 823,
    X86_FsORPDrm	= 824,
    X86_FsORPDrr	= 825,
    X86_FsORPSrm	= 826,
    X86_FsORPSrr	= 827,
    X86_FsVMOVAPDrm	= 828,
    X86_FsVMOVAPSrm	= 829,
    X86_FsXORPDrm	= 830,
    X86_FsXORPDrr	= 831,
    X86_FsXORPSrm	= 832,
    X86_FsXORPSrr	= 833,
    X86_GETSEC	= 834,
    X86_HADDPDrm	= 835,
    X86_HADDPDrr	= 836,
    X86_HADDPSrm	= 837,
    X86_HADDPSrr	= 838,
    X86_HLT	= 839,
    X86_HSUBPDrm	= 840,
    X86_HSUBPDrr	= 841,
    X86_HSUBPSrm	= 842,
    X86_HSUBPSrr	= 843,
    X86_IDIV16m	= 844,
    X86_IDIV16r	= 845,
    X86_IDIV32m	= 846,
    X86_IDIV32r	= 847,
    X86_IDIV64m	= 848,
    X86_IDIV64r	= 849,
    X86_IDIV8m	= 850,
    X86_IDIV8r	= 851,
    X86_ILD_F16m	= 852,
    X86_ILD_F32m	= 853,
    X86_ILD_F64m	= 854,
    X86_ILD_Fp16m32	= 855,
    X86_ILD_Fp16m64	= 856,
    X86_ILD_Fp16m80	= 857,
    X86_ILD_Fp32m32	= 858,
    X86_ILD_Fp32m64	= 859,
    X86_ILD_Fp32m80	= 860,
    X86_ILD_Fp64m32	= 861,
    X86_ILD_Fp64m64	= 862,
    X86_ILD_Fp64m80	= 863,
    X86_IMUL16m	= 864,
    X86_IMUL16r	= 865,
    X86_IMUL16rm	= 866,
    X86_IMUL16rmi	= 867,
    X86_IMUL16rmi8	= 868,
    X86_IMUL16rr	= 869,
    X86_IMUL16rri	= 870,
    X86_IMUL16rri8	= 871,
    X86_IMUL32m	= 872,
    X86_IMUL32r	= 873,
    X86_IMUL32rm	= 874,
    X86_IMUL32rmi	= 875,
    X86_IMUL32rmi8	= 876,
    X86_IMUL32rr	= 877,
    X86_IMUL32rri	= 878,
    X86_IMUL32rri8	= 879,
    X86_IMUL64m	= 880,
    X86_IMUL64r	= 881,
    X86_IMUL64rm	= 882,
    X86_IMUL64rmi32	= 883,
    X86_IMUL64rmi8	= 884,
    X86_IMUL64rr	= 885,
    X86_IMUL64rri32	= 886,
    X86_IMUL64rri8	= 887,
    X86_IMUL8m	= 888,
    X86_IMUL8r	= 889,
    X86_IN16ri	= 890,
    X86_IN16rr	= 891,
    X86_IN32ri	= 892,
    X86_IN32rr	= 893,
    X86_IN8ri	= 894,
    X86_IN8rr	= 895,
    X86_INC16m	= 896,
    X86_INC16r	= 897,
    X86_INC32_16r	= 898,
    X86_INC32_32r	= 899,
    X86_INC32m	= 900,
    X86_INC32r	= 901,
    X86_INC64_16m	= 902,
    X86_INC64_16r	= 903,
    X86_INC64_32m	= 904,
    X86_INC64_32r	= 905,
    X86_INC64m	= 906,
    X86_INC64r	= 907,
    X86_INC8m	= 908,
    X86_INC8r	= 909,
    X86_INSB	= 910,
    X86_INSERTPSrm	= 911,
    X86_INSERTPSrr	= 912,
    X86_INSERTQ	= 913,
    X86_INSERTQI	= 914,
    X86_INSL	= 915,
    X86_INSW	= 916,
    X86_INT	= 917,
    X86_INT1	= 918,
    X86_INT3	= 919,
    X86_INTO	= 920,
    X86_INVD	= 921,
    X86_INVEPT32	= 922,
    X86_INVEPT64	= 923,
    X86_INVLPG	= 924,
    X86_INVLPGA32	= 925,
    X86_INVLPGA64	= 926,
    X86_INVPCID32	= 927,
    X86_INVPCID64	= 928,
    X86_INVVPID32	= 929,
    X86_INVVPID64	= 930,
    X86_IRET16	= 931,
    X86_IRET32	= 932,
    X86_IRET64	= 933,
    X86_ISTT_FP16m	= 934,
    X86_ISTT_FP32m	= 935,
    X86_ISTT_FP64m	= 936,
    X86_ISTT_Fp16m32	= 937,
    X86_ISTT_Fp16m64	= 938,
    X86_ISTT_Fp16m80	= 939,
    X86_ISTT_Fp32m32	= 940,
    X86_ISTT_Fp32m64	= 941,
    X86_ISTT_Fp32m80	= 942,
    X86_ISTT_Fp64m32	= 943,
    X86_ISTT_Fp64m64	= 944,
    X86_ISTT_Fp64m80	= 945,
    X86_IST_F16m	= 946,
    X86_IST_F32m	= 947,
    X86_IST_FP16m	= 948,
    X86_IST_FP32m	= 949,
    X86_IST_FP64m	= 950,
    X86_IST_Fp16m32	= 951,
    X86_IST_Fp16m64	= 952,
    X86_IST_Fp16m80	= 953,
    X86_IST_Fp32m32	= 954,
    X86_IST_Fp32m64	= 955,
    X86_IST_Fp32m80	= 956,
    X86_IST_Fp64m32	= 957,
    X86_IST_Fp64m64	= 958,
    X86_IST_Fp64m80	= 959,
    X86_Int_CMPSDrm	= 960,
    X86_Int_CMPSDrr	= 961,
    X86_Int_CMPSSrm	= 962,
    X86_Int_CMPSSrr	= 963,
    X86_Int_COMISDrm	= 964,
    X86_Int_COMISDrr	= 965,
    X86_Int_COMISSrm	= 966,
    X86_Int_COMISSrr	= 967,
    X86_Int_CVTSD2SSrm	= 968,
    X86_Int_CVTSD2SSrr	= 969,
    X86_Int_CVTSI2SD64rm	= 970,
    X86_Int_CVTSI2SD64rr	= 971,
    X86_Int_CVTSI2SDrm	= 972,
    X86_Int_CVTSI2SDrr	= 973,
    X86_Int_CVTSI2SS64rm	= 974,
    X86_Int_CVTSI2SS64rr	= 975,
    X86_Int_CVTSI2SSrm	= 976,
    X86_Int_CVTSI2SSrr	= 977,
    X86_Int_CVTSS2SDrm	= 978,
    X86_Int_CVTSS2SDrr	= 979,
    X86_Int_CVTTSD2SI64rm	= 980,
    X86_Int_CVTTSD2SI64rr	= 981,
    X86_Int_CVTTSD2SIrm	= 982,
    X86_Int_CVTTSD2SIrr	= 983,
    X86_Int_CVTTSS2SI64rm	= 984,
    X86_Int_CVTTSS2SI64rr	= 985,
    X86_Int_CVTTSS2SIrm	= 986,
    X86_Int_CVTTSS2SIrr	= 987,
    X86_Int_MemBarrier	= 988,
    X86_Int_UCOMISDrm	= 989,
    X86_Int_UCOMISDrr	= 990,
    X86_Int_UCOMISSrm	= 991,
    X86_Int_UCOMISSrr	= 992,
    X86_Int_VCMPSDrm	= 993,
    X86_Int_VCMPSDrr	= 994,
    X86_Int_VCMPSSrm	= 995,
    X86_Int_VCMPSSrr	= 996,
    X86_Int_VCOMISDZrm	= 997,
    X86_Int_VCOMISDZrr	= 998,
    X86_Int_VCOMISDrm	= 999,
    X86_Int_VCOMISDrr	= 1000,
    X86_Int_VCOMISSZrm	= 1001,
    X86_Int_VCOMISSZrr	= 1002,
    X86_Int_VCOMISSrm	= 1003,
    X86_Int_VCOMISSrr	= 1004,
    X86_Int_VCVTSD2SSrm	= 1005,
    X86_Int_VCVTSD2SSrr	= 1006,
    X86_Int_VCVTSI2SD64Zrm	= 1007,
    X86_Int_VCVTSI2SD64Zrr	= 1008,
    X86_Int_VCVTSI2SD64rm	= 1009,
    X86_Int_VCVTSI2SD64rr	= 1010,
    X86_Int_VCVTSI2SDZrm	= 1011,
    X86_Int_VCVTSI2SDZrr	= 1012,
    X86_Int_VCVTSI2SDrm	= 1013,
    X86_Int_VCVTSI2SDrr	= 1014,
    X86_Int_VCVTSI2SS64Zrm	= 1015,
    X86_Int_VCVTSI2SS64Zrr	= 1016,
    X86_Int_VCVTSI2SS64rm	= 1017,
    X86_Int_VCVTSI2SS64rr	= 1018,
    X86_Int_VCVTSI2SSZrm	= 1019,
    X86_Int_VCVTSI2SSZrr	= 1020,
    X86_Int_VCVTSI2SSrm	= 1021,
    X86_Int_VCVTSI2SSrr	= 1022,
    X86_Int_VCVTSS2SDrm	= 1023,
    X86_Int_VCVTSS2SDrr	= 1024,
    X86_Int_VCVTTSD2SI64Zrm	= 1025,
    X86_Int_VCVTTSD2SI64Zrr	= 1026,
    X86_Int_VCVTTSD2SI64rm	= 1027,
    X86_Int_VCVTTSD2SI64rr	= 1028,
    X86_Int_VCVTTSD2SIZrm	= 1029,
    X86_Int_VCVTTSD2SIZrr	= 1030,
    X86_Int_VCVTTSD2SIrm	= 1031,
    X86_Int_VCVTTSD2SIrr	= 1032,
    X86_Int_VCVTTSD2USI64Zrm	= 1033,
    X86_Int_VCVTTSD2USI64Zrr	= 1034,
    X86_Int_VCVTTSD2USIZrm	= 1035,
    X86_Int_VCVTTSD2USIZrr	= 1036,
    X86_Int_VCVTTSS2SI64Zrm	= 1037,
    X86_Int_VCVTTSS2SI64Zrr	= 1038,
    X86_Int_VCVTTSS2SI64rm	= 1039,
    X86_Int_VCVTTSS2SI64rr	= 1040,
    X86_Int_VCVTTSS2SIZrm	= 1041,
    X86_Int_VCVTTSS2SIZrr	= 1042,
    X86_Int_VCVTTSS2SIrm	= 1043,
    X86_Int_VCVTTSS2SIrr	= 1044,
    X86_Int_VCVTTSS2USI64Zrm	= 1045,
    X86_Int_VCVTTSS2USI64Zrr	= 1046,
    X86_Int_VCVTTSS2USIZrm	= 1047,
    X86_Int_VCVTTSS2USIZrr	= 1048,
    X86_Int_VCVTUSI2SD64Zrm	= 1049,
    X86_Int_VCVTUSI2SD64Zrr	= 1050,
    X86_Int_VCVTUSI2SDZrm	= 1051,
    X86_Int_VCVTUSI2SDZrr	= 1052,
    X86_Int_VCVTUSI2SS64Zrm	= 1053,
    X86_Int_VCVTUSI2SS64Zrr	= 1054,
    X86_Int_VCVTUSI2SSZrm	= 1055,
    X86_Int_VCVTUSI2SSZrr	= 1056,
    X86_Int_VUCOMISDZrm	= 1057,
    X86_Int_VUCOMISDZrr	= 1058,
    X86_Int_VUCOMISDrm	= 1059,
    X86_Int_VUCOMISDrr	= 1060,
    X86_Int_VUCOMISSZrm	= 1061,
    X86_Int_VUCOMISSZrr	= 1062,
    X86_Int_VUCOMISSrm	= 1063,
    X86_Int_VUCOMISSrr	= 1064,
    X86_JAE_1	= 1065,
    X86_JAE_2	= 1066,
    X86_JAE_4	= 1067,
    X86_JA_1	= 1068,
    X86_JA_2	= 1069,
    X86_JA_4	= 1070,
    X86_JBE_1	= 1071,
    X86_JBE_2	= 1072,
    X86_JBE_4	= 1073,
    X86_JB_1	= 1074,
    X86_JB_2	= 1075,
    X86_JB_4	= 1076,
    X86_JCXZ	= 1077,
    X86_JECXZ_32	= 1078,
    X86_JECXZ_64	= 1079,
    X86_JE_1	= 1080,
    X86_JE_2	= 1081,
    X86_JE_4	= 1082,
    X86_JGE_1	= 1083,
    X86_JGE_2	= 1084,
    X86_JGE_4	= 1085,
    X86_JG_1	= 1086,
    X86_JG_2	= 1087,
    X86_JG_4	= 1088,
    X86_JLE_1	= 1089,
    X86_JLE_2	= 1090,
    X86_JLE_4	= 1091,
    X86_JL_1	= 1092,
    X86_JL_2	= 1093,
    X86_JL_4	= 1094,
    X86_JMP16m	= 1095,
    X86_JMP16r	= 1096,
    X86_JMP32m	= 1097,
    X86_JMP32r	= 1098,
    X86_JMP64m	= 1099,
    X86_JMP64r	= 1100,
    X86_JMP_1	= 1101,
    X86_JMP_2	= 1102,
    X86_JMP_4	= 1103,
    X86_JNE_1	= 1104,
    X86_JNE_2	= 1105,
    X86_JNE_4	= 1106,
    X86_JNO_1	= 1107,
    X86_JNO_2	= 1108,
    X86_JNO_4	= 1109,
    X86_JNP_1	= 1110,
    X86_JNP_2	= 1111,
    X86_JNP_4	= 1112,
    X86_JNS_1	= 1113,
    X86_JNS_2	= 1114,
    X86_JNS_4	= 1115,
    X86_JO_1	= 1116,
    X86_JO_2	= 1117,
    X86_JO_4	= 1118,
    X86_JP_1	= 1119,
    X86_JP_2	= 1120,
    X86_JP_4	= 1121,
    X86_JRCXZ	= 1122,
    X86_JS_1	= 1123,
    X86_JS_2	= 1124,
    X86_JS_4	= 1125,
    X86_KANDBrr	= 1126,
    X86_KANDDrr	= 1127,
    X86_KANDNBrr	= 1128,
    X86_KANDNDrr	= 1129,
    X86_KANDNQrr	= 1130,
    X86_KANDNWrr	= 1131,
    X86_KANDQrr	= 1132,
    X86_KANDWrr	= 1133,
    X86_KMOVBkk	= 1134,
    X86_KMOVBkm	= 1135,
    X86_KMOVBkr	= 1136,
    X86_KMOVBmk	= 1137,
    X86_KMOVBrk	= 1138,
    X86_KMOVDkk	= 1139,
    X86_KMOVDkm	= 1140,
    X86_KMOVDkr	= 1141,
    X86_KMOVDmk	= 1142,
    X86_KMOVDrk	= 1143,
    X86_KMOVQkk	= 1144,
    X86_KMOVQkm	= 1145,
    X86_KMOVQkr	= 1146,
    X86_KMOVQmk	= 1147,
    X86_KMOVQrk	= 1148,
    X86_KMOVWkk	= 1149,
    X86_KMOVWkm	= 1150,
    X86_KMOVWkr	= 1151,
    X86_KMOVWmk	= 1152,
    X86_KMOVWrk	= 1153,
    X86_KNOTBrr	= 1154,
    X86_KNOTDrr	= 1155,
    X86_KNOTQrr	= 1156,
    X86_KNOTWrr	= 1157,
    X86_KORBrr	= 1158,
    X86_KORDrr	= 1159,
    X86_KORQrr	= 1160,
    X86_KORTESTWrr	= 1161,
    X86_KORWrr	= 1162,
    X86_KSET0B	= 1163,
    X86_KSET0W	= 1164,
    X86_KSET1B	= 1165,
    X86_KSET1W	= 1166,
    X86_KSHIFTLWri	= 1167,
    X86_KSHIFTRWri	= 1168,
    X86_KUNPCKBWrr	= 1169,
    X86_KXNORBrr	= 1170,
    X86_KXNORDrr	= 1171,
    X86_KXNORQrr	= 1172,
    X86_KXNORWrr	= 1173,
    X86_KXORBrr	= 1174,
    X86_KXORDrr	= 1175,
    X86_KXORQrr	= 1176,
    X86_KXORWrr	= 1177,
    X86_LAHF	= 1178,
    X86_LAR16rm	= 1179,
    X86_LAR16rr	= 1180,
    X86_LAR32rm	= 1181,
    X86_LAR32rr	= 1182,
    X86_LAR64rm	= 1183,
    X86_LAR64rr	= 1184,
    X86_LCMPXCHG16	= 1185,
    X86_LCMPXCHG16B	= 1186,
    X86_LCMPXCHG32	= 1187,
    X86_LCMPXCHG64	= 1188,
    X86_LCMPXCHG8	= 1189,
    X86_LCMPXCHG8B	= 1190,
    X86_LDDQUrm	= 1191,
    X86_LDMXCSR	= 1192,
    X86_LDS16rm	= 1193,
    X86_LDS32rm	= 1194,
    X86_LD_F0	= 1195,
    X86_LD_F1	= 1196,
    X86_LD_F32m	= 1197,
    X86_LD_F64m	= 1198,
    X86_LD_F80m	= 1199,
    X86_LD_Fp032	= 1200,
    X86_LD_Fp064	= 1201,
    X86_LD_Fp080	= 1202,
    X86_LD_Fp132	= 1203,
    X86_LD_Fp164	= 1204,
    X86_LD_Fp180	= 1205,
    X86_LD_Fp32m	= 1206,
    X86_LD_Fp32m64	= 1207,
    X86_LD_Fp32m80	= 1208,
    X86_LD_Fp64m	= 1209,
    X86_LD_Fp64m80	= 1210,
    X86_LD_Fp80m	= 1211,
    X86_LD_Frr	= 1212,
    X86_LEA16r	= 1213,
    X86_LEA32r	= 1214,
    X86_LEA64_32r	= 1215,
    X86_LEA64r	= 1216,
    X86_LEAVE	= 1217,
    X86_LEAVE64	= 1218,
    X86_LES16rm	= 1219,
    X86_LES32rm	= 1220,
    X86_LFENCE	= 1221,
    X86_LFS16rm	= 1222,
    X86_LFS32rm	= 1223,
    X86_LFS64rm	= 1224,
    X86_LGDT16m	= 1225,
    X86_LGDT32m	= 1226,
    X86_LGDT64m	= 1227,
    X86_LGS16rm	= 1228,
    X86_LGS32rm	= 1229,
    X86_LGS64rm	= 1230,
    X86_LIDT16m	= 1231,
    X86_LIDT32m	= 1232,
    X86_LIDT64m	= 1233,
    X86_LLDT16m	= 1234,
    X86_LLDT16r	= 1235,
    X86_LMSW16m	= 1236,
    X86_LMSW16r	= 1237,
    X86_LOCK_ADD16mi	= 1238,
    X86_LOCK_ADD16mi8	= 1239,
    X86_LOCK_ADD16mr	= 1240,
    X86_LOCK_ADD32mi	= 1241,
    X86_LOCK_ADD32mi8	= 1242,
    X86_LOCK_ADD32mr	= 1243,
    X86_LOCK_ADD64mi32	= 1244,
    X86_LOCK_ADD64mi8	= 1245,
    X86_LOCK_ADD64mr	= 1246,
    X86_LOCK_ADD8mi	= 1247,
    X86_LOCK_ADD8mr	= 1248,
    X86_LOCK_AND16mi	= 1249,
    X86_LOCK_AND16mi8	= 1250,
    X86_LOCK_AND16mr	= 1251,
    X86_LOCK_AND32mi	= 1252,
    X86_LOCK_AND32mi8	= 1253,
    X86_LOCK_AND32mr	= 1254,
    X86_LOCK_AND64mi32	= 1255,
    X86_LOCK_AND64mi8	= 1256,
    X86_LOCK_AND64mr	= 1257,
    X86_LOCK_AND8mi	= 1258,
    X86_LOCK_AND8mr	= 1259,
    X86_LOCK_DEC16m	= 1260,
    X86_LOCK_DEC32m	= 1261,
    X86_LOCK_DEC64m	= 1262,
    X86_LOCK_DEC8m	= 1263,
    X86_LOCK_INC16m	= 1264,
    X86_LOCK_INC32m	= 1265,
    X86_LOCK_INC64m	= 1266,
    X86_LOCK_INC8m	= 1267,
    X86_LOCK_OR16mi	= 1268,
    X86_LOCK_OR16mi8	= 1269,
    X86_LOCK_OR16mr	= 1270,
    X86_LOCK_OR32mi	= 1271,
    X86_LOCK_OR32mi8	= 1272,
    X86_LOCK_OR32mr	= 1273,
    X86_LOCK_OR64mi32	= 1274,
    X86_LOCK_OR64mi8	= 1275,
    X86_LOCK_OR64mr	= 1276,
    X86_LOCK_OR8mi	= 1277,
    X86_LOCK_OR8mr	= 1278,
    X86_LOCK_PREFIX	= 1279,
    X86_LOCK_SUB16mi	= 1280,
    X86_LOCK_SUB16mi8	= 1281,
    X86_LOCK_SUB16mr	= 1282,
    X86_LOCK_SUB32mi	= 1283,
    X86_LOCK_SUB32mi8	= 1284,
    X86_LOCK_SUB32mr	= 1285,
    X86_LOCK_SUB64mi32	= 1286,
    X86_LOCK_SUB64mi8	= 1287,
    X86_LOCK_SUB64mr	= 1288,
    X86_LOCK_SUB8mi	= 1289,
    X86_LOCK_SUB8mr	= 1290,
    X86_LOCK_XOR16mi	= 1291,
    X86_LOCK_XOR16mi8	= 1292,
    X86_LOCK_XOR16mr	= 1293,
    X86_LOCK_XOR32mi	= 1294,
    X86_LOCK_XOR32mi8	= 1295,
    X86_LOCK_XOR32mr	= 1296,
    X86_LOCK_XOR64mi32	= 1297,
    X86_LOCK_XOR64mi8	= 1298,
    X86_LOCK_XOR64mr	= 1299,
    X86_LOCK_XOR8mi	= 1300,
    X86_LOCK_XOR8mr	= 1301,
    X86_LODSB	= 1302,
    X86_LODSL	= 1303,
    X86_LODSQ	= 1304,
    X86_LODSW	= 1305,
    X86_LOOP	= 1306,
    X86_LOOPE	= 1307,
    X86_LOOPNE	= 1308,
    X86_LRETIL	= 1309,
    X86_LRETIQ	= 1310,
    X86_LRETIW	= 1311,
    X86_LRETL	= 1312,
    X86_LRETQ	= 1313,
    X86_LRETW	= 1314,
    X86_LSL16rm	= 1315,
    X86_LSL16rr	= 1316,
    X86_LSL32rm	= 1317,
    X86_LSL32rr	= 1318,
    X86_LSL64rm	= 1319,
    X86_LSL64rr	= 1320,
    X86_LSS16rm	= 1321,
    X86_LSS32rm	= 1322,
    X86_LSS64rm	= 1323,
    X86_LTRm	= 1324,
    X86_LTRr	= 1325,
    X86_LXADD16	= 1326,
    X86_LXADD32	= 1327,
    X86_LXADD64	= 1328,
    X86_LXADD8	= 1329,
    X86_LZCNT16rm	= 1330,
    X86_LZCNT16rr	= 1331,
    X86_LZCNT32rm	= 1332,
    X86_LZCNT32rr	= 1333,
    X86_LZCNT64rm	= 1334,
    X86_LZCNT64rr	= 1335,
    X86_MASKMOVDQU	= 1336,
    X86_MASKMOVDQU64	= 1337,
    X86_MAXCPDrm	= 1338,
    X86_MAXCPDrr	= 1339,
    X86_MAXCPSrm	= 1340,
    X86_MAXCPSrr	= 1341,
    X86_MAXCSDrm	= 1342,
    X86_MAXCSDrr	= 1343,
    X86_MAXCSSrm	= 1344,
    X86_MAXCSSrr	= 1345,
    X86_MAXPDrm	= 1346,
    X86_MAXPDrr	= 1347,
    X86_MAXPSrm	= 1348,
    X86_MAXPSrr	= 1349,
    X86_MAXSDrm	= 1350,
    X86_MAXSDrm_Int	= 1351,
    X86_MAXSDrr	= 1352,
    X86_MAXSDrr_Int	= 1353,
    X86_MAXSSrm	= 1354,
    X86_MAXSSrm_Int	= 1355,
    X86_MAXSSrr	= 1356,
    X86_MAXSSrr_Int	= 1357,
    X86_MFENCE	= 1358,
    X86_MINCPDrm	= 1359,
    X86_MINCPDrr	= 1360,
    X86_MINCPSrm	= 1361,
    X86_MINCPSrr	= 1362,
    X86_MINCSDrm	= 1363,
    X86_MINCSDrr	= 1364,
    X86_MINCSSrm	= 1365,
    X86_MINCSSrr	= 1366,
    X86_MINPDrm	= 1367,
    X86_MINPDrr	= 1368,
    X86_MINPSrm	= 1369,
    X86_MINPSrr	= 1370,
    X86_MINSDrm	= 1371,
    X86_MINSDrm_Int	= 1372,
    X86_MINSDrr	= 1373,
    X86_MINSDrr_Int	= 1374,
    X86_MINSSrm	= 1375,
    X86_MINSSrm_Int	= 1376,
    X86_MINSSrr	= 1377,
    X86_MINSSrr_Int	= 1378,
    X86_MMX_CVTPD2PIirm	= 1379,
    X86_MMX_CVTPD2PIirr	= 1380,
    X86_MMX_CVTPI2PDirm	= 1381,
    X86_MMX_CVTPI2PDirr	= 1382,
    X86_MMX_CVTPI2PSirm	= 1383,
    X86_MMX_CVTPI2PSirr	= 1384,
    X86_MMX_CVTPS2PIirm	= 1385,
    X86_MMX_CVTPS2PIirr	= 1386,
    X86_MMX_CVTTPD2PIirm	= 1387,
    X86_MMX_CVTTPD2PIirr	= 1388,
    X86_MMX_CVTTPS2PIirm	= 1389,
    X86_MMX_CVTTPS2PIirr	= 1390,
    X86_MMX_EMMS	= 1391,
    X86_MMX_MASKMOVQ	= 1392,
    X86_MMX_MASKMOVQ64	= 1393,
    X86_MMX_MOVD64from64rr	= 1394,
    X86_MMX_MOVD64grr	= 1395,
    X86_MMX_MOVD64mr	= 1396,
    X86_MMX_MOVD64rm	= 1397,
    X86_MMX_MOVD64rr	= 1398,
    X86_MMX_MOVD64to64rr	= 1399,
    X86_MMX_MOVDQ2Qrr	= 1400,
    X86_MMX_MOVFR642Qrr	= 1401,
    X86_MMX_MOVNTQmr	= 1402,
    X86_MMX_MOVQ2DQrr	= 1403,
    X86_MMX_MOVQ2FR64rr	= 1404,
    X86_MMX_MOVQ64mr	= 1405,
    X86_MMX_MOVQ64rm	= 1406,
    X86_MMX_MOVQ64rr	= 1407,
    X86_MMX_MOVQ64rr_REV	= 1408,
    X86_MMX_PABSBrm64	= 1409,
    X86_MMX_PABSBrr64	= 1410,
    X86_MMX_PABSDrm64	= 1411,
    X86_MMX_PABSDrr64	= 1412,
    X86_MMX_PABSWrm64	= 1413,
    X86_MMX_PABSWrr64	= 1414,
    X86_MMX_PACKSSDWirm	= 1415,
    X86_MMX_PACKSSDWirr	= 1416,
    X86_MMX_PACKSSWBirm	= 1417,
    X86_MMX_PACKSSWBirr	= 1418,
    X86_MMX_PACKUSWBirm	= 1419,
    X86_MMX_PACKUSWBirr	= 1420,
    X86_MMX_PADDBirm	= 1421,
    X86_MMX_PADDBirr	= 1422,
    X86_MMX_PADDDirm	= 1423,
    X86_MMX_PADDDirr	= 1424,
    X86_MMX_PADDQirm	= 1425,
    X86_MMX_PADDQirr	= 1426,
    X86_MMX_PADDSBirm	= 1427,
    X86_MMX_PADDSBirr	= 1428,
    X86_MMX_PADDSWirm	= 1429,
    X86_MMX_PADDSWirr	= 1430,
    X86_MMX_PADDUSBirm	= 1431,
    X86_MMX_PADDUSBirr	= 1432,
    X86_MMX_PADDUSWirm	= 1433,
    X86_MMX_PADDUSWirr	= 1434,
    X86_MMX_PADDWirm	= 1435,
    X86_MMX_PADDWirr	= 1436,
    X86_MMX_PALIGNR64irm	= 1437,
    X86_MMX_PALIGNR64irr	= 1438,
    X86_MMX_PANDNirm	= 1439,
    X86_MMX_PANDNirr	= 1440,
    X86_MMX_PANDirm	= 1441,
    X86_MMX_PANDirr	= 1442,
    X86_MMX_PAVGBirm	= 1443,
    X86_MMX_PAVGBirr	= 1444,
    X86_MMX_PAVGWirm	= 1445,
    X86_MMX_PAVGWirr	= 1446,
    X86_MMX_PCMPEQBirm	= 1447,
    X86_MMX_PCMPEQBirr	= 1448,
    X86_MMX_PCMPEQDirm	= 1449,
    X86_MMX_PCMPEQDirr	= 1450,
    X86_MMX_PCMPEQWirm	= 1451,
    X86_MMX_PCMPEQWirr	= 1452,
    X86_MMX_PCMPGTBirm	= 1453,
    X86_MMX_PCMPGTBirr	= 1454,
    X86_MMX_PCMPGTDirm	= 1455,
    X86_MMX_PCMPGTDirr	= 1456,
    X86_MMX_PCMPGTWirm	= 1457,
    X86_MMX_PCMPGTWirr	= 1458,
    X86_MMX_PEXTRWirri	= 1459,
    X86_MMX_PHADDSWrm64	= 1460,
    X86_MMX_PHADDSWrr64	= 1461,
    X86_MMX_PHADDWrm64	= 1462,
    X86_MMX_PHADDWrr64	= 1463,
    X86_MMX_PHADDrm64	= 1464,
    X86_MMX_PHADDrr64	= 1465,
    X86_MMX_PHSUBDrm64	= 1466,
    X86_MMX_PHSUBDrr64	= 1467,
    X86_MMX_PHSUBSWrm64	= 1468,
    X86_MMX_PHSUBSWrr64	= 1469,
    X86_MMX_PHSUBWrm64	= 1470,
    X86_MMX_PHSUBWrr64	= 1471,
    X86_MMX_PINSRWirmi	= 1472,
    X86_MMX_PINSRWirri	= 1473,
    X86_MMX_PMADDUBSWrm64	= 1474,
    X86_MMX_PMADDUBSWrr64	= 1475,
    X86_MMX_PMADDWDirm	= 1476,
    X86_MMX_PMADDWDirr	= 1477,
    X86_MMX_PMAXSWirm	= 1478,
    X86_MMX_PMAXSWirr	= 1479,
    X86_MMX_PMAXUBirm	= 1480,
    X86_MMX_PMAXUBirr	= 1481,
    X86_MMX_PMINSWirm	= 1482,
    X86_MMX_PMINSWirr	= 1483,
    X86_MMX_PMINUBirm	= 1484,
    X86_MMX_PMINUBirr	= 1485,
    X86_MMX_PMOVMSKBrr	= 1486,
    X86_MMX_PMULHRSWrm64	= 1487,
    X86_MMX_PMULHRSWrr64	= 1488,
    X86_MMX_PMULHUWirm	= 1489,
    X86_MMX_PMULHUWirr	= 1490,
    X86_MMX_PMULHWirm	= 1491,
    X86_MMX_PMULHWirr	= 1492,
    X86_MMX_PMULLWirm	= 1493,
    X86_MMX_PMULLWirr	= 1494,
    X86_MMX_PMULUDQirm	= 1495,
    X86_MMX_PMULUDQirr	= 1496,
    X86_MMX_PORirm	= 1497,
    X86_MMX_PORirr	= 1498,
    X86_MMX_PSADBWirm	= 1499,
    X86_MMX_PSADBWirr	= 1500,
    X86_MMX_PSHUFBrm64	= 1501,
    X86_MMX_PSHUFBrr64	= 1502,
    X86_MMX_PSHUFWmi	= 1503,
    X86_MMX_PSHUFWri	= 1504,
    X86_MMX_PSIGNBrm64	= 1505,
    X86_MMX_PSIGNBrr64	= 1506,
    X86_MMX_PSIGNDrm64	= 1507,
    X86_MMX_PSIGNDrr64	= 1508,
    X86_MMX_PSIGNWrm64	= 1509,
    X86_MMX_PSIGNWrr64	= 1510,
    X86_MMX_PSLLDri	= 1511,
    X86_MMX_PSLLDrm	= 1512,
    X86_MMX_PSLLDrr	= 1513,
    X86_MMX_PSLLQri	= 1514,
    X86_MMX_PSLLQrm	= 1515,
    X86_MMX_PSLLQrr	= 1516,
    X86_MMX_PSLLWri	= 1517,
    X86_MMX_PSLLWrm	= 1518,
    X86_MMX_PSLLWrr	= 1519,
    X86_MMX_PSRADri	= 1520,
    X86_MMX_PSRADrm	= 1521,
    X86_MMX_PSRADrr	= 1522,
    X86_MMX_PSRAWri	= 1523,
    X86_MMX_PSRAWrm	= 1524,
    X86_MMX_PSRAWrr	= 1525,
    X86_MMX_PSRLDri	= 1526,
    X86_MMX_PSRLDrm	= 1527,
    X86_MMX_PSRLDrr	= 1528,
    X86_MMX_PSRLQri	= 1529,
    X86_MMX_PSRLQrm	= 1530,
    X86_MMX_PSRLQrr	= 1531,
    X86_MMX_PSRLWri	= 1532,
    X86_MMX_PSRLWrm	= 1533,
    X86_MMX_PSRLWrr	= 1534,
    X86_MMX_PSUBBirm	= 1535,
    X86_MMX_PSUBBirr	= 1536,
    X86_MMX_PSUBDirm	= 1537,
    X86_MMX_PSUBDirr	= 1538,
    X86_MMX_PSUBQirm	= 1539,
    X86_MMX_PSUBQirr	= 1540,
    X86_MMX_PSUBSBirm	= 1541,
    X86_MMX_PSUBSBirr	= 1542,
    X86_MMX_PSUBSWirm	= 1543,
    X86_MMX_PSUBSWirr	= 1544,
    X86_MMX_PSUBUSBirm	= 1545,
    X86_MMX_PSUBUSBirr	= 1546,
    X86_MMX_PSUBUSWirm	= 1547,
    X86_MMX_PSUBUSWirr	= 1548,
    X86_MMX_PSUBWirm	= 1549,
    X86_MMX_PSUBWirr	= 1550,
    X86_MMX_PUNPCKHBWirm	= 1551,
    X86_MMX_PUNPCKHBWirr	= 1552,
    X86_MMX_PUNPCKHDQirm	= 1553,
    X86_MMX_PUNPCKHDQirr	= 1554,
    X86_MMX_PUNPCKHWDirm	= 1555,
    X86_MMX_PUNPCKHWDirr	= 1556,
    X86_MMX_PUNPCKLBWirm	= 1557,
    X86_MMX_PUNPCKLBWirr	= 1558,
    X86_MMX_PUNPCKLDQirm	= 1559,
    X86_MMX_PUNPCKLDQirr	= 1560,
    X86_MMX_PUNPCKLWDirm	= 1561,
    X86_MMX_PUNPCKLWDirr	= 1562,
    X86_MMX_PXORirm	= 1563,
    X86_MMX_PXORirr	= 1564,
    X86_MONITOR	= 1565,
    X86_MONITORrrr	= 1566,
    X86_MONTMUL	= 1567,
    X86_MORESTACK_RET	= 1568,
    X86_MORESTACK_RET_RESTORE_R10	= 1569,
    X86_MOV16ao16	= 1570,
    X86_MOV16ao16_16	= 1571,
    X86_MOV16mi	= 1572,
    X86_MOV16mr	= 1573,
    X86_MOV16ms	= 1574,
    X86_MOV16o16a	= 1575,
    X86_MOV16o16a_16	= 1576,
    X86_MOV16ri	= 1577,
    X86_MOV16ri_alt	= 1578,
    X86_MOV16rm	= 1579,
    X86_MOV16rr	= 1580,
    X86_MOV16rr_REV	= 1581,
    X86_MOV16rs	= 1582,
    X86_MOV16sm	= 1583,
    X86_MOV16sr	= 1584,
    X86_MOV32ao32	= 1585,
    X86_MOV32ao32_16	= 1586,
    X86_MOV32cr	= 1587,
    X86_MOV32dr	= 1588,
    X86_MOV32mi	= 1589,
    X86_MOV32mr	= 1590,
    X86_MOV32ms	= 1591,
    X86_MOV32o32a	= 1592,
    X86_MOV32o32a_16	= 1593,
    X86_MOV32r0	= 1594,
    X86_MOV32rc	= 1595,
    X86_MOV32rd	= 1596,
    X86_MOV32ri	= 1597,
    X86_MOV32ri64	= 1598,
    X86_MOV32ri_alt	= 1599,
    X86_MOV32rm	= 1600,
    X86_MOV32rr	= 1601,
    X86_MOV32rr_REV	= 1602,
    X86_MOV32rs	= 1603,
    X86_MOV32sm	= 1604,
    X86_MOV32sr	= 1605,
    X86_MOV64ao16	= 1606,
    X86_MOV64ao32	= 1607,
    X86_MOV64ao64	= 1608,
    X86_MOV64ao8	= 1609,
    X86_MOV64cr	= 1610,
    X86_MOV64dr	= 1611,
    X86_MOV64mi32	= 1612,
    X86_MOV64mr	= 1613,
    X86_MOV64ms	= 1614,
    X86_MOV64o16a	= 1615,
    X86_MOV64o32a	= 1616,
    X86_MOV64o64a	= 1617,
    X86_MOV64o8a	= 1618,
    X86_MOV64rc	= 1619,
    X86_MOV64rd	= 1620,
    X86_MOV64ri	= 1621,
    X86_MOV64ri32	= 1622,
    X86_MOV64rm	= 1623,
    X86_MOV64rr	= 1624,
    X86_MOV64rr_REV	= 1625,
    X86_MOV64rs	= 1626,
    X86_MOV64sm	= 1627,
    X86_MOV64sr	= 1628,
    X86_MOV64toPQIrr	= 1629,
    X86_MOV64toSDrm	= 1630,
    X86_MOV64toSDrr	= 1631,
    X86_MOV8ao8	= 1632,
    X86_MOV8ao8_16	= 1633,
    X86_MOV8mi	= 1634,
    X86_MOV8mr	= 1635,
    X86_MOV8mr_NOREX	= 1636,
    X86_MOV8o8a	= 1637,
    X86_MOV8o8a_16	= 1638,
    X86_MOV8ri	= 1639,
    X86_MOV8ri_alt	= 1640,
    X86_MOV8rm	= 1641,
    X86_MOV8rm_NOREX	= 1642,
    X86_MOV8rr	= 1643,
    X86_MOV8rr_NOREX	= 1644,
    X86_MOV8rr_REV	= 1645,
    X86_MOVAPDmr	= 1646,
    X86_MOVAPDrm	= 1647,
    X86_MOVAPDrr	= 1648,
    X86_MOVAPDrr_REV	= 1649,
    X86_MOVAPSmr	= 1650,
    X86_MOVAPSrm	= 1651,
    X86_MOVAPSrr	= 1652,
    X86_MOVAPSrr_REV	= 1653,
    X86_MOVBE16mr	= 1654,
    X86_MOVBE16rm	= 1655,
    X86_MOVBE32mr	= 1656,
    X86_MOVBE32rm	= 1657,
    X86_MOVBE64mr	= 1658,
    X86_MOVBE64rm	= 1659,
    X86_MOVDDUPrm	= 1660,
    X86_MOVDDUPrr	= 1661,
    X86_MOVDI2PDIrm	= 1662,
    X86_MOVDI2PDIrr	= 1663,
    X86_MOVDI2SSrm	= 1664,
    X86_MOVDI2SSrr	= 1665,
    X86_MOVDQAmr	= 1666,
    X86_MOVDQArm	= 1667,
    X86_MOVDQArr	= 1668,
    X86_MOVDQArr_REV	= 1669,
    X86_MOVDQUmr	= 1670,
    X86_MOVDQUrm	= 1671,
    X86_MOVDQUrr	= 1672,
    X86_MOVDQUrr_REV	= 1673,
    X86_MOVHLPSrr	= 1674,
    X86_MOVHPDmr	= 1675,
    X86_MOVHPDrm	= 1676,
    X86_MOVHPSmr	= 1677,
    X86_MOVHPSrm	= 1678,
    X86_MOVLHPSrr	= 1679,
    X86_MOVLPDmr	= 1680,
    X86_MOVLPDrm	= 1681,
    X86_MOVLPSmr	= 1682,
    X86_MOVLPSrm	= 1683,
    X86_MOVMSKPDrr	= 1684,
    X86_MOVMSKPSrr	= 1685,
    X86_MOVNTDQArm	= 1686,
    X86_MOVNTDQmr	= 1687,
    X86_MOVNTI_64mr	= 1688,
    X86_MOVNTImr	= 1689,
    X86_MOVNTPDmr	= 1690,
    X86_MOVNTPSmr	= 1691,
    X86_MOVNTSD	= 1692,
    X86_MOVNTSS	= 1693,
    X86_MOVPC32r	= 1694,
    X86_MOVPDI2DImr	= 1695,
    X86_MOVPDI2DIrr	= 1696,
    X86_MOVPQI2QImr	= 1697,
    X86_MOVPQI2QIrr	= 1698,
    X86_MOVPQIto64rr	= 1699,
    X86_MOVQI2PQIrm	= 1700,
    X86_MOVSB	= 1701,
    X86_MOVSDmr	= 1702,
    X86_MOVSDrm	= 1703,
    X86_MOVSDrr	= 1704,
    X86_MOVSDrr_REV	= 1705,
    X86_MOVSDto64mr	= 1706,
    X86_MOVSDto64rr	= 1707,
    X86_MOVSHDUPrm	= 1708,
    X86_MOVSHDUPrr	= 1709,
    X86_MOVSL	= 1710,
    X86_MOVSLDUPrm	= 1711,
    X86_MOVSLDUPrr	= 1712,
    X86_MOVSQ	= 1713,
    X86_MOVSS2DImr	= 1714,
    X86_MOVSS2DIrr	= 1715,
    X86_MOVSSmr	= 1716,
    X86_MOVSSrm	= 1717,
    X86_MOVSSrr	= 1718,
    X86_MOVSSrr_REV	= 1719,
    X86_MOVSW	= 1720,
    X86_MOVSX16rm8	= 1721,
    X86_MOVSX16rr8	= 1722,
    X86_MOVSX32rm16	= 1723,
    X86_MOVSX32rm8	= 1724,
    X86_MOVSX32rr16	= 1725,
    X86_MOVSX32rr8	= 1726,
    X86_MOVSX64_NOREXrr32	= 1727,
    X86_MOVSX64rm16	= 1728,
    X86_MOVSX64rm32	= 1729,
    X86_MOVSX64rm8	= 1730,
    X86_MOVSX64rr16	= 1731,
    X86_MOVSX64rr32	= 1732,
    X86_MOVSX64rr8	= 1733,
    X86_MOVUPDmr	= 1734,
    X86_MOVUPDrm	= 1735,
    X86_MOVUPDrr	= 1736,
    X86_MOVUPDrr_REV	= 1737,
    X86_MOVUPSmr	= 1738,
    X86_MOVUPSrm	= 1739,
    X86_MOVUPSrr	= 1740,
    X86_MOVUPSrr_REV	= 1741,
    X86_MOVZPQILo2PQIrm	= 1742,
    X86_MOVZPQILo2PQIrr	= 1743,
    X86_MOVZQI2PQIrm	= 1744,
    X86_MOVZQI2PQIrr	= 1745,
    X86_MOVZX16rm8	= 1746,
    X86_MOVZX16rr8	= 1747,
    X86_MOVZX32_NOREXrm8	= 1748,
    X86_MOVZX32_NOREXrr8	= 1749,
    X86_MOVZX32rm16	= 1750,
    X86_MOVZX32rm8	= 1751,
    X86_MOVZX32rr16	= 1752,
    X86_MOVZX32rr8	= 1753,
    X86_MOVZX64rm16_Q	= 1754,
    X86_MOVZX64rm8_Q	= 1755,
    X86_MOVZX64rr16_Q	= 1756,
    X86_MOVZX64rr8_Q	= 1757,
    X86_MPSADBWrmi	= 1758,
    X86_MPSADBWrri	= 1759,
    X86_MUL16m	= 1760,
    X86_MUL16r	= 1761,
    X86_MUL32m	= 1762,
    X86_MUL32r	= 1763,
    X86_MUL64m	= 1764,
    X86_MUL64r	= 1765,
    X86_MUL8m	= 1766,
    X86_MUL8r	= 1767,
    X86_MULPDrm	= 1768,
    X86_MULPDrr	= 1769,
    X86_MULPSrm	= 1770,
    X86_MULPSrr	= 1771,
    X86_MULSDrm	= 1772,
    X86_MULSDrm_Int	= 1773,
    X86_MULSDrr	= 1774,
    X86_MULSDrr_Int	= 1775,
    X86_MULSSrm	= 1776,
    X86_MULSSrm_Int	= 1777,
    X86_MULSSrr	= 1778,
    X86_MULSSrr_Int	= 1779,
    X86_MULX32rm	= 1780,
    X86_MULX32rr	= 1781,
    X86_MULX64rm	= 1782,
    X86_MULX64rr	= 1783,
    X86_MUL_F32m	= 1784,
    X86_MUL_F64m	= 1785,
    X86_MUL_FI16m	= 1786,
    X86_MUL_FI32m	= 1787,
    X86_MUL_FPrST0	= 1788,
    X86_MUL_FST0r	= 1789,
    X86_MUL_Fp32	= 1790,
    X86_MUL_Fp32m	= 1791,
    X86_MUL_Fp64	= 1792,
    X86_MUL_Fp64m	= 1793,
    X86_MUL_Fp64m32	= 1794,
    X86_MUL_Fp80	= 1795,
    X86_MUL_Fp80m32	= 1796,
    X86_MUL_Fp80m64	= 1797,
    X86_MUL_FpI16m32	= 1798,
    X86_MUL_FpI16m64	= 1799,
    X86_MUL_FpI16m80	= 1800,
    X86_MUL_FpI32m32	= 1801,
    X86_MUL_FpI32m64	= 1802,
    X86_MUL_FpI32m80	= 1803,
    X86_MUL_FrST0	= 1804,
    X86_MWAITrr	= 1805,
    X86_NEG16m	= 1806,
    X86_NEG16r	= 1807,
    X86_NEG32m	= 1808,
    X86_NEG32r	= 1809,
    X86_NEG64m	= 1810,
    X86_NEG64r	= 1811,
    X86_NEG8m	= 1812,
    X86_NEG8r	= 1813,
    X86_NOOP	= 1814,
    X86_NOOP18_16m4	= 1815,
    X86_NOOP18_16m5	= 1816,
    X86_NOOP18_16m6	= 1817,
    X86_NOOP18_16m7	= 1818,
    X86_NOOP18_16r4	= 1819,
    X86_NOOP18_16r5	= 1820,
    X86_NOOP18_16r6	= 1821,
    X86_NOOP18_16r7	= 1822,
    X86_NOOP18_m4	= 1823,
    X86_NOOP18_m5	= 1824,
    X86_NOOP18_m6	= 1825,
    X86_NOOP18_m7	= 1826,
    X86_NOOP18_r4	= 1827,
    X86_NOOP18_r5	= 1828,
    X86_NOOP18_r6	= 1829,
    X86_NOOP18_r7	= 1830,
    X86_NOOP19rr	= 1831,
    X86_NOOPL	= 1832,
    X86_NOOPL_19	= 1833,
    X86_NOOPL_1a	= 1834,
    X86_NOOPL_1b	= 1835,
    X86_NOOPL_1c	= 1836,
    X86_NOOPL_1d	= 1837,
    X86_NOOPL_1e	= 1838,
    X86_NOOPW	= 1839,
    X86_NOOPW_19	= 1840,
    X86_NOOPW_1a	= 1841,
    X86_NOOPW_1b	= 1842,
    X86_NOOPW_1c	= 1843,
    X86_NOOPW_1d	= 1844,
    X86_NOOPW_1e	= 1845,
    X86_NOT16m	= 1846,
    X86_NOT16r	= 1847,
    X86_NOT32m	= 1848,
    X86_NOT32r	= 1849,
    X86_NOT64m	= 1850,
    X86_NOT64r	= 1851,
    X86_NOT8m	= 1852,
    X86_NOT8r	= 1853,
    X86_OR16i16	= 1854,
    X86_OR16mi	= 1855,
    X86_OR16mi8	= 1856,
    X86_OR16mr	= 1857,
    X86_OR16ri	= 1858,
    X86_OR16ri8	= 1859,
    X86_OR16rm	= 1860,
    X86_OR16rr	= 1861,
    X86_OR16rr_REV	= 1862,
    X86_OR32i32	= 1863,
    X86_OR32mi	= 1864,
    X86_OR32mi8	= 1865,
    X86_OR32mr	= 1866,
    X86_OR32mrLocked	= 1867,
    X86_OR32ri	= 1868,
    X86_OR32ri8	= 1869,
    X86_OR32rm	= 1870,
    X86_OR32rr	= 1871,
    X86_OR32rr_REV	= 1872,
    X86_OR64i32	= 1873,
    X86_OR64mi32	= 1874,
    X86_OR64mi8	= 1875,
    X86_OR64mr	= 1876,
    X86_OR64ri32	= 1877,
    X86_OR64ri8	= 1878,
    X86_OR64rm	= 1879,
    X86_OR64rr	= 1880,
    X86_OR64rr_REV	= 1881,
    X86_OR8i8	= 1882,
    X86_OR8mi	= 1883,
    X86_OR8mr	= 1884,
    X86_OR8ri	= 1885,
    X86_OR8ri8	= 1886,
    X86_OR8rm	= 1887,
    X86_OR8rr	= 1888,
    X86_OR8rr_REV	= 1889,
    X86_ORPDrm	= 1890,
    X86_ORPDrr	= 1891,
    X86_ORPSrm	= 1892,
    X86_ORPSrr	= 1893,
    X86_OUT16ir	= 1894,
    X86_OUT16rr	= 1895,
    X86_OUT32ir	= 1896,
    X86_OUT32rr	= 1897,
    X86_OUT8ir	= 1898,
    X86_OUT8rr	= 1899,
    X86_OUTSB	= 1900,
    X86_OUTSL	= 1901,
    X86_OUTSW	= 1902,
    X86_PABSBrm128	= 1903,
    X86_PABSBrr128	= 1904,
    X86_PABSDrm128	= 1905,
    X86_PABSDrr128	= 1906,
    X86_PABSWrm128	= 1907,
    X86_PABSWrr128	= 1908,
    X86_PACKSSDWrm	= 1909,
    X86_PACKSSDWrr	= 1910,
    X86_PACKSSWBrm	= 1911,
    X86_PACKSSWBrr	= 1912,
    X86_PACKUSDWrm	= 1913,
    X86_PACKUSDWrr	= 1914,
    X86_PACKUSWBrm	= 1915,
    X86_PACKUSWBrr	= 1916,
    X86_PADDBrm	= 1917,
    X86_PADDBrr	= 1918,
    X86_PADDDrm	= 1919,
    X86_PADDDrr	= 1920,
    X86_PADDQrm	= 1921,
    X86_PADDQrr	= 1922,
    X86_PADDSBrm	= 1923,
    X86_PADDSBrr	= 1924,
    X86_PADDSWrm	= 1925,
    X86_PADDSWrr	= 1926,
    X86_PADDUSBrm	= 1927,
    X86_PADDUSBrr	= 1928,
    X86_PADDUSWrm	= 1929,
    X86_PADDUSWrr	= 1930,
    X86_PADDWrm	= 1931,
    X86_PADDWrr	= 1932,
    X86_PALIGNR128rm	= 1933,
    X86_PALIGNR128rr	= 1934,
    X86_PANDNrm	= 1935,
    X86_PANDNrr	= 1936,
    X86_PANDrm	= 1937,
    X86_PANDrr	= 1938,
    X86_PAUSE	= 1939,
    X86_PAVGBrm	= 1940,
    X86_PAVGBrr	= 1941,
    X86_PAVGUSBrm	= 1942,
    X86_PAVGUSBrr	= 1943,
    X86_PAVGWrm	= 1944,
    X86_PAVGWrr	= 1945,
    X86_PBLENDVBrm0	= 1946,
    X86_PBLENDVBrr0	= 1947,
    X86_PBLENDWrmi	= 1948,
    X86_PBLENDWrri	= 1949,
    X86_PCLMULQDQrm	= 1950,
    X86_PCLMULQDQrr	= 1951,
    X86_PCMPEQBrm	= 1952,
    X86_PCMPEQBrr	= 1953,
    X86_PCMPEQDrm	= 1954,
    X86_PCMPEQDrr	= 1955,
    X86_PCMPEQQrm	= 1956,
    X86_PCMPEQQrr	= 1957,
    X86_PCMPEQWrm	= 1958,
    X86_PCMPEQWrr	= 1959,
    X86_PCMPESTRIMEM	= 1960,
    X86_PCMPESTRIREG	= 1961,
    X86_PCMPESTRIrm	= 1962,
    X86_PCMPESTRIrr	= 1963,
    X86_PCMPESTRM128MEM	= 1964,
    X86_PCMPESTRM128REG	= 1965,
    X86_PCMPESTRM128rm	= 1966,
    X86_PCMPESTRM128rr	= 1967,
    X86_PCMPGTBrm	= 1968,
    X86_PCMPGTBrr	= 1969,
    X86_PCMPGTDrm	= 1970,
    X86_PCMPGTDrr	= 1971,
    X86_PCMPGTQrm	= 1972,
    X86_PCMPGTQrr	= 1973,
    X86_PCMPGTWrm	= 1974,
    X86_PCMPGTWrr	= 1975,
    X86_PCMPISTRIMEM	= 1976,
    X86_PCMPISTRIREG	= 1977,
    X86_PCMPISTRIrm	= 1978,
    X86_PCMPISTRIrr	= 1979,
    X86_PCMPISTRM128MEM	= 1980,
    X86_PCMPISTRM128REG	= 1981,
    X86_PCMPISTRM128rm	= 1982,
    X86_PCMPISTRM128rr	= 1983,
    X86_PDEP32rm	= 1984,
    X86_PDEP32rr	= 1985,
    X86_PDEP64rm	= 1986,
    X86_PDEP64rr	= 1987,
    X86_PEXT32rm	= 1988,
    X86_PEXT32rr	= 1989,
    X86_PEXT64rm	= 1990,
    X86_PEXT64rr	= 1991,
    X86_PEXTRBmr	= 1992,
    X86_PEXTRBrr	= 1993,
    X86_PEXTRDmr	= 1994,
    X86_PEXTRDrr	= 1995,
    X86_PEXTRQmr	= 1996,
    X86_PEXTRQrr	= 1997,
    X86_PEXTRWmr	= 1998,
    X86_PEXTRWri	= 1999,
    X86_PEXTRWrr_REV	= 2000,
    X86_PF2IDrm	= 2001,
    X86_PF2IDrr	= 2002,
    X86_PF2IWrm	= 2003,
    X86_PF2IWrr	= 2004,
    X86_PFACCrm	= 2005,
    X86_PFACCrr	= 2006,
    X86_PFADDrm	= 2007,
    X86_PFADDrr	= 2008,
    X86_PFCMPEQrm	= 2009,
    X86_PFCMPEQrr	= 2010,
    X86_PFCMPGErm	= 2011,
    X86_PFCMPGErr	= 2012,
    X86_PFCMPGTrm	= 2013,
    X86_PFCMPGTrr	= 2014,
    X86_PFMAXrm	= 2015,
    X86_PFMAXrr	= 2016,
    X86_PFMINrm	= 2017,
    X86_PFMINrr	= 2018,
    X86_PFMULrm	= 2019,
    X86_PFMULrr	= 2020,
    X86_PFNACCrm	= 2021,
    X86_PFNACCrr	= 2022,
    X86_PFPNACCrm	= 2023,
    X86_PFPNACCrr	= 2024,
    X86_PFRCPIT1rm	= 2025,
    X86_PFRCPIT1rr	= 2026,
    X86_PFRCPIT2rm	= 2027,
    X86_PFRCPIT2rr	= 2028,
    X86_PFRCPrm	= 2029,
    X86_PFRCPrr	= 2030,
    X86_PFRSQIT1rm	= 2031,
    X86_PFRSQIT1rr	= 2032,
    X86_PFRSQRTrm	= 2033,
    X86_PFRSQRTrr	= 2034,
    X86_PFSUBRrm	= 2035,
    X86_PFSUBRrr	= 2036,
    X86_PFSUBrm	= 2037,
    X86_PFSUBrr	= 2038,
    X86_PHADDDrm	= 2039,
    X86_PHADDDrr	= 2040,
    X86_PHADDSWrm128	= 2041,
    X86_PHADDSWrr128	= 2042,
    X86_PHADDWrm	= 2043,
    X86_PHADDWrr	= 2044,
    X86_PHMINPOSUWrm128	= 2045,
    X86_PHMINPOSUWrr128	= 2046,
    X86_PHSUBDrm	= 2047,
    X86_PHSUBDrr	= 2048,
    X86_PHSUBSWrm128	= 2049,
    X86_PHSUBSWrr128	= 2050,
    X86_PHSUBWrm	= 2051,
    X86_PHSUBWrr	= 2052,
    X86_PI2FDrm	= 2053,
    X86_PI2FDrr	= 2054,
    X86_PI2FWrm	= 2055,
    X86_PI2FWrr	= 2056,
    X86_PINSRBrm	= 2057,
    X86_PINSRBrr	= 2058,
    X86_PINSRDrm	= 2059,
    X86_PINSRDrr	= 2060,
    X86_PINSRQrm	= 2061,
    X86_PINSRQrr	= 2062,
    X86_PINSRWrmi	= 2063,
    X86_PINSRWrri	= 2064,
    X86_PMADDUBSWrm128	= 2065,
    X86_PMADDUBSWrr128	= 2066,
    X86_PMADDWDrm	= 2067,
    X86_PMADDWDrr	= 2068,
    X86_PMAXSBrm	= 2069,
    X86_PMAXSBrr	= 2070,
    X86_PMAXSDrm	= 2071,
    X86_PMAXSDrr	= 2072,
    X86_PMAXSWrm	= 2073,
    X86_PMAXSWrr	= 2074,
    X86_PMAXUBrm	= 2075,
    X86_PMAXUBrr	= 2076,
    X86_PMAXUDrm	= 2077,
    X86_PMAXUDrr	= 2078,
    X86_PMAXUWrm	= 2079,
    X86_PMAXUWrr	= 2080,
    X86_PMINSBrm	= 2081,
    X86_PMINSBrr	= 2082,
    X86_PMINSDrm	= 2083,
    X86_PMINSDrr	= 2084,
    X86_PMINSWrm	= 2085,
    X86_PMINSWrr	= 2086,
    X86_PMINUBrm	= 2087,
    X86_PMINUBrr	= 2088,
    X86_PMINUDrm	= 2089,
    X86_PMINUDrr	= 2090,
    X86_PMINUWrm	= 2091,
    X86_PMINUWrr	= 2092,
    X86_PMOVMSKBrr	= 2093,
    X86_PMOVSXBDrm	= 2094,
    X86_PMOVSXBDrr	= 2095,
    X86_PMOVSXBQrm	= 2096,
    X86_PMOVSXBQrr	= 2097,
    X86_PMOVSXBWrm	= 2098,
    X86_PMOVSXBWrr	= 2099,
    X86_PMOVSXDQrm	= 2100,
    X86_PMOVSXDQrr	= 2101,
    X86_PMOVSXWDrm	= 2102,
    X86_PMOVSXWDrr	= 2103,
    X86_PMOVSXWQrm	= 2104,
    X86_PMOVSXWQrr	= 2105,
    X86_PMOVZXBDrm	= 2106,
    X86_PMOVZXBDrr	= 2107,
    X86_PMOVZXBQrm	= 2108,
    X86_PMOVZXBQrr	= 2109,
    X86_PMOVZXBWrm	= 2110,
    X86_PMOVZXBWrr	= 2111,
    X86_PMOVZXDQrm	= 2112,
    X86_PMOVZXDQrr	= 2113,
    X86_PMOVZXWDrm	= 2114,
    X86_PMOVZXWDrr	= 2115,
    X86_PMOVZXWQrm	= 2116,
    X86_PMOVZXWQrr	= 2117,
    X86_PMULDQrm	= 2118,
    X86_PMULDQrr	= 2119,
    X86_PMULHRSWrm128	= 2120,
    X86_PMULHRSWrr128	= 2121,
    X86_PMULHRWrm	= 2122,
    X86_PMULHRWrr	= 2123,
    X86_PMULHUWrm	= 2124,
    X86_PMULHUWrr	= 2125,
    X86_PMULHWrm	= 2126,
    X86_PMULHWrr	= 2127,
    X86_PMULLDrm	= 2128,
    X86_PMULLDrr	= 2129,
    X86_PMULLWrm	= 2130,
    X86_PMULLWrr	= 2131,
    X86_PMULUDQrm	= 2132,
    X86_PMULUDQrr	= 2133,
    X86_POP16r	= 2134,
    X86_POP16rmm	= 2135,
    X86_POP16rmr	= 2136,
    X86_POP32r	= 2137,
    X86_POP32rmm	= 2138,
    X86_POP32rmr	= 2139,
    X86_POP64r	= 2140,
    X86_POP64rmm	= 2141,
    X86_POP64rmr	= 2142,
    X86_POPA16	= 2143,
    X86_POPA32	= 2144,
    X86_POPCNT16rm	= 2145,
    X86_POPCNT16rr	= 2146,
    X86_POPCNT32rm	= 2147,
    X86_POPCNT32rr	= 2148,
    X86_POPCNT64rm	= 2149,
    X86_POPCNT64rr	= 2150,
    X86_POPDS16	= 2151,
    X86_POPDS32	= 2152,
    X86_POPES16	= 2153,
    X86_POPES32	= 2154,
    X86_POPF16	= 2155,
    X86_POPF32	= 2156,
    X86_POPF64	= 2157,
    X86_POPFS16	= 2158,
    X86_POPFS32	= 2159,
    X86_POPFS64	= 2160,
    X86_POPGS16	= 2161,
    X86_POPGS32	= 2162,
    X86_POPGS64	= 2163,
    X86_POPSS16	= 2164,
    X86_POPSS32	= 2165,
    X86_PORrm	= 2166,
    X86_PORrr	= 2167,
    X86_PREFETCH	= 2168,
    X86_PREFETCHNTA	= 2169,
    X86_PREFETCHT0	= 2170,
    X86_PREFETCHT1	= 2171,
    X86_PREFETCHT2	= 2172,
    X86_PREFETCHW	= 2173,
    X86_PSADBWrm	= 2174,
    X86_PSADBWrr	= 2175,
    X86_PSHUFBrm	= 2176,
    X86_PSHUFBrr	= 2177,
    X86_PSHUFDmi	= 2178,
    X86_PSHUFDri	= 2179,
    X86_PSHUFHWmi	= 2180,
    X86_PSHUFHWri	= 2181,
    X86_PSHUFLWmi	= 2182,
    X86_PSHUFLWri	= 2183,
    X86_PSIGNBrm	= 2184,
    X86_PSIGNBrr	= 2185,
    X86_PSIGNDrm	= 2186,
    X86_PSIGNDrr	= 2187,
    X86_PSIGNWrm	= 2188,
    X86_PSIGNWrr	= 2189,
    X86_PSLLDQri	= 2190,
    X86_PSLLDri	= 2191,
    X86_PSLLDrm	= 2192,
    X86_PSLLDrr	= 2193,
    X86_PSLLQri	= 2194,
    X86_PSLLQrm	= 2195,
    X86_PSLLQrr	= 2196,
    X86_PSLLWri	= 2197,
    X86_PSLLWrm	= 2198,
    X86_PSLLWrr	= 2199,
    X86_PSRADri	= 2200,
    X86_PSRADrm	= 2201,
    X86_PSRADrr	= 2202,
    X86_PSRAWri	= 2203,
    X86_PSRAWrm	= 2204,
    X86_PSRAWrr	= 2205,
    X86_PSRLDQri	= 2206,
    X86_PSRLDri	= 2207,
    X86_PSRLDrm	= 2208,
    X86_PSRLDrr	= 2209,
    X86_PSRLQri	= 2210,
    X86_PSRLQrm	= 2211,
    X86_PSRLQrr	= 2212,
    X86_PSRLWri	= 2213,
    X86_PSRLWrm	= 2214,
    X86_PSRLWrr	= 2215,
    X86_PSUBBrm	= 2216,
    X86_PSUBBrr	= 2217,
    X86_PSUBDrm	= 2218,
    X86_PSUBDrr	= 2219,
    X86_PSUBQrm	= 2220,
    X86_PSUBQrr	= 2221,
    X86_PSUBSBrm	= 2222,
    X86_PSUBSBrr	= 2223,
    X86_PSUBSWrm	= 2224,
    X86_PSUBSWrr	= 2225,
    X86_PSUBUSBrm	= 2226,
    X86_PSUBUSBrr	= 2227,
    X86_PSUBUSWrm	= 2228,
    X86_PSUBUSWrr	= 2229,
    X86_PSUBWrm	= 2230,
    X86_PSUBWrr	= 2231,
    X86_PSWAPDrm	= 2232,
    X86_PSWAPDrr	= 2233,
    X86_PTESTrm	= 2234,
    X86_PTESTrr	= 2235,
    X86_PUNPCKHBWrm	= 2236,
    X86_PUNPCKHBWrr	= 2237,
    X86_PUNPCKHDQrm	= 2238,
    X86_PUNPCKHDQrr	= 2239,
    X86_PUNPCKHQDQrm	= 2240,
    X86_PUNPCKHQDQrr	= 2241,
    X86_PUNPCKHWDrm	= 2242,
    X86_PUNPCKHWDrr	= 2243,
    X86_PUNPCKLBWrm	= 2244,
    X86_PUNPCKLBWrr	= 2245,
    X86_PUNPCKLDQrm	= 2246,
    X86_PUNPCKLDQrr	= 2247,
    X86_PUNPCKLQDQrm	= 2248,
    X86_PUNPCKLQDQrr	= 2249,
    X86_PUNPCKLWDrm	= 2250,
    X86_PUNPCKLWDrr	= 2251,
    X86_PUSH16i8	= 2252,
    X86_PUSH16r	= 2253,
    X86_PUSH16rmm	= 2254,
    X86_PUSH16rmr	= 2255,
    X86_PUSH32i8	= 2256,
    X86_PUSH32r	= 2257,
    X86_PUSH32rmm	= 2258,
    X86_PUSH32rmr	= 2259,
    X86_PUSH64i16	= 2260,
    X86_PUSH64i32	= 2261,
    X86_PUSH64i8	= 2262,
    X86_PUSH64r	= 2263,
    X86_PUSH64rmm	= 2264,
    X86_PUSH64rmr	= 2265,
    X86_PUSHA16	= 2266,
    X86_PUSHA32	= 2267,
    X86_PUSHCS16	= 2268,
    X86_PUSHCS32	= 2269,
    X86_PUSHDS16	= 2270,
    X86_PUSHDS32	= 2271,
    X86_PUSHES16	= 2272,
    X86_PUSHES32	= 2273,
    X86_PUSHF16	= 2274,
    X86_PUSHF32	= 2275,
    X86_PUSHF64	= 2276,
    X86_PUSHFS16	= 2277,
    X86_PUSHFS32	= 2278,
    X86_PUSHFS64	= 2279,
    X86_PUSHGS16	= 2280,
    X86_PUSHGS32	= 2281,
    X86_PUSHGS64	= 2282,
    X86_PUSHSS16	= 2283,
    X86_PUSHSS32	= 2284,
    X86_PUSHi16	= 2285,
    X86_PUSHi32	= 2286,
    X86_PXORrm	= 2287,
    X86_PXORrr	= 2288,
    X86_RCL16m1	= 2289,
    X86_RCL16mCL	= 2290,
    X86_RCL16mi	= 2291,
    X86_RCL16r1	= 2292,
    X86_RCL16rCL	= 2293,
    X86_RCL16ri	= 2294,
    X86_RCL32m1	= 2295,
    X86_RCL32mCL	= 2296,
    X86_RCL32mi	= 2297,
    X86_RCL32r1	= 2298,
    X86_RCL32rCL	= 2299,
    X86_RCL32ri	= 2300,
    X86_RCL64m1	= 2301,
    X86_RCL64mCL	= 2302,
    X86_RCL64mi	= 2303,
    X86_RCL64r1	= 2304,
    X86_RCL64rCL	= 2305,
    X86_RCL64ri	= 2306,
    X86_RCL8m1	= 2307,
    X86_RCL8mCL	= 2308,
    X86_RCL8mi	= 2309,
    X86_RCL8r1	= 2310,
    X86_RCL8rCL	= 2311,
    X86_RCL8ri	= 2312,
    X86_RCPPSm	= 2313,
    X86_RCPPSm_Int	= 2314,
    X86_RCPPSr	= 2315,
    X86_RCPPSr_Int	= 2316,
    X86_RCPSSm	= 2317,
    X86_RCPSSm_Int	= 2318,
    X86_RCPSSr	= 2319,
    X86_RCPSSr_Int	= 2320,
    X86_RCR16m1	= 2321,
    X86_RCR16mCL	= 2322,
    X86_RCR16mi	= 2323,
    X86_RCR16r1	= 2324,
    X86_RCR16rCL	= 2325,
    X86_RCR16ri	= 2326,
    X86_RCR32m1	= 2327,
    X86_RCR32mCL	= 2328,
    X86_RCR32mi	= 2329,
    X86_RCR32r1	= 2330,
    X86_RCR32rCL	= 2331,
    X86_RCR32ri	= 2332,
    X86_RCR64m1	= 2333,
    X86_RCR64mCL	= 2334,
    X86_RCR64mi	= 2335,
    X86_RCR64r1	= 2336,
    X86_RCR64rCL	= 2337,
    X86_RCR64ri	= 2338,
    X86_RCR8m1	= 2339,
    X86_RCR8mCL	= 2340,
    X86_RCR8mi	= 2341,
    X86_RCR8r1	= 2342,
    X86_RCR8rCL	= 2343,
    X86_RCR8ri	= 2344,
    X86_RDFSBASE	= 2345,
    X86_RDFSBASE64	= 2346,
    X86_RDGSBASE	= 2347,
    X86_RDGSBASE64	= 2348,
    X86_RDMSR	= 2349,
    X86_RDPMC	= 2350,
    X86_RDRAND16r	= 2351,
    X86_RDRAND32r	= 2352,
    X86_RDRAND64r	= 2353,
    X86_RDSEED16r	= 2354,
    X86_RDSEED32r	= 2355,
    X86_RDSEED64r	= 2356,
    X86_RDTSC	= 2357,
    X86_RDTSCP	= 2358,
    X86_RELEASE_MOV16mr	= 2359,
    X86_RELEASE_MOV32mr	= 2360,
    X86_RELEASE_MOV64mr	= 2361,
    X86_RELEASE_MOV8mr	= 2362,
    X86_REPNE_PREFIX	= 2363,
    X86_REP_MOVSB_32	= 2364,
    X86_REP_MOVSB_64	= 2365,
    X86_REP_MOVSD_32	= 2366,
    X86_REP_MOVSD_64	= 2367,
    X86_REP_MOVSQ_64	= 2368,
    X86_REP_MOVSW_32	= 2369,
    X86_REP_MOVSW_64	= 2370,
    X86_REP_PREFIX	= 2371,
    X86_REP_STOSB_32	= 2372,
    X86_REP_STOSB_64	= 2373,
    X86_REP_STOSD_32	= 2374,
    X86_REP_STOSD_64	= 2375,
    X86_REP_STOSQ_64	= 2376,
    X86_REP_STOSW_32	= 2377,
    X86_REP_STOSW_64	= 2378,
    X86_RETIL	= 2379,
    X86_RETIQ	= 2380,
    X86_RETIW	= 2381,
    X86_RETL	= 2382,
    X86_RETQ	= 2383,
    X86_RETW	= 2384,
    X86_REX64_PREFIX	= 2385,
    X86_ROL16m1	= 2386,
    X86_ROL16mCL	= 2387,
    X86_ROL16mi	= 2388,
    X86_ROL16r1	= 2389,
    X86_ROL16rCL	= 2390,
    X86_ROL16ri	= 2391,
    X86_ROL32m1	= 2392,
    X86_ROL32mCL	= 2393,
    X86_ROL32mi	= 2394,
    X86_ROL32r1	= 2395,
    X86_ROL32rCL	= 2396,
    X86_ROL32ri	= 2397,
    X86_ROL64m1	= 2398,
    X86_ROL64mCL	= 2399,
    X86_ROL64mi	= 2400,
    X86_ROL64r1	= 2401,
    X86_ROL64rCL	= 2402,
    X86_ROL64ri	= 2403,
    X86_ROL8m1	= 2404,
    X86_ROL8mCL	= 2405,
    X86_ROL8mi	= 2406,
    X86_ROL8r1	= 2407,
    X86_ROL8rCL	= 2408,
    X86_ROL8ri	= 2409,
    X86_ROR16m1	= 2410,
    X86_ROR16mCL	= 2411,
    X86_ROR16mi	= 2412,
    X86_ROR16r1	= 2413,
    X86_ROR16rCL	= 2414,
    X86_ROR16ri	= 2415,
    X86_ROR32m1	= 2416,
    X86_ROR32mCL	= 2417,
    X86_ROR32mi	= 2418,
    X86_ROR32r1	= 2419,
    X86_ROR32rCL	= 2420,
    X86_ROR32ri	= 2421,
    X86_ROR64m1	= 2422,
    X86_ROR64mCL	= 2423,
    X86_ROR64mi	= 2424,
    X86_ROR64r1	= 2425,
    X86_ROR64rCL	= 2426,
    X86_ROR64ri	= 2427,
    X86_ROR8m1	= 2428,
    X86_ROR8mCL	= 2429,
    X86_ROR8mi	= 2430,
    X86_ROR8r1	= 2431,
    X86_ROR8rCL	= 2432,
    X86_ROR8ri	= 2433,
    X86_RORX32mi	= 2434,
    X86_RORX32ri	= 2435,
    X86_RORX64mi	= 2436,
    X86_RORX64ri	= 2437,
    X86_ROUNDPDm	= 2438,
    X86_ROUNDPDr	= 2439,
    X86_ROUNDPSm	= 2440,
    X86_ROUNDPSr	= 2441,
    X86_ROUNDSDm	= 2442,
    X86_ROUNDSDr	= 2443,
    X86_ROUNDSDr_Int	= 2444,
    X86_ROUNDSSm	= 2445,
    X86_ROUNDSSr	= 2446,
    X86_ROUNDSSr_Int	= 2447,
    X86_RSM	= 2448,
    X86_RSQRTPSm	= 2449,
    X86_RSQRTPSm_Int	= 2450,
    X86_RSQRTPSr	= 2451,
    X86_RSQRTPSr_Int	= 2452,
    X86_RSQRTSSm	= 2453,
    X86_RSQRTSSm_Int	= 2454,
    X86_RSQRTSSr	= 2455,
    X86_RSQRTSSr_Int	= 2456,
    X86_SAHF	= 2457,
    X86_SAL16m1	= 2458,
    X86_SAL16mCL	= 2459,
    X86_SAL16mi	= 2460,
    X86_SAL16r1	= 2461,
    X86_SAL16rCL	= 2462,
    X86_SAL16ri	= 2463,
    X86_SAL32m1	= 2464,
    X86_SAL32mCL	= 2465,
    X86_SAL32mi	= 2466,
    X86_SAL32r1	= 2467,
    X86_SAL32rCL	= 2468,
    X86_SAL32ri	= 2469,
    X86_SAL64m1	= 2470,
    X86_SAL64mCL	= 2471,
    X86_SAL64mi	= 2472,
    X86_SAL64r1	= 2473,
    X86_SAL64rCL	= 2474,
    X86_SAL64ri	= 2475,
    X86_SAL8m1	= 2476,
    X86_SAL8mCL	= 2477,
    X86_SAL8mi	= 2478,
    X86_SAL8r1	= 2479,
    X86_SAL8rCL	= 2480,
    X86_SAL8ri	= 2481,
    X86_SALC	= 2482,
    X86_SAR16m1	= 2483,
    X86_SAR16mCL	= 2484,
    X86_SAR16mi	= 2485,
    X86_SAR16r1	= 2486,
    X86_SAR16rCL	= 2487,
    X86_SAR16ri	= 2488,
    X86_SAR32m1	= 2489,
    X86_SAR32mCL	= 2490,
    X86_SAR32mi	= 2491,
    X86_SAR32r1	= 2492,
    X86_SAR32rCL	= 2493,
    X86_SAR32ri	= 2494,
    X86_SAR64m1	= 2495,
    X86_SAR64mCL	= 2496,
    X86_SAR64mi	= 2497,
    X86_SAR64r1	= 2498,
    X86_SAR64rCL	= 2499,
    X86_SAR64ri	= 2500,
    X86_SAR8m1	= 2501,
    X86_SAR8mCL	= 2502,
    X86_SAR8mi	= 2503,
    X86_SAR8r1	= 2504,
    X86_SAR8rCL	= 2505,
    X86_SAR8ri	= 2506,
    X86_SARX32rm	= 2507,
    X86_SARX32rr	= 2508,
    X86_SARX64rm	= 2509,
    X86_SARX64rr	= 2510,
    X86_SBB16i16	= 2511,
    X86_SBB16mi	= 2512,
    X86_SBB16mi8	= 2513,
    X86_SBB16mr	= 2514,
    X86_SBB16ri	= 2515,
    X86_SBB16ri8	= 2516,
    X86_SBB16rm	= 2517,
    X86_SBB16rr	= 2518,
    X86_SBB16rr_REV	= 2519,
    X86_SBB32i32	= 2520,
    X86_SBB32mi	= 2521,
    X86_SBB32mi8	= 2522,
    X86_SBB32mr	= 2523,
    X86_SBB32ri	= 2524,
    X86_SBB32ri8	= 2525,
    X86_SBB32rm	= 2526,
    X86_SBB32rr	= 2527,
    X86_SBB32rr_REV	= 2528,
    X86_SBB64i32	= 2529,
    X86_SBB64mi32	= 2530,
    X86_SBB64mi8	= 2531,
    X86_SBB64mr	= 2532,
    X86_SBB64ri32	= 2533,
    X86_SBB64ri8	= 2534,
    X86_SBB64rm	= 2535,
    X86_SBB64rr	= 2536,
    X86_SBB64rr_REV	= 2537,
    X86_SBB8i8	= 2538,
    X86_SBB8mi	= 2539,
    X86_SBB8mr	= 2540,
    X86_SBB8ri	= 2541,
    X86_SBB8rm	= 2542,
    X86_SBB8rr	= 2543,
    X86_SBB8rr_REV	= 2544,
    X86_SCASB	= 2545,
    X86_SCASL	= 2546,
    X86_SCASQ	= 2547,
    X86_SCASW	= 2548,
    X86_SEG_ALLOCA_32	= 2549,
    X86_SEG_ALLOCA_64	= 2550,
    X86_SEH_EndPrologue	= 2551,
    X86_SEH_Epilogue	= 2552,
    X86_SEH_PushFrame	= 2553,
    X86_SEH_PushReg	= 2554,
    X86_SEH_SaveReg	= 2555,
    X86_SEH_SaveXMM	= 2556,
    X86_SEH_SetFrame	= 2557,
    X86_SEH_StackAlloc	= 2558,
    X86_SETAEm	= 2559,
    X86_SETAEr	= 2560,
    X86_SETAm	= 2561,
    X86_SETAr	= 2562,
    X86_SETBEm	= 2563,
    X86_SETBEr	= 2564,
    X86_SETB_C16r	= 2565,
    X86_SETB_C32r	= 2566,
    X86_SETB_C64r	= 2567,
    X86_SETB_C8r	= 2568,
    X86_SETBm	= 2569,
    X86_SETBr	= 2570,
    X86_SETEm	= 2571,
    X86_SETEr	= 2572,
    X86_SETGEm	= 2573,
    X86_SETGEr	= 2574,
    X86_SETGm	= 2575,
    X86_SETGr	= 2576,
    X86_SETLEm	= 2577,
    X86_SETLEr	= 2578,
    X86_SETLm	= 2579,
    X86_SETLr	= 2580,
    X86_SETNEm	= 2581,
    X86_SETNEr	= 2582,
    X86_SETNOm	= 2583,
    X86_SETNOr	= 2584,
    X86_SETNPm	= 2585,
    X86_SETNPr	= 2586,
    X86_SETNSm	= 2587,
    X86_SETNSr	= 2588,
    X86_SETOm	= 2589,
    X86_SETOr	= 2590,
    X86_SETPm	= 2591,
    X86_SETPr	= 2592,
    X86_SETSm	= 2593,
    X86_SETSr	= 2594,
    X86_SFENCE	= 2595,
    X86_SGDT16m	= 2596,
    X86_SGDT32m	= 2597,
    X86_SGDT64m	= 2598,
    X86_SHA1MSG1rm	= 2599,
    X86_SHA1MSG1rr	= 2600,
    X86_SHA1MSG2rm	= 2601,
    X86_SHA1MSG2rr	= 2602,
    X86_SHA1NEXTErm	= 2603,
    X86_SHA1NEXTErr	= 2604,
    X86_SHA1RNDS4rmi	= 2605,
    X86_SHA1RNDS4rri	= 2606,
    X86_SHA256MSG1rm	= 2607,
    X86_SHA256MSG1rr	= 2608,
    X86_SHA256MSG2rm	= 2609,
    X86_SHA256MSG2rr	= 2610,
    X86_SHA256RNDS2rm	= 2611,
    X86_SHA256RNDS2rr	= 2612,
    X86_SHL16m1	= 2613,
    X86_SHL16mCL	= 2614,
    X86_SHL16mi	= 2615,
    X86_SHL16r1	= 2616,
    X86_SHL16rCL	= 2617,
    X86_SHL16ri	= 2618,
    X86_SHL32m1	= 2619,
    X86_SHL32mCL	= 2620,
    X86_SHL32mi	= 2621,
    X86_SHL32r1	= 2622,
    X86_SHL32rCL	= 2623,
    X86_SHL32ri	= 2624,
    X86_SHL64m1	= 2625,
    X86_SHL64mCL	= 2626,
    X86_SHL64mi	= 2627,
    X86_SHL64r1	= 2628,
    X86_SHL64rCL	= 2629,
    X86_SHL64ri	= 2630,
    X86_SHL8m1	= 2631,
    X86_SHL8mCL	= 2632,
    X86_SHL8mi	= 2633,
    X86_SHL8r1	= 2634,
    X86_SHL8rCL	= 2635,
    X86_SHL8ri	= 2636,
    X86_SHLD16mrCL	= 2637,
    X86_SHLD16mri8	= 2638,
    X86_SHLD16rrCL	= 2639,
    X86_SHLD16rri8	= 2640,
    X86_SHLD32mrCL	= 2641,
    X86_SHLD32mri8	= 2642,
    X86_SHLD32rrCL	= 2643,
    X86_SHLD32rri8	= 2644,
    X86_SHLD64mrCL	= 2645,
    X86_SHLD64mri8	= 2646,
    X86_SHLD64rrCL	= 2647,
    X86_SHLD64rri8	= 2648,
    X86_SHLX32rm	= 2649,
    X86_SHLX32rr	= 2650,
    X86_SHLX64rm	= 2651,
    X86_SHLX64rr	= 2652,
    X86_SHR16m1	= 2653,
    X86_SHR16mCL	= 2654,
    X86_SHR16mi	= 2655,
    X86_SHR16r1	= 2656,
    X86_SHR16rCL	= 2657,
    X86_SHR16ri	= 2658,
    X86_SHR32m1	= 2659,
    X86_SHR32mCL	= 2660,
    X86_SHR32mi	= 2661,
    X86_SHR32r1	= 2662,
    X86_SHR32rCL	= 2663,
    X86_SHR32ri	= 2664,
    X86_SHR64m1	= 2665,
    X86_SHR64mCL	= 2666,
    X86_SHR64mi	= 2667,
    X86_SHR64r1	= 2668,
    X86_SHR64rCL	= 2669,
    X86_SHR64ri	= 2670,
    X86_SHR8m1	= 2671,
    X86_SHR8mCL	= 2672,
    X86_SHR8mi	= 2673,
    X86_SHR8r1	= 2674,
    X86_SHR8rCL	= 2675,
    X86_SHR8ri	= 2676,
    X86_SHRD16mrCL	= 2677,
    X86_SHRD16mri8	= 2678,
    X86_SHRD16rrCL	= 2679,
    X86_SHRD16rri8	= 2680,
    X86_SHRD32mrCL	= 2681,
    X86_SHRD32mri8	= 2682,
    X86_SHRD32rrCL	= 2683,
    X86_SHRD32rri8	= 2684,
    X86_SHRD64mrCL	= 2685,
    X86_SHRD64mri8	= 2686,
    X86_SHRD64rrCL	= 2687,
    X86_SHRD64rri8	= 2688,
    X86_SHRX32rm	= 2689,
    X86_SHRX32rr	= 2690,
    X86_SHRX64rm	= 2691,
    X86_SHRX64rr	= 2692,
    X86_SHUFPDrmi	= 2693,
    X86_SHUFPDrri	= 2694,
    X86_SHUFPSrmi	= 2695,
    X86_SHUFPSrri	= 2696,
    X86_SIDT16m	= 2697,
    X86_SIDT32m	= 2698,
    X86_SIDT64m	= 2699,
    X86_SIN_F	= 2700,
    X86_SIN_Fp32	= 2701,
    X86_SIN_Fp64	= 2702,
    X86_SIN_Fp80	= 2703,
    X86_SKINIT	= 2704,
    X86_SLDT16m	= 2705,
    X86_SLDT16r	= 2706,
    X86_SLDT32r	= 2707,
    X86_SLDT64m	= 2708,
    X86_SLDT64r	= 2709,
    X86_SMSW16m	= 2710,
    X86_SMSW16r	= 2711,
    X86_SMSW32r	= 2712,
    X86_SMSW64r	= 2713,
    X86_SQRTPDm	= 2714,
    X86_SQRTPDr	= 2715,
    X86_SQRTPSm	= 2716,
    X86_SQRTPSr	= 2717,
    X86_SQRTSDm	= 2718,
    X86_SQRTSDm_Int	= 2719,
    X86_SQRTSDr	= 2720,
    X86_SQRTSDr_Int	= 2721,
    X86_SQRTSSm	= 2722,
    X86_SQRTSSm_Int	= 2723,
    X86_SQRTSSr	= 2724,
    X86_SQRTSSr_Int	= 2725,
    X86_SQRT_F	= 2726,
    X86_SQRT_Fp32	= 2727,
    X86_SQRT_Fp64	= 2728,
    X86_SQRT_Fp80	= 2729,
    X86_STAC	= 2730,
    X86_STC	= 2731,
    X86_STD	= 2732,
    X86_STGI	= 2733,
    X86_STI	= 2734,
    X86_STMXCSR	= 2735,
    X86_STOSB	= 2736,
    X86_STOSL	= 2737,
    X86_STOSQ	= 2738,
    X86_STOSW	= 2739,
    X86_STR16r	= 2740,
    X86_STR32r	= 2741,
    X86_STR64r	= 2742,
    X86_STRm	= 2743,
    X86_ST_F32m	= 2744,
    X86_ST_F64m	= 2745,
    X86_ST_FCOMPST0r	= 2746,
    X86_ST_FCOMPST0r_alt	= 2747,
    X86_ST_FCOMST0r	= 2748,
    X86_ST_FP32m	= 2749,
    X86_ST_FP64m	= 2750,
    X86_ST_FP80m	= 2751,
    X86_ST_FPNCEST0r	= 2752,
    X86_ST_FPST0r	= 2753,
    X86_ST_FPST0r_alt	= 2754,
    X86_ST_FPrr	= 2755,
    X86_ST_FXCHST0r	= 2756,
    X86_ST_FXCHST0r_alt	= 2757,
    X86_ST_Fp32m	= 2758,
    X86_ST_Fp64m	= 2759,
    X86_ST_Fp64m32	= 2760,
    X86_ST_Fp80m32	= 2761,
    X86_ST_Fp80m64	= 2762,
    X86_ST_FpP32m	= 2763,
    X86_ST_FpP64m	= 2764,
    X86_ST_FpP64m32	= 2765,
    X86_ST_FpP80m	= 2766,
    X86_ST_FpP80m32	= 2767,
    X86_ST_FpP80m64	= 2768,
    X86_ST_Frr	= 2769,
    X86_SUB16i16	= 2770,
    X86_SUB16mi	= 2771,
    X86_SUB16mi8	= 2772,
    X86_SUB16mr	= 2773,
    X86_SUB16ri	= 2774,
    X86_SUB16ri8	= 2775,
    X86_SUB16rm	= 2776,
    X86_SUB16rr	= 2777,
    X86_SUB16rr_REV	= 2778,
    X86_SUB32i32	= 2779,
    X86_SUB32mi	= 2780,
    X86_SUB32mi8	= 2781,
    X86_SUB32mr	= 2782,
    X86_SUB32ri	= 2783,
    X86_SUB32ri8	= 2784,
    X86_SUB32rm	= 2785,
    X86_SUB32rr	= 2786,
    X86_SUB32rr_REV	= 2787,
    X86_SUB64i32	= 2788,
    X86_SUB64mi32	= 2789,
    X86_SUB64mi8	= 2790,
    X86_SUB64mr	= 2791,
    X86_SUB64ri32	= 2792,
    X86_SUB64ri8	= 2793,
    X86_SUB64rm	= 2794,
    X86_SUB64rr	= 2795,
    X86_SUB64rr_REV	= 2796,
    X86_SUB8i8	= 2797,
    X86_SUB8mi	= 2798,
    X86_SUB8mr	= 2799,
    X86_SUB8ri	= 2800,
    X86_SUB8ri8	= 2801,
    X86_SUB8rm	= 2802,
    X86_SUB8rr	= 2803,
    X86_SUB8rr_REV	= 2804,
    X86_SUBPDrm	= 2805,
    X86_SUBPDrr	= 2806,
    X86_SUBPSrm	= 2807,
    X86_SUBPSrr	= 2808,
    X86_SUBR_F32m	= 2809,
    X86_SUBR_F64m	= 2810,
    X86_SUBR_FI16m	= 2811,
    X86_SUBR_FI32m	= 2812,
    X86_SUBR_FPrST0	= 2813,
    X86_SUBR_FST0r	= 2814,
    X86_SUBR_Fp32m	= 2815,
    X86_SUBR_Fp64m	= 2816,
    X86_SUBR_Fp64m32	= 2817,
    X86_SUBR_Fp80m32	= 2818,
    X86_SUBR_Fp80m64	= 2819,
    X86_SUBR_FpI16m32	= 2820,
    X86_SUBR_FpI16m64	= 2821,
    X86_SUBR_FpI16m80	= 2822,
    X86_SUBR_FpI32m32	= 2823,
    X86_SUBR_FpI32m64	= 2824,
    X86_SUBR_FpI32m80	= 2825,
    X86_SUBR_FrST0	= 2826,
    X86_SUBSDrm	= 2827,
    X86_SUBSDrm_Int	= 2828,
    X86_SUBSDrr	= 2829,
    X86_SUBSDrr_Int	= 2830,
    X86_SUBSSrm	= 2831,
    X86_SUBSSrm_Int	= 2832,
    X86_SUBSSrr	= 2833,
    X86_SUBSSrr_Int	= 2834,
    X86_SUB_F32m	= 2835,
    X86_SUB_F64m	= 2836,
    X86_SUB_FI16m	= 2837,
    X86_SUB_FI32m	= 2838,
    X86_SUB_FPrST0	= 2839,
    X86_SUB_FST0r	= 2840,
    X86_SUB_Fp32	= 2841,
    X86_SUB_Fp32m	= 2842,
    X86_SUB_Fp64	= 2843,
    X86_SUB_Fp64m	= 2844,
    X86_SUB_Fp64m32	= 2845,
    X86_SUB_Fp80	= 2846,
    X86_SUB_Fp80m32	= 2847,
    X86_SUB_Fp80m64	= 2848,
    X86_SUB_FpI16m32	= 2849,
    X86_SUB_FpI16m64	= 2850,
    X86_SUB_FpI16m80	= 2851,
    X86_SUB_FpI32m32	= 2852,
    X86_SUB_FpI32m64	= 2853,
    X86_SUB_FpI32m80	= 2854,
    X86_SUB_FrST0	= 2855,
    X86_SWAPGS	= 2856,
    X86_SYSCALL	= 2857,
    X86_SYSENTER	= 2858,
    X86_SYSEXIT	= 2859,
    X86_SYSEXIT64	= 2860,
    X86_SYSRET	= 2861,
    X86_SYSRET64	= 2862,
    X86_T1MSKC32rm	= 2863,
    X86_T1MSKC32rr	= 2864,
    X86_T1MSKC64rm	= 2865,
    X86_T1MSKC64rr	= 2866,
    X86_TAILJMPd	= 2867,
    X86_TAILJMPd64	= 2868,
    X86_TAILJMPm	= 2869,
    X86_TAILJMPm64	= 2870,
    X86_TAILJMPr	= 2871,
    X86_TAILJMPr64	= 2872,
    X86_TCRETURNdi	= 2873,
    X86_TCRETURNdi64	= 2874,
    X86_TCRETURNmi	= 2875,
    X86_TCRETURNmi64	= 2876,
    X86_TCRETURNri	= 2877,
    X86_TCRETURNri64	= 2878,
    X86_TEST16i16	= 2879,
    X86_TEST16mi	= 2880,
    X86_TEST16mi_alt	= 2881,
    X86_TEST16ri	= 2882,
    X86_TEST16ri_alt	= 2883,
    X86_TEST16rm	= 2884,
    X86_TEST16rr	= 2885,
    X86_TEST32i32	= 2886,
    X86_TEST32mi	= 2887,
    X86_TEST32mi_alt	= 2888,
    X86_TEST32ri	= 2889,
    X86_TEST32ri_alt	= 2890,
    X86_TEST32rm	= 2891,
    X86_TEST32rr	= 2892,
    X86_TEST64i32	= 2893,
    X86_TEST64mi32	= 2894,
    X86_TEST64mi32_alt	= 2895,
    X86_TEST64ri32	= 2896,
    X86_TEST64ri32_alt	= 2897,
    X86_TEST64rm	= 2898,
    X86_TEST64rr	= 2899,
    X86_TEST8i8	= 2900,
    X86_TEST8mi	= 2901,
    X86_TEST8mi_alt	= 2902,
    X86_TEST8ri	= 2903,
    X86_TEST8ri_NOREX	= 2904,
    X86_TEST8ri_alt	= 2905,
    X86_TEST8rm	= 2906,
    X86_TEST8rr	= 2907,
    X86_TLSCall_32	= 2908,
    X86_TLSCall_64	= 2909,
    X86_TLS_addr32	= 2910,
    X86_TLS_addr64	= 2911,
    X86_TLS_base_addr32	= 2912,
    X86_TLS_base_addr64	= 2913,
    X86_TRAP	= 2914,
    X86_TST_F	= 2915,
    X86_TST_Fp32	= 2916,
    X86_TST_Fp64	= 2917,
    X86_TST_Fp80	= 2918,
    X86_TZCNT16rm	= 2919,
    X86_TZCNT16rr	= 2920,
    X86_TZCNT32rm	= 2921,
    X86_TZCNT32rr	= 2922,
    X86_TZCNT64rm	= 2923,
    X86_TZCNT64rr	= 2924,
    X86_TZMSK32rm	= 2925,
    X86_TZMSK32rr	= 2926,
    X86_TZMSK64rm	= 2927,
    X86_TZMSK64rr	= 2928,
    X86_UCOMISDrm	= 2929,
    X86_UCOMISDrr	= 2930,
    X86_UCOMISSrm	= 2931,
    X86_UCOMISSrr	= 2932,
    X86_UCOM_FIPr	= 2933,
    X86_UCOM_FIr	= 2934,
    X86_UCOM_FPPr	= 2935,
    X86_UCOM_FPr	= 2936,
    X86_UCOM_FpIr32	= 2937,
    X86_UCOM_FpIr64	= 2938,
    X86_UCOM_FpIr80	= 2939,
    X86_UCOM_Fpr32	= 2940,
    X86_UCOM_Fpr64	= 2941,
    X86_UCOM_Fpr80	= 2942,
    X86_UCOM_Fr	= 2943,
    X86_UD2B	= 2944,
    X86_UNPCKHPDrm	= 2945,
    X86_UNPCKHPDrr	= 2946,
    X86_UNPCKHPSrm	= 2947,
    X86_UNPCKHPSrr	= 2948,
    X86_UNPCKLPDrm	= 2949,
    X86_UNPCKLPDrr	= 2950,
    X86_UNPCKLPSrm	= 2951,
    X86_UNPCKLPSrr	= 2952,
    X86_VAARG_64	= 2953,
    X86_VADDPDYrm	= 2954,
    X86_VADDPDYrr	= 2955,
    X86_VADDPDZrm	= 2956,
    X86_VADDPDZrmb	= 2957,
    X86_VADDPDZrmbk	= 2958,
    X86_VADDPDZrmbkz	= 2959,
    X86_VADDPDZrmk	= 2960,
    X86_VADDPDZrmkz	= 2961,
    X86_VADDPDZrr	= 2962,
    X86_VADDPDZrrk	= 2963,
    X86_VADDPDZrrkz	= 2964,
    X86_VADDPDrm	= 2965,
    X86_VADDPDrr	= 2966,
    X86_VADDPSYrm	= 2967,
    X86_VADDPSYrr	= 2968,
    X86_VADDPSZrm	= 2969,
    X86_VADDPSZrmb	= 2970,
    X86_VADDPSZrmbk	= 2971,
    X86_VADDPSZrmbkz	= 2972,
    X86_VADDPSZrmk	= 2973,
    X86_VADDPSZrmkz	= 2974,
    X86_VADDPSZrr	= 2975,
    X86_VADDPSZrrk	= 2976,
    X86_VADDPSZrrkz	= 2977,
    X86_VADDPSrm	= 2978,
    X86_VADDPSrr	= 2979,
    X86_VADDSDZrm	= 2980,
    X86_VADDSDZrr	= 2981,
    X86_VADDSDrm	= 2982,
    X86_VADDSDrm_Int	= 2983,
    X86_VADDSDrr	= 2984,
    X86_VADDSDrr_Int	= 2985,
    X86_VADDSSZrm	= 2986,
    X86_VADDSSZrr	= 2987,
    X86_VADDSSrm	= 2988,
    X86_VADDSSrm_Int	= 2989,
    X86_VADDSSrr	= 2990,
    X86_VADDSSrr_Int	= 2991,
    X86_VADDSUBPDYrm	= 2992,
    X86_VADDSUBPDYrr	= 2993,
    X86_VADDSUBPDrm	= 2994,
    X86_VADDSUBPDrr	= 2995,
    X86_VADDSUBPSYrm	= 2996,
    X86_VADDSUBPSYrr	= 2997,
    X86_VADDSUBPSrm	= 2998,
    X86_VADDSUBPSrr	= 2999,
    X86_VAESDECLASTrm	= 3000,
    X86_VAESDECLASTrr	= 3001,
    X86_VAESDECrm	= 3002,
    X86_VAESDECrr	= 3003,
    X86_VAESENCLASTrm	= 3004,
    X86_VAESENCLASTrr	= 3005,
    X86_VAESENCrm	= 3006,
    X86_VAESENCrr	= 3007,
    X86_VAESIMCrm	= 3008,
    X86_VAESIMCrr	= 3009,
    X86_VAESKEYGENASSIST128rm	= 3010,
    X86_VAESKEYGENASSIST128rr	= 3011,
    X86_VALIGNDrmi	= 3012,
    X86_VALIGNDrri	= 3013,
    X86_VALIGNDrrik	= 3014,
    X86_VALIGNDrrikz	= 3015,
    X86_VALIGNQrmi	= 3016,
    X86_VALIGNQrri	= 3017,
    X86_VALIGNQrrik	= 3018,
    X86_VALIGNQrrikz	= 3019,
    X86_VANDNPDYrm	= 3020,
    X86_VANDNPDYrr	= 3021,
    X86_VANDNPDrm	= 3022,
    X86_VANDNPDrr	= 3023,
    X86_VANDNPSYrm	= 3024,
    X86_VANDNPSYrr	= 3025,
    X86_VANDNPSrm	= 3026,
    X86_VANDNPSrr	= 3027,
    X86_VANDPDYrm	= 3028,
    X86_VANDPDYrr	= 3029,
    X86_VANDPDrm	= 3030,
    X86_VANDPDrr	= 3031,
    X86_VANDPSYrm	= 3032,
    X86_VANDPSYrr	= 3033,
    X86_VANDPSrm	= 3034,
    X86_VANDPSrr	= 3035,
    X86_VASTART_SAVE_XMM_REGS	= 3036,
    X86_VBLENDMPDZrm	= 3037,
    X86_VBLENDMPDZrr	= 3038,
    X86_VBLENDMPSZrm	= 3039,
    X86_VBLENDMPSZrr	= 3040,
    X86_VBLENDPDYrmi	= 3041,
    X86_VBLENDPDYrri	= 3042,
    X86_VBLENDPDrmi	= 3043,
    X86_VBLENDPDrri	= 3044,
    X86_VBLENDPSYrmi	= 3045,
    X86_VBLENDPSYrri	= 3046,
    X86_VBLENDPSrmi	= 3047,
    X86_VBLENDPSrri	= 3048,
    X86_VBLENDVPDYrm	= 3049,
    X86_VBLENDVPDYrr	= 3050,
    X86_VBLENDVPDrm	= 3051,
    X86_VBLENDVPDrr	= 3052,
    X86_VBLENDVPSYrm	= 3053,
    X86_VBLENDVPSYrr	= 3054,
    X86_VBLENDVPSrm	= 3055,
    X86_VBLENDVPSrr	= 3056,
    X86_VBROADCASTF128	= 3057,
    X86_VBROADCASTI128	= 3058,
    X86_VBROADCASTI32X4krm	= 3059,
    X86_VBROADCASTI32X4rm	= 3060,
    X86_VBROADCASTI64X4krm	= 3061,
    X86_VBROADCASTI64X4rm	= 3062,
    X86_VBROADCASTSDYrm	= 3063,
    X86_VBROADCASTSDYrr	= 3064,
    X86_VBROADCASTSDZrm	= 3065,
    X86_VBROADCASTSDZrr	= 3066,
    X86_VBROADCASTSSYrm	= 3067,
    X86_VBROADCASTSSYrr	= 3068,
    X86_VBROADCASTSSZrm	= 3069,
    X86_VBROADCASTSSZrr	= 3070,
    X86_VBROADCASTSSrm	= 3071,
    X86_VBROADCASTSSrr	= 3072,
    X86_VCMPPDYrmi	= 3073,
    X86_VCMPPDYrmi_alt	= 3074,
    X86_VCMPPDYrri	= 3075,
    X86_VCMPPDYrri_alt	= 3076,
    X86_VCMPPDZrmi	= 3077,
    X86_VCMPPDZrmi_alt	= 3078,
    X86_VCMPPDZrri	= 3079,
    X86_VCMPPDZrri_alt	= 3080,
    X86_VCMPPDZrrib	= 3081,
    X86_VCMPPDrmi	= 3082,
    X86_VCMPPDrmi_alt	= 3083,
    X86_VCMPPDrri	= 3084,
    X86_VCMPPDrri_alt	= 3085,
    X86_VCMPPSYrmi	= 3086,
    X86_VCMPPSYrmi_alt	= 3087,
    X86_VCMPPSYrri	= 3088,
    X86_VCMPPSYrri_alt	= 3089,
    X86_VCMPPSZrmi	= 3090,
    X86_VCMPPSZrmi_alt	= 3091,
    X86_VCMPPSZrri	= 3092,
    X86_VCMPPSZrri_alt	= 3093,
    X86_VCMPPSZrrib	= 3094,
    X86_VCMPPSrmi	= 3095,
    X86_VCMPPSrmi_alt	= 3096,
    X86_VCMPPSrri	= 3097,
    X86_VCMPPSrri_alt	= 3098,
    X86_VCMPSDZrm	= 3099,
    X86_VCMPSDZrmi_alt	= 3100,
    X86_VCMPSDZrr	= 3101,
    X86_VCMPSDZrri_alt	= 3102,
    X86_VCMPSDrm	= 3103,
    X86_VCMPSDrm_alt	= 3104,
    X86_VCMPSDrr	= 3105,
    X86_VCMPSDrr_alt	= 3106,
    X86_VCMPSSZrm	= 3107,
    X86_VCMPSSZrmi_alt	= 3108,
    X86_VCMPSSZrr	= 3109,
    X86_VCMPSSZrri_alt	= 3110,
    X86_VCMPSSrm	= 3111,
    X86_VCMPSSrm_alt	= 3112,
    X86_VCMPSSrr	= 3113,
    X86_VCMPSSrr_alt	= 3114,
    X86_VCOMISDZrm	= 3115,
    X86_VCOMISDZrr	= 3116,
    X86_VCOMISDrm	= 3117,
    X86_VCOMISDrr	= 3118,
    X86_VCOMISSZrm	= 3119,
    X86_VCOMISSZrr	= 3120,
    X86_VCOMISSrm	= 3121,
    X86_VCOMISSrr	= 3122,
    X86_VCVTDQ2PDYrm	= 3123,
    X86_VCVTDQ2PDYrr	= 3124,
    X86_VCVTDQ2PDZrm	= 3125,
    X86_VCVTDQ2PDZrr	= 3126,
    X86_VCVTDQ2PDrm	= 3127,
    X86_VCVTDQ2PDrr	= 3128,
    X86_VCVTDQ2PSYrm	= 3129,
    X86_VCVTDQ2PSYrr	= 3130,
    X86_VCVTDQ2PSZrm	= 3131,
    X86_VCVTDQ2PSZrr	= 3132,
    X86_VCVTDQ2PSZrrb	= 3133,
    X86_VCVTDQ2PSrm	= 3134,
    X86_VCVTDQ2PSrr	= 3135,
    X86_VCVTPD2DQXrm	= 3136,
    X86_VCVTPD2DQYrm	= 3137,
    X86_VCVTPD2DQYrr	= 3138,
    X86_VCVTPD2DQZrm	= 3139,
    X86_VCVTPD2DQZrr	= 3140,
    X86_VCVTPD2DQZrrb	= 3141,
    X86_VCVTPD2DQrr	= 3142,
    X86_VCVTPD2PSXrm	= 3143,
    X86_VCVTPD2PSYrm	= 3144,
    X86_VCVTPD2PSYrr	= 3145,
    X86_VCVTPD2PSZrm	= 3146,
    X86_VCVTPD2PSZrr	= 3147,
    X86_VCVTPD2PSZrrb	= 3148,
    X86_VCVTPD2PSrr	= 3149,
    X86_VCVTPD2UDQZrm	= 3150,
    X86_VCVTPD2UDQZrr	= 3151,
    X86_VCVTPD2UDQZrrb	= 3152,
    X86_VCVTPH2PSYrm	= 3153,
    X86_VCVTPH2PSYrr	= 3154,
    X86_VCVTPH2PSZrm	= 3155,
    X86_VCVTPH2PSZrr	= 3156,
    X86_VCVTPH2PSrm	= 3157,
    X86_VCVTPH2PSrr	= 3158,
    X86_VCVTPS2DQYrm	= 3159,
    X86_VCVTPS2DQYrr	= 3160,
    X86_VCVTPS2DQZrm	= 3161,
    X86_VCVTPS2DQZrr	= 3162,
    X86_VCVTPS2DQZrrb	= 3163,
    X86_VCVTPS2DQrm	= 3164,
    X86_VCVTPS2DQrr	= 3165,
    X86_VCVTPS2PDYrm	= 3166,
    X86_VCVTPS2PDYrr	= 3167,
    X86_VCVTPS2PDZrm	= 3168,
    X86_VCVTPS2PDZrr	= 3169,
    X86_VCVTPS2PDrm	= 3170,
    X86_VCVTPS2PDrr	= 3171,
    X86_VCVTPS2PHYmr	= 3172,
    X86_VCVTPS2PHYrr	= 3173,
    X86_VCVTPS2PHZmr	= 3174,
    X86_VCVTPS2PHZrr	= 3175,
    X86_VCVTPS2PHmr	= 3176,
    X86_VCVTPS2PHrr	= 3177,
    X86_VCVTPS2UDQZrm	= 3178,
    X86_VCVTPS2UDQZrr	= 3179,
    X86_VCVTPS2UDQZrrb	= 3180,
    X86_VCVTSD2SI64Zrm	= 3181,
    X86_VCVTSD2SI64Zrr	= 3182,
    X86_VCVTSD2SI64rm	= 3183,
    X86_VCVTSD2SI64rr	= 3184,
    X86_VCVTSD2SIZrm	= 3185,
    X86_VCVTSD2SIZrr	= 3186,
    X86_VCVTSD2SIrm	= 3187,
    X86_VCVTSD2SIrr	= 3188,
    X86_VCVTSD2SSZrm	= 3189,
    X86_VCVTSD2SSZrr	= 3190,
    X86_VCVTSD2SSrm	= 3191,
    X86_VCVTSD2SSrr	= 3192,
    X86_VCVTSD2USI64Zrm	= 3193,
    X86_VCVTSD2USI64Zrr	= 3194,
    X86_VCVTSD2USIZrm	= 3195,
    X86_VCVTSD2USIZrr	= 3196,
    X86_VCVTSI2SD64rm	= 3197,
    X86_VCVTSI2SD64rr	= 3198,
    X86_VCVTSI2SDZrm	= 3199,
    X86_VCVTSI2SDZrr	= 3200,
    X86_VCVTSI2SDrm	= 3201,
    X86_VCVTSI2SDrr	= 3202,
    X86_VCVTSI2SS64rm	= 3203,
    X86_VCVTSI2SS64rr	= 3204,
    X86_VCVTSI2SSZrm	= 3205,
    X86_VCVTSI2SSZrr	= 3206,
    X86_VCVTSI2SSrm	= 3207,
    X86_VCVTSI2SSrr	= 3208,
    X86_VCVTSI642SDZrm	= 3209,
    X86_VCVTSI642SDZrr	= 3210,
    X86_VCVTSI642SSZrm	= 3211,
    X86_VCVTSI642SSZrr	= 3212,
    X86_VCVTSS2SDZrm	= 3213,
    X86_VCVTSS2SDZrr	= 3214,
    X86_VCVTSS2SDrm	= 3215,
    X86_VCVTSS2SDrr	= 3216,
    X86_VCVTSS2SI64Zrm	= 3217,
    X86_VCVTSS2SI64Zrr	= 3218,
    X86_VCVTSS2SI64rm	= 3219,
    X86_VCVTSS2SI64rr	= 3220,
    X86_VCVTSS2SIZrm	= 3221,
    X86_VCVTSS2SIZrr	= 3222,
    X86_VCVTSS2SIrm	= 3223,
    X86_VCVTSS2SIrr	= 3224,
    X86_VCVTSS2USI64Zrm	= 3225,
    X86_VCVTSS2USI64Zrr	= 3226,
    X86_VCVTSS2USIZrm	= 3227,
    X86_VCVTSS2USIZrr	= 3228,
    X86_VCVTTPD2DQXrm	= 3229,
    X86_VCVTTPD2DQYrm	= 3230,
    X86_VCVTTPD2DQYrr	= 3231,
    X86_VCVTTPD2DQZrm	= 3232,
    X86_VCVTTPD2DQZrr	= 3233,
    X86_VCVTTPD2DQrr	= 3234,
    X86_VCVTTPD2UDQZrm	= 3235,
    X86_VCVTTPD2UDQZrr	= 3236,
    X86_VCVTTPS2DQYrm	= 3237,
    X86_VCVTTPS2DQYrr	= 3238,
    X86_VCVTTPS2DQZrm	= 3239,
    X86_VCVTTPS2DQZrr	= 3240,
    X86_VCVTTPS2DQrm	= 3241,
    X86_VCVTTPS2DQrr	= 3242,
    X86_VCVTTPS2UDQZrm	= 3243,
    X86_VCVTTPS2UDQZrr	= 3244,
    X86_VCVTTSD2SI64Zrm	= 3245,
    X86_VCVTTSD2SI64Zrr	= 3246,
    X86_VCVTTSD2SI64rm	= 3247,
    X86_VCVTTSD2SI64rr	= 3248,
    X86_VCVTTSD2SIZrm	= 3249,
    X86_VCVTTSD2SIZrr	= 3250,
    X86_VCVTTSD2SIrm	= 3251,
    X86_VCVTTSD2SIrr	= 3252,
    X86_VCVTTSD2USI64Zrm	= 3253,
    X86_VCVTTSD2USI64Zrr	= 3254,
    X86_VCVTTSD2USIZrm	= 3255,
    X86_VCVTTSD2USIZrr	= 3256,
    X86_VCVTTSS2SI64Zrm	= 3257,
    X86_VCVTTSS2SI64Zrr	= 3258,
    X86_VCVTTSS2SI64rm	= 3259,
    X86_VCVTTSS2SI64rr	= 3260,
    X86_VCVTTSS2SIZrm	= 3261,
    X86_VCVTTSS2SIZrr	= 3262,
    X86_VCVTTSS2SIrm	= 3263,
    X86_VCVTTSS2SIrr	= 3264,
    X86_VCVTTSS2USI64Zrm	= 3265,
    X86_VCVTTSS2USI64Zrr	= 3266,
    X86_VCVTTSS2USIZrm	= 3267,
    X86_VCVTTSS2USIZrr	= 3268,
    X86_VCVTUDQ2PDZrm	= 3269,
    X86_VCVTUDQ2PDZrr	= 3270,
    X86_VCVTUDQ2PSZrm	= 3271,
    X86_VCVTUDQ2PSZrr	= 3272,
    X86_VCVTUDQ2PSZrrb	= 3273,
    X86_VCVTUSI2SDZrm	= 3274,
    X86_VCVTUSI2SDZrr	= 3275,
    X86_VCVTUSI2SSZrm	= 3276,
    X86_VCVTUSI2SSZrr	= 3277,
    X86_VCVTUSI642SDZrm	= 3278,
    X86_VCVTUSI642SDZrr	= 3279,
    X86_VCVTUSI642SSZrm	= 3280,
    X86_VCVTUSI642SSZrr	= 3281,
    X86_VDIVPDYrm	= 3282,
    X86_VDIVPDYrr	= 3283,
    X86_VDIVPDZrm	= 3284,
    X86_VDIVPDZrmb	= 3285,
    X86_VDIVPDZrmbk	= 3286,
    X86_VDIVPDZrmbkz	= 3287,
    X86_VDIVPDZrmk	= 3288,
    X86_VDIVPDZrmkz	= 3289,
    X86_VDIVPDZrr	= 3290,
    X86_VDIVPDZrrk	= 3291,
    X86_VDIVPDZrrkz	= 3292,
    X86_VDIVPDrm	= 3293,
    X86_VDIVPDrr	= 3294,
    X86_VDIVPSYrm	= 3295,
    X86_VDIVPSYrr	= 3296,
    X86_VDIVPSZrm	= 3297,
    X86_VDIVPSZrmb	= 3298,
    X86_VDIVPSZrmbk	= 3299,
    X86_VDIVPSZrmbkz	= 3300,
    X86_VDIVPSZrmk	= 3301,
    X86_VDIVPSZrmkz	= 3302,
    X86_VDIVPSZrr	= 3303,
    X86_VDIVPSZrrk	= 3304,
    X86_VDIVPSZrrkz	= 3305,
    X86_VDIVPSrm	= 3306,
    X86_VDIVPSrr	= 3307,
    X86_VDIVSDZrm	= 3308,
    X86_VDIVSDZrr	= 3309,
    X86_VDIVSDrm	= 3310,
    X86_VDIVSDrm_Int	= 3311,
    X86_VDIVSDrr	= 3312,
    X86_VDIVSDrr_Int	= 3313,
    X86_VDIVSSZrm	= 3314,
    X86_VDIVSSZrr	= 3315,
    X86_VDIVSSrm	= 3316,
    X86_VDIVSSrm_Int	= 3317,
    X86_VDIVSSrr	= 3318,
    X86_VDIVSSrr_Int	= 3319,
    X86_VDPPDrmi	= 3320,
    X86_VDPPDrri	= 3321,
    X86_VDPPSYrmi	= 3322,
    X86_VDPPSYrri	= 3323,
    X86_VDPPSrmi	= 3324,
    X86_VDPPSrri	= 3325,
    X86_VERRm	= 3326,
    X86_VERRr	= 3327,
    X86_VERWm	= 3328,
    X86_VERWr	= 3329,
    X86_VEXTRACTF128mr	= 3330,
    X86_VEXTRACTF128rr	= 3331,
    X86_VEXTRACTF32x4mr	= 3332,
    X86_VEXTRACTF32x4rr	= 3333,
    X86_VEXTRACTF64x4mr	= 3334,
    X86_VEXTRACTF64x4rr	= 3335,
    X86_VEXTRACTI128mr	= 3336,
    X86_VEXTRACTI128rr	= 3337,
    X86_VEXTRACTI32x4mr	= 3338,
    X86_VEXTRACTI32x4rr	= 3339,
    X86_VEXTRACTI64x4mr	= 3340,
    X86_VEXTRACTI64x4rr	= 3341,
    X86_VEXTRACTPSmr	= 3342,
    X86_VEXTRACTPSrr	= 3343,
    X86_VEXTRACTPSzmr	= 3344,
    X86_VEXTRACTPSzrr	= 3345,
    X86_VFMADD132PDZm	= 3346,
    X86_VFMADD132PDZmb	= 3347,
    X86_VFMADD132PSZm	= 3348,
    X86_VFMADD132PSZmb	= 3349,
    X86_VFMADD213PDZm	= 3350,
    X86_VFMADD213PDZmb	= 3351,
    X86_VFMADD213PDZr	= 3352,
    X86_VFMADD213PDZrk	= 3353,
    X86_VFMADD213PDZrkz	= 3354,
    X86_VFMADD213PSZm	= 3355,
    X86_VFMADD213PSZmb	= 3356,
    X86_VFMADD213PSZr	= 3357,
    X86_VFMADD213PSZrk	= 3358,
    X86_VFMADD213PSZrkz	= 3359,
    X86_VFMADDPD4mr	= 3360,
    X86_VFMADDPD4mrY	= 3361,
    X86_VFMADDPD4rm	= 3362,
    X86_VFMADDPD4rmY	= 3363,
    X86_VFMADDPD4rr	= 3364,
    X86_VFMADDPD4rrY	= 3365,
    X86_VFMADDPD4rrY_REV	= 3366,
    X86_VFMADDPD4rr_REV	= 3367,
    X86_VFMADDPDr132m	= 3368,
    X86_VFMADDPDr132mY	= 3369,
    X86_VFMADDPDr132r	= 3370,
    X86_VFMADDPDr132rY	= 3371,
    X86_VFMADDPDr213m	= 3372,
    X86_VFMADDPDr213mY	= 3373,
    X86_VFMADDPDr213r	= 3374,
    X86_VFMADDPDr213rY	= 3375,
    X86_VFMADDPDr231m	= 3376,
    X86_VFMADDPDr231mY	= 3377,
    X86_VFMADDPDr231r	= 3378,
    X86_VFMADDPDr231rY	= 3379,
    X86_VFMADDPS4mr	= 3380,
    X86_VFMADDPS4mrY	= 3381,
    X86_VFMADDPS4rm	= 3382,
    X86_VFMADDPS4rmY	= 3383,
    X86_VFMADDPS4rr	= 3384,
    X86_VFMADDPS4rrY	= 3385,
    X86_VFMADDPS4rrY_REV	= 3386,
    X86_VFMADDPS4rr_REV	= 3387,
    X86_VFMADDPSr132m	= 3388,
    X86_VFMADDPSr132mY	= 3389,
    X86_VFMADDPSr132r	= 3390,
    X86_VFMADDPSr132rY	= 3391,
    X86_VFMADDPSr213m	= 3392,
    X86_VFMADDPSr213mY	= 3393,
    X86_VFMADDPSr213r	= 3394,
    X86_VFMADDPSr213rY	= 3395,
    X86_VFMADDPSr231m	= 3396,
    X86_VFMADDPSr231mY	= 3397,
    X86_VFMADDPSr231r	= 3398,
    X86_VFMADDPSr231rY	= 3399,
    X86_VFMADDSD4mr	= 3400,
    X86_VFMADDSD4mr_Int	= 3401,
    X86_VFMADDSD4rm	= 3402,
    X86_VFMADDSD4rm_Int	= 3403,
    X86_VFMADDSD4rr	= 3404,
    X86_VFMADDSD4rr_Int	= 3405,
    X86_VFMADDSD4rr_REV	= 3406,
    X86_VFMADDSDZm	= 3407,
    X86_VFMADDSDZr	= 3408,
    X86_VFMADDSDr132m	= 3409,
    X86_VFMADDSDr132r	= 3410,
    X86_VFMADDSDr213m	= 3411,
    X86_VFMADDSDr213r	= 3412,
    X86_VFMADDSDr231m	= 3413,
    X86_VFMADDSDr231r	= 3414,
    X86_VFMADDSS4mr	= 3415,
    X86_VFMADDSS4mr_Int	= 3416,
    X86_VFMADDSS4rm	= 3417,
    X86_VFMADDSS4rm_Int	= 3418,
    X86_VFMADDSS4rr	= 3419,
    X86_VFMADDSS4rr_Int	= 3420,
    X86_VFMADDSS4rr_REV	= 3421,
    X86_VFMADDSSZm	= 3422,
    X86_VFMADDSSZr	= 3423,
    X86_VFMADDSSr132m	= 3424,
    X86_VFMADDSSr132r	= 3425,
    X86_VFMADDSSr213m	= 3426,
    X86_VFMADDSSr213r	= 3427,
    X86_VFMADDSSr231m	= 3428,
    X86_VFMADDSSr231r	= 3429,
    X86_VFMADDSUB132PDZm	= 3430,
    X86_VFMADDSUB132PDZmb	= 3431,
    X86_VFMADDSUB132PSZm	= 3432,
    X86_VFMADDSUB132PSZmb	= 3433,
    X86_VFMADDSUB213PDZm	= 3434,
    X86_VFMADDSUB213PDZmb	= 3435,
    X86_VFMADDSUB213PDZr	= 3436,
    X86_VFMADDSUB213PDZrk	= 3437,
    X86_VFMADDSUB213PDZrkz	= 3438,
    X86_VFMADDSUB213PSZm	= 3439,
    X86_VFMADDSUB213PSZmb	= 3440,
    X86_VFMADDSUB213PSZr	= 3441,
    X86_VFMADDSUB213PSZrk	= 3442,
    X86_VFMADDSUB213PSZrkz	= 3443,
    X86_VFMADDSUBPD4mr	= 3444,
    X86_VFMADDSUBPD4mrY	= 3445,
    X86_VFMADDSUBPD4rm	= 3446,
    X86_VFMADDSUBPD4rmY	= 3447,
    X86_VFMADDSUBPD4rr	= 3448,
    X86_VFMADDSUBPD4rrY	= 3449,
    X86_VFMADDSUBPD4rrY_REV	= 3450,
    X86_VFMADDSUBPD4rr_REV	= 3451,
    X86_VFMADDSUBPDr132m	= 3452,
    X86_VFMADDSUBPDr132mY	= 3453,
    X86_VFMADDSUBPDr132r	= 3454,
    X86_VFMADDSUBPDr132rY	= 3455,
    X86_VFMADDSUBPDr213m	= 3456,
    X86_VFMADDSUBPDr213mY	= 3457,
    X86_VFMADDSUBPDr213r	= 3458,
    X86_VFMADDSUBPDr213rY	= 3459,
    X86_VFMADDSUBPDr231m	= 3460,
    X86_VFMADDSUBPDr231mY	= 3461,
    X86_VFMADDSUBPDr231r	= 3462,
    X86_VFMADDSUBPDr231rY	= 3463,
    X86_VFMADDSUBPS4mr	= 3464,
    X86_VFMADDSUBPS4mrY	= 3465,
    X86_VFMADDSUBPS4rm	= 3466,
    X86_VFMADDSUBPS4rmY	= 3467,
    X86_VFMADDSUBPS4rr	= 3468,
    X86_VFMADDSUBPS4rrY	= 3469,
    X86_VFMADDSUBPS4rrY_REV	= 3470,
    X86_VFMADDSUBPS4rr_REV	= 3471,
    X86_VFMADDSUBPSr132m	= 3472,
    X86_VFMADDSUBPSr132mY	= 3473,
    X86_VFMADDSUBPSr132r	= 3474,
    X86_VFMADDSUBPSr132rY	= 3475,
    X86_VFMADDSUBPSr213m	= 3476,
    X86_VFMADDSUBPSr213mY	= 3477,
    X86_VFMADDSUBPSr213r	= 3478,
    X86_VFMADDSUBPSr213rY	= 3479,
    X86_VFMADDSUBPSr231m	= 3480,
    X86_VFMADDSUBPSr231mY	= 3481,
    X86_VFMADDSUBPSr231r	= 3482,
    X86_VFMADDSUBPSr231rY	= 3483,
    X86_VFMSUB132PDZm	= 3484,
    X86_VFMSUB132PDZmb	= 3485,
    X86_VFMSUB132PSZm	= 3486,
    X86_VFMSUB132PSZmb	= 3487,
    X86_VFMSUB213PDZm	= 3488,
    X86_VFMSUB213PDZmb	= 3489,
    X86_VFMSUB213PDZr	= 3490,
    X86_VFMSUB213PDZrk	= 3491,
    X86_VFMSUB213PDZrkz	= 3492,
    X86_VFMSUB213PSZm	= 3493,
    X86_VFMSUB213PSZmb	= 3494,
    X86_VFMSUB213PSZr	= 3495,
    X86_VFMSUB213PSZrk	= 3496,
    X86_VFMSUB213PSZrkz	= 3497,
    X86_VFMSUBADD132PDZm	= 3498,
    X86_VFMSUBADD132PDZmb	= 3499,
    X86_VFMSUBADD132PSZm	= 3500,
    X86_VFMSUBADD132PSZmb	= 3501,
    X86_VFMSUBADD213PDZm	= 3502,
    X86_VFMSUBADD213PDZmb	= 3503,
    X86_VFMSUBADD213PDZr	= 3504,
    X86_VFMSUBADD213PDZrk	= 3505,
    X86_VFMSUBADD213PDZrkz	= 3506,
    X86_VFMSUBADD213PSZm	= 3507,
    X86_VFMSUBADD213PSZmb	= 3508,
    X86_VFMSUBADD213PSZr	= 3509,
    X86_VFMSUBADD213PSZrk	= 3510,
    X86_VFMSUBADD213PSZrkz	= 3511,
    X86_VFMSUBADDPD4mr	= 3512,
    X86_VFMSUBADDPD4mrY	= 3513,
    X86_VFMSUBADDPD4rm	= 3514,
    X86_VFMSUBADDPD4rmY	= 3515,
    X86_VFMSUBADDPD4rr	= 3516,
    X86_VFMSUBADDPD4rrY	= 3517,
    X86_VFMSUBADDPD4rrY_REV	= 3518,
    X86_VFMSUBADDPD4rr_REV	= 3519,
    X86_VFMSUBADDPDr132m	= 3520,
    X86_VFMSUBADDPDr132mY	= 3521,
    X86_VFMSUBADDPDr132r	= 3522,
    X86_VFMSUBADDPDr132rY	= 3523,
    X86_VFMSUBADDPDr213m	= 3524,
    X86_VFMSUBADDPDr213mY	= 3525,
    X86_VFMSUBADDPDr213r	= 3526,
    X86_VFMSUBADDPDr213rY	= 3527,
    X86_VFMSUBADDPDr231m	= 3528,
    X86_VFMSUBADDPDr231mY	= 3529,
    X86_VFMSUBADDPDr231r	= 3530,
    X86_VFMSUBADDPDr231rY	= 3531,
    X86_VFMSUBADDPS4mr	= 3532,
    X86_VFMSUBADDPS4mrY	= 3533,
    X86_VFMSUBADDPS4rm	= 3534,
    X86_VFMSUBADDPS4rmY	= 3535,
    X86_VFMSUBADDPS4rr	= 3536,
    X86_VFMSUBADDPS4rrY	= 3537,
    X86_VFMSUBADDPS4rrY_REV	= 3538,
    X86_VFMSUBADDPS4rr_REV	= 3539,
    X86_VFMSUBADDPSr132m	= 3540,
    X86_VFMSUBADDPSr132mY	= 3541,
    X86_VFMSUBADDPSr132r	= 3542,
    X86_VFMSUBADDPSr132rY	= 3543,
    X86_VFMSUBADDPSr213m	= 3544,
    X86_VFMSUBADDPSr213mY	= 3545,
    X86_VFMSUBADDPSr213r	= 3546,
    X86_VFMSUBADDPSr213rY	= 3547,
    X86_VFMSUBADDPSr231m	= 3548,
    X86_VFMSUBADDPSr231mY	= 3549,
    X86_VFMSUBADDPSr231r	= 3550,
    X86_VFMSUBADDPSr231rY	= 3551,
    X86_VFMSUBPD4mr	= 3552,
    X86_VFMSUBPD4mrY	= 3553,
    X86_VFMSUBPD4rm	= 3554,
    X86_VFMSUBPD4rmY	= 3555,
    X86_VFMSUBPD4rr	= 3556,
    X86_VFMSUBPD4rrY	= 3557,
    X86_VFMSUBPD4rrY_REV	= 3558,
    X86_VFMSUBPD4rr_REV	= 3559,
    X86_VFMSUBPDr132m	= 3560,
    X86_VFMSUBPDr132mY	= 3561,
    X86_VFMSUBPDr132r	= 3562,
    X86_VFMSUBPDr132rY	= 3563,
    X86_VFMSUBPDr213m	= 3564,
    X86_VFMSUBPDr213mY	= 3565,
    X86_VFMSUBPDr213r	= 3566,
    X86_VFMSUBPDr213rY	= 3567,
    X86_VFMSUBPDr231m	= 3568,
    X86_VFMSUBPDr231mY	= 3569,
    X86_VFMSUBPDr231r	= 3570,
    X86_VFMSUBPDr231rY	= 3571,
    X86_VFMSUBPS4mr	= 3572,
    X86_VFMSUBPS4mrY	= 3573,
    X86_VFMSUBPS4rm	= 3574,
    X86_VFMSUBPS4rmY	= 3575,
    X86_VFMSUBPS4rr	= 3576,
    X86_VFMSUBPS4rrY	= 3577,
    X86_VFMSUBPS4rrY_REV	= 3578,
    X86_VFMSUBPS4rr_REV	= 3579,
    X86_VFMSUBPSr132m	= 3580,
    X86_VFMSUBPSr132mY	= 3581,
    X86_VFMSUBPSr132r	= 3582,
    X86_VFMSUBPSr132rY	= 3583,
    X86_VFMSUBPSr213m	= 3584,
    X86_VFMSUBPSr213mY	= 3585,
    X86_VFMSUBPSr213r	= 3586,
    X86_VFMSUBPSr213rY	= 3587,
    X86_VFMSUBPSr231m	= 3588,
    X86_VFMSUBPSr231mY	= 3589,
    X86_VFMSUBPSr231r	= 3590,
    X86_VFMSUBPSr231rY	= 3591,
    X86_VFMSUBSD4mr	= 3592,
    X86_VFMSUBSD4mr_Int	= 3593,
    X86_VFMSUBSD4rm	= 3594,
    X86_VFMSUBSD4rm_Int	= 3595,
    X86_VFMSUBSD4rr	= 3596,
    X86_VFMSUBSD4rr_Int	= 3597,
    X86_VFMSUBSD4rr_REV	= 3598,
    X86_VFMSUBSDZm	= 3599,
    X86_VFMSUBSDZr	= 3600,
    X86_VFMSUBSDr132m	= 3601,
    X86_VFMSUBSDr132r	= 3602,
    X86_VFMSUBSDr213m	= 3603,
    X86_VFMSUBSDr213r	= 3604,
    X86_VFMSUBSDr231m	= 3605,
    X86_VFMSUBSDr231r	= 3606,
    X86_VFMSUBSS4mr	= 3607,
    X86_VFMSUBSS4mr_Int	= 3608,
    X86_VFMSUBSS4rm	= 3609,
    X86_VFMSUBSS4rm_Int	= 3610,
    X86_VFMSUBSS4rr	= 3611,
    X86_VFMSUBSS4rr_Int	= 3612,
    X86_VFMSUBSS4rr_REV	= 3613,
    X86_VFMSUBSSZm	= 3614,
    X86_VFMSUBSSZr	= 3615,
    X86_VFMSUBSSr132m	= 3616,
    X86_VFMSUBSSr132r	= 3617,
    X86_VFMSUBSSr213m	= 3618,
    X86_VFMSUBSSr213r	= 3619,
    X86_VFMSUBSSr231m	= 3620,
    X86_VFMSUBSSr231r	= 3621,
    X86_VFNMADD132PDZm	= 3622,
    X86_VFNMADD132PDZmb	= 3623,
    X86_VFNMADD132PSZm	= 3624,
    X86_VFNMADD132PSZmb	= 3625,
    X86_VFNMADD213PDZm	= 3626,
    X86_VFNMADD213PDZmb	= 3627,
    X86_VFNMADD213PDZr	= 3628,
    X86_VFNMADD213PDZrk	= 3629,
    X86_VFNMADD213PDZrkz	= 3630,
    X86_VFNMADD213PSZm	= 3631,
    X86_VFNMADD213PSZmb	= 3632,
    X86_VFNMADD213PSZr	= 3633,
    X86_VFNMADD213PSZrk	= 3634,
    X86_VFNMADD213PSZrkz	= 3635,
    X86_VFNMADDPD4mr	= 3636,
    X86_VFNMADDPD4mrY	= 3637,
    X86_VFNMADDPD4rm	= 3638,
    X86_VFNMADDPD4rmY	= 3639,
    X86_VFNMADDPD4rr	= 3640,
    X86_VFNMADDPD4rrY	= 3641,
    X86_VFNMADDPD4rrY_REV	= 3642,
    X86_VFNMADDPD4rr_REV	= 3643,
    X86_VFNMADDPDr132m	= 3644,
    X86_VFNMADDPDr132mY	= 3645,
    X86_VFNMADDPDr132r	= 3646,
    X86_VFNMADDPDr132rY	= 3647,
    X86_VFNMADDPDr213m	= 3648,
    X86_VFNMADDPDr213mY	= 3649,
    X86_VFNMADDPDr213r	= 3650,
    X86_VFNMADDPDr213rY	= 3651,
    X86_VFNMADDPDr231m	= 3652,
    X86_VFNMADDPDr231mY	= 3653,
    X86_VFNMADDPDr231r	= 3654,
    X86_VFNMADDPDr231rY	= 3655,
    X86_VFNMADDPS4mr	= 3656,
    X86_VFNMADDPS4mrY	= 3657,
    X86_VFNMADDPS4rm	= 3658,
    X86_VFNMADDPS4rmY	= 3659,
    X86_VFNMADDPS4rr	= 3660,
    X86_VFNMADDPS4rrY	= 3661,
    X86_VFNMADDPS4rrY_REV	= 3662,
    X86_VFNMADDPS4rr_REV	= 3663,
    X86_VFNMADDPSr132m	= 3664,
    X86_VFNMADDPSr132mY	= 3665,
    X86_VFNMADDPSr132r	= 3666,
    X86_VFNMADDPSr132rY	= 3667,
    X86_VFNMADDPSr213m	= 3668,
    X86_VFNMADDPSr213mY	= 3669,
    X86_VFNMADDPSr213r	= 3670,
    X86_VFNMADDPSr213rY	= 3671,
    X86_VFNMADDPSr231m	= 3672,
    X86_VFNMADDPSr231mY	= 3673,
    X86_VFNMADDPSr231r	= 3674,
    X86_VFNMADDPSr231rY	= 3675,
    X86_VFNMADDSD4mr	= 3676,
    X86_VFNMADDSD4mr_Int	= 3677,
    X86_VFNMADDSD4rm	= 3678,
    X86_VFNMADDSD4rm_Int	= 3679,
    X86_VFNMADDSD4rr	= 3680,
    X86_VFNMADDSD4rr_Int	= 3681,
    X86_VFNMADDSD4rr_REV	= 3682,
    X86_VFNMADDSDZm	= 3683,
    X86_VFNMADDSDZr	= 3684,
    X86_VFNMADDSDr132m	= 3685,
    X86_VFNMADDSDr132r	= 3686,
    X86_VFNMADDSDr213m	= 3687,
    X86_VFNMADDSDr213r	= 3688,
    X86_VFNMADDSDr231m	= 3689,
    X86_VFNMADDSDr231r	= 3690,
    X86_VFNMADDSS4mr	= 3691,
    X86_VFNMADDSS4mr_Int	= 3692,
    X86_VFNMADDSS4rm	= 3693,
    X86_VFNMADDSS4rm_Int	= 3694,
    X86_VFNMADDSS4rr	= 3695,
    X86_VFNMADDSS4rr_Int	= 3696,
    X86_VFNMADDSS4rr_REV	= 3697,
    X86_VFNMADDSSZm	= 3698,
    X86_VFNMADDSSZr	= 3699,
    X86_VFNMADDSSr132m	= 3700,
    X86_VFNMADDSSr132r	= 3701,
    X86_VFNMADDSSr213m	= 3702,
    X86_VFNMADDSSr213r	= 3703,
    X86_VFNMADDSSr231m	= 3704,
    X86_VFNMADDSSr231r	= 3705,
    X86_VFNMSUB132PDZm	= 3706,
    X86_VFNMSUB132PDZmb	= 3707,
    X86_VFNMSUB132PSZm	= 3708,
    X86_VFNMSUB132PSZmb	= 3709,
    X86_VFNMSUB213PDZm	= 3710,
    X86_VFNMSUB213PDZmb	= 3711,
    X86_VFNMSUB213PDZr	= 3712,
    X86_VFNMSUB213PDZrk	= 3713,
    X86_VFNMSUB213PDZrkz	= 3714,
    X86_VFNMSUB213PSZm	= 3715,
    X86_VFNMSUB213PSZmb	= 3716,
    X86_VFNMSUB213PSZr	= 3717,
    X86_VFNMSUB213PSZrk	= 3718,
    X86_VFNMSUB213PSZrkz	= 3719,
    X86_VFNMSUBPD4mr	= 3720,
    X86_VFNMSUBPD4mrY	= 3721,
    X86_VFNMSUBPD4rm	= 3722,
    X86_VFNMSUBPD4rmY	= 3723,
    X86_VFNMSUBPD4rr	= 3724,
    X86_VFNMSUBPD4rrY	= 3725,
    X86_VFNMSUBPD4rrY_REV	= 3726,
    X86_VFNMSUBPD4rr_REV	= 3727,
    X86_VFNMSUBPDr132m	= 3728,
    X86_VFNMSUBPDr132mY	= 3729,
    X86_VFNMSUBPDr132r	= 3730,
    X86_VFNMSUBPDr132rY	= 3731,
    X86_VFNMSUBPDr213m	= 3732,
    X86_VFNMSUBPDr213mY	= 3733,
    X86_VFNMSUBPDr213r	= 3734,
    X86_VFNMSUBPDr213rY	= 3735,
    X86_VFNMSUBPDr231m	= 3736,
    X86_VFNMSUBPDr231mY	= 3737,
    X86_VFNMSUBPDr231r	= 3738,
    X86_VFNMSUBPDr231rY	= 3739,
    X86_VFNMSUBPS4mr	= 3740,
    X86_VFNMSUBPS4mrY	= 3741,
    X86_VFNMSUBPS4rm	= 3742,
    X86_VFNMSUBPS4rmY	= 3743,
    X86_VFNMSUBPS4rr	= 3744,
    X86_VFNMSUBPS4rrY	= 3745,
    X86_VFNMSUBPS4rrY_REV	= 3746,
    X86_VFNMSUBPS4rr_REV	= 3747,
    X86_VFNMSUBPSr132m	= 3748,
    X86_VFNMSUBPSr132mY	= 3749,
    X86_VFNMSUBPSr132r	= 3750,
    X86_VFNMSUBPSr132rY	= 3751,
    X86_VFNMSUBPSr213m	= 3752,
    X86_VFNMSUBPSr213mY	= 3753,
    X86_VFNMSUBPSr213r	= 3754,
    X86_VFNMSUBPSr213rY	= 3755,
    X86_VFNMSUBPSr231m	= 3756,
    X86_VFNMSUBPSr231mY	= 3757,
    X86_VFNMSUBPSr231r	= 3758,
    X86_VFNMSUBPSr231rY	= 3759,
    X86_VFNMSUBSD4mr	= 3760,
    X86_VFNMSUBSD4mr_Int	= 3761,
    X86_VFNMSUBSD4rm	= 3762,
    X86_VFNMSUBSD4rm_Int	= 3763,
    X86_VFNMSUBSD4rr	= 3764,
    X86_VFNMSUBSD4rr_Int	= 3765,
    X86_VFNMSUBSD4rr_REV	= 3766,
    X86_VFNMSUBSDZm	= 3767,
    X86_VFNMSUBSDZr	= 3768,
    X86_VFNMSUBSDr132m	= 3769,
    X86_VFNMSUBSDr132r	= 3770,
    X86_VFNMSUBSDr213m	= 3771,
    X86_VFNMSUBSDr213r	= 3772,
    X86_VFNMSUBSDr231m	= 3773,
    X86_VFNMSUBSDr231r	= 3774,
    X86_VFNMSUBSS4mr	= 3775,
    X86_VFNMSUBSS4mr_Int	= 3776,
    X86_VFNMSUBSS4rm	= 3777,
    X86_VFNMSUBSS4rm_Int	= 3778,
    X86_VFNMSUBSS4rr	= 3779,
    X86_VFNMSUBSS4rr_Int	= 3780,
    X86_VFNMSUBSS4rr_REV	= 3781,
    X86_VFNMSUBSSZm	= 3782,
    X86_VFNMSUBSSZr	= 3783,
    X86_VFNMSUBSSr132m	= 3784,
    X86_VFNMSUBSSr132r	= 3785,
    X86_VFNMSUBSSr213m	= 3786,
    X86_VFNMSUBSSr213r	= 3787,
    X86_VFNMSUBSSr231m	= 3788,
    X86_VFNMSUBSSr231r	= 3789,
    X86_VFRCZPDrm	= 3790,
    X86_VFRCZPDrmY	= 3791,
    X86_VFRCZPDrr	= 3792,
    X86_VFRCZPDrrY	= 3793,
    X86_VFRCZPSrm	= 3794,
    X86_VFRCZPSrmY	= 3795,
    X86_VFRCZPSrr	= 3796,
    X86_VFRCZPSrrY	= 3797,
    X86_VFRCZSDrm	= 3798,
    X86_VFRCZSDrr	= 3799,
    X86_VFRCZSSrm	= 3800,
    X86_VFRCZSSrr	= 3801,
    X86_VFsANDNPDrm	= 3802,
    X86_VFsANDNPDrr	= 3803,
    X86_VFsANDNPSrm	= 3804,
    X86_VFsANDNPSrr	= 3805,
    X86_VFsANDPDrm	= 3806,
    X86_VFsANDPDrr	= 3807,
    X86_VFsANDPSrm	= 3808,
    X86_VFsANDPSrr	= 3809,
    X86_VFsORPDrm	= 3810,
    X86_VFsORPDrr	= 3811,
    X86_VFsORPSrm	= 3812,
    X86_VFsORPSrr	= 3813,
    X86_VFsXORPDrm	= 3814,
    X86_VFsXORPDrr	= 3815,
    X86_VFsXORPSrm	= 3816,
    X86_VFsXORPSrr	= 3817,
    X86_VGATHERDPDYrm	= 3818,
    X86_VGATHERDPDZrm	= 3819,
    X86_VGATHERDPDrm	= 3820,
    X86_VGATHERDPSYrm	= 3821,
    X86_VGATHERDPSZrm	= 3822,
    X86_VGATHERDPSrm	= 3823,
    X86_VGATHERPF0DPDm	= 3824,
    X86_VGATHERPF0DPSm	= 3825,
    X86_VGATHERPF0QPDm	= 3826,
    X86_VGATHERPF0QPSm	= 3827,
    X86_VGATHERPF1DPDm	= 3828,
    X86_VGATHERPF1DPSm	= 3829,
    X86_VGATHERPF1QPDm	= 3830,
    X86_VGATHERPF1QPSm	= 3831,
    X86_VGATHERQPDYrm	= 3832,
    X86_VGATHERQPDZrm	= 3833,
    X86_VGATHERQPDrm	= 3834,
    X86_VGATHERQPSYrm	= 3835,
    X86_VGATHERQPSZrm	= 3836,
    X86_VGATHERQPSrm	= 3837,
    X86_VHADDPDYrm	= 3838,
    X86_VHADDPDYrr	= 3839,
    X86_VHADDPDrm	= 3840,
    X86_VHADDPDrr	= 3841,
    X86_VHADDPSYrm	= 3842,
    X86_VHADDPSYrr	= 3843,
    X86_VHADDPSrm	= 3844,
    X86_VHADDPSrr	= 3845,
    X86_VHSUBPDYrm	= 3846,
    X86_VHSUBPDYrr	= 3847,
    X86_VHSUBPDrm	= 3848,
    X86_VHSUBPDrr	= 3849,
    X86_VHSUBPSYrm	= 3850,
    X86_VHSUBPSYrr	= 3851,
    X86_VHSUBPSrm	= 3852,
    X86_VHSUBPSrr	= 3853,
    X86_VINSERTF128rm	= 3854,
    X86_VINSERTF128rr	= 3855,
    X86_VINSERTF32x4rm	= 3856,
    X86_VINSERTF32x4rr	= 3857,
    X86_VINSERTF64x4rm	= 3858,
    X86_VINSERTF64x4rr	= 3859,
    X86_VINSERTI128rm	= 3860,
    X86_VINSERTI128rr	= 3861,
    X86_VINSERTI32x4rm	= 3862,
    X86_VINSERTI32x4rr	= 3863,
    X86_VINSERTI64x4rm	= 3864,
    X86_VINSERTI64x4rr	= 3865,
    X86_VINSERTPSrm	= 3866,
    X86_VINSERTPSrr	= 3867,
    X86_VINSERTPSzrm	= 3868,
    X86_VINSERTPSzrr	= 3869,
    X86_VLDDQUYrm	= 3870,
    X86_VLDDQUrm	= 3871,
    X86_VLDMXCSR	= 3872,
    X86_VMASKMOVDQU	= 3873,
    X86_VMASKMOVDQU64	= 3874,
    X86_VMASKMOVPDYmr	= 3875,
    X86_VMASKMOVPDYrm	= 3876,
    X86_VMASKMOVPDmr	= 3877,
    X86_VMASKMOVPDrm	= 3878,
    X86_VMASKMOVPSYmr	= 3879,
    X86_VMASKMOVPSYrm	= 3880,
    X86_VMASKMOVPSmr	= 3881,
    X86_VMASKMOVPSrm	= 3882,
    X86_VMAXCPDYrm	= 3883,
    X86_VMAXCPDYrr	= 3884,
    X86_VMAXCPDrm	= 3885,
    X86_VMAXCPDrr	= 3886,
    X86_VMAXCPSYrm	= 3887,
    X86_VMAXCPSYrr	= 3888,
    X86_VMAXCPSrm	= 3889,
    X86_VMAXCPSrr	= 3890,
    X86_VMAXCSDrm	= 3891,
    X86_VMAXCSDrr	= 3892,
    X86_VMAXCSSrm	= 3893,
    X86_VMAXCSSrr	= 3894,
    X86_VMAXPDYrm	= 3895,
    X86_VMAXPDYrr	= 3896,
    X86_VMAXPDZrm	= 3897,
    X86_VMAXPDZrmb	= 3898,
    X86_VMAXPDZrmbk	= 3899,
    X86_VMAXPDZrmbkz	= 3900,
    X86_VMAXPDZrmk	= 3901,
    X86_VMAXPDZrmkz	= 3902,
    X86_VMAXPDZrr	= 3903,
    X86_VMAXPDZrrk	= 3904,
    X86_VMAXPDZrrkz	= 3905,
    X86_VMAXPDrm	= 3906,
    X86_VMAXPDrr	= 3907,
    X86_VMAXPSYrm	= 3908,
    X86_VMAXPSYrr	= 3909,
    X86_VMAXPSZrm	= 3910,
    X86_VMAXPSZrmb	= 3911,
    X86_VMAXPSZrmbk	= 3912,
    X86_VMAXPSZrmbkz	= 3913,
    X86_VMAXPSZrmk	= 3914,
    X86_VMAXPSZrmkz	= 3915,
    X86_VMAXPSZrr	= 3916,
    X86_VMAXPSZrrk	= 3917,
    X86_VMAXPSZrrkz	= 3918,
    X86_VMAXPSrm	= 3919,
    X86_VMAXPSrr	= 3920,
    X86_VMAXSDZrm	= 3921,
    X86_VMAXSDZrr	= 3922,
    X86_VMAXSDrm	= 3923,
    X86_VMAXSDrm_Int	= 3924,
    X86_VMAXSDrr	= 3925,
    X86_VMAXSDrr_Int	= 3926,
    X86_VMAXSSZrm	= 3927,
    X86_VMAXSSZrr	= 3928,
    X86_VMAXSSrm	= 3929,
    X86_VMAXSSrm_Int	= 3930,
    X86_VMAXSSrr	= 3931,
    X86_VMAXSSrr_Int	= 3932,
    X86_VMCALL	= 3933,
    X86_VMCLEARm	= 3934,
    X86_VMFUNC	= 3935,
    X86_VMINCPDYrm	= 3936,
    X86_VMINCPDYrr	= 3937,
    X86_VMINCPDrm	= 3938,
    X86_VMINCPDrr	= 3939,
    X86_VMINCPSYrm	= 3940,
    X86_VMINCPSYrr	= 3941,
    X86_VMINCPSrm	= 3942,
    X86_VMINCPSrr	= 3943,
    X86_VMINCSDrm	= 3944,
    X86_VMINCSDrr	= 3945,
    X86_VMINCSSrm	= 3946,
    X86_VMINCSSrr	= 3947,
    X86_VMINPDYrm	= 3948,
    X86_VMINPDYrr	= 3949,
    X86_VMINPDZrm	= 3950,
    X86_VMINPDZrmb	= 3951,
    X86_VMINPDZrmbk	= 3952,
    X86_VMINPDZrmbkz	= 3953,
    X86_VMINPDZrmk	= 3954,
    X86_VMINPDZrmkz	= 3955,
    X86_VMINPDZrr	= 3956,
    X86_VMINPDZrrk	= 3957,
    X86_VMINPDZrrkz	= 3958,
    X86_VMINPDrm	= 3959,
    X86_VMINPDrr	= 3960,
    X86_VMINPSYrm	= 3961,
    X86_VMINPSYrr	= 3962,
    X86_VMINPSZrm	= 3963,
    X86_VMINPSZrmb	= 3964,
    X86_VMINPSZrmbk	= 3965,
    X86_VMINPSZrmbkz	= 3966,
    X86_VMINPSZrmk	= 3967,
    X86_VMINPSZrmkz	= 3968,
    X86_VMINPSZrr	= 3969,
    X86_VMINPSZrrk	= 3970,
    X86_VMINPSZrrkz	= 3971,
    X86_VMINPSrm	= 3972,
    X86_VMINPSrr	= 3973,
    X86_VMINSDZrm	= 3974,
    X86_VMINSDZrr	= 3975,
    X86_VMINSDrm	= 3976,
    X86_VMINSDrm_Int	= 3977,
    X86_VMINSDrr	= 3978,
    X86_VMINSDrr_Int	= 3979,
    X86_VMINSSZrm	= 3980,
    X86_VMINSSZrr	= 3981,
    X86_VMINSSrm	= 3982,
    X86_VMINSSrm_Int	= 3983,
    X86_VMINSSrr	= 3984,
    X86_VMINSSrr_Int	= 3985,
    X86_VMLAUNCH	= 3986,
    X86_VMLOAD32	= 3987,
    X86_VMLOAD64	= 3988,
    X86_VMMCALL	= 3989,
    X86_VMOV64toPQIZrr	= 3990,
    X86_VMOV64toPQIrr	= 3991,
    X86_VMOV64toSDZrr	= 3992,
    X86_VMOV64toSDrm	= 3993,
    X86_VMOV64toSDrr	= 3994,
    X86_VMOVAPDYmr	= 3995,
    X86_VMOVAPDYrm	= 3996,
    X86_VMOVAPDYrr	= 3997,
    X86_VMOVAPDYrr_REV	= 3998,
    X86_VMOVAPDZ128mr	= 3999,
    X86_VMOVAPDZ128mrk	= 4000,
    X86_VMOVAPDZ128rm	= 4001,
    X86_VMOVAPDZ128rmk	= 4002,
    X86_VMOVAPDZ128rmkz	= 4003,
    X86_VMOVAPDZ128rr	= 4004,
    X86_VMOVAPDZ128rr_alt	= 4005,
    X86_VMOVAPDZ128rrk	= 4006,
    X86_VMOVAPDZ128rrk_alt	= 4007,
    X86_VMOVAPDZ128rrkz	= 4008,
    X86_VMOVAPDZ128rrkz_alt	= 4009,
    X86_VMOVAPDZ256mr	= 4010,
    X86_VMOVAPDZ256mrk	= 4011,
    X86_VMOVAPDZ256rm	= 4012,
    X86_VMOVAPDZ256rmk	= 4013,
    X86_VMOVAPDZ256rmkz	= 4014,
    X86_VMOVAPDZ256rr	= 4015,
    X86_VMOVAPDZ256rr_alt	= 4016,
    X86_VMOVAPDZ256rrk	= 4017,
    X86_VMOVAPDZ256rrk_alt	= 4018,
    X86_VMOVAPDZ256rrkz	= 4019,
    X86_VMOVAPDZ256rrkz_alt	= 4020,
    X86_VMOVAPDZmr	= 4021,
    X86_VMOVAPDZmrk	= 4022,
    X86_VMOVAPDZrm	= 4023,
    X86_VMOVAPDZrmk	= 4024,
    X86_VMOVAPDZrmkz	= 4025,
    X86_VMOVAPDZrr	= 4026,
    X86_VMOVAPDZrr_alt	= 4027,
    X86_VMOVAPDZrrk	= 4028,
    X86_VMOVAPDZrrk_alt	= 4029,
    X86_VMOVAPDZrrkz	= 4030,
    X86_VMOVAPDZrrkz_alt	= 4031,
    X86_VMOVAPDmr	= 4032,
    X86_VMOVAPDrm	= 4033,
    X86_VMOVAPDrr	= 4034,
    X86_VMOVAPDrr_REV	= 4035,
    X86_VMOVAPSYmr	= 4036,
    X86_VMOVAPSYrm	= 4037,
    X86_VMOVAPSYrr	= 4038,
    X86_VMOVAPSYrr_REV	= 4039,
    X86_VMOVAPSZ128mr	= 4040,
    X86_VMOVAPSZ128mrk	= 4041,
    X86_VMOVAPSZ128rm	= 4042,
    X86_VMOVAPSZ128rmk	= 4043,
    X86_VMOVAPSZ128rmkz	= 4044,
    X86_VMOVAPSZ128rr	= 4045,
    X86_VMOVAPSZ128rr_alt	= 4046,
    X86_VMOVAPSZ128rrk	= 4047,
    X86_VMOVAPSZ128rrk_alt	= 4048,
    X86_VMOVAPSZ128rrkz	= 4049,
    X86_VMOVAPSZ128rrkz_alt	= 4050,
    X86_VMOVAPSZ256mr	= 4051,
    X86_VMOVAPSZ256mrk	= 4052,
    X86_VMOVAPSZ256rm	= 4053,
    X86_VMOVAPSZ256rmk	= 4054,
    X86_VMOVAPSZ256rmkz	= 4055,
    X86_VMOVAPSZ256rr	= 4056,
    X86_VMOVAPSZ256rr_alt	= 4057,
    X86_VMOVAPSZ256rrk	= 4058,
    X86_VMOVAPSZ256rrk_alt	= 4059,
    X86_VMOVAPSZ256rrkz	= 4060,
    X86_VMOVAPSZ256rrkz_alt	= 4061,
    X86_VMOVAPSZmr	= 4062,
    X86_VMOVAPSZmrk	= 4063,
    X86_VMOVAPSZrm	= 4064,
    X86_VMOVAPSZrmk	= 4065,
    X86_VMOVAPSZrmkz	= 4066,
    X86_VMOVAPSZrr	= 4067,
    X86_VMOVAPSZrr_alt	= 4068,
    X86_VMOVAPSZrrk	= 4069,
    X86_VMOVAPSZrrk_alt	= 4070,
    X86_VMOVAPSZrrkz	= 4071,
    X86_VMOVAPSZrrkz_alt	= 4072,
    X86_VMOVAPSmr	= 4073,
    X86_VMOVAPSrm	= 4074,
    X86_VMOVAPSrr	= 4075,
    X86_VMOVAPSrr_REV	= 4076,
    X86_VMOVDDUPYrm	= 4077,
    X86_VMOVDDUPYrr	= 4078,
    X86_VMOVDDUPZrm	= 4079,
    X86_VMOVDDUPZrr	= 4080,
    X86_VMOVDDUPrm	= 4081,
    X86_VMOVDDUPrr	= 4082,
    X86_VMOVDI2PDIZrm	= 4083,
    X86_VMOVDI2PDIZrr	= 4084,
    X86_VMOVDI2PDIrm	= 4085,
    X86_VMOVDI2PDIrr	= 4086,
    X86_VMOVDI2SSZrm	= 4087,
    X86_VMOVDI2SSZrr	= 4088,
    X86_VMOVDI2SSrm	= 4089,
    X86_VMOVDI2SSrr	= 4090,
    X86_VMOVDQA32Z128mr	= 4091,
    X86_VMOVDQA32Z128mrk	= 4092,
    X86_VMOVDQA32Z128rm	= 4093,
    X86_VMOVDQA32Z128rmk	= 4094,
    X86_VMOVDQA32Z128rmkz	= 4095,
    X86_VMOVDQA32Z128rr	= 4096,
    X86_VMOVDQA32Z128rr_alt	= 4097,
    X86_VMOVDQA32Z128rrk	= 4098,
    X86_VMOVDQA32Z128rrk_alt	= 4099,
    X86_VMOVDQA32Z128rrkz	= 4100,
    X86_VMOVDQA32Z128rrkz_alt	= 4101,
    X86_VMOVDQA32Z256mr	= 4102,
    X86_VMOVDQA32Z256mrk	= 4103,
    X86_VMOVDQA32Z256rm	= 4104,
    X86_VMOVDQA32Z256rmk	= 4105,
    X86_VMOVDQA32Z256rmkz	= 4106,
    X86_VMOVDQA32Z256rr	= 4107,
    X86_VMOVDQA32Z256rr_alt	= 4108,
    X86_VMOVDQA32Z256rrk	= 4109,
    X86_VMOVDQA32Z256rrk_alt	= 4110,
    X86_VMOVDQA32Z256rrkz	= 4111,
    X86_VMOVDQA32Z256rrkz_alt	= 4112,
    X86_VMOVDQA32Zmr	= 4113,
    X86_VMOVDQA32Zmrk	= 4114,
    X86_VMOVDQA32Zrm	= 4115,
    X86_VMOVDQA32Zrmk	= 4116,
    X86_VMOVDQA32Zrmkz	= 4117,
    X86_VMOVDQA32Zrr	= 4118,
    X86_VMOVDQA32Zrr_alt	= 4119,
    X86_VMOVDQA32Zrrk	= 4120,
    X86_VMOVDQA32Zrrk_alt	= 4121,
    X86_VMOVDQA32Zrrkz	= 4122,
    X86_VMOVDQA32Zrrkz_alt	= 4123,
    X86_VMOVDQA64Z128mr	= 4124,
    X86_VMOVDQA64Z128mrk	= 4125,
    X86_VMOVDQA64Z128rm	= 4126,
    X86_VMOVDQA64Z128rmk	= 4127,
    X86_VMOVDQA64Z128rmkz	= 4128,
    X86_VMOVDQA64Z128rr	= 4129,
    X86_VMOVDQA64Z128rr_alt	= 4130,
    X86_VMOVDQA64Z128rrk	= 4131,
    X86_VMOVDQA64Z128rrk_alt	= 4132,
    X86_VMOVDQA64Z128rrkz	= 4133,
    X86_VMOVDQA64Z128rrkz_alt	= 4134,
    X86_VMOVDQA64Z256mr	= 4135,
    X86_VMOVDQA64Z256mrk	= 4136,
    X86_VMOVDQA64Z256rm	= 4137,
    X86_VMOVDQA64Z256rmk	= 4138,
    X86_VMOVDQA64Z256rmkz	= 4139,
    X86_VMOVDQA64Z256rr	= 4140,
    X86_VMOVDQA64Z256rr_alt	= 4141,
    X86_VMOVDQA64Z256rrk	= 4142,
    X86_VMOVDQA64Z256rrk_alt	= 4143,
    X86_VMOVDQA64Z256rrkz	= 4144,
    X86_VMOVDQA64Z256rrkz_alt	= 4145,
    X86_VMOVDQA64Zmr	= 4146,
    X86_VMOVDQA64Zmrk	= 4147,
    X86_VMOVDQA64Zrm	= 4148,
    X86_VMOVDQA64Zrmk	= 4149,
    X86_VMOVDQA64Zrmkz	= 4150,
    X86_VMOVDQA64Zrr	= 4151,
    X86_VMOVDQA64Zrr_alt	= 4152,
    X86_VMOVDQA64Zrrk	= 4153,
    X86_VMOVDQA64Zrrk_alt	= 4154,
    X86_VMOVDQA64Zrrkz	= 4155,
    X86_VMOVDQA64Zrrkz_alt	= 4156,
    X86_VMOVDQAYmr	= 4157,
    X86_VMOVDQAYrm	= 4158,
    X86_VMOVDQAYrr	= 4159,
    X86_VMOVDQAYrr_REV	= 4160,
    X86_VMOVDQAmr	= 4161,
    X86_VMOVDQArm	= 4162,
    X86_VMOVDQArr	= 4163,
    X86_VMOVDQArr_REV	= 4164,
    X86_VMOVDQU16Z128mr	= 4165,
    X86_VMOVDQU16Z128mrk	= 4166,
    X86_VMOVDQU16Z128rm	= 4167,
    X86_VMOVDQU16Z128rmk	= 4168,
    X86_VMOVDQU16Z128rmkz	= 4169,
    X86_VMOVDQU16Z128rr	= 4170,
    X86_VMOVDQU16Z128rr_alt	= 4171,
    X86_VMOVDQU16Z128rrk	= 4172,
    X86_VMOVDQU16Z128rrk_alt	= 4173,
    X86_VMOVDQU16Z128rrkz	= 4174,
    X86_VMOVDQU16Z128rrkz_alt	= 4175,
    X86_VMOVDQU16Z256mr	= 4176,
    X86_VMOVDQU16Z256mrk	= 4177,
    X86_VMOVDQU16Z256rm	= 4178,
    X86_VMOVDQU16Z256rmk	= 4179,
    X86_VMOVDQU16Z256rmkz	= 4180,
    X86_VMOVDQU16Z256rr	= 4181,
    X86_VMOVDQU16Z256rr_alt	= 4182,
    X86_VMOVDQU16Z256rrk	= 4183,
    X86_VMOVDQU16Z256rrk_alt	= 4184,
    X86_VMOVDQU16Z256rrkz	= 4185,
    X86_VMOVDQU16Z256rrkz_alt	= 4186,
    X86_VMOVDQU16Zmr	= 4187,
    X86_VMOVDQU16Zmrk	= 4188,
    X86_VMOVDQU16Zrm	= 4189,
    X86_VMOVDQU16Zrmk	= 4190,
    X86_VMOVDQU16Zrmkz	= 4191,
    X86_VMOVDQU16Zrr	= 4192,
    X86_VMOVDQU16Zrr_alt	= 4193,
    X86_VMOVDQU16Zrrk	= 4194,
    X86_VMOVDQU16Zrrk_alt	= 4195,
    X86_VMOVDQU16Zrrkz	= 4196,
    X86_VMOVDQU16Zrrkz_alt	= 4197,
    X86_VMOVDQU32Z128mr	= 4198,
    X86_VMOVDQU32Z128mrk	= 4199,
    X86_VMOVDQU32Z128rm	= 4200,
    X86_VMOVDQU32Z128rmk	= 4201,
    X86_VMOVDQU32Z128rmkz	= 4202,
    X86_VMOVDQU32Z128rr	= 4203,
    X86_VMOVDQU32Z128rr_alt	= 4204,
    X86_VMOVDQU32Z128rrk	= 4205,
    X86_VMOVDQU32Z128rrk_alt	= 4206,
    X86_VMOVDQU32Z128rrkz	= 4207,
    X86_VMOVDQU32Z128rrkz_alt	= 4208,
    X86_VMOVDQU32Z256mr	= 4209,
    X86_VMOVDQU32Z256mrk	= 4210,
    X86_VMOVDQU32Z256rm	= 4211,
    X86_VMOVDQU32Z256rmk	= 4212,
    X86_VMOVDQU32Z256rmkz	= 4213,
    X86_VMOVDQU32Z256rr	= 4214,
    X86_VMOVDQU32Z256rr_alt	= 4215,
    X86_VMOVDQU32Z256rrk	= 4216,
    X86_VMOVDQU32Z256rrk_alt	= 4217,
    X86_VMOVDQU32Z256rrkz	= 4218,
    X86_VMOVDQU32Z256rrkz_alt	= 4219,
    X86_VMOVDQU32Zmr	= 4220,
    X86_VMOVDQU32Zmrk	= 4221,
    X86_VMOVDQU32Zrm	= 4222,
    X86_VMOVDQU32Zrmk	= 4223,
    X86_VMOVDQU32Zrmkz	= 4224,
    X86_VMOVDQU32Zrr	= 4225,
    X86_VMOVDQU32Zrr_alt	= 4226,
    X86_VMOVDQU32Zrrk	= 4227,
    X86_VMOVDQU32Zrrk_alt	= 4228,
    X86_VMOVDQU32Zrrkz	= 4229,
    X86_VMOVDQU32Zrrkz_alt	= 4230,
    X86_VMOVDQU64Z128mr	= 4231,
    X86_VMOVDQU64Z128mrk	= 4232,
    X86_VMOVDQU64Z128rm	= 4233,
    X86_VMOVDQU64Z128rmk	= 4234,
    X86_VMOVDQU64Z128rmkz	= 4235,
    X86_VMOVDQU64Z128rr	= 4236,
    X86_VMOVDQU64Z128rr_alt	= 4237,
    X86_VMOVDQU64Z128rrk	= 4238,
    X86_VMOVDQU64Z128rrk_alt	= 4239,
    X86_VMOVDQU64Z128rrkz	= 4240,
    X86_VMOVDQU64Z128rrkz_alt	= 4241,
    X86_VMOVDQU64Z256mr	= 4242,
    X86_VMOVDQU64Z256mrk	= 4243,
    X86_VMOVDQU64Z256rm	= 4244,
    X86_VMOVDQU64Z256rmk	= 4245,
    X86_VMOVDQU64Z256rmkz	= 4246,
    X86_VMOVDQU64Z256rr	= 4247,
    X86_VMOVDQU64Z256rr_alt	= 4248,
    X86_VMOVDQU64Z256rrk	= 4249,
    X86_VMOVDQU64Z256rrk_alt	= 4250,
    X86_VMOVDQU64Z256rrkz	= 4251,
    X86_VMOVDQU64Z256rrkz_alt	= 4252,
    X86_VMOVDQU64Zmr	= 4253,
    X86_VMOVDQU64Zmrk	= 4254,
    X86_VMOVDQU64Zrm	= 4255,
    X86_VMOVDQU64Zrmk	= 4256,
    X86_VMOVDQU64Zrmkz	= 4257,
    X86_VMOVDQU64Zrr	= 4258,
    X86_VMOVDQU64Zrr_alt	= 4259,
    X86_VMOVDQU64Zrrk	= 4260,
    X86_VMOVDQU64Zrrk_alt	= 4261,
    X86_VMOVDQU64Zrrkz	= 4262,
    X86_VMOVDQU64Zrrkz_alt	= 4263,
    X86_VMOVDQU8Z128mr	= 4264,
    X86_VMOVDQU8Z128mrk	= 4265,
    X86_VMOVDQU8Z128rm	= 4266,
    X86_VMOVDQU8Z128rmk	= 4267,
    X86_VMOVDQU8Z128rmkz	= 4268,
    X86_VMOVDQU8Z128rr	= 4269,
    X86_VMOVDQU8Z128rr_alt	= 4270,
    X86_VMOVDQU8Z128rrk	= 4271,
    X86_VMOVDQU8Z128rrk_alt	= 4272,
    X86_VMOVDQU8Z128rrkz	= 4273,
    X86_VMOVDQU8Z128rrkz_alt	= 4274,
    X86_VMOVDQU8Z256mr	= 4275,
    X86_VMOVDQU8Z256mrk	= 4276,
    X86_VMOVDQU8Z256rm	= 4277,
    X86_VMOVDQU8Z256rmk	= 4278,
    X86_VMOVDQU8Z256rmkz	= 4279,
    X86_VMOVDQU8Z256rr	= 4280,
    X86_VMOVDQU8Z256rr_alt	= 4281,
    X86_VMOVDQU8Z256rrk	= 4282,
    X86_VMOVDQU8Z256rrk_alt	= 4283,
    X86_VMOVDQU8Z256rrkz	= 4284,
    X86_VMOVDQU8Z256rrkz_alt	= 4285,
    X86_VMOVDQU8Zmr	= 4286,
    X86_VMOVDQU8Zmrk	= 4287,
    X86_VMOVDQU8Zrm	= 4288,
    X86_VMOVDQU8Zrmk	= 4289,
    X86_VMOVDQU8Zrmkz	= 4290,
    X86_VMOVDQU8Zrr	= 4291,
    X86_VMOVDQU8Zrr_alt	= 4292,
    X86_VMOVDQU8Zrrk	= 4293,
    X86_VMOVDQU8Zrrk_alt	= 4294,
    X86_VMOVDQU8Zrrkz	= 4295,
    X86_VMOVDQU8Zrrkz_alt	= 4296,
    X86_VMOVDQUYmr	= 4297,
    X86_VMOVDQUYrm	= 4298,
    X86_VMOVDQUYrr	= 4299,
    X86_VMOVDQUYrr_REV	= 4300,
    X86_VMOVDQUmr	= 4301,
    X86_VMOVDQUrm	= 4302,
    X86_VMOVDQUrr	= 4303,
    X86_VMOVDQUrr_REV	= 4304,
    X86_VMOVHLPSZrr	= 4305,
    X86_VMOVHLPSrr	= 4306,
    X86_VMOVHPDmr	= 4307,
    X86_VMOVHPDrm	= 4308,
    X86_VMOVHPSmr	= 4309,
    X86_VMOVHPSrm	= 4310,
    X86_VMOVLHPSZrr	= 4311,
    X86_VMOVLHPSrr	= 4312,
    X86_VMOVLPDmr	= 4313,
    X86_VMOVLPDrm	= 4314,
    X86_VMOVLPSmr	= 4315,
    X86_VMOVLPSrm	= 4316,
    X86_VMOVMSKPDYrr	= 4317,
    X86_VMOVMSKPDrr	= 4318,
    X86_VMOVMSKPSYrr	= 4319,
    X86_VMOVMSKPSrr	= 4320,
    X86_VMOVNTDQAYrm	= 4321,
    X86_VMOVNTDQAZ128rm	= 4322,
    X86_VMOVNTDQAZ256rm	= 4323,
    X86_VMOVNTDQAZrm	= 4324,
    X86_VMOVNTDQArm	= 4325,
    X86_VMOVNTDQYmr	= 4326,
    X86_VMOVNTDQZ128mr	= 4327,
    X86_VMOVNTDQZ256mr	= 4328,
    X86_VMOVNTDQZmr	= 4329,
    X86_VMOVNTDQmr	= 4330,
    X86_VMOVNTPDYmr	= 4331,
    X86_VMOVNTPDZ128mr	= 4332,
    X86_VMOVNTPDZ256mr	= 4333,
    X86_VMOVNTPDZmr	= 4334,
    X86_VMOVNTPDmr	= 4335,
    X86_VMOVNTPSYmr	= 4336,
    X86_VMOVNTPSZ128mr	= 4337,
    X86_VMOVNTPSZ256mr	= 4338,
    X86_VMOVNTPSZmr	= 4339,
    X86_VMOVNTPSmr	= 4340,
    X86_VMOVPDI2DIZmr	= 4341,
    X86_VMOVPDI2DIZrr	= 4342,
    X86_VMOVPDI2DImr	= 4343,
    X86_VMOVPDI2DIrr	= 4344,
    X86_VMOVPQI2QImr	= 4345,
    X86_VMOVPQI2QIrr	= 4346,
    X86_VMOVPQIto64Zmr	= 4347,
    X86_VMOVPQIto64Zrr	= 4348,
    X86_VMOVPQIto64rr	= 4349,
    X86_VMOVQI2PQIZrm	= 4350,
    X86_VMOVQI2PQIrm	= 4351,
    X86_VMOVSDZmr	= 4352,
    X86_VMOVSDZrm	= 4353,
    X86_VMOVSDZrr	= 4354,
    X86_VMOVSDZrr_REV	= 4355,
    X86_VMOVSDZrrk	= 4356,
    X86_VMOVSDmr	= 4357,
    X86_VMOVSDrm	= 4358,
    X86_VMOVSDrr	= 4359,
    X86_VMOVSDrr_REV	= 4360,
    X86_VMOVSDto64Zmr	= 4361,
    X86_VMOVSDto64Zrr	= 4362,
    X86_VMOVSDto64mr	= 4363,
    X86_VMOVSDto64rr	= 4364,
    X86_VMOVSHDUPYrm	= 4365,
    X86_VMOVSHDUPYrr	= 4366,
    X86_VMOVSHDUPZrm	= 4367,
    X86_VMOVSHDUPZrr	= 4368,
    X86_VMOVSHDUPrm	= 4369,
    X86_VMOVSHDUPrr	= 4370,
    X86_VMOVSLDUPYrm	= 4371,
    X86_VMOVSLDUPYrr	= 4372,
    X86_VMOVSLDUPZrm	= 4373,
    X86_VMOVSLDUPZrr	= 4374,
    X86_VMOVSLDUPrm	= 4375,
    X86_VMOVSLDUPrr	= 4376,
    X86_VMOVSS2DIZmr	= 4377,
    X86_VMOVSS2DIZrr	= 4378,
    X86_VMOVSS2DImr	= 4379,
    X86_VMOVSS2DIrr	= 4380,
    X86_VMOVSSZmr	= 4381,
    X86_VMOVSSZrm	= 4382,
    X86_VMOVSSZrr	= 4383,
    X86_VMOVSSZrr_REV	= 4384,
    X86_VMOVSSZrrk	= 4385,
    X86_VMOVSSmr	= 4386,
    X86_VMOVSSrm	= 4387,
    X86_VMOVSSrr	= 4388,
    X86_VMOVSSrr_REV	= 4389,
    X86_VMOVUPDYmr	= 4390,
    X86_VMOVUPDYrm	= 4391,
    X86_VMOVUPDYrr	= 4392,
    X86_VMOVUPDYrr_REV	= 4393,
    X86_VMOVUPDZ128mr	= 4394,
    X86_VMOVUPDZ128mrk	= 4395,
    X86_VMOVUPDZ128rm	= 4396,
    X86_VMOVUPDZ128rmk	= 4397,
    X86_VMOVUPDZ128rmkz	= 4398,
    X86_VMOVUPDZ128rr	= 4399,
    X86_VMOVUPDZ128rr_alt	= 4400,
    X86_VMOVUPDZ128rrk	= 4401,
    X86_VMOVUPDZ128rrk_alt	= 4402,
    X86_VMOVUPDZ128rrkz	= 4403,
    X86_VMOVUPDZ128rrkz_alt	= 4404,
    X86_VMOVUPDZ256mr	= 4405,
    X86_VMOVUPDZ256mrk	= 4406,
    X86_VMOVUPDZ256rm	= 4407,
    X86_VMOVUPDZ256rmk	= 4408,
    X86_VMOVUPDZ256rmkz	= 4409,
    X86_VMOVUPDZ256rr	= 4410,
    X86_VMOVUPDZ256rr_alt	= 4411,
    X86_VMOVUPDZ256rrk	= 4412,
    X86_VMOVUPDZ256rrk_alt	= 4413,
    X86_VMOVUPDZ256rrkz	= 4414,
    X86_VMOVUPDZ256rrkz_alt	= 4415,
    X86_VMOVUPDZmr	= 4416,
    X86_VMOVUPDZmrk	= 4417,
    X86_VMOVUPDZrm	= 4418,
    X86_VMOVUPDZrmk	= 4419,
    X86_VMOVUPDZrmkz	= 4420,
    X86_VMOVUPDZrr	= 4421,
    X86_VMOVUPDZrr_alt	= 4422,
    X86_VMOVUPDZrrk	= 4423,
    X86_VMOVUPDZrrk_alt	= 4424,
    X86_VMOVUPDZrrkz	= 4425,
    X86_VMOVUPDZrrkz_alt	= 4426,
    X86_VMOVUPDmr	= 4427,
    X86_VMOVUPDrm	= 4428,
    X86_VMOVUPDrr	= 4429,
    X86_VMOVUPDrr_REV	= 4430,
    X86_VMOVUPSYmr	= 4431,
    X86_VMOVUPSYrm	= 4432,
    X86_VMOVUPSYrr	= 4433,
    X86_VMOVUPSYrr_REV	= 4434,
    X86_VMOVUPSZ128mr	= 4435,
    X86_VMOVUPSZ128mrk	= 4436,
    X86_VMOVUPSZ128rm	= 4437,
    X86_VMOVUPSZ128rmk	= 4438,
    X86_VMOVUPSZ128rmkz	= 4439,
    X86_VMOVUPSZ128rr	= 4440,
    X86_VMOVUPSZ128rr_alt	= 4441,
    X86_VMOVUPSZ128rrk	= 4442,
    X86_VMOVUPSZ128rrk_alt	= 4443,
    X86_VMOVUPSZ128rrkz	= 4444,
    X86_VMOVUPSZ128rrkz_alt	= 4445,
    X86_VMOVUPSZ256mr	= 4446,
    X86_VMOVUPSZ256mrk	= 4447,
    X86_VMOVUPSZ256rm	= 4448,
    X86_VMOVUPSZ256rmk	= 4449,
    X86_VMOVUPSZ256rmkz	= 4450,
    X86_VMOVUPSZ256rr	= 4451,
    X86_VMOVUPSZ256rr_alt	= 4452,
    X86_VMOVUPSZ256rrk	= 4453,
    X86_VMOVUPSZ256rrk_alt	= 4454,
    X86_VMOVUPSZ256rrkz	= 4455,
    X86_VMOVUPSZ256rrkz_alt	= 4456,
    X86_VMOVUPSZmr	= 4457,
    X86_VMOVUPSZmrk	= 4458,
    X86_VMOVUPSZrm	= 4459,
    X86_VMOVUPSZrmk	= 4460,
    X86_VMOVUPSZrmkz	= 4461,
    X86_VMOVUPSZrr	= 4462,
    X86_VMOVUPSZrr_alt	= 4463,
    X86_VMOVUPSZrrk	= 4464,
    X86_VMOVUPSZrrk_alt	= 4465,
    X86_VMOVUPSZrrkz	= 4466,
    X86_VMOVUPSZrrkz_alt	= 4467,
    X86_VMOVUPSmr	= 4468,
    X86_VMOVUPSrm	= 4469,
    X86_VMOVUPSrr	= 4470,
    X86_VMOVUPSrr_REV	= 4471,
    X86_VMOVZPQILo2PQIZrm	= 4472,
    X86_VMOVZPQILo2PQIZrr	= 4473,
    X86_VMOVZPQILo2PQIrm	= 4474,
    X86_VMOVZPQILo2PQIrr	= 4475,
    X86_VMOVZQI2PQIrm	= 4476,
    X86_VMOVZQI2PQIrr	= 4477,
    X86_VMPSADBWYrmi	= 4478,
    X86_VMPSADBWYrri	= 4479,
    X86_VMPSADBWrmi	= 4480,
    X86_VMPSADBWrri	= 4481,
    X86_VMPTRLDm	= 4482,
    X86_VMPTRSTm	= 4483,
    X86_VMREAD32rm	= 4484,
    X86_VMREAD32rr	= 4485,
    X86_VMREAD64rm	= 4486,
    X86_VMREAD64rr	= 4487,
    X86_VMRESUME	= 4488,
    X86_VMRUN32	= 4489,
    X86_VMRUN64	= 4490,
    X86_VMSAVE32	= 4491,
    X86_VMSAVE64	= 4492,
    X86_VMULPDYrm	= 4493,
    X86_VMULPDYrr	= 4494,
    X86_VMULPDZrm	= 4495,
    X86_VMULPDZrmb	= 4496,
    X86_VMULPDZrmbk	= 4497,
    X86_VMULPDZrmbkz	= 4498,
    X86_VMULPDZrmk	= 4499,
    X86_VMULPDZrmkz	= 4500,
    X86_VMULPDZrr	= 4501,
    X86_VMULPDZrrk	= 4502,
    X86_VMULPDZrrkz	= 4503,
    X86_VMULPDrm	= 4504,
    X86_VMULPDrr	= 4505,
    X86_VMULPSYrm	= 4506,
    X86_VMULPSYrr	= 4507,
    X86_VMULPSZrm	= 4508,
    X86_VMULPSZrmb	= 4509,
    X86_VMULPSZrmbk	= 4510,
    X86_VMULPSZrmbkz	= 4511,
    X86_VMULPSZrmk	= 4512,
    X86_VMULPSZrmkz	= 4513,
    X86_VMULPSZrr	= 4514,
    X86_VMULPSZrrk	= 4515,
    X86_VMULPSZrrkz	= 4516,
    X86_VMULPSrm	= 4517,
    X86_VMULPSrr	= 4518,
    X86_VMULSDZrm	= 4519,
    X86_VMULSDZrr	= 4520,
    X86_VMULSDrm	= 4521,
    X86_VMULSDrm_Int	= 4522,
    X86_VMULSDrr	= 4523,
    X86_VMULSDrr_Int	= 4524,
    X86_VMULSSZrm	= 4525,
    X86_VMULSSZrr	= 4526,
    X86_VMULSSrm	= 4527,
    X86_VMULSSrm_Int	= 4528,
    X86_VMULSSrr	= 4529,
    X86_VMULSSrr_Int	= 4530,
    X86_VMWRITE32rm	= 4531,
    X86_VMWRITE32rr	= 4532,
    X86_VMWRITE64rm	= 4533,
    X86_VMWRITE64rr	= 4534,
    X86_VMXOFF	= 4535,
    X86_VMXON	= 4536,
    X86_VORPDYrm	= 4537,
    X86_VORPDYrr	= 4538,
    X86_VORPDrm	= 4539,
    X86_VORPDrr	= 4540,
    X86_VORPSYrm	= 4541,
    X86_VORPSYrr	= 4542,
    X86_VORPSrm	= 4543,
    X86_VORPSrr	= 4544,
    X86_VPABSBrm128	= 4545,
    X86_VPABSBrm256	= 4546,
    X86_VPABSBrr128	= 4547,
    X86_VPABSBrr256	= 4548,
    X86_VPABSDZrm	= 4549,
    X86_VPABSDZrmb	= 4550,
    X86_VPABSDZrmbk	= 4551,
    X86_VPABSDZrmbkz	= 4552,
    X86_VPABSDZrmk	= 4553,
    X86_VPABSDZrmkz	= 4554,
    X86_VPABSDZrr	= 4555,
    X86_VPABSDZrrk	= 4556,
    X86_VPABSDZrrkz	= 4557,
    X86_VPABSDrm128	= 4558,
    X86_VPABSDrm256	= 4559,
    X86_VPABSDrr128	= 4560,
    X86_VPABSDrr256	= 4561,
    X86_VPABSQZrm	= 4562,
    X86_VPABSQZrmb	= 4563,
    X86_VPABSQZrmbk	= 4564,
    X86_VPABSQZrmbkz	= 4565,
    X86_VPABSQZrmk	= 4566,
    X86_VPABSQZrmkz	= 4567,
    X86_VPABSQZrr	= 4568,
    X86_VPABSQZrrk	= 4569,
    X86_VPABSQZrrkz	= 4570,
    X86_VPABSWrm128	= 4571,
    X86_VPABSWrm256	= 4572,
    X86_VPABSWrr128	= 4573,
    X86_VPABSWrr256	= 4574,
    X86_VPACKSSDWYrm	= 4575,
    X86_VPACKSSDWYrr	= 4576,
    X86_VPACKSSDWrm	= 4577,
    X86_VPACKSSDWrr	= 4578,
    X86_VPACKSSWBYrm	= 4579,
    X86_VPACKSSWBYrr	= 4580,
    X86_VPACKSSWBrm	= 4581,
    X86_VPACKSSWBrr	= 4582,
    X86_VPACKUSDWYrm	= 4583,
    X86_VPACKUSDWYrr	= 4584,
    X86_VPACKUSDWrm	= 4585,
    X86_VPACKUSDWrr	= 4586,
    X86_VPACKUSWBYrm	= 4587,
    X86_VPACKUSWBYrr	= 4588,
    X86_VPACKUSWBrm	= 4589,
    X86_VPACKUSWBrr	= 4590,
    X86_VPADDBYrm	= 4591,
    X86_VPADDBYrr	= 4592,
    X86_VPADDBrm	= 4593,
    X86_VPADDBrr	= 4594,
    X86_VPADDDYrm	= 4595,
    X86_VPADDDYrr	= 4596,
    X86_VPADDDZrm	= 4597,
    X86_VPADDDZrmb	= 4598,
    X86_VPADDDZrmbk	= 4599,
    X86_VPADDDZrmbkz	= 4600,
    X86_VPADDDZrmk	= 4601,
    X86_VPADDDZrmkz	= 4602,
    X86_VPADDDZrr	= 4603,
    X86_VPADDDZrrk	= 4604,
    X86_VPADDDZrrkz	= 4605,
    X86_VPADDDrm	= 4606,
    X86_VPADDDrr	= 4607,
    X86_VPADDQYrm	= 4608,
    X86_VPADDQYrr	= 4609,
    X86_VPADDQZrm	= 4610,
    X86_VPADDQZrmb	= 4611,
    X86_VPADDQZrmbk	= 4612,
    X86_VPADDQZrmbkz	= 4613,
    X86_VPADDQZrmk	= 4614,
    X86_VPADDQZrmkz	= 4615,
    X86_VPADDQZrr	= 4616,
    X86_VPADDQZrrk	= 4617,
    X86_VPADDQZrrkz	= 4618,
    X86_VPADDQrm	= 4619,
    X86_VPADDQrr	= 4620,
    X86_VPADDSBYrm	= 4621,
    X86_VPADDSBYrr	= 4622,
    X86_VPADDSBrm	= 4623,
    X86_VPADDSBrr	= 4624,
    X86_VPADDSWYrm	= 4625,
    X86_VPADDSWYrr	= 4626,
    X86_VPADDSWrm	= 4627,
    X86_VPADDSWrr	= 4628,
    X86_VPADDUSBYrm	= 4629,
    X86_VPADDUSBYrr	= 4630,
    X86_VPADDUSBrm	= 4631,
    X86_VPADDUSBrr	= 4632,
    X86_VPADDUSWYrm	= 4633,
    X86_VPADDUSWYrr	= 4634,
    X86_VPADDUSWrm	= 4635,
    X86_VPADDUSWrr	= 4636,
    X86_VPADDWYrm	= 4637,
    X86_VPADDWYrr	= 4638,
    X86_VPADDWrm	= 4639,
    X86_VPADDWrr	= 4640,
    X86_VPALIGNR128rm	= 4641,
    X86_VPALIGNR128rr	= 4642,
    X86_VPALIGNR256rm	= 4643,
    X86_VPALIGNR256rr	= 4644,
    X86_VPANDDZrm	= 4645,
    X86_VPANDDZrmb	= 4646,
    X86_VPANDDZrmbk	= 4647,
    X86_VPANDDZrmbkz	= 4648,
    X86_VPANDDZrmk	= 4649,
    X86_VPANDDZrmkz	= 4650,
    X86_VPANDDZrr	= 4651,
    X86_VPANDDZrrk	= 4652,
    X86_VPANDDZrrkz	= 4653,
    X86_VPANDNDZrm	= 4654,
    X86_VPANDNDZrmb	= 4655,
    X86_VPANDNDZrmbk	= 4656,
    X86_VPANDNDZrmbkz	= 4657,
    X86_VPANDNDZrmk	= 4658,
    X86_VPANDNDZrmkz	= 4659,
    X86_VPANDNDZrr	= 4660,
    X86_VPANDNDZrrk	= 4661,
    X86_VPANDNDZrrkz	= 4662,
    X86_VPANDNQZrm	= 4663,
    X86_VPANDNQZrmb	= 4664,
    X86_VPANDNQZrmbk	= 4665,
    X86_VPANDNQZrmbkz	= 4666,
    X86_VPANDNQZrmk	= 4667,
    X86_VPANDNQZrmkz	= 4668,
    X86_VPANDNQZrr	= 4669,
    X86_VPANDNQZrrk	= 4670,
    X86_VPANDNQZrrkz	= 4671,
    X86_VPANDNYrm	= 4672,
    X86_VPANDNYrr	= 4673,
    X86_VPANDNrm	= 4674,
    X86_VPANDNrr	= 4675,
    X86_VPANDQZrm	= 4676,
    X86_VPANDQZrmb	= 4677,
    X86_VPANDQZrmbk	= 4678,
    X86_VPANDQZrmbkz	= 4679,
    X86_VPANDQZrmk	= 4680,
    X86_VPANDQZrmkz	= 4681,
    X86_VPANDQZrr	= 4682,
    X86_VPANDQZrrk	= 4683,
    X86_VPANDQZrrkz	= 4684,
    X86_VPANDYrm	= 4685,
    X86_VPANDYrr	= 4686,
    X86_VPANDrm	= 4687,
    X86_VPANDrr	= 4688,
    X86_VPAVGBYrm	= 4689,
    X86_VPAVGBYrr	= 4690,
    X86_VPAVGBrm	= 4691,
    X86_VPAVGBrr	= 4692,
    X86_VPAVGWYrm	= 4693,
    X86_VPAVGWYrr	= 4694,
    X86_VPAVGWrm	= 4695,
    X86_VPAVGWrr	= 4696,
    X86_VPBLENDDYrmi	= 4697,
    X86_VPBLENDDYrri	= 4698,
    X86_VPBLENDDrmi	= 4699,
    X86_VPBLENDDrri	= 4700,
    X86_VPBLENDMDZrm	= 4701,
    X86_VPBLENDMDZrr	= 4702,
    X86_VPBLENDMQZrm	= 4703,
    X86_VPBLENDMQZrr	= 4704,
    X86_VPBLENDVBYrm	= 4705,
    X86_VPBLENDVBYrr	= 4706,
    X86_VPBLENDVBrm	= 4707,
    X86_VPBLENDVBrr	= 4708,
    X86_VPBLENDWYrmi	= 4709,
    X86_VPBLENDWYrri	= 4710,
    X86_VPBLENDWrmi	= 4711,
    X86_VPBLENDWrri	= 4712,
    X86_VPBROADCASTBYrm	= 4713,
    X86_VPBROADCASTBYrr	= 4714,
    X86_VPBROADCASTBrm	= 4715,
    X86_VPBROADCASTBrr	= 4716,
    X86_VPBROADCASTDYrm	= 4717,
    X86_VPBROADCASTDYrr	= 4718,
    X86_VPBROADCASTDZkrm	= 4719,
    X86_VPBROADCASTDZkrr	= 4720,
    X86_VPBROADCASTDZrm	= 4721,
    X86_VPBROADCASTDZrr	= 4722,
    X86_VPBROADCASTDrZkrr	= 4723,
    X86_VPBROADCASTDrZrr	= 4724,
    X86_VPBROADCASTDrm	= 4725,
    X86_VPBROADCASTDrr	= 4726,
    X86_VPBROADCASTMB2Qrr	= 4727,
    X86_VPBROADCASTMW2Drr	= 4728,
    X86_VPBROADCASTQYrm	= 4729,
    X86_VPBROADCASTQYrr	= 4730,
    X86_VPBROADCASTQZkrm	= 4731,
    X86_VPBROADCASTQZkrr	= 4732,
    X86_VPBROADCASTQZrm	= 4733,
    X86_VPBROADCASTQZrr	= 4734,
    X86_VPBROADCASTQrZkrr	= 4735,
    X86_VPBROADCASTQrZrr	= 4736,
    X86_VPBROADCASTQrm	= 4737,
    X86_VPBROADCASTQrr	= 4738,
    X86_VPBROADCASTWYrm	= 4739,
    X86_VPBROADCASTWYrr	= 4740,
    X86_VPBROADCASTWrm	= 4741,
    X86_VPBROADCASTWrr	= 4742,
    X86_VPCLMULQDQrm	= 4743,
    X86_VPCLMULQDQrr	= 4744,
    X86_VPCMOVmr	= 4745,
    X86_VPCMOVmrY	= 4746,
    X86_VPCMOVrm	= 4747,
    X86_VPCMOVrmY	= 4748,
    X86_VPCMOVrr	= 4749,
    X86_VPCMOVrrY	= 4750,
    X86_VPCMPDZrmi	= 4751,
    X86_VPCMPDZrmi_alt	= 4752,
    X86_VPCMPDZrmik_alt	= 4753,
    X86_VPCMPDZrri	= 4754,
    X86_VPCMPDZrri_alt	= 4755,
    X86_VPCMPDZrrik_alt	= 4756,
    X86_VPCMPEQBYrm	= 4757,
    X86_VPCMPEQBYrr	= 4758,
    X86_VPCMPEQBZ128rm	= 4759,
    X86_VPCMPEQBZ128rmk	= 4760,
    X86_VPCMPEQBZ128rr	= 4761,
    X86_VPCMPEQBZ128rrk	= 4762,
    X86_VPCMPEQBZ256rm	= 4763,
    X86_VPCMPEQBZ256rmk	= 4764,
    X86_VPCMPEQBZ256rr	= 4765,
    X86_VPCMPEQBZ256rrk	= 4766,
    X86_VPCMPEQBZrm	= 4767,
    X86_VPCMPEQBZrmk	= 4768,
    X86_VPCMPEQBZrr	= 4769,
    X86_VPCMPEQBZrrk	= 4770,
    X86_VPCMPEQBrm	= 4771,
    X86_VPCMPEQBrr	= 4772,
    X86_VPCMPEQDYrm	= 4773,
    X86_VPCMPEQDYrr	= 4774,
    X86_VPCMPEQDZ128rm	= 4775,
    X86_VPCMPEQDZ128rmb	= 4776,
    X86_VPCMPEQDZ128rmbk	= 4777,
    X86_VPCMPEQDZ128rmk	= 4778,
    X86_VPCMPEQDZ128rr	= 4779,
    X86_VPCMPEQDZ128rrk	= 4780,
    X86_VPCMPEQDZ256rm	= 4781,
    X86_VPCMPEQDZ256rmb	= 4782,
    X86_VPCMPEQDZ256rmbk	= 4783,
    X86_VPCMPEQDZ256rmk	= 4784,
    X86_VPCMPEQDZ256rr	= 4785,
    X86_VPCMPEQDZ256rrk	= 4786,
    X86_VPCMPEQDZrm	= 4787,
    X86_VPCMPEQDZrmb	= 4788,
    X86_VPCMPEQDZrmbk	= 4789,
    X86_VPCMPEQDZrmk	= 4790,
    X86_VPCMPEQDZrr	= 4791,
    X86_VPCMPEQDZrrk	= 4792,
    X86_VPCMPEQDrm	= 4793,
    X86_VPCMPEQDrr	= 4794,
    X86_VPCMPEQQYrm	= 4795,
    X86_VPCMPEQQYrr	= 4796,
    X86_VPCMPEQQZ128rm	= 4797,
    X86_VPCMPEQQZ128rmb	= 4798,
    X86_VPCMPEQQZ128rmbk	= 4799,
    X86_VPCMPEQQZ128rmk	= 4800,
    X86_VPCMPEQQZ128rr	= 4801,
    X86_VPCMPEQQZ128rrk	= 4802,
    X86_VPCMPEQQZ256rm	= 4803,
    X86_VPCMPEQQZ256rmb	= 4804,
    X86_VPCMPEQQZ256rmbk	= 4805,
    X86_VPCMPEQQZ256rmk	= 4806,
    X86_VPCMPEQQZ256rr	= 4807,
    X86_VPCMPEQQZ256rrk	= 4808,
    X86_VPCMPEQQZrm	= 4809,
    X86_VPCMPEQQZrmb	= 4810,
    X86_VPCMPEQQZrmbk	= 4811,
    X86_VPCMPEQQZrmk	= 4812,
    X86_VPCMPEQQZrr	= 4813,
    X86_VPCMPEQQZrrk	= 4814,
    X86_VPCMPEQQrm	= 4815,
    X86_VPCMPEQQrr	= 4816,
    X86_VPCMPEQWYrm	= 4817,
    X86_VPCMPEQWYrr	= 4818,
    X86_VPCMPEQWZ128rm	= 4819,
    X86_VPCMPEQWZ128rmk	= 4820,
    X86_VPCMPEQWZ128rr	= 4821,
    X86_VPCMPEQWZ128rrk	= 4822,
    X86_VPCMPEQWZ256rm	= 4823,
    X86_VPCMPEQWZ256rmk	= 4824,
    X86_VPCMPEQWZ256rr	= 4825,
    X86_VPCMPEQWZ256rrk	= 4826,
    X86_VPCMPEQWZrm	= 4827,
    X86_VPCMPEQWZrmk	= 4828,
    X86_VPCMPEQWZrr	= 4829,
    X86_VPCMPEQWZrrk	= 4830,
    X86_VPCMPEQWrm	= 4831,
    X86_VPCMPEQWrr	= 4832,
    X86_VPCMPESTRIMEM	= 4833,
    X86_VPCMPESTRIREG	= 4834,
    X86_VPCMPESTRIrm	= 4835,
    X86_VPCMPESTRIrr	= 4836,
    X86_VPCMPESTRM128MEM	= 4837,
    X86_VPCMPESTRM128REG	= 4838,
    X86_VPCMPESTRM128rm	= 4839,
    X86_VPCMPESTRM128rr	= 4840,
    X86_VPCMPGTBYrm	= 4841,
    X86_VPCMPGTBYrr	= 4842,
    X86_VPCMPGTBZ128rm	= 4843,
    X86_VPCMPGTBZ128rmk	= 4844,
    X86_VPCMPGTBZ128rr	= 4845,
    X86_VPCMPGTBZ128rrk	= 4846,
    X86_VPCMPGTBZ256rm	= 4847,
    X86_VPCMPGTBZ256rmk	= 4848,
    X86_VPCMPGTBZ256rr	= 4849,
    X86_VPCMPGTBZ256rrk	= 4850,
    X86_VPCMPGTBZrm	= 4851,
    X86_VPCMPGTBZrmk	= 4852,
    X86_VPCMPGTBZrr	= 4853,
    X86_VPCMPGTBZrrk	= 4854,
    X86_VPCMPGTBrm	= 4855,
    X86_VPCMPGTBrr	= 4856,
    X86_VPCMPGTDYrm	= 4857,
    X86_VPCMPGTDYrr	= 4858,
    X86_VPCMPGTDZ128rm	= 4859,
    X86_VPCMPGTDZ128rmb	= 4860,
    X86_VPCMPGTDZ128rmbk	= 4861,
    X86_VPCMPGTDZ128rmk	= 4862,
    X86_VPCMPGTDZ128rr	= 4863,
    X86_VPCMPGTDZ128rrk	= 4864,
    X86_VPCMPGTDZ256rm	= 4865,
    X86_VPCMPGTDZ256rmb	= 4866,
    X86_VPCMPGTDZ256rmbk	= 4867,
    X86_VPCMPGTDZ256rmk	= 4868,
    X86_VPCMPGTDZ256rr	= 4869,
    X86_VPCMPGTDZ256rrk	= 4870,
    X86_VPCMPGTDZrm	= 4871,
    X86_VPCMPGTDZrmb	= 4872,
    X86_VPCMPGTDZrmbk	= 4873,
    X86_VPCMPGTDZrmk	= 4874,
    X86_VPCMPGTDZrr	= 4875,
    X86_VPCMPGTDZrrk	= 4876,
    X86_VPCMPGTDrm	= 4877,
    X86_VPCMPGTDrr	= 4878,
    X86_VPCMPGTQYrm	= 4879,
    X86_VPCMPGTQYrr	= 4880,
    X86_VPCMPGTQZ128rm	= 4881,
    X86_VPCMPGTQZ128rmb	= 4882,
    X86_VPCMPGTQZ128rmbk	= 4883,
    X86_VPCMPGTQZ128rmk	= 4884,
    X86_VPCMPGTQZ128rr	= 4885,
    X86_VPCMPGTQZ128rrk	= 4886,
    X86_VPCMPGTQZ256rm	= 4887,
    X86_VPCMPGTQZ256rmb	= 4888,
    X86_VPCMPGTQZ256rmbk	= 4889,
    X86_VPCMPGTQZ256rmk	= 4890,
    X86_VPCMPGTQZ256rr	= 4891,
    X86_VPCMPGTQZ256rrk	= 4892,
    X86_VPCMPGTQZrm	= 4893,
    X86_VPCMPGTQZrmb	= 4894,
    X86_VPCMPGTQZrmbk	= 4895,
    X86_VPCMPGTQZrmk	= 4896,
    X86_VPCMPGTQZrr	= 4897,
    X86_VPCMPGTQZrrk	= 4898,
    X86_VPCMPGTQrm	= 4899,
    X86_VPCMPGTQrr	= 4900,
    X86_VPCMPGTWYrm	= 4901,
    X86_VPCMPGTWYrr	= 4902,
    X86_VPCMPGTWZ128rm	= 4903,
    X86_VPCMPGTWZ128rmk	= 4904,
    X86_VPCMPGTWZ128rr	= 4905,
    X86_VPCMPGTWZ128rrk	= 4906,
    X86_VPCMPGTWZ256rm	= 4907,
    X86_VPCMPGTWZ256rmk	= 4908,
    X86_VPCMPGTWZ256rr	= 4909,
    X86_VPCMPGTWZ256rrk	= 4910,
    X86_VPCMPGTWZrm	= 4911,
    X86_VPCMPGTWZrmk	= 4912,
    X86_VPCMPGTWZrr	= 4913,
    X86_VPCMPGTWZrrk	= 4914,
    X86_VPCMPGTWrm	= 4915,
    X86_VPCMPGTWrr	= 4916,
    X86_VPCMPISTRIMEM	= 4917,
    X86_VPCMPISTRIREG	= 4918,
    X86_VPCMPISTRIrm	= 4919,
    X86_VPCMPISTRIrr	= 4920,
    X86_VPCMPISTRM128MEM	= 4921,
    X86_VPCMPISTRM128REG	= 4922,
    X86_VPCMPISTRM128rm	= 4923,
    X86_VPCMPISTRM128rr	= 4924,
    X86_VPCMPQZrmi	= 4925,
    X86_VPCMPQZrmi_alt	= 4926,
    X86_VPCMPQZrmik_alt	= 4927,
    X86_VPCMPQZrri	= 4928,
    X86_VPCMPQZrri_alt	= 4929,
    X86_VPCMPQZrrik_alt	= 4930,
    X86_VPCMPUDZrmi	= 4931,
    X86_VPCMPUDZrmi_alt	= 4932,
    X86_VPCMPUDZrmik_alt	= 4933,
    X86_VPCMPUDZrri	= 4934,
    X86_VPCMPUDZrri_alt	= 4935,
    X86_VPCMPUDZrrik_alt	= 4936,
    X86_VPCMPUQZrmi	= 4937,
    X86_VPCMPUQZrmi_alt	= 4938,
    X86_VPCMPUQZrmik_alt	= 4939,
    X86_VPCMPUQZrri	= 4940,
    X86_VPCMPUQZrri_alt	= 4941,
    X86_VPCMPUQZrrik_alt	= 4942,
    X86_VPCOMBmi	= 4943,
    X86_VPCOMBri	= 4944,
    X86_VPCOMDmi	= 4945,
    X86_VPCOMDri	= 4946,
    X86_VPCOMQmi	= 4947,
    X86_VPCOMQri	= 4948,
    X86_VPCOMUBmi	= 4949,
    X86_VPCOMUBri	= 4950,
    X86_VPCOMUDmi	= 4951,
    X86_VPCOMUDri	= 4952,
    X86_VPCOMUQmi	= 4953,
    X86_VPCOMUQri	= 4954,
    X86_VPCOMUWmi	= 4955,
    X86_VPCOMUWri	= 4956,
    X86_VPCOMWmi	= 4957,
    X86_VPCOMWri	= 4958,
    X86_VPCONFLICTDrm	= 4959,
    X86_VPCONFLICTDrmb	= 4960,
    X86_VPCONFLICTDrmbk	= 4961,
    X86_VPCONFLICTDrmbkz	= 4962,
    X86_VPCONFLICTDrmk	= 4963,
    X86_VPCONFLICTDrmkz	= 4964,
    X86_VPCONFLICTDrr	= 4965,
    X86_VPCONFLICTDrrk	= 4966,
    X86_VPCONFLICTDrrkz	= 4967,
    X86_VPCONFLICTQrm	= 4968,
    X86_VPCONFLICTQrmb	= 4969,
    X86_VPCONFLICTQrmbk	= 4970,
    X86_VPCONFLICTQrmbkz	= 4971,
    X86_VPCONFLICTQrmk	= 4972,
    X86_VPCONFLICTQrmkz	= 4973,
    X86_VPCONFLICTQrr	= 4974,
    X86_VPCONFLICTQrrk	= 4975,
    X86_VPCONFLICTQrrkz	= 4976,
    X86_VPERM2F128rm	= 4977,
    X86_VPERM2F128rr	= 4978,
    X86_VPERM2I128rm	= 4979,
    X86_VPERM2I128rr	= 4980,
    X86_VPERMDYrm	= 4981,
    X86_VPERMDYrr	= 4982,
    X86_VPERMDZrm	= 4983,
    X86_VPERMDZrr	= 4984,
    X86_VPERMI2Drm	= 4985,
    X86_VPERMI2Drmk	= 4986,
    X86_VPERMI2Drmkz	= 4987,
    X86_VPERMI2Drr	= 4988,
    X86_VPERMI2Drrk	= 4989,
    X86_VPERMI2Drrkz	= 4990,
    X86_VPERMI2PDrm	= 4991,
    X86_VPERMI2PDrmk	= 4992,
    X86_VPERMI2PDrmkz	= 4993,
    X86_VPERMI2PDrr	= 4994,
    X86_VPERMI2PDrrk	= 4995,
    X86_VPERMI2PDrrkz	= 4996,
    X86_VPERMI2PSrm	= 4997,
    X86_VPERMI2PSrmk	= 4998,
    X86_VPERMI2PSrmkz	= 4999,
    X86_VPERMI2PSrr	= 5000,
    X86_VPERMI2PSrrk	= 5001,
    X86_VPERMI2PSrrkz	= 5002,
    X86_VPERMI2Qrm	= 5003,
    X86_VPERMI2Qrmk	= 5004,
    X86_VPERMI2Qrmkz	= 5005,
    X86_VPERMI2Qrr	= 5006,
    X86_VPERMI2Qrrk	= 5007,
    X86_VPERMI2Qrrkz	= 5008,
    X86_VPERMIL2PDmr	= 5009,
    X86_VPERMIL2PDmrY	= 5010,
    X86_VPERMIL2PDrm	= 5011,
    X86_VPERMIL2PDrmY	= 5012,
    X86_VPERMIL2PDrr	= 5013,
    X86_VPERMIL2PDrrY	= 5014,
    X86_VPERMIL2PSmr	= 5015,
    X86_VPERMIL2PSmrY	= 5016,
    X86_VPERMIL2PSrm	= 5017,
    X86_VPERMIL2PSrmY	= 5018,
    X86_VPERMIL2PSrr	= 5019,
    X86_VPERMIL2PSrrY	= 5020,
    X86_VPERMILPDYmi	= 5021,
    X86_VPERMILPDYri	= 5022,
    X86_VPERMILPDYrm	= 5023,
    X86_VPERMILPDYrr	= 5024,
    X86_VPERMILPDZmi	= 5025,
    X86_VPERMILPDZri	= 5026,
    X86_VPERMILPDmi	= 5027,
    X86_VPERMILPDri	= 5028,
    X86_VPERMILPDrm	= 5029,
    X86_VPERMILPDrr	= 5030,
    X86_VPERMILPSYmi	= 5031,
    X86_VPERMILPSYri	= 5032,
    X86_VPERMILPSYrm	= 5033,
    X86_VPERMILPSYrr	= 5034,
    X86_VPERMILPSZmi	= 5035,
    X86_VPERMILPSZri	= 5036,
    X86_VPERMILPSmi	= 5037,
    X86_VPERMILPSri	= 5038,
    X86_VPERMILPSrm	= 5039,
    X86_VPERMILPSrr	= 5040,
    X86_VPERMPDYmi	= 5041,
    X86_VPERMPDYri	= 5042,
    X86_VPERMPDZmi	= 5043,
    X86_VPERMPDZri	= 5044,
    X86_VPERMPDZrm	= 5045,
    X86_VPERMPDZrr	= 5046,
    X86_VPERMPSYrm	= 5047,
    X86_VPERMPSYrr	= 5048,
    X86_VPERMPSZrm	= 5049,
    X86_VPERMPSZrr	= 5050,
    X86_VPERMQYmi	= 5051,
    X86_VPERMQYri	= 5052,
    X86_VPERMQZmi	= 5053,
    X86_VPERMQZri	= 5054,
    X86_VPERMQZrm	= 5055,
    X86_VPERMQZrr	= 5056,
    X86_VPERMT2Drm	= 5057,
    X86_VPERMT2Drmk	= 5058,
    X86_VPERMT2Drmkz	= 5059,
    X86_VPERMT2Drr	= 5060,
    X86_VPERMT2Drrk	= 5061,
    X86_VPERMT2Drrkz	= 5062,
    X86_VPERMT2PDrm	= 5063,
    X86_VPERMT2PDrmk	= 5064,
    X86_VPERMT2PDrmkz	= 5065,
    X86_VPERMT2PDrr	= 5066,
    X86_VPERMT2PDrrk	= 5067,
    X86_VPERMT2PDrrkz	= 5068,
    X86_VPERMT2PSrm	= 5069,
    X86_VPERMT2PSrmk	= 5070,
    X86_VPERMT2PSrmkz	= 5071,
    X86_VPERMT2PSrr	= 5072,
    X86_VPERMT2PSrrk	= 5073,
    X86_VPERMT2PSrrkz	= 5074,
    X86_VPERMT2Qrm	= 5075,
    X86_VPERMT2Qrmk	= 5076,
    X86_VPERMT2Qrmkz	= 5077,
    X86_VPERMT2Qrr	= 5078,
    X86_VPERMT2Qrrk	= 5079,
    X86_VPERMT2Qrrkz	= 5080,
    X86_VPEXTRBmr	= 5081,
    X86_VPEXTRBrr	= 5082,
    X86_VPEXTRDmr	= 5083,
    X86_VPEXTRDrr	= 5084,
    X86_VPEXTRQmr	= 5085,
    X86_VPEXTRQrr	= 5086,
    X86_VPEXTRWmr	= 5087,
    X86_VPEXTRWri	= 5088,
    X86_VPEXTRWrr_REV	= 5089,
    X86_VPGATHERDDYrm	= 5090,
    X86_VPGATHERDDZrm	= 5091,
    X86_VPGATHERDDrm	= 5092,
    X86_VPGATHERDQYrm	= 5093,
    X86_VPGATHERDQZrm	= 5094,
    X86_VPGATHERDQrm	= 5095,
    X86_VPGATHERQDYrm	= 5096,
    X86_VPGATHERQDZrm	= 5097,
    X86_VPGATHERQDrm	= 5098,
    X86_VPGATHERQQYrm	= 5099,
    X86_VPGATHERQQZrm	= 5100,
    X86_VPGATHERQQrm	= 5101,
    X86_VPHADDBDrm	= 5102,
    X86_VPHADDBDrr	= 5103,
    X86_VPHADDBQrm	= 5104,
    X86_VPHADDBQrr	= 5105,
    X86_VPHADDBWrm	= 5106,
    X86_VPHADDBWrr	= 5107,
    X86_VPHADDDQrm	= 5108,
    X86_VPHADDDQrr	= 5109,
    X86_VPHADDDYrm	= 5110,
    X86_VPHADDDYrr	= 5111,
    X86_VPHADDDrm	= 5112,
    X86_VPHADDDrr	= 5113,
    X86_VPHADDSWrm128	= 5114,
    X86_VPHADDSWrm256	= 5115,
    X86_VPHADDSWrr128	= 5116,
    X86_VPHADDSWrr256	= 5117,
    X86_VPHADDUBDrm	= 5118,
    X86_VPHADDUBDrr	= 5119,
    X86_VPHADDUBQrm	= 5120,
    X86_VPHADDUBQrr	= 5121,
    X86_VPHADDUBWrm	= 5122,
    X86_VPHADDUBWrr	= 5123,
    X86_VPHADDUDQrm	= 5124,
    X86_VPHADDUDQrr	= 5125,
    X86_VPHADDUWDrm	= 5126,
    X86_VPHADDUWDrr	= 5127,
    X86_VPHADDUWQrm	= 5128,
    X86_VPHADDUWQrr	= 5129,
    X86_VPHADDWDrm	= 5130,
    X86_VPHADDWDrr	= 5131,
    X86_VPHADDWQrm	= 5132,
    X86_VPHADDWQrr	= 5133,
    X86_VPHADDWYrm	= 5134,
    X86_VPHADDWYrr	= 5135,
    X86_VPHADDWrm	= 5136,
    X86_VPHADDWrr	= 5137,
    X86_VPHMINPOSUWrm128	= 5138,
    X86_VPHMINPOSUWrr128	= 5139,
    X86_VPHSUBBWrm	= 5140,
    X86_VPHSUBBWrr	= 5141,
    X86_VPHSUBDQrm	= 5142,
    X86_VPHSUBDQrr	= 5143,
    X86_VPHSUBDYrm	= 5144,
    X86_VPHSUBDYrr	= 5145,
    X86_VPHSUBDrm	= 5146,
    X86_VPHSUBDrr	= 5147,
    X86_VPHSUBSWrm128	= 5148,
    X86_VPHSUBSWrm256	= 5149,
    X86_VPHSUBSWrr128	= 5150,
    X86_VPHSUBSWrr256	= 5151,
    X86_VPHSUBWDrm	= 5152,
    X86_VPHSUBWDrr	= 5153,
    X86_VPHSUBWYrm	= 5154,
    X86_VPHSUBWYrr	= 5155,
    X86_VPHSUBWrm	= 5156,
    X86_VPHSUBWrr	= 5157,
    X86_VPINSRBrm	= 5158,
    X86_VPINSRBrr	= 5159,
    X86_VPINSRDrm	= 5160,
    X86_VPINSRDrr	= 5161,
    X86_VPINSRQrm	= 5162,
    X86_VPINSRQrr	= 5163,
    X86_VPINSRWrmi	= 5164,
    X86_VPINSRWrri	= 5165,
    X86_VPLZCNTDrm	= 5166,
    X86_VPLZCNTDrmb	= 5167,
    X86_VPLZCNTDrmbk	= 5168,
    X86_VPLZCNTDrmbkz	= 5169,
    X86_VPLZCNTDrmk	= 5170,
    X86_VPLZCNTDrmkz	= 5171,
    X86_VPLZCNTDrr	= 5172,
    X86_VPLZCNTDrrk	= 5173,
    X86_VPLZCNTDrrkz	= 5174,
    X86_VPLZCNTQrm	= 5175,
    X86_VPLZCNTQrmb	= 5176,
    X86_VPLZCNTQrmbk	= 5177,
    X86_VPLZCNTQrmbkz	= 5178,
    X86_VPLZCNTQrmk	= 5179,
    X86_VPLZCNTQrmkz	= 5180,
    X86_VPLZCNTQrr	= 5181,
    X86_VPLZCNTQrrk	= 5182,
    X86_VPLZCNTQrrkz	= 5183,
    X86_VPMACSDDrm	= 5184,
    X86_VPMACSDDrr	= 5185,
    X86_VPMACSDQHrm	= 5186,
    X86_VPMACSDQHrr	= 5187,
    X86_VPMACSDQLrm	= 5188,
    X86_VPMACSDQLrr	= 5189,
    X86_VPMACSSDDrm	= 5190,
    X86_VPMACSSDDrr	= 5191,
    X86_VPMACSSDQHrm	= 5192,
    X86_VPMACSSDQHrr	= 5193,
    X86_VPMACSSDQLrm	= 5194,
    X86_VPMACSSDQLrr	= 5195,
    X86_VPMACSSWDrm	= 5196,
    X86_VPMACSSWDrr	= 5197,
    X86_VPMACSSWWrm	= 5198,
    X86_VPMACSSWWrr	= 5199,
    X86_VPMACSWDrm	= 5200,
    X86_VPMACSWDrr	= 5201,
    X86_VPMACSWWrm	= 5202,
    X86_VPMACSWWrr	= 5203,
    X86_VPMADCSSWDrm	= 5204,
    X86_VPMADCSSWDrr	= 5205,
    X86_VPMADCSWDrm	= 5206,
    X86_VPMADCSWDrr	= 5207,
    X86_VPMADDUBSWrm128	= 5208,
    X86_VPMADDUBSWrm256	= 5209,
    X86_VPMADDUBSWrr128	= 5210,
    X86_VPMADDUBSWrr256	= 5211,
    X86_VPMADDWDYrm	= 5212,
    X86_VPMADDWDYrr	= 5213,
    X86_VPMADDWDrm	= 5214,
    X86_VPMADDWDrr	= 5215,
    X86_VPMASKMOVDYmr	= 5216,
    X86_VPMASKMOVDYrm	= 5217,
    X86_VPMASKMOVDmr	= 5218,
    X86_VPMASKMOVDrm	= 5219,
    X86_VPMASKMOVQYmr	= 5220,
    X86_VPMASKMOVQYrm	= 5221,
    X86_VPMASKMOVQmr	= 5222,
    X86_VPMASKMOVQrm	= 5223,
    X86_VPMAXSBYrm	= 5224,
    X86_VPMAXSBYrr	= 5225,
    X86_VPMAXSBrm	= 5226,
    X86_VPMAXSBrr	= 5227,
    X86_VPMAXSDYrm	= 5228,
    X86_VPMAXSDYrr	= 5229,
    X86_VPMAXSDZrm	= 5230,
    X86_VPMAXSDZrmb	= 5231,
    X86_VPMAXSDZrmbk	= 5232,
    X86_VPMAXSDZrmbkz	= 5233,
    X86_VPMAXSDZrmk	= 5234,
    X86_VPMAXSDZrmkz	= 5235,
    X86_VPMAXSDZrr	= 5236,
    X86_VPMAXSDZrrk	= 5237,
    X86_VPMAXSDZrrkz	= 5238,
    X86_VPMAXSDrm	= 5239,
    X86_VPMAXSDrr	= 5240,
    X86_VPMAXSQZrm	= 5241,
    X86_VPMAXSQZrmb	= 5242,
    X86_VPMAXSQZrmbk	= 5243,
    X86_VPMAXSQZrmbkz	= 5244,
    X86_VPMAXSQZrmk	= 5245,
    X86_VPMAXSQZrmkz	= 5246,
    X86_VPMAXSQZrr	= 5247,
    X86_VPMAXSQZrrk	= 5248,
    X86_VPMAXSQZrrkz	= 5249,
    X86_VPMAXSWYrm	= 5250,
    X86_VPMAXSWYrr	= 5251,
    X86_VPMAXSWrm	= 5252,
    X86_VPMAXSWrr	= 5253,
    X86_VPMAXUBYrm	= 5254,
    X86_VPMAXUBYrr	= 5255,
    X86_VPMAXUBrm	= 5256,
    X86_VPMAXUBrr	= 5257,
    X86_VPMAXUDYrm	= 5258,
    X86_VPMAXUDYrr	= 5259,
    X86_VPMAXUDZrm	= 5260,
    X86_VPMAXUDZrmb	= 5261,
    X86_VPMAXUDZrmbk	= 5262,
    X86_VPMAXUDZrmbkz	= 5263,
    X86_VPMAXUDZrmk	= 5264,
    X86_VPMAXUDZrmkz	= 5265,
    X86_VPMAXUDZrr	= 5266,
    X86_VPMAXUDZrrk	= 5267,
    X86_VPMAXUDZrrkz	= 5268,
    X86_VPMAXUDrm	= 5269,
    X86_VPMAXUDrr	= 5270,
    X86_VPMAXUQZrm	= 5271,
    X86_VPMAXUQZrmb	= 5272,
    X86_VPMAXUQZrmbk	= 5273,
    X86_VPMAXUQZrmbkz	= 5274,
    X86_VPMAXUQZrmk	= 5275,
    X86_VPMAXUQZrmkz	= 5276,
    X86_VPMAXUQZrr	= 5277,
    X86_VPMAXUQZrrk	= 5278,
    X86_VPMAXUQZrrkz	= 5279,
    X86_VPMAXUWYrm	= 5280,
    X86_VPMAXUWYrr	= 5281,
    X86_VPMAXUWrm	= 5282,
    X86_VPMAXUWrr	= 5283,
    X86_VPMINSBYrm	= 5284,
    X86_VPMINSBYrr	= 5285,
    X86_VPMINSBrm	= 5286,
    X86_VPMINSBrr	= 5287,
    X86_VPMINSDYrm	= 5288,
    X86_VPMINSDYrr	= 5289,
    X86_VPMINSDZrm	= 5290,
    X86_VPMINSDZrmb	= 5291,
    X86_VPMINSDZrmbk	= 5292,
    X86_VPMINSDZrmbkz	= 5293,
    X86_VPMINSDZrmk	= 5294,
    X86_VPMINSDZrmkz	= 5295,
    X86_VPMINSDZrr	= 5296,
    X86_VPMINSDZrrk	= 5297,
    X86_VPMINSDZrrkz	= 5298,
    X86_VPMINSDrm	= 5299,
    X86_VPMINSDrr	= 5300,
    X86_VPMINSQZrm	= 5301,
    X86_VPMINSQZrmb	= 5302,
    X86_VPMINSQZrmbk	= 5303,
    X86_VPMINSQZrmbkz	= 5304,
    X86_VPMINSQZrmk	= 5305,
    X86_VPMINSQZrmkz	= 5306,
    X86_VPMINSQZrr	= 5307,
    X86_VPMINSQZrrk	= 5308,
    X86_VPMINSQZrrkz	= 5309,
    X86_VPMINSWYrm	= 5310,
    X86_VPMINSWYrr	= 5311,
    X86_VPMINSWrm	= 5312,
    X86_VPMINSWrr	= 5313,
    X86_VPMINUBYrm	= 5314,
    X86_VPMINUBYrr	= 5315,
    X86_VPMINUBrm	= 5316,
    X86_VPMINUBrr	= 5317,
    X86_VPMINUDYrm	= 5318,
    X86_VPMINUDYrr	= 5319,
    X86_VPMINUDZrm	= 5320,
    X86_VPMINUDZrmb	= 5321,
    X86_VPMINUDZrmbk	= 5322,
    X86_VPMINUDZrmbkz	= 5323,
    X86_VPMINUDZrmk	= 5324,
    X86_VPMINUDZrmkz	= 5325,
    X86_VPMINUDZrr	= 5326,
    X86_VPMINUDZrrk	= 5327,
    X86_VPMINUDZrrkz	= 5328,
    X86_VPMINUDrm	= 5329,
    X86_VPMINUDrr	= 5330,
    X86_VPMINUQZrm	= 5331,
    X86_VPMINUQZrmb	= 5332,
    X86_VPMINUQZrmbk	= 5333,
    X86_VPMINUQZrmbkz	= 5334,
    X86_VPMINUQZrmk	= 5335,
    X86_VPMINUQZrmkz	= 5336,
    X86_VPMINUQZrr	= 5337,
    X86_VPMINUQZrrk	= 5338,
    X86_VPMINUQZrrkz	= 5339,
    X86_VPMINUWYrm	= 5340,
    X86_VPMINUWYrr	= 5341,
    X86_VPMINUWrm	= 5342,
    X86_VPMINUWrr	= 5343,
    X86_VPMOVDBmr	= 5344,
    X86_VPMOVDBmrk	= 5345,
    X86_VPMOVDBrr	= 5346,
    X86_VPMOVDBrrk	= 5347,
    X86_VPMOVDBrrkz	= 5348,
    X86_VPMOVDWmr	= 5349,
    X86_VPMOVDWmrk	= 5350,
    X86_VPMOVDWrr	= 5351,
    X86_VPMOVDWrrk	= 5352,
    X86_VPMOVDWrrkz	= 5353,
    X86_VPMOVMSKBYrr	= 5354,
    X86_VPMOVMSKBrr	= 5355,
    X86_VPMOVQBmr	= 5356,
    X86_VPMOVQBmrk	= 5357,
    X86_VPMOVQBrr	= 5358,
    X86_VPMOVQBrrk	= 5359,
    X86_VPMOVQBrrkz	= 5360,
    X86_VPMOVQDmr	= 5361,
    X86_VPMOVQDmrk	= 5362,
    X86_VPMOVQDrr	= 5363,
    X86_VPMOVQDrrk	= 5364,
    X86_VPMOVQDrrkz	= 5365,
    X86_VPMOVQWmr	= 5366,
    X86_VPMOVQWmrk	= 5367,
    X86_VPMOVQWrr	= 5368,
    X86_VPMOVQWrrk	= 5369,
    X86_VPMOVQWrrkz	= 5370,
    X86_VPMOVSDBmr	= 5371,
    X86_VPMOVSDBmrk	= 5372,
    X86_VPMOVSDBrr	= 5373,
    X86_VPMOVSDBrrk	= 5374,
    X86_VPMOVSDBrrkz	= 5375,
    X86_VPMOVSDWmr	= 5376,
    X86_VPMOVSDWmrk	= 5377,
    X86_VPMOVSDWrr	= 5378,
    X86_VPMOVSDWrrk	= 5379,
    X86_VPMOVSDWrrkz	= 5380,
    X86_VPMOVSQBmr	= 5381,
    X86_VPMOVSQBmrk	= 5382,
    X86_VPMOVSQBrr	= 5383,
    X86_VPMOVSQBrrk	= 5384,
    X86_VPMOVSQBrrkz	= 5385,
    X86_VPMOVSQDmr	= 5386,
    X86_VPMOVSQDmrk	= 5387,
    X86_VPMOVSQDrr	= 5388,
    X86_VPMOVSQDrrk	= 5389,
    X86_VPMOVSQDrrkz	= 5390,
    X86_VPMOVSQWmr	= 5391,
    X86_VPMOVSQWmrk	= 5392,
    X86_VPMOVSQWrr	= 5393,
    X86_VPMOVSQWrrk	= 5394,
    X86_VPMOVSQWrrkz	= 5395,
    X86_VPMOVSXBDYrm	= 5396,
    X86_VPMOVSXBDYrr	= 5397,
    X86_VPMOVSXBDZrm	= 5398,
    X86_VPMOVSXBDZrmk	= 5399,
    X86_VPMOVSXBDZrmkz	= 5400,
    X86_VPMOVSXBDZrr	= 5401,
    X86_VPMOVSXBDZrrk	= 5402,
    X86_VPMOVSXBDZrrkz	= 5403,
    X86_VPMOVSXBDrm	= 5404,
    X86_VPMOVSXBDrr	= 5405,
    X86_VPMOVSXBQYrm	= 5406,
    X86_VPMOVSXBQYrr	= 5407,
    X86_VPMOVSXBQZrm	= 5408,
    X86_VPMOVSXBQZrmk	= 5409,
    X86_VPMOVSXBQZrmkz	= 5410,
    X86_VPMOVSXBQZrr	= 5411,
    X86_VPMOVSXBQZrrk	= 5412,
    X86_VPMOVSXBQZrrkz	= 5413,
    X86_VPMOVSXBQrm	= 5414,
    X86_VPMOVSXBQrr	= 5415,
    X86_VPMOVSXBWYrm	= 5416,
    X86_VPMOVSXBWYrr	= 5417,
    X86_VPMOVSXBWrm	= 5418,
    X86_VPMOVSXBWrr	= 5419,
    X86_VPMOVSXDQYrm	= 5420,
    X86_VPMOVSXDQYrr	= 5421,
    X86_VPMOVSXDQZrm	= 5422,
    X86_VPMOVSXDQZrmk	= 5423,
    X86_VPMOVSXDQZrmkz	= 5424,
    X86_VPMOVSXDQZrr	= 5425,
    X86_VPMOVSXDQZrrk	= 5426,
    X86_VPMOVSXDQZrrkz	= 5427,
    X86_VPMOVSXDQrm	= 5428,
    X86_VPMOVSXDQrr	= 5429,
    X86_VPMOVSXWDYrm	= 5430,
    X86_VPMOVSXWDYrr	= 5431,
    X86_VPMOVSXWDZrm	= 5432,
    X86_VPMOVSXWDZrmk	= 5433,
    X86_VPMOVSXWDZrmkz	= 5434,
    X86_VPMOVSXWDZrr	= 5435,
    X86_VPMOVSXWDZrrk	= 5436,
    X86_VPMOVSXWDZrrkz	= 5437,
    X86_VPMOVSXWDrm	= 5438,
    X86_VPMOVSXWDrr	= 5439,
    X86_VPMOVSXWQYrm	= 5440,
    X86_VPMOVSXWQYrr	= 5441,
    X86_VPMOVSXWQZrm	= 5442,
    X86_VPMOVSXWQZrmk	= 5443,
    X86_VPMOVSXWQZrmkz	= 5444,
    X86_VPMOVSXWQZrr	= 5445,
    X86_VPMOVSXWQZrrk	= 5446,
    X86_VPMOVSXWQZrrkz	= 5447,
    X86_VPMOVSXWQrm	= 5448,
    X86_VPMOVSXWQrr	= 5449,
    X86_VPMOVUSDBmr	= 5450,
    X86_VPMOVUSDBmrk	= 5451,
    X86_VPMOVUSDBrr	= 5452,
    X86_VPMOVUSDBrrk	= 5453,
    X86_VPMOVUSDBrrkz	= 5454,
    X86_VPMOVUSDWmr	= 5455,
    X86_VPMOVUSDWmrk	= 5456,
    X86_VPMOVUSDWrr	= 5457,
    X86_VPMOVUSDWrrk	= 5458,
    X86_VPMOVUSDWrrkz	= 5459,
    X86_VPMOVUSQBmr	= 5460,
    X86_VPMOVUSQBmrk	= 5461,
    X86_VPMOVUSQBrr	= 5462,
    X86_VPMOVUSQBrrk	= 5463,
    X86_VPMOVUSQBrrkz	= 5464,
    X86_VPMOVUSQDmr	= 5465,
    X86_VPMOVUSQDmrk	= 5466,
    X86_VPMOVUSQDrr	= 5467,
    X86_VPMOVUSQDrrk	= 5468,
    X86_VPMOVUSQDrrkz	= 5469,
    X86_VPMOVUSQWmr	= 5470,
    X86_VPMOVUSQWmrk	= 5471,
    X86_VPMOVUSQWrr	= 5472,
    X86_VPMOVUSQWrrk	= 5473,
    X86_VPMOVUSQWrrkz	= 5474,
    X86_VPMOVZXBDYrm	= 5475,
    X86_VPMOVZXBDYrr	= 5476,
    X86_VPMOVZXBDZrm	= 5477,
    X86_VPMOVZXBDZrmk	= 5478,
    X86_VPMOVZXBDZrmkz	= 5479,
    X86_VPMOVZXBDZrr	= 5480,
    X86_VPMOVZXBDZrrk	= 5481,
    X86_VPMOVZXBDZrrkz	= 5482,
    X86_VPMOVZXBDrm	= 5483,
    X86_VPMOVZXBDrr	= 5484,
    X86_VPMOVZXBQYrm	= 5485,
    X86_VPMOVZXBQYrr	= 5486,
    X86_VPMOVZXBQZrm	= 5487,
    X86_VPMOVZXBQZrmk	= 5488,
    X86_VPMOVZXBQZrmkz	= 5489,
    X86_VPMOVZXBQZrr	= 5490,
    X86_VPMOVZXBQZrrk	= 5491,
    X86_VPMOVZXBQZrrkz	= 5492,
    X86_VPMOVZXBQrm	= 5493,
    X86_VPMOVZXBQrr	= 5494,
    X86_VPMOVZXBWYrm	= 5495,
    X86_VPMOVZXBWYrr	= 5496,
    X86_VPMOVZXBWrm	= 5497,
    X86_VPMOVZXBWrr	= 5498,
    X86_VPMOVZXDQYrm	= 5499,
    X86_VPMOVZXDQYrr	= 5500,
    X86_VPMOVZXDQZrm	= 5501,
    X86_VPMOVZXDQZrmk	= 5502,
    X86_VPMOVZXDQZrmkz	= 5503,
    X86_VPMOVZXDQZrr	= 5504,
    X86_VPMOVZXDQZrrk	= 5505,
    X86_VPMOVZXDQZrrkz	= 5506,
    X86_VPMOVZXDQrm	= 5507,
    X86_VPMOVZXDQrr	= 5508,
    X86_VPMOVZXWDYrm	= 5509,
    X86_VPMOVZXWDYrr	= 5510,
    X86_VPMOVZXWDZrm	= 5511,
    X86_VPMOVZXWDZrmk	= 5512,
    X86_VPMOVZXWDZrmkz	= 5513,
    X86_VPMOVZXWDZrr	= 5514,
    X86_VPMOVZXWDZrrk	= 5515,
    X86_VPMOVZXWDZrrkz	= 5516,
    X86_VPMOVZXWDrm	= 5517,
    X86_VPMOVZXWDrr	= 5518,
    X86_VPMOVZXWQYrm	= 5519,
    X86_VPMOVZXWQYrr	= 5520,
    X86_VPMOVZXWQZrm	= 5521,
    X86_VPMOVZXWQZrmk	= 5522,
    X86_VPMOVZXWQZrmkz	= 5523,
    X86_VPMOVZXWQZrr	= 5524,
    X86_VPMOVZXWQZrrk	= 5525,
    X86_VPMOVZXWQZrrkz	= 5526,
    X86_VPMOVZXWQrm	= 5527,
    X86_VPMOVZXWQrr	= 5528,
    X86_VPMULDQYrm	= 5529,
    X86_VPMULDQYrr	= 5530,
    X86_VPMULDQZrm	= 5531,
    X86_VPMULDQZrmb	= 5532,
    X86_VPMULDQZrmbk	= 5533,
    X86_VPMULDQZrmbkz	= 5534,
    X86_VPMULDQZrmk	= 5535,
    X86_VPMULDQZrmkz	= 5536,
    X86_VPMULDQZrr	= 5537,
    X86_VPMULDQZrrk	= 5538,
    X86_VPMULDQZrrkz	= 5539,
    X86_VPMULDQrm	= 5540,
    X86_VPMULDQrr	= 5541,
    X86_VPMULHRSWrm128	= 5542,
    X86_VPMULHRSWrm256	= 5543,
    X86_VPMULHRSWrr128	= 5544,
    X86_VPMULHRSWrr256	= 5545,
    X86_VPMULHUWYrm	= 5546,
    X86_VPMULHUWYrr	= 5547,
    X86_VPMULHUWrm	= 5548,
    X86_VPMULHUWrr	= 5549,
    X86_VPMULHWYrm	= 5550,
    X86_VPMULHWYrr	= 5551,
    X86_VPMULHWrm	= 5552,
    X86_VPMULHWrr	= 5553,
    X86_VPMULLDYrm	= 5554,
    X86_VPMULLDYrr	= 5555,
    X86_VPMULLDZrm	= 5556,
    X86_VPMULLDZrmb	= 5557,
    X86_VPMULLDZrmbk	= 5558,
    X86_VPMULLDZrmbkz	= 5559,
    X86_VPMULLDZrmk	= 5560,
    X86_VPMULLDZrmkz	= 5561,
    X86_VPMULLDZrr	= 5562,
    X86_VPMULLDZrrk	= 5563,
    X86_VPMULLDZrrkz	= 5564,
    X86_VPMULLDrm	= 5565,
    X86_VPMULLDrr	= 5566,
    X86_VPMULLWYrm	= 5567,
    X86_VPMULLWYrr	= 5568,
    X86_VPMULLWrm	= 5569,
    X86_VPMULLWrr	= 5570,
    X86_VPMULUDQYrm	= 5571,
    X86_VPMULUDQYrr	= 5572,
    X86_VPMULUDQZrm	= 5573,
    X86_VPMULUDQZrmb	= 5574,
    X86_VPMULUDQZrmbk	= 5575,
    X86_VPMULUDQZrmbkz	= 5576,
    X86_VPMULUDQZrmk	= 5577,
    X86_VPMULUDQZrmkz	= 5578,
    X86_VPMULUDQZrr	= 5579,
    X86_VPMULUDQZrrk	= 5580,
    X86_VPMULUDQZrrkz	= 5581,
    X86_VPMULUDQrm	= 5582,
    X86_VPMULUDQrr	= 5583,
    X86_VPORDZrm	= 5584,
    X86_VPORDZrmb	= 5585,
    X86_VPORDZrmbk	= 5586,
    X86_VPORDZrmbkz	= 5587,
    X86_VPORDZrmk	= 5588,
    X86_VPORDZrmkz	= 5589,
    X86_VPORDZrr	= 5590,
    X86_VPORDZrrk	= 5591,
    X86_VPORDZrrkz	= 5592,
    X86_VPORQZrm	= 5593,
    X86_VPORQZrmb	= 5594,
    X86_VPORQZrmbk	= 5595,
    X86_VPORQZrmbkz	= 5596,
    X86_VPORQZrmk	= 5597,
    X86_VPORQZrmkz	= 5598,
    X86_VPORQZrr	= 5599,
    X86_VPORQZrrk	= 5600,
    X86_VPORQZrrkz	= 5601,
    X86_VPORYrm	= 5602,
    X86_VPORYrr	= 5603,
    X86_VPORrm	= 5604,
    X86_VPORrr	= 5605,
    X86_VPPERMmr	= 5606,
    X86_VPPERMrm	= 5607,
    X86_VPPERMrr	= 5608,
    X86_VPROTBmi	= 5609,
    X86_VPROTBmr	= 5610,
    X86_VPROTBri	= 5611,
    X86_VPROTBrm	= 5612,
    X86_VPROTBrr	= 5613,
    X86_VPROTDmi	= 5614,
    X86_VPROTDmr	= 5615,
    X86_VPROTDri	= 5616,
    X86_VPROTDrm	= 5617,
    X86_VPROTDrr	= 5618,
    X86_VPROTQmi	= 5619,
    X86_VPROTQmr	= 5620,
    X86_VPROTQri	= 5621,
    X86_VPROTQrm	= 5622,
    X86_VPROTQrr	= 5623,
    X86_VPROTWmi	= 5624,
    X86_VPROTWmr	= 5625,
    X86_VPROTWri	= 5626,
    X86_VPROTWrm	= 5627,
    X86_VPROTWrr	= 5628,
    X86_VPSADBWYrm	= 5629,
    X86_VPSADBWYrr	= 5630,
    X86_VPSADBWrm	= 5631,
    X86_VPSADBWrr	= 5632,
    X86_VPSCATTERDDZmr	= 5633,
    X86_VPSCATTERDQZmr	= 5634,
    X86_VPSCATTERQDZmr	= 5635,
    X86_VPSCATTERQQZmr	= 5636,
    X86_VPSHABmr	= 5637,
    X86_VPSHABrm	= 5638,
    X86_VPSHABrr	= 5639,
    X86_VPSHADmr	= 5640,
    X86_VPSHADrm	= 5641,
    X86_VPSHADrr	= 5642,
    X86_VPSHAQmr	= 5643,
    X86_VPSHAQrm	= 5644,
    X86_VPSHAQrr	= 5645,
    X86_VPSHAWmr	= 5646,
    X86_VPSHAWrm	= 5647,
    X86_VPSHAWrr	= 5648,
    X86_VPSHLBmr	= 5649,
    X86_VPSHLBrm	= 5650,
    X86_VPSHLBrr	= 5651,
    X86_VPSHLDmr	= 5652,
    X86_VPSHLDrm	= 5653,
    X86_VPSHLDrr	= 5654,
    X86_VPSHLQmr	= 5655,
    X86_VPSHLQrm	= 5656,
    X86_VPSHLQrr	= 5657,
    X86_VPSHLWmr	= 5658,
    X86_VPSHLWrm	= 5659,
    X86_VPSHLWrr	= 5660,
    X86_VPSHUFBYrm	= 5661,
    X86_VPSHUFBYrr	= 5662,
    X86_VPSHUFBrm	= 5663,
    X86_VPSHUFBrr	= 5664,
    X86_VPSHUFDYmi	= 5665,
    X86_VPSHUFDYri	= 5666,
    X86_VPSHUFDZmi	= 5667,
    X86_VPSHUFDZri	= 5668,
    X86_VPSHUFDmi	= 5669,
    X86_VPSHUFDri	= 5670,
    X86_VPSHUFHWYmi	= 5671,
    X86_VPSHUFHWYri	= 5672,
    X86_VPSHUFHWmi	= 5673,
    X86_VPSHUFHWri	= 5674,
    X86_VPSHUFLWYmi	= 5675,
    X86_VPSHUFLWYri	= 5676,
    X86_VPSHUFLWmi	= 5677,
    X86_VPSHUFLWri	= 5678,
    X86_VPSIGNBYrm	= 5679,
    X86_VPSIGNBYrr	= 5680,
    X86_VPSIGNBrm	= 5681,
    X86_VPSIGNBrr	= 5682,
    X86_VPSIGNDYrm	= 5683,
    X86_VPSIGNDYrr	= 5684,
    X86_VPSIGNDrm	= 5685,
    X86_VPSIGNDrr	= 5686,
    X86_VPSIGNWYrm	= 5687,
    X86_VPSIGNWYrr	= 5688,
    X86_VPSIGNWrm	= 5689,
    X86_VPSIGNWrr	= 5690,
    X86_VPSLLDQYri	= 5691,
    X86_VPSLLDQri	= 5692,
    X86_VPSLLDYri	= 5693,
    X86_VPSLLDYrm	= 5694,
    X86_VPSLLDYrr	= 5695,
    X86_VPSLLDZmi	= 5696,
    X86_VPSLLDZmik	= 5697,
    X86_VPSLLDZri	= 5698,
    X86_VPSLLDZrik	= 5699,
    X86_VPSLLDZrm	= 5700,
    X86_VPSLLDZrmk	= 5701,
    X86_VPSLLDZrr	= 5702,
    X86_VPSLLDZrrk	= 5703,
    X86_VPSLLDri	= 5704,
    X86_VPSLLDrm	= 5705,
    X86_VPSLLDrr	= 5706,
    X86_VPSLLQYri	= 5707,
    X86_VPSLLQYrm	= 5708,
    X86_VPSLLQYrr	= 5709,
    X86_VPSLLQZmi	= 5710,
    X86_VPSLLQZmik	= 5711,
    X86_VPSLLQZri	= 5712,
    X86_VPSLLQZrik	= 5713,
    X86_VPSLLQZrm	= 5714,
    X86_VPSLLQZrmk	= 5715,
    X86_VPSLLQZrr	= 5716,
    X86_VPSLLQZrrk	= 5717,
    X86_VPSLLQri	= 5718,
    X86_VPSLLQrm	= 5719,
    X86_VPSLLQrr	= 5720,
    X86_VPSLLVDYrm	= 5721,
    X86_VPSLLVDYrr	= 5722,
    X86_VPSLLVDZrm	= 5723,
    X86_VPSLLVDZrr	= 5724,
    X86_VPSLLVDrm	= 5725,
    X86_VPSLLVDrr	= 5726,
    X86_VPSLLVQYrm	= 5727,
    X86_VPSLLVQYrr	= 5728,
    X86_VPSLLVQZrm	= 5729,
    X86_VPSLLVQZrr	= 5730,
    X86_VPSLLVQrm	= 5731,
    X86_VPSLLVQrr	= 5732,
    X86_VPSLLWYri	= 5733,
    X86_VPSLLWYrm	= 5734,
    X86_VPSLLWYrr	= 5735,
    X86_VPSLLWri	= 5736,
    X86_VPSLLWrm	= 5737,
    X86_VPSLLWrr	= 5738,
    X86_VPSRADYri	= 5739,
    X86_VPSRADYrm	= 5740,
    X86_VPSRADYrr	= 5741,
    X86_VPSRADZmi	= 5742,
    X86_VPSRADZmik	= 5743,
    X86_VPSRADZri	= 5744,
    X86_VPSRADZrik	= 5745,
    X86_VPSRADZrm	= 5746,
    X86_VPSRADZrmk	= 5747,
    X86_VPSRADZrr	= 5748,
    X86_VPSRADZrrk	= 5749,
    X86_VPSRADri	= 5750,
    X86_VPSRADrm	= 5751,
    X86_VPSRADrr	= 5752,
    X86_VPSRAQZmi	= 5753,
    X86_VPSRAQZmik	= 5754,
    X86_VPSRAQZri	= 5755,
    X86_VPSRAQZrik	= 5756,
    X86_VPSRAQZrm	= 5757,
    X86_VPSRAQZrmk	= 5758,
    X86_VPSRAQZrr	= 5759,
    X86_VPSRAQZrrk	= 5760,
    X86_VPSRAVDYrm	= 5761,
    X86_VPSRAVDYrr	= 5762,
    X86_VPSRAVDZrm	= 5763,
    X86_VPSRAVDZrr	= 5764,
    X86_VPSRAVDrm	= 5765,
    X86_VPSRAVDrr	= 5766,
    X86_VPSRAVQZrm	= 5767,
    X86_VPSRAVQZrr	= 5768,
    X86_VPSRAWYri	= 5769,
    X86_VPSRAWYrm	= 5770,
    X86_VPSRAWYrr	= 5771,
    X86_VPSRAWri	= 5772,
    X86_VPSRAWrm	= 5773,
    X86_VPSRAWrr	= 5774,
    X86_VPSRLDQYri	= 5775,
    X86_VPSRLDQri	= 5776,
    X86_VPSRLDYri	= 5777,
    X86_VPSRLDYrm	= 5778,
    X86_VPSRLDYrr	= 5779,
    X86_VPSRLDZmi	= 5780,
    X86_VPSRLDZmik	= 5781,
    X86_VPSRLDZri	= 5782,
    X86_VPSRLDZrik	= 5783,
    X86_VPSRLDZrm	= 5784,
    X86_VPSRLDZrmk	= 5785,
    X86_VPSRLDZrr	= 5786,
    X86_VPSRLDZrrk	= 5787,
    X86_VPSRLDri	= 5788,
    X86_VPSRLDrm	= 5789,
    X86_VPSRLDrr	= 5790,
    X86_VPSRLQYri	= 5791,
    X86_VPSRLQYrm	= 5792,
    X86_VPSRLQYrr	= 5793,
    X86_VPSRLQZmi	= 5794,
    X86_VPSRLQZmik	= 5795,
    X86_VPSRLQZri	= 5796,
    X86_VPSRLQZrik	= 5797,
    X86_VPSRLQZrm	= 5798,
    X86_VPSRLQZrmk	= 5799,
    X86_VPSRLQZrr	= 5800,
    X86_VPSRLQZrrk	= 5801,
    X86_VPSRLQri	= 5802,
    X86_VPSRLQrm	= 5803,
    X86_VPSRLQrr	= 5804,
    X86_VPSRLVDYrm	= 5805,
    X86_VPSRLVDYrr	= 5806,
    X86_VPSRLVDZrm	= 5807,
    X86_VPSRLVDZrr	= 5808,
    X86_VPSRLVDrm	= 5809,
    X86_VPSRLVDrr	= 5810,
    X86_VPSRLVQYrm	= 5811,
    X86_VPSRLVQYrr	= 5812,
    X86_VPSRLVQZrm	= 5813,
    X86_VPSRLVQZrr	= 5814,
    X86_VPSRLVQrm	= 5815,
    X86_VPSRLVQrr	= 5816,
    X86_VPSRLWYri	= 5817,
    X86_VPSRLWYrm	= 5818,
    X86_VPSRLWYrr	= 5819,
    X86_VPSRLWri	= 5820,
    X86_VPSRLWrm	= 5821,
    X86_VPSRLWrr	= 5822,
    X86_VPSUBBYrm	= 5823,
    X86_VPSUBBYrr	= 5824,
    X86_VPSUBBrm	= 5825,
    X86_VPSUBBrr	= 5826,
    X86_VPSUBDYrm	= 5827,
    X86_VPSUBDYrr	= 5828,
    X86_VPSUBDZrm	= 5829,
    X86_VPSUBDZrmb	= 5830,
    X86_VPSUBDZrmbk	= 5831,
    X86_VPSUBDZrmbkz	= 5832,
    X86_VPSUBDZrmk	= 5833,
    X86_VPSUBDZrmkz	= 5834,
    X86_VPSUBDZrr	= 5835,
    X86_VPSUBDZrrk	= 5836,
    X86_VPSUBDZrrkz	= 5837,
    X86_VPSUBDrm	= 5838,
    X86_VPSUBDrr	= 5839,
    X86_VPSUBQYrm	= 5840,
    X86_VPSUBQYrr	= 5841,
    X86_VPSUBQZrm	= 5842,
    X86_VPSUBQZrmb	= 5843,
    X86_VPSUBQZrmbk	= 5844,
    X86_VPSUBQZrmbkz	= 5845,
    X86_VPSUBQZrmk	= 5846,
    X86_VPSUBQZrmkz	= 5847,
    X86_VPSUBQZrr	= 5848,
    X86_VPSUBQZrrk	= 5849,
    X86_VPSUBQZrrkz	= 5850,
    X86_VPSUBQrm	= 5851,
    X86_VPSUBQrr	= 5852,
    X86_VPSUBSBYrm	= 5853,
    X86_VPSUBSBYrr	= 5854,
    X86_VPSUBSBrm	= 5855,
    X86_VPSUBSBrr	= 5856,
    X86_VPSUBSWYrm	= 5857,
    X86_VPSUBSWYrr	= 5858,
    X86_VPSUBSWrm	= 5859,
    X86_VPSUBSWrr	= 5860,
    X86_VPSUBUSBYrm	= 5861,
    X86_VPSUBUSBYrr	= 5862,
    X86_VPSUBUSBrm	= 5863,
    X86_VPSUBUSBrr	= 5864,
    X86_VPSUBUSWYrm	= 5865,
    X86_VPSUBUSWYrr	= 5866,
    X86_VPSUBUSWrm	= 5867,
    X86_VPSUBUSWrr	= 5868,
    X86_VPSUBWYrm	= 5869,
    X86_VPSUBWYrr	= 5870,
    X86_VPSUBWrm	= 5871,
    X86_VPSUBWrr	= 5872,
    X86_VPTESTMDZrm	= 5873,
    X86_VPTESTMDZrr	= 5874,
    X86_VPTESTMQZrm	= 5875,
    X86_VPTESTMQZrr	= 5876,
    X86_VPTESTNMDZrm	= 5877,
    X86_VPTESTNMDZrr	= 5878,
    X86_VPTESTNMQZrm	= 5879,
    X86_VPTESTNMQZrr	= 5880,
    X86_VPTESTYrm	= 5881,
    X86_VPTESTYrr	= 5882,
    X86_VPTESTrm	= 5883,
    X86_VPTESTrr	= 5884,
    X86_VPUNPCKHBWYrm	= 5885,
    X86_VPUNPCKHBWYrr	= 5886,
    X86_VPUNPCKHBWrm	= 5887,
    X86_VPUNPCKHBWrr	= 5888,
    X86_VPUNPCKHDQYrm	= 5889,
    X86_VPUNPCKHDQYrr	= 5890,
    X86_VPUNPCKHDQZrm	= 5891,
    X86_VPUNPCKHDQZrr	= 5892,
    X86_VPUNPCKHDQrm	= 5893,
    X86_VPUNPCKHDQrr	= 5894,
    X86_VPUNPCKHQDQYrm	= 5895,
    X86_VPUNPCKHQDQYrr	= 5896,
    X86_VPUNPCKHQDQZrm	= 5897,
    X86_VPUNPCKHQDQZrr	= 5898,
    X86_VPUNPCKHQDQrm	= 5899,
    X86_VPUNPCKHQDQrr	= 5900,
    X86_VPUNPCKHWDYrm	= 5901,
    X86_VPUNPCKHWDYrr	= 5902,
    X86_VPUNPCKHWDrm	= 5903,
    X86_VPUNPCKHWDrr	= 5904,
    X86_VPUNPCKLBWYrm	= 5905,
    X86_VPUNPCKLBWYrr	= 5906,
    X86_VPUNPCKLBWrm	= 5907,
    X86_VPUNPCKLBWrr	= 5908,
    X86_VPUNPCKLDQYrm	= 5909,
    X86_VPUNPCKLDQYrr	= 5910,
    X86_VPUNPCKLDQZrm	= 5911,
    X86_VPUNPCKLDQZrr	= 5912,
    X86_VPUNPCKLDQrm	= 5913,
    X86_VPUNPCKLDQrr	= 5914,
    X86_VPUNPCKLQDQYrm	= 5915,
    X86_VPUNPCKLQDQYrr	= 5916,
    X86_VPUNPCKLQDQZrm	= 5917,
    X86_VPUNPCKLQDQZrr	= 5918,
    X86_VPUNPCKLQDQrm	= 5919,
    X86_VPUNPCKLQDQrr	= 5920,
    X86_VPUNPCKLWDYrm	= 5921,
    X86_VPUNPCKLWDYrr	= 5922,
    X86_VPUNPCKLWDrm	= 5923,
    X86_VPUNPCKLWDrr	= 5924,
    X86_VPXORDZrm	= 5925,
    X86_VPXORDZrmb	= 5926,
    X86_VPXORDZrmbk	= 5927,
    X86_VPXORDZrmbkz	= 5928,
    X86_VPXORDZrmk	= 5929,
    X86_VPXORDZrmkz	= 5930,
    X86_VPXORDZrr	= 5931,
    X86_VPXORDZrrk	= 5932,
    X86_VPXORDZrrkz	= 5933,
    X86_VPXORQZrm	= 5934,
    X86_VPXORQZrmb	= 5935,
    X86_VPXORQZrmbk	= 5936,
    X86_VPXORQZrmbkz	= 5937,
    X86_VPXORQZrmk	= 5938,
    X86_VPXORQZrmkz	= 5939,
    X86_VPXORQZrr	= 5940,
    X86_VPXORQZrrk	= 5941,
    X86_VPXORQZrrkz	= 5942,
    X86_VPXORYrm	= 5943,
    X86_VPXORYrr	= 5944,
    X86_VPXORrm	= 5945,
    X86_VPXORrr	= 5946,
    X86_VRCP14PDZm	= 5947,
    X86_VRCP14PDZr	= 5948,
    X86_VRCP14PSZm	= 5949,
    X86_VRCP14PSZr	= 5950,
    X86_VRCP14SDrm	= 5951,
    X86_VRCP14SDrr	= 5952,
    X86_VRCP14SSrm	= 5953,
    X86_VRCP14SSrr	= 5954,
    X86_VRCP28PDZm	= 5955,
    X86_VRCP28PDZr	= 5956,
    X86_VRCP28PDZrb	= 5957,
    X86_VRCP28PSZm	= 5958,
    X86_VRCP28PSZr	= 5959,
    X86_VRCP28PSZrb	= 5960,
    X86_VRCP28SDrm	= 5961,
    X86_VRCP28SDrr	= 5962,
    X86_VRCP28SDrrb	= 5963,
    X86_VRCP28SSrm	= 5964,
    X86_VRCP28SSrr	= 5965,
    X86_VRCP28SSrrb	= 5966,
    X86_VRCPPSYm	= 5967,
    X86_VRCPPSYm_Int	= 5968,
    X86_VRCPPSYr	= 5969,
    X86_VRCPPSYr_Int	= 5970,
    X86_VRCPPSm	= 5971,
    X86_VRCPPSm_Int	= 5972,
    X86_VRCPPSr	= 5973,
    X86_VRCPPSr_Int	= 5974,
    X86_VRCPSSm	= 5975,
    X86_VRCPSSm_Int	= 5976,
    X86_VRCPSSr	= 5977,
    X86_VRNDSCALEPDZm	= 5978,
    X86_VRNDSCALEPDZr	= 5979,
    X86_VRNDSCALEPSZm	= 5980,
    X86_VRNDSCALEPSZr	= 5981,
    X86_VRNDSCALESDm	= 5982,
    X86_VRNDSCALESDr	= 5983,
    X86_VRNDSCALESSm	= 5984,
    X86_VRNDSCALESSr	= 5985,
    X86_VROUNDPDm	= 5986,
    X86_VROUNDPDr	= 5987,
    X86_VROUNDPSm	= 5988,
    X86_VROUNDPSr	= 5989,
    X86_VROUNDSDm	= 5990,
    X86_VROUNDSDr	= 5991,
    X86_VROUNDSDr_Int	= 5992,
    X86_VROUNDSSm	= 5993,
    X86_VROUNDSSr	= 5994,
    X86_VROUNDSSr_Int	= 5995,
    X86_VROUNDYPDm	= 5996,
    X86_VROUNDYPDr	= 5997,
    X86_VROUNDYPSm	= 5998,
    X86_VROUNDYPSr	= 5999,
    X86_VRSQRT14PDZm	= 6000,
    X86_VRSQRT14PDZr	= 6001,
    X86_VRSQRT14PSZm	= 6002,
    X86_VRSQRT14PSZr	= 6003,
    X86_VRSQRT14SDrm	= 6004,
    X86_VRSQRT14SDrr	= 6005,
    X86_VRSQRT14SSrm	= 6006,
    X86_VRSQRT14SSrr	= 6007,
    X86_VRSQRT28PDZm	= 6008,
    X86_VRSQRT28PDZr	= 6009,
    X86_VRSQRT28PDZrb	= 6010,
    X86_VRSQRT28PSZm	= 6011,
    X86_VRSQRT28PSZr	= 6012,
    X86_VRSQRT28PSZrb	= 6013,
    X86_VRSQRT28SDrm	= 6014,
    X86_VRSQRT28SDrr	= 6015,
    X86_VRSQRT28SDrrb	= 6016,
    X86_VRSQRT28SSrm	= 6017,
    X86_VRSQRT28SSrr	= 6018,
    X86_VRSQRT28SSrrb	= 6019,
    X86_VRSQRTPSYm	= 6020,
    X86_VRSQRTPSYm_Int	= 6021,
    X86_VRSQRTPSYr	= 6022,
    X86_VRSQRTPSYr_Int	= 6023,
    X86_VRSQRTPSm	= 6024,
    X86_VRSQRTPSm_Int	= 6025,
    X86_VRSQRTPSr	= 6026,
    X86_VRSQRTPSr_Int	= 6027,
    X86_VRSQRTSSm	= 6028,
    X86_VRSQRTSSm_Int	= 6029,
    X86_VRSQRTSSr	= 6030,
    X86_VSCATTERDPDZmr	= 6031,
    X86_VSCATTERDPSZmr	= 6032,
    X86_VSCATTERPF0DPDm	= 6033,
    X86_VSCATTERPF0DPSm	= 6034,
    X86_VSCATTERPF0QPDm	= 6035,
    X86_VSCATTERPF0QPSm	= 6036,
    X86_VSCATTERPF1DPDm	= 6037,
    X86_VSCATTERPF1DPSm	= 6038,
    X86_VSCATTERPF1QPDm	= 6039,
    X86_VSCATTERPF1QPSm	= 6040,
    X86_VSCATTERQPDZmr	= 6041,
    X86_VSCATTERQPSZmr	= 6042,
    X86_VSHUFPDYrmi	= 6043,
    X86_VSHUFPDYrri	= 6044,
    X86_VSHUFPDZrmi	= 6045,
    X86_VSHUFPDZrri	= 6046,
    X86_VSHUFPDrmi	= 6047,
    X86_VSHUFPDrri	= 6048,
    X86_VSHUFPSYrmi	= 6049,
    X86_VSHUFPSYrri	= 6050,
    X86_VSHUFPSZrmi	= 6051,
    X86_VSHUFPSZrri	= 6052,
    X86_VSHUFPSrmi	= 6053,
    X86_VSHUFPSrri	= 6054,
    X86_VSQRTPDYm	= 6055,
    X86_VSQRTPDYr	= 6056,
    X86_VSQRTPDZrm	= 6057,
    X86_VSQRTPDZrr	= 6058,
    X86_VSQRTPDm	= 6059,
    X86_VSQRTPDr	= 6060,
    X86_VSQRTPSYm	= 6061,
    X86_VSQRTPSYr	= 6062,
    X86_VSQRTPSZrm	= 6063,
    X86_VSQRTPSZrr	= 6064,
    X86_VSQRTPSm	= 6065,
    X86_VSQRTPSr	= 6066,
    X86_VSQRTSDZm	= 6067,
    X86_VSQRTSDZm_Int	= 6068,
    X86_VSQRTSDZr	= 6069,
    X86_VSQRTSDZr_Int	= 6070,
    X86_VSQRTSDm	= 6071,
    X86_VSQRTSDm_Int	= 6072,
    X86_VSQRTSDr	= 6073,
    X86_VSQRTSSZm	= 6074,
    X86_VSQRTSSZm_Int	= 6075,
    X86_VSQRTSSZr	= 6076,
    X86_VSQRTSSZr_Int	= 6077,
    X86_VSQRTSSm	= 6078,
    X86_VSQRTSSm_Int	= 6079,
    X86_VSQRTSSr	= 6080,
    X86_VSTMXCSR	= 6081,
    X86_VSUBPDYrm	= 6082,
    X86_VSUBPDYrr	= 6083,
    X86_VSUBPDZrm	= 6084,
    X86_VSUBPDZrmb	= 6085,
    X86_VSUBPDZrmbk	= 6086,
    X86_VSUBPDZrmbkz	= 6087,
    X86_VSUBPDZrmk	= 6088,
    X86_VSUBPDZrmkz	= 6089,
    X86_VSUBPDZrr	= 6090,
    X86_VSUBPDZrrk	= 6091,
    X86_VSUBPDZrrkz	= 6092,
    X86_VSUBPDrm	= 6093,
    X86_VSUBPDrr	= 6094,
    X86_VSUBPSYrm	= 6095,
    X86_VSUBPSYrr	= 6096,
    X86_VSUBPSZrm	= 6097,
    X86_VSUBPSZrmb	= 6098,
    X86_VSUBPSZrmbk	= 6099,
    X86_VSUBPSZrmbkz	= 6100,
    X86_VSUBPSZrmk	= 6101,
    X86_VSUBPSZrmkz	= 6102,
    X86_VSUBPSZrr	= 6103,
    X86_VSUBPSZrrk	= 6104,
    X86_VSUBPSZrrkz	= 6105,
    X86_VSUBPSrm	= 6106,
    X86_VSUBPSrr	= 6107,
    X86_VSUBSDZrm	= 6108,
    X86_VSUBSDZrr	= 6109,
    X86_VSUBSDrm	= 6110,
    X86_VSUBSDrm_Int	= 6111,
    X86_VSUBSDrr	= 6112,
    X86_VSUBSDrr_Int	= 6113,
    X86_VSUBSSZrm	= 6114,
    X86_VSUBSSZrr	= 6115,
    X86_VSUBSSrm	= 6116,
    X86_VSUBSSrm_Int	= 6117,
    X86_VSUBSSrr	= 6118,
    X86_VSUBSSrr_Int	= 6119,
    X86_VTESTPDYrm	= 6120,
    X86_VTESTPDYrr	= 6121,
    X86_VTESTPDrm	= 6122,
    X86_VTESTPDrr	= 6123,
    X86_VTESTPSYrm	= 6124,
    X86_VTESTPSYrr	= 6125,
    X86_VTESTPSrm	= 6126,
    X86_VTESTPSrr	= 6127,
    X86_VUCOMISDZrm	= 6128,
    X86_VUCOMISDZrr	= 6129,
    X86_VUCOMISDrm	= 6130,
    X86_VUCOMISDrr	= 6131,
    X86_VUCOMISSZrm	= 6132,
    X86_VUCOMISSZrr	= 6133,
    X86_VUCOMISSrm	= 6134,
    X86_VUCOMISSrr	= 6135,
    X86_VUNPCKHPDYrm	= 6136,
    X86_VUNPCKHPDYrr	= 6137,
    X86_VUNPCKHPDZrm	= 6138,
    X86_VUNPCKHPDZrr	= 6139,
    X86_VUNPCKHPDrm	= 6140,
    X86_VUNPCKHPDrr	= 6141,
    X86_VUNPCKHPSYrm	= 6142,
    X86_VUNPCKHPSYrr	= 6143,
    X86_VUNPCKHPSZrm	= 6144,
    X86_VUNPCKHPSZrr	= 6145,
    X86_VUNPCKHPSrm	= 6146,
    X86_VUNPCKHPSrr	= 6147,
    X86_VUNPCKLPDYrm	= 6148,
    X86_VUNPCKLPDYrr	= 6149,
    X86_VUNPCKLPDZrm	= 6150,
    X86_VUNPCKLPDZrr	= 6151,
    X86_VUNPCKLPDrm	= 6152,
    X86_VUNPCKLPDrr	= 6153,
    X86_VUNPCKLPSYrm	= 6154,
    X86_VUNPCKLPSYrr	= 6155,
    X86_VUNPCKLPSZrm	= 6156,
    X86_VUNPCKLPSZrr	= 6157,
    X86_VUNPCKLPSrm	= 6158,
    X86_VUNPCKLPSrr	= 6159,
    X86_VXORPDYrm	= 6160,
    X86_VXORPDYrr	= 6161,
    X86_VXORPDrm	= 6162,
    X86_VXORPDrr	= 6163,
    X86_VXORPSYrm	= 6164,
    X86_VXORPSYrr	= 6165,
    X86_VXORPSrm	= 6166,
    X86_VXORPSrr	= 6167,
    X86_VZEROALL	= 6168,
    X86_VZEROUPPER	= 6169,
    X86_V_SET0	= 6170,
    X86_V_SETALLONES	= 6171,
    X86_W64ALLOCA	= 6172,
    X86_WAIT	= 6173,
    X86_WBINVD	= 6174,
    X86_WIN_ALLOCA	= 6175,
    X86_WIN_FTOL_32	= 6176,
    X86_WIN_FTOL_64	= 6177,
    X86_WRFSBASE	= 6178,
    X86_WRFSBASE64	= 6179,
    X86_WRGSBASE	= 6180,
    X86_WRGSBASE64	= 6181,
    X86_WRMSR	= 6182,
    X86_XABORT	= 6183,
    X86_XACQUIRE_PREFIX	= 6184,
    X86_XADD16rm	= 6185,
    X86_XADD16rr	= 6186,
    X86_XADD32rm	= 6187,
    X86_XADD32rr	= 6188,
    X86_XADD64rm	= 6189,
    X86_XADD64rr	= 6190,
    X86_XADD8rm	= 6191,
    X86_XADD8rr	= 6192,
    X86_XBEGIN	= 6193,
    X86_XBEGIN_4	= 6194,
    X86_XCHG16ar	= 6195,
    X86_XCHG16rm	= 6196,
    X86_XCHG16rr	= 6197,
    X86_XCHG32ar	= 6198,
    X86_XCHG32ar64	= 6199,
    X86_XCHG32rm	= 6200,
    X86_XCHG32rr	= 6201,
    X86_XCHG64ar	= 6202,
    X86_XCHG64rm	= 6203,
    X86_XCHG64rr	= 6204,
    X86_XCHG8rm	= 6205,
    X86_XCHG8rr	= 6206,
    X86_XCH_F	= 6207,
    X86_XCRYPTCBC	= 6208,
    X86_XCRYPTCFB	= 6209,
    X86_XCRYPTCTR	= 6210,
    X86_XCRYPTECB	= 6211,
    X86_XCRYPTOFB	= 6212,
    X86_XEND	= 6213,
    X86_XGETBV	= 6214,
    X86_XLAT	= 6215,
    X86_XOR16i16	= 6216,
    X86_XOR16mi	= 6217,
    X86_XOR16mi8	= 6218,
    X86_XOR16mr	= 6219,
    X86_XOR16ri	= 6220,
    X86_XOR16ri8	= 6221,
    X86_XOR16rm	= 6222,
    X86_XOR16rr	= 6223,
    X86_XOR16rr_REV	= 6224,
    X86_XOR32i32	= 6225,
    X86_XOR32mi	= 6226,
    X86_XOR32mi8	= 6227,
    X86_XOR32mr	= 6228,
    X86_XOR32ri	= 6229,
    X86_XOR32ri8	= 6230,
    X86_XOR32rm	= 6231,
    X86_XOR32rr	= 6232,
    X86_XOR32rr_REV	= 6233,
    X86_XOR64i32	= 6234,
    X86_XOR64mi32	= 6235,
    X86_XOR64mi8	= 6236,
    X86_XOR64mr	= 6237,
    X86_XOR64ri32	= 6238,
    X86_XOR64ri8	= 6239,
    X86_XOR64rm	= 6240,
    X86_XOR64rr	= 6241,
    X86_XOR64rr_REV	= 6242,
    X86_XOR8i8	= 6243,
    X86_XOR8mi	= 6244,
    X86_XOR8mr	= 6245,
    X86_XOR8ri	= 6246,
    X86_XOR8ri8	= 6247,
    X86_XOR8rm	= 6248,
    X86_XOR8rr	= 6249,
    X86_XOR8rr_REV	= 6250,
    X86_XORPDrm	= 6251,
    X86_XORPDrr	= 6252,
    X86_XORPSrm	= 6253,
    X86_XORPSrr	= 6254,
    X86_XRELEASE_PREFIX	= 6255,
    X86_XRSTOR	= 6256,
    X86_XRSTOR64	= 6257,
    X86_XSAVE	= 6258,
    X86_XSAVE64	= 6259,
    X86_XSAVEOPT	= 6260,
    X86_XSAVEOPT64	= 6261,
    X86_XSETBV	= 6262,
    X86_XSHA1	= 6263,
    X86_XSHA256	= 6264,
    X86_XSTORE	= 6265,
    X86_XTEST	= 6266,
    X86_INSTRUCTION_LIST_END = 6267
};

#endif // GET_INSTRINFO_ENUM


#ifdef GET_INSTRINFO_MC_DESC
#undef GET_INSTRINFO_MC_DESC

typedef struct x86_op_id_pair {
	uint16_t first;
	uint16_t second;
} x86_op_id_pair;

static const x86_op_id_pair x86_16_bit_eq_tbl[] = {
	{ 29, 28 },
	{ 30, 28 },
	{ 41, 32 },
	{ 42, 33 },
	{ 43, 34 },
	{ 44, 35 },
	{ 45, 36 },
	{ 46, 37 },
	{ 47, 38 },
	{ 48, 39 },
	{ 49, 40 },
	{ 50, 32 },
	{ 52, 34 },
	{ 53, 35 },
	{ 55, 37 },
	{ 56, 38 },
	{ 57, 39 },
	{ 58, 40 },
	{ 82, 70 },
	{ 83, 71 },
	{ 84, 72 },
	{ 85, 73 },
	{ 86, 74 },
	{ 87, 75 },
	{ 88, 76 },
	{ 89, 77 },
	{ 90, 78 },
	{ 91, 79 },
	{ 92, 80 },
	{ 93, 81 },
	{ 94, 70 },
	{ 96, 72 },
	{ 97, 73 },
	{ 100, 75 },
	{ 101, 76 },
	{ 102, 78 },
	{ 103, 79 },
	{ 104, 80 },
	{ 105, 81 },
	{ 133, 132 },
	{ 147, 144 },
	{ 148, 145 },
	{ 149, 146 },
	{ 180, 171 },
	{ 181, 172 },
	{ 182, 173 },
	{ 183, 174 },
	{ 184, 175 },
	{ 185, 176 },
	{ 186, 177 },
	{ 187, 178 },
	{ 188, 179 },
	{ 189, 171 },
	{ 191, 173 },
	{ 192, 174 },
	{ 194, 176 },
	{ 195, 177 },
	{ 196, 178 },
	{ 197, 179 },
	{ 280, 279 },
	{ 283, 281 },
	{ 284, 282 },
	{ 285, 281 },
	{ 286, 282 },
	{ 289, 287 },
	{ 290, 288 },
	{ 291, 287 },
	{ 292, 288 },
	{ 299, 295 },
	{ 300, 296 },
	{ 301, 297 },
	{ 302, 298 },
	{ 303, 295 },
	{ 304, 296 },
	{ 305, 297 },
	{ 306, 298 },
	{ 311, 307 },
	{ 312, 308 },
	{ 313, 309 },
	{ 314, 310 },
	{ 315, 307 },
	{ 316, 308 },
	{ 317, 309 },
	{ 318, 310 },
	{ 323, 319 },
	{ 324, 320 },
	{ 325, 321 },
	{ 326, 322 },
	{ 327, 319 },
	{ 328, 320 },
	{ 329, 321 },
	{ 330, 322 },
	{ 335, 331 },
	{ 336, 332 },
	{ 337, 333 },
	{ 338, 334 },
	{ 339, 331 },
	{ 340, 332 },
	{ 341, 333 },
	{ 342, 334 },
	{ 349, 347 },
	{ 350, 348 },
	{ 351, 347 },
	{ 353, 348 },
	{ 355, 354 },
	{ 365, 649 },
	{ 373, 371 },
	{ 374, 372 },
	{ 375, 371 },
	{ 376, 372 },
	{ 379, 377 },
	{ 380, 378 },
	{ 381, 377 },
	{ 382, 378 },
	{ 385, 383 },
	{ 386, 384 },
	{ 387, 383 },
	{ 388, 384 },
	{ 391, 389 },
	{ 392, 390 },
	{ 393, 389 },
	{ 394, 390 },
	{ 405, 403 },
	{ 406, 404 },
	{ 407, 403 },
	{ 408, 404 },
	{ 415, 413 },
	{ 416, 414 },
	{ 417, 413 },
	{ 418, 414 },
	{ 421, 419 },
	{ 422, 420 },
	{ 423, 419 },
	{ 424, 420 },
	{ 427, 425 },
	{ 428, 426 },
	{ 429, 425 },
	{ 430, 426 },
	{ 433, 431 },
	{ 434, 432 },
	{ 435, 431 },
	{ 436, 432 },
	{ 447, 445 },
	{ 448, 446 },
	{ 449, 445 },
	{ 450, 446 },
	{ 457, 455 },
	{ 458, 456 },
	{ 459, 455 },
	{ 460, 456 },
	{ 463, 461 },
	{ 464, 462 },
	{ 465, 461 },
	{ 466, 462 },
	{ 473, 471 },
	{ 474, 472 },
	{ 475, 471 },
	{ 476, 472 },
	{ 479, 477 },
	{ 480, 478 },
	{ 481, 477 },
	{ 482, 478 },
	{ 485, 483 },
	{ 486, 484 },
	{ 487, 483 },
	{ 488, 484 },
	{ 495, 493 },
	{ 496, 494 },
	{ 497, 493 },
	{ 498, 494 },
	{ 502, 501 },
	{ 525, 516 },
	{ 526, 517 },
	{ 527, 518 },
	{ 528, 519 },
	{ 529, 520 },
	{ 530, 521 },
	{ 531, 522 },
	{ 532, 523 },
	{ 533, 524 },
	{ 534, 516 },
	{ 536, 518 },
	{ 537, 519 },
	{ 539, 521 },
	{ 540, 522 },
	{ 541, 523 },
	{ 542, 524 },
	{ 563, 569 },
	{ 564, 569 },
	{ 573, 571 },
	{ 574, 572 },
	{ 575, 571 },
	{ 576, 572 },
	{ 596, 595 },
	{ 599, 598 },
	{ 657, 656 },
	{ 658, 654 },
	{ 659, 655 },
	{ 662, 660 },
	{ 663, 661 },
	{ 664, 654 },
	{ 665, 655 },
	{ 670, 668 },
	{ 671, 669 },
	{ 672, 668 },
	{ 673, 669 },
	{ 683, 682 },
	{ 694, 691 },
	{ 695, 692 },
	{ 696, 693 },
	{ 709, 708 },
	{ 723, 720 },
	{ 724, 721 },
	{ 725, 722 },
	{ 748, 746 },
	{ 749, 747 },
	{ 753, 751 },
	{ 754, 752 },
	{ 767, 766 },
	{ 769, 768 },
	{ 785, 784 },
	{ 786, 784 },
	{ 788, 787 },
	{ 789, 787 },
	{ 791, 790 },
	{ 792, 790 },
	{ 846, 844 },
	{ 847, 845 },
	{ 848, 844 },
	{ 849, 845 },
	{ 853, 852 },
	{ 854, 852 },
	{ 858, 855 },
	{ 859, 856 },
	{ 860, 857 },
	{ 861, 855 },
	{ 862, 856 },
	{ 863, 857 },
	{ 872, 864 },
	{ 873, 865 },
	{ 874, 866 },
	{ 875, 867 },
	{ 876, 868 },
	{ 877, 869 },
	{ 878, 870 },
	{ 879, 871 },
	{ 880, 864 },
	{ 881, 865 },
	{ 882, 866 },
	{ 884, 868 },
	{ 885, 869 },
	{ 887, 871 },
	{ 892, 890 },
	{ 893, 891 },
	{ 899, 898 },
	{ 900, 896 },
	{ 901, 897 },
	{ 904, 902 },
	{ 905, 903 },
	{ 906, 896 },
	{ 907, 897 },
	{ 915, 916 },
	{ 919, 918 },
	{ 932, 931 },
	{ 933, 931 },
	{ 935, 934 },
	{ 936, 934 },
	{ 940, 937 },
	{ 941, 938 },
	{ 942, 939 },
	{ 943, 937 },
	{ 944, 938 },
	{ 945, 939 },
	{ 947, 946 },
	{ 949, 948 },
	{ 950, 948 },
	{ 954, 951 },
	{ 955, 952 },
	{ 956, 953 },
	{ 957, 951 },
	{ 958, 952 },
	{ 959, 953 },
	{ 1097, 1095 },
	{ 1098, 1096 },
	{ 1099, 1095 },
	{ 1100, 1096 },
	{ 1130, 1131 },
	{ 1132, 1133 },
	{ 1144, 1149 },
	{ 1145, 1150 },
	{ 1146, 1151 },
	{ 1147, 1152 },
	{ 1148, 1153 },
	{ 1156, 1157 },
	{ 1160, 1162 },
	{ 1172, 1173 },
	{ 1176, 1177 },
	{ 1181, 1179 },
	{ 1182, 1180 },
	{ 1183, 1179 },
	{ 1184, 1180 },
	{ 1187, 1185 },
	{ 1188, 1185 },
	{ 1194, 1193 },
	{ 1214, 1213 },
	{ 1216, 1213 },
	{ 1220, 1219 },
	{ 1223, 1222 },
	{ 1224, 1222 },
	{ 1226, 1225 },
	{ 1227, 1225 },
	{ 1229, 1228 },
	{ 1230, 1228 },
	{ 1232, 1231 },
	{ 1233, 1231 },
	{ 1241, 1238 },
	{ 1242, 1239 },
	{ 1243, 1240 },
	{ 1245, 1239 },
	{ 1246, 1240 },
	{ 1252, 1249 },
	{ 1253, 1250 },
	{ 1254, 1251 },
	{ 1256, 1250 },
	{ 1257, 1251 },
	{ 1261, 1260 },
	{ 1262, 1260 },
	{ 1265, 1264 },
	{ 1266, 1264 },
	{ 1271, 1268 },
	{ 1272, 1269 },
	{ 1273, 1270 },
	{ 1275, 1269 },
	{ 1276, 1270 },
	{ 1283, 1280 },
	{ 1284, 1281 },
	{ 1285, 1282 },
	{ 1287, 1281 },
	{ 1288, 1282 },
	{ 1294, 1291 },
	{ 1295, 1292 },
	{ 1296, 1293 },
	{ 1298, 1292 },
	{ 1299, 1293 },
	{ 1303, 1305 },
	{ 1304, 1305 },
	{ 1309, 1311 },
	{ 1310, 1311 },
	{ 1312, 1314 },
	{ 1313, 1314 },
	{ 1317, 1315 },
	{ 1318, 1316 },
	{ 1319, 1315 },
	{ 1320, 1316 },
	{ 1322, 1321 },
	{ 1323, 1321 },
	{ 1327, 1326 },
	{ 1328, 1326 },
	{ 1332, 1330 },
	{ 1333, 1331 },
	{ 1334, 1330 },
	{ 1335, 1331 },
	{ 1425, 1435 },
	{ 1426, 1436 },
	{ 1514, 1517 },
	{ 1515, 1518 },
	{ 1516, 1519 },
	{ 1529, 1532 },
	{ 1530, 1533 },
	{ 1531, 1534 },
	{ 1539, 1549 },
	{ 1540, 1550 },
	{ 1585, 1570 },
	{ 1586, 1571 },
	{ 1589, 1572 },
	{ 1590, 1573 },
	{ 1591, 1574 },
	{ 1592, 1575 },
	{ 1593, 1576 },
	{ 1597, 1577 },
	{ 1599, 1578 },
	{ 1600, 1579 },
	{ 1601, 1580 },
	{ 1602, 1581 },
	{ 1603, 1582 },
	{ 1604, 1583 },
	{ 1605, 1584 },
	{ 1606, 1570 },
	{ 1607, 1570 },
	{ 1607, 1606 },
	{ 1608, 1570 },
	{ 1608, 1606 },
	{ 1613, 1573 },
	{ 1614, 1574 },
	{ 1615, 1575 },
	{ 1616, 1575 },
	{ 1616, 1615 },
	{ 1617, 1575 },
	{ 1617, 1615 },
	{ 1621, 1577 },
	{ 1623, 1579 },
	{ 1624, 1580 },
	{ 1625, 1581 },
	{ 1626, 1582 },
	{ 1627, 1583 },
	{ 1628, 1584 },
	{ 1656, 1654 },
	{ 1657, 1655 },
	{ 1658, 1654 },
	{ 1659, 1655 },
	{ 1710, 1720 },
	{ 1713, 1720 },
	{ 1724, 1721 },
	{ 1726, 1722 },
	{ 1729, 1728 },
	{ 1730, 1721 },
	{ 1732, 1731 },
	{ 1733, 1722 },
	{ 1751, 1746 },
	{ 1753, 1747 },
	{ 1762, 1760 },
	{ 1763, 1761 },
	{ 1764, 1760 },
	{ 1765, 1761 },
	{ 1787, 1786 },
	{ 1801, 1798 },
	{ 1802, 1799 },
	{ 1803, 1800 },
	{ 1808, 1806 },
	{ 1809, 1807 },
	{ 1810, 1806 },
	{ 1811, 1807 },
	{ 1815, 1817 },
	{ 1819, 1821 },
	{ 1823, 1825 },
	{ 1827, 1829 },
	{ 1832, 1839 },
	{ 1833, 1840 },
	{ 1834, 1841 },
	{ 1835, 1842 },
	{ 1836, 1843 },
	{ 1837, 1844 },
	{ 1838, 1845 },
	{ 1848, 1846 },
	{ 1849, 1847 },
	{ 1850, 1846 },
	{ 1851, 1847 },
	{ 1863, 1854 },
	{ 1864, 1855 },
	{ 1865, 1856 },
	{ 1866, 1857 },
	{ 1868, 1858 },
	{ 1869, 1859 },
	{ 1870, 1860 },
	{ 1871, 1861 },
	{ 1872, 1862 },
	{ 1873, 1854 },
	{ 1875, 1856 },
	{ 1876, 1857 },
	{ 1878, 1859 },
	{ 1879, 1860 },
	{ 1880, 1861 },
	{ 1881, 1862 },
	{ 1896, 1894 },
	{ 1897, 1895 },
	{ 1901, 1902 },
	{ 1921, 1931 },
	{ 1922, 1932 },
	{ 1956, 1958 },
	{ 1957, 1959 },
	{ 1972, 1974 },
	{ 1973, 1975 },
	{ 1996, 1998 },
	{ 2096, 2098 },
	{ 2097, 2099 },
	{ 2108, 2110 },
	{ 2109, 2111 },
	{ 2137, 2134 },
	{ 2138, 2135 },
	{ 2139, 2136 },
	{ 2140, 2134 },
	{ 2141, 2135 },
	{ 2142, 2136 },
	{ 2144, 2143 },
	{ 2147, 2145 },
	{ 2148, 2146 },
	{ 2149, 2145 },
	{ 2150, 2146 },
	{ 2152, 2151 },
	{ 2154, 2153 },
	{ 2156, 2155 },
	{ 2157, 2155 },
	{ 2159, 2158 },
	{ 2160, 2158 },
	{ 2162, 2161 },
	{ 2163, 2161 },
	{ 2165, 2164 },
	{ 2194, 2197 },
	{ 2195, 2198 },
	{ 2196, 2199 },
	{ 2210, 2213 },
	{ 2211, 2214 },
	{ 2212, 2215 },
	{ 2220, 2230 },
	{ 2221, 2231 },
	{ 2256, 2252 },
	{ 2257, 2253 },
	{ 2258, 2254 },
	{ 2259, 2255 },
	{ 2261, 2260 },
	{ 2262, 2252 },
	{ 2263, 2253 },
	{ 2264, 2254 },
	{ 2265, 2255 },
	{ 2267, 2266 },
	{ 2269, 2268 },
	{ 2271, 2270 },
	{ 2273, 2272 },
	{ 2275, 2274 },
	{ 2276, 2274 },
	{ 2278, 2277 },
	{ 2279, 2277 },
	{ 2281, 2280 },
	{ 2282, 2280 },
	{ 2284, 2283 },
	{ 2286, 2285 },
	{ 2295, 2289 },
	{ 2296, 2290 },
	{ 2297, 2291 },
	{ 2298, 2292 },
	{ 2299, 2293 },
	{ 2300, 2294 },
	{ 2301, 2289 },
	{ 2302, 2290 },
	{ 2303, 2291 },
	{ 2304, 2292 },
	{ 2305, 2293 },
	{ 2306, 2294 },
	{ 2327, 2321 },
	{ 2328, 2322 },
	{ 2329, 2323 },
	{ 2330, 2324 },
	{ 2331, 2325 },
	{ 2332, 2326 },
	{ 2333, 2321 },
	{ 2334, 2322 },
	{ 2335, 2323 },
	{ 2336, 2324 },
	{ 2337, 2325 },
	{ 2338, 2326 },
	{ 2352, 2351 },
	{ 2353, 2351 },
	{ 2355, 2354 },
	{ 2356, 2354 },
	{ 2360, 2359 },
	{ 2361, 2359 },
	{ 2368, 2370 },
	{ 2376, 2378 },
	{ 2379, 2381 },
	{ 2380, 2381 },
	{ 2382, 2384 },
	{ 2383, 2384 },
	{ 2392, 2386 },
	{ 2393, 2387 },
	{ 2394, 2388 },
	{ 2395, 2389 },
	{ 2396, 2390 },
	{ 2397, 2391 },
	{ 2398, 2386 },
	{ 2399, 2387 },
	{ 2400, 2388 },
	{ 2401, 2389 },
	{ 2402, 2390 },
	{ 2403, 2391 },
	{ 2416, 2410 },
	{ 2417, 2411 },
	{ 2418, 2412 },
	{ 2419, 2413 },
	{ 2420, 2414 },
	{ 2421, 2415 },
	{ 2422, 2410 },
	{ 2423, 2411 },
	{ 2424, 2412 },
	{ 2425, 2413 },
	{ 2426, 2414 },
	{ 2427, 2415 },
	{ 2464, 2458 },
	{ 2465, 2459 },
	{ 2466, 2460 },
	{ 2467, 2461 },
	{ 2468, 2462 },
	{ 2469, 2463 },
	{ 2470, 2458 },
	{ 2471, 2459 },
	{ 2472, 2460 },
	{ 2473, 2461 },
	{ 2474, 2462 },
	{ 2475, 2463 },
	{ 2489, 2483 },
	{ 2490, 2484 },
	{ 2491, 2485 },
	{ 2492, 2486 },
	{ 2493, 2487 },
	{ 2494, 2488 },
	{ 2495, 2483 },
	{ 2496, 2484 },
	{ 2497, 2485 },
	{ 2498, 2486 },
	{ 2499, 2487 },
	{ 2500, 2488 },
	{ 2520, 2511 },
	{ 2521, 2512 },
	{ 2522, 2513 },
	{ 2523, 2514 },
	{ 2524, 2515 },
	{ 2525, 2516 },
	{ 2526, 2517 },
	{ 2527, 2518 },
	{ 2528, 2519 },
	{ 2529, 2511 },
	{ 2531, 2513 },
	{ 2532, 2514 },
	{ 2534, 2516 },
	{ 2535, 2517 },
	{ 2536, 2518 },
	{ 2537, 2519 },
	{ 2546, 2548 },
	{ 2547, 2548 },
	{ 2566, 2565 },
	{ 2567, 2565 },
	{ 2597, 2596 },
	{ 2598, 2596 },
	{ 2619, 2613 },
	{ 2620, 2614 },
	{ 2621, 2615 },
	{ 2622, 2616 },
	{ 2623, 2617 },
	{ 2624, 2618 },
	{ 2625, 2613 },
	{ 2626, 2614 },
	{ 2627, 2615 },
	{ 2628, 2616 },
	{ 2629, 2617 },
	{ 2630, 2618 },
	{ 2641, 2637 },
	{ 2642, 2638 },
	{ 2643, 2639 },
	{ 2644, 2640 },
	{ 2645, 2637 },
	{ 2646, 2638 },
	{ 2647, 2639 },
	{ 2648, 2640 },
	{ 2659, 2653 },
	{ 2660, 2654 },
	{ 2661, 2655 },
	{ 2662, 2656 },
	{ 2663, 2657 },
	{ 2664, 2658 },
	{ 2665, 2653 },
	{ 2666, 2654 },
	{ 2667, 2655 },
	{ 2668, 2656 },
	{ 2669, 2657 },
	{ 2670, 2658 },
	{ 2681, 2677 },
	{ 2682, 2678 },
	{ 2683, 2679 },
	{ 2684, 2680 },
	{ 2685, 2677 },
	{ 2686, 2678 },
	{ 2687, 2679 },
	{ 2688, 2680 },
	{ 2698, 2697 },
	{ 2699, 2697 },
	{ 2707, 2706 },
	{ 2708, 2705 },
	{ 2709, 2706 },
	{ 2712, 2711 },
	{ 2713, 2711 },
	{ 2737, 2739 },
	{ 2738, 2739 },
	{ 2741, 2740 },
	{ 2742, 2740 },
	{ 2779, 2770 },
	{ 2780, 2771 },
	{ 2781, 2772 },
	{ 2782, 2773 },
	{ 2783, 2774 },
	{ 2784, 2775 },
	{ 2785, 2776 },
	{ 2786, 2777 },
	{ 2787, 2778 },
	{ 2788, 2770 },
	{ 2790, 2772 },
	{ 2791, 2773 },
	{ 2793, 2775 },
	{ 2794, 2776 },
	{ 2795, 2777 },
	{ 2796, 2778 },
	{ 2812, 2811 },
	{ 2823, 2820 },
	{ 2824, 2821 },
	{ 2825, 2822 },
	{ 2838, 2837 },
	{ 2852, 2849 },
	{ 2853, 2850 },
	{ 2854, 2851 },
	{ 2886, 2879 },
	{ 2887, 2880 },
	{ 2888, 2881 },
	{ 2889, 2882 },
	{ 2890, 2883 },
	{ 2891, 2884 },
	{ 2892, 2885 },
	{ 2893, 2879 },
	{ 2898, 2884 },
	{ 2899, 2885 },
	{ 2921, 2919 },
	{ 2922, 2920 },
	{ 2923, 2919 },
	{ 2924, 2920 },
	{ 4198, 4165 },
	{ 4199, 4166 },
	{ 4200, 4167 },
	{ 4201, 4168 },
	{ 4202, 4169 },
	{ 4203, 4170 },
	{ 4204, 4171 },
	{ 4205, 4172 },
	{ 4206, 4173 },
	{ 4207, 4174 },
	{ 4208, 4175 },
	{ 4209, 4176 },
	{ 4210, 4177 },
	{ 4211, 4178 },
	{ 4212, 4179 },
	{ 4213, 4180 },
	{ 4214, 4181 },
	{ 4215, 4182 },
	{ 4216, 4183 },
	{ 4217, 4184 },
	{ 4218, 4185 },
	{ 4219, 4186 },
	{ 4220, 4187 },
	{ 4221, 4188 },
	{ 4222, 4189 },
	{ 4223, 4190 },
	{ 4224, 4191 },
	{ 4225, 4192 },
	{ 4226, 4193 },
	{ 4227, 4194 },
	{ 4228, 4195 },
	{ 4229, 4196 },
	{ 4230, 4197 },
	{ 4231, 4165 },
	{ 4232, 4166 },
	{ 4233, 4167 },
	{ 4234, 4168 },
	{ 4235, 4169 },
	{ 4236, 4170 },
	{ 4237, 4171 },
	{ 4238, 4172 },
	{ 4239, 4173 },
	{ 4240, 4174 },
	{ 4241, 4175 },
	{ 4242, 4176 },
	{ 4243, 4177 },
	{ 4244, 4178 },
	{ 4245, 4179 },
	{ 4246, 4180 },
	{ 4247, 4181 },
	{ 4248, 4182 },
	{ 4249, 4183 },
	{ 4250, 4184 },
	{ 4251, 4185 },
	{ 4252, 4186 },
	{ 4253, 4187 },
	{ 4254, 4188 },
	{ 4255, 4189 },
	{ 4256, 4190 },
	{ 4257, 4191 },
	{ 4258, 4192 },
	{ 4259, 4193 },
	{ 4260, 4194 },
	{ 4261, 4195 },
	{ 4262, 4196 },
	{ 4263, 4197 },
	{ 4608, 4637 },
	{ 4609, 4638 },
	{ 4619, 4639 },
	{ 4620, 4640 },
	{ 4729, 4739 },
	{ 4730, 4740 },
	{ 4737, 4741 },
	{ 4738, 4742 },
	{ 4795, 4817 },
	{ 4796, 4818 },
	{ 4797, 4819 },
	{ 4800, 4820 },
	{ 4801, 4821 },
	{ 4802, 4822 },
	{ 4803, 4823 },
	{ 4806, 4824 },
	{ 4807, 4825 },
	{ 4808, 4826 },
	{ 4809, 4827 },
	{ 4812, 4828 },
	{ 4813, 4829 },
	{ 4814, 4830 },
	{ 4815, 4831 },
	{ 4816, 4832 },
	{ 4879, 4901 },
	{ 4880, 4902 },
	{ 4881, 4903 },
	{ 4884, 4904 },
	{ 4885, 4905 },
	{ 4886, 4906 },
	{ 4887, 4907 },
	{ 4890, 4908 },
	{ 4891, 4909 },
	{ 4892, 4910 },
	{ 4893, 4911 },
	{ 4896, 4912 },
	{ 4897, 4913 },
	{ 4898, 4914 },
	{ 4899, 4915 },
	{ 4900, 4916 },
	{ 4947, 4957 },
	{ 4948, 4958 },
	{ 4953, 4955 },
	{ 4954, 4956 },
	{ 5085, 5087 },
	{ 5104, 5106 },
	{ 5105, 5107 },
	{ 5120, 5122 },
	{ 5121, 5123 },
	{ 5406, 5416 },
	{ 5407, 5417 },
	{ 5414, 5418 },
	{ 5415, 5419 },
	{ 5485, 5495 },
	{ 5486, 5496 },
	{ 5493, 5497 },
	{ 5494, 5498 },
	{ 5619, 5624 },
	{ 5620, 5625 },
	{ 5621, 5626 },
	{ 5622, 5627 },
	{ 5623, 5628 },
	{ 5643, 5646 },
	{ 5644, 5647 },
	{ 5645, 5648 },
	{ 5655, 5658 },
	{ 5656, 5659 },
	{ 5657, 5660 },
	{ 5707, 5733 },
	{ 5708, 5734 },
	{ 5709, 5735 },
	{ 5718, 5736 },
	{ 5719, 5737 },
	{ 5720, 5738 },
	{ 5791, 5817 },
	{ 5792, 5818 },
	{ 5793, 5819 },
	{ 5802, 5820 },
	{ 5803, 5821 },
	{ 5804, 5822 },
	{ 5840, 5869 },
	{ 5841, 5870 },
	{ 5851, 5871 },
	{ 5852, 5872 },
	{ 6187, 6185 },
	{ 6188, 6186 },
	{ 6189, 6185 },
	{ 6190, 6186 },
	{ 6198, 6195 },
	{ 6200, 6196 },
	{ 6201, 6197 },
	{ 6202, 6195 },
	{ 6203, 6196 },
	{ 6204, 6197 },
	{ 6225, 6216 },
	{ 6226, 6217 },
	{ 6227, 6218 },
	{ 6228, 6219 },
	{ 6229, 6220 },
	{ 6230, 6221 },
	{ 6231, 6222 },
	{ 6232, 6223 },
	{ 6233, 6224 },
	{ 6234, 6216 },
	{ 6236, 6218 },
	{ 6237, 6219 },
	{ 6239, 6221 },
	{ 6240, 6222 },
	{ 6241, 6223 },
	{ 6242, 6224 },
};

static const uint16_t x86_16_bit_eq_lookup[] = {
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 3, 4, 5, 6, 7, 8, 9, 
	10, 11, 12, 0, 13, 14, 0, 15, 16, 17, 18, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 20, 
	21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 0, 
	32, 33, 0, 0, 34, 35, 36, 37, 38, 39, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 41, 42, 43, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 0, 54, 
	55, 0, 56, 57, 58, 59, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 60, 0, 0, 61, 62, 63, 64, 0, 
	0, 65, 66, 67, 68, 0, 0, 0, 0, 0, 0, 69, 
	70, 71, 72, 73, 74, 75, 76, 0, 0, 0, 0, 77, 
	78, 79, 80, 81, 82, 83, 84, 0, 0, 0, 0, 85, 
	86, 87, 88, 89, 90, 91, 92, 0, 0, 0, 0, 93, 
	94, 95, 96, 97, 98, 99, 100, 0, 0, 0, 0, 0, 
	0, 101, 102, 103, 0, 104, 0, 105, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0, 
	0, 107, 108, 109, 110, 0, 0, 111, 112, 113, 114, 0, 
	0, 115, 116, 117, 118, 0, 0, 119, 120, 121, 122, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 123, 124, 125, 
	126, 0, 0, 0, 0, 0, 0, 127, 128, 129, 130, 0, 
	0, 131, 132, 133, 134, 0, 0, 135, 136, 137, 138, 0, 
	0, 139, 140, 141, 142, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 143, 144, 145, 146, 0, 0, 0, 0, 0, 
	0, 147, 148, 149, 150, 0, 0, 151, 152, 153, 154, 0, 
	0, 0, 0, 0, 0, 155, 156, 157, 158, 0, 0, 159, 
	160, 161, 162, 0, 0, 163, 164, 165, 166, 0, 0, 0, 
	0, 0, 0, 167, 168, 169, 170, 0, 0, 0, 171, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 173, 174, 
	175, 176, 177, 178, 179, 180, 181, 0, 182, 183, 0, 184, 
	185, 186, 187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 188, 
	189, 0, 0, 0, 0, 0, 0, 0, 0, 190, 191, 192, 
	193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 194, 0, 0, 195, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 196, 197, 198, 
	0, 0, 199, 200, 201, 202, 0, 0, 0, 0, 203, 204, 
	205, 206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 207, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 208, 209, 
	210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 211, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 212, 213, 214, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 215, 216, 0, 0, 0, 217, 218, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 219, 
	0, 220, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 221, 222, 0, 223, 224, 0, 225, 
	226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 227, 228, 229, 230, 0, 0, 
	0, 231, 232, 0, 0, 0, 233, 234, 235, 236, 237, 238, 
	0, 0, 0, 0, 0, 0, 0, 0, 239, 240, 241, 242, 
	243, 244, 245, 246, 247, 248, 249, 0, 250, 251, 0, 252, 
	0, 0, 0, 0, 253, 254, 0, 0, 0, 0, 0, 255, 
	256, 257, 0, 0, 258, 259, 260, 261, 0, 0, 0, 0, 
	0, 0, 0, 262, 0, 0, 0, 263, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 264, 265, 0, 266, 
	267, 0, 0, 0, 268, 269, 270, 271, 272, 273, 0, 274, 
	0, 275, 276, 0, 0, 0, 277, 278, 279, 280, 281, 282, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 283, 284, 285, 286, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 287, 0, 288, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 289, 290, 291, 292, 293, 0, 0, 0, 
	0, 0, 0, 0, 294, 0, 0, 0, 295, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 
	297, 0, 0, 0, 0, 298, 299, 300, 301, 0, 0, 302, 
	303, 0, 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 305, 0, 306, 0, 0, 0, 307, 0, 0, 308, 
	309, 0, 310, 311, 0, 312, 313, 0, 314, 315, 0, 0, 
	0, 0, 0, 0, 0, 316, 317, 318, 0, 319, 320, 0, 
	0, 0, 0, 0, 321, 322, 323, 0, 324, 325, 0, 0, 
	0, 326, 327, 0, 0, 328, 329, 0, 0, 0, 0, 330, 
	331, 332, 0, 333, 334, 0, 0, 0, 0, 0, 0, 335, 
	336, 337, 0, 338, 339, 0, 0, 0, 0, 0, 340, 341, 
	342, 0, 343, 344, 0, 0, 0, 345, 346, 0, 0, 0, 
	0, 347, 348, 0, 349, 350, 0, 0, 0, 351, 352, 353, 
	354, 0, 355, 356, 0, 0, 0, 357, 358, 0, 0, 0, 
	359, 360, 361, 362, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 363, 364, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 365, 366, 367, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 368, 369, 370, 0, 0, 0, 0, 
	0, 0, 0, 371, 372, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 373, 374, 0, 0, 375, 376, 377, 378, 379, 0, 0, 
	0, 380, 0, 381, 382, 383, 384, 385, 386, 387, 388, 389, 
	391, 0, 0, 0, 0, 393, 394, 395, 396, 398, 0, 0, 
	0, 400, 0, 401, 402, 403, 404, 405, 406, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	407, 408, 409, 410, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 411, 0, 0, 412, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 413, 0, 414, 0, 
	0, 415, 416, 0, 417, 418, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 419, 
	0, 420, 0, 0, 0, 0, 0, 0, 0, 0, 421, 422, 
	423, 424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 425, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 426, 427, 428, 0, 0, 0, 0, 429, 430, 431, 432, 
	0, 0, 0, 433, 0, 0, 0, 434, 0, 0, 0, 435, 
	0, 0, 0, 436, 0, 0, 0, 0, 437, 438, 439, 440, 
	441, 442, 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	444, 445, 446, 447, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 448, 449, 450, 451, 0, 452, 453, 454, 455, 
	456, 457, 0, 458, 459, 0, 460, 461, 462, 463, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	464, 465, 0, 0, 0, 466, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 467, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	469, 470, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 471, 472, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 473, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 474, 475, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 476, 477, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 478, 479, 480, 481, 482, 483, 0, 484, 0, 0, 485, 
	486, 487, 488, 0, 489, 0, 490, 0, 491, 492, 0, 493, 
	494, 0, 495, 496, 0, 497, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 498, 499, 
	500, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 501, 502, 503, 0, 0, 0, 0, 0, 0, 0, 
	504, 505, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	506, 507, 508, 509, 0, 510, 511, 512, 513, 514, 0, 515, 
	0, 516, 0, 517, 0, 518, 0, 519, 520, 0, 521, 522, 
	0, 523, 524, 0, 525, 0, 526, 0, 0, 0, 0, 0, 
	0, 0, 0, 527, 528, 529, 530, 531, 532, 533, 534, 535, 
	536, 537, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 539, 
	540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	551, 552, 0, 553, 554, 0, 0, 0, 555, 556, 0, 0, 
	0, 0, 0, 0, 557, 0, 0, 0, 0, 0, 0, 0, 
	558, 0, 0, 559, 560, 0, 561, 562, 0, 0, 0, 0, 
	0, 0, 0, 0, 563, 564, 565, 566, 567, 568, 569, 570, 
	571, 572, 573, 574, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 575, 576, 577, 578, 579, 580, 581, 582, 
	583, 584, 585, 586, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 587, 588, 589, 590, 591, 592, 593, 594, 
	595, 596, 597, 598, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 599, 600, 601, 602, 603, 604, 605, 
	606, 607, 608, 609, 610, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 0, 621, 
	622, 0, 623, 624, 625, 626, 0, 0, 0, 0, 0, 0, 
	0, 0, 627, 628, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 629, 630, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 631, 632, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 633, 634, 635, 636, 637, 638, 639, 640, 641, 
	642, 643, 644, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 645, 646, 647, 648, 649, 650, 651, 652, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 653, 654, 655, 656, 657, 
	658, 659, 660, 661, 662, 663, 664, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 665, 666, 667, 668, 669, 670, 671, 
	672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 673, 674, 
	0, 0, 0, 0, 0, 0, 0, 675, 676, 677, 0, 0, 
	678, 679, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 680, 681, 0, 0, 682, 683, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 684, 685, 686, 687, 688, 
	689, 690, 691, 692, 693, 0, 694, 695, 0, 696, 697, 698, 
	699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 700, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 701, 702, 703, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 704, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 705, 706, 707, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 708, 709, 710, 711, 712, 713, 
	714, 715, 0, 0, 0, 0, 716, 717, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 718, 719, 720, 721, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 722, 723, 
	724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 
	736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 
	748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 
	760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 
	772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 
	784, 785, 786, 787, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	788, 789, 0, 0, 0, 0, 0, 0, 0, 0, 0, 790, 
	791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 792, 793, 0, 0, 0, 0, 0, 0, 794, 795, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 796, 797, 798, 0, 0, 
	799, 800, 801, 802, 0, 0, 803, 804, 805, 806, 0, 0, 
	807, 808, 809, 810, 811, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 812, 813, 814, 0, 0, 
	815, 816, 817, 818, 0, 0, 819, 820, 821, 822, 0, 0, 
	823, 824, 825, 826, 827, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 828, 829, 0, 0, 0, 0, 830, 831, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 832, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 833, 834, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 835, 836, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 837, 838, 0, 0, 0, 0, 
	0, 0, 839, 840, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 841, 842, 0, 0, 0, 0, 0, 0, 843, 844, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 845, 846, 847, 848, 849, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 850, 851, 852, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 853, 854, 855, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 856, 857, 858, 0, 0, 
	0, 0, 0, 0, 0, 0, 859, 860, 861, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 862, 863, 864, 0, 0, 
	0, 0, 0, 0, 0, 0, 865, 866, 867, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 868, 869, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 870, 871, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 872, 873, 874, 875, 0, 
	0, 0, 0, 0, 0, 0, 876, 0, 877, 878, 879, 880, 
	881, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 882, 883, 884, 
	885, 886, 887, 888, 889, 890, 891, 0, 892, 893, 0, 894, 
	895, 896, 897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0
};

#endif // GET_INSTRINFO_MC_DESC
