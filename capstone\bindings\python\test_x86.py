#!/usr/bin/env python

# Capstone Python bindings, by <PERSON><PERSON><PERSON>nnh <<EMAIL>>
from __future__ import print_function
from capstone import *
from capstone.x86 import *
from xprint import to_hex, to_x, to_x_32


X86_CODE64 = b"\x55\x48\x8b\x05\xb8\x13\x00\x00"
X86_CODE16 = b"\x8d\x4c\x32\x08\x01\xd8\x81\xc6\x34\x12\x00\x00\x05\x23\x01\x00\x00\x36\x8b\x84\x91\x23\x01\x00\x00\x41\x8d\x84\x39\x89\x67\x00\x00\x8d\x87\x89\x67\x00\x00\xb4\xc6"
X86_CODE32 = b"\x8d\x4c\x32\x08\x01\xd8\x81\xc6\x34\x12\x00\x00\x05\x23\x01\x00\x00\x36\x8b\x84\x91\x23\x01\x00\x00\x41\x8d\x84\x39\x89\x67\x00\x00\x8d\x87\x89\x67\x00\x00\xb4\xc6"

all_tests = (
        (CS_ARCH_X86, CS_MODE_16, X86_CODE16, "X86 16bit (Intel syntax)", 0),
        (CS_ARCH_X86, CS_MODE_32, X86_CODE32, "X86 32 (AT&T syntax)", CS_OPT_SYNTAX_ATT),
        (CS_ARCH_X86, CS_MODE_32, X86_CODE32, "X86 32 (Intel syntax)", 0),
        (CS_ARCH_X86, CS_MODE_64, X86_CODE64, "X86 64 (Intel syntax)", 0),
        )


def print_insn_detail(mode, insn):
    def print_string_hex(comment, str):
        print(comment, end=' '),
        for c in str:
            print("0x%02x " % c, end=''),
        print()

    # print address, mnemonic and operands
    print("0x%x:\t%s\t%s" % (insn.address, insn.mnemonic, insn.op_str))

    # "data" instruction generated by SKIPDATA option has no detail
    if insn.id == 0:
        return

    # print instruction prefix
    print_string_hex("\tPrefix:", insn.prefix)

    # print instruction's opcode
    print_string_hex("\tOpcode:", insn.opcode)

    # print operand's REX prefix (non-zero value is relavant for x86_64 instructions)
    print("\trex: 0x%x" % (insn.rex))

    # print operand's address size
    print("\taddr_size: %u" % (insn.addr_size))

    # print modRM byte
    print("\tmodrm: 0x%x" % (insn.modrm))

    # print displacement value
    print("\tdisp: 0x%s" % to_x_32(insn.disp))

    # SIB is not available in 16-bit mode
    if (mode & CS_MODE_16 == 0):
        # print SIB byte
        print("\tsib: 0x%x" % (insn.sib))
        if (insn.sib):
            if insn.sib_base != 0:
                print("\t\tsib_base: %s" % (insn.reg_name(insn.sib_base)))
            if insn.sib_index != 0:
                print("\t\tsib_index: %s" % (insn.reg_name(insn.sib_index)))
            if insn.sib_scale != 0:
                print("\t\tsib_scale: %d" % (insn.sib_scale))

    # SSE CC type
    if insn.sse_cc != X86_SSE_CC_INVALID:
        print("\tsse_cc: %u" % (insn.sse_cc))

    # AVX CC type
    if insn.avx_cc != X86_AVX_CC_INVALID:
        print("\tavx_cc: %u" % (insn.avx_cc))

    # AVX Suppress All Exception
    if insn.avx_sae:
        print("\tavx_sae: TRUE")

    # AVX Rounding Mode type
    if insn.avx_rm != X86_AVX_RM_INVALID:
        print("\tavx_rm: %u" % (insn.avx_rm))

    count = insn.op_count(X86_OP_IMM)
    if count > 0:
        print("\timm_count: %u" % count)
        for i in range(count):
            op = insn.op_find(X86_OP_IMM, i + 1)
            print("\t\timms[%u]: 0x%s" % (i + 1, to_x(op.imm)))

    if len(insn.operands) > 0:
        print("\top_count: %u" % len(insn.operands))
        c = -1
        for i in insn.operands:
            c += 1
            if i.type == X86_OP_REG:
                print("\t\toperands[%u].type: REG = %s" % (c, insn.reg_name(i.reg)))
            if i.type == X86_OP_IMM:
                print("\t\toperands[%u].type: IMM = 0x%s" % (c, to_x(i.imm)))
            if i.type == X86_OP_FP:
                print("\t\toperands[%u].type: FP = %f" % (c, i.fp))
            if i.type == X86_OP_MEM:
                print("\t\toperands[%u].type: MEM" % c)
                if i.mem.segment != 0:
                    print("\t\t\toperands[%u].mem.segment: REG = %s" % (c, insn.reg_name(i.mem.segment)))
                if i.mem.base != 0:
                    print("\t\t\toperands[%u].mem.base: REG = %s" % (c, insn.reg_name(i.mem.base)))
                if i.mem.index != 0:
                    print("\t\t\toperands[%u].mem.index: REG = %s" % (c, insn.reg_name(i.mem.index)))
                if i.mem.scale != 1:
                    print("\t\t\toperands[%u].mem.scale: %u" % (c, i.mem.scale))
                if i.mem.disp != 0:
                    print("\t\t\toperands[%u].mem.disp: 0x%s" % (c, to_x(i.mem.disp)))

            # AVX broadcast type
            if i.avx_bcast != X86_AVX_BCAST_INVALID:
                print("\t\toperands[%u].avx_bcast: %u" % (c, i.avx_bcast))

            # AVX zero opmask {z}
            if i.avx_zero_opmask:
                print("\t\toperands[%u].avx_zero_opmask: TRUE" % (c))

            print("\t\toperands[%u].size: %u" % (c, i.size))


# ## Test class Cs
def test_class():

    for (arch, mode, code, comment, syntax) in all_tests:
        print("*" * 16)
        print("Platform: %s" % comment)
        print("Code: %s" % to_hex(code))
        print("Disasm:")

        try:
            md = Cs(arch, mode)
            md.detail = True

            if syntax != 0:
                md.syntax = syntax

            for insn in md.disasm(code, 0x1000):
                print_insn_detail(mode, insn)
                print ()
            print ("0x%x:\n" % (insn.address + insn.size))
        except CsError as e:
            print("ERROR: %s" % e)


if __name__ == '__main__':
    test_class()
