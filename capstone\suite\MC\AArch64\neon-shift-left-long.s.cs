# CS_ARCH_ARM64, 0, None
0x20,0xa4,0x0b,0x0f = sshll v0.8h, v1.8b, #3
0x20,0xa4,0x13,0x0f = sshll v0.4s, v1.4h, #3
0x20,0xa4,0x23,0x0f = sshll v0.2d, v1.2s, #3
0x20,0xa4,0x0b,0x4f = sshll2 v0.8h, v1.16b, #3
0x20,0xa4,0x13,0x4f = sshll2 v0.4s, v1.8h, #3
0x20,0xa4,0x23,0x4f = sshll2 v0.2d, v1.4s, #3
0x20,0xa4,0x0b,0x2f = ushll v0.8h, v1.8b, #3
0x20,0xa4,0x13,0x2f = ushll v0.4s, v1.4h, #3
0x20,0xa4,0x23,0x2f = ushll v0.2d, v1.2s, #3
0x20,0xa4,0x0b,0x6f = ushll2 v0.8h, v1.16b, #3
0x20,0xa4,0x13,0x6f = ushll2 v0.4s, v1.8h, #3
0x20,0xa4,0x23,0x6f = ushll2 v0.2d, v1.4s, #3
