/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    X86_PHI	= 0,
    X86_INLINEASM	= 1,
    X86_CFI_INSTRUCTION	= 2,
    X86_EH_LABEL	= 3,
    X86_GC_LABEL	= 4,
    X86_KILL	= 5,
    X86_EXTRACT_SUBREG	= 6,
    X86_INSERT_SUBREG	= 7,
    X86_IMPLICIT_DEF	= 8,
    X86_SUBREG_TO_REG	= 9,
    X86_COPY_TO_REGCLASS	= 10,
    X86_DBG_VALUE	= 11,
    X86_REG_SEQUENCE	= 12,
    X86_COPY	= 13,
    X86_BUNDLE	= 14,
    X86_LIFETIME_START	= 15,
    X86_LIFETIME_END	= 16,
    X86_STACKMAP	= 17,
    X86_PATCHPOINT	= 18,
    X86_LOAD_STACK_GUARD	= 19,
    X86_AAA	= 20,
    X86_AAD8i8	= 21,
    X86_AAM8i8	= 22,
    X86_AAS	= 23,
    X86_ACQUIRE_MOV16rm	= 24,
    X86_ACQUIRE_MOV32rm	= 25,
    X86_ACQUIRE_MOV64rm	= 26,
    X86_ACQUIRE_MOV8rm	= 27,
    X86_ADC16i16	= 28,
    X86_ADC16mi	= 29,
    X86_ADC16mi8	= 30,
    X86_ADC16mr	= 31,
    X86_ADC16ri	= 32,
    X86_ADC16ri8	= 33,
    X86_ADC16rm	= 34,
    X86_ADC16rr	= 35,
    X86_ADC16rr_REV	= 36,
    X86_ADC32i32	= 37,
    X86_ADC32mi	= 38,
    X86_ADC32mi8	= 39,
    X86_ADC32mr	= 40,
    X86_ADC32ri	= 41,
    X86_ADC32ri8	= 42,
    X86_ADC32rm	= 43,
    X86_ADC32rr	= 44,
    X86_ADC32rr_REV	= 45,
    X86_ADC64i32	= 46,
    X86_ADC64mi32	= 47,
    X86_ADC64mi8	= 48,
    X86_ADC64mr	= 49,
    X86_ADC64ri32	= 50,
    X86_ADC64ri8	= 51,
    X86_ADC64rm	= 52,
    X86_ADC64rr	= 53,
    X86_ADC64rr_REV	= 54,
    X86_ADC8i8	= 55,
    X86_ADC8mi	= 56,
    X86_ADC8mr	= 57,
    X86_ADC8ri	= 58,
    X86_ADC8rm	= 59,
    X86_ADC8rr	= 60,
    X86_ADC8rr_REV	= 61,
    X86_ADCX32rm	= 62,
    X86_ADCX32rr	= 63,
    X86_ADCX64rm	= 64,
    X86_ADCX64rr	= 65,
    X86_ADD16i16	= 66,
    X86_ADD16mi	= 67,
    X86_ADD16mi8	= 68,
    X86_ADD16mr	= 69,
    X86_ADD16ri	= 70,
    X86_ADD16ri8	= 71,
    X86_ADD16ri8_DB	= 72,
    X86_ADD16ri_DB	= 73,
    X86_ADD16rm	= 74,
    X86_ADD16rr	= 75,
    X86_ADD16rr_DB	= 76,
    X86_ADD16rr_REV	= 77,
    X86_ADD32i32	= 78,
    X86_ADD32mi	= 79,
    X86_ADD32mi8	= 80,
    X86_ADD32mr	= 81,
    X86_ADD32ri	= 82,
    X86_ADD32ri8	= 83,
    X86_ADD32ri8_DB	= 84,
    X86_ADD32ri_DB	= 85,
    X86_ADD32rm	= 86,
    X86_ADD32rr	= 87,
    X86_ADD32rr_DB	= 88,
    X86_ADD32rr_REV	= 89,
    X86_ADD64i32	= 90,
    X86_ADD64mi32	= 91,
    X86_ADD64mi8	= 92,
    X86_ADD64mr	= 93,
    X86_ADD64ri32	= 94,
    X86_ADD64ri32_DB	= 95,
    X86_ADD64ri8	= 96,
    X86_ADD64ri8_DB	= 97,
    X86_ADD64rm	= 98,
    X86_ADD64rr	= 99,
    X86_ADD64rr_DB	= 100,
    X86_ADD64rr_REV	= 101,
    X86_ADD8i8	= 102,
    X86_ADD8mi	= 103,
    X86_ADD8mr	= 104,
    X86_ADD8ri	= 105,
    X86_ADD8ri8	= 106,
    X86_ADD8rm	= 107,
    X86_ADD8rr	= 108,
    X86_ADD8rr_REV	= 109,
    X86_ADJCALLSTACKDOWN32	= 110,
    X86_ADJCALLSTACKDOWN64	= 111,
    X86_ADJCALLSTACKUP32	= 112,
    X86_ADJCALLSTACKUP64	= 113,
    X86_ADOX32rm	= 114,
    X86_ADOX32rr	= 115,
    X86_ADOX64rm	= 116,
    X86_ADOX64rr	= 117,
    X86_AND16i16	= 118,
    X86_AND16mi	= 119,
    X86_AND16mi8	= 120,
    X86_AND16mr	= 121,
    X86_AND16ri	= 122,
    X86_AND16ri8	= 123,
    X86_AND16rm	= 124,
    X86_AND16rr	= 125,
    X86_AND16rr_REV	= 126,
    X86_AND32i32	= 127,
    X86_AND32mi	= 128,
    X86_AND32mi8	= 129,
    X86_AND32mr	= 130,
    X86_AND32ri	= 131,
    X86_AND32ri8	= 132,
    X86_AND32rm	= 133,
    X86_AND32rr	= 134,
    X86_AND32rr_REV	= 135,
    X86_AND64i32	= 136,
    X86_AND64mi32	= 137,
    X86_AND64mi8	= 138,
    X86_AND64mr	= 139,
    X86_AND64ri32	= 140,
    X86_AND64ri8	= 141,
    X86_AND64rm	= 142,
    X86_AND64rr	= 143,
    X86_AND64rr_REV	= 144,
    X86_AND8i8	= 145,
    X86_AND8mi	= 146,
    X86_AND8mr	= 147,
    X86_AND8ri	= 148,
    X86_AND8ri8	= 149,
    X86_AND8rm	= 150,
    X86_AND8rr	= 151,
    X86_AND8rr_REV	= 152,
    X86_ANDN32rm	= 153,
    X86_ANDN32rr	= 154,
    X86_ANDN64rm	= 155,
    X86_ANDN64rr	= 156,
    X86_ARPL16mr	= 157,
    X86_ARPL16rr	= 158,
    X86_BEXTR32rm	= 159,
    X86_BEXTR32rr	= 160,
    X86_BEXTR64rm	= 161,
    X86_BEXTR64rr	= 162,
    X86_BEXTRI32mi	= 163,
    X86_BEXTRI32ri	= 164,
    X86_BEXTRI64mi	= 165,
    X86_BEXTRI64ri	= 166,
    X86_BLCFILL32rm	= 167,
    X86_BLCFILL32rr	= 168,
    X86_BLCFILL64rm	= 169,
    X86_BLCFILL64rr	= 170,
    X86_BLCI32rm	= 171,
    X86_BLCI32rr	= 172,
    X86_BLCI64rm	= 173,
    X86_BLCI64rr	= 174,
    X86_BLCIC32rm	= 175,
    X86_BLCIC32rr	= 176,
    X86_BLCIC64rm	= 177,
    X86_BLCIC64rr	= 178,
    X86_BLCMSK32rm	= 179,
    X86_BLCMSK32rr	= 180,
    X86_BLCMSK64rm	= 181,
    X86_BLCMSK64rr	= 182,
    X86_BLCS32rm	= 183,
    X86_BLCS32rr	= 184,
    X86_BLCS64rm	= 185,
    X86_BLCS64rr	= 186,
    X86_BLSFILL32rm	= 187,
    X86_BLSFILL32rr	= 188,
    X86_BLSFILL64rm	= 189,
    X86_BLSFILL64rr	= 190,
    X86_BLSI32rm	= 191,
    X86_BLSI32rr	= 192,
    X86_BLSI64rm	= 193,
    X86_BLSI64rr	= 194,
    X86_BLSIC32rm	= 195,
    X86_BLSIC32rr	= 196,
    X86_BLSIC64rm	= 197,
    X86_BLSIC64rr	= 198,
    X86_BLSMSK32rm	= 199,
    X86_BLSMSK32rr	= 200,
    X86_BLSMSK64rm	= 201,
    X86_BLSMSK64rr	= 202,
    X86_BLSR32rm	= 203,
    X86_BLSR32rr	= 204,
    X86_BLSR64rm	= 205,
    X86_BLSR64rr	= 206,
    X86_BOUNDS16rm	= 207,
    X86_BOUNDS32rm	= 208,
    X86_BSF16rm	= 209,
    X86_BSF16rr	= 210,
    X86_BSF32rm	= 211,
    X86_BSF32rr	= 212,
    X86_BSF64rm	= 213,
    X86_BSF64rr	= 214,
    X86_BSR16rm	= 215,
    X86_BSR16rr	= 216,
    X86_BSR32rm	= 217,
    X86_BSR32rr	= 218,
    X86_BSR64rm	= 219,
    X86_BSR64rr	= 220,
    X86_BSWAP32r	= 221,
    X86_BSWAP64r	= 222,
    X86_BT16mi8	= 223,
    X86_BT16mr	= 224,
    X86_BT16ri8	= 225,
    X86_BT16rr	= 226,
    X86_BT32mi8	= 227,
    X86_BT32mr	= 228,
    X86_BT32ri8	= 229,
    X86_BT32rr	= 230,
    X86_BT64mi8	= 231,
    X86_BT64mr	= 232,
    X86_BT64ri8	= 233,
    X86_BT64rr	= 234,
    X86_BTC16mi8	= 235,
    X86_BTC16mr	= 236,
    X86_BTC16ri8	= 237,
    X86_BTC16rr	= 238,
    X86_BTC32mi8	= 239,
    X86_BTC32mr	= 240,
    X86_BTC32ri8	= 241,
    X86_BTC32rr	= 242,
    X86_BTC64mi8	= 243,
    X86_BTC64mr	= 244,
    X86_BTC64ri8	= 245,
    X86_BTC64rr	= 246,
    X86_BTR16mi8	= 247,
    X86_BTR16mr	= 248,
    X86_BTR16ri8	= 249,
    X86_BTR16rr	= 250,
    X86_BTR32mi8	= 251,
    X86_BTR32mr	= 252,
    X86_BTR32ri8	= 253,
    X86_BTR32rr	= 254,
    X86_BTR64mi8	= 255,
    X86_BTR64mr	= 256,
    X86_BTR64ri8	= 257,
    X86_BTR64rr	= 258,
    X86_BTS16mi8	= 259,
    X86_BTS16mr	= 260,
    X86_BTS16ri8	= 261,
    X86_BTS16rr	= 262,
    X86_BTS32mi8	= 263,
    X86_BTS32mr	= 264,
    X86_BTS32ri8	= 265,
    X86_BTS32rr	= 266,
    X86_BTS64mi8	= 267,
    X86_BTS64mr	= 268,
    X86_BTS64ri8	= 269,
    X86_BTS64rr	= 270,
    X86_BZHI32rm	= 271,
    X86_BZHI32rr	= 272,
    X86_BZHI64rm	= 273,
    X86_BZHI64rr	= 274,
    X86_CALL16m	= 275,
    X86_CALL16r	= 276,
    X86_CALL32m	= 277,
    X86_CALL32r	= 278,
    X86_CALL64m	= 279,
    X86_CALL64pcrel32	= 280,
    X86_CALL64r	= 281,
    X86_CALLpcrel16	= 282,
    X86_CALLpcrel32	= 283,
    X86_CBW	= 284,
    X86_CDQ	= 285,
    X86_CDQE	= 286,
    X86_CLAC	= 287,
    X86_CLC	= 288,
    X86_CLD	= 289,
    X86_CLGI	= 290,
    X86_CLI	= 291,
    X86_CLTS	= 292,
    X86_CMC	= 293,
    X86_CMOVA16rm	= 294,
    X86_CMOVA16rr	= 295,
    X86_CMOVA32rm	= 296,
    X86_CMOVA32rr	= 297,
    X86_CMOVA64rm	= 298,
    X86_CMOVA64rr	= 299,
    X86_CMOVAE16rm	= 300,
    X86_CMOVAE16rr	= 301,
    X86_CMOVAE32rm	= 302,
    X86_CMOVAE32rr	= 303,
    X86_CMOVAE64rm	= 304,
    X86_CMOVAE64rr	= 305,
    X86_CMOVB16rm	= 306,
    X86_CMOVB16rr	= 307,
    X86_CMOVB32rm	= 308,
    X86_CMOVB32rr	= 309,
    X86_CMOVB64rm	= 310,
    X86_CMOVB64rr	= 311,
    X86_CMOVBE16rm	= 312,
    X86_CMOVBE16rr	= 313,
    X86_CMOVBE32rm	= 314,
    X86_CMOVBE32rr	= 315,
    X86_CMOVBE64rm	= 316,
    X86_CMOVBE64rr	= 317,
    X86_CMOVE16rm	= 318,
    X86_CMOVE16rr	= 319,
    X86_CMOVE32rm	= 320,
    X86_CMOVE32rr	= 321,
    X86_CMOVE64rm	= 322,
    X86_CMOVE64rr	= 323,
    X86_CMOVG16rm	= 324,
    X86_CMOVG16rr	= 325,
    X86_CMOVG32rm	= 326,
    X86_CMOVG32rr	= 327,
    X86_CMOVG64rm	= 328,
    X86_CMOVG64rr	= 329,
    X86_CMOVGE16rm	= 330,
    X86_CMOVGE16rr	= 331,
    X86_CMOVGE32rm	= 332,
    X86_CMOVGE32rr	= 333,
    X86_CMOVGE64rm	= 334,
    X86_CMOVGE64rr	= 335,
    X86_CMOVL16rm	= 336,
    X86_CMOVL16rr	= 337,
    X86_CMOVL32rm	= 338,
    X86_CMOVL32rr	= 339,
    X86_CMOVL64rm	= 340,
    X86_CMOVL64rr	= 341,
    X86_CMOVLE16rm	= 342,
    X86_CMOVLE16rr	= 343,
    X86_CMOVLE32rm	= 344,
    X86_CMOVLE32rr	= 345,
    X86_CMOVLE64rm	= 346,
    X86_CMOVLE64rr	= 347,
    X86_CMOVNE16rm	= 348,
    X86_CMOVNE16rr	= 349,
    X86_CMOVNE32rm	= 350,
    X86_CMOVNE32rr	= 351,
    X86_CMOVNE64rm	= 352,
    X86_CMOVNE64rr	= 353,
    X86_CMOVNO16rm	= 354,
    X86_CMOVNO16rr	= 355,
    X86_CMOVNO32rm	= 356,
    X86_CMOVNO32rr	= 357,
    X86_CMOVNO64rm	= 358,
    X86_CMOVNO64rr	= 359,
    X86_CMOVNP16rm	= 360,
    X86_CMOVNP16rr	= 361,
    X86_CMOVNP32rm	= 362,
    X86_CMOVNP32rr	= 363,
    X86_CMOVNP64rm	= 364,
    X86_CMOVNP64rr	= 365,
    X86_CMOVNS16rm	= 366,
    X86_CMOVNS16rr	= 367,
    X86_CMOVNS32rm	= 368,
    X86_CMOVNS32rr	= 369,
    X86_CMOVNS64rm	= 370,
    X86_CMOVNS64rr	= 371,
    X86_CMOVO16rm	= 372,
    X86_CMOVO16rr	= 373,
    X86_CMOVO32rm	= 374,
    X86_CMOVO32rr	= 375,
    X86_CMOVO64rm	= 376,
    X86_CMOVO64rr	= 377,
    X86_CMOVP16rm	= 378,
    X86_CMOVP16rr	= 379,
    X86_CMOVP32rm	= 380,
    X86_CMOVP32rr	= 381,
    X86_CMOVP64rm	= 382,
    X86_CMOVP64rr	= 383,
    X86_CMOVS16rm	= 384,
    X86_CMOVS16rr	= 385,
    X86_CMOVS32rm	= 386,
    X86_CMOVS32rr	= 387,
    X86_CMOVS64rm	= 388,
    X86_CMOVS64rr	= 389,
    X86_CMOV_FR32	= 390,
    X86_CMOV_FR64	= 391,
    X86_CMOV_GR16	= 392,
    X86_CMOV_GR32	= 393,
    X86_CMOV_GR8	= 394,
    X86_CMOV_RFP32	= 395,
    X86_CMOV_RFP64	= 396,
    X86_CMOV_RFP80	= 397,
    X86_CMOV_V16F32	= 398,
    X86_CMOV_V2F64	= 399,
    X86_CMOV_V2I64	= 400,
    X86_CMOV_V4F32	= 401,
    X86_CMOV_V4F64	= 402,
    X86_CMOV_V4I64	= 403,
    X86_CMOV_V8F32	= 404,
    X86_CMOV_V8F64	= 405,
    X86_CMOV_V8I64	= 406,
    X86_CMP16i16	= 407,
    X86_CMP16mi	= 408,
    X86_CMP16mi8	= 409,
    X86_CMP16mr	= 410,
    X86_CMP16ri	= 411,
    X86_CMP16ri8	= 412,
    X86_CMP16rm	= 413,
    X86_CMP16rr	= 414,
    X86_CMP16rr_REV	= 415,
    X86_CMP32i32	= 416,
    X86_CMP32mi	= 417,
    X86_CMP32mi8	= 418,
    X86_CMP32mr	= 419,
    X86_CMP32ri	= 420,
    X86_CMP32ri8	= 421,
    X86_CMP32rm	= 422,
    X86_CMP32rr	= 423,
    X86_CMP32rr_REV	= 424,
    X86_CMP64i32	= 425,
    X86_CMP64mi32	= 426,
    X86_CMP64mi8	= 427,
    X86_CMP64mr	= 428,
    X86_CMP64ri32	= 429,
    X86_CMP64ri8	= 430,
    X86_CMP64rm	= 431,
    X86_CMP64rr	= 432,
    X86_CMP64rr_REV	= 433,
    X86_CMP8i8	= 434,
    X86_CMP8mi	= 435,
    X86_CMP8mr	= 436,
    X86_CMP8ri	= 437,
    X86_CMP8rm	= 438,
    X86_CMP8rr	= 439,
    X86_CMP8rr_REV	= 440,
    X86_CMPSB	= 441,
    X86_CMPSL	= 442,
    X86_CMPSQ	= 443,
    X86_CMPSW	= 444,
    X86_CMPXCHG16B	= 445,
    X86_CMPXCHG16rm	= 446,
    X86_CMPXCHG16rr	= 447,
    X86_CMPXCHG32rm	= 448,
    X86_CMPXCHG32rr	= 449,
    X86_CMPXCHG64rm	= 450,
    X86_CMPXCHG64rr	= 451,
    X86_CMPXCHG8B	= 452,
    X86_CMPXCHG8rm	= 453,
    X86_CMPXCHG8rr	= 454,
    X86_CPUID32	= 455,
    X86_CPUID64	= 456,
    X86_CQO	= 457,
    X86_CWD	= 458,
    X86_CWDE	= 459,
    X86_DAA	= 460,
    X86_DAS	= 461,
    X86_DATA16_PREFIX	= 462,
    X86_DEC16m	= 463,
    X86_DEC16r	= 464,
    X86_DEC32_16r	= 465,
    X86_DEC32_32r	= 466,
    X86_DEC32m	= 467,
    X86_DEC32r	= 468,
    X86_DEC64_16m	= 469,
    X86_DEC64_16r	= 470,
    X86_DEC64_32m	= 471,
    X86_DEC64_32r	= 472,
    X86_DEC64m	= 473,
    X86_DEC64r	= 474,
    X86_DEC8m	= 475,
    X86_DEC8r	= 476,
    X86_DIV16m	= 477,
    X86_DIV16r	= 478,
    X86_DIV32m	= 479,
    X86_DIV32r	= 480,
    X86_DIV64m	= 481,
    X86_DIV64r	= 482,
    X86_DIV8m	= 483,
    X86_DIV8r	= 484,
    X86_EH_RETURN	= 485,
    X86_EH_RETURN64	= 486,
    X86_EH_SjLj_LongJmp32	= 487,
    X86_EH_SjLj_LongJmp64	= 488,
    X86_EH_SjLj_SetJmp32	= 489,
    X86_EH_SjLj_SetJmp64	= 490,
    X86_EH_SjLj_Setup	= 491,
    X86_ENTER	= 492,
    X86_FARCALL16i	= 493,
    X86_FARCALL16m	= 494,
    X86_FARCALL32i	= 495,
    X86_FARCALL32m	= 496,
    X86_FARCALL64	= 497,
    X86_FARJMP16i	= 498,
    X86_FARJMP16m	= 499,
    X86_FARJMP32i	= 500,
    X86_FARJMP32m	= 501,
    X86_FARJMP64	= 502,
    X86_FSETPM	= 503,
    X86_GETSEC	= 504,
    X86_HLT	= 505,
    X86_IDIV16m	= 506,
    X86_IDIV16r	= 507,
    X86_IDIV32m	= 508,
    X86_IDIV32r	= 509,
    X86_IDIV64m	= 510,
    X86_IDIV64r	= 511,
    X86_IDIV8m	= 512,
    X86_IDIV8r	= 513,
    X86_IMUL16m	= 514,
    X86_IMUL16r	= 515,
    X86_IMUL16rm	= 516,
    X86_IMUL16rmi	= 517,
    X86_IMUL16rmi8	= 518,
    X86_IMUL16rr	= 519,
    X86_IMUL16rri	= 520,
    X86_IMUL16rri8	= 521,
    X86_IMUL32m	= 522,
    X86_IMUL32r	= 523,
    X86_IMUL32rm	= 524,
    X86_IMUL32rmi	= 525,
    X86_IMUL32rmi8	= 526,
    X86_IMUL32rr	= 527,
    X86_IMUL32rri	= 528,
    X86_IMUL32rri8	= 529,
    X86_IMUL64m	= 530,
    X86_IMUL64r	= 531,
    X86_IMUL64rm	= 532,
    X86_IMUL64rmi32	= 533,
    X86_IMUL64rmi8	= 534,
    X86_IMUL64rr	= 535,
    X86_IMUL64rri32	= 536,
    X86_IMUL64rri8	= 537,
    X86_IMUL8m	= 538,
    X86_IMUL8r	= 539,
    X86_IN16ri	= 540,
    X86_IN16rr	= 541,
    X86_IN32ri	= 542,
    X86_IN32rr	= 543,
    X86_IN8ri	= 544,
    X86_IN8rr	= 545,
    X86_INC16m	= 546,
    X86_INC16r	= 547,
    X86_INC32_16r	= 548,
    X86_INC32_32r	= 549,
    X86_INC32m	= 550,
    X86_INC32r	= 551,
    X86_INC64_16m	= 552,
    X86_INC64_16r	= 553,
    X86_INC64_32m	= 554,
    X86_INC64_32r	= 555,
    X86_INC64m	= 556,
    X86_INC64r	= 557,
    X86_INC8m	= 558,
    X86_INC8r	= 559,
    X86_INSB	= 560,
    X86_INSL	= 561,
    X86_INSW	= 562,
    X86_INT	= 563,
    X86_INT1	= 564,
    X86_INT3	= 565,
    X86_INTO	= 566,
    X86_INVD	= 567,
    X86_INVEPT32	= 568,
    X86_INVEPT64	= 569,
    X86_INVLPG	= 570,
    X86_INVLPGA32	= 571,
    X86_INVLPGA64	= 572,
    X86_INVPCID32	= 573,
    X86_INVPCID64	= 574,
    X86_INVVPID32	= 575,
    X86_INVVPID64	= 576,
    X86_IRET16	= 577,
    X86_IRET32	= 578,
    X86_IRET64	= 579,
    X86_Int_MemBarrier	= 580,
    X86_JAE_1	= 581,
    X86_JAE_2	= 582,
    X86_JAE_4	= 583,
    X86_JA_1	= 584,
    X86_JA_2	= 585,
    X86_JA_4	= 586,
    X86_JBE_1	= 587,
    X86_JBE_2	= 588,
    X86_JBE_4	= 589,
    X86_JB_1	= 590,
    X86_JB_2	= 591,
    X86_JB_4	= 592,
    X86_JCXZ	= 593,
    X86_JECXZ_32	= 594,
    X86_JECXZ_64	= 595,
    X86_JE_1	= 596,
    X86_JE_2	= 597,
    X86_JE_4	= 598,
    X86_JGE_1	= 599,
    X86_JGE_2	= 600,
    X86_JGE_4	= 601,
    X86_JG_1	= 602,
    X86_JG_2	= 603,
    X86_JG_4	= 604,
    X86_JLE_1	= 605,
    X86_JLE_2	= 606,
    X86_JLE_4	= 607,
    X86_JL_1	= 608,
    X86_JL_2	= 609,
    X86_JL_4	= 610,
    X86_JMP16m	= 611,
    X86_JMP16r	= 612,
    X86_JMP32m	= 613,
    X86_JMP32r	= 614,
    X86_JMP64m	= 615,
    X86_JMP64r	= 616,
    X86_JMP_1	= 617,
    X86_JMP_2	= 618,
    X86_JMP_4	= 619,
    X86_JNE_1	= 620,
    X86_JNE_2	= 621,
    X86_JNE_4	= 622,
    X86_JNO_1	= 623,
    X86_JNO_2	= 624,
    X86_JNO_4	= 625,
    X86_JNP_1	= 626,
    X86_JNP_2	= 627,
    X86_JNP_4	= 628,
    X86_JNS_1	= 629,
    X86_JNS_2	= 630,
    X86_JNS_4	= 631,
    X86_JO_1	= 632,
    X86_JO_2	= 633,
    X86_JO_4	= 634,
    X86_JP_1	= 635,
    X86_JP_2	= 636,
    X86_JP_4	= 637,
    X86_JRCXZ	= 638,
    X86_JS_1	= 639,
    X86_JS_2	= 640,
    X86_JS_4	= 641,
    X86_LAHF	= 642,
    X86_LAR16rm	= 643,
    X86_LAR16rr	= 644,
    X86_LAR32rm	= 645,
    X86_LAR32rr	= 646,
    X86_LAR64rm	= 647,
    X86_LAR64rr	= 648,
    X86_LCMPXCHG16	= 649,
    X86_LCMPXCHG16B	= 650,
    X86_LCMPXCHG32	= 651,
    X86_LCMPXCHG64	= 652,
    X86_LCMPXCHG8	= 653,
    X86_LCMPXCHG8B	= 654,
    X86_LDS16rm	= 655,
    X86_LDS32rm	= 656,
    X86_LEA16r	= 657,
    X86_LEA32r	= 658,
    X86_LEA64_32r	= 659,
    X86_LEA64r	= 660,
    X86_LEAVE	= 661,
    X86_LEAVE64	= 662,
    X86_LES16rm	= 663,
    X86_LES32rm	= 664,
    X86_LFS16rm	= 665,
    X86_LFS32rm	= 666,
    X86_LFS64rm	= 667,
    X86_LGDT16m	= 668,
    X86_LGDT32m	= 669,
    X86_LGDT64m	= 670,
    X86_LGS16rm	= 671,
    X86_LGS32rm	= 672,
    X86_LGS64rm	= 673,
    X86_LIDT16m	= 674,
    X86_LIDT32m	= 675,
    X86_LIDT64m	= 676,
    X86_LLDT16m	= 677,
    X86_LLDT16r	= 678,
    X86_LMSW16m	= 679,
    X86_LMSW16r	= 680,
    X86_LOCK_ADD16mi	= 681,
    X86_LOCK_ADD16mi8	= 682,
    X86_LOCK_ADD16mr	= 683,
    X86_LOCK_ADD32mi	= 684,
    X86_LOCK_ADD32mi8	= 685,
    X86_LOCK_ADD32mr	= 686,
    X86_LOCK_ADD64mi32	= 687,
    X86_LOCK_ADD64mi8	= 688,
    X86_LOCK_ADD64mr	= 689,
    X86_LOCK_ADD8mi	= 690,
    X86_LOCK_ADD8mr	= 691,
    X86_LOCK_AND16mi	= 692,
    X86_LOCK_AND16mi8	= 693,
    X86_LOCK_AND16mr	= 694,
    X86_LOCK_AND32mi	= 695,
    X86_LOCK_AND32mi8	= 696,
    X86_LOCK_AND32mr	= 697,
    X86_LOCK_AND64mi32	= 698,
    X86_LOCK_AND64mi8	= 699,
    X86_LOCK_AND64mr	= 700,
    X86_LOCK_AND8mi	= 701,
    X86_LOCK_AND8mr	= 702,
    X86_LOCK_DEC16m	= 703,
    X86_LOCK_DEC32m	= 704,
    X86_LOCK_DEC64m	= 705,
    X86_LOCK_DEC8m	= 706,
    X86_LOCK_INC16m	= 707,
    X86_LOCK_INC32m	= 708,
    X86_LOCK_INC64m	= 709,
    X86_LOCK_INC8m	= 710,
    X86_LOCK_OR16mi	= 711,
    X86_LOCK_OR16mi8	= 712,
    X86_LOCK_OR16mr	= 713,
    X86_LOCK_OR32mi	= 714,
    X86_LOCK_OR32mi8	= 715,
    X86_LOCK_OR32mr	= 716,
    X86_LOCK_OR64mi32	= 717,
    X86_LOCK_OR64mi8	= 718,
    X86_LOCK_OR64mr	= 719,
    X86_LOCK_OR8mi	= 720,
    X86_LOCK_OR8mr	= 721,
    X86_LOCK_PREFIX	= 722,
    X86_LOCK_SUB16mi	= 723,
    X86_LOCK_SUB16mi8	= 724,
    X86_LOCK_SUB16mr	= 725,
    X86_LOCK_SUB32mi	= 726,
    X86_LOCK_SUB32mi8	= 727,
    X86_LOCK_SUB32mr	= 728,
    X86_LOCK_SUB64mi32	= 729,
    X86_LOCK_SUB64mi8	= 730,
    X86_LOCK_SUB64mr	= 731,
    X86_LOCK_SUB8mi	= 732,
    X86_LOCK_SUB8mr	= 733,
    X86_LOCK_XOR16mi	= 734,
    X86_LOCK_XOR16mi8	= 735,
    X86_LOCK_XOR16mr	= 736,
    X86_LOCK_XOR32mi	= 737,
    X86_LOCK_XOR32mi8	= 738,
    X86_LOCK_XOR32mr	= 739,
    X86_LOCK_XOR64mi32	= 740,
    X86_LOCK_XOR64mi8	= 741,
    X86_LOCK_XOR64mr	= 742,
    X86_LOCK_XOR8mi	= 743,
    X86_LOCK_XOR8mr	= 744,
    X86_LODSB	= 745,
    X86_LODSL	= 746,
    X86_LODSQ	= 747,
    X86_LODSW	= 748,
    X86_LOOP	= 749,
    X86_LOOPE	= 750,
    X86_LOOPNE	= 751,
    X86_LRETIL	= 752,
    X86_LRETIQ	= 753,
    X86_LRETIW	= 754,
    X86_LRETL	= 755,
    X86_LRETQ	= 756,
    X86_LRETW	= 757,
    X86_LSL16rm	= 758,
    X86_LSL16rr	= 759,
    X86_LSL32rm	= 760,
    X86_LSL32rr	= 761,
    X86_LSL64rm	= 762,
    X86_LSL64rr	= 763,
    X86_LSS16rm	= 764,
    X86_LSS32rm	= 765,
    X86_LSS64rm	= 766,
    X86_LTRm	= 767,
    X86_LTRr	= 768,
    X86_LXADD16	= 769,
    X86_LXADD32	= 770,
    X86_LXADD64	= 771,
    X86_LXADD8	= 772,
    X86_LZCNT16rm	= 773,
    X86_LZCNT16rr	= 774,
    X86_LZCNT32rm	= 775,
    X86_LZCNT32rr	= 776,
    X86_LZCNT64rm	= 777,
    X86_LZCNT64rr	= 778,
    X86_MONTMUL	= 779,
    X86_MORESTACK_RET	= 780,
    X86_MORESTACK_RET_RESTORE_R10	= 781,
    X86_MOV16ao16	= 782,
    X86_MOV16ao16_16	= 783,
    X86_MOV16mi	= 784,
    X86_MOV16mr	= 785,
    X86_MOV16ms	= 786,
    X86_MOV16o16a	= 787,
    X86_MOV16o16a_16	= 788,
    X86_MOV16ri	= 789,
    X86_MOV16ri_alt	= 790,
    X86_MOV16rm	= 791,
    X86_MOV16rr	= 792,
    X86_MOV16rr_REV	= 793,
    X86_MOV16rs	= 794,
    X86_MOV16sm	= 795,
    X86_MOV16sr	= 796,
    X86_MOV32ao32	= 797,
    X86_MOV32ao32_16	= 798,
    X86_MOV32cr	= 799,
    X86_MOV32dr	= 800,
    X86_MOV32mi	= 801,
    X86_MOV32mr	= 802,
    X86_MOV32ms	= 803,
    X86_MOV32o32a	= 804,
    X86_MOV32o32a_16	= 805,
    X86_MOV32r0	= 806,
    X86_MOV32rc	= 807,
    X86_MOV32rd	= 808,
    X86_MOV32ri	= 809,
    X86_MOV32ri64	= 810,
    X86_MOV32ri_alt	= 811,
    X86_MOV32rm	= 812,
    X86_MOV32rr	= 813,
    X86_MOV32rr_REV	= 814,
    X86_MOV32rs	= 815,
    X86_MOV32sm	= 816,
    X86_MOV32sr	= 817,
    X86_MOV64ao16	= 818,
    X86_MOV64ao32	= 819,
    X86_MOV64ao64	= 820,
    X86_MOV64ao8	= 821,
    X86_MOV64cr	= 822,
    X86_MOV64dr	= 823,
    X86_MOV64mi32	= 824,
    X86_MOV64mr	= 825,
    X86_MOV64ms	= 826,
    X86_MOV64o16a	= 827,
    X86_MOV64o32a	= 828,
    X86_MOV64o64a	= 829,
    X86_MOV64o8a	= 830,
    X86_MOV64rc	= 831,
    X86_MOV64rd	= 832,
    X86_MOV64ri	= 833,
    X86_MOV64ri32	= 834,
    X86_MOV64rm	= 835,
    X86_MOV64rr	= 836,
    X86_MOV64rr_REV	= 837,
    X86_MOV64rs	= 838,
    X86_MOV64sm	= 839,
    X86_MOV64sr	= 840,
    X86_MOV8ao8	= 841,
    X86_MOV8ao8_16	= 842,
    X86_MOV8mi	= 843,
    X86_MOV8mr	= 844,
    X86_MOV8mr_NOREX	= 845,
    X86_MOV8o8a	= 846,
    X86_MOV8o8a_16	= 847,
    X86_MOV8ri	= 848,
    X86_MOV8ri_alt	= 849,
    X86_MOV8rm	= 850,
    X86_MOV8rm_NOREX	= 851,
    X86_MOV8rr	= 852,
    X86_MOV8rr_NOREX	= 853,
    X86_MOV8rr_REV	= 854,
    X86_MOVBE16mr	= 855,
    X86_MOVBE16rm	= 856,
    X86_MOVBE32mr	= 857,
    X86_MOVBE32rm	= 858,
    X86_MOVBE64mr	= 859,
    X86_MOVBE64rm	= 860,
    X86_MOVPC32r	= 861,
    X86_MOVSB	= 862,
    X86_MOVSL	= 863,
    X86_MOVSQ	= 864,
    X86_MOVSW	= 865,
    X86_MOVSX16rm8	= 866,
    X86_MOVSX16rr8	= 867,
    X86_MOVSX32rm16	= 868,
    X86_MOVSX32rm8	= 869,
    X86_MOVSX32rr16	= 870,
    X86_MOVSX32rr8	= 871,
    X86_MOVSX64_NOREXrr32	= 872,
    X86_MOVSX64rm16	= 873,
    X86_MOVSX64rm32	= 874,
    X86_MOVSX64rm8	= 875,
    X86_MOVSX64rr16	= 876,
    X86_MOVSX64rr32	= 877,
    X86_MOVSX64rr8	= 878,
    X86_MOVZX16rm8	= 879,
    X86_MOVZX16rr8	= 880,
    X86_MOVZX32_NOREXrm8	= 881,
    X86_MOVZX32_NOREXrr8	= 882,
    X86_MOVZX32rm16	= 883,
    X86_MOVZX32rm8	= 884,
    X86_MOVZX32rr16	= 885,
    X86_MOVZX32rr8	= 886,
    X86_MOVZX64rm16_Q	= 887,
    X86_MOVZX64rm8_Q	= 888,
    X86_MOVZX64rr16_Q	= 889,
    X86_MOVZX64rr8_Q	= 890,
    X86_MUL16m	= 891,
    X86_MUL16r	= 892,
    X86_MUL32m	= 893,
    X86_MUL32r	= 894,
    X86_MUL64m	= 895,
    X86_MUL64r	= 896,
    X86_MUL8m	= 897,
    X86_MUL8r	= 898,
    X86_MULX32rm	= 899,
    X86_MULX32rr	= 900,
    X86_MULX64rm	= 901,
    X86_MULX64rr	= 902,
    X86_NEG16m	= 903,
    X86_NEG16r	= 904,
    X86_NEG32m	= 905,
    X86_NEG32r	= 906,
    X86_NEG64m	= 907,
    X86_NEG64r	= 908,
    X86_NEG8m	= 909,
    X86_NEG8r	= 910,
    X86_NOOP	= 911,
    X86_NOOP18_16m4	= 912,
    X86_NOOP18_16m5	= 913,
    X86_NOOP18_16m6	= 914,
    X86_NOOP18_16m7	= 915,
    X86_NOOP18_16r4	= 916,
    X86_NOOP18_16r5	= 917,
    X86_NOOP18_16r6	= 918,
    X86_NOOP18_16r7	= 919,
    X86_NOOP18_m4	= 920,
    X86_NOOP18_m5	= 921,
    X86_NOOP18_m6	= 922,
    X86_NOOP18_m7	= 923,
    X86_NOOP18_r4	= 924,
    X86_NOOP18_r5	= 925,
    X86_NOOP18_r6	= 926,
    X86_NOOP18_r7	= 927,
    X86_NOOP19rr	= 928,
    X86_NOOPL	= 929,
    X86_NOOPL_19	= 930,
    X86_NOOPL_1a	= 931,
    X86_NOOPL_1b	= 932,
    X86_NOOPL_1c	= 933,
    X86_NOOPL_1d	= 934,
    X86_NOOPL_1e	= 935,
    X86_NOOPW	= 936,
    X86_NOOPW_19	= 937,
    X86_NOOPW_1a	= 938,
    X86_NOOPW_1b	= 939,
    X86_NOOPW_1c	= 940,
    X86_NOOPW_1d	= 941,
    X86_NOOPW_1e	= 942,
    X86_NOT16m	= 943,
    X86_NOT16r	= 944,
    X86_NOT32m	= 945,
    X86_NOT32r	= 946,
    X86_NOT64m	= 947,
    X86_NOT64r	= 948,
    X86_NOT8m	= 949,
    X86_NOT8r	= 950,
    X86_OR16i16	= 951,
    X86_OR16mi	= 952,
    X86_OR16mi8	= 953,
    X86_OR16mr	= 954,
    X86_OR16ri	= 955,
    X86_OR16ri8	= 956,
    X86_OR16rm	= 957,
    X86_OR16rr	= 958,
    X86_OR16rr_REV	= 959,
    X86_OR32i32	= 960,
    X86_OR32mi	= 961,
    X86_OR32mi8	= 962,
    X86_OR32mr	= 963,
    X86_OR32mrLocked	= 964,
    X86_OR32ri	= 965,
    X86_OR32ri8	= 966,
    X86_OR32rm	= 967,
    X86_OR32rr	= 968,
    X86_OR32rr_REV	= 969,
    X86_OR64i32	= 970,
    X86_OR64mi32	= 971,
    X86_OR64mi8	= 972,
    X86_OR64mr	= 973,
    X86_OR64ri32	= 974,
    X86_OR64ri8	= 975,
    X86_OR64rm	= 976,
    X86_OR64rr	= 977,
    X86_OR64rr_REV	= 978,
    X86_OR8i8	= 979,
    X86_OR8mi	= 980,
    X86_OR8mr	= 981,
    X86_OR8ri	= 982,
    X86_OR8ri8	= 983,
    X86_OR8rm	= 984,
    X86_OR8rr	= 985,
    X86_OR8rr_REV	= 986,
    X86_OUT16ir	= 987,
    X86_OUT16rr	= 988,
    X86_OUT32ir	= 989,
    X86_OUT32rr	= 990,
    X86_OUT8ir	= 991,
    X86_OUT8rr	= 992,
    X86_OUTSB	= 993,
    X86_OUTSL	= 994,
    X86_OUTSW	= 995,
    X86_PDEP32rm	= 996,
    X86_PDEP32rr	= 997,
    X86_PDEP64rm	= 998,
    X86_PDEP64rr	= 999,
    X86_PEXT32rm	= 1000,
    X86_PEXT32rr	= 1001,
    X86_PEXT64rm	= 1002,
    X86_PEXT64rr	= 1003,
    X86_POP16r	= 1004,
    X86_POP16rmm	= 1005,
    X86_POP16rmr	= 1006,
    X86_POP32r	= 1007,
    X86_POP32rmm	= 1008,
    X86_POP32rmr	= 1009,
    X86_POP64r	= 1010,
    X86_POP64rmm	= 1011,
    X86_POP64rmr	= 1012,
    X86_POPA16	= 1013,
    X86_POPA32	= 1014,
    X86_POPDS16	= 1015,
    X86_POPDS32	= 1016,
    X86_POPES16	= 1017,
    X86_POPES32	= 1018,
    X86_POPF16	= 1019,
    X86_POPF32	= 1020,
    X86_POPF64	= 1021,
    X86_POPFS16	= 1022,
    X86_POPFS32	= 1023,
    X86_POPFS64	= 1024,
    X86_POPGS16	= 1025,
    X86_POPGS32	= 1026,
    X86_POPGS64	= 1027,
    X86_POPSS16	= 1028,
    X86_POPSS32	= 1029,
    X86_PUSH16i8	= 1030,
    X86_PUSH16r	= 1031,
    X86_PUSH16rmm	= 1032,
    X86_PUSH16rmr	= 1033,
    X86_PUSH32i8	= 1034,
    X86_PUSH32r	= 1035,
    X86_PUSH32rmm	= 1036,
    X86_PUSH32rmr	= 1037,
    X86_PUSH64i16	= 1038,
    X86_PUSH64i32	= 1039,
    X86_PUSH64i8	= 1040,
    X86_PUSH64r	= 1041,
    X86_PUSH64rmm	= 1042,
    X86_PUSH64rmr	= 1043,
    X86_PUSHA16	= 1044,
    X86_PUSHA32	= 1045,
    X86_PUSHCS16	= 1046,
    X86_PUSHCS32	= 1047,
    X86_PUSHDS16	= 1048,
    X86_PUSHDS32	= 1049,
    X86_PUSHES16	= 1050,
    X86_PUSHES32	= 1051,
    X86_PUSHF16	= 1052,
    X86_PUSHF32	= 1053,
    X86_PUSHF64	= 1054,
    X86_PUSHFS16	= 1055,
    X86_PUSHFS32	= 1056,
    X86_PUSHFS64	= 1057,
    X86_PUSHGS16	= 1058,
    X86_PUSHGS32	= 1059,
    X86_PUSHGS64	= 1060,
    X86_PUSHSS16	= 1061,
    X86_PUSHSS32	= 1062,
    X86_PUSHi16	= 1063,
    X86_PUSHi32	= 1064,
    X86_RCL16m1	= 1065,
    X86_RCL16mCL	= 1066,
    X86_RCL16mi	= 1067,
    X86_RCL16r1	= 1068,
    X86_RCL16rCL	= 1069,
    X86_RCL16ri	= 1070,
    X86_RCL32m1	= 1071,
    X86_RCL32mCL	= 1072,
    X86_RCL32mi	= 1073,
    X86_RCL32r1	= 1074,
    X86_RCL32rCL	= 1075,
    X86_RCL32ri	= 1076,
    X86_RCL64m1	= 1077,
    X86_RCL64mCL	= 1078,
    X86_RCL64mi	= 1079,
    X86_RCL64r1	= 1080,
    X86_RCL64rCL	= 1081,
    X86_RCL64ri	= 1082,
    X86_RCL8m1	= 1083,
    X86_RCL8mCL	= 1084,
    X86_RCL8mi	= 1085,
    X86_RCL8r1	= 1086,
    X86_RCL8rCL	= 1087,
    X86_RCL8ri	= 1088,
    X86_RCR16m1	= 1089,
    X86_RCR16mCL	= 1090,
    X86_RCR16mi	= 1091,
    X86_RCR16r1	= 1092,
    X86_RCR16rCL	= 1093,
    X86_RCR16ri	= 1094,
    X86_RCR32m1	= 1095,
    X86_RCR32mCL	= 1096,
    X86_RCR32mi	= 1097,
    X86_RCR32r1	= 1098,
    X86_RCR32rCL	= 1099,
    X86_RCR32ri	= 1100,
    X86_RCR64m1	= 1101,
    X86_RCR64mCL	= 1102,
    X86_RCR64mi	= 1103,
    X86_RCR64r1	= 1104,
    X86_RCR64rCL	= 1105,
    X86_RCR64ri	= 1106,
    X86_RCR8m1	= 1107,
    X86_RCR8mCL	= 1108,
    X86_RCR8mi	= 1109,
    X86_RCR8r1	= 1110,
    X86_RCR8rCL	= 1111,
    X86_RCR8ri	= 1112,
    X86_RDFSBASE	= 1113,
    X86_RDFSBASE64	= 1114,
    X86_RDGSBASE	= 1115,
    X86_RDGSBASE64	= 1116,
    X86_RDMSR	= 1117,
    X86_RDPMC	= 1118,
    X86_RDRAND16r	= 1119,
    X86_RDRAND32r	= 1120,
    X86_RDRAND64r	= 1121,
    X86_RDSEED16r	= 1122,
    X86_RDSEED32r	= 1123,
    X86_RDSEED64r	= 1124,
    X86_RDTSC	= 1125,
    X86_RDTSCP	= 1126,
    X86_RELEASE_MOV16mr	= 1127,
    X86_RELEASE_MOV32mr	= 1128,
    X86_RELEASE_MOV64mr	= 1129,
    X86_RELEASE_MOV8mr	= 1130,
    X86_REPNE_PREFIX	= 1131,
    X86_REP_MOVSB_32	= 1132,
    X86_REP_MOVSB_64	= 1133,
    X86_REP_MOVSD_32	= 1134,
    X86_REP_MOVSD_64	= 1135,
    X86_REP_MOVSQ_64	= 1136,
    X86_REP_MOVSW_32	= 1137,
    X86_REP_MOVSW_64	= 1138,
    X86_REP_PREFIX	= 1139,
    X86_REP_STOSB_32	= 1140,
    X86_REP_STOSB_64	= 1141,
    X86_REP_STOSD_32	= 1142,
    X86_REP_STOSD_64	= 1143,
    X86_REP_STOSQ_64	= 1144,
    X86_REP_STOSW_32	= 1145,
    X86_REP_STOSW_64	= 1146,
    X86_RETIL	= 1147,
    X86_RETIQ	= 1148,
    X86_RETIW	= 1149,
    X86_RETL	= 1150,
    X86_RETQ	= 1151,
    X86_RETW	= 1152,
    X86_REX64_PREFIX	= 1153,
    X86_ROL16m1	= 1154,
    X86_ROL16mCL	= 1155,
    X86_ROL16mi	= 1156,
    X86_ROL16r1	= 1157,
    X86_ROL16rCL	= 1158,
    X86_ROL16ri	= 1159,
    X86_ROL32m1	= 1160,
    X86_ROL32mCL	= 1161,
    X86_ROL32mi	= 1162,
    X86_ROL32r1	= 1163,
    X86_ROL32rCL	= 1164,
    X86_ROL32ri	= 1165,
    X86_ROL64m1	= 1166,
    X86_ROL64mCL	= 1167,
    X86_ROL64mi	= 1168,
    X86_ROL64r1	= 1169,
    X86_ROL64rCL	= 1170,
    X86_ROL64ri	= 1171,
    X86_ROL8m1	= 1172,
    X86_ROL8mCL	= 1173,
    X86_ROL8mi	= 1174,
    X86_ROL8r1	= 1175,
    X86_ROL8rCL	= 1176,
    X86_ROL8ri	= 1177,
    X86_ROR16m1	= 1178,
    X86_ROR16mCL	= 1179,
    X86_ROR16mi	= 1180,
    X86_ROR16r1	= 1181,
    X86_ROR16rCL	= 1182,
    X86_ROR16ri	= 1183,
    X86_ROR32m1	= 1184,
    X86_ROR32mCL	= 1185,
    X86_ROR32mi	= 1186,
    X86_ROR32r1	= 1187,
    X86_ROR32rCL	= 1188,
    X86_ROR32ri	= 1189,
    X86_ROR64m1	= 1190,
    X86_ROR64mCL	= 1191,
    X86_ROR64mi	= 1192,
    X86_ROR64r1	= 1193,
    X86_ROR64rCL	= 1194,
    X86_ROR64ri	= 1195,
    X86_ROR8m1	= 1196,
    X86_ROR8mCL	= 1197,
    X86_ROR8mi	= 1198,
    X86_ROR8r1	= 1199,
    X86_ROR8rCL	= 1200,
    X86_ROR8ri	= 1201,
    X86_RORX32mi	= 1202,
    X86_RORX32ri	= 1203,
    X86_RORX64mi	= 1204,
    X86_RORX64ri	= 1205,
    X86_RSM	= 1206,
    X86_SAHF	= 1207,
    X86_SAL16m1	= 1208,
    X86_SAL16mCL	= 1209,
    X86_SAL16mi	= 1210,
    X86_SAL16r1	= 1211,
    X86_SAL16rCL	= 1212,
    X86_SAL16ri	= 1213,
    X86_SAL32m1	= 1214,
    X86_SAL32mCL	= 1215,
    X86_SAL32mi	= 1216,
    X86_SAL32r1	= 1217,
    X86_SAL32rCL	= 1218,
    X86_SAL32ri	= 1219,
    X86_SAL64m1	= 1220,
    X86_SAL64mCL	= 1221,
    X86_SAL64mi	= 1222,
    X86_SAL64r1	= 1223,
    X86_SAL64rCL	= 1224,
    X86_SAL64ri	= 1225,
    X86_SAL8m1	= 1226,
    X86_SAL8mCL	= 1227,
    X86_SAL8mi	= 1228,
    X86_SAL8r1	= 1229,
    X86_SAL8rCL	= 1230,
    X86_SAL8ri	= 1231,
    X86_SALC	= 1232,
    X86_SAR16m1	= 1233,
    X86_SAR16mCL	= 1234,
    X86_SAR16mi	= 1235,
    X86_SAR16r1	= 1236,
    X86_SAR16rCL	= 1237,
    X86_SAR16ri	= 1238,
    X86_SAR32m1	= 1239,
    X86_SAR32mCL	= 1240,
    X86_SAR32mi	= 1241,
    X86_SAR32r1	= 1242,
    X86_SAR32rCL	= 1243,
    X86_SAR32ri	= 1244,
    X86_SAR64m1	= 1245,
    X86_SAR64mCL	= 1246,
    X86_SAR64mi	= 1247,
    X86_SAR64r1	= 1248,
    X86_SAR64rCL	= 1249,
    X86_SAR64ri	= 1250,
    X86_SAR8m1	= 1251,
    X86_SAR8mCL	= 1252,
    X86_SAR8mi	= 1253,
    X86_SAR8r1	= 1254,
    X86_SAR8rCL	= 1255,
    X86_SAR8ri	= 1256,
    X86_SARX32rm	= 1257,
    X86_SARX32rr	= 1258,
    X86_SARX64rm	= 1259,
    X86_SARX64rr	= 1260,
    X86_SBB16i16	= 1261,
    X86_SBB16mi	= 1262,
    X86_SBB16mi8	= 1263,
    X86_SBB16mr	= 1264,
    X86_SBB16ri	= 1265,
    X86_SBB16ri8	= 1266,
    X86_SBB16rm	= 1267,
    X86_SBB16rr	= 1268,
    X86_SBB16rr_REV	= 1269,
    X86_SBB32i32	= 1270,
    X86_SBB32mi	= 1271,
    X86_SBB32mi8	= 1272,
    X86_SBB32mr	= 1273,
    X86_SBB32ri	= 1274,
    X86_SBB32ri8	= 1275,
    X86_SBB32rm	= 1276,
    X86_SBB32rr	= 1277,
    X86_SBB32rr_REV	= 1278,
    X86_SBB64i32	= 1279,
    X86_SBB64mi32	= 1280,
    X86_SBB64mi8	= 1281,
    X86_SBB64mr	= 1282,
    X86_SBB64ri32	= 1283,
    X86_SBB64ri8	= 1284,
    X86_SBB64rm	= 1285,
    X86_SBB64rr	= 1286,
    X86_SBB64rr_REV	= 1287,
    X86_SBB8i8	= 1288,
    X86_SBB8mi	= 1289,
    X86_SBB8mr	= 1290,
    X86_SBB8ri	= 1291,
    X86_SBB8rm	= 1292,
    X86_SBB8rr	= 1293,
    X86_SBB8rr_REV	= 1294,
    X86_SCASB	= 1295,
    X86_SCASL	= 1296,
    X86_SCASQ	= 1297,
    X86_SCASW	= 1298,
    X86_SEG_ALLOCA_32	= 1299,
    X86_SEG_ALLOCA_64	= 1300,
    X86_SEH_EndPrologue	= 1301,
    X86_SEH_Epilogue	= 1302,
    X86_SEH_PushFrame	= 1303,
    X86_SEH_PushReg	= 1304,
    X86_SEH_SaveReg	= 1305,
    X86_SEH_SaveXMM	= 1306,
    X86_SEH_SetFrame	= 1307,
    X86_SEH_StackAlloc	= 1308,
    X86_SETAEm	= 1309,
    X86_SETAEr	= 1310,
    X86_SETAm	= 1311,
    X86_SETAr	= 1312,
    X86_SETBEm	= 1313,
    X86_SETBEr	= 1314,
    X86_SETB_C16r	= 1315,
    X86_SETB_C32r	= 1316,
    X86_SETB_C64r	= 1317,
    X86_SETB_C8r	= 1318,
    X86_SETBm	= 1319,
    X86_SETBr	= 1320,
    X86_SETEm	= 1321,
    X86_SETEr	= 1322,
    X86_SETGEm	= 1323,
    X86_SETGEr	= 1324,
    X86_SETGm	= 1325,
    X86_SETGr	= 1326,
    X86_SETLEm	= 1327,
    X86_SETLEr	= 1328,
    X86_SETLm	= 1329,
    X86_SETLr	= 1330,
    X86_SETNEm	= 1331,
    X86_SETNEr	= 1332,
    X86_SETNOm	= 1333,
    X86_SETNOr	= 1334,
    X86_SETNPm	= 1335,
    X86_SETNPr	= 1336,
    X86_SETNSm	= 1337,
    X86_SETNSr	= 1338,
    X86_SETOm	= 1339,
    X86_SETOr	= 1340,
    X86_SETPm	= 1341,
    X86_SETPr	= 1342,
    X86_SETSm	= 1343,
    X86_SETSr	= 1344,
    X86_SGDT16m	= 1345,
    X86_SGDT32m	= 1346,
    X86_SGDT64m	= 1347,
    X86_SHL16m1	= 1348,
    X86_SHL16mCL	= 1349,
    X86_SHL16mi	= 1350,
    X86_SHL16r1	= 1351,
    X86_SHL16rCL	= 1352,
    X86_SHL16ri	= 1353,
    X86_SHL32m1	= 1354,
    X86_SHL32mCL	= 1355,
    X86_SHL32mi	= 1356,
    X86_SHL32r1	= 1357,
    X86_SHL32rCL	= 1358,
    X86_SHL32ri	= 1359,
    X86_SHL64m1	= 1360,
    X86_SHL64mCL	= 1361,
    X86_SHL64mi	= 1362,
    X86_SHL64r1	= 1363,
    X86_SHL64rCL	= 1364,
    X86_SHL64ri	= 1365,
    X86_SHL8m1	= 1366,
    X86_SHL8mCL	= 1367,
    X86_SHL8mi	= 1368,
    X86_SHL8r1	= 1369,
    X86_SHL8rCL	= 1370,
    X86_SHL8ri	= 1371,
    X86_SHLD16mrCL	= 1372,
    X86_SHLD16mri8	= 1373,
    X86_SHLD16rrCL	= 1374,
    X86_SHLD16rri8	= 1375,
    X86_SHLD32mrCL	= 1376,
    X86_SHLD32mri8	= 1377,
    X86_SHLD32rrCL	= 1378,
    X86_SHLD32rri8	= 1379,
    X86_SHLD64mrCL	= 1380,
    X86_SHLD64mri8	= 1381,
    X86_SHLD64rrCL	= 1382,
    X86_SHLD64rri8	= 1383,
    X86_SHLX32rm	= 1384,
    X86_SHLX32rr	= 1385,
    X86_SHLX64rm	= 1386,
    X86_SHLX64rr	= 1387,
    X86_SHR16m1	= 1388,
    X86_SHR16mCL	= 1389,
    X86_SHR16mi	= 1390,
    X86_SHR16r1	= 1391,
    X86_SHR16rCL	= 1392,
    X86_SHR16ri	= 1393,
    X86_SHR32m1	= 1394,
    X86_SHR32mCL	= 1395,
    X86_SHR32mi	= 1396,
    X86_SHR32r1	= 1397,
    X86_SHR32rCL	= 1398,
    X86_SHR32ri	= 1399,
    X86_SHR64m1	= 1400,
    X86_SHR64mCL	= 1401,
    X86_SHR64mi	= 1402,
    X86_SHR64r1	= 1403,
    X86_SHR64rCL	= 1404,
    X86_SHR64ri	= 1405,
    X86_SHR8m1	= 1406,
    X86_SHR8mCL	= 1407,
    X86_SHR8mi	= 1408,
    X86_SHR8r1	= 1409,
    X86_SHR8rCL	= 1410,
    X86_SHR8ri	= 1411,
    X86_SHRD16mrCL	= 1412,
    X86_SHRD16mri8	= 1413,
    X86_SHRD16rrCL	= 1414,
    X86_SHRD16rri8	= 1415,
    X86_SHRD32mrCL	= 1416,
    X86_SHRD32mri8	= 1417,
    X86_SHRD32rrCL	= 1418,
    X86_SHRD32rri8	= 1419,
    X86_SHRD64mrCL	= 1420,
    X86_SHRD64mri8	= 1421,
    X86_SHRD64rrCL	= 1422,
    X86_SHRD64rri8	= 1423,
    X86_SHRX32rm	= 1424,
    X86_SHRX32rr	= 1425,
    X86_SHRX64rm	= 1426,
    X86_SHRX64rr	= 1427,
    X86_SIDT16m	= 1428,
    X86_SIDT32m	= 1429,
    X86_SIDT64m	= 1430,
    X86_SKINIT	= 1431,
    X86_SLDT16m	= 1432,
    X86_SLDT16r	= 1433,
    X86_SLDT32r	= 1434,
    X86_SLDT64m	= 1435,
    X86_SLDT64r	= 1436,
    X86_SMSW16m	= 1437,
    X86_SMSW16r	= 1438,
    X86_SMSW32r	= 1439,
    X86_SMSW64r	= 1440,
    X86_STAC	= 1441,
    X86_STC	= 1442,
    X86_STD	= 1443,
    X86_STGI	= 1444,
    X86_STI	= 1445,
    X86_STOSB	= 1446,
    X86_STOSL	= 1447,
    X86_STOSQ	= 1448,
    X86_STOSW	= 1449,
    X86_STR16r	= 1450,
    X86_STR32r	= 1451,
    X86_STR64r	= 1452,
    X86_STRm	= 1453,
    X86_SUB16i16	= 1454,
    X86_SUB16mi	= 1455,
    X86_SUB16mi8	= 1456,
    X86_SUB16mr	= 1457,
    X86_SUB16ri	= 1458,
    X86_SUB16ri8	= 1459,
    X86_SUB16rm	= 1460,
    X86_SUB16rr	= 1461,
    X86_SUB16rr_REV	= 1462,
    X86_SUB32i32	= 1463,
    X86_SUB32mi	= 1464,
    X86_SUB32mi8	= 1465,
    X86_SUB32mr	= 1466,
    X86_SUB32ri	= 1467,
    X86_SUB32ri8	= 1468,
    X86_SUB32rm	= 1469,
    X86_SUB32rr	= 1470,
    X86_SUB32rr_REV	= 1471,
    X86_SUB64i32	= 1472,
    X86_SUB64mi32	= 1473,
    X86_SUB64mi8	= 1474,
    X86_SUB64mr	= 1475,
    X86_SUB64ri32	= 1476,
    X86_SUB64ri8	= 1477,
    X86_SUB64rm	= 1478,
    X86_SUB64rr	= 1479,
    X86_SUB64rr_REV	= 1480,
    X86_SUB8i8	= 1481,
    X86_SUB8mi	= 1482,
    X86_SUB8mr	= 1483,
    X86_SUB8ri	= 1484,
    X86_SUB8ri8	= 1485,
    X86_SUB8rm	= 1486,
    X86_SUB8rr	= 1487,
    X86_SUB8rr_REV	= 1488,
    X86_SWAPGS	= 1489,
    X86_SYSCALL	= 1490,
    X86_SYSENTER	= 1491,
    X86_SYSEXIT	= 1492,
    X86_SYSEXIT64	= 1493,
    X86_SYSRET	= 1494,
    X86_SYSRET64	= 1495,
    X86_T1MSKC32rm	= 1496,
    X86_T1MSKC32rr	= 1497,
    X86_T1MSKC64rm	= 1498,
    X86_T1MSKC64rr	= 1499,
    X86_TAILJMPd	= 1500,
    X86_TAILJMPd64	= 1501,
    X86_TAILJMPm	= 1502,
    X86_TAILJMPm64	= 1503,
    X86_TAILJMPr	= 1504,
    X86_TAILJMPr64	= 1505,
    X86_TCRETURNdi	= 1506,
    X86_TCRETURNdi64	= 1507,
    X86_TCRETURNmi	= 1508,
    X86_TCRETURNmi64	= 1509,
    X86_TCRETURNri	= 1510,
    X86_TCRETURNri64	= 1511,
    X86_TEST16i16	= 1512,
    X86_TEST16mi	= 1513,
    X86_TEST16mi_alt	= 1514,
    X86_TEST16ri	= 1515,
    X86_TEST16ri_alt	= 1516,
    X86_TEST16rm	= 1517,
    X86_TEST16rr	= 1518,
    X86_TEST32i32	= 1519,
    X86_TEST32mi	= 1520,
    X86_TEST32mi_alt	= 1521,
    X86_TEST32ri	= 1522,
    X86_TEST32ri_alt	= 1523,
    X86_TEST32rm	= 1524,
    X86_TEST32rr	= 1525,
    X86_TEST64i32	= 1526,
    X86_TEST64mi32	= 1527,
    X86_TEST64mi32_alt	= 1528,
    X86_TEST64ri32	= 1529,
    X86_TEST64ri32_alt	= 1530,
    X86_TEST64rm	= 1531,
    X86_TEST64rr	= 1532,
    X86_TEST8i8	= 1533,
    X86_TEST8mi	= 1534,
    X86_TEST8mi_alt	= 1535,
    X86_TEST8ri	= 1536,
    X86_TEST8ri_NOREX	= 1537,
    X86_TEST8ri_alt	= 1538,
    X86_TEST8rm	= 1539,
    X86_TEST8rr	= 1540,
    X86_TLSCall_32	= 1541,
    X86_TLSCall_64	= 1542,
    X86_TLS_addr32	= 1543,
    X86_TLS_addr64	= 1544,
    X86_TLS_base_addr32	= 1545,
    X86_TLS_base_addr64	= 1546,
    X86_TRAP	= 1547,
    X86_TZCNT16rm	= 1548,
    X86_TZCNT16rr	= 1549,
    X86_TZCNT32rm	= 1550,
    X86_TZCNT32rr	= 1551,
    X86_TZCNT64rm	= 1552,
    X86_TZCNT64rr	= 1553,
    X86_TZMSK32rm	= 1554,
    X86_TZMSK32rr	= 1555,
    X86_TZMSK64rm	= 1556,
    X86_TZMSK64rr	= 1557,
    X86_UD2B	= 1558,
    X86_VAARG_64	= 1559,
    X86_VASTART_SAVE_XMM_REGS	= 1560,
    X86_VERRm	= 1561,
    X86_VERRr	= 1562,
    X86_VERWm	= 1563,
    X86_VERWr	= 1564,
    X86_VMCALL	= 1565,
    X86_VMCLEARm	= 1566,
    X86_VMFUNC	= 1567,
    X86_VMLAUNCH	= 1568,
    X86_VMLOAD32	= 1569,
    X86_VMLOAD64	= 1570,
    X86_VMMCALL	= 1571,
    X86_VMPTRLDm	= 1572,
    X86_VMPTRSTm	= 1573,
    X86_VMREAD32rm	= 1574,
    X86_VMREAD32rr	= 1575,
    X86_VMREAD64rm	= 1576,
    X86_VMREAD64rr	= 1577,
    X86_VMRESUME	= 1578,
    X86_VMRUN32	= 1579,
    X86_VMRUN64	= 1580,
    X86_VMSAVE32	= 1581,
    X86_VMSAVE64	= 1582,
    X86_VMWRITE32rm	= 1583,
    X86_VMWRITE32rr	= 1584,
    X86_VMWRITE64rm	= 1585,
    X86_VMWRITE64rr	= 1586,
    X86_VMXOFF	= 1587,
    X86_VMXON	= 1588,
    X86_W64ALLOCA	= 1589,
    X86_WBINVD	= 1590,
    X86_WIN_ALLOCA	= 1591,
    X86_WIN_FTOL_32	= 1592,
    X86_WIN_FTOL_64	= 1593,
    X86_WRFSBASE	= 1594,
    X86_WRFSBASE64	= 1595,
    X86_WRGSBASE	= 1596,
    X86_WRGSBASE64	= 1597,
    X86_WRMSR	= 1598,
    X86_XADD16rm	= 1599,
    X86_XADD16rr	= 1600,
    X86_XADD32rm	= 1601,
    X86_XADD32rr	= 1602,
    X86_XADD64rm	= 1603,
    X86_XADD64rr	= 1604,
    X86_XADD8rm	= 1605,
    X86_XADD8rr	= 1606,
    X86_XCHG16ar	= 1607,
    X86_XCHG16rm	= 1608,
    X86_XCHG16rr	= 1609,
    X86_XCHG32ar	= 1610,
    X86_XCHG32ar64	= 1611,
    X86_XCHG32rm	= 1612,
    X86_XCHG32rr	= 1613,
    X86_XCHG64ar	= 1614,
    X86_XCHG64rm	= 1615,
    X86_XCHG64rr	= 1616,
    X86_XCHG8rm	= 1617,
    X86_XCHG8rr	= 1618,
    X86_XCRYPTCBC	= 1619,
    X86_XCRYPTCFB	= 1620,
    X86_XCRYPTCTR	= 1621,
    X86_XCRYPTECB	= 1622,
    X86_XCRYPTOFB	= 1623,
    X86_XGETBV	= 1624,
    X86_XLAT	= 1625,
    X86_XOR16i16	= 1626,
    X86_XOR16mi	= 1627,
    X86_XOR16mi8	= 1628,
    X86_XOR16mr	= 1629,
    X86_XOR16ri	= 1630,
    X86_XOR16ri8	= 1631,
    X86_XOR16rm	= 1632,
    X86_XOR16rr	= 1633,
    X86_XOR16rr_REV	= 1634,
    X86_XOR32i32	= 1635,
    X86_XOR32mi	= 1636,
    X86_XOR32mi8	= 1637,
    X86_XOR32mr	= 1638,
    X86_XOR32ri	= 1639,
    X86_XOR32ri8	= 1640,
    X86_XOR32rm	= 1641,
    X86_XOR32rr	= 1642,
    X86_XOR32rr_REV	= 1643,
    X86_XOR64i32	= 1644,
    X86_XOR64mi32	= 1645,
    X86_XOR64mi8	= 1646,
    X86_XOR64mr	= 1647,
    X86_XOR64ri32	= 1648,
    X86_XOR64ri8	= 1649,
    X86_XOR64rm	= 1650,
    X86_XOR64rr	= 1651,
    X86_XOR64rr_REV	= 1652,
    X86_XOR8i8	= 1653,
    X86_XOR8mi	= 1654,
    X86_XOR8mr	= 1655,
    X86_XOR8ri	= 1656,
    X86_XOR8ri8	= 1657,
    X86_XOR8rm	= 1658,
    X86_XOR8rr	= 1659,
    X86_XOR8rr_REV	= 1660,
    X86_XRSTOR	= 1661,
    X86_XRSTOR64	= 1662,
    X86_XSAVE	= 1663,
    X86_XSAVE64	= 1664,
    X86_XSAVEOPT	= 1665,
    X86_XSAVEOPT64	= 1666,
    X86_XSETBV	= 1667,
    X86_XSHA1	= 1668,
    X86_XSHA256	= 1669,
    X86_XSTORE	= 1670,
    X86_INSTRUCTION_LIST_END = 1671
};

#endif // GET_INSTRINFO_ENUM


#ifdef GET_INSTRINFO_MC_DESC
#undef GET_INSTRINFO_MC_DESC

typedef struct x86_op_id_pair {
	uint16_t first;
	uint16_t second;
} x86_op_id_pair;

static const x86_op_id_pair x86_16_bit_eq_tbl[] = {
	{ 25, 24 },
	{ 26, 24 },
	{ 37, 28 },
	{ 38, 29 },
	{ 39, 30 },
	{ 40, 31 },
	{ 41, 32 },
	{ 42, 33 },
	{ 43, 34 },
	{ 44, 35 },
	{ 45, 36 },
	{ 46, 28 },
	{ 48, 30 },
	{ 49, 31 },
	{ 51, 33 },
	{ 52, 34 },
	{ 53, 35 },
	{ 54, 36 },
	{ 78, 66 },
	{ 79, 67 },
	{ 80, 68 },
	{ 81, 69 },
	{ 82, 70 },
	{ 83, 71 },
	{ 84, 72 },
	{ 85, 73 },
	{ 86, 74 },
	{ 87, 75 },
	{ 88, 76 },
	{ 89, 77 },
	{ 90, 66 },
	{ 92, 68 },
	{ 93, 69 },
	{ 96, 71 },
	{ 97, 72 },
	{ 98, 74 },
	{ 99, 75 },
	{ 100, 76 },
	{ 101, 77 },
	{ 127, 118 },
	{ 128, 119 },
	{ 129, 120 },
	{ 130, 121 },
	{ 131, 122 },
	{ 132, 123 },
	{ 133, 124 },
	{ 134, 125 },
	{ 135, 126 },
	{ 136, 118 },
	{ 138, 120 },
	{ 139, 121 },
	{ 141, 123 },
	{ 142, 124 },
	{ 143, 125 },
	{ 144, 126 },
	{ 208, 207 },
	{ 211, 209 },
	{ 212, 210 },
	{ 213, 209 },
	{ 214, 210 },
	{ 217, 215 },
	{ 218, 216 },
	{ 219, 215 },
	{ 220, 216 },
	{ 227, 223 },
	{ 228, 224 },
	{ 229, 225 },
	{ 230, 226 },
	{ 231, 223 },
	{ 232, 224 },
	{ 233, 225 },
	{ 234, 226 },
	{ 239, 235 },
	{ 240, 236 },
	{ 241, 237 },
	{ 242, 238 },
	{ 243, 235 },
	{ 244, 236 },
	{ 245, 237 },
	{ 246, 238 },
	{ 251, 247 },
	{ 252, 248 },
	{ 253, 249 },
	{ 254, 250 },
	{ 255, 247 },
	{ 256, 248 },
	{ 257, 249 },
	{ 258, 250 },
	{ 263, 259 },
	{ 264, 260 },
	{ 265, 261 },
	{ 266, 262 },
	{ 267, 259 },
	{ 268, 260 },
	{ 269, 261 },
	{ 270, 262 },
	{ 277, 275 },
	{ 278, 276 },
	{ 279, 275 },
	{ 281, 276 },
	{ 283, 282 },
	{ 289, 458 },
	{ 296, 294 },
	{ 297, 295 },
	{ 298, 294 },
	{ 299, 295 },
	{ 302, 300 },
	{ 303, 301 },
	{ 304, 300 },
	{ 305, 301 },
	{ 308, 306 },
	{ 309, 307 },
	{ 310, 306 },
	{ 311, 307 },
	{ 314, 312 },
	{ 315, 313 },
	{ 316, 312 },
	{ 317, 313 },
	{ 320, 318 },
	{ 321, 319 },
	{ 322, 318 },
	{ 323, 319 },
	{ 326, 324 },
	{ 327, 325 },
	{ 328, 324 },
	{ 329, 325 },
	{ 332, 330 },
	{ 333, 331 },
	{ 334, 330 },
	{ 335, 331 },
	{ 338, 336 },
	{ 339, 337 },
	{ 340, 336 },
	{ 341, 337 },
	{ 344, 342 },
	{ 345, 343 },
	{ 346, 342 },
	{ 347, 343 },
	{ 350, 348 },
	{ 351, 349 },
	{ 352, 348 },
	{ 353, 349 },
	{ 356, 354 },
	{ 357, 355 },
	{ 358, 354 },
	{ 359, 355 },
	{ 362, 360 },
	{ 363, 361 },
	{ 364, 360 },
	{ 365, 361 },
	{ 368, 366 },
	{ 369, 367 },
	{ 370, 366 },
	{ 371, 367 },
	{ 374, 372 },
	{ 375, 373 },
	{ 376, 372 },
	{ 377, 373 },
	{ 380, 378 },
	{ 381, 379 },
	{ 382, 378 },
	{ 383, 379 },
	{ 386, 384 },
	{ 387, 385 },
	{ 388, 384 },
	{ 389, 385 },
	{ 393, 392 },
	{ 416, 407 },
	{ 417, 408 },
	{ 418, 409 },
	{ 419, 410 },
	{ 420, 411 },
	{ 421, 412 },
	{ 422, 413 },
	{ 423, 414 },
	{ 424, 415 },
	{ 425, 407 },
	{ 427, 409 },
	{ 428, 410 },
	{ 430, 412 },
	{ 431, 413 },
	{ 432, 414 },
	{ 433, 415 },
	{ 442, 444 },
	{ 443, 444 },
	{ 448, 446 },
	{ 449, 447 },
	{ 450, 446 },
	{ 451, 447 },
	{ 466, 465 },
	{ 467, 463 },
	{ 468, 464 },
	{ 471, 469 },
	{ 472, 470 },
	{ 473, 463 },
	{ 474, 464 },
	{ 479, 477 },
	{ 480, 478 },
	{ 481, 477 },
	{ 482, 478 },
	{ 495, 493 },
	{ 496, 494 },
	{ 500, 498 },
	{ 501, 499 },
	{ 508, 506 },
	{ 509, 507 },
	{ 510, 506 },
	{ 511, 507 },
	{ 522, 514 },
	{ 523, 515 },
	{ 524, 516 },
	{ 525, 517 },
	{ 526, 518 },
	{ 527, 519 },
	{ 528, 520 },
	{ 529, 521 },
	{ 530, 514 },
	{ 531, 515 },
	{ 532, 516 },
	{ 534, 518 },
	{ 535, 519 },
	{ 537, 521 },
	{ 542, 540 },
	{ 543, 541 },
	{ 549, 548 },
	{ 550, 546 },
	{ 551, 547 },
	{ 554, 552 },
	{ 555, 553 },
	{ 556, 546 },
	{ 557, 547 },
	{ 561, 562 },
	{ 565, 564 },
	{ 578, 577 },
	{ 579, 577 },
	{ 613, 611 },
	{ 614, 612 },
	{ 615, 611 },
	{ 616, 612 },
	{ 645, 643 },
	{ 646, 644 },
	{ 647, 643 },
	{ 648, 644 },
	{ 651, 649 },
	{ 652, 649 },
	{ 656, 655 },
	{ 658, 657 },
	{ 660, 657 },
	{ 664, 663 },
	{ 666, 665 },
	{ 667, 665 },
	{ 669, 668 },
	{ 670, 668 },
	{ 672, 671 },
	{ 673, 671 },
	{ 675, 674 },
	{ 676, 674 },
	{ 684, 681 },
	{ 685, 682 },
	{ 686, 683 },
	{ 688, 682 },
	{ 689, 683 },
	{ 695, 692 },
	{ 696, 693 },
	{ 697, 694 },
	{ 699, 693 },
	{ 700, 694 },
	{ 704, 703 },
	{ 705, 703 },
	{ 708, 707 },
	{ 709, 707 },
	{ 714, 711 },
	{ 715, 712 },
	{ 716, 713 },
	{ 718, 712 },
	{ 719, 713 },
	{ 726, 723 },
	{ 727, 724 },
	{ 728, 725 },
	{ 730, 724 },
	{ 731, 725 },
	{ 737, 734 },
	{ 738, 735 },
	{ 739, 736 },
	{ 741, 735 },
	{ 742, 736 },
	{ 746, 748 },
	{ 747, 748 },
	{ 752, 754 },
	{ 753, 754 },
	{ 755, 757 },
	{ 756, 757 },
	{ 760, 758 },
	{ 761, 759 },
	{ 762, 758 },
	{ 763, 759 },
	{ 765, 764 },
	{ 766, 764 },
	{ 770, 769 },
	{ 771, 769 },
	{ 775, 773 },
	{ 776, 774 },
	{ 777, 773 },
	{ 778, 774 },
	{ 797, 782 },
	{ 798, 783 },
	{ 801, 784 },
	{ 802, 785 },
	{ 803, 786 },
	{ 804, 787 },
	{ 805, 788 },
	{ 809, 789 },
	{ 811, 790 },
	{ 812, 791 },
	{ 813, 792 },
	{ 814, 793 },
	{ 815, 794 },
	{ 816, 795 },
	{ 817, 796 },
	{ 818, 782 },
	{ 819, 782 },
	{ 819, 818 },
	{ 820, 782 },
	{ 820, 818 },
	{ 825, 785 },
	{ 826, 786 },
	{ 827, 787 },
	{ 828, 787 },
	{ 828, 827 },
	{ 829, 787 },
	{ 829, 827 },
	{ 833, 789 },
	{ 835, 791 },
	{ 836, 792 },
	{ 837, 793 },
	{ 838, 794 },
	{ 839, 795 },
	{ 840, 796 },
	{ 857, 855 },
	{ 858, 856 },
	{ 859, 855 },
	{ 860, 856 },
	{ 863, 865 },
	{ 864, 865 },
	{ 869, 866 },
	{ 871, 867 },
	{ 874, 873 },
	{ 875, 866 },
	{ 877, 876 },
	{ 878, 867 },
	{ 884, 879 },
	{ 886, 880 },
	{ 893, 891 },
	{ 894, 892 },
	{ 895, 891 },
	{ 896, 892 },
	{ 905, 903 },
	{ 906, 904 },
	{ 907, 903 },
	{ 908, 904 },
	{ 912, 914 },
	{ 916, 918 },
	{ 920, 922 },
	{ 924, 926 },
	{ 929, 936 },
	{ 930, 937 },
	{ 931, 938 },
	{ 932, 939 },
	{ 933, 940 },
	{ 934, 941 },
	{ 935, 942 },
	{ 945, 943 },
	{ 946, 944 },
	{ 947, 943 },
	{ 948, 944 },
	{ 960, 951 },
	{ 961, 952 },
	{ 962, 953 },
	{ 963, 954 },
	{ 965, 955 },
	{ 966, 956 },
	{ 967, 957 },
	{ 968, 958 },
	{ 969, 959 },
	{ 970, 951 },
	{ 972, 953 },
	{ 973, 954 },
	{ 975, 956 },
	{ 976, 957 },
	{ 977, 958 },
	{ 978, 959 },
	{ 989, 987 },
	{ 990, 988 },
	{ 994, 995 },
	{ 1007, 1004 },
	{ 1008, 1005 },
	{ 1009, 1006 },
	{ 1010, 1004 },
	{ 1011, 1005 },
	{ 1012, 1006 },
	{ 1014, 1013 },
	{ 1016, 1015 },
	{ 1018, 1017 },
	{ 1020, 1019 },
	{ 1021, 1019 },
	{ 1023, 1022 },
	{ 1024, 1022 },
	{ 1026, 1025 },
	{ 1027, 1025 },
	{ 1029, 1028 },
	{ 1034, 1030 },
	{ 1035, 1031 },
	{ 1036, 1032 },
	{ 1037, 1033 },
	{ 1039, 1038 },
	{ 1040, 1030 },
	{ 1041, 1031 },
	{ 1042, 1032 },
	{ 1043, 1033 },
	{ 1045, 1044 },
	{ 1047, 1046 },
	{ 1049, 1048 },
	{ 1051, 1050 },
	{ 1053, 1052 },
	{ 1054, 1052 },
	{ 1056, 1055 },
	{ 1057, 1055 },
	{ 1059, 1058 },
	{ 1060, 1058 },
	{ 1062, 1061 },
	{ 1064, 1063 },
	{ 1071, 1065 },
	{ 1072, 1066 },
	{ 1073, 1067 },
	{ 1074, 1068 },
	{ 1075, 1069 },
	{ 1076, 1070 },
	{ 1077, 1065 },
	{ 1078, 1066 },
	{ 1079, 1067 },
	{ 1080, 1068 },
	{ 1081, 1069 },
	{ 1082, 1070 },
	{ 1095, 1089 },
	{ 1096, 1090 },
	{ 1097, 1091 },
	{ 1098, 1092 },
	{ 1099, 1093 },
	{ 1100, 1094 },
	{ 1101, 1089 },
	{ 1102, 1090 },
	{ 1103, 1091 },
	{ 1104, 1092 },
	{ 1105, 1093 },
	{ 1106, 1094 },
	{ 1120, 1119 },
	{ 1121, 1119 },
	{ 1123, 1122 },
	{ 1124, 1122 },
	{ 1128, 1127 },
	{ 1129, 1127 },
	{ 1136, 1138 },
	{ 1144, 1146 },
	{ 1147, 1149 },
	{ 1148, 1149 },
	{ 1150, 1152 },
	{ 1151, 1152 },
	{ 1160, 1154 },
	{ 1161, 1155 },
	{ 1162, 1156 },
	{ 1163, 1157 },
	{ 1164, 1158 },
	{ 1165, 1159 },
	{ 1166, 1154 },
	{ 1167, 1155 },
	{ 1168, 1156 },
	{ 1169, 1157 },
	{ 1170, 1158 },
	{ 1171, 1159 },
	{ 1184, 1178 },
	{ 1185, 1179 },
	{ 1186, 1180 },
	{ 1187, 1181 },
	{ 1188, 1182 },
	{ 1189, 1183 },
	{ 1190, 1178 },
	{ 1191, 1179 },
	{ 1192, 1180 },
	{ 1193, 1181 },
	{ 1194, 1182 },
	{ 1195, 1183 },
	{ 1214, 1208 },
	{ 1215, 1209 },
	{ 1216, 1210 },
	{ 1217, 1211 },
	{ 1218, 1212 },
	{ 1219, 1213 },
	{ 1220, 1208 },
	{ 1221, 1209 },
	{ 1222, 1210 },
	{ 1223, 1211 },
	{ 1224, 1212 },
	{ 1225, 1213 },
	{ 1239, 1233 },
	{ 1240, 1234 },
	{ 1241, 1235 },
	{ 1242, 1236 },
	{ 1243, 1237 },
	{ 1244, 1238 },
	{ 1245, 1233 },
	{ 1246, 1234 },
	{ 1247, 1235 },
	{ 1248, 1236 },
	{ 1249, 1237 },
	{ 1250, 1238 },
	{ 1270, 1261 },
	{ 1271, 1262 },
	{ 1272, 1263 },
	{ 1273, 1264 },
	{ 1274, 1265 },
	{ 1275, 1266 },
	{ 1276, 1267 },
	{ 1277, 1268 },
	{ 1278, 1269 },
	{ 1279, 1261 },
	{ 1281, 1263 },
	{ 1282, 1264 },
	{ 1284, 1266 },
	{ 1285, 1267 },
	{ 1286, 1268 },
	{ 1287, 1269 },
	{ 1296, 1298 },
	{ 1297, 1298 },
	{ 1316, 1315 },
	{ 1317, 1315 },
	{ 1346, 1345 },
	{ 1347, 1345 },
	{ 1354, 1348 },
	{ 1355, 1349 },
	{ 1356, 1350 },
	{ 1357, 1351 },
	{ 1358, 1352 },
	{ 1359, 1353 },
	{ 1360, 1348 },
	{ 1361, 1349 },
	{ 1362, 1350 },
	{ 1363, 1351 },
	{ 1364, 1352 },
	{ 1365, 1353 },
	{ 1376, 1372 },
	{ 1377, 1373 },
	{ 1378, 1374 },
	{ 1379, 1375 },
	{ 1380, 1372 },
	{ 1381, 1373 },
	{ 1382, 1374 },
	{ 1383, 1375 },
	{ 1394, 1388 },
	{ 1395, 1389 },
	{ 1396, 1390 },
	{ 1397, 1391 },
	{ 1398, 1392 },
	{ 1399, 1393 },
	{ 1400, 1388 },
	{ 1401, 1389 },
	{ 1402, 1390 },
	{ 1403, 1391 },
	{ 1404, 1392 },
	{ 1405, 1393 },
	{ 1416, 1412 },
	{ 1417, 1413 },
	{ 1418, 1414 },
	{ 1419, 1415 },
	{ 1420, 1412 },
	{ 1421, 1413 },
	{ 1422, 1414 },
	{ 1423, 1415 },
	{ 1429, 1428 },
	{ 1430, 1428 },
	{ 1434, 1433 },
	{ 1435, 1432 },
	{ 1436, 1433 },
	{ 1439, 1438 },
	{ 1440, 1438 },
	{ 1447, 1449 },
	{ 1448, 1449 },
	{ 1451, 1450 },
	{ 1452, 1450 },
	{ 1463, 1454 },
	{ 1464, 1455 },
	{ 1465, 1456 },
	{ 1466, 1457 },
	{ 1467, 1458 },
	{ 1468, 1459 },
	{ 1469, 1460 },
	{ 1470, 1461 },
	{ 1471, 1462 },
	{ 1472, 1454 },
	{ 1474, 1456 },
	{ 1475, 1457 },
	{ 1477, 1459 },
	{ 1478, 1460 },
	{ 1479, 1461 },
	{ 1480, 1462 },
	{ 1519, 1512 },
	{ 1520, 1513 },
	{ 1521, 1514 },
	{ 1522, 1515 },
	{ 1523, 1516 },
	{ 1524, 1517 },
	{ 1525, 1518 },
	{ 1526, 1512 },
	{ 1531, 1517 },
	{ 1532, 1518 },
	{ 1550, 1548 },
	{ 1551, 1549 },
	{ 1552, 1548 },
	{ 1553, 1549 },
	{ 1601, 1599 },
	{ 1602, 1600 },
	{ 1603, 1599 },
	{ 1604, 1600 },
	{ 1610, 1607 },
	{ 1612, 1608 },
	{ 1613, 1609 },
	{ 1614, 1607 },
	{ 1615, 1608 },
	{ 1616, 1609 },
	{ 1635, 1626 },
	{ 1636, 1627 },
	{ 1637, 1628 },
	{ 1638, 1629 },
	{ 1639, 1630 },
	{ 1640, 1631 },
	{ 1641, 1632 },
	{ 1642, 1633 },
	{ 1643, 1634 },
	{ 1644, 1626 },
	{ 1646, 1628 },
	{ 1647, 1629 },
	{ 1649, 1631 },
	{ 1650, 1632 },
	{ 1651, 1633 },
	{ 1652, 1634 },
};

static const uint16_t x86_16_bit_eq_lookup[] = {
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 0, 
	13, 14, 0, 15, 16, 17, 18, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 19, 20, 21, 22, 23, 24, 
	25, 26, 27, 28, 29, 30, 31, 0, 32, 33, 0, 0, 
	34, 35, 36, 37, 38, 39, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 40, 41, 42, 43, 44, 
	45, 46, 47, 48, 49, 0, 50, 51, 0, 52, 53, 54, 
	55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 56, 0, 0, 57, 58, 59, 60, 0, 
	0, 61, 62, 63, 64, 0, 0, 0, 0, 0, 0, 65, 
	66, 67, 68, 69, 70, 71, 72, 0, 0, 0, 0, 73, 
	74, 75, 76, 77, 78, 79, 80, 0, 0, 0, 0, 81, 
	82, 83, 84, 85, 86, 87, 88, 0, 0, 0, 0, 89, 
	90, 91, 92, 93, 94, 95, 96, 0, 0, 0, 0, 0, 
	0, 97, 98, 99, 0, 100, 0, 101, 0, 0, 0, 0, 
	0, 102, 0, 0, 0, 0, 0, 0, 103, 104, 105, 106, 
	0, 0, 107, 108, 109, 110, 0, 0, 111, 112, 113, 114, 
	0, 0, 115, 116, 117, 118, 0, 0, 119, 120, 121, 122, 
	0, 0, 123, 124, 125, 126, 0, 0, 127, 128, 129, 130, 
	0, 0, 131, 132, 133, 134, 0, 0, 135, 136, 137, 138, 
	0, 0, 139, 140, 141, 142, 0, 0, 143, 144, 145, 146, 
	0, 0, 147, 148, 149, 150, 0, 0, 151, 152, 153, 154, 
	0, 0, 155, 156, 157, 158, 0, 0, 159, 160, 161, 162, 
	0, 0, 163, 164, 165, 166, 0, 0, 0, 167, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 168, 169, 170, 171, 
	172, 173, 174, 175, 176, 177, 0, 178, 179, 0, 180, 181, 
	182, 183, 0, 0, 0, 0, 0, 0, 0, 0, 184, 185, 
	0, 0, 0, 0, 186, 187, 188, 189, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 190, 191, 
	192, 0, 0, 193, 194, 195, 196, 0, 0, 0, 0, 197, 
	198, 199, 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 201, 202, 0, 0, 0, 203, 204, 0, 0, 
	0, 0, 0, 0, 205, 206, 207, 208, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 209, 210, 211, 212, 213, 214, 
	215, 216, 217, 218, 219, 0, 220, 221, 0, 222, 0, 0, 
	0, 0, 223, 224, 0, 0, 0, 0, 0, 225, 226, 227, 
	0, 0, 228, 229, 230, 231, 0, 0, 0, 232, 0, 0, 
	0, 233, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 234, 235, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 236, 237, 238, 239, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 240, 241, 242, 
	243, 0, 0, 244, 245, 0, 0, 0, 246, 0, 247, 0, 
	248, 0, 0, 0, 249, 0, 250, 251, 0, 252, 253, 0, 
	254, 255, 0, 256, 257, 0, 0, 0, 0, 0, 0, 0, 
	258, 259, 260, 0, 261, 262, 0, 0, 0, 0, 0, 263, 
	264, 265, 0, 266, 267, 0, 0, 0, 268, 269, 0, 0, 
	270, 271, 0, 0, 0, 0, 272, 273, 274, 0, 275, 276, 
	0, 0, 0, 0, 0, 0, 277, 278, 279, 0, 280, 281, 
	0, 0, 0, 0, 0, 282, 283, 284, 0, 285, 286, 0, 
	0, 0, 287, 288, 0, 0, 0, 0, 289, 290, 0, 291, 
	292, 0, 0, 0, 293, 294, 295, 296, 0, 297, 298, 0, 
	0, 0, 299, 300, 0, 0, 0, 301, 302, 303, 304, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 305, 306, 0, 0, 307, 308, 309, 
	310, 311, 0, 0, 0, 312, 0, 313, 314, 315, 316, 317, 
	318, 319, 320, 321, 323, 0, 0, 0, 0, 325, 326, 327, 
	328, 330, 0, 0, 0, 332, 0, 333, 334, 335, 336, 337, 
	338, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 339, 340, 341, 342, 0, 0, 343, 
	344, 0, 0, 0, 0, 345, 0, 346, 0, 0, 347, 348, 
	0, 349, 350, 0, 0, 0, 0, 0, 351, 0, 352, 0, 
	0, 0, 0, 0, 0, 353, 354, 355, 356, 0, 0, 0, 
	0, 0, 0, 0, 0, 357, 358, 359, 360, 0, 0, 0, 
	361, 0, 0, 0, 362, 0, 0, 0, 363, 0, 0, 0, 
	364, 0, 0, 0, 0, 365, 366, 367, 368, 369, 370, 371, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 373, 374, 
	375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	376, 377, 378, 379, 0, 380, 381, 382, 383, 384, 385, 0, 
	386, 387, 0, 388, 389, 390, 391, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 392, 393, 0, 0, 0, 394, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 395, 
	396, 397, 398, 399, 400, 0, 401, 0, 402, 0, 403, 0, 
	404, 405, 0, 406, 407, 0, 408, 409, 0, 410, 0, 0, 
	0, 0, 411, 412, 413, 414, 0, 415, 416, 417, 418, 419, 
	0, 420, 0, 421, 0, 422, 0, 423, 0, 424, 425, 0, 
	426, 427, 0, 428, 429, 0, 430, 0, 431, 0, 0, 0, 
	0, 0, 0, 432, 433, 434, 435, 436, 437, 438, 439, 440, 
	441, 442, 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 444, 445, 446, 447, 448, 449, 450, 451, 452, 
	453, 454, 455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 456, 457, 0, 458, 459, 0, 0, 0, 
	460, 461, 0, 0, 0, 0, 0, 0, 462, 0, 0, 0, 
	0, 0, 0, 0, 463, 0, 0, 464, 465, 0, 466, 467, 
	0, 0, 0, 0, 0, 0, 0, 0, 468, 469, 470, 471, 
	472, 473, 474, 475, 476, 477, 478, 479, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 480, 481, 482, 483, 
	484, 485, 486, 487, 488, 489, 490, 491, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 
	502, 503, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 504, 505, 506, 507, 508, 509, 510, 511, 512, 
	513, 514, 515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 516, 517, 
	518, 519, 520, 521, 522, 523, 524, 525, 0, 526, 527, 0, 
	528, 529, 530, 531, 0, 0, 0, 0, 0, 0, 0, 0, 
	532, 533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 534, 535, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 536, 537, 0, 0, 0, 0, 0, 0, 538, 539, 
	540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 550, 551, 552, 553, 
	554, 555, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 
	568, 569, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	570, 571, 572, 573, 574, 575, 576, 577, 0, 0, 0, 0, 
	0, 578, 579, 0, 0, 0, 580, 581, 582, 0, 0, 583, 
	584, 0, 0, 0, 0, 0, 0, 585, 586, 0, 0, 587, 
	588, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 589, 
	590, 591, 592, 593, 594, 595, 596, 597, 598, 0, 599, 600, 
	0, 601, 602, 603, 604, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 605, 606, 607, 608, 609, 
	610, 611, 612, 0, 0, 0, 0, 613, 614, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 615, 616, 617, 618, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 619, 620, 621, 622, 0, 0, 0, 
	0, 0, 623, 0, 624, 625, 626, 627, 628, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 629, 630, 631, 632, 633, 634, 635, 636, 637, 
	638, 0, 639, 640, 0, 641, 642, 643, 644, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0,
};

#endif // GET_INSTRINFO_MC_DESC
