This directory includes all the necessary files to compile Capstone on Windows
using Microsoft Visual Studio (VS).


NOTE:

(1) Visual Studio 2010 or newer versions is required. Open "capstone.sln" to
    build the libraries & test code with Visual Studio. The resulted binaries
    are put under either msvc/Debug, msvc/Release, msvc/x64/Debug, or
    msvc/x64/Release, depending on how you choose to compile them.

(2) The solution (capstone.sln) & all project files (*.vcxproj) are made in
    Visual Studio 2010, so if you open them using newer version, an extra step
    is needed to convert them to current version. Just accept this when
    asked at the initial dialog, and proceed to build the solution normally
    afterwards.

(3) The capstone_static_winkernel and test_winkernel projects are for Windows
    kernel drivers and excluded from build by default. In order to build them,
    you need to install Visual Studio 2013 or newer versions, and Windows Driver
    Kit 8.1 Update 1 or newer versions, then check "Build" check boxes for those
    projects on the Configuration Manager through the [Build] menu.
