# CS_ARCH_MIPS, CS_MODE_MIPS64+CS_MODE_BIG_ENDIAN, None
0x64,0x00,0x00,0x00 = daddiu $zero, $zero, 0
0x64,0x01,0x00,0x00 = daddiu $at, $zero, 0
0x64,0x02,0x00,0x00 = daddiu $v0, $zero, 0
0x64,0x03,0x00,0x00 = daddiu $v1, $zero, 0
0x64,0x04,0x00,0x00 = daddiu $a0, $zero, 0
0x64,0x05,0x00,0x00 = daddiu $a1, $zero, 0
0x64,0x06,0x00,0x00 = daddiu $a2, $zero, 0
0x64,0x07,0x00,0x00 = daddiu $a2, $zero, 0
0x64,0x08,0x00,0x00 = daddiu $a4, $zero, 0
0x64,0x09,0x00,0x00 = daddiu $a5, $zero, 0
0x64,0x0a,0x00,0x00 = daddiu $a6, $zero, 0
0x64,0x0b,0x00,0x00 = daddiu $a7, $zero, 0
0x64,0x0c,0x00,0x00 = daddiu $t4, $zero, 0
0x64,0x0d,0x00,0x00 = daddiu $t5, $zero, 0
0x64,0x0e,0x00,0x00 = daddiu $t6, $zero, 0
0x64,0x0f,0x00,0x00 = daddiu $t7, $zero, 0
0x64,0x10,0x00,0x00 = daddiu $s0, $zero, 0
0x64,0x11,0x00,0x00 = daddiu $s1, $zero, 0
0x64,0x12,0x00,0x00 = daddiu $s2, $zero, 0
0x64,0x13,0x00,0x00 = daddiu $s3, $zero, 0
0x64,0x14,0x00,0x00 = daddiu $s4, $zero, 0
0x64,0x15,0x00,0x00 = daddiu $s5, $zero, 0
0x64,0x16,0x00,0x00 = daddiu $s6, $zero, 0
0x64,0x17,0x00,0x00 = daddiu $s7, $zero, 0
0x64,0x18,0x00,0x00 = daddiu $t8, $zero, 0
0x64,0x19,0x00,0x00 = daddiu $t9, $zero, 0
0x64,0x1a,0x00,0x00 = daddiu $kt0, $zero, 0
0x64,0x1b,0x00,0x00 = daddiu $kt1, $zero, 0
0x64,0x1c,0x00,0x00 = daddiu $gp, $zero, 0
0x64,0x1d,0x00,0x00 = daddiu $sp, $zero, 0
0x64,0x1e,0x00,0x00 = daddiu $s8, $zero, 0
0x64,0x1f,0x00,0x00 = daddiu $ra, $zero, 0
