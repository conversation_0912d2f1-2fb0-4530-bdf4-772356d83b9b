(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [sparc_const.ml] *)

(* Enums corresponding to Sparc condition codes, both icc's and fcc's. *)

let _SPARC_CC_INVALID = 0;;

(* Integer condition codes *)
let _SPARC_CC_ICC_A = 8+256;;
let _SPARC_CC_ICC_N = 0+256;;
let _SPARC_CC_ICC_NE = 9+256;;
let _SPARC_CC_ICC_E = 1+256;;
let _SPARC_CC_ICC_G = 10+256;;
let _SPARC_CC_ICC_LE = 2+256;;
let _SPARC_CC_ICC_GE = 11+256;;
let _SPARC_CC_ICC_L = 3+256;;
let _SPARC_CC_ICC_GU = 12+256;;
let _SPARC_CC_ICC_LEU = 4+256;;
let _SPARC_CC_ICC_CC = 13+256;;
let _SPARC_CC_ICC_CS = 5+256;;
let _SPARC_CC_ICC_POS = 14+256;;
let _SPARC_CC_ICC_NEG = 6+256;;
let _SPARC_CC_ICC_VC = 15+256;;
let _SPARC_CC_ICC_VS = 7+256;;

(* Floating condition codes *)
let _SPARC_CC_FCC_A = 8+16+256;;
let _SPARC_CC_FCC_N = 0+16+256;;
let _SPARC_CC_FCC_U = 7+16+256;;
let _SPARC_CC_FCC_G = 6+16+256;;
let _SPARC_CC_FCC_UG = 5+16+256;;
let _SPARC_CC_FCC_L = 4+16+256;;
let _SPARC_CC_FCC_UL = 3+16+256;;
let _SPARC_CC_FCC_LG = 2+16+256;;
let _SPARC_CC_FCC_NE = 1+16+256;;
let _SPARC_CC_FCC_E = 9+16+256;;
let _SPARC_CC_FCC_UE = 10+16+256;;
let _SPARC_CC_FCC_GE = 11+16+256;;
let _SPARC_CC_FCC_UGE = 12+16+256;;
let _SPARC_CC_FCC_LE = 13+16+256;;
let _SPARC_CC_FCC_ULE = 14+16+256;;
let _SPARC_CC_FCC_O = 15+16+256;;

(* Branch hint *)

let _SPARC_HINT_INVALID = 0;;
let _SPARC_HINT_A = 1 lsl 0;;
let _SPARC_HINT_PT = 1 lsl 1;;
let _SPARC_HINT_PN = 1 lsl 2;;

(* Operand type for instruction's operands *)

let _SPARC_OP_INVALID = 0;;
let _SPARC_OP_REG = 1;;
let _SPARC_OP_IMM = 2;;
let _SPARC_OP_MEM = 3;;

(* SPARC registers *)

let _SPARC_REG_INVALID = 0;;
let _SPARC_REG_F0 = 1;;
let _SPARC_REG_F1 = 2;;
let _SPARC_REG_F2 = 3;;
let _SPARC_REG_F3 = 4;;
let _SPARC_REG_F4 = 5;;
let _SPARC_REG_F5 = 6;;
let _SPARC_REG_F6 = 7;;
let _SPARC_REG_F7 = 8;;
let _SPARC_REG_F8 = 9;;
let _SPARC_REG_F9 = 10;;
let _SPARC_REG_F10 = 11;;
let _SPARC_REG_F11 = 12;;
let _SPARC_REG_F12 = 13;;
let _SPARC_REG_F13 = 14;;
let _SPARC_REG_F14 = 15;;
let _SPARC_REG_F15 = 16;;
let _SPARC_REG_F16 = 17;;
let _SPARC_REG_F17 = 18;;
let _SPARC_REG_F18 = 19;;
let _SPARC_REG_F19 = 20;;
let _SPARC_REG_F20 = 21;;
let _SPARC_REG_F21 = 22;;
let _SPARC_REG_F22 = 23;;
let _SPARC_REG_F23 = 24;;
let _SPARC_REG_F24 = 25;;
let _SPARC_REG_F25 = 26;;
let _SPARC_REG_F26 = 27;;
let _SPARC_REG_F27 = 28;;
let _SPARC_REG_F28 = 29;;
let _SPARC_REG_F29 = 30;;
let _SPARC_REG_F30 = 31;;
let _SPARC_REG_F31 = 32;;
let _SPARC_REG_F32 = 33;;
let _SPARC_REG_F34 = 34;;
let _SPARC_REG_F36 = 35;;
let _SPARC_REG_F38 = 36;;
let _SPARC_REG_F40 = 37;;
let _SPARC_REG_F42 = 38;;
let _SPARC_REG_F44 = 39;;
let _SPARC_REG_F46 = 40;;
let _SPARC_REG_F48 = 41;;
let _SPARC_REG_F50 = 42;;
let _SPARC_REG_F52 = 43;;
let _SPARC_REG_F54 = 44;;
let _SPARC_REG_F56 = 45;;
let _SPARC_REG_F58 = 46;;
let _SPARC_REG_F60 = 47;;
let _SPARC_REG_F62 = 48;;
let _SPARC_REG_FCC0 = 49;;
let _SPARC_REG_FCC1 = 50;;
let _SPARC_REG_FCC2 = 51;;
let _SPARC_REG_FCC3 = 52;;
let _SPARC_REG_FP = 53;;
let _SPARC_REG_G0 = 54;;
let _SPARC_REG_G1 = 55;;
let _SPARC_REG_G2 = 56;;
let _SPARC_REG_G3 = 57;;
let _SPARC_REG_G4 = 58;;
let _SPARC_REG_G5 = 59;;
let _SPARC_REG_G6 = 60;;
let _SPARC_REG_G7 = 61;;
let _SPARC_REG_I0 = 62;;
let _SPARC_REG_I1 = 63;;
let _SPARC_REG_I2 = 64;;
let _SPARC_REG_I3 = 65;;
let _SPARC_REG_I4 = 66;;
let _SPARC_REG_I5 = 67;;
let _SPARC_REG_I7 = 68;;
let _SPARC_REG_ICC = 69;;
let _SPARC_REG_L0 = 70;;
let _SPARC_REG_L1 = 71;;
let _SPARC_REG_L2 = 72;;
let _SPARC_REG_L3 = 73;;
let _SPARC_REG_L4 = 74;;
let _SPARC_REG_L5 = 75;;
let _SPARC_REG_L6 = 76;;
let _SPARC_REG_L7 = 77;;
let _SPARC_REG_O0 = 78;;
let _SPARC_REG_O1 = 79;;
let _SPARC_REG_O2 = 80;;
let _SPARC_REG_O3 = 81;;
let _SPARC_REG_O4 = 82;;
let _SPARC_REG_O5 = 83;;
let _SPARC_REG_O7 = 84;;
let _SPARC_REG_SP = 85;;
let _SPARC_REG_Y = 86;;
let _SPARC_REG_XCC = 87;;
let _SPARC_REG_ENDING = 88;;
let _SPARC_REG_O6 = _SPARC_REG_SP;;
let _SPARC_REG_I6 = _SPARC_REG_FP;;

(* SPARC instruction *)

let _SPARC_INS_INVALID = 0;;
let _SPARC_INS_ADDCC = 1;;
let _SPARC_INS_ADDX = 2;;
let _SPARC_INS_ADDXCC = 3;;
let _SPARC_INS_ADDXC = 4;;
let _SPARC_INS_ADDXCCC = 5;;
let _SPARC_INS_ADD = 6;;
let _SPARC_INS_ALIGNADDR = 7;;
let _SPARC_INS_ALIGNADDRL = 8;;
let _SPARC_INS_ANDCC = 9;;
let _SPARC_INS_ANDNCC = 10;;
let _SPARC_INS_ANDN = 11;;
let _SPARC_INS_AND = 12;;
let _SPARC_INS_ARRAY16 = 13;;
let _SPARC_INS_ARRAY32 = 14;;
let _SPARC_INS_ARRAY8 = 15;;
let _SPARC_INS_B = 16;;
let _SPARC_INS_JMP = 17;;
let _SPARC_INS_BMASK = 18;;
let _SPARC_INS_FB = 19;;
let _SPARC_INS_BRGEZ = 20;;
let _SPARC_INS_BRGZ = 21;;
let _SPARC_INS_BRLEZ = 22;;
let _SPARC_INS_BRLZ = 23;;
let _SPARC_INS_BRNZ = 24;;
let _SPARC_INS_BRZ = 25;;
let _SPARC_INS_BSHUFFLE = 26;;
let _SPARC_INS_CALL = 27;;
let _SPARC_INS_CASX = 28;;
let _SPARC_INS_CAS = 29;;
let _SPARC_INS_CMASK16 = 30;;
let _SPARC_INS_CMASK32 = 31;;
let _SPARC_INS_CMASK8 = 32;;
let _SPARC_INS_CMP = 33;;
let _SPARC_INS_EDGE16 = 34;;
let _SPARC_INS_EDGE16L = 35;;
let _SPARC_INS_EDGE16LN = 36;;
let _SPARC_INS_EDGE16N = 37;;
let _SPARC_INS_EDGE32 = 38;;
let _SPARC_INS_EDGE32L = 39;;
let _SPARC_INS_EDGE32LN = 40;;
let _SPARC_INS_EDGE32N = 41;;
let _SPARC_INS_EDGE8 = 42;;
let _SPARC_INS_EDGE8L = 43;;
let _SPARC_INS_EDGE8LN = 44;;
let _SPARC_INS_EDGE8N = 45;;
let _SPARC_INS_FABSD = 46;;
let _SPARC_INS_FABSQ = 47;;
let _SPARC_INS_FABSS = 48;;
let _SPARC_INS_FADDD = 49;;
let _SPARC_INS_FADDQ = 50;;
let _SPARC_INS_FADDS = 51;;
let _SPARC_INS_FALIGNDATA = 52;;
let _SPARC_INS_FAND = 53;;
let _SPARC_INS_FANDNOT1 = 54;;
let _SPARC_INS_FANDNOT1S = 55;;
let _SPARC_INS_FANDNOT2 = 56;;
let _SPARC_INS_FANDNOT2S = 57;;
let _SPARC_INS_FANDS = 58;;
let _SPARC_INS_FCHKSM16 = 59;;
let _SPARC_INS_FCMPD = 60;;
let _SPARC_INS_FCMPEQ16 = 61;;
let _SPARC_INS_FCMPEQ32 = 62;;
let _SPARC_INS_FCMPGT16 = 63;;
let _SPARC_INS_FCMPGT32 = 64;;
let _SPARC_INS_FCMPLE16 = 65;;
let _SPARC_INS_FCMPLE32 = 66;;
let _SPARC_INS_FCMPNE16 = 67;;
let _SPARC_INS_FCMPNE32 = 68;;
let _SPARC_INS_FCMPQ = 69;;
let _SPARC_INS_FCMPS = 70;;
let _SPARC_INS_FDIVD = 71;;
let _SPARC_INS_FDIVQ = 72;;
let _SPARC_INS_FDIVS = 73;;
let _SPARC_INS_FDMULQ = 74;;
let _SPARC_INS_FDTOI = 75;;
let _SPARC_INS_FDTOQ = 76;;
let _SPARC_INS_FDTOS = 77;;
let _SPARC_INS_FDTOX = 78;;
let _SPARC_INS_FEXPAND = 79;;
let _SPARC_INS_FHADDD = 80;;
let _SPARC_INS_FHADDS = 81;;
let _SPARC_INS_FHSUBD = 82;;
let _SPARC_INS_FHSUBS = 83;;
let _SPARC_INS_FITOD = 84;;
let _SPARC_INS_FITOQ = 85;;
let _SPARC_INS_FITOS = 86;;
let _SPARC_INS_FLCMPD = 87;;
let _SPARC_INS_FLCMPS = 88;;
let _SPARC_INS_FLUSHW = 89;;
let _SPARC_INS_FMEAN16 = 90;;
let _SPARC_INS_FMOVD = 91;;
let _SPARC_INS_FMOVQ = 92;;
let _SPARC_INS_FMOVRDGEZ = 93;;
let _SPARC_INS_FMOVRQGEZ = 94;;
let _SPARC_INS_FMOVRSGEZ = 95;;
let _SPARC_INS_FMOVRDGZ = 96;;
let _SPARC_INS_FMOVRQGZ = 97;;
let _SPARC_INS_FMOVRSGZ = 98;;
let _SPARC_INS_FMOVRDLEZ = 99;;
let _SPARC_INS_FMOVRQLEZ = 100;;
let _SPARC_INS_FMOVRSLEZ = 101;;
let _SPARC_INS_FMOVRDLZ = 102;;
let _SPARC_INS_FMOVRQLZ = 103;;
let _SPARC_INS_FMOVRSLZ = 104;;
let _SPARC_INS_FMOVRDNZ = 105;;
let _SPARC_INS_FMOVRQNZ = 106;;
let _SPARC_INS_FMOVRSNZ = 107;;
let _SPARC_INS_FMOVRDZ = 108;;
let _SPARC_INS_FMOVRQZ = 109;;
let _SPARC_INS_FMOVRSZ = 110;;
let _SPARC_INS_FMOVS = 111;;
let _SPARC_INS_FMUL8SUX16 = 112;;
let _SPARC_INS_FMUL8ULX16 = 113;;
let _SPARC_INS_FMUL8X16 = 114;;
let _SPARC_INS_FMUL8X16AL = 115;;
let _SPARC_INS_FMUL8X16AU = 116;;
let _SPARC_INS_FMULD = 117;;
let _SPARC_INS_FMULD8SUX16 = 118;;
let _SPARC_INS_FMULD8ULX16 = 119;;
let _SPARC_INS_FMULQ = 120;;
let _SPARC_INS_FMULS = 121;;
let _SPARC_INS_FNADDD = 122;;
let _SPARC_INS_FNADDS = 123;;
let _SPARC_INS_FNAND = 124;;
let _SPARC_INS_FNANDS = 125;;
let _SPARC_INS_FNEGD = 126;;
let _SPARC_INS_FNEGQ = 127;;
let _SPARC_INS_FNEGS = 128;;
let _SPARC_INS_FNHADDD = 129;;
let _SPARC_INS_FNHADDS = 130;;
let _SPARC_INS_FNOR = 131;;
let _SPARC_INS_FNORS = 132;;
let _SPARC_INS_FNOT1 = 133;;
let _SPARC_INS_FNOT1S = 134;;
let _SPARC_INS_FNOT2 = 135;;
let _SPARC_INS_FNOT2S = 136;;
let _SPARC_INS_FONE = 137;;
let _SPARC_INS_FONES = 138;;
let _SPARC_INS_FOR = 139;;
let _SPARC_INS_FORNOT1 = 140;;
let _SPARC_INS_FORNOT1S = 141;;
let _SPARC_INS_FORNOT2 = 142;;
let _SPARC_INS_FORNOT2S = 143;;
let _SPARC_INS_FORS = 144;;
let _SPARC_INS_FPACK16 = 145;;
let _SPARC_INS_FPACK32 = 146;;
let _SPARC_INS_FPACKFIX = 147;;
let _SPARC_INS_FPADD16 = 148;;
let _SPARC_INS_FPADD16S = 149;;
let _SPARC_INS_FPADD32 = 150;;
let _SPARC_INS_FPADD32S = 151;;
let _SPARC_INS_FPADD64 = 152;;
let _SPARC_INS_FPMERGE = 153;;
let _SPARC_INS_FPSUB16 = 154;;
let _SPARC_INS_FPSUB16S = 155;;
let _SPARC_INS_FPSUB32 = 156;;
let _SPARC_INS_FPSUB32S = 157;;
let _SPARC_INS_FQTOD = 158;;
let _SPARC_INS_FQTOI = 159;;
let _SPARC_INS_FQTOS = 160;;
let _SPARC_INS_FQTOX = 161;;
let _SPARC_INS_FSLAS16 = 162;;
let _SPARC_INS_FSLAS32 = 163;;
let _SPARC_INS_FSLL16 = 164;;
let _SPARC_INS_FSLL32 = 165;;
let _SPARC_INS_FSMULD = 166;;
let _SPARC_INS_FSQRTD = 167;;
let _SPARC_INS_FSQRTQ = 168;;
let _SPARC_INS_FSQRTS = 169;;
let _SPARC_INS_FSRA16 = 170;;
let _SPARC_INS_FSRA32 = 171;;
let _SPARC_INS_FSRC1 = 172;;
let _SPARC_INS_FSRC1S = 173;;
let _SPARC_INS_FSRC2 = 174;;
let _SPARC_INS_FSRC2S = 175;;
let _SPARC_INS_FSRL16 = 176;;
let _SPARC_INS_FSRL32 = 177;;
let _SPARC_INS_FSTOD = 178;;
let _SPARC_INS_FSTOI = 179;;
let _SPARC_INS_FSTOQ = 180;;
let _SPARC_INS_FSTOX = 181;;
let _SPARC_INS_FSUBD = 182;;
let _SPARC_INS_FSUBQ = 183;;
let _SPARC_INS_FSUBS = 184;;
let _SPARC_INS_FXNOR = 185;;
let _SPARC_INS_FXNORS = 186;;
let _SPARC_INS_FXOR = 187;;
let _SPARC_INS_FXORS = 188;;
let _SPARC_INS_FXTOD = 189;;
let _SPARC_INS_FXTOQ = 190;;
let _SPARC_INS_FXTOS = 191;;
let _SPARC_INS_FZERO = 192;;
let _SPARC_INS_FZEROS = 193;;
let _SPARC_INS_JMPL = 194;;
let _SPARC_INS_LDD = 195;;
let _SPARC_INS_LD = 196;;
let _SPARC_INS_LDQ = 197;;
let _SPARC_INS_LDSB = 198;;
let _SPARC_INS_LDSH = 199;;
let _SPARC_INS_LDSW = 200;;
let _SPARC_INS_LDUB = 201;;
let _SPARC_INS_LDUH = 202;;
let _SPARC_INS_LDX = 203;;
let _SPARC_INS_LZCNT = 204;;
let _SPARC_INS_MEMBAR = 205;;
let _SPARC_INS_MOVDTOX = 206;;
let _SPARC_INS_MOV = 207;;
let _SPARC_INS_MOVRGEZ = 208;;
let _SPARC_INS_MOVRGZ = 209;;
let _SPARC_INS_MOVRLEZ = 210;;
let _SPARC_INS_MOVRLZ = 211;;
let _SPARC_INS_MOVRNZ = 212;;
let _SPARC_INS_MOVRZ = 213;;
let _SPARC_INS_MOVSTOSW = 214;;
let _SPARC_INS_MOVSTOUW = 215;;
let _SPARC_INS_MULX = 216;;
let _SPARC_INS_NOP = 217;;
let _SPARC_INS_ORCC = 218;;
let _SPARC_INS_ORNCC = 219;;
let _SPARC_INS_ORN = 220;;
let _SPARC_INS_OR = 221;;
let _SPARC_INS_PDIST = 222;;
let _SPARC_INS_PDISTN = 223;;
let _SPARC_INS_POPC = 224;;
let _SPARC_INS_RD = 225;;
let _SPARC_INS_RESTORE = 226;;
let _SPARC_INS_RETT = 227;;
let _SPARC_INS_SAVE = 228;;
let _SPARC_INS_SDIVCC = 229;;
let _SPARC_INS_SDIVX = 230;;
let _SPARC_INS_SDIV = 231;;
let _SPARC_INS_SETHI = 232;;
let _SPARC_INS_SHUTDOWN = 233;;
let _SPARC_INS_SIAM = 234;;
let _SPARC_INS_SLLX = 235;;
let _SPARC_INS_SLL = 236;;
let _SPARC_INS_SMULCC = 237;;
let _SPARC_INS_SMUL = 238;;
let _SPARC_INS_SRAX = 239;;
let _SPARC_INS_SRA = 240;;
let _SPARC_INS_SRLX = 241;;
let _SPARC_INS_SRL = 242;;
let _SPARC_INS_STBAR = 243;;
let _SPARC_INS_STB = 244;;
let _SPARC_INS_STD = 245;;
let _SPARC_INS_ST = 246;;
let _SPARC_INS_STH = 247;;
let _SPARC_INS_STQ = 248;;
let _SPARC_INS_STX = 249;;
let _SPARC_INS_SUBCC = 250;;
let _SPARC_INS_SUBX = 251;;
let _SPARC_INS_SUBXCC = 252;;
let _SPARC_INS_SUB = 253;;
let _SPARC_INS_SWAP = 254;;
let _SPARC_INS_TADDCCTV = 255;;
let _SPARC_INS_TADDCC = 256;;
let _SPARC_INS_T = 257;;
let _SPARC_INS_TSUBCCTV = 258;;
let _SPARC_INS_TSUBCC = 259;;
let _SPARC_INS_UDIVCC = 260;;
let _SPARC_INS_UDIVX = 261;;
let _SPARC_INS_UDIV = 262;;
let _SPARC_INS_UMULCC = 263;;
let _SPARC_INS_UMULXHI = 264;;
let _SPARC_INS_UMUL = 265;;
let _SPARC_INS_UNIMP = 266;;
let _SPARC_INS_FCMPED = 267;;
let _SPARC_INS_FCMPEQ = 268;;
let _SPARC_INS_FCMPES = 269;;
let _SPARC_INS_WR = 270;;
let _SPARC_INS_XMULX = 271;;
let _SPARC_INS_XMULXHI = 272;;
let _SPARC_INS_XNORCC = 273;;
let _SPARC_INS_XNOR = 274;;
let _SPARC_INS_XORCC = 275;;
let _SPARC_INS_XOR = 276;;
let _SPARC_INS_RET = 277;;
let _SPARC_INS_RETL = 278;;
let _SPARC_INS_ENDING = 279;;

(* Group of SPARC instructions *)

let _SPARC_GRP_INVALID = 0;;

(* Generic groups *)
let _SPARC_GRP_JUMP = 1;;

(* Architecture-specific groups *)
let _SPARC_GRP_HARDQUAD = 128;;
let _SPARC_GRP_V9 = 129;;
let _SPARC_GRP_VIS = 130;;
let _SPARC_GRP_VIS2 = 131;;
let _SPARC_GRP_VIS3 = 132;;
let _SPARC_GRP_32BIT = 133;;
let _SPARC_GRP_64BIT = 134;;
let _SPARC_GRP_ENDING = 135;;
