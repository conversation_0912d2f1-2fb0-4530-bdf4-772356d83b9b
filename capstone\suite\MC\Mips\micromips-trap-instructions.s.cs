# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_MICRO, None
0x28,0x01,0x3c,0x00 = teq $8, $9
0x28,0x01,0x3c,0x02 = tge $8, $9
0x28,0x01,0x3c,0x04 = tgeu $8, $9
0x28,0x01,0x3c,0x08 = tlt $8, $9
0x28,0x01,0x3c,0x0a = tltu $8, $9
0x28,0x01,0x3c,0x0c = tne $8, $9
0xc9,0x41,0x67,0x45 = teqi $9, 17767
0x29,0x41,0x67,0x45 = tgei $9, 17767
0x69,0x41,0x67,0x45 = tgeiu $9, 17767
0x09,0x41,0x67,0x45 = tlti $9, 17767
0x49,0x41,0x67,0x45 = tltiu $9, 17767
0x89,0x41,0x67,0x45 = tnei $9, 17767
