# CS_ARCH_ARM, CS_MODE_THUMB, None
0xc0,0xef,0x18,0x0e = vmov.i8 d16, #0x8
0xc1,0xef,0x10,0x08 = vmov.i16 d16, #0x10
0xc1,0xef,0x10,0x0a = vmov.i16 d16, #0x1000
0xc2,0xef,0x10,0x00 = vmov.i32 d16, #0x20
0xc2,0xef,0x10,0x02 = vmov.i32 d16, #0x2000
0xc2,0xef,0x10,0x04 = vmov.i32 d16, #0x200000
0xc2,0xef,0x10,0x06 = vmov.i32 d16, #0x20000000
0xc2,0xef,0x10,0x0c = vmov.i32 d16, #0x20ff
0xc2,0xef,0x10,0x0d = vmov.i32 d16, #0x20ffff
0xc1,0xff,0x33,0x0e = vmov.i64 d16, #0xff0000ff0000ffff
0xc0,0xef,0x58,0x0e = vmov.i8 q8, #0x8
0xc1,0xef,0x50,0x08 = vmov.i16 q8, #0x10
0xc1,0xef,0x50,0x0a = vmov.i16 q8, #0x1000
0xc2,0xef,0x50,0x00 = vmov.i32 q8, #0x20
0xc2,0xef,0x50,0x02 = vmov.i32 q8, #0x2000
0xc2,0xef,0x50,0x04 = vmov.i32 q8, #0x200000
0xc2,0xef,0x50,0x06 = vmov.i32 q8, #0x20000000
0xc2,0xef,0x50,0x0c = vmov.i32 q8, #0x20ff
0xc2,0xef,0x50,0x0d = vmov.i32 q8, #0x20ffff
0xc1,0xff,0x73,0x0e = vmov.i64 q8, #0xff0000ff0000ffff
0xc1,0xef,0x30,0x08 = vmvn.i16 d16, #0x10
0xc1,0xef,0x30,0x0a = vmvn.i16 d16, #0x1000
0xc2,0xef,0x30,0x00 = vmvn.i32 d16, #0x20
0xc2,0xef,0x30,0x02 = vmvn.i32 d16, #0x2000
0xc2,0xef,0x30,0x04 = vmvn.i32 d16, #0x200000
0xc2,0xef,0x30,0x06 = vmvn.i32 d16, #0x20000000
0xc2,0xef,0x30,0x0c = vmvn.i32 d16, #0x20ff
0xc2,0xef,0x30,0x0d = vmvn.i32 d16, #0x20ffff
0xc8,0xef,0x30,0x0a = vmovl.s8 q8, d16
0xd0,0xef,0x30,0x0a = vmovl.s16 q8, d16
0xe0,0xef,0x30,0x0a = vmovl.s32 q8, d16
0xc8,0xff,0x30,0x0a = vmovl.u8 q8, d16
0xd0,0xff,0x30,0x0a = vmovl.u16 q8, d16
0xe0,0xff,0x30,0x0a = vmovl.u32 q8, d16
0xf2,0xff,0x20,0x02 = vmovn.i16 d16, q8
0xf6,0xff,0x20,0x02 = vmovn.i32 d16, q8
0xfa,0xff,0x20,0x02 = vmovn.i64 d16, q8
0xf2,0xff,0xa0,0x02 = vqmovn.s16 d16, q8
0xf6,0xff,0xa0,0x02 = vqmovn.s32 d16, q8
0xfa,0xff,0xa0,0x02 = vqmovn.s64 d16, q8
0xf2,0xff,0xe0,0x02 = vqmovn.u16 d16, q8
0xf6,0xff,0xe0,0x02 = vqmovn.u32 d16, q8
0xfa,0xff,0xe0,0x02 = vqmovn.u64 d16, q8
0xf2,0xff,0x60,0x02 = vqmovun.s16 d16, q8
0xf6,0xff,0x60,0x02 = vqmovun.s32 d16, q8
0xfa,0xff,0x60,0x02 = vqmovun.s64 d16, q8
0x50,0xee,0xb0,0x0b = vmov.s8 r0, d16[1]
0x10,0xee,0xf0,0x0b = vmov.s16 r0, d16[1]
0xd0,0xee,0xb0,0x0b = vmov.u8 r0, d16[1]
0x90,0xee,0xf0,0x0b = vmov.u16 r0, d16[1]
0x30,0xee,0x90,0x0b = vmov.32 r0, d16[1]
0x40,0xee,0xb0,0x1b = vmov.8 d16[1], r1
0x00,0xee,0xf0,0x1b = vmov.16 d16[1], r1
0x20,0xee,0x90,0x1b = vmov.32 d16[1], r1
0x42,0xee,0xb0,0x1b = vmov.8 d18[1], r1
0x02,0xee,0xf0,0x1b = vmov.16 d18[1], r1
0x22,0xee,0x90,0x1b = vmov.32 d18[1], r1
