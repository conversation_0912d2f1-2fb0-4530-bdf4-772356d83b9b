# CS_ARCH_ARM, CS_MODE_ARM, None
0x20,0x03,0xf1,0xf3 = vabs.s8 d16, d16
0x20,0x03,0xf5,0xf3 = vabs.s16 d16, d16
0x20,0x03,0xf9,0xf3 = vabs.s32 d16, d16
0x20,0x07,0xf9,0xf3 = vabs.f32 d16, d16
0x60,0x03,0xf1,0xf3 = vabs.s8 q8, q8
0x60,0x03,0xf5,0xf3 = vabs.s16 q8, q8
0x60,0x03,0xf9,0xf3 = vabs.s32 q8, q8
0x60,0x07,0xf9,0xf3 = vabs.f32 q8, q8
0x20,0x07,0xf0,0xf3 = vqabs.s8 d16, d16
0x20,0x07,0xf4,0xf3 = vqabs.s16 d16, d16
0x20,0x07,0xf8,0xf3 = vqabs.s32 d16, d16
0x60,0x07,0xf0,0xf3 = vqabs.s8 q8, q8
0x60,0x07,0xf4,0xf3 = vqabs.s16 q8, q8
0x60,0x07,0xf8,0xf3 = vqabs.s32 q8, q8
