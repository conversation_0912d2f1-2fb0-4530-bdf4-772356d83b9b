# CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_ATT
0x65,0x48,0x8b,0x07 = movq %gs:(%rdi), %rax
0xf2,0x0f,0x38,0xf0,0xc3 = crc32b %bl, %eax
0xf2,0x0f,0x38,0xf0,0x43,0x04 = crc32b 4(%rbx), %eax
0x66,0xf2,0x0f,0x38,0xf1,0xc3 = crc32w %bx, %eax
0x66,0xf2,0x0f,0x38,0xf1,0x43,0x04 = crc32w 4(%rbx), %eax
0xf2,0x0f,0x38,0xf1,0xc3 = crc32l %ebx, %eax
0xf2,0x0f,0x38,0xf1,0x43,0x04 = crc32l 4(%rbx), %eax
0xf2,0x0f,0x38,0xf1,0x8c,0xcb,0xef,0xbe,0xad,0xde = crc32l 0xdeadbeef(%rbx, %rcx, 8),%ecx
0xf2,0x0f,0x38,0xf1,0x0c,0x25,0x45,0x00,0x00,0x00 = crc32l 0x45, %ecx
0xf2,0x0f,0x38,0xf1,0x0c,0x25,0xed,0x7e,0x00,0x00 = crc32l 0x7eed, %ecx
0xf2,0x0f,0x38,0xf1,0x0c,0x25,0xfe,0xca,0xbe,0xba = crc32l 0xbabecafe, %ecx
0xf2,0x0f,0x38,0xf1,0xc9 = crc32l %ecx, %ecx
0xf2,0x41,0x0f,0x38,0xf0,0xc3 = crc32b %r11b, %eax
0xf2,0x0f,0x38,0xf0,0x43,0x04 = crc32b 4(%rbx), %eax
0xf2,0x48,0x0f,0x38,0xf0,0xc7 = crc32b %dil, %rax
0xf2,0x49,0x0f,0x38,0xf0,0xc3 = crc32b %r11b, %rax
0xf2,0x48,0x0f,0x38,0xf0,0x43,0x04 = crc32b 4(%rbx), %rax
0xf2,0x48,0x0f,0x38,0xf1,0xc3 = crc32q %rbx, %rax
0xf2,0x48,0x0f,0x38,0xf1,0x43,0x04 = crc32q 4(%rbx), %rax
0x49,0x0f,0x6e,0xc8 = movd %r8, %mm1
0x41,0x0f,0x6e,0xc8 = movd %r8d, %mm1
0x48,0x0f,0x6e,0xca = movd %rdx, %mm1
0x0f,0x6e,0xca = movd %edx, %mm1
0x49,0x0f,0x7e,0xc8 = movd %mm1, %r8
0x41,0x0f,0x7e,0xc8 = movd %mm1, %r8d
0x48,0x0f,0x7e,0xca = movd %mm1, %rdx
0x0f,0x7e,0xca = movd %mm1, %edx
0x0f,0x3a,0xcc,0xd1,0x01 = sha1rnds4 $1, %xmm1, %xmm2
0x0f,0x3a,0xcc,0x10,0x01 = sha1rnds4 $1, (%rax), %xmm2
0x0f,0x38,0xc8,0xd1 = sha1nexte %xmm1, %xmm2
0x0f,0x38,0xc9,0xd1 = sha1msg1 %xmm1, %xmm2
0x0f,0x38,0xc9,0x10 = sha1msg1 (%rax), %xmm2
0x0f,0x38,0xca,0xd1 = sha1msg2 %xmm1, %xmm2
0x0f,0x38,0xca,0x10 = sha1msg2 (%rax), %xmm2
0x0f,0x38,0xcb,0x10 = sha256rnds2 (%rax), %xmm2
0x0f,0x38,0xcb,0xd1 = sha256rnds2 %xmm1, %xmm2
0x0f,0x38,0xcb,0x10 = sha256rnds2 %xmm0, (%rax), %xmm2
0x0f,0x38,0xcb,0xd1 = sha256rnds2 %xmm0, %xmm1, %xmm2
0x0f,0x38,0xcc,0xd1 = sha256msg1 %xmm1, %xmm2
0x0f,0x38,0xcc,0x10 = sha256msg1 (%rax), %xmm2
0x0f,0x38,0xcd,0xd1 = sha256msg2 %xmm1, %xmm2
0x0f,0x38,0xcd,0x10 = sha256msg2 (%rax), %xmm2
0x48,0x8b,0x1c,0x25,0xad,0xde,0x00,0x00 = movq 57005(, %riz), %rbx
0x48,0x8b,0x04,0x25,0xef,0xbe,0x00,0x00 = movq 48879(, %riz), %rax
0x48,0x8b,0x04,0xe5,0xfc,0xff,0xff,0xff = movq -4(, %riz, 8), %rax
0x48,0x8b,0x04,0x21 = movq (%rcx, %riz), %rax
0x48,0x8b,0x04,0xe1 = movq (%rcx, %riz, 8), %rax
0x48,0x0f,0xae,0x00 = fxsaveq (%rax)
0x48,0x0f,0xae,0x08 = fxrstorq (%rax)
0xc9 = leave 
0xc9 = leave 
0x67,0xd9,0x07 = flds (%edi)
0x67,0xdf,0x07 = filds (%edi)
0xd9,0x07 = flds (%rdi)
0xdf,0x07 = filds (%rdi)
0x66,0x0f,0xd7,0xcd = pmovmskb %xmm5, %ecx
0x66,0x0f,0xc4,0xe9,0x03 = pinsrw $3, %ecx, %xmm5
0x66,0x0f,0xc4,0xe9,0x03 = pinsrw $3, %ecx, %xmm5
