# CS_ARCH_ARM, CS_MODE_THUMB, None
0x40,0xff,0xa1,0x04 = vshl.u8 d16, d17, d16
0x50,0xff,0xa1,0x04 = vshl.u16 d16, d17, d16
0x60,0xff,0xa1,0x04 = vshl.u32 d16, d17, d16
0x70,0xff,0xa1,0x04 = vshl.u64 d16, d17, d16
0xcf,0xef,0x30,0x05 = vshl.i8 d16, d16, #7
0xdf,0xef,0x30,0x05 = vshl.i16 d16, d16, #15
0xff,0xef,0x30,0x05 = vshl.i32 d16, d16, #31
0xff,0xef,0xb0,0x05 = vshl.i64 d16, d16, #63
0x40,0xff,0xe2,0x04 = vshl.u8 q8, q9, q8
0x50,0xff,0xe2,0x04 = vshl.u16 q8, q9, q8
0x60,0xff,0xe2,0x04 = vshl.u32 q8, q9, q8
0x70,0xff,0xe2,0x04 = vshl.u64 q8, q9, q8
0xcf,0xef,0x70,0x05 = vshl.i8 q8, q8, #7
0xdf,0xef,0x70,0x05 = vshl.i16 q8, q8, #15
0xff,0xef,0x70,0x05 = vshl.i32 q8, q8, #31
0xff,0xef,0xf0,0x05 = vshl.i64 q8, q8, #63
0xc8,0xff,0x30,0x00 = vshr.u8 d16, d16, #8
0xd0,0xff,0x30,0x00 = vshr.u16 d16, d16, #16
0xe0,0xff,0x30,0x00 = vshr.u32 d16, d16, #32
0xc0,0xff,0xb0,0x00 = vshr.u64 d16, d16, #64
0xc8,0xff,0x70,0x00 = vshr.u8 q8, q8, #8
0xd0,0xff,0x70,0x00 = vshr.u16 q8, q8, #16
0xe0,0xff,0x70,0x00 = vshr.u32 q8, q8, #32
0xc0,0xff,0xf0,0x00 = vshr.u64 q8, q8, #64
0xc8,0xef,0x30,0x00 = vshr.s8 d16, d16, #8
0xd0,0xef,0x30,0x00 = vshr.s16 d16, d16, #16
0xe0,0xef,0x30,0x00 = vshr.s32 d16, d16, #32
0xc0,0xef,0xb0,0x00 = vshr.s64 d16, d16, #64
0xc8,0xef,0x70,0x00 = vshr.s8 q8, q8, #8
0xd0,0xef,0x70,0x00 = vshr.s16 q8, q8, #16
0xe0,0xef,0x70,0x00 = vshr.s32 q8, q8, #32
0xc0,0xef,0xf0,0x00 = vshr.s64 q8, q8, #64
0xcf,0xef,0x30,0x0a = vshll.s8 q8, d16, #7
0xdf,0xef,0x30,0x0a = vshll.s16 q8, d16, #15
0xff,0xef,0x30,0x0a = vshll.s32 q8, d16, #31
0xcf,0xff,0x30,0x0a = vshll.u8 q8, d16, #7
0xdf,0xff,0x30,0x0a = vshll.u16 q8, d16, #15
0xff,0xff,0x30,0x0a = vshll.u32 q8, d16, #31
0xf2,0xff,0x20,0x03 = vshll.i8 q8, d16, #8
0xf6,0xff,0x20,0x03 = vshll.i16 q8, d16, #16
0xfa,0xff,0x20,0x03 = vshll.i32 q8, d16, #32
0xc8,0xef,0x30,0x08 = vshrn.i16 d16, q8, #8
0xd0,0xef,0x30,0x08 = vshrn.i32 d16, q8, #16
0xe0,0xef,0x30,0x08 = vshrn.i64 d16, q8, #32
0x40,0xef,0xa1,0x05 = vrshl.s8 d16, d17, d16
0x50,0xef,0xa1,0x05 = vrshl.s16 d16, d17, d16
0x60,0xef,0xa1,0x05 = vrshl.s32 d16, d17, d16
0x70,0xef,0xa1,0x05 = vrshl.s64 d16, d17, d16
0x40,0xff,0xa1,0x05 = vrshl.u8 d16, d17, d16
0x50,0xff,0xa1,0x05 = vrshl.u16 d16, d17, d16
0x60,0xff,0xa1,0x05 = vrshl.u32 d16, d17, d16
0x70,0xff,0xa1,0x05 = vrshl.u64 d16, d17, d16
0x40,0xef,0xe2,0x05 = vrshl.s8 q8, q9, q8
0x50,0xef,0xe2,0x05 = vrshl.s16 q8, q9, q8
0x60,0xef,0xe2,0x05 = vrshl.s32 q8, q9, q8
0x70,0xef,0xe2,0x05 = vrshl.s64 q8, q9, q8
0x40,0xff,0xe2,0x05 = vrshl.u8 q8, q9, q8
0x50,0xff,0xe2,0x05 = vrshl.u16 q8, q9, q8
0x60,0xff,0xe2,0x05 = vrshl.u32 q8, q9, q8
0x70,0xff,0xe2,0x05 = vrshl.u64 q8, q9, q8
0xc8,0xef,0x30,0x02 = vrshr.s8 d16, d16, #8
0xd0,0xef,0x30,0x02 = vrshr.s16 d16, d16, #16
0xe0,0xef,0x30,0x02 = vrshr.s32 d16, d16, #32
0xc0,0xef,0xb0,0x02 = vrshr.s64 d16, d16, #64
0xc8,0xff,0x30,0x02 = vrshr.u8 d16, d16, #8
0xd0,0xff,0x30,0x02 = vrshr.u16 d16, d16, #16
0xe0,0xff,0x30,0x02 = vrshr.u32 d16, d16, #32
0xc0,0xff,0xb0,0x02 = vrshr.u64 d16, d16, #64
0xc8,0xef,0x70,0x02 = vrshr.s8 q8, q8, #8
0xd0,0xef,0x70,0x02 = vrshr.s16 q8, q8, #16
0xe0,0xef,0x70,0x02 = vrshr.s32 q8, q8, #32
0xc0,0xef,0xf0,0x02 = vrshr.s64 q8, q8, #64
0xc8,0xff,0x70,0x02 = vrshr.u8 q8, q8, #8
0xd0,0xff,0x70,0x02 = vrshr.u16 q8, q8, #16
0xe0,0xff,0x70,0x02 = vrshr.u32 q8, q8, #32
0xc0,0xff,0xf0,0x02 = vrshr.u64 q8, q8, #64
0xc8,0xef,0x70,0x08 = vrshrn.i16 d16, q8, #8
0xd0,0xef,0x70,0x08 = vrshrn.i32 d16, q8, #16
0xe0,0xef,0x70,0x08 = vrshrn.i64 d16, q8, #32
