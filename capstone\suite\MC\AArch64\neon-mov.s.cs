# CS_ARCH_ARM64, 0, None
0x20,0x04,0x00,0x0f = movi v0.2s, #0x1
0x01,0x04,0x00,0x0f = movi v1.2s, #0x0
0x2f,0x24,0x00,0x0f = movi v15.2s, #0x1, lsl #8
0x30,0x44,0x00,0x0f = movi v16.2s, #0x1, lsl #16
0x3f,0x64,0x00,0x0f = movi v31.2s, #0x1, lsl #24
0x20,0x04,0x00,0x4f = movi v0.4s, #0x1
0x20,0x24,0x00,0x4f = movi v0.4s, #0x1, lsl #8
0x20,0x44,0x00,0x4f = movi v0.4s, #0x1, lsl #16
0x20,0x64,0x00,0x4f = movi v0.4s, #0x1, lsl #24
0x20,0x84,0x00,0x0f = movi v0.4h, #0x1
0x20,0xa4,0x00,0x0f = movi v0.4h, #0x1, lsl #8
0x20,0x84,0x00,0x4f = movi v0.8h, #0x1
0x20,0xa4,0x00,0x4f = movi v0.8h, #0x1, lsl #8
0x20,0x04,0x00,0x2f = mvni v0.2s, #0x1
0x01,0x04,0x00,0x2f = mvni v1.2s, #0x0
0x20,0x24,0x00,0x2f = mvni v0.2s, #0x1, lsl #8
0x20,0x44,0x00,0x2f = mvni v0.2s, #0x1, lsl #16
0x20,0x64,0x00,0x2f = mvni v0.2s, #0x1, lsl #24
0x20,0x04,0x00,0x6f = mvni v0.4s, #0x1
0x2f,0x24,0x00,0x6f = mvni v15.4s, #0x1, lsl #8
0x30,0x44,0x00,0x6f = mvni v16.4s, #0x1, lsl #16
0x3f,0x64,0x00,0x6f = mvni v31.4s, #0x1, lsl #24
0x20,0x84,0x00,0x2f = mvni v0.4h, #0x1
0x20,0xa4,0x00,0x2f = mvni v0.4h, #0x1, lsl #8
0x20,0x84,0x00,0x6f = mvni v0.8h, #0x1
0x20,0xa4,0x00,0x6f = mvni v0.8h, #0x1, lsl #8
0x20,0x14,0x00,0x2f = bic v0.2s, #0x1
0x01,0x14,0x00,0x2f = bic v1.2s, #0x0
0x20,0x34,0x00,0x2f = bic v0.2s, #0x1, lsl #8
0x20,0x54,0x00,0x2f = bic v0.2s, #0x1, lsl #16
0x20,0x74,0x00,0x2f = bic v0.2s, #0x1, lsl #24
0x20,0x14,0x00,0x6f = bic v0.4s, #0x1
0x20,0x34,0x00,0x6f = bic v0.4s, #0x1, lsl #8
0x20,0x54,0x00,0x6f = bic v0.4s, #0x1, lsl #16
0x20,0x74,0x00,0x6f = bic v0.4s, #0x1, lsl #24
0x2f,0x94,0x00,0x2f = bic v15.4h, #0x1
0x30,0xb4,0x00,0x2f = bic v16.4h, #0x1, lsl #8
0x20,0x94,0x00,0x6f = bic v0.8h, #0x1
0x3f,0xb4,0x00,0x6f = bic v31.8h, #0x1, lsl #8
0x20,0x14,0x00,0x0f = orr v0.2s, #0x1
0x01,0x14,0x00,0x0f = orr v1.2s, #0x0
0x20,0x34,0x00,0x0f = orr v0.2s, #0x1, lsl #8
0x20,0x54,0x00,0x0f = orr v0.2s, #0x1, lsl #16
0x20,0x74,0x00,0x0f = orr v0.2s, #0x1, lsl #24
0x20,0x14,0x00,0x4f = orr v0.4s, #0x1
0x20,0x34,0x00,0x4f = orr v0.4s, #0x1, lsl #8
0x20,0x54,0x00,0x4f = orr v0.4s, #0x1, lsl #16
0x20,0x74,0x00,0x4f = orr v0.4s, #0x1, lsl #24
0x3f,0x94,0x00,0x0f = orr v31.4h, #0x1
0x2f,0xb4,0x00,0x0f = orr v15.4h, #0x1, lsl #8
0x20,0x94,0x00,0x4f = orr v0.8h, #0x1
0x30,0xb4,0x00,0x4f = orr v16.8h, #0x1, lsl #8
0x20,0xc4,0x00,0x0f = movi v0.2s, #0x1, msl #8
0x21,0xd4,0x00,0x0f = movi v1.2s, #0x1, msl #16
0x20,0xc4,0x00,0x4f = movi v0.4s, #0x1, msl #8
0x3f,0xd4,0x00,0x4f = movi v31.4s, #0x1, msl #16
0x21,0xc4,0x00,0x2f = mvni v1.2s, #0x1, msl #8
0x20,0xd4,0x00,0x2f = mvni v0.2s, #0x1, msl #16
0x3f,0xc4,0x00,0x6f = mvni v31.4s, #0x1, msl #8
0x20,0xd4,0x00,0x6f = mvni v0.4s, #0x1, msl #16
0x00,0xe4,0x00,0x0f = movi v0.8b, #0x0
0xff,0xe7,0x07,0x0f = movi v31.8b, #0xff
0xef,0xe5,0x00,0x4f = movi v15.16b, #0xf
0xff,0xe7,0x00,0x4f = movi v31.16b, #0x1f
0x40,0xe5,0x05,0x6f = movi v0.2d, #0xff00ff00ff00ff00
0x40,0xe5,0x05,0x2f = movi d0,  #0xff00ff00ff00ff00
0x01,0xf6,0x03,0x0f = fmov v1.2s, #1.00000000
0x0f,0xf6,0x03,0x4f = fmov v15.4s, #1.00000000
0x1f,0xf6,0x03,0x6f = fmov v31.2d, #1.00000000
0xe0,0x1f,0xbf,0x0e = orr v0.8b, v31.8b, v31.8b
0x0f,0x1e,0xb0,0x4e = orr v15.16b, v16.16b, v16.16b
0xe0,0x1f,0xbf,0x0e = orr v0.8b, v31.8b, v31.8b
0x0f,0x1e,0xb0,0x4e = orr v15.16b, v16.16b, v16.16b
