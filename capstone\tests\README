This directory contains some test code to show how to use Capstone API.

- test.c
  This code shows the most simple form of API where we only want to get basic
  information out of disassembled instruction, such as address, mnemonic and
  operand string.

- test_detail.c:
  This code shows how to access to architecture-neutral information in disassembled
  instructions, such as implicit registers read/written, or groups of instructions
  that this instruction belong to.

- test_skipdata.c:
  This code shows how to use SKIPDATA option to skip broken instructions (most likely
  some data mixed with instructions) and continue to decode at the next legitimate
  instructions.

- test_iter.c:
  This code shows how to use the API cs_disasm_iter() to decode one instruction at
  a time inside a loop.

- test_<arch>.c
  These code show how to access architecture-specific information for each
  architecture.

- test_winkernel.cpp
  This code shows how to use Capstone from a Windows driver.
