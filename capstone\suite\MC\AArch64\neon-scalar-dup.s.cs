# CS_ARCH_ARM64, 0, None
0x00,0x04,0x1f,0x5e = dup b0, v0.b[15]
0x01,0x04,0x0f,0x5e = dup b1, v0.b[7]
0x11,0x04,0x01,0x5e = dup b17, v0.b[0]
0xe5,0x07,0x1e,0x5e = dup h5, v31.h[7]
0x29,0x04,0x12,0x5e = dup h9, v1.h[4]
0x2b,0x06,0x02,0x5e = dup h11, v17.h[0]
0x42,0x04,0x1c,0x5e = dup s2, v2.s[3]
0xa4,0x06,0x04,0x5e = dup s4, v21.s[0]
0xbf,0x06,0x14,0x5e = dup s31, v21.s[2]
0xa3,0x04,0x08,0x5e = dup d3, v5.d[0]
0xa6,0x04,0x18,0x5e = dup d6, v5.d[1]
0x00,0x04,0x1f,0x5e = dup b0, v0.b[15]
0x01,0x04,0x0f,0x5e = dup b1, v0.b[7]
0x11,0x04,0x01,0x5e = dup b17, v0.b[0]
0xe5,0x07,0x1e,0x5e = dup h5, v31.h[7]
0x29,0x04,0x12,0x5e = dup h9, v1.h[4]
0x2b,0x06,0x02,0x5e = dup h11, v17.h[0]
0x42,0x04,0x1c,0x5e = dup s2, v2.s[3]
0xa4,0x06,0x04,0x5e = dup s4, v21.s[0]
0xbf,0x06,0x14,0x5e = dup s31, v21.s[2]
0xa3,0x04,0x08,0x5e = dup d3, v5.d[0]
0xa6,0x04,0x18,0x5e = dup d6, v5.d[1]
