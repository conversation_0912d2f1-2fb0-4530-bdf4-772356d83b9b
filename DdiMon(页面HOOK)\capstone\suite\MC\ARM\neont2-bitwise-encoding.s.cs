# CS_ARCH_ARM, CS_MODE_THUMB, None
0x41,0xef,0xb0,0x01 = vand d16, d17, d16
0x40,0xef,0xf2,0x01 = vand q8, q8, q9
0x41,0xff,0xb0,0x01 = veor d16, d17, d16
0x40,0xff,0xf2,0x01 = veor q8, q8, q9
0x61,0xef,0xb0,0x01 = vorr d16, d17, d16
0x60,0xef,0xf2,0x01 = vorr q8, q8, q9
0x51,0xef,0xb0,0x01 = vbic d16, d17, d16
0x50,0xef,0xf2,0x01 = vbic q8, q8, q9
0x71,0xef,0xb0,0x01 = vorn d16, d17, d16
0x70,0xef,0xf2,0x01 = vorn q8, q8, q9
0xf0,0xff,0xa0,0x05 = vmvn d16, d16
0xf0,0xff,0xe0,0x05 = vmvn q8, q8
0x51,0xff,0xb0,0x21 = vbsl d18, d17, d16
0x54,0xff,0xf2,0x01 = vbsl q8, q10, q9
