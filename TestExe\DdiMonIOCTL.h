// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// User-mode interface for DdiMon IOCTL operations.

#ifndef DDIMON_IOCTL_USER_H_
#define DDIMON_IOCTL_USER_H_

#include <windows.h>
#include <vector>

// Define NT_SUCCESS macro for user mode
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL Codes (same as kernel mode)
//

// Device type for DdiMon
#define DDIMON_DEVICE_TYPE 0x8000

// IOCTL codes for EPT memory operations (only read/write memory supported)
#define IOCTL_DDIMON_EPT_READ_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_EPT_WRITE_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)

// IOCTL codes for process hiding operations
#define IOCTL_DDIMON_HIDE_PROCESS \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x810, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_SHOW_PROCESS \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x811, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_LIST_HIDDEN_PROCESSES \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x812, METHOD_BUFFERED, FILE_ANY_ACCESS)

// Removed: Unused IOCTL codes for manual hook management
// - IOCTL_DDIMON_CHECK_EPT_HOOK
// - IOCTL_DDIMON_CREATE_EPT_HOOK
// - IOCTL_DDIMON_CREATE_EPT_HOOK_EX
// - IOCTL_DDIMON_REMOVE_EPT_HOOK
// - IOCTL_DDIMON_LIST_EPT_HOOKS
// - IOCTL_DDIMON_REMOVE_ALL_HOOKS
// - IOCTL_EPT_HOOK_CREATE (alias)
// - IOCTL_EPT_HOOK_REMOVE (alias)
// - IOCTL_EPT_HOOK_LIST (alias)

////////////////////////////////////////////////////////////////////////////////
//
// Data Structures (same as kernel mode)
//

// EPT memory read request
#pragma pack(push, 1)
typedef struct _EPT_READ_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to read from
    ULONG Size;                     // Number of bytes to read
} EPT_READ_MEMORY_REQUEST, *PEPT_READ_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory read response
typedef struct _EPT_READ_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesRead;                // Actual bytes read
    UCHAR Data[1];                  // Variable length data buffer
} EPT_READ_MEMORY_RESPONSE, *PEPT_READ_MEMORY_RESPONSE;

// EPT memory write request
#pragma pack(push, 1)
typedef struct _EPT_WRITE_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to write to
    ULONG Size;                     // Number of bytes to write
    UCHAR Data[1];                  // Variable length data buffer
} EPT_WRITE_MEMORY_REQUEST, *PEPT_WRITE_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory write response
typedef struct _EPT_WRITE_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesWritten;             // Actual bytes written
} EPT_WRITE_MEMORY_RESPONSE, *PEPT_WRITE_MEMORY_RESPONSE;

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding structures
//

// Process hide/show request
typedef struct _PROCESS_HIDE_REQUEST {
    ULONG ProcessId;                // Target process ID
    BOOLEAN Hide;                   // TRUE=hide, FALSE=show
} PROCESS_HIDE_REQUEST, *PPROCESS_HIDE_REQUEST;

// Process hide/show response
typedef struct _PROCESS_HIDE_RESPONSE {
    NTSTATUS Status;                // Operation status
    BOOLEAN PreviousState;          // Previous hiding state
} PROCESS_HIDE_RESPONSE, *PPROCESS_HIDE_RESPONSE;

// Hidden process information
typedef struct _HIDDEN_PROCESS_INFO {
    ULONG ProcessId;                // Process ID
    LARGE_INTEGER HideTime;         // Time when hidden
    WCHAR ProcessName[16];          // Process name (truncated)
} HIDDEN_PROCESS_INFO, *PHIDDEN_PROCESS_INFO;

// List hidden processes request
typedef struct _LIST_HIDDEN_PROCESSES_REQUEST {
    ULONG MaxCount;                 // Maximum number of processes to return
} LIST_HIDDEN_PROCESSES_REQUEST, *PLIST_HIDDEN_PROCESSES_REQUEST;

// List hidden processes response
typedef struct _LIST_HIDDEN_PROCESSES_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG ProcessCount;             // Actual number of processes returned
    HIDDEN_PROCESS_INFO Processes[1]; // Variable length array
} LIST_HIDDEN_PROCESSES_RESPONSE, *PLIST_HIDDEN_PROCESSES_RESPONSE;

// Removed: Unused EPT hook check structures
// - EPT_HOOK_CHECK_REQUEST
// - EPT_HOOK_CHECK_RESPONSE

// Removed: Unused EPT hook management structures
// - EPT_HOOK_CREATE_REQUEST
// - EPT_HOOK_CREATE_REQUEST_EX
// - EPT_HOOK_CREATE_RESPONSE
// - EPT_HOOK_REMOVE_REQUEST
// - EPT_HOOK_REMOVE_RESPONSE

// Removed: Unused EPT hook list structures
// - EPT_HOOK_INFO
// - EPT_HOOK_LIST_REQUEST
// - EPT_HOOK_LIST_RESPONSE

////////////////////////////////////////////////////////////////////////////////
//
// Helper Functions
//

class DdiMonIOCTL {
private:
    HANDLE m_hDevice;
    
public:
    DdiMonIOCTL() : m_hDevice(INVALID_HANDLE_VALUE) {}
    
    ~DdiMonIOCTL() {
        Close();
    }
    
    // Open connection to DdiMon driver
    bool Open() {
        if (m_hDevice != INVALID_HANDLE_VALUE) {
            return true; // Already open
        }
        
        m_hDevice = CreateFileW(L"\\\\.\\DdiMon",
                               GENERIC_READ | GENERIC_WRITE,
                               0,
                               NULL,
                               OPEN_EXISTING,
                               FILE_ATTRIBUTE_NORMAL,
                               NULL);
        
        return (m_hDevice != INVALID_HANDLE_VALUE);
    }
    
    // Close connection to DdiMon driver
    void Close() {
        if (m_hDevice != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hDevice);
            m_hDevice = INVALID_HANDLE_VALUE;
        }
    }
    
    // Check if connection is open
    bool IsOpen() const {
        return (m_hDevice != INVALID_HANDLE_VALUE);
    }
    
    // Read memory from EPT shadow page
    bool EptReadMemory(ULONG ProcessId, ULONG64 VirtualAddress, PVOID Buffer, ULONG Size, PULONG BytesRead) {
        if (!IsOpen()) return false;
        
        EPT_READ_MEMORY_REQUEST request = {0};
        request.ProcessId = ProcessId;
        request.VirtualAddress = VirtualAddress;
        request.Size = Size;
        
        ULONG responseSize = sizeof(EPT_READ_MEMORY_RESPONSE) + Size;
        PEPT_READ_MEMORY_RESPONSE response = (PEPT_READ_MEMORY_RESPONSE)malloc(responseSize);
        if (!response) return false;
        
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_EPT_READ_MEMORY,
                                       &request, sizeof(request),
                                       response, responseSize,
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response->Status)) {
            memcpy(Buffer, response->Data, response->BytesRead);
            if (BytesRead) *BytesRead = response->BytesRead;
            free(response);
            return true;
        }
        
        free(response);
        return false;
    }
    
    // Write memory to EPT shadow page
    bool EptWriteMemory(ULONG ProcessId, ULONG64 VirtualAddress, PVOID Buffer, ULONG Size, PULONG BytesWritten) {
        if (!IsOpen()) return false;
        
        ULONG requestSize = sizeof(EPT_WRITE_MEMORY_REQUEST) + Size;
        PEPT_WRITE_MEMORY_REQUEST request = (PEPT_WRITE_MEMORY_REQUEST)malloc(requestSize);
        if (!request) return false;
        
        request->ProcessId = ProcessId;
        request->VirtualAddress = VirtualAddress;
        request->Size = Size;
        memcpy(request->Data, Buffer, Size);
        
        EPT_WRITE_MEMORY_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_EPT_WRITE_MEMORY,
                                       request, requestSize,
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response.Status)) {
            if (BytesWritten) *BytesWritten = response.BytesWritten;
            free(request);
            return true;
        }
        
        free(request);
        return false;
    }
    
    // Removed: Unused EPT hook management functions
    // - CheckEptHook (used deleted structures)
    // - CreateEptHook (used deleted structures)

    // Process hiding functions
    bool HideProcess(ULONG ProcessId, bool* PreviousState = nullptr) {
        if (!IsOpen()) return false;

        PROCESS_HIDE_REQUEST request = {0};
        request.ProcessId = ProcessId;
        request.Hide = TRUE;

        PROCESS_HIDE_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_HIDE_PROCESS,
                                       &request, sizeof(request),
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);

        if (result && NT_SUCCESS(response.Status)) {
            if (PreviousState) *PreviousState = (response.PreviousState != FALSE);
            return true;
        }

        return false;
    }

    bool ShowProcess(ULONG ProcessId, bool* PreviousState = nullptr) {
        if (!IsOpen()) return false;

        PROCESS_HIDE_REQUEST request = {0};
        request.ProcessId = ProcessId;
        request.Hide = FALSE;

        PROCESS_HIDE_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_SHOW_PROCESS,
                                       &request, sizeof(request),
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);

        if (result && NT_SUCCESS(response.Status)) {
            if (PreviousState) *PreviousState = (response.PreviousState != FALSE);
            return true;
        }

        return false;
    }

    bool ListHiddenProcesses(std::vector<HIDDEN_PROCESS_INFO>& processes, ULONG maxCount = 100) {
        if (!IsOpen()) return false;

        LIST_HIDDEN_PROCESSES_REQUEST request = {0};
        request.MaxCount = maxCount;

        ULONG responseSize = sizeof(LIST_HIDDEN_PROCESSES_RESPONSE) + (maxCount * sizeof(HIDDEN_PROCESS_INFO));
        PLIST_HIDDEN_PROCESSES_RESPONSE response = (PLIST_HIDDEN_PROCESSES_RESPONSE)malloc(responseSize);
        if (!response) return false;

        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_LIST_HIDDEN_PROCESSES,
                                       &request, sizeof(request),
                                       response, responseSize,
                                       &bytesReturned,
                                       NULL);

        if (result && NT_SUCCESS(response->Status)) {
            processes.clear();
            for (ULONG i = 0; i < response->ProcessCount; i++) {
                processes.push_back(response->Processes[i]);
            }
            free(response);
            return true;
        }

        free(response);
        return false;
    }

    // 通用DeviceIoControl方法
    bool DeviceIoControl(DWORD IoControlCode,
                        PVOID InputBuffer, DWORD InputBufferSize,
                        PVOID OutputBuffer, DWORD OutputBufferSize,
                        PDWORD BytesReturned) {
        if (!IsOpen()) return false;

        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IoControlCode,
                                       InputBuffer, InputBufferSize,
                                       OutputBuffer, OutputBufferSize,
                                       &bytesReturned,
                                       NULL);

        if (BytesReturned) {
            *BytesReturned = bytesReturned;
        }

        return (result == TRUE);
    }
};

#endif  // DDIMON_IOCTL_USER_H_
