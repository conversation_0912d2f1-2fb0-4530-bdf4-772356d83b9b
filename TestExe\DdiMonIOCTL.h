// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// User-mode interface for DdiMon IOCTL operations.

#ifndef DDIMON_IOCTL_USER_H_
#define DDIMON_IOCTL_USER_H_

#include <windows.h>

// Define NT_SUCCESS macro for user mode
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL Codes (same as kernel mode)
//

// Device type for DdiMon
#define DDIMON_DEVICE_TYPE 0x8000

// IOCTL codes for EPT memory operations (only read/write memory supported)
#define IOCTL_DDIMON_EPT_READ_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_EPT_WRITE_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)

// Removed: Unused IOCTL codes for manual hook management
// - IOCTL_DDIMON_CHECK_EPT_HOOK
// - IOCTL_DDIMON_CREATE_EPT_HOOK
// - IOCTL_DDIMON_CREATE_EPT_HOOK_EX
// - IOCTL_DDIMON_REMOVE_EPT_HOOK
// - IOCTL_DDIMON_LIST_EPT_HOOKS
// - IOCTL_DDIMON_REMOVE_ALL_HOOKS
// - IOCTL_EPT_HOOK_CREATE (alias)
// - IOCTL_EPT_HOOK_REMOVE (alias)
// - IOCTL_EPT_HOOK_LIST (alias)

////////////////////////////////////////////////////////////////////////////////
//
// Data Structures (same as kernel mode)
//

// EPT memory read request
#pragma pack(push, 1)
typedef struct _EPT_READ_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to read from
    ULONG Size;                     // Number of bytes to read
} EPT_READ_MEMORY_REQUEST, *PEPT_READ_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory read response
typedef struct _EPT_READ_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesRead;                // Actual bytes read
    UCHAR Data[1];                  // Variable length data buffer
} EPT_READ_MEMORY_RESPONSE, *PEPT_READ_MEMORY_RESPONSE;

// EPT memory write request
#pragma pack(push, 1)
typedef struct _EPT_WRITE_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to write to
    ULONG Size;                     // Number of bytes to write
    UCHAR Data[1];                  // Variable length data buffer
} EPT_WRITE_MEMORY_REQUEST, *PEPT_WRITE_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory write response
typedef struct _EPT_WRITE_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesWritten;             // Actual bytes written
} EPT_WRITE_MEMORY_RESPONSE, *PEPT_WRITE_MEMORY_RESPONSE;

// Removed: Unused EPT hook check structures
// - EPT_HOOK_CHECK_REQUEST
// - EPT_HOOK_CHECK_RESPONSE

// Removed: Unused EPT hook management structures
// - EPT_HOOK_CREATE_REQUEST
// - EPT_HOOK_CREATE_REQUEST_EX
// - EPT_HOOK_CREATE_RESPONSE
// - EPT_HOOK_REMOVE_REQUEST
// - EPT_HOOK_REMOVE_RESPONSE

// Removed: Unused EPT hook list structures
// - EPT_HOOK_INFO
// - EPT_HOOK_LIST_REQUEST
// - EPT_HOOK_LIST_RESPONSE

////////////////////////////////////////////////////////////////////////////////
//
// Helper Functions
//

class DdiMonIOCTL {
private:
    HANDLE m_hDevice;
    
public:
    DdiMonIOCTL() : m_hDevice(INVALID_HANDLE_VALUE) {}
    
    ~DdiMonIOCTL() {
        Close();
    }
    
    // Open connection to DdiMon driver
    bool Open() {
        if (m_hDevice != INVALID_HANDLE_VALUE) {
            return true; // Already open
        }
        
        m_hDevice = CreateFileW(L"\\\\.\\DdiMon",
                               GENERIC_READ | GENERIC_WRITE,
                               0,
                               NULL,
                               OPEN_EXISTING,
                               FILE_ATTRIBUTE_NORMAL,
                               NULL);
        
        return (m_hDevice != INVALID_HANDLE_VALUE);
    }
    
    // Close connection to DdiMon driver
    void Close() {
        if (m_hDevice != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hDevice);
            m_hDevice = INVALID_HANDLE_VALUE;
        }
    }
    
    // Check if connection is open
    bool IsOpen() const {
        return (m_hDevice != INVALID_HANDLE_VALUE);
    }
    
    // Read memory from EPT shadow page
    bool EptReadMemory(ULONG ProcessId, ULONG64 VirtualAddress, PVOID Buffer, ULONG Size, PULONG BytesRead) {
        if (!IsOpen()) return false;
        
        EPT_READ_MEMORY_REQUEST request = {0};
        request.ProcessId = ProcessId;
        request.VirtualAddress = VirtualAddress;
        request.Size = Size;
        
        ULONG responseSize = sizeof(EPT_READ_MEMORY_RESPONSE) + Size;
        PEPT_READ_MEMORY_RESPONSE response = (PEPT_READ_MEMORY_RESPONSE)malloc(responseSize);
        if (!response) return false;
        
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_EPT_READ_MEMORY,
                                       &request, sizeof(request),
                                       response, responseSize,
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response->Status)) {
            memcpy(Buffer, response->Data, response->BytesRead);
            if (BytesRead) *BytesRead = response->BytesRead;
            free(response);
            return true;
        }
        
        free(response);
        return false;
    }
    
    // Write memory to EPT shadow page
    bool EptWriteMemory(ULONG ProcessId, ULONG64 VirtualAddress, PVOID Buffer, ULONG Size, PULONG BytesWritten) {
        if (!IsOpen()) return false;
        
        ULONG requestSize = sizeof(EPT_WRITE_MEMORY_REQUEST) + Size;
        PEPT_WRITE_MEMORY_REQUEST request = (PEPT_WRITE_MEMORY_REQUEST)malloc(requestSize);
        if (!request) return false;
        
        request->ProcessId = ProcessId;
        request->VirtualAddress = VirtualAddress;
        request->Size = Size;
        memcpy(request->Data, Buffer, Size);
        
        EPT_WRITE_MEMORY_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_EPT_WRITE_MEMORY,
                                       request, requestSize,
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response.Status)) {
            if (BytesWritten) *BytesWritten = response.BytesWritten;
            free(request);
            return true;
        }
        
        free(request);
        return false;
    }
    
    // Removed: Unused EPT hook management functions
    // - CheckEptHook (used deleted structures)
    // - CreateEptHook (used deleted structures)

    // 通用DeviceIoControl方法
    bool DeviceIoControl(DWORD IoControlCode,
                        PVOID InputBuffer, DWORD InputBufferSize,
                        PVOID OutputBuffer, DWORD OutputBufferSize,
                        PDWORD BytesReturned) {
        if (!IsOpen()) return false;

        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IoControlCode,
                                       InputBuffer, InputBufferSize,
                                       OutputBuffer, OutputBufferSize,
                                       &bytesReturned,
                                       NULL);

        if (BytesReturned) {
            *BytesReturned = bytesReturned;
        }

        return (result == TRUE);
    }
};

#endif  // DDIMON_IOCTL_USER_H_
