/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    1329U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    1322U,	// BUNDLE
    1339U,	// LIFETIME_START
    1309U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// ABS
    5768U,	// ADCri
    5768U,	// ADCrr
    9864U,	// ADCrsi
    13960U,	// ADCrsr
    0U,	// ADDSri
    0U,	// ADDSrr
    0U,	// ADDSrsi
    0U,	// ADDSrsr
    5829U,	// ADDri
    5829U,	// ADDrr
    9925U,	// ADDrsi
    14021U,	// ADDrsr
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    18806U,	// ADR
    1090671288U,	// AESD
    1090671296U,	// AESE
    1107448485U,	// AESIMC
    1107448495U,	// AESMC
    5882U,	// ANDri
    5882U,	// ANDrr
    9978U,	// ANDrsi
    14074U,	// ANDrsr
    268708U,	// ASRi
    268708U,	// ASRr
    0U,	// B
    0U,	// BCCZi64
    0U,	// BCCi64
    26256U,	// BFC
    30677U,	// BFI
    5781U,	// BICri
    5781U,	// BICrr
    9877U,	// BICrsi
    13973U,	// BICrsr
    414542U,	// BKPT
    414522U,	// BL
    414582U,	// BLX
    1073777581U,	// BLX_pred
    414582U,	// BLXi
    1073776678U,	// BL_pred
    0U,	// BMOVPCB_CALL
    0U,	// BMOVPCRX_CALL
    0U,	// BR_JTadd
    0U,	// BR_JTm
    0U,	// BR_JTr
    414578U,	// BX
    1073776615U,	// BXJ
    0U,	// BX_CALL
    564041U,	// BX_RET
    1073777481U,	// BX_pred
    1073776035U,	// Bcc
    2197858625U,	// CDP
    67809687U,	// CDP2
    2967U,	// CLREX
    19417U,	// CLZ
    18663U,	// CMNri
    18663U,	// CMNzrr
    26855U,	// CMNzrsi
    30951U,	// CMNzrsr
    18763U,	// CMPri
    18763U,	// CMPrr
    26955U,	// CMPrsi
    31051U,	// CMPrsr
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_STRUCT_BYVAL_I32
    414526U,	// CPS1p
    1157679610U,	// CPS2p
    83937786U,	// CPS3p
    33706710U,	// CRC32B
    33706718U,	// CRC32CB
    33706782U,	// CRC32CH
    33706851U,	// CRC32CW
    33706774U,	// CRC32H
    33706843U,	// CRC32W
    1073776474U,	// DBG
    54005U,	// DMB
    54010U,	// DSB
    6546U,	// EORri
    6546U,	// EORrr
    10642U,	// EORrsi
    14738U,	// EORrsr
    3322694386U,	// FCONSTD
    3322825458U,	// FCONSTS
    33573700U,	// FLDMXDB_UPD
    35597U,	// FLDMXIA
    33573645U,	// FLDMXIA_UPD
    1087998U,	// FMSTAT
    33573708U,	// FSTMXDB_UPD
    35605U,	// FSTMXIA
    33573653U,	// FSTMXIA_UPD
    1073777285U,	// HINT
    414537U,	// HLT
    58111U,	// ISB
    117766771U,	// ITasm
    0U,	// Int_eh_sjlj_dispatchsetup
    0U,	// Int_eh_sjlj_longjmp
    0U,	// Int_eh_sjlj_setjmp
    0U,	// Int_eh_sjlj_setjmp_nofp
    17743U,	// LDA
    17824U,	// LDAB
    19333U,	// LDAEX
    18024U,	// LDAEXB
    134235924U,	// LDAEXD
    18361U,	// LDAEXH
    18281U,	// LDAH
    152220460U,	// LDC2L_OFFSET
    1242739500U,	// LDC2L_OPTION
    2316481324U,	// LDC2L_POST
    185774892U,	// LDC2L_PRE
    152220030U,	// LDC2_OFFSET
    1242739070U,	// LDC2_OPTION
    2316480894U,	// LDC2_POST
    185774462U,	// LDC2_PRE
    3271587887U,	// LDCL_OFFSET
    3271587887U,	// LDCL_OPTION
    3271587887U,	// LDCL_POST
    3271587887U,	// LDCL_PRE
    3271587468U,	// LDC_OFFSET
    3271587468U,	// LDC_OPTION
    3271587468U,	// LDC_POST
    3271587468U,	// LDC_PRE
    34131U,	// LDMDA
    33572179U,	// LDMDA_UPD
    34258U,	// LDMDB
    33572306U,	// LDMDB_UPD
    34998U,	// LDMIA
    0U,	// LDMIA_RET
    33573046U,	// LDMIA_UPD
    34277U,	// LDMIB
    33572325U,	// LDMIB_UPD
    281152U,	// LDRBT_POST
    68160U,	// LDRBT_POST_IMM
    68160U,	// LDRBT_POST_REG
    67071U,	// LDRB_POST_IMM
    67071U,	// LDRB_POST_REG
    30207U,	// LDRB_PRE_IMM
    67071U,	// LDRB_PRE_REG
    26111U,	// LDRBi12
    30207U,	// LDRBrs
    67326U,	// LDRD
    42750U,	// LDRD_POST
    42750U,	// LDRD_PRE
    19345U,	// LDREX
    18038U,	// LDREXB
    134235938U,	// LDREXD
    18375U,	// LDREXH
    30612U,	// LDRH
    31326U,	// LDRHTi
    68190U,	// LDRHTr
    67476U,	// LDRH_POST
    67476U,	// LDRH_PRE
    0U,	// LDRLIT_ga_abs
    0U,	// LDRLIT_ga_pcrel
    0U,	// LDRLIT_ga_pcrel_ldr
    30225U,	// LDRSB
    31308U,	// LDRSBTi
    68172U,	// LDRSBTr
    67089U,	// LDRSB_POST
    67089U,	// LDRSB_PRE
    30622U,	// LDRSH
    31338U,	// LDRSHTi
    68202U,	// LDRSHTr
    67486U,	// LDRSH_POST
    67486U,	// LDRSH_PRE
    281226U,	// LDRT_POST
    68234U,	// LDRT_POST_IMM
    68234U,	// LDRT_POST_REG
    67963U,	// LDR_POST_IMM
    67963U,	// LDR_POST_REG
    31099U,	// LDR_PRE_IMM
    67963U,	// LDR_PRE_REG
    27003U,	// LDRcp
    27003U,	// LDRi12
    31099U,	// LDRrs
    0U,	// LEApcrel
    0U,	// LEApcrelJT
    268433U,	// LSLi
    268433U,	// LSLr
    268715U,	// LSRi
    268715U,	// LSRr
    2197858674U,	// MCR
    17478045U,	// MCR2
    2197883290U,	// MCRR
    17478051U,	// MCRR2
    9595U,	// MLA
    0U,	// MLAv5
    31197U,	// MLS
    0U,	// MOVCCi
    0U,	// MOVCCi16
    0U,	// MOVCCi32imm
    0U,	// MOVCCr
    0U,	// MOVCCsi
    0U,	// MOVCCsr
    1350387U,	// MOVPCLR
    0U,	// MOVPCRX
    27328U,	// MOVTi16
    0U,	// MOVTi16_ga_pcrel
    0U,	// MOV_ga_pcrel
    0U,	// MOV_ga_pcrel_ldr
    72435U,	// MOVi
    19208U,	// MOVi16
    0U,	// MOVi16_ga_pcrel
    0U,	// MOVi32imm
    72435U,	// MOVr
    72435U,	// MOVr_TC
    6899U,	// MOVsi
    10995U,	// MOVsr
    0U,	// MOVsra_flag
    0U,	// MOVsrl_flag
    201369245U,	// MRC
    74116U,	// MRC2
    2197882529U,	// MRRC
    17478026U,	// MRRC2
    35327U,	// MRS
    1073777151U,	// MRSsys
    218122672U,	// MSR
    218122672U,	// MSRi
    6305U,	// MUL
    0U,	// MULv5
    0U,	// MVNCCi
    71979U,	// MVNi
    71979U,	// MVNr
    6443U,	// MVNsi
    10539U,	// MVNsr
    6560U,	// ORRri
    6560U,	// ORRrr
    10656U,	// ORRrsi
    14752U,	// ORRrsr
    0U,	// PICADD
    0U,	// PICLDR
    0U,	// PICLDRB
    0U,	// PICLDRH
    0U,	// PICLDRSB
    0U,	// PICLDRSH
    0U,	// PICSTR
    0U,	// PICSTRB
    0U,	// PICSTRH
    31275U,	// PKHBT
    30238U,	// PKHTB
    78700U,	// PLDWi12
    82796U,	// PLDWrs
    78596U,	// PLDi12
    82692U,	// PLDrs
    78631U,	// PLIi12
    82727U,	// PLIrs
    26333U,	// QADD
    25764U,	// QADD16
    25867U,	// QADD8
    27586U,	// QASX
    26307U,	// QDADD
    26179U,	// QDSUB
    27445U,	// QSAX
    26192U,	// QSUB
    25726U,	// QSUB16
    25828U,	// QSUB8
    19057U,	// RBIT
    19167U,	// REV
    17608U,	// REV16
    18345U,	// REVSH
    414408U,	// RFEDA
    1462984U,	// RFEDA_UPD
    414439U,	// RFEDB
    1463015U,	// RFEDB_UPD
    414415U,	// RFEIA
    1462991U,	// RFEIA_UPD
    414446U,	// RFEIB
    1463022U,	// RFEIB_UPD
    268694U,	// RORi
    268694U,	// RORr
    0U,	// RRX
    334769U,	// RRXi
    0U,	// RSBSri
    0U,	// RSBSrsi
    0U,	// RSBSrsr
    5651U,	// RSBri
    5651U,	// RSBrr
    9747U,	// RSBrsi
    13843U,	// RSBrsr
    5798U,	// RSCri
    5798U,	// RSCrr
    9894U,	// RSCrsi
    13990U,	// RSCrsr
    25771U,	// SADD16
    25873U,	// SADD8
    27591U,	// SASX
    5764U,	// SBCri
    5764U,	// SBCrr
    9860U,	// SBCrsi
    13956U,	// SBCrsr
    31651U,	// SBFX
    27363U,	// SDIV
    26700U,	// SEL
    86793U,	// SETEND
    16928834U,	// SHA1C
    1107447884U,	// SHA1H
    16928866U,	// SHA1M
    16928876U,	// SHA1P
    16928769U,	// SHA1SU0
    1090670619U,	// SHA1SU1
    16928854U,	// SHA256H
    16928821U,	// SHA256H2
    1090670605U,	// SHA256SU0
    16928807U,	// SHA256SU1
    25747U,	// SHADD16
    25852U,	// SHADD8
    27573U,	// SHASX
    27432U,	// SHSAX
    25709U,	// SHSUB16
    25813U,	// SHSUB8
    1073776281U,	// SMC
    30129U,	// SMLABB
    31268U,	// SMLABT
    30386U,	// SMLAD
    31577U,	// SMLADX
    92178U,	// SMLAL
    30136U,	// SMLALBB
    31281U,	// SMLALBT
    30439U,	// SMLALD
    31591U,	// SMLALDX
    30244U,	// SMLALTB
    31398U,	// SMLALTT
    0U,	// SMLALv5
    30231U,	// SMLATB
    31391U,	// SMLATT
    30298U,	// SMLAWB
    31429U,	// SMLAWT
    30472U,	// SMLSD
    31607U,	// SMLSDX
    30450U,	// SMLSLD
    31599U,	// SMLSLDX
    30073U,	// SMMLA
    31083U,	// SMMLAR
    31195U,	// SMMLS
    31144U,	// SMMLSR
    26783U,	// SMMUL
    27018U,	// SMMULR
    26296U,	// SMUAD
    27488U,	// SMUADX
    26048U,	// SMULBB
    27193U,	// SMULBT
    10358U,	// SMULL
    0U,	// SMULLv5
    26156U,	// SMULTB
    27310U,	// SMULTT
    26209U,	// SMULWB
    27340U,	// SMULWT
    26382U,	// SMUSD
    27518U,	// SMUSDX
    414646U,	// SRSDA
    414598U,	// SRSDA_UPD
    414668U,	// SRSDB
    414622U,	// SRSDB_UPD
    414657U,	// SRSIA
    414610U,	// SRSIA_UPD
    414679U,	// SRSIB
    414634U,	// SRSIB_UPD
    31258U,	// SSAT
    25785U,	// SSAT16
    27450U,	// SSAX
    25733U,	// SSUB16
    25834U,	// SSUB8
    152220467U,	// STC2L_OFFSET
    1242739507U,	// STC2L_OPTION
    2316481331U,	// STC2L_POST
    185774899U,	// STC2L_PRE
    152220049U,	// STC2_OFFSET
    1242739089U,	// STC2_OPTION
    2316480913U,	// STC2_POST
    185774481U,	// STC2_PRE
    3271587892U,	// STCL_OFFSET
    3271587892U,	// STCL_OPTION
    3271587892U,	// STCL_POST
    3271587892U,	// STCL_PRE
    3271587498U,	// STC_OFFSET
    3271587498U,	// STC_OPTION
    3271587498U,	// STC_POST
    3271587498U,	// STC_PRE
    18587U,	// STL
    17905U,	// STLB
    27531U,	// STLEX
    26223U,	// STLEXB
    26395U,	// STLEXD
    26560U,	// STLEXH
    18302U,	// STLH
    34137U,	// STMDA
    33572185U,	// STMDA_UPD
    34265U,	// STMDB
    33572313U,	// STMDB_UPD
    35002U,	// STMIA
    33573050U,	// STMIA_UPD
    34283U,	// STMIB
    33572331U,	// STMIB_UPD
    281158U,	// STRBT_POST
    33622598U,	// STRBT_POST_IMM
    33622598U,	// STRBT_POST_REG
    33621508U,	// STRB_POST_IMM
    33621508U,	// STRB_POST_REG
    33584644U,	// STRB_PRE_IMM
    33621508U,	// STRB_PRE_REG
    26116U,	// STRBi12
    0U,	// STRBi_preidx
    0U,	// STRBr_preidx
    30212U,	// STRBrs
    67331U,	// STRD
    33597187U,	// STRD_POST
    33597187U,	// STRD_PRE
    27549U,	// STREX
    26237U,	// STREXB
    26409U,	// STREXD
    26574U,	// STREXH
    30617U,	// STRH
    33585764U,	// STRHTi
    33622628U,	// STRHTr
    33621913U,	// STRH_POST
    33621913U,	// STRH_PRE
    0U,	// STRH_preidx
    281237U,	// STRT_POST
    33622677U,	// STRT_POST_IMM
    33622677U,	// STRT_POST_REG
    33622460U,	// STR_POST_IMM
    33622460U,	// STR_POST_REG
    33585596U,	// STR_PRE_IMM
    33622460U,	// STR_PRE_REG
    27068U,	// STRi12
    0U,	// STRi_preidx
    0U,	// STRr_preidx
    31164U,	// STRrs
    0U,	// SUBS_PC_LR
    0U,	// SUBSri
    0U,	// SUBSrr
    0U,	// SUBSrsi
    0U,	// SUBSrsr
    5701U,	// SUBri
    5701U,	// SUBrr
    9797U,	// SUBrsi
    13893U,	// SUBrsr
    1073776302U,	// SVC
    26969U,	// SWP
    26106U,	// SWPB
    30117U,	// SXTAB
    29775U,	// SXTAB16
    30574U,	// SXTAH
    26169U,	// SXTB
    25695U,	// SXTB16
    26543U,	// SXTH
    0U,	// TAILJMPd
    0U,	// TAILJMPr
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    18791U,	// TEQri
    18791U,	// TEQrr
    26983U,	// TEQrsi
    31079U,	// TEQrsr
    0U,	// TPsoft
    2364U,	// TRAP
    2364U,	// TRAPNaCl
    19099U,	// TSTri
    19099U,	// TSTrr
    27291U,	// TSTrsi
    31387U,	// TSTrsr
    25778U,	// UADD16
    25879U,	// UADD8
    27596U,	// UASX
    31656U,	// UBFX
    414481U,	// UDF
    27368U,	// UDIV
    25755U,	// UHADD16
    25859U,	// UHADD8
    27579U,	// UHASX
    27438U,	// UHSAX
    25717U,	// UHSUB16
    25820U,	// UHSUB8
    30711U,	// UMAAL
    92184U,	// UMLAL
    0U,	// UMLALv5
    10364U,	// UMULL
    0U,	// UMULLv5
    25763U,	// UQADD16
    25866U,	// UQADD8
    27585U,	// UQASX
    27444U,	// UQSAX
    25725U,	// UQSUB16
    25827U,	// UQSUB8
    25846U,	// USAD8
    29902U,	// USADA8
    31263U,	// USAT
    25792U,	// USAT16
    27455U,	// USAX
    25740U,	// USUB16
    25840U,	// USUB8
    30123U,	// UXTAB
    29783U,	// UXTAB16
    30580U,	// UXTAH
    26174U,	// UXTB
    25702U,	// UXTB16
    26548U,	// UXTH
    18380797U,	// VABALsv2i64
    18511869U,	// VABALsv4i32
    18642941U,	// VABALsv8i16
    18774013U,	// VABALuv2i64
    18905085U,	// VABALuv4i32
    19036157U,	// VABALuv8i16
    18642250U,	// VABAsv16i8
    18380106U,	// VABAsv2i32
    18511178U,	// VABAsv4i16
    18380106U,	// VABAsv4i32
    18511178U,	// VABAsv8i16
    18642250U,	// VABAsv8i8
    19035466U,	// VABAuv16i8
    18773322U,	// VABAuv2i32
    18904394U,	// VABAuv4i16
    18773322U,	// VABAuv4i32
    18904394U,	// VABAuv8i16
    19035466U,	// VABAuv8i8
    35153977U,	// VABDLsv2i64
    35285049U,	// VABDLsv4i32
    35416121U,	// VABDLsv8i16
    35547193U,	// VABDLuv2i64
    35678265U,	// VABDLuv4i32
    35809337U,	// VABDLuv8i16
    2249090750U,	// VABDfd
    2249090750U,	// VABDfq
    35415742U,	// VABDsv16i8
    35153598U,	// VABDsv2i32
    35284670U,	// VABDsv4i16
    35153598U,	// VABDsv4i32
    35284670U,	// VABDsv8i16
    35415742U,	// VABDsv8i8
    35808958U,	// VABDuv16i8
    35546814U,	// VABDuv2i32
    35677886U,	// VABDuv4i16
    35546814U,	// VABDuv4i32
    35677886U,	// VABDuv8i16
    35808958U,	// VABDuv8i8
    2248952268U,	// VABSD
    2249083340U,	// VABSS
    2249083340U,	// VABSfd
    2249083340U,	// VABSfq
    1109150156U,	// VABSv16i8
    1108888012U,	// VABSv2i32
    1109019084U,	// VABSv4i16
    1108888012U,	// VABSv4i32
    1109019084U,	// VABSv8i16
    1109150156U,	// VABSv8i8
    2249090864U,	// VACGEd
    2249090864U,	// VACGEq
    2249091667U,	// VACGTd
    2249091667U,	// VACGTq
    2248959714U,	// VADDD
    35940565U,	// VADDHNv2i32
    36071637U,	// VADDHNv4i16
    36202709U,	// VADDHNv8i8
    35153990U,	// VADDLsv2i64
    35285062U,	// VADDLsv4i32
    35416134U,	// VADDLsv8i16
    35547206U,	// VADDLuv2i64
    35678278U,	// VADDLuv4i32
    35809350U,	// VADDLuv8i16
    2249090786U,	// VADDS
    35154685U,	// VADDWsv2i64
    35285757U,	// VADDWsv4i32
    35416829U,	// VADDWsv8i16
    35547901U,	// VADDWuv2i64
    35678973U,	// VADDWuv4i32
    35810045U,	// VADDWuv8i16
    2249090786U,	// VADDfd
    2249090786U,	// VADDfq
    36333282U,	// VADDv16i8
    35940066U,	// VADDv1i64
    36071138U,	// VADDv2i32
    35940066U,	// VADDv2i64
    36202210U,	// VADDv4i16
    36071138U,	// VADDv4i32
    36202210U,	// VADDv8i16
    36333282U,	// VADDv8i8
    26361U,	// VANDd
    26361U,	// VANDq
    26260U,	// VBICd
    237397652U,	// VBICiv2i32
    237528724U,	// VBICiv4i16
    237397652U,	// VBICiv4i32
    237528724U,	// VBICiv8i16
    26260U,	// VBICq
    30549U,	// VBIFd
    30549U,	// VBIFq
    31350U,	// VBITd
    31350U,	// VBITq
    30856U,	// VBSLd
    30856U,	// VBSLq
    2249091426U,	// VCEQfd
    2249091426U,	// VCEQfq
    36333922U,	// VCEQv16i8
    36071778U,	// VCEQv2i32
    36202850U,	// VCEQv4i16
    36071778U,	// VCEQv4i32
    36202850U,	// VCEQv8i16
    36333922U,	// VCEQv8i8
    2183809378U,	// VCEQzv16i8
    2249083234U,	// VCEQzv2f32
    2183547234U,	// VCEQzv2i32
    2249083234U,	// VCEQzv4f32
    2183678306U,	// VCEQzv4i16
    2183547234U,	// VCEQzv4i32
    2183678306U,	// VCEQzv8i16
    2183809378U,	// VCEQzv8i8
    2249090870U,	// VCGEfd
    2249090870U,	// VCGEfq
    35415862U,	// VCGEsv16i8
    35153718U,	// VCGEsv2i32
    35284790U,	// VCGEsv4i16
    35153718U,	// VCGEsv4i32
    35284790U,	// VCGEsv8i16
    35415862U,	// VCGEsv8i8
    35809078U,	// VCGEuv16i8
    35546934U,	// VCGEuv2i32
    35678006U,	// VCGEuv4i16
    35546934U,	// VCGEuv4i32
    35678006U,	// VCGEuv8i16
    35809078U,	// VCGEuv8i8
    2182891318U,	// VCGEzv16i8
    2249082678U,	// VCGEzv2f32
    2182629174U,	// VCGEzv2i32
    2249082678U,	// VCGEzv4f32
    2182760246U,	// VCGEzv4i16
    2182629174U,	// VCGEzv4i32
    2182760246U,	// VCGEzv8i16
    2182891318U,	// VCGEzv8i8
    2249091673U,	// VCGTfd
    2249091673U,	// VCGTfq
    35416665U,	// VCGTsv16i8
    35154521U,	// VCGTsv2i32
    35285593U,	// VCGTsv4i16
    35154521U,	// VCGTsv4i32
    35285593U,	// VCGTsv8i16
    35416665U,	// VCGTsv8i8
    35809881U,	// VCGTuv16i8
    35547737U,	// VCGTuv2i32
    35678809U,	// VCGTuv4i16
    35547737U,	// VCGTuv4i32
    35678809U,	// VCGTuv8i16
    35809881U,	// VCGTuv8i8
    2182892121U,	// VCGTzv16i8
    2249083481U,	// VCGTzv2f32
    2182629977U,	// VCGTzv2i32
    2249083481U,	// VCGTzv4f32
    2182761049U,	// VCGTzv4i16
    2182629977U,	// VCGTzv4i32
    2182761049U,	// VCGTzv8i16
    2182892121U,	// VCGTzv8i8
    2182891323U,	// VCLEzv16i8
    2249082683U,	// VCLEzv2f32
    2182629179U,	// VCLEzv2i32
    2249082683U,	// VCLEzv4f32
    2182760251U,	// VCLEzv4i16
    2182629179U,	// VCLEzv4i32
    2182760251U,	// VCLEzv8i16
    2182891323U,	// VCLEzv8i8
    1109150166U,	// VCLSv16i8
    1108888022U,	// VCLSv2i32
    1109019094U,	// VCLSv4i16
    1108888022U,	// VCLSv4i32
    1109019094U,	// VCLSv8i16
    1109150166U,	// VCLSv8i8
    2182892155U,	// VCLTzv16i8
    2249083515U,	// VCLTzv2f32
    2182630011U,	// VCLTzv2i32
    2249083515U,	// VCLTzv4f32
    2182761083U,	// VCLTzv4i16
    2182630011U,	// VCLTzv4i32
    2182761083U,	// VCLTzv8i16
    2182892155U,	// VCLTzv8i8
    1110068184U,	// VCLZv16i8
    1109806040U,	// VCLZv2i32
    1109937112U,	// VCLZv4i16
    1109806040U,	// VCLZv4i32
    1109937112U,	// VCLZv8i16
    1110068184U,	// VCLZv8i8
    2248952138U,	// VCMPD
    2248951623U,	// VCMPED
    2249082695U,	// VCMPES
    252479303U,	// VCMPEZD
    252610375U,	// VCMPEZS
    2249083210U,	// VCMPS
    252479818U,	// VCMPZD
    252610890U,	// VCMPZS
    2902656U,	// VCNTd
    2902656U,	// VCNTq
    1107447926U,	// VCVTANSD
    1107447926U,	// VCVTANSQ
    1107447986U,	// VCVTANUD
    1107447986U,	// VCVTANUQ
    1107448234U,	// VCVTASD
    1107447926U,	// VCVTASS
    1107448294U,	// VCVTAUD
    1107447986U,	// VCVTAUS
    3032627U,	// VCVTBDH
    3163699U,	// VCVTBHD
    3294771U,	// VCVTBHS
    3425843U,	// VCVTBSH
    3558075U,	// VCVTDS
    1107447941U,	// VCVTMNSD
    1107447941U,	// VCVTMNSQ
    1107448001U,	// VCVTMNUD
    1107448001U,	// VCVTMNUQ
    1107448249U,	// VCVTMSD
    1107447941U,	// VCVTMSS
    1107448309U,	// VCVTMUD
    1107448001U,	// VCVTMUS
    1107447956U,	// VCVTNNSD
    1107447956U,	// VCVTNNSQ
    1107448016U,	// VCVTNNUD
    1107448016U,	// VCVTNNUQ
    1107448264U,	// VCVTNSD
    1107447956U,	// VCVTNSS
    1107448324U,	// VCVTNUD
    1107448016U,	// VCVTNUS
    1107447971U,	// VCVTPNSD
    1107447971U,	// VCVTPNSQ
    1107448031U,	// VCVTPNUD
    1107448031U,	// VCVTPNUQ
    1107448279U,	// VCVTPSD
    1107447971U,	// VCVTPSS
    1107448339U,	// VCVTPUD
    1107448031U,	// VCVTPUS
    3689147U,	// VCVTSD
    3033781U,	// VCVTTDH
    3164853U,	// VCVTTHD
    3295925U,	// VCVTTHS
    3426997U,	// VCVTTSH
    3427003U,	// VCVTf2h
    272255675U,	// VCVTf2sd
    272255675U,	// VCVTf2sq
    272386747U,	// VCVTf2ud
    272386747U,	// VCVTf2uq
    3325717179U,	// VCVTf2xsd
    3325717179U,	// VCVTf2xsq
    3325848251U,	// VCVTf2xud
    3325848251U,	// VCVTf2xuq
    3295931U,	// VCVTh2f
    272517819U,	// VCVTs2fd
    272517819U,	// VCVTs2fq
    272648891U,	// VCVTu2fd
    272648891U,	// VCVTu2fq
    3325979323U,	// VCVTxs2fd
    3325979323U,	// VCVTxs2fq
    3326110395U,	// VCVTxu2fd
    3326110395U,	// VCVTxu2fq
    2248960749U,	// VDIVD
    2249091821U,	// VDIVS
    4344147U,	// VDUP16d
    4344147U,	// VDUP16q
    4475219U,	// VDUP32d
    4475219U,	// VDUP32q
    2902355U,	// VDUP8d
    2902355U,	// VDUP8q
    4352339U,	// VDUPLN16d
    4352339U,	// VDUPLN16q
    4483411U,	// VDUPLN32d
    4483411U,	// VDUPLN32q
    2910547U,	// VDUPLN8d
    2910547U,	// VDUPLN8q
    27025U,	// VEORd
    27025U,	// VEORq
    4356819U,	// VEXTd16
    4487891U,	// VEXTd32
    2915027U,	// VEXTd8
    4356819U,	// VEXTq16
    4487891U,	// VEXTq32
    4618963U,	// VEXTq64
    2915027U,	// VEXTq8
    3322705290U,	// VFMAD
    3322836362U,	// VFMAS
    3322836362U,	// VFMAfd
    3322836362U,	// VFMAfq
    3322706412U,	// VFMSD
    3322837484U,	// VFMSS
    3322837484U,	// VFMSfd
    3322837484U,	// VFMSfq
    3322705295U,	// VFNMAD
    3322836367U,	// VFNMAS
    3322706417U,	// VFNMSD
    3322837489U,	// VFNMSS
    4483826U,	// VGETLNi32
    35285746U,	// VGETLNs16
    35416818U,	// VGETLNs8
    35678962U,	// VGETLNu16
    35810034U,	// VGETLNu8
    35415760U,	// VHADDsv16i8
    35153616U,	// VHADDsv2i32
    35284688U,	// VHADDsv4i16
    35153616U,	// VHADDsv4i32
    35284688U,	// VHADDsv8i16
    35415760U,	// VHADDsv8i8
    35808976U,	// VHADDuv16i8
    35546832U,	// VHADDuv2i32
    35677904U,	// VHADDuv4i16
    35546832U,	// VHADDuv4i32
    35677904U,	// VHADDuv8i16
    35808976U,	// VHADDuv8i8
    35415625U,	// VHSUBsv16i8
    35153481U,	// VHSUBsv2i32
    35284553U,	// VHSUBsv4i16
    35153481U,	// VHSUBsv4i32
    35284553U,	// VHSUBsv8i16
    35415625U,	// VHSUBsv8i8
    35808841U,	// VHSUBuv16i8
    35546697U,	// VHSUBuv2i32
    35677769U,	// VHSUBuv4i16
    35546697U,	// VHSUBuv4i32
    35677769U,	// VHSUBuv8i16
    35808841U,	// VHSUBuv8i8
    1363305442U,	// VLD1DUPd16
    2437051362U,	// VLD1DUPd16wb_fixed
    2437088226U,	// VLD1DUPd16wb_register
    1363436514U,	// VLD1DUPd32
    2437182434U,	// VLD1DUPd32wb_fixed
    2437219298U,	// VLD1DUPd32wb_register
    1361863650U,	// VLD1DUPd8
    2435609570U,	// VLD1DUPd8wb_fixed
    2435646434U,	// VLD1DUPd8wb_register
    1380082658U,	// VLD1DUPq16
    2453828578U,	// VLD1DUPq16wb_fixed
    2453865442U,	// VLD1DUPq16wb_register
    1380213730U,	// VLD1DUPq32
    2453959650U,	// VLD1DUPq32wb_fixed
    2453996514U,	// VLD1DUPq32wb_register
    1378640866U,	// VLD1DUPq8
    2452386786U,	// VLD1DUPq8wb_fixed
    2452423650U,	// VLD1DUPq8wb_register
    3226010594U,	// VLD1LNd16
    3226039266U,	// VLD1LNd16_UPD
    3226141666U,	// VLD1LNd32
    3226170338U,	// VLD1LNd32_UPD
    3226272738U,	// VLD1LNd8
    3226301410U,	// VLD1LNd8_UPD
    4355042U,	// VLD1LNdAsm_16
    4486114U,	// VLD1LNdAsm_32
    2913250U,	// VLD1LNdAsm_8
    4355042U,	// VLD1LNdWB_fixed_Asm_16
    4486114U,	// VLD1LNdWB_fixed_Asm_32
    2913250U,	// VLD1LNdWB_fixed_Asm_8
    4391906U,	// VLD1LNdWB_register_Asm_16
    4522978U,	// VLD1LNdWB_register_Asm_32
    2950114U,	// VLD1LNdWB_register_Asm_8
    0U,	// VLD1LNq16Pseudo
    0U,	// VLD1LNq16Pseudo_UPD
    0U,	// VLD1LNq32Pseudo
    0U,	// VLD1LNq32Pseudo_UPD
    0U,	// VLD1LNq8Pseudo
    0U,	// VLD1LNq8Pseudo_UPD
    1396859874U,	// VLD1d16
    1413637090U,	// VLD1d16Q
    2487383010U,	// VLD1d16Qwb_fixed
    2487419874U,	// VLD1d16Qwb_register
    1430414306U,	// VLD1d16T
    2504160226U,	// VLD1d16Twb_fixed
    2504197090U,	// VLD1d16Twb_register
    2470605794U,	// VLD1d16wb_fixed
    2470642658U,	// VLD1d16wb_register
    1396990946U,	// VLD1d32
    1413768162U,	// VLD1d32Q
    2487514082U,	// VLD1d32Qwb_fixed
    2487550946U,	// VLD1d32Qwb_register
    1430545378U,	// VLD1d32T
    2504291298U,	// VLD1d32Twb_fixed
    2504328162U,	// VLD1d32Twb_register
    2470736866U,	// VLD1d32wb_fixed
    2470773730U,	// VLD1d32wb_register
    1397122018U,	// VLD1d64
    1413899234U,	// VLD1d64Q
    0U,	// VLD1d64QPseudo
    0U,	// VLD1d64QPseudoWB_fixed
    0U,	// VLD1d64QPseudoWB_register
    2487645154U,	// VLD1d64Qwb_fixed
    2487682018U,	// VLD1d64Qwb_register
    1430676450U,	// VLD1d64T
    0U,	// VLD1d64TPseudo
    0U,	// VLD1d64TPseudoWB_fixed
    0U,	// VLD1d64TPseudoWB_register
    2504422370U,	// VLD1d64Twb_fixed
    2504459234U,	// VLD1d64Twb_register
    2470867938U,	// VLD1d64wb_fixed
    2470904802U,	// VLD1d64wb_register
    1395418082U,	// VLD1d8
    1412195298U,	// VLD1d8Q
    2485941218U,	// VLD1d8Qwb_fixed
    2485978082U,	// VLD1d8Qwb_register
    1428972514U,	// VLD1d8T
    2502718434U,	// VLD1d8Twb_fixed
    2502755298U,	// VLD1d8Twb_register
    2469164002U,	// VLD1d8wb_fixed
    2469200866U,	// VLD1d8wb_register
    1447191522U,	// VLD1q16
    2520937442U,	// VLD1q16wb_fixed
    2520974306U,	// VLD1q16wb_register
    1447322594U,	// VLD1q32
    2521068514U,	// VLD1q32wb_fixed
    2521105378U,	// VLD1q32wb_register
    1447453666U,	// VLD1q64
    2521199586U,	// VLD1q64wb_fixed
    2521236450U,	// VLD1q64wb_register
    1445749730U,	// VLD1q8
    2519495650U,	// VLD1q8wb_fixed
    2519532514U,	// VLD1q8wb_register
    1380082702U,	// VLD2DUPd16
    2453828622U,	// VLD2DUPd16wb_fixed
    2453865486U,	// VLD2DUPd16wb_register
    1463968782U,	// VLD2DUPd16x2
    2537714702U,	// VLD2DUPd16x2wb_fixed
    2537751566U,	// VLD2DUPd16x2wb_register
    1380213774U,	// VLD2DUPd32
    2453959694U,	// VLD2DUPd32wb_fixed
    2453996558U,	// VLD2DUPd32wb_register
    1464099854U,	// VLD2DUPd32x2
    2537845774U,	// VLD2DUPd32x2wb_fixed
    2537882638U,	// VLD2DUPd32x2wb_register
    1378640910U,	// VLD2DUPd8
    2452386830U,	// VLD2DUPd8wb_fixed
    2452423694U,	// VLD2DUPd8wb_register
    1462526990U,	// VLD2DUPd8x2
    2536272910U,	// VLD2DUPd8x2wb_fixed
    2536309774U,	// VLD2DUPd8x2wb_register
    3226039310U,	// VLD2LNd16
    0U,	// VLD2LNd16Pseudo
    0U,	// VLD2LNd16Pseudo_UPD
    3226043406U,	// VLD2LNd16_UPD
    3226170382U,	// VLD2LNd32
    0U,	// VLD2LNd32Pseudo
    0U,	// VLD2LNd32Pseudo_UPD
    3226174478U,	// VLD2LNd32_UPD
    3226301454U,	// VLD2LNd8
    0U,	// VLD2LNd8Pseudo
    0U,	// VLD2LNd8Pseudo_UPD
    3226305550U,	// VLD2LNd8_UPD
    4355086U,	// VLD2LNdAsm_16
    4486158U,	// VLD2LNdAsm_32
    2913294U,	// VLD2LNdAsm_8
    4355086U,	// VLD2LNdWB_fixed_Asm_16
    4486158U,	// VLD2LNdWB_fixed_Asm_32
    2913294U,	// VLD2LNdWB_fixed_Asm_8
    4391950U,	// VLD2LNdWB_register_Asm_16
    4523022U,	// VLD2LNdWB_register_Asm_32
    2950158U,	// VLD2LNdWB_register_Asm_8
    3226039310U,	// VLD2LNq16
    0U,	// VLD2LNq16Pseudo
    0U,	// VLD2LNq16Pseudo_UPD
    3226043406U,	// VLD2LNq16_UPD
    3226170382U,	// VLD2LNq32
    0U,	// VLD2LNq32Pseudo
    0U,	// VLD2LNq32Pseudo_UPD
    3226174478U,	// VLD2LNq32_UPD
    4355086U,	// VLD2LNqAsm_16
    4486158U,	// VLD2LNqAsm_32
    4355086U,	// VLD2LNqWB_fixed_Asm_16
    4486158U,	// VLD2LNqWB_fixed_Asm_32
    4391950U,	// VLD2LNqWB_register_Asm_16
    4523022U,	// VLD2LNqWB_register_Asm_32
    1480745998U,	// VLD2b16
    2554491918U,	// VLD2b16wb_fixed
    2554528782U,	// VLD2b16wb_register
    1480877070U,	// VLD2b32
    2554622990U,	// VLD2b32wb_fixed
    2554659854U,	// VLD2b32wb_register
    1479304206U,	// VLD2b8
    2553050126U,	// VLD2b8wb_fixed
    2553086990U,	// VLD2b8wb_register
    1447191566U,	// VLD2d16
    2520937486U,	// VLD2d16wb_fixed
    2520974350U,	// VLD2d16wb_register
    1447322638U,	// VLD2d32
    2521068558U,	// VLD2d32wb_fixed
    2521105422U,	// VLD2d32wb_register
    1445749774U,	// VLD2d8
    2519495694U,	// VLD2d8wb_fixed
    2519532558U,	// VLD2d8wb_register
    1413637134U,	// VLD2q16
    0U,	// VLD2q16Pseudo
    0U,	// VLD2q16PseudoWB_fixed
    0U,	// VLD2q16PseudoWB_register
    2487383054U,	// VLD2q16wb_fixed
    2487419918U,	// VLD2q16wb_register
    1413768206U,	// VLD2q32
    0U,	// VLD2q32Pseudo
    0U,	// VLD2q32PseudoWB_fixed
    0U,	// VLD2q32PseudoWB_register
    2487514126U,	// VLD2q32wb_fixed
    2487550990U,	// VLD2q32wb_register
    1412195342U,	// VLD2q8
    0U,	// VLD2q8Pseudo
    0U,	// VLD2q8PseudoWB_fixed
    0U,	// VLD2q8PseudoWB_register
    2485941262U,	// VLD2q8wb_fixed
    2485978126U,	// VLD2q8wb_register
    4785198U,	// VLD3DUPd16
    0U,	// VLD3DUPd16Pseudo
    0U,	// VLD3DUPd16Pseudo_UPD
    4813870U,	// VLD3DUPd16_UPD
    4916270U,	// VLD3DUPd32
    0U,	// VLD3DUPd32Pseudo
    0U,	// VLD3DUPd32Pseudo_UPD
    4944942U,	// VLD3DUPd32_UPD
    5047342U,	// VLD3DUPd8
    0U,	// VLD3DUPd8Pseudo
    0U,	// VLD3DUPd8Pseudo_UPD
    5076014U,	// VLD3DUPd8_UPD
    1497523246U,	// VLD3DUPdAsm_16
    1497654318U,	// VLD3DUPdAsm_32
    1496081454U,	// VLD3DUPdAsm_8
    1497523246U,	// VLD3DUPdWB_fixed_Asm_16
    1497654318U,	// VLD3DUPdWB_fixed_Asm_32
    1496081454U,	// VLD3DUPdWB_fixed_Asm_8
    423785518U,	// VLD3DUPdWB_register_Asm_16
    423916590U,	// VLD3DUPdWB_register_Asm_32
    422343726U,	// VLD3DUPdWB_register_Asm_8
    4785198U,	// VLD3DUPq16
    4813870U,	// VLD3DUPq16_UPD
    4916270U,	// VLD3DUPq32
    4944942U,	// VLD3DUPq32_UPD
    5047342U,	// VLD3DUPq8
    5076014U,	// VLD3DUPq8_UPD
    1514300462U,	// VLD3DUPqAsm_16
    1514431534U,	// VLD3DUPqAsm_32
    1512858670U,	// VLD3DUPqAsm_8
    1514300462U,	// VLD3DUPqWB_fixed_Asm_16
    1514431534U,	// VLD3DUPqWB_fixed_Asm_32
    1512858670U,	// VLD3DUPqWB_fixed_Asm_8
    440562734U,	// VLD3DUPqWB_register_Asm_16
    440693806U,	// VLD3DUPqWB_register_Asm_32
    439120942U,	// VLD3DUPqWB_register_Asm_8
    3226043438U,	// VLD3LNd16
    0U,	// VLD3LNd16Pseudo
    0U,	// VLD3LNd16Pseudo_UPD
    3226047534U,	// VLD3LNd16_UPD
    3226174510U,	// VLD3LNd32
    0U,	// VLD3LNd32Pseudo
    0U,	// VLD3LNd32Pseudo_UPD
    3226178606U,	// VLD3LNd32_UPD
    3226305582U,	// VLD3LNd8
    0U,	// VLD3LNd8Pseudo
    0U,	// VLD3LNd8Pseudo_UPD
    3226309678U,	// VLD3LNd8_UPD
    4355118U,	// VLD3LNdAsm_16
    4486190U,	// VLD3LNdAsm_32
    2913326U,	// VLD3LNdAsm_8
    4355118U,	// VLD3LNdWB_fixed_Asm_16
    4486190U,	// VLD3LNdWB_fixed_Asm_32
    2913326U,	// VLD3LNdWB_fixed_Asm_8
    4391982U,	// VLD3LNdWB_register_Asm_16
    4523054U,	// VLD3LNdWB_register_Asm_32
    2950190U,	// VLD3LNdWB_register_Asm_8
    3226043438U,	// VLD3LNq16
    0U,	// VLD3LNq16Pseudo
    0U,	// VLD3LNq16Pseudo_UPD
    3226047534U,	// VLD3LNq16_UPD
    3226174510U,	// VLD3LNq32
    0U,	// VLD3LNq32Pseudo
    0U,	// VLD3LNq32Pseudo_UPD
    3226178606U,	// VLD3LNq32_UPD
    4355118U,	// VLD3LNqAsm_16
    4486190U,	// VLD3LNqAsm_32
    4355118U,	// VLD3LNqWB_fixed_Asm_16
    4486190U,	// VLD3LNqWB_fixed_Asm_32
    4391982U,	// VLD3LNqWB_register_Asm_16
    4523054U,	// VLD3LNqWB_register_Asm_32
    4785198U,	// VLD3d16
    0U,	// VLD3d16Pseudo
    0U,	// VLD3d16Pseudo_UPD
    4813870U,	// VLD3d16_UPD
    4916270U,	// VLD3d32
    0U,	// VLD3d32Pseudo
    0U,	// VLD3d32Pseudo_UPD
    4944942U,	// VLD3d32_UPD
    5047342U,	// VLD3d8
    0U,	// VLD3d8Pseudo
    0U,	// VLD3d8Pseudo_UPD
    5076014U,	// VLD3d8_UPD
    1430414382U,	// VLD3dAsm_16
    1430545454U,	// VLD3dAsm_32
    1428972590U,	// VLD3dAsm_8
    1430414382U,	// VLD3dWB_fixed_Asm_16
    1430545454U,	// VLD3dWB_fixed_Asm_32
    1428972590U,	// VLD3dWB_fixed_Asm_8
    1430418478U,	// VLD3dWB_register_Asm_16
    1430549550U,	// VLD3dWB_register_Asm_32
    1428976686U,	// VLD3dWB_register_Asm_8
    4785198U,	// VLD3q16
    0U,	// VLD3q16Pseudo_UPD
    4813870U,	// VLD3q16_UPD
    0U,	// VLD3q16oddPseudo
    0U,	// VLD3q16oddPseudo_UPD
    4916270U,	// VLD3q32
    0U,	// VLD3q32Pseudo_UPD
    4944942U,	// VLD3q32_UPD
    0U,	// VLD3q32oddPseudo
    0U,	// VLD3q32oddPseudo_UPD
    5047342U,	// VLD3q8
    0U,	// VLD3q8Pseudo_UPD
    5076014U,	// VLD3q8_UPD
    0U,	// VLD3q8oddPseudo
    0U,	// VLD3q8oddPseudo_UPD
    1531077678U,	// VLD3qAsm_16
    1531208750U,	// VLD3qAsm_32
    1529635886U,	// VLD3qAsm_8
    1531077678U,	// VLD3qWB_fixed_Asm_16
    1531208750U,	// VLD3qWB_fixed_Asm_32
    1529635886U,	// VLD3qWB_fixed_Asm_8
    457339950U,	// VLD3qWB_register_Asm_16
    457471022U,	// VLD3qWB_register_Asm_32
    455898158U,	// VLD3qWB_register_Asm_8
    4760645U,	// VLD4DUPd16
    0U,	// VLD4DUPd16Pseudo
    0U,	// VLD4DUPd16Pseudo_UPD
    4826181U,	// VLD4DUPd16_UPD
    4891717U,	// VLD4DUPd32
    0U,	// VLD4DUPd32Pseudo
    0U,	// VLD4DUPd32Pseudo_UPD
    4957253U,	// VLD4DUPd32_UPD
    5022789U,	// VLD4DUPd8
    0U,	// VLD4DUPd8Pseudo
    0U,	// VLD4DUPd8Pseudo_UPD
    5088325U,	// VLD4DUPd8_UPD
    1547854917U,	// VLD4DUPdAsm_16
    1547985989U,	// VLD4DUPdAsm_32
    1546413125U,	// VLD4DUPdAsm_8
    1547854917U,	// VLD4DUPdWB_fixed_Asm_16
    1547985989U,	// VLD4DUPdWB_fixed_Asm_32
    1546413125U,	// VLD4DUPdWB_fixed_Asm_8
    474117189U,	// VLD4DUPdWB_register_Asm_16
    474248261U,	// VLD4DUPdWB_register_Asm_32
    472675397U,	// VLD4DUPdWB_register_Asm_8
    4760645U,	// VLD4DUPq16
    4826181U,	// VLD4DUPq16_UPD
    4891717U,	// VLD4DUPq32
    4957253U,	// VLD4DUPq32_UPD
    5022789U,	// VLD4DUPq8
    5088325U,	// VLD4DUPq8_UPD
    1564632133U,	// VLD4DUPqAsm_16
    1564763205U,	// VLD4DUPqAsm_32
    1563190341U,	// VLD4DUPqAsm_8
    1564632133U,	// VLD4DUPqWB_fixed_Asm_16
    1564763205U,	// VLD4DUPqWB_fixed_Asm_32
    1563190341U,	// VLD4DUPqWB_fixed_Asm_8
    490894405U,	// VLD4DUPqWB_register_Asm_16
    491025477U,	// VLD4DUPqWB_register_Asm_32
    489452613U,	// VLD4DUPqWB_register_Asm_8
    3226047557U,	// VLD4LNd16
    0U,	// VLD4LNd16Pseudo
    0U,	// VLD4LNd16Pseudo_UPD
    3226055749U,	// VLD4LNd16_UPD
    3226178629U,	// VLD4LNd32
    0U,	// VLD4LNd32Pseudo
    0U,	// VLD4LNd32Pseudo_UPD
    3226186821U,	// VLD4LNd32_UPD
    3226309701U,	// VLD4LNd8
    0U,	// VLD4LNd8Pseudo
    0U,	// VLD4LNd8Pseudo_UPD
    3226317893U,	// VLD4LNd8_UPD
    4355141U,	// VLD4LNdAsm_16
    4486213U,	// VLD4LNdAsm_32
    2913349U,	// VLD4LNdAsm_8
    4355141U,	// VLD4LNdWB_fixed_Asm_16
    4486213U,	// VLD4LNdWB_fixed_Asm_32
    2913349U,	// VLD4LNdWB_fixed_Asm_8
    4392005U,	// VLD4LNdWB_register_Asm_16
    4523077U,	// VLD4LNdWB_register_Asm_32
    2950213U,	// VLD4LNdWB_register_Asm_8
    3226047557U,	// VLD4LNq16
    0U,	// VLD4LNq16Pseudo
    0U,	// VLD4LNq16Pseudo_UPD
    3226055749U,	// VLD4LNq16_UPD
    3226178629U,	// VLD4LNq32
    0U,	// VLD4LNq32Pseudo
    0U,	// VLD4LNq32Pseudo_UPD
    3226186821U,	// VLD4LNq32_UPD
    4355141U,	// VLD4LNqAsm_16
    4486213U,	// VLD4LNqAsm_32
    4355141U,	// VLD4LNqWB_fixed_Asm_16
    4486213U,	// VLD4LNqWB_fixed_Asm_32
    4392005U,	// VLD4LNqWB_register_Asm_16
    4523077U,	// VLD4LNqWB_register_Asm_32
    4760645U,	// VLD4d16
    0U,	// VLD4d16Pseudo
    0U,	// VLD4d16Pseudo_UPD
    4826181U,	// VLD4d16_UPD
    4891717U,	// VLD4d32
    0U,	// VLD4d32Pseudo
    0U,	// VLD4d32Pseudo_UPD
    4957253U,	// VLD4d32_UPD
    5022789U,	// VLD4d8
    0U,	// VLD4d8Pseudo
    0U,	// VLD4d8Pseudo_UPD
    5088325U,	// VLD4d8_UPD
    1413637189U,	// VLD4dAsm_16
    1413768261U,	// VLD4dAsm_32
    1412195397U,	// VLD4dAsm_8
    1413637189U,	// VLD4dWB_fixed_Asm_16
    1413768261U,	// VLD4dWB_fixed_Asm_32
    1412195397U,	// VLD4dWB_fixed_Asm_8
    1413641285U,	// VLD4dWB_register_Asm_16
    1413772357U,	// VLD4dWB_register_Asm_32
    1412199493U,	// VLD4dWB_register_Asm_8
    4760645U,	// VLD4q16
    0U,	// VLD4q16Pseudo_UPD
    4826181U,	// VLD4q16_UPD
    0U,	// VLD4q16oddPseudo
    0U,	// VLD4q16oddPseudo_UPD
    4891717U,	// VLD4q32
    0U,	// VLD4q32Pseudo_UPD
    4957253U,	// VLD4q32_UPD
    0U,	// VLD4q32oddPseudo
    0U,	// VLD4q32oddPseudo_UPD
    5022789U,	// VLD4q8
    0U,	// VLD4q8Pseudo_UPD
    5088325U,	// VLD4q8_UPD
    0U,	// VLD4q8oddPseudo
    0U,	// VLD4q8oddPseudo_UPD
    1581409349U,	// VLD4qAsm_16
    1581540421U,	// VLD4qAsm_32
    1579967557U,	// VLD4qAsm_8
    1581409349U,	// VLD4qWB_fixed_Asm_16
    1581540421U,	// VLD4qWB_fixed_Asm_32
    1579967557U,	// VLD4qWB_fixed_Asm_8
    507671621U,	// VLD4qWB_register_Asm_16
    507802693U,	// VLD4qWB_register_Asm_32
    506229829U,	// VLD4qWB_register_Asm_8
    33572305U,	// VLDMDDB_UPD
    34149U,	// VLDMDIA
    33572197U,	// VLDMDIA_UPD
    0U,	// VLDMQIA
    33572305U,	// VLDMSDB_UPD
    34149U,	// VLDMSIA
    33572197U,	// VLDMSIA_UPD
    27002U,	// VLDRD
    27002U,	// VLDRS
    33706566U,	// VMAXNMD
    33706258U,	// VMAXNMND
    33706258U,	// VMAXNMNQ
    33706258U,	// VMAXNMS
    2249091875U,	// VMAXfd
    2249091875U,	// VMAXfq
    35416867U,	// VMAXsv16i8
    35154723U,	// VMAXsv2i32
    35285795U,	// VMAXsv4i16
    35154723U,	// VMAXsv4i32
    35285795U,	// VMAXsv8i16
    35416867U,	// VMAXsv8i8
    35810083U,	// VMAXuv16i8
    35547939U,	// VMAXuv2i32
    35679011U,	// VMAXuv4i16
    35547939U,	// VMAXuv4i32
    35679011U,	// VMAXuv8i16
    35810083U,	// VMAXuv8i8
    33706554U,	// VMINNMD
    33706246U,	// VMINNMND
    33706246U,	// VMINNMNQ
    33706246U,	// VMINNMS
    2249091298U,	// VMINfd
    2249091298U,	// VMINfq
    35416290U,	// VMINsv16i8
    35154146U,	// VMINsv2i32
    35285218U,	// VMINsv4i16
    35154146U,	// VMINsv4i32
    35285218U,	// VMINsv8i16
    35416290U,	// VMINsv8i8
    35809506U,	// VMINuv16i8
    35547362U,	// VMINuv2i32
    35678434U,	// VMINuv4i16
    35547362U,	// VMINuv4i32
    35678434U,	// VMINuv8i16
    35809506U,	// VMINuv8i8
    3322705285U,	// VMLAD
    18417694U,	// VMLALslsv2i32
    18548766U,	// VMLALslsv4i16
    18810910U,	// VMLALsluv2i32
    18941982U,	// VMLALsluv4i16
    18380830U,	// VMLALsv2i64
    18511902U,	// VMLALsv4i32
    18642974U,	// VMLALsv8i16
    18774046U,	// VMLALuv2i64
    18905118U,	// VMLALuv4i32
    19036190U,	// VMLALuv8i16
    3322836357U,	// VMLAS
    3322836357U,	// VMLAfd
    3322836357U,	// VMLAfq
    3322873221U,	// VMLAslfd
    3322873221U,	// VMLAslfq
    19334533U,	// VMLAslv2i32
    19465605U,	// VMLAslv4i16
    19334533U,	// VMLAslv4i32
    19465605U,	// VMLAslv8i16
    19559813U,	// VMLAv16i8
    19297669U,	// VMLAv2i32
    19428741U,	// VMLAv4i16
    19297669U,	// VMLAv4i32
    19428741U,	// VMLAv8i16
    19559813U,	// VMLAv8i8
    3322706407U,	// VMLSD
    18417813U,	// VMLSLslsv2i32
    18548885U,	// VMLSLslsv4i16
    18811029U,	// VMLSLsluv2i32
    18942101U,	// VMLSLsluv4i16
    18380949U,	// VMLSLsv2i64
    18512021U,	// VMLSLsv4i32
    18643093U,	// VMLSLsv8i16
    18774165U,	// VMLSLuv2i64
    18905237U,	// VMLSLuv4i32
    19036309U,	// VMLSLuv8i16
    3322837479U,	// VMLSS
    3322837479U,	// VMLSfd
    3322837479U,	// VMLSfq
    3322874343U,	// VMLSslfd
    3322874343U,	// VMLSslfq
    19335655U,	// VMLSslv2i32
    19466727U,	// VMLSslv4i16
    19335655U,	// VMLSslv4i32
    19466727U,	// VMLSslv8i16
    19560935U,	// VMLSv16i8
    19298791U,	// VMLSv2i32
    19429863U,	// VMLSv4i16
    19298791U,	// VMLSv4i32
    19429863U,	// VMLSv8i16
    19560935U,	// VMLSv8i8
    2248952562U,	// VMOVD
    0U,	// VMOVD0
    27378U,	// VMOVDRR
    0U,	// VMOVDcc
    1108887728U,	// VMOVLsv2i64
    1109018800U,	// VMOVLsv4i32
    1109149872U,	// VMOVLsv8i16
    1109280944U,	// VMOVLuv2i64
    1109412016U,	// VMOVLuv4i32
    1109543088U,	// VMOVLuv8i16
    1109674294U,	// VMOVNv2i32
    1109805366U,	// VMOVNv4i16
    1109936438U,	// VMOVNv8i8
    0U,	// VMOVQ0
    27378U,	// VMOVRRD
    31474U,	// VMOVRRS
    19186U,	// VMOVRS
    2249083634U,	// VMOVS
    19186U,	// VMOVSR
    31474U,	// VMOVSRR
    0U,	// VMOVScc
    237652722U,	// VMOVv16i8
    237259506U,	// VMOVv1i64
    3322825458U,	// VMOVv2f32
    237390578U,	// VMOVv2i32
    237259506U,	// VMOVv2i64
    3322825458U,	// VMOVv4f32
    237521650U,	// VMOVv4i16
    237390578U,	// VMOVv4i32
    237521650U,	// VMOVv8i16
    237652722U,	// VMOVv8i8
    2147518974U,	// VMRS
    3221260798U,	// VMRS_FPEXC
    35326U,	// VMRS_FPINST
    1073777150U,	// VMRS_FPINST2
    2147518974U,	// VMRS_FPSID
    3221260798U,	// VMRS_MVFR0
    35326U,	// VMRS_MVFR1
    1073777150U,	// VMRS_MVFR2
    5147055U,	// VMSR
    5278127U,	// VMSR_FPEXC
    5409199U,	// VMSR_FPINST
    5540271U,	// VMSR_FPINST2
    5671343U,	// VMSR_FPSID
    2248960171U,	// VMULD
    33706650U,	// VMULLp64
    5793922U,	// VMULLp8
    35158146U,	// VMULLslsv2i32
    35289218U,	// VMULLslsv4i16
    35551362U,	// VMULLsluv2i32
    35682434U,	// VMULLsluv4i16
    35154050U,	// VMULLsv2i64
    35285122U,	// VMULLsv4i32
    35416194U,	// VMULLsv8i16
    35547266U,	// VMULLuv2i64
    35678338U,	// VMULLuv4i32
    35809410U,	// VMULLuv8i16
    2249091243U,	// VMULS
    2249091243U,	// VMULfd
    2249091243U,	// VMULfq
    5793963U,	// VMULpd
    5793963U,	// VMULpq
    2249095339U,	// VMULslfd
    2249095339U,	// VMULslfq
    36075691U,	// VMULslv2i32
    36206763U,	// VMULslv4i16
    36075691U,	// VMULslv4i32
    36206763U,	// VMULslv8i16
    36333739U,	// VMULv16i8
    36071595U,	// VMULv2i32
    36202667U,	// VMULv4i16
    36071595U,	// VMULv4i32
    36202667U,	// VMULv8i16
    36333739U,	// VMULv8i8
    18730U,	// VMVNd
    18730U,	// VMVNq
    237390122U,	// VMVNv2i32
    237521194U,	// VMVNv4i16
    237390122U,	// VMVNv4i32
    237521194U,	// VMVNv8i16
    2248951652U,	// VNEGD
    2249082724U,	// VNEGS
    2249082724U,	// VNEGf32q
    2249082724U,	// VNEGfd
    1109018468U,	// VNEGs16d
    1109018468U,	// VNEGs16q
    1108887396U,	// VNEGs32d
    1108887396U,	// VNEGs32q
    1109149540U,	// VNEGs8d
    1109149540U,	// VNEGs8q
    3322705279U,	// VNMLAD
    3322836351U,	// VNMLAS
    3322706401U,	// VNMLSD
    3322837473U,	// VNMLSS
    2248960165U,	// VNMULD
    2249091237U,	// VNMULS
    26887U,	// VORNd
    26887U,	// VORNq
    27039U,	// VORRd
    237398431U,	// VORRiv2i32
    237529503U,	// VORRiv4i16
    237398431U,	// VORRiv4i32
    237529503U,	// VORRiv8i16
    27039U,	// VORRq
    1092380675U,	// VPADALsv16i8
    1092118531U,	// VPADALsv2i32
    1092249603U,	// VPADALsv4i16
    1092118531U,	// VPADALsv4i32
    1092249603U,	// VPADALsv8i16
    1092380675U,	// VPADALsv8i8
    1092773891U,	// VPADALuv16i8
    1092511747U,	// VPADALuv2i32
    1092642819U,	// VPADALuv4i16
    1092511747U,	// VPADALuv4i32
    1092642819U,	// VPADALuv8i16
    1092773891U,	// VPADALuv8i8
    1109149759U,	// VPADDLsv16i8
    1108887615U,	// VPADDLsv2i32
    1109018687U,	// VPADDLsv4i16
    1108887615U,	// VPADDLsv4i32
    1109018687U,	// VPADDLsv8i16
    1109149759U,	// VPADDLsv8i8
    1109542975U,	// VPADDLuv16i8
    1109280831U,	// VPADDLuv2i32
    1109411903U,	// VPADDLuv4i16
    1109280831U,	// VPADDLuv4i32
    1109411903U,	// VPADDLuv8i16
    1109542975U,	// VPADDLuv8i8
    2249090774U,	// VPADDf
    36202198U,	// VPADDi16
    36071126U,	// VPADDi32
    36333270U,	// VPADDi8
    2249091869U,	// VPMAXf
    35285789U,	// VPMAXs16
    35154717U,	// VPMAXs32
    35416861U,	// VPMAXs8
    35679005U,	// VPMAXu16
    35547933U,	// VPMAXu32
    35810077U,	// VPMAXu8
    2249091292U,	// VPMINf
    35285212U,	// VPMINs16
    35154140U,	// VPMINs32
    35416284U,	// VPMINs8
    35678428U,	// VPMINu16
    35547356U,	// VPMINu32
    35809500U,	// VPMINu8
    1109150150U,	// VQABSv16i8
    1108888006U,	// VQABSv2i32
    1109019078U,	// VQABSv4i16
    1108888006U,	// VQABSv4i32
    1109019078U,	// VQABSv8i16
    1109150150U,	// VQABSv8i8
    35415772U,	// VQADDsv16i8
    39479004U,	// VQADDsv1i64
    35153628U,	// VQADDsv2i32
    39479004U,	// VQADDsv2i64
    35284700U,	// VQADDsv4i16
    35153628U,	// VQADDsv4i32
    35284700U,	// VQADDsv8i16
    35415772U,	// VQADDsv8i8
    35808988U,	// VQADDuv16i8
    39610076U,	// VQADDuv1i64
    35546844U,	// VQADDuv2i32
    39610076U,	// VQADDuv2i64
    35677916U,	// VQADDuv4i16
    35546844U,	// VQADDuv4i32
    35677916U,	// VQADDuv8i16
    35808988U,	// VQADDuv8i8
    18417674U,	// VQDMLALslv2i32
    18548746U,	// VQDMLALslv4i16
    18380810U,	// VQDMLALv2i64
    18511882U,	// VQDMLALv4i32
    18417805U,	// VQDMLSLslv2i32
    18548877U,	// VQDMLSLslv4i16
    18380941U,	// VQDMLSLv2i64
    18512013U,	// VQDMLSLv4i32
    35157891U,	// VQDMULHslv2i32
    35288963U,	// VQDMULHslv4i16
    35157891U,	// VQDMULHslv4i32
    35288963U,	// VQDMULHslv8i16
    35153795U,	// VQDMULHv2i32
    35284867U,	// VQDMULHv4i16
    35153795U,	// VQDMULHv4i32
    35284867U,	// VQDMULHv8i16
    35158126U,	// VQDMULLslv2i32
    35289198U,	// VQDMULLslv4i16
    35154030U,	// VQDMULLv2i64
    35285102U,	// VQDMULLv4i32
    1113213218U,	// VQMOVNsuv2i32
    1108887842U,	// VQMOVNsuv4i16
    1109018914U,	// VQMOVNsuv8i8
    1113213231U,	// VQMOVNsv2i32
    1108887855U,	// VQMOVNsv4i16
    1109018927U,	// VQMOVNsv8i8
    1113344303U,	// VQMOVNuv2i32
    1109281071U,	// VQMOVNuv4i16
    1109412143U,	// VQMOVNuv8i8
    1109149534U,	// VQNEGv16i8
    1108887390U,	// VQNEGv2i32
    1109018462U,	// VQNEGv4i16
    1108887390U,	// VQNEGv4i32
    1109018462U,	// VQNEGv8i16
    1109149534U,	// VQNEGv8i8
    35157899U,	// VQRDMULHslv2i32
    35288971U,	// VQRDMULHslv4i16
    35157899U,	// VQRDMULHslv4i32
    35288971U,	// VQRDMULHslv8i16
    35153803U,	// VQRDMULHv2i32
    35284875U,	// VQRDMULHv4i16
    35153803U,	// VQRDMULHv4i32
    35284875U,	// VQRDMULHv8i16
    35416150U,	// VQRSHLsv16i8
    39479382U,	// VQRSHLsv1i64
    35154006U,	// VQRSHLsv2i32
    39479382U,	// VQRSHLsv2i64
    35285078U,	// VQRSHLsv4i16
    35154006U,	// VQRSHLsv4i32
    35285078U,	// VQRSHLsv8i16
    35416150U,	// VQRSHLsv8i8
    35809366U,	// VQRSHLuv16i8
    39610454U,	// VQRSHLuv1i64
    35547222U,	// VQRSHLuv2i32
    39610454U,	// VQRSHLuv2i64
    35678294U,	// VQRSHLuv4i16
    35547222U,	// VQRSHLuv4i32
    35678294U,	// VQRSHLuv8i16
    35809366U,	// VQRSHLuv8i8
    39479538U,	// VQRSHRNsv2i32
    35154162U,	// VQRSHRNsv4i16
    35285234U,	// VQRSHRNsv8i8
    39610610U,	// VQRSHRNuv2i32
    35547378U,	// VQRSHRNuv4i16
    35678450U,	// VQRSHRNuv8i8
    39479577U,	// VQRSHRUNv2i32
    35154201U,	// VQRSHRUNv4i16
    35285273U,	// VQRSHRUNv8i8
    35416144U,	// VQSHLsiv16i8
    39479376U,	// VQSHLsiv1i64
    35154000U,	// VQSHLsiv2i32
    39479376U,	// VQSHLsiv2i64
    35285072U,	// VQSHLsiv4i16
    35154000U,	// VQSHLsiv4i32
    35285072U,	// VQSHLsiv8i16
    35416144U,	// VQSHLsiv8i8
    35416792U,	// VQSHLsuv16i8
    39480024U,	// VQSHLsuv1i64
    35154648U,	// VQSHLsuv2i32
    39480024U,	// VQSHLsuv2i64
    35285720U,	// VQSHLsuv4i16
    35154648U,	// VQSHLsuv4i32
    35285720U,	// VQSHLsuv8i16
    35416792U,	// VQSHLsuv8i8
    35416144U,	// VQSHLsv16i8
    39479376U,	// VQSHLsv1i64
    35154000U,	// VQSHLsv2i32
    39479376U,	// VQSHLsv2i64
    35285072U,	// VQSHLsv4i16
    35154000U,	// VQSHLsv4i32
    35285072U,	// VQSHLsv8i16
    35416144U,	// VQSHLsv8i8
    35809360U,	// VQSHLuiv16i8
    39610448U,	// VQSHLuiv1i64
    35547216U,	// VQSHLuiv2i32
    39610448U,	// VQSHLuiv2i64
    35678288U,	// VQSHLuiv4i16
    35547216U,	// VQSHLuiv4i32
    35678288U,	// VQSHLuiv8i16
    35809360U,	// VQSHLuiv8i8
    35809360U,	// VQSHLuv16i8
    39610448U,	// VQSHLuv1i64
    35547216U,	// VQSHLuv2i32
    39610448U,	// VQSHLuv2i64
    35678288U,	// VQSHLuv4i16
    35547216U,	// VQSHLuv4i32
    35678288U,	// VQSHLuv8i16
    35809360U,	// VQSHLuv8i8
    39479531U,	// VQSHRNsv2i32
    35154155U,	// VQSHRNsv4i16
    35285227U,	// VQSHRNsv8i8
    39610603U,	// VQSHRNuv2i32
    35547371U,	// VQSHRNuv4i16
    35678443U,	// VQSHRNuv8i8
    39479569U,	// VQSHRUNv2i32
    35154193U,	// VQSHRUNv4i16
    35285265U,	// VQSHRUNv8i8
    35415631U,	// VQSUBsv16i8
    39478863U,	// VQSUBsv1i64
    35153487U,	// VQSUBsv2i32
    39478863U,	// VQSUBsv2i64
    35284559U,	// VQSUBsv4i16
    35153487U,	// VQSUBsv4i32
    35284559U,	// VQSUBsv8i16
    35415631U,	// VQSUBsv8i8
    35808847U,	// VQSUBuv16i8
    39609935U,	// VQSUBuv1i64
    35546703U,	// VQSUBuv2i32
    39609935U,	// VQSUBuv2i64
    35677775U,	// VQSUBuv4i16
    35546703U,	// VQSUBuv4i32
    35677775U,	// VQSUBuv8i16
    35808847U,	// VQSUBuv8i8
    35940557U,	// VRADDHNv2i32
    36071629U,	// VRADDHNv4i16
    36202701U,	// VRADDHNv8i8
    1109280576U,	// VRECPEd
    2249082688U,	// VRECPEfd
    2249082688U,	// VRECPEfq
    1109280576U,	// VRECPEq
    2249091575U,	// VRECPSfd
    2249091575U,	// VRECPSfq
    2901191U,	// VREV16d8
    2901191U,	// VREV16q8
    4342770U,	// VREV32d16
    2900978U,	// VREV32d8
    4342770U,	// VREV32q16
    2900978U,	// VREV32q8
    4342846U,	// VREV64d16
    4473918U,	// VREV64d32
    2901054U,	// VREV64d8
    4342846U,	// VREV64q16
    4473918U,	// VREV64q32
    2901054U,	// VREV64q8
    35415753U,	// VRHADDsv16i8
    35153609U,	// VRHADDsv2i32
    35284681U,	// VRHADDsv4i16
    35153609U,	// VRHADDsv4i32
    35284681U,	// VRHADDsv8i16
    35415753U,	// VRHADDsv8i8
    35808969U,	// VRHADDuv16i8
    35546825U,	// VRHADDuv2i32
    35677897U,	// VRHADDuv4i16
    35546825U,	// VRHADDuv4i32
    35677897U,	// VRHADDuv8i16
    35808969U,	// VRHADDuv8i8
    1107448354U,	// VRINTAD
    1107448046U,	// VRINTAND
    1107448046U,	// VRINTANQ
    1107448046U,	// VRINTAS
    1107448402U,	// VRINTMD
    1107448094U,	// VRINTMND
    1107448094U,	// VRINTMNQ
    1107448094U,	// VRINTMS
    1107448414U,	// VRINTND
    1107448106U,	// VRINTNND
    1107448106U,	// VRINTNNQ
    1107448106U,	// VRINTNS
    1107448426U,	// VRINTPD
    1107448118U,	// VRINTPND
    1107448118U,	// VRINTPNQ
    1107448118U,	// VRINTPS
    2248952244U,	// VRINTRD
    2249083316U,	// VRINTRS
    2248952785U,	// VRINTXD
    1107448166U,	// VRINTXND
    1107448166U,	// VRINTXNQ
    2249083857U,	// VRINTXS
    2248952797U,	// VRINTZD
    1107448178U,	// VRINTZND
    1107448178U,	// VRINTZNQ
    2249083869U,	// VRINTZS
    35416157U,	// VRSHLsv16i8
    39479389U,	// VRSHLsv1i64
    35154013U,	// VRSHLsv2i32
    39479389U,	// VRSHLsv2i64
    35285085U,	// VRSHLsv4i16
    35154013U,	// VRSHLsv4i32
    35285085U,	// VRSHLsv8i16
    35416157U,	// VRSHLsv8i8
    35809373U,	// VRSHLuv16i8
    39610461U,	// VRSHLuv1i64
    35547229U,	// VRSHLuv2i32
    39610461U,	// VRSHLuv2i64
    35678301U,	// VRSHLuv4i16
    35547229U,	// VRSHLuv4i32
    35678301U,	// VRSHLuv8i16
    35809373U,	// VRSHLuv8i8
    35940602U,	// VRSHRNv2i32
    36071674U,	// VRSHRNv4i16
    36202746U,	// VRSHRNv8i8
    35416447U,	// VRSHRsv16i8
    39479679U,	// VRSHRsv1i64
    35154303U,	// VRSHRsv2i32
    39479679U,	// VRSHRsv2i64
    35285375U,	// VRSHRsv4i16
    35154303U,	// VRSHRsv4i32
    35285375U,	// VRSHRsv8i16
    35416447U,	// VRSHRsv8i8
    35809663U,	// VRSHRuv16i8
    39610751U,	// VRSHRuv1i64
    35547519U,	// VRSHRuv2i32
    39610751U,	// VRSHRuv2i64
    35678591U,	// VRSHRuv4i16
    35547519U,	// VRSHRuv4i32
    35678591U,	// VRSHRuv8i16
    35809663U,	// VRSHRuv8i8
    1109280589U,	// VRSQRTEd
    2249082701U,	// VRSQRTEfd
    2249082701U,	// VRSQRTEfq
    1109280589U,	// VRSQRTEq
    2249091597U,	// VRSQRTSfd
    2249091597U,	// VRSQRTSfq
    18642325U,	// VRSRAsv16i8
    22705557U,	// VRSRAsv1i64
    18380181U,	// VRSRAsv2i32
    22705557U,	// VRSRAsv2i64
    18511253U,	// VRSRAsv4i16
    18380181U,	// VRSRAsv4i32
    18511253U,	// VRSRAsv8i16
    18642325U,	// VRSRAsv8i8
    19035541U,	// VRSRAuv16i8
    22836629U,	// VRSRAuv1i64
    18773397U,	// VRSRAuv2i32
    22836629U,	// VRSRAuv2i64
    18904469U,	// VRSRAuv4i16
    18773397U,	// VRSRAuv4i32
    18904469U,	// VRSRAuv8i16
    19035541U,	// VRSRAuv8i8
    35940542U,	// VRSUBHNv2i32
    36071614U,	// VRSUBHNv4i16
    36202686U,	// VRSUBHNv8i8
    33706614U,	// VSELEQD
    33706306U,	// VSELEQS
    33706542U,	// VSELGED
    33706234U,	// VSELGES
    33706638U,	// VSELGTD
    33706330U,	// VSELGTS
    33706626U,	// VSELVSD
    33706318U,	// VSELVSS
    2151840498U,	// VSETLNi16
    2151971570U,	// VSETLNi32
    2150398706U,	// VSETLNi8
    36202600U,	// VSHLLi16
    36071528U,	// VSHLLi32
    36333672U,	// VSHLLi8
    35154024U,	// VSHLLsv2i64
    35285096U,	// VSHLLsv4i32
    35416168U,	// VSHLLsv8i16
    35547240U,	// VSHLLuv2i64
    35678312U,	// VSHLLuv4i32
    35809384U,	// VSHLLuv8i16
    36333667U,	// VSHLiv16i8
    35940451U,	// VSHLiv1i64
    36071523U,	// VSHLiv2i32
    35940451U,	// VSHLiv2i64
    36202595U,	// VSHLiv4i16
    36071523U,	// VSHLiv4i32
    36202595U,	// VSHLiv8i16
    36333667U,	// VSHLiv8i8
    35416163U,	// VSHLsv16i8
    39479395U,	// VSHLsv1i64
    35154019U,	// VSHLsv2i32
    39479395U,	// VSHLsv2i64
    35285091U,	// VSHLsv4i16
    35154019U,	// VSHLsv4i32
    35285091U,	// VSHLsv8i16
    35416163U,	// VSHLsv8i8
    35809379U,	// VSHLuv16i8
    39610467U,	// VSHLuv1i64
    35547235U,	// VSHLuv2i32
    39610467U,	// VSHLuv2i64
    35678307U,	// VSHLuv4i16
    35547235U,	// VSHLuv4i32
    35678307U,	// VSHLuv8i16
    35809379U,	// VSHLuv8i8
    35940609U,	// VSHRNv2i32
    36071681U,	// VSHRNv4i16
    36202753U,	// VSHRNv8i8
    35416453U,	// VSHRsv16i8
    39479685U,	// VSHRsv1i64
    35154309U,	// VSHRsv2i32
    39479685U,	// VSHRsv2i64
    35285381U,	// VSHRsv4i16
    35154309U,	// VSHRsv4i32
    35285381U,	// VSHRsv8i16
    35416453U,	// VSHRsv8i8
    35809669U,	// VSHRuv16i8
    39610757U,	// VSHRuv1i64
    35547525U,	// VSHRuv2i32
    39610757U,	// VSHRuv2i64
    35678597U,	// VSHRuv4i16
    35547525U,	// VSHRuv4i32
    35678597U,	// VSHRuv8i16
    35809669U,	// VSHRuv8i8
    6187707U,	// VSHTOD
    6318779U,	// VSHTOS
    274877115U,	// VSITOD
    272517819U,	// VSITOS
    2914269U,	// VSLIv16i8
    4618205U,	// VSLIv1i64
    4487133U,	// VSLIv2i32
    4618205U,	// VSLIv2i64
    4356061U,	// VSLIv4i16
    4487133U,	// VSLIv4i32
    4356061U,	// VSLIv8i16
    2914269U,	// VSLIv8i8
    3328338619U,	// VSLTOD
    3325979323U,	// VSLTOS
    2248952463U,	// VSQRTD
    2249083535U,	// VSQRTS
    18642331U,	// VSRAsv16i8
    22705563U,	// VSRAsv1i64
    18380187U,	// VSRAsv2i32
    22705563U,	// VSRAsv2i64
    18511259U,	// VSRAsv4i16
    18380187U,	// VSRAsv4i32
    18511259U,	// VSRAsv8i16
    18642331U,	// VSRAsv8i8
    19035547U,	// VSRAuv16i8
    22836635U,	// VSRAuv1i64
    18773403U,	// VSRAuv2i32
    22836635U,	// VSRAuv2i64
    18904475U,	// VSRAuv4i16
    18773403U,	// VSRAuv4i32
    18904475U,	// VSRAuv8i16
    19035547U,	// VSRAuv8i8
    2914274U,	// VSRIv16i8
    4618210U,	// VSRIv1i64
    4487138U,	// VSRIv2i32
    4618210U,	// VSRIv2i64
    4356066U,	// VSRIv4i16
    4487138U,	// VSRIv4i32
    4356066U,	// VSRIv8i16
    2914274U,	// VSRIv8i8
    3242750957U,	// VST1LNd16
    3746079725U,	// VST1LNd16_UPD
    3242882029U,	// VST1LNd32
    3746210797U,	// VST1LNd32_UPD
    3243013101U,	// VST1LNd8
    3746341869U,	// VST1LNd8_UPD
    4355053U,	// VST1LNdAsm_16
    4486125U,	// VST1LNdAsm_32
    2913261U,	// VST1LNdAsm_8
    4355053U,	// VST1LNdWB_fixed_Asm_16
    4486125U,	// VST1LNdWB_fixed_Asm_32
    2913261U,	// VST1LNdWB_fixed_Asm_8
    4391917U,	// VST1LNdWB_register_Asm_16
    4522989U,	// VST1LNdWB_register_Asm_32
    2950125U,	// VST1LNdWB_register_Asm_8
    0U,	// VST1LNq16Pseudo
    0U,	// VST1LNq16Pseudo_UPD
    0U,	// VST1LNq32Pseudo
    0U,	// VST1LNq32Pseudo_UPD
    0U,	// VST1LNq8Pseudo
    0U,	// VST1LNq8Pseudo_UPD
    541221869U,	// VST1d16
    557999085U,	// VST1d16Q
    574780397U,	// VST1d16Qwb_fixed
    591594477U,	// VST1d16Qwb_register
    608330733U,	// VST1d16T
    625112045U,	// VST1d16Twb_fixed
    641926125U,	// VST1d16Twb_register
    658666477U,	// VST1d16wb_fixed
    675480557U,	// VST1d16wb_register
    541352941U,	// VST1d32
    558130157U,	// VST1d32Q
    574911469U,	// VST1d32Qwb_fixed
    591725549U,	// VST1d32Qwb_register
    608461805U,	// VST1d32T
    625243117U,	// VST1d32Twb_fixed
    642057197U,	// VST1d32Twb_register
    658797549U,	// VST1d32wb_fixed
    675611629U,	// VST1d32wb_register
    541484013U,	// VST1d64
    558261229U,	// VST1d64Q
    0U,	// VST1d64QPseudo
    0U,	// VST1d64QPseudoWB_fixed
    0U,	// VST1d64QPseudoWB_register
    575042541U,	// VST1d64Qwb_fixed
    591856621U,	// VST1d64Qwb_register
    608592877U,	// VST1d64T
    0U,	// VST1d64TPseudo
    0U,	// VST1d64TPseudoWB_fixed
    0U,	// VST1d64TPseudoWB_register
    625374189U,	// VST1d64Twb_fixed
    642188269U,	// VST1d64Twb_register
    658928621U,	// VST1d64wb_fixed
    675742701U,	// VST1d64wb_register
    539780077U,	// VST1d8
    556557293U,	// VST1d8Q
    573338605U,	// VST1d8Qwb_fixed
    590152685U,	// VST1d8Qwb_register
    606888941U,	// VST1d8T
    623670253U,	// VST1d8Twb_fixed
    640484333U,	// VST1d8Twb_register
    657224685U,	// VST1d8wb_fixed
    674038765U,	// VST1d8wb_register
    692216813U,	// VST1q16
    708998125U,	// VST1q16wb_fixed
    725812205U,	// VST1q16wb_register
    692347885U,	// VST1q32
    709129197U,	// VST1q32wb_fixed
    725943277U,	// VST1q32wb_register
    692478957U,	// VST1q64
    709260269U,	// VST1q64wb_fixed
    726074349U,	// VST1q64wb_register
    690775021U,	// VST1q8
    707556333U,	// VST1q8wb_fixed
    724370413U,	// VST1q8wb_register
    3242787881U,	// VST2LNd16
    0U,	// VST2LNd16Pseudo
    0U,	// VST2LNd16Pseudo_UPD
    3746133033U,	// VST2LNd16_UPD
    3242918953U,	// VST2LNd32
    0U,	// VST2LNd32Pseudo
    0U,	// VST2LNd32Pseudo_UPD
    3746264105U,	// VST2LNd32_UPD
    3243050025U,	// VST2LNd8
    0U,	// VST2LNd8Pseudo
    0U,	// VST2LNd8Pseudo_UPD
    3746395177U,	// VST2LNd8_UPD
    4355113U,	// VST2LNdAsm_16
    4486185U,	// VST2LNdAsm_32
    2913321U,	// VST2LNdAsm_8
    4355113U,	// VST2LNdWB_fixed_Asm_16
    4486185U,	// VST2LNdWB_fixed_Asm_32
    2913321U,	// VST2LNdWB_fixed_Asm_8
    4391977U,	// VST2LNdWB_register_Asm_16
    4523049U,	// VST2LNdWB_register_Asm_32
    2950185U,	// VST2LNdWB_register_Asm_8
    3242787881U,	// VST2LNq16
    0U,	// VST2LNq16Pseudo
    0U,	// VST2LNq16Pseudo_UPD
    3746133033U,	// VST2LNq16_UPD
    3242918953U,	// VST2LNq32
    0U,	// VST2LNq32Pseudo
    0U,	// VST2LNq32Pseudo_UPD
    3746264105U,	// VST2LNq32_UPD
    4355113U,	// VST2LNqAsm_16
    4486185U,	// VST2LNqAsm_32
    4355113U,	// VST2LNqWB_fixed_Asm_16
    4486185U,	// VST2LNqWB_fixed_Asm_32
    4391977U,	// VST2LNqWB_register_Asm_16
    4523049U,	// VST2LNqWB_register_Asm_32
    742548521U,	// VST2b16
    759329833U,	// VST2b16wb_fixed
    776143913U,	// VST2b16wb_register
    742679593U,	// VST2b32
    759460905U,	// VST2b32wb_fixed
    776274985U,	// VST2b32wb_register
    741106729U,	// VST2b8
    757888041U,	// VST2b8wb_fixed
    774702121U,	// VST2b8wb_register
    692216873U,	// VST2d16
    708998185U,	// VST2d16wb_fixed
    725812265U,	// VST2d16wb_register
    692347945U,	// VST2d32
    709129257U,	// VST2d32wb_fixed
    725943337U,	// VST2d32wb_register
    690775081U,	// VST2d8
    707556393U,	// VST2d8wb_fixed
    724370473U,	// VST2d8wb_register
    557999145U,	// VST2q16
    0U,	// VST2q16Pseudo
    0U,	// VST2q16PseudoWB_fixed
    0U,	// VST2q16PseudoWB_register
    574780457U,	// VST2q16wb_fixed
    591594537U,	// VST2q16wb_register
    558130217U,	// VST2q32
    0U,	// VST2q32Pseudo
    0U,	// VST2q32PseudoWB_fixed
    0U,	// VST2q32PseudoWB_register
    574911529U,	// VST2q32wb_fixed
    591725609U,	// VST2q32wb_register
    556557353U,	// VST2q8
    0U,	// VST2q8Pseudo
    0U,	// VST2q8PseudoWB_fixed
    0U,	// VST2q8PseudoWB_register
    573338665U,	// VST2q8wb_fixed
    590152745U,	// VST2q8wb_register
    3242763321U,	// VST3LNd16
    0U,	// VST3LNd16Pseudo
    0U,	// VST3LNd16Pseudo_UPD
    3746145337U,	// VST3LNd16_UPD
    3242894393U,	// VST3LNd32
    0U,	// VST3LNd32Pseudo
    0U,	// VST3LNd32Pseudo_UPD
    3746276409U,	// VST3LNd32_UPD
    3243025465U,	// VST3LNd8
    0U,	// VST3LNd8Pseudo
    0U,	// VST3LNd8Pseudo_UPD
    3746407481U,	// VST3LNd8_UPD
    4355129U,	// VST3LNdAsm_16
    4486201U,	// VST3LNdAsm_32
    2913337U,	// VST3LNdAsm_8
    4355129U,	// VST3LNdWB_fixed_Asm_16
    4486201U,	// VST3LNdWB_fixed_Asm_32
    2913337U,	// VST3LNdWB_fixed_Asm_8
    4391993U,	// VST3LNdWB_register_Asm_16
    4523065U,	// VST3LNdWB_register_Asm_32
    2950201U,	// VST3LNdWB_register_Asm_8
    3242763321U,	// VST3LNq16
    0U,	// VST3LNq16Pseudo
    0U,	// VST3LNq16Pseudo_UPD
    3746145337U,	// VST3LNq16_UPD
    3242894393U,	// VST3LNq32
    0U,	// VST3LNq32Pseudo
    0U,	// VST3LNq32Pseudo_UPD
    3746276409U,	// VST3LNq32_UPD
    4355129U,	// VST3LNqAsm_16
    4486201U,	// VST3LNqAsm_32
    4355129U,	// VST3LNqWB_fixed_Asm_16
    4486201U,	// VST3LNqWB_fixed_Asm_32
    4391993U,	// VST3LNqWB_register_Asm_16
    4523065U,	// VST3LNqWB_register_Asm_32
    21562425U,	// VST3d16
    0U,	// VST3d16Pseudo
    0U,	// VST3d16Pseudo_UPD
    524907577U,	// VST3d16_UPD
    21693497U,	// VST3d32
    0U,	// VST3d32Pseudo
    0U,	// VST3d32Pseudo_UPD
    525038649U,	// VST3d32_UPD
    21824569U,	// VST3d8
    0U,	// VST3d8Pseudo
    0U,	// VST3d8Pseudo_UPD
    525169721U,	// VST3d8_UPD
    1430414393U,	// VST3dAsm_16
    1430545465U,	// VST3dAsm_32
    1428972601U,	// VST3dAsm_8
    1430414393U,	// VST3dWB_fixed_Asm_16
    1430545465U,	// VST3dWB_fixed_Asm_32
    1428972601U,	// VST3dWB_fixed_Asm_8
    1430418489U,	// VST3dWB_register_Asm_16
    1430549561U,	// VST3dWB_register_Asm_32
    1428976697U,	// VST3dWB_register_Asm_8
    21562425U,	// VST3q16
    0U,	// VST3q16Pseudo_UPD
    524907577U,	// VST3q16_UPD
    0U,	// VST3q16oddPseudo
    0U,	// VST3q16oddPseudo_UPD
    21693497U,	// VST3q32
    0U,	// VST3q32Pseudo_UPD
    525038649U,	// VST3q32_UPD
    0U,	// VST3q32oddPseudo
    0U,	// VST3q32oddPseudo_UPD
    21824569U,	// VST3q8
    0U,	// VST3q8Pseudo_UPD
    525169721U,	// VST3q8_UPD
    0U,	// VST3q8oddPseudo
    0U,	// VST3q8oddPseudo_UPD
    1531077689U,	// VST3qAsm_16
    1531208761U,	// VST3qAsm_32
    1529635897U,	// VST3qAsm_8
    1531077689U,	// VST3qWB_fixed_Asm_16
    1531208761U,	// VST3qWB_fixed_Asm_32
    1529635897U,	// VST3qWB_fixed_Asm_8
    457339961U,	// VST3qWB_register_Asm_16
    457471033U,	// VST3qWB_register_Asm_32
    455898169U,	// VST3qWB_register_Asm_8
    3242816586U,	// VST4LNd16
    0U,	// VST4LNd16Pseudo
    0U,	// VST4LNd16Pseudo_UPD
    3746137162U,	// VST4LNd16_UPD
    3242947658U,	// VST4LNd32
    0U,	// VST4LNd32Pseudo
    0U,	// VST4LNd32Pseudo_UPD
    3746268234U,	// VST4LNd32_UPD
    3243078730U,	// VST4LNd8
    0U,	// VST4LNd8Pseudo
    0U,	// VST4LNd8Pseudo_UPD
    3746399306U,	// VST4LNd8_UPD
    4355146U,	// VST4LNdAsm_16
    4486218U,	// VST4LNdAsm_32
    2913354U,	// VST4LNdAsm_8
    4355146U,	// VST4LNdWB_fixed_Asm_16
    4486218U,	// VST4LNdWB_fixed_Asm_32
    2913354U,	// VST4LNdWB_fixed_Asm_8
    4392010U,	// VST4LNdWB_register_Asm_16
    4523082U,	// VST4LNdWB_register_Asm_32
    2950218U,	// VST4LNdWB_register_Asm_8
    3242816586U,	// VST4LNq16
    0U,	// VST4LNq16Pseudo
    0U,	// VST4LNq16Pseudo_UPD
    3746137162U,	// VST4LNq16_UPD
    3242947658U,	// VST4LNq32
    0U,	// VST4LNq32Pseudo
    0U,	// VST4LNq32Pseudo_UPD
    3746268234U,	// VST4LNq32_UPD
    4355146U,	// VST4LNqAsm_16
    4486218U,	// VST4LNqAsm_32
    4355146U,	// VST4LNqWB_fixed_Asm_16
    4486218U,	// VST4LNqWB_fixed_Asm_32
    4392010U,	// VST4LNqWB_register_Asm_16
    4523082U,	// VST4LNqWB_register_Asm_32
    21537866U,	// VST4d16
    0U,	// VST4d16Pseudo
    0U,	// VST4d16Pseudo_UPD
    524919882U,	// VST4d16_UPD
    21668938U,	// VST4d32
    0U,	// VST4d32Pseudo
    0U,	// VST4d32Pseudo_UPD
    525050954U,	// VST4d32_UPD
    21800010U,	// VST4d8
    0U,	// VST4d8Pseudo
    0U,	// VST4d8Pseudo_UPD
    525182026U,	// VST4d8_UPD
    1413637194U,	// VST4dAsm_16
    1413768266U,	// VST4dAsm_32
    1412195402U,	// VST4dAsm_8
    1413637194U,	// VST4dWB_fixed_Asm_16
    1413768266U,	// VST4dWB_fixed_Asm_32
    1412195402U,	// VST4dWB_fixed_Asm_8
    1413641290U,	// VST4dWB_register_Asm_16
    1413772362U,	// VST4dWB_register_Asm_32
    1412199498U,	// VST4dWB_register_Asm_8
    21537866U,	// VST4q16
    0U,	// VST4q16Pseudo_UPD
    524919882U,	// VST4q16_UPD
    0U,	// VST4q16oddPseudo
    0U,	// VST4q16oddPseudo_UPD
    21668938U,	// VST4q32
    0U,	// VST4q32Pseudo_UPD
    525050954U,	// VST4q32_UPD
    0U,	// VST4q32oddPseudo
    0U,	// VST4q32oddPseudo_UPD
    21800010U,	// VST4q8
    0U,	// VST4q8Pseudo_UPD
    525182026U,	// VST4q8_UPD
    0U,	// VST4q8oddPseudo
    0U,	// VST4q8oddPseudo_UPD
    1581409354U,	// VST4qAsm_16
    1581540426U,	// VST4qAsm_32
    1579967562U,	// VST4qAsm_8
    1581409354U,	// VST4qWB_fixed_Asm_16
    1581540426U,	// VST4qWB_fixed_Asm_32
    1579967562U,	// VST4qWB_fixed_Asm_8
    507671626U,	// VST4qWB_register_Asm_16
    507802698U,	// VST4qWB_register_Asm_32
    506229834U,	// VST4qWB_register_Asm_8
    33572312U,	// VSTMDDB_UPD
    34156U,	// VSTMDIA
    33572204U,	// VSTMDIA_UPD
    0U,	// VSTMQIA
    33572312U,	// VSTMSDB_UPD
    34156U,	// VSTMSIA
    33572204U,	// VSTMSIA_UPD
    27067U,	// VSTRD
    27067U,	// VSTRS
    2248959573U,	// VSUBD
    35940550U,	// VSUBHNv2i32
    36071622U,	// VSUBHNv4i16
    36202694U,	// VSUBHNv8i8
    35153961U,	// VSUBLsv2i64
    35285033U,	// VSUBLsv4i32
    35416105U,	// VSUBLsv8i16
    35547177U,	// VSUBLuv2i64
    35678249U,	// VSUBLuv4i32
    35809321U,	// VSUBLuv8i16
    2249090645U,	// VSUBS
    35154679U,	// VSUBWsv2i64
    35285751U,	// VSUBWsv4i32
    35416823U,	// VSUBWsv8i16
    35547895U,	// VSUBWuv2i64
    35678967U,	// VSUBWuv4i32
    35810039U,	// VSUBWuv8i16
    2249090645U,	// VSUBfd
    2249090645U,	// VSUBfq
    36333141U,	// VSUBv16i8
    35939925U,	// VSUBv1i64
    36070997U,	// VSUBv2i32
    35939925U,	// VSUBv2i64
    36202069U,	// VSUBv4i16
    36070997U,	// VSUBv4i32
    36202069U,	// VSUBv8i16
    36333141U,	// VSUBv8i8
    31064U,	// VSWPd
    31064U,	// VSWPq
    2910244U,	// VTBL1
    2910244U,	// VTBL2
    2910244U,	// VTBL3
    0U,	// VTBL3Pseudo
    2910244U,	// VTBL4
    0U,	// VTBL4Pseudo
    2915156U,	// VTBX1
    2915156U,	// VTBX2
    2915156U,	// VTBX3
    0U,	// VTBX3Pseudo
    2915156U,	// VTBX4
    0U,	// VTBX4Pseudo
    6580923U,	// VTOSHD
    6711995U,	// VTOSHS
    275270080U,	// VTOSIRD
    272255424U,	// VTOSIRS
    275270331U,	// VTOSIZD
    272255675U,	// VTOSIZS
    3328731835U,	// VTOSLD
    3325717179U,	// VTOSLS
    6974139U,	// VTOUHD
    7105211U,	// VTOUHS
    275663296U,	// VTOUIRD
    272386496U,	// VTOUIRS
    275663547U,	// VTOUIZD
    272386747U,	// VTOUIZS
    3329125051U,	// VTOULD
    3325848251U,	// VTOULS
    4356364U,	// VTRNd16
    4487436U,	// VTRNd32
    2914572U,	// VTRNd8
    4356364U,	// VTRNq16
    4487436U,	// VTRNq32
    2914572U,	// VTRNq8
    2910874U,	// VTSTv16i8
    4483738U,	// VTSTv2i32
    4352666U,	// VTSTv4i16
    4483738U,	// VTSTv4i32
    4352666U,	// VTSTv8i16
    2910874U,	// VTSTv8i8
    7367355U,	// VUHTOD
    7498427U,	// VUHTOS
    276056763U,	// VUITOD
    272648891U,	// VUITOS
    3329518267U,	// VULTOD
    3326110395U,	// VULTOS
    4356445U,	// VUZPd16
    2914653U,	// VUZPd8
    4356445U,	// VUZPq16
    4487517U,	// VUZPq32
    2914653U,	// VUZPq8
    4356421U,	// VZIPd16
    2914629U,	// VZIPd8
    4356421U,	// VZIPq16
    4487493U,	// VZIPq32
    2914629U,	// VZIPq8
    0U,	// WIN__CHKSTK
    34131U,	// sysLDMDA
    33572179U,	// sysLDMDA_UPD
    34258U,	// sysLDMDB
    33572306U,	// sysLDMDB_UPD
    34998U,	// sysLDMIA
    33573046U,	// sysLDMIA_UPD
    34277U,	// sysLDMIB
    33572325U,	// sysLDMIB_UPD
    34137U,	// sysSTMDA
    33572185U,	// sysSTMDA_UPD
    34265U,	// sysSTMDB
    33572313U,	// sysSTMDB_UPD
    35002U,	// sysSTMIA
    33573050U,	// sysSTMIA_UPD
    34283U,	// sysSTMIB
    33572331U,	// sysSTMIB_UPD
    0U,	// t2ABS
    5768U,	// t2ADCri
    7739016U,	// t2ADCrr
    7743112U,	// t2ADCrs
    0U,	// t2ADDSri
    0U,	// t2ADDSrr
    0U,	// t2ADDSrs
    7739077U,	// t2ADDri
    27390U,	// t2ADDri12
    7739077U,	// t2ADDrr
    7743173U,	// t2ADDrs
    7752054U,	// t2ADR
    5882U,	// t2ANDri
    7739130U,	// t2ANDrr
    7743226U,	// t2ANDrs
    7739812U,	// t2ASRri
    7739812U,	// t2ASRrr
    1081509283U,	// t2B
    26256U,	// t2BFC
    30677U,	// t2BFI
    5781U,	// t2BICri
    7739029U,	// t2BICrr
    7743125U,	// t2BICrs
    0U,	// t2BR_JT
    1073776615U,	// t2BXJ
    1081509283U,	// t2Bcc
    2197858625U,	// t2CDP
    2197857299U,	// t2CDP2
    433047U,	// t2CLREX
    19417U,	// t2CLZ
    7751911U,	// t2CMNri
    7751911U,	// t2CMNzrr
    7760103U,	// t2CMNzrs
    7752011U,	// t2CMPri
    7752011U,	// t2CMPrr
    7760203U,	// t2CMPrs
    414526U,	// t2CPS1p
    1165412858U,	// t2CPS2p
    83937786U,	// t2CPS3p
    33706710U,	// t2CRC32B
    33706718U,	// t2CRC32CB
    33706782U,	// t2CRC32CH
    33706851U,	// t2CRC32CW
    33706774U,	// t2CRC32H
    33706843U,	// t2CRC32W
    1073776474U,	// t2DBG
    431079U,	// t2DCPS1
    431139U,	// t2DCPS2
    431155U,	// t2DCPS3
    788563446U,	// t2DMB
    788563465U,	// t2DSB
    6546U,	// t2EORri
    7739794U,	// t2EORrr
    7743890U,	// t2EORrs
    1081510533U,	// t2HINT
    805340685U,	// t2ISB
    117504627U,	// t2IT
    0U,	// t2Int_eh_sjlj_setjmp
    0U,	// t2Int_eh_sjlj_setjmp_nofp
    17743U,	// t2LDA
    17824U,	// t2LDAB
    19333U,	// t2LDAEX
    18024U,	// t2LDAEXB
    26388U,	// t2LDAEXD
    18361U,	// t2LDAEXH
    18281U,	// t2LDAH
    3271587819U,	// t2LDC2L_OFFSET
    3271587819U,	// t2LDC2L_OPTION
    3271587819U,	// t2LDC2L_POST
    3271587819U,	// t2LDC2L_PRE
    3271586809U,	// t2LDC2_OFFSET
    3271586809U,	// t2LDC2_OPTION
    3271586809U,	// t2LDC2_POST
    3271586809U,	// t2LDC2_PRE
    3271587887U,	// t2LDCL_OFFSET
    3271587887U,	// t2LDCL_OPTION
    3271587887U,	// t2LDCL_POST
    3271587887U,	// t2LDCL_PRE
    3271587468U,	// t2LDC_OFFSET
    3271587468U,	// t2LDC_OPTION
    3271587468U,	// t2LDC_POST
    3271587468U,	// t2LDC_PRE
    34258U,	// t2LDMDB
    33572306U,	// t2LDMDB_UPD
    7768246U,	// t2LDMIA
    0U,	// t2LDMIA_RET
    41306294U,	// t2LDMIA_UPD
    27200U,	// t2LDRBT
    30207U,	// t2LDRB_POST
    30207U,	// t2LDRB_PRE
    7759359U,	// t2LDRBi12
    26111U,	// t2LDRBi8
    7751167U,	// t2LDRBpci
    280063U,	// t2LDRBpcrel
    7763455U,	// t2LDRBs
    67326U,	// t2LDRD_POST
    67326U,	// t2LDRD_PRE
    30462U,	// t2LDRDi8
    27537U,	// t2LDREX
    18038U,	// t2LDREXB
    26402U,	// t2LDREXD
    18375U,	// t2LDREXH
    27230U,	// t2LDRHT
    30612U,	// t2LDRH_POST
    30612U,	// t2LDRH_PRE
    7759764U,	// t2LDRHi12
    26516U,	// t2LDRHi8
    7751572U,	// t2LDRHpci
    280468U,	// t2LDRHpcrel
    7763860U,	// t2LDRHs
    27212U,	// t2LDRSBT
    30225U,	// t2LDRSB_POST
    30225U,	// t2LDRSB_PRE
    7759377U,	// t2LDRSBi12
    26129U,	// t2LDRSBi8
    7751185U,	// t2LDRSBpci
    280081U,	// t2LDRSBpcrel
    7763473U,	// t2LDRSBs
    27242U,	// t2LDRSHT
    30622U,	// t2LDRSH_POST
    30622U,	// t2LDRSH_PRE
    7759774U,	// t2LDRSHi12
    26526U,	// t2LDRSHi8
    7751582U,	// t2LDRSHpci
    280478U,	// t2LDRSHpcrel
    7763870U,	// t2LDRSHs
    27274U,	// t2LDRT
    31099U,	// t2LDR_POST
    31099U,	// t2LDR_PRE
    7760251U,	// t2LDRi12
    27003U,	// t2LDRi8
    7752059U,	// t2LDRpci
    0U,	// t2LDRpci_pic
    280955U,	// t2LDRpcrel
    7764347U,	// t2LDRs
    0U,	// t2LEApcrel
    0U,	// t2LEApcrelJT
    7739537U,	// t2LSLri
    7739537U,	// t2LSLrr
    7739819U,	// t2LSRri
    7739819U,	// t2LSRrr
    2197858674U,	// t2MCR
    2197857304U,	// t2MCR2
    2197883290U,	// t2MCRR
    2197881885U,	// t2MCRR2
    30075U,	// t2MLA
    31197U,	// t2MLS
    0U,	// t2MOVCCasr
    0U,	// t2MOVCCi
    0U,	// t2MOVCCi16
    0U,	// t2MOVCCi32imm
    0U,	// t2MOVCClsl
    0U,	// t2MOVCClsr
    0U,	// t2MOVCCr
    0U,	// t2MOVCCror
    289301U,	// t2MOVSsi
    293397U,	// t2MOVSsr
    27328U,	// t2MOVTi16
    0U,	// t2MOVTi16_ga_pcrel
    0U,	// t2MOV_ga_pcrel
    7805683U,	// t2MOVi
    19208U,	// t2MOVi16
    0U,	// t2MOVi16_ga_pcrel
    0U,	// t2MOVi32imm
    7805683U,	// t2MOVr
    289523U,	// t2MOVsi
    293619U,	// t2MOVsr
    7752195U,	// t2MOVsra_flag
    7752200U,	// t2MOVsrl_flag
    201369245U,	// t2MRC
    201368574U,	// t2MRC2
    2197882529U,	// t2MRRC
    2197881859U,	// t2MRRC2
    35327U,	// t2MRS_AR
    18943U,	// t2MRS_M
    1073777151U,	// t2MRSsys_AR
    218122672U,	// t2MSR_AR
    218122672U,	// t2MSR_M
    26785U,	// t2MUL
    0U,	// t2MVNCCi
    71979U,	// t2MVNi
    7805227U,	// t2MVNr
    7739691U,	// t2MVNs
    6408U,	// t2ORNri
    6408U,	// t2ORNrr
    10504U,	// t2ORNrs
    6560U,	// t2ORRri
    7739808U,	// t2ORRrr
    7743904U,	// t2ORRrs
    31275U,	// t2PKHBT
    30238U,	// t2PKHTB
    822102787U,	// t2PLDWi12
    838880003U,	// t2PLDWi8
    855665411U,	// t2PLDWs
    822101742U,	// t2PLDi12
    838878958U,	// t2PLDi8
    872449774U,	// t2PLDpci
    855664366U,	// t2PLDs
    822101977U,	// t2PLIi12
    838879193U,	// t2PLIi8
    872450009U,	// t2PLIpci
    855664601U,	// t2PLIs
    26333U,	// t2QADD
    25764U,	// t2QADD16
    25867U,	// t2QADD8
    27586U,	// t2QASX
    26307U,	// t2QDADD
    26179U,	// t2QDSUB
    27445U,	// t2QSAX
    26192U,	// t2QSUB
    25726U,	// t2QSUB16
    25828U,	// t2QSUB8
    19057U,	// t2RBIT
    7752415U,	// t2REV
    7750856U,	// t2REV16
    7751593U,	// t2REVSH
    1073776075U,	// t2RFEDB
    1073776075U,	// t2RFEDBW
    1073775967U,	// t2RFEIA
    1073775967U,	// t2RFEIAW
    7739798U,	// t2RORri
    7739798U,	// t2RORrr
    72625U,	// t2RRX
    0U,	// t2RSBSri
    0U,	// t2RSBSrs
    7738899U,	// t2RSBri
    5651U,	// t2RSBrr
    9747U,	// t2RSBrs
    25771U,	// t2SADD16
    25873U,	// t2SADD8
    27591U,	// t2SASX
    5764U,	// t2SBCri
    7739012U,	// t2SBCrr
    7743108U,	// t2SBCrs
    31651U,	// t2SBFX
    27363U,	// t2SDIV
    26700U,	// t2SEL
    25747U,	// t2SHADD16
    25852U,	// t2SHADD8
    27573U,	// t2SHASX
    27432U,	// t2SHSAX
    25709U,	// t2SHSUB16
    25813U,	// t2SHSUB8
    1073776281U,	// t2SMC
    30129U,	// t2SMLABB
    31268U,	// t2SMLABT
    30386U,	// t2SMLAD
    31577U,	// t2SMLADX
    43026U,	// t2SMLAL
    30136U,	// t2SMLALBB
    31281U,	// t2SMLALBT
    30439U,	// t2SMLALD
    31591U,	// t2SMLALDX
    30244U,	// t2SMLALTB
    31398U,	// t2SMLALTT
    30231U,	// t2SMLATB
    31391U,	// t2SMLATT
    30298U,	// t2SMLAWB
    31429U,	// t2SMLAWT
    30472U,	// t2SMLSD
    31607U,	// t2SMLSDX
    30450U,	// t2SMLSLD
    31599U,	// t2SMLSLDX
    30073U,	// t2SMMLA
    31083U,	// t2SMMLAR
    31195U,	// t2SMMLS
    31144U,	// t2SMMLSR
    26783U,	// t2SMMUL
    27018U,	// t2SMMULR
    26296U,	// t2SMUAD
    27488U,	// t2SMUADX
    26048U,	// t2SMULBB
    27193U,	// t2SMULBT
    30838U,	// t2SMULL
    26156U,	// t2SMULTB
    27310U,	// t2SMULTT
    26209U,	// t2SMULWB
    27340U,	// t2SMULWT
    26382U,	// t2SMUSD
    27518U,	// t2SMUSDX
    7898591U,	// t2SRSDB
    8029663U,	// t2SRSDB_UPD
    7898483U,	// t2SRSIA
    8029555U,	// t2SRSIA_UPD
    31258U,	// t2SSAT
    25785U,	// t2SSAT16
    27450U,	// t2SSAX
    25733U,	// t2SSUB16
    25834U,	// t2SSUB8
    3271587825U,	// t2STC2L_OFFSET
    3271587825U,	// t2STC2L_OPTION
    3271587825U,	// t2STC2L_POST
    3271587825U,	// t2STC2L_PRE
    3271586825U,	// t2STC2_OFFSET
    3271586825U,	// t2STC2_OPTION
    3271586825U,	// t2STC2_POST
    3271586825U,	// t2STC2_PRE
    3271587892U,	// t2STCL_OFFSET
    3271587892U,	// t2STCL_OPTION
    3271587892U,	// t2STCL_POST
    3271587892U,	// t2STCL_PRE
    3271587498U,	// t2STC_OFFSET
    3271587498U,	// t2STC_OPTION
    3271587498U,	// t2STC_POST
    3271587498U,	// t2STC_PRE
    18587U,	// t2STL
    17905U,	// t2STLB
    27531U,	// t2STLEX
    26223U,	// t2STLEXB
    30491U,	// t2STLEXD
    26560U,	// t2STLEXH
    18302U,	// t2STLH
    34265U,	// t2STMDB
    33572313U,	// t2STMDB_UPD
    7768250U,	// t2STMIA
    41306298U,	// t2STMIA_UPD
    27206U,	// t2STRBT
    33584644U,	// t2STRB_POST
    33584644U,	// t2STRB_PRE
    0U,	// t2STRB_preidx
    7759364U,	// t2STRBi12
    26116U,	// t2STRBi8
    7763460U,	// t2STRBs
    33621763U,	// t2STRD_POST
    33621763U,	// t2STRD_PRE
    30467U,	// t2STRDi8
    31645U,	// t2STREX
    26237U,	// t2STREXB
    30505U,	// t2STREXD
    26574U,	// t2STREXH
    27236U,	// t2STRHT
    33585049U,	// t2STRH_POST
    33585049U,	// t2STRH_PRE
    0U,	// t2STRH_preidx
    7759769U,	// t2STRHi12
    26521U,	// t2STRHi8
    7763865U,	// t2STRHs
    27285U,	// t2STRT
    33585596U,	// t2STR_POST
    33585596U,	// t2STR_PRE
    0U,	// t2STR_preidx
    7760316U,	// t2STRi12
    27068U,	// t2STRi8
    7764412U,	// t2STRs
    8161745U,	// t2SUBS_PC_LR
    0U,	// t2SUBSri
    0U,	// t2SUBSrr
    0U,	// t2SUBSrs
    7738949U,	// t2SUBri
    27384U,	// t2SUBri12
    7738949U,	// t2SUBrr
    7743045U,	// t2SUBrs
    30117U,	// t2SXTAB
    29775U,	// t2SXTAB16
    30574U,	// t2SXTAH
    7759417U,	// t2SXTB
    25695U,	// t2SXTB16
    7759791U,	// t2SXTH
    889210311U,	// t2TBB
    0U,	// t2TBB_JT
    905987962U,	// t2TBH
    0U,	// t2TBH_JT
    7752039U,	// t2TEQri
    7752039U,	// t2TEQrr
    7760231U,	// t2TEQrs
    7752347U,	// t2TSTri
    7752347U,	// t2TSTrr
    7760539U,	// t2TSTrs
    25778U,	// t2UADD16
    25879U,	// t2UADD8
    27596U,	// t2UASX
    31656U,	// t2UBFX
    414548U,	// t2UDF
    27368U,	// t2UDIV
    25755U,	// t2UHADD16
    25859U,	// t2UHADD8
    27579U,	// t2UHASX
    27438U,	// t2UHSAX
    25717U,	// t2UHSUB16
    25820U,	// t2UHSUB8
    30711U,	// t2UMAAL
    43032U,	// t2UMLAL
    30844U,	// t2UMULL
    25763U,	// t2UQADD16
    25866U,	// t2UQADD8
    27585U,	// t2UQASX
    27444U,	// t2UQSAX
    25725U,	// t2UQSUB16
    25827U,	// t2UQSUB8
    25846U,	// t2USAD8
    29902U,	// t2USADA8
    31263U,	// t2USAT
    25792U,	// t2USAT16
    27455U,	// t2USAX
    25740U,	// t2USUB16
    25840U,	// t2USUB8
    30123U,	// t2UXTAB
    29783U,	// t2UXTAB16
    30580U,	// t2UXTAH
    7759422U,	// t2UXTB
    25702U,	// t2UXTB16
    7759796U,	// t2UXTH
    931120776U,	// tADC
    26309U,	// tADDhirr
    25151173U,	// tADDi3
    931120837U,	// tADDi8
    26309U,	// tADDrSP
    26309U,	// tADDrSPi
    25151173U,	// tADDrr
    26309U,	// tADDspi
    26309U,	// tADDspr
    0U,	// tADJCALLSTACKDOWN
    0U,	// tADJCALLSTACKUP
    18806U,	// tADR
    931120890U,	// tAND
    25151908U,	// tASRri
    931121572U,	// tASRrr
    1073776035U,	// tB
    931120789U,	// tBIC
    414542U,	// tBKPT
    1090557990U,	// tBL
    1090558893U,	// tBLXi
    1090558893U,	// tBLXr
    0U,	// tBRIND
    0U,	// tBR_JTr
    1073777481U,	// tBX
    0U,	// tBX_CALL
    0U,	// tBX_RET
    0U,	// tBX_RET_vararg
    1073776035U,	// tBcc
    0U,	// tBfar
    1107448704U,	// tCBNZ
    1107448699U,	// tCBZ
    18663U,	// tCMNz
    18763U,	// tCMPhir
    18763U,	// tCMPi8
    18763U,	// tCMPr
    1157941754U,	// tCPS
    931121554U,	// tEOR
    1073777285U,	// tHINT
    414537U,	// tHLT
    0U,	// tInt_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_setjmp
    34998U,	// tLDMIA
    0U,	// tLDMIA_UPD
    26111U,	// tLDRBi
    26111U,	// tLDRBr
    26516U,	// tLDRHi
    26516U,	// tLDRHr
    0U,	// tLDRLIT_ga_abs
    0U,	// tLDRLIT_ga_pcrel
    26129U,	// tLDRSB
    26526U,	// tLDRSH
    27003U,	// tLDRi
    18811U,	// tLDRpci
    0U,	// tLDRpci_pic
    27003U,	// tLDRr
    27003U,	// tLDRspi
    0U,	// tLEApcrel
    0U,	// tLEApcrelJT
    25151633U,	// tLSLri
    931121297U,	// tLSLrr
    25151915U,	// tLSRri
    931121579U,	// tLSRrr
    0U,	// tMOVCCr_pseudo
    1107448643U,	// tMOVSr
    276941555U,	// tMOVi8
    19187U,	// tMOVr
    25151649U,	// tMUL
    276941099U,	// tMVN
    931121568U,	// tORR
    0U,	// tPICADD
    939563343U,	// tPOP
    0U,	// tPOP_RET
    939562916U,	// tPUSH
    19167U,	// tREV
    17608U,	// tREV16
    18345U,	// tREVSH
    931121558U,	// tROR
    260163091U,	// tRSB
    931120772U,	// tSBC
    86793U,	// tSETEND
    33573050U,	// tSTMIA_UPD
    26116U,	// tSTRBi
    26116U,	// tSTRBr
    26521U,	// tSTRHi
    26521U,	// tSTRHr
    27068U,	// tSTRi
    27068U,	// tSTRr
    27068U,	// tSTRspi
    25151045U,	// tSUBi3
    931120709U,	// tSUBi8
    25151045U,	// tSUBrr
    26181U,	// tSUBspi
    1073776302U,	// tSVC
    17977U,	// tSXTB
    18351U,	// tSXTH
    0U,	// tTAILJMPd
    0U,	// tTAILJMPdND
    0U,	// tTAILJMPr
    0U,	// tTPsoft
    2364U,	// tTRAP
    19099U,	// tTST
    414481U,	// tUDF
    17982U,	// tUXTB
    18356U,	// tUXTH
    0U
  };

  static const uint32_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// ABS
    0U,	// ADCri
    0U,	// ADCrr
    16384U,	// ADCrsi
    0U,	// ADCrsr
    0U,	// ADDSri
    0U,	// ADDSrr
    0U,	// ADDSrsi
    0U,	// ADDSrsr
    0U,	// ADDri
    0U,	// ADDrr
    16384U,	// ADDrsi
    0U,	// ADDrsr
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    8U,	// ADR
    0U,	// AESD
    0U,	// AESE
    0U,	// AESIMC
    0U,	// AESMC
    0U,	// ANDri
    0U,	// ANDrr
    16384U,	// ANDrsi
    0U,	// ANDrsr
    0U,	// ASRi
    0U,	// ASRr
    0U,	// B
    0U,	// BCCZi64
    0U,	// BCCi64
    16U,	// BFC
    32792U,	// BFI
    0U,	// BICri
    0U,	// BICrr
    16384U,	// BICrsi
    0U,	// BICrsr
    0U,	// BKPT
    0U,	// BL
    0U,	// BLX
    0U,	// BLX_pred
    0U,	// BLXi
    0U,	// BL_pred
    0U,	// BMOVPCB_CALL
    0U,	// BMOVPCRX_CALL
    0U,	// BR_JTadd
    0U,	// BR_JTm
    0U,	// BR_JTr
    0U,	// BX
    0U,	// BXJ
    0U,	// BX_CALL
    0U,	// BX_RET
    0U,	// BX_pred
    0U,	// Bcc
    544U,	// CDP
    0U,	// CDP2
    0U,	// CLREX
    1024U,	// CLZ
    1024U,	// CMNri
    1024U,	// CMNzrr
    40U,	// CMNzrsi
    48U,	// CMNzrsr
    1024U,	// CMPri
    1024U,	// CMPrr
    40U,	// CMPrsi
    48U,	// CMPrsr
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_STRUCT_BYVAL_I32
    0U,	// CPS1p
    0U,	// CPS2p
    1048U,	// CPS3p
    1048U,	// CRC32B
    1048U,	// CRC32CB
    1048U,	// CRC32CH
    1048U,	// CRC32CW
    1048U,	// CRC32H
    1048U,	// CRC32W
    0U,	// DBG
    0U,	// DMB
    0U,	// DSB
    0U,	// EORri
    0U,	// EORrr
    16384U,	// EORrsi
    0U,	// EORrsr
    0U,	// FCONSTD
    0U,	// FCONSTS
    57U,	// FLDMXDB_UPD
    1088U,	// FLDMXIA
    57U,	// FLDMXIA_UPD
    0U,	// FMSTAT
    57U,	// FSTMXDB_UPD
    1088U,	// FSTMXIA
    57U,	// FSTMXIA_UPD
    0U,	// HINT
    0U,	// HLT
    0U,	// ISB
    0U,	// ITasm
    0U,	// Int_eh_sjlj_dispatchsetup
    0U,	// Int_eh_sjlj_longjmp
    0U,	// Int_eh_sjlj_setjmp
    0U,	// Int_eh_sjlj_setjmp_nofp
    72U,	// LDA
    72U,	// LDAB
    72U,	// LDAEX
    72U,	// LDAEXB
    0U,	// LDAEXD
    72U,	// LDAEXH
    72U,	// LDAH
    0U,	// LDC2L_OFFSET
    1U,	// LDC2L_OPTION
    1U,	// LDC2L_POST
    0U,	// LDC2L_PRE
    0U,	// LDC2_OFFSET
    1U,	// LDC2_OPTION
    1U,	// LDC2_POST
    0U,	// LDC2_PRE
    81U,	// LDCL_OFFSET
    49241U,	// LDCL_OPTION
    65625U,	// LDCL_POST
    97U,	// LDCL_PRE
    81U,	// LDC_OFFSET
    49241U,	// LDC_OPTION
    65625U,	// LDC_POST
    97U,	// LDC_PRE
    1088U,	// LDMDA
    57U,	// LDMDA_UPD
    1088U,	// LDMDB
    57U,	// LDMDB_UPD
    1088U,	// LDMIA
    0U,	// LDMIA_RET
    57U,	// LDMIA_UPD
    1088U,	// LDMIB
    57U,	// LDMIB_UPD
    72U,	// LDRBT_POST
    82008U,	// LDRBT_POST_IMM
    82008U,	// LDRBT_POST_REG
    82008U,	// LDRB_POST_IMM
    82008U,	// LDRB_POST_REG
    104U,	// LDRB_PRE_IMM
    112U,	// LDRB_PRE_REG
    120U,	// LDRBi12
    128U,	// LDRBrs
    98304U,	// LDRD
    1163264U,	// LDRD_POST
    131072U,	// LDRD_PRE
    72U,	// LDREX
    72U,	// LDREXB
    0U,	// LDREXD
    72U,	// LDREXH
    136U,	// LDRH
    147544U,	// LDRHTi
    163928U,	// LDRHTr
    180312U,	// LDRH_POST
    144U,	// LDRH_PRE
    0U,	// LDRLIT_ga_abs
    0U,	// LDRLIT_ga_pcrel
    0U,	// LDRLIT_ga_pcrel_ldr
    136U,	// LDRSB
    147544U,	// LDRSBTi
    163928U,	// LDRSBTr
    180312U,	// LDRSB_POST
    144U,	// LDRSB_PRE
    136U,	// LDRSH
    147544U,	// LDRSHTi
    163928U,	// LDRSHTr
    180312U,	// LDRSH_POST
    144U,	// LDRSH_PRE
    72U,	// LDRT_POST
    82008U,	// LDRT_POST_IMM
    82008U,	// LDRT_POST_REG
    82008U,	// LDR_POST_IMM
    82008U,	// LDR_POST_REG
    104U,	// LDR_PRE_IMM
    112U,	// LDR_PRE_REG
    120U,	// LDRcp
    120U,	// LDRi12
    128U,	// LDRrs
    0U,	// LEApcrel
    0U,	// LEApcrelJT
    0U,	// LSLi
    0U,	// LSLr
    0U,	// LSRi
    0U,	// LSRr
    2295328U,	// MCR
    152U,	// MCR2
    3343904U,	// MCRR
    213152U,	// MCRR2
    17825792U,	// MLA
    0U,	// MLAv5
    17825792U,	// MLS
    0U,	// MOVCCi
    0U,	// MOVCCi16
    0U,	// MOVCCi32imm
    0U,	// MOVCCr
    0U,	// MOVCCsi
    0U,	// MOVCCsr
    0U,	// MOVPCLR
    0U,	// MOVPCRX
    1048U,	// MOVTi16
    0U,	// MOVTi16_ga_pcrel
    0U,	// MOV_ga_pcrel
    0U,	// MOV_ga_pcrel_ldr
    1024U,	// MOVi
    1024U,	// MOVi16
    0U,	// MOVi16_ga_pcrel
    0U,	// MOVi32imm
    1024U,	// MOVr
    1024U,	// MOVr_TC
    40U,	// MOVsi
    48U,	// MOVsr
    0U,	// MOVsra_flag
    0U,	// MOVsrl_flag
    0U,	// MRC
    0U,	// MRC2
    3343904U,	// MRRC
    213152U,	// MRRC2
    2U,	// MRS
    2U,	// MRSsys
    0U,	// MSR
    0U,	// MSRi
    0U,	// MUL
    0U,	// MULv5
    0U,	// MVNCCi
    1024U,	// MVNi
    1024U,	// MVNr
    40U,	// MVNsi
    48U,	// MVNsr
    0U,	// ORRri
    0U,	// ORRrr
    16384U,	// ORRrsi
    0U,	// ORRrsr
    0U,	// PICADD
    0U,	// PICLDR
    0U,	// PICLDRB
    0U,	// PICLDRH
    0U,	// PICLDRSB
    0U,	// PICLDRSH
    0U,	// PICSTR
    0U,	// PICSTRB
    0U,	// PICSTRH
    4194304U,	// PKHBT
    5242880U,	// PKHTB
    0U,	// PLDWi12
    0U,	// PLDWrs
    0U,	// PLDi12
    0U,	// PLDrs
    0U,	// PLIi12
    0U,	// PLIrs
    0U,	// QADD
    0U,	// QADD16
    0U,	// QADD8
    0U,	// QASX
    0U,	// QDADD
    0U,	// QDSUB
    0U,	// QSAX
    0U,	// QSUB
    0U,	// QSUB16
    0U,	// QSUB8
    1024U,	// RBIT
    1024U,	// REV
    1024U,	// REV16
    1024U,	// REVSH
    0U,	// RFEDA
    0U,	// RFEDA_UPD
    0U,	// RFEDB
    0U,	// RFEDB_UPD
    0U,	// RFEIA
    0U,	// RFEIA_UPD
    0U,	// RFEIB
    0U,	// RFEIB_UPD
    0U,	// RORi
    0U,	// RORr
    0U,	// RRX
    1024U,	// RRXi
    0U,	// RSBSri
    0U,	// RSBSrsi
    0U,	// RSBSrsr
    0U,	// RSBri
    0U,	// RSBrr
    16384U,	// RSBrsi
    0U,	// RSBrsr
    0U,	// RSCri
    0U,	// RSCrr
    16384U,	// RSCrsi
    0U,	// RSCrsr
    0U,	// SADD16
    0U,	// SADD8
    0U,	// SASX
    0U,	// SBCri
    0U,	// SBCrr
    16384U,	// SBCrsi
    0U,	// SBCrsr
    34603008U,	// SBFX
    0U,	// SDIV
    0U,	// SEL
    0U,	// SETEND
    1184U,	// SHA1C
    0U,	// SHA1H
    1184U,	// SHA1M
    1184U,	// SHA1P
    1184U,	// SHA1SU0
    0U,	// SHA1SU1
    1184U,	// SHA256H
    1184U,	// SHA256H2
    0U,	// SHA256SU0
    1184U,	// SHA256SU1
    0U,	// SHADD16
    0U,	// SHADD8
    0U,	// SHASX
    0U,	// SHSAX
    0U,	// SHSUB16
    0U,	// SHSUB8
    0U,	// SMC
    17825792U,	// SMLABB
    17825792U,	// SMLABT
    17825792U,	// SMLAD
    17825792U,	// SMLADX
    0U,	// SMLAL
    17825792U,	// SMLALBB
    17825792U,	// SMLALBT
    17825792U,	// SMLALD
    17825792U,	// SMLALDX
    17825792U,	// SMLALTB
    17825792U,	// SMLALTT
    0U,	// SMLALv5
    17825792U,	// SMLATB
    17825792U,	// SMLATT
    17825792U,	// SMLAWB
    17825792U,	// SMLAWT
    17825792U,	// SMLSD
    17825792U,	// SMLSDX
    17825792U,	// SMLSLD
    17825792U,	// SMLSLDX
    17825792U,	// SMMLA
    17825792U,	// SMMLAR
    17825792U,	// SMMLS
    17825792U,	// SMMLSR
    0U,	// SMMUL
    0U,	// SMMULR
    0U,	// SMUAD
    0U,	// SMUADX
    0U,	// SMULBB
    0U,	// SMULBT
    17825792U,	// SMULL
    0U,	// SMULLv5
    0U,	// SMULTB
    0U,	// SMULTT
    0U,	// SMULWB
    0U,	// SMULWT
    0U,	// SMUSD
    0U,	// SMUSDX
    0U,	// SRSDA
    0U,	// SRSDA_UPD
    0U,	// SRSDB
    0U,	// SRSDB_UPD
    0U,	// SRSIA
    0U,	// SRSIA_UPD
    0U,	// SRSIB
    0U,	// SRSIB_UPD
    2216U,	// SSAT
    1192U,	// SSAT16
    0U,	// SSAX
    0U,	// SSUB16
    0U,	// SSUB8
    0U,	// STC2L_OFFSET
    1U,	// STC2L_OPTION
    1U,	// STC2L_POST
    0U,	// STC2L_PRE
    0U,	// STC2_OFFSET
    1U,	// STC2_OPTION
    1U,	// STC2_POST
    0U,	// STC2_PRE
    81U,	// STCL_OFFSET
    49241U,	// STCL_OPTION
    65625U,	// STCL_POST
    97U,	// STCL_PRE
    81U,	// STC_OFFSET
    49241U,	// STC_OPTION
    65625U,	// STC_POST
    97U,	// STC_PRE
    72U,	// STL
    72U,	// STLB
    229376U,	// STLEX
    229376U,	// STLEXB
    176U,	// STLEXD
    229376U,	// STLEXH
    72U,	// STLH
    1088U,	// STMDA
    57U,	// STMDA_UPD
    1088U,	// STMDB
    57U,	// STMDB_UPD
    1088U,	// STMIA
    57U,	// STMIA_UPD
    1088U,	// STMIB
    57U,	// STMIB_UPD
    72U,	// STRBT_POST
    82008U,	// STRBT_POST_IMM
    82008U,	// STRBT_POST_REG
    82008U,	// STRB_POST_IMM
    82008U,	// STRB_POST_REG
    104U,	// STRB_PRE_IMM
    112U,	// STRB_PRE_REG
    120U,	// STRBi12
    0U,	// STRBi_preidx
    0U,	// STRBr_preidx
    128U,	// STRBrs
    98304U,	// STRD
    1163288U,	// STRD_POST
    131096U,	// STRD_PRE
    229376U,	// STREX
    229376U,	// STREXB
    176U,	// STREXD
    229376U,	// STREXH
    136U,	// STRH
    147544U,	// STRHTi
    163928U,	// STRHTr
    180312U,	// STRH_POST
    144U,	// STRH_PRE
    0U,	// STRH_preidx
    72U,	// STRT_POST
    82008U,	// STRT_POST_IMM
    82008U,	// STRT_POST_REG
    82008U,	// STR_POST_IMM
    82008U,	// STR_POST_REG
    104U,	// STR_PRE_IMM
    112U,	// STR_PRE_REG
    120U,	// STRi12
    0U,	// STRi_preidx
    0U,	// STRr_preidx
    128U,	// STRrs
    0U,	// SUBS_PC_LR
    0U,	// SUBSri
    0U,	// SUBSrr
    0U,	// SUBSrsi
    0U,	// SUBSrsr
    0U,	// SUBri
    0U,	// SUBrr
    16384U,	// SUBrsi
    0U,	// SUBrsr
    0U,	// SVC
    229376U,	// SWP
    229376U,	// SWPB
    6291456U,	// SXTAB
    6291456U,	// SXTAB16
    6291456U,	// SXTAH
    2560U,	// SXTB
    2560U,	// SXTB16
    2560U,	// SXTH
    0U,	// TAILJMPd
    0U,	// TAILJMPr
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    1024U,	// TEQri
    1024U,	// TEQrr
    40U,	// TEQrsi
    48U,	// TEQrsr
    0U,	// TPsoft
    0U,	// TRAP
    0U,	// TRAPNaCl
    1024U,	// TSTri
    1024U,	// TSTrr
    40U,	// TSTrsi
    48U,	// TSTrsr
    0U,	// UADD16
    0U,	// UADD8
    0U,	// UASX
    34603008U,	// UBFX
    0U,	// UDF
    0U,	// UDIV
    0U,	// UHADD16
    0U,	// UHADD8
    0U,	// UHASX
    0U,	// UHSAX
    0U,	// UHSUB16
    0U,	// UHSUB8
    17825792U,	// UMAAL
    0U,	// UMLAL
    0U,	// UMLALv5
    17825792U,	// UMULL
    0U,	// UMULLv5
    0U,	// UQADD16
    0U,	// UQADD8
    0U,	// UQASX
    0U,	// UQSAX
    0U,	// UQSUB16
    0U,	// UQSUB8
    0U,	// USAD8
    17825792U,	// USADA8
    7340032U,	// USAT
    0U,	// USAT16
    0U,	// USAX
    0U,	// USUB16
    0U,	// USUB8
    6291456U,	// UXTAB
    6291456U,	// UXTAB16
    6291456U,	// UXTAH
    2560U,	// UXTB
    2560U,	// UXTB16
    2560U,	// UXTH
    1184U,	// VABALsv2i64
    1184U,	// VABALsv4i32
    1184U,	// VABALsv8i16
    1184U,	// VABALuv2i64
    1184U,	// VABALuv4i32
    1184U,	// VABALuv8i16
    1184U,	// VABAsv16i8
    1184U,	// VABAsv2i32
    1184U,	// VABAsv4i16
    1184U,	// VABAsv4i32
    1184U,	// VABAsv8i16
    1184U,	// VABAsv8i8
    1184U,	// VABAuv16i8
    1184U,	// VABAuv2i32
    1184U,	// VABAuv4i16
    1184U,	// VABAuv4i32
    1184U,	// VABAuv8i16
    1184U,	// VABAuv8i8
    1048U,	// VABDLsv2i64
    1048U,	// VABDLsv4i32
    1048U,	// VABDLsv8i16
    1048U,	// VABDLuv2i64
    1048U,	// VABDLuv4i32
    1048U,	// VABDLuv8i16
    247328U,	// VABDfd
    247328U,	// VABDfq
    1048U,	// VABDsv16i8
    1048U,	// VABDsv2i32
    1048U,	// VABDsv4i16
    1048U,	// VABDsv4i32
    1048U,	// VABDsv8i16
    1048U,	// VABDsv8i8
    1048U,	// VABDuv16i8
    1048U,	// VABDuv2i32
    1048U,	// VABDuv4i16
    1048U,	// VABDuv4i32
    1048U,	// VABDuv8i16
    1048U,	// VABDuv8i8
    56U,	// VABSD
    56U,	// VABSS
    56U,	// VABSfd
    56U,	// VABSfq
    0U,	// VABSv16i8
    0U,	// VABSv2i32
    0U,	// VABSv4i16
    0U,	// VABSv4i32
    0U,	// VABSv8i16
    0U,	// VABSv8i8
    247328U,	// VACGEd
    247328U,	// VACGEq
    247328U,	// VACGTd
    247328U,	// VACGTq
    247328U,	// VADDD
    1048U,	// VADDHNv2i32
    1048U,	// VADDHNv4i16
    1048U,	// VADDHNv8i8
    1048U,	// VADDLsv2i64
    1048U,	// VADDLsv4i32
    1048U,	// VADDLsv8i16
    1048U,	// VADDLuv2i64
    1048U,	// VADDLuv4i32
    1048U,	// VADDLuv8i16
    247328U,	// VADDS
    1048U,	// VADDWsv2i64
    1048U,	// VADDWsv4i32
    1048U,	// VADDWsv8i16
    1048U,	// VADDWuv2i64
    1048U,	// VADDWuv4i32
    1048U,	// VADDWuv8i16
    247328U,	// VADDfd
    247328U,	// VADDfq
    1048U,	// VADDv16i8
    1048U,	// VADDv1i64
    1048U,	// VADDv2i32
    1048U,	// VADDv2i64
    1048U,	// VADDv4i16
    1048U,	// VADDv4i32
    1048U,	// VADDv8i16
    1048U,	// VADDv8i8
    0U,	// VANDd
    0U,	// VANDq
    0U,	// VBICd
    0U,	// VBICiv2i32
    0U,	// VBICiv4i16
    0U,	// VBICiv4i32
    0U,	// VBICiv8i16
    0U,	// VBICq
    262168U,	// VBIFd
    262168U,	// VBIFq
    262168U,	// VBITd
    262168U,	// VBITq
    262168U,	// VBSLd
    262168U,	// VBSLq
    247328U,	// VCEQfd
    247328U,	// VCEQfq
    1048U,	// VCEQv16i8
    1048U,	// VCEQv2i32
    1048U,	// VCEQv4i16
    1048U,	// VCEQv4i32
    1048U,	// VCEQv8i16
    1048U,	// VCEQv8i8
    2U,	// VCEQzv16i8
    184U,	// VCEQzv2f32
    2U,	// VCEQzv2i32
    184U,	// VCEQzv4f32
    2U,	// VCEQzv4i16
    2U,	// VCEQzv4i32
    2U,	// VCEQzv8i16
    2U,	// VCEQzv8i8
    247328U,	// VCGEfd
    247328U,	// VCGEfq
    1048U,	// VCGEsv16i8
    1048U,	// VCGEsv2i32
    1048U,	// VCGEsv4i16
    1048U,	// VCGEsv4i32
    1048U,	// VCGEsv8i16
    1048U,	// VCGEsv8i8
    1048U,	// VCGEuv16i8
    1048U,	// VCGEuv2i32
    1048U,	// VCGEuv4i16
    1048U,	// VCGEuv4i32
    1048U,	// VCGEuv8i16
    1048U,	// VCGEuv8i8
    2U,	// VCGEzv16i8
    184U,	// VCGEzv2f32
    2U,	// VCGEzv2i32
    184U,	// VCGEzv4f32
    2U,	// VCGEzv4i16
    2U,	// VCGEzv4i32
    2U,	// VCGEzv8i16
    2U,	// VCGEzv8i8
    247328U,	// VCGTfd
    247328U,	// VCGTfq
    1048U,	// VCGTsv16i8
    1048U,	// VCGTsv2i32
    1048U,	// VCGTsv4i16
    1048U,	// VCGTsv4i32
    1048U,	// VCGTsv8i16
    1048U,	// VCGTsv8i8
    1048U,	// VCGTuv16i8
    1048U,	// VCGTuv2i32
    1048U,	// VCGTuv4i16
    1048U,	// VCGTuv4i32
    1048U,	// VCGTuv8i16
    1048U,	// VCGTuv8i8
    2U,	// VCGTzv16i8
    184U,	// VCGTzv2f32
    2U,	// VCGTzv2i32
    184U,	// VCGTzv4f32
    2U,	// VCGTzv4i16
    2U,	// VCGTzv4i32
    2U,	// VCGTzv8i16
    2U,	// VCGTzv8i8
    2U,	// VCLEzv16i8
    184U,	// VCLEzv2f32
    2U,	// VCLEzv2i32
    184U,	// VCLEzv4f32
    2U,	// VCLEzv4i16
    2U,	// VCLEzv4i32
    2U,	// VCLEzv8i16
    2U,	// VCLEzv8i8
    0U,	// VCLSv16i8
    0U,	// VCLSv2i32
    0U,	// VCLSv4i16
    0U,	// VCLSv4i32
    0U,	// VCLSv8i16
    0U,	// VCLSv8i8
    2U,	// VCLTzv16i8
    184U,	// VCLTzv2f32
    2U,	// VCLTzv2i32
    184U,	// VCLTzv4f32
    2U,	// VCLTzv4i16
    2U,	// VCLTzv4i32
    2U,	// VCLTzv8i16
    2U,	// VCLTzv8i8
    0U,	// VCLZv16i8
    0U,	// VCLZv2i32
    0U,	// VCLZv4i16
    0U,	// VCLZv4i32
    0U,	// VCLZv8i16
    0U,	// VCLZv8i8
    56U,	// VCMPD
    56U,	// VCMPED
    56U,	// VCMPES
    0U,	// VCMPEZD
    0U,	// VCMPEZS
    56U,	// VCMPS
    0U,	// VCMPZD
    0U,	// VCMPZS
    1024U,	// VCNTd
    1024U,	// VCNTq
    0U,	// VCVTANSD
    0U,	// VCVTANSQ
    0U,	// VCVTANUD
    0U,	// VCVTANUQ
    0U,	// VCVTASD
    0U,	// VCVTASS
    0U,	// VCVTAUD
    0U,	// VCVTAUS
    0U,	// VCVTBDH
    0U,	// VCVTBHD
    0U,	// VCVTBHS
    0U,	// VCVTBSH
    0U,	// VCVTDS
    0U,	// VCVTMNSD
    0U,	// VCVTMNSQ
    0U,	// VCVTMNUD
    0U,	// VCVTMNUQ
    0U,	// VCVTMSD
    0U,	// VCVTMSS
    0U,	// VCVTMUD
    0U,	// VCVTMUS
    0U,	// VCVTNNSD
    0U,	// VCVTNNSQ
    0U,	// VCVTNNUD
    0U,	// VCVTNNUQ
    0U,	// VCVTNSD
    0U,	// VCVTNSS
    0U,	// VCVTNUD
    0U,	// VCVTNUS
    0U,	// VCVTPNSD
    0U,	// VCVTPNSQ
    0U,	// VCVTPNUD
    0U,	// VCVTPNUQ
    0U,	// VCVTPSD
    0U,	// VCVTPSS
    0U,	// VCVTPUD
    0U,	// VCVTPUS
    0U,	// VCVTSD
    0U,	// VCVTTDH
    0U,	// VCVTTHD
    0U,	// VCVTTHS
    0U,	// VCVTTSH
    0U,	// VCVTf2h
    0U,	// VCVTf2sd
    0U,	// VCVTf2sq
    0U,	// VCVTf2ud
    0U,	// VCVTf2uq
    58U,	// VCVTf2xsd
    58U,	// VCVTf2xsq
    58U,	// VCVTf2xud
    58U,	// VCVTf2xuq
    0U,	// VCVTh2f
    0U,	// VCVTs2fd
    0U,	// VCVTs2fq
    0U,	// VCVTu2fd
    0U,	// VCVTu2fq
    58U,	// VCVTxs2fd
    58U,	// VCVTxs2fq
    58U,	// VCVTxu2fd
    58U,	// VCVTxu2fq
    247328U,	// VDIVD
    247328U,	// VDIVS
    1024U,	// VDUP16d
    1024U,	// VDUP16q
    1024U,	// VDUP32d
    1024U,	// VDUP32q
    1024U,	// VDUP8d
    1024U,	// VDUP8q
    3072U,	// VDUPLN16d
    3072U,	// VDUPLN16q
    3072U,	// VDUPLN32d
    3072U,	// VDUPLN32q
    3072U,	// VDUPLN8d
    3072U,	// VDUPLN8q
    0U,	// VEORd
    0U,	// VEORq
    17825792U,	// VEXTd16
    17825792U,	// VEXTd32
    17825792U,	// VEXTd8
    17825792U,	// VEXTq16
    17825792U,	// VEXTq32
    17825792U,	// VEXTq64
    17825792U,	// VEXTq8
    249378U,	// VFMAD
    249378U,	// VFMAS
    249378U,	// VFMAfd
    249378U,	// VFMAfq
    249378U,	// VFMSD
    249378U,	// VFMSS
    249378U,	// VFMSfd
    249378U,	// VFMSfq
    249378U,	// VFNMAD
    249378U,	// VFNMAS
    249378U,	// VFNMSD
    249378U,	// VFNMSS
    3072U,	// VGETLNi32
    3U,	// VGETLNs16
    3U,	// VGETLNs8
    3U,	// VGETLNu16
    3U,	// VGETLNu8
    1048U,	// VHADDsv16i8
    1048U,	// VHADDsv2i32
    1048U,	// VHADDsv4i16
    1048U,	// VHADDsv4i32
    1048U,	// VHADDsv8i16
    1048U,	// VHADDsv8i8
    1048U,	// VHADDuv16i8
    1048U,	// VHADDuv2i32
    1048U,	// VHADDuv4i16
    1048U,	// VHADDuv4i32
    1048U,	// VHADDuv8i16
    1048U,	// VHADDuv8i8
    1048U,	// VHSUBsv16i8
    1048U,	// VHSUBsv2i32
    1048U,	// VHSUBsv4i16
    1048U,	// VHSUBsv4i32
    1048U,	// VHSUBsv8i16
    1048U,	// VHSUBsv8i8
    1048U,	// VHSUBuv16i8
    1048U,	// VHSUBuv2i32
    1048U,	// VHSUBuv4i16
    1048U,	// VHSUBuv4i32
    1048U,	// VHSUBuv8i16
    1048U,	// VHSUBuv8i8
    59U,	// VLD1DUPd16
    195U,	// VLD1DUPd16wb_fixed
    4131U,	// VLD1DUPd16wb_register
    59U,	// VLD1DUPd32
    195U,	// VLD1DUPd32wb_fixed
    4131U,	// VLD1DUPd32wb_register
    59U,	// VLD1DUPd8
    195U,	// VLD1DUPd8wb_fixed
    4131U,	// VLD1DUPd8wb_register
    59U,	// VLD1DUPq16
    195U,	// VLD1DUPq16wb_fixed
    4131U,	// VLD1DUPq16wb_register
    59U,	// VLD1DUPq32
    195U,	// VLD1DUPq32wb_fixed
    4131U,	// VLD1DUPq32wb_register
    59U,	// VLD1DUPq8
    195U,	// VLD1DUPq8wb_fixed
    4131U,	// VLD1DUPq8wb_register
    283339U,	// VLD1LNd16
    299731U,	// VLD1LNd16_UPD
    283339U,	// VLD1LNd32
    299731U,	// VLD1LNd32_UPD
    283339U,	// VLD1LNd8
    299731U,	// VLD1LNd8_UPD
    1240U,	// VLD1LNdAsm_16
    1240U,	// VLD1LNdAsm_32
    1240U,	// VLD1LNdAsm_8
    5336U,	// VLD1LNdWB_fixed_Asm_16
    5336U,	// VLD1LNdWB_fixed_Asm_32
    5336U,	// VLD1LNdWB_fixed_Asm_8
    311512U,	// VLD1LNdWB_register_Asm_16
    311512U,	// VLD1LNdWB_register_Asm_32
    311512U,	// VLD1LNdWB_register_Asm_8
    0U,	// VLD1LNq16Pseudo
    0U,	// VLD1LNq16Pseudo_UPD
    0U,	// VLD1LNq32Pseudo
    0U,	// VLD1LNq32Pseudo_UPD
    0U,	// VLD1LNq8Pseudo
    0U,	// VLD1LNq8Pseudo_UPD
    59U,	// VLD1d16
    59U,	// VLD1d16Q
    195U,	// VLD1d16Qwb_fixed
    4131U,	// VLD1d16Qwb_register
    59U,	// VLD1d16T
    195U,	// VLD1d16Twb_fixed
    4131U,	// VLD1d16Twb_register
    195U,	// VLD1d16wb_fixed
    4131U,	// VLD1d16wb_register
    59U,	// VLD1d32
    59U,	// VLD1d32Q
    195U,	// VLD1d32Qwb_fixed
    4131U,	// VLD1d32Qwb_register
    59U,	// VLD1d32T
    195U,	// VLD1d32Twb_fixed
    4131U,	// VLD1d32Twb_register
    195U,	// VLD1d32wb_fixed
    4131U,	// VLD1d32wb_register
    59U,	// VLD1d64
    59U,	// VLD1d64Q
    0U,	// VLD1d64QPseudo
    0U,	// VLD1d64QPseudoWB_fixed
    0U,	// VLD1d64QPseudoWB_register
    195U,	// VLD1d64Qwb_fixed
    4131U,	// VLD1d64Qwb_register
    59U,	// VLD1d64T
    0U,	// VLD1d64TPseudo
    0U,	// VLD1d64TPseudoWB_fixed
    0U,	// VLD1d64TPseudoWB_register
    195U,	// VLD1d64Twb_fixed
    4131U,	// VLD1d64Twb_register
    195U,	// VLD1d64wb_fixed
    4131U,	// VLD1d64wb_register
    59U,	// VLD1d8
    59U,	// VLD1d8Q
    195U,	// VLD1d8Qwb_fixed
    4131U,	// VLD1d8Qwb_register
    59U,	// VLD1d8T
    195U,	// VLD1d8Twb_fixed
    4131U,	// VLD1d8Twb_register
    195U,	// VLD1d8wb_fixed
    4131U,	// VLD1d8wb_register
    59U,	// VLD1q16
    195U,	// VLD1q16wb_fixed
    4131U,	// VLD1q16wb_register
    59U,	// VLD1q32
    195U,	// VLD1q32wb_fixed
    4131U,	// VLD1q32wb_register
    59U,	// VLD1q64
    195U,	// VLD1q64wb_fixed
    4131U,	// VLD1q64wb_register
    59U,	// VLD1q8
    195U,	// VLD1q8wb_fixed
    4131U,	// VLD1q8wb_register
    59U,	// VLD2DUPd16
    195U,	// VLD2DUPd16wb_fixed
    4131U,	// VLD2DUPd16wb_register
    59U,	// VLD2DUPd16x2
    195U,	// VLD2DUPd16x2wb_fixed
    4131U,	// VLD2DUPd16x2wb_register
    59U,	// VLD2DUPd32
    195U,	// VLD2DUPd32wb_fixed
    4131U,	// VLD2DUPd32wb_register
    59U,	// VLD2DUPd32x2
    195U,	// VLD2DUPd32x2wb_fixed
    4131U,	// VLD2DUPd32x2wb_register
    59U,	// VLD2DUPd8
    195U,	// VLD2DUPd8wb_fixed
    4131U,	// VLD2DUPd8wb_register
    59U,	// VLD2DUPd8x2
    195U,	// VLD2DUPd8x2wb_fixed
    4131U,	// VLD2DUPd8x2wb_register
    333523U,	// VLD2LNd16
    0U,	// VLD2LNd16Pseudo
    0U,	// VLD2LNd16Pseudo_UPD
    350435U,	// VLD2LNd16_UPD
    333523U,	// VLD2LNd32
    0U,	// VLD2LNd32Pseudo
    0U,	// VLD2LNd32Pseudo_UPD
    350435U,	// VLD2LNd32_UPD
    333523U,	// VLD2LNd8
    0U,	// VLD2LNd8Pseudo
    0U,	// VLD2LNd8Pseudo_UPD
    350435U,	// VLD2LNd8_UPD
    1240U,	// VLD2LNdAsm_16
    1240U,	// VLD2LNdAsm_32
    1240U,	// VLD2LNdAsm_8
    5336U,	// VLD2LNdWB_fixed_Asm_16
    5336U,	// VLD2LNdWB_fixed_Asm_32
    5336U,	// VLD2LNdWB_fixed_Asm_8
    311512U,	// VLD2LNdWB_register_Asm_16
    311512U,	// VLD2LNdWB_register_Asm_32
    311512U,	// VLD2LNdWB_register_Asm_8
    333523U,	// VLD2LNq16
    0U,	// VLD2LNq16Pseudo
    0U,	// VLD2LNq16Pseudo_UPD
    350435U,	// VLD2LNq16_UPD
    333523U,	// VLD2LNq32
    0U,	// VLD2LNq32Pseudo
    0U,	// VLD2LNq32Pseudo_UPD
    350435U,	// VLD2LNq32_UPD
    1240U,	// VLD2LNqAsm_16
    1240U,	// VLD2LNqAsm_32
    5336U,	// VLD2LNqWB_fixed_Asm_16
    5336U,	// VLD2LNqWB_fixed_Asm_32
    311512U,	// VLD2LNqWB_register_Asm_16
    311512U,	// VLD2LNqWB_register_Asm_32
    59U,	// VLD2b16
    195U,	// VLD2b16wb_fixed
    4131U,	// VLD2b16wb_register
    59U,	// VLD2b32
    195U,	// VLD2b32wb_fixed
    4131U,	// VLD2b32wb_register
    59U,	// VLD2b8
    195U,	// VLD2b8wb_fixed
    4131U,	// VLD2b8wb_register
    59U,	// VLD2d16
    195U,	// VLD2d16wb_fixed
    4131U,	// VLD2d16wb_register
    59U,	// VLD2d32
    195U,	// VLD2d32wb_fixed
    4131U,	// VLD2d32wb_register
    59U,	// VLD2d8
    195U,	// VLD2d8wb_fixed
    4131U,	// VLD2d8wb_register
    59U,	// VLD2q16
    0U,	// VLD2q16Pseudo
    0U,	// VLD2q16PseudoWB_fixed
    0U,	// VLD2q16PseudoWB_register
    195U,	// VLD2q16wb_fixed
    4131U,	// VLD2q16wb_register
    59U,	// VLD2q32
    0U,	// VLD2q32Pseudo
    0U,	// VLD2q32PseudoWB_fixed
    0U,	// VLD2q32PseudoWB_register
    195U,	// VLD2q32wb_fixed
    4131U,	// VLD2q32wb_register
    59U,	// VLD2q8
    0U,	// VLD2q8Pseudo
    0U,	// VLD2q8PseudoWB_fixed
    0U,	// VLD2q8PseudoWB_register
    195U,	// VLD2q8wb_fixed
    4131U,	// VLD2q8wb_register
    6892U,	// VLD3DUPd16
    0U,	// VLD3DUPd16Pseudo
    0U,	// VLD3DUPd16Pseudo_UPD
    367852U,	// VLD3DUPd16_UPD
    6892U,	// VLD3DUPd32
    0U,	// VLD3DUPd32Pseudo
    0U,	// VLD3DUPd32Pseudo_UPD
    367852U,	// VLD3DUPd32_UPD
    6892U,	// VLD3DUPd8
    0U,	// VLD3DUPd8Pseudo
    0U,	// VLD3DUPd8Pseudo_UPD
    367852U,	// VLD3DUPd8_UPD
    0U,	// VLD3DUPdAsm_16
    0U,	// VLD3DUPdAsm_32
    0U,	// VLD3DUPdAsm_8
    4U,	// VLD3DUPdWB_fixed_Asm_16
    4U,	// VLD3DUPdWB_fixed_Asm_32
    4U,	// VLD3DUPdWB_fixed_Asm_8
    1184U,	// VLD3DUPdWB_register_Asm_16
    1184U,	// VLD3DUPdWB_register_Asm_32
    1184U,	// VLD3DUPdWB_register_Asm_8
    6892U,	// VLD3DUPq16
    367852U,	// VLD3DUPq16_UPD
    6892U,	// VLD3DUPq32
    367852U,	// VLD3DUPq32_UPD
    6892U,	// VLD3DUPq8
    367852U,	// VLD3DUPq8_UPD
    0U,	// VLD3DUPqAsm_16
    0U,	// VLD3DUPqAsm_32
    0U,	// VLD3DUPqAsm_8
    4U,	// VLD3DUPqWB_fixed_Asm_16
    4U,	// VLD3DUPqWB_fixed_Asm_32
    4U,	// VLD3DUPqWB_fixed_Asm_8
    1184U,	// VLD3DUPqWB_register_Asm_16
    1184U,	// VLD3DUPqWB_register_Asm_32
    1184U,	// VLD3DUPqWB_register_Asm_8
    383203U,	// VLD3LNd16
    0U,	// VLD3LNd16Pseudo
    0U,	// VLD3LNd16Pseudo_UPD
    398067U,	// VLD3LNd16_UPD
    383203U,	// VLD3LNd32
    0U,	// VLD3LNd32Pseudo
    0U,	// VLD3LNd32Pseudo_UPD
    398067U,	// VLD3LNd32_UPD
    383203U,	// VLD3LNd8
    0U,	// VLD3LNd8Pseudo
    0U,	// VLD3LNd8Pseudo_UPD
    398067U,	// VLD3LNd8_UPD
    1240U,	// VLD3LNdAsm_16
    1240U,	// VLD3LNdAsm_32
    1240U,	// VLD3LNdAsm_8
    5336U,	// VLD3LNdWB_fixed_Asm_16
    5336U,	// VLD3LNdWB_fixed_Asm_32
    5336U,	// VLD3LNdWB_fixed_Asm_8
    311512U,	// VLD3LNdWB_register_Asm_16
    311512U,	// VLD3LNdWB_register_Asm_32
    311512U,	// VLD3LNdWB_register_Asm_8
    383203U,	// VLD3LNq16
    0U,	// VLD3LNq16Pseudo
    0U,	// VLD3LNq16Pseudo_UPD
    398067U,	// VLD3LNq16_UPD
    383203U,	// VLD3LNq32
    0U,	// VLD3LNq32Pseudo
    0U,	// VLD3LNq32Pseudo_UPD
    398067U,	// VLD3LNq32_UPD
    1240U,	// VLD3LNqAsm_16
    1240U,	// VLD3LNqAsm_32
    5336U,	// VLD3LNqWB_fixed_Asm_16
    5336U,	// VLD3LNqWB_fixed_Asm_32
    311512U,	// VLD3LNqWB_register_Asm_16
    311512U,	// VLD3LNqWB_register_Asm_32
    58720256U,	// VLD3d16
    0U,	// VLD3d16Pseudo
    0U,	// VLD3d16Pseudo_UPD
    75497472U,	// VLD3d16_UPD
    58720256U,	// VLD3d32
    0U,	// VLD3d32Pseudo
    0U,	// VLD3d32Pseudo_UPD
    75497472U,	// VLD3d32_UPD
    58720256U,	// VLD3d8
    0U,	// VLD3d8Pseudo
    0U,	// VLD3d8Pseudo_UPD
    75497472U,	// VLD3d8_UPD
    59U,	// VLD3dAsm_16
    59U,	// VLD3dAsm_32
    59U,	// VLD3dAsm_8
    195U,	// VLD3dWB_fixed_Asm_16
    195U,	// VLD3dWB_fixed_Asm_32
    195U,	// VLD3dWB_fixed_Asm_8
    249379U,	// VLD3dWB_register_Asm_16
    249379U,	// VLD3dWB_register_Asm_32
    249379U,	// VLD3dWB_register_Asm_8
    58720256U,	// VLD3q16
    0U,	// VLD3q16Pseudo_UPD
    75497472U,	// VLD3q16_UPD
    0U,	// VLD3q16oddPseudo
    0U,	// VLD3q16oddPseudo_UPD
    58720256U,	// VLD3q32
    0U,	// VLD3q32Pseudo_UPD
    75497472U,	// VLD3q32_UPD
    0U,	// VLD3q32oddPseudo
    0U,	// VLD3q32oddPseudo_UPD
    58720256U,	// VLD3q8
    0U,	// VLD3q8Pseudo_UPD
    75497472U,	// VLD3q8_UPD
    0U,	// VLD3q8oddPseudo
    0U,	// VLD3q8oddPseudo_UPD
    0U,	// VLD3qAsm_16
    0U,	// VLD3qAsm_32
    0U,	// VLD3qAsm_8
    4U,	// VLD3qWB_fixed_Asm_16
    4U,	// VLD3qWB_fixed_Asm_32
    4U,	// VLD3qWB_fixed_Asm_8
    1184U,	// VLD3qWB_register_Asm_16
    1184U,	// VLD3qWB_register_Asm_32
    1184U,	// VLD3qWB_register_Asm_8
    253180U,	// VLD4DUPd16
    0U,	// VLD4DUPd16Pseudo
    0U,	// VLD4DUPd16Pseudo_UPD
    7932U,	// VLD4DUPd16_UPD
    253180U,	// VLD4DUPd32
    0U,	// VLD4DUPd32Pseudo
    0U,	// VLD4DUPd32Pseudo_UPD
    7932U,	// VLD4DUPd32_UPD
    253180U,	// VLD4DUPd8
    0U,	// VLD4DUPd8Pseudo
    0U,	// VLD4DUPd8Pseudo_UPD
    7932U,	// VLD4DUPd8_UPD
    0U,	// VLD4DUPdAsm_16
    0U,	// VLD4DUPdAsm_32
    0U,	// VLD4DUPdAsm_8
    4U,	// VLD4DUPdWB_fixed_Asm_16
    4U,	// VLD4DUPdWB_fixed_Asm_32
    4U,	// VLD4DUPdWB_fixed_Asm_8
    1184U,	// VLD4DUPdWB_register_Asm_16
    1184U,	// VLD4DUPdWB_register_Asm_32
    1184U,	// VLD4DUPdWB_register_Asm_8
    253180U,	// VLD4DUPq16
    7932U,	// VLD4DUPq16_UPD
    253180U,	// VLD4DUPq32
    7932U,	// VLD4DUPq32_UPD
    253180U,	// VLD4DUPq8
    7932U,	// VLD4DUPq8_UPD
    0U,	// VLD4DUPqAsm_16
    0U,	// VLD4DUPqAsm_32
    0U,	// VLD4DUPqAsm_8
    4U,	// VLD4DUPqWB_fixed_Asm_16
    4U,	// VLD4DUPqWB_fixed_Asm_32
    4U,	// VLD4DUPqWB_fixed_Asm_8
    1184U,	// VLD4DUPqWB_register_Asm_16
    1184U,	// VLD4DUPqWB_register_Asm_32
    1184U,	// VLD4DUPqWB_register_Asm_8
    93591283U,	// VLD4LNd16
    0U,	// VLD4LNd16Pseudo
    0U,	// VLD4LNd16Pseudo_UPD
    259U,	// VLD4LNd16_UPD
    93591283U,	// VLD4LNd32
    0U,	// VLD4LNd32Pseudo
    0U,	// VLD4LNd32Pseudo_UPD
    259U,	// VLD4LNd32_UPD
    93591283U,	// VLD4LNd8
    0U,	// VLD4LNd8Pseudo
    0U,	// VLD4LNd8Pseudo_UPD
    259U,	// VLD4LNd8_UPD
    1240U,	// VLD4LNdAsm_16
    1240U,	// VLD4LNdAsm_32
    1240U,	// VLD4LNdAsm_8
    5336U,	// VLD4LNdWB_fixed_Asm_16
    5336U,	// VLD4LNdWB_fixed_Asm_32
    5336U,	// VLD4LNdWB_fixed_Asm_8
    311512U,	// VLD4LNdWB_register_Asm_16
    311512U,	// VLD4LNdWB_register_Asm_32
    311512U,	// VLD4LNdWB_register_Asm_8
    93591283U,	// VLD4LNq16
    0U,	// VLD4LNq16Pseudo
    0U,	// VLD4LNq16Pseudo_UPD
    259U,	// VLD4LNq16_UPD
    93591283U,	// VLD4LNq32
    0U,	// VLD4LNq32Pseudo
    0U,	// VLD4LNq32Pseudo_UPD
    259U,	// VLD4LNq32_UPD
    1240U,	// VLD4LNqAsm_16
    1240U,	// VLD4LNqAsm_32
    5336U,	// VLD4LNqWB_fixed_Asm_16
    5336U,	// VLD4LNqWB_fixed_Asm_32
    311512U,	// VLD4LNqWB_register_Asm_16
    311512U,	// VLD4LNqWB_register_Asm_32
    286261248U,	// VLD4d16
    0U,	// VLD4d16Pseudo
    0U,	// VLD4d16Pseudo_UPD
    823132160U,	// VLD4d16_UPD
    286261248U,	// VLD4d32
    0U,	// VLD4d32Pseudo
    0U,	// VLD4d32Pseudo_UPD
    823132160U,	// VLD4d32_UPD
    286261248U,	// VLD4d8
    0U,	// VLD4d8Pseudo
    0U,	// VLD4d8Pseudo_UPD
    823132160U,	// VLD4d8_UPD
    59U,	// VLD4dAsm_16
    59U,	// VLD4dAsm_32
    59U,	// VLD4dAsm_8
    195U,	// VLD4dWB_fixed_Asm_16
    195U,	// VLD4dWB_fixed_Asm_32
    195U,	// VLD4dWB_fixed_Asm_8
    249379U,	// VLD4dWB_register_Asm_16
    249379U,	// VLD4dWB_register_Asm_32
    249379U,	// VLD4dWB_register_Asm_8
    286261248U,	// VLD4q16
    0U,	// VLD4q16Pseudo_UPD
    823132160U,	// VLD4q16_UPD
    0U,	// VLD4q16oddPseudo
    0U,	// VLD4q16oddPseudo_UPD
    286261248U,	// VLD4q32
    0U,	// VLD4q32Pseudo_UPD
    823132160U,	// VLD4q32_UPD
    0U,	// VLD4q32oddPseudo
    0U,	// VLD4q32oddPseudo_UPD
    286261248U,	// VLD4q8
    0U,	// VLD4q8Pseudo_UPD
    823132160U,	// VLD4q8_UPD
    0U,	// VLD4q8oddPseudo
    0U,	// VLD4q8oddPseudo_UPD
    0U,	// VLD4qAsm_16
    0U,	// VLD4qAsm_32
    0U,	// VLD4qAsm_8
    4U,	// VLD4qWB_fixed_Asm_16
    4U,	// VLD4qWB_fixed_Asm_32
    4U,	// VLD4qWB_fixed_Asm_8
    1184U,	// VLD4qWB_register_Asm_16
    1184U,	// VLD4qWB_register_Asm_32
    1184U,	// VLD4qWB_register_Asm_8
    57U,	// VLDMDDB_UPD
    1088U,	// VLDMDIA
    57U,	// VLDMDIA_UPD
    0U,	// VLDMQIA
    57U,	// VLDMSDB_UPD
    1088U,	// VLDMSIA
    57U,	// VLDMSIA_UPD
    264U,	// VLDRD
    264U,	// VLDRS
    1048U,	// VMAXNMD
    1048U,	// VMAXNMND
    1048U,	// VMAXNMNQ
    1048U,	// VMAXNMS
    247328U,	// VMAXfd
    247328U,	// VMAXfq
    1048U,	// VMAXsv16i8
    1048U,	// VMAXsv2i32
    1048U,	// VMAXsv4i16
    1048U,	// VMAXsv4i32
    1048U,	// VMAXsv8i16
    1048U,	// VMAXsv8i8
    1048U,	// VMAXuv16i8
    1048U,	// VMAXuv2i32
    1048U,	// VMAXuv4i16
    1048U,	// VMAXuv4i32
    1048U,	// VMAXuv8i16
    1048U,	// VMAXuv8i8
    1048U,	// VMINNMD
    1048U,	// VMINNMND
    1048U,	// VMINNMNQ
    1048U,	// VMINNMS
    247328U,	// VMINfd
    247328U,	// VMINfq
    1048U,	// VMINsv16i8
    1048U,	// VMINsv2i32
    1048U,	// VMINsv4i16
    1048U,	// VMINsv4i32
    1048U,	// VMINsv8i16
    1048U,	// VMINsv8i8
    1048U,	// VMINuv16i8
    1048U,	// VMINuv2i32
    1048U,	// VMINuv4i16
    1048U,	// VMINuv4i32
    1048U,	// VMINuv8i16
    1048U,	// VMINuv8i8
    249378U,	// VMLAD
    8352U,	// VMLALslsv2i32
    8352U,	// VMLALslsv4i16
    8352U,	// VMLALsluv2i32
    8352U,	// VMLALsluv4i16
    1184U,	// VMLALsv2i64
    1184U,	// VMLALsv4i32
    1184U,	// VMLALsv8i16
    1184U,	// VMLALuv2i64
    1184U,	// VMLALuv4i32
    1184U,	// VMLALuv8i16
    249378U,	// VMLAS
    249378U,	// VMLAfd
    249378U,	// VMLAfq
    413218U,	// VMLAslfd
    413218U,	// VMLAslfq
    8352U,	// VMLAslv2i32
    8352U,	// VMLAslv4i16
    8352U,	// VMLAslv4i32
    8352U,	// VMLAslv8i16
    1184U,	// VMLAv16i8
    1184U,	// VMLAv2i32
    1184U,	// VMLAv4i16
    1184U,	// VMLAv4i32
    1184U,	// VMLAv8i16
    1184U,	// VMLAv8i8
    249378U,	// VMLSD
    8352U,	// VMLSLslsv2i32
    8352U,	// VMLSLslsv4i16
    8352U,	// VMLSLsluv2i32
    8352U,	// VMLSLsluv4i16
    1184U,	// VMLSLsv2i64
    1184U,	// VMLSLsv4i32
    1184U,	// VMLSLsv8i16
    1184U,	// VMLSLuv2i64
    1184U,	// VMLSLuv4i32
    1184U,	// VMLSLuv8i16
    249378U,	// VMLSS
    249378U,	// VMLSfd
    249378U,	// VMLSfq
    413218U,	// VMLSslfd
    413218U,	// VMLSslfq
    8352U,	// VMLSslv2i32
    8352U,	// VMLSslv4i16
    8352U,	// VMLSslv4i32
    8352U,	// VMLSslv8i16
    1184U,	// VMLSv16i8
    1184U,	// VMLSv2i32
    1184U,	// VMLSv4i16
    1184U,	// VMLSv4i32
    1184U,	// VMLSv8i16
    1184U,	// VMLSv8i8
    56U,	// VMOVD
    0U,	// VMOVD0
    0U,	// VMOVDRR
    0U,	// VMOVDcc
    0U,	// VMOVLsv2i64
    0U,	// VMOVLsv4i32
    0U,	// VMOVLsv8i16
    0U,	// VMOVLuv2i64
    0U,	// VMOVLuv4i32
    0U,	// VMOVLuv8i16
    0U,	// VMOVNv2i32
    0U,	// VMOVNv4i16
    0U,	// VMOVNv8i8
    0U,	// VMOVQ0
    0U,	// VMOVRRD
    17825792U,	// VMOVRRS
    1024U,	// VMOVRS
    56U,	// VMOVS
    1024U,	// VMOVSR
    17825792U,	// VMOVSRR
    0U,	// VMOVScc
    0U,	// VMOVv16i8
    0U,	// VMOVv1i64
    0U,	// VMOVv2f32
    0U,	// VMOVv2i32
    0U,	// VMOVv2i64
    0U,	// VMOVv4f32
    0U,	// VMOVv4i16
    0U,	// VMOVv4i32
    0U,	// VMOVv8i16
    0U,	// VMOVv8i8
    4U,	// VMRS
    4U,	// VMRS_FPEXC
    5U,	// VMRS_FPINST
    5U,	// VMRS_FPINST2
    5U,	// VMRS_FPSID
    5U,	// VMRS_MVFR0
    6U,	// VMRS_MVFR1
    6U,	// VMRS_MVFR2
    0U,	// VMSR
    0U,	// VMSR_FPEXC
    0U,	// VMSR_FPINST
    0U,	// VMSR_FPINST2
    0U,	// VMSR_FPSID
    247328U,	// VMULD
    1048U,	// VMULLp64
    0U,	// VMULLp8
    8728U,	// VMULLslsv2i32
    8728U,	// VMULLslsv4i16
    8728U,	// VMULLsluv2i32
    8728U,	// VMULLsluv4i16
    1048U,	// VMULLsv2i64
    1048U,	// VMULLsv4i32
    1048U,	// VMULLsv8i16
    1048U,	// VMULLuv2i64
    1048U,	// VMULLuv4i32
    1048U,	// VMULLuv8i16
    247328U,	// VMULS
    247328U,	// VMULfd
    247328U,	// VMULfq
    0U,	// VMULpd
    0U,	// VMULpq
    427552U,	// VMULslfd
    427552U,	// VMULslfq
    8728U,	// VMULslv2i32
    8728U,	// VMULslv4i16
    8728U,	// VMULslv4i32
    8728U,	// VMULslv8i16
    1048U,	// VMULv16i8
    1048U,	// VMULv2i32
    1048U,	// VMULv4i16
    1048U,	// VMULv4i32
    1048U,	// VMULv8i16
    1048U,	// VMULv8i8
    1024U,	// VMVNd
    1024U,	// VMVNq
    0U,	// VMVNv2i32
    0U,	// VMVNv4i16
    0U,	// VMVNv4i32
    0U,	// VMVNv8i16
    56U,	// VNEGD
    56U,	// VNEGS
    56U,	// VNEGf32q
    56U,	// VNEGfd
    0U,	// VNEGs16d
    0U,	// VNEGs16q
    0U,	// VNEGs32d
    0U,	// VNEGs32q
    0U,	// VNEGs8d
    0U,	// VNEGs8q
    249378U,	// VNMLAD
    249378U,	// VNMLAS
    249378U,	// VNMLSD
    249378U,	// VNMLSS
    247328U,	// VNMULD
    247328U,	// VNMULS
    0U,	// VORNd
    0U,	// VORNq
    0U,	// VORRd
    0U,	// VORRiv2i32
    0U,	// VORRiv4i16
    0U,	// VORRiv4i32
    0U,	// VORRiv8i16
    0U,	// VORRq
    0U,	// VPADALsv16i8
    0U,	// VPADALsv2i32
    0U,	// VPADALsv4i16
    0U,	// VPADALsv4i32
    0U,	// VPADALsv8i16
    0U,	// VPADALsv8i8
    0U,	// VPADALuv16i8
    0U,	// VPADALuv2i32
    0U,	// VPADALuv4i16
    0U,	// VPADALuv4i32
    0U,	// VPADALuv8i16
    0U,	// VPADALuv8i8
    0U,	// VPADDLsv16i8
    0U,	// VPADDLsv2i32
    0U,	// VPADDLsv4i16
    0U,	// VPADDLsv4i32
    0U,	// VPADDLsv8i16
    0U,	// VPADDLsv8i8
    0U,	// VPADDLuv16i8
    0U,	// VPADDLuv2i32
    0U,	// VPADDLuv4i16
    0U,	// VPADDLuv4i32
    0U,	// VPADDLuv8i16
    0U,	// VPADDLuv8i8
    247328U,	// VPADDf
    1048U,	// VPADDi16
    1048U,	// VPADDi32
    1048U,	// VPADDi8
    247328U,	// VPMAXf
    1048U,	// VPMAXs16
    1048U,	// VPMAXs32
    1048U,	// VPMAXs8
    1048U,	// VPMAXu16
    1048U,	// VPMAXu32
    1048U,	// VPMAXu8
    247328U,	// VPMINf
    1048U,	// VPMINs16
    1048U,	// VPMINs32
    1048U,	// VPMINs8
    1048U,	// VPMINu16
    1048U,	// VPMINu32
    1048U,	// VPMINu8
    0U,	// VQABSv16i8
    0U,	// VQABSv2i32
    0U,	// VQABSv4i16
    0U,	// VQABSv4i32
    0U,	// VQABSv8i16
    0U,	// VQABSv8i8
    1048U,	// VQADDsv16i8
    1048U,	// VQADDsv1i64
    1048U,	// VQADDsv2i32
    1048U,	// VQADDsv2i64
    1048U,	// VQADDsv4i16
    1048U,	// VQADDsv4i32
    1048U,	// VQADDsv8i16
    1048U,	// VQADDsv8i8
    1048U,	// VQADDuv16i8
    1048U,	// VQADDuv1i64
    1048U,	// VQADDuv2i32
    1048U,	// VQADDuv2i64
    1048U,	// VQADDuv4i16
    1048U,	// VQADDuv4i32
    1048U,	// VQADDuv8i16
    1048U,	// VQADDuv8i8
    8352U,	// VQDMLALslv2i32
    8352U,	// VQDMLALslv4i16
    1184U,	// VQDMLALv2i64
    1184U,	// VQDMLALv4i32
    8352U,	// VQDMLSLslv2i32
    8352U,	// VQDMLSLslv4i16
    1184U,	// VQDMLSLv2i64
    1184U,	// VQDMLSLv4i32
    8728U,	// VQDMULHslv2i32
    8728U,	// VQDMULHslv4i16
    8728U,	// VQDMULHslv4i32
    8728U,	// VQDMULHslv8i16
    1048U,	// VQDMULHv2i32
    1048U,	// VQDMULHv4i16
    1048U,	// VQDMULHv4i32
    1048U,	// VQDMULHv8i16
    8728U,	// VQDMULLslv2i32
    8728U,	// VQDMULLslv4i16
    1048U,	// VQDMULLv2i64
    1048U,	// VQDMULLv4i32
    0U,	// VQMOVNsuv2i32
    0U,	// VQMOVNsuv4i16
    0U,	// VQMOVNsuv8i8
    0U,	// VQMOVNsv2i32
    0U,	// VQMOVNsv4i16
    0U,	// VQMOVNsv8i8
    0U,	// VQMOVNuv2i32
    0U,	// VQMOVNuv4i16
    0U,	// VQMOVNuv8i8
    0U,	// VQNEGv16i8
    0U,	// VQNEGv2i32
    0U,	// VQNEGv4i16
    0U,	// VQNEGv4i32
    0U,	// VQNEGv8i16
    0U,	// VQNEGv8i8
    8728U,	// VQRDMULHslv2i32
    8728U,	// VQRDMULHslv4i16
    8728U,	// VQRDMULHslv4i32
    8728U,	// VQRDMULHslv8i16
    1048U,	// VQRDMULHv2i32
    1048U,	// VQRDMULHv4i16
    1048U,	// VQRDMULHv4i32
    1048U,	// VQRDMULHv8i16
    1048U,	// VQRSHLsv16i8
    1048U,	// VQRSHLsv1i64
    1048U,	// VQRSHLsv2i32
    1048U,	// VQRSHLsv2i64
    1048U,	// VQRSHLsv4i16
    1048U,	// VQRSHLsv4i32
    1048U,	// VQRSHLsv8i16
    1048U,	// VQRSHLsv8i8
    1048U,	// VQRSHLuv16i8
    1048U,	// VQRSHLuv1i64
    1048U,	// VQRSHLuv2i32
    1048U,	// VQRSHLuv2i64
    1048U,	// VQRSHLuv4i16
    1048U,	// VQRSHLuv4i32
    1048U,	// VQRSHLuv8i16
    1048U,	// VQRSHLuv8i8
    1048U,	// VQRSHRNsv2i32
    1048U,	// VQRSHRNsv4i16
    1048U,	// VQRSHRNsv8i8
    1048U,	// VQRSHRNuv2i32
    1048U,	// VQRSHRNuv4i16
    1048U,	// VQRSHRNuv8i8
    1048U,	// VQRSHRUNv2i32
    1048U,	// VQRSHRUNv4i16
    1048U,	// VQRSHRUNv8i8
    1048U,	// VQSHLsiv16i8
    1048U,	// VQSHLsiv1i64
    1048U,	// VQSHLsiv2i32
    1048U,	// VQSHLsiv2i64
    1048U,	// VQSHLsiv4i16
    1048U,	// VQSHLsiv4i32
    1048U,	// VQSHLsiv8i16
    1048U,	// VQSHLsiv8i8
    1048U,	// VQSHLsuv16i8
    1048U,	// VQSHLsuv1i64
    1048U,	// VQSHLsuv2i32
    1048U,	// VQSHLsuv2i64
    1048U,	// VQSHLsuv4i16
    1048U,	// VQSHLsuv4i32
    1048U,	// VQSHLsuv8i16
    1048U,	// VQSHLsuv8i8
    1048U,	// VQSHLsv16i8
    1048U,	// VQSHLsv1i64
    1048U,	// VQSHLsv2i32
    1048U,	// VQSHLsv2i64
    1048U,	// VQSHLsv4i16
    1048U,	// VQSHLsv4i32
    1048U,	// VQSHLsv8i16
    1048U,	// VQSHLsv8i8
    1048U,	// VQSHLuiv16i8
    1048U,	// VQSHLuiv1i64
    1048U,	// VQSHLuiv2i32
    1048U,	// VQSHLuiv2i64
    1048U,	// VQSHLuiv4i16
    1048U,	// VQSHLuiv4i32
    1048U,	// VQSHLuiv8i16
    1048U,	// VQSHLuiv8i8
    1048U,	// VQSHLuv16i8
    1048U,	// VQSHLuv1i64
    1048U,	// VQSHLuv2i32
    1048U,	// VQSHLuv2i64
    1048U,	// VQSHLuv4i16
    1048U,	// VQSHLuv4i32
    1048U,	// VQSHLuv8i16
    1048U,	// VQSHLuv8i8
    1048U,	// VQSHRNsv2i32
    1048U,	// VQSHRNsv4i16
    1048U,	// VQSHRNsv8i8
    1048U,	// VQSHRNuv2i32
    1048U,	// VQSHRNuv4i16
    1048U,	// VQSHRNuv8i8
    1048U,	// VQSHRUNv2i32
    1048U,	// VQSHRUNv4i16
    1048U,	// VQSHRUNv8i8
    1048U,	// VQSUBsv16i8
    1048U,	// VQSUBsv1i64
    1048U,	// VQSUBsv2i32
    1048U,	// VQSUBsv2i64
    1048U,	// VQSUBsv4i16
    1048U,	// VQSUBsv4i32
    1048U,	// VQSUBsv8i16
    1048U,	// VQSUBsv8i8
    1048U,	// VQSUBuv16i8
    1048U,	// VQSUBuv1i64
    1048U,	// VQSUBuv2i32
    1048U,	// VQSUBuv2i64
    1048U,	// VQSUBuv4i16
    1048U,	// VQSUBuv4i32
    1048U,	// VQSUBuv8i16
    1048U,	// VQSUBuv8i8
    1048U,	// VRADDHNv2i32
    1048U,	// VRADDHNv4i16
    1048U,	// VRADDHNv8i8
    0U,	// VRECPEd
    56U,	// VRECPEfd
    56U,	// VRECPEfq
    0U,	// VRECPEq
    247328U,	// VRECPSfd
    247328U,	// VRECPSfq
    1024U,	// VREV16d8
    1024U,	// VREV16q8
    1024U,	// VREV32d16
    1024U,	// VREV32d8
    1024U,	// VREV32q16
    1024U,	// VREV32q8
    1024U,	// VREV64d16
    1024U,	// VREV64d32
    1024U,	// VREV64d8
    1024U,	// VREV64q16
    1024U,	// VREV64q32
    1024U,	// VREV64q8
    1048U,	// VRHADDsv16i8
    1048U,	// VRHADDsv2i32
    1048U,	// VRHADDsv4i16
    1048U,	// VRHADDsv4i32
    1048U,	// VRHADDsv8i16
    1048U,	// VRHADDsv8i8
    1048U,	// VRHADDuv16i8
    1048U,	// VRHADDuv2i32
    1048U,	// VRHADDuv4i16
    1048U,	// VRHADDuv4i32
    1048U,	// VRHADDuv8i16
    1048U,	// VRHADDuv8i8
    0U,	// VRINTAD
    0U,	// VRINTAND
    0U,	// VRINTANQ
    0U,	// VRINTAS
    0U,	// VRINTMD
    0U,	// VRINTMND
    0U,	// VRINTMNQ
    0U,	// VRINTMS
    0U,	// VRINTND
    0U,	// VRINTNND
    0U,	// VRINTNNQ
    0U,	// VRINTNS
    0U,	// VRINTPD
    0U,	// VRINTPND
    0U,	// VRINTPNQ
    0U,	// VRINTPS
    56U,	// VRINTRD
    56U,	// VRINTRS
    56U,	// VRINTXD
    0U,	// VRINTXND
    0U,	// VRINTXNQ
    56U,	// VRINTXS
    56U,	// VRINTZD
    0U,	// VRINTZND
    0U,	// VRINTZNQ
    56U,	// VRINTZS
    1048U,	// VRSHLsv16i8
    1048U,	// VRSHLsv1i64
    1048U,	// VRSHLsv2i32
    1048U,	// VRSHLsv2i64
    1048U,	// VRSHLsv4i16
    1048U,	// VRSHLsv4i32
    1048U,	// VRSHLsv8i16
    1048U,	// VRSHLsv8i8
    1048U,	// VRSHLuv16i8
    1048U,	// VRSHLuv1i64
    1048U,	// VRSHLuv2i32
    1048U,	// VRSHLuv2i64
    1048U,	// VRSHLuv4i16
    1048U,	// VRSHLuv4i32
    1048U,	// VRSHLuv8i16
    1048U,	// VRSHLuv8i8
    1048U,	// VRSHRNv2i32
    1048U,	// VRSHRNv4i16
    1048U,	// VRSHRNv8i8
    1048U,	// VRSHRsv16i8
    1048U,	// VRSHRsv1i64
    1048U,	// VRSHRsv2i32
    1048U,	// VRSHRsv2i64
    1048U,	// VRSHRsv4i16
    1048U,	// VRSHRsv4i32
    1048U,	// VRSHRsv8i16
    1048U,	// VRSHRsv8i8
    1048U,	// VRSHRuv16i8
    1048U,	// VRSHRuv1i64
    1048U,	// VRSHRuv2i32
    1048U,	// VRSHRuv2i64
    1048U,	// VRSHRuv4i16
    1048U,	// VRSHRuv4i32
    1048U,	// VRSHRuv8i16
    1048U,	// VRSHRuv8i8
    0U,	// VRSQRTEd
    56U,	// VRSQRTEfd
    56U,	// VRSQRTEfq
    0U,	// VRSQRTEq
    247328U,	// VRSQRTSfd
    247328U,	// VRSQRTSfq
    1184U,	// VRSRAsv16i8
    1184U,	// VRSRAsv1i64
    1184U,	// VRSRAsv2i32
    1184U,	// VRSRAsv2i64
    1184U,	// VRSRAsv4i16
    1184U,	// VRSRAsv4i32
    1184U,	// VRSRAsv8i16
    1184U,	// VRSRAsv8i8
    1184U,	// VRSRAuv16i8
    1184U,	// VRSRAuv1i64
    1184U,	// VRSRAuv2i32
    1184U,	// VRSRAuv2i64
    1184U,	// VRSRAuv4i16
    1184U,	// VRSRAuv4i32
    1184U,	// VRSRAuv8i16
    1184U,	// VRSRAuv8i8
    1048U,	// VRSUBHNv2i32
    1048U,	// VRSUBHNv4i16
    1048U,	// VRSUBHNv8i8
    1048U,	// VSELEQD
    1048U,	// VSELEQS
    1048U,	// VSELGED
    1048U,	// VSELGES
    1048U,	// VSELGTD
    1048U,	// VSELGTS
    1048U,	// VSELVSD
    1048U,	// VSELVSS
    6U,	// VSETLNi16
    6U,	// VSETLNi32
    6U,	// VSETLNi8
    1048U,	// VSHLLi16
    1048U,	// VSHLLi32
    1048U,	// VSHLLi8
    1048U,	// VSHLLsv2i64
    1048U,	// VSHLLsv4i32
    1048U,	// VSHLLsv8i16
    1048U,	// VSHLLuv2i64
    1048U,	// VSHLLuv4i32
    1048U,	// VSHLLuv8i16
    1048U,	// VSHLiv16i8
    1048U,	// VSHLiv1i64
    1048U,	// VSHLiv2i32
    1048U,	// VSHLiv2i64
    1048U,	// VSHLiv4i16
    1048U,	// VSHLiv4i32
    1048U,	// VSHLiv8i16
    1048U,	// VSHLiv8i8
    1048U,	// VSHLsv16i8
    1048U,	// VSHLsv1i64
    1048U,	// VSHLsv2i32
    1048U,	// VSHLsv2i64
    1048U,	// VSHLsv4i16
    1048U,	// VSHLsv4i32
    1048U,	// VSHLsv8i16
    1048U,	// VSHLsv8i8
    1048U,	// VSHLuv16i8
    1048U,	// VSHLuv1i64
    1048U,	// VSHLuv2i32
    1048U,	// VSHLuv2i64
    1048U,	// VSHLuv4i16
    1048U,	// VSHLuv4i32
    1048U,	// VSHLuv8i16
    1048U,	// VSHLuv8i8
    1048U,	// VSHRNv2i32
    1048U,	// VSHRNv4i16
    1048U,	// VSHRNv8i8
    1048U,	// VSHRsv16i8
    1048U,	// VSHRsv1i64
    1048U,	// VSHRsv2i32
    1048U,	// VSHRsv2i64
    1048U,	// VSHRsv4i16
    1048U,	// VSHRsv4i32
    1048U,	// VSHRsv8i16
    1048U,	// VSHRsv8i8
    1048U,	// VSHRuv16i8
    1048U,	// VSHRuv1i64
    1048U,	// VSHRuv2i32
    1048U,	// VSHRuv2i64
    1048U,	// VSHRuv4i16
    1048U,	// VSHRuv4i32
    1048U,	// VSHRuv8i16
    1048U,	// VSHRuv8i8
    0U,	// VSHTOD
    0U,	// VSHTOS
    0U,	// VSITOD
    0U,	// VSITOS
    262168U,	// VSLIv16i8
    262168U,	// VSLIv1i64
    262168U,	// VSLIv2i32
    262168U,	// VSLIv2i64
    262168U,	// VSLIv4i16
    262168U,	// VSLIv4i32
    262168U,	// VSLIv8i16
    262168U,	// VSLIv8i8
    6U,	// VSLTOD
    6U,	// VSLTOS
    56U,	// VSQRTD
    56U,	// VSQRTS
    1184U,	// VSRAsv16i8
    1184U,	// VSRAsv1i64
    1184U,	// VSRAsv2i32
    1184U,	// VSRAsv2i64
    1184U,	// VSRAsv4i16
    1184U,	// VSRAsv4i32
    1184U,	// VSRAsv8i16
    1184U,	// VSRAsv8i8
    1184U,	// VSRAuv16i8
    1184U,	// VSRAuv1i64
    1184U,	// VSRAuv2i32
    1184U,	// VSRAuv2i64
    1184U,	// VSRAuv4i16
    1184U,	// VSRAuv4i32
    1184U,	// VSRAuv8i16
    1184U,	// VSRAuv8i8
    262168U,	// VSRIv16i8
    262168U,	// VSRIv1i64
    262168U,	// VSRIv2i32
    262168U,	// VSRIv2i64
    262168U,	// VSRIv4i16
    262168U,	// VSRIv4i32
    262168U,	// VSRIv8i16
    262168U,	// VSRIv8i8
    275U,	// VST1LNd16
    10769179U,	// VST1LNd16_UPD
    275U,	// VST1LNd32
    10769179U,	// VST1LNd32_UPD
    275U,	// VST1LNd8
    10769179U,	// VST1LNd8_UPD
    1240U,	// VST1LNdAsm_16
    1240U,	// VST1LNdAsm_32
    1240U,	// VST1LNdAsm_8
    5336U,	// VST1LNdWB_fixed_Asm_16
    5336U,	// VST1LNdWB_fixed_Asm_32
    5336U,	// VST1LNdWB_fixed_Asm_8
    311512U,	// VST1LNdWB_register_Asm_16
    311512U,	// VST1LNdWB_register_Asm_32
    311512U,	// VST1LNdWB_register_Asm_8
    0U,	// VST1LNq16Pseudo
    0U,	// VST1LNq16Pseudo_UPD
    0U,	// VST1LNq32Pseudo
    0U,	// VST1LNq32Pseudo_UPD
    0U,	// VST1LNq8Pseudo
    0U,	// VST1LNq8Pseudo_UPD
    0U,	// VST1d16
    0U,	// VST1d16Q
    0U,	// VST1d16Qwb_fixed
    0U,	// VST1d16Qwb_register
    0U,	// VST1d16T
    0U,	// VST1d16Twb_fixed
    0U,	// VST1d16Twb_register
    0U,	// VST1d16wb_fixed
    0U,	// VST1d16wb_register
    0U,	// VST1d32
    0U,	// VST1d32Q
    0U,	// VST1d32Qwb_fixed
    0U,	// VST1d32Qwb_register
    0U,	// VST1d32T
    0U,	// VST1d32Twb_fixed
    0U,	// VST1d32Twb_register
    0U,	// VST1d32wb_fixed
    0U,	// VST1d32wb_register
    0U,	// VST1d64
    0U,	// VST1d64Q
    0U,	// VST1d64QPseudo
    0U,	// VST1d64QPseudoWB_fixed
    0U,	// VST1d64QPseudoWB_register
    0U,	// VST1d64Qwb_fixed
    0U,	// VST1d64Qwb_register
    0U,	// VST1d64T
    0U,	// VST1d64TPseudo
    0U,	// VST1d64TPseudoWB_fixed
    0U,	// VST1d64TPseudoWB_register
    0U,	// VST1d64Twb_fixed
    0U,	// VST1d64Twb_register
    0U,	// VST1d64wb_fixed
    0U,	// VST1d64wb_register
    0U,	// VST1d8
    0U,	// VST1d8Q
    0U,	// VST1d8Qwb_fixed
    0U,	// VST1d8Qwb_register
    0U,	// VST1d8T
    0U,	// VST1d8Twb_fixed
    0U,	// VST1d8Twb_register
    0U,	// VST1d8wb_fixed
    0U,	// VST1d8wb_register
    0U,	// VST1q16
    0U,	// VST1q16wb_fixed
    0U,	// VST1q16wb_register
    0U,	// VST1q32
    0U,	// VST1q32wb_fixed
    0U,	// VST1q32wb_register
    0U,	// VST1q64
    0U,	// VST1q64wb_fixed
    0U,	// VST1q64wb_register
    0U,	// VST1q8
    0U,	// VST1q8wb_fixed
    0U,	// VST1q8wb_register
    110368459U,	// VST2LNd16
    0U,	// VST2LNd16Pseudo
    0U,	// VST2LNd16Pseudo_UPD
    448211U,	// VST2LNd16_UPD
    110368459U,	// VST2LNd32
    0U,	// VST2LNd32Pseudo
    0U,	// VST2LNd32Pseudo_UPD
    448211U,	// VST2LNd32_UPD
    110368459U,	// VST2LNd8
    0U,	// VST2LNd8Pseudo
    0U,	// VST2LNd8Pseudo_UPD
    448211U,	// VST2LNd8_UPD
    1240U,	// VST2LNdAsm_16
    1240U,	// VST2LNdAsm_32
    1240U,	// VST2LNdAsm_8
    5336U,	// VST2LNdWB_fixed_Asm_16
    5336U,	// VST2LNdWB_fixed_Asm_32
    5336U,	// VST2LNdWB_fixed_Asm_8
    311512U,	// VST2LNdWB_register_Asm_16
    311512U,	// VST2LNdWB_register_Asm_32
    311512U,	// VST2LNdWB_register_Asm_8
    110368459U,	// VST2LNq16
    0U,	// VST2LNq16Pseudo
    0U,	// VST2LNq16Pseudo_UPD
    448211U,	// VST2LNq16_UPD
    110368459U,	// VST2LNq32
    0U,	// VST2LNq32Pseudo
    0U,	// VST2LNq32Pseudo_UPD
    448211U,	// VST2LNq32_UPD
    1240U,	// VST2LNqAsm_16
    1240U,	// VST2LNqAsm_32
    5336U,	// VST2LNqWB_fixed_Asm_16
    5336U,	// VST2LNqWB_fixed_Asm_32
    311512U,	// VST2LNqWB_register_Asm_16
    311512U,	// VST2LNqWB_register_Asm_32
    0U,	// VST2b16
    0U,	// VST2b16wb_fixed
    0U,	// VST2b16wb_register
    0U,	// VST2b32
    0U,	// VST2b32wb_fixed
    0U,	// VST2b32wb_register
    0U,	// VST2b8
    0U,	// VST2b8wb_fixed
    0U,	// VST2b8wb_register
    0U,	// VST2d16
    0U,	// VST2d16wb_fixed
    0U,	// VST2d16wb_register
    0U,	// VST2d32
    0U,	// VST2d32wb_fixed
    0U,	// VST2d32wb_register
    0U,	// VST2d8
    0U,	// VST2d8wb_fixed
    0U,	// VST2d8wb_register
    0U,	// VST2q16
    0U,	// VST2q16Pseudo
    0U,	// VST2q16PseudoWB_fixed
    0U,	// VST2q16PseudoWB_register
    0U,	// VST2q16wb_fixed
    0U,	// VST2q16wb_register
    0U,	// VST2q32
    0U,	// VST2q32Pseudo
    0U,	// VST2q32PseudoWB_fixed
    0U,	// VST2q32PseudoWB_register
    0U,	// VST2q32wb_fixed
    0U,	// VST2q32wb_register
    0U,	// VST2q8
    0U,	// VST2q8Pseudo
    0U,	// VST2q8PseudoWB_fixed
    0U,	// VST2q8PseudoWB_register
    0U,	// VST2q8wb_fixed
    0U,	// VST2q8wb_register
    127145755U,	// VST3LNd16
    0U,	// VST3LNd16Pseudo
    0U,	// VST3LNd16Pseudo_UPD
    291U,	// VST3LNd16_UPD
    127145755U,	// VST3LNd32
    0U,	// VST3LNd32Pseudo
    0U,	// VST3LNd32Pseudo_UPD
    291U,	// VST3LNd32_UPD
    127145755U,	// VST3LNd8
    0U,	// VST3LNd8Pseudo
    0U,	// VST3LNd8Pseudo_UPD
    291U,	// VST3LNd8_UPD
    1240U,	// VST3LNdAsm_16
    1240U,	// VST3LNdAsm_32
    1240U,	// VST3LNdAsm_8
    5336U,	// VST3LNdWB_fixed_Asm_16
    5336U,	// VST3LNdWB_fixed_Asm_32
    5336U,	// VST3LNdWB_fixed_Asm_8
    311512U,	// VST3LNdWB_register_Asm_16
    311512U,	// VST3LNdWB_register_Asm_32
    311512U,	// VST3LNdWB_register_Asm_8
    127145755U,	// VST3LNq16
    0U,	// VST3LNq16Pseudo
    0U,	// VST3LNq16Pseudo_UPD
    291U,	// VST3LNq16_UPD
    127145755U,	// VST3LNq32
    0U,	// VST3LNq32Pseudo
    0U,	// VST3LNq32Pseudo_UPD
    291U,	// VST3LNq32_UPD
    1240U,	// VST3LNqAsm_16
    1240U,	// VST3LNqAsm_32
    5336U,	// VST3LNqWB_fixed_Asm_16
    5336U,	// VST3LNqWB_fixed_Asm_32
    311512U,	// VST3LNqWB_register_Asm_16
    311512U,	// VST3LNqWB_register_Asm_32
    142917792U,	// VST3d16
    0U,	// VST3d16Pseudo
    0U,	// VST3d16Pseudo_UPD
    9512U,	// VST3d16_UPD
    142917792U,	// VST3d32
    0U,	// VST3d32Pseudo
    0U,	// VST3d32Pseudo_UPD
    9512U,	// VST3d32_UPD
    142917792U,	// VST3d8
    0U,	// VST3d8Pseudo
    0U,	// VST3d8Pseudo_UPD
    9512U,	// VST3d8_UPD
    59U,	// VST3dAsm_16
    59U,	// VST3dAsm_32
    59U,	// VST3dAsm_8
    195U,	// VST3dWB_fixed_Asm_16
    195U,	// VST3dWB_fixed_Asm_32
    195U,	// VST3dWB_fixed_Asm_8
    249379U,	// VST3dWB_register_Asm_16
    249379U,	// VST3dWB_register_Asm_32
    249379U,	// VST3dWB_register_Asm_8
    142917792U,	// VST3q16
    0U,	// VST3q16Pseudo_UPD
    9512U,	// VST3q16_UPD
    0U,	// VST3q16oddPseudo
    0U,	// VST3q16oddPseudo_UPD
    142917792U,	// VST3q32
    0U,	// VST3q32Pseudo_UPD
    9512U,	// VST3q32_UPD
    0U,	// VST3q32oddPseudo
    0U,	// VST3q32oddPseudo_UPD
    142917792U,	// VST3q8
    0U,	// VST3q8Pseudo_UPD
    9512U,	// VST3q8_UPD
    0U,	// VST3q8oddPseudo
    0U,	// VST3q8oddPseudo_UPD
    0U,	// VST3qAsm_16
    0U,	// VST3qAsm_32
    0U,	// VST3qAsm_8
    4U,	// VST3qWB_fixed_Asm_16
    4U,	// VST3qWB_fixed_Asm_32
    4U,	// VST3qWB_fixed_Asm_8
    1184U,	// VST3qWB_register_Asm_16
    1184U,	// VST3qWB_register_Asm_32
    1184U,	// VST3qWB_register_Asm_8
    160700115U,	// VST4LNd16
    0U,	// VST4LNd16Pseudo
    0U,	// VST4LNd16Pseudo_UPD
    9955U,	// VST4LNd16_UPD
    160700115U,	// VST4LNd32
    0U,	// VST4LNd32Pseudo
    0U,	// VST4LNd32Pseudo_UPD
    9955U,	// VST4LNd32_UPD
    160700115U,	// VST4LNd8
    0U,	// VST4LNd8Pseudo
    0U,	// VST4LNd8Pseudo_UPD
    9955U,	// VST4LNd8_UPD
    1240U,	// VST4LNdAsm_16
    1240U,	// VST4LNdAsm_32
    1240U,	// VST4LNdAsm_8
    5336U,	// VST4LNdWB_fixed_Asm_16
    5336U,	// VST4LNdWB_fixed_Asm_32
    5336U,	// VST4LNdWB_fixed_Asm_8
    311512U,	// VST4LNdWB_register_Asm_16
    311512U,	// VST4LNdWB_register_Asm_32
    311512U,	// VST4LNdWB_register_Asm_8
    160700115U,	// VST4LNq16
    0U,	// VST4LNq16Pseudo
    0U,	// VST4LNq16Pseudo_UPD
    9955U,	// VST4LNq16_UPD
    160700115U,	// VST4LNq32
    0U,	// VST4LNq32Pseudo
    0U,	// VST4LNq32Pseudo_UPD
    9955U,	// VST4LNq32_UPD
    1240U,	// VST4LNqAsm_16
    1240U,	// VST4LNqAsm_32
    5336U,	// VST4LNqWB_fixed_Asm_16
    5336U,	// VST4LNqWB_fixed_Asm_32
    311512U,	// VST4LNqWB_register_Asm_16
    311512U,	// VST4LNqWB_register_Asm_32
    169132192U,	// VST4d16
    0U,	// VST4d16Pseudo
    0U,	// VST4d16Pseudo_UPD
    459048U,	// VST4d16_UPD
    169132192U,	// VST4d32
    0U,	// VST4d32Pseudo
    0U,	// VST4d32Pseudo_UPD
    459048U,	// VST4d32_UPD
    169132192U,	// VST4d8
    0U,	// VST4d8Pseudo
    0U,	// VST4d8Pseudo_UPD
    459048U,	// VST4d8_UPD
    59U,	// VST4dAsm_16
    59U,	// VST4dAsm_32
    59U,	// VST4dAsm_8
    195U,	// VST4dWB_fixed_Asm_16
    195U,	// VST4dWB_fixed_Asm_32
    195U,	// VST4dWB_fixed_Asm_8
    249379U,	// VST4dWB_register_Asm_16
    249379U,	// VST4dWB_register_Asm_32
    249379U,	// VST4dWB_register_Asm_8
    169132192U,	// VST4q16
    0U,	// VST4q16Pseudo_UPD
    459048U,	// VST4q16_UPD
    0U,	// VST4q16oddPseudo
    0U,	// VST4q16oddPseudo_UPD
    169132192U,	// VST4q32
    0U,	// VST4q32Pseudo_UPD
    459048U,	// VST4q32_UPD
    0U,	// VST4q32oddPseudo
    0U,	// VST4q32oddPseudo_UPD
    169132192U,	// VST4q8
    0U,	// VST4q8Pseudo_UPD
    459048U,	// VST4q8_UPD
    0U,	// VST4q8oddPseudo
    0U,	// VST4q8oddPseudo_UPD
    0U,	// VST4qAsm_16
    0U,	// VST4qAsm_32
    0U,	// VST4qAsm_8
    4U,	// VST4qWB_fixed_Asm_16
    4U,	// VST4qWB_fixed_Asm_32
    4U,	// VST4qWB_fixed_Asm_8
    1184U,	// VST4qWB_register_Asm_16
    1184U,	// VST4qWB_register_Asm_32
    1184U,	// VST4qWB_register_Asm_8
    57U,	// VSTMDDB_UPD
    1088U,	// VSTMDIA
    57U,	// VSTMDIA_UPD
    0U,	// VSTMQIA
    57U,	// VSTMSDB_UPD
    1088U,	// VSTMSIA
    57U,	// VSTMSIA_UPD
    264U,	// VSTRD
    264U,	// VSTRS
    247328U,	// VSUBD
    1048U,	// VSUBHNv2i32
    1048U,	// VSUBHNv4i16
    1048U,	// VSUBHNv8i8
    1048U,	// VSUBLsv2i64
    1048U,	// VSUBLsv4i32
    1048U,	// VSUBLsv8i16
    1048U,	// VSUBLuv2i64
    1048U,	// VSUBLuv4i32
    1048U,	// VSUBLuv8i16
    247328U,	// VSUBS
    1048U,	// VSUBWsv2i64
    1048U,	// VSUBWsv4i32
    1048U,	// VSUBWsv8i16
    1048U,	// VSUBWuv2i64
    1048U,	// VSUBWuv4i32
    1048U,	// VSUBWuv8i16
    247328U,	// VSUBfd
    247328U,	// VSUBfq
    1048U,	// VSUBv16i8
    1048U,	// VSUBv1i64
    1048U,	// VSUBv2i32
    1048U,	// VSUBv2i64
    1048U,	// VSUBv4i16
    1048U,	// VSUBv4i32
    1048U,	// VSUBv8i16
    1048U,	// VSUBv8i8
    1024U,	// VSWPd
    1024U,	// VSWPq
    304U,	// VTBL1
    312U,	// VTBL2
    320U,	// VTBL3
    0U,	// VTBL3Pseudo
    328U,	// VTBL4
    0U,	// VTBL4Pseudo
    336U,	// VTBX1
    344U,	// VTBX2
    352U,	// VTBX3
    0U,	// VTBX3Pseudo
    360U,	// VTBX4
    0U,	// VTBX4Pseudo
    0U,	// VTOSHD
    0U,	// VTOSHS
    0U,	// VTOSIRD
    0U,	// VTOSIRS
    0U,	// VTOSIZD
    0U,	// VTOSIZS
    6U,	// VTOSLD
    6U,	// VTOSLS
    0U,	// VTOUHD
    0U,	// VTOUHS
    0U,	// VTOUIRD
    0U,	// VTOUIRS
    0U,	// VTOUIZD
    0U,	// VTOUIZS
    6U,	// VTOULD
    6U,	// VTOULS
    1024U,	// VTRNd16
    1024U,	// VTRNd32
    1024U,	// VTRNd8
    1024U,	// VTRNq16
    1024U,	// VTRNq32
    1024U,	// VTRNq8
    0U,	// VTSTv16i8
    0U,	// VTSTv2i32
    0U,	// VTSTv4i16
    0U,	// VTSTv4i32
    0U,	// VTSTv8i16
    0U,	// VTSTv8i8
    0U,	// VUHTOD
    0U,	// VUHTOS
    0U,	// VUITOD
    0U,	// VUITOS
    6U,	// VULTOD
    6U,	// VULTOS
    1024U,	// VUZPd16
    1024U,	// VUZPd8
    1024U,	// VUZPq16
    1024U,	// VUZPq32
    1024U,	// VUZPq8
    1024U,	// VZIPd16
    1024U,	// VZIPd8
    1024U,	// VZIPq16
    1024U,	// VZIPq32
    1024U,	// VZIPq8
    0U,	// WIN__CHKSTK
    10304U,	// sysLDMDA
    369U,	// sysLDMDA_UPD
    10304U,	// sysLDMDB
    369U,	// sysLDMDB_UPD
    10304U,	// sysLDMIA
    369U,	// sysLDMIA_UPD
    10304U,	// sysLDMIB
    369U,	// sysLDMIB_UPD
    10304U,	// sysSTMDA
    369U,	// sysSTMDA_UPD
    10304U,	// sysSTMDB
    369U,	// sysSTMDB_UPD
    10304U,	// sysSTMIA
    369U,	// sysSTMIA_UPD
    10304U,	// sysSTMIB
    369U,	// sysSTMIB_UPD
    0U,	// t2ABS
    0U,	// t2ADCri
    0U,	// t2ADCrr
    475136U,	// t2ADCrs
    0U,	// t2ADDSri
    0U,	// t2ADDSrr
    0U,	// t2ADDSrs
    0U,	// t2ADDri
    0U,	// t2ADDri12
    0U,	// t2ADDrr
    475136U,	// t2ADDrs
    8U,	// t2ADR
    0U,	// t2ANDri
    0U,	// t2ANDrr
    475136U,	// t2ANDrs
    491520U,	// t2ASRri
    0U,	// t2ASRrr
    0U,	// t2B
    16U,	// t2BFC
    32792U,	// t2BFI
    0U,	// t2BICri
    0U,	// t2BICrr
    475136U,	// t2BICrs
    0U,	// t2BR_JT
    0U,	// t2BXJ
    0U,	// t2Bcc
    544U,	// t2CDP
    544U,	// t2CDP2
    0U,	// t2CLREX
    1024U,	// t2CLZ
    1024U,	// t2CMNri
    1024U,	// t2CMNzrr
    376U,	// t2CMNzrs
    1024U,	// t2CMPri
    1024U,	// t2CMPrr
    376U,	// t2CMPrs
    0U,	// t2CPS1p
    0U,	// t2CPS2p
    1048U,	// t2CPS3p
    1048U,	// t2CRC32B
    1048U,	// t2CRC32CB
    1048U,	// t2CRC32CH
    1048U,	// t2CRC32CW
    1048U,	// t2CRC32H
    1048U,	// t2CRC32W
    0U,	// t2DBG
    0U,	// t2DCPS1
    0U,	// t2DCPS2
    0U,	// t2DCPS3
    0U,	// t2DMB
    0U,	// t2DSB
    0U,	// t2EORri
    0U,	// t2EORrr
    475136U,	// t2EORrs
    0U,	// t2HINT
    0U,	// t2ISB
    0U,	// t2IT
    0U,	// t2Int_eh_sjlj_setjmp
    0U,	// t2Int_eh_sjlj_setjmp_nofp
    72U,	// t2LDA
    72U,	// t2LDAB
    72U,	// t2LDAEX
    72U,	// t2LDAEXB
    229376U,	// t2LDAEXD
    72U,	// t2LDAEXH
    72U,	// t2LDAH
    81U,	// t2LDC2L_OFFSET
    49241U,	// t2LDC2L_OPTION
    65625U,	// t2LDC2L_POST
    97U,	// t2LDC2L_PRE
    81U,	// t2LDC2_OFFSET
    49241U,	// t2LDC2_OPTION
    65625U,	// t2LDC2_POST
    97U,	// t2LDC2_PRE
    81U,	// t2LDCL_OFFSET
    49241U,	// t2LDCL_OPTION
    65625U,	// t2LDCL_POST
    97U,	// t2LDCL_PRE
    81U,	// t2LDC_OFFSET
    49241U,	// t2LDC_OPTION
    65625U,	// t2LDC_POST
    97U,	// t2LDC_PRE
    1088U,	// t2LDMDB
    57U,	// t2LDMDB_UPD
    1088U,	// t2LDMIA
    0U,	// t2LDMIA_RET
    57U,	// t2LDMIA_UPD
    384U,	// t2LDRBT
    10840U,	// t2LDRB_POST
    392U,	// t2LDRB_PRE
    120U,	// t2LDRBi12
    384U,	// t2LDRBi8
    400U,	// t2LDRBpci
    1024U,	// t2LDRBpcrel
    408U,	// t2LDRBs
    11649024U,	// t2LDRD_POST
    507904U,	// t2LDRD_PRE
    524288U,	// t2LDRDi8
    416U,	// t2LDREX
    72U,	// t2LDREXB
    229376U,	// t2LDREXD
    72U,	// t2LDREXH
    384U,	// t2LDRHT
    10840U,	// t2LDRH_POST
    392U,	// t2LDRH_PRE
    120U,	// t2LDRHi12
    384U,	// t2LDRHi8
    400U,	// t2LDRHpci
    1024U,	// t2LDRHpcrel
    408U,	// t2LDRHs
    384U,	// t2LDRSBT
    10840U,	// t2LDRSB_POST
    392U,	// t2LDRSB_PRE
    120U,	// t2LDRSBi12
    384U,	// t2LDRSBi8
    400U,	// t2LDRSBpci
    1024U,	// t2LDRSBpcrel
    408U,	// t2LDRSBs
    384U,	// t2LDRSHT
    10840U,	// t2LDRSH_POST
    392U,	// t2LDRSH_PRE
    120U,	// t2LDRSHi12
    384U,	// t2LDRSHi8
    400U,	// t2LDRSHpci
    1024U,	// t2LDRSHpcrel
    408U,	// t2LDRSHs
    384U,	// t2LDRT
    10840U,	// t2LDR_POST
    392U,	// t2LDR_PRE
    120U,	// t2LDRi12
    384U,	// t2LDRi8
    400U,	// t2LDRpci
    0U,	// t2LDRpci_pic
    1024U,	// t2LDRpcrel
    408U,	// t2LDRs
    0U,	// t2LEApcrel
    0U,	// t2LEApcrelJT
    0U,	// t2LSLri
    0U,	// t2LSLrr
    491520U,	// t2LSRri
    0U,	// t2LSRrr
    2295328U,	// t2MCR
    2295328U,	// t2MCR2
    3343904U,	// t2MCRR
    3343904U,	// t2MCRR2
    17825792U,	// t2MLA
    17825792U,	// t2MLS
    0U,	// t2MOVCCasr
    0U,	// t2MOVCCi
    0U,	// t2MOVCCi16
    0U,	// t2MOVCCi32imm
    0U,	// t2MOVCClsl
    0U,	// t2MOVCClsr
    0U,	// t2MOVCCr
    0U,	// t2MOVCCror
    376U,	// t2MOVSsi
    48U,	// t2MOVSsr
    1048U,	// t2MOVTi16
    0U,	// t2MOVTi16_ga_pcrel
    0U,	// t2MOV_ga_pcrel
    1024U,	// t2MOVi
    1024U,	// t2MOVi16
    0U,	// t2MOVi16_ga_pcrel
    0U,	// t2MOVi32imm
    1024U,	// t2MOVr
    376U,	// t2MOVsi
    48U,	// t2MOVsr
    11264U,	// t2MOVsra_flag
    11264U,	// t2MOVsrl_flag
    0U,	// t2MRC
    0U,	// t2MRC2
    3343904U,	// t2MRRC
    3343904U,	// t2MRRC2
    2U,	// t2MRS_AR
    424U,	// t2MRS_M
    2U,	// t2MRSsys_AR
    0U,	// t2MSR_AR
    0U,	// t2MSR_M
    0U,	// t2MUL
    0U,	// t2MVNCCi
    1024U,	// t2MVNi
    1024U,	// t2MVNr
    376U,	// t2MVNs
    0U,	// t2ORNri
    0U,	// t2ORNrr
    475136U,	// t2ORNrs
    0U,	// t2ORRri
    0U,	// t2ORRrr
    475136U,	// t2ORRrs
    4194304U,	// t2PKHBT
    5242880U,	// t2PKHTB
    0U,	// t2PLDWi12
    0U,	// t2PLDWi8
    0U,	// t2PLDWs
    0U,	// t2PLDi12
    0U,	// t2PLDi8
    0U,	// t2PLDpci
    0U,	// t2PLDs
    0U,	// t2PLIi12
    0U,	// t2PLIi8
    0U,	// t2PLIpci
    0U,	// t2PLIs
    0U,	// t2QADD
    0U,	// t2QADD16
    0U,	// t2QADD8
    0U,	// t2QASX
    0U,	// t2QDADD
    0U,	// t2QDSUB
    0U,	// t2QSAX
    0U,	// t2QSUB
    0U,	// t2QSUB16
    0U,	// t2QSUB8
    1024U,	// t2RBIT
    1024U,	// t2REV
    1024U,	// t2REV16
    1024U,	// t2REVSH
    0U,	// t2RFEDB
    4U,	// t2RFEDBW
    0U,	// t2RFEIA
    4U,	// t2RFEIAW
    0U,	// t2RORri
    0U,	// t2RORrr
    1024U,	// t2RRX
    0U,	// t2RSBSri
    0U,	// t2RSBSrs
    0U,	// t2RSBri
    0U,	// t2RSBrr
    475136U,	// t2RSBrs
    0U,	// t2SADD16
    0U,	// t2SADD8
    0U,	// t2SASX
    0U,	// t2SBCri
    0U,	// t2SBCrr
    475136U,	// t2SBCrs
    34603008U,	// t2SBFX
    0U,	// t2SDIV
    0U,	// t2SEL
    0U,	// t2SHADD16
    0U,	// t2SHADD8
    0U,	// t2SHASX
    0U,	// t2SHSAX
    0U,	// t2SHSUB16
    0U,	// t2SHSUB8
    0U,	// t2SMC
    17825792U,	// t2SMLABB
    17825792U,	// t2SMLABT
    17825792U,	// t2SMLAD
    17825792U,	// t2SMLADX
    17825792U,	// t2SMLAL
    17825792U,	// t2SMLALBB
    17825792U,	// t2SMLALBT
    17825792U,	// t2SMLALD
    17825792U,	// t2SMLALDX
    17825792U,	// t2SMLALTB
    17825792U,	// t2SMLALTT
    17825792U,	// t2SMLATB
    17825792U,	// t2SMLATT
    17825792U,	// t2SMLAWB
    17825792U,	// t2SMLAWT
    17825792U,	// t2SMLSD
    17825792U,	// t2SMLSDX
    17825792U,	// t2SMLSLD
    185860096U,	// t2SMLSLDX
    17825792U,	// t2SMMLA
    17825792U,	// t2SMMLAR
    17825792U,	// t2SMMLS
    17825792U,	// t2SMMLSR
    0U,	// t2SMMUL
    0U,	// t2SMMULR
    0U,	// t2SMUAD
    0U,	// t2SMUADX
    0U,	// t2SMULBB
    0U,	// t2SMULBT
    17825792U,	// t2SMULL
    0U,	// t2SMULTB
    0U,	// t2SMULTT
    0U,	// t2SMULWB
    0U,	// t2SMULWT
    0U,	// t2SMUSD
    0U,	// t2SMUSDX
    0U,	// t2SRSDB
    0U,	// t2SRSDB_UPD
    0U,	// t2SRSIA
    0U,	// t2SRSIA_UPD
    2216U,	// t2SSAT
    1192U,	// t2SSAT16
    0U,	// t2SSAX
    0U,	// t2SSUB16
    0U,	// t2SSUB8
    81U,	// t2STC2L_OFFSET
    49241U,	// t2STC2L_OPTION
    65625U,	// t2STC2L_POST
    97U,	// t2STC2L_PRE
    81U,	// t2STC2_OFFSET
    49241U,	// t2STC2_OPTION
    65625U,	// t2STC2_POST
    97U,	// t2STC2_PRE
    81U,	// t2STCL_OFFSET
    49241U,	// t2STCL_OPTION
    65625U,	// t2STCL_POST
    97U,	// t2STCL_PRE
    81U,	// t2STC_OFFSET
    49241U,	// t2STC_OPTION
    65625U,	// t2STC_POST
    97U,	// t2STC_PRE
    72U,	// t2STL
    72U,	// t2STLB
    229376U,	// t2STLEX
    229376U,	// t2STLEXB
    202375168U,	// t2STLEXD
    229376U,	// t2STLEXH
    72U,	// t2STLH
    1088U,	// t2STMDB
    57U,	// t2STMDB_UPD
    1088U,	// t2STMIA
    57U,	// t2STMIA_UPD
    384U,	// t2STRBT
    10840U,	// t2STRB_POST
    392U,	// t2STRB_PRE
    0U,	// t2STRB_preidx
    120U,	// t2STRBi12
    384U,	// t2STRBi8
    408U,	// t2STRBs
    11649048U,	// t2STRD_POST
    507928U,	// t2STRD_PRE
    524288U,	// t2STRDi8
    540672U,	// t2STREX
    229376U,	// t2STREXB
    202375168U,	// t2STREXD
    229376U,	// t2STREXH
    384U,	// t2STRHT
    10840U,	// t2STRH_POST
    392U,	// t2STRH_PRE
    0U,	// t2STRH_preidx
    120U,	// t2STRHi12
    384U,	// t2STRHi8
    408U,	// t2STRHs
    384U,	// t2STRT
    10840U,	// t2STR_POST
    392U,	// t2STR_PRE
    0U,	// t2STR_preidx
    120U,	// t2STRi12
    384U,	// t2STRi8
    408U,	// t2STRs
    0U,	// t2SUBS_PC_LR
    0U,	// t2SUBSri
    0U,	// t2SUBSrr
    0U,	// t2SUBSrs
    0U,	// t2SUBri
    0U,	// t2SUBri12
    0U,	// t2SUBrr
    475136U,	// t2SUBrs
    6291456U,	// t2SXTAB
    6291456U,	// t2SXTAB16
    6291456U,	// t2SXTAH
    2560U,	// t2SXTB
    2560U,	// t2SXTB16
    2560U,	// t2SXTH
    0U,	// t2TBB
    0U,	// t2TBB_JT
    0U,	// t2TBH
    0U,	// t2TBH_JT
    1024U,	// t2TEQri
    1024U,	// t2TEQrr
    376U,	// t2TEQrs
    1024U,	// t2TSTri
    1024U,	// t2TSTrr
    376U,	// t2TSTrs
    0U,	// t2UADD16
    0U,	// t2UADD8
    0U,	// t2UASX
    34603008U,	// t2UBFX
    0U,	// t2UDF
    0U,	// t2UDIV
    0U,	// t2UHADD16
    0U,	// t2UHADD8
    0U,	// t2UHASX
    0U,	// t2UHSAX
    0U,	// t2UHSUB16
    0U,	// t2UHSUB8
    17825792U,	// t2UMAAL
    17825792U,	// t2UMLAL
    17825792U,	// t2UMULL
    0U,	// t2UQADD16
    0U,	// t2UQADD8
    0U,	// t2UQASX
    0U,	// t2UQSAX
    0U,	// t2UQSUB16
    0U,	// t2UQSUB8
    0U,	// t2USAD8
    17825792U,	// t2USADA8
    7340032U,	// t2USAT
    0U,	// t2USAT16
    0U,	// t2USAX
    0U,	// t2USUB16
    0U,	// t2USUB8
    6291456U,	// t2UXTAB
    6291456U,	// t2UXTAB16
    6291456U,	// t2UXTAH
    2560U,	// t2UXTB
    2560U,	// t2UXTB16
    2560U,	// t2UXTH
    0U,	// tADC
    1048U,	// tADDhirr
    1184U,	// tADDi3
    0U,	// tADDi8
    0U,	// tADDrSP
    557056U,	// tADDrSPi
    1184U,	// tADDrr
    432U,	// tADDspi
    1048U,	// tADDspr
    0U,	// tADJCALLSTACKDOWN
    0U,	// tADJCALLSTACKUP
    440U,	// tADR
    0U,	// tAND
    448U,	// tASRri
    0U,	// tASRrr
    0U,	// tB
    0U,	// tBIC
    0U,	// tBKPT
    0U,	// tBL
    0U,	// tBLXi
    0U,	// tBLXr
    0U,	// tBRIND
    0U,	// tBR_JTr
    0U,	// tBX
    0U,	// tBX_CALL
    0U,	// tBX_RET
    0U,	// tBX_RET_vararg
    0U,	// tBcc
    0U,	// tBfar
    0U,	// tCBNZ
    0U,	// tCBZ
    1024U,	// tCMNz
    1024U,	// tCMPhir
    1024U,	// tCMPi8
    1024U,	// tCMPr
    0U,	// tCPS
    0U,	// tEOR
    0U,	// tHINT
    0U,	// tHLT
    0U,	// tInt_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_setjmp
    1088U,	// tLDMIA
    0U,	// tLDMIA_UPD
    456U,	// tLDRBi
    464U,	// tLDRBr
    472U,	// tLDRHi
    464U,	// tLDRHr
    0U,	// tLDRLIT_ga_abs
    0U,	// tLDRLIT_ga_pcrel
    464U,	// tLDRSB
    464U,	// tLDRSH
    480U,	// tLDRi
    400U,	// tLDRpci
    0U,	// tLDRpci_pic
    464U,	// tLDRr
    488U,	// tLDRspi
    0U,	// tLEApcrel
    0U,	// tLEApcrelJT
    1184U,	// tLSLri
    0U,	// tLSLrr
    448U,	// tLSRri
    0U,	// tLSRrr
    0U,	// tMOVCCr_pseudo
    0U,	// tMOVSr
    0U,	// tMOVi8
    1024U,	// tMOVr
    1184U,	// tMUL
    0U,	// tMVN
    0U,	// tORR
    0U,	// tPICADD
    0U,	// tPOP
    0U,	// tPOP_RET
    0U,	// tPUSH
    1024U,	// tREV
    1024U,	// tREV16
    1024U,	// tREVSH
    0U,	// tROR
    0U,	// tRSB
    0U,	// tSBC
    0U,	// tSETEND
    57U,	// tSTMIA_UPD
    456U,	// tSTRBi
    464U,	// tSTRBr
    472U,	// tSTRHi
    464U,	// tSTRHr
    480U,	// tSTRi
    464U,	// tSTRr
    488U,	// tSTRspi
    1184U,	// tSUBi3
    0U,	// tSUBi8
    1184U,	// tSUBrr
    432U,	// tSUBspi
    0U,	// tSVC
    1024U,	// tSXTB
    1024U,	// tSXTH
    0U,	// tTAILJMPd
    0U,	// tTAILJMPdND
    0U,	// tTAILJMPr
    0U,	// tTPsoft
    0U,	// tTRAP
    1024U,	// tTST
    0U,	// tUDF
    1024U,	// tUXTB
    1024U,	// tUXTH
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 's', 'h', 'a', '1', 's', 'u', '0', '.', '3', '2', 9, 0,
  /* 12 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '0', '.', '3', '2', 9, 0,
  /* 26 */ 's', 'h', 'a', '1', 's', 'u', '1', '.', '3', '2', 9, 0,
  /* 38 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '1', '.', '3', '2', 9, 0,
  /* 52 */ 's', 'h', 'a', '2', '5', '6', 'h', '2', '.', '3', '2', 9, 0,
  /* 65 */ 's', 'h', 'a', '1', 'c', '.', '3', '2', 9, 0,
  /* 75 */ 's', 'h', 'a', '1', 'h', '.', '3', '2', 9, 0,
  /* 85 */ 's', 'h', 'a', '2', '5', '6', 'h', '.', '3', '2', 9, 0,
  /* 97 */ 's', 'h', 'a', '1', 'm', '.', '3', '2', 9, 0,
  /* 107 */ 's', 'h', 'a', '1', 'p', '.', '3', '2', 9, 0,
  /* 117 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 132 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 147 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 162 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 177 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 192 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 207 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 222 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 237 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '3', '2', 9, 0,
  /* 249 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '3', '2', 9, 0,
  /* 261 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '3', '2', 9, 0,
  /* 273 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '3', '2', 9, 0,
  /* 285 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '3', '2', 9, 0,
  /* 297 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '3', '2', 9, 0,
  /* 309 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '3', '2', 9, 0,
  /* 321 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '3', '2', 9, 0,
  /* 333 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '3', '2', 9, 0,
  /* 345 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '3', '2', 9, 0,
  /* 357 */ 'v', 'r', 'i', 'n', 't', 'x', '.', 'f', '3', '2', 9, 0,
  /* 369 */ 'v', 'r', 'i', 'n', 't', 'z', '.', 'f', '3', '2', 9, 0,
  /* 381 */ 'l', 'd', 'c', '2', 9, 0,
  /* 387 */ 'm', 'r', 'c', '2', 9, 0,
  /* 393 */ 'm', 'r', 'r', 'c', '2', 9, 0,
  /* 400 */ 's', 't', 'c', '2', 9, 0,
  /* 406 */ 'c', 'd', 'p', '2', 9, 0,
  /* 412 */ 'm', 'c', 'r', '2', 9, 0,
  /* 418 */ 'm', 'c', 'r', 'r', '2', 9, 0,
  /* 425 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 440 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 455 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 470 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 485 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 500 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 515 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 530 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 545 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '6', '4', 9, 0,
  /* 557 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '6', '4', 9, 0,
  /* 569 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '6', '4', 9, 0,
  /* 581 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '6', '4', 9, 0,
  /* 593 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '6', '4', 9, 0,
  /* 605 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '6', '4', 9, 0,
  /* 617 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '6', '4', 9, 0,
  /* 629 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '6', '4', 9, 0,
  /* 641 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '6', '4', 9, 0,
  /* 653 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '6', '4', 9, 0,
  /* 665 */ 'v', 'm', 'u', 'l', 'l', '.', 'p', '6', '4', 9, 0,
  /* 676 */ 'a', 'e', 's', 'i', 'm', 'c', '.', '8', 9, 0,
  /* 686 */ 'a', 'e', 's', 'm', 'c', '.', '8', 9, 0,
  /* 695 */ 'a', 'e', 's', 'd', '.', '8', 9, 0,
  /* 703 */ 'a', 'e', 's', 'e', '.', '8', 9, 0,
  /* 711 */ 'r', 'f', 'e', 'd', 'a', 9, 0,
  /* 718 */ 'r', 'f', 'e', 'i', 'a', 9, 0,
  /* 725 */ 'c', 'r', 'c', '3', '2', 'b', 9, 0,
  /* 733 */ 'c', 'r', 'c', '3', '2', 'c', 'b', 9, 0,
  /* 742 */ 'r', 'f', 'e', 'd', 'b', 9, 0,
  /* 749 */ 'r', 'f', 'e', 'i', 'b', 9, 0,
  /* 756 */ 'd', 'm', 'b', 9, 0,
  /* 761 */ 'd', 's', 'b', 9, 0,
  /* 766 */ 'i', 's', 'b', 9, 0,
  /* 771 */ 'p', 'l', 'd', 9, 0,
  /* 776 */ 's', 'e', 't', 'e', 'n', 'd', 9, 0,
  /* 784 */ 'u', 'd', 'f', 9, 0,
  /* 789 */ 'c', 'r', 'c', '3', '2', 'h', 9, 0,
  /* 797 */ 'c', 'r', 'c', '3', '2', 'c', 'h', 9, 0,
  /* 806 */ 'p', 'l', 'i', 9, 0,
  /* 811 */ 'l', 'd', 'c', '2', 'l', 9, 0,
  /* 818 */ 's', 't', 'c', '2', 'l', 9, 0,
  /* 825 */ 'b', 'l', 9, 0,
  /* 829 */ 'c', 'p', 's', 9, 0,
  /* 834 */ 'm', 'o', 'v', 's', 9, 0,
  /* 840 */ 'h', 'l', 't', 9, 0,
  /* 845 */ 'b', 'k', 'p', 't', 9, 0,
  /* 851 */ 'u', 'd', 'f', '.', 'w', 9, 0,
  /* 858 */ 'c', 'r', 'c', '3', '2', 'w', 9, 0,
  /* 866 */ 'c', 'r', 'c', '3', '2', 'c', 'w', 9, 0,
  /* 875 */ 'p', 'l', 'd', 'w', 9, 0,
  /* 881 */ 'b', 'x', 9, 0,
  /* 885 */ 'b', 'l', 'x', 9, 0,
  /* 890 */ 'c', 'b', 'z', 9, 0,
  /* 895 */ 'c', 'b', 'n', 'z', 9, 0,
  /* 901 */ 's', 'r', 's', 'd', 'a', 9, 's', 'p', '!', ',', 32, 0,
  /* 913 */ 's', 'r', 's', 'i', 'a', 9, 's', 'p', '!', ',', 32, 0,
  /* 925 */ 's', 'r', 's', 'd', 'b', 9, 's', 'p', '!', ',', 32, 0,
  /* 937 */ 's', 'r', 's', 'i', 'b', 9, 's', 'p', '!', ',', 32, 0,
  /* 949 */ 's', 'r', 's', 'd', 'a', 9, 's', 'p', ',', 32, 0,
  /* 960 */ 's', 'r', 's', 'i', 'a', 9, 's', 'p', ',', 32, 0,
  /* 971 */ 's', 'r', 's', 'd', 'b', 9, 's', 'p', ',', 32, 0,
  /* 982 */ 's', 'r', 's', 'i', 'b', 9, 's', 'p', ',', 32, 0,
  /* 993 */ 'v', 'l', 'd', '1', 0,
  /* 998 */ 'd', 'c', 'p', 's', '1', 0,
  /* 1004 */ 'v', 's', 't', '1', 0,
  /* 1009 */ 'v', 'r', 'e', 'v', '3', '2', 0,
  /* 1016 */ 'l', 'd', 'c', '2', 0,
  /* 1021 */ 'm', 'r', 'c', '2', 0,
  /* 1026 */ 'm', 'r', 'r', 'c', '2', 0,
  /* 1032 */ 's', 't', 'c', '2', 0,
  /* 1037 */ 'v', 'l', 'd', '2', 0,
  /* 1042 */ 'c', 'd', 'p', '2', 0,
  /* 1047 */ 'm', 'c', 'r', '2', 0,
  /* 1052 */ 'm', 'c', 'r', 'r', '2', 0,
  /* 1058 */ 'd', 'c', 'p', 's', '2', 0,
  /* 1064 */ 'v', 's', 't', '2', 0,
  /* 1069 */ 'v', 'l', 'd', '3', 0,
  /* 1074 */ 'd', 'c', 'p', 's', '3', 0,
  /* 1080 */ 'v', 's', 't', '3', 0,
  /* 1085 */ 'v', 'r', 'e', 'v', '6', '4', 0,
  /* 1092 */ 'v', 'l', 'd', '4', 0,
  /* 1097 */ 'v', 's', 't', '4', 0,
  /* 1102 */ 's', 'x', 't', 'a', 'b', '1', '6', 0,
  /* 1110 */ 'u', 'x', 't', 'a', 'b', '1', '6', 0,
  /* 1118 */ 's', 'x', 't', 'b', '1', '6', 0,
  /* 1125 */ 'u', 'x', 't', 'b', '1', '6', 0,
  /* 1132 */ 's', 'h', 's', 'u', 'b', '1', '6', 0,
  /* 1140 */ 'u', 'h', 's', 'u', 'b', '1', '6', 0,
  /* 1148 */ 'u', 'q', 's', 'u', 'b', '1', '6', 0,
  /* 1156 */ 's', 's', 'u', 'b', '1', '6', 0,
  /* 1163 */ 'u', 's', 'u', 'b', '1', '6', 0,
  /* 1170 */ 's', 'h', 'a', 'd', 'd', '1', '6', 0,
  /* 1178 */ 'u', 'h', 'a', 'd', 'd', '1', '6', 0,
  /* 1186 */ 'u', 'q', 'a', 'd', 'd', '1', '6', 0,
  /* 1194 */ 's', 'a', 'd', 'd', '1', '6', 0,
  /* 1201 */ 'u', 'a', 'd', 'd', '1', '6', 0,
  /* 1208 */ 's', 's', 'a', 't', '1', '6', 0,
  /* 1215 */ 'u', 's', 'a', 't', '1', '6', 0,
  /* 1222 */ 'v', 'r', 'e', 'v', '1', '6', 0,
  /* 1229 */ 'u', 's', 'a', 'd', 'a', '8', 0,
  /* 1236 */ 's', 'h', 's', 'u', 'b', '8', 0,
  /* 1243 */ 'u', 'h', 's', 'u', 'b', '8', 0,
  /* 1250 */ 'u', 'q', 's', 'u', 'b', '8', 0,
  /* 1257 */ 's', 's', 'u', 'b', '8', 0,
  /* 1263 */ 'u', 's', 'u', 'b', '8', 0,
  /* 1269 */ 'u', 's', 'a', 'd', '8', 0,
  /* 1275 */ 's', 'h', 'a', 'd', 'd', '8', 0,
  /* 1282 */ 'u', 'h', 'a', 'd', 'd', '8', 0,
  /* 1289 */ 'u', 'q', 'a', 'd', 'd', '8', 0,
  /* 1296 */ 's', 'a', 'd', 'd', '8', 0,
  /* 1302 */ 'u', 'a', 'd', 'd', '8', 0,
  /* 1308 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 1321 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 1328 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 1338 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 1353 */ 'v', 'a', 'b', 'a', 0,
  /* 1358 */ 'l', 'd', 'a', 0,
  /* 1362 */ 'l', 'd', 'm', 'd', 'a', 0,
  /* 1368 */ 's', 't', 'm', 'd', 'a', 0,
  /* 1374 */ 'r', 'f', 'e', 'i', 'a', 0,
  /* 1380 */ 'v', 'l', 'd', 'm', 'i', 'a', 0,
  /* 1387 */ 'v', 's', 't', 'm', 'i', 'a', 0,
  /* 1394 */ 's', 'r', 's', 'i', 'a', 0,
  /* 1400 */ 's', 'm', 'm', 'l', 'a', 0,
  /* 1406 */ 'v', 'n', 'm', 'l', 'a', 0,
  /* 1412 */ 'v', 'm', 'l', 'a', 0,
  /* 1417 */ 'v', 'f', 'm', 'a', 0,
  /* 1422 */ 'v', 'f', 'n', 'm', 'a', 0,
  /* 1428 */ 'v', 'r', 's', 'r', 'a', 0,
  /* 1434 */ 'v', 's', 'r', 'a', 0,
  /* 1439 */ 'l', 'd', 'a', 'b', 0,
  /* 1444 */ 's', 'x', 't', 'a', 'b', 0,
  /* 1450 */ 'u', 'x', 't', 'a', 'b', 0,
  /* 1456 */ 's', 'm', 'l', 'a', 'b', 'b', 0,
  /* 1463 */ 's', 'm', 'l', 'a', 'l', 'b', 'b', 0,
  /* 1471 */ 's', 'm', 'u', 'l', 'b', 'b', 0,
  /* 1478 */ 't', 'b', 'b', 0,
  /* 1482 */ 'r', 'f', 'e', 'd', 'b', 0,
  /* 1488 */ 'v', 'l', 'd', 'm', 'd', 'b', 0,
  /* 1495 */ 'v', 's', 't', 'm', 'd', 'b', 0,
  /* 1502 */ 's', 'r', 's', 'd', 'b', 0,
  /* 1508 */ 'l', 'd', 'm', 'i', 'b', 0,
  /* 1514 */ 's', 't', 'm', 'i', 'b', 0,
  /* 1520 */ 's', 't', 'l', 'b', 0,
  /* 1525 */ 'd', 'm', 'b', 0,
  /* 1529 */ 's', 'w', 'p', 'b', 0,
  /* 1534 */ 'l', 'd', 'r', 'b', 0,
  /* 1539 */ 's', 't', 'r', 'b', 0,
  /* 1544 */ 'd', 's', 'b', 0,
  /* 1548 */ 'i', 's', 'b', 0,
  /* 1552 */ 'l', 'd', 'r', 's', 'b', 0,
  /* 1558 */ 's', 'm', 'l', 'a', 't', 'b', 0,
  /* 1565 */ 'p', 'k', 'h', 't', 'b', 0,
  /* 1571 */ 's', 'm', 'l', 'a', 'l', 't', 'b', 0,
  /* 1579 */ 's', 'm', 'u', 'l', 't', 'b', 0,
  /* 1586 */ 'v', 'c', 'v', 't', 'b', 0,
  /* 1592 */ 's', 'x', 't', 'b', 0,
  /* 1597 */ 'u', 'x', 't', 'b', 0,
  /* 1602 */ 'q', 'd', 's', 'u', 'b', 0,
  /* 1608 */ 'v', 'h', 's', 'u', 'b', 0,
  /* 1614 */ 'v', 'q', 's', 'u', 'b', 0,
  /* 1620 */ 'v', 's', 'u', 'b', 0,
  /* 1625 */ 's', 'm', 'l', 'a', 'w', 'b', 0,
  /* 1632 */ 's', 'm', 'u', 'l', 'w', 'b', 0,
  /* 1639 */ 'l', 'd', 'a', 'e', 'x', 'b', 0,
  /* 1646 */ 's', 't', 'l', 'e', 'x', 'b', 0,
  /* 1653 */ 'l', 'd', 'r', 'e', 'x', 'b', 0,
  /* 1660 */ 's', 't', 'r', 'e', 'x', 'b', 0,
  /* 1667 */ 's', 'b', 'c', 0,
  /* 1671 */ 'a', 'd', 'c', 0,
  /* 1675 */ 'l', 'd', 'c', 0,
  /* 1679 */ 'b', 'f', 'c', 0,
  /* 1683 */ 'v', 'b', 'i', 'c', 0,
  /* 1688 */ 's', 'm', 'c', 0,
  /* 1692 */ 'm', 'r', 'c', 0,
  /* 1696 */ 'm', 'r', 'r', 'c', 0,
  /* 1701 */ 'r', 's', 'c', 0,
  /* 1705 */ 's', 't', 'c', 0,
  /* 1709 */ 's', 'v', 'c', 0,
  /* 1713 */ 's', 'm', 'l', 'a', 'd', 0,
  /* 1719 */ 's', 'm', 'u', 'a', 'd', 0,
  /* 1725 */ 'v', 'a', 'b', 'd', 0,
  /* 1730 */ 'q', 'd', 'a', 'd', 'd', 0,
  /* 1736 */ 'v', 'r', 'h', 'a', 'd', 'd', 0,
  /* 1743 */ 'v', 'h', 'a', 'd', 'd', 0,
  /* 1749 */ 'v', 'p', 'a', 'd', 'd', 0,
  /* 1755 */ 'v', 'q', 'a', 'd', 'd', 0,
  /* 1761 */ 'v', 'a', 'd', 'd', 0,
  /* 1766 */ 's', 'm', 'l', 'a', 'l', 'd', 0,
  /* 1773 */ 'p', 'l', 'd', 0,
  /* 1777 */ 's', 'm', 'l', 's', 'l', 'd', 0,
  /* 1784 */ 'v', 'a', 'n', 'd', 0,
  /* 1789 */ 'l', 'd', 'r', 'd', 0,
  /* 1794 */ 's', 't', 'r', 'd', 0,
  /* 1799 */ 's', 'm', 'l', 's', 'd', 0,
  /* 1805 */ 's', 'm', 'u', 's', 'd', 0,
  /* 1811 */ 'l', 'd', 'a', 'e', 'x', 'd', 0,
  /* 1818 */ 's', 't', 'l', 'e', 'x', 'd', 0,
  /* 1825 */ 'l', 'd', 'r', 'e', 'x', 'd', 0,
  /* 1832 */ 's', 't', 'r', 'e', 'x', 'd', 0,
  /* 1839 */ 'v', 'a', 'c', 'g', 'e', 0,
  /* 1845 */ 'v', 'c', 'g', 'e', 0,
  /* 1850 */ 'v', 'c', 'l', 'e', 0,
  /* 1855 */ 'v', 'r', 'e', 'c', 'p', 'e', 0,
  /* 1862 */ 'v', 'c', 'm', 'p', 'e', 0,
  /* 1868 */ 'v', 'r', 's', 'q', 'r', 't', 'e', 0,
  /* 1876 */ 'v', 'b', 'i', 'f', 0,
  /* 1881 */ 'd', 'b', 'g', 0,
  /* 1885 */ 'v', 'q', 'n', 'e', 'g', 0,
  /* 1891 */ 'v', 'n', 'e', 'g', 0,
  /* 1896 */ 'l', 'd', 'a', 'h', 0,
  /* 1901 */ 's', 'x', 't', 'a', 'h', 0,
  /* 1907 */ 'u', 'x', 't', 'a', 'h', 0,
  /* 1913 */ 't', 'b', 'h', 0,
  /* 1917 */ 's', 't', 'l', 'h', 0,
  /* 1922 */ 'v', 'q', 'd', 'm', 'u', 'l', 'h', 0,
  /* 1930 */ 'v', 'q', 'r', 'd', 'm', 'u', 'l', 'h', 0,
  /* 1939 */ 'l', 'd', 'r', 'h', 0,
  /* 1944 */ 's', 't', 'r', 'h', 0,
  /* 1949 */ 'l', 'd', 'r', 's', 'h', 0,
  /* 1955 */ 'p', 'u', 's', 'h', 0,
  /* 1960 */ 'r', 'e', 'v', 's', 'h', 0,
  /* 1966 */ 's', 'x', 't', 'h', 0,
  /* 1971 */ 'u', 'x', 't', 'h', 0,
  /* 1976 */ 'l', 'd', 'a', 'e', 'x', 'h', 0,
  /* 1983 */ 's', 't', 'l', 'e', 'x', 'h', 0,
  /* 1990 */ 'l', 'd', 'r', 'e', 'x', 'h', 0,
  /* 1997 */ 's', 't', 'r', 'e', 'x', 'h', 0,
  /* 2004 */ 'b', 'f', 'i', 0,
  /* 2008 */ 'p', 'l', 'i', 0,
  /* 2012 */ 'v', 's', 'l', 'i', 0,
  /* 2017 */ 'v', 's', 'r', 'i', 0,
  /* 2022 */ 'b', 'x', 'j', 0,
  /* 2026 */ 'l', 'd', 'c', '2', 'l', 0,
  /* 2032 */ 's', 't', 'c', '2', 'l', 0,
  /* 2038 */ 'u', 'm', 'a', 'a', 'l', 0,
  /* 2044 */ 'v', 'a', 'b', 'a', 'l', 0,
  /* 2050 */ 'v', 'p', 'a', 'd', 'a', 'l', 0,
  /* 2057 */ 'v', 'q', 'd', 'm', 'l', 'a', 'l', 0,
  /* 2065 */ 's', 'm', 'l', 'a', 'l', 0,
  /* 2071 */ 'u', 'm', 'l', 'a', 'l', 0,
  /* 2077 */ 'v', 'm', 'l', 'a', 'l', 0,
  /* 2083 */ 'v', 't', 'b', 'l', 0,
  /* 2088 */ 'v', 's', 'u', 'b', 'l', 0,
  /* 2094 */ 'l', 'd', 'c', 'l', 0,
  /* 2099 */ 's', 't', 'c', 'l', 0,
  /* 2104 */ 'v', 'a', 'b', 'd', 'l', 0,
  /* 2110 */ 'v', 'p', 'a', 'd', 'd', 'l', 0,
  /* 2117 */ 'v', 'a', 'd', 'd', 'l', 0,
  /* 2123 */ 's', 'e', 'l', 0,
  /* 2127 */ 'v', 'q', 's', 'h', 'l', 0,
  /* 2133 */ 'v', 'q', 'r', 's', 'h', 'l', 0,
  /* 2140 */ 'v', 'r', 's', 'h', 'l', 0,
  /* 2146 */ 'v', 's', 'h', 'l', 0,
  /* 2151 */ 'v', 's', 'h', 'l', 'l', 0,
  /* 2157 */ 'v', 'q', 'd', 'm', 'u', 'l', 'l', 0,
  /* 2165 */ 's', 'm', 'u', 'l', 'l', 0,
  /* 2171 */ 'u', 'm', 'u', 'l', 'l', 0,
  /* 2177 */ 'v', 'm', 'u', 'l', 'l', 0,
  /* 2183 */ 'v', 'b', 's', 'l', 0,
  /* 2188 */ 'v', 'q', 'd', 'm', 'l', 's', 'l', 0,
  /* 2196 */ 'v', 'm', 'l', 's', 'l', 0,
  /* 2202 */ 's', 't', 'l', 0,
  /* 2206 */ 's', 'm', 'm', 'u', 'l', 0,
  /* 2212 */ 'v', 'n', 'm', 'u', 'l', 0,
  /* 2218 */ 'v', 'm', 'u', 'l', 0,
  /* 2223 */ 'v', 'm', 'o', 'v', 'l', 0,
  /* 2229 */ 'l', 'd', 'm', 0,
  /* 2233 */ 's', 't', 'm', 0,
  /* 2237 */ 'v', 'r', 's', 'u', 'b', 'h', 'n', 0,
  /* 2245 */ 'v', 's', 'u', 'b', 'h', 'n', 0,
  /* 2252 */ 'v', 'r', 'a', 'd', 'd', 'h', 'n', 0,
  /* 2260 */ 'v', 'a', 'd', 'd', 'h', 'n', 0,
  /* 2267 */ 'v', 'p', 'm', 'i', 'n', 0,
  /* 2273 */ 'v', 'm', 'i', 'n', 0,
  /* 2278 */ 'c', 'm', 'n', 0,
  /* 2282 */ 'v', 'q', 's', 'h', 'r', 'n', 0,
  /* 2289 */ 'v', 'q', 'r', 's', 'h', 'r', 'n', 0,
  /* 2297 */ 'v', 'r', 's', 'h', 'r', 'n', 0,
  /* 2304 */ 'v', 's', 'h', 'r', 'n', 0,
  /* 2310 */ 'v', 'o', 'r', 'n', 0,
  /* 2315 */ 'v', 't', 'r', 'n', 0,
  /* 2320 */ 'v', 'q', 's', 'h', 'r', 'u', 'n', 0,
  /* 2328 */ 'v', 'q', 'r', 's', 'h', 'r', 'u', 'n', 0,
  /* 2337 */ 'v', 'q', 'm', 'o', 'v', 'u', 'n', 0,
  /* 2345 */ 'v', 'm', 'v', 'n', 0,
  /* 2350 */ 'v', 'q', 'm', 'o', 'v', 'n', 0,
  /* 2357 */ 'v', 'm', 'o', 'v', 'n', 0,
  /* 2363 */ 't', 'r', 'a', 'p', 0,
  /* 2368 */ 'c', 'd', 'p', 0,
  /* 2372 */ 'v', 'z', 'i', 'p', 0,
  /* 2377 */ 'v', 'c', 'm', 'p', 0,
  /* 2382 */ 'p', 'o', 'p', 0,
  /* 2386 */ 'v', 'd', 'u', 'p', 0,
  /* 2391 */ 'v', 's', 'w', 'p', 0,
  /* 2396 */ 'v', 'u', 'z', 'p', 0,
  /* 2401 */ 'v', 'c', 'e', 'q', 0,
  /* 2406 */ 't', 'e', 'q', 0,
  /* 2410 */ 's', 'm', 'm', 'l', 'a', 'r', 0,
  /* 2417 */ 'm', 'c', 'r', 0,
  /* 2421 */ 'a', 'd', 'r', 0,
  /* 2425 */ 'v', 'l', 'd', 'r', 0,
  /* 2430 */ 'v', 'r', 's', 'h', 'r', 0,
  /* 2436 */ 'v', 's', 'h', 'r', 0,
  /* 2441 */ 's', 'm', 'm', 'u', 'l', 'r', 0,
  /* 2448 */ 'v', 'e', 'o', 'r', 0,
  /* 2453 */ 'r', 'o', 'r', 0,
  /* 2457 */ 'm', 'c', 'r', 'r', 0,
  /* 2462 */ 'v', 'o', 'r', 'r', 0,
  /* 2467 */ 'a', 's', 'r', 0,
  /* 2471 */ 's', 'm', 'm', 'l', 's', 'r', 0,
  /* 2478 */ 'v', 'm', 's', 'r', 0,
  /* 2483 */ 'v', 'r', 'i', 'n', 't', 'r', 0,
  /* 2490 */ 'v', 's', 't', 'r', 0,
  /* 2495 */ 'v', 'c', 'v', 't', 'r', 0,
  /* 2501 */ 'v', 'q', 'a', 'b', 's', 0,
  /* 2507 */ 'v', 'a', 'b', 's', 0,
  /* 2512 */ 's', 'u', 'b', 's', 0,
  /* 2517 */ 'v', 'c', 'l', 's', 0,
  /* 2522 */ 's', 'm', 'm', 'l', 's', 0,
  /* 2528 */ 'v', 'n', 'm', 'l', 's', 0,
  /* 2534 */ 'v', 'm', 'l', 's', 0,
  /* 2539 */ 'v', 'f', 'm', 's', 0,
  /* 2544 */ 'v', 'f', 'n', 'm', 's', 0,
  /* 2550 */ 'v', 'r', 'e', 'c', 'p', 's', 0,
  /* 2557 */ 'v', 'm', 'r', 's', 0,
  /* 2562 */ 'a', 's', 'r', 's', 0,
  /* 2567 */ 'l', 's', 'r', 's', 0,
  /* 2572 */ 'v', 'r', 's', 'q', 'r', 't', 's', 0,
  /* 2580 */ 'm', 'o', 'v', 's', 0,
  /* 2585 */ 's', 's', 'a', 't', 0,
  /* 2590 */ 'u', 's', 'a', 't', 0,
  /* 2595 */ 's', 'm', 'l', 'a', 'b', 't', 0,
  /* 2602 */ 'p', 'k', 'h', 'b', 't', 0,
  /* 2608 */ 's', 'm', 'l', 'a', 'l', 'b', 't', 0,
  /* 2616 */ 's', 'm', 'u', 'l', 'b', 't', 0,
  /* 2623 */ 'l', 'd', 'r', 'b', 't', 0,
  /* 2629 */ 's', 't', 'r', 'b', 't', 0,
  /* 2635 */ 'l', 'd', 'r', 's', 'b', 't', 0,
  /* 2642 */ 'v', 'a', 'c', 'g', 't', 0,
  /* 2648 */ 'v', 'c', 'g', 't', 0,
  /* 2653 */ 'l', 'd', 'r', 'h', 't', 0,
  /* 2659 */ 's', 't', 'r', 'h', 't', 0,
  /* 2665 */ 'l', 'd', 'r', 's', 'h', 't', 0,
  /* 2672 */ 'r', 'b', 'i', 't', 0,
  /* 2677 */ 'v', 'b', 'i', 't', 0,
  /* 2682 */ 'v', 'c', 'l', 't', 0,
  /* 2687 */ 'v', 'c', 'n', 't', 0,
  /* 2692 */ 'h', 'i', 'n', 't', 0,
  /* 2697 */ 'l', 'd', 'r', 't', 0,
  /* 2702 */ 'v', 's', 'q', 'r', 't', 0,
  /* 2708 */ 's', 't', 'r', 't', 0,
  /* 2713 */ 'v', 't', 's', 't', 0,
  /* 2718 */ 's', 'm', 'l', 'a', 't', 't', 0,
  /* 2725 */ 's', 'm', 'l', 'a', 'l', 't', 't', 0,
  /* 2733 */ 's', 'm', 'u', 'l', 't', 't', 0,
  /* 2740 */ 'v', 'c', 'v', 't', 't', 0,
  /* 2746 */ 'v', 'c', 'v', 't', 0,
  /* 2751 */ 'm', 'o', 'v', 't', 0,
  /* 2756 */ 's', 'm', 'l', 'a', 'w', 't', 0,
  /* 2763 */ 's', 'm', 'u', 'l', 'w', 't', 0,
  /* 2770 */ 'v', 'e', 'x', 't', 0,
  /* 2775 */ 'v', 'q', 's', 'h', 'l', 'u', 0,
  /* 2782 */ 'r', 'e', 'v', 0,
  /* 2786 */ 's', 'd', 'i', 'v', 0,
  /* 2791 */ 'u', 'd', 'i', 'v', 0,
  /* 2796 */ 'v', 'd', 'i', 'v', 0,
  /* 2801 */ 'v', 'm', 'o', 'v', 0,
  /* 2806 */ 'v', 's', 'u', 'b', 'w', 0,
  /* 2812 */ 'v', 'a', 'd', 'd', 'w', 0,
  /* 2818 */ 'p', 'l', 'd', 'w', 0,
  /* 2823 */ 'm', 'o', 'v', 'w', 0,
  /* 2828 */ 'f', 'l', 'd', 'm', 'i', 'a', 'x', 0,
  /* 2836 */ 'f', 's', 't', 'm', 'i', 'a', 'x', 0,
  /* 2844 */ 'v', 'p', 'm', 'a', 'x', 0,
  /* 2850 */ 'v', 'm', 'a', 'x', 0,
  /* 2855 */ 's', 'h', 's', 'a', 'x', 0,
  /* 2861 */ 'u', 'h', 's', 'a', 'x', 0,
  /* 2867 */ 'u', 'q', 's', 'a', 'x', 0,
  /* 2873 */ 's', 's', 'a', 'x', 0,
  /* 2878 */ 'u', 's', 'a', 'x', 0,
  /* 2883 */ 'f', 'l', 'd', 'm', 'd', 'b', 'x', 0,
  /* 2891 */ 'f', 's', 't', 'm', 'd', 'b', 'x', 0,
  /* 2899 */ 'v', 't', 'b', 'x', 0,
  /* 2904 */ 's', 'm', 'l', 'a', 'd', 'x', 0,
  /* 2911 */ 's', 'm', 'u', 'a', 'd', 'x', 0,
  /* 2918 */ 's', 'm', 'l', 'a', 'l', 'd', 'x', 0,
  /* 2926 */ 's', 'm', 'l', 's', 'l', 'd', 'x', 0,
  /* 2934 */ 's', 'm', 'l', 's', 'd', 'x', 0,
  /* 2941 */ 's', 'm', 'u', 's', 'd', 'x', 0,
  /* 2948 */ 'l', 'd', 'a', 'e', 'x', 0,
  /* 2954 */ 's', 't', 'l', 'e', 'x', 0,
  /* 2960 */ 'l', 'd', 'r', 'e', 'x', 0,
  /* 2966 */ 'c', 'l', 'r', 'e', 'x', 0,
  /* 2972 */ 's', 't', 'r', 'e', 'x', 0,
  /* 2978 */ 's', 'b', 'f', 'x', 0,
  /* 2983 */ 'u', 'b', 'f', 'x', 0,
  /* 2988 */ 'b', 'l', 'x', 0,
  /* 2992 */ 'r', 'r', 'x', 0,
  /* 2996 */ 's', 'h', 'a', 's', 'x', 0,
  /* 3002 */ 'u', 'h', 'a', 's', 'x', 0,
  /* 3008 */ 'u', 'q', 'a', 's', 'x', 0,
  /* 3014 */ 's', 'a', 's', 'x', 0,
  /* 3019 */ 'u', 'a', 's', 'x', 0,
  /* 3024 */ 'v', 'r', 'i', 'n', 't', 'x', 0,
  /* 3031 */ 'v', 'c', 'l', 'z', 0,
  /* 3036 */ 'v', 'r', 'i', 'n', 't', 'z', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint64_t Bits1 = OpInfo[MCInst_getOpcode(MI)];
  uint64_t Bits2 = OpInfo2[MCInst_getOpcode(MI)];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif


  // Fragment 0 encoded into 5 bits for 29 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 12) & 31);
  switch ((Bits >> 12) & 31) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, CLREX, TRAP, TRAPNaCl...
    return;
    break;
  case 1:
    // ADCri, ADCrr, ADDri, ADDrr, ANDri, ANDrr, ASRi, ASRr, BICri, BICrr, EO...
    printSBitModifierOperand(MI, 5, O); 
    printPredicateOperand(MI, 3, O); 
    break;
  case 2:
    // ADCrsi, ADDrsi, ANDrsi, BICrsi, EORrsi, MLA, MOVsr, MVNsr, ORRrsi, RSB...
    printSBitModifierOperand(MI, 6, O); 
    printPredicateOperand(MI, 4, O); 
    break;
  case 3:
    // ADCrsr, ADDrsr, ANDrsr, BICrsr, EORrsr, ORRrsr, RSBrsr, RSCrsr, SBCrsr...
    printSBitModifierOperand(MI, 7, O); 
    printPredicateOperand(MI, 5, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printSORegRegOperand(MI, 2, O); 
    return;
    break;
  case 4:
    // ADR, CLZ, CMNri, CMNzrr, CMPri, CMPrr, FCONSTD, FCONSTS, FLDMXDB_UPD, ...
    printPredicateOperand(MI, 2, O); 
    break;
  case 5:
    // AESD, AESE, AESIMC, AESMC, BKPT, BL, BLX, BLXi, BX, CPS1p, CRC32B, CRC...
    printOperand(MI, 0, O); 
    break;
  case 6:
    // BFC, CMNzrsi, CMPrsi, LDRBi12, LDRcp, LDRi12, MOVTi16, QADD, QADD16, Q...
    printPredicateOperand(MI, 3, O); 
    break;
  case 7:
    // BFI, CMNzrsr, CMPrsr, LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, L...
    printPredicateOperand(MI, 4, O); 
    break;
  case 8:
    // BLX_pred, BL_pred, BXJ, BX_pred, Bcc, DBG, FLDMXIA, FSTMXIA, HINT, LDM...
    printPredicateOperand(MI, 1, O); 
    break;
  case 9:
    // BX_RET, FMSTAT, MOVPCLR, t2CLREX, t2DCPS1, t2DCPS2, t2DCPS3, tBL, tBLX...
    printPredicateOperand(MI, 0, O); 
    break;
  case 10:
    // CDP, LDRD_POST, LDRD_PRE, MCR, MRC, STRD_POST, STRD_PRE, VLD4DUPd16, V...
    printPredicateOperand(MI, 6, O); 
    break;
  case 11:
    // CDP2, LDC2L_OFFSET, LDC2L_OPTION, LDC2L_POST, LDC2L_PRE, LDC2_OFFSET, ...
    printPImmediate(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // CPS2p, CPS3p, t2CPS2p, t2CPS3p, tCPS
    printCPSIMod(MI, 0, O); 
    break;
  case 13:
    // DMB, DSB
    printMemBOption(MI, 0, O); 
    return;
    break;
  case 14:
    // ISB
    printInstSyncBOption(MI, 0, O); 
    return;
    break;
  case 15:
    // ITasm, t2IT
    printThumbITMask(MI, 1, O); 
    break;
  case 16:
    // LDRBT_POST_IMM, LDRBT_POST_REG, LDRB_POST_IMM, LDRB_POST_REG, LDRB_PRE...
    printPredicateOperand(MI, 5, O); 
    break;
  case 17:
    // MOVi, MOVr, MOVr_TC, MVNi, MVNr, RRXi, t2MOVi, t2MOVr, t2MVNi, t2MVNr,...
    printSBitModifierOperand(MI, 4, O); 
    printPredicateOperand(MI, 2, O); 
    break;
  case 18:
    // MRC2
    printPImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 19:
    // PLDWi12, PLDi12, PLIi12
    printAddrModeImm12Operand(MI, 0, O, false); 
    return;
    break;
  case 20:
    // PLDWrs, PLDrs, PLIrs
    printAddrMode2Operand(MI, 0, O); 
    return;
    break;
  case 21:
    // SETEND, tSETEND
    printSetendOperand(MI, 0, O); 
    return;
    break;
  case 22:
    // SMLAL, UMLAL
    printSBitModifierOperand(MI, 8, O); 
    printPredicateOperand(MI, 6, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 23:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD, VLD2LNd16, VLD2LNd32, VLD2...
    printPredicateOperand(MI, 7, O); 
    break;
  case 24:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printPredicateOperand(MI, 9, O); 
    break;
  case 25:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printPredicateOperand(MI, 11, O); 
    break;
  case 26:
    // VLD4DUPd16_UPD, VLD4DUPd32_UPD, VLD4DUPd8_UPD, VLD4DUPq16_UPD, VLD4DUP...
    printPredicateOperand(MI, 8, O); 
    break;
  case 27:
    // VLD4LNd16_UPD, VLD4LNd32_UPD, VLD4LNd8_UPD, VLD4LNq16_UPD, VLD4LNq32_U...
    printPredicateOperand(MI, 13, O); 
    break;
  case 28:
    // tADC, tADDi3, tADDi8, tADDrr, tAND, tASRri, tASRrr, tBIC, tEOR, tLSLri...
    printSBitModifierOperand(MI, 1, O); 
    break;
  }


  // Fragment 1 encoded into 7 bits for 65 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 17) & 127);
  switch ((Bits >> 17) & 127) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    SStream_concat0(O, "\t"); 
    break;
  case 1:
    // AESD, AESE, AESIMC, AESMC, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, ...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // ASRi, ASRr, ITasm, LDRBT_POST, LDRT_POST, LSLi, LSLr, LSRi, LSRr, RORi...
    SStream_concat0(O, " "); 
    break;
  case 3:
    // BKPT, BL, BLX, BLXi, BX, CPS1p, HLT, RFEDA, RFEDB, RFEIA, RFEIB, SRSDA...
    return;
    break;
  case 4:
    // BX_RET
    SStream_concat0(O, "\tlr");
	ARM_addReg(MI, ARM_REG_LR);
    return;
    break;
  case 5:
    // CDP2, MCR2, MCRR2, MRRC2
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // FCONSTD, VABSD, VADDD, VCMPD, VCMPED, VCMPEZD, VCMPZD, VDIVD, VFMAD, V...
    SStream_concat0(O, ".f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64);
    printOperand(MI, 0, O); 
    break;
  case 7:
    // FCONSTS, VABDfd, VABDfq, VABSS, VABSfd, VABSfq, VACGEd, VACGEq, VACGTd...
    SStream_concat0(O, ".f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32);
    printOperand(MI, 0, O); 
    break;
  case 8:
    // FMSTAT
    SStream_concat0(O, "\tapsr_nzcv, fpscr");
	ARM_addReg(MI, ARM_REG_APSR_NZCV);
	ARM_addReg(MI, ARM_REG_FPSCR);
    return;
    break;
  case 9:
    // LDC2L_OFFSET, LDC2L_OPTION, LDC2L_POST, LDC2L_PRE, LDC2_OFFSET, LDC2_O...
    printCImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 10:
    // MOVPCLR
    SStream_concat0(O, "\tpc, lr");
	ARM_addReg(MI, ARM_REG_PC);
	ARM_addReg(MI, ARM_REG_LR);
    return;
    break;
  case 11:
    // RFEDA_UPD, RFEDB_UPD, RFEIA_UPD, RFEIB_UPD
    SStream_concat0(O, "!"); 
    return;
    break;
  case 12:
    // VABALsv2i64, VABAsv2i32, VABAsv4i32, VABDLsv2i64, VABDsv2i32, VABDsv4i...
    SStream_concat0(O, ".s32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 13:
    // VABALsv4i32, VABAsv4i16, VABAsv8i16, VABDLsv4i32, VABDsv4i16, VABDsv8i...
    SStream_concat0(O, ".s16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 14:
    // VABALsv8i16, VABAsv16i8, VABAsv8i8, VABDLsv8i16, VABDsv16i8, VABDsv8i8...
    SStream_concat0(O, ".s8\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 15:
    // VABALuv2i64, VABAuv2i32, VABAuv4i32, VABDLuv2i64, VABDuv2i32, VABDuv4i...
    SStream_concat0(O, ".u32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 16:
    // VABALuv4i32, VABAuv4i16, VABAuv8i16, VABDLuv4i32, VABDuv4i16, VABDuv8i...
    SStream_concat0(O, ".u16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 17:
    // VABALuv8i16, VABAuv16i8, VABAuv8i8, VABDLuv8i16, VABDuv16i8, VABDuv8i8...
    SStream_concat0(O, ".u8\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 18:
    // VADDHNv2i32, VADDv1i64, VADDv2i64, VMOVNv2i32, VMOVv1i64, VMOVv2i64, V...
    SStream_concat0(O, ".i64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // VADDHNv4i16, VADDv2i32, VADDv4i32, VBICiv2i32, VBICiv4i32, VCEQv2i32, ...
    SStream_concat0(O, ".i32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 20:
    // VADDHNv8i8, VADDv4i16, VADDv8i16, VBICiv4i16, VBICiv8i16, VCEQv4i16, V...
    SStream_concat0(O, ".i16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 21:
    // VADDv16i8, VADDv8i8, VCEQv16i8, VCEQv8i8, VCEQzv16i8, VCEQzv8i8, VCLZv...
    SStream_concat0(O, ".i8\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // VCNTd, VCNTq, VDUP8d, VDUP8q, VDUPLN8d, VDUPLN8q, VEXTd8, VEXTq8, VLD1...
    SStream_concat0(O, ".8\t");
	ARM_addVectorDataSize(MI, 8);
    break;
  case 23:
    // VCVTBDH, VCVTTDH
    SStream_concat0(O, ".f16.f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F16F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 24:
    // VCVTBHD, VCVTTHD
    SStream_concat0(O, ".f64.f16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64F16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 25:
    // VCVTBHS, VCVTTHS, VCVTh2f
    SStream_concat0(O, ".f32.f16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32F16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 26:
    // VCVTBSH, VCVTTSH, VCVTf2h
    SStream_concat0(O, ".f16.f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F16F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 27:
    // VCVTDS
    SStream_concat0(O, ".f64.f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 28:
    // VCVTSD
    SStream_concat0(O, ".f32.f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 29:
    // VCVTf2sd, VCVTf2sq, VCVTf2xsd, VCVTf2xsq, VTOSIRS, VTOSIZS, VTOSLS
    SStream_concat0(O, ".s32.f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S32F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 30:
    // VCVTf2ud, VCVTf2uq, VCVTf2xud, VCVTf2xuq, VTOUIRS, VTOUIZS, VTOULS
    SStream_concat0(O, ".u32.f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U32F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 31:
    // VCVTs2fd, VCVTs2fq, VCVTxs2fd, VCVTxs2fq, VSITOS, VSLTOS
    SStream_concat0(O, ".f32.s32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32S32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 32:
    // VCVTu2fd, VCVTu2fq, VCVTxu2fd, VCVTxu2fq, VUITOS, VULTOS
    SStream_concat0(O, ".f32.u32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32U32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 33:
    // VDUP16d, VDUP16q, VDUPLN16d, VDUPLN16q, VEXTd16, VEXTq16, VLD1DUPd16, ...
    SStream_concat0(O, ".16\t");
	ARM_addVectorDataSize(MI, 16);
    break;
  case 34:
    // VDUP32d, VDUP32q, VDUPLN32d, VDUPLN32q, VEXTd32, VEXTq32, VGETLNi32, V...
    SStream_concat0(O, ".32\t");
	ARM_addVectorDataSize(MI, 32);
    break;
  case 35:
    // VEXTq64, VLD1d64, VLD1d64Q, VLD1d64Qwb_fixed, VLD1d64Qwb_register, VLD...
    SStream_concat0(O, ".64\t");
	ARM_addVectorDataSize(MI, 64);
    break;
  case 36:
    // VLD1LNd16, VLD1LNd16_UPD, VLD2LNd16, VLD2LNd16_UPD, VLD2LNq16, VLD2LNq...
    SStream_concat0(O, ".16\t{");
	ARM_addVectorDataSize(MI, 16);
    break;
  case 37:
    // VLD1LNd32, VLD1LNd32_UPD, VLD2LNd32, VLD2LNd32_UPD, VLD2LNq32, VLD2LNq...
    SStream_concat0(O, ".32\t{");
	ARM_addVectorDataSize(MI, 32);
    break;
  case 38:
    // VLD1LNd8, VLD1LNd8_UPD, VLD2LNd8, VLD2LNd8_UPD, VLD3DUPd8, VLD3DUPd8_U...
    SStream_concat0(O, ".8\t{");
	ARM_addVectorDataSize(MI, 8);
    break;
  case 39:
    // VMSR
    SStream_concat0(O, "\tfpscr, ");
	ARM_addReg(MI, ARM_REG_FPSCR);
    printOperand(MI, 0, O); 
    return;
    break;
  case 40:
    // VMSR_FPEXC
    SStream_concat0(O, "\tfpexc, ");
	ARM_addReg(MI, ARM_REG_FPEXC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 41:
    // VMSR_FPINST
    SStream_concat0(O, "\tfpinst, ");
	ARM_addReg(MI, ARM_REG_FPINST);
    printOperand(MI, 0, O); 
    return;
    break;
  case 42:
    // VMSR_FPINST2
    SStream_concat0(O, "\tfpinst2, ");
	ARM_addReg(MI, ARM_REG_FPINST2);
    printOperand(MI, 0, O); 
    return;
    break;
  case 43:
    // VMSR_FPSID
    SStream_concat0(O, "\tfpsid, ");
	ARM_addReg(MI, ARM_REG_FPSID);
    printOperand(MI, 0, O); 
    return;
    break;
  case 44:
    // VMULLp8, VMULpd, VMULpq
    SStream_concat0(O, ".p8\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_P8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 45:
    // VQADDsv1i64, VQADDsv2i64, VQMOVNsuv2i32, VQMOVNsv2i32, VQRSHLsv1i64, V...
    SStream_concat0(O, ".s64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 46:
    // VQADDuv1i64, VQADDuv2i64, VQMOVNuv2i32, VQRSHLuv1i64, VQRSHLuv2i64, VQ...
    SStream_concat0(O, ".u64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 47:
    // VSHTOD
    SStream_concat0(O, ".f64.s16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64S16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 48:
    // VSHTOS
    SStream_concat0(O, ".f32.s16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32S16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 49:
    // VSITOD, VSLTOD
    SStream_concat0(O, ".f64.s32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64S32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 50:
    // VTOSHD
    SStream_concat0(O, ".s16.f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S16F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 51:
    // VTOSHS
    SStream_concat0(O, ".s16.f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S16F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 52:
    // VTOSIRD, VTOSIZD, VTOSLD
    SStream_concat0(O, ".s32.f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S32F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 53:
    // VTOUHD
    SStream_concat0(O, ".u16.f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U16F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 54:
    // VTOUHS
    SStream_concat0(O, ".u16.f32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U16F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 55:
    // VTOUIRD, VTOUIZD, VTOULD
    SStream_concat0(O, ".u32.f64\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U32F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 56:
    // VUHTOD
    SStream_concat0(O, ".f64.u16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64U16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 57:
    // VUHTOS
    SStream_concat0(O, ".f32.u16\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32U16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 58:
    // VUITOD, VULTOD
    SStream_concat0(O, ".f64.u32\t");
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64U32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 59:
    // t2ADCrr, t2ADCrs, t2ADDri, t2ADDrr, t2ADDrs, t2ADR, t2ANDrr, t2ANDrs, ...
    SStream_concat0(O, ".w\t"); 
    break;
  case 60:
    // t2SRSDB, t2SRSIA
    SStream_concat0(O, "\tsp, ");
	ARM_addReg(MI, ARM_REG_SP);
    printOperand(MI, 0, O); 
    return;
    break;
  case 61:
    // t2SRSDB_UPD, t2SRSIA_UPD
    SStream_concat0(O, "\tsp!, ");
	ARM_addReg(MI, ARM_REG_SP);
    printOperand(MI, 0, O); 
    return;
    break;
  case 62:
    // t2SUBS_PC_LR
    SStream_concat0(O, "\tpc, lr, ");
	ARM_addReg(MI, ARM_REG_PC);
	ARM_addReg(MI, ARM_REG_LR);
    printOperand(MI, 0, O); 
    return;
    break;
  case 63:
    // tADC, tADDi3, tADDi8, tADDrr, tAND, tASRri, tASRrr, tBIC, tEOR, tLSLri...
    printPredicateOperand(MI, 4, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 64:
    // tMOVi8, tMVN, tRSB
    printPredicateOperand(MI, 3, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    break;
  }


  // Fragment 2 encoded into 6 bits for 57 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 24) & 63);
  switch ((Bits >> 24) & 63) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    printOperand(MI, 0, O); 
    break;
  case 1:
    // AESD, AESE, MCR2, MCRR2, MRRC2, SHA1C, SHA1M, SHA1P, SHA1SU0, SHA1SU1,...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // AESIMC, AESMC, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, CRC32W, FLDM...
    printOperand(MI, 1, O); 
    break;
  case 3:
    // CDP, LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, LDC_OFFSET, LDC_OP...
    printPImmediate(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // CDP2
    printCImmediate(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 5:
    // CPS2p, CPS3p, t2CPS2p, t2CPS3p, tCPS
    printCPSIFlag(MI, 1, O); 
    break;
  case 6:
    // FCONSTD, FCONSTS, VABDfd, VABDfq, VABSD, VABSS, VABSfd, VABSfq, VACGEd...
    SStream_concat0(O, ", "); 
    break;
  case 7:
    // ITasm, t2IT
    printMandatoryPredicateOperand(MI, 0, O); 
    return;
    break;
  case 8:
    // LDAEXD, LDREXD
    printGPRPairOperand(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode7Operand(MI, 1, O); 
    return;
    break;
  case 9:
    // LDC2L_OFFSET, LDC2_OFFSET, STC2L_OFFSET, STC2_OFFSET
    printAddrMode5Operand(MI, 2, O, false); 
    return;
    break;
  case 10:
    // LDC2L_OPTION, LDC2L_POST, LDC2_OPTION, LDC2_POST, STC2L_OPTION, STC2L_...
    printAddrMode7Operand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 11:
    // LDC2L_PRE, LDC2_PRE, STC2L_PRE, STC2_PRE
    printAddrMode5Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 12:
    // MRC, t2MRC, t2MRC2
    printPImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 13:
    // MSR, MSRi, t2MSR_AR, t2MSR_M
    printMSRMaskOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 14:
    // VBICiv2i32, VBICiv4i16, VBICiv4i32, VBICiv8i16, VMOVv16i8, VMOVv1i64, ...
    printNEONModImmOperand(MI, 1, O); 
    return;
    break;
  case 15:
    // VCMPEZD, VCMPEZS, VCMPZD, VCMPZS, tRSB
    SStream_concat0(O, ", #0"); 
	op_addImm(MI, 0);
    return;
    break;
  case 16:
    // VCVTf2sd, VCVTf2sq, VCVTf2ud, VCVTf2uq, VCVTs2fd, VCVTs2fq, VCVTu2fd, ...
    return;
    break;
  case 17:
    // VLD1DUPd16, VLD1DUPd16wb_fixed, VLD1DUPd16wb_register, VLD1DUPd32, VLD...
    printVectorListOneAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 18:
    // VLD1DUPq16, VLD1DUPq16wb_fixed, VLD1DUPq16wb_register, VLD1DUPq32, VLD...
    printVectorListTwoAllLanes(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // VLD1d16, VLD1d16wb_fixed, VLD1d16wb_register, VLD1d32, VLD1d32wb_fixed...
    printVectorListOne(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 20:
    // VLD1d16Q, VLD1d16Qwb_fixed, VLD1d16Qwb_register, VLD1d32Q, VLD1d32Qwb_...
    printVectorListFour(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 21:
    // VLD1d16T, VLD1d16Twb_fixed, VLD1d16Twb_register, VLD1d32T, VLD1d32Twb_...
    printVectorListThree(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // VLD1q16, VLD1q16wb_fixed, VLD1q16wb_register, VLD1q32, VLD1q32wb_fixed...
    printVectorListTwo(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 23:
    // VLD2DUPd16x2, VLD2DUPd16x2wb_fixed, VLD2DUPd16x2wb_register, VLD2DUPd3...
    printVectorListTwoSpacedAllLanes(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 24:
    // VLD2b16, VLD2b16wb_fixed, VLD2b16wb_register, VLD2b32, VLD2b32wb_fixed...
    printVectorListTwoSpaced(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 25:
    // VLD3DUPdAsm_16, VLD3DUPdAsm_32, VLD3DUPdAsm_8, VLD3DUPdWB_fixed_Asm_16...
    printVectorListThreeAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 26:
    // VLD3DUPqAsm_16, VLD3DUPqAsm_32, VLD3DUPqAsm_8, VLD3DUPqWB_fixed_Asm_16...
    printVectorListThreeSpacedAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 27:
    // VLD3qAsm_16, VLD3qAsm_32, VLD3qAsm_8, VLD3qWB_fixed_Asm_16, VLD3qWB_fi...
    printVectorListThreeSpaced(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 28:
    // VLD4DUPdAsm_16, VLD4DUPdAsm_32, VLD4DUPdAsm_8, VLD4DUPdWB_fixed_Asm_16...
    printVectorListFourAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 29:
    // VLD4DUPqAsm_16, VLD4DUPqAsm_32, VLD4DUPqAsm_8, VLD4DUPqWB_fixed_Asm_16...
    printVectorListFourSpacedAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 30:
    // VLD4qAsm_16, VLD4qAsm_32, VLD4qAsm_8, VLD4qWB_fixed_Asm_16, VLD4qWB_fi...
    printVectorListFourSpaced(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 31:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD, VST2LNd16_UPD, VST2LNd32_U...
    printOperand(MI, 4, O); 
    break;
  case 32:
    // VST1d16, VST1d32, VST1d64, VST1d8
    printVectorListOne(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 33:
    // VST1d16Q, VST1d32Q, VST1d64Q, VST1d8Q, VST2q16, VST2q32, VST2q8
    printVectorListFour(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 34:
    // VST1d16Qwb_fixed, VST1d32Qwb_fixed, VST1d64Qwb_fixed, VST1d8Qwb_fixed,...
    printVectorListFour(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 35:
    // VST1d16Qwb_register, VST1d32Qwb_register, VST1d64Qwb_register, VST1d8Q...
    printVectorListFour(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 36:
    // VST1d16T, VST1d32T, VST1d64T, VST1d8T
    printVectorListThree(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 37:
    // VST1d16Twb_fixed, VST1d32Twb_fixed, VST1d64Twb_fixed, VST1d8Twb_fixed
    printVectorListThree(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 38:
    // VST1d16Twb_register, VST1d32Twb_register, VST1d64Twb_register, VST1d8T...
    printVectorListThree(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 39:
    // VST1d16wb_fixed, VST1d32wb_fixed, VST1d64wb_fixed, VST1d8wb_fixed
    printVectorListOne(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 40:
    // VST1d16wb_register, VST1d32wb_register, VST1d64wb_register, VST1d8wb_r...
    printVectorListOne(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 41:
    // VST1q16, VST1q32, VST1q64, VST1q8, VST2d16, VST2d32, VST2d8
    printVectorListTwo(MI, 2, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 42:
    // VST1q16wb_fixed, VST1q32wb_fixed, VST1q64wb_fixed, VST1q8wb_fixed, VST...
    printVectorListTwo(MI, 3, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 43:
    // VST1q16wb_register, VST1q32wb_register, VST1q64wb_register, VST1q8wb_r...
    printVectorListTwo(MI, 4, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 44:
    // VST2b16, VST2b32, VST2b8
    printVectorListTwoSpaced(MI, 2, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 45:
    // VST2b16wb_fixed, VST2b32wb_fixed, VST2b8wb_fixed
    printVectorListTwoSpaced(MI, 3, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 46:
    // VST2b16wb_register, VST2b32wb_register, VST2b8wb_register
    printVectorListTwoSpaced(MI, 4, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 47:
    // t2DMB, t2DSB
    printMemBOption(MI, 0, O); 
    return;
    break;
  case 48:
    // t2ISB
    printInstSyncBOption(MI, 0, O); 
    return;
    break;
  case 49:
    // t2PLDWi12, t2PLDi12, t2PLIi12
    printAddrModeImm12Operand(MI, 0, O, false); 
    return;
    break;
  case 50:
    // t2PLDWi8, t2PLDi8, t2PLIi8
    printT2AddrModeImm8Operand(MI, 0, O, false); 
    return;
    break;
  case 51:
    // t2PLDWs, t2PLDs, t2PLIs
    printT2AddrModeSoRegOperand(MI, 0, O); 
    return;
    break;
  case 52:
    // t2PLDpci, t2PLIpci
    printThumbLdrLabelOperand(MI, 0, O); 
    return;
    break;
  case 53:
    // t2TBB
    printAddrModeTBB(MI, 0, O); 
    return;
    break;
  case 54:
    // t2TBH
    printAddrModeTBH(MI, 0, O); 
    return;
    break;
  case 55:
    // tADC, tADDi8, tAND, tASRrr, tBIC, tEOR, tLSLrr, tLSRrr, tORR, tROR, tS...
    printOperand(MI, 3, O); 
    return;
    break;
  case 56:
    // tPOP, tPUSH
    printRegisterList(MI, 2, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 5 bits for 28 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 30) & 31);
  switch ((Bits >> 30) & 31) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // AESD, AESE, AESIMC, AESMC, BLX_pred, BL_pred, BXJ, BX_pred, Bcc, CPS2p...
    return;
    break;
  case 2:
    // CDP, MCR, MCRR, MRRC, VABDfd, VABDfq, VABSD, VABSS, VABSfd, VABSfq, VA...
    printOperand(MI, 1, O); 
    break;
  case 3:
    // FCONSTD, FCONSTS, VMOVv2f32, VMOVv4f32
    printFPImmOperand(MI, 1, O); 
    return;
    break;
  case 4:
    // FLDMXDB_UPD, FLDMXIA_UPD, FSTMXDB_UPD, FSTMXIA_UPD, LDMDA_UPD, LDMDB_U...
    SStream_concat0(O, "!, "); 
    printRegisterList(MI, 4, O); 
    break;
  case 5:
    // LDC2L_OPTION, LDC2_OPTION, STC2L_OPTION, STC2_OPTION
    printCoprocOptionImm(MI, 3, O); 
    return;
    break;
  case 6:
    // LDC2L_POST, LDC2_POST, STC2L_POST, STC2_POST
    printPostIdxImm8s4Operand(MI, 3, O); 
    return;
    break;
  case 7:
    // LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, LDC_OFFSET, LDC_OPTION,...
    printCImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 8:
    // MRS, t2MRS_AR
    SStream_concat0(O, ", apsr");
	ARM_addReg(MI, ARM_REG_APSR);
    return;
    break;
  case 9:
    // MRSsys, t2MRSsys_AR
    SStream_concat0(O, ", spsr");
	ARM_addReg(MI, ARM_REG_SPSR);
    return;
    break;
  case 10:
    // VCEQzv16i8, VCEQzv2i32, VCEQzv4i16, VCEQzv4i32, VCEQzv8i16, VCEQzv8i8,...
    SStream_concat0(O, ", #0"); 
	op_addImm(MI, 0);
    return;
    break;
  case 11:
    // VCVTf2xsd, VCVTf2xsq, VCVTf2xud, VCVTf2xuq, VCVTxs2fd, VCVTxs2fq, VCVT...
    printOperand(MI, 2, O); 
    break;
  case 12:
    // VGETLNs16, VGETLNs8, VGETLNu16, VGETLNu8
    printVectorIndex(MI, 2, O); 
    return;
    break;
  case 13:
    // VLD1DUPd16, VLD1DUPd32, VLD1DUPd8, VLD1DUPq16, VLD1DUPq32, VLD1DUPq8, ...
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 14:
    // VLD1DUPd16wb_fixed, VLD1DUPd16wb_register, VLD1DUPd32wb_fixed, VLD1DUP...
    printAddrMode6Operand(MI, 2, O); 
    break;
  case 15:
    // VLD1LNd16, VLD1LNd16_UPD, VLD1LNd32, VLD1LNd32_UPD, VLD1LNd8, VLD1LNd8...
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    break;
  case 16:
    // VLD3DUPd16, VLD3DUPd16_UPD, VLD3DUPd32, VLD3DUPd32_UPD, VLD3DUPd8, VLD...
    SStream_concat0(O, "[], "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, "[], "); 
    printOperand(MI, 2, O); 
    break;
  case 17:
    // VLD3DUPdWB_fixed_Asm_16, VLD3DUPdWB_fixed_Asm_32, VLD3DUPdWB_fixed_Asm...
    SStream_concat0(O, "!"); 
    return;
    break;
  case 18:
    // VMRS
    SStream_concat0(O, ", fpscr");
	ARM_addReg(MI, ARM_REG_FPSCR);
    return;
    break;
  case 19:
    // VMRS_FPEXC
    SStream_concat0(O, ", fpexc");
	ARM_addReg(MI, ARM_REG_FPEXC);
    return;
    break;
  case 20:
    // VMRS_FPINST
    SStream_concat0(O, ", fpinst");
	ARM_addReg(MI, ARM_REG_FPINST);
    return;
    break;
  case 21:
    // VMRS_FPINST2
    SStream_concat0(O, ", fpinst2");
	ARM_addReg(MI, ARM_REG_FPINST2);
    return;
    break;
  case 22:
    // VMRS_FPSID
    SStream_concat0(O, ", fpsid");
	ARM_addReg(MI, ARM_REG_FPSID);
    return;
    break;
  case 23:
    // VMRS_MVFR0
    SStream_concat0(O, ", mvfr0");
	ARM_addReg(MI, ARM_REG_MVFR0);
    return;
    break;
  case 24:
    // VMRS_MVFR1
    SStream_concat0(O, ", mvfr1");
	ARM_addReg(MI, ARM_REG_MVFR1);
    return;
    break;
  case 25:
    // VMRS_MVFR2
    SStream_concat0(O, ", mvfr2");
	ARM_addReg(MI, ARM_REG_MVFR2);
    return;
    break;
  case 26:
    // VSETLNi16, VSETLNi32, VSETLNi8
    printVectorIndex(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 27:
    // VSLTOD, VSLTOS, VTOSLD, VTOSLS, VTOULD, VTOULS, VULTOD, VULTOS
    printFBits32(MI, 2, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 6 bits for 62 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 35) & 63);
  switch ((Bits >> 35) & 63) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ANDri, ANDrr, ANDrsi, ASRi...
    printOperand(MI, 1, O); 
    break;
  case 1:
    // ADR, t2ADR
    printAdrLabelOperand(MI, 1, O, 0); 
    return;
    break;
  case 2:
    // BFC, t2BFC
    printBitfieldInvMaskImmOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // BFI, CPS3p, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, CRC32W, MOVTi16...
    printOperand(MI, 2, O); 
    break;
  case 4:
    // CDP, MCR, MCRR, MRRC, VABDfd, VABDfq, VACGEd, VACGEq, VACGTd, VACGTq, ...
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // CMNzrsi, CMPrsi, MOVsi, MVNsi, TEQrsi, TSTrsi
    printSORegImmOperand(MI, 1, O); 
    return;
    break;
  case 6:
    // CMNzrsr, CMPrsr, MOVsr, MVNsr, TEQrsr, TSTrsr, t2MOVSsr, t2MOVsr
    printSORegRegOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // FLDMXDB_UPD, FLDMXIA_UPD, FSTMXDB_UPD, FSTMXIA_UPD, LDMDA_UPD, LDMDB_U...
    return;
    break;
  case 8:
    // FLDMXIA, FSTMXIA, LDMDA, LDMDB, LDMIA, LDMIB, STMDA, STMDB, STMIA, STM...
    printRegisterList(MI, 3, O); 
    break;
  case 9:
    // LDA, LDAB, LDAEX, LDAEXB, LDAEXH, LDAH, LDRBT_POST, LDREX, LDREXB, LDR...
    printAddrMode7Operand(MI, 1, O); 
    return;
    break;
  case 10:
    // LDCL_OFFSET, LDC_OFFSET, STCL_OFFSET, STC_OFFSET, t2LDC2L_OFFSET, t2LD...
    printAddrMode5Operand(MI, 2, O, false); 
    return;
    break;
  case 11:
    // LDCL_OPTION, LDCL_POST, LDC_OPTION, LDC_POST, LDRBT_POST_IMM, LDRBT_PO...
    printAddrMode7Operand(MI, 2, O); 
    break;
  case 12:
    // LDCL_PRE, LDC_PRE, STCL_PRE, STC_PRE, t2LDC2L_PRE, t2LDC2_PRE, t2LDCL_...
    printAddrMode5Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 13:
    // LDRB_PRE_IMM, LDR_PRE_IMM, STRB_PRE_IMM, STR_PRE_IMM
    printAddrModeImm12Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 14:
    // LDRB_PRE_REG, LDR_PRE_REG, STRB_PRE_REG, STR_PRE_REG
    printAddrMode2Operand(MI, 2, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 15:
    // LDRBi12, LDRcp, LDRi12, STRBi12, STRi12, t2LDRBi12, t2LDRHi12, t2LDRSB...
    printAddrModeImm12Operand(MI, 1, O, false); 
    return;
    break;
  case 16:
    // LDRBrs, LDRrs, STRBrs, STRrs
    printAddrMode2Operand(MI, 1, O); 
    return;
    break;
  case 17:
    // LDRH, LDRSB, LDRSH, STRH
    printAddrMode3Operand(MI, 1, O, false); 
    return;
    break;
  case 18:
    // LDRH_PRE, LDRSB_PRE, LDRSH_PRE, STRH_PRE
    printAddrMode3Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 19:
    // MCR2
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 20:
    // MCRR2, MRRC2, SHA1C, SHA1M, SHA1P, SHA1SU0, SHA256H, SHA256H2, SHA256S...
    printOperand(MI, 3, O); 
    break;
  case 21:
    // SSAT, SSAT16, t2SSAT, t2SSAT16
    printImmPlusOneOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    break;
  case 22:
    // STLEXD, STREXD
    printGPRPairOperand(MI, 1, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode7Operand(MI, 2, O); 
    return;
    break;
  case 23:
    // VCEQzv2f32, VCEQzv4f32, VCGEzv2f32, VCGEzv4f32, VCGTzv2f32, VCGTzv4f32...
    SStream_concat0(O, ", #0"); 
	op_addImm(MI, 0);
    return;
    break;
  case 24:
    // VLD1DUPd16wb_fixed, VLD1DUPd32wb_fixed, VLD1DUPd8wb_fixed, VLD1DUPq16w...
    SStream_concat0(O, "!"); 
    return;
    break;
  case 25:
    // VLD1LNd16, VLD1LNd32, VLD1LNd8, VST2LNd16, VST2LNd32, VST2LNd8, VST2LN...
    printNoHashImmediate(MI, 4, O); 
    break;
  case 26:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD, VLD2LNd16, VLD2LNd32, VLD2...
    printNoHashImmediate(MI, 6, O); 
    break;
  case 27:
    // VLD1LNdAsm_16, VLD1LNdAsm_32, VLD1LNdAsm_8, VLD1LNdWB_fixed_Asm_16, VL...
    printAddrMode6Operand(MI, 2, O); 
    break;
  case 28:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    break;
  case 29:
    // VLD3DUPd16, VLD3DUPd16_UPD, VLD3DUPd32, VLD3DUPd32_UPD, VLD3DUPd8, VLD...
    SStream_concat0(O, "[]}, "); 
    break;
  case 30:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printNoHashImmediate(MI, 10, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 10, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 10, O); 
    break;
  case 31:
    // VLD4DUPd16, VLD4DUPd16_UPD, VLD4DUPd32, VLD4DUPd32_UPD, VLD4DUPd8, VLD...
    SStream_concat0(O, "[], "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, "[]}, "); 
    break;
  case 32:
    // VLD4LNd16_UPD, VLD4LNd32_UPD, VLD4LNd8_UPD, VLD4LNq16_UPD, VLD4LNq32_U...
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 3, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 5, O); 
    printAddrMode6OffsetOperand(MI, 7, O); 
    return;
    break;
  case 33:
    // VLDRD, VLDRS, VSTRD, VSTRS
    printAddrMode5Operand(MI, 1, O, false); 
    return;
    break;
  case 34:
    // VST1LNd16, VST1LNd32, VST1LNd8
    printNoHashImmediate(MI, 3, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 35:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD, VST3LNd16, VST3LNd32, VST3...
    printNoHashImmediate(MI, 5, O); 
    break;
  case 36:
    // VST3LNd16_UPD, VST3LNd32_UPD, VST3LNd8_UPD, VST3LNq16_UPD, VST3LNq32_U...
    printNoHashImmediate(MI, 7, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 7, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 6, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 7, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 37:
    // VST3d16_UPD, VST3d32_UPD, VST3d8_UPD, VST3q16_UPD, VST3q32_UPD, VST3q8...
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 6, O); 
    break;
  case 38:
    // VTBL1
    printVectorListOne(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 39:
    // VTBL2
    printVectorListTwo(MI, 1, O, MRI); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 40:
    // VTBL3
    printVectorListThree(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 41:
    // VTBL4
    printVectorListFour(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 42:
    // VTBX1
    printVectorListOne(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 43:
    // VTBX2
    printVectorListTwo(MI, 2, O, MRI); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 44:
    // VTBX3
    printVectorListThree(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 45:
    // VTBX4
    printVectorListFour(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 46:
    // sysLDMDA_UPD, sysLDMDB_UPD, sysLDMIA_UPD, sysLDMIB_UPD, sysSTMDA_UPD, ...
    SStream_concat0(O, " ^");
	ARM_addUserMode(MI);
    return;
    break;
  case 47:
    // t2CMNzrs, t2CMPrs, t2MOVSsi, t2MOVsi, t2MVNs, t2TEQrs, t2TSTrs
    printT2SOOperand(MI, 1, O); 
    return;
    break;
  case 48:
    // t2LDRBT, t2LDRBi8, t2LDRHT, t2LDRHi8, t2LDRSBT, t2LDRSBi8, t2LDRSHT, t...
    printT2AddrModeImm8Operand(MI, 1, O, false); 
    return;
    break;
  case 49:
    // t2LDRB_PRE, t2LDRH_PRE, t2LDRSB_PRE, t2LDRSH_PRE, t2LDR_PRE, t2STRB_PR...
    printT2AddrModeImm8Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 50:
    // t2LDRBpci, t2LDRHpci, t2LDRSBpci, t2LDRSHpci, t2LDRpci, tLDRpci
    printThumbLdrLabelOperand(MI, 1, O); 
    return;
    break;
  case 51:
    // t2LDRBs, t2LDRHs, t2LDRSBs, t2LDRSHs, t2LDRs, t2STRBs, t2STRHs, t2STRs
    printT2AddrModeSoRegOperand(MI, 1, O); 
    return;
    break;
  case 52:
    // t2LDREX
    printT2AddrModeImm0_1020s4Operand(MI, 1, O); 
    return;
    break;
  case 53:
    // t2MRS_M
    printMSRMaskOperand(MI, 1, O); 
    return;
    break;
  case 54:
    // tADDspi, tSUBspi
    printThumbS4ImmOperand(MI, 2, O); 
    return;
    break;
  case 55:
    // tADR
    printAdrLabelOperand(MI, 1, O, 2); 
    return;
    break;
  case 56:
    // tASRri, tLSRri
    printThumbSRImm(MI, 3, O); 
    return;
    break;
  case 57:
    // tLDRBi, tSTRBi
    printThumbAddrModeImm5S1Operand(MI, 1, O); 
    return;
    break;
  case 58:
    // tLDRBr, tLDRHr, tLDRSB, tLDRSH, tLDRr, tSTRBr, tSTRHr, tSTRr
    printThumbAddrModeRROperand(MI, 1, O); 
    return;
    break;
  case 59:
    // tLDRHi, tSTRHi
    printThumbAddrModeImm5S2Operand(MI, 1, O); 
    return;
    break;
  case 60:
    // tLDRi, tSTRi
    printThumbAddrModeImm5S4Operand(MI, 1, O); 
    return;
    break;
  case 61:
    // tLDRspi, tSTRspi
    printThumbAddrModeSPOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 5 bits for 23 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 41) & 31);
  switch ((Bits >> 41) & 31) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ANDri, ANDrr, ANDrsi, ASRi...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // CDP, t2CDP, t2CDP2
    printCImmediate(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 2:
    // CLZ, CMNri, CMNzrr, CMPri, CMPrr, CPS3p, CRC32B, CRC32CB, CRC32CH, CRC...
    return;
    break;
  case 3:
    // MCR, MCRR, MRRC, VABDfd, VABDfq, VACGEd, VACGEq, VACGTd, VACGTq, VADDD...
    printOperand(MI, 2, O); 
    break;
  case 4:
    // SSAT, t2SSAT
    printShiftImmOperand(MI, 3, O); 
    return;
    break;
  case 5:
    // SXTB, SXTB16, SXTH, UXTB, UXTB16, UXTH, t2SXTB, t2SXTB16, t2SXTH, t2UX...
    printRotImmOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // VDUPLN16d, VDUPLN16q, VDUPLN32d, VDUPLN32q, VDUPLN8d, VDUPLN8q, VGETLN...
    printVectorIndex(MI, 2, O); 
    return;
    break;
  case 7:
    // VFMAD, VFMAS, VFMAfd, VFMAfq, VFMSD, VFMSS, VFMSfd, VFMSfq, VFNMAD, VF...
    printOperand(MI, 3, O); 
    break;
  case 8:
    // VLD1DUPd16wb_register, VLD1DUPd32wb_register, VLD1DUPd8wb_register, VL...
    printOperand(MI, 4, O); 
    return;
    break;
  case 9:
    // VLD1LNd16, VLD1LNd16_UPD, VLD1LNd32, VLD1LNd32_UPD, VLD1LNd8, VLD1LNd8...
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    break;
  case 10:
    // VLD1LNdWB_fixed_Asm_16, VLD1LNdWB_fixed_Asm_32, VLD1LNdWB_fixed_Asm_8,...
    SStream_concat0(O, "!"); 
    return;
    break;
  case 11:
    // VLD2LNd16, VLD2LNd32, VLD2LNd8, VLD2LNq16, VLD2LNq32, VLD4LNd16, VLD4L...
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    break;
  case 12:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    break;
  case 13:
    // VLD3DUPd16, VLD3DUPd32, VLD3DUPd8, VLD3DUPq16, VLD3DUPq32, VLD3DUPq8
    printAddrMode6Operand(MI, 3, O); 
    return;
    break;
  case 14:
    // VLD3DUPd16_UPD, VLD3DUPd32_UPD, VLD3DUPd8_UPD, VLD3DUPq16_UPD, VLD3DUP...
    printAddrMode6Operand(MI, 4, O); 
    break;
  case 15:
    // VLD4DUPd16_UPD, VLD4DUPd32_UPD, VLD4DUPd8_UPD, VLD4DUPq16_UPD, VLD4DUP...
    printAddrMode6Operand(MI, 5, O); 
    printAddrMode6OffsetOperand(MI, 7, O); 
    return;
    break;
  case 16:
    // VMLALslsv2i32, VMLALslsv4i16, VMLALsluv2i32, VMLALsluv4i16, VMLAslv2i3...
    printVectorIndex(MI, 4, O); 
    return;
    break;
  case 17:
    // VMULLslsv2i32, VMULLslsv4i16, VMULLsluv2i32, VMULLsluv4i16, VMULslv2i3...
    printVectorIndex(MI, 3, O); 
    return;
    break;
  case 18:
    // VST3d16_UPD, VST3d32_UPD, VST3d8_UPD, VST3q16_UPD, VST3q32_UPD, VST3q8...
    SStream_concat0(O, "}, "); 
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 19:
    // VST4LNd16_UPD, VST4LNd32_UPD, VST4LNd8_UPD, VST4LNq16_UPD, VST4LNq32_U...
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 6, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 7, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 20:
    // sysLDMDA, sysLDMDB, sysLDMIA, sysLDMIB, sysSTMDA, sysSTMDB, sysSTMIA, ...
    SStream_concat0(O, " ^");
	ARM_addUserMode(MI);
    return;
    break;
  case 21:
    // t2LDRB_POST, t2LDRH_POST, t2LDRSB_POST, t2LDRSH_POST, t2LDR_POST, t2ST...
    printT2AddrModeImm8OffsetOperand(MI, 3, O); 
    return;
    break;
  case 22:
    // t2MOVsra_flag, t2MOVsrl_flag
    SStream_concat0(O, ", #1"); 
	op_addImm(MI, 1);
    return;
    break;
  }


  // Fragment 6 encoded into 6 bits for 35 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 46) & 63);
  switch ((Bits >> 46) & 63) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADDri, ADDrr, ANDri, ANDrr, ASRi, ASRr, BICri, BICrr, EO...
    printOperand(MI, 2, O); 
    break;
  case 1:
    // ADCrsi, ADDrsi, ANDrsi, BICrsi, EORrsi, ORRrsi, RSBrsi, RSCrsi, SBCrsi...
    printSORegImmOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // BFI, t2BFI
    printBitfieldInvMaskImmOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // LDCL_OPTION, LDC_OPTION, STCL_OPTION, STC_OPTION, t2LDC2L_OPTION, t2LD...
    printCoprocOptionImm(MI, 3, O); 
    return;
    break;
  case 4:
    // LDCL_POST, LDC_POST, STCL_POST, STC_POST, t2LDC2L_POST, t2LDC2_POST, t...
    printPostIdxImm8s4Operand(MI, 3, O); 
    return;
    break;
  case 5:
    // LDRBT_POST_IMM, LDRBT_POST_REG, LDRB_POST_IMM, LDRB_POST_REG, LDRT_POS...
    printAddrMode2OffsetOperand(MI, 3, O); 
    return;
    break;
  case 6:
    // LDRD, STRD
    printAddrMode3Operand(MI, 2, O, false); 
    return;
    break;
  case 7:
    // LDRD_POST, STRD_POST, t2LDRD_POST, t2STRD_POST
    printAddrMode7Operand(MI, 3, O); 
    break;
  case 8:
    // LDRD_PRE, STRD_PRE
    printAddrMode3Operand(MI, 3, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 9:
    // LDRHTi, LDRSBTi, LDRSHTi, STRHTi
    printPostIdxImm8Operand(MI, 3, O); 
    return;
    break;
  case 10:
    // LDRHTr, LDRSBTr, LDRSHTr, STRHTr
    printPostIdxRegOperand(MI, 3, O); 
    return;
    break;
  case 11:
    // LDRH_POST, LDRSB_POST, LDRSH_POST, STRH_POST
    printAddrMode3OffsetOperand(MI, 3, O); 
    return;
    break;
  case 12:
    // MCR, MCRR, MRRC, t2MCR, t2MCR2, t2MCRR, t2MCRR2, t2MRRC, t2MRRC2
    SStream_concat0(O, ", "); 
    break;
  case 13:
    // MCRR2, MRRC2
    printCImmediate(MI, 4, O); 
    return;
    break;
  case 14:
    // STLEX, STLEXB, STLEXH, STREX, STREXB, STREXH, SWP, SWPB, t2LDAEXD, t2L...
    printAddrMode7Operand(MI, 2, O); 
    return;
    break;
  case 15:
    // VABDfd, VABDfq, VACGEd, VACGEq, VACGTd, VACGTq, VADDD, VADDS, VADDfd, ...
    return;
    break;
  case 16:
    // VBIFd, VBIFq, VBITd, VBITq, VBSLd, VBSLq, VLD4LNd16, VLD4LNd32, VLD4LN...
    printOperand(MI, 3, O); 
    break;
  case 17:
    // VLD1LNd16, VLD1LNd32, VLD1LNd8, VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8...
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 18:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD
    printAddrMode6Operand(MI, 2, O); 
    printAddrMode6OffsetOperand(MI, 4, O); 
    return;
    break;
  case 19:
    // VLD1LNdWB_register_Asm_16, VLD1LNdWB_register_Asm_32, VLD1LNdWB_regist...
    printOperand(MI, 4, O); 
    break;
  case 20:
    // VLD2LNd16, VLD2LNd32, VLD2LNd8, VLD2LNq16, VLD2LNq32
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 2, O); 
    return;
    break;
  case 21:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 3, O); 
    printAddrMode6OffsetOperand(MI, 5, O); 
    return;
    break;
  case 22:
    // VLD3DUPd16_UPD, VLD3DUPd32_UPD, VLD3DUPd8_UPD, VLD3DUPq16_UPD, VLD3DUP...
    printAddrMode6OffsetOperand(MI, 6, O); 
    return;
    break;
  case 23:
    // VLD3LNd16, VLD3LNd32, VLD3LNd8, VLD3LNq16, VLD3LNq32
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 3, O); 
    return;
    break;
  case 24:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printAddrMode6Operand(MI, 4, O); 
    printAddrMode6OffsetOperand(MI, 6, O); 
    return;
    break;
  case 25:
    // VMLAslfd, VMLAslfq, VMLSslfd, VMLSslfq
    printVectorIndex(MI, 4, O); 
    return;
    break;
  case 26:
    // VMULslfd, VMULslfq
    printVectorIndex(MI, 3, O); 
    return;
    break;
  case 27:
    // VST2LNd16_UPD, VST2LNd32_UPD, VST2LNd8_UPD, VST2LNq16_UPD, VST2LNq32_U...
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 28:
    // VST4d16_UPD, VST4d32_UPD, VST4d8_UPD, VST4q16_UPD, VST4q32_UPD, VST4q8...
    printOperand(MI, 7, O); 
    SStream_concat0(O, "}, "); 
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 29:
    // t2ADCrs, t2ADDrs, t2ANDrs, t2BICrs, t2EORrs, t2ORNrs, t2ORRrs, t2RSBrs...
    printT2SOOperand(MI, 2, O); 
    return;
    break;
  case 30:
    // t2ASRri, t2LSRri
    printThumbSRImm(MI, 2, O); 
    return;
    break;
  case 31:
    // t2LDRD_PRE, t2STRD_PRE
    printT2AddrModeImm8s4Operand(MI, 3, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 32:
    // t2LDRDi8, t2STRDi8
    printT2AddrModeImm8s4Operand(MI, 2, O, false); 
    return;
    break;
  case 33:
    // t2STREX
    printT2AddrModeImm0_1020s4Operand(MI, 2, O); 
    return;
    break;
  case 34:
    // tADDrSPi
    printThumbS4ImmOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 7 encoded into 4 bits for 12 unique commands.
  //printf("Frag-7: %"PRIu64"\n", (Bits >> 52) & 15);
  switch ((Bits >> 52) & 15) {
  default:   // unreachable.
  case 0:
    // ADCri, ADCrr, ADDri, ADDrr, ANDri, ANDrr, ASRi, ASRr, BICri, BICrr, EO...
    return;
    break;
  case 1:
    // LDRD_POST, MLA, MLS, SBFX, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SML...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // MCR, t2MCR, t2MCR2
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 3:
    // MCRR, MRRC, t2MCRR, t2MCRR2, t2MRRC, t2MRRC2
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    return;
    break;
  case 4:
    // PKHBT, t2PKHBT
    printPKHLSLShiftImm(MI, 3, O); 
    return;
    break;
  case 5:
    // PKHTB, t2PKHTB
    printPKHASRShiftImm(MI, 3, O); 
    return;
    break;
  case 6:
    // SXTAB, SXTAB16, SXTAH, UXTAB, UXTAB16, UXTAH, t2SXTAB, t2SXTAB16, t2SX...
    printRotImmOperand(MI, 3, O); 
    return;
    break;
  case 7:
    // USAT, t2USAT
    printShiftImmOperand(MI, 3, O); 
    return;
    break;
  case 8:
    // VLD3d16, VLD3d16_UPD, VLD3d32, VLD3d32_UPD, VLD3d8, VLD3d8_UPD, VLD3q1...
    SStream_concat0(O, "}, "); 
    break;
  case 9:
    // VLD4LNd16, VLD4LNd32, VLD4LNd8, VLD4LNq16, VLD4LNq32, VST2LNd16, VST2L...
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    break;
  case 10:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 11:
    // t2LDRD_POST, t2STRD_POST
    printT2AddrModeImm8s4OffsetOperand(MI, 4, O); 
    return;
    break;
  }


  // Fragment 8 encoded into 4 bits for 13 unique commands.
  //printf("Frag-8: %"PRIu64"\n", (Bits >> 56) & 15);
  switch ((Bits >> 56) & 15) {
  default:   // unreachable.
  case 0:
    // LDRD_POST, STRD_POST
    printAddrMode3OffsetOperand(MI, 4, O); 
    return;
    break;
  case 1:
    // MLA, MLS, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SMLALBT, SMLALD, SML...
    printOperand(MI, 3, O); 
    break;
  case 2:
    // SBFX, UBFX, t2SBFX, t2UBFX
    printImmPlusOneOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // VLD3d16, VLD3d32, VLD3d8, VLD3q16, VLD3q32, VLD3q8
    printAddrMode6Operand(MI, 3, O); 
    return;
    break;
  case 4:
    // VLD3d16_UPD, VLD3d32_UPD, VLD3d8_UPD, VLD3q16_UPD, VLD3q32_UPD, VLD3q8...
    printAddrMode6Operand(MI, 4, O); 
    printAddrMode6OffsetOperand(MI, 6, O); 
    return;
    break;
  case 5:
    // VLD4LNd16, VLD4LNd32, VLD4LNd8, VLD4LNq16, VLD4LNq32
    printNoHashImmediate(MI, 10, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 4, O); 
    return;
    break;
  case 6:
    // VST2LNd16, VST2LNd32, VST2LNd8, VST2LNq16, VST2LNq32
    printNoHashImmediate(MI, 4, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 7:
    // VST3LNd16, VST3LNd32, VST3LNd8, VST3LNq16, VST3LNq32
    printNoHashImmediate(MI, 5, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 5, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 8:
    // VST3d16, VST3d32, VST3d8, VST3q16, VST3q32, VST3q8
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 9:
    // VST4LNd16, VST4LNd32, VST4LNd8, VST4LNq16, VST4LNq32
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 10:
    // VST4d16, VST4d32, VST4d8, VST4q16, VST4q32, VST4q8
    printOperand(MI, 5, O); 
    SStream_concat0(O, "}, "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 11:
    // t2SMLSLDX
    printOperand(MI, 2, O); 
    return;
    break;
  case 12:
    // t2STLEXD, t2STREXD
    printAddrMode7Operand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 9 encoded into 1 bits for 2 unique commands.
  //printf("Frag-9: %"PRIu64"\n", (Bits >> 60) & 1);
  if ((Bits >> 60) & 1) {
    // VLD4d16, VLD4d16_UPD, VLD4d32, VLD4d32_UPD, VLD4d8, VLD4d8_UPD, VLD4q1...
    SStream_concat0(O, "}, "); 
  } else {
    // MLA, MLS, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SMLALBT, SMLALD, SML...
    return;
  }


  // Fragment 10 encoded into 1 bits for 2 unique commands.
  //printf("Frag-10: %"PRIu64"\n", (Bits >> 61) & 1);
  if ((Bits >> 61) & 1) {
    // VLD4d16_UPD, VLD4d32_UPD, VLD4d8_UPD, VLD4q16_UPD, VLD4q32_UPD, VLD4q8...
    printAddrMode6Operand(MI, 5, O); 
    printAddrMode6OffsetOperand(MI, 7, O); 
    return;
  } else {
    // VLD4d16, VLD4d32, VLD4d8, VLD4q16, VLD4q32, VLD4q8
    printAddrMode6Operand(MI, 4, O); 
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 289 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'D', '4', '_', 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', 0,
  /* 13 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', 0,
  /* 26 */ 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', 0,
  /* 39 */ 'd', '1', '0', 0,
  /* 43 */ 'q', '1', '0', 0,
  /* 47 */ 's', '1', '0', 0,
  /* 51 */ 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', 0,
  /* 67 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', 0,
  /* 83 */ 'd', '2', '0', 0,
  /* 87 */ 's', '2', '0', 0,
  /* 91 */ 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', '_', 'D', '3', '0', 0,
  /* 107 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', 0,
  /* 123 */ 'd', '3', '0', 0,
  /* 127 */ 's', '3', '0', 0,
  /* 131 */ 'd', '0', 0,
  /* 134 */ 'q', '0', 0,
  /* 137 */ 'm', 'v', 'f', 'r', '0', 0,
  /* 143 */ 's', '0', 0,
  /* 146 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', 0,
  /* 157 */ 'D', '5', '_', 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', 0,
  /* 170 */ 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', 0,
  /* 184 */ 'R', '1', '0', '_', 'R', '1', '1', 0,
  /* 192 */ 'd', '1', '1', 0,
  /* 196 */ 'q', '1', '1', 0,
  /* 200 */ 's', '1', '1', 0,
  /* 204 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', 0,
  /* 216 */ 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', 0,
  /* 232 */ 'd', '2', '1', 0,
  /* 236 */ 's', '2', '1', 0,
  /* 240 */ 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', 0,
  /* 252 */ 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', '_', 'D', '3', '1', 0,
  /* 268 */ 'd', '3', '1', 0,
  /* 272 */ 's', '3', '1', 0,
  /* 276 */ 'Q', '0', '_', 'Q', '1', 0,
  /* 282 */ 'R', '0', '_', 'R', '1', 0,
  /* 288 */ 'd', '1', 0,
  /* 291 */ 'q', '1', 0,
  /* 294 */ 'm', 'v', 'f', 'r', '1', 0,
  /* 300 */ 's', '1', 0,
  /* 303 */ 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', 0,
  /* 317 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', 0,
  /* 332 */ 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', 0,
  /* 347 */ 'd', '1', '2', 0,
  /* 351 */ 'q', '1', '2', 0,
  /* 355 */ 's', '1', '2', 0,
  /* 359 */ 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', 0,
  /* 375 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', 0,
  /* 391 */ 'd', '2', '2', 0,
  /* 395 */ 's', '2', '2', 0,
  /* 399 */ 'D', '0', '_', 'D', '2', 0,
  /* 405 */ 'D', '0', '_', 'D', '1', '_', 'D', '2', 0,
  /* 414 */ 'Q', '1', '_', 'Q', '2', 0,
  /* 420 */ 'd', '2', 0,
  /* 423 */ 'q', '2', 0,
  /* 426 */ 'm', 'v', 'f', 'r', '2', 0,
  /* 432 */ 's', '2', 0,
  /* 435 */ 'f', 'p', 'i', 'n', 's', 't', '2', 0,
  /* 443 */ 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', 0,
  /* 457 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', 0,
  /* 469 */ 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', 0,
  /* 485 */ 'd', '1', '3', 0,
  /* 489 */ 'q', '1', '3', 0,
  /* 493 */ 's', '1', '3', 0,
  /* 497 */ 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', 0,
  /* 513 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', 0,
  /* 525 */ 'd', '2', '3', 0,
  /* 529 */ 's', '2', '3', 0,
  /* 533 */ 'D', '1', '_', 'D', '3', 0,
  /* 539 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', 0,
  /* 548 */ 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', 0,
  /* 560 */ 'R', '2', '_', 'R', '3', 0,
  /* 566 */ 'd', '3', 0,
  /* 569 */ 'q', '3', 0,
  /* 572 */ 'r', '3', 0,
  /* 575 */ 's', '3', 0,
  /* 578 */ 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', 0,
  /* 593 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', 0,
  /* 609 */ 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', 0,
  /* 625 */ 'd', '1', '4', 0,
  /* 629 */ 'q', '1', '4', 0,
  /* 633 */ 's', '1', '4', 0,
  /* 637 */ 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', 0,
  /* 653 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', 0,
  /* 669 */ 'd', '2', '4', 0,
  /* 673 */ 's', '2', '4', 0,
  /* 677 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', 0,
  /* 686 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', '_', 'D', '4', 0,
  /* 698 */ 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', 0,
  /* 710 */ 'd', '4', 0,
  /* 713 */ 'q', '4', 0,
  /* 716 */ 'r', '4', 0,
  /* 719 */ 's', '4', 0,
  /* 722 */ 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', 0,
  /* 737 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', 0,
  /* 749 */ 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', 0,
  /* 765 */ 'd', '1', '5', 0,
  /* 769 */ 'q', '1', '5', 0,
  /* 773 */ 's', '1', '5', 0,
  /* 777 */ 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', 0,
  /* 793 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', 0,
  /* 805 */ 'd', '2', '5', 0,
  /* 809 */ 's', '2', '5', 0,
  /* 813 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', 0,
  /* 822 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', 0,
  /* 831 */ 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', 0,
  /* 843 */ 'R', '4', '_', 'R', '5', 0,
  /* 849 */ 'd', '5', 0,
  /* 852 */ 'q', '5', 0,
  /* 855 */ 'r', '5', 0,
  /* 858 */ 's', '5', 0,
  /* 861 */ 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', 0,
  /* 877 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', 0,
  /* 893 */ 'd', '1', '6', 0,
  /* 897 */ 's', '1', '6', 0,
  /* 901 */ 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', 0,
  /* 917 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', 0,
  /* 933 */ 'd', '2', '6', 0,
  /* 937 */ 's', '2', '6', 0,
  /* 941 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', '_', 'D', '6', 0,
  /* 953 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', '_', 'D', '6', 0,
  /* 965 */ 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', 0,
  /* 977 */ 'd', '6', 0,
  /* 980 */ 'q', '6', 0,
  /* 983 */ 'r', '6', 0,
  /* 986 */ 's', '6', 0,
  /* 989 */ 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', 0,
  /* 1005 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', 0,
  /* 1017 */ 'd', '1', '7', 0,
  /* 1021 */ 's', '1', '7', 0,
  /* 1025 */ 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', 0,
  /* 1041 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', 0,
  /* 1053 */ 'd', '2', '7', 0,
  /* 1057 */ 's', '2', '7', 0,
  /* 1061 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', '_', 'D', '7', 0,
  /* 1073 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', 0,
  /* 1082 */ 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', 0,
  /* 1094 */ 'R', '6', '_', 'R', '7', 0,
  /* 1100 */ 'd', '7', 0,
  /* 1103 */ 'q', '7', 0,
  /* 1106 */ 'r', '7', 0,
  /* 1109 */ 's', '7', 0,
  /* 1112 */ 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', 0,
  /* 1128 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', 0,
  /* 1144 */ 'd', '1', '8', 0,
  /* 1148 */ 's', '1', '8', 0,
  /* 1152 */ 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', 0,
  /* 1168 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', 0,
  /* 1184 */ 'd', '2', '8', 0,
  /* 1188 */ 's', '2', '8', 0,
  /* 1192 */ 'D', '2', '_', 'D', '4', '_', 'D', '6', '_', 'D', '8', 0,
  /* 1204 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', '_', 'D', '8', 0,
  /* 1216 */ 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', 0,
  /* 1228 */ 'd', '8', 0,
  /* 1231 */ 'q', '8', 0,
  /* 1234 */ 'r', '8', 0,
  /* 1237 */ 's', '8', 0,
  /* 1240 */ 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', 0,
  /* 1256 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', 0,
  /* 1268 */ 'd', '1', '9', 0,
  /* 1272 */ 's', '1', '9', 0,
  /* 1276 */ 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', 0,
  /* 1292 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', 0,
  /* 1304 */ 'd', '2', '9', 0,
  /* 1308 */ 's', '2', '9', 0,
  /* 1312 */ 'D', '3', '_', 'D', '5', '_', 'D', '7', '_', 'D', '9', 0,
  /* 1324 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', 0,
  /* 1333 */ 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', 0,
  /* 1345 */ 'R', '8', '_', 'R', '9', 0,
  /* 1351 */ 'd', '9', 0,
  /* 1354 */ 'q', '9', 0,
  /* 1357 */ 's', '9', 0,
  /* 1360 */ 'R', '1', '2', '_', 'S', 'P', 0,
  /* 1367 */ 's', 'b', 0,
  /* 1370 */ 'p', 'c', 0,
  /* 1373 */ 'f', 'p', 'e', 'x', 'c', 0,
  /* 1379 */ 'f', 'p', 's', 'i', 'd', 0,
  /* 1385 */ 'i', 't', 's', 't', 'a', 't', 'e', 0,
  /* 1393 */ 's', 'l', 0,
  /* 1396 */ 'f', 'p', 0,
  /* 1399 */ 'i', 'p', 0,
  /* 1402 */ 's', 'p', 0,
  /* 1405 */ 'f', 'p', 's', 'c', 'r', 0,
  /* 1411 */ 'l', 'r', 0,
  /* 1414 */ 'a', 'p', 's', 'r', 0,
  /* 1419 */ 'c', 'p', 's', 'r', 0,
  /* 1424 */ 's', 'p', 's', 'r', 0,
  /* 1429 */ 'f', 'p', 'i', 'n', 's', 't', 0,
  /* 1436 */ 'f', 'p', 's', 'c', 'r', '_', 'n', 'z', 'c', 'v', 0,
  /* 1447 */ 'a', 'p', 's', 'r', '_', 'n', 'z', 'c', 'v', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    1414, 1447, 1419, 1373, 1429, 1405, 1436, 1379, 1385, 1411, 1370, 1402, 1424, 131, 
    288, 420, 566, 710, 849, 977, 1100, 1228, 1351, 39, 192, 347, 485, 625, 
    765, 893, 1017, 1144, 1268, 83, 232, 391, 525, 669, 805, 933, 1053, 1184, 
    1304, 123, 268, 435, 137, 294, 426, 134, 291, 423, 569, 713, 852, 980, 
    1103, 1231, 1354, 43, 196, 351, 489, 629, 769, 140, 297, 429, 572, 716, 
    855, 983, 1106, 1234, 1367, 1393, 1396, 1399, 143, 300, 432, 575, 719, 858, 
    986, 1109, 1237, 1357, 47, 200, 355, 493, 633, 773, 897, 1021, 1148, 1272, 
    87, 236, 395, 529, 673, 809, 937, 1057, 1188, 1308, 127, 272, 399, 533, 
    680, 816, 947, 1067, 1198, 1318, 6, 163, 309, 449, 585, 729, 869, 997, 
    1120, 1248, 59, 224, 367, 505, 645, 785, 909, 1033, 1160, 1284, 99, 260, 
    276, 414, 554, 704, 837, 971, 1088, 1222, 1339, 32, 176, 339, 477, 617, 
    757, 548, 698, 831, 965, 1082, 1216, 1333, 26, 170, 332, 469, 609, 749, 
    1360, 282, 560, 843, 1094, 1345, 184, 405, 539, 689, 822, 956, 1073, 1207, 
    1324, 16, 146, 320, 457, 597, 737, 881, 1005, 1132, 1256, 71, 204, 379, 
    513, 657, 793, 921, 1041, 1172, 1292, 111, 240, 677, 813, 944, 1064, 1195, 
    1315, 3, 160, 306, 446, 581, 725, 865, 993, 1116, 1244, 55, 220, 363, 
    501, 641, 781, 905, 1029, 1156, 1280, 95, 256, 941, 1061, 1192, 1312, 0, 
    157, 303, 443, 578, 722, 861, 989, 1112, 1240, 51, 216, 359, 497, 637, 
    777, 901, 1025, 1152, 1276, 91, 252, 408, 692, 959, 1210, 19, 324, 601, 
    885, 1136, 75, 383, 661, 925, 1176, 115, 686, 953, 1204, 13, 317, 593, 
    877, 1128, 67, 375, 653, 917, 1168, 107, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

// get registers with number only
static char *getRegisterName2(unsigned RegNo)
{
  // assert(RegNo && RegNo < 289 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'D', '4', '_', 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', 0,
  /* 13 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', 0,
  /* 26 */ 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', 0,
  /* 39 */ 'd', '1', '0', 0,
  /* 43 */ 'q', '1', '0', 0,
  /* 47 */ 'r', '1', '0', 0,
  /* 51 */ 's', '1', '0', 0,
  /* 55 */ 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', 0,
  /* 71 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', 0,
  /* 87 */ 'd', '2', '0', 0,
  /* 91 */ 's', '2', '0', 0,
  /* 95 */ 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', '_', 'D', '3', '0', 0,
  /* 111 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', 0,
  /* 127 */ 'd', '3', '0', 0,
  /* 131 */ 's', '3', '0', 0,
  /* 135 */ 'd', '0', 0,
  /* 138 */ 'q', '0', 0,
  /* 141 */ 'm', 'v', 'f', 'r', '0', 0,
  /* 147 */ 's', '0', 0,
  /* 150 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', 0,
  /* 161 */ 'D', '5', '_', 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', 0,
  /* 174 */ 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', 0,
  /* 188 */ 'R', '1', '0', '_', 'R', '1', '1', 0,
  /* 196 */ 'd', '1', '1', 0,
  /* 200 */ 'q', '1', '1', 0,
  /* 204 */ 'r', '1', '1', 0,
  /* 208 */ 's', '1', '1', 0,
  /* 212 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', 0,
  /* 224 */ 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', 0,
  /* 240 */ 'd', '2', '1', 0,
  /* 244 */ 's', '2', '1', 0,
  /* 248 */ 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', 0,
  /* 260 */ 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', '_', 'D', '3', '1', 0,
  /* 276 */ 'd', '3', '1', 0,
  /* 280 */ 's', '3', '1', 0,
  /* 284 */ 'Q', '0', '_', 'Q', '1', 0,
  /* 290 */ 'R', '0', '_', 'R', '1', 0,
  /* 296 */ 'd', '1', 0,
  /* 299 */ 'q', '1', 0,
  /* 302 */ 'm', 'v', 'f', 'r', '1', 0,
  /* 308 */ 's', '1', 0,
  /* 311 */ 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', 0,
  /* 325 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', 0,
  /* 340 */ 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', 0,
  /* 355 */ 'd', '1', '2', 0,
  /* 359 */ 'q', '1', '2', 0,
  /* 363 */ 'r', '1', '2', 0,
  /* 367 */ 's', '1', '2', 0,
  /* 371 */ 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', 0,
  /* 387 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', 0,
  /* 403 */ 'd', '2', '2', 0,
  /* 407 */ 's', '2', '2', 0,
  /* 411 */ 'D', '0', '_', 'D', '2', 0,
  /* 417 */ 'D', '0', '_', 'D', '1', '_', 'D', '2', 0,
  /* 426 */ 'Q', '1', '_', 'Q', '2', 0,
  /* 432 */ 'd', '2', 0,
  /* 435 */ 'q', '2', 0,
  /* 438 */ 'm', 'v', 'f', 'r', '2', 0,
  /* 444 */ 's', '2', 0,
  /* 447 */ 'f', 'p', 'i', 'n', 's', 't', '2', 0,
  /* 455 */ 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', 0,
  /* 469 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', 0,
  /* 481 */ 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', 0,
  /* 497 */ 'd', '1', '3', 0,
  /* 501 */ 'q', '1', '3', 0,
  /* 505 */ 's', '1', '3', 0,
  /* 509 */ 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', 0,
  /* 525 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', 0,
  /* 537 */ 'd', '2', '3', 0,
  /* 541 */ 's', '2', '3', 0,
  /* 545 */ 'D', '1', '_', 'D', '3', 0,
  /* 551 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', 0,
  /* 560 */ 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', 0,
  /* 572 */ 'R', '2', '_', 'R', '3', 0,
  /* 578 */ 'd', '3', 0,
  /* 581 */ 'q', '3', 0,
  /* 584 */ 'r', '3', 0,
  /* 587 */ 's', '3', 0,
  /* 590 */ 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', 0,
  /* 605 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', 0,
  /* 621 */ 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', 0,
  /* 637 */ 'd', '1', '4', 0,
  /* 641 */ 'q', '1', '4', 0,
  /* 645 */ 's', '1', '4', 0,
  /* 649 */ 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', 0,
  /* 665 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', 0,
  /* 681 */ 'd', '2', '4', 0,
  /* 685 */ 's', '2', '4', 0,
  /* 689 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', 0,
  /* 698 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', '_', 'D', '4', 0,
  /* 710 */ 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', 0,
  /* 722 */ 'd', '4', 0,
  /* 725 */ 'q', '4', 0,
  /* 728 */ 'r', '4', 0,
  /* 731 */ 's', '4', 0,
  /* 734 */ 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', 0,
  /* 749 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', 0,
  /* 761 */ 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', 0,
  /* 777 */ 'd', '1', '5', 0,
  /* 781 */ 'q', '1', '5', 0,
  /* 785 */ 's', '1', '5', 0,
  /* 789 */ 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', 0,
  /* 805 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', 0,
  /* 817 */ 'd', '2', '5', 0,
  /* 821 */ 's', '2', '5', 0,
  /* 825 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', 0,
  /* 834 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', 0,
  /* 843 */ 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', 0,
  /* 855 */ 'R', '4', '_', 'R', '5', 0,
  /* 861 */ 'd', '5', 0,
  /* 864 */ 'q', '5', 0,
  /* 867 */ 'r', '5', 0,
  /* 870 */ 's', '5', 0,
  /* 873 */ 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', 0,
  /* 889 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', 0,
  /* 905 */ 'd', '1', '6', 0,
  /* 909 */ 's', '1', '6', 0,
  /* 913 */ 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', 0,
  /* 929 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', 0,
  /* 945 */ 'd', '2', '6', 0,
  /* 949 */ 's', '2', '6', 0,
  /* 953 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', '_', 'D', '6', 0,
  /* 965 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', '_', 'D', '6', 0,
  /* 977 */ 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', 0,
  /* 989 */ 'd', '6', 0,
  /* 992 */ 'q', '6', 0,
  /* 995 */ 'r', '6', 0,
  /* 998 */ 's', '6', 0,
  /* 1001 */ 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', 0,
  /* 1017 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', 0,
  /* 1029 */ 'd', '1', '7', 0,
  /* 1033 */ 's', '1', '7', 0,
  /* 1037 */ 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', 0,
  /* 1053 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', 0,
  /* 1065 */ 'd', '2', '7', 0,
  /* 1069 */ 's', '2', '7', 0,
  /* 1073 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', '_', 'D', '7', 0,
  /* 1085 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', 0,
  /* 1094 */ 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', 0,
  /* 1106 */ 'R', '6', '_', 'R', '7', 0,
  /* 1112 */ 'd', '7', 0,
  /* 1115 */ 'q', '7', 0,
  /* 1118 */ 'r', '7', 0,
  /* 1121 */ 's', '7', 0,
  /* 1124 */ 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', 0,
  /* 1140 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', 0,
  /* 1156 */ 'd', '1', '8', 0,
  /* 1160 */ 's', '1', '8', 0,
  /* 1164 */ 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', 0,
  /* 1180 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', 0,
  /* 1196 */ 'd', '2', '8', 0,
  /* 1200 */ 's', '2', '8', 0,
  /* 1204 */ 'D', '2', '_', 'D', '4', '_', 'D', '6', '_', 'D', '8', 0,
  /* 1216 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', '_', 'D', '8', 0,
  /* 1228 */ 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', 0,
  /* 1240 */ 'd', '8', 0,
  /* 1243 */ 'q', '8', 0,
  /* 1246 */ 'r', '8', 0,
  /* 1249 */ 's', '8', 0,
  /* 1252 */ 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', 0,
  /* 1268 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', 0,
  /* 1280 */ 'd', '1', '9', 0,
  /* 1284 */ 's', '1', '9', 0,
  /* 1288 */ 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', 0,
  /* 1304 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', 0,
  /* 1316 */ 'd', '2', '9', 0,
  /* 1320 */ 's', '2', '9', 0,
  /* 1324 */ 'D', '3', '_', 'D', '5', '_', 'D', '7', '_', 'D', '9', 0,
  /* 1336 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', 0,
  /* 1345 */ 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', 0,
  /* 1357 */ 'R', '8', '_', 'R', '9', 0,
  /* 1363 */ 'd', '9', 0,
  /* 1366 */ 'q', '9', 0,
  /* 1369 */ 'r', '9', 0,
  /* 1372 */ 's', '9', 0,
  /* 1375 */ 'R', '1', '2', '_', 'S', 'P', 0,
  /* 1382 */ 'p', 'c', 0,
  /* 1385 */ 'f', 'p', 'e', 'x', 'c', 0,
  /* 1391 */ 'f', 'p', 's', 'i', 'd', 0,
  /* 1397 */ 'i', 't', 's', 't', 'a', 't', 'e', 0,
  /* 1405 */ 's', 'p', 0,
  /* 1408 */ 'f', 'p', 's', 'c', 'r', 0,
  /* 1414 */ 'l', 'r', 0,
  /* 1417 */ 'a', 'p', 's', 'r', 0,
  /* 1422 */ 'c', 'p', 's', 'r', 0,
  /* 1427 */ 's', 'p', 's', 'r', 0,
  /* 1432 */ 'f', 'p', 'i', 'n', 's', 't', 0,
  /* 1439 */ 'f', 'p', 's', 'c', 'r', '_', 'n', 'z', 'c', 'v', 0,
  /* 1450 */ 'a', 'p', 's', 'r', '_', 'n', 'z', 'c', 'v', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    1417, 1450, 1422, 1385, 1432, 1408, 1439, 1391, 1397, 1414, 1382, 1405, 1427, 135, 
    296, 432, 578, 722, 861, 989, 1112, 1240, 1363, 39, 196, 355, 497, 637, 
    777, 905, 1029, 1156, 1280, 87, 240, 403, 537, 681, 817, 945, 1065, 1196, 
    1316, 127, 276, 447, 141, 302, 438, 138, 299, 435, 581, 725, 864, 992, 
    1115, 1243, 1366, 43, 200, 359, 501, 641, 781, 144, 305, 441, 584, 728, 
    867, 995, 1118, 1246, 1369, 47, 204, 363, 147, 308, 444, 587, 731, 870, 
    998, 1121, 1249, 1372, 51, 208, 367, 505, 645, 785, 909, 1033, 1160, 1284, 
    91, 244, 407, 541, 685, 821, 949, 1069, 1200, 1320, 131, 280, 411, 545, 
    692, 828, 959, 1079, 1210, 1330, 6, 167, 317, 461, 597, 741, 881, 1009, 
    1132, 1260, 63, 232, 379, 517, 657, 797, 921, 1045, 1172, 1296, 103, 268, 
    284, 426, 566, 716, 849, 983, 1100, 1234, 1351, 32, 180, 347, 489, 629, 
    769, 560, 710, 843, 977, 1094, 1228, 1345, 26, 174, 340, 481, 621, 761, 
    1375, 290, 572, 855, 1106, 1357, 188, 417, 551, 701, 834, 968, 1085, 1219, 
    1336, 16, 150, 328, 469, 609, 749, 893, 1017, 1144, 1268, 75, 212, 391, 
    525, 669, 805, 933, 1053, 1184, 1304, 115, 248, 689, 825, 956, 1076, 1207, 
    1327, 3, 164, 314, 458, 593, 737, 877, 1005, 1128, 1256, 59, 228, 375, 
    513, 653, 793, 917, 1041, 1168, 1292, 99, 264, 953, 1073, 1204, 1324, 0, 
    161, 311, 455, 590, 734, 873, 1001, 1124, 1252, 55, 224, 371, 509, 649, 
    789, 913, 1037, 1164, 1288, 95, 260, 420, 704, 971, 1222, 19, 332, 613, 
    897, 1148, 79, 395, 673, 937, 1188, 119, 698, 965, 1216, 13, 325, 605, 
    889, 1140, 71, 387, 665, 929, 1180, 111, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printPredicateOperand(MI, OpIdx, OS);
    break;
  case 1:
    printSBitModifierOperand(MI, OpIdx, OS);
    break;
  case 2:
    printFPImmOperand(MI, OpIdx, OS);
    break;
  case 3:
    printRegisterList(MI, OpIdx, OS);
    break;
  case 4:
    printPImmediate(MI, OpIdx, OS);
    break;
  case 5:
    printCImmediate(MI, OpIdx, OS);
    break;
  case 6:
    printImmPlusOneOperand(MI, OpIdx, OS);
    break;
  case 7:
    printAddrMode5Operand(MI, OpIdx, OS, false);
    break;
  case 8:
    printNEONModImmOperand(MI, OpIdx, OS);
    break;
  case 9:
    printT2SOOperand(MI, OpIdx, OS);
    break;
  case 10:
    printAdrLabelOperand<0>(MI, OpIdx, OS, 0);
    break;
  case 11:
    printThumbSRImm(MI, OpIdx, OS);
    break;
  case 12:
    printAddrModeImm12Operand(MI, OpIdx, OS, false);
    break;
  case 13:
    printThumbLdrLabelOperand(MI, OpIdx, OS);
    break;
  case 14:
    printT2AddrModeSoRegOperand(MI, OpIdx, OS);
    break;
  case 15:
    printRotImmOperand(MI, OpIdx, OS);
    break;
  case 16:
    printCPSIMod(MI, OpIdx, OS);
    break;
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case ARM_ANDri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (ANDri rGPR:$Rd, rGPR:$Rn, so_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (ANDri rGPR:$Rdn, rGPR:$Rdn, so_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_BICri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (BICri rGPR:$Rd, rGPR:$Rn, so_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (BICri rGPR:$Rdn, rGPR:$Rdn, so_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_BKPT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (BKPT 0)
      AsmString = "bkpt";
      break;
    }
    return NULL;
  case ARM_CMNri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (CMNri rGPR:$Rd, so_imm_neg:$imm, pred:$p)
      AsmString = "cmp$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_CMPri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (CMPri rGPR:$Rd, so_imm_neg:$imm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_DMB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (DMB 15)
      AsmString = "dmb";
      break;
    }
    return NULL;
  case ARM_DSB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (DSB 15)
      AsmString = "dsb";
      break;
    }
    return NULL;
  case ARM_FCONSTD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (FCONSTD DPR:$Dd, vfp_f64imm:$val, pred:$p)
      AsmString = "fconstd$\xFF\x03\x01} $\x01, $\xFF\x02\x03";
      break;
    }
    return NULL;
  case ARM_FCONSTS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (FCONSTS SPR:$Sd, vfp_f32imm:$val, pred:$p)
      AsmString = "fconsts$\xFF\x03\x01} $\x01, $\xFF\x02\x03";
      break;
    }
    return NULL;
  case ARM_FMSTAT:
    if (MCInst_getNumOperands(MI) == 2) {
      // (FMSTAT pred:$p)
      AsmString = "fmstat$\xFF\x01\x01}";
      break;
    }
    return NULL;
  case ARM_HINT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (HINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (HINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (HINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (HINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (HINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (HINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_ISB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (ISB 15)
      AsmString = "isb";
      break;
    }
    return NULL;
  case ARM_LDMIA_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_SP) {
      // (LDMIA_UPD SP, pred:$p, reglist:$regs)
      AsmString = "pop$\xFF\x02\x01} $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_MCR:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MCR p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mcr$\xFF\x07\x01} $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MCR2:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MCR2 p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0)
      AsmString = "mcr2 $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MLA:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 3)) {
      // (MLA GPRnopc:$Rd, GPRnopc:$Rn, GPRnopc:$Rm, GPRnopc:$Ra, pred:$p, cc_out:$s)
      AsmString = "mla$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_MOVi:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (MOVi rGPR:$Rd, so_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x05\x02}$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_MOVi16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (MOVi16 GPR:$Rd, imm0_65535_expr:$imm, pred:$p)
      AsmString = "mov$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_MRC:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MRC GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mrc$\xFF\x07\x01} $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MRC2:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MRC2 GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0)
      AsmString = "mrc2 $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MRS:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (MRS GPRnopc:$Rd, pred:$p)
      AsmString = "mrs$\xFF\x02\x01} $\x01, cpsr";
      break;
    }
    return NULL;
  case ARM_MUL:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2)) {
      // (MUL GPRnopc:$Rd, GPRnopc:$Rn, GPRnopc:$Rm, pred:$p, cc_out:$s)
      AsmString = "mul$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_MVNi:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (MVNi rGPR:$Rd, so_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "mov$\xFF\x05\x02}$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_RSBri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (RSBri GPR:$Rd, GPR:$Rm, 0, pred:$p, cc_out:$s)
      AsmString = "neg$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_SMLAL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (SMLAL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "smlal$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_SMULL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (SMULL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "smull$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_SRSDA:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDA imm0_31:$mode)
      AsmString = "srsda $\x01";
      break;
    }
    return NULL;
  case ARM_SRSDA_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDA_UPD imm0_31:$mode)
      AsmString = "srsda $\x01!";
      break;
    }
    return NULL;
  case ARM_SRSDB:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDB imm0_31:$mode)
      AsmString = "srsdb $\x01";
      break;
    }
    return NULL;
  case ARM_SRSDB_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDB_UPD imm0_31:$mode)
      AsmString = "srsdb $\x01!";
      break;
    }
    return NULL;
  case ARM_SRSIA:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIA imm0_31:$mode)
      AsmString = "srsia $\x01";
      break;
    }
    return NULL;
  case ARM_SRSIA_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIA_UPD imm0_31:$mode)
      AsmString = "srsia $\x01!";
      break;
    }
    return NULL;
  case ARM_SRSIB:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIB imm0_31:$mode)
      AsmString = "srsib $\x01";
      break;
    }
    return NULL;
  case ARM_SRSIB_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIB_UPD imm0_31:$mode)
      AsmString = "srsib $\x01!";
      break;
    }
    return NULL;
  case ARM_SSAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SSAT GPRnopc:$Rd, imm1_32:$sat_imm, GPRnopc:$Rn, 0, pred:$p)
      AsmString = "ssat$\xFF\x05\x01} $\x01, $\xFF\x02\x07, $\x03";
      break;
    }
    return NULL;
  case ARM_STMDB_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_SP) {
      // (STMDB_UPD SP, pred:$p, reglist:$regs)
      AsmString = "push$\xFF\x02\x01} $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_SUBri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1)) {
      // (SUBri GPR:$Rd, GPR:$Rn, so_imm_neg:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (SUBri GPR:$Rd, GPR:$Rd, so_imm_neg:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SXTAB GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtab$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SXTAB16 GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtab16$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SXTAH GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtah$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SXTB GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtb$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_SXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SXTB16 GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtb16$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_SXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SXTH GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxth$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_UMLAL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (UMLAL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "umlal$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_UMULL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (UMULL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "umull$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_USAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (USAT GPRnopc:$Rd, imm0_31:$sat_imm, GPRnopc:$Rn, 0, pred:$p)
      AsmString = "usat$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (UXTAB GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtab$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (UXTAB16 GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtab16$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (UXTAH GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtah$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (UXTB GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtb$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_UXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (UXTB16 GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtb16$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_UXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (UXTH GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxth$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGEd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VACGEd DPR:$Vd, DPR:$Vm, DPR:$Vn, pred:$p)
      AsmString = "vacle$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGEd DPR:$Vd, DPR:$Vm, DPR:$Vd, pred:$p)
      AsmString = "vacle$\xFF\x04\x01}.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGEq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VACGEq QPR:$Vd, QPR:$Vm, QPR:$Vn, pred:$p)
      AsmString = "vacle$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGEq QPR:$Vd, QPR:$Vm, QPR:$Vd, pred:$p)
      AsmString = "vacle$\xFF\x04\x01}.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGTd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VACGTd DPR:$Vd, DPR:$Vm, DPR:$Vn, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGTd DPR:$Vd, DPR:$Vm, DPR:$Vd, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01}.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGTq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VACGTq QPR:$Vd, QPR:$Vm, QPR:$Vn, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGTq QPR:$Vd, QPR:$Vm, QPR:$Vd, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01}.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VADDD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VADDD DPR:$Dd, DPR:$Dn, DPR:$Dm, pred:$p)
      AsmString = "faddd$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VADDS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 2)) {
      // (VADDS SPR:$Sd, SPR:$Sn, SPR:$Sm, pred:$p)
      AsmString = "fadds$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VCGEfd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEfd DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEfq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEfq QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEsv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEsv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEsv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEsv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEsv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEsv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEuv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEuv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEuv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEuv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEuv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEuv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01}.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTfd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTfd DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTfq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTfq QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTsv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTsv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTsv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTsv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTsv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTsv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTuv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTuv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTuv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTuv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTuv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTuv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01}.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCMPZD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VCMPZD DPR:$val, pred:$p)
      AsmString = "fcmpzd$\xFF\x02\x01} $\x01";
      break;
    }
    return NULL;
  case ARM_VCMPZS:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (VCMPZS SPR:$val, pred:$p)
      AsmString = "fcmpzs$\xFF\x02\x01} $\x01";
      break;
    }
    return NULL;
  case ARM_VLDRD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VLDRD DPR:$Dd, addrmode5:$addr, pred:$p)
      AsmString = "vldr$\xFF\x04\x01}.64 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VLDRS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (VLDRS SPR:$Sd, addrmode5:$addr, pred:$p)
      AsmString = "vldr$\xFF\x04\x01}.32 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VMOVDRR:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2)) {
      // (VMOVDRR DPR:$Dn, GPR:$Rt, GPR:$Rt2, pred:$p)
      AsmString = "vmov$\xFF\x04\x01}.f64 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VMOVRRD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VMOVRRD GPR:$Rt, GPR:$Rt2, DPR:$Dn, pred:$p)
      AsmString = "vmov$\xFF\x04\x01}.f64 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VMOVS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VMOVS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vmov$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VMVNv2i32:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VMVNv2i32 DPR:$Vd, nImmVMOVI32Neg:$imm, pred:$p)
      AsmString = "vmov$\xFF\x03\x01}.i32 $\x01, $\xFF\x02\x09";
      break;
    }
    return NULL;
  case ARM_VMVNv4i32:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0)) {
      // (VMVNv4i32 QPR:$Vd, nImmVMOVI32Neg:$imm, pred:$p)
      AsmString = "vmov$\xFF\x03\x01}.i32 $\x01, $\xFF\x02\x09";
      break;
    }
    return NULL;
  case ARM_VRINTAD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTAD DPR:$Dd, DPR:$Dm)
      AsmString = "vrinta.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTAND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTAND DPR:$Dd, DPR:$Dm)
      AsmString = "vrinta.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTANQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTANQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrinta.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTAS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTAS SPR:$Sd, SPR:$Sm)
      AsmString = "vrinta.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTMD DPR:$Dd, DPR:$Dm)
      AsmString = "vrintm.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTMND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintm.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTMNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintm.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTMS SPR:$Sd, SPR:$Sm)
      AsmString = "vrintm.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintn.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTNND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTNND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintn.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTNNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTNNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintn.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTNS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTNS SPR:$Sd, SPR:$Sm)
      AsmString = "vrintn.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTPD DPR:$Dd, DPR:$Dm)
      AsmString = "vrintp.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTPND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintp.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTPNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintp.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTPS SPR:$Sd, SPR:$Sm)
      AsmString = "vrintp.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTRD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTRD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vrintr$\xFF\x03\x01.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTRS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTRS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vrintr$\xFF\x03\x01.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTXD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vrintx$\xFF\x03\x01.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTXND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintx.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTXNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintx.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTXS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vrintx$\xFF\x03\x01.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTZD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vrintz$\xFF\x03\x01.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTZND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintz.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTZNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintz.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTZS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vrintz$\xFF\x03\x01.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSETLNi32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (VSETLNi32 DPR:$Dd, GPR:$Rn, 1, pred:$p)
      AsmString = "fmdhr$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (VSETLNi32 DPR:$Dd, GPR:$Rn, 0, pred:$p)
      AsmString = "fmdlr$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSQRTD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VSQRTD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vsqrt$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSQRTS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VSQRTS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vsqrt$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSTRD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VSTRD DPR:$Dd, addrmode5:$addr, pred:$p)
      AsmString = "vstr$\xFF\x04\x01}.64 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VSTRS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (VSTRS SPR:$Sd, addrmode5:$addr, pred:$p)
      AsmString = "vstr$\xFF\x04\x01}.32 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VSUBD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VSUBD DPR:$Dd, DPR:$Dn, DPR:$Dm, pred:$p)
      AsmString = "fsubd$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VSUBS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 2)) {
      // (VSUBS SPR:$Sd, SPR:$Sn, SPR:$Sm, pred:$p)
      AsmString = "fsubs$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADCrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ADCrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "adc$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADCrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ADCrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "adc$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ADDri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1)) {
      // (t2ADDri GPRnopc:$Rd, GPRnopc:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ADDri GPRnopc:$Rdn, GPRnopc:$Rdn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADDri12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1)) {
      // (t2ADDri12 GPRnopc:$Rd, GPR:$Rn, imm0_4095:$imm, pred:$p)
      AsmString = "add$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ADDri12 GPRnopc:$Rdn, GPRnopc:$Rdn, imm0_4095:$imm, pred:$p)
      AsmString = "add$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADDrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ADDrr GPRnopc:$Rd, GPRnopc:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ADDrr GPRnopc:$Rdn, GPRnopc:$Rdn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADDrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1)) {
      // (t2ADDrs GPRnopc:$Rd, GPRnopc:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ADDrs GPRnopc:$Rdn, GPRnopc:$Rdn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ADR:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2ADR rGPR:$Rd, t2adrlabel:$addr, pred:$p)
      AsmString = "adr$\xFF\x03\x01} $\x01, $\xFF\x02\x0B";
      break;
    }
    return NULL;
  case ARM_t2ANDrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ANDrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ANDrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ANDrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ASRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ASRri rGPR:$Rd, rGPR:$Rn, imm_sr:$imm, pred:$p, cc_out:$s)
      AsmString = "asr$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\xFF\x03\x0C";
      break;
    }
    return NULL;
  case ARM_t2ASRrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ASRrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "asr$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2BICrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2BICrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2BICrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2BICrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2CMNri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMNri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2CMNri rGPR:$Rd, t2_so_imm_neg:$imm, pred:$p)
      AsmString = "cmp$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2CMNzrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2CMNzrr GPRnopc:$Rn, rGPR:$Rm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2CMNzrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMNzrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "cmn$\xFF\x04\x01} $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2CMPri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2CMPri rGPR:$Rd, t2_so_imm_neg:$imm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMPri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "cmp$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2CMPrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMPrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "cmp$\xFF\x04\x01} $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2DMB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (t2DMB 15, pred:$p)
      AsmString = "dmb$\xFF\x02\x01}";
      break;
    }
    return NULL;
  case ARM_t2DSB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (t2DSB 15, pred:$p)
      AsmString = "dsb$\xFF\x02\x01}";
      break;
    }
    return NULL;
  case ARM_t2EORri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2EORri rGPR:$Rd, rGPR:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "eor$\xFF\x06\x02}$\xFF\x04\x01}.w $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2EORrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2EORrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "eor$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2EORrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2EORrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "eor$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2HINT:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2HINT imm0_239:$imm, pred:$p)
      AsmString = "hint$\xFF\x02\x01 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (t2HINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (t2HINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (t2HINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (t2HINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (t2HINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (t2HINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01.w";
      break;
    }
    return NULL;
  case ARM_t2ISB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (t2ISB 15, pred:$p)
      AsmString = "isb$\xFF\x02\x01}";
      break;
    }
    return NULL;
  case ARM_t2LDMDB:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMDB GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldmdb$\xFF\x02\x01}.w $\x01, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDMDB_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMDB_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldmdb$\xFF\x02\x01}.w $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDMIA:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMIA GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldm$\xFF\x02\x01} $\x01, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDMIA_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMIA_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldm$\xFF\x02\x01} $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDRBi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRBi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRBpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRBpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x03\x01} $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRBpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRBpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x03\x01}.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRBs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRBs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRHi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRHi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRHpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRHpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x03\x01} $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRHpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRHpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x03\x01}.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRHs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRHs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRSBi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSBi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRSBpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSBpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x03\x01} $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRSBpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRSBpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x03\x01}.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRSBs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSBs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRSHi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSHi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRSHpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSHpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x03\x01} $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRSHpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRSHpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x03\x01}.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRSHs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSHs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDRi12 GPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldr$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRpci GPRnopc:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldr$\xFF\x03\x01} $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDRs GPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldr$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LSLri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2LSLri rGPR:$Rd, rGPR:$Rn, imm0_31:$imm, pred:$p, cc_out:$s)
      AsmString = "lsl$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2LSLrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2LSLrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "lsl$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2LSRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2LSRri rGPR:$Rd, rGPR:$Rn, imm_sr:$imm, pred:$p, cc_out:$s)
      AsmString = "lsr$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\xFF\x03\x0C";
      break;
    }
    return NULL;
  case ARM_t2LSRrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2LSRrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "lsr$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2MCR:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MCR p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mcr$\xFF\x07\x01} $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MCR2:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MCR2 p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mcr2$\xFF\x07\x01} $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MOVi16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2MOVi16 rGPR:$Rd, imm256_65535_expr:$imm, pred:$p)
      AsmString = "mov$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MRC:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MRC GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mrc$\xFF\x07\x01} $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MRC2:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MRC2 GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mrc2$\xFF\x07\x01} $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MRS_AR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2MRS_AR GPR:$Rd, pred:$p)
      AsmString = "mrs$\xFF\x02\x01} $\x01, cpsr";
      break;
    }
    return NULL;
  case ARM_t2MUL:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2MUL rGPR:$Rn, rGPR:$Rm, rGPR:$Rn, pred:$p)
      AsmString = "mul$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MVNi:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2MVNi rGPR:$Rd, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x05\x02}$\xFF\x03\x01}.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MVNr:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2MVNr rGPR:$Rd, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x05\x02}$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MVNs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2MVNs rGPR:$Rd, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2ORNri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ORNri rGPR:$Rdn, rGPR:$Rdn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "orn$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORNrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ORNrr rGPR:$Rdn, rGPR:$Rdn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "orn$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORNrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ORNrs rGPR:$Rdn, rGPR:$Rdn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "orn$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ORRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ORRri rGPR:$Rd, rGPR:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "orr$\xFF\x06\x02}$\xFF\x04\x01}.w $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORRrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ORRrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "orr$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORRrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ORRrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "orr$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2PLDpci:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2PLDpci t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "pld$\xFF\x02\x01} $\x01";
      break;
    }
    return NULL;
  case ARM_t2PLIpci:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2PLIpci t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "pli$\xFF\x02\x01} $\x01";
      break;
    }
    return NULL;
  case ARM_t2REV:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2REV rGPR:$Rd, rGPR:$Rm, pred:$p)
      AsmString = "rev$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2REV16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2REV16 rGPR:$Rd, rGPR:$Rm, pred:$p)
      AsmString = "rev16$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2REVSH:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2REVSH rGPR:$Rd, rGPR:$Rm, pred:$p)
      AsmString = "revsh$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2RORri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2RORri rGPR:$Rd, rGPR:$Rn, imm0_31:$imm, pred:$p, cc_out:$s)
      AsmString = "ror$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2RORrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2RORrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "ror$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2RSBri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2RSBri rGPR:$Rd, rGPR:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2RSBri rGPR:$Rdn, rGPR:$Rdn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (t2RSBri rGPR:$Rd, rGPR:$Rm, 0, pred:$p, cc_out:$s)
      AsmString = "neg$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2RSBrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2RSBrr rGPR:$Rdn, rGPR:$Rdn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2RSBrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2RSBrs rGPR:$Rdn, rGPR:$Rdn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2SBCrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2SBCrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "sbc$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SBCrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SBCrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "sbc$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2SRSDB:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSDB imm0_31:$mode, pred:$p)
      AsmString = "srsdb$\xFF\x02\x01} $\x01";
      break;
    }
    return NULL;
  case ARM_t2SRSDB_UPD:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSDB_UPD imm0_31:$mode, pred:$p)
      AsmString = "srsdb$\xFF\x02\x01} $\x01!";
      break;
    }
    return NULL;
  case ARM_t2SRSIA:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSIA imm0_31:$mode, pred:$p)
      AsmString = "srsia$\xFF\x02\x01} $\x01";
      break;
    }
    return NULL;
  case ARM_t2SRSIA_UPD:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSIA_UPD imm0_31:$mode, pred:$p)
      AsmString = "srsia$\xFF\x02\x01} $\x01!";
      break;
    }
    return NULL;
  case ARM_t2SSAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SSAT rGPR:$Rd, imm1_32:$sat_imm, rGPR:$Rn, 0, pred:$p)
      AsmString = "ssat$\xFF\x05\x01} $\x01, $\xFF\x02\x07, $\x03";
      break;
    }
    return NULL;
  case ARM_t2STMDB:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STMDB GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "stmdb$\xFF\x02\x01}.w $\x01, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2STMDB_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STMDB_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "stmdb$\xFF\x02\x01}.w $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2STMIA_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STMIA_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "stm$\xFF\x02\x01} $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2STRBi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRBi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "strb$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2STRBs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRBs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "strb$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2STRHi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRHi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "strh$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2STRHs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRHs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "strh$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2STRi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STRi12 GPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "str$\xFF\x04\x01} $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2STRs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STRs GPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "str$\xFF\x05\x01} $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2SUBrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2SUBrr GPRnopc:$Rd, GPRnopc:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "sub$\xFF\x06\x02}$\xFF\x04\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SUBrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1)) {
      // (t2SUBrs GPRnopc:$Rd, GPRnopc:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "sub$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2SUBrs GPRnopc:$Rdn, GPRnopc:$Rdn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "sub$\xFF\x07\x02}$\xFF\x05\x01} $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2SXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SXTAB rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtab$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SXTAB16 rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtab16$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SXTAH rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtah$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SXTB rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "sxtb$\xFF\x04\x01} $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2SXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (t2SXTB16 rGPR:$Rd, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtb16$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SXTB16 rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "sxtb16$\xFF\x04\x01} $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2SXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SXTH rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "sxth$\xFF\x04\x01} $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2TEQri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TEQri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "teq$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TEQrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2TEQrr GPRnopc:$Rn, rGPR:$Rm, pred:$p)
      AsmString = "teq$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TEQrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TEQrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "teq$\xFF\x04\x01} $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2TSTri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TSTri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "tst$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TSTrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2TSTrr GPRnopc:$Rn, rGPR:$Rm, pred:$p)
      AsmString = "tst$\xFF\x03\x01} $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TSTrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TSTrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "tst$\xFF\x04\x01} $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2USAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2USAT rGPR:$Rd, imm0_31:$sat_imm, rGPR:$Rn, 0, pred:$p)
      AsmString = "usat$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2UXTAB rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtab$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2UXTAB16 rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtab16$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2UXTAH rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtah$\xFF\x05\x01} $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2UXTB rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "uxtb$\xFF\x04\x01} $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2UXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (t2UXTB16 rGPR:$Rd, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtb16$\xFF\x04\x01} $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2UXTB16 rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "uxtb16$\xFF\x04\x01} $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2UXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2UXTH rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "uxth$\xFF\x04\x01} $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_tASRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (tASRri tGPR:$Rdm, cc_out:$s, tGPR:$Rdm, imm_sr:$imm, pred:$p)
      AsmString = "asr$\xFF\x02\x02}$\xFF\x05\x01} $\x01, $\xFF\x04\x0C";
      break;
    }
    return NULL;
  case ARM_tBKPT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (tBKPT 0)
      AsmString = "bkpt";
      break;
    }
    return NULL;
  case ARM_tHINT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (tHINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (tHINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (tHINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (tHINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (tHINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (tHINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_tLDMIA:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0)) {
      // (tLDMIA tGPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldm$\xFF\x02\x01} $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_tLSLri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (tLSLri tGPR:$Rdm, cc_out:$s, tGPR:$Rdm, imm0_31:$imm, pred:$p)
      AsmString = "lsl$\xFF\x02\x02}$\xFF\x05\x01} $\x01, $\x04";
      break;
    }
    return NULL;
  case ARM_tLSRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (tLSRri tGPR:$Rdm, cc_out:$s, tGPR:$Rdm, imm_sr:$imm, pred:$p)
      AsmString = "lsr$\xFF\x02\x02}$\xFF\x05\x01} $\x01, $\xFF\x04\x0C";
      break;
    }
    return NULL;
  case ARM_tMOVi8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == ARM_CPSR &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (tMOVi8 tGPR:$Rdn, CPSR, imm0_255:$imm, 14, 0)
      AsmString = "movs $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_tMOVr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_R8 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == ARM_R8 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (tMOVr R8, R8, 14, 0)
      AsmString = "nop";
      break;
    }
    return NULL;
  case ARM_tMUL:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 2)) {
      // (tMUL tGPR:$Rdm, s_cc_out:$s, tGPR:$Rn, pred:$p)
      AsmString = "mul$\xFF\x02\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_tRSB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 2)) {
      // (tRSB tGPR:$Rd, s_cc_out:$s, tGPR:$Rm, pred:$p)
      AsmString = "neg$\xFF\x02\x02}$\xFF\x04\x01} $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_tSUBspi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_SP) {
      // (tSUBspi SP, t_imm0_508s4_neg:$imm, pred:$p)
      AsmString = "add$\xFF\x03\x01} sp, $\x02";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }

  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
