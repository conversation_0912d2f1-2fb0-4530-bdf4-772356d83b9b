(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [ppc_const.ml] *)

(* PPC branch codes for some branch instructions *)

let _PPC_BC_INVALID = 0;;
let _PPC_BC_LT = (0 lsl 5) lor 12;;
let _PPC_BC_LE = (1 lsl 5) lor 4;;
let _PPC_BC_EQ = (2 lsl 5) lor 12;;
let _PPC_BC_GE = (0 lsl 5) lor 4;;
let _PPC_BC_GT = (1 lsl 5) lor 12;;
let _PPC_BC_NE = (2 lsl 5) lor 4;;
let _PPC_BC_UN = (3 lsl 5) lor 12;;
let _PPC_BC_NU = (3 lsl 5) lor 4;;
let _PPC_BC_SO = (4 lsl 5) lor 12;;
let _PPC_BC_NS = (4 lsl 5) lor 4;;

(* PPC branch hint for some branch instructions *)

let _PPC_BH_INVALID = 0;;
let _PPC_BH_PLUS = 1;;
let _PPC_BH_MINUS = 2;;

(* Operand type for instruction's operands *)

let _PPC_OP_INVALID = 0;;
let _PPC_OP_REG = 1;;
let _PPC_OP_IMM = 2;;
let _PPC_OP_MEM = 3;;
let _PPC_OP_CRX = 64;;

(* PPC registers *)

let _PPC_REG_INVALID = 0;;
let _PPC_REG_CARRY = 1;;
let _PPC_REG_CC = 2;;
let _PPC_REG_CR0 = 3;;
let _PPC_REG_CR1 = 4;;
let _PPC_REG_CR2 = 5;;
let _PPC_REG_CR3 = 6;;
let _PPC_REG_CR4 = 7;;
let _PPC_REG_CR5 = 8;;
let _PPC_REG_CR6 = 9;;
let _PPC_REG_CR7 = 10;;
let _PPC_REG_CTR = 11;;
let _PPC_REG_F0 = 12;;
let _PPC_REG_F1 = 13;;
let _PPC_REG_F2 = 14;;
let _PPC_REG_F3 = 15;;
let _PPC_REG_F4 = 16;;
let _PPC_REG_F5 = 17;;
let _PPC_REG_F6 = 18;;
let _PPC_REG_F7 = 19;;
let _PPC_REG_F8 = 20;;
let _PPC_REG_F9 = 21;;
let _PPC_REG_F10 = 22;;
let _PPC_REG_F11 = 23;;
let _PPC_REG_F12 = 24;;
let _PPC_REG_F13 = 25;;
let _PPC_REG_F14 = 26;;
let _PPC_REG_F15 = 27;;
let _PPC_REG_F16 = 28;;
let _PPC_REG_F17 = 29;;
let _PPC_REG_F18 = 30;;
let _PPC_REG_F19 = 31;;
let _PPC_REG_F20 = 32;;
let _PPC_REG_F21 = 33;;
let _PPC_REG_F22 = 34;;
let _PPC_REG_F23 = 35;;
let _PPC_REG_F24 = 36;;
let _PPC_REG_F25 = 37;;
let _PPC_REG_F26 = 38;;
let _PPC_REG_F27 = 39;;
let _PPC_REG_F28 = 40;;
let _PPC_REG_F29 = 41;;
let _PPC_REG_F30 = 42;;
let _PPC_REG_F31 = 43;;
let _PPC_REG_LR = 44;;
let _PPC_REG_R0 = 45;;
let _PPC_REG_R1 = 46;;
let _PPC_REG_R2 = 47;;
let _PPC_REG_R3 = 48;;
let _PPC_REG_R4 = 49;;
let _PPC_REG_R5 = 50;;
let _PPC_REG_R6 = 51;;
let _PPC_REG_R7 = 52;;
let _PPC_REG_R8 = 53;;
let _PPC_REG_R9 = 54;;
let _PPC_REG_R10 = 55;;
let _PPC_REG_R11 = 56;;
let _PPC_REG_R12 = 57;;
let _PPC_REG_R13 = 58;;
let _PPC_REG_R14 = 59;;
let _PPC_REG_R15 = 60;;
let _PPC_REG_R16 = 61;;
let _PPC_REG_R17 = 62;;
let _PPC_REG_R18 = 63;;
let _PPC_REG_R19 = 64;;
let _PPC_REG_R20 = 65;;
let _PPC_REG_R21 = 66;;
let _PPC_REG_R22 = 67;;
let _PPC_REG_R23 = 68;;
let _PPC_REG_R24 = 69;;
let _PPC_REG_R25 = 70;;
let _PPC_REG_R26 = 71;;
let _PPC_REG_R27 = 72;;
let _PPC_REG_R28 = 73;;
let _PPC_REG_R29 = 74;;
let _PPC_REG_R30 = 75;;
let _PPC_REG_R31 = 76;;
let _PPC_REG_V0 = 77;;
let _PPC_REG_V1 = 78;;
let _PPC_REG_V2 = 79;;
let _PPC_REG_V3 = 80;;
let _PPC_REG_V4 = 81;;
let _PPC_REG_V5 = 82;;
let _PPC_REG_V6 = 83;;
let _PPC_REG_V7 = 84;;
let _PPC_REG_V8 = 85;;
let _PPC_REG_V9 = 86;;
let _PPC_REG_V10 = 87;;
let _PPC_REG_V11 = 88;;
let _PPC_REG_V12 = 89;;
let _PPC_REG_V13 = 90;;
let _PPC_REG_V14 = 91;;
let _PPC_REG_V15 = 92;;
let _PPC_REG_V16 = 93;;
let _PPC_REG_V17 = 94;;
let _PPC_REG_V18 = 95;;
let _PPC_REG_V19 = 96;;
let _PPC_REG_V20 = 97;;
let _PPC_REG_V21 = 98;;
let _PPC_REG_V22 = 99;;
let _PPC_REG_V23 = 100;;
let _PPC_REG_V24 = 101;;
let _PPC_REG_V25 = 102;;
let _PPC_REG_V26 = 103;;
let _PPC_REG_V27 = 104;;
let _PPC_REG_V28 = 105;;
let _PPC_REG_V29 = 106;;
let _PPC_REG_V30 = 107;;
let _PPC_REG_V31 = 108;;
let _PPC_REG_VRSAVE = 109;;
let _PPC_REG_VS0 = 110;;
let _PPC_REG_VS1 = 111;;
let _PPC_REG_VS2 = 112;;
let _PPC_REG_VS3 = 113;;
let _PPC_REG_VS4 = 114;;
let _PPC_REG_VS5 = 115;;
let _PPC_REG_VS6 = 116;;
let _PPC_REG_VS7 = 117;;
let _PPC_REG_VS8 = 118;;
let _PPC_REG_VS9 = 119;;
let _PPC_REG_VS10 = 120;;
let _PPC_REG_VS11 = 121;;
let _PPC_REG_VS12 = 122;;
let _PPC_REG_VS13 = 123;;
let _PPC_REG_VS14 = 124;;
let _PPC_REG_VS15 = 125;;
let _PPC_REG_VS16 = 126;;
let _PPC_REG_VS17 = 127;;
let _PPC_REG_VS18 = 128;;
let _PPC_REG_VS19 = 129;;
let _PPC_REG_VS20 = 130;;
let _PPC_REG_VS21 = 131;;
let _PPC_REG_VS22 = 132;;
let _PPC_REG_VS23 = 133;;
let _PPC_REG_VS24 = 134;;
let _PPC_REG_VS25 = 135;;
let _PPC_REG_VS26 = 136;;
let _PPC_REG_VS27 = 137;;
let _PPC_REG_VS28 = 138;;
let _PPC_REG_VS29 = 139;;
let _PPC_REG_VS30 = 140;;
let _PPC_REG_VS31 = 141;;
let _PPC_REG_VS32 = 142;;
let _PPC_REG_VS33 = 143;;
let _PPC_REG_VS34 = 144;;
let _PPC_REG_VS35 = 145;;
let _PPC_REG_VS36 = 146;;
let _PPC_REG_VS37 = 147;;
let _PPC_REG_VS38 = 148;;
let _PPC_REG_VS39 = 149;;
let _PPC_REG_VS40 = 150;;
let _PPC_REG_VS41 = 151;;
let _PPC_REG_VS42 = 152;;
let _PPC_REG_VS43 = 153;;
let _PPC_REG_VS44 = 154;;
let _PPC_REG_VS45 = 155;;
let _PPC_REG_VS46 = 156;;
let _PPC_REG_VS47 = 157;;
let _PPC_REG_VS48 = 158;;
let _PPC_REG_VS49 = 159;;
let _PPC_REG_VS50 = 160;;
let _PPC_REG_VS51 = 161;;
let _PPC_REG_VS52 = 162;;
let _PPC_REG_VS53 = 163;;
let _PPC_REG_VS54 = 164;;
let _PPC_REG_VS55 = 165;;
let _PPC_REG_VS56 = 166;;
let _PPC_REG_VS57 = 167;;
let _PPC_REG_VS58 = 168;;
let _PPC_REG_VS59 = 169;;
let _PPC_REG_VS60 = 170;;
let _PPC_REG_VS61 = 171;;
let _PPC_REG_VS62 = 172;;
let _PPC_REG_VS63 = 173;;
let _PPC_REG_RM = 174;;
let _PPC_REG_CTR8 = 175;;
let _PPC_REG_LR8 = 176;;
let _PPC_REG_CR1EQ = 177;;
let _PPC_REG_ENDING = 178;;

(* PPC instruction *)

let _PPC_INS_INVALID = 0;;
let _PPC_INS_ADD = 1;;
let _PPC_INS_ADDC = 2;;
let _PPC_INS_ADDE = 3;;
let _PPC_INS_ADDI = 4;;
let _PPC_INS_ADDIC = 5;;
let _PPC_INS_ADDIS = 6;;
let _PPC_INS_ADDME = 7;;
let _PPC_INS_ADDZE = 8;;
let _PPC_INS_AND = 9;;
let _PPC_INS_ANDC = 10;;
let _PPC_INS_ANDIS = 11;;
let _PPC_INS_ANDI = 12;;
let _PPC_INS_B = 13;;
let _PPC_INS_BA = 14;;
let _PPC_INS_BC = 15;;
let _PPC_INS_BCCTR = 16;;
let _PPC_INS_BCCTRL = 17;;
let _PPC_INS_BCL = 18;;
let _PPC_INS_BCLR = 19;;
let _PPC_INS_BCLRL = 20;;
let _PPC_INS_BCTR = 21;;
let _PPC_INS_BCTRL = 22;;
let _PPC_INS_BDNZ = 23;;
let _PPC_INS_BDNZA = 24;;
let _PPC_INS_BDNZL = 25;;
let _PPC_INS_BDNZLA = 26;;
let _PPC_INS_BDNZLR = 27;;
let _PPC_INS_BDNZLRL = 28;;
let _PPC_INS_BDZ = 29;;
let _PPC_INS_BDZA = 30;;
let _PPC_INS_BDZL = 31;;
let _PPC_INS_BDZLA = 32;;
let _PPC_INS_BDZLR = 33;;
let _PPC_INS_BDZLRL = 34;;
let _PPC_INS_BL = 35;;
let _PPC_INS_BLA = 36;;
let _PPC_INS_BLR = 37;;
let _PPC_INS_BLRL = 38;;
let _PPC_INS_BRINC = 39;;
let _PPC_INS_CMPD = 40;;
let _PPC_INS_CMPDI = 41;;
let _PPC_INS_CMPLD = 42;;
let _PPC_INS_CMPLDI = 43;;
let _PPC_INS_CMPLW = 44;;
let _PPC_INS_CMPLWI = 45;;
let _PPC_INS_CMPW = 46;;
let _PPC_INS_CMPWI = 47;;
let _PPC_INS_CNTLZD = 48;;
let _PPC_INS_CNTLZW = 49;;
let _PPC_INS_CREQV = 50;;
let _PPC_INS_CRXOR = 51;;
let _PPC_INS_CRAND = 52;;
let _PPC_INS_CRANDC = 53;;
let _PPC_INS_CRNAND = 54;;
let _PPC_INS_CRNOR = 55;;
let _PPC_INS_CROR = 56;;
let _PPC_INS_CRORC = 57;;
let _PPC_INS_DCBA = 58;;
let _PPC_INS_DCBF = 59;;
let _PPC_INS_DCBI = 60;;
let _PPC_INS_DCBST = 61;;
let _PPC_INS_DCBT = 62;;
let _PPC_INS_DCBTST = 63;;
let _PPC_INS_DCBZ = 64;;
let _PPC_INS_DCBZL = 65;;
let _PPC_INS_DCCCI = 66;;
let _PPC_INS_DIVD = 67;;
let _PPC_INS_DIVDU = 68;;
let _PPC_INS_DIVW = 69;;
let _PPC_INS_DIVWU = 70;;
let _PPC_INS_DSS = 71;;
let _PPC_INS_DSSALL = 72;;
let _PPC_INS_DST = 73;;
let _PPC_INS_DSTST = 74;;
let _PPC_INS_DSTSTT = 75;;
let _PPC_INS_DSTT = 76;;
let _PPC_INS_EIEIO = 77;;
let _PPC_INS_EQV = 78;;
let _PPC_INS_EVABS = 79;;
let _PPC_INS_EVADDIW = 80;;
let _PPC_INS_EVADDSMIAAW = 81;;
let _PPC_INS_EVADDSSIAAW = 82;;
let _PPC_INS_EVADDUMIAAW = 83;;
let _PPC_INS_EVADDUSIAAW = 84;;
let _PPC_INS_EVADDW = 85;;
let _PPC_INS_EVAND = 86;;
let _PPC_INS_EVANDC = 87;;
let _PPC_INS_EVCMPEQ = 88;;
let _PPC_INS_EVCMPGTS = 89;;
let _PPC_INS_EVCMPGTU = 90;;
let _PPC_INS_EVCMPLTS = 91;;
let _PPC_INS_EVCMPLTU = 92;;
let _PPC_INS_EVCNTLSW = 93;;
let _PPC_INS_EVCNTLZW = 94;;
let _PPC_INS_EVDIVWS = 95;;
let _PPC_INS_EVDIVWU = 96;;
let _PPC_INS_EVEQV = 97;;
let _PPC_INS_EVEXTSB = 98;;
let _PPC_INS_EVEXTSH = 99;;
let _PPC_INS_EVLDD = 100;;
let _PPC_INS_EVLDDX = 101;;
let _PPC_INS_EVLDH = 102;;
let _PPC_INS_EVLDHX = 103;;
let _PPC_INS_EVLDW = 104;;
let _PPC_INS_EVLDWX = 105;;
let _PPC_INS_EVLHHESPLAT = 106;;
let _PPC_INS_EVLHHESPLATX = 107;;
let _PPC_INS_EVLHHOSSPLAT = 108;;
let _PPC_INS_EVLHHOSSPLATX = 109;;
let _PPC_INS_EVLHHOUSPLAT = 110;;
let _PPC_INS_EVLHHOUSPLATX = 111;;
let _PPC_INS_EVLWHE = 112;;
let _PPC_INS_EVLWHEX = 113;;
let _PPC_INS_EVLWHOS = 114;;
let _PPC_INS_EVLWHOSX = 115;;
let _PPC_INS_EVLWHOU = 116;;
let _PPC_INS_EVLWHOUX = 117;;
let _PPC_INS_EVLWHSPLAT = 118;;
let _PPC_INS_EVLWHSPLATX = 119;;
let _PPC_INS_EVLWWSPLAT = 120;;
let _PPC_INS_EVLWWSPLATX = 121;;
let _PPC_INS_EVMERGEHI = 122;;
let _PPC_INS_EVMERGEHILO = 123;;
let _PPC_INS_EVMERGELO = 124;;
let _PPC_INS_EVMERGELOHI = 125;;
let _PPC_INS_EVMHEGSMFAA = 126;;
let _PPC_INS_EVMHEGSMFAN = 127;;
let _PPC_INS_EVMHEGSMIAA = 128;;
let _PPC_INS_EVMHEGSMIAN = 129;;
let _PPC_INS_EVMHEGUMIAA = 130;;
let _PPC_INS_EVMHEGUMIAN = 131;;
let _PPC_INS_EVMHESMF = 132;;
let _PPC_INS_EVMHESMFA = 133;;
let _PPC_INS_EVMHESMFAAW = 134;;
let _PPC_INS_EVMHESMFANW = 135;;
let _PPC_INS_EVMHESMI = 136;;
let _PPC_INS_EVMHESMIA = 137;;
let _PPC_INS_EVMHESMIAAW = 138;;
let _PPC_INS_EVMHESMIANW = 139;;
let _PPC_INS_EVMHESSF = 140;;
let _PPC_INS_EVMHESSFA = 141;;
let _PPC_INS_EVMHESSFAAW = 142;;
let _PPC_INS_EVMHESSFANW = 143;;
let _PPC_INS_EVMHESSIAAW = 144;;
let _PPC_INS_EVMHESSIANW = 145;;
let _PPC_INS_EVMHEUMI = 146;;
let _PPC_INS_EVMHEUMIA = 147;;
let _PPC_INS_EVMHEUMIAAW = 148;;
let _PPC_INS_EVMHEUMIANW = 149;;
let _PPC_INS_EVMHEUSIAAW = 150;;
let _PPC_INS_EVMHEUSIANW = 151;;
let _PPC_INS_EVMHOGSMFAA = 152;;
let _PPC_INS_EVMHOGSMFAN = 153;;
let _PPC_INS_EVMHOGSMIAA = 154;;
let _PPC_INS_EVMHOGSMIAN = 155;;
let _PPC_INS_EVMHOGUMIAA = 156;;
let _PPC_INS_EVMHOGUMIAN = 157;;
let _PPC_INS_EVMHOSMF = 158;;
let _PPC_INS_EVMHOSMFA = 159;;
let _PPC_INS_EVMHOSMFAAW = 160;;
let _PPC_INS_EVMHOSMFANW = 161;;
let _PPC_INS_EVMHOSMI = 162;;
let _PPC_INS_EVMHOSMIA = 163;;
let _PPC_INS_EVMHOSMIAAW = 164;;
let _PPC_INS_EVMHOSMIANW = 165;;
let _PPC_INS_EVMHOSSF = 166;;
let _PPC_INS_EVMHOSSFA = 167;;
let _PPC_INS_EVMHOSSFAAW = 168;;
let _PPC_INS_EVMHOSSFANW = 169;;
let _PPC_INS_EVMHOSSIAAW = 170;;
let _PPC_INS_EVMHOSSIANW = 171;;
let _PPC_INS_EVMHOUMI = 172;;
let _PPC_INS_EVMHOUMIA = 173;;
let _PPC_INS_EVMHOUMIAAW = 174;;
let _PPC_INS_EVMHOUMIANW = 175;;
let _PPC_INS_EVMHOUSIAAW = 176;;
let _PPC_INS_EVMHOUSIANW = 177;;
let _PPC_INS_EVMRA = 178;;
let _PPC_INS_EVMWHSMF = 179;;
let _PPC_INS_EVMWHSMFA = 180;;
let _PPC_INS_EVMWHSMI = 181;;
let _PPC_INS_EVMWHSMIA = 182;;
let _PPC_INS_EVMWHSSF = 183;;
let _PPC_INS_EVMWHSSFA = 184;;
let _PPC_INS_EVMWHUMI = 185;;
let _PPC_INS_EVMWHUMIA = 186;;
let _PPC_INS_EVMWLSMIAAW = 187;;
let _PPC_INS_EVMWLSMIANW = 188;;
let _PPC_INS_EVMWLSSIAAW = 189;;
let _PPC_INS_EVMWLSSIANW = 190;;
let _PPC_INS_EVMWLUMI = 191;;
let _PPC_INS_EVMWLUMIA = 192;;
let _PPC_INS_EVMWLUMIAAW = 193;;
let _PPC_INS_EVMWLUMIANW = 194;;
let _PPC_INS_EVMWLUSIAAW = 195;;
let _PPC_INS_EVMWLUSIANW = 196;;
let _PPC_INS_EVMWSMF = 197;;
let _PPC_INS_EVMWSMFA = 198;;
let _PPC_INS_EVMWSMFAA = 199;;
let _PPC_INS_EVMWSMFAN = 200;;
let _PPC_INS_EVMWSMI = 201;;
let _PPC_INS_EVMWSMIA = 202;;
let _PPC_INS_EVMWSMIAA = 203;;
let _PPC_INS_EVMWSMIAN = 204;;
let _PPC_INS_EVMWSSF = 205;;
let _PPC_INS_EVMWSSFA = 206;;
let _PPC_INS_EVMWSSFAA = 207;;
let _PPC_INS_EVMWSSFAN = 208;;
let _PPC_INS_EVMWUMI = 209;;
let _PPC_INS_EVMWUMIA = 210;;
let _PPC_INS_EVMWUMIAA = 211;;
let _PPC_INS_EVMWUMIAN = 212;;
let _PPC_INS_EVNAND = 213;;
let _PPC_INS_EVNEG = 214;;
let _PPC_INS_EVNOR = 215;;
let _PPC_INS_EVOR = 216;;
let _PPC_INS_EVORC = 217;;
let _PPC_INS_EVRLW = 218;;
let _PPC_INS_EVRLWI = 219;;
let _PPC_INS_EVRNDW = 220;;
let _PPC_INS_EVSLW = 221;;
let _PPC_INS_EVSLWI = 222;;
let _PPC_INS_EVSPLATFI = 223;;
let _PPC_INS_EVSPLATI = 224;;
let _PPC_INS_EVSRWIS = 225;;
let _PPC_INS_EVSRWIU = 226;;
let _PPC_INS_EVSRWS = 227;;
let _PPC_INS_EVSRWU = 228;;
let _PPC_INS_EVSTDD = 229;;
let _PPC_INS_EVSTDDX = 230;;
let _PPC_INS_EVSTDH = 231;;
let _PPC_INS_EVSTDHX = 232;;
let _PPC_INS_EVSTDW = 233;;
let _PPC_INS_EVSTDWX = 234;;
let _PPC_INS_EVSTWHE = 235;;
let _PPC_INS_EVSTWHEX = 236;;
let _PPC_INS_EVSTWHO = 237;;
let _PPC_INS_EVSTWHOX = 238;;
let _PPC_INS_EVSTWWE = 239;;
let _PPC_INS_EVSTWWEX = 240;;
let _PPC_INS_EVSTWWO = 241;;
let _PPC_INS_EVSTWWOX = 242;;
let _PPC_INS_EVSUBFSMIAAW = 243;;
let _PPC_INS_EVSUBFSSIAAW = 244;;
let _PPC_INS_EVSUBFUMIAAW = 245;;
let _PPC_INS_EVSUBFUSIAAW = 246;;
let _PPC_INS_EVSUBFW = 247;;
let _PPC_INS_EVSUBIFW = 248;;
let _PPC_INS_EVXOR = 249;;
let _PPC_INS_EXTSB = 250;;
let _PPC_INS_EXTSH = 251;;
let _PPC_INS_EXTSW = 252;;
let _PPC_INS_FABS = 253;;
let _PPC_INS_FADD = 254;;
let _PPC_INS_FADDS = 255;;
let _PPC_INS_FCFID = 256;;
let _PPC_INS_FCFIDS = 257;;
let _PPC_INS_FCFIDU = 258;;
let _PPC_INS_FCFIDUS = 259;;
let _PPC_INS_FCMPU = 260;;
let _PPC_INS_FCPSGN = 261;;
let _PPC_INS_FCTID = 262;;
let _PPC_INS_FCTIDUZ = 263;;
let _PPC_INS_FCTIDZ = 264;;
let _PPC_INS_FCTIW = 265;;
let _PPC_INS_FCTIWUZ = 266;;
let _PPC_INS_FCTIWZ = 267;;
let _PPC_INS_FDIV = 268;;
let _PPC_INS_FDIVS = 269;;
let _PPC_INS_FMADD = 270;;
let _PPC_INS_FMADDS = 271;;
let _PPC_INS_FMR = 272;;
let _PPC_INS_FMSUB = 273;;
let _PPC_INS_FMSUBS = 274;;
let _PPC_INS_FMUL = 275;;
let _PPC_INS_FMULS = 276;;
let _PPC_INS_FNABS = 277;;
let _PPC_INS_FNEG = 278;;
let _PPC_INS_FNMADD = 279;;
let _PPC_INS_FNMADDS = 280;;
let _PPC_INS_FNMSUB = 281;;
let _PPC_INS_FNMSUBS = 282;;
let _PPC_INS_FRE = 283;;
let _PPC_INS_FRES = 284;;
let _PPC_INS_FRIM = 285;;
let _PPC_INS_FRIN = 286;;
let _PPC_INS_FRIP = 287;;
let _PPC_INS_FRIZ = 288;;
let _PPC_INS_FRSP = 289;;
let _PPC_INS_FRSQRTE = 290;;
let _PPC_INS_FRSQRTES = 291;;
let _PPC_INS_FSEL = 292;;
let _PPC_INS_FSQRT = 293;;
let _PPC_INS_FSQRTS = 294;;
let _PPC_INS_FSUB = 295;;
let _PPC_INS_FSUBS = 296;;
let _PPC_INS_ICBI = 297;;
let _PPC_INS_ICCCI = 298;;
let _PPC_INS_ISEL = 299;;
let _PPC_INS_ISYNC = 300;;
let _PPC_INS_LA = 301;;
let _PPC_INS_LBZ = 302;;
let _PPC_INS_LBZU = 303;;
let _PPC_INS_LBZUX = 304;;
let _PPC_INS_LBZX = 305;;
let _PPC_INS_LD = 306;;
let _PPC_INS_LDARX = 307;;
let _PPC_INS_LDBRX = 308;;
let _PPC_INS_LDU = 309;;
let _PPC_INS_LDUX = 310;;
let _PPC_INS_LDX = 311;;
let _PPC_INS_LFD = 312;;
let _PPC_INS_LFDU = 313;;
let _PPC_INS_LFDUX = 314;;
let _PPC_INS_LFDX = 315;;
let _PPC_INS_LFIWAX = 316;;
let _PPC_INS_LFIWZX = 317;;
let _PPC_INS_LFS = 318;;
let _PPC_INS_LFSU = 319;;
let _PPC_INS_LFSUX = 320;;
let _PPC_INS_LFSX = 321;;
let _PPC_INS_LHA = 322;;
let _PPC_INS_LHAU = 323;;
let _PPC_INS_LHAUX = 324;;
let _PPC_INS_LHAX = 325;;
let _PPC_INS_LHBRX = 326;;
let _PPC_INS_LHZ = 327;;
let _PPC_INS_LHZU = 328;;
let _PPC_INS_LHZUX = 329;;
let _PPC_INS_LHZX = 330;;
let _PPC_INS_LI = 331;;
let _PPC_INS_LIS = 332;;
let _PPC_INS_LMW = 333;;
let _PPC_INS_LSWI = 334;;
let _PPC_INS_LVEBX = 335;;
let _PPC_INS_LVEHX = 336;;
let _PPC_INS_LVEWX = 337;;
let _PPC_INS_LVSL = 338;;
let _PPC_INS_LVSR = 339;;
let _PPC_INS_LVX = 340;;
let _PPC_INS_LVXL = 341;;
let _PPC_INS_LWA = 342;;
let _PPC_INS_LWARX = 343;;
let _PPC_INS_LWAUX = 344;;
let _PPC_INS_LWAX = 345;;
let _PPC_INS_LWBRX = 346;;
let _PPC_INS_LWZ = 347;;
let _PPC_INS_LWZU = 348;;
let _PPC_INS_LWZUX = 349;;
let _PPC_INS_LWZX = 350;;
let _PPC_INS_LXSDX = 351;;
let _PPC_INS_LXVD2X = 352;;
let _PPC_INS_LXVDSX = 353;;
let _PPC_INS_LXVW4X = 354;;
let _PPC_INS_MBAR = 355;;
let _PPC_INS_MCRF = 356;;
let _PPC_INS_MFCR = 357;;
let _PPC_INS_MFCTR = 358;;
let _PPC_INS_MFDCR = 359;;
let _PPC_INS_MFFS = 360;;
let _PPC_INS_MFLR = 361;;
let _PPC_INS_MFMSR = 362;;
let _PPC_INS_MFOCRF = 363;;
let _PPC_INS_MFSPR = 364;;
let _PPC_INS_MFSR = 365;;
let _PPC_INS_MFSRIN = 366;;
let _PPC_INS_MFTB = 367;;
let _PPC_INS_MFVSCR = 368;;
let _PPC_INS_MSYNC = 369;;
let _PPC_INS_MTCRF = 370;;
let _PPC_INS_MTCTR = 371;;
let _PPC_INS_MTDCR = 372;;
let _PPC_INS_MTFSB0 = 373;;
let _PPC_INS_MTFSB1 = 374;;
let _PPC_INS_MTFSF = 375;;
let _PPC_INS_MTLR = 376;;
let _PPC_INS_MTMSR = 377;;
let _PPC_INS_MTMSRD = 378;;
let _PPC_INS_MTOCRF = 379;;
let _PPC_INS_MTSPR = 380;;
let _PPC_INS_MTSR = 381;;
let _PPC_INS_MTSRIN = 382;;
let _PPC_INS_MTVSCR = 383;;
let _PPC_INS_MULHD = 384;;
let _PPC_INS_MULHDU = 385;;
let _PPC_INS_MULHW = 386;;
let _PPC_INS_MULHWU = 387;;
let _PPC_INS_MULLD = 388;;
let _PPC_INS_MULLI = 389;;
let _PPC_INS_MULLW = 390;;
let _PPC_INS_NAND = 391;;
let _PPC_INS_NEG = 392;;
let _PPC_INS_NOP = 393;;
let _PPC_INS_ORI = 394;;
let _PPC_INS_NOR = 395;;
let _PPC_INS_OR = 396;;
let _PPC_INS_ORC = 397;;
let _PPC_INS_ORIS = 398;;
let _PPC_INS_POPCNTD = 399;;
let _PPC_INS_POPCNTW = 400;;
let _PPC_INS_RFCI = 401;;
let _PPC_INS_RFDI = 402;;
let _PPC_INS_RFI = 403;;
let _PPC_INS_RFID = 404;;
let _PPC_INS_RFMCI = 405;;
let _PPC_INS_RLDCL = 406;;
let _PPC_INS_RLDCR = 407;;
let _PPC_INS_RLDIC = 408;;
let _PPC_INS_RLDICL = 409;;
let _PPC_INS_RLDICR = 410;;
let _PPC_INS_RLDIMI = 411;;
let _PPC_INS_RLWIMI = 412;;
let _PPC_INS_RLWINM = 413;;
let _PPC_INS_RLWNM = 414;;
let _PPC_INS_SC = 415;;
let _PPC_INS_SLBIA = 416;;
let _PPC_INS_SLBIE = 417;;
let _PPC_INS_SLBMFEE = 418;;
let _PPC_INS_SLBMTE = 419;;
let _PPC_INS_SLD = 420;;
let _PPC_INS_SLW = 421;;
let _PPC_INS_SRAD = 422;;
let _PPC_INS_SRADI = 423;;
let _PPC_INS_SRAW = 424;;
let _PPC_INS_SRAWI = 425;;
let _PPC_INS_SRD = 426;;
let _PPC_INS_SRW = 427;;
let _PPC_INS_STB = 428;;
let _PPC_INS_STBU = 429;;
let _PPC_INS_STBUX = 430;;
let _PPC_INS_STBX = 431;;
let _PPC_INS_STD = 432;;
let _PPC_INS_STDBRX = 433;;
let _PPC_INS_STDCX = 434;;
let _PPC_INS_STDU = 435;;
let _PPC_INS_STDUX = 436;;
let _PPC_INS_STDX = 437;;
let _PPC_INS_STFD = 438;;
let _PPC_INS_STFDU = 439;;
let _PPC_INS_STFDUX = 440;;
let _PPC_INS_STFDX = 441;;
let _PPC_INS_STFIWX = 442;;
let _PPC_INS_STFS = 443;;
let _PPC_INS_STFSU = 444;;
let _PPC_INS_STFSUX = 445;;
let _PPC_INS_STFSX = 446;;
let _PPC_INS_STH = 447;;
let _PPC_INS_STHBRX = 448;;
let _PPC_INS_STHU = 449;;
let _PPC_INS_STHUX = 450;;
let _PPC_INS_STHX = 451;;
let _PPC_INS_STMW = 452;;
let _PPC_INS_STSWI = 453;;
let _PPC_INS_STVEBX = 454;;
let _PPC_INS_STVEHX = 455;;
let _PPC_INS_STVEWX = 456;;
let _PPC_INS_STVX = 457;;
let _PPC_INS_STVXL = 458;;
let _PPC_INS_STW = 459;;
let _PPC_INS_STWBRX = 460;;
let _PPC_INS_STWCX = 461;;
let _PPC_INS_STWU = 462;;
let _PPC_INS_STWUX = 463;;
let _PPC_INS_STWX = 464;;
let _PPC_INS_STXSDX = 465;;
let _PPC_INS_STXVD2X = 466;;
let _PPC_INS_STXVW4X = 467;;
let _PPC_INS_SUBF = 468;;
let _PPC_INS_SUBFC = 469;;
let _PPC_INS_SUBFE = 470;;
let _PPC_INS_SUBFIC = 471;;
let _PPC_INS_SUBFME = 472;;
let _PPC_INS_SUBFZE = 473;;
let _PPC_INS_SYNC = 474;;
let _PPC_INS_TD = 475;;
let _PPC_INS_TDI = 476;;
let _PPC_INS_TLBIA = 477;;
let _PPC_INS_TLBIE = 478;;
let _PPC_INS_TLBIEL = 479;;
let _PPC_INS_TLBIVAX = 480;;
let _PPC_INS_TLBLD = 481;;
let _PPC_INS_TLBLI = 482;;
let _PPC_INS_TLBRE = 483;;
let _PPC_INS_TLBSX = 484;;
let _PPC_INS_TLBSYNC = 485;;
let _PPC_INS_TLBWE = 486;;
let _PPC_INS_TRAP = 487;;
let _PPC_INS_TW = 488;;
let _PPC_INS_TWI = 489;;
let _PPC_INS_VADDCUW = 490;;
let _PPC_INS_VADDFP = 491;;
let _PPC_INS_VADDSBS = 492;;
let _PPC_INS_VADDSHS = 493;;
let _PPC_INS_VADDSWS = 494;;
let _PPC_INS_VADDUBM = 495;;
let _PPC_INS_VADDUBS = 496;;
let _PPC_INS_VADDUHM = 497;;
let _PPC_INS_VADDUHS = 498;;
let _PPC_INS_VADDUWM = 499;;
let _PPC_INS_VADDUWS = 500;;
let _PPC_INS_VAND = 501;;
let _PPC_INS_VANDC = 502;;
let _PPC_INS_VAVGSB = 503;;
let _PPC_INS_VAVGSH = 504;;
let _PPC_INS_VAVGSW = 505;;
let _PPC_INS_VAVGUB = 506;;
let _PPC_INS_VAVGUH = 507;;
let _PPC_INS_VAVGUW = 508;;
let _PPC_INS_VCFSX = 509;;
let _PPC_INS_VCFUX = 510;;
let _PPC_INS_VCMPBFP = 511;;
let _PPC_INS_VCMPEQFP = 512;;
let _PPC_INS_VCMPEQUB = 513;;
let _PPC_INS_VCMPEQUH = 514;;
let _PPC_INS_VCMPEQUW = 515;;
let _PPC_INS_VCMPGEFP = 516;;
let _PPC_INS_VCMPGTFP = 517;;
let _PPC_INS_VCMPGTSB = 518;;
let _PPC_INS_VCMPGTSH = 519;;
let _PPC_INS_VCMPGTSW = 520;;
let _PPC_INS_VCMPGTUB = 521;;
let _PPC_INS_VCMPGTUH = 522;;
let _PPC_INS_VCMPGTUW = 523;;
let _PPC_INS_VCTSXS = 524;;
let _PPC_INS_VCTUXS = 525;;
let _PPC_INS_VEXPTEFP = 526;;
let _PPC_INS_VLOGEFP = 527;;
let _PPC_INS_VMADDFP = 528;;
let _PPC_INS_VMAXFP = 529;;
let _PPC_INS_VMAXSB = 530;;
let _PPC_INS_VMAXSH = 531;;
let _PPC_INS_VMAXSW = 532;;
let _PPC_INS_VMAXUB = 533;;
let _PPC_INS_VMAXUH = 534;;
let _PPC_INS_VMAXUW = 535;;
let _PPC_INS_VMHADDSHS = 536;;
let _PPC_INS_VMHRADDSHS = 537;;
let _PPC_INS_VMINFP = 538;;
let _PPC_INS_VMINSB = 539;;
let _PPC_INS_VMINSH = 540;;
let _PPC_INS_VMINSW = 541;;
let _PPC_INS_VMINUB = 542;;
let _PPC_INS_VMINUH = 543;;
let _PPC_INS_VMINUW = 544;;
let _PPC_INS_VMLADDUHM = 545;;
let _PPC_INS_VMRGHB = 546;;
let _PPC_INS_VMRGHH = 547;;
let _PPC_INS_VMRGHW = 548;;
let _PPC_INS_VMRGLB = 549;;
let _PPC_INS_VMRGLH = 550;;
let _PPC_INS_VMRGLW = 551;;
let _PPC_INS_VMSUMMBM = 552;;
let _PPC_INS_VMSUMSHM = 553;;
let _PPC_INS_VMSUMSHS = 554;;
let _PPC_INS_VMSUMUBM = 555;;
let _PPC_INS_VMSUMUHM = 556;;
let _PPC_INS_VMSUMUHS = 557;;
let _PPC_INS_VMULESB = 558;;
let _PPC_INS_VMULESH = 559;;
let _PPC_INS_VMULEUB = 560;;
let _PPC_INS_VMULEUH = 561;;
let _PPC_INS_VMULOSB = 562;;
let _PPC_INS_VMULOSH = 563;;
let _PPC_INS_VMULOUB = 564;;
let _PPC_INS_VMULOUH = 565;;
let _PPC_INS_VNMSUBFP = 566;;
let _PPC_INS_VNOR = 567;;
let _PPC_INS_VOR = 568;;
let _PPC_INS_VPERM = 569;;
let _PPC_INS_VPKPX = 570;;
let _PPC_INS_VPKSHSS = 571;;
let _PPC_INS_VPKSHUS = 572;;
let _PPC_INS_VPKSWSS = 573;;
let _PPC_INS_VPKSWUS = 574;;
let _PPC_INS_VPKUHUM = 575;;
let _PPC_INS_VPKUHUS = 576;;
let _PPC_INS_VPKUWUM = 577;;
let _PPC_INS_VPKUWUS = 578;;
let _PPC_INS_VREFP = 579;;
let _PPC_INS_VRFIM = 580;;
let _PPC_INS_VRFIN = 581;;
let _PPC_INS_VRFIP = 582;;
let _PPC_INS_VRFIZ = 583;;
let _PPC_INS_VRLB = 584;;
let _PPC_INS_VRLH = 585;;
let _PPC_INS_VRLW = 586;;
let _PPC_INS_VRSQRTEFP = 587;;
let _PPC_INS_VSEL = 588;;
let _PPC_INS_VSL = 589;;
let _PPC_INS_VSLB = 590;;
let _PPC_INS_VSLDOI = 591;;
let _PPC_INS_VSLH = 592;;
let _PPC_INS_VSLO = 593;;
let _PPC_INS_VSLW = 594;;
let _PPC_INS_VSPLTB = 595;;
let _PPC_INS_VSPLTH = 596;;
let _PPC_INS_VSPLTISB = 597;;
let _PPC_INS_VSPLTISH = 598;;
let _PPC_INS_VSPLTISW = 599;;
let _PPC_INS_VSPLTW = 600;;
let _PPC_INS_VSR = 601;;
let _PPC_INS_VSRAB = 602;;
let _PPC_INS_VSRAH = 603;;
let _PPC_INS_VSRAW = 604;;
let _PPC_INS_VSRB = 605;;
let _PPC_INS_VSRH = 606;;
let _PPC_INS_VSRO = 607;;
let _PPC_INS_VSRW = 608;;
let _PPC_INS_VSUBCUW = 609;;
let _PPC_INS_VSUBFP = 610;;
let _PPC_INS_VSUBSBS = 611;;
let _PPC_INS_VSUBSHS = 612;;
let _PPC_INS_VSUBSWS = 613;;
let _PPC_INS_VSUBUBM = 614;;
let _PPC_INS_VSUBUBS = 615;;
let _PPC_INS_VSUBUHM = 616;;
let _PPC_INS_VSUBUHS = 617;;
let _PPC_INS_VSUBUWM = 618;;
let _PPC_INS_VSUBUWS = 619;;
let _PPC_INS_VSUM2SWS = 620;;
let _PPC_INS_VSUM4SBS = 621;;
let _PPC_INS_VSUM4SHS = 622;;
let _PPC_INS_VSUM4UBS = 623;;
let _PPC_INS_VSUMSWS = 624;;
let _PPC_INS_VUPKHPX = 625;;
let _PPC_INS_VUPKHSB = 626;;
let _PPC_INS_VUPKHSH = 627;;
let _PPC_INS_VUPKLPX = 628;;
let _PPC_INS_VUPKLSB = 629;;
let _PPC_INS_VUPKLSH = 630;;
let _PPC_INS_VXOR = 631;;
let _PPC_INS_WAIT = 632;;
let _PPC_INS_WRTEE = 633;;
let _PPC_INS_WRTEEI = 634;;
let _PPC_INS_XOR = 635;;
let _PPC_INS_XORI = 636;;
let _PPC_INS_XORIS = 637;;
let _PPC_INS_XSABSDP = 638;;
let _PPC_INS_XSADDDP = 639;;
let _PPC_INS_XSCMPODP = 640;;
let _PPC_INS_XSCMPUDP = 641;;
let _PPC_INS_XSCPSGNDP = 642;;
let _PPC_INS_XSCVDPSP = 643;;
let _PPC_INS_XSCVDPSXDS = 644;;
let _PPC_INS_XSCVDPSXWS = 645;;
let _PPC_INS_XSCVDPUXDS = 646;;
let _PPC_INS_XSCVDPUXWS = 647;;
let _PPC_INS_XSCVSPDP = 648;;
let _PPC_INS_XSCVSXDDP = 649;;
let _PPC_INS_XSCVUXDDP = 650;;
let _PPC_INS_XSDIVDP = 651;;
let _PPC_INS_XSMADDADP = 652;;
let _PPC_INS_XSMADDMDP = 653;;
let _PPC_INS_XSMAXDP = 654;;
let _PPC_INS_XSMINDP = 655;;
let _PPC_INS_XSMSUBADP = 656;;
let _PPC_INS_XSMSUBMDP = 657;;
let _PPC_INS_XSMULDP = 658;;
let _PPC_INS_XSNABSDP = 659;;
let _PPC_INS_XSNEGDP = 660;;
let _PPC_INS_XSNMADDADP = 661;;
let _PPC_INS_XSNMADDMDP = 662;;
let _PPC_INS_XSNMSUBADP = 663;;
let _PPC_INS_XSNMSUBMDP = 664;;
let _PPC_INS_XSRDPI = 665;;
let _PPC_INS_XSRDPIC = 666;;
let _PPC_INS_XSRDPIM = 667;;
let _PPC_INS_XSRDPIP = 668;;
let _PPC_INS_XSRDPIZ = 669;;
let _PPC_INS_XSREDP = 670;;
let _PPC_INS_XSRSQRTEDP = 671;;
let _PPC_INS_XSSQRTDP = 672;;
let _PPC_INS_XSSUBDP = 673;;
let _PPC_INS_XSTDIVDP = 674;;
let _PPC_INS_XSTSQRTDP = 675;;
let _PPC_INS_XVABSDP = 676;;
let _PPC_INS_XVABSSP = 677;;
let _PPC_INS_XVADDDP = 678;;
let _PPC_INS_XVADDSP = 679;;
let _PPC_INS_XVCMPEQDP = 680;;
let _PPC_INS_XVCMPEQSP = 681;;
let _PPC_INS_XVCMPGEDP = 682;;
let _PPC_INS_XVCMPGESP = 683;;
let _PPC_INS_XVCMPGTDP = 684;;
let _PPC_INS_XVCMPGTSP = 685;;
let _PPC_INS_XVCPSGNDP = 686;;
let _PPC_INS_XVCPSGNSP = 687;;
let _PPC_INS_XVCVDPSP = 688;;
let _PPC_INS_XVCVDPSXDS = 689;;
let _PPC_INS_XVCVDPSXWS = 690;;
let _PPC_INS_XVCVDPUXDS = 691;;
let _PPC_INS_XVCVDPUXWS = 692;;
let _PPC_INS_XVCVSPDP = 693;;
let _PPC_INS_XVCVSPSXDS = 694;;
let _PPC_INS_XVCVSPSXWS = 695;;
let _PPC_INS_XVCVSPUXDS = 696;;
let _PPC_INS_XVCVSPUXWS = 697;;
let _PPC_INS_XVCVSXDDP = 698;;
let _PPC_INS_XVCVSXDSP = 699;;
let _PPC_INS_XVCVSXWDP = 700;;
let _PPC_INS_XVCVSXWSP = 701;;
let _PPC_INS_XVCVUXDDP = 702;;
let _PPC_INS_XVCVUXDSP = 703;;
let _PPC_INS_XVCVUXWDP = 704;;
let _PPC_INS_XVCVUXWSP = 705;;
let _PPC_INS_XVDIVDP = 706;;
let _PPC_INS_XVDIVSP = 707;;
let _PPC_INS_XVMADDADP = 708;;
let _PPC_INS_XVMADDASP = 709;;
let _PPC_INS_XVMADDMDP = 710;;
let _PPC_INS_XVMADDMSP = 711;;
let _PPC_INS_XVMAXDP = 712;;
let _PPC_INS_XVMAXSP = 713;;
let _PPC_INS_XVMINDP = 714;;
let _PPC_INS_XVMINSP = 715;;
let _PPC_INS_XVMSUBADP = 716;;
let _PPC_INS_XVMSUBASP = 717;;
let _PPC_INS_XVMSUBMDP = 718;;
let _PPC_INS_XVMSUBMSP = 719;;
let _PPC_INS_XVMULDP = 720;;
let _PPC_INS_XVMULSP = 721;;
let _PPC_INS_XVNABSDP = 722;;
let _PPC_INS_XVNABSSP = 723;;
let _PPC_INS_XVNEGDP = 724;;
let _PPC_INS_XVNEGSP = 725;;
let _PPC_INS_XVNMADDADP = 726;;
let _PPC_INS_XVNMADDASP = 727;;
let _PPC_INS_XVNMADDMDP = 728;;
let _PPC_INS_XVNMADDMSP = 729;;
let _PPC_INS_XVNMSUBADP = 730;;
let _PPC_INS_XVNMSUBASP = 731;;
let _PPC_INS_XVNMSUBMDP = 732;;
let _PPC_INS_XVNMSUBMSP = 733;;
let _PPC_INS_XVRDPI = 734;;
let _PPC_INS_XVRDPIC = 735;;
let _PPC_INS_XVRDPIM = 736;;
let _PPC_INS_XVRDPIP = 737;;
let _PPC_INS_XVRDPIZ = 738;;
let _PPC_INS_XVREDP = 739;;
let _PPC_INS_XVRESP = 740;;
let _PPC_INS_XVRSPI = 741;;
let _PPC_INS_XVRSPIC = 742;;
let _PPC_INS_XVRSPIM = 743;;
let _PPC_INS_XVRSPIP = 744;;
let _PPC_INS_XVRSPIZ = 745;;
let _PPC_INS_XVRSQRTEDP = 746;;
let _PPC_INS_XVRSQRTESP = 747;;
let _PPC_INS_XVSQRTDP = 748;;
let _PPC_INS_XVSQRTSP = 749;;
let _PPC_INS_XVSUBDP = 750;;
let _PPC_INS_XVSUBSP = 751;;
let _PPC_INS_XVTDIVDP = 752;;
let _PPC_INS_XVTDIVSP = 753;;
let _PPC_INS_XVTSQRTDP = 754;;
let _PPC_INS_XVTSQRTSP = 755;;
let _PPC_INS_XXLAND = 756;;
let _PPC_INS_XXLANDC = 757;;
let _PPC_INS_XXLNOR = 758;;
let _PPC_INS_XXLOR = 759;;
let _PPC_INS_XXLXOR = 760;;
let _PPC_INS_XXMRGHW = 761;;
let _PPC_INS_XXMRGLW = 762;;
let _PPC_INS_XXPERMDI = 763;;
let _PPC_INS_XXSEL = 764;;
let _PPC_INS_XXSLDWI = 765;;
let _PPC_INS_XXSPLTW = 766;;
let _PPC_INS_BCA = 767;;
let _PPC_INS_BCLA = 768;;
let _PPC_INS_SLWI = 769;;
let _PPC_INS_SRWI = 770;;
let _PPC_INS_SLDI = 771;;
let _PPC_INS_BTA = 772;;
let _PPC_INS_CRSET = 773;;
let _PPC_INS_CRNOT = 774;;
let _PPC_INS_CRMOVE = 775;;
let _PPC_INS_CRCLR = 776;;
let _PPC_INS_MFBR0 = 777;;
let _PPC_INS_MFBR1 = 778;;
let _PPC_INS_MFBR2 = 779;;
let _PPC_INS_MFBR3 = 780;;
let _PPC_INS_MFBR4 = 781;;
let _PPC_INS_MFBR5 = 782;;
let _PPC_INS_MFBR6 = 783;;
let _PPC_INS_MFBR7 = 784;;
let _PPC_INS_MFXER = 785;;
let _PPC_INS_MFRTCU = 786;;
let _PPC_INS_MFRTCL = 787;;
let _PPC_INS_MFDSCR = 788;;
let _PPC_INS_MFDSISR = 789;;
let _PPC_INS_MFDAR = 790;;
let _PPC_INS_MFSRR2 = 791;;
let _PPC_INS_MFSRR3 = 792;;
let _PPC_INS_MFCFAR = 793;;
let _PPC_INS_MFAMR = 794;;
let _PPC_INS_MFPID = 795;;
let _PPC_INS_MFTBLO = 796;;
let _PPC_INS_MFTBHI = 797;;
let _PPC_INS_MFDBATU = 798;;
let _PPC_INS_MFDBATL = 799;;
let _PPC_INS_MFIBATU = 800;;
let _PPC_INS_MFIBATL = 801;;
let _PPC_INS_MFDCCR = 802;;
let _PPC_INS_MFICCR = 803;;
let _PPC_INS_MFDEAR = 804;;
let _PPC_INS_MFESR = 805;;
let _PPC_INS_MFSPEFSCR = 806;;
let _PPC_INS_MFTCR = 807;;
let _PPC_INS_MFASR = 808;;
let _PPC_INS_MFPVR = 809;;
let _PPC_INS_MFTBU = 810;;
let _PPC_INS_MTCR = 811;;
let _PPC_INS_MTBR0 = 812;;
let _PPC_INS_MTBR1 = 813;;
let _PPC_INS_MTBR2 = 814;;
let _PPC_INS_MTBR3 = 815;;
let _PPC_INS_MTBR4 = 816;;
let _PPC_INS_MTBR5 = 817;;
let _PPC_INS_MTBR6 = 818;;
let _PPC_INS_MTBR7 = 819;;
let _PPC_INS_MTXER = 820;;
let _PPC_INS_MTDSCR = 821;;
let _PPC_INS_MTDSISR = 822;;
let _PPC_INS_MTDAR = 823;;
let _PPC_INS_MTSRR2 = 824;;
let _PPC_INS_MTSRR3 = 825;;
let _PPC_INS_MTCFAR = 826;;
let _PPC_INS_MTAMR = 827;;
let _PPC_INS_MTPID = 828;;
let _PPC_INS_MTTBL = 829;;
let _PPC_INS_MTTBU = 830;;
let _PPC_INS_MTTBLO = 831;;
let _PPC_INS_MTTBHI = 832;;
let _PPC_INS_MTDBATU = 833;;
let _PPC_INS_MTDBATL = 834;;
let _PPC_INS_MTIBATU = 835;;
let _PPC_INS_MTIBATL = 836;;
let _PPC_INS_MTDCCR = 837;;
let _PPC_INS_MTICCR = 838;;
let _PPC_INS_MTDEAR = 839;;
let _PPC_INS_MTESR = 840;;
let _PPC_INS_MTSPEFSCR = 841;;
let _PPC_INS_MTTCR = 842;;
let _PPC_INS_NOT = 843;;
let _PPC_INS_MR = 844;;
let _PPC_INS_ROTLD = 845;;
let _PPC_INS_ROTLDI = 846;;
let _PPC_INS_CLRLDI = 847;;
let _PPC_INS_ROTLWI = 848;;
let _PPC_INS_CLRLWI = 849;;
let _PPC_INS_ROTLW = 850;;
let _PPC_INS_SUB = 851;;
let _PPC_INS_SUBC = 852;;
let _PPC_INS_LWSYNC = 853;;
let _PPC_INS_PTESYNC = 854;;
let _PPC_INS_TDLT = 855;;
let _PPC_INS_TDEQ = 856;;
let _PPC_INS_TDGT = 857;;
let _PPC_INS_TDNE = 858;;
let _PPC_INS_TDLLT = 859;;
let _PPC_INS_TDLGT = 860;;
let _PPC_INS_TDU = 861;;
let _PPC_INS_TDLTI = 862;;
let _PPC_INS_TDEQI = 863;;
let _PPC_INS_TDGTI = 864;;
let _PPC_INS_TDNEI = 865;;
let _PPC_INS_TDLLTI = 866;;
let _PPC_INS_TDLGTI = 867;;
let _PPC_INS_TDUI = 868;;
let _PPC_INS_TLBREHI = 869;;
let _PPC_INS_TLBRELO = 870;;
let _PPC_INS_TLBWEHI = 871;;
let _PPC_INS_TLBWELO = 872;;
let _PPC_INS_TWLT = 873;;
let _PPC_INS_TWEQ = 874;;
let _PPC_INS_TWGT = 875;;
let _PPC_INS_TWNE = 876;;
let _PPC_INS_TWLLT = 877;;
let _PPC_INS_TWLGT = 878;;
let _PPC_INS_TWU = 879;;
let _PPC_INS_TWLTI = 880;;
let _PPC_INS_TWEQI = 881;;
let _PPC_INS_TWGTI = 882;;
let _PPC_INS_TWNEI = 883;;
let _PPC_INS_TWLLTI = 884;;
let _PPC_INS_TWLGTI = 885;;
let _PPC_INS_TWUI = 886;;
let _PPC_INS_WAITRSV = 887;;
let _PPC_INS_WAITIMPL = 888;;
let _PPC_INS_XNOP = 889;;
let _PPC_INS_XVMOVDP = 890;;
let _PPC_INS_XVMOVSP = 891;;
let _PPC_INS_XXSPLTD = 892;;
let _PPC_INS_XXMRGHD = 893;;
let _PPC_INS_XXMRGLD = 894;;
let _PPC_INS_XXSWAPD = 895;;
let _PPC_INS_BT = 896;;
let _PPC_INS_BF = 897;;
let _PPC_INS_BDNZT = 898;;
let _PPC_INS_BDNZF = 899;;
let _PPC_INS_BDZF = 900;;
let _PPC_INS_BDZT = 901;;
let _PPC_INS_BFA = 902;;
let _PPC_INS_BDNZTA = 903;;
let _PPC_INS_BDNZFA = 904;;
let _PPC_INS_BDZTA = 905;;
let _PPC_INS_BDZFA = 906;;
let _PPC_INS_BTCTR = 907;;
let _PPC_INS_BFCTR = 908;;
let _PPC_INS_BTCTRL = 909;;
let _PPC_INS_BFCTRL = 910;;
let _PPC_INS_BTL = 911;;
let _PPC_INS_BFL = 912;;
let _PPC_INS_BDNZTL = 913;;
let _PPC_INS_BDNZFL = 914;;
let _PPC_INS_BDZTL = 915;;
let _PPC_INS_BDZFL = 916;;
let _PPC_INS_BTLA = 917;;
let _PPC_INS_BFLA = 918;;
let _PPC_INS_BDNZTLA = 919;;
let _PPC_INS_BDNZFLA = 920;;
let _PPC_INS_BDZTLA = 921;;
let _PPC_INS_BDZFLA = 922;;
let _PPC_INS_BTLR = 923;;
let _PPC_INS_BFLR = 924;;
let _PPC_INS_BDNZTLR = 925;;
let _PPC_INS_BDZTLR = 926;;
let _PPC_INS_BDZFLR = 927;;
let _PPC_INS_BTLRL = 928;;
let _PPC_INS_BFLRL = 929;;
let _PPC_INS_BDNZTLRL = 930;;
let _PPC_INS_BDNZFLRL = 931;;
let _PPC_INS_BDZTLRL = 932;;
let _PPC_INS_BDZFLRL = 933;;
let _PPC_INS_ENDING = 934;;

(* Group of PPC instructions *)

let _PPC_GRP_INVALID = 0;;

(* Generic groups *)
let _PPC_GRP_JUMP = 1;;

(* Architecture-specific groups *)
let _PPC_GRP_ALTIVEC = 128;;
let _PPC_GRP_MODE32 = 129;;
let _PPC_GRP_MODE64 = 130;;
let _PPC_GRP_BOOKE = 131;;
let _PPC_GRP_NOTBOOKE = 132;;
let _PPC_GRP_SPE = 133;;
let _PPC_GRP_VSX = 134;;
let _PPC_GRP_E500 = 135;;
let _PPC_GRP_PPC4XX = 136;;
let _PPC_GRP_PPC6XX = 137;;
let _PPC_GRP_ENDING = 138;;
