// Copyright (c) 2025, DdiMon EPT Hook Project. All rights reserved.
// Stub implementations for DdiMon functions used by HyperPlatform

#include "ddi_mon_stubs.h"
#include "log.h"

// Function pointer type for dynamic linking
typedef NTSTATUS (*DdimonHandleEptHookCheckPtr)(
    void* InputBuffer,
    ULONG InputBufferLength,
    void* OutputBuffer,
    ULONG OutputBufferLength,
    PULONG BytesReturned
);

typedef NTSTATUS (*DdimonHandleEptHookCreatePtr)(
    void* InputBuffer,
    ULONG InputBufferLength,
    void* OutputBuffer,
    ULONG OutputBufferLength,
    PULONG BytesReturned
);

typedef BOOLEAN (*DdimonpHandleDynamicEptViolationPtr)(
    struct _EptData* ept_data,
    void* fault_va
);

// Global function pointers (will be set when DdiMon driver is loaded)
static DdimonHandleEptHookCheckPtr g_DdimonHandleEptHookCheck = nullptr;
static DdimonHandleEptHookCreatePtr g_DdimonHandleEptHookCreate = nullptr;
static DdimonpHandleDynamicEptViolationPtr g_DdimonpHandleDynamicEptViolation = nullptr;

// Stub implementations that will be replaced by dynamic linking

_Use_decl_annotations_ EXTERN_C NTSTATUS DdimonHandleEptHookCheck(
    void* InputBuffer,
    ULONG InputBufferLength,
    void* OutputBuffer,
    ULONG OutputBufferLength,
    PULONG BytesReturned
) {
    if (g_DdimonHandleEptHookCheck) {
        return g_DdimonHandleEptHookCheck(InputBuffer, InputBufferLength, OutputBuffer, OutputBufferLength, BytesReturned);
    }
    
    HYPERPLATFORM_LOG_DEBUG("DdimonHandleEptHookCheck: DdiMon not loaded, returning STATUS_NOT_SUPPORTED");
    return STATUS_NOT_SUPPORTED;
}

_Use_decl_annotations_ EXTERN_C NTSTATUS DdimonHandleEptHookCreate(
    void* InputBuffer,
    ULONG InputBufferLength,
    void* OutputBuffer,
    ULONG OutputBufferLength,
    PULONG BytesReturned
) {
    if (g_DdimonHandleEptHookCreate) {
        return g_DdimonHandleEptHookCreate(InputBuffer, InputBufferLength, OutputBuffer, OutputBufferLength, BytesReturned);
    }
    
    HYPERPLATFORM_LOG_DEBUG("DdimonHandleEptHookCreate: DdiMon not loaded, returning STATUS_NOT_SUPPORTED");
    return STATUS_NOT_SUPPORTED;
}

_Use_decl_annotations_ EXTERN_C BOOLEAN DdimonpHandleDynamicEptViolation(
    struct _EptData* ept_data,
    void* fault_va
) {
    if (g_DdimonpHandleDynamicEptViolation) {
        return g_DdimonpHandleDynamicEptViolation(ept_data, fault_va);
    }

    // Return FALSE to indicate this EPT violation was not handled by DdiMon
    return FALSE;
}

_Use_decl_annotations_ EXTERN_C NTSTATUS DdimonHandleEptReadMemory(
    void* InputBuffer,
    ULONG InputBufferLength,
    void* OutputBuffer,
    ULONG OutputBufferLength,
    PULONG BytesReturned
) {
    UNREFERENCED_PARAMETER(InputBuffer);
    UNREFERENCED_PARAMETER(InputBufferLength);
    UNREFERENCED_PARAMETER(OutputBuffer);
    UNREFERENCED_PARAMETER(OutputBufferLength);
    UNREFERENCED_PARAMETER(BytesReturned);

    HYPERPLATFORM_LOG_DEBUG("DdimonHandleEptReadMemory: DdiMon not loaded, returning STATUS_NOT_SUPPORTED");
    return STATUS_NOT_SUPPORTED;
}

_Use_decl_annotations_ EXTERN_C NTSTATUS DdimonHandleEptWriteMemory(
    void* InputBuffer,
    ULONG InputBufferLength,
    void* OutputBuffer,
    ULONG OutputBufferLength,
    PULONG BytesReturned
) {
    UNREFERENCED_PARAMETER(InputBuffer);
    UNREFERENCED_PARAMETER(InputBufferLength);
    UNREFERENCED_PARAMETER(OutputBuffer);
    UNREFERENCED_PARAMETER(OutputBufferLength);
    UNREFERENCED_PARAMETER(BytesReturned);

    HYPERPLATFORM_LOG_DEBUG("DdimonHandleEptWriteMemory: DdiMon not loaded, returning STATUS_NOT_SUPPORTED");
    return STATUS_NOT_SUPPORTED;
}

// Function to set the function pointers when DdiMon is loaded
_Use_decl_annotations_ EXTERN_C void DdimonSetFunctionPointers(
    DdimonHandleEptHookCheckPtr hookCheck,
    DdimonHandleEptHookCreatePtr hookCreate,
    DdimonpHandleDynamicEptViolationPtr eptViolation
) {
    g_DdimonHandleEptHookCheck = hookCheck;
    g_DdimonHandleEptHookCreate = hookCreate;
    g_DdimonpHandleDynamicEptViolation = eptViolation;
    
    HYPERPLATFORM_LOG_INFO("DdiMon function pointers set successfully");
}
