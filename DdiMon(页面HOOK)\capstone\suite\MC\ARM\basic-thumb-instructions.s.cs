# CS_ARCH_ARM, CS_MODE_THUMB, None
0x74,0x41 = adcs r4, r6
0xd1,0x1c = adds r1, r2, #3
0x03,0x32 = adds r2, #3
0x08,0x32 = adds r2, #8
0xd1,0x18 = adds r1, r2, r3
0x42,0x44 = add r2, r8
0x01,0xb0 = add sp, #4
0x7f,0xb0 = add sp, #508
0x01,0xb0 = add sp, #4
0x02,0xaa = add r2, sp, #8
0xff,0xaa = add r2, sp, #1020
0x82,0xb0 = sub sp, #8
0x82,0xb0 = sub sp, #8
0x9d,0x44 = add sp, r3
0x6a,0x44 = add r2, sp, r2
0x00,0xa5 = adr r5, #0
0x01,0xa2 = adr r2, #4
0xff,0xa3 = adr r3, #1020
0x1a,0x10 = asrs r2, r3, #32
0x5a,0x11 = asrs r2, r3, #5
0x5a,0x10 = asrs r2, r3, #1
0x6d,0x15 = asrs r5, r5, #21
0x6d,0x15 = asrs r5, r5, #21
0x6b,0x15 = asrs r3, r5, #21
0x15,0x41 = asrs r5, r2
0x97,0xe3 = b #1838
0x2e,0xe7 = b #-420
0x80,0xd0 = beq #-256
0x50,0xd0 = beq #160
0xd8,0xf0,0x20,0xe8 = blx #884800
0xb0,0xf1,0x40,0xe8 = blx #1769600
0xb1,0x43 = bics r1, r6
0x00,0xbe = bkpt #0
0xff,0xbe = bkpt #255
0xa0,0x47 = blx r4
0x10,0x47 = bx r2
0xcd,0x42 = cmn r5, r1
0x20,0x2e = cmp r6, #32
0xa3,0x42 = cmp r3, r4
0x88,0x45 = cmp r8, r1
0x61,0xb6 = cpsie f
0x74,0xb6 = cpsid a
0x6c,0x40 = eors r4, r5
0xff,0xcb = ldm r3, {r0, r1, r2, r3, r4, r5, r6, r7}
0xba,0xca = ldm r2!, {r1, r3, r4, r5, r7}
0x02,0xc9 = ldm r1, {r1}
0x29,0x68 = ldr r1, [r5]
0x32,0x6a = ldr r2, [r6, #32]
0xfb,0x6f = ldr r3, [r7, #124]
0x00,0x99 = ldr r1, [sp]
0x06,0x9a = ldr r2, [sp, #24]
0xff,0x9b = ldr r3, [sp, #1020]
0x97,0x4b = ldr r3, [pc, #604]
0x5c,0x4b = ldr r3, [pc, #368]
0xd1,0x58 = ldr r1, [r2, r3]
0x1c,0x78 = ldrb r4, [r3]
0x35,0x78 = ldrb r5, [r6]
0xfe,0x7f = ldrb r6, [r7, #31]
0x66,0x5d = ldrb r6, [r4, r5]
0x1b,0x88 = ldrh r3, [r3]
0x74,0x88 = ldrh r4, [r6, #2]
0xfd,0x8f = ldrh r5, [r7, #62]
0x96,0x5b = ldrh r6, [r2, r6]
0x96,0x57 = ldrsb r6, [r2, r6]
0x7b,0x5e = ldrsh r3, [r7, r1]
0x2c,0x00 = lsls r4, r5, #0
0x2c,0x01 = lsls r4, r5, #4
0x1b,0x03 = lsls r3, r3, #12
0x1b,0x03 = lsls r3, r3, #12
0x19,0x03 = lsls r1, r3, #12
0xb2,0x40 = lsls r2, r6
0x59,0x08 = lsrs r1, r3, #1
0x19,0x08 = lsrs r1, r3, #32
0x24,0x0d = lsrs r4, r4, #20
0x24,0x0d = lsrs r4, r4, #20
0x22,0x0d = lsrs r2, r4, #20
0xf2,0x40 = lsrs r2, r6
0x00,0x22 = movs r2, #0
0xff,0x22 = movs r2, #255
0x17,0x22 = movs r2, #23
0x23,0x46 = mov r3, r4
0x19,0x00 = movs r1, r3
0x51,0x43 = muls r1, r2, r1
0x5a,0x43 = muls r2, r3, r2
0x63,0x43 = muls r3, r4, r3
0xde,0x43 = mvns r6, r3
0x63,0x42 = rsbs r3, r4, #0
0x4c,0xbc = pop {r2, r3, r6}
0x86,0xb4 = push {r1, r2, r7}
0x1e,0xba = rev r6, r3
0x57,0xba = rev16 r7, r2
0xcd,0xba = revsh r5, r1
0xfa,0x41 = rors r2, r7
0x59,0x42 = rsbs r1, r3, #0
0x9c,0x41 = sbcs r4, r3
0x58,0xb6 = setend be
0x50,0xb6 = setend le
0x44,0xc1 = stm r1!, {r2, r6}
0x8e,0xc1 = stm r1!, {r1, r2, r3, r7}
0x3a,0x60 = str r2, [r7]
0x3a,0x60 = str r2, [r7]
0x4d,0x60 = str r5, [r1, #4]
0xfb,0x67 = str r3, [r7, #124]
0x00,0x92 = str r2, [sp]
0x00,0x93 = str r3, [sp]
0x05,0x94 = str r4, [sp, #20]
0xff,0x95 = str r5, [sp, #1020]
0xfa,0x50 = str r2, [r7, r3]
0x1c,0x70 = strb r4, [r3]
0x35,0x70 = strb r5, [r6]
0xfe,0x77 = strb r6, [r7, #31]
0x66,0x55 = strb r6, [r4, r5]
0x1b,0x80 = strh r3, [r3]
0x74,0x80 = strh r4, [r6, #2]
0xfd,0x87 = strh r5, [r7, #62]
0x96,0x53 = strh r6, [r2, r6]
0xd1,0x1e = subs r1, r2, #3
0x03,0x3a = subs r2, #3
0x08,0x3a = subs r2, #8
0x83,0xb0 = sub sp, #12
0xff,0xb0 = sub sp, #508
0xd1,0x1a = subs r1, r2, r3
0x00,0xdf = svc #0
0xff,0xdf = svc #255
0x6b,0xb2 = sxtb r3, r5
0x2b,0xb2 = sxth r3, r5
0x0e,0x42 = tst r6, r1
0xd7,0xb2 = uxtb r7, r2
0xa1,0xb2 = uxth r1, r4
