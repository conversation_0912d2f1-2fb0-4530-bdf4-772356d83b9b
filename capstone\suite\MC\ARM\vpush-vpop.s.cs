# CS_ARCH_ARM, CS_MODE_ARM, None
0x0a,0x8b,0x2d,0xed = vpush {d8, d9, d10, d11, d12}
0x05,0x4a,0x2d,0xed = vpush {s8, s9, s10, s11, s12}
0x0a,0x8b,0xbd,0xec = vpop {d8, d9, d10, d11, d12}
0x05,0x4a,0xbd,0xec = vpop {s8, s9, s10, s11, s12}
0x0a,0x8b,0x2d,0xed = vpush {d8, d9, d10, d11, d12}
0x05,0x4a,0x2d,0xed = vpush {s8, s9, s10, s11, s12}
0x0a,0x8b,0xbd,0xec = vpop {d8, d9, d10, d11, d12}
0x05,0x4a,0xbd,0xec = vpop {s8, s9, s10, s11, s12}
