# CS_ARCH_ARM, CS_MODE_ARM, None
0x0f,0x10,0xa2,0xe2 = adc r1, r2, #15
0xf0,0x10,0xa2,0xe2 = adc r1, r2, #240
0x0f,0x1c,0xa2,0xe2 = adc r1, r2, #3840
0x0f,0x1a,0xa2,0xe2 = adc r1, r2, #61440
0x0f,0x18,0xa2,0xe2 = adc r1, r2, #983040
0x0f,0x16,0xa2,0xe2 = adc r1, r2, #15728640
0x0f,0x14,0xa2,0xe2 = adc r1, r2, #251658240
0x0f,0x12,0xa2,0xe2 = adc r1, r2, #4026531840
0xff,0x12,0xa2,0xe2 = adc r1, r2, #4026531855
0x0f,0x1c,0xb2,0xe2 = adcs r1, r2, #3840
0x0f,0x1c,0xb2,0x02 = adcseq r1, r2, #3840
0x0f,0x1c,0xa2,0x02 = adceq r1, r2, #3840
0x06,0x40,0xa5,0xe0 = adc r4, r5, r6
0x86,0x40,0xa5,0xe0 = adc r4, r5, r6, lsl #1
0x86,0x4f,0xa5,0xe0 = adc r4, r5, r6, lsl #31
0xa6,0x40,0xa5,0xe0 = adc r4, r5, r6, lsr #1
0xa6,0x4f,0xa5,0xe0 = adc r4, r5, r6, lsr #31
0x26,0x40,0xa5,0xe0 = adc r4, r5, r6, lsr #32
0xc6,0x40,0xa5,0xe0 = adc r4, r5, r6, asr #1
0xc6,0x4f,0xa5,0xe0 = adc r4, r5, r6, asr #31
0x46,0x40,0xa5,0xe0 = adc r4, r5, r6, asr #32
0xe6,0x40,0xa5,0xe0 = adc r4, r5, r6, ror #1
0xe6,0x4f,0xa5,0xe0 = adc r4, r5, r6, ror #31
0x18,0x69,0xa7,0xe0 = adc r6, r7, r8, lsl r9
0x38,0x69,0xa7,0xe0 = adc r6, r7, r8, lsr r9
0x58,0x69,0xa7,0xe0 = adc r6, r7, r8, asr r9
0x78,0x69,0xa7,0xe0 = adc r6, r7, r8, ror r9
0x66,0x40,0xa5,0xe0 = adc r4, r5, r6, rrx
0x06,0x50,0xa5,0xe0 = adc r5, r5, r6
0x85,0x40,0xa4,0xe0 = adc r4, r4, r5, lsl #1
0x85,0x4f,0xa4,0xe0 = adc r4, r4, r5, lsl #31
0xa5,0x40,0xa4,0xe0 = adc r4, r4, r5, lsr #1
0xa5,0x4f,0xa4,0xe0 = adc r4, r4, r5, lsr #31
0x25,0x40,0xa4,0xe0 = adc r4, r4, r5, lsr #32
0xc5,0x40,0xa4,0xe0 = adc r4, r4, r5, asr #1
0xc5,0x4f,0xa4,0xe0 = adc r4, r4, r5, asr #31
0x45,0x40,0xa4,0xe0 = adc r4, r4, r5, asr #32
0xe5,0x40,0xa4,0xe0 = adc r4, r4, r5, ror #1
0xe5,0x4f,0xa4,0xe0 = adc r4, r4, r5, ror #31
0x65,0x40,0xa4,0xe0 = adc r4, r4, r5, rrx
0x17,0x69,0xa6,0xe0 = adc r6, r6, r7, lsl r9
0x37,0x69,0xa6,0xe0 = adc r6, r6, r7, lsr r9
0x57,0x69,0xa6,0xe0 = adc r6, r6, r7, asr r9
0x77,0x69,0xa6,0xe0 = adc r6, r6, r7, ror r9
0x65,0x40,0xa4,0xe0 = adc r4, r4, r5, rrx
0x0f,0x4a,0x85,0xe2 = add r4, r5, #61440
0x06,0x40,0x85,0xe0 = add r4, r5, r6
0x86,0x42,0x85,0xe0 = add r4, r5, r6, lsl #5
0xa6,0x42,0x85,0xe0 = add r4, r5, r6, lsr #5
0xa6,0x42,0x85,0xe0 = add r4, r5, r6, lsr #5
0xc6,0x42,0x85,0xe0 = add r4, r5, r6, asr #5
0xe6,0x42,0x85,0xe0 = add r4, r5, r6, ror #5
0x18,0x69,0x87,0xe0 = add r6, r7, r8, lsl r9
0x13,0x49,0x84,0xe0 = add r4, r4, r3, lsl r9
0x38,0x69,0x87,0xe0 = add r6, r7, r8, lsr r9
0x58,0x69,0x87,0xe0 = add r6, r7, r8, asr r9
0x78,0x69,0x87,0xe0 = add r6, r7, r8, ror r9
0x66,0x40,0x85,0xe0 = add r4, r5, r6, rrx
0x0f,0x5a,0x85,0xe2 = add r5, r5, #61440
0x05,0x40,0x84,0xe0 = add r4, r4, r5
0x85,0x42,0x84,0xe0 = add r4, r4, r5, lsl #5
0xa5,0x42,0x84,0xe0 = add r4, r4, r5, lsr #5
0xa5,0x42,0x84,0xe0 = add r4, r4, r5, lsr #5
0xc5,0x42,0x84,0xe0 = add r4, r4, r5, asr #5
0xe5,0x42,0x84,0xe0 = add r4, r4, r5, ror #5
0x17,0x69,0x86,0xe0 = add r6, r6, r7, lsl r9
0x37,0x69,0x86,0xe0 = add r6, r6, r7, lsr r9
0x57,0x69,0x86,0xe0 = add r6, r6, r7, asr r9
0x77,0x69,0x86,0xe0 = add r6, r6, r7, ror r9
0x65,0x40,0x84,0xe0 = add r4, r4, r5, rrx
0x04,0x00,0x40,0xe2 = sub r0, r0, #4
0x15,0x40,0x45,0xe2 = sub r4, r5, #21
0x22,0x30,0x81,0xe0 = add r3, r1, r2, lsr #32
0x42,0x30,0x81,0xe0 = add r3, r1, r2, asr #32
0x0f,0xa0,0x01,0xe2 = and r10, r1, #15
0x06,0xa0,0x01,0xe0 = and r10, r1, r6
0x06,0xa5,0x01,0xe0 = and r10, r1, r6, lsl #10
0x26,0xa5,0x01,0xe0 = and r10, r1, r6, lsr #10
0x26,0xa5,0x01,0xe0 = and r10, r1, r6, lsr #10
0x46,0xa5,0x01,0xe0 = and r10, r1, r6, asr #10
0x66,0xa5,0x01,0xe0 = and r10, r1, r6, ror #10
0x18,0x62,0x07,0xe0 = and r6, r7, r8, lsl r2
0x38,0x62,0x07,0xe0 = and r6, r7, r8, lsr r2
0x58,0x62,0x07,0xe0 = and r6, r7, r8, asr r2
0x78,0x62,0x07,0xe0 = and r6, r7, r8, ror r2
0x66,0xa0,0x01,0xe0 = and r10, r1, r6, rrx
0x02,0x21,0xc3,0xe3 = bic r2, r3, #-2147483648
0x0f,0x10,0x01,0xe2 = and r1, r1, #15
0x01,0xa0,0x0a,0xe0 = and r10, r10, r1
0x01,0xa5,0x0a,0xe0 = and r10, r10, r1, lsl #10
0x21,0xa5,0x0a,0xe0 = and r10, r10, r1, lsr #10
0x21,0xa5,0x0a,0xe0 = and r10, r10, r1, lsr #10
0x41,0xa5,0x0a,0xe0 = and r10, r10, r1, asr #10
0x61,0xa5,0x0a,0xe0 = and r10, r10, r1, ror #10
0x17,0x62,0x06,0xe0 = and r6, r6, r7, lsl r2
0x37,0x62,0x06,0xe0 = and r6, r6, r7, lsr r2
0x57,0x62,0x06,0xe0 = and r6, r6, r7, asr r2
0x77,0x62,0x06,0xe0 = and r6, r6, r7, ror r2
0x61,0xa0,0x0a,0xe0 = and r10, r10, r1, rrx
0x22,0x30,0x01,0xe0 = and r3, r1, r2, lsr #32
0x42,0x30,0x01,0xe0 = and r3, r1, r2, asr #32
0x44,0x20,0xa0,0xe1 = asr r2, r4, #32
0x44,0x21,0xa0,0xe1 = asr r2, r4, #2
0x04,0x20,0xa0,0xe1 = mov r2, r4
0x44,0x41,0xa0,0xe1 = asr r4, r4, #2
0x9f,0x51,0xd3,0xe7 = bfc r5, #3, #17
0x9f,0x51,0xd3,0x37 = bfclo r5, #3, #17
0x92,0x51,0xd3,0xe7 = bfi r5, r2, #3, #17
0x92,0x51,0xd3,0x17 = bfine r5, r2, #3, #17
0x0f,0xa0,0xc1,0xe3 = bic r10, r1, #15
0x06,0xa0,0xc1,0xe1 = bic r10, r1, r6
0x06,0xa5,0xc1,0xe1 = bic r10, r1, r6, lsl #10
0x26,0xa5,0xc1,0xe1 = bic r10, r1, r6, lsr #10
0x26,0xa5,0xc1,0xe1 = bic r10, r1, r6, lsr #10
0x46,0xa5,0xc1,0xe1 = bic r10, r1, r6, asr #10
0x66,0xa5,0xc1,0xe1 = bic r10, r1, r6, ror #10
0x18,0x62,0xc7,0xe1 = bic r6, r7, r8, lsl r2
0x38,0x62,0xc7,0xe1 = bic r6, r7, r8, lsr r2
0x58,0x62,0xc7,0xe1 = bic r6, r7, r8, asr r2
0x78,0x62,0xc7,0xe1 = bic r6, r7, r8, ror r2
0x66,0xa0,0xc1,0xe1 = bic r10, r1, r6, rrx
0x0f,0x10,0xc1,0xe3 = bic r1, r1, #15
0x01,0xa0,0xca,0xe1 = bic r10, r10, r1
0x01,0xa5,0xca,0xe1 = bic r10, r10, r1, lsl #10
0x21,0xa5,0xca,0xe1 = bic r10, r10, r1, lsr #10
0x21,0xa5,0xca,0xe1 = bic r10, r10, r1, lsr #10
0x41,0xa5,0xca,0xe1 = bic r10, r10, r1, asr #10
0x61,0xa5,0xca,0xe1 = bic r10, r10, r1, ror #10
0x17,0x62,0xc6,0xe1 = bic r6, r6, r7, lsl r2
0x37,0x62,0xc6,0xe1 = bic r6, r6, r7, lsr r2
0x57,0x62,0xc6,0xe1 = bic r6, r6, r7, asr r2
0x77,0x62,0xc6,0xe1 = bic r6, r6, r7, ror r2
0x61,0xa0,0xca,0xe1 = bic r10, r10, r1, rrx
0x22,0x30,0xc1,0xe1 = bic r3, r1, r2, lsr #32
0x42,0x30,0xc1,0xe1 = bic r3, r1, r2, asr #32
0x7a,0x00,0x20,0xe1 = bkpt #10
0x7f,0xff,0x2f,0xe1 = bkpt #65535
0x27,0x3b,0x6d,0x9b = blls #28634268
0xa0,0xb0,0x7b,0xfa = blx #32424576
0x50,0xd8,0x3d,0xfa = blx #16212288
0x32,0xff,0x2f,0xe1 = blx r2
0x32,0xff,0x2f,0x11 = blxne r2
0x12,0xff,0x2f,0xe1 = bx r2
0x12,0xff,0x2f,0x11 = bxne r2
0x22,0xff,0x2f,0xe1 = bxj r2
0x22,0xff,0x2f,0x11 = bxjne r2
0x81,0x17,0x11,0xee = cdp p7, #1, c1, c1, c1, #4
0x81,0x17,0x11,0xfe = cdp2 p7, #1, c1, c1, c1, #4
0xe0,0x6c,0x0c,0xfe = cdp2 p12, #0, c6, c12, c0, #7
0x81,0x17,0x11,0x1e = cdpne p7, #1, c1, c1, c1, #4
0x1f,0xf0,0x7f,0xf5 = clrex 
0x12,0x1f,0x6f,0xe1 = clz r1, r2
0x12,0x1f,0x6f,0x01 = clzeq r1, r2
0x0f,0x00,0x71,0xe3 = cmn r1, #15
0x06,0x00,0x71,0xe1 = cmn r1, r6
0x06,0x05,0x71,0xe1 = cmn r1, r6, lsl #10
0x26,0x05,0x71,0xe1 = cmn r1, r6, lsr #10
0x26,0x05,0x7d,0xe1 = cmn sp, r6, lsr #10
0x46,0x05,0x71,0xe1 = cmn r1, r6, asr #10
0x66,0x05,0x71,0xe1 = cmn r1, r6, ror #10
0x18,0x02,0x77,0xe1 = cmn r7, r8, lsl r2
0x38,0x02,0x7d,0xe1 = cmn sp, r8, lsr r2
0x58,0x02,0x77,0xe1 = cmn r7, r8, asr r2
0x78,0x02,0x77,0xe1 = cmn r7, r8, ror r2
0x66,0x00,0x71,0xe1 = cmn r1, r6, rrx
0x0f,0x00,0x51,0xe3 = cmp r1, #15
0x06,0x00,0x51,0xe1 = cmp r1, r6
0x06,0x05,0x51,0xe1 = cmp r1, r6, lsl #10
0x26,0x05,0x51,0xe1 = cmp r1, r6, lsr #10
0x26,0x05,0x5d,0xe1 = cmp sp, r6, lsr #10
0x46,0x05,0x51,0xe1 = cmp r1, r6, asr #10
0x66,0x05,0x51,0xe1 = cmp r1, r6, ror #10
0x18,0x02,0x57,0xe1 = cmp r7, r8, lsl r2
0x38,0x02,0x5d,0xe1 = cmp sp, r8, lsr r2
0x58,0x02,0x57,0xe1 = cmp r7, r8, asr r2
0x78,0x02,0x57,0xe1 = cmp r7, r8, ror r2
0x66,0x00,0x51,0xe1 = cmp r1, r6, rrx
0x02,0x00,0x70,0xe3 = cmn r0, #2
0x00,0x00,0x5e,0xe3 = cmp lr, #0
0xc0,0x01,0x08,0xf1 = cpsie aif
0x0f,0x00,0x02,0xf1 = cps #15
0xca,0x00,0x0e,0xf1 = cpsid if, #10
0xf0,0xf0,0x20,0xe3 = dbg #0
0xf5,0xf0,0x20,0xe3 = dbg #5
0xff,0xf0,0x20,0xe3 = dbg #15
0x5f,0xf0,0x7f,0xf5 = dmb sy
0x5e,0xf0,0x7f,0xf5 = dmb st
0x5d,0xf0,0x7f,0xf5 = dmb #0xd
0x5c,0xf0,0x7f,0xf5 = dmb #0xc
0x5b,0xf0,0x7f,0xf5 = dmb ish
0x5a,0xf0,0x7f,0xf5 = dmb ishst
0x59,0xf0,0x7f,0xf5 = dmb #0x9
0x58,0xf0,0x7f,0xf5 = dmb #0x8
0x57,0xf0,0x7f,0xf5 = dmb nsh
0x56,0xf0,0x7f,0xf5 = dmb nshst
0x55,0xf0,0x7f,0xf5 = dmb #0x5
0x54,0xf0,0x7f,0xf5 = dmb #0x4
0x53,0xf0,0x7f,0xf5 = dmb osh
0x52,0xf0,0x7f,0xf5 = dmb oshst
0x51,0xf0,0x7f,0xf5 = dmb #0x1
0x50,0xf0,0x7f,0xf5 = dmb #0x0
0x5f,0xf0,0x7f,0xf5 = dmb sy
0x5e,0xf0,0x7f,0xf5 = dmb st
0x5b,0xf0,0x7f,0xf5 = dmb ish
0x5b,0xf0,0x7f,0xf5 = dmb ish
0x5a,0xf0,0x7f,0xf5 = dmb ishst
0x5a,0xf0,0x7f,0xf5 = dmb ishst
0x57,0xf0,0x7f,0xf5 = dmb nsh
0x57,0xf0,0x7f,0xf5 = dmb nsh
0x56,0xf0,0x7f,0xf5 = dmb nshst
0x56,0xf0,0x7f,0xf5 = dmb nshst
0x53,0xf0,0x7f,0xf5 = dmb osh
0x52,0xf0,0x7f,0xf5 = dmb oshst
0x5f,0xf0,0x7f,0xf5 = dmb sy
0x4f,0xf0,0x7f,0xf5 = dsb sy
0x4e,0xf0,0x7f,0xf5 = dsb st
0x4d,0xf0,0x7f,0xf5 = dsb #0xd
0x4c,0xf0,0x7f,0xf5 = dsb #0xc
0x4b,0xf0,0x7f,0xf5 = dsb ish
0x4a,0xf0,0x7f,0xf5 = dsb ishst
0x49,0xf0,0x7f,0xf5 = dsb #0x9
0x48,0xf0,0x7f,0xf5 = dsb #0x8
0x47,0xf0,0x7f,0xf5 = dsb nsh
0x46,0xf0,0x7f,0xf5 = dsb nshst
0x45,0xf0,0x7f,0xf5 = dsb #0x5
0x44,0xf0,0x7f,0xf5 = dsb #0x4
0x43,0xf0,0x7f,0xf5 = dsb osh
0x42,0xf0,0x7f,0xf5 = dsb oshst
0x41,0xf0,0x7f,0xf5 = dsb #0x1
0x40,0xf0,0x7f,0xf5 = dsb #0x0
0x48,0xf0,0x7f,0xf5 = dsb #0x8
0x47,0xf0,0x7f,0xf5 = dsb nsh
0x4f,0xf0,0x7f,0xf5 = dsb sy
0x4e,0xf0,0x7f,0xf5 = dsb st
0x4b,0xf0,0x7f,0xf5 = dsb ish
0x4b,0xf0,0x7f,0xf5 = dsb ish
0x4a,0xf0,0x7f,0xf5 = dsb ishst
0x4a,0xf0,0x7f,0xf5 = dsb ishst
0x47,0xf0,0x7f,0xf5 = dsb nsh
0x47,0xf0,0x7f,0xf5 = dsb nsh
0x46,0xf0,0x7f,0xf5 = dsb nshst
0x46,0xf0,0x7f,0xf5 = dsb nshst
0x43,0xf0,0x7f,0xf5 = dsb osh
0x42,0xf0,0x7f,0xf5 = dsb oshst
0x4f,0xf0,0x7f,0xf5 = dsb sy
0x4f,0xf0,0x7f,0xf5 = dsb sy
0x42,0xf0,0x7f,0xf5 = dsb oshst
0x0f,0x4a,0x25,0xe2 = eor r4, r5, #61440
0x06,0x40,0x25,0xe0 = eor r4, r5, r6
0x86,0x42,0x25,0xe0 = eor r4, r5, r6, lsl #5
0xa6,0x42,0x25,0xe0 = eor r4, r5, r6, lsr #5
0xa6,0x42,0x25,0xe0 = eor r4, r5, r6, lsr #5
0xc6,0x42,0x25,0xe0 = eor r4, r5, r6, asr #5
0xe6,0x42,0x25,0xe0 = eor r4, r5, r6, ror #5
0x18,0x69,0x27,0xe0 = eor r6, r7, r8, lsl r9
0x38,0x69,0x27,0xe0 = eor r6, r7, r8, lsr r9
0x58,0x69,0x27,0xe0 = eor r6, r7, r8, asr r9
0x78,0x69,0x27,0xe0 = eor r6, r7, r8, ror r9
0x66,0x40,0x25,0xe0 = eor r4, r5, r6, rrx
0x0f,0x5a,0x25,0xe2 = eor r5, r5, #61440
0x05,0x40,0x24,0xe0 = eor r4, r4, r5
0x85,0x42,0x24,0xe0 = eor r4, r4, r5, lsl #5
0xa5,0x42,0x24,0xe0 = eor r4, r4, r5, lsr #5
0xa5,0x42,0x24,0xe0 = eor r4, r4, r5, lsr #5
0xc5,0x42,0x24,0xe0 = eor r4, r4, r5, asr #5
0xe5,0x42,0x24,0xe0 = eor r4, r4, r5, ror #5
0x17,0x69,0x26,0xe0 = eor r6, r6, r7, lsl r9
0x37,0x69,0x26,0xe0 = eor r6, r6, r7, lsr r9
0x57,0x69,0x26,0xe0 = eor r6, r6, r7, asr r9
0x77,0x69,0x26,0xe0 = eor r6, r6, r7, ror r9
0x65,0x40,0x24,0xe0 = eor r4, r4, r5, rrx
0x22,0x30,0x21,0xe0 = eor r3, r1, r2, lsr #32
0x42,0x30,0x21,0xe0 = eor r3, r1, r2, asr #32
0x6f,0xf0,0x7f,0xf5 = isb sy
0x6f,0xf0,0x7f,0xf5 = isb sy
0x6f,0xf0,0x7f,0xf5 = isb sy
0x61,0xf0,0x7f,0xf5 = isb #0x1
0x01,0x80,0x91,0xfd = ldc2 p0, c8, [r1, #4]
0x00,0x71,0x92,0xfd = ldc2 p1, c7, [r2]
0x38,0x62,0x13,0xfd = ldc2 p2, c6, [r3, #-224]
0x1e,0x53,0x34,0xfd = ldc2 p3, c5, [r4, #-120]!
0x04,0x44,0xb5,0xfc = ldc2 p4, c4, [r5], #16
0x12,0x35,0x36,0xfc = ldc2 p5, c3, [r6], #-72
0x01,0x26,0xd7,0xfd = ldc2l p6, c2, [r7, #4]
0x00,0x17,0xd8,0xfd = ldc2l p7, c1, [r8]
0x38,0x08,0x59,0xfd = ldc2l p8, c0, [r9, #-224]
0x1e,0x19,0x7a,0xfd = ldc2l p9, c1, [r10, #-120]!
0x04,0x20,0xfb,0xfc = ldc2l p0, c2, [r11], #16
0x12,0x31,0x7c,0xfc = ldc2l p1, c3, [r12], #-72
0x01,0x4c,0x90,0xed = ldc p12, c4, [r0, #4]
0x00,0x5d,0x91,0xed = ldc p13, c5, [r1]
0x38,0x6e,0x12,0xed = ldc p14, c6, [r2, #-224]
0x1e,0x7f,0x33,0xed = ldc p15, c7, [r3, #-120]!
0x04,0x85,0xb4,0xec = ldc p5, c8, [r4], #16
0x12,0x94,0x35,0xec = ldc p4, c9, [r5], #-72
0x01,0xa3,0xd6,0xed = ldcl p3, c10, [r6, #4]
0x00,0xb2,0xd7,0xed = ldcl p2, c11, [r7]
0x38,0xc1,0x58,0xed = ldcl p1, c12, [r8, #-224]
0x1e,0xd0,0x79,0xed = ldcl p0, c13, [r9, #-120]!
0x04,0xe6,0xfa,0xec = ldcl p6, c14, [r10], #16
0x12,0xf7,0x7b,0xec = ldcl p7, c15, [r11], #-72
0x01,0x4c,0x90,0x3d = ldclo p12, c4, [r0, #4]
0x00,0x5d,0x91,0x8d = ldchi p13, c5, [r1]
0x38,0x6e,0x12,0x2d = ldchs p14, c6, [r2, #-224]
0x1e,0x7f,0x33,0x3d = ldclo p15, c7, [r3, #-120]!
0x04,0x85,0xb4,0x0c = ldceq p5, c8, [r4], #16
0x12,0x94,0x35,0xcc = ldcgt p4, c9, [r5], #-72
0x01,0xa3,0xd6,0xbd = ldcllt p3, c10, [r6, #4]
0x00,0xb2,0xd7,0xad = ldclge p2, c11, [r7]
0x38,0xc1,0x58,0xdd = ldclle p1, c12, [r8, #-224]
0x1e,0xd0,0x79,0x1d = ldclne p0, c13, [r9, #-120]!
0x04,0xe6,0xfa,0x0c = ldcleq p6, c14, [r10], #16
0x12,0xf7,0x7b,0x8c = ldclhi p7, c15, [r11], #-72
0x19,0x82,0x91,0xfc = ldc2 p2, c8, [r1], {25}
0x7a,0x20,0x92,0xe8 = ldm r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x92,0xe8 = ldm r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x92,0xe9 = ldmib r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x12,0xe8 = ldmda r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x12,0xe9 = ldmdb r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x92,0xe8 = ldm r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0xb2,0xe8 = ldm r2!, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0xb2,0xe9 = ldmib r2!, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x32,0xe8 = ldmda r2!, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x32,0xe9 = ldmdb r2!, {r1, r3, r4, r5, r6, sp}
0x05,0x40,0xd0,0xe8 = ldm r0, {r0, r2, lr} ^
0x0f,0x80,0xfd,0xe8 = ldm sp!, {r0, r1, r2, r3, pc} ^
0x9f,0x3f,0xd4,0xe1 = ldrexb r3, [r4]
0x9f,0x2f,0xf5,0xe1 = ldrexh r2, [r5]
0x9f,0x1f,0x97,0xe1 = ldrex r1, [r7]
0x9f,0x6f,0xb8,0xe1 = ldrexd r6, r7, [r8]
0xb0,0x80,0x7b,0x80 = ldrhthi r8, [r11], #-0
0xb0,0x80,0xfb,0x80 = ldrhthi r8, [r11], #0
0x84,0x2f,0xa0,0xe1 = lsl r2, r4, #31
0x84,0x20,0xa0,0xe1 = lsl r2, r4, #1
0x04,0x20,0xa0,0xe1 = mov r2, r4
0x84,0x40,0xa0,0xe1 = lsl r4, r4, #1
0x24,0x20,0xa0,0xe1 = lsr r2, r4, #32
0x24,0x21,0xa0,0xe1 = lsr r2, r4, #2
0x04,0x20,0xa0,0xe1 = mov r2, r4
0x24,0x41,0xa0,0xe1 = lsr r4, r4, #2
0x91,0x57,0x21,0xee = mcr p7, #1, r5, c1, c1, #4
0x91,0x57,0x21,0xfe = mcr2 p7, #1, r5, c1, c1, #4
0x91,0x57,0x21,0x9e = mcrls p7, #1, r5, c1, c1, #4
0xf1,0x57,0x44,0xec = mcrr p7, #15, r5, r4, c1
0xf1,0x57,0x44,0xfc = mcrr2 p7, #15, r5, r4, c1
0xf1,0x57,0x44,0xcc = mcrrgt p7, #15, r5, r4, c1
0x92,0x43,0x21,0xe0 = mla r1, r2, r3, r4
0x92,0x43,0x31,0xe0 = mlas r1, r2, r3, r4
0x92,0x43,0x21,0x10 = mlane r1, r2, r3, r4
0x92,0x43,0x31,0x10 = mlasne r1, r2, r3, r4
0x95,0x36,0x62,0xe0 = mls r2, r5, r6, r3
0x95,0x36,0x62,0x10 = mlsne r2, r5, r6, r3
0x07,0x30,0xa0,0xe3 = mov r3, #7
0xff,0x4e,0xa0,0xe3 = mov r4, #4080
0xff,0x58,0xa0,0xe3 = mov r5, #16711680
0xff,0x6f,0x0f,0xe3 = movw r6, #65535
0xff,0x9f,0x0f,0xe3 = movw r9, #65535
0x07,0x30,0xb0,0xe3 = movs r3, #7
0xff,0x4e,0xa0,0x03 = moveq r4, #4080
0xff,0x58,0xb0,0x03 = movseq r5, #16711680
0x03,0x20,0xa0,0xe1 = mov r2, r3
0x03,0x20,0xb0,0xe1 = movs r2, r3
0x03,0x20,0xa0,0x01 = moveq r2, r3
0x03,0x20,0xb0,0x01 = movseq r2, r3
0x08,0xc0,0xa0,0xe1 = mov r12, r8
0x03,0x20,0xa0,0xe1 = mov r2, r3
0x08,0xc0,0xa0,0xe1 = mov r12, r8
0x03,0x20,0xa0,0xe1 = mov r2, r3
0x08,0xc0,0xa0,0xe1 = mov r12, r8
0x03,0x20,0xa0,0xe1 = mov r2, r3
0x08,0xc0,0xa0,0xe1 = mov r12, r8
0x03,0x20,0xa0,0xe1 = mov r2, r3
0x07,0x30,0x40,0xe3 = movt r3, #7
0xff,0x6f,0x4f,0xe3 = movt r6, #65535
0xf0,0x4f,0x40,0x03 = movteq r4, #4080
0x92,0x1e,0x11,0xee = mrc p14, #0, r1, c1, c2, #4
0xd6,0xff,0xff,0xee = mrc p15, #7, apsr_nzcv, c15, c6, #6
0x92,0x1e,0x11,0xfe = mrc2 p14, #0, r1, c1, c2, #4
0x30,0xf9,0xff,0xfe = mrc2 p9, #7, apsr_nzcv, c15, c0, #1
0xd6,0xff,0xff,0x0e = mrceq p15, #7, apsr_nzcv, c15, c6, #6
0x11,0x57,0x54,0xec = mrrc p7, #1, r5, r4, c1
0x11,0x57,0x54,0xfc = mrrc2 p7, #1, r5, r4, c1
0x11,0x57,0x54,0x3c = mrrclo p7, #1, r5, r4, c1
0x00,0x80,0x0f,0xe1 = mrs r8, apsr
0x00,0x80,0x0f,0xe1 = mrs r8, apsr
0x00,0x80,0x4f,0xe1 = mrs r8, spsr
0x05,0xf0,0x28,0xe3 = msr APSR_nzcvq, #5
0x05,0xf0,0x24,0xe3 = msr APSR_g, #5
0x05,0xf0,0x28,0xe3 = msr APSR_nzcvq, #5
0x05,0xf0,0x28,0xe3 = msr APSR_nzcvq, #5
0x05,0xf0,0x2c,0xe3 = msr APSR_nzcvqg, #5
0x05,0xf0,0x29,0xe3 = msr CPSR_fc, #5
0x05,0xf0,0x21,0xe3 = msr CPSR_c, #5
0x05,0xf0,0x22,0xe3 = msr CPSR_x, #5
0x05,0xf0,0x29,0xe3 = msr CPSR_fc, #5
0x05,0xf0,0x29,0xe3 = msr CPSR_fc, #5
0x05,0xf0,0x2e,0xe3 = msr CPSR_fsx, #5
0x05,0xf0,0x69,0xe3 = msr SPSR_fc, #5
0x05,0xf0,0x6f,0xe3 = msr SPSR_fsxc, #5
0x05,0xf0,0x2f,0xe3 = msr CPSR_fsxc, #5
0x00,0xf0,0x28,0xe1 = msr APSR_nzcvq, r0
0x00,0xf0,0x24,0xe1 = msr APSR_g, r0
0x00,0xf0,0x28,0xe1 = msr APSR_nzcvq, r0
0x00,0xf0,0x28,0xe1 = msr APSR_nzcvq, r0
0x00,0xf0,0x2c,0xe1 = msr APSR_nzcvqg, r0
0x00,0xf0,0x29,0xe1 = msr CPSR_fc, r0
0x00,0xf0,0x21,0xe1 = msr CPSR_c, r0
0x00,0xf0,0x22,0xe1 = msr CPSR_x, r0
0x00,0xf0,0x29,0xe1 = msr CPSR_fc, r0
0x00,0xf0,0x29,0xe1 = msr CPSR_fc, r0
0x00,0xf0,0x2e,0xe1 = msr CPSR_fsx, r0
0x00,0xf0,0x69,0xe1 = msr SPSR_fc, r0
0x00,0xf0,0x6f,0xe1 = msr SPSR_fsxc, r0
0x00,0xf0,0x2f,0xe1 = msr CPSR_fsxc, r0
0x96,0x07,0x05,0xe0 = mul r5, r6, r7
0x96,0x07,0x15,0xe0 = muls r5, r6, r7
0x96,0x07,0x05,0xc0 = mulgt r5, r6, r7
0x96,0x07,0x15,0xd0 = mulsle r5, r6, r7
0x07,0x30,0xe0,0xe3 = mvn r3, #7
0xff,0x4e,0xe0,0xe3 = mvn r4, #4080
0xff,0x58,0xe0,0xe3 = mvn r5, #16711680
0x07,0x30,0xf0,0xe3 = mvns r3, #7
0xff,0x4e,0xe0,0x03 = mvneq r4, #4080
0xff,0x58,0xf0,0x03 = mvnseq r5, #16711680
0x03,0x20,0xe0,0xe1 = mvn r2, r3
0x03,0x20,0xf0,0xe1 = mvns r2, r3
0x86,0x59,0xe0,0xe1 = mvn r5, r6, lsl #19
0xa6,0x54,0xe0,0xe1 = mvn r5, r6, lsr #9
0x46,0x52,0xe0,0xe1 = mvn r5, r6, asr #4
0x66,0x53,0xe0,0xe1 = mvn r5, r6, ror #6
0x66,0x50,0xe0,0xe1 = mvn r5, r6, rrx
0x03,0x20,0xe0,0x01 = mvneq r2, r3
0x03,0x25,0xf0,0x01 = mvnseq r2, r3, lsl #10
0x16,0x57,0xe0,0xe1 = mvn r5, r6, lsl r7
0x36,0x57,0xf0,0xe1 = mvns r5, r6, lsr r7
0x56,0x57,0xe0,0xc1 = mvngt r5, r6, asr r7
0x76,0x57,0xf0,0xb1 = mvnslt r5, r6, ror r7
0x00,0x50,0x68,0xe2 = rsb r5, r8, #0
0x00,0xf0,0x20,0xe3 = nop 
0x00,0xf0,0x20,0xe3 = nop 
0x00,0xf0,0x20,0xc3 = nopgt 
0x0f,0x4a,0x85,0xe3 = orr r4, r5, #61440
0x06,0x40,0x85,0xe1 = orr r4, r5, r6
0x86,0x42,0x85,0xe1 = orr r4, r5, r6, lsl #5
0xa6,0x42,0x85,0xe1 = orr r4, r5, r6, lsr #5
0xa6,0x42,0x85,0xe1 = orr r4, r5, r6, lsr #5
0xc6,0x42,0x85,0xe1 = orr r4, r5, r6, asr #5
0xe6,0x42,0x85,0xe1 = orr r4, r5, r6, ror #5
0x18,0x69,0x87,0xe1 = orr r6, r7, r8, lsl r9
0x38,0x69,0x87,0xe1 = orr r6, r7, r8, lsr r9
0x58,0x69,0x87,0xe1 = orr r6, r7, r8, asr r9
0x78,0x69,0x87,0xe1 = orr r6, r7, r8, ror r9
0x66,0x40,0x85,0xe1 = orr r4, r5, r6, rrx
0x0f,0x5a,0x85,0xe3 = orr r5, r5, #61440
0x05,0x40,0x84,0xe1 = orr r4, r4, r5
0x85,0x42,0x84,0xe1 = orr r4, r4, r5, lsl #5
0xa5,0x42,0x84,0xe1 = orr r4, r4, r5, lsr #5
0xa5,0x42,0x84,0xe1 = orr r4, r4, r5, lsr #5
0xc5,0x42,0x84,0xe1 = orr r4, r4, r5, asr #5
0xe5,0x42,0x84,0xe1 = orr r4, r4, r5, ror #5
0x17,0x69,0x86,0xe1 = orr r6, r6, r7, lsl r9
0x37,0x69,0x86,0xe1 = orr r6, r6, r7, lsr r9
0x57,0x69,0x86,0xe1 = orr r6, r6, r7, asr r9
0x77,0x69,0x86,0xe1 = orr r6, r6, r7, ror r9
0x65,0x40,0x84,0xe1 = orr r4, r4, r5, rrx
0x0f,0x4a,0x95,0x03 = orrseq r4, r5, #61440
0x06,0x40,0x85,0x11 = orrne r4, r5, r6
0x86,0x42,0x95,0x01 = orrseq r4, r5, r6, lsl #5
0x78,0x69,0x87,0x31 = orrlo r6, r7, r8, ror r9
0x66,0x40,0x95,0x81 = orrshi r4, r5, r6, rrx
0x0f,0x5a,0x85,0x23 = orrhs r5, r5, #61440
0x05,0x40,0x94,0x01 = orrseq r4, r4, r5
0x57,0x69,0x86,0x11 = orrne r6, r6, r7, asr r9
0x77,0x69,0x96,0xb1 = orrslt r6, r6, r7, ror r9
0x65,0x40,0x94,0xc1 = orrsgt r4, r4, r5, rrx
0x22,0x30,0x81,0xe1 = orr r3, r1, r2, lsr #32
0x42,0x30,0x81,0xe1 = orr r3, r1, r2, asr #32
0x13,0x20,0x82,0xe6 = pkhbt r2, r2, r3
0x93,0x2f,0x82,0xe6 = pkhbt r2, r2, r3, lsl #31
0x13,0x20,0x82,0xe6 = pkhbt r2, r2, r3
0x93,0x27,0x82,0xe6 = pkhbt r2, r2, r3, lsl #15
0x13,0x20,0x82,0xe6 = pkhbt r2, r2, r3
0xd3,0x2f,0x82,0xe6 = pkhtb r2, r2, r3, asr #31
0xd3,0x27,0x82,0xe6 = pkhtb r2, r2, r3, asr #15
0x04,0x70,0x9d,0xe4 = pop {r7}
0x80,0x07,0xbd,0xe8 = pop {r7, r8, r9, r10}
0x04,0x70,0x2d,0xe5 = push {r7}
0x80,0x07,0x2d,0xe9 = push {r7, r8, r9, r10}
0x52,0x10,0x03,0xe1 = qadd r1, r2, r3
0x52,0x10,0x03,0x11 = qaddne r1, r2, r3
0x13,0x1f,0x22,0xe6 = qadd16 r1, r2, r3
0x13,0x1f,0x22,0xc6 = qadd16gt r1, r2, r3
0x93,0x1f,0x22,0xe6 = qadd8 r1, r2, r3
0x93,0x1f,0x22,0xd6 = qadd8le r1, r2, r3
0x57,0x60,0x48,0xe1 = qdadd r6, r7, r8
0x57,0x60,0x48,0x81 = qdaddhi r6, r7, r8
0x57,0x60,0x68,0xe1 = qdsub r6, r7, r8
0x57,0x60,0x68,0x81 = qdsubhi r6, r7, r8
0x50,0x9f,0x2c,0xe6 = qsax r9, r12, r0
0x50,0x9f,0x2c,0x06 = qsaxeq r9, r12, r0
0x52,0x10,0x23,0xe1 = qsub r1, r2, r3
0x52,0x10,0x23,0x11 = qsubne r1, r2, r3
0x73,0x1f,0x22,0xe6 = qsub16 r1, r2, r3
0x73,0x1f,0x22,0xc6 = qsub16gt r1, r2, r3
0xf3,0x1f,0x22,0xe6 = qsub8 r1, r2, r3
0xf3,0x1f,0x22,0xd6 = qsub8le r1, r2, r3
0x32,0x1f,0xff,0xe6 = rbit r1, r2
0x32,0x1f,0xff,0x16 = rbitne r1, r2
0x39,0x1f,0xbf,0xe6 = rev r1, r9
0x35,0x1f,0xbf,0x16 = revne r1, r5
0xb3,0x8f,0xbf,0xe6 = rev16 r8, r3
0xb4,0xcf,0xbf,0x16 = rev16ne r12, r4
0xb9,0x4f,0xff,0xe6 = revsh r4, r9
0xb1,0x9f,0xff,0x16 = revshne r9, r1
0x00,0x0a,0x12,0xf8 = rfeda r2
0x00,0x0a,0x13,0xf9 = rfedb r3
0x00,0x0a,0x95,0xf8 = rfeia r5
0x00,0x0a,0x96,0xf9 = rfeib r6
0x00,0x0a,0x34,0xf8 = rfeda r4!
0x00,0x0a,0x37,0xf9 = rfedb r7!
0x00,0x0a,0xb9,0xf8 = rfeia r9!
0x00,0x0a,0xb8,0xf9 = rfeib r8!
0x00,0x0a,0x12,0xf8 = rfeda r2
0x00,0x0a,0x13,0xf9 = rfedb r3
0x00,0x0a,0x95,0xf8 = rfeia r5
0x00,0x0a,0x96,0xf9 = rfeib r6
0x00,0x0a,0x34,0xf8 = rfeda r4!
0x00,0x0a,0x37,0xf9 = rfedb r7!
0x00,0x0a,0xb9,0xf8 = rfeia r9!
0x00,0x0a,0xb8,0xf9 = rfeib r8!
0x00,0x0a,0x91,0xf8 = rfeia r1
0x00,0x0a,0xb1,0xf8 = rfeia r1!
0xe4,0x2f,0xa0,0xe1 = ror r2, r4, #31
0xe4,0x20,0xa0,0xe1 = ror r2, r4, #1
0x04,0x20,0xa0,0xe1 = mov r2, r4
0xe4,0x40,0xa0,0xe1 = ror r4, r4, #1
0x0f,0x4a,0x65,0xe2 = rsb r4, r5, #61440
0x06,0x40,0x65,0xe0 = rsb r4, r5, r6
0x86,0x42,0x65,0xe0 = rsb r4, r5, r6, lsl #5
0xa6,0x42,0x65,0x30 = rsblo r4, r5, r6, lsr #5
0xa6,0x42,0x65,0xe0 = rsb r4, r5, r6, lsr #5
0xc6,0x42,0x65,0xe0 = rsb r4, r5, r6, asr #5
0xe6,0x42,0x65,0xe0 = rsb r4, r5, r6, ror #5
0x18,0x69,0x67,0xe0 = rsb r6, r7, r8, lsl r9
0x38,0x69,0x67,0xe0 = rsb r6, r7, r8, lsr r9
0x58,0x69,0x67,0xe0 = rsb r6, r7, r8, asr r9
0x78,0x69,0x67,0xd0 = rsble r6, r7, r8, ror r9
0x66,0x40,0x65,0xe0 = rsb r4, r5, r6, rrx
0x0f,0x5a,0x65,0xe2 = rsb r5, r5, #61440
0x05,0x40,0x64,0xe0 = rsb r4, r4, r5
0x85,0x42,0x64,0xe0 = rsb r4, r4, r5, lsl #5
0xa5,0x42,0x64,0xe0 = rsb r4, r4, r5, lsr #5
0xa5,0x42,0x64,0x10 = rsbne r4, r4, r5, lsr #5
0xc5,0x42,0x64,0xe0 = rsb r4, r4, r5, asr #5
0xe5,0x42,0x64,0xe0 = rsb r4, r4, r5, ror #5
0x17,0x69,0x66,0xc0 = rsbgt r6, r6, r7, lsl r9
0x37,0x69,0x66,0xe0 = rsb r6, r6, r7, lsr r9
0x57,0x69,0x66,0xe0 = rsb r6, r6, r7, asr r9
0x77,0x69,0x66,0xe0 = rsb r6, r6, r7, ror r9
0x65,0x40,0x64,0xe0 = rsb r4, r4, r5, rrx
0x0f,0x4a,0xe5,0xe2 = rsc r4, r5, #61440
0x06,0x40,0xe5,0xe0 = rsc r4, r5, r6
0x86,0x42,0xe5,0xe0 = rsc r4, r5, r6, lsl #5
0xa6,0x42,0xe5,0x30 = rsclo r4, r5, r6, lsr #5
0xa6,0x42,0xe5,0xe0 = rsc r4, r5, r6, lsr #5
0xc6,0x42,0xe5,0xe0 = rsc r4, r5, r6, asr #5
0xe6,0x42,0xe5,0xe0 = rsc r4, r5, r6, ror #5
0x18,0x69,0xe7,0xe0 = rsc r6, r7, r8, lsl r9
0x38,0x69,0xe7,0xe0 = rsc r6, r7, r8, lsr r9
0x58,0x69,0xe7,0xe0 = rsc r6, r7, r8, asr r9
0x78,0x69,0xe7,0xd0 = rscle r6, r7, r8, ror r9
0xfe,0x1e,0xf8,0xe2 = rscs r1, r8, #4064
0x0f,0x5a,0xe5,0xe2 = rsc r5, r5, #61440
0x05,0x40,0xe4,0xe0 = rsc r4, r4, r5
0x85,0x42,0xe4,0xe0 = rsc r4, r4, r5, lsl #5
0xa5,0x42,0xe4,0xe0 = rsc r4, r4, r5, lsr #5
0xa5,0x42,0xe4,0x10 = rscne r4, r4, r5, lsr #5
0xc5,0x42,0xe4,0xe0 = rsc r4, r4, r5, asr #5
0xe5,0x42,0xe4,0xe0 = rsc r4, r4, r5, ror #5
0x17,0x69,0xe6,0xc0 = rscgt r6, r6, r7, lsl r9
0x37,0x69,0xe6,0xe0 = rsc r6, r6, r7, lsr r9
0x57,0x69,0xe6,0xe0 = rsc r6, r6, r7, asr r9
0x77,0x69,0xe6,0xe0 = rsc r6, r6, r7, ror r9
0x61,0x00,0xa0,0xe1 = rrx r0, r1
0x6f,0xd0,0xa0,0xe1 = rrx sp, pc
0x6e,0xf0,0xa0,0xe1 = rrx pc, lr
0x6d,0xe0,0xa0,0xe1 = rrx lr, sp
0x61,0x00,0xb0,0xe1 = rrxs r0, r1
0x6f,0xd0,0xb0,0xe1 = rrxs sp, pc
0x6e,0xf0,0xb0,0xe1 = rrxs pc, lr
0x6d,0xe0,0xb0,0xe1 = rrxs lr, sp
0x13,0x1f,0x12,0xe6 = sadd16 r1, r2, r3
0x13,0x1f,0x12,0xc6 = sadd16gt r1, r2, r3
0x93,0x1f,0x12,0xe6 = sadd8 r1, r2, r3
0x93,0x1f,0x12,0xd6 = sadd8le r1, r2, r3
0x30,0x9f,0x1c,0xe6 = sasx r9, r12, r0
0x30,0x9f,0x1c,0x06 = sasxeq r9, r12, r0
0x0f,0x4a,0xc5,0xe2 = sbc r4, r5, #61440
0x06,0x40,0xc5,0xe0 = sbc r4, r5, r6
0x86,0x42,0xc5,0xe0 = sbc r4, r5, r6, lsl #5
0xa6,0x42,0xc5,0xe0 = sbc r4, r5, r6, lsr #5
0xa6,0x42,0xc5,0xe0 = sbc r4, r5, r6, lsr #5
0xc6,0x42,0xc5,0xe0 = sbc r4, r5, r6, asr #5
0xe6,0x42,0xc5,0xe0 = sbc r4, r5, r6, ror #5
0x18,0x69,0xc7,0xe0 = sbc r6, r7, r8, lsl r9
0x38,0x69,0xc7,0xe0 = sbc r6, r7, r8, lsr r9
0x58,0x69,0xc7,0xe0 = sbc r6, r7, r8, asr r9
0x78,0x69,0xc7,0xe0 = sbc r6, r7, r8, ror r9
0x0f,0x5a,0xc5,0xe2 = sbc r5, r5, #61440
0x05,0x40,0xc4,0xe0 = sbc r4, r4, r5
0x85,0x42,0xc4,0xe0 = sbc r4, r4, r5, lsl #5
0xa5,0x42,0xc4,0xe0 = sbc r4, r4, r5, lsr #5
0xa5,0x42,0xc4,0xe0 = sbc r4, r4, r5, lsr #5
0xc5,0x42,0xc4,0xe0 = sbc r4, r4, r5, asr #5
0xe5,0x42,0xc4,0xe0 = sbc r4, r4, r5, ror #5
0x17,0x69,0xc6,0xe0 = sbc r6, r6, r7, lsl r9
0x37,0x69,0xc6,0xe0 = sbc r6, r6, r7, lsr r9
0x57,0x69,0xc6,0xe0 = sbc r6, r6, r7, asr r9
0x77,0x69,0xc6,0xe0 = sbc r6, r6, r7, ror r9
0x55,0x48,0xa0,0xe7 = sbfx r4, r5, #16, #1
0x55,0x48,0xaf,0xc7 = sbfxgt r4, r5, #16, #16
0xb1,0x9f,0x82,0xe6 = sel r9, r2, r1
0xb1,0x9f,0x82,0x16 = selne r9, r2, r1
0x00,0x02,0x01,0xf1 = setend be
0x00,0x02,0x01,0xf1 = setend be
0x00,0x00,0x01,0xf1 = setend le
0x00,0x00,0x01,0xf1 = setend le
0x04,0xf0,0x20,0xe3 = sev 
0x04,0xf0,0x20,0x03 = seveq 
0x12,0x4f,0x38,0xe6 = shadd16 r4, r8, r2
0x12,0x4f,0x38,0xc6 = shadd16gt r4, r8, r2
0x92,0x4f,0x38,0xe6 = shadd8 r4, r8, r2
0x92,0x4f,0x38,0xc6 = shadd8gt r4, r8, r2
0x32,0x4f,0x38,0xe6 = shasx r4, r8, r2
0x32,0x4f,0x38,0xc6 = shasxgt r4, r8, r2
0x72,0x4f,0x38,0xe6 = shsub16 r4, r8, r2
0x72,0x4f,0x38,0xc6 = shsub16gt r4, r8, r2
0xf2,0x4f,0x38,0xe6 = shsub8 r4, r8, r2
0xf2,0x4f,0x38,0xc6 = shsub8gt r4, r8, r2
0x81,0x09,0x03,0xe1 = smlabb r3, r1, r9, r0
0xc6,0x14,0x05,0xe1 = smlabt r5, r6, r4, r1
0xa2,0x23,0x04,0xe1 = smlatb r4, r2, r3, r2
0xe3,0x48,0x08,0xe1 = smlatt r8, r3, r8, r4
0x81,0x09,0x03,0xa1 = smlabbge r3, r1, r9, r0
0xc6,0x14,0x05,0xd1 = smlabtle r5, r6, r4, r1
0xa2,0x23,0x04,0x11 = smlatbne r4, r2, r3, r2
0xe3,0x48,0x08,0x01 = smlatteq r8, r3, r8, r4
0x13,0x85,0x02,0xe7 = smlad r2, r3, r5, r8
0x33,0x85,0x02,0xe7 = smladx r2, r3, r5, r8
0x13,0x85,0x02,0x07 = smladeq r2, r3, r5, r8
0x33,0x85,0x02,0x87 = smladxhi r2, r3, r5, r8
0x95,0x28,0xe3,0xe0 = smlal r2, r3, r5, r8
0x95,0x28,0xf3,0xe0 = smlals r2, r3, r5, r8
0x95,0x28,0xe3,0x00 = smlaleq r2, r3, r5, r8
0x95,0x28,0xf3,0x80 = smlalshi r2, r3, r5, r8
0x89,0x30,0x41,0xe1 = smlalbb r3, r1, r9, r0
0xc4,0x51,0x46,0xe1 = smlalbt r5, r6, r4, r1
0xa3,0x42,0x42,0xe1 = smlaltb r4, r2, r3, r2
0xe8,0x84,0x43,0xe1 = smlaltt r8, r3, r8, r4
0x89,0x30,0x41,0xa1 = smlalbbge r3, r1, r9, r0
0xc4,0x51,0x46,0xd1 = smlalbtle r5, r6, r4, r1
0xa3,0x42,0x42,0x11 = smlaltbne r4, r2, r3, r2
0xe8,0x84,0x43,0x01 = smlaltteq r8, r3, r8, r4
0x15,0x28,0x43,0xe7 = smlald r2, r3, r5, r8
0x35,0x28,0x43,0xe7 = smlaldx r2, r3, r5, r8
0x15,0x28,0x43,0x07 = smlaldeq r2, r3, r5, r8
0x35,0x28,0x43,0x87 = smlaldxhi r2, r3, r5, r8
0x83,0x8a,0x22,0xe1 = smlawb r2, r3, r10, r8
0xc3,0x95,0x28,0xe1 = smlawt r8, r3, r5, r9
0x87,0x85,0x22,0x01 = smlawbeq r2, r7, r5, r8
0xc3,0x80,0x21,0x81 = smlawthi r1, r3, r0, r8
0x53,0x85,0x02,0xe7 = smlsd r2, r3, r5, r8
0x73,0x85,0x02,0xe7 = smlsdx r2, r3, r5, r8
0x53,0x85,0x02,0x07 = smlsdeq r2, r3, r5, r8
0x73,0x85,0x02,0x87 = smlsdxhi r2, r3, r5, r8
0x55,0x21,0x49,0xe7 = smlsld r2, r9, r5, r1
0x72,0x48,0x4b,0xe7 = smlsldx r4, r11, r2, r8
0x55,0x86,0x42,0x07 = smlsldeq r8, r2, r5, r6
0x73,0x18,0x40,0x87 = smlsldxhi r1, r0, r3, r8
0x12,0x43,0x51,0xe7 = smmla r1, r2, r3, r4
0x33,0x12,0x54,0xe7 = smmlar r4, r3, r2, r1
0x12,0x43,0x51,0x37 = smmlalo r1, r2, r3, r4
0x33,0x12,0x54,0x27 = smmlarhs r4, r3, r2, r1
0xd2,0x43,0x51,0xe7 = smmls r1, r2, r3, r4
0xf3,0x12,0x54,0xe7 = smmlsr r4, r3, r2, r1
0xd2,0x43,0x51,0x37 = smmlslo r1, r2, r3, r4
0xf3,0x12,0x54,0x27 = smmlsrhs r4, r3, r2, r1
0x13,0xf4,0x52,0xe7 = smmul r2, r3, r4
0x32,0xf1,0x53,0xe7 = smmulr r3, r2, r1
0x13,0xf4,0x52,0x37 = smmullo r2, r3, r4
0x32,0xf1,0x53,0x27 = smmulrhs r3, r2, r1
0x13,0xf4,0x02,0xe7 = smuad r2, r3, r4
0x32,0xf1,0x03,0xe7 = smuadx r3, r2, r1
0x13,0xf4,0x02,0xb7 = smuadlt r2, r3, r4
0x32,0xf1,0x03,0xa7 = smuadxge r3, r2, r1
0x89,0x00,0x63,0xe1 = smulbb r3, r9, r0
0xc4,0x01,0x65,0xe1 = smulbt r5, r4, r1
0xa2,0x02,0x64,0xe1 = smultb r4, r2, r2
0xe3,0x04,0x68,0xe1 = smultt r8, r3, r4
0x89,0x00,0x61,0xa1 = smulbbge r1, r9, r0
0xc6,0x04,0x65,0xd1 = smulbtle r5, r6, r4
0xa3,0x02,0x62,0x11 = smultbne r2, r3, r2
0xe3,0x04,0x68,0x01 = smultteq r8, r3, r4
0x90,0x31,0xc9,0xe0 = smull r3, r9, r0, r1
0x90,0x32,0xd9,0xe0 = smulls r3, r9, r0, r2
0x94,0x85,0xc3,0x00 = smulleq r8, r3, r4, r5
0x94,0x83,0xd3,0x00 = smullseq r8, r3, r4, r3
0xa9,0x00,0x23,0xe1 = smulwb r3, r9, r0
0xe9,0x02,0x23,0xe1 = smulwt r3, r9, r2
0x50,0xf1,0x03,0xe7 = smusd r3, r0, r1
0x79,0xf2,0x03,0xe7 = smusdx r3, r9, r2
0x53,0xf2,0x08,0x07 = smusdeq r8, r3, r2
0x74,0xf3,0x07,0x17 = smusdxne r7, r4, r3
0x05,0x05,0x4d,0xf8 = srsda sp, #5
0x01,0x05,0x4d,0xf9 = srsdb sp, #1
0x00,0x05,0xcd,0xf8 = srsia sp, #0
0x0f,0x05,0xcd,0xf9 = srsib sp, #15
0x1f,0x05,0x6d,0xf8 = srsda sp!, #31
0x13,0x05,0x6d,0xf9 = srsdb sp!, #19
0x02,0x05,0xed,0xf8 = srsia sp!, #2
0x0e,0x05,0xed,0xf9 = srsib sp!, #14
0x0b,0x05,0xcd,0xf9 = srsib sp, #11
0x0a,0x05,0xcd,0xf8 = srsia sp, #10
0x09,0x05,0x4d,0xf9 = srsdb sp, #9
0x05,0x05,0x4d,0xf8 = srsda sp, #5
0x05,0x05,0xed,0xf9 = srsib sp!, #5
0x05,0x05,0xed,0xf8 = srsia sp!, #5
0x05,0x05,0x6d,0xf9 = srsdb sp!, #5
0x05,0x05,0x6d,0xf8 = srsda sp!, #5
0x05,0x05,0xcd,0xf8 = srsia sp, #5
0x05,0x05,0xed,0xf8 = srsia sp!, #5
0x05,0x05,0x4d,0xf8 = srsda sp, #5
0x01,0x05,0x4d,0xf9 = srsdb sp, #1
0x00,0x05,0xcd,0xf8 = srsia sp, #0
0x0f,0x05,0xcd,0xf9 = srsib sp, #15
0x1f,0x05,0x6d,0xf8 = srsda sp!, #31
0x13,0x05,0x6d,0xf9 = srsdb sp!, #19
0x02,0x05,0xed,0xf8 = srsia sp!, #2
0x0e,0x05,0xed,0xf9 = srsib sp!, #14
0x0b,0x05,0xcd,0xf9 = srsib sp, #11
0x0a,0x05,0xcd,0xf8 = srsia sp, #10
0x09,0x05,0x4d,0xf9 = srsdb sp, #9
0x05,0x05,0x4d,0xf8 = srsda sp, #5
0x05,0x05,0xed,0xf9 = srsib sp!, #5
0x05,0x05,0xed,0xf8 = srsia sp!, #5
0x05,0x05,0x6d,0xf9 = srsdb sp!, #5
0x05,0x05,0x6d,0xf8 = srsda sp!, #5
0x05,0x05,0xcd,0xf8 = srsia sp, #5
0x05,0x05,0xed,0xf8 = srsia sp!, #5
0x1a,0x80,0xa0,0xe6 = ssat r8, #1, r10
0x1a,0x80,0xa0,0xe6 = ssat r8, #1, r10
0x9a,0x8f,0xa0,0xe6 = ssat r8, #1, r10, lsl #31
0x5a,0x80,0xa0,0xe6 = ssat r8, #1, r10, asr #32
0xda,0x80,0xa0,0xe6 = ssat r8, #1, r10, asr #1
0x37,0x2f,0xa0,0xe6 = ssat16 r2, #1, r7
0x35,0x3f,0xaf,0xe6 = ssat16 r3, #16, r5
0x54,0x2f,0x13,0xe6 = ssax r2, r3, r4
0x54,0x2f,0x13,0xb6 = ssaxlt r2, r3, r4
0x76,0x1f,0x10,0xe6 = ssub16 r1, r0, r6
0x72,0x5f,0x13,0x16 = ssub16ne r5, r3, r2
0xf4,0x9f,0x12,0xe6 = ssub8 r9, r2, r4
0xf2,0x5f,0x11,0x06 = ssub8eq r5, r1, r2
0x01,0x80,0x81,0xfd = stc2 p0, c8, [r1, #4]
0x00,0x71,0x82,0xfd = stc2 p1, c7, [r2]
0x38,0x62,0x03,0xfd = stc2 p2, c6, [r3, #-224]
0x1e,0x53,0x24,0xfd = stc2 p3, c5, [r4, #-120]!
0x04,0x44,0xa5,0xfc = stc2 p4, c4, [r5], #16
0x12,0x35,0x26,0xfc = stc2 p5, c3, [r6], #-72
0x01,0x26,0xc7,0xfd = stc2l p6, c2, [r7, #4]
0x00,0x17,0xc8,0xfd = stc2l p7, c1, [r8]
0x38,0x08,0x49,0xfd = stc2l p8, c0, [r9, #-224]
0x1e,0x19,0x6a,0xfd = stc2l p9, c1, [r10, #-120]!
0x04,0x20,0xeb,0xfc = stc2l p0, c2, [r11], #16
0x12,0x31,0x6c,0xfc = stc2l p1, c3, [r12], #-72
0x01,0x4c,0x80,0xed = stc p12, c4, [r0, #4]
0x00,0x5d,0x81,0xed = stc p13, c5, [r1]
0x38,0x6e,0x02,0xed = stc p14, c6, [r2, #-224]
0x1e,0x7f,0x23,0xed = stc p15, c7, [r3, #-120]!
0x04,0x85,0xa4,0xec = stc p5, c8, [r4], #16
0x12,0x94,0x25,0xec = stc p4, c9, [r5], #-72
0x01,0xa3,0xc6,0xed = stcl p3, c10, [r6, #4]
0x00,0xb2,0xc7,0xed = stcl p2, c11, [r7]
0x38,0xc1,0x48,0xed = stcl p1, c12, [r8, #-224]
0x1e,0xd0,0x69,0xed = stcl p0, c13, [r9, #-120]!
0x04,0xe6,0xea,0xec = stcl p6, c14, [r10], #16
0x12,0xf7,0x6b,0xec = stcl p7, c15, [r11], #-72
0x01,0x4c,0x80,0x3d = stclo p12, c4, [r0, #4]
0x00,0x5d,0x81,0x8d = stchi p13, c5, [r1]
0x38,0x6e,0x02,0x2d = stchs p14, c6, [r2, #-224]
0x1e,0x7f,0x23,0x3d = stclo p15, c7, [r3, #-120]!
0x04,0x85,0xa4,0x0c = stceq p5, c8, [r4], #16
0x12,0x94,0x25,0xcc = stcgt p4, c9, [r5], #-72
0x01,0xa3,0xc6,0xbd = stcllt p3, c10, [r6, #4]
0x00,0xb2,0xc7,0xad = stclge p2, c11, [r7]
0x38,0xc1,0x48,0xdd = stclle p1, c12, [r8, #-224]
0x1e,0xd0,0x69,0x1d = stclne p0, c13, [r9, #-120]!
0x04,0xe6,0xea,0x0c = stcleq p6, c14, [r10], #16
0x12,0xf7,0x6b,0x8c = stclhi p7, c15, [r11], #-72
0x19,0x82,0x81,0xfc = stc2 p2, c8, [r1], {25}
0x7a,0x20,0x82,0xe8 = stm r2, {r1, r3, r4, r5, r6, sp}
0x7a,0x40,0x83,0xe8 = stm r3, {r1, r3, r4, r5, r6, lr}
0x7a,0x20,0x84,0xe9 = stmib r4, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0x05,0xe8 = stmda r5, {r1, r3, r4, r5, r6, sp}
0x7a,0x01,0x06,0xe9 = stmdb r6, {r1, r3, r4, r5, r6, r8}
0x7a,0x20,0x0d,0xe9 = stmdb sp, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0xa8,0xe8 = stm r8!, {r1, r3, r4, r5, r6, sp}
0x7a,0x20,0xa9,0xe9 = stmib r9!, {r1, r3, r4, r5, r6, sp}
0x7a,0x00,0x2d,0xe8 = stmda sp!, {r1, r3, r4, r5, r6}
0xa2,0x20,0x20,0xe9 = stmdb r0!, {r1, r5, r7, sp}
0x93,0x1f,0xc4,0xe1 = strexb r1, r3, [r4]
0x92,0x4f,0xe5,0xe1 = strexh r4, r2, [r5]
0x91,0x2f,0x87,0xe1 = strex r2, r1, [r7]
0x92,0x6f,0xa8,0xe1 = strexd r6, r2, r3, [r8]
0x00,0x30,0x2a,0x55 = strpl r3, [r10, #-0]!
0x00,0x30,0xaa,0x55 = strpl r3, [r10, #0]!
0x0f,0x4a,0x45,0xe2 = sub r4, r5, #61440
0x06,0x40,0x45,0xe0 = sub r4, r5, r6
0x86,0x42,0x45,0xe0 = sub r4, r5, r6, lsl #5
0xa6,0x42,0x45,0xe0 = sub r4, r5, r6, lsr #5
0xa6,0x42,0x45,0xe0 = sub r4, r5, r6, lsr #5
0xc6,0x42,0x45,0xe0 = sub r4, r5, r6, asr #5
0xe6,0x42,0x45,0xe0 = sub r4, r5, r6, ror #5
0x18,0x69,0x47,0xe0 = sub r6, r7, r8, lsl r9
0x38,0x69,0x47,0xe0 = sub r6, r7, r8, lsr r9
0x58,0x69,0x47,0xe0 = sub r6, r7, r8, asr r9
0x78,0x69,0x47,0xe0 = sub r6, r7, r8, ror r9
0x0f,0x5a,0x45,0xe2 = sub r5, r5, #61440
0x05,0x40,0x44,0xe0 = sub r4, r4, r5
0x85,0x42,0x44,0xe0 = sub r4, r4, r5, lsl #5
0xa5,0x42,0x44,0xe0 = sub r4, r4, r5, lsr #5
0xa5,0x42,0x44,0xe0 = sub r4, r4, r5, lsr #5
0xc5,0x42,0x44,0xe0 = sub r4, r4, r5, asr #5
0xe5,0x42,0x44,0xe0 = sub r4, r4, r5, ror #5
0x17,0x69,0x46,0xe0 = sub r6, r6, r7, lsl r9
0x37,0x69,0x46,0xe0 = sub r6, r6, r7, lsr r9
0x57,0x69,0x46,0xe0 = sub r6, r6, r7, asr r9
0x77,0x69,0x46,0xe0 = sub r6, r6, r7, ror r9
0x22,0x30,0x41,0xe0 = sub r3, r1, r2, lsr #32
0x42,0x30,0x41,0xe0 = sub r3, r1, r2, asr #32
0x10,0x00,0x00,0xef = svc #16
0x00,0x00,0x00,0xef = svc #0
0xff,0xff,0xff,0xef = svc #16777215
0x92,0x10,0x03,0xe1 = swp r1, r2, [r3]
0x94,0x40,0x06,0xe1 = swp r4, r4, [r6]
0x91,0x50,0x49,0xe1 = swpb r5, r1, [r9]
0x74,0x20,0xa3,0xe6 = sxtab r2, r3, r4
0x76,0x40,0xa5,0xe6 = sxtab r4, r5, r6
0x79,0x64,0xa2,0xb6 = sxtablt r6, r2, r9, ror #8
0x74,0x58,0xa1,0xe6 = sxtab r5, r1, r4, ror #16
0x73,0x7c,0xa8,0xe6 = sxtab r7, r8, r3, ror #24
0x74,0x00,0x81,0xa6 = sxtab16ge r0, r1, r4
0x77,0x60,0x82,0xe6 = sxtab16 r6, r2, r7
0x78,0x34,0x85,0xe6 = sxtab16 r3, r5, r8, ror #8
0x71,0x38,0x82,0xe6 = sxtab16 r3, r2, r1, ror #16
0x73,0x1c,0x82,0x06 = sxtab16eq r1, r2, r3, ror #24
0x79,0x10,0xb3,0xe6 = sxtah r1, r3, r9
0x76,0x60,0xb1,0x86 = sxtahhi r6, r1, r6
0x73,0x34,0xb8,0xe6 = sxtah r3, r8, r3, ror #8
0x74,0x28,0xb2,0x36 = sxtahlo r2, r2, r4, ror #16
0x73,0x9c,0xb3,0xe6 = sxtah r9, r3, r3, ror #24
0x74,0x20,0xaf,0xa6 = sxtbge r2, r4
0x76,0x50,0xaf,0xe6 = sxtb r5, r6
0x79,0x64,0xaf,0xe6 = sxtb r6, r9, ror #8
0x71,0x58,0xaf,0x36 = sxtblo r5, r1, ror #16
0x73,0x8c,0xaf,0xe6 = sxtb r8, r3, ror #24
0x74,0x10,0x8f,0xe6 = sxtb16 r1, r4
0x77,0x60,0x8f,0xe6 = sxtb16 r6, r7
0x75,0x34,0x8f,0x26 = sxtb16hs r3, r5, ror #8
0x71,0x38,0x8f,0xe6 = sxtb16 r3, r1, ror #16
0x73,0x2c,0x8f,0xa6 = sxtb16ge r2, r3, ror #24
0x79,0x30,0xbf,0x16 = sxthne r3, r9
0x76,0x10,0xbf,0xe6 = sxth r1, r6
0x78,0x34,0xbf,0xe6 = sxth r3, r8, ror #8
0x72,0x28,0xbf,0xd6 = sxthle r2, r2, ror #16
0x73,0x9c,0xbf,0xe6 = sxth r9, r3, ror #24
0x0f,0x0a,0x35,0xe3 = teq r5, #61440
0x05,0x00,0x34,0xe1 = teq r4, r5
0x85,0x02,0x34,0xe1 = teq r4, r5, lsl #5
0xa5,0x02,0x34,0xe1 = teq r4, r5, lsr #5
0xa5,0x02,0x34,0xe1 = teq r4, r5, lsr #5
0xc5,0x02,0x34,0xe1 = teq r4, r5, asr #5
0xe5,0x02,0x34,0xe1 = teq r4, r5, ror #5
0x17,0x09,0x36,0xe1 = teq r6, r7, lsl r9
0x37,0x09,0x36,0xe1 = teq r6, r7, lsr r9
0x57,0x09,0x36,0xe1 = teq r6, r7, asr r9
0x77,0x09,0x36,0xe1 = teq r6, r7, ror r9
0x0f,0x0a,0x15,0xe3 = tst r5, #61440
0x05,0x00,0x14,0xe1 = tst r4, r5
0x85,0x02,0x14,0xe1 = tst r4, r5, lsl #5
0xa5,0x02,0x14,0xe1 = tst r4, r5, lsr #5
0xa5,0x02,0x14,0xe1 = tst r4, r5, lsr #5
0xc5,0x02,0x14,0xe1 = tst r4, r5, asr #5
0xe5,0x02,0x14,0xe1 = tst r4, r5, ror #5
0x17,0x09,0x16,0xe1 = tst r6, r7, lsl r9
0x37,0x09,0x16,0xe1 = tst r6, r7, lsr r9
0x57,0x09,0x16,0xe1 = tst r6, r7, asr r9
0x77,0x09,0x16,0xe1 = tst r6, r7, ror r9
0x13,0x1f,0x52,0xe6 = uadd16 r1, r2, r3
0x13,0x1f,0x52,0xc6 = uadd16gt r1, r2, r3
0x93,0x1f,0x52,0xe6 = uadd8 r1, r2, r3
0x93,0x1f,0x52,0xd6 = uadd8le r1, r2, r3
0x30,0x9f,0x5c,0xe6 = uasx r9, r12, r0
0x30,0x9f,0x5c,0x06 = uasxeq r9, r12, r0
0x55,0x48,0xe0,0xe7 = ubfx r4, r5, #16, #1
0x55,0x48,0xef,0xc7 = ubfxgt r4, r5, #16, #16
0x12,0x4f,0x78,0xe6 = uhadd16 r4, r8, r2
0x12,0x4f,0x78,0xc6 = uhadd16gt r4, r8, r2
0x92,0x4f,0x78,0xe6 = uhadd8 r4, r8, r2
0x92,0x4f,0x78,0xc6 = uhadd8gt r4, r8, r2
0x32,0x4f,0x78,0xe6 = uhasx r4, r8, r2
0x32,0x4f,0x78,0xc6 = uhasxgt r4, r8, r2
0x72,0x4f,0x78,0xe6 = uhsub16 r4, r8, r2
0x72,0x4f,0x78,0xc6 = uhsub16gt r4, r8, r2
0xf2,0x4f,0x78,0xe6 = uhsub8 r4, r8, r2
0xf2,0x4f,0x78,0xc6 = uhsub8gt r4, r8, r2
0x95,0x36,0x44,0xe0 = umaal r3, r4, r5, r6
0x95,0x36,0x44,0xb0 = umaallt r3, r4, r5, r6
0x96,0x28,0xa4,0xe0 = umlal r2, r4, r6, r8
0x92,0x66,0xa1,0xc0 = umlalgt r6, r1, r2, r6
0x92,0x23,0xb9,0xe0 = umlals r2, r9, r2, r3
0x91,0x32,0xb5,0x00 = umlalseq r3, r5, r1, r2
0x96,0x28,0x84,0xe0 = umull r2, r4, r6, r8
0x92,0x66,0x81,0xc0 = umullgt r6, r1, r2, r6
0x92,0x23,0x99,0xe0 = umulls r2, r9, r2, r3
0x91,0x32,0x95,0x00 = umullseq r3, r5, r1, r2
0x13,0x1f,0x62,0xe6 = uqadd16 r1, r2, r3
0x19,0x4f,0x67,0xc6 = uqadd16gt r4, r7, r9
0x98,0x3f,0x64,0xe6 = uqadd8 r3, r4, r8
0x92,0x8f,0x61,0xd6 = uqadd8le r8, r1, r2
0x31,0x2f,0x64,0xe6 = uqasx r2, r4, r1
0x39,0x5f,0x62,0x86 = uqasxhi r5, r2, r9
0x57,0x1f,0x63,0xe6 = uqsax r1, r3, r7
0x52,0x3f,0x66,0xe6 = uqsax r3, r6, r2
0x73,0x1f,0x65,0xe6 = uqsub16 r1, r5, r3
0x75,0x3f,0x62,0xc6 = uqsub16gt r3, r2, r5
0xf4,0x2f,0x61,0xe6 = uqsub8 r2, r1, r4
0xf9,0x4f,0x66,0xd6 = uqsub8le r4, r6, r9
0x11,0xf4,0x82,0xe7 = usad8 r2, r1, r4
0x16,0xf9,0x84,0xd7 = usad8le r4, r6, r9
0x15,0x73,0x81,0xe7 = usada8 r1, r5, r3, r7
0x12,0x15,0x83,0xc7 = usada8gt r3, r2, r5, r1
0x1a,0x80,0xe1,0xe6 = usat r8, #1, r10
0x1a,0x80,0xe4,0xe6 = usat r8, #4, r10
0x9a,0x8f,0xe5,0xe6 = usat r8, #5, r10, lsl #31
0x5a,0x80,0xff,0xe6 = usat r8, #31, r10, asr #32
0xda,0x80,0xf0,0xe6 = usat r8, #16, r10, asr #1
0x37,0x2f,0xe2,0xe6 = usat16 r2, #2, r7
0x35,0x3f,0xef,0xe6 = usat16 r3, #15, r5
0x54,0x2f,0x53,0xe6 = usax r2, r3, r4
0x54,0x2f,0x53,0x16 = usaxne r2, r3, r4
0x77,0x4f,0x52,0xe6 = usub16 r4, r2, r7
0x73,0x1f,0x51,0x86 = usub16hi r1, r1, r3
0xf5,0x1f,0x58,0xe6 = usub8 r1, r8, r5
0xf3,0x9f,0x52,0xd6 = usub8le r9, r2, r3
0x74,0x20,0xe3,0xe6 = uxtab r2, r3, r4
0x76,0x40,0xe5,0xe6 = uxtab r4, r5, r6
0x79,0x64,0xe2,0xb6 = uxtablt r6, r2, r9, ror #8
0x74,0x58,0xe1,0xe6 = uxtab r5, r1, r4, ror #16
0x73,0x7c,0xe8,0xe6 = uxtab r7, r8, r3, ror #24
0x74,0x00,0xc1,0xa6 = uxtab16ge r0, r1, r4
0x77,0x60,0xc2,0xe6 = uxtab16 r6, r2, r7
0x78,0x34,0xc5,0xe6 = uxtab16 r3, r5, r8, ror #8
0x71,0x38,0xc2,0xe6 = uxtab16 r3, r2, r1, ror #16
0x73,0x1c,0xc2,0x06 = uxtab16eq r1, r2, r3, ror #24
0x79,0x10,0xf3,0xe6 = uxtah r1, r3, r9
0x76,0x60,0xf1,0x86 = uxtahhi r6, r1, r6
0x73,0x34,0xf8,0xe6 = uxtah r3, r8, r3, ror #8
0x74,0x28,0xf2,0x36 = uxtahlo r2, r2, r4, ror #16
0x73,0x9c,0xf3,0xe6 = uxtah r9, r3, r3, ror #24
0x74,0x20,0xef,0xa6 = uxtbge r2, r4
0x76,0x50,0xef,0xe6 = uxtb r5, r6
0x79,0x64,0xef,0xe6 = uxtb r6, r9, ror #8
0x71,0x58,0xef,0x36 = uxtblo r5, r1, ror #16
0x73,0x8c,0xef,0xe6 = uxtb r8, r3, ror #24
0x74,0x10,0xcf,0xe6 = uxtb16 r1, r4
0x77,0x60,0xcf,0xe6 = uxtb16 r6, r7
0x75,0x34,0xcf,0x26 = uxtb16hs r3, r5, ror #8
0x71,0x38,0xcf,0xe6 = uxtb16 r3, r1, ror #16
0x73,0x2c,0xcf,0xa6 = uxtb16ge r2, r3, ror #24
0x79,0x30,0xff,0x16 = uxthne r3, r9
0x76,0x10,0xff,0xe6 = uxth r1, r6
0x78,0x34,0xff,0xe6 = uxth r3, r8, ror #8
0x72,0x28,0xff,0xd6 = uxthle r2, r2, ror #16
0x73,0x9c,0xff,0xe6 = uxth r9, r3, ror #24
0x02,0xf0,0x20,0xe3 = wfe 
0x02,0xf0,0x20,0x83 = wfehi 
0x03,0xf0,0x20,0xe3 = wfi 
0x03,0xf0,0x20,0xb3 = wfilt 
0x01,0xf0,0x20,0xe3 = yield 
0x01,0xf0,0x20,0x13 = yieldne 
0x04,0xf0,0x20,0xe3 = sev 
0x03,0xf0,0x20,0xe3 = wfi 
0x02,0xf0,0x20,0xe3 = wfe 
0x01,0xf0,0x20,0xe3 = yield 
0x00,0xf0,0x20,0xe3 = nop 
0xef,0xf0,0x20,0xc3 = hintgt #239
