/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    SP_PHI	= 0,
    SP_INLINEASM	= 1,
    SP_CFI_INSTRUCTION	= 2,
    SP_EH_LABEL	= 3,
    SP_GC_LABEL	= 4,
    SP_KILL	= 5,
    SP_EXTRACT_SUBREG	= 6,
    SP_INSERT_SUBREG	= 7,
    SP_IMPLICIT_DEF	= 8,
    SP_SUBREG_TO_REG	= 9,
    SP_COPY_TO_REGCLASS	= 10,
    SP_DBG_VALUE	= 11,
    SP_REG_SEQUENCE	= 12,
    SP_COPY	= 13,
    SP_BUNDLE	= 14,
    SP_LIFETIME_START	= 15,
    SP_LIFETIME_END	= 16,
    SP_STACKMAP	= 17,
    SP_PATCHPOINT	= 18,
    SP_LOAD_STACK_GUARD	= 19,
    SP_ADDCCri	= 20,
    SP_ADDCCrr	= 21,
    SP_ADDCri	= 22,
    SP_ADDCrr	= 23,
    SP_ADDEri	= 24,
    SP_ADDErr	= 25,
    SP_ADDXC	= 26,
    SP_ADDXCCC	= 27,
    SP_ADDXri	= 28,
    SP_ADDXrr	= 29,
    SP_ADDri	= 30,
    SP_ADDrr	= 31,
    SP_ADJCALLSTACKDOWN	= 32,
    SP_ADJCALLSTACKUP	= 33,
    SP_ALIGNADDR	= 34,
    SP_ALIGNADDRL	= 35,
    SP_ANDCCri	= 36,
    SP_ANDCCrr	= 37,
    SP_ANDNCCri	= 38,
    SP_ANDNCCrr	= 39,
    SP_ANDNri	= 40,
    SP_ANDNrr	= 41,
    SP_ANDXNrr	= 42,
    SP_ANDXri	= 43,
    SP_ANDXrr	= 44,
    SP_ANDri	= 45,
    SP_ANDrr	= 46,
    SP_ARRAY16	= 47,
    SP_ARRAY32	= 48,
    SP_ARRAY8	= 49,
    SP_ATOMIC_LOAD_ADD_32	= 50,
    SP_ATOMIC_LOAD_ADD_64	= 51,
    SP_ATOMIC_LOAD_AND_32	= 52,
    SP_ATOMIC_LOAD_AND_64	= 53,
    SP_ATOMIC_LOAD_MAX_32	= 54,
    SP_ATOMIC_LOAD_MAX_64	= 55,
    SP_ATOMIC_LOAD_MIN_32	= 56,
    SP_ATOMIC_LOAD_MIN_64	= 57,
    SP_ATOMIC_LOAD_NAND_32	= 58,
    SP_ATOMIC_LOAD_NAND_64	= 59,
    SP_ATOMIC_LOAD_OR_32	= 60,
    SP_ATOMIC_LOAD_OR_64	= 61,
    SP_ATOMIC_LOAD_SUB_32	= 62,
    SP_ATOMIC_LOAD_SUB_64	= 63,
    SP_ATOMIC_LOAD_UMAX_32	= 64,
    SP_ATOMIC_LOAD_UMAX_64	= 65,
    SP_ATOMIC_LOAD_UMIN_32	= 66,
    SP_ATOMIC_LOAD_UMIN_64	= 67,
    SP_ATOMIC_LOAD_XOR_32	= 68,
    SP_ATOMIC_LOAD_XOR_64	= 69,
    SP_ATOMIC_SWAP_64	= 70,
    SP_BA	= 71,
    SP_BCOND	= 72,
    SP_BCONDA	= 73,
    SP_BINDri	= 74,
    SP_BINDrr	= 75,
    SP_BMASK	= 76,
    SP_BPFCC	= 77,
    SP_BPFCCA	= 78,
    SP_BPFCCANT	= 79,
    SP_BPFCCNT	= 80,
    SP_BPGEZapn	= 81,
    SP_BPGEZapt	= 82,
    SP_BPGEZnapn	= 83,
    SP_BPGEZnapt	= 84,
    SP_BPGZapn	= 85,
    SP_BPGZapt	= 86,
    SP_BPGZnapn	= 87,
    SP_BPGZnapt	= 88,
    SP_BPICC	= 89,
    SP_BPICCA	= 90,
    SP_BPICCANT	= 91,
    SP_BPICCNT	= 92,
    SP_BPLEZapn	= 93,
    SP_BPLEZapt	= 94,
    SP_BPLEZnapn	= 95,
    SP_BPLEZnapt	= 96,
    SP_BPLZapn	= 97,
    SP_BPLZapt	= 98,
    SP_BPLZnapn	= 99,
    SP_BPLZnapt	= 100,
    SP_BPNZapn	= 101,
    SP_BPNZapt	= 102,
    SP_BPNZnapn	= 103,
    SP_BPNZnapt	= 104,
    SP_BPXCC	= 105,
    SP_BPXCCA	= 106,
    SP_BPXCCANT	= 107,
    SP_BPXCCNT	= 108,
    SP_BPZapn	= 109,
    SP_BPZapt	= 110,
    SP_BPZnapn	= 111,
    SP_BPZnapt	= 112,
    SP_BSHUFFLE	= 113,
    SP_CALL	= 114,
    SP_CALLri	= 115,
    SP_CALLrr	= 116,
    SP_CASXrr	= 117,
    SP_CASrr	= 118,
    SP_CMASK16	= 119,
    SP_CMASK32	= 120,
    SP_CMASK8	= 121,
    SP_CMPri	= 122,
    SP_CMPrr	= 123,
    SP_EDGE16	= 124,
    SP_EDGE16L	= 125,
    SP_EDGE16LN	= 126,
    SP_EDGE16N	= 127,
    SP_EDGE32	= 128,
    SP_EDGE32L	= 129,
    SP_EDGE32LN	= 130,
    SP_EDGE32N	= 131,
    SP_EDGE8	= 132,
    SP_EDGE8L	= 133,
    SP_EDGE8LN	= 134,
    SP_EDGE8N	= 135,
    SP_FABSD	= 136,
    SP_FABSQ	= 137,
    SP_FABSS	= 138,
    SP_FADDD	= 139,
    SP_FADDQ	= 140,
    SP_FADDS	= 141,
    SP_FALIGNADATA	= 142,
    SP_FAND	= 143,
    SP_FANDNOT1	= 144,
    SP_FANDNOT1S	= 145,
    SP_FANDNOT2	= 146,
    SP_FANDNOT2S	= 147,
    SP_FANDS	= 148,
    SP_FBCOND	= 149,
    SP_FBCONDA	= 150,
    SP_FCHKSM16	= 151,
    SP_FCMPD	= 152,
    SP_FCMPEQ16	= 153,
    SP_FCMPEQ32	= 154,
    SP_FCMPGT16	= 155,
    SP_FCMPGT32	= 156,
    SP_FCMPLE16	= 157,
    SP_FCMPLE32	= 158,
    SP_FCMPNE16	= 159,
    SP_FCMPNE32	= 160,
    SP_FCMPQ	= 161,
    SP_FCMPS	= 162,
    SP_FDIVD	= 163,
    SP_FDIVQ	= 164,
    SP_FDIVS	= 165,
    SP_FDMULQ	= 166,
    SP_FDTOI	= 167,
    SP_FDTOQ	= 168,
    SP_FDTOS	= 169,
    SP_FDTOX	= 170,
    SP_FEXPAND	= 171,
    SP_FHADDD	= 172,
    SP_FHADDS	= 173,
    SP_FHSUBD	= 174,
    SP_FHSUBS	= 175,
    SP_FITOD	= 176,
    SP_FITOQ	= 177,
    SP_FITOS	= 178,
    SP_FLCMPD	= 179,
    SP_FLCMPS	= 180,
    SP_FLUSHW	= 181,
    SP_FMEAN16	= 182,
    SP_FMOVD	= 183,
    SP_FMOVD_FCC	= 184,
    SP_FMOVD_ICC	= 185,
    SP_FMOVD_XCC	= 186,
    SP_FMOVQ	= 187,
    SP_FMOVQ_FCC	= 188,
    SP_FMOVQ_ICC	= 189,
    SP_FMOVQ_XCC	= 190,
    SP_FMOVRGEZD	= 191,
    SP_FMOVRGEZQ	= 192,
    SP_FMOVRGEZS	= 193,
    SP_FMOVRGZD	= 194,
    SP_FMOVRGZQ	= 195,
    SP_FMOVRGZS	= 196,
    SP_FMOVRLEZD	= 197,
    SP_FMOVRLEZQ	= 198,
    SP_FMOVRLEZS	= 199,
    SP_FMOVRLZD	= 200,
    SP_FMOVRLZQ	= 201,
    SP_FMOVRLZS	= 202,
    SP_FMOVRNZD	= 203,
    SP_FMOVRNZQ	= 204,
    SP_FMOVRNZS	= 205,
    SP_FMOVRZD	= 206,
    SP_FMOVRZQ	= 207,
    SP_FMOVRZS	= 208,
    SP_FMOVS	= 209,
    SP_FMOVS_FCC	= 210,
    SP_FMOVS_ICC	= 211,
    SP_FMOVS_XCC	= 212,
    SP_FMUL8SUX16	= 213,
    SP_FMUL8ULX16	= 214,
    SP_FMUL8X16	= 215,
    SP_FMUL8X16AL	= 216,
    SP_FMUL8X16AU	= 217,
    SP_FMULD	= 218,
    SP_FMULD8SUX16	= 219,
    SP_FMULD8ULX16	= 220,
    SP_FMULQ	= 221,
    SP_FMULS	= 222,
    SP_FNADDD	= 223,
    SP_FNADDS	= 224,
    SP_FNAND	= 225,
    SP_FNANDS	= 226,
    SP_FNEGD	= 227,
    SP_FNEGQ	= 228,
    SP_FNEGS	= 229,
    SP_FNHADDD	= 230,
    SP_FNHADDS	= 231,
    SP_FNMULD	= 232,
    SP_FNMULS	= 233,
    SP_FNOR	= 234,
    SP_FNORS	= 235,
    SP_FNOT1	= 236,
    SP_FNOT1S	= 237,
    SP_FNOT2	= 238,
    SP_FNOT2S	= 239,
    SP_FNSMULD	= 240,
    SP_FONE	= 241,
    SP_FONES	= 242,
    SP_FOR	= 243,
    SP_FORNOT1	= 244,
    SP_FORNOT1S	= 245,
    SP_FORNOT2	= 246,
    SP_FORNOT2S	= 247,
    SP_FORS	= 248,
    SP_FPACK16	= 249,
    SP_FPACK32	= 250,
    SP_FPACKFIX	= 251,
    SP_FPADD16	= 252,
    SP_FPADD16S	= 253,
    SP_FPADD32	= 254,
    SP_FPADD32S	= 255,
    SP_FPADD64	= 256,
    SP_FPMERGE	= 257,
    SP_FPSUB16	= 258,
    SP_FPSUB16S	= 259,
    SP_FPSUB32	= 260,
    SP_FPSUB32S	= 261,
    SP_FQTOD	= 262,
    SP_FQTOI	= 263,
    SP_FQTOS	= 264,
    SP_FQTOX	= 265,
    SP_FSLAS16	= 266,
    SP_FSLAS32	= 267,
    SP_FSLL16	= 268,
    SP_FSLL32	= 269,
    SP_FSMULD	= 270,
    SP_FSQRTD	= 271,
    SP_FSQRTQ	= 272,
    SP_FSQRTS	= 273,
    SP_FSRA16	= 274,
    SP_FSRA32	= 275,
    SP_FSRC1	= 276,
    SP_FSRC1S	= 277,
    SP_FSRC2	= 278,
    SP_FSRC2S	= 279,
    SP_FSRL16	= 280,
    SP_FSRL32	= 281,
    SP_FSTOD	= 282,
    SP_FSTOI	= 283,
    SP_FSTOQ	= 284,
    SP_FSTOX	= 285,
    SP_FSUBD	= 286,
    SP_FSUBQ	= 287,
    SP_FSUBS	= 288,
    SP_FXNOR	= 289,
    SP_FXNORS	= 290,
    SP_FXOR	= 291,
    SP_FXORS	= 292,
    SP_FXTOD	= 293,
    SP_FXTOQ	= 294,
    SP_FXTOS	= 295,
    SP_FZERO	= 296,
    SP_FZEROS	= 297,
    SP_GETPCX	= 298,
    SP_JMPLri	= 299,
    SP_JMPLrr	= 300,
    SP_LDDFri	= 301,
    SP_LDDFrr	= 302,
    SP_LDFri	= 303,
    SP_LDFrr	= 304,
    SP_LDQFri	= 305,
    SP_LDQFrr	= 306,
    SP_LDSBri	= 307,
    SP_LDSBrr	= 308,
    SP_LDSHri	= 309,
    SP_LDSHrr	= 310,
    SP_LDSWri	= 311,
    SP_LDSWrr	= 312,
    SP_LDUBri	= 313,
    SP_LDUBrr	= 314,
    SP_LDUHri	= 315,
    SP_LDUHrr	= 316,
    SP_LDXri	= 317,
    SP_LDXrr	= 318,
    SP_LDri	= 319,
    SP_LDrr	= 320,
    SP_LEAX_ADDri	= 321,
    SP_LEA_ADDri	= 322,
    SP_LZCNT	= 323,
    SP_MEMBARi	= 324,
    SP_MOVDTOX	= 325,
    SP_MOVFCCri	= 326,
    SP_MOVFCCrr	= 327,
    SP_MOVICCri	= 328,
    SP_MOVICCrr	= 329,
    SP_MOVRGEZri	= 330,
    SP_MOVRGEZrr	= 331,
    SP_MOVRGZri	= 332,
    SP_MOVRGZrr	= 333,
    SP_MOVRLEZri	= 334,
    SP_MOVRLEZrr	= 335,
    SP_MOVRLZri	= 336,
    SP_MOVRLZrr	= 337,
    SP_MOVRNZri	= 338,
    SP_MOVRNZrr	= 339,
    SP_MOVRRZri	= 340,
    SP_MOVRRZrr	= 341,
    SP_MOVSTOSW	= 342,
    SP_MOVSTOUW	= 343,
    SP_MOVWTOS	= 344,
    SP_MOVXCCri	= 345,
    SP_MOVXCCrr	= 346,
    SP_MOVXTOD	= 347,
    SP_MULXri	= 348,
    SP_MULXrr	= 349,
    SP_NOP	= 350,
    SP_ORCCri	= 351,
    SP_ORCCrr	= 352,
    SP_ORNCCri	= 353,
    SP_ORNCCrr	= 354,
    SP_ORNri	= 355,
    SP_ORNrr	= 356,
    SP_ORXNrr	= 357,
    SP_ORXri	= 358,
    SP_ORXrr	= 359,
    SP_ORri	= 360,
    SP_ORrr	= 361,
    SP_PDIST	= 362,
    SP_PDISTN	= 363,
    SP_POPCrr	= 364,
    SP_RDY	= 365,
    SP_RESTOREri	= 366,
    SP_RESTORErr	= 367,
    SP_RET	= 368,
    SP_RETL	= 369,
    SP_RETTri	= 370,
    SP_RETTrr	= 371,
    SP_SAVEri	= 372,
    SP_SAVErr	= 373,
    SP_SDIVCCri	= 374,
    SP_SDIVCCrr	= 375,
    SP_SDIVXri	= 376,
    SP_SDIVXrr	= 377,
    SP_SDIVri	= 378,
    SP_SDIVrr	= 379,
    SP_SELECT_CC_DFP_FCC	= 380,
    SP_SELECT_CC_DFP_ICC	= 381,
    SP_SELECT_CC_FP_FCC	= 382,
    SP_SELECT_CC_FP_ICC	= 383,
    SP_SELECT_CC_Int_FCC	= 384,
    SP_SELECT_CC_Int_ICC	= 385,
    SP_SELECT_CC_QFP_FCC	= 386,
    SP_SELECT_CC_QFP_ICC	= 387,
    SP_SETHIXi	= 388,
    SP_SETHIi	= 389,
    SP_SHUTDOWN	= 390,
    SP_SIAM	= 391,
    SP_SLLXri	= 392,
    SP_SLLXrr	= 393,
    SP_SLLri	= 394,
    SP_SLLrr	= 395,
    SP_SMULCCri	= 396,
    SP_SMULCCrr	= 397,
    SP_SMULri	= 398,
    SP_SMULrr	= 399,
    SP_SRAXri	= 400,
    SP_SRAXrr	= 401,
    SP_SRAri	= 402,
    SP_SRArr	= 403,
    SP_SRLXri	= 404,
    SP_SRLXrr	= 405,
    SP_SRLri	= 406,
    SP_SRLrr	= 407,
    SP_STBAR	= 408,
    SP_STBri	= 409,
    SP_STBrr	= 410,
    SP_STDFri	= 411,
    SP_STDFrr	= 412,
    SP_STFri	= 413,
    SP_STFrr	= 414,
    SP_STHri	= 415,
    SP_STHrr	= 416,
    SP_STQFri	= 417,
    SP_STQFrr	= 418,
    SP_STXri	= 419,
    SP_STXrr	= 420,
    SP_STri	= 421,
    SP_STrr	= 422,
    SP_SUBCCri	= 423,
    SP_SUBCCrr	= 424,
    SP_SUBCri	= 425,
    SP_SUBCrr	= 426,
    SP_SUBEri	= 427,
    SP_SUBErr	= 428,
    SP_SUBXri	= 429,
    SP_SUBXrr	= 430,
    SP_SUBri	= 431,
    SP_SUBrr	= 432,
    SP_SWAPri	= 433,
    SP_SWAPrr	= 434,
    SP_TA3	= 435,
    SP_TA5	= 436,
    SP_TADDCCTVri	= 437,
    SP_TADDCCTVrr	= 438,
    SP_TADDCCri	= 439,
    SP_TADDCCrr	= 440,
    SP_TICCri	= 441,
    SP_TICCrr	= 442,
    SP_TLS_ADDXrr	= 443,
    SP_TLS_ADDrr	= 444,
    SP_TLS_CALL	= 445,
    SP_TLS_LDXrr	= 446,
    SP_TLS_LDrr	= 447,
    SP_TSUBCCTVri	= 448,
    SP_TSUBCCTVrr	= 449,
    SP_TSUBCCri	= 450,
    SP_TSUBCCrr	= 451,
    SP_TXCCri	= 452,
    SP_TXCCrr	= 453,
    SP_UDIVCCri	= 454,
    SP_UDIVCCrr	= 455,
    SP_UDIVXri	= 456,
    SP_UDIVXrr	= 457,
    SP_UDIVri	= 458,
    SP_UDIVrr	= 459,
    SP_UMULCCri	= 460,
    SP_UMULCCrr	= 461,
    SP_UMULXHI	= 462,
    SP_UMULri	= 463,
    SP_UMULrr	= 464,
    SP_UNIMP	= 465,
    SP_V9FCMPD	= 466,
    SP_V9FCMPED	= 467,
    SP_V9FCMPEQ	= 468,
    SP_V9FCMPES	= 469,
    SP_V9FCMPQ	= 470,
    SP_V9FCMPS	= 471,
    SP_V9FMOVD_FCC	= 472,
    SP_V9FMOVQ_FCC	= 473,
    SP_V9FMOVS_FCC	= 474,
    SP_V9MOVFCCri	= 475,
    SP_V9MOVFCCrr	= 476,
    SP_WRYri	= 477,
    SP_WRYrr	= 478,
    SP_XMULX	= 479,
    SP_XMULXHI	= 480,
    SP_XNORCCri	= 481,
    SP_XNORCCrr	= 482,
    SP_XNORXrr	= 483,
    SP_XNORri	= 484,
    SP_XNORrr	= 485,
    SP_XORCCri	= 486,
    SP_XORCCrr	= 487,
    SP_XORXri	= 488,
    SP_XORXrr	= 489,
    SP_XORri	= 490,
    SP_XORrr	= 491,
    SP_INSTRUCTION_LIST_END = 492
};

#endif // GET_INSTRINFO_ENUM
