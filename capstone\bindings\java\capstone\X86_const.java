// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class X86_const {

	// X86 registers

	public static final int X86_REG_INVALID = 0;
	public static final int X86_REG_AH = 1;
	public static final int X86_REG_AL = 2;
	public static final int X86_REG_AX = 3;
	public static final int X86_REG_BH = 4;
	public static final int X86_REG_BL = 5;
	public static final int X86_REG_BP = 6;
	public static final int X86_REG_BPL = 7;
	public static final int X86_REG_BX = 8;
	public static final int X86_REG_CH = 9;
	public static final int X86_REG_CL = 10;
	public static final int X86_REG_CS = 11;
	public static final int X86_REG_CX = 12;
	public static final int X86_REG_DH = 13;
	public static final int X86_REG_DI = 14;
	public static final int X86_REG_DIL = 15;
	public static final int X86_REG_DL = 16;
	public static final int X86_REG_DS = 17;
	public static final int X86_REG_DX = 18;
	public static final int X86_REG_EAX = 19;
	public static final int X86_REG_EBP = 20;
	public static final int X86_REG_EBX = 21;
	public static final int X86_REG_ECX = 22;
	public static final int X86_REG_EDI = 23;
	public static final int X86_REG_EDX = 24;
	public static final int X86_REG_EFLAGS = 25;
	public static final int X86_REG_EIP = 26;
	public static final int X86_REG_EIZ = 27;
	public static final int X86_REG_ES = 28;
	public static final int X86_REG_ESI = 29;
	public static final int X86_REG_ESP = 30;
	public static final int X86_REG_FPSW = 31;
	public static final int X86_REG_FS = 32;
	public static final int X86_REG_GS = 33;
	public static final int X86_REG_IP = 34;
	public static final int X86_REG_RAX = 35;
	public static final int X86_REG_RBP = 36;
	public static final int X86_REG_RBX = 37;
	public static final int X86_REG_RCX = 38;
	public static final int X86_REG_RDI = 39;
	public static final int X86_REG_RDX = 40;
	public static final int X86_REG_RIP = 41;
	public static final int X86_REG_RIZ = 42;
	public static final int X86_REG_RSI = 43;
	public static final int X86_REG_RSP = 44;
	public static final int X86_REG_SI = 45;
	public static final int X86_REG_SIL = 46;
	public static final int X86_REG_SP = 47;
	public static final int X86_REG_SPL = 48;
	public static final int X86_REG_SS = 49;
	public static final int X86_REG_CR0 = 50;
	public static final int X86_REG_CR1 = 51;
	public static final int X86_REG_CR2 = 52;
	public static final int X86_REG_CR3 = 53;
	public static final int X86_REG_CR4 = 54;
	public static final int X86_REG_CR5 = 55;
	public static final int X86_REG_CR6 = 56;
	public static final int X86_REG_CR7 = 57;
	public static final int X86_REG_CR8 = 58;
	public static final int X86_REG_CR9 = 59;
	public static final int X86_REG_CR10 = 60;
	public static final int X86_REG_CR11 = 61;
	public static final int X86_REG_CR12 = 62;
	public static final int X86_REG_CR13 = 63;
	public static final int X86_REG_CR14 = 64;
	public static final int X86_REG_CR15 = 65;
	public static final int X86_REG_DR0 = 66;
	public static final int X86_REG_DR1 = 67;
	public static final int X86_REG_DR2 = 68;
	public static final int X86_REG_DR3 = 69;
	public static final int X86_REG_DR4 = 70;
	public static final int X86_REG_DR5 = 71;
	public static final int X86_REG_DR6 = 72;
	public static final int X86_REG_DR7 = 73;
	public static final int X86_REG_FP0 = 74;
	public static final int X86_REG_FP1 = 75;
	public static final int X86_REG_FP2 = 76;
	public static final int X86_REG_FP3 = 77;
	public static final int X86_REG_FP4 = 78;
	public static final int X86_REG_FP5 = 79;
	public static final int X86_REG_FP6 = 80;
	public static final int X86_REG_FP7 = 81;
	public static final int X86_REG_K0 = 82;
	public static final int X86_REG_K1 = 83;
	public static final int X86_REG_K2 = 84;
	public static final int X86_REG_K3 = 85;
	public static final int X86_REG_K4 = 86;
	public static final int X86_REG_K5 = 87;
	public static final int X86_REG_K6 = 88;
	public static final int X86_REG_K7 = 89;
	public static final int X86_REG_MM0 = 90;
	public static final int X86_REG_MM1 = 91;
	public static final int X86_REG_MM2 = 92;
	public static final int X86_REG_MM3 = 93;
	public static final int X86_REG_MM4 = 94;
	public static final int X86_REG_MM5 = 95;
	public static final int X86_REG_MM6 = 96;
	public static final int X86_REG_MM7 = 97;
	public static final int X86_REG_R8 = 98;
	public static final int X86_REG_R9 = 99;
	public static final int X86_REG_R10 = 100;
	public static final int X86_REG_R11 = 101;
	public static final int X86_REG_R12 = 102;
	public static final int X86_REG_R13 = 103;
	public static final int X86_REG_R14 = 104;
	public static final int X86_REG_R15 = 105;
	public static final int X86_REG_ST0 = 106;
	public static final int X86_REG_ST1 = 107;
	public static final int X86_REG_ST2 = 108;
	public static final int X86_REG_ST3 = 109;
	public static final int X86_REG_ST4 = 110;
	public static final int X86_REG_ST5 = 111;
	public static final int X86_REG_ST6 = 112;
	public static final int X86_REG_ST7 = 113;
	public static final int X86_REG_XMM0 = 114;
	public static final int X86_REG_XMM1 = 115;
	public static final int X86_REG_XMM2 = 116;
	public static final int X86_REG_XMM3 = 117;
	public static final int X86_REG_XMM4 = 118;
	public static final int X86_REG_XMM5 = 119;
	public static final int X86_REG_XMM6 = 120;
	public static final int X86_REG_XMM7 = 121;
	public static final int X86_REG_XMM8 = 122;
	public static final int X86_REG_XMM9 = 123;
	public static final int X86_REG_XMM10 = 124;
	public static final int X86_REG_XMM11 = 125;
	public static final int X86_REG_XMM12 = 126;
	public static final int X86_REG_XMM13 = 127;
	public static final int X86_REG_XMM14 = 128;
	public static final int X86_REG_XMM15 = 129;
	public static final int X86_REG_XMM16 = 130;
	public static final int X86_REG_XMM17 = 131;
	public static final int X86_REG_XMM18 = 132;
	public static final int X86_REG_XMM19 = 133;
	public static final int X86_REG_XMM20 = 134;
	public static final int X86_REG_XMM21 = 135;
	public static final int X86_REG_XMM22 = 136;
	public static final int X86_REG_XMM23 = 137;
	public static final int X86_REG_XMM24 = 138;
	public static final int X86_REG_XMM25 = 139;
	public static final int X86_REG_XMM26 = 140;
	public static final int X86_REG_XMM27 = 141;
	public static final int X86_REG_XMM28 = 142;
	public static final int X86_REG_XMM29 = 143;
	public static final int X86_REG_XMM30 = 144;
	public static final int X86_REG_XMM31 = 145;
	public static final int X86_REG_YMM0 = 146;
	public static final int X86_REG_YMM1 = 147;
	public static final int X86_REG_YMM2 = 148;
	public static final int X86_REG_YMM3 = 149;
	public static final int X86_REG_YMM4 = 150;
	public static final int X86_REG_YMM5 = 151;
	public static final int X86_REG_YMM6 = 152;
	public static final int X86_REG_YMM7 = 153;
	public static final int X86_REG_YMM8 = 154;
	public static final int X86_REG_YMM9 = 155;
	public static final int X86_REG_YMM10 = 156;
	public static final int X86_REG_YMM11 = 157;
	public static final int X86_REG_YMM12 = 158;
	public static final int X86_REG_YMM13 = 159;
	public static final int X86_REG_YMM14 = 160;
	public static final int X86_REG_YMM15 = 161;
	public static final int X86_REG_YMM16 = 162;
	public static final int X86_REG_YMM17 = 163;
	public static final int X86_REG_YMM18 = 164;
	public static final int X86_REG_YMM19 = 165;
	public static final int X86_REG_YMM20 = 166;
	public static final int X86_REG_YMM21 = 167;
	public static final int X86_REG_YMM22 = 168;
	public static final int X86_REG_YMM23 = 169;
	public static final int X86_REG_YMM24 = 170;
	public static final int X86_REG_YMM25 = 171;
	public static final int X86_REG_YMM26 = 172;
	public static final int X86_REG_YMM27 = 173;
	public static final int X86_REG_YMM28 = 174;
	public static final int X86_REG_YMM29 = 175;
	public static final int X86_REG_YMM30 = 176;
	public static final int X86_REG_YMM31 = 177;
	public static final int X86_REG_ZMM0 = 178;
	public static final int X86_REG_ZMM1 = 179;
	public static final int X86_REG_ZMM2 = 180;
	public static final int X86_REG_ZMM3 = 181;
	public static final int X86_REG_ZMM4 = 182;
	public static final int X86_REG_ZMM5 = 183;
	public static final int X86_REG_ZMM6 = 184;
	public static final int X86_REG_ZMM7 = 185;
	public static final int X86_REG_ZMM8 = 186;
	public static final int X86_REG_ZMM9 = 187;
	public static final int X86_REG_ZMM10 = 188;
	public static final int X86_REG_ZMM11 = 189;
	public static final int X86_REG_ZMM12 = 190;
	public static final int X86_REG_ZMM13 = 191;
	public static final int X86_REG_ZMM14 = 192;
	public static final int X86_REG_ZMM15 = 193;
	public static final int X86_REG_ZMM16 = 194;
	public static final int X86_REG_ZMM17 = 195;
	public static final int X86_REG_ZMM18 = 196;
	public static final int X86_REG_ZMM19 = 197;
	public static final int X86_REG_ZMM20 = 198;
	public static final int X86_REG_ZMM21 = 199;
	public static final int X86_REG_ZMM22 = 200;
	public static final int X86_REG_ZMM23 = 201;
	public static final int X86_REG_ZMM24 = 202;
	public static final int X86_REG_ZMM25 = 203;
	public static final int X86_REG_ZMM26 = 204;
	public static final int X86_REG_ZMM27 = 205;
	public static final int X86_REG_ZMM28 = 206;
	public static final int X86_REG_ZMM29 = 207;
	public static final int X86_REG_ZMM30 = 208;
	public static final int X86_REG_ZMM31 = 209;
	public static final int X86_REG_R8B = 210;
	public static final int X86_REG_R9B = 211;
	public static final int X86_REG_R10B = 212;
	public static final int X86_REG_R11B = 213;
	public static final int X86_REG_R12B = 214;
	public static final int X86_REG_R13B = 215;
	public static final int X86_REG_R14B = 216;
	public static final int X86_REG_R15B = 217;
	public static final int X86_REG_R8D = 218;
	public static final int X86_REG_R9D = 219;
	public static final int X86_REG_R10D = 220;
	public static final int X86_REG_R11D = 221;
	public static final int X86_REG_R12D = 222;
	public static final int X86_REG_R13D = 223;
	public static final int X86_REG_R14D = 224;
	public static final int X86_REG_R15D = 225;
	public static final int X86_REG_R8W = 226;
	public static final int X86_REG_R9W = 227;
	public static final int X86_REG_R10W = 228;
	public static final int X86_REG_R11W = 229;
	public static final int X86_REG_R12W = 230;
	public static final int X86_REG_R13W = 231;
	public static final int X86_REG_R14W = 232;
	public static final int X86_REG_R15W = 233;
	public static final int X86_REG_ENDING = 234;

	// Operand type for instruction's operands

	public static final int X86_OP_INVALID = 0;
	public static final int X86_OP_REG = 1;
	public static final int X86_OP_IMM = 2;
	public static final int X86_OP_MEM = 3;
	public static final int X86_OP_FP = 4;

	// AVX broadcast type

	public static final int X86_AVX_BCAST_INVALID = 0;
	public static final int X86_AVX_BCAST_2 = 1;
	public static final int X86_AVX_BCAST_4 = 2;
	public static final int X86_AVX_BCAST_8 = 3;
	public static final int X86_AVX_BCAST_16 = 4;

	// SSE Code Condition type

	public static final int X86_SSE_CC_INVALID = 0;
	public static final int X86_SSE_CC_EQ = 1;
	public static final int X86_SSE_CC_LT = 2;
	public static final int X86_SSE_CC_LE = 3;
	public static final int X86_SSE_CC_UNORD = 4;
	public static final int X86_SSE_CC_NEQ = 5;
	public static final int X86_SSE_CC_NLT = 6;
	public static final int X86_SSE_CC_NLE = 7;
	public static final int X86_SSE_CC_ORD = 8;
	public static final int X86_SSE_CC_EQ_UQ = 9;
	public static final int X86_SSE_CC_NGE = 10;
	public static final int X86_SSE_CC_NGT = 11;
	public static final int X86_SSE_CC_FALSE = 12;
	public static final int X86_SSE_CC_NEQ_OQ = 13;
	public static final int X86_SSE_CC_GE = 14;
	public static final int X86_SSE_CC_GT = 15;
	public static final int X86_SSE_CC_TRUE = 16;

	// AVX Code Condition type

	public static final int X86_AVX_CC_INVALID = 0;
	public static final int X86_AVX_CC_EQ = 1;
	public static final int X86_AVX_CC_LT = 2;
	public static final int X86_AVX_CC_LE = 3;
	public static final int X86_AVX_CC_UNORD = 4;
	public static final int X86_AVX_CC_NEQ = 5;
	public static final int X86_AVX_CC_NLT = 6;
	public static final int X86_AVX_CC_NLE = 7;
	public static final int X86_AVX_CC_ORD = 8;
	public static final int X86_AVX_CC_EQ_UQ = 9;
	public static final int X86_AVX_CC_NGE = 10;
	public static final int X86_AVX_CC_NGT = 11;
	public static final int X86_AVX_CC_FALSE = 12;
	public static final int X86_AVX_CC_NEQ_OQ = 13;
	public static final int X86_AVX_CC_GE = 14;
	public static final int X86_AVX_CC_GT = 15;
	public static final int X86_AVX_CC_TRUE = 16;
	public static final int X86_AVX_CC_EQ_OS = 17;
	public static final int X86_AVX_CC_LT_OQ = 18;
	public static final int X86_AVX_CC_LE_OQ = 19;
	public static final int X86_AVX_CC_UNORD_S = 20;
	public static final int X86_AVX_CC_NEQ_US = 21;
	public static final int X86_AVX_CC_NLT_UQ = 22;
	public static final int X86_AVX_CC_NLE_UQ = 23;
	public static final int X86_AVX_CC_ORD_S = 24;
	public static final int X86_AVX_CC_EQ_US = 25;
	public static final int X86_AVX_CC_NGE_UQ = 26;
	public static final int X86_AVX_CC_NGT_UQ = 27;
	public static final int X86_AVX_CC_FALSE_OS = 28;
	public static final int X86_AVX_CC_NEQ_OS = 29;
	public static final int X86_AVX_CC_GE_OQ = 30;
	public static final int X86_AVX_CC_GT_OQ = 31;
	public static final int X86_AVX_CC_TRUE_US = 32;

	// AVX static rounding mode type

	public static final int X86_AVX_RM_INVALID = 0;
	public static final int X86_AVX_RM_RN = 1;
	public static final int X86_AVX_RM_RD = 2;
	public static final int X86_AVX_RM_RU = 3;
	public static final int X86_AVX_RM_RZ = 4;

	// Instruction prefixes - to be used in cs_x86.prefix[]
	public static final int X86_PREFIX_LOCK = 0xf0;
	public static final int X86_PREFIX_REP = 0xf3;
	public static final int X86_PREFIX_REPNE = 0xf2;
	public static final int X86_PREFIX_CS = 0x2e;
	public static final int X86_PREFIX_SS = 0x36;
	public static final int X86_PREFIX_DS = 0x3e;
	public static final int X86_PREFIX_ES = 0x26;
	public static final int X86_PREFIX_FS = 0x64;
	public static final int X86_PREFIX_GS = 0x65;
	public static final int X86_PREFIX_OPSIZE = 0x66;
	public static final int X86_PREFIX_ADDRSIZE = 0x67;

	// X86 instructions

	public static final int X86_INS_INVALID = 0;
	public static final int X86_INS_AAA = 1;
	public static final int X86_INS_AAD = 2;
	public static final int X86_INS_AAM = 3;
	public static final int X86_INS_AAS = 4;
	public static final int X86_INS_FABS = 5;
	public static final int X86_INS_ADC = 6;
	public static final int X86_INS_ADCX = 7;
	public static final int X86_INS_ADD = 8;
	public static final int X86_INS_ADDPD = 9;
	public static final int X86_INS_ADDPS = 10;
	public static final int X86_INS_ADDSD = 11;
	public static final int X86_INS_ADDSS = 12;
	public static final int X86_INS_ADDSUBPD = 13;
	public static final int X86_INS_ADDSUBPS = 14;
	public static final int X86_INS_FADD = 15;
	public static final int X86_INS_FIADD = 16;
	public static final int X86_INS_FADDP = 17;
	public static final int X86_INS_ADOX = 18;
	public static final int X86_INS_AESDECLAST = 19;
	public static final int X86_INS_AESDEC = 20;
	public static final int X86_INS_AESENCLAST = 21;
	public static final int X86_INS_AESENC = 22;
	public static final int X86_INS_AESIMC = 23;
	public static final int X86_INS_AESKEYGENASSIST = 24;
	public static final int X86_INS_AND = 25;
	public static final int X86_INS_ANDN = 26;
	public static final int X86_INS_ANDNPD = 27;
	public static final int X86_INS_ANDNPS = 28;
	public static final int X86_INS_ANDPD = 29;
	public static final int X86_INS_ANDPS = 30;
	public static final int X86_INS_ARPL = 31;
	public static final int X86_INS_BEXTR = 32;
	public static final int X86_INS_BLCFILL = 33;
	public static final int X86_INS_BLCI = 34;
	public static final int X86_INS_BLCIC = 35;
	public static final int X86_INS_BLCMSK = 36;
	public static final int X86_INS_BLCS = 37;
	public static final int X86_INS_BLENDPD = 38;
	public static final int X86_INS_BLENDPS = 39;
	public static final int X86_INS_BLENDVPD = 40;
	public static final int X86_INS_BLENDVPS = 41;
	public static final int X86_INS_BLSFILL = 42;
	public static final int X86_INS_BLSI = 43;
	public static final int X86_INS_BLSIC = 44;
	public static final int X86_INS_BLSMSK = 45;
	public static final int X86_INS_BLSR = 46;
	public static final int X86_INS_BOUND = 47;
	public static final int X86_INS_BSF = 48;
	public static final int X86_INS_BSR = 49;
	public static final int X86_INS_BSWAP = 50;
	public static final int X86_INS_BT = 51;
	public static final int X86_INS_BTC = 52;
	public static final int X86_INS_BTR = 53;
	public static final int X86_INS_BTS = 54;
	public static final int X86_INS_BZHI = 55;
	public static final int X86_INS_CALL = 56;
	public static final int X86_INS_CBW = 57;
	public static final int X86_INS_CDQ = 58;
	public static final int X86_INS_CDQE = 59;
	public static final int X86_INS_FCHS = 60;
	public static final int X86_INS_CLAC = 61;
	public static final int X86_INS_CLC = 62;
	public static final int X86_INS_CLD = 63;
	public static final int X86_INS_CLFLUSH = 64;
	public static final int X86_INS_CLGI = 65;
	public static final int X86_INS_CLI = 66;
	public static final int X86_INS_CLTS = 67;
	public static final int X86_INS_CMC = 68;
	public static final int X86_INS_CMOVA = 69;
	public static final int X86_INS_CMOVAE = 70;
	public static final int X86_INS_CMOVB = 71;
	public static final int X86_INS_CMOVBE = 72;
	public static final int X86_INS_FCMOVBE = 73;
	public static final int X86_INS_FCMOVB = 74;
	public static final int X86_INS_CMOVE = 75;
	public static final int X86_INS_FCMOVE = 76;
	public static final int X86_INS_CMOVG = 77;
	public static final int X86_INS_CMOVGE = 78;
	public static final int X86_INS_CMOVL = 79;
	public static final int X86_INS_CMOVLE = 80;
	public static final int X86_INS_FCMOVNBE = 81;
	public static final int X86_INS_FCMOVNB = 82;
	public static final int X86_INS_CMOVNE = 83;
	public static final int X86_INS_FCMOVNE = 84;
	public static final int X86_INS_CMOVNO = 85;
	public static final int X86_INS_CMOVNP = 86;
	public static final int X86_INS_FCMOVNU = 87;
	public static final int X86_INS_CMOVNS = 88;
	public static final int X86_INS_CMOVO = 89;
	public static final int X86_INS_CMOVP = 90;
	public static final int X86_INS_FCMOVU = 91;
	public static final int X86_INS_CMOVS = 92;
	public static final int X86_INS_CMP = 93;
	public static final int X86_INS_CMPPD = 94;
	public static final int X86_INS_CMPPS = 95;
	public static final int X86_INS_CMPSB = 96;
	public static final int X86_INS_CMPSD = 97;
	public static final int X86_INS_CMPSQ = 98;
	public static final int X86_INS_CMPSS = 99;
	public static final int X86_INS_CMPSW = 100;
	public static final int X86_INS_CMPXCHG16B = 101;
	public static final int X86_INS_CMPXCHG = 102;
	public static final int X86_INS_CMPXCHG8B = 103;
	public static final int X86_INS_COMISD = 104;
	public static final int X86_INS_COMISS = 105;
	public static final int X86_INS_FCOMP = 106;
	public static final int X86_INS_FCOMPI = 107;
	public static final int X86_INS_FCOMI = 108;
	public static final int X86_INS_FCOM = 109;
	public static final int X86_INS_FCOS = 110;
	public static final int X86_INS_CPUID = 111;
	public static final int X86_INS_CQO = 112;
	public static final int X86_INS_CRC32 = 113;
	public static final int X86_INS_CVTDQ2PD = 114;
	public static final int X86_INS_CVTDQ2PS = 115;
	public static final int X86_INS_CVTPD2DQ = 116;
	public static final int X86_INS_CVTPD2PS = 117;
	public static final int X86_INS_CVTPS2DQ = 118;
	public static final int X86_INS_CVTPS2PD = 119;
	public static final int X86_INS_CVTSD2SI = 120;
	public static final int X86_INS_CVTSD2SS = 121;
	public static final int X86_INS_CVTSI2SD = 122;
	public static final int X86_INS_CVTSI2SS = 123;
	public static final int X86_INS_CVTSS2SD = 124;
	public static final int X86_INS_CVTSS2SI = 125;
	public static final int X86_INS_CVTTPD2DQ = 126;
	public static final int X86_INS_CVTTPS2DQ = 127;
	public static final int X86_INS_CVTTSD2SI = 128;
	public static final int X86_INS_CVTTSS2SI = 129;
	public static final int X86_INS_CWD = 130;
	public static final int X86_INS_CWDE = 131;
	public static final int X86_INS_DAA = 132;
	public static final int X86_INS_DAS = 133;
	public static final int X86_INS_DATA16 = 134;
	public static final int X86_INS_DEC = 135;
	public static final int X86_INS_DIV = 136;
	public static final int X86_INS_DIVPD = 137;
	public static final int X86_INS_DIVPS = 138;
	public static final int X86_INS_FDIVR = 139;
	public static final int X86_INS_FIDIVR = 140;
	public static final int X86_INS_FDIVRP = 141;
	public static final int X86_INS_DIVSD = 142;
	public static final int X86_INS_DIVSS = 143;
	public static final int X86_INS_FDIV = 144;
	public static final int X86_INS_FIDIV = 145;
	public static final int X86_INS_FDIVP = 146;
	public static final int X86_INS_DPPD = 147;
	public static final int X86_INS_DPPS = 148;
	public static final int X86_INS_RET = 149;
	public static final int X86_INS_ENCLS = 150;
	public static final int X86_INS_ENCLU = 151;
	public static final int X86_INS_ENTER = 152;
	public static final int X86_INS_EXTRACTPS = 153;
	public static final int X86_INS_EXTRQ = 154;
	public static final int X86_INS_F2XM1 = 155;
	public static final int X86_INS_LCALL = 156;
	public static final int X86_INS_LJMP = 157;
	public static final int X86_INS_FBLD = 158;
	public static final int X86_INS_FBSTP = 159;
	public static final int X86_INS_FCOMPP = 160;
	public static final int X86_INS_FDECSTP = 161;
	public static final int X86_INS_FEMMS = 162;
	public static final int X86_INS_FFREE = 163;
	public static final int X86_INS_FICOM = 164;
	public static final int X86_INS_FICOMP = 165;
	public static final int X86_INS_FINCSTP = 166;
	public static final int X86_INS_FLDCW = 167;
	public static final int X86_INS_FLDENV = 168;
	public static final int X86_INS_FLDL2E = 169;
	public static final int X86_INS_FLDL2T = 170;
	public static final int X86_INS_FLDLG2 = 171;
	public static final int X86_INS_FLDLN2 = 172;
	public static final int X86_INS_FLDPI = 173;
	public static final int X86_INS_FNCLEX = 174;
	public static final int X86_INS_FNINIT = 175;
	public static final int X86_INS_FNOP = 176;
	public static final int X86_INS_FNSTCW = 177;
	public static final int X86_INS_FNSTSW = 178;
	public static final int X86_INS_FPATAN = 179;
	public static final int X86_INS_FPREM = 180;
	public static final int X86_INS_FPREM1 = 181;
	public static final int X86_INS_FPTAN = 182;
	public static final int X86_INS_FRNDINT = 183;
	public static final int X86_INS_FRSTOR = 184;
	public static final int X86_INS_FNSAVE = 185;
	public static final int X86_INS_FSCALE = 186;
	public static final int X86_INS_FSETPM = 187;
	public static final int X86_INS_FSINCOS = 188;
	public static final int X86_INS_FNSTENV = 189;
	public static final int X86_INS_FXAM = 190;
	public static final int X86_INS_FXRSTOR = 191;
	public static final int X86_INS_FXRSTOR64 = 192;
	public static final int X86_INS_FXSAVE = 193;
	public static final int X86_INS_FXSAVE64 = 194;
	public static final int X86_INS_FXTRACT = 195;
	public static final int X86_INS_FYL2X = 196;
	public static final int X86_INS_FYL2XP1 = 197;
	public static final int X86_INS_MOVAPD = 198;
	public static final int X86_INS_MOVAPS = 199;
	public static final int X86_INS_ORPD = 200;
	public static final int X86_INS_ORPS = 201;
	public static final int X86_INS_VMOVAPD = 202;
	public static final int X86_INS_VMOVAPS = 203;
	public static final int X86_INS_XORPD = 204;
	public static final int X86_INS_XORPS = 205;
	public static final int X86_INS_GETSEC = 206;
	public static final int X86_INS_HADDPD = 207;
	public static final int X86_INS_HADDPS = 208;
	public static final int X86_INS_HLT = 209;
	public static final int X86_INS_HSUBPD = 210;
	public static final int X86_INS_HSUBPS = 211;
	public static final int X86_INS_IDIV = 212;
	public static final int X86_INS_FILD = 213;
	public static final int X86_INS_IMUL = 214;
	public static final int X86_INS_IN = 215;
	public static final int X86_INS_INC = 216;
	public static final int X86_INS_INSB = 217;
	public static final int X86_INS_INSERTPS = 218;
	public static final int X86_INS_INSERTQ = 219;
	public static final int X86_INS_INSD = 220;
	public static final int X86_INS_INSW = 221;
	public static final int X86_INS_INT = 222;
	public static final int X86_INS_INT1 = 223;
	public static final int X86_INS_INT3 = 224;
	public static final int X86_INS_INTO = 225;
	public static final int X86_INS_INVD = 226;
	public static final int X86_INS_INVEPT = 227;
	public static final int X86_INS_INVLPG = 228;
	public static final int X86_INS_INVLPGA = 229;
	public static final int X86_INS_INVPCID = 230;
	public static final int X86_INS_INVVPID = 231;
	public static final int X86_INS_IRET = 232;
	public static final int X86_INS_IRETD = 233;
	public static final int X86_INS_IRETQ = 234;
	public static final int X86_INS_FISTTP = 235;
	public static final int X86_INS_FIST = 236;
	public static final int X86_INS_FISTP = 237;
	public static final int X86_INS_UCOMISD = 238;
	public static final int X86_INS_UCOMISS = 239;
	public static final int X86_INS_VCMP = 240;
	public static final int X86_INS_VCOMISD = 241;
	public static final int X86_INS_VCOMISS = 242;
	public static final int X86_INS_VCVTSD2SS = 243;
	public static final int X86_INS_VCVTSI2SD = 244;
	public static final int X86_INS_VCVTSI2SS = 245;
	public static final int X86_INS_VCVTSS2SD = 246;
	public static final int X86_INS_VCVTTSD2SI = 247;
	public static final int X86_INS_VCVTTSD2USI = 248;
	public static final int X86_INS_VCVTTSS2SI = 249;
	public static final int X86_INS_VCVTTSS2USI = 250;
	public static final int X86_INS_VCVTUSI2SD = 251;
	public static final int X86_INS_VCVTUSI2SS = 252;
	public static final int X86_INS_VUCOMISD = 253;
	public static final int X86_INS_VUCOMISS = 254;
	public static final int X86_INS_JAE = 255;
	public static final int X86_INS_JA = 256;
	public static final int X86_INS_JBE = 257;
	public static final int X86_INS_JB = 258;
	public static final int X86_INS_JCXZ = 259;
	public static final int X86_INS_JECXZ = 260;
	public static final int X86_INS_JE = 261;
	public static final int X86_INS_JGE = 262;
	public static final int X86_INS_JG = 263;
	public static final int X86_INS_JLE = 264;
	public static final int X86_INS_JL = 265;
	public static final int X86_INS_JMP = 266;
	public static final int X86_INS_JNE = 267;
	public static final int X86_INS_JNO = 268;
	public static final int X86_INS_JNP = 269;
	public static final int X86_INS_JNS = 270;
	public static final int X86_INS_JO = 271;
	public static final int X86_INS_JP = 272;
	public static final int X86_INS_JRCXZ = 273;
	public static final int X86_INS_JS = 274;
	public static final int X86_INS_KANDB = 275;
	public static final int X86_INS_KANDD = 276;
	public static final int X86_INS_KANDNB = 277;
	public static final int X86_INS_KANDND = 278;
	public static final int X86_INS_KANDNQ = 279;
	public static final int X86_INS_KANDNW = 280;
	public static final int X86_INS_KANDQ = 281;
	public static final int X86_INS_KANDW = 282;
	public static final int X86_INS_KMOVB = 283;
	public static final int X86_INS_KMOVD = 284;
	public static final int X86_INS_KMOVQ = 285;
	public static final int X86_INS_KMOVW = 286;
	public static final int X86_INS_KNOTB = 287;
	public static final int X86_INS_KNOTD = 288;
	public static final int X86_INS_KNOTQ = 289;
	public static final int X86_INS_KNOTW = 290;
	public static final int X86_INS_KORB = 291;
	public static final int X86_INS_KORD = 292;
	public static final int X86_INS_KORQ = 293;
	public static final int X86_INS_KORTESTW = 294;
	public static final int X86_INS_KORW = 295;
	public static final int X86_INS_KSHIFTLW = 296;
	public static final int X86_INS_KSHIFTRW = 297;
	public static final int X86_INS_KUNPCKBW = 298;
	public static final int X86_INS_KXNORB = 299;
	public static final int X86_INS_KXNORD = 300;
	public static final int X86_INS_KXNORQ = 301;
	public static final int X86_INS_KXNORW = 302;
	public static final int X86_INS_KXORB = 303;
	public static final int X86_INS_KXORD = 304;
	public static final int X86_INS_KXORQ = 305;
	public static final int X86_INS_KXORW = 306;
	public static final int X86_INS_LAHF = 307;
	public static final int X86_INS_LAR = 308;
	public static final int X86_INS_LDDQU = 309;
	public static final int X86_INS_LDMXCSR = 310;
	public static final int X86_INS_LDS = 311;
	public static final int X86_INS_FLDZ = 312;
	public static final int X86_INS_FLD1 = 313;
	public static final int X86_INS_FLD = 314;
	public static final int X86_INS_LEA = 315;
	public static final int X86_INS_LEAVE = 316;
	public static final int X86_INS_LES = 317;
	public static final int X86_INS_LFENCE = 318;
	public static final int X86_INS_LFS = 319;
	public static final int X86_INS_LGDT = 320;
	public static final int X86_INS_LGS = 321;
	public static final int X86_INS_LIDT = 322;
	public static final int X86_INS_LLDT = 323;
	public static final int X86_INS_LMSW = 324;
	public static final int X86_INS_OR = 325;
	public static final int X86_INS_SUB = 326;
	public static final int X86_INS_XOR = 327;
	public static final int X86_INS_LODSB = 328;
	public static final int X86_INS_LODSD = 329;
	public static final int X86_INS_LODSQ = 330;
	public static final int X86_INS_LODSW = 331;
	public static final int X86_INS_LOOP = 332;
	public static final int X86_INS_LOOPE = 333;
	public static final int X86_INS_LOOPNE = 334;
	public static final int X86_INS_RETF = 335;
	public static final int X86_INS_RETFQ = 336;
	public static final int X86_INS_LSL = 337;
	public static final int X86_INS_LSS = 338;
	public static final int X86_INS_LTR = 339;
	public static final int X86_INS_XADD = 340;
	public static final int X86_INS_LZCNT = 341;
	public static final int X86_INS_MASKMOVDQU = 342;
	public static final int X86_INS_MAXPD = 343;
	public static final int X86_INS_MAXPS = 344;
	public static final int X86_INS_MAXSD = 345;
	public static final int X86_INS_MAXSS = 346;
	public static final int X86_INS_MFENCE = 347;
	public static final int X86_INS_MINPD = 348;
	public static final int X86_INS_MINPS = 349;
	public static final int X86_INS_MINSD = 350;
	public static final int X86_INS_MINSS = 351;
	public static final int X86_INS_CVTPD2PI = 352;
	public static final int X86_INS_CVTPI2PD = 353;
	public static final int X86_INS_CVTPI2PS = 354;
	public static final int X86_INS_CVTPS2PI = 355;
	public static final int X86_INS_CVTTPD2PI = 356;
	public static final int X86_INS_CVTTPS2PI = 357;
	public static final int X86_INS_EMMS = 358;
	public static final int X86_INS_MASKMOVQ = 359;
	public static final int X86_INS_MOVD = 360;
	public static final int X86_INS_MOVDQ2Q = 361;
	public static final int X86_INS_MOVNTQ = 362;
	public static final int X86_INS_MOVQ2DQ = 363;
	public static final int X86_INS_MOVQ = 364;
	public static final int X86_INS_PABSB = 365;
	public static final int X86_INS_PABSD = 366;
	public static final int X86_INS_PABSW = 367;
	public static final int X86_INS_PACKSSDW = 368;
	public static final int X86_INS_PACKSSWB = 369;
	public static final int X86_INS_PACKUSWB = 370;
	public static final int X86_INS_PADDB = 371;
	public static final int X86_INS_PADDD = 372;
	public static final int X86_INS_PADDQ = 373;
	public static final int X86_INS_PADDSB = 374;
	public static final int X86_INS_PADDSW = 375;
	public static final int X86_INS_PADDUSB = 376;
	public static final int X86_INS_PADDUSW = 377;
	public static final int X86_INS_PADDW = 378;
	public static final int X86_INS_PALIGNR = 379;
	public static final int X86_INS_PANDN = 380;
	public static final int X86_INS_PAND = 381;
	public static final int X86_INS_PAVGB = 382;
	public static final int X86_INS_PAVGW = 383;
	public static final int X86_INS_PCMPEQB = 384;
	public static final int X86_INS_PCMPEQD = 385;
	public static final int X86_INS_PCMPEQW = 386;
	public static final int X86_INS_PCMPGTB = 387;
	public static final int X86_INS_PCMPGTD = 388;
	public static final int X86_INS_PCMPGTW = 389;
	public static final int X86_INS_PEXTRW = 390;
	public static final int X86_INS_PHADDSW = 391;
	public static final int X86_INS_PHADDW = 392;
	public static final int X86_INS_PHADDD = 393;
	public static final int X86_INS_PHSUBD = 394;
	public static final int X86_INS_PHSUBSW = 395;
	public static final int X86_INS_PHSUBW = 396;
	public static final int X86_INS_PINSRW = 397;
	public static final int X86_INS_PMADDUBSW = 398;
	public static final int X86_INS_PMADDWD = 399;
	public static final int X86_INS_PMAXSW = 400;
	public static final int X86_INS_PMAXUB = 401;
	public static final int X86_INS_PMINSW = 402;
	public static final int X86_INS_PMINUB = 403;
	public static final int X86_INS_PMOVMSKB = 404;
	public static final int X86_INS_PMULHRSW = 405;
	public static final int X86_INS_PMULHUW = 406;
	public static final int X86_INS_PMULHW = 407;
	public static final int X86_INS_PMULLW = 408;
	public static final int X86_INS_PMULUDQ = 409;
	public static final int X86_INS_POR = 410;
	public static final int X86_INS_PSADBW = 411;
	public static final int X86_INS_PSHUFB = 412;
	public static final int X86_INS_PSHUFW = 413;
	public static final int X86_INS_PSIGNB = 414;
	public static final int X86_INS_PSIGND = 415;
	public static final int X86_INS_PSIGNW = 416;
	public static final int X86_INS_PSLLD = 417;
	public static final int X86_INS_PSLLQ = 418;
	public static final int X86_INS_PSLLW = 419;
	public static final int X86_INS_PSRAD = 420;
	public static final int X86_INS_PSRAW = 421;
	public static final int X86_INS_PSRLD = 422;
	public static final int X86_INS_PSRLQ = 423;
	public static final int X86_INS_PSRLW = 424;
	public static final int X86_INS_PSUBB = 425;
	public static final int X86_INS_PSUBD = 426;
	public static final int X86_INS_PSUBQ = 427;
	public static final int X86_INS_PSUBSB = 428;
	public static final int X86_INS_PSUBSW = 429;
	public static final int X86_INS_PSUBUSB = 430;
	public static final int X86_INS_PSUBUSW = 431;
	public static final int X86_INS_PSUBW = 432;
	public static final int X86_INS_PUNPCKHBW = 433;
	public static final int X86_INS_PUNPCKHDQ = 434;
	public static final int X86_INS_PUNPCKHWD = 435;
	public static final int X86_INS_PUNPCKLBW = 436;
	public static final int X86_INS_PUNPCKLDQ = 437;
	public static final int X86_INS_PUNPCKLWD = 438;
	public static final int X86_INS_PXOR = 439;
	public static final int X86_INS_MONITOR = 440;
	public static final int X86_INS_MONTMUL = 441;
	public static final int X86_INS_MOV = 442;
	public static final int X86_INS_MOVABS = 443;
	public static final int X86_INS_MOVBE = 444;
	public static final int X86_INS_MOVDDUP = 445;
	public static final int X86_INS_MOVDQA = 446;
	public static final int X86_INS_MOVDQU = 447;
	public static final int X86_INS_MOVHLPS = 448;
	public static final int X86_INS_MOVHPD = 449;
	public static final int X86_INS_MOVHPS = 450;
	public static final int X86_INS_MOVLHPS = 451;
	public static final int X86_INS_MOVLPD = 452;
	public static final int X86_INS_MOVLPS = 453;
	public static final int X86_INS_MOVMSKPD = 454;
	public static final int X86_INS_MOVMSKPS = 455;
	public static final int X86_INS_MOVNTDQA = 456;
	public static final int X86_INS_MOVNTDQ = 457;
	public static final int X86_INS_MOVNTI = 458;
	public static final int X86_INS_MOVNTPD = 459;
	public static final int X86_INS_MOVNTPS = 460;
	public static final int X86_INS_MOVNTSD = 461;
	public static final int X86_INS_MOVNTSS = 462;
	public static final int X86_INS_MOVSB = 463;
	public static final int X86_INS_MOVSD = 464;
	public static final int X86_INS_MOVSHDUP = 465;
	public static final int X86_INS_MOVSLDUP = 466;
	public static final int X86_INS_MOVSQ = 467;
	public static final int X86_INS_MOVSS = 468;
	public static final int X86_INS_MOVSW = 469;
	public static final int X86_INS_MOVSX = 470;
	public static final int X86_INS_MOVSXD = 471;
	public static final int X86_INS_MOVUPD = 472;
	public static final int X86_INS_MOVUPS = 473;
	public static final int X86_INS_MOVZX = 474;
	public static final int X86_INS_MPSADBW = 475;
	public static final int X86_INS_MUL = 476;
	public static final int X86_INS_MULPD = 477;
	public static final int X86_INS_MULPS = 478;
	public static final int X86_INS_MULSD = 479;
	public static final int X86_INS_MULSS = 480;
	public static final int X86_INS_MULX = 481;
	public static final int X86_INS_FMUL = 482;
	public static final int X86_INS_FIMUL = 483;
	public static final int X86_INS_FMULP = 484;
	public static final int X86_INS_MWAIT = 485;
	public static final int X86_INS_NEG = 486;
	public static final int X86_INS_NOP = 487;
	public static final int X86_INS_NOT = 488;
	public static final int X86_INS_OUT = 489;
	public static final int X86_INS_OUTSB = 490;
	public static final int X86_INS_OUTSD = 491;
	public static final int X86_INS_OUTSW = 492;
	public static final int X86_INS_PACKUSDW = 493;
	public static final int X86_INS_PAUSE = 494;
	public static final int X86_INS_PAVGUSB = 495;
	public static final int X86_INS_PBLENDVB = 496;
	public static final int X86_INS_PBLENDW = 497;
	public static final int X86_INS_PCLMULQDQ = 498;
	public static final int X86_INS_PCMPEQQ = 499;
	public static final int X86_INS_PCMPESTRI = 500;
	public static final int X86_INS_PCMPESTRM = 501;
	public static final int X86_INS_PCMPGTQ = 502;
	public static final int X86_INS_PCMPISTRI = 503;
	public static final int X86_INS_PCMPISTRM = 504;
	public static final int X86_INS_PDEP = 505;
	public static final int X86_INS_PEXT = 506;
	public static final int X86_INS_PEXTRB = 507;
	public static final int X86_INS_PEXTRD = 508;
	public static final int X86_INS_PEXTRQ = 509;
	public static final int X86_INS_PF2ID = 510;
	public static final int X86_INS_PF2IW = 511;
	public static final int X86_INS_PFACC = 512;
	public static final int X86_INS_PFADD = 513;
	public static final int X86_INS_PFCMPEQ = 514;
	public static final int X86_INS_PFCMPGE = 515;
	public static final int X86_INS_PFCMPGT = 516;
	public static final int X86_INS_PFMAX = 517;
	public static final int X86_INS_PFMIN = 518;
	public static final int X86_INS_PFMUL = 519;
	public static final int X86_INS_PFNACC = 520;
	public static final int X86_INS_PFPNACC = 521;
	public static final int X86_INS_PFRCPIT1 = 522;
	public static final int X86_INS_PFRCPIT2 = 523;
	public static final int X86_INS_PFRCP = 524;
	public static final int X86_INS_PFRSQIT1 = 525;
	public static final int X86_INS_PFRSQRT = 526;
	public static final int X86_INS_PFSUBR = 527;
	public static final int X86_INS_PFSUB = 528;
	public static final int X86_INS_PHMINPOSUW = 529;
	public static final int X86_INS_PI2FD = 530;
	public static final int X86_INS_PI2FW = 531;
	public static final int X86_INS_PINSRB = 532;
	public static final int X86_INS_PINSRD = 533;
	public static final int X86_INS_PINSRQ = 534;
	public static final int X86_INS_PMAXSB = 535;
	public static final int X86_INS_PMAXSD = 536;
	public static final int X86_INS_PMAXUD = 537;
	public static final int X86_INS_PMAXUW = 538;
	public static final int X86_INS_PMINSB = 539;
	public static final int X86_INS_PMINSD = 540;
	public static final int X86_INS_PMINUD = 541;
	public static final int X86_INS_PMINUW = 542;
	public static final int X86_INS_PMOVSXBD = 543;
	public static final int X86_INS_PMOVSXBQ = 544;
	public static final int X86_INS_PMOVSXBW = 545;
	public static final int X86_INS_PMOVSXDQ = 546;
	public static final int X86_INS_PMOVSXWD = 547;
	public static final int X86_INS_PMOVSXWQ = 548;
	public static final int X86_INS_PMOVZXBD = 549;
	public static final int X86_INS_PMOVZXBQ = 550;
	public static final int X86_INS_PMOVZXBW = 551;
	public static final int X86_INS_PMOVZXDQ = 552;
	public static final int X86_INS_PMOVZXWD = 553;
	public static final int X86_INS_PMOVZXWQ = 554;
	public static final int X86_INS_PMULDQ = 555;
	public static final int X86_INS_PMULHRW = 556;
	public static final int X86_INS_PMULLD = 557;
	public static final int X86_INS_POP = 558;
	public static final int X86_INS_POPAW = 559;
	public static final int X86_INS_POPAL = 560;
	public static final int X86_INS_POPCNT = 561;
	public static final int X86_INS_POPF = 562;
	public static final int X86_INS_POPFD = 563;
	public static final int X86_INS_POPFQ = 564;
	public static final int X86_INS_PREFETCH = 565;
	public static final int X86_INS_PREFETCHNTA = 566;
	public static final int X86_INS_PREFETCHT0 = 567;
	public static final int X86_INS_PREFETCHT1 = 568;
	public static final int X86_INS_PREFETCHT2 = 569;
	public static final int X86_INS_PREFETCHW = 570;
	public static final int X86_INS_PSHUFD = 571;
	public static final int X86_INS_PSHUFHW = 572;
	public static final int X86_INS_PSHUFLW = 573;
	public static final int X86_INS_PSLLDQ = 574;
	public static final int X86_INS_PSRLDQ = 575;
	public static final int X86_INS_PSWAPD = 576;
	public static final int X86_INS_PTEST = 577;
	public static final int X86_INS_PUNPCKHQDQ = 578;
	public static final int X86_INS_PUNPCKLQDQ = 579;
	public static final int X86_INS_PUSH = 580;
	public static final int X86_INS_PUSHAW = 581;
	public static final int X86_INS_PUSHAL = 582;
	public static final int X86_INS_PUSHF = 583;
	public static final int X86_INS_PUSHFD = 584;
	public static final int X86_INS_PUSHFQ = 585;
	public static final int X86_INS_RCL = 586;
	public static final int X86_INS_RCPPS = 587;
	public static final int X86_INS_RCPSS = 588;
	public static final int X86_INS_RCR = 589;
	public static final int X86_INS_RDFSBASE = 590;
	public static final int X86_INS_RDGSBASE = 591;
	public static final int X86_INS_RDMSR = 592;
	public static final int X86_INS_RDPMC = 593;
	public static final int X86_INS_RDRAND = 594;
	public static final int X86_INS_RDSEED = 595;
	public static final int X86_INS_RDTSC = 596;
	public static final int X86_INS_RDTSCP = 597;
	public static final int X86_INS_ROL = 598;
	public static final int X86_INS_ROR = 599;
	public static final int X86_INS_RORX = 600;
	public static final int X86_INS_ROUNDPD = 601;
	public static final int X86_INS_ROUNDPS = 602;
	public static final int X86_INS_ROUNDSD = 603;
	public static final int X86_INS_ROUNDSS = 604;
	public static final int X86_INS_RSM = 605;
	public static final int X86_INS_RSQRTPS = 606;
	public static final int X86_INS_RSQRTSS = 607;
	public static final int X86_INS_SAHF = 608;
	public static final int X86_INS_SAL = 609;
	public static final int X86_INS_SALC = 610;
	public static final int X86_INS_SAR = 611;
	public static final int X86_INS_SARX = 612;
	public static final int X86_INS_SBB = 613;
	public static final int X86_INS_SCASB = 614;
	public static final int X86_INS_SCASD = 615;
	public static final int X86_INS_SCASQ = 616;
	public static final int X86_INS_SCASW = 617;
	public static final int X86_INS_SETAE = 618;
	public static final int X86_INS_SETA = 619;
	public static final int X86_INS_SETBE = 620;
	public static final int X86_INS_SETB = 621;
	public static final int X86_INS_SETE = 622;
	public static final int X86_INS_SETGE = 623;
	public static final int X86_INS_SETG = 624;
	public static final int X86_INS_SETLE = 625;
	public static final int X86_INS_SETL = 626;
	public static final int X86_INS_SETNE = 627;
	public static final int X86_INS_SETNO = 628;
	public static final int X86_INS_SETNP = 629;
	public static final int X86_INS_SETNS = 630;
	public static final int X86_INS_SETO = 631;
	public static final int X86_INS_SETP = 632;
	public static final int X86_INS_SETS = 633;
	public static final int X86_INS_SFENCE = 634;
	public static final int X86_INS_SGDT = 635;
	public static final int X86_INS_SHA1MSG1 = 636;
	public static final int X86_INS_SHA1MSG2 = 637;
	public static final int X86_INS_SHA1NEXTE = 638;
	public static final int X86_INS_SHA1RNDS4 = 639;
	public static final int X86_INS_SHA256MSG1 = 640;
	public static final int X86_INS_SHA256MSG2 = 641;
	public static final int X86_INS_SHA256RNDS2 = 642;
	public static final int X86_INS_SHL = 643;
	public static final int X86_INS_SHLD = 644;
	public static final int X86_INS_SHLX = 645;
	public static final int X86_INS_SHR = 646;
	public static final int X86_INS_SHRD = 647;
	public static final int X86_INS_SHRX = 648;
	public static final int X86_INS_SHUFPD = 649;
	public static final int X86_INS_SHUFPS = 650;
	public static final int X86_INS_SIDT = 651;
	public static final int X86_INS_FSIN = 652;
	public static final int X86_INS_SKINIT = 653;
	public static final int X86_INS_SLDT = 654;
	public static final int X86_INS_SMSW = 655;
	public static final int X86_INS_SQRTPD = 656;
	public static final int X86_INS_SQRTPS = 657;
	public static final int X86_INS_SQRTSD = 658;
	public static final int X86_INS_SQRTSS = 659;
	public static final int X86_INS_FSQRT = 660;
	public static final int X86_INS_STAC = 661;
	public static final int X86_INS_STC = 662;
	public static final int X86_INS_STD = 663;
	public static final int X86_INS_STGI = 664;
	public static final int X86_INS_STI = 665;
	public static final int X86_INS_STMXCSR = 666;
	public static final int X86_INS_STOSB = 667;
	public static final int X86_INS_STOSD = 668;
	public static final int X86_INS_STOSQ = 669;
	public static final int X86_INS_STOSW = 670;
	public static final int X86_INS_STR = 671;
	public static final int X86_INS_FST = 672;
	public static final int X86_INS_FSTP = 673;
	public static final int X86_INS_FSTPNCE = 674;
	public static final int X86_INS_SUBPD = 675;
	public static final int X86_INS_SUBPS = 676;
	public static final int X86_INS_FSUBR = 677;
	public static final int X86_INS_FISUBR = 678;
	public static final int X86_INS_FSUBRP = 679;
	public static final int X86_INS_SUBSD = 680;
	public static final int X86_INS_SUBSS = 681;
	public static final int X86_INS_FSUB = 682;
	public static final int X86_INS_FISUB = 683;
	public static final int X86_INS_FSUBP = 684;
	public static final int X86_INS_SWAPGS = 685;
	public static final int X86_INS_SYSCALL = 686;
	public static final int X86_INS_SYSENTER = 687;
	public static final int X86_INS_SYSEXIT = 688;
	public static final int X86_INS_SYSRET = 689;
	public static final int X86_INS_T1MSKC = 690;
	public static final int X86_INS_TEST = 691;
	public static final int X86_INS_UD2 = 692;
	public static final int X86_INS_FTST = 693;
	public static final int X86_INS_TZCNT = 694;
	public static final int X86_INS_TZMSK = 695;
	public static final int X86_INS_FUCOMPI = 696;
	public static final int X86_INS_FUCOMI = 697;
	public static final int X86_INS_FUCOMPP = 698;
	public static final int X86_INS_FUCOMP = 699;
	public static final int X86_INS_FUCOM = 700;
	public static final int X86_INS_UD2B = 701;
	public static final int X86_INS_UNPCKHPD = 702;
	public static final int X86_INS_UNPCKHPS = 703;
	public static final int X86_INS_UNPCKLPD = 704;
	public static final int X86_INS_UNPCKLPS = 705;
	public static final int X86_INS_VADDPD = 706;
	public static final int X86_INS_VADDPS = 707;
	public static final int X86_INS_VADDSD = 708;
	public static final int X86_INS_VADDSS = 709;
	public static final int X86_INS_VADDSUBPD = 710;
	public static final int X86_INS_VADDSUBPS = 711;
	public static final int X86_INS_VAESDECLAST = 712;
	public static final int X86_INS_VAESDEC = 713;
	public static final int X86_INS_VAESENCLAST = 714;
	public static final int X86_INS_VAESENC = 715;
	public static final int X86_INS_VAESIMC = 716;
	public static final int X86_INS_VAESKEYGENASSIST = 717;
	public static final int X86_INS_VALIGND = 718;
	public static final int X86_INS_VALIGNQ = 719;
	public static final int X86_INS_VANDNPD = 720;
	public static final int X86_INS_VANDNPS = 721;
	public static final int X86_INS_VANDPD = 722;
	public static final int X86_INS_VANDPS = 723;
	public static final int X86_INS_VBLENDMPD = 724;
	public static final int X86_INS_VBLENDMPS = 725;
	public static final int X86_INS_VBLENDPD = 726;
	public static final int X86_INS_VBLENDPS = 727;
	public static final int X86_INS_VBLENDVPD = 728;
	public static final int X86_INS_VBLENDVPS = 729;
	public static final int X86_INS_VBROADCASTF128 = 730;
	public static final int X86_INS_VBROADCASTI128 = 731;
	public static final int X86_INS_VBROADCASTI32X4 = 732;
	public static final int X86_INS_VBROADCASTI64X4 = 733;
	public static final int X86_INS_VBROADCASTSD = 734;
	public static final int X86_INS_VBROADCASTSS = 735;
	public static final int X86_INS_VCMPPD = 736;
	public static final int X86_INS_VCMPPS = 737;
	public static final int X86_INS_VCMPSD = 738;
	public static final int X86_INS_VCMPSS = 739;
	public static final int X86_INS_VCVTDQ2PD = 740;
	public static final int X86_INS_VCVTDQ2PS = 741;
	public static final int X86_INS_VCVTPD2DQX = 742;
	public static final int X86_INS_VCVTPD2DQ = 743;
	public static final int X86_INS_VCVTPD2PSX = 744;
	public static final int X86_INS_VCVTPD2PS = 745;
	public static final int X86_INS_VCVTPD2UDQ = 746;
	public static final int X86_INS_VCVTPH2PS = 747;
	public static final int X86_INS_VCVTPS2DQ = 748;
	public static final int X86_INS_VCVTPS2PD = 749;
	public static final int X86_INS_VCVTPS2PH = 750;
	public static final int X86_INS_VCVTPS2UDQ = 751;
	public static final int X86_INS_VCVTSD2SI = 752;
	public static final int X86_INS_VCVTSD2USI = 753;
	public static final int X86_INS_VCVTSS2SI = 754;
	public static final int X86_INS_VCVTSS2USI = 755;
	public static final int X86_INS_VCVTTPD2DQX = 756;
	public static final int X86_INS_VCVTTPD2DQ = 757;
	public static final int X86_INS_VCVTTPD2UDQ = 758;
	public static final int X86_INS_VCVTTPS2DQ = 759;
	public static final int X86_INS_VCVTTPS2UDQ = 760;
	public static final int X86_INS_VCVTUDQ2PD = 761;
	public static final int X86_INS_VCVTUDQ2PS = 762;
	public static final int X86_INS_VDIVPD = 763;
	public static final int X86_INS_VDIVPS = 764;
	public static final int X86_INS_VDIVSD = 765;
	public static final int X86_INS_VDIVSS = 766;
	public static final int X86_INS_VDPPD = 767;
	public static final int X86_INS_VDPPS = 768;
	public static final int X86_INS_VERR = 769;
	public static final int X86_INS_VERW = 770;
	public static final int X86_INS_VEXTRACTF128 = 771;
	public static final int X86_INS_VEXTRACTF32X4 = 772;
	public static final int X86_INS_VEXTRACTF64X4 = 773;
	public static final int X86_INS_VEXTRACTI128 = 774;
	public static final int X86_INS_VEXTRACTI32X4 = 775;
	public static final int X86_INS_VEXTRACTI64X4 = 776;
	public static final int X86_INS_VEXTRACTPS = 777;
	public static final int X86_INS_VFMADD132PD = 778;
	public static final int X86_INS_VFMADD132PS = 779;
	public static final int X86_INS_VFMADD213PD = 780;
	public static final int X86_INS_VFMADD213PS = 781;
	public static final int X86_INS_VFMADDPD = 782;
	public static final int X86_INS_VFMADD231PD = 783;
	public static final int X86_INS_VFMADDPS = 784;
	public static final int X86_INS_VFMADD231PS = 785;
	public static final int X86_INS_VFMADDSD = 786;
	public static final int X86_INS_VFMADD213SD = 787;
	public static final int X86_INS_VFMADD132SD = 788;
	public static final int X86_INS_VFMADD231SD = 789;
	public static final int X86_INS_VFMADDSS = 790;
	public static final int X86_INS_VFMADD213SS = 791;
	public static final int X86_INS_VFMADD132SS = 792;
	public static final int X86_INS_VFMADD231SS = 793;
	public static final int X86_INS_VFMADDSUB132PD = 794;
	public static final int X86_INS_VFMADDSUB132PS = 795;
	public static final int X86_INS_VFMADDSUB213PD = 796;
	public static final int X86_INS_VFMADDSUB213PS = 797;
	public static final int X86_INS_VFMADDSUBPD = 798;
	public static final int X86_INS_VFMADDSUB231PD = 799;
	public static final int X86_INS_VFMADDSUBPS = 800;
	public static final int X86_INS_VFMADDSUB231PS = 801;
	public static final int X86_INS_VFMSUB132PD = 802;
	public static final int X86_INS_VFMSUB132PS = 803;
	public static final int X86_INS_VFMSUB213PD = 804;
	public static final int X86_INS_VFMSUB213PS = 805;
	public static final int X86_INS_VFMSUBADD132PD = 806;
	public static final int X86_INS_VFMSUBADD132PS = 807;
	public static final int X86_INS_VFMSUBADD213PD = 808;
	public static final int X86_INS_VFMSUBADD213PS = 809;
	public static final int X86_INS_VFMSUBADDPD = 810;
	public static final int X86_INS_VFMSUBADD231PD = 811;
	public static final int X86_INS_VFMSUBADDPS = 812;
	public static final int X86_INS_VFMSUBADD231PS = 813;
	public static final int X86_INS_VFMSUBPD = 814;
	public static final int X86_INS_VFMSUB231PD = 815;
	public static final int X86_INS_VFMSUBPS = 816;
	public static final int X86_INS_VFMSUB231PS = 817;
	public static final int X86_INS_VFMSUBSD = 818;
	public static final int X86_INS_VFMSUB213SD = 819;
	public static final int X86_INS_VFMSUB132SD = 820;
	public static final int X86_INS_VFMSUB231SD = 821;
	public static final int X86_INS_VFMSUBSS = 822;
	public static final int X86_INS_VFMSUB213SS = 823;
	public static final int X86_INS_VFMSUB132SS = 824;
	public static final int X86_INS_VFMSUB231SS = 825;
	public static final int X86_INS_VFNMADD132PD = 826;
	public static final int X86_INS_VFNMADD132PS = 827;
	public static final int X86_INS_VFNMADD213PD = 828;
	public static final int X86_INS_VFNMADD213PS = 829;
	public static final int X86_INS_VFNMADDPD = 830;
	public static final int X86_INS_VFNMADD231PD = 831;
	public static final int X86_INS_VFNMADDPS = 832;
	public static final int X86_INS_VFNMADD231PS = 833;
	public static final int X86_INS_VFNMADDSD = 834;
	public static final int X86_INS_VFNMADD213SD = 835;
	public static final int X86_INS_VFNMADD132SD = 836;
	public static final int X86_INS_VFNMADD231SD = 837;
	public static final int X86_INS_VFNMADDSS = 838;
	public static final int X86_INS_VFNMADD213SS = 839;
	public static final int X86_INS_VFNMADD132SS = 840;
	public static final int X86_INS_VFNMADD231SS = 841;
	public static final int X86_INS_VFNMSUB132PD = 842;
	public static final int X86_INS_VFNMSUB132PS = 843;
	public static final int X86_INS_VFNMSUB213PD = 844;
	public static final int X86_INS_VFNMSUB213PS = 845;
	public static final int X86_INS_VFNMSUBPD = 846;
	public static final int X86_INS_VFNMSUB231PD = 847;
	public static final int X86_INS_VFNMSUBPS = 848;
	public static final int X86_INS_VFNMSUB231PS = 849;
	public static final int X86_INS_VFNMSUBSD = 850;
	public static final int X86_INS_VFNMSUB213SD = 851;
	public static final int X86_INS_VFNMSUB132SD = 852;
	public static final int X86_INS_VFNMSUB231SD = 853;
	public static final int X86_INS_VFNMSUBSS = 854;
	public static final int X86_INS_VFNMSUB213SS = 855;
	public static final int X86_INS_VFNMSUB132SS = 856;
	public static final int X86_INS_VFNMSUB231SS = 857;
	public static final int X86_INS_VFRCZPD = 858;
	public static final int X86_INS_VFRCZPS = 859;
	public static final int X86_INS_VFRCZSD = 860;
	public static final int X86_INS_VFRCZSS = 861;
	public static final int X86_INS_VORPD = 862;
	public static final int X86_INS_VORPS = 863;
	public static final int X86_INS_VXORPD = 864;
	public static final int X86_INS_VXORPS = 865;
	public static final int X86_INS_VGATHERDPD = 866;
	public static final int X86_INS_VGATHERDPS = 867;
	public static final int X86_INS_VGATHERPF0DPD = 868;
	public static final int X86_INS_VGATHERPF0DPS = 869;
	public static final int X86_INS_VGATHERPF0QPD = 870;
	public static final int X86_INS_VGATHERPF0QPS = 871;
	public static final int X86_INS_VGATHERPF1DPD = 872;
	public static final int X86_INS_VGATHERPF1DPS = 873;
	public static final int X86_INS_VGATHERPF1QPD = 874;
	public static final int X86_INS_VGATHERPF1QPS = 875;
	public static final int X86_INS_VGATHERQPD = 876;
	public static final int X86_INS_VGATHERQPS = 877;
	public static final int X86_INS_VHADDPD = 878;
	public static final int X86_INS_VHADDPS = 879;
	public static final int X86_INS_VHSUBPD = 880;
	public static final int X86_INS_VHSUBPS = 881;
	public static final int X86_INS_VINSERTF128 = 882;
	public static final int X86_INS_VINSERTF32X4 = 883;
	public static final int X86_INS_VINSERTF64X4 = 884;
	public static final int X86_INS_VINSERTI128 = 885;
	public static final int X86_INS_VINSERTI32X4 = 886;
	public static final int X86_INS_VINSERTI64X4 = 887;
	public static final int X86_INS_VINSERTPS = 888;
	public static final int X86_INS_VLDDQU = 889;
	public static final int X86_INS_VLDMXCSR = 890;
	public static final int X86_INS_VMASKMOVDQU = 891;
	public static final int X86_INS_VMASKMOVPD = 892;
	public static final int X86_INS_VMASKMOVPS = 893;
	public static final int X86_INS_VMAXPD = 894;
	public static final int X86_INS_VMAXPS = 895;
	public static final int X86_INS_VMAXSD = 896;
	public static final int X86_INS_VMAXSS = 897;
	public static final int X86_INS_VMCALL = 898;
	public static final int X86_INS_VMCLEAR = 899;
	public static final int X86_INS_VMFUNC = 900;
	public static final int X86_INS_VMINPD = 901;
	public static final int X86_INS_VMINPS = 902;
	public static final int X86_INS_VMINSD = 903;
	public static final int X86_INS_VMINSS = 904;
	public static final int X86_INS_VMLAUNCH = 905;
	public static final int X86_INS_VMLOAD = 906;
	public static final int X86_INS_VMMCALL = 907;
	public static final int X86_INS_VMOVQ = 908;
	public static final int X86_INS_VMOVDDUP = 909;
	public static final int X86_INS_VMOVD = 910;
	public static final int X86_INS_VMOVDQA32 = 911;
	public static final int X86_INS_VMOVDQA64 = 912;
	public static final int X86_INS_VMOVDQA = 913;
	public static final int X86_INS_VMOVDQU16 = 914;
	public static final int X86_INS_VMOVDQU32 = 915;
	public static final int X86_INS_VMOVDQU64 = 916;
	public static final int X86_INS_VMOVDQU8 = 917;
	public static final int X86_INS_VMOVDQU = 918;
	public static final int X86_INS_VMOVHLPS = 919;
	public static final int X86_INS_VMOVHPD = 920;
	public static final int X86_INS_VMOVHPS = 921;
	public static final int X86_INS_VMOVLHPS = 922;
	public static final int X86_INS_VMOVLPD = 923;
	public static final int X86_INS_VMOVLPS = 924;
	public static final int X86_INS_VMOVMSKPD = 925;
	public static final int X86_INS_VMOVMSKPS = 926;
	public static final int X86_INS_VMOVNTDQA = 927;
	public static final int X86_INS_VMOVNTDQ = 928;
	public static final int X86_INS_VMOVNTPD = 929;
	public static final int X86_INS_VMOVNTPS = 930;
	public static final int X86_INS_VMOVSD = 931;
	public static final int X86_INS_VMOVSHDUP = 932;
	public static final int X86_INS_VMOVSLDUP = 933;
	public static final int X86_INS_VMOVSS = 934;
	public static final int X86_INS_VMOVUPD = 935;
	public static final int X86_INS_VMOVUPS = 936;
	public static final int X86_INS_VMPSADBW = 937;
	public static final int X86_INS_VMPTRLD = 938;
	public static final int X86_INS_VMPTRST = 939;
	public static final int X86_INS_VMREAD = 940;
	public static final int X86_INS_VMRESUME = 941;
	public static final int X86_INS_VMRUN = 942;
	public static final int X86_INS_VMSAVE = 943;
	public static final int X86_INS_VMULPD = 944;
	public static final int X86_INS_VMULPS = 945;
	public static final int X86_INS_VMULSD = 946;
	public static final int X86_INS_VMULSS = 947;
	public static final int X86_INS_VMWRITE = 948;
	public static final int X86_INS_VMXOFF = 949;
	public static final int X86_INS_VMXON = 950;
	public static final int X86_INS_VPABSB = 951;
	public static final int X86_INS_VPABSD = 952;
	public static final int X86_INS_VPABSQ = 953;
	public static final int X86_INS_VPABSW = 954;
	public static final int X86_INS_VPACKSSDW = 955;
	public static final int X86_INS_VPACKSSWB = 956;
	public static final int X86_INS_VPACKUSDW = 957;
	public static final int X86_INS_VPACKUSWB = 958;
	public static final int X86_INS_VPADDB = 959;
	public static final int X86_INS_VPADDD = 960;
	public static final int X86_INS_VPADDQ = 961;
	public static final int X86_INS_VPADDSB = 962;
	public static final int X86_INS_VPADDSW = 963;
	public static final int X86_INS_VPADDUSB = 964;
	public static final int X86_INS_VPADDUSW = 965;
	public static final int X86_INS_VPADDW = 966;
	public static final int X86_INS_VPALIGNR = 967;
	public static final int X86_INS_VPANDD = 968;
	public static final int X86_INS_VPANDND = 969;
	public static final int X86_INS_VPANDNQ = 970;
	public static final int X86_INS_VPANDN = 971;
	public static final int X86_INS_VPANDQ = 972;
	public static final int X86_INS_VPAND = 973;
	public static final int X86_INS_VPAVGB = 974;
	public static final int X86_INS_VPAVGW = 975;
	public static final int X86_INS_VPBLENDD = 976;
	public static final int X86_INS_VPBLENDMD = 977;
	public static final int X86_INS_VPBLENDMQ = 978;
	public static final int X86_INS_VPBLENDVB = 979;
	public static final int X86_INS_VPBLENDW = 980;
	public static final int X86_INS_VPBROADCASTB = 981;
	public static final int X86_INS_VPBROADCASTD = 982;
	public static final int X86_INS_VPBROADCASTMB2Q = 983;
	public static final int X86_INS_VPBROADCASTMW2D = 984;
	public static final int X86_INS_VPBROADCASTQ = 985;
	public static final int X86_INS_VPBROADCASTW = 986;
	public static final int X86_INS_VPCLMULQDQ = 987;
	public static final int X86_INS_VPCMOV = 988;
	public static final int X86_INS_VPCMP = 989;
	public static final int X86_INS_VPCMPD = 990;
	public static final int X86_INS_VPCMPEQB = 991;
	public static final int X86_INS_VPCMPEQD = 992;
	public static final int X86_INS_VPCMPEQQ = 993;
	public static final int X86_INS_VPCMPEQW = 994;
	public static final int X86_INS_VPCMPESTRI = 995;
	public static final int X86_INS_VPCMPESTRM = 996;
	public static final int X86_INS_VPCMPGTB = 997;
	public static final int X86_INS_VPCMPGTD = 998;
	public static final int X86_INS_VPCMPGTQ = 999;
	public static final int X86_INS_VPCMPGTW = 1000;
	public static final int X86_INS_VPCMPISTRI = 1001;
	public static final int X86_INS_VPCMPISTRM = 1002;
	public static final int X86_INS_VPCMPQ = 1003;
	public static final int X86_INS_VPCMPUD = 1004;
	public static final int X86_INS_VPCMPUQ = 1005;
	public static final int X86_INS_VPCOMB = 1006;
	public static final int X86_INS_VPCOMD = 1007;
	public static final int X86_INS_VPCOMQ = 1008;
	public static final int X86_INS_VPCOMUB = 1009;
	public static final int X86_INS_VPCOMUD = 1010;
	public static final int X86_INS_VPCOMUQ = 1011;
	public static final int X86_INS_VPCOMUW = 1012;
	public static final int X86_INS_VPCOMW = 1013;
	public static final int X86_INS_VPCONFLICTD = 1014;
	public static final int X86_INS_VPCONFLICTQ = 1015;
	public static final int X86_INS_VPERM2F128 = 1016;
	public static final int X86_INS_VPERM2I128 = 1017;
	public static final int X86_INS_VPERMD = 1018;
	public static final int X86_INS_VPERMI2D = 1019;
	public static final int X86_INS_VPERMI2PD = 1020;
	public static final int X86_INS_VPERMI2PS = 1021;
	public static final int X86_INS_VPERMI2Q = 1022;
	public static final int X86_INS_VPERMIL2PD = 1023;
	public static final int X86_INS_VPERMIL2PS = 1024;
	public static final int X86_INS_VPERMILPD = 1025;
	public static final int X86_INS_VPERMILPS = 1026;
	public static final int X86_INS_VPERMPD = 1027;
	public static final int X86_INS_VPERMPS = 1028;
	public static final int X86_INS_VPERMQ = 1029;
	public static final int X86_INS_VPERMT2D = 1030;
	public static final int X86_INS_VPERMT2PD = 1031;
	public static final int X86_INS_VPERMT2PS = 1032;
	public static final int X86_INS_VPERMT2Q = 1033;
	public static final int X86_INS_VPEXTRB = 1034;
	public static final int X86_INS_VPEXTRD = 1035;
	public static final int X86_INS_VPEXTRQ = 1036;
	public static final int X86_INS_VPEXTRW = 1037;
	public static final int X86_INS_VPGATHERDD = 1038;
	public static final int X86_INS_VPGATHERDQ = 1039;
	public static final int X86_INS_VPGATHERQD = 1040;
	public static final int X86_INS_VPGATHERQQ = 1041;
	public static final int X86_INS_VPHADDBD = 1042;
	public static final int X86_INS_VPHADDBQ = 1043;
	public static final int X86_INS_VPHADDBW = 1044;
	public static final int X86_INS_VPHADDDQ = 1045;
	public static final int X86_INS_VPHADDD = 1046;
	public static final int X86_INS_VPHADDSW = 1047;
	public static final int X86_INS_VPHADDUBD = 1048;
	public static final int X86_INS_VPHADDUBQ = 1049;
	public static final int X86_INS_VPHADDUBW = 1050;
	public static final int X86_INS_VPHADDUDQ = 1051;
	public static final int X86_INS_VPHADDUWD = 1052;
	public static final int X86_INS_VPHADDUWQ = 1053;
	public static final int X86_INS_VPHADDWD = 1054;
	public static final int X86_INS_VPHADDWQ = 1055;
	public static final int X86_INS_VPHADDW = 1056;
	public static final int X86_INS_VPHMINPOSUW = 1057;
	public static final int X86_INS_VPHSUBBW = 1058;
	public static final int X86_INS_VPHSUBDQ = 1059;
	public static final int X86_INS_VPHSUBD = 1060;
	public static final int X86_INS_VPHSUBSW = 1061;
	public static final int X86_INS_VPHSUBWD = 1062;
	public static final int X86_INS_VPHSUBW = 1063;
	public static final int X86_INS_VPINSRB = 1064;
	public static final int X86_INS_VPINSRD = 1065;
	public static final int X86_INS_VPINSRQ = 1066;
	public static final int X86_INS_VPINSRW = 1067;
	public static final int X86_INS_VPLZCNTD = 1068;
	public static final int X86_INS_VPLZCNTQ = 1069;
	public static final int X86_INS_VPMACSDD = 1070;
	public static final int X86_INS_VPMACSDQH = 1071;
	public static final int X86_INS_VPMACSDQL = 1072;
	public static final int X86_INS_VPMACSSDD = 1073;
	public static final int X86_INS_VPMACSSDQH = 1074;
	public static final int X86_INS_VPMACSSDQL = 1075;
	public static final int X86_INS_VPMACSSWD = 1076;
	public static final int X86_INS_VPMACSSWW = 1077;
	public static final int X86_INS_VPMACSWD = 1078;
	public static final int X86_INS_VPMACSWW = 1079;
	public static final int X86_INS_VPMADCSSWD = 1080;
	public static final int X86_INS_VPMADCSWD = 1081;
	public static final int X86_INS_VPMADDUBSW = 1082;
	public static final int X86_INS_VPMADDWD = 1083;
	public static final int X86_INS_VPMASKMOVD = 1084;
	public static final int X86_INS_VPMASKMOVQ = 1085;
	public static final int X86_INS_VPMAXSB = 1086;
	public static final int X86_INS_VPMAXSD = 1087;
	public static final int X86_INS_VPMAXSQ = 1088;
	public static final int X86_INS_VPMAXSW = 1089;
	public static final int X86_INS_VPMAXUB = 1090;
	public static final int X86_INS_VPMAXUD = 1091;
	public static final int X86_INS_VPMAXUQ = 1092;
	public static final int X86_INS_VPMAXUW = 1093;
	public static final int X86_INS_VPMINSB = 1094;
	public static final int X86_INS_VPMINSD = 1095;
	public static final int X86_INS_VPMINSQ = 1096;
	public static final int X86_INS_VPMINSW = 1097;
	public static final int X86_INS_VPMINUB = 1098;
	public static final int X86_INS_VPMINUD = 1099;
	public static final int X86_INS_VPMINUQ = 1100;
	public static final int X86_INS_VPMINUW = 1101;
	public static final int X86_INS_VPMOVDB = 1102;
	public static final int X86_INS_VPMOVDW = 1103;
	public static final int X86_INS_VPMOVMSKB = 1104;
	public static final int X86_INS_VPMOVQB = 1105;
	public static final int X86_INS_VPMOVQD = 1106;
	public static final int X86_INS_VPMOVQW = 1107;
	public static final int X86_INS_VPMOVSDB = 1108;
	public static final int X86_INS_VPMOVSDW = 1109;
	public static final int X86_INS_VPMOVSQB = 1110;
	public static final int X86_INS_VPMOVSQD = 1111;
	public static final int X86_INS_VPMOVSQW = 1112;
	public static final int X86_INS_VPMOVSXBD = 1113;
	public static final int X86_INS_VPMOVSXBQ = 1114;
	public static final int X86_INS_VPMOVSXBW = 1115;
	public static final int X86_INS_VPMOVSXDQ = 1116;
	public static final int X86_INS_VPMOVSXWD = 1117;
	public static final int X86_INS_VPMOVSXWQ = 1118;
	public static final int X86_INS_VPMOVUSDB = 1119;
	public static final int X86_INS_VPMOVUSDW = 1120;
	public static final int X86_INS_VPMOVUSQB = 1121;
	public static final int X86_INS_VPMOVUSQD = 1122;
	public static final int X86_INS_VPMOVUSQW = 1123;
	public static final int X86_INS_VPMOVZXBD = 1124;
	public static final int X86_INS_VPMOVZXBQ = 1125;
	public static final int X86_INS_VPMOVZXBW = 1126;
	public static final int X86_INS_VPMOVZXDQ = 1127;
	public static final int X86_INS_VPMOVZXWD = 1128;
	public static final int X86_INS_VPMOVZXWQ = 1129;
	public static final int X86_INS_VPMULDQ = 1130;
	public static final int X86_INS_VPMULHRSW = 1131;
	public static final int X86_INS_VPMULHUW = 1132;
	public static final int X86_INS_VPMULHW = 1133;
	public static final int X86_INS_VPMULLD = 1134;
	public static final int X86_INS_VPMULLW = 1135;
	public static final int X86_INS_VPMULUDQ = 1136;
	public static final int X86_INS_VPORD = 1137;
	public static final int X86_INS_VPORQ = 1138;
	public static final int X86_INS_VPOR = 1139;
	public static final int X86_INS_VPPERM = 1140;
	public static final int X86_INS_VPROTB = 1141;
	public static final int X86_INS_VPROTD = 1142;
	public static final int X86_INS_VPROTQ = 1143;
	public static final int X86_INS_VPROTW = 1144;
	public static final int X86_INS_VPSADBW = 1145;
	public static final int X86_INS_VPSCATTERDD = 1146;
	public static final int X86_INS_VPSCATTERDQ = 1147;
	public static final int X86_INS_VPSCATTERQD = 1148;
	public static final int X86_INS_VPSCATTERQQ = 1149;
	public static final int X86_INS_VPSHAB = 1150;
	public static final int X86_INS_VPSHAD = 1151;
	public static final int X86_INS_VPSHAQ = 1152;
	public static final int X86_INS_VPSHAW = 1153;
	public static final int X86_INS_VPSHLB = 1154;
	public static final int X86_INS_VPSHLD = 1155;
	public static final int X86_INS_VPSHLQ = 1156;
	public static final int X86_INS_VPSHLW = 1157;
	public static final int X86_INS_VPSHUFB = 1158;
	public static final int X86_INS_VPSHUFD = 1159;
	public static final int X86_INS_VPSHUFHW = 1160;
	public static final int X86_INS_VPSHUFLW = 1161;
	public static final int X86_INS_VPSIGNB = 1162;
	public static final int X86_INS_VPSIGND = 1163;
	public static final int X86_INS_VPSIGNW = 1164;
	public static final int X86_INS_VPSLLDQ = 1165;
	public static final int X86_INS_VPSLLD = 1166;
	public static final int X86_INS_VPSLLQ = 1167;
	public static final int X86_INS_VPSLLVD = 1168;
	public static final int X86_INS_VPSLLVQ = 1169;
	public static final int X86_INS_VPSLLW = 1170;
	public static final int X86_INS_VPSRAD = 1171;
	public static final int X86_INS_VPSRAQ = 1172;
	public static final int X86_INS_VPSRAVD = 1173;
	public static final int X86_INS_VPSRAVQ = 1174;
	public static final int X86_INS_VPSRAW = 1175;
	public static final int X86_INS_VPSRLDQ = 1176;
	public static final int X86_INS_VPSRLD = 1177;
	public static final int X86_INS_VPSRLQ = 1178;
	public static final int X86_INS_VPSRLVD = 1179;
	public static final int X86_INS_VPSRLVQ = 1180;
	public static final int X86_INS_VPSRLW = 1181;
	public static final int X86_INS_VPSUBB = 1182;
	public static final int X86_INS_VPSUBD = 1183;
	public static final int X86_INS_VPSUBQ = 1184;
	public static final int X86_INS_VPSUBSB = 1185;
	public static final int X86_INS_VPSUBSW = 1186;
	public static final int X86_INS_VPSUBUSB = 1187;
	public static final int X86_INS_VPSUBUSW = 1188;
	public static final int X86_INS_VPSUBW = 1189;
	public static final int X86_INS_VPTESTMD = 1190;
	public static final int X86_INS_VPTESTMQ = 1191;
	public static final int X86_INS_VPTESTNMD = 1192;
	public static final int X86_INS_VPTESTNMQ = 1193;
	public static final int X86_INS_VPTEST = 1194;
	public static final int X86_INS_VPUNPCKHBW = 1195;
	public static final int X86_INS_VPUNPCKHDQ = 1196;
	public static final int X86_INS_VPUNPCKHQDQ = 1197;
	public static final int X86_INS_VPUNPCKHWD = 1198;
	public static final int X86_INS_VPUNPCKLBW = 1199;
	public static final int X86_INS_VPUNPCKLDQ = 1200;
	public static final int X86_INS_VPUNPCKLQDQ = 1201;
	public static final int X86_INS_VPUNPCKLWD = 1202;
	public static final int X86_INS_VPXORD = 1203;
	public static final int X86_INS_VPXORQ = 1204;
	public static final int X86_INS_VPXOR = 1205;
	public static final int X86_INS_VRCP14PD = 1206;
	public static final int X86_INS_VRCP14PS = 1207;
	public static final int X86_INS_VRCP14SD = 1208;
	public static final int X86_INS_VRCP14SS = 1209;
	public static final int X86_INS_VRCP28PD = 1210;
	public static final int X86_INS_VRCP28PS = 1211;
	public static final int X86_INS_VRCP28SD = 1212;
	public static final int X86_INS_VRCP28SS = 1213;
	public static final int X86_INS_VRCPPS = 1214;
	public static final int X86_INS_VRCPSS = 1215;
	public static final int X86_INS_VRNDSCALEPD = 1216;
	public static final int X86_INS_VRNDSCALEPS = 1217;
	public static final int X86_INS_VRNDSCALESD = 1218;
	public static final int X86_INS_VRNDSCALESS = 1219;
	public static final int X86_INS_VROUNDPD = 1220;
	public static final int X86_INS_VROUNDPS = 1221;
	public static final int X86_INS_VROUNDSD = 1222;
	public static final int X86_INS_VROUNDSS = 1223;
	public static final int X86_INS_VRSQRT14PD = 1224;
	public static final int X86_INS_VRSQRT14PS = 1225;
	public static final int X86_INS_VRSQRT14SD = 1226;
	public static final int X86_INS_VRSQRT14SS = 1227;
	public static final int X86_INS_VRSQRT28PD = 1228;
	public static final int X86_INS_VRSQRT28PS = 1229;
	public static final int X86_INS_VRSQRT28SD = 1230;
	public static final int X86_INS_VRSQRT28SS = 1231;
	public static final int X86_INS_VRSQRTPS = 1232;
	public static final int X86_INS_VRSQRTSS = 1233;
	public static final int X86_INS_VSCATTERDPD = 1234;
	public static final int X86_INS_VSCATTERDPS = 1235;
	public static final int X86_INS_VSCATTERPF0DPD = 1236;
	public static final int X86_INS_VSCATTERPF0DPS = 1237;
	public static final int X86_INS_VSCATTERPF0QPD = 1238;
	public static final int X86_INS_VSCATTERPF0QPS = 1239;
	public static final int X86_INS_VSCATTERPF1DPD = 1240;
	public static final int X86_INS_VSCATTERPF1DPS = 1241;
	public static final int X86_INS_VSCATTERPF1QPD = 1242;
	public static final int X86_INS_VSCATTERPF1QPS = 1243;
	public static final int X86_INS_VSCATTERQPD = 1244;
	public static final int X86_INS_VSCATTERQPS = 1245;
	public static final int X86_INS_VSHUFPD = 1246;
	public static final int X86_INS_VSHUFPS = 1247;
	public static final int X86_INS_VSQRTPD = 1248;
	public static final int X86_INS_VSQRTPS = 1249;
	public static final int X86_INS_VSQRTSD = 1250;
	public static final int X86_INS_VSQRTSS = 1251;
	public static final int X86_INS_VSTMXCSR = 1252;
	public static final int X86_INS_VSUBPD = 1253;
	public static final int X86_INS_VSUBPS = 1254;
	public static final int X86_INS_VSUBSD = 1255;
	public static final int X86_INS_VSUBSS = 1256;
	public static final int X86_INS_VTESTPD = 1257;
	public static final int X86_INS_VTESTPS = 1258;
	public static final int X86_INS_VUNPCKHPD = 1259;
	public static final int X86_INS_VUNPCKHPS = 1260;
	public static final int X86_INS_VUNPCKLPD = 1261;
	public static final int X86_INS_VUNPCKLPS = 1262;
	public static final int X86_INS_VZEROALL = 1263;
	public static final int X86_INS_VZEROUPPER = 1264;
	public static final int X86_INS_WAIT = 1265;
	public static final int X86_INS_WBINVD = 1266;
	public static final int X86_INS_WRFSBASE = 1267;
	public static final int X86_INS_WRGSBASE = 1268;
	public static final int X86_INS_WRMSR = 1269;
	public static final int X86_INS_XABORT = 1270;
	public static final int X86_INS_XACQUIRE = 1271;
	public static final int X86_INS_XBEGIN = 1272;
	public static final int X86_INS_XCHG = 1273;
	public static final int X86_INS_FXCH = 1274;
	public static final int X86_INS_XCRYPTCBC = 1275;
	public static final int X86_INS_XCRYPTCFB = 1276;
	public static final int X86_INS_XCRYPTCTR = 1277;
	public static final int X86_INS_XCRYPTECB = 1278;
	public static final int X86_INS_XCRYPTOFB = 1279;
	public static final int X86_INS_XEND = 1280;
	public static final int X86_INS_XGETBV = 1281;
	public static final int X86_INS_XLATB = 1282;
	public static final int X86_INS_XRELEASE = 1283;
	public static final int X86_INS_XRSTOR = 1284;
	public static final int X86_INS_XRSTOR64 = 1285;
	public static final int X86_INS_XSAVE = 1286;
	public static final int X86_INS_XSAVE64 = 1287;
	public static final int X86_INS_XSAVEOPT = 1288;
	public static final int X86_INS_XSAVEOPT64 = 1289;
	public static final int X86_INS_XSETBV = 1290;
	public static final int X86_INS_XSHA1 = 1291;
	public static final int X86_INS_XSHA256 = 1292;
	public static final int X86_INS_XSTORE = 1293;
	public static final int X86_INS_XTEST = 1294;
	public static final int X86_INS_ENDING = 1295;

	// Group of X86 instructions

	public static final int X86_GRP_INVALID = 0;

	// Generic groups
	public static final int X86_GRP_JUMP = 1;
	public static final int X86_GRP_CALL = 2;
	public static final int X86_GRP_RET = 3;
	public static final int X86_GRP_INT = 4;
	public static final int X86_GRP_IRET = 5;

	// Architecture-specific groups
	public static final int X86_GRP_VM = 128;
	public static final int X86_GRP_3DNOW = 129;
	public static final int X86_GRP_AES = 130;
	public static final int X86_GRP_ADX = 131;
	public static final int X86_GRP_AVX = 132;
	public static final int X86_GRP_AVX2 = 133;
	public static final int X86_GRP_AVX512 = 134;
	public static final int X86_GRP_BMI = 135;
	public static final int X86_GRP_BMI2 = 136;
	public static final int X86_GRP_CMOV = 137;
	public static final int X86_GRP_F16C = 138;
	public static final int X86_GRP_FMA = 139;
	public static final int X86_GRP_FMA4 = 140;
	public static final int X86_GRP_FSGSBASE = 141;
	public static final int X86_GRP_HLE = 142;
	public static final int X86_GRP_MMX = 143;
	public static final int X86_GRP_MODE32 = 144;
	public static final int X86_GRP_MODE64 = 145;
	public static final int X86_GRP_RTM = 146;
	public static final int X86_GRP_SHA = 147;
	public static final int X86_GRP_SSE1 = 148;
	public static final int X86_GRP_SSE2 = 149;
	public static final int X86_GRP_SSE3 = 150;
	public static final int X86_GRP_SSE41 = 151;
	public static final int X86_GRP_SSE42 = 152;
	public static final int X86_GRP_SSE4A = 153;
	public static final int X86_GRP_SSSE3 = 154;
	public static final int X86_GRP_PCLMUL = 155;
	public static final int X86_GRP_XOP = 156;
	public static final int X86_GRP_CDI = 157;
	public static final int X86_GRP_ERI = 158;
	public static final int X86_GRP_TBM = 159;
	public static final int X86_GRP_16BITMODE = 160;
	public static final int X86_GRP_NOT64BITMODE = 161;
	public static final int X86_GRP_SGX = 162;
	public static final int X86_GRP_DQI = 163;
	public static final int X86_GRP_BWI = 164;
	public static final int X86_GRP_PFI = 165;
	public static final int X86_GRP_VLX = 166;
	public static final int X86_GRP_SMAP = 167;
	public static final int X86_GRP_NOVLX = 168;
	public static final int X86_GRP_ENDING = 169;
}