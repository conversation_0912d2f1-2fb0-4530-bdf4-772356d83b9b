// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class Ppc_const {

	// PPC branch codes for some branch instructions

	public static final int PPC_BC_INVALID = 0;
	public static final int PPC_BC_LT = (0<<5)|12;
	public static final int PPC_BC_LE = (1<<5)|4;
	public static final int PPC_BC_EQ = (2<<5)|12;
	public static final int PPC_BC_GE = (0<<5)|4;
	public static final int PPC_BC_GT = (1<<5)|12;
	public static final int PPC_BC_NE = (2<<5)|4;
	public static final int PPC_BC_UN = (3<<5)|12;
	public static final int PPC_BC_NU = (3<<5)|4;
	public static final int PPC_BC_SO = (4<<5)|12;
	public static final int PPC_BC_NS = (4<<5)|4;

	// PPC branch hint for some branch instructions

	public static final int PPC_BH_INVALID = 0;
	public static final int PPC_BH_PLUS = 1;
	public static final int PPC_BH_MINUS = 2;

	// Operand type for instruction's operands

	public static final int PPC_OP_INVALID = 0;
	public static final int PPC_OP_REG = 1;
	public static final int PPC_OP_IMM = 2;
	public static final int PPC_OP_MEM = 3;
	public static final int PPC_OP_CRX = 64;

	// PPC registers

	public static final int PPC_REG_INVALID = 0;
	public static final int PPC_REG_CARRY = 1;
	public static final int PPC_REG_CC = 2;
	public static final int PPC_REG_CR0 = 3;
	public static final int PPC_REG_CR1 = 4;
	public static final int PPC_REG_CR2 = 5;
	public static final int PPC_REG_CR3 = 6;
	public static final int PPC_REG_CR4 = 7;
	public static final int PPC_REG_CR5 = 8;
	public static final int PPC_REG_CR6 = 9;
	public static final int PPC_REG_CR7 = 10;
	public static final int PPC_REG_CTR = 11;
	public static final int PPC_REG_F0 = 12;
	public static final int PPC_REG_F1 = 13;
	public static final int PPC_REG_F2 = 14;
	public static final int PPC_REG_F3 = 15;
	public static final int PPC_REG_F4 = 16;
	public static final int PPC_REG_F5 = 17;
	public static final int PPC_REG_F6 = 18;
	public static final int PPC_REG_F7 = 19;
	public static final int PPC_REG_F8 = 20;
	public static final int PPC_REG_F9 = 21;
	public static final int PPC_REG_F10 = 22;
	public static final int PPC_REG_F11 = 23;
	public static final int PPC_REG_F12 = 24;
	public static final int PPC_REG_F13 = 25;
	public static final int PPC_REG_F14 = 26;
	public static final int PPC_REG_F15 = 27;
	public static final int PPC_REG_F16 = 28;
	public static final int PPC_REG_F17 = 29;
	public static final int PPC_REG_F18 = 30;
	public static final int PPC_REG_F19 = 31;
	public static final int PPC_REG_F20 = 32;
	public static final int PPC_REG_F21 = 33;
	public static final int PPC_REG_F22 = 34;
	public static final int PPC_REG_F23 = 35;
	public static final int PPC_REG_F24 = 36;
	public static final int PPC_REG_F25 = 37;
	public static final int PPC_REG_F26 = 38;
	public static final int PPC_REG_F27 = 39;
	public static final int PPC_REG_F28 = 40;
	public static final int PPC_REG_F29 = 41;
	public static final int PPC_REG_F30 = 42;
	public static final int PPC_REG_F31 = 43;
	public static final int PPC_REG_LR = 44;
	public static final int PPC_REG_R0 = 45;
	public static final int PPC_REG_R1 = 46;
	public static final int PPC_REG_R2 = 47;
	public static final int PPC_REG_R3 = 48;
	public static final int PPC_REG_R4 = 49;
	public static final int PPC_REG_R5 = 50;
	public static final int PPC_REG_R6 = 51;
	public static final int PPC_REG_R7 = 52;
	public static final int PPC_REG_R8 = 53;
	public static final int PPC_REG_R9 = 54;
	public static final int PPC_REG_R10 = 55;
	public static final int PPC_REG_R11 = 56;
	public static final int PPC_REG_R12 = 57;
	public static final int PPC_REG_R13 = 58;
	public static final int PPC_REG_R14 = 59;
	public static final int PPC_REG_R15 = 60;
	public static final int PPC_REG_R16 = 61;
	public static final int PPC_REG_R17 = 62;
	public static final int PPC_REG_R18 = 63;
	public static final int PPC_REG_R19 = 64;
	public static final int PPC_REG_R20 = 65;
	public static final int PPC_REG_R21 = 66;
	public static final int PPC_REG_R22 = 67;
	public static final int PPC_REG_R23 = 68;
	public static final int PPC_REG_R24 = 69;
	public static final int PPC_REG_R25 = 70;
	public static final int PPC_REG_R26 = 71;
	public static final int PPC_REG_R27 = 72;
	public static final int PPC_REG_R28 = 73;
	public static final int PPC_REG_R29 = 74;
	public static final int PPC_REG_R30 = 75;
	public static final int PPC_REG_R31 = 76;
	public static final int PPC_REG_V0 = 77;
	public static final int PPC_REG_V1 = 78;
	public static final int PPC_REG_V2 = 79;
	public static final int PPC_REG_V3 = 80;
	public static final int PPC_REG_V4 = 81;
	public static final int PPC_REG_V5 = 82;
	public static final int PPC_REG_V6 = 83;
	public static final int PPC_REG_V7 = 84;
	public static final int PPC_REG_V8 = 85;
	public static final int PPC_REG_V9 = 86;
	public static final int PPC_REG_V10 = 87;
	public static final int PPC_REG_V11 = 88;
	public static final int PPC_REG_V12 = 89;
	public static final int PPC_REG_V13 = 90;
	public static final int PPC_REG_V14 = 91;
	public static final int PPC_REG_V15 = 92;
	public static final int PPC_REG_V16 = 93;
	public static final int PPC_REG_V17 = 94;
	public static final int PPC_REG_V18 = 95;
	public static final int PPC_REG_V19 = 96;
	public static final int PPC_REG_V20 = 97;
	public static final int PPC_REG_V21 = 98;
	public static final int PPC_REG_V22 = 99;
	public static final int PPC_REG_V23 = 100;
	public static final int PPC_REG_V24 = 101;
	public static final int PPC_REG_V25 = 102;
	public static final int PPC_REG_V26 = 103;
	public static final int PPC_REG_V27 = 104;
	public static final int PPC_REG_V28 = 105;
	public static final int PPC_REG_V29 = 106;
	public static final int PPC_REG_V30 = 107;
	public static final int PPC_REG_V31 = 108;
	public static final int PPC_REG_VRSAVE = 109;
	public static final int PPC_REG_VS0 = 110;
	public static final int PPC_REG_VS1 = 111;
	public static final int PPC_REG_VS2 = 112;
	public static final int PPC_REG_VS3 = 113;
	public static final int PPC_REG_VS4 = 114;
	public static final int PPC_REG_VS5 = 115;
	public static final int PPC_REG_VS6 = 116;
	public static final int PPC_REG_VS7 = 117;
	public static final int PPC_REG_VS8 = 118;
	public static final int PPC_REG_VS9 = 119;
	public static final int PPC_REG_VS10 = 120;
	public static final int PPC_REG_VS11 = 121;
	public static final int PPC_REG_VS12 = 122;
	public static final int PPC_REG_VS13 = 123;
	public static final int PPC_REG_VS14 = 124;
	public static final int PPC_REG_VS15 = 125;
	public static final int PPC_REG_VS16 = 126;
	public static final int PPC_REG_VS17 = 127;
	public static final int PPC_REG_VS18 = 128;
	public static final int PPC_REG_VS19 = 129;
	public static final int PPC_REG_VS20 = 130;
	public static final int PPC_REG_VS21 = 131;
	public static final int PPC_REG_VS22 = 132;
	public static final int PPC_REG_VS23 = 133;
	public static final int PPC_REG_VS24 = 134;
	public static final int PPC_REG_VS25 = 135;
	public static final int PPC_REG_VS26 = 136;
	public static final int PPC_REG_VS27 = 137;
	public static final int PPC_REG_VS28 = 138;
	public static final int PPC_REG_VS29 = 139;
	public static final int PPC_REG_VS30 = 140;
	public static final int PPC_REG_VS31 = 141;
	public static final int PPC_REG_VS32 = 142;
	public static final int PPC_REG_VS33 = 143;
	public static final int PPC_REG_VS34 = 144;
	public static final int PPC_REG_VS35 = 145;
	public static final int PPC_REG_VS36 = 146;
	public static final int PPC_REG_VS37 = 147;
	public static final int PPC_REG_VS38 = 148;
	public static final int PPC_REG_VS39 = 149;
	public static final int PPC_REG_VS40 = 150;
	public static final int PPC_REG_VS41 = 151;
	public static final int PPC_REG_VS42 = 152;
	public static final int PPC_REG_VS43 = 153;
	public static final int PPC_REG_VS44 = 154;
	public static final int PPC_REG_VS45 = 155;
	public static final int PPC_REG_VS46 = 156;
	public static final int PPC_REG_VS47 = 157;
	public static final int PPC_REG_VS48 = 158;
	public static final int PPC_REG_VS49 = 159;
	public static final int PPC_REG_VS50 = 160;
	public static final int PPC_REG_VS51 = 161;
	public static final int PPC_REG_VS52 = 162;
	public static final int PPC_REG_VS53 = 163;
	public static final int PPC_REG_VS54 = 164;
	public static final int PPC_REG_VS55 = 165;
	public static final int PPC_REG_VS56 = 166;
	public static final int PPC_REG_VS57 = 167;
	public static final int PPC_REG_VS58 = 168;
	public static final int PPC_REG_VS59 = 169;
	public static final int PPC_REG_VS60 = 170;
	public static final int PPC_REG_VS61 = 171;
	public static final int PPC_REG_VS62 = 172;
	public static final int PPC_REG_VS63 = 173;
	public static final int PPC_REG_RM = 174;
	public static final int PPC_REG_CTR8 = 175;
	public static final int PPC_REG_LR8 = 176;
	public static final int PPC_REG_CR1EQ = 177;
	public static final int PPC_REG_ENDING = 178;

	// PPC instruction

	public static final int PPC_INS_INVALID = 0;
	public static final int PPC_INS_ADD = 1;
	public static final int PPC_INS_ADDC = 2;
	public static final int PPC_INS_ADDE = 3;
	public static final int PPC_INS_ADDI = 4;
	public static final int PPC_INS_ADDIC = 5;
	public static final int PPC_INS_ADDIS = 6;
	public static final int PPC_INS_ADDME = 7;
	public static final int PPC_INS_ADDZE = 8;
	public static final int PPC_INS_AND = 9;
	public static final int PPC_INS_ANDC = 10;
	public static final int PPC_INS_ANDIS = 11;
	public static final int PPC_INS_ANDI = 12;
	public static final int PPC_INS_B = 13;
	public static final int PPC_INS_BA = 14;
	public static final int PPC_INS_BC = 15;
	public static final int PPC_INS_BCCTR = 16;
	public static final int PPC_INS_BCCTRL = 17;
	public static final int PPC_INS_BCL = 18;
	public static final int PPC_INS_BCLR = 19;
	public static final int PPC_INS_BCLRL = 20;
	public static final int PPC_INS_BCTR = 21;
	public static final int PPC_INS_BCTRL = 22;
	public static final int PPC_INS_BDNZ = 23;
	public static final int PPC_INS_BDNZA = 24;
	public static final int PPC_INS_BDNZL = 25;
	public static final int PPC_INS_BDNZLA = 26;
	public static final int PPC_INS_BDNZLR = 27;
	public static final int PPC_INS_BDNZLRL = 28;
	public static final int PPC_INS_BDZ = 29;
	public static final int PPC_INS_BDZA = 30;
	public static final int PPC_INS_BDZL = 31;
	public static final int PPC_INS_BDZLA = 32;
	public static final int PPC_INS_BDZLR = 33;
	public static final int PPC_INS_BDZLRL = 34;
	public static final int PPC_INS_BL = 35;
	public static final int PPC_INS_BLA = 36;
	public static final int PPC_INS_BLR = 37;
	public static final int PPC_INS_BLRL = 38;
	public static final int PPC_INS_BRINC = 39;
	public static final int PPC_INS_CMPD = 40;
	public static final int PPC_INS_CMPDI = 41;
	public static final int PPC_INS_CMPLD = 42;
	public static final int PPC_INS_CMPLDI = 43;
	public static final int PPC_INS_CMPLW = 44;
	public static final int PPC_INS_CMPLWI = 45;
	public static final int PPC_INS_CMPW = 46;
	public static final int PPC_INS_CMPWI = 47;
	public static final int PPC_INS_CNTLZD = 48;
	public static final int PPC_INS_CNTLZW = 49;
	public static final int PPC_INS_CREQV = 50;
	public static final int PPC_INS_CRXOR = 51;
	public static final int PPC_INS_CRAND = 52;
	public static final int PPC_INS_CRANDC = 53;
	public static final int PPC_INS_CRNAND = 54;
	public static final int PPC_INS_CRNOR = 55;
	public static final int PPC_INS_CROR = 56;
	public static final int PPC_INS_CRORC = 57;
	public static final int PPC_INS_DCBA = 58;
	public static final int PPC_INS_DCBF = 59;
	public static final int PPC_INS_DCBI = 60;
	public static final int PPC_INS_DCBST = 61;
	public static final int PPC_INS_DCBT = 62;
	public static final int PPC_INS_DCBTST = 63;
	public static final int PPC_INS_DCBZ = 64;
	public static final int PPC_INS_DCBZL = 65;
	public static final int PPC_INS_DCCCI = 66;
	public static final int PPC_INS_DIVD = 67;
	public static final int PPC_INS_DIVDU = 68;
	public static final int PPC_INS_DIVW = 69;
	public static final int PPC_INS_DIVWU = 70;
	public static final int PPC_INS_DSS = 71;
	public static final int PPC_INS_DSSALL = 72;
	public static final int PPC_INS_DST = 73;
	public static final int PPC_INS_DSTST = 74;
	public static final int PPC_INS_DSTSTT = 75;
	public static final int PPC_INS_DSTT = 76;
	public static final int PPC_INS_EIEIO = 77;
	public static final int PPC_INS_EQV = 78;
	public static final int PPC_INS_EVABS = 79;
	public static final int PPC_INS_EVADDIW = 80;
	public static final int PPC_INS_EVADDSMIAAW = 81;
	public static final int PPC_INS_EVADDSSIAAW = 82;
	public static final int PPC_INS_EVADDUMIAAW = 83;
	public static final int PPC_INS_EVADDUSIAAW = 84;
	public static final int PPC_INS_EVADDW = 85;
	public static final int PPC_INS_EVAND = 86;
	public static final int PPC_INS_EVANDC = 87;
	public static final int PPC_INS_EVCMPEQ = 88;
	public static final int PPC_INS_EVCMPGTS = 89;
	public static final int PPC_INS_EVCMPGTU = 90;
	public static final int PPC_INS_EVCMPLTS = 91;
	public static final int PPC_INS_EVCMPLTU = 92;
	public static final int PPC_INS_EVCNTLSW = 93;
	public static final int PPC_INS_EVCNTLZW = 94;
	public static final int PPC_INS_EVDIVWS = 95;
	public static final int PPC_INS_EVDIVWU = 96;
	public static final int PPC_INS_EVEQV = 97;
	public static final int PPC_INS_EVEXTSB = 98;
	public static final int PPC_INS_EVEXTSH = 99;
	public static final int PPC_INS_EVLDD = 100;
	public static final int PPC_INS_EVLDDX = 101;
	public static final int PPC_INS_EVLDH = 102;
	public static final int PPC_INS_EVLDHX = 103;
	public static final int PPC_INS_EVLDW = 104;
	public static final int PPC_INS_EVLDWX = 105;
	public static final int PPC_INS_EVLHHESPLAT = 106;
	public static final int PPC_INS_EVLHHESPLATX = 107;
	public static final int PPC_INS_EVLHHOSSPLAT = 108;
	public static final int PPC_INS_EVLHHOSSPLATX = 109;
	public static final int PPC_INS_EVLHHOUSPLAT = 110;
	public static final int PPC_INS_EVLHHOUSPLATX = 111;
	public static final int PPC_INS_EVLWHE = 112;
	public static final int PPC_INS_EVLWHEX = 113;
	public static final int PPC_INS_EVLWHOS = 114;
	public static final int PPC_INS_EVLWHOSX = 115;
	public static final int PPC_INS_EVLWHOU = 116;
	public static final int PPC_INS_EVLWHOUX = 117;
	public static final int PPC_INS_EVLWHSPLAT = 118;
	public static final int PPC_INS_EVLWHSPLATX = 119;
	public static final int PPC_INS_EVLWWSPLAT = 120;
	public static final int PPC_INS_EVLWWSPLATX = 121;
	public static final int PPC_INS_EVMERGEHI = 122;
	public static final int PPC_INS_EVMERGEHILO = 123;
	public static final int PPC_INS_EVMERGELO = 124;
	public static final int PPC_INS_EVMERGELOHI = 125;
	public static final int PPC_INS_EVMHEGSMFAA = 126;
	public static final int PPC_INS_EVMHEGSMFAN = 127;
	public static final int PPC_INS_EVMHEGSMIAA = 128;
	public static final int PPC_INS_EVMHEGSMIAN = 129;
	public static final int PPC_INS_EVMHEGUMIAA = 130;
	public static final int PPC_INS_EVMHEGUMIAN = 131;
	public static final int PPC_INS_EVMHESMF = 132;
	public static final int PPC_INS_EVMHESMFA = 133;
	public static final int PPC_INS_EVMHESMFAAW = 134;
	public static final int PPC_INS_EVMHESMFANW = 135;
	public static final int PPC_INS_EVMHESMI = 136;
	public static final int PPC_INS_EVMHESMIA = 137;
	public static final int PPC_INS_EVMHESMIAAW = 138;
	public static final int PPC_INS_EVMHESMIANW = 139;
	public static final int PPC_INS_EVMHESSF = 140;
	public static final int PPC_INS_EVMHESSFA = 141;
	public static final int PPC_INS_EVMHESSFAAW = 142;
	public static final int PPC_INS_EVMHESSFANW = 143;
	public static final int PPC_INS_EVMHESSIAAW = 144;
	public static final int PPC_INS_EVMHESSIANW = 145;
	public static final int PPC_INS_EVMHEUMI = 146;
	public static final int PPC_INS_EVMHEUMIA = 147;
	public static final int PPC_INS_EVMHEUMIAAW = 148;
	public static final int PPC_INS_EVMHEUMIANW = 149;
	public static final int PPC_INS_EVMHEUSIAAW = 150;
	public static final int PPC_INS_EVMHEUSIANW = 151;
	public static final int PPC_INS_EVMHOGSMFAA = 152;
	public static final int PPC_INS_EVMHOGSMFAN = 153;
	public static final int PPC_INS_EVMHOGSMIAA = 154;
	public static final int PPC_INS_EVMHOGSMIAN = 155;
	public static final int PPC_INS_EVMHOGUMIAA = 156;
	public static final int PPC_INS_EVMHOGUMIAN = 157;
	public static final int PPC_INS_EVMHOSMF = 158;
	public static final int PPC_INS_EVMHOSMFA = 159;
	public static final int PPC_INS_EVMHOSMFAAW = 160;
	public static final int PPC_INS_EVMHOSMFANW = 161;
	public static final int PPC_INS_EVMHOSMI = 162;
	public static final int PPC_INS_EVMHOSMIA = 163;
	public static final int PPC_INS_EVMHOSMIAAW = 164;
	public static final int PPC_INS_EVMHOSMIANW = 165;
	public static final int PPC_INS_EVMHOSSF = 166;
	public static final int PPC_INS_EVMHOSSFA = 167;
	public static final int PPC_INS_EVMHOSSFAAW = 168;
	public static final int PPC_INS_EVMHOSSFANW = 169;
	public static final int PPC_INS_EVMHOSSIAAW = 170;
	public static final int PPC_INS_EVMHOSSIANW = 171;
	public static final int PPC_INS_EVMHOUMI = 172;
	public static final int PPC_INS_EVMHOUMIA = 173;
	public static final int PPC_INS_EVMHOUMIAAW = 174;
	public static final int PPC_INS_EVMHOUMIANW = 175;
	public static final int PPC_INS_EVMHOUSIAAW = 176;
	public static final int PPC_INS_EVMHOUSIANW = 177;
	public static final int PPC_INS_EVMRA = 178;
	public static final int PPC_INS_EVMWHSMF = 179;
	public static final int PPC_INS_EVMWHSMFA = 180;
	public static final int PPC_INS_EVMWHSMI = 181;
	public static final int PPC_INS_EVMWHSMIA = 182;
	public static final int PPC_INS_EVMWHSSF = 183;
	public static final int PPC_INS_EVMWHSSFA = 184;
	public static final int PPC_INS_EVMWHUMI = 185;
	public static final int PPC_INS_EVMWHUMIA = 186;
	public static final int PPC_INS_EVMWLSMIAAW = 187;
	public static final int PPC_INS_EVMWLSMIANW = 188;
	public static final int PPC_INS_EVMWLSSIAAW = 189;
	public static final int PPC_INS_EVMWLSSIANW = 190;
	public static final int PPC_INS_EVMWLUMI = 191;
	public static final int PPC_INS_EVMWLUMIA = 192;
	public static final int PPC_INS_EVMWLUMIAAW = 193;
	public static final int PPC_INS_EVMWLUMIANW = 194;
	public static final int PPC_INS_EVMWLUSIAAW = 195;
	public static final int PPC_INS_EVMWLUSIANW = 196;
	public static final int PPC_INS_EVMWSMF = 197;
	public static final int PPC_INS_EVMWSMFA = 198;
	public static final int PPC_INS_EVMWSMFAA = 199;
	public static final int PPC_INS_EVMWSMFAN = 200;
	public static final int PPC_INS_EVMWSMI = 201;
	public static final int PPC_INS_EVMWSMIA = 202;
	public static final int PPC_INS_EVMWSMIAA = 203;
	public static final int PPC_INS_EVMWSMIAN = 204;
	public static final int PPC_INS_EVMWSSF = 205;
	public static final int PPC_INS_EVMWSSFA = 206;
	public static final int PPC_INS_EVMWSSFAA = 207;
	public static final int PPC_INS_EVMWSSFAN = 208;
	public static final int PPC_INS_EVMWUMI = 209;
	public static final int PPC_INS_EVMWUMIA = 210;
	public static final int PPC_INS_EVMWUMIAA = 211;
	public static final int PPC_INS_EVMWUMIAN = 212;
	public static final int PPC_INS_EVNAND = 213;
	public static final int PPC_INS_EVNEG = 214;
	public static final int PPC_INS_EVNOR = 215;
	public static final int PPC_INS_EVOR = 216;
	public static final int PPC_INS_EVORC = 217;
	public static final int PPC_INS_EVRLW = 218;
	public static final int PPC_INS_EVRLWI = 219;
	public static final int PPC_INS_EVRNDW = 220;
	public static final int PPC_INS_EVSLW = 221;
	public static final int PPC_INS_EVSLWI = 222;
	public static final int PPC_INS_EVSPLATFI = 223;
	public static final int PPC_INS_EVSPLATI = 224;
	public static final int PPC_INS_EVSRWIS = 225;
	public static final int PPC_INS_EVSRWIU = 226;
	public static final int PPC_INS_EVSRWS = 227;
	public static final int PPC_INS_EVSRWU = 228;
	public static final int PPC_INS_EVSTDD = 229;
	public static final int PPC_INS_EVSTDDX = 230;
	public static final int PPC_INS_EVSTDH = 231;
	public static final int PPC_INS_EVSTDHX = 232;
	public static final int PPC_INS_EVSTDW = 233;
	public static final int PPC_INS_EVSTDWX = 234;
	public static final int PPC_INS_EVSTWHE = 235;
	public static final int PPC_INS_EVSTWHEX = 236;
	public static final int PPC_INS_EVSTWHO = 237;
	public static final int PPC_INS_EVSTWHOX = 238;
	public static final int PPC_INS_EVSTWWE = 239;
	public static final int PPC_INS_EVSTWWEX = 240;
	public static final int PPC_INS_EVSTWWO = 241;
	public static final int PPC_INS_EVSTWWOX = 242;
	public static final int PPC_INS_EVSUBFSMIAAW = 243;
	public static final int PPC_INS_EVSUBFSSIAAW = 244;
	public static final int PPC_INS_EVSUBFUMIAAW = 245;
	public static final int PPC_INS_EVSUBFUSIAAW = 246;
	public static final int PPC_INS_EVSUBFW = 247;
	public static final int PPC_INS_EVSUBIFW = 248;
	public static final int PPC_INS_EVXOR = 249;
	public static final int PPC_INS_EXTSB = 250;
	public static final int PPC_INS_EXTSH = 251;
	public static final int PPC_INS_EXTSW = 252;
	public static final int PPC_INS_FABS = 253;
	public static final int PPC_INS_FADD = 254;
	public static final int PPC_INS_FADDS = 255;
	public static final int PPC_INS_FCFID = 256;
	public static final int PPC_INS_FCFIDS = 257;
	public static final int PPC_INS_FCFIDU = 258;
	public static final int PPC_INS_FCFIDUS = 259;
	public static final int PPC_INS_FCMPU = 260;
	public static final int PPC_INS_FCPSGN = 261;
	public static final int PPC_INS_FCTID = 262;
	public static final int PPC_INS_FCTIDUZ = 263;
	public static final int PPC_INS_FCTIDZ = 264;
	public static final int PPC_INS_FCTIW = 265;
	public static final int PPC_INS_FCTIWUZ = 266;
	public static final int PPC_INS_FCTIWZ = 267;
	public static final int PPC_INS_FDIV = 268;
	public static final int PPC_INS_FDIVS = 269;
	public static final int PPC_INS_FMADD = 270;
	public static final int PPC_INS_FMADDS = 271;
	public static final int PPC_INS_FMR = 272;
	public static final int PPC_INS_FMSUB = 273;
	public static final int PPC_INS_FMSUBS = 274;
	public static final int PPC_INS_FMUL = 275;
	public static final int PPC_INS_FMULS = 276;
	public static final int PPC_INS_FNABS = 277;
	public static final int PPC_INS_FNEG = 278;
	public static final int PPC_INS_FNMADD = 279;
	public static final int PPC_INS_FNMADDS = 280;
	public static final int PPC_INS_FNMSUB = 281;
	public static final int PPC_INS_FNMSUBS = 282;
	public static final int PPC_INS_FRE = 283;
	public static final int PPC_INS_FRES = 284;
	public static final int PPC_INS_FRIM = 285;
	public static final int PPC_INS_FRIN = 286;
	public static final int PPC_INS_FRIP = 287;
	public static final int PPC_INS_FRIZ = 288;
	public static final int PPC_INS_FRSP = 289;
	public static final int PPC_INS_FRSQRTE = 290;
	public static final int PPC_INS_FRSQRTES = 291;
	public static final int PPC_INS_FSEL = 292;
	public static final int PPC_INS_FSQRT = 293;
	public static final int PPC_INS_FSQRTS = 294;
	public static final int PPC_INS_FSUB = 295;
	public static final int PPC_INS_FSUBS = 296;
	public static final int PPC_INS_ICBI = 297;
	public static final int PPC_INS_ICCCI = 298;
	public static final int PPC_INS_ISEL = 299;
	public static final int PPC_INS_ISYNC = 300;
	public static final int PPC_INS_LA = 301;
	public static final int PPC_INS_LBZ = 302;
	public static final int PPC_INS_LBZU = 303;
	public static final int PPC_INS_LBZUX = 304;
	public static final int PPC_INS_LBZX = 305;
	public static final int PPC_INS_LD = 306;
	public static final int PPC_INS_LDARX = 307;
	public static final int PPC_INS_LDBRX = 308;
	public static final int PPC_INS_LDU = 309;
	public static final int PPC_INS_LDUX = 310;
	public static final int PPC_INS_LDX = 311;
	public static final int PPC_INS_LFD = 312;
	public static final int PPC_INS_LFDU = 313;
	public static final int PPC_INS_LFDUX = 314;
	public static final int PPC_INS_LFDX = 315;
	public static final int PPC_INS_LFIWAX = 316;
	public static final int PPC_INS_LFIWZX = 317;
	public static final int PPC_INS_LFS = 318;
	public static final int PPC_INS_LFSU = 319;
	public static final int PPC_INS_LFSUX = 320;
	public static final int PPC_INS_LFSX = 321;
	public static final int PPC_INS_LHA = 322;
	public static final int PPC_INS_LHAU = 323;
	public static final int PPC_INS_LHAUX = 324;
	public static final int PPC_INS_LHAX = 325;
	public static final int PPC_INS_LHBRX = 326;
	public static final int PPC_INS_LHZ = 327;
	public static final int PPC_INS_LHZU = 328;
	public static final int PPC_INS_LHZUX = 329;
	public static final int PPC_INS_LHZX = 330;
	public static final int PPC_INS_LI = 331;
	public static final int PPC_INS_LIS = 332;
	public static final int PPC_INS_LMW = 333;
	public static final int PPC_INS_LSWI = 334;
	public static final int PPC_INS_LVEBX = 335;
	public static final int PPC_INS_LVEHX = 336;
	public static final int PPC_INS_LVEWX = 337;
	public static final int PPC_INS_LVSL = 338;
	public static final int PPC_INS_LVSR = 339;
	public static final int PPC_INS_LVX = 340;
	public static final int PPC_INS_LVXL = 341;
	public static final int PPC_INS_LWA = 342;
	public static final int PPC_INS_LWARX = 343;
	public static final int PPC_INS_LWAUX = 344;
	public static final int PPC_INS_LWAX = 345;
	public static final int PPC_INS_LWBRX = 346;
	public static final int PPC_INS_LWZ = 347;
	public static final int PPC_INS_LWZU = 348;
	public static final int PPC_INS_LWZUX = 349;
	public static final int PPC_INS_LWZX = 350;
	public static final int PPC_INS_LXSDX = 351;
	public static final int PPC_INS_LXVD2X = 352;
	public static final int PPC_INS_LXVDSX = 353;
	public static final int PPC_INS_LXVW4X = 354;
	public static final int PPC_INS_MBAR = 355;
	public static final int PPC_INS_MCRF = 356;
	public static final int PPC_INS_MFCR = 357;
	public static final int PPC_INS_MFCTR = 358;
	public static final int PPC_INS_MFDCR = 359;
	public static final int PPC_INS_MFFS = 360;
	public static final int PPC_INS_MFLR = 361;
	public static final int PPC_INS_MFMSR = 362;
	public static final int PPC_INS_MFOCRF = 363;
	public static final int PPC_INS_MFSPR = 364;
	public static final int PPC_INS_MFSR = 365;
	public static final int PPC_INS_MFSRIN = 366;
	public static final int PPC_INS_MFTB = 367;
	public static final int PPC_INS_MFVSCR = 368;
	public static final int PPC_INS_MSYNC = 369;
	public static final int PPC_INS_MTCRF = 370;
	public static final int PPC_INS_MTCTR = 371;
	public static final int PPC_INS_MTDCR = 372;
	public static final int PPC_INS_MTFSB0 = 373;
	public static final int PPC_INS_MTFSB1 = 374;
	public static final int PPC_INS_MTFSF = 375;
	public static final int PPC_INS_MTLR = 376;
	public static final int PPC_INS_MTMSR = 377;
	public static final int PPC_INS_MTMSRD = 378;
	public static final int PPC_INS_MTOCRF = 379;
	public static final int PPC_INS_MTSPR = 380;
	public static final int PPC_INS_MTSR = 381;
	public static final int PPC_INS_MTSRIN = 382;
	public static final int PPC_INS_MTVSCR = 383;
	public static final int PPC_INS_MULHD = 384;
	public static final int PPC_INS_MULHDU = 385;
	public static final int PPC_INS_MULHW = 386;
	public static final int PPC_INS_MULHWU = 387;
	public static final int PPC_INS_MULLD = 388;
	public static final int PPC_INS_MULLI = 389;
	public static final int PPC_INS_MULLW = 390;
	public static final int PPC_INS_NAND = 391;
	public static final int PPC_INS_NEG = 392;
	public static final int PPC_INS_NOP = 393;
	public static final int PPC_INS_ORI = 394;
	public static final int PPC_INS_NOR = 395;
	public static final int PPC_INS_OR = 396;
	public static final int PPC_INS_ORC = 397;
	public static final int PPC_INS_ORIS = 398;
	public static final int PPC_INS_POPCNTD = 399;
	public static final int PPC_INS_POPCNTW = 400;
	public static final int PPC_INS_RFCI = 401;
	public static final int PPC_INS_RFDI = 402;
	public static final int PPC_INS_RFI = 403;
	public static final int PPC_INS_RFID = 404;
	public static final int PPC_INS_RFMCI = 405;
	public static final int PPC_INS_RLDCL = 406;
	public static final int PPC_INS_RLDCR = 407;
	public static final int PPC_INS_RLDIC = 408;
	public static final int PPC_INS_RLDICL = 409;
	public static final int PPC_INS_RLDICR = 410;
	public static final int PPC_INS_RLDIMI = 411;
	public static final int PPC_INS_RLWIMI = 412;
	public static final int PPC_INS_RLWINM = 413;
	public static final int PPC_INS_RLWNM = 414;
	public static final int PPC_INS_SC = 415;
	public static final int PPC_INS_SLBIA = 416;
	public static final int PPC_INS_SLBIE = 417;
	public static final int PPC_INS_SLBMFEE = 418;
	public static final int PPC_INS_SLBMTE = 419;
	public static final int PPC_INS_SLD = 420;
	public static final int PPC_INS_SLW = 421;
	public static final int PPC_INS_SRAD = 422;
	public static final int PPC_INS_SRADI = 423;
	public static final int PPC_INS_SRAW = 424;
	public static final int PPC_INS_SRAWI = 425;
	public static final int PPC_INS_SRD = 426;
	public static final int PPC_INS_SRW = 427;
	public static final int PPC_INS_STB = 428;
	public static final int PPC_INS_STBU = 429;
	public static final int PPC_INS_STBUX = 430;
	public static final int PPC_INS_STBX = 431;
	public static final int PPC_INS_STD = 432;
	public static final int PPC_INS_STDBRX = 433;
	public static final int PPC_INS_STDCX = 434;
	public static final int PPC_INS_STDU = 435;
	public static final int PPC_INS_STDUX = 436;
	public static final int PPC_INS_STDX = 437;
	public static final int PPC_INS_STFD = 438;
	public static final int PPC_INS_STFDU = 439;
	public static final int PPC_INS_STFDUX = 440;
	public static final int PPC_INS_STFDX = 441;
	public static final int PPC_INS_STFIWX = 442;
	public static final int PPC_INS_STFS = 443;
	public static final int PPC_INS_STFSU = 444;
	public static final int PPC_INS_STFSUX = 445;
	public static final int PPC_INS_STFSX = 446;
	public static final int PPC_INS_STH = 447;
	public static final int PPC_INS_STHBRX = 448;
	public static final int PPC_INS_STHU = 449;
	public static final int PPC_INS_STHUX = 450;
	public static final int PPC_INS_STHX = 451;
	public static final int PPC_INS_STMW = 452;
	public static final int PPC_INS_STSWI = 453;
	public static final int PPC_INS_STVEBX = 454;
	public static final int PPC_INS_STVEHX = 455;
	public static final int PPC_INS_STVEWX = 456;
	public static final int PPC_INS_STVX = 457;
	public static final int PPC_INS_STVXL = 458;
	public static final int PPC_INS_STW = 459;
	public static final int PPC_INS_STWBRX = 460;
	public static final int PPC_INS_STWCX = 461;
	public static final int PPC_INS_STWU = 462;
	public static final int PPC_INS_STWUX = 463;
	public static final int PPC_INS_STWX = 464;
	public static final int PPC_INS_STXSDX = 465;
	public static final int PPC_INS_STXVD2X = 466;
	public static final int PPC_INS_STXVW4X = 467;
	public static final int PPC_INS_SUBF = 468;
	public static final int PPC_INS_SUBFC = 469;
	public static final int PPC_INS_SUBFE = 470;
	public static final int PPC_INS_SUBFIC = 471;
	public static final int PPC_INS_SUBFME = 472;
	public static final int PPC_INS_SUBFZE = 473;
	public static final int PPC_INS_SYNC = 474;
	public static final int PPC_INS_TD = 475;
	public static final int PPC_INS_TDI = 476;
	public static final int PPC_INS_TLBIA = 477;
	public static final int PPC_INS_TLBIE = 478;
	public static final int PPC_INS_TLBIEL = 479;
	public static final int PPC_INS_TLBIVAX = 480;
	public static final int PPC_INS_TLBLD = 481;
	public static final int PPC_INS_TLBLI = 482;
	public static final int PPC_INS_TLBRE = 483;
	public static final int PPC_INS_TLBSX = 484;
	public static final int PPC_INS_TLBSYNC = 485;
	public static final int PPC_INS_TLBWE = 486;
	public static final int PPC_INS_TRAP = 487;
	public static final int PPC_INS_TW = 488;
	public static final int PPC_INS_TWI = 489;
	public static final int PPC_INS_VADDCUW = 490;
	public static final int PPC_INS_VADDFP = 491;
	public static final int PPC_INS_VADDSBS = 492;
	public static final int PPC_INS_VADDSHS = 493;
	public static final int PPC_INS_VADDSWS = 494;
	public static final int PPC_INS_VADDUBM = 495;
	public static final int PPC_INS_VADDUBS = 496;
	public static final int PPC_INS_VADDUHM = 497;
	public static final int PPC_INS_VADDUHS = 498;
	public static final int PPC_INS_VADDUWM = 499;
	public static final int PPC_INS_VADDUWS = 500;
	public static final int PPC_INS_VAND = 501;
	public static final int PPC_INS_VANDC = 502;
	public static final int PPC_INS_VAVGSB = 503;
	public static final int PPC_INS_VAVGSH = 504;
	public static final int PPC_INS_VAVGSW = 505;
	public static final int PPC_INS_VAVGUB = 506;
	public static final int PPC_INS_VAVGUH = 507;
	public static final int PPC_INS_VAVGUW = 508;
	public static final int PPC_INS_VCFSX = 509;
	public static final int PPC_INS_VCFUX = 510;
	public static final int PPC_INS_VCMPBFP = 511;
	public static final int PPC_INS_VCMPEQFP = 512;
	public static final int PPC_INS_VCMPEQUB = 513;
	public static final int PPC_INS_VCMPEQUH = 514;
	public static final int PPC_INS_VCMPEQUW = 515;
	public static final int PPC_INS_VCMPGEFP = 516;
	public static final int PPC_INS_VCMPGTFP = 517;
	public static final int PPC_INS_VCMPGTSB = 518;
	public static final int PPC_INS_VCMPGTSH = 519;
	public static final int PPC_INS_VCMPGTSW = 520;
	public static final int PPC_INS_VCMPGTUB = 521;
	public static final int PPC_INS_VCMPGTUH = 522;
	public static final int PPC_INS_VCMPGTUW = 523;
	public static final int PPC_INS_VCTSXS = 524;
	public static final int PPC_INS_VCTUXS = 525;
	public static final int PPC_INS_VEXPTEFP = 526;
	public static final int PPC_INS_VLOGEFP = 527;
	public static final int PPC_INS_VMADDFP = 528;
	public static final int PPC_INS_VMAXFP = 529;
	public static final int PPC_INS_VMAXSB = 530;
	public static final int PPC_INS_VMAXSH = 531;
	public static final int PPC_INS_VMAXSW = 532;
	public static final int PPC_INS_VMAXUB = 533;
	public static final int PPC_INS_VMAXUH = 534;
	public static final int PPC_INS_VMAXUW = 535;
	public static final int PPC_INS_VMHADDSHS = 536;
	public static final int PPC_INS_VMHRADDSHS = 537;
	public static final int PPC_INS_VMINFP = 538;
	public static final int PPC_INS_VMINSB = 539;
	public static final int PPC_INS_VMINSH = 540;
	public static final int PPC_INS_VMINSW = 541;
	public static final int PPC_INS_VMINUB = 542;
	public static final int PPC_INS_VMINUH = 543;
	public static final int PPC_INS_VMINUW = 544;
	public static final int PPC_INS_VMLADDUHM = 545;
	public static final int PPC_INS_VMRGHB = 546;
	public static final int PPC_INS_VMRGHH = 547;
	public static final int PPC_INS_VMRGHW = 548;
	public static final int PPC_INS_VMRGLB = 549;
	public static final int PPC_INS_VMRGLH = 550;
	public static final int PPC_INS_VMRGLW = 551;
	public static final int PPC_INS_VMSUMMBM = 552;
	public static final int PPC_INS_VMSUMSHM = 553;
	public static final int PPC_INS_VMSUMSHS = 554;
	public static final int PPC_INS_VMSUMUBM = 555;
	public static final int PPC_INS_VMSUMUHM = 556;
	public static final int PPC_INS_VMSUMUHS = 557;
	public static final int PPC_INS_VMULESB = 558;
	public static final int PPC_INS_VMULESH = 559;
	public static final int PPC_INS_VMULEUB = 560;
	public static final int PPC_INS_VMULEUH = 561;
	public static final int PPC_INS_VMULOSB = 562;
	public static final int PPC_INS_VMULOSH = 563;
	public static final int PPC_INS_VMULOUB = 564;
	public static final int PPC_INS_VMULOUH = 565;
	public static final int PPC_INS_VNMSUBFP = 566;
	public static final int PPC_INS_VNOR = 567;
	public static final int PPC_INS_VOR = 568;
	public static final int PPC_INS_VPERM = 569;
	public static final int PPC_INS_VPKPX = 570;
	public static final int PPC_INS_VPKSHSS = 571;
	public static final int PPC_INS_VPKSHUS = 572;
	public static final int PPC_INS_VPKSWSS = 573;
	public static final int PPC_INS_VPKSWUS = 574;
	public static final int PPC_INS_VPKUHUM = 575;
	public static final int PPC_INS_VPKUHUS = 576;
	public static final int PPC_INS_VPKUWUM = 577;
	public static final int PPC_INS_VPKUWUS = 578;
	public static final int PPC_INS_VREFP = 579;
	public static final int PPC_INS_VRFIM = 580;
	public static final int PPC_INS_VRFIN = 581;
	public static final int PPC_INS_VRFIP = 582;
	public static final int PPC_INS_VRFIZ = 583;
	public static final int PPC_INS_VRLB = 584;
	public static final int PPC_INS_VRLH = 585;
	public static final int PPC_INS_VRLW = 586;
	public static final int PPC_INS_VRSQRTEFP = 587;
	public static final int PPC_INS_VSEL = 588;
	public static final int PPC_INS_VSL = 589;
	public static final int PPC_INS_VSLB = 590;
	public static final int PPC_INS_VSLDOI = 591;
	public static final int PPC_INS_VSLH = 592;
	public static final int PPC_INS_VSLO = 593;
	public static final int PPC_INS_VSLW = 594;
	public static final int PPC_INS_VSPLTB = 595;
	public static final int PPC_INS_VSPLTH = 596;
	public static final int PPC_INS_VSPLTISB = 597;
	public static final int PPC_INS_VSPLTISH = 598;
	public static final int PPC_INS_VSPLTISW = 599;
	public static final int PPC_INS_VSPLTW = 600;
	public static final int PPC_INS_VSR = 601;
	public static final int PPC_INS_VSRAB = 602;
	public static final int PPC_INS_VSRAH = 603;
	public static final int PPC_INS_VSRAW = 604;
	public static final int PPC_INS_VSRB = 605;
	public static final int PPC_INS_VSRH = 606;
	public static final int PPC_INS_VSRO = 607;
	public static final int PPC_INS_VSRW = 608;
	public static final int PPC_INS_VSUBCUW = 609;
	public static final int PPC_INS_VSUBFP = 610;
	public static final int PPC_INS_VSUBSBS = 611;
	public static final int PPC_INS_VSUBSHS = 612;
	public static final int PPC_INS_VSUBSWS = 613;
	public static final int PPC_INS_VSUBUBM = 614;
	public static final int PPC_INS_VSUBUBS = 615;
	public static final int PPC_INS_VSUBUHM = 616;
	public static final int PPC_INS_VSUBUHS = 617;
	public static final int PPC_INS_VSUBUWM = 618;
	public static final int PPC_INS_VSUBUWS = 619;
	public static final int PPC_INS_VSUM2SWS = 620;
	public static final int PPC_INS_VSUM4SBS = 621;
	public static final int PPC_INS_VSUM4SHS = 622;
	public static final int PPC_INS_VSUM4UBS = 623;
	public static final int PPC_INS_VSUMSWS = 624;
	public static final int PPC_INS_VUPKHPX = 625;
	public static final int PPC_INS_VUPKHSB = 626;
	public static final int PPC_INS_VUPKHSH = 627;
	public static final int PPC_INS_VUPKLPX = 628;
	public static final int PPC_INS_VUPKLSB = 629;
	public static final int PPC_INS_VUPKLSH = 630;
	public static final int PPC_INS_VXOR = 631;
	public static final int PPC_INS_WAIT = 632;
	public static final int PPC_INS_WRTEE = 633;
	public static final int PPC_INS_WRTEEI = 634;
	public static final int PPC_INS_XOR = 635;
	public static final int PPC_INS_XORI = 636;
	public static final int PPC_INS_XORIS = 637;
	public static final int PPC_INS_XSABSDP = 638;
	public static final int PPC_INS_XSADDDP = 639;
	public static final int PPC_INS_XSCMPODP = 640;
	public static final int PPC_INS_XSCMPUDP = 641;
	public static final int PPC_INS_XSCPSGNDP = 642;
	public static final int PPC_INS_XSCVDPSP = 643;
	public static final int PPC_INS_XSCVDPSXDS = 644;
	public static final int PPC_INS_XSCVDPSXWS = 645;
	public static final int PPC_INS_XSCVDPUXDS = 646;
	public static final int PPC_INS_XSCVDPUXWS = 647;
	public static final int PPC_INS_XSCVSPDP = 648;
	public static final int PPC_INS_XSCVSXDDP = 649;
	public static final int PPC_INS_XSCVUXDDP = 650;
	public static final int PPC_INS_XSDIVDP = 651;
	public static final int PPC_INS_XSMADDADP = 652;
	public static final int PPC_INS_XSMADDMDP = 653;
	public static final int PPC_INS_XSMAXDP = 654;
	public static final int PPC_INS_XSMINDP = 655;
	public static final int PPC_INS_XSMSUBADP = 656;
	public static final int PPC_INS_XSMSUBMDP = 657;
	public static final int PPC_INS_XSMULDP = 658;
	public static final int PPC_INS_XSNABSDP = 659;
	public static final int PPC_INS_XSNEGDP = 660;
	public static final int PPC_INS_XSNMADDADP = 661;
	public static final int PPC_INS_XSNMADDMDP = 662;
	public static final int PPC_INS_XSNMSUBADP = 663;
	public static final int PPC_INS_XSNMSUBMDP = 664;
	public static final int PPC_INS_XSRDPI = 665;
	public static final int PPC_INS_XSRDPIC = 666;
	public static final int PPC_INS_XSRDPIM = 667;
	public static final int PPC_INS_XSRDPIP = 668;
	public static final int PPC_INS_XSRDPIZ = 669;
	public static final int PPC_INS_XSREDP = 670;
	public static final int PPC_INS_XSRSQRTEDP = 671;
	public static final int PPC_INS_XSSQRTDP = 672;
	public static final int PPC_INS_XSSUBDP = 673;
	public static final int PPC_INS_XSTDIVDP = 674;
	public static final int PPC_INS_XSTSQRTDP = 675;
	public static final int PPC_INS_XVABSDP = 676;
	public static final int PPC_INS_XVABSSP = 677;
	public static final int PPC_INS_XVADDDP = 678;
	public static final int PPC_INS_XVADDSP = 679;
	public static final int PPC_INS_XVCMPEQDP = 680;
	public static final int PPC_INS_XVCMPEQSP = 681;
	public static final int PPC_INS_XVCMPGEDP = 682;
	public static final int PPC_INS_XVCMPGESP = 683;
	public static final int PPC_INS_XVCMPGTDP = 684;
	public static final int PPC_INS_XVCMPGTSP = 685;
	public static final int PPC_INS_XVCPSGNDP = 686;
	public static final int PPC_INS_XVCPSGNSP = 687;
	public static final int PPC_INS_XVCVDPSP = 688;
	public static final int PPC_INS_XVCVDPSXDS = 689;
	public static final int PPC_INS_XVCVDPSXWS = 690;
	public static final int PPC_INS_XVCVDPUXDS = 691;
	public static final int PPC_INS_XVCVDPUXWS = 692;
	public static final int PPC_INS_XVCVSPDP = 693;
	public static final int PPC_INS_XVCVSPSXDS = 694;
	public static final int PPC_INS_XVCVSPSXWS = 695;
	public static final int PPC_INS_XVCVSPUXDS = 696;
	public static final int PPC_INS_XVCVSPUXWS = 697;
	public static final int PPC_INS_XVCVSXDDP = 698;
	public static final int PPC_INS_XVCVSXDSP = 699;
	public static final int PPC_INS_XVCVSXWDP = 700;
	public static final int PPC_INS_XVCVSXWSP = 701;
	public static final int PPC_INS_XVCVUXDDP = 702;
	public static final int PPC_INS_XVCVUXDSP = 703;
	public static final int PPC_INS_XVCVUXWDP = 704;
	public static final int PPC_INS_XVCVUXWSP = 705;
	public static final int PPC_INS_XVDIVDP = 706;
	public static final int PPC_INS_XVDIVSP = 707;
	public static final int PPC_INS_XVMADDADP = 708;
	public static final int PPC_INS_XVMADDASP = 709;
	public static final int PPC_INS_XVMADDMDP = 710;
	public static final int PPC_INS_XVMADDMSP = 711;
	public static final int PPC_INS_XVMAXDP = 712;
	public static final int PPC_INS_XVMAXSP = 713;
	public static final int PPC_INS_XVMINDP = 714;
	public static final int PPC_INS_XVMINSP = 715;
	public static final int PPC_INS_XVMSUBADP = 716;
	public static final int PPC_INS_XVMSUBASP = 717;
	public static final int PPC_INS_XVMSUBMDP = 718;
	public static final int PPC_INS_XVMSUBMSP = 719;
	public static final int PPC_INS_XVMULDP = 720;
	public static final int PPC_INS_XVMULSP = 721;
	public static final int PPC_INS_XVNABSDP = 722;
	public static final int PPC_INS_XVNABSSP = 723;
	public static final int PPC_INS_XVNEGDP = 724;
	public static final int PPC_INS_XVNEGSP = 725;
	public static final int PPC_INS_XVNMADDADP = 726;
	public static final int PPC_INS_XVNMADDASP = 727;
	public static final int PPC_INS_XVNMADDMDP = 728;
	public static final int PPC_INS_XVNMADDMSP = 729;
	public static final int PPC_INS_XVNMSUBADP = 730;
	public static final int PPC_INS_XVNMSUBASP = 731;
	public static final int PPC_INS_XVNMSUBMDP = 732;
	public static final int PPC_INS_XVNMSUBMSP = 733;
	public static final int PPC_INS_XVRDPI = 734;
	public static final int PPC_INS_XVRDPIC = 735;
	public static final int PPC_INS_XVRDPIM = 736;
	public static final int PPC_INS_XVRDPIP = 737;
	public static final int PPC_INS_XVRDPIZ = 738;
	public static final int PPC_INS_XVREDP = 739;
	public static final int PPC_INS_XVRESP = 740;
	public static final int PPC_INS_XVRSPI = 741;
	public static final int PPC_INS_XVRSPIC = 742;
	public static final int PPC_INS_XVRSPIM = 743;
	public static final int PPC_INS_XVRSPIP = 744;
	public static final int PPC_INS_XVRSPIZ = 745;
	public static final int PPC_INS_XVRSQRTEDP = 746;
	public static final int PPC_INS_XVRSQRTESP = 747;
	public static final int PPC_INS_XVSQRTDP = 748;
	public static final int PPC_INS_XVSQRTSP = 749;
	public static final int PPC_INS_XVSUBDP = 750;
	public static final int PPC_INS_XVSUBSP = 751;
	public static final int PPC_INS_XVTDIVDP = 752;
	public static final int PPC_INS_XVTDIVSP = 753;
	public static final int PPC_INS_XVTSQRTDP = 754;
	public static final int PPC_INS_XVTSQRTSP = 755;
	public static final int PPC_INS_XXLAND = 756;
	public static final int PPC_INS_XXLANDC = 757;
	public static final int PPC_INS_XXLNOR = 758;
	public static final int PPC_INS_XXLOR = 759;
	public static final int PPC_INS_XXLXOR = 760;
	public static final int PPC_INS_XXMRGHW = 761;
	public static final int PPC_INS_XXMRGLW = 762;
	public static final int PPC_INS_XXPERMDI = 763;
	public static final int PPC_INS_XXSEL = 764;
	public static final int PPC_INS_XXSLDWI = 765;
	public static final int PPC_INS_XXSPLTW = 766;
	public static final int PPC_INS_BCA = 767;
	public static final int PPC_INS_BCLA = 768;
	public static final int PPC_INS_SLWI = 769;
	public static final int PPC_INS_SRWI = 770;
	public static final int PPC_INS_SLDI = 771;
	public static final int PPC_INS_BTA = 772;
	public static final int PPC_INS_CRSET = 773;
	public static final int PPC_INS_CRNOT = 774;
	public static final int PPC_INS_CRMOVE = 775;
	public static final int PPC_INS_CRCLR = 776;
	public static final int PPC_INS_MFBR0 = 777;
	public static final int PPC_INS_MFBR1 = 778;
	public static final int PPC_INS_MFBR2 = 779;
	public static final int PPC_INS_MFBR3 = 780;
	public static final int PPC_INS_MFBR4 = 781;
	public static final int PPC_INS_MFBR5 = 782;
	public static final int PPC_INS_MFBR6 = 783;
	public static final int PPC_INS_MFBR7 = 784;
	public static final int PPC_INS_MFXER = 785;
	public static final int PPC_INS_MFRTCU = 786;
	public static final int PPC_INS_MFRTCL = 787;
	public static final int PPC_INS_MFDSCR = 788;
	public static final int PPC_INS_MFDSISR = 789;
	public static final int PPC_INS_MFDAR = 790;
	public static final int PPC_INS_MFSRR2 = 791;
	public static final int PPC_INS_MFSRR3 = 792;
	public static final int PPC_INS_MFCFAR = 793;
	public static final int PPC_INS_MFAMR = 794;
	public static final int PPC_INS_MFPID = 795;
	public static final int PPC_INS_MFTBLO = 796;
	public static final int PPC_INS_MFTBHI = 797;
	public static final int PPC_INS_MFDBATU = 798;
	public static final int PPC_INS_MFDBATL = 799;
	public static final int PPC_INS_MFIBATU = 800;
	public static final int PPC_INS_MFIBATL = 801;
	public static final int PPC_INS_MFDCCR = 802;
	public static final int PPC_INS_MFICCR = 803;
	public static final int PPC_INS_MFDEAR = 804;
	public static final int PPC_INS_MFESR = 805;
	public static final int PPC_INS_MFSPEFSCR = 806;
	public static final int PPC_INS_MFTCR = 807;
	public static final int PPC_INS_MFASR = 808;
	public static final int PPC_INS_MFPVR = 809;
	public static final int PPC_INS_MFTBU = 810;
	public static final int PPC_INS_MTCR = 811;
	public static final int PPC_INS_MTBR0 = 812;
	public static final int PPC_INS_MTBR1 = 813;
	public static final int PPC_INS_MTBR2 = 814;
	public static final int PPC_INS_MTBR3 = 815;
	public static final int PPC_INS_MTBR4 = 816;
	public static final int PPC_INS_MTBR5 = 817;
	public static final int PPC_INS_MTBR6 = 818;
	public static final int PPC_INS_MTBR7 = 819;
	public static final int PPC_INS_MTXER = 820;
	public static final int PPC_INS_MTDSCR = 821;
	public static final int PPC_INS_MTDSISR = 822;
	public static final int PPC_INS_MTDAR = 823;
	public static final int PPC_INS_MTSRR2 = 824;
	public static final int PPC_INS_MTSRR3 = 825;
	public static final int PPC_INS_MTCFAR = 826;
	public static final int PPC_INS_MTAMR = 827;
	public static final int PPC_INS_MTPID = 828;
	public static final int PPC_INS_MTTBL = 829;
	public static final int PPC_INS_MTTBU = 830;
	public static final int PPC_INS_MTTBLO = 831;
	public static final int PPC_INS_MTTBHI = 832;
	public static final int PPC_INS_MTDBATU = 833;
	public static final int PPC_INS_MTDBATL = 834;
	public static final int PPC_INS_MTIBATU = 835;
	public static final int PPC_INS_MTIBATL = 836;
	public static final int PPC_INS_MTDCCR = 837;
	public static final int PPC_INS_MTICCR = 838;
	public static final int PPC_INS_MTDEAR = 839;
	public static final int PPC_INS_MTESR = 840;
	public static final int PPC_INS_MTSPEFSCR = 841;
	public static final int PPC_INS_MTTCR = 842;
	public static final int PPC_INS_NOT = 843;
	public static final int PPC_INS_MR = 844;
	public static final int PPC_INS_ROTLD = 845;
	public static final int PPC_INS_ROTLDI = 846;
	public static final int PPC_INS_CLRLDI = 847;
	public static final int PPC_INS_ROTLWI = 848;
	public static final int PPC_INS_CLRLWI = 849;
	public static final int PPC_INS_ROTLW = 850;
	public static final int PPC_INS_SUB = 851;
	public static final int PPC_INS_SUBC = 852;
	public static final int PPC_INS_LWSYNC = 853;
	public static final int PPC_INS_PTESYNC = 854;
	public static final int PPC_INS_TDLT = 855;
	public static final int PPC_INS_TDEQ = 856;
	public static final int PPC_INS_TDGT = 857;
	public static final int PPC_INS_TDNE = 858;
	public static final int PPC_INS_TDLLT = 859;
	public static final int PPC_INS_TDLGT = 860;
	public static final int PPC_INS_TDU = 861;
	public static final int PPC_INS_TDLTI = 862;
	public static final int PPC_INS_TDEQI = 863;
	public static final int PPC_INS_TDGTI = 864;
	public static final int PPC_INS_TDNEI = 865;
	public static final int PPC_INS_TDLLTI = 866;
	public static final int PPC_INS_TDLGTI = 867;
	public static final int PPC_INS_TDUI = 868;
	public static final int PPC_INS_TLBREHI = 869;
	public static final int PPC_INS_TLBRELO = 870;
	public static final int PPC_INS_TLBWEHI = 871;
	public static final int PPC_INS_TLBWELO = 872;
	public static final int PPC_INS_TWLT = 873;
	public static final int PPC_INS_TWEQ = 874;
	public static final int PPC_INS_TWGT = 875;
	public static final int PPC_INS_TWNE = 876;
	public static final int PPC_INS_TWLLT = 877;
	public static final int PPC_INS_TWLGT = 878;
	public static final int PPC_INS_TWU = 879;
	public static final int PPC_INS_TWLTI = 880;
	public static final int PPC_INS_TWEQI = 881;
	public static final int PPC_INS_TWGTI = 882;
	public static final int PPC_INS_TWNEI = 883;
	public static final int PPC_INS_TWLLTI = 884;
	public static final int PPC_INS_TWLGTI = 885;
	public static final int PPC_INS_TWUI = 886;
	public static final int PPC_INS_WAITRSV = 887;
	public static final int PPC_INS_WAITIMPL = 888;
	public static final int PPC_INS_XNOP = 889;
	public static final int PPC_INS_XVMOVDP = 890;
	public static final int PPC_INS_XVMOVSP = 891;
	public static final int PPC_INS_XXSPLTD = 892;
	public static final int PPC_INS_XXMRGHD = 893;
	public static final int PPC_INS_XXMRGLD = 894;
	public static final int PPC_INS_XXSWAPD = 895;
	public static final int PPC_INS_BT = 896;
	public static final int PPC_INS_BF = 897;
	public static final int PPC_INS_BDNZT = 898;
	public static final int PPC_INS_BDNZF = 899;
	public static final int PPC_INS_BDZF = 900;
	public static final int PPC_INS_BDZT = 901;
	public static final int PPC_INS_BFA = 902;
	public static final int PPC_INS_BDNZTA = 903;
	public static final int PPC_INS_BDNZFA = 904;
	public static final int PPC_INS_BDZTA = 905;
	public static final int PPC_INS_BDZFA = 906;
	public static final int PPC_INS_BTCTR = 907;
	public static final int PPC_INS_BFCTR = 908;
	public static final int PPC_INS_BTCTRL = 909;
	public static final int PPC_INS_BFCTRL = 910;
	public static final int PPC_INS_BTL = 911;
	public static final int PPC_INS_BFL = 912;
	public static final int PPC_INS_BDNZTL = 913;
	public static final int PPC_INS_BDNZFL = 914;
	public static final int PPC_INS_BDZTL = 915;
	public static final int PPC_INS_BDZFL = 916;
	public static final int PPC_INS_BTLA = 917;
	public static final int PPC_INS_BFLA = 918;
	public static final int PPC_INS_BDNZTLA = 919;
	public static final int PPC_INS_BDNZFLA = 920;
	public static final int PPC_INS_BDZTLA = 921;
	public static final int PPC_INS_BDZFLA = 922;
	public static final int PPC_INS_BTLR = 923;
	public static final int PPC_INS_BFLR = 924;
	public static final int PPC_INS_BDNZTLR = 925;
	public static final int PPC_INS_BDZTLR = 926;
	public static final int PPC_INS_BDZFLR = 927;
	public static final int PPC_INS_BTLRL = 928;
	public static final int PPC_INS_BFLRL = 929;
	public static final int PPC_INS_BDNZTLRL = 930;
	public static final int PPC_INS_BDNZFLRL = 931;
	public static final int PPC_INS_BDZTLRL = 932;
	public static final int PPC_INS_BDZFLRL = 933;
	public static final int PPC_INS_ENDING = 934;

	// Group of PPC instructions

	public static final int PPC_GRP_INVALID = 0;

	// Generic groups
	public static final int PPC_GRP_JUMP = 1;

	// Architecture-specific groups
	public static final int PPC_GRP_ALTIVEC = 128;
	public static final int PPC_GRP_MODE32 = 129;
	public static final int PPC_GRP_MODE64 = 130;
	public static final int PPC_GRP_BOOKE = 131;
	public static final int PPC_GRP_NOTBOOKE = 132;
	public static final int PPC_GRP_SPE = 133;
	public static final int PPC_GRP_VSX = 134;
	public static final int PPC_GRP_E500 = 135;
	public static final int PPC_GRP_PPC4XX = 136;
	public static final int PPC_GRP_PPC6XX = 137;
	public static final int PPC_GRP_ENDING = 138;
}