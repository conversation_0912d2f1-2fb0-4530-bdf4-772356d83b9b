# CS_ARCH_ARM, CS_MODE_THUMB, None
0xf1,0xef,0xa0,0x03 = vext.8 d16, d17, d16, #3
0xf1,0xef,0xa0,0x05 = vext.8 d16, d17, d16, #5
0xf2,0xef,0xe0,0x03 = vext.8 q8, q9, q8, #3
0xf2,0xef,0xe0,0x07 = vext.8 q8, q9, q8, #7
0xf1,0xef,0xa0,0x06 = vext.16 d16, d17, d16, #3
0xf2,0xef,0xe0,0x0c = vext.32 q8, q9, q8, #3
0xf2,0xff,0xa0,0x10 = vtrn.8 d17, d16
0xf6,0xff,0xa0,0x10 = vtrn.16 d17, d16
0xfa,0xff,0xa0,0x10 = vtrn.32 d17, d16
0xf2,0xff,0xe0,0x20 = vtrn.8 q9, q8
0xf6,0xff,0xe0,0x20 = vtrn.16 q9, q8
0xfa,0xff,0xe0,0x20 = vtrn.32 q9, q8
0xf2,0xff,0x20,0x11 = vuzp.8 d17, d16
0xf6,0xff,0x20,0x11 = vuzp.16 d17, d16
0xf2,0xff,0x60,0x21 = vuzp.8 q9, q8
0xf6,0xff,0x60,0x21 = vuzp.16 q9, q8
0xfa,0xff,0x60,0x21 = vuzp.32 q9, q8
0xf2,0xff,0xa0,0x11 = vzip.8 d17, d16
0xf6,0xff,0xa0,0x11 = vzip.16 d17, d16
0xf2,0xff,0xe0,0x21 = vzip.8 q9, q8
0xf6,0xff,0xe0,0x21 = vzip.16 q9, q8
0xfa,0xff,0xe0,0x21 = vzip.32 q9, q8
