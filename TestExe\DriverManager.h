#pragma once

#include <windows.h>
#include <winternl.h>
#include <string>

// NtUnloadDriver函数声明
typedef NTSTATUS (WINAPI *PNtUnloadDriver)(PUNICODE_STRING DriverServiceName);

class DriverManager {
private:
    SC_HANDLE hSCManager;
    SC_HANDLE hService;
    PNtUnloadDriver NtUnloadDriverFunc;
    std::wstring driverPath;
    std::wstring serviceName;
    
    // 日志回调函数类型
    typedef void (*LogCallback)(const std::wstring& message);
    LogCallback logCallback;
    
    // 内部辅助函数
    void LogMessage(const std::wstring& message);
    BOOL CheckAdminPrivileges();
    BOOL CleanupExistingService();
    BOOL CreateDriverService();
    BOOL StartDriverService();
    BOOL StopDriverService();
    BOOL DeleteDriverService();
    BOOL ForceUnloadDriver();

public:
    DriverManager();
    ~DriverManager();
    
    // 设置日志回调
    void SetLogCallback(LogCallback callback);
    
    // 设置驱动路径和服务名
    void SetDriverPath(const std::wstring& path);
    void SetServiceName(const std::wstring& name);
    
    // 主要功能函数
    BOOL LoadDriver();
    BOOL UnloadDriver();
    BOOL IsDriverLoaded();
    
    // 获取状态信息
    std::wstring GetLastError();
};
