# CS_ARCH_ARM64, 0, None
0x20,0x1c,0x22,0x0e = and v0.8b, v1.8b, v2.8b
0x20,0x1c,0x22,0x4e = and v0.16b, v1.16b, v2.16b
0x20,0x1c,0xa2,0x0e = orr v0.8b, v1.8b, v2.8b
0x20,0x1c,0xa2,0x4e = orr v0.16b, v1.16b, v2.16b
0x20,0x1c,0x22,0x2e = eor v0.8b, v1.8b, v2.8b
0x20,0x1c,0x22,0x6e = eor v0.16b, v1.16b, v2.16b
0x20,0x1c,0xa2,0x2e = bit v0.8b, v1.8b, v2.8b
0x20,0x1c,0xa2,0x6e = bit v0.16b, v1.16b, v2.16b
0x20,0x1c,0xe2,0x2e = bif v0.8b, v1.8b, v2.8b
0x20,0x1c,0xe2,0x6e = bif v0.16b, v1.16b, v2.16b
0x20,0x1c,0x62,0x2e = bsl v0.8b, v1.8b, v2.8b
0x20,0x1c,0x62,0x6e = bsl v0.16b, v1.16b, v2.16b
0x20,0x1c,0xe2,0x0e = orn v0.8b, v1.8b, v2.8b
0x20,0x1c,0xe2,0x4e = orn v0.16b, v1.16b, v2.16b
0x20,0x1c,0x62,0x0e = bic v0.8b, v1.8b, v2.8b
0x20,0x1c,0x62,0x4e = bic v0.16b, v1.16b, v2.16b
