// Copyright (c) 2015-2022, <PERSON><PERSON>. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

/// @file
/// Implements an entry point of the driver.

#include "driver.h"
#include "common.h"
#include "global_object.h"
#include "hotplug_callback.h"
#include "log.h"
#include "power_callback.h"
#include "util.h"
#include "vm.h"
#include "performance.h"
#include "../../DdiMon/ddi_mon_ioctl.h"

extern "C" {
////////////////////////////////////////////////////////////////////////////////
//
// macro utilities
//

////////////////////////////////////////////////////////////////////////////////
//
// constants and macros
//

////////////////////////////////////////////////////////////////////////////////
//
// types
//

////////////////////////////////////////////////////////////////////////////////
//
// prototypes
//

DRIVER_INITIALIZE DriverEntry;

static DRIVER_UNLOAD DriverpDriverUnload;

static DRIVER_DISPATCH DriverpDeviceControl;
static DRIVER_DISPATCH DriverpCreateClose;

_IRQL_requires_max_(PASSIVE_LEVEL) bool DriverpIsSuppoetedOS();

#if defined(ALLOC_PRAGMA)
#pragma alloc_text(INIT, DriverEntry)
#pragma alloc_text(PAGE, DriverpDriverUnload)
#pragma alloc_text(INIT, DriverpIsSuppoetedOS)
#endif

////////////////////////////////////////////////////////////////////////////////
//
// variables
//

// Device object for IOCTL communication
static PDEVICE_OBJECT g_DeviceObject = nullptr;
static UNICODE_STRING g_DeviceName = RTL_CONSTANT_STRING(L"\\Device\\DdiMon");
static UNICODE_STRING g_SymbolicLink = RTL_CONSTANT_STRING(L"\\??\\DdiMon");

////////////////////////////////////////////////////////////////////////////////
//
// implementations
//

// A driver entry point
_Use_decl_annotations_ NTSTATUS DriverEntry(PDRIVER_OBJECT driver_object,
                                            PUNICODE_STRING registry_path) {
  UNREFERENCED_PARAMETER(registry_path);
  PAGED_CODE()

  static const wchar_t kLogFilePath[] = L"\\SystemRoot\\HyperPlatform.log";
  // PERFORMANCE: Minimize logging for EPT hook optimization
  static const auto kLogLevel = kLogPutLevelError | kLogOptDisableFunctionName | kLogOptDisableTime | kLogOptDisableProcessorNumber;

  auto status = STATUS_UNSUCCESSFUL;
  driver_object->DriverUnload = DriverpDriverUnload;

  // Set up device control handlers
  driver_object->MajorFunction[IRP_MJ_CREATE] = DriverpCreateClose;
  driver_object->MajorFunction[IRP_MJ_CLOSE] = DriverpCreateClose;
  driver_object->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DriverpDeviceControl;

  // PERFORMANCE: Disable debug break for EPT hook optimization
  // HYPERPLATFORM_COMMON_DBG_BREAK();

  // Request NX Non-Paged Pool when available
  ExInitializeDriverRuntime(DrvRtPoolNxOptIn);

  // Initialize log functions
  bool need_reinitialization = false;
  status = LogInitialization(kLogLevel, kLogFilePath);
  if (status == STATUS_REINITIALIZATION_NEEDED) {
    need_reinitialization = true;
  } else if (!NT_SUCCESS(status)) {
    return status;
  }

  // Test if the system is supported
  if (!DriverpIsSuppoetedOS()) {
    LogTermination();
    return STATUS_CANCELLED;
  }

  // Initialize global variables
  status = GlobalObjectInitialization();
  if (!NT_SUCCESS(status)) {
    LogTermination();
    return status;
  }

  // Initialize perf functions
  status = PerfInitialization();
  if (!NT_SUCCESS(status)) {
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Initialize utility functions
  status = UtilInitialization(driver_object);
  if (!NT_SUCCESS(status)) {
    PerfTermination();
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Initialize power callback
  status = PowerCallbackInitialization();
  if (!NT_SUCCESS(status)) {
    UtilTermination();
    PerfTermination();
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Initialize hot-plug callback
  status = HotplugCallbackInitialization();
  if (!NT_SUCCESS(status)) {
    PowerCallbackTermination();
    UtilTermination();
    PerfTermination();
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Virtualize all processors with additional safety checks
  HYPERPLATFORM_LOG_INFO("Starting VMM initialization...");

  // Add a small delay to ensure system stability
  LARGE_INTEGER delay;
  delay.QuadPart = -10000000; // 1 second delay
  KeDelayExecutionThread(KernelMode, FALSE, &delay);

  status = VmInitialization();
  if (!NT_SUCCESS(status)) {
    HYPERPLATFORM_LOG_ERROR("VMM initialization failed with status: 0x%08X", status);
    HotplugCallbackTermination();
    PowerCallbackTermination();
    UtilTermination();
    PerfTermination();
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Create device object for IOCTL communication
  // TEMP FIX: Remove FILE_DEVICE_SECURE_OPEN to avoid access denied issues
  status = IoCreateDevice(driver_object, 0, &g_DeviceName, FILE_DEVICE_UNKNOWN,
                         0, FALSE, &g_DeviceObject);
  if (!NT_SUCCESS(status)) {
    HYPERPLATFORM_LOG_ERROR("Failed to create device object: 0x%08X", status);
    VmTermination();
    HotplugCallbackTermination();
    PowerCallbackTermination();
    UtilTermination();
    PerfTermination();
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Create symbolic link
  status = IoCreateSymbolicLink(&g_SymbolicLink, &g_DeviceName);
  if (!NT_SUCCESS(status)) {
    HYPERPLATFORM_LOG_ERROR("Failed to create symbolic link: 0x%08X", status);
    IoDeleteDevice(g_DeviceObject);
    g_DeviceObject = nullptr;
    VmTermination();
    HotplugCallbackTermination();
    PowerCallbackTermination();
    UtilTermination();
    PerfTermination();
    GlobalObjectTermination();
    LogTermination();
    return status;
  }

  // Register re-initialization for the log functions if needed
  if (need_reinitialization) {
    LogRegisterReinitialization(driver_object);
  }

  HYPERPLATFORM_LOG_INFO("The VMM has been installed.");
  HYPERPLATFORM_LOG_INFO("Device object created for IOCTL communication.");
  return status;
}

// Unload handler
_Use_decl_annotations_ static void DriverpDriverUnload(
    PDRIVER_OBJECT driver_object) {
  UNREFERENCED_PARAMETER(driver_object);
  PAGED_CODE()

  HYPERPLATFORM_COMMON_DBG_BREAK();

  // CRITICAL: Clean up all EPT hooks before terminating VM
  // TODO: Fix linking issue with DdimonpRemoveAllPageHooks
  // DdimonpRemoveAllPageHooks();
  HYPERPLATFORM_LOG_INFO("EPT hooks cleanup - TODO: implement proper cleanup");

  // Clean up device object
  if (g_DeviceObject) {
    IoDeleteSymbolicLink(&g_SymbolicLink);
    IoDeleteDevice(g_DeviceObject);
    g_DeviceObject = nullptr;
    HYPERPLATFORM_LOG_INFO("Device object cleaned up.");
  }

  VmTermination();
  HotplugCallbackTermination();
  PowerCallbackTermination();
  UtilTermination();
  PerfTermination();
  GlobalObjectTermination();
  LogTermination();
}

// Test if the system is one of supported OS versions
_Use_decl_annotations_ bool DriverpIsSuppoetedOS() {
  PAGED_CODE()

  RTL_OSVERSIONINFOW os_version = {};
  auto status = RtlGetVersion(&os_version);
  if (!NT_SUCCESS(status)) {
    return false;
  }
  if (os_version.dwMajorVersion != 6 && os_version.dwMajorVersion != 10) {
    return false;
  }
  // 4-gigabyte tuning (4GT) should not be enabled
  if (!IsX64() &&
      reinterpret_cast<ULONG_PTR>(MmSystemRangeStart) != 0x80000000) {
    return false;
  }
  return true;
}

// Create/Close handler
_Use_decl_annotations_ static NTSTATUS DriverpCreateClose(
    PDEVICE_OBJECT device_object, PIRP irp) {
  UNREFERENCED_PARAMETER(device_object);
  PAGED_CODE()

  irp->IoStatus.Status = STATUS_SUCCESS;
  irp->IoStatus.Information = 0;
  IoCompleteRequest(irp, IO_NO_INCREMENT);
  return STATUS_SUCCESS;
}

// Device control handler
_Use_decl_annotations_ static NTSTATUS DriverpDeviceControl(
    PDEVICE_OBJECT device_object, PIRP irp) {
  UNREFERENCED_PARAMETER(device_object);
  PAGED_CODE()

  auto irp_stack = IoGetCurrentIrpStackLocation(irp);
  auto status = STATUS_INVALID_DEVICE_REQUEST;
  ULONG_PTR information = 0;

  switch (irp_stack->Parameters.DeviceIoControl.IoControlCode) {
    case IOCTL_DDIMON_EPT_READ_MEMORY: {
      ULONG bytes_returned = 0;
      status = DdimonHandleEptReadMemory(
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.InputBufferLength,
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.OutputBufferLength,
          &bytes_returned);
      information = bytes_returned;
      break;
    }
    case IOCTL_DDIMON_EPT_WRITE_MEMORY: {
      ULONG bytes_returned = 0;
      status = DdimonHandleEptWriteMemory(
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.InputBufferLength,
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.OutputBufferLength,
          &bytes_returned);
      information = bytes_returned;
      break;
    }
    case IOCTL_DDIMON_HIDE_PROCESS: {
      ULONG bytes_returned = 0;
      status = DdimonHandleHideProcess(
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.InputBufferLength,
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.OutputBufferLength,
          &bytes_returned);
      information = bytes_returned;
      break;
    }
    case IOCTL_DDIMON_SHOW_PROCESS: {
      ULONG bytes_returned = 0;
      status = DdimonHandleShowProcess(
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.InputBufferLength,
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.OutputBufferLength,
          &bytes_returned);
      information = bytes_returned;
      break;
    }
    case IOCTL_DDIMON_LIST_HIDDEN_PROCESSES: {
      ULONG bytes_returned = 0;
      status = DdimonHandleListHiddenProcesses(
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.InputBufferLength,
          irp->AssociatedIrp.SystemBuffer,
          irp_stack->Parameters.DeviceIoControl.OutputBufferLength,
          &bytes_returned);
      information = bytes_returned;
      break;
    }
    // Removed: All manual hook management IOCTL case statements
    // These IOCTL codes have been deleted from the header files
    default:
      HYPERPLATFORM_LOG_WARN("Unknown IOCTL code: 0x%08X",
                             irp_stack->Parameters.DeviceIoControl.IoControlCode);
      status = STATUS_INVALID_DEVICE_REQUEST;
      break;
  }

  irp->IoStatus.Status = status;
  irp->IoStatus.Information = information;
  IoCompleteRequest(irp, IO_NO_INCREMENT);
  return status;
}

}  // extern "C"
