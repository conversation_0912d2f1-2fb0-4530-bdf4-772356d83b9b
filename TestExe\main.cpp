#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <shellapi.h>
#include <wchar.h>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <tlhelp32.h>
#include "resource.h"
#include "DdiMonIOCTL.h"
#include "DdiMonIOCTL.h"
#include "DriverManager.h"

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "shell32.lib")

// 全局变量
HINSTANCE g_hInst = NULL;
HWND g_hMainDlg = NULL;
HWND g_hEditDriverPath = NULL;
HWND g_hListLog = NULL;
HWND g_hStaticStatus = NULL;
HWND g_hCheckAutoScroll = NULL;

// 驱动管理相关
SC_HANDLE g_hSCManager = NULL;
SC_HANDLE g_hService = NULL;
std::wstring g_strServiceName = L"DdiMon";
bool g_bDriverLoaded = false;
bool g_bMonitorStarted = false;
DriverManager g_DriverManager;
DdiMonIOCTL g_DdiMonIOCTL;

// 日志文件监控
HANDLE g_hLogFile = INVALID_HANDLE_VALUE;
LARGE_INTEGER g_lastFileSize = {0};
UINT_PTR g_nTimerID = 0;

// EPT操作相关控件
HWND g_hEditProcessId = NULL;
HWND g_hEditAddress = NULL;
HWND g_hEditSize = NULL;
HWND g_hEditData = NULL;
HWND g_hEditResults = NULL;

// 进程隐藏相关控件
HWND g_hEditHideProcessId = NULL;
HWND g_hListHiddenProcesses = NULL;

// EPT操作相关变量
HANDLE g_hDevice = INVALID_HANDLE_VALUE;
DWORD g_selectedProcessId = 0;

// REMOVED: Hook management variables
// Per ultimate simplification: Hooks are automatically managed during read/write operations

// DdiMon IOCTL接口已在上面定义

// 函数声明
void HandleEptReadMemory();
void HandleEptWriteMemory();
void HandleGetProcesses();

// 进程隐藏函数声明
void HandleHideProcess();
void HandleShowProcess();
void HandleListHiddenProcesses();

// REMOVED: Hook management function declarations
// Per ultimate simplification: Hooks are automatically managed during read/write operations
void AddResultMessage(const std::wstring& message);
std::vector<BYTE> HexStringToBytes(const std::wstring& hexStr);
std::wstring BytesToHexString(const BYTE* data, size_t length);
bool IsProcessValid(DWORD processId);
INT_PTR CALLBACK ProcessSelectDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);

// REMOVED: Hook manager dialog procedure declaration
// Per ultimate simplification: No hook management dialog needed

// 函数声明
INT_PTR CALLBACK MainDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);
void UpdateUI();
void AddLogMessage(const std::wstring& message);
void SetStatus(const std::wstring& status);
bool LoadDriver(const std::wstring& driverPath);
bool UnloadDriver();
bool StartMonitoring();
bool StopMonitoring();
void RefreshLogFromFile();
bool CreateDriverService(const std::wstring& driverPath);
bool DeleteDriverService();
void InitializeServiceManager();
void CleanupServiceManager();

// 程序入口点
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    g_hInst = hInstance;

    // 初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);

    // 显示主对话框
    DialogBox(hInstance, MAKEINTRESOURCE(IDD_MAIN_DIALOG), NULL, MainDlgProc);

    return 0;
}

// 主对话框过程
INT_PTR CALLBACK MainDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    UNREFERENCED_PARAMETER(lParam);

    switch (message)
    {
    case WM_INITDIALOG:
        {
            g_hMainDlg = hDlg;
            
            // 获取控件句柄
            g_hEditDriverPath = GetDlgItem(hDlg, IDC_EDIT_DRIVER_PATH);
            g_hListLog = GetDlgItem(hDlg, IDC_LIST_LOG);
            g_hStaticStatus = GetDlgItem(hDlg, IDC_STATIC_STATUS);
            g_hCheckAutoScroll = GetDlgItem(hDlg, IDC_CHECK_AUTO_SCROLL);

            // 获取EPT操作控件句柄
            g_hEditProcessId = GetDlgItem(hDlg, IDC_EDIT_PROCESS_ID);
            g_hEditAddress = GetDlgItem(hDlg, IDC_EDIT_ADDRESS);
            g_hEditSize = GetDlgItem(hDlg, IDC_EDIT_SIZE);
            g_hEditData = GetDlgItem(hDlg, IDC_EDIT_DATA);
            // REMOVED: Hook status control
            // Per ultimate simplification: No hook status display needed
            g_hEditResults = GetDlgItem(hDlg, IDC_EDIT_RESULTS);

            // 进程隐藏相关控件
            g_hEditHideProcessId = GetDlgItem(hDlg, IDC_EDIT_HIDE_PROCESS_ID);
            g_hListHiddenProcesses = GetDlgItem(hDlg, IDC_LIST_HIDDEN_PROCESSES);

            // 设置默认驱动路径
            wchar_t szPath[MAX_PATH];
            GetModuleFileNameW(NULL, szPath, MAX_PATH);
            std::wstring strAppPath = szPath;
            size_t nPos = strAppPath.find_last_of(L'\\');
            if (nPos != std::wstring::npos)
            {
                strAppPath = strAppPath.substr(0, nPos);
                std::wstring strDriverPath = strAppPath + L"\\..\\x64\\Release\\DdiMon.sys";
                SetWindowTextW(g_hEditDriverPath, strDriverPath.c_str());
            }

            // 设置中文界面文本
            SetWindowTextW(hDlg, L"DdiMon 管理工具");
            SetWindowTextW(GetDlgItem(hDlg, 2001), L"驱动管理");
            SetWindowTextW(GetDlgItem(hDlg, 2002), L"驱动路径:");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_SELECT_DRIVER), L"选择...");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_LOAD_DRIVER), L"加载驱动");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_UNLOAD_DRIVER), L"卸载驱动");
            SetWindowTextW(GetDlgItem(hDlg, 2003), L"监控控制");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_START_MONITOR), L"开始监控");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_STOP_MONITOR), L"停止监控");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_VIEW_LOG_FILE), L"查看日志文件");
            SetWindowTextW(GetDlgItem(hDlg, IDC_CHECK_AUTO_SCROLL), L"自动滚动");
            SetWindowTextW(GetDlgItem(hDlg, 2005), L"EPT内存操作");
            SetWindowTextW(GetDlgItem(hDlg, 2006), L"进程ID:");
            SetWindowTextW(GetDlgItem(hDlg, 1021), L"获取进程");
            SetWindowTextW(GetDlgItem(hDlg, 2007), L"内存地址:");
            SetWindowTextW(GetDlgItem(hDlg, 2008), L"大小:");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_EPT_READ), L"EPT读取");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_EPT_WRITE), L"EPT写入");
            SetWindowTextW(GetDlgItem(hDlg, 2009), L"数据(十六进制):");

            // REMOVED: Hook management UI text
            // Per ultimate simplification: Hooks are automatically managed
            SetWindowTextW(GetDlgItem(hDlg, 2004), L"日志信息");
            SetWindowTextW(GetDlgItem(hDlg, IDC_BTN_CLEAR_LOG), L"清除日志");
            SetWindowTextW(GetDlgItem(hDlg, 2011), L"EPT操作结果");
            SetWindowTextW(GetDlgItem(hDlg, IDCANCEL), L"退出");

            // 初始化服务管理器
            InitializeServiceManager();

            // 设置自动滚动默认选中
            SendMessage(g_hCheckAutoScroll, BM_SETCHECK, BST_CHECKED, 0);

            // 更新界面状态
            UpdateUI();
            SetStatus(L"就绪");

            // 添加启动日志
            if (g_hListLog) {
                AddLogMessage(L"DdiMon 管理工具已启动");
                AddLogMessage(L"请选择驱动文件并加载驱动");
            }

            return TRUE;
        }

    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            switch (wmId)
            {
            case IDC_BTN_SELECT_DRIVER:
                {
                    OPENFILENAMEW ofn;
                    wchar_t szFile[MAX_PATH] = {0};
                    
                    ZeroMemory(&ofn, sizeof(ofn));
                    ofn.lStructSize = sizeof(ofn);
                    ofn.hwndOwner = hDlg;
                    ofn.lpstrFile = szFile;
                    ofn.nMaxFile = sizeof(szFile) / sizeof(wchar_t);
                    ofn.lpstrFilter = L"驱动文件 (*.sys)\0*.sys\0所有文件 (*.*)\0*.*\0";
                    ofn.nFilterIndex = 1;
                    ofn.lpstrFileTitle = NULL;
                    ofn.nMaxFileTitle = 0;
                    ofn.lpstrInitialDir = NULL;
                    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;

                    if (GetOpenFileNameW(&ofn))
                    {
                        SetWindowTextW(g_hEditDriverPath, szFile);
                        AddLogMessage(L"已选择驱动文件: " + std::wstring(szFile));
                    }
                }
                break;

            case IDC_BTN_LOAD_DRIVER:
                {
                    wchar_t szDriverPath[MAX_PATH];
                    GetWindowTextW(g_hEditDriverPath, szDriverPath, MAX_PATH);
                    
                    if (wcslen(szDriverPath) == 0)
                    {
                        MessageBoxW(hDlg, L"请先选择驱动文件！", L"错误", MB_OK | MB_ICONERROR);
                        break;
                    }

                    if (LoadDriver(szDriverPath))
                    {
                        g_bDriverLoaded = true;
                        SetStatus(L"驱动已加载");
                    }
                    else
                    {
                        SetStatus(L"驱动加载失败");
                    }
                    
                    UpdateUI();
                }
                break;

            case IDC_BTN_UNLOAD_DRIVER:
                {
                    if (g_bMonitorStarted)
                    {
                        StopMonitoring();
                    }

                    if (UnloadDriver())
                    {
                        g_bDriverLoaded = false;
                        SetStatus(L"驱动已卸载");
                    }
                    else
                    {
                        SetStatus(L"驱动卸载失败");
                    }
                    
                    UpdateUI();
                }
                break;

            case IDC_BTN_START_MONITOR:
                {
                    if (!g_bDriverLoaded)
                    {
                        MessageBoxW(hDlg, L"请先加载驱动！", L"错误", MB_OK | MB_ICONERROR);
                        break;
                    }

                    if (StartMonitoring())
                    {
                        g_bMonitorStarted = true;
                        AddLogMessage(L"开始监控系统调用");
                        SetStatus(L"正在监控");
                        
                        // 启动定时器监控日志文件
                        g_nTimerID = SetTimer(hDlg, 1, 1000, NULL); // 每秒检查一次
                    }
                    else
                    {
                        AddLogMessage(L"启动监控失败");
                        SetStatus(L"监控启动失败");
                    }
                    
                    UpdateUI();
                }
                break;

            case IDC_BTN_STOP_MONITOR:
                {
                    if (StopMonitoring())
                    {
                        g_bMonitorStarted = false;
                        AddLogMessage(L"停止监控");
                        SetStatus(L"监控已停止");
                        
                        // 停止定时器
                        if (g_nTimerID != 0)
                        {
                            KillTimer(hDlg, g_nTimerID);
                            g_nTimerID = 0;
                        }
                    }
                    else
                    {
                        AddLogMessage(L"停止监控失败");
                    }
                    
                    UpdateUI();
                }
                break;

            case IDC_BTN_CLEAR_LOG:
                {
                    SetWindowTextW(g_hListLog, L"");
                    AddLogMessage(L"日志已清除");
                }
                break;

            case IDC_BTN_VIEW_LOG_FILE:
                {
                    std::wstring strLogPath = L"C:\\Windows\\HyperPlatform.log";
                    
                    if (GetFileAttributesW(strLogPath.c_str()) != INVALID_FILE_ATTRIBUTES)
                    {
                        ShellExecuteW(NULL, L"open", L"notepad.exe", strLogPath.c_str(), NULL, SW_SHOW);
                    }
                    else
                    {
                        MessageBoxW(hDlg, (L"日志文件不存在: " + strLogPath).c_str(), L"提示", MB_OK | MB_ICONINFORMATION);
                    }
                }
                break;

            // REMOVED: Create hook button handler
            // Per ultimate simplification: Hooks are automatically created during write operations

            case IDC_BTN_EPT_READ:
                {
                    HandleEptReadMemory();
                }
                break;

            case IDC_BTN_EPT_WRITE:
                {
                    HandleEptWriteMemory();
                }
                break;

            case IDC_BTN_HIDE_PROCESS:
                {
                    HandleHideProcess();
                }
                break;

            case IDC_BTN_SHOW_PROCESS:
                {
                    HandleShowProcess();
                }
                break;

            case IDC_BTN_LIST_HIDDEN:
                {
                    HandleListHiddenProcesses();
                }
                break;

            case 1021: // IDC_BTN_GET_PROCESSES
                {
                    HandleGetProcesses();
                }
                break;

            // REMOVED: Manage hooks button handler
            // Per ultimate simplification: Hooks are automatically managed during read/write operations

            case IDCANCEL:
                CleanupServiceManager();
                EndDialog(hDlg, LOWORD(wParam));
                return TRUE;
            }
        }
        break;

    case WM_TIMER:
        {
            if (wParam == 1 && g_bMonitorStarted)
            {
                RefreshLogFromFile();
            }
        }
        break;

    case WM_CLOSE:
        CleanupServiceManager();
        EndDialog(hDlg, 0);
        return TRUE;
    }

    return FALSE;
}

// 更新界面状态
void UpdateUI()
{
    EnableWindow(GetDlgItem(g_hMainDlg, IDC_BTN_LOAD_DRIVER), !g_bDriverLoaded);
    EnableWindow(GetDlgItem(g_hMainDlg, IDC_BTN_UNLOAD_DRIVER), g_bDriverLoaded);
    EnableWindow(GetDlgItem(g_hMainDlg, IDC_BTN_START_MONITOR), g_bDriverLoaded && !g_bMonitorStarted);
    EnableWindow(GetDlgItem(g_hMainDlg, IDC_BTN_STOP_MONITOR), g_bMonitorStarted);

    // EPT memory operations are always available when driver is loaded
    EnableWindow(GetDlgItem(g_hMainDlg, IDC_BTN_EPT_READ), g_bDriverLoaded);
    EnableWindow(GetDlgItem(g_hMainDlg, IDC_BTN_EPT_WRITE), g_bDriverLoaded);
}

// 添加日志消息
void AddLogMessage(const std::wstring& message)
{
    // 获取当前时间
    SYSTEMTIME st;
    GetLocalTime(&st);

    wchar_t timeStr[64];
    swprintf_s(timeStr, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

    std::wstring fullMessage = timeStr + message + L"\r\n";

    // 获取当前编辑框内容
    int currentLength = GetWindowTextLengthW(g_hListLog);

    // 添加到编辑框末尾
    SendMessageW(g_hListLog, EM_SETSEL, currentLength, currentLength);
    SendMessageW(g_hListLog, EM_REPLACESEL, FALSE, (LPARAM)fullMessage.c_str());

    // 自动滚动到底部
    if (SendMessage(g_hCheckAutoScroll, BM_GETCHECK, 0, 0) == BST_CHECKED)
    {
        SendMessage(g_hListLog, EM_SCROLLCARET, 0, 0);
    }
}

// 设置状态
void SetStatus(const std::wstring& status)
{
    std::wstring fullStatus = L"状态: " + status;
    SetWindowTextW(g_hStaticStatus, fullStatus.c_str());
}

// 初始化服务管理器
void InitializeServiceManager()
{
    g_hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (g_hSCManager == NULL)
    {
        AddLogMessage(L"无法打开服务管理器");
    }
}

// 清理服务管理器
void CleanupServiceManager()
{
    if (g_hService)
    {
        CloseServiceHandle(g_hService);
        g_hService = NULL;
    }

    if (g_hSCManager)
    {
        CloseServiceHandle(g_hSCManager);
        g_hSCManager = NULL;
    }

    if (g_hLogFile != INVALID_HANDLE_VALUE)
    {
        CloseHandle(g_hLogFile);
        g_hLogFile = INVALID_HANDLE_VALUE;
    }

    if (g_nTimerID != 0)
    {
        KillTimer(g_hMainDlg, g_nTimerID);
        g_nTimerID = 0;
    }
}

// 加载驱动
bool LoadDriver(const std::wstring& driverPath)
{
    // 设置日志回调
    g_DriverManager.SetLogCallback([](const std::wstring& message) {
        AddLogMessage(message);
    });

    // 设置驱动路径和服务名
    g_DriverManager.SetDriverPath(driverPath);
    g_DriverManager.SetServiceName(L"DdiMon");

    // 加载驱动
    bool result = g_DriverManager.LoadDriver();

    if (result) {
        // 驱动加载成功后，建立DdiMonIOCTL连接
        AddLogMessage(L"正在建立驱动连接...");

        // 等待一小段时间让驱动完全初始化
        Sleep(1000);

        if (g_DdiMonIOCTL.Open()) {
            AddLogMessage(L"驱动连接建立成功");
        } else {
            AddLogMessage(L"警告: 驱动连接建立失败，进程隐藏功能可能不可用");
        }
    }

    return result;
}

// 卸载驱动
bool UnloadDriver()
{
    // 关闭DdiMonIOCTL连接
    if (g_DdiMonIOCTL.IsOpen()) {
        g_DdiMonIOCTL.Close();
        AddLogMessage(L"驱动连接已关闭");
    }

    // 使用DriverManager卸载驱动
    return g_DriverManager.UnloadDriver();
}

// 创建驱动服务
bool CreateDriverService(const std::wstring& driverPath)
{
    g_hService = CreateServiceW(
        g_hSCManager,
        g_strServiceName.c_str(),
        g_strServiceName.c_str(),
        SERVICE_ALL_ACCESS,
        SERVICE_KERNEL_DRIVER,
        SERVICE_DEMAND_START,
        SERVICE_ERROR_NORMAL,
        driverPath.c_str(),
        NULL, NULL, NULL, NULL, NULL
    );

    if (g_hService == NULL)
    {
        DWORD dwError = GetLastError();
        wchar_t errorMsg[256];
        swprintf_s(errorMsg, L"创建服务失败，错误代码: %d", dwError);
        AddLogMessage(errorMsg);
        return false;
    }

    AddLogMessage(L"驱动服务创建成功");
    return true;
}

// 删除驱动服务
bool DeleteDriverService()
{
    if (g_hService)
    {
        DeleteService(g_hService);
        CloseServiceHandle(g_hService);
        g_hService = NULL;
    }
    else
    {
        // 尝试打开可能存在的服务
        SC_HANDLE hService = OpenServiceW(g_hSCManager, g_strServiceName.c_str(), SERVICE_ALL_ACCESS);
        if (hService)
        {
            DeleteService(hService);
            CloseServiceHandle(hService);
        }
    }

    return true;
}

// 开始监控
bool StartMonitoring()
{
    // DdiMon驱动加载后会自动开始监控
    // 这里主要是打开日志文件进行监控
    std::wstring strLogPath = L"C:\\Windows\\HyperPlatform.log";

    g_hLogFile = CreateFileW(strLogPath.c_str(), GENERIC_READ, FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);

    if (g_hLogFile != INVALID_HANDLE_VALUE)
    {
        // 获取当前文件大小
        GetFileSizeEx(g_hLogFile, &g_lastFileSize);
        AddLogMessage(L"开始监控日志文件: " + strLogPath);
        return true;
    }
    else
    {
        AddLogMessage(L"无法打开日志文件: " + strLogPath);
        return false;
    }
}

// 停止监控
bool StopMonitoring()
{
    if (g_hLogFile != INVALID_HANDLE_VALUE)
    {
        CloseHandle(g_hLogFile);
        g_hLogFile = INVALID_HANDLE_VALUE;
    }

    AddLogMessage(L"停止监控日志文件");
    return true;
}

// 从日志文件刷新内容
void RefreshLogFromFile()
{
    if (g_hLogFile == INVALID_HANDLE_VALUE)
        return;

    LARGE_INTEGER currentSize;
    if (!GetFileSizeEx(g_hLogFile, &currentSize))
        return;

    // 检查文件是否有新内容
    if (currentSize.QuadPart > g_lastFileSize.QuadPart)
    {
        // 移动到上次读取的位置
        SetFilePointerEx(g_hLogFile, g_lastFileSize, NULL, FILE_BEGIN);

        // 读取新内容
        DWORD dwBytesToRead = (DWORD)(currentSize.QuadPart - g_lastFileSize.QuadPart);
        if (dwBytesToRead > 0 && dwBytesToRead < 1024 * 1024) // 限制最大1MB
        {
            char* buffer = new char[dwBytesToRead + 1];
            DWORD dwBytesRead = 0;

            if (ReadFile(g_hLogFile, buffer, dwBytesToRead, &dwBytesRead, NULL))
            {
                buffer[dwBytesRead] = '\0';

                // 转换为Unicode
                int wideSize = MultiByteToWideChar(CP_UTF8, 0, buffer, dwBytesRead, NULL, 0);
                if (wideSize > 0)
                {
                    wchar_t* wideBuffer = new wchar_t[wideSize + 1];
                    MultiByteToWideChar(CP_UTF8, 0, buffer, dwBytesRead, wideBuffer, wideSize);
                    wideBuffer[wideSize] = L'\0';

                    std::wstring strNewContent = wideBuffer;

                    // 按行分割并添加
                    size_t start = 0;
                    size_t end = strNewContent.find(L'\n');
                    while (end != std::wstring::npos)
                    {
                        std::wstring line = strNewContent.substr(start, end - start);
                        if (!line.empty() && line.back() == L'\r')
                        {
                            line.pop_back(); // 移除\r
                        }
                        if (!line.empty())
                        {
                            AddLogMessage(L"[DdiMon] " + line);
                        }
                        start = end + 1;
                        end = strNewContent.find(L'\n', start);
                    }

                    // 处理最后一行
                    if (start < strNewContent.length())
                    {
                        std::wstring line = strNewContent.substr(start);
                        if (!line.empty() && line.back() == L'\r')
                        {
                            line.pop_back();
                        }
                        if (!line.empty())
                        {
                            AddLogMessage(L"[DdiMon] " + line);
                        }
                    }

                    delete[] wideBuffer;
                }
            }

            delete[] buffer;
        }

        g_lastFileSize = currentSize;
    }
}

// 辅助函数：将十六进制字符串转换为字节数组
std::vector<BYTE> HexStringToBytes(const std::wstring& hexStr) {
    std::vector<BYTE> bytes;
    for (size_t i = 0; i < hexStr.length(); i += 2) {
        if (i + 1 < hexStr.length()) {
            std::wstring byteStr = hexStr.substr(i, 2);
            BYTE byte = (BYTE)wcstoul(byteStr.c_str(), nullptr, 16);
            bytes.push_back(byte);
        }
    }
    return bytes;
}

// 辅助函数：将字节数组转换为十六进制字符串
std::wstring BytesToHexString(const BYTE* data, size_t length) {
    std::wstringstream ss;
    ss << std::hex << std::uppercase << std::setfill(L'0');
    for (size_t i = 0; i < length; ++i) {
        ss << std::setw(2) << data[i];
        if (i < length - 1) ss << L" ";
    }
    return ss.str();
}

// 辅助函数：添加结果到结果窗口
void AddResultMessage(const std::wstring& message) {
    if (g_hEditResults) {
        // 获取当前文本长度
        int textLength = GetWindowTextLengthW(g_hEditResults);

        // 移动到文本末尾
        SendMessageW(g_hEditResults, EM_SETSEL, textLength, textLength);

        // 添加新消息
        std::wstring newMessage = message + L"\r\n";
        SendMessageW(g_hEditResults, EM_REPLACESEL, FALSE, (LPARAM)newMessage.c_str());

        // 滚动到底部
        SendMessageW(g_hEditResults, EM_SCROLLCARET, 0, 0);
    }
}

// REMOVED: HandleCreateHook function
// Per ultimate simplification: Hooks are automatically created during write operations
/*
void HandleCreateHook() {
    // 检查驱动是否已加载
    if (!g_bDriverLoaded) {
        MessageBoxW(g_hMainDlg, L"请先加载DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 检查是否已选择进程
    if (g_selectedProcessId == 0) {
        MessageBoxW(g_hMainDlg, L"请先选择目标进程！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 获取用户输入的目标地址
    wchar_t addressText[32];
    GetWindowTextW(GetDlgItem(g_hMainDlg, IDC_EDIT_ADDRESS), addressText, 32);

    ULONG64 targetAddress = 0;
    if (wcslen(addressText) == 0) {
        // 如果没有输入地址，使用默认地址
        targetAddress = 0x400000;  // 标准PE模块基址
        AddLogMessage(L"未指定地址，使用默认地址: 0x400000");
    } else {
        // 解析用户输入的地址
        targetAddress = _wcstoui64(addressText, nullptr, 16);
        if (targetAddress == 0) {
            MessageBoxW(g_hMainDlg, L"无效的地址格式！请输入十六进制地址（如：400000）", L"错误", MB_OK | MB_ICONERROR);
            return;
        }
    }

    AddLogMessage(L"开始创建页面级EPT钩子...");
    AddLogMessage(std::wstring(L"目标进程: ") + std::to_wstring(g_selectedProcessId));

    // 正确显示十六进制地址
    wchar_t addressDisplayText[64];
    swprintf_s(addressDisplayText, L"目标地址: 0x%llX", targetAddress);
    AddLogMessage(addressDisplayText);

    // 使用DdiMonIOCTL类创建页面级钩子
    DdiMonIOCTL ddimon;
    if (!ddimon.Open()) {
        AddLogMessage(L"无法连接到DdiMon驱动！");
        return;
    }

    // 创建页面级EPT钩子（包含目标进程ID）
    // 驱动端会自动检查重复钩子并返回相应错误
    EPT_HOOK_CREATE_REQUEST request = {};
    request.VirtualAddress = targetAddress;
    request.EnableExecOnly = TRUE;  // 启用执行钩子
    request.ProcessId = g_selectedProcessId;  // 传递目标进程ID

    EPT_HOOK_CREATE_RESPONSE response = {};
    ULONG bytesReturned = 0;

    bool result = ddimon.DeviceIoControl(
        IOCTL_DDIMON_CREATE_EPT_HOOK,
        &request, sizeof(request),
        &response, sizeof(response),
        &bytesReturned
    );

    if (result && NT_SUCCESS(response.Status)) {
        g_bHookCreated = true;  // 设置Hook创建状态
        g_currentHookId = response.HookId;  // 保存当前钩子ID

        AddLogMessage(L"页面级EPT钩子创建成功！");
        AddLogMessage(std::wstring(L"钩子ID: ") + std::to_wstring(response.HookId));

        // 正确显示十六进制地址
        wchar_t pageDisplayText[64];
        swprintf_s(pageDisplayText, L"目标页面: 0x%llX", targetAddress & ~0xFFF);
        AddLogMessage(pageDisplayText);

        wchar_t shadowDisplayText[64];
        swprintf_s(shadowDisplayText, L"影子页面PA: 0x%llX", response.ShadowPagePA);
        AddLogMessage(shadowDisplayText);
        AddLogMessage(L"钩子范围: 单个4KB页面 (高效安全)");
        AddLogMessage(L"现在可以读写该页面的内存");
        UpdateHookUI();  // 更新UI状态
    } else {
        g_bHookCreated = false;
        AddLogMessage(L"页面级EPT钩子创建失败！");

        // 详细的错误诊断
        if (!result) {
            DWORD lastError = GetLastError();
            wchar_t errorMsg[256];
            swprintf_s(errorMsg, L"DeviceIoControl失败: GetLastError=0x%08X", lastError);
            AddLogMessage(errorMsg);

            // 检查常见错误
            if (lastError == ERROR_INVALID_FUNCTION) {
                AddLogMessage(L"错误：驱动不支持此IOCTL代码");
            } else if (lastError == ERROR_FILE_NOT_FOUND) {
                AddLogMessage(L"错误：设备未找到");
            } else if (lastError == ERROR_ACCESS_DENIED) {
                AddLogMessage(L"错误：访问被拒绝");
            }
        } else {
            wchar_t statusMsg[256];
            swprintf_s(statusMsg, L"驱动返回错误: Status=0x%08X", response.Status);
            AddLogMessage(statusMsg);

            // 检查特定错误类型
            if (response.Status == 0xC0000035) { // STATUS_OBJECT_NAME_COLLISION
                AddLogMessage(L"错误原因：该地址已存在钩子");
                AddLogMessage(L"解决方案：请先移除现有钩子或选择其他地址");
            }
        }

        // 显示请求详情用于调试
        wchar_t debugMsg[512];
        swprintf_s(debugMsg, L"调试信息: IOCTL=0x%08X, RequestSize=%zu, ResponseSize=%zu, BytesReturned=%lu",
                   IOCTL_DDIMON_CREATE_EPT_HOOK, sizeof(request), sizeof(response), bytesReturned);
        AddLogMessage(debugMsg);

        UpdateHookUI();  // 更新UI状态
    }
}
*/

// REMOVED: UpdateHookUI function
// Per ultimate simplification: No hook status management needed

// 处理EPT读取内存操作
void HandleEptReadMemory() {
    // 检查驱动是否已加载
    if (!g_bDriverLoaded) {
        MessageBoxW(g_hMainDlg, L"请先加载DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // REMOVED: Hook creation check
    // Per ultimate simplification: Hooks are automatically created during read/write operations

    // 获取输入参数
    wchar_t szProcessId[32], szAddress[64], szSize[32];
    GetWindowTextW(g_hEditProcessId, szProcessId, 32);
    GetWindowTextW(g_hEditAddress, szAddress, 64);
    GetWindowTextW(g_hEditSize, szSize, 32);

    if (wcslen(szProcessId) == 0 || wcslen(szAddress) == 0 || wcslen(szSize) == 0) {
        MessageBoxW(g_hMainDlg, L"请填写完整的进程ID、地址和大小！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    ULONG processId = wcstoul(szProcessId, nullptr, 10);
    ULONG64 address = wcstoull(szAddress, nullptr, 16);
    ULONG size = wcstoul(szSize, nullptr, 10);

    if (processId == 0 || processId > 65535) {
        MessageBoxW(g_hMainDlg, L"进程ID无效！请输入有效的进程ID（1-65535）", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 验证进程是否存在
    if (!IsProcessValid(processId)) {
        wchar_t errorMsg[256];
        swprintf_s(errorMsg, L"进程ID %d 不存在或已退出！\n请使用'获取进程'按钮查看当前运行的进程。", processId);
        MessageBoxW(g_hMainDlg, errorMsg, L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    if (address == 0) {
        MessageBoxW(g_hMainDlg, L"地址无效！请输入有效的十六进制地址", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    if (size == 0 || size > 1024) {
        MessageBoxW(g_hMainDlg, L"读取大小必须在1-1024字节之间！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 打开DdiMon设备
    if (!g_DdiMonIOCTL.Open()) {
        MessageBoxW(g_hMainDlg, L"无法连接到DdiMon驱动！请确保驱动已正确加载。", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 执行EPT读取操作
    std::vector<BYTE> buffer(size);
    ULONG bytesRead = 0;

    // EPT读取内存操作 - 不自动创建钩子

    if (g_DdiMonIOCTL.EptReadMemory(processId, address, buffer.data(), size, &bytesRead)) {
        // 成功读取
        std::wstring hexData = BytesToHexString(buffer.data(), bytesRead);
        SetWindowTextW(g_hEditData, hexData.c_str());

        wchar_t resultMsg[512];
        swprintf_s(resultMsg, L"EPT读取成功: PID=%d, 地址=0x%llX, 读取了%d字节",
                  processId, address, bytesRead);
        AddResultMessage(resultMsg);
        AddLogMessage(resultMsg);

        // 读取成功，不更新钩子状态（读取操作不涉及钩子管理）
    } else {
        AddResultMessage(L"EPT读取失败！");
        AddResultMessage(L"可能原因：");
        AddResultMessage(L"1. 进程已退出或进程ID无效");
        AddResultMessage(L"2. 目标地址无效或无法访问");
        AddResultMessage(L"3. 内存保护机制阻止访问");
        AddResultMessage(L"4. 驱动权限不足");
        AddResultMessage(L"");
        AddResultMessage(L"建议：");
        AddResultMessage(L"1. 使用'获取进程'按钮确认进程ID");
        AddResultMessage(L"2. 确保地址在进程的有效内存范围内");
        AddResultMessage(L"3. 尝试读取较小的数据量");
        AddLogMessage(L"EPT读取操作失败");
    }
}

// 处理EPT写入内存操作
void HandleEptWriteMemory() {
    // 检查驱动是否已加载
    if (!g_bDriverLoaded) {
        MessageBoxW(g_hMainDlg, L"请先加载DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // REMOVED: Hook creation check
    // Per ultimate simplification: Hooks are automatically created during write operations

    // 获取输入参数
    wchar_t szProcessId[32], szAddress[64], szData[2048];
    GetWindowTextW(g_hEditProcessId, szProcessId, 32);
    GetWindowTextW(g_hEditAddress, szAddress, 64);
    GetWindowTextW(g_hEditData, szData, 2048);

    if (wcslen(szProcessId) == 0 || wcslen(szAddress) == 0 || wcslen(szData) == 0) {
        MessageBoxW(g_hMainDlg, L"请填写完整的进程ID、地址和数据！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    ULONG processId = wcstoul(szProcessId, nullptr, 10);
    ULONG64 address = wcstoull(szAddress, nullptr, 16);

    if (processId == 0 || processId > 65535) {
        MessageBoxW(g_hMainDlg, L"进程ID无效！请输入有效的进程ID（1-65535）", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 验证进程是否存在
    if (!IsProcessValid(processId)) {
        wchar_t errorMsg[256];
        swprintf_s(errorMsg, L"进程ID %d 不存在或已退出！\n请使用'获取进程'按钮查看当前运行的进程。", processId);
        MessageBoxW(g_hMainDlg, errorMsg, L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    if (address == 0) {
        MessageBoxW(g_hMainDlg, L"地址无效！请输入有效的十六进制地址", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 检查数据是否为空
    if (wcslen(szData) == 0) {
        MessageBoxW(g_hMainDlg, L"请填写要写入的数据！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 将十六进制字符串转换为字节数组
    std::wstring hexStr = szData;
    // 移除空格
    hexStr.erase(std::remove(hexStr.begin(), hexStr.end(), L' '), hexStr.end());

    if (hexStr.empty()) {
        MessageBoxW(g_hMainDlg, L"请填写要写入的数据！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    if (hexStr.length() % 2 != 0) {
        MessageBoxW(g_hMainDlg, L"数据格式错误！请输入有效的十六进制数据（如：41 42 43 或 414243）", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    std::vector<BYTE> data = HexStringToBytes(hexStr);
    if (data.empty() || data.size() > 1024) {
        MessageBoxW(g_hMainDlg, L"数据大小必须在1-1024字节之间！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 打开DdiMon设备
    if (!g_DdiMonIOCTL.Open()) {
        MessageBoxW(g_hMainDlg, L"无法连接到DdiMon驱动！请确保驱动已正确加载。", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 执行EPT写入操作
    ULONG bytesWritten = 0;

    // EPT写入内存操作 - 不自动创建钩子

    if (g_DdiMonIOCTL.EptWriteMemory(processId, address, data.data(), (ULONG)data.size(), &bytesWritten)) {
        // 成功写入
        wchar_t resultMsg[512];
        swprintf_s(resultMsg, L"EPT写入成功: PID=%d, 地址=0x%llX, 写入了%d字节",
                  processId, address, bytesWritten);
        AddResultMessage(resultMsg);
        AddLogMessage(resultMsg);

        // 写入成功，不更新钩子状态（写入操作不涉及钩子管理）
    } else {
        AddResultMessage(L"EPT写入失败！");
        AddResultMessage(L"可能原因：");
        AddResultMessage(L"1. 进程已退出或进程ID无效");
        AddResultMessage(L"2. 目标地址无效或无法访问");
        AddResultMessage(L"3. 内存保护机制阻止写入");
        AddResultMessage(L"4. 目标内存区域为只读");
        AddResultMessage(L"");
        AddResultMessage(L"建议：");
        AddResultMessage(L"1. 使用'获取进程'按钮确认进程ID");
        AddResultMessage(L"2. 确保地址在进程的可写内存范围内");
        AddResultMessage(L"3. 检查数据格式是否正确");
        AddLogMessage(L"EPT写入操作失败");
    }
}

// 处理检查EPT钩子操作
void HandleCheckEptHook() {
    // 检查驱动是否已加载
    if (!g_bDriverLoaded) {
        MessageBoxW(g_hMainDlg, L"请先加载DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 检查监控是否已启动
    if (!g_bMonitorStarted) {
        MessageBoxW(g_hMainDlg, L"请先启动监控以激活EPT钩子！", L"提示", MB_OK | MB_ICONWARNING);
        return;
    }

    // 获取地址参数
    wchar_t szAddress[64];
    GetWindowTextW(g_hEditAddress, szAddress, 64);

    if (wcslen(szAddress) == 0) {
        MessageBoxW(g_hMainDlg, L"请输入要检查的地址！", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    ULONG64 address = wcstoull(szAddress, nullptr, 16);

    // 打开DdiMon设备
    if (!g_DdiMonIOCTL.Open()) {
        MessageBoxW(g_hMainDlg, L"无法连接到DdiMon驱动！请确保驱动已正确加载。", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 显示简化的消息
    wchar_t resultMsg[256];
    swprintf_s(resultMsg, L"EPT钩子检查功能已简化: 地址=0x%llX", address);
    AddResultMessage(resultMsg);
    AddResultMessage(L"注意: 钩子状态通过读写内存操作自动管理");
    AddLogMessage(L"EPT钩子检查功能已简化 - 使用读写内存操作代替");
}

// 处理获取进程列表操作
void HandleGetProcesses() {
    // 弹出进程选择对话框
    INT_PTR result = DialogBox(GetModuleHandle(NULL), MAKEINTRESOURCE(IDD_PROCESS_SELECT), g_hMainDlg, ProcessSelectDlgProc);

    if (result > 0) {
        // 用户选择了进程，result是进程ID
        g_selectedProcessId = (DWORD)result;  // 更新全局变量

        wchar_t processIdStr[32];
        swprintf_s(processIdStr, L"%d", (DWORD)result);
        SetWindowTextW(GetDlgItem(g_hMainDlg, IDC_EDIT_PROCESS_ID), processIdStr);

        wchar_t logMsg[256];
        swprintf_s(logMsg, L"用户选择了进程ID: %d", (DWORD)result);
        AddLogMessage(logMsg);
        AddResultMessage(L"进程ID已自动填入，可以进行EPT操作");
    }
}

// 旧的HandleManageHooks函数已删除，使用新的钩子管理对话框

// 验证进程是否有效
bool IsProcessValid(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
    if (hProcess == NULL) {
        return false;
    }

    DWORD exitCode;
    bool isValid = GetExitCodeProcess(hProcess, &exitCode) && (exitCode == STILL_ACTIVE);
    CloseHandle(hProcess);

    return isValid;
}

// 进程选择对话框处理函数
INT_PTR CALLBACK ProcessSelectDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
        case WM_INITDIALOG:
            {
                // 设置中文标题和标签
                SetWindowTextW(hDlg, L"选择进程");
                SetWindowTextW(GetDlgItem(hDlg, 2025), L"双击进程名称进行选择:");
                SetWindowTextW(GetDlgItem(hDlg, IDCANCEL), L"取消");

                // 获取进程列表控件
                HWND hListBox = GetDlgItem(hDlg, 2024);

                // 获取进程快照
                HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
                if (hSnapshot == INVALID_HANDLE_VALUE) {
                    MessageBoxW(hDlg, L"获取进程快照失败！", L"错误", MB_OK | MB_ICONERROR);
                    EndDialog(hDlg, 0);
                    return TRUE;
                }

                PROCESSENTRY32W pe32;
                pe32.dwSize = sizeof(PROCESSENTRY32W);

                // 获取第一个进程
                if (!Process32FirstW(hSnapshot, &pe32)) {
                    CloseHandle(hSnapshot);
                    MessageBoxW(hDlg, L"枚举进程失败！", L"错误", MB_OK | MB_ICONERROR);
                    EndDialog(hDlg, 0);
                    return TRUE;
                }

                // 添加进程到列表框
                do {
                    wchar_t processInfo[512];
                    swprintf_s(processInfo, L"%s (%d)", pe32.szExeFile, pe32.th32ProcessID);

                    int index = SendMessageW(hListBox, LB_ADDSTRING, 0, (LPARAM)processInfo);
                    // 将进程ID存储为项目数据
                    SendMessageW(hListBox, LB_SETITEMDATA, index, pe32.th32ProcessID);

                } while (Process32NextW(hSnapshot, &pe32));

                CloseHandle(hSnapshot);
                return TRUE;
            }

        case WM_COMMAND:
            switch (LOWORD(wParam)) {
                case 2024: // IDC_LIST_PROCESSES
                    if (HIWORD(wParam) == LBN_DBLCLK) {
                        // 双击列表项
                        HWND hListBox = GetDlgItem(hDlg, 2024);
                        int selectedIndex = SendMessageW(hListBox, LB_GETCURSEL, 0, 0);

                        if (selectedIndex != LB_ERR) {
                            // 获取选中项的进程ID
                            DWORD processId = (DWORD)SendMessageW(hListBox, LB_GETITEMDATA, selectedIndex, 0);

                            // 验证进程是否仍然有效
                            if (IsProcessValid(processId)) {
                                EndDialog(hDlg, processId);
                            } else {
                                MessageBoxW(hDlg, L"选择的进程已退出，请重新选择！", L"提示", MB_OK | MB_ICONWARNING);
                                // 刷新列表
                                SendMessage(hDlg, WM_INITDIALOG, 0, 0);
                            }
                        }
                        return TRUE;
                    }
                    break;

                case IDCANCEL:
                    EndDialog(hDlg, 0);
                    return TRUE;
            }
            break;

        case WM_CLOSE:
            EndDialog(hDlg, 0);
            return TRUE;
    }

    return FALSE;
}

// REMOVED: Hook management functionality
// Per ultimate simplification: Hooks are automatically managed during read/write operations

// REMOVED: Hook manager dialog procedure
// Per ultimate simplification: No hook management dialog needed
/*
INT_PTR CALLBACK HookManagerDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam) {
    static HWND hListHooks = NULL;
    static HWND hStaticCount = NULL;

    switch (message) {
        case WM_INITDIALOG:
            {
                // 初始化控件
                hListHooks = GetDlgItem(hDlg, IDC_LIST_HOOKS);
                hStaticCount = GetDlgItem(hDlg, IDC_STATIC_HOOK_COUNT);

                // 设置列表视图样式
                ListView_SetExtendedListViewStyle(hListHooks, LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES);

                // 添加列
                LVCOLUMN lvc = {};
                lvc.mask = LVCF_TEXT | LVCF_WIDTH;

                wchar_t col1[] = L"钩子ID";
                lvc.pszText = col1;
                lvc.cx = 80;
                ListView_InsertColumn(hListHooks, 0, &lvc);

                wchar_t col2[] = L"进程ID";
                lvc.pszText = col2;
                lvc.cx = 80;
                ListView_InsertColumn(hListHooks, 1, &lvc);

                wchar_t col3[] = L"目标地址";
                lvc.pszText = col3;
                lvc.cx = 120;
                ListView_InsertColumn(hListHooks, 2, &lvc);

                wchar_t col4[] = L"影子页面PA";
                lvc.pszText = col4;
                lvc.cx = 120;
                ListView_InsertColumn(hListHooks, 3, &lvc);

                wchar_t col5[] = L"状态";
                lvc.pszText = col5;
                lvc.cx = 60;
                ListView_InsertColumn(hListHooks, 4, &lvc);

                wchar_t col6[] = L"创建时间";
                lvc.pszText = col6;
                lvc.cx = 140;
                ListView_InsertColumn(hListHooks, 5, &lvc);

                // 刷新钩子列表
                SendMessage(hDlg, WM_COMMAND, IDC_BTN_REFRESH_HOOKS, 0);

                return TRUE;
            }

        case WM_COMMAND:
            switch (LOWORD(wParam)) {
                case IDC_BTN_REFRESH_HOOKS:
                    {
                        // 清空列表
                        ListView_DeleteAllItems(hListHooks);

                        // 获取钩子列表
                        DdiMonIOCTL ddimon;
                        if (!ddimon.Open()) {
                            MessageBoxW(hDlg, L"无法连接到DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
                            break;
                        }

                        // 准备请求
                        EPT_HOOK_LIST_REQUEST request = {};
                        request.MaxCount = 100;  // 最多100个钩子

                        // 分配响应缓冲区
                        ULONG responseSize = sizeof(EPT_HOOK_LIST_RESPONSE) + sizeof(EPT_HOOK_INFO) * 100;
                        PEPT_HOOK_LIST_RESPONSE response = (PEPT_HOOK_LIST_RESPONSE)malloc(responseSize);
                        if (!response) {
                            MessageBoxW(hDlg, L"内存分配失败！", L"错误", MB_OK | MB_ICONERROR);
                            break;
                        }

                        ULONG bytesReturned = 0;
                        bool result = ddimon.DeviceIoControl(
                            IOCTL_EPT_HOOK_LIST,
                            &request, sizeof(request),
                            response, responseSize,
                            &bytesReturned
                        );

                        if (result && NT_SUCCESS(response->Status)) {
                            // 添加钩子到列表
                            for (ULONG i = 0; i < response->HookCount; i++) {
                                EPT_HOOK_INFO* hook = &response->Hooks[i];

                                LVITEM lvi = {};
                                lvi.mask = LVIF_TEXT;
                                lvi.iItem = i;

                                // 钩子ID
                                wchar_t hookIdText[32];
                                swprintf_s(hookIdText, L"%llu", hook->HookId);
                                lvi.pszText = hookIdText;
                                lvi.iSubItem = 0;
                                ListView_InsertItem(hListHooks, &lvi);

                                // 进程ID
                                wchar_t processIdText[32];
                                swprintf_s(processIdText, L"%llu", hook->ProcessId);
                                ListView_SetItemText(hListHooks, i, 1, processIdText);

                                // 目标地址
                                wchar_t addressText[32];
                                swprintf_s(addressText, L"0x%llX", hook->TargetAddress);
                                ListView_SetItemText(hListHooks, i, 2, addressText);

                                // 影子页面PA
                                wchar_t shadowPAText[32];
                                swprintf_s(shadowPAText, L"0x%llX", hook->ShadowPagePA);
                                ListView_SetItemText(hListHooks, i, 3, shadowPAText);

                                // 状态
                                wchar_t statusText[16];
                                wcscpy_s(statusText, hook->IsActive ? L"激活" : L"未激活");
                                ListView_SetItemText(hListHooks, i, 4, statusText);

                                // 创建时间（简化显示）
                                wchar_t timeText[16];
                                wcscpy_s(timeText, L"刚刚");
                                ListView_SetItemText(hListHooks, i, 5, timeText);
                            }

                            // 更新钩子数量
                            wchar_t countText[64];
                            swprintf_s(countText, L"当前钩子数量: %d", response->HookCount);
                            SetWindowTextW(hStaticCount, countText);
                        } else {
                            MessageBoxW(hDlg, L"获取钩子列表失败！", L"错误", MB_OK | MB_ICONERROR);
                            SetWindowTextW(hStaticCount, L"当前钩子数量: 0");
                        }

                        free(response);
                    }
                    break;

                case IDC_BTN_REMOVE_HOOK:
                    {
                        // 获取选中的钩子
                        int selectedIndex = ListView_GetNextItem(hListHooks, -1, LVNI_SELECTED);
                        if (selectedIndex == -1) {
                            MessageBoxW(hDlg, L"请先选择要删除的钩子！", L"提示", MB_OK | MB_ICONINFORMATION);
                            break;
                        }

                        // 获取钩子ID
                        wchar_t hookIdText[32];
                        ListView_GetItemText(hListHooks, selectedIndex, 0, hookIdText, 32);
                        ULONG64 hookId = _wcstoui64(hookIdText, nullptr, 10);

                        // 确认删除
                        wchar_t confirmText[128];
                        swprintf_s(confirmText, L"确定要删除钩子 %llu 吗？", hookId);
                        if (MessageBoxW(hDlg, confirmText, L"确认删除", MB_YESNO | MB_ICONQUESTION) != IDYES) {
                            break;
                        }

                        // 删除钩子
                        DdiMonIOCTL ddimon;
                        if (!ddimon.Open()) {
                            MessageBoxW(hDlg, L"无法连接到DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
                            break;
                        }

                        EPT_HOOK_REMOVE_REQUEST request = {};
                        request.HookId = hookId;

                        EPT_HOOK_REMOVE_RESPONSE response = {};
                        ULONG bytesReturned = 0;

                        bool result = ddimon.DeviceIoControl(
                            IOCTL_EPT_HOOK_REMOVE,
                            &request, sizeof(request),
                            &response, sizeof(response),
                            &bytesReturned
                        );

                        if (result && NT_SUCCESS(response.Status)) {
                            MessageBoxW(hDlg, L"钩子删除成功！", L"成功", MB_OK | MB_ICONINFORMATION);
                            // 刷新列表
                            SendMessage(hDlg, WM_COMMAND, IDC_BTN_REFRESH_HOOKS, 0);
                        } else {
                            MessageBoxW(hDlg, L"钩子删除失败！", L"错误", MB_OK | MB_ICONERROR);
                        }
                    }
                    break;

                case IDC_BTN_REMOVE_ALL_HOOKS:
                    {
                        // 确认删除所有钩子
                        if (MessageBoxW(hDlg, L"确定要删除所有钩子吗？此操作不可撤销！", L"确认删除", MB_YESNO | MB_ICONWARNING) != IDYES) {
                            break;
                        }

                        // 删除所有钩子
                        DdiMonIOCTL ddimon;
                        if (!ddimon.Open()) {
                            MessageBoxW(hDlg, L"无法连接到DdiMon驱动！", L"错误", MB_OK | MB_ICONERROR);
                            break;
                        }

                        ULONG bytesReturned = 0;
                        bool result = ddimon.DeviceIoControl(
                            IOCTL_DDIMON_REMOVE_ALL_HOOKS,
                            nullptr, 0,
                            nullptr, 0,
                            &bytesReturned
                        );

                        if (result) {
                            MessageBoxW(hDlg, L"所有钩子删除成功！", L"成功", MB_OK | MB_ICONINFORMATION);
                            // 刷新列表
                            SendMessage(hDlg, WM_COMMAND, IDC_BTN_REFRESH_HOOKS, 0);
                        } else {
                            MessageBoxW(hDlg, L"删除钩子失败！", L"错误", MB_OK | MB_ICONERROR);
                        }
                    }
                    break;

                case IDCANCEL:
                    EndDialog(hDlg, 0);
                    return TRUE;
            }
            break;

        case WM_CLOSE:
            EndDialog(hDlg, 0);
            return TRUE;
    }

    return FALSE;
}
*/

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding functions
//

// Handle hide process button
void HandleHideProcess() {
    if (!g_DdiMonIOCTL.IsOpen()) {
        // 尝试重新连接
        AddLogMessage(L"驱动程序未连接，尝试重新连接...");
        if (!g_DdiMonIOCTL.Open()) {
            AddLogMessage(L"错误: 无法连接到驱动程序，请确保驱动已正确加载");
            return;
        }
        AddLogMessage(L"驱动连接重新建立成功");
    }

    // 首先测试IOCTL通信是否正常
    std::vector<HIDDEN_PROCESS_INFO> testProcesses;
    if (g_DdiMonIOCTL.ListHiddenProcesses(testProcesses)) {
        AddLogMessage(L"IOCTL通信测试成功");
    } else {
        DWORD lastError = GetLastError();
        std::wstringstream ss;
        ss << L"IOCTL通信测试失败 (错误代码: " << lastError << L")";
        AddLogMessage(ss.str());
        return;
    }

    // Get process ID from edit control
    wchar_t szProcessId[32];
    GetWindowTextW(g_hEditHideProcessId, szProcessId, 32);

    ULONG processId = _wtol(szProcessId);
    if (processId == 0) {
        AddLogMessage(L"错误: 请输入有效的进程ID");
        return;
    }

    // Hide the process
    bool previousState = false;
    if (g_DdiMonIOCTL.HideProcess(processId, &previousState)) {
        std::wstringstream ss;
        ss << L"成功隐藏进程 PID=" << processId;
        if (previousState) {
            ss << L" (之前已隐藏)";
        }
        AddLogMessage(ss.str());

        // Refresh hidden process list
        HandleListHiddenProcesses();
    } else {
        std::wstringstream ss;
        ss << L"隐藏进程失败 PID=" << processId;

        // 尝试获取更详细的错误信息
        DWORD lastError = GetLastError();
        if (lastError != 0) {
            ss << L" (错误代码: " << lastError << L")";
        }

        // 检查进程是否存在
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == NULL) {
            ss << L" - 进程不存在或无权限访问";
        } else {
            CloseHandle(hProcess);
            ss << L" - 进程存在但隐藏失败";
        }

        AddLogMessage(ss.str());
    }
}

// Handle show process button
void HandleShowProcess() {
    if (!g_DdiMonIOCTL.IsOpen()) {
        // 尝试重新连接
        AddLogMessage(L"驱动程序未连接，尝试重新连接...");
        if (!g_DdiMonIOCTL.Open()) {
            AddLogMessage(L"错误: 无法连接到驱动程序，请确保驱动已正确加载");
            return;
        }
        AddLogMessage(L"驱动连接重新建立成功");
    }

    // Get process ID from edit control
    wchar_t szProcessId[32];
    GetWindowTextW(g_hEditHideProcessId, szProcessId, 32);

    ULONG processId = _wtol(szProcessId);
    if (processId == 0) {
        AddLogMessage(L"错误: 请输入有效的进程ID");
        return;
    }

    // Show the process
    bool previousState = false;
    if (g_DdiMonIOCTL.ShowProcess(processId, &previousState)) {
        std::wstringstream ss;
        ss << L"成功显示进程 PID=" << processId;
        if (!previousState) {
            ss << L" (之前未隐藏)";
        }
        AddLogMessage(ss.str());

        // Refresh hidden process list
        HandleListHiddenProcesses();
    } else {
        std::wstringstream ss;
        ss << L"显示进程失败 PID=" << processId;
        AddLogMessage(ss.str());
    }
}

// Handle list hidden processes button
void HandleListHiddenProcesses() {
    if (!g_DdiMonIOCTL.IsOpen()) {
        // 尝试重新连接
        AddLogMessage(L"驱动程序未连接，尝试重新连接...");
        if (!g_DdiMonIOCTL.Open()) {
            AddLogMessage(L"错误: 无法连接到驱动程序，请确保驱动已正确加载");
            return;
        }
        AddLogMessage(L"驱动连接重新建立成功");
    }

    // Clear the list
    SendMessage(g_hListHiddenProcesses, LB_RESETCONTENT, 0, 0);

    // Get hidden processes
    std::vector<HIDDEN_PROCESS_INFO> processes;
    if (g_DdiMonIOCTL.ListHiddenProcesses(processes)) {
        if (processes.empty()) {
            SendMessage(g_hListHiddenProcesses, LB_ADDSTRING, 0, (LPARAM)L"(无隐藏进程)");
            AddLogMessage(L"当前无隐藏进程");
        } else {
            for (const auto& proc : processes) {
                std::wstringstream ss;
                ss << L"PID=" << proc.ProcessId << L" Name=" << proc.ProcessName;

                std::wstring item = ss.str();
                SendMessage(g_hListHiddenProcesses, LB_ADDSTRING, 0, (LPARAM)item.c_str());
            }

            std::wstringstream ss;
            ss << L"列出 " << processes.size() << L" 个隐藏进程";
            AddLogMessage(ss.str());
        }
    } else {
        AddLogMessage(L"获取隐藏进程列表失败");
        SendMessage(g_hListHiddenProcesses, LB_ADDSTRING, 0, (LPARAM)L"(获取失败)");
    }
}
