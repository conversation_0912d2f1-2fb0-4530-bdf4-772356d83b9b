# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_MICRO, None
0xe6,0x00,0x10,0x49 = add $9, $6, $7
0x26,0x11,0x67,0x45 = addi $9, $6, 17767
0x26,0x31,0x67,0xc5 = addiu $9, $6, -15001
0x26,0x11,0x67,0x45 = addi $9, $6, 17767
0x26,0x31,0x67,0xc5 = addiu $9, $6, -15001
0xe6,0x00,0x50,0x49 = addu $9, $6, $7
0xe6,0x00,0x90,0x49 = sub $9, $6, $7
0xa3,0x00,0xd0,0x21 = subu $4, $3, $5
0xe0,0x00,0x90,0x31 = neg $6, $7
0xe0,0x00,0xd0,0x31 = negu $6, $7
0x08,0x00,0x50,0x39 = move $7, $8
0xa3,0x00,0x50,0x1b = slt $3, $3, $5
0x63,0x90,0x67,0x00 = slti $3, $3, 103
0x63,0x90,0x67,0x00 = slti $3, $3, 103
0x63,0xb0,0x67,0x00 = sltiu $3, $3, 103
0xa3,0x00,0x90,0x1b = sltu $3, $3, $5
0xa9,0x41,0x67,0x45 = lui $9, 17767
0xe6,0x00,0x50,0x4a = and $9, $6, $7
0x26,0xd1,0x67,0x45 = andi $9, $6, 17767
0x26,0xd1,0x67,0x45 = andi $9, $6, 17767
0xa4,0x00,0x90,0x1a = or $3, $4, $5
0x26,0x51,0x67,0x45 = ori $9, $6, 17767
0xa3,0x00,0x10,0x1b = xor $3, $3, $5
0x26,0x71,0x67,0x45 = xori $9, $6, 17767
0x26,0x71,0x67,0x45 = xori $9, $6, 17767
0xe6,0x00,0xd0,0x4a = nor $9, $6, $7
0x08,0x00,0xd0,0x3a = not $7, $8
0xe6,0x00,0x10,0x4a = mul $9, $6, $7
0xe9,0x00,0x3c,0x8b = mult $9, $7
0xe9,0x00,0x3c,0x9b = multu $9, $7
0xe9,0x00,0x3c,0xab = div $zero, $9, $7
0xe9,0x00,0x3c,0xbb = divu $zero, $9, $7
