/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    2566U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    2559U,	// BUNDLE
    2622U,	// LIFETIME_START
    2546U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    2637U,	// AAA
    4269U,	// AAD8i8
    4749U,	// AAM8i8
    3179U,	// AAS
    2301U,	// ACQUIRE_MOV16rm
    2301U,	// ACQUIRE_MOV32rm
    2301U,	// ACQUIRE_MOV64rm
    2301U,	// ACQUIRE_MOV8rm
    5486U,	// ADC16i16
    139395U,	// ADC16mi
    139395U,	// ADC16mi8
    139395U,	// ADC16mr
    2240643U,	// ADC16ri
    2240643U,	// ADC16ri8
    4337795U,	// ADC16rm
    2240643U,	// ADC16rr
    2232451U,	// ADC16rr_REV
    5622U,	// ADC32i32
    147587U,	// ADC32mi
    147587U,	// ADC32mi8
    147587U,	// ADC32mr
    2240643U,	// ADC32ri
    2240643U,	// ADC32ri8
    6434947U,	// ADC32rm
    2240643U,	// ADC32rr
    2232451U,	// ADC32rr_REV
    5770U,	// ADC64i32
    151683U,	// ADC64mi32
    151683U,	// ADC64mi8
    151683U,	// ADC64mr
    2240643U,	// ADC64ri32
    2240643U,	// ADC64ri8
    8532099U,	// ADC64rm
    2240643U,	// ADC64rr
    2232451U,	// ADC64rr_REV
    5384U,	// ADC8i8
    155779U,	// ADC8mi
    155779U,	// ADC8mr
    2240643U,	// ADC8ri
    10629251U,	// ADC8rm
    2240643U,	// ADC8rr
    2232451U,	// ADC8rr_REV
    6427796U,	// ADCX32rm
    2233492U,	// ADCX32rr
    8524948U,	// ADCX64rm
    2233492U,	// ADCX64rr
    5495U,	// ADD16i16
    139451U,	// ADD16mi
    139451U,	// ADD16mi8
    139451U,	// ADD16mr
    2240699U,	// ADD16ri
    2240699U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    4337851U,	// ADD16rm
    2240699U,	// ADD16rr
    0U,	// ADD16rr_DB
    2232507U,	// ADD16rr_REV
    5632U,	// ADD32i32
    147643U,	// ADD32mi
    147643U,	// ADD32mi8
    147643U,	// ADD32mr
    2240699U,	// ADD32ri
    2240699U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    6435003U,	// ADD32rm
    2240699U,	// ADD32rr
    0U,	// ADD32rr_DB
    2232507U,	// ADD32rr_REV
    5780U,	// ADD64i32
    151739U,	// ADD64mi32
    151739U,	// ADD64mi8
    151739U,	// ADD64mr
    2240699U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    2240699U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    8532155U,	// ADD64rm
    2240699U,	// ADD64rr
    0U,	// ADD64rr_DB
    2232507U,	// ADD64rr_REV
    5393U,	// ADD8i8
    155835U,	// ADD8mi
    155835U,	// ADD8mr
    2240699U,	// ADD8ri
    2240699U,	// ADD8ri8
    10629307U,	// ADD8rm
    2240699U,	// ADD8rr
    2232507U,	// ADD8rr_REV
    2576U,	// ADJCALLSTACKDOWN32
    2576U,	// ADJCALLSTACKDOWN64
    2594U,	// ADJCALLSTACKUP32
    2594U,	// ADJCALLSTACKUP64
    12719270U,	// ADOX32rm
    14816422U,	// ADOX32rr
    16913574U,	// ADOX64rm
    14816422U,	// ADOX64rr
    5504U,	// AND16i16
    139500U,	// AND16mi
    139500U,	// AND16mi8
    139500U,	// AND16mr
    2240748U,	// AND16ri
    2240748U,	// AND16ri8
    4337900U,	// AND16rm
    2240748U,	// AND16rr
    2232556U,	// AND16rr_REV
    5642U,	// AND32i32
    147692U,	// AND32mi
    147692U,	// AND32mi8
    147692U,	// AND32mr
    2240748U,	// AND32ri
    2240748U,	// AND32ri8
    6435052U,	// AND32rm
    2240748U,	// AND32rr
    2232556U,	// AND32rr_REV
    5790U,	// AND64i32
    151788U,	// AND64mi32
    151788U,	// AND64mi8
    151788U,	// AND64mr
    2240748U,	// AND64ri32
    2240748U,	// AND64ri8
    8532204U,	// AND64rm
    2240748U,	// AND64rr
    2232556U,	// AND64rr_REV
    5402U,	// AND8i8
    155884U,	// AND8mi
    155884U,	// AND8mr
    2240748U,	// AND8ri
    2240748U,	// AND8ri8
    10629356U,	// AND8rm
    2240748U,	// AND8rr
    2232556U,	// AND8rr_REV
    81924754U,	// ANDN32rm
    350360210U,	// ANDN32rr
    618795666U,	// ANDN64rm
    350360210U,	// ANDN64rr
    139887U,	// ARPL16mr
    14815855U,	// ARPL16rr
    885134227U,	// BEXTR32rm
    350360467U,	// BEXTR32rr
    889328531U,	// BEXTR64rm
    350360467U,	// BEXTR64rr
    885134227U,	// BEXTRI32mi
    350360467U,	// BEXTRI32ri
    889328531U,	// BEXTRI64mi
    350360467U,	// BEXTRI64ri
    12718680U,	// BLCFILL32rm
    14815832U,	// BLCFILL32rr
    16912984U,	// BLCFILL64rm
    14815832U,	// BLCFILL64rr
    12718613U,	// BLCI32rm
    14815765U,	// BLCI32rr
    16912917U,	// BLCI64rm
    14815765U,	// BLCI64rr
    12718221U,	// BLCIC32rm
    14815373U,	// BLCIC32rr
    16912525U,	// BLCIC64rm
    14815373U,	// BLCIC64rr
    12718631U,	// BLCMSK32rm
    14815783U,	// BLCMSK32rr
    16912935U,	// BLCMSK64rm
    14815783U,	// BLCMSK64rr
    12719010U,	// BLCS32rm
    14816162U,	// BLCS32rr
    16913314U,	// BLCS64rm
    14816162U,	// BLCS64rr
    12718689U,	// BLSFILL32rm
    14815841U,	// BLSFILL32rr
    16912993U,	// BLSFILL64rm
    14815841U,	// BLSFILL64rr
    12718625U,	// BLSI32rm
    14815777U,	// BLSI32rr
    16912929U,	// BLSI64rm
    14815777U,	// BLSI64rr
    12718228U,	// BLSIC32rm
    14815380U,	// BLSIC32rr
    16912532U,	// BLSIC64rm
    14815380U,	// BLSIC64rr
    12718639U,	// BLSMSK32rm
    14815791U,	// BLSMSK32rr
    16912943U,	// BLSMSK64rm
    14815791U,	// BLSMSK64rr
    12718974U,	// BLSR32rm
    14816126U,	// BLSR32rr
    16913278U,	// BLSR64rm
    14816126U,	// BLSR64rr
    12718321U,	// BOUNDS16rm
    16912625U,	// BOUNDS32rm
    19010013U,	// BSF16rm
    14815709U,	// BSF16rr
    12718557U,	// BSF32rm
    14815709U,	// BSF32rr
    16912861U,	// BSF64rm
    14815709U,	// BSF64rr
    19010425U,	// BSR16rm
    14816121U,	// BSR16rr
    12718969U,	// BSR32rm
    14816121U,	// BSR32rr
    16913273U,	// BSR64rm
    14816121U,	// BSR64rr
    4804U,	// BSWAP32r
    4804U,	// BSWAP64r
    140267U,	// BT16mi8
    140267U,	// BT16mr
    14816235U,	// BT16ri8
    14816235U,	// BT16rr
    148459U,	// BT32mi8
    148459U,	// BT32mr
    14816235U,	// BT32ri8
    14816235U,	// BT32rr
    152555U,	// BT64mi8
    152555U,	// BT64mr
    14816235U,	// BT64ri8
    14816235U,	// BT64rr
    139432U,	// BTC16mi8
    139432U,	// BTC16mr
    14815400U,	// BTC16ri8
    14815400U,	// BTC16rr
    147624U,	// BTC32mi8
    147624U,	// BTC32mr
    14815400U,	// BTC32ri8
    14815400U,	// BTC32rr
    151720U,	// BTC64mi8
    151720U,	// BTC64mr
    14815400U,	// BTC64ri8
    14815400U,	// BTC64rr
    140164U,	// BTR16mi8
    140164U,	// BTR16mr
    14816132U,	// BTR16ri8
    14816132U,	// BTR16rr
    148356U,	// BTR32mi8
    148356U,	// BTR32mr
    14816132U,	// BTR32ri8
    14816132U,	// BTR32rr
    152452U,	// BTR64mi8
    152452U,	// BTR64mr
    14816132U,	// BTR64ri8
    14816132U,	// BTR64rr
    140249U,	// BTS16mi8
    140249U,	// BTS16mr
    14816217U,	// BTS16ri8
    14816217U,	// BTS16rr
    148441U,	// BTS32mi8
    148441U,	// BTS32mr
    14816217U,	// BTS32ri8
    14816217U,	// BTS32rr
    152537U,	// BTS64mi8
    152537U,	// BTS64mr
    14816217U,	// BTS64ri8
    14816217U,	// BTS64rr
    885133851U,	// BZHI32rm
    350360091U,	// BZHI32rr
    889328155U,	// BZHI64rm
    350360091U,	// BZHI64rr
    8786U,	// CALL16m
    4690U,	// CALL16r
    16978U,	// CALL32m
    4690U,	// CALL32r
    21074U,	// CALL64m
    29266U,	// CALL64pcrel32
    4690U,	// CALL64r
    29266U,	// CALLpcrel16
    29266U,	// CALLpcrel32
    3378U,	// CBW
    3099U,	// CDQ
    2853U,	// CDQE
    2706U,	// CLAC
    2738U,	// CLC
    2788U,	// CLD
    2944U,	// CLGI
    2954U,	// CLI
    3322U,	// CLTS
    2742U,	// CMC
    4329519U,	// CMOVA16rm
    2232367U,	// CMOVA16rr
    6426671U,	// CMOVA32rm
    2232367U,	// CMOVA32rr
    8523823U,	// CMOVA64rm
    2232367U,	// CMOVA64rr
    4329773U,	// CMOVAE16rm
    2232621U,	// CMOVAE16rr
    6426925U,	// CMOVAE32rm
    2232621U,	// CMOVAE32rr
    8524077U,	// CMOVAE64rm
    2232621U,	// CMOVAE64rr
    4329596U,	// CMOVB16rm
    2232444U,	// CMOVB16rr
    6426748U,	// CMOVB32rm
    2232444U,	// CMOVB32rr
    8523900U,	// CMOVB64rm
    2232444U,	// CMOVB64rr
    4329793U,	// CMOVBE16rm
    2232641U,	// CMOVBE16rr
    6426945U,	// CMOVBE32rm
    2232641U,	// CMOVBE32rr
    8524097U,	// CMOVBE64rm
    2232641U,	// CMOVBE64rr
    4329942U,	// CMOVE16rm
    2232790U,	// CMOVE16rr
    6427094U,	// CMOVE32rm
    2232790U,	// CMOVE32rr
    8524246U,	// CMOVE64rm
    2232790U,	// CMOVE64rr
    4329992U,	// CMOVG16rm
    2232840U,	// CMOVG16rr
    6427144U,	// CMOVG32rm
    2232840U,	// CMOVG32rr
    8524296U,	// CMOVG64rm
    2232840U,	// CMOVG64rr
    4329813U,	// CMOVGE16rm
    2232661U,	// CMOVGE16rr
    6426965U,	// CMOVGE32rm
    2232661U,	// CMOVGE32rr
    8524117U,	// CMOVGE64rm
    2232661U,	// CMOVGE64rr
    4330118U,	// CMOVL16rm
    2232966U,	// CMOVL16rr
    6427270U,	// CMOVL32rm
    2232966U,	// CMOVL32rr
    8524422U,	// CMOVL64rm
    2232966U,	// CMOVL64rr
    4329837U,	// CMOVLE16rm
    2232685U,	// CMOVLE16rr
    6426989U,	// CMOVLE32rm
    2232685U,	// CMOVLE32rr
    8524141U,	// CMOVLE64rm
    2232685U,	// CMOVLE64rr
    4329865U,	// CMOVNE16rm
    2232713U,	// CMOVNE16rr
    6427017U,	// CMOVNE32rm
    2232713U,	// CMOVNE32rr
    8524169U,	// CMOVNE64rm
    2232713U,	// CMOVNE64rr
    4330159U,	// CMOVNO16rm
    2233007U,	// CMOVNO16rr
    6427311U,	// CMOVNO32rm
    2233007U,	// CMOVNO32rr
    8524463U,	// CMOVNO64rm
    2233007U,	// CMOVNO64rr
    4330220U,	// CMOVNP16rm
    2233068U,	// CMOVNP16rr
    6427372U,	// CMOVNP32rm
    2233068U,	// CMOVNP32rr
    8524524U,	// CMOVNP64rm
    2233068U,	// CMOVNP64rr
    4330444U,	// CMOVNS16rm
    2233292U,	// CMOVNS16rr
    6427596U,	// CMOVNS32rm
    2233292U,	// CMOVNS32rr
    8524748U,	// CMOVNS64rm
    2233292U,	// CMOVNS64rr
    4330173U,	// CMOVO16rm
    2233021U,	// CMOVO16rr
    6427325U,	// CMOVO32rm
    2233021U,	// CMOVO32rr
    8524477U,	// CMOVO64rm
    2233021U,	// CMOVO64rr
    4330266U,	// CMOVP16rm
    2233114U,	// CMOVP16rr
    6427418U,	// CMOVP32rm
    2233114U,	// CMOVP32rr
    8524570U,	// CMOVP64rm
    2233114U,	// CMOVP64rr
    4330468U,	// CMOVS16rm
    2233316U,	// CMOVS16rr
    6427620U,	// CMOVS32rm
    2233316U,	// CMOVS32rr
    8524772U,	// CMOVS64rm
    2233316U,	// CMOVS64rr
    2105U,	// CMOV_FR32
    2264U,	// CMOV_FR64
    1984U,	// CMOV_GR16
    1964U,	// CMOV_GR32
    2283U,	// CMOV_GR8
    2085U,	// CMOV_RFP32
    2244U,	// CMOV_RFP64
    2004U,	// CMOV_RFP80
    2044U,	// CMOV_V16F32
    2124U,	// CMOV_V2F64
    2184U,	// CMOV_V2I64
    2024U,	// CMOV_V4F32
    2144U,	// CMOV_V4F64
    2204U,	// CMOV_V4I64
    2065U,	// CMOV_V8F32
    2164U,	// CMOV_V8F64
    2224U,	// CMOV_V8I64
    5531U,	// CMP16i16
    139989U,	// CMP16mi
    139989U,	// CMP16mi8
    139989U,	// CMP16mr
    14815957U,	// CMP16ri
    14815957U,	// CMP16ri8
    19010261U,	// CMP16rm
    14815957U,	// CMP16rr
    14815957U,	// CMP16rr_REV
    5696U,	// CMP32i32
    148181U,	// CMP32mi
    148181U,	// CMP32mi8
    148181U,	// CMP32mr
    14815957U,	// CMP32ri
    14815957U,	// CMP32ri8
    12718805U,	// CMP32rm
    14815957U,	// CMP32rr
    14815957U,	// CMP32rr_REV
    5811U,	// CMP64i32
    152277U,	// CMP64mi32
    152277U,	// CMP64mi8
    152277U,	// CMP64mr
    14815957U,	// CMP64ri32
    14815957U,	// CMP64ri8
    16913109U,	// CMP64rm
    14815957U,	// CMP64rr
    14815957U,	// CMP64rr_REV
    5419U,	// CMP8i8
    156373U,	// CMP8mi
    156373U,	// CMP8mr
    14815957U,	// CMP8ri
    21107413U,	// CMP8rm
    14815957U,	// CMP8rr
    14815957U,	// CMP8rr_REV
    32867U,	// CMPSB
    37131U,	// CMPSL
    41775U,	// CMPSQ
    46214U,	// CMPSW
    49206U,	// CMPXCHG16B
    139757U,	// CMPXCHG16rm
    14815725U,	// CMPXCHG16rr
    147949U,	// CMPXCHG32rm
    14815725U,	// CMPXCHG32rr
    152045U,	// CMPXCHG64rm
    14815725U,	// CMPXCHG64rr
    20546U,	// CMPXCHG8B
    156141U,	// CMPXCHG8rm
    14815725U,	// CMPXCHG8rr
    2782U,	// CPUID32
    2782U,	// CPUID64
    3075U,	// CQO
    2829U,	// CWD
    2833U,	// CWDE
    2641U,	// DAA
    3183U,	// DAS
    2531U,	// DATA16_PREFIX
    8328U,	// DEC16m
    4232U,	// DEC16r
    4232U,	// DEC32_16r
    4232U,	// DEC32_32r
    16520U,	// DEC32m
    4232U,	// DEC32r
    8328U,	// DEC64_16m
    4232U,	// DEC64_16r
    16520U,	// DEC64_32m
    4232U,	// DEC64_32r
    20616U,	// DEC64m
    4232U,	// DEC64r
    24712U,	// DEC8m
    4232U,	// DEC8r
    9309U,	// DIV16m
    5213U,	// DIV16r
    17501U,	// DIV32m
    5213U,	// DIV32r
    21597U,	// DIV64m
    5213U,	// DIV64r
    25693U,	// DIV8m
    5213U,	// DIV8r
    5923U,	// EH_RETURN
    5923U,	// EH_RETURN64
    2354U,	// EH_SjLj_LongJmp32
    2444U,	// EH_SjLj_LongJmp64
    2373U,	// EH_SjLj_SetJmp32
    2463U,	// EH_SjLj_SetJmp64
    29450U,	// EH_SjLj_Setup
    14816085U,	// ENTER
    275025U,	// FARCALL16i
    53841U,	// FARCALL16m
    275025U,	// FARCALL32i
    53841U,	// FARCALL32m
    53841U,	// FARCALL64
    275162U,	// FARJMP16i
    53978U,	// FARJMP16m
    275162U,	// FARJMP32i
    53978U,	// FARJMP32m
    53978U,	// FARJMP64
    3037U,	// FSETPM
    2726U,	// GETSEC
    3347U,	// HLT
    9308U,	// IDIV16m
    5212U,	// IDIV16r
    17500U,	// IDIV32m
    5212U,	// IDIV32r
    21596U,	// IDIV64m
    5212U,	// IDIV64r
    25692U,	// IDIV8m
    5212U,	// IDIV8r
    8832U,	// IMUL16m
    4736U,	// IMUL16r
    4330112U,	// IMUL16rm
    891425408U,	// IMUL16rmi
    891425408U,	// IMUL16rmi8
    2232960U,	// IMUL16rr
    350360192U,	// IMUL16rri
    350360192U,	// IMUL16rri8
    17024U,	// IMUL32m
    4736U,	// IMUL32r
    6427264U,	// IMUL32rm
    885133952U,	// IMUL32rmi
    885133952U,	// IMUL32rmi8
    2232960U,	// IMUL32rr
    350360192U,	// IMUL32rri
    350360192U,	// IMUL32rri8
    21120U,	// IMUL64m
    4736U,	// IMUL64r
    8524416U,	// IMUL64rm
    889328256U,	// IMUL64rmi32
    889328256U,	// IMUL64rmi8
    2232960U,	// IMUL64rr
    350360192U,	// IMUL64rri32
    350360192U,	// IMUL64rri8
    25216U,	// IMUL8m
    4736U,	// IMUL8r
    5523U,	// IN16ri
    3544U,	// IN16rr
    5687U,	// IN32ri
    3554U,	// IN32rr
    5411U,	// IN8ri
    3534U,	// IN8rr
    8355U,	// INC16m
    4259U,	// INC16r
    4259U,	// INC32_16r
    4259U,	// INC32_32r
    16547U,	// INC32m
    4259U,	// INC32r
    8355U,	// INC64_16m
    4259U,	// INC64_16r
    16547U,	// INC64_32m
    4259U,	// INC64_32r
    20643U,	// INC64m
    4259U,	// INC64r
    24739U,	// INC8m
    4259U,	// INC8r
    450646U,	// INSB
    454910U,	// INSL
    459897U,	// INSW
    5158U,	// INT
    2349U,	// INT1
    2439U,	// INT3
    3079U,	// INTO
    2824U,	// INVD
    23204912U,	// INVEPT32
    23204912U,	// INVEPT64
    25082U,	// INVLPG
    3500U,	// INVLPGA32
    3517U,	// INVLPGA64
    23204040U,	// INVPCID32
    23204040U,	// INVPCID64
    23204049U,	// INVVPID32
    23204049U,	// INVVPID64
    3327U,	// IRET16
    2812U,	// IRET32
    3142U,	// IRET64
    2610U,	// Int_MemBarrier
    28961U,	// JAE_1
    28961U,	// JAE_2
    28961U,	// JAE_4
    28709U,	// JA_1
    28709U,	// JA_2
    28709U,	// JA_4
    28981U,	// JBE_1
    28981U,	// JBE_2
    28981U,	// JBE_4
    28754U,	// JB_1
    28754U,	// JB_2
    28754U,	// JB_4
    29907U,	// JCXZ
    29900U,	// JECXZ_32
    29900U,	// JECXZ_64
    29021U,	// JE_1
    29021U,	// JE_2
    29021U,	// JE_4
    29001U,	// JGE_1
    29001U,	// JGE_2
    29001U,	// JGE_4
    29174U,	// JG_1
    29174U,	// JG_2
    29174U,	// JG_4
    29025U,	// JLE_1
    29025U,	// JLE_2
    29025U,	// JLE_4
    29261U,	// JL_1
    29261U,	// JL_2
    29261U,	// JL_4
    8923U,	// JMP16m
    4827U,	// JMP16r
    17115U,	// JMP32m
    4827U,	// JMP32r
    21211U,	// JMP64m
    4827U,	// JMP64r
    29403U,	// JMP_1
    29403U,	// JMP_2
    29403U,	// JMP_4
    29045U,	// JNE_1
    29045U,	// JNE_2
    29045U,	// JNE_4
    29347U,	// JNO_1
    29347U,	// JNO_2
    29347U,	// JNO_4
    29408U,	// JNP_1
    29408U,	// JNP_2
    29408U,	// JNP_4
    29632U,	// JNS_1
    29632U,	// JNS_2
    29632U,	// JNS_4
    29343U,	// JO_1
    29343U,	// JO_2
    29343U,	// JO_4
    29393U,	// JP_1
    29393U,	// JP_2
    29393U,	// JP_4
    29913U,	// JRCXZ
    29628U,	// JS_1
    29628U,	// JS_2
    29628U,	// JS_4
    2909U,	// LAHF
    19010374U,	// LAR16rm
    14816070U,	// LAR16rr
    19010374U,	// LAR32rm
    14816070U,	// LAR32rr
    19010374U,	// LAR64rm
    14816070U,	// LAR64rr
    139757U,	// LCMPXCHG16
    49206U,	// LCMPXCHG16B
    147949U,	// LCMPXCHG32
    152045U,	// LCMPXCHG64
    156141U,	// LCMPXCHG8
    20546U,	// LCMPXCHG8B
    25301928U,	// LDS16rm
    25301928U,	// LDS32rm
    19009568U,	// LEA16r
    12718112U,	// LEA32r
    12718112U,	// LEA64_32r
    16912416U,	// LEA64r
    2896U,	// LEAVE
    2896U,	// LEAVE64
    25301933U,	// LES16rm
    25301933U,	// LES32rm
    25301938U,	// LFS16rm
    25301938U,	// LFS32rm
    25301938U,	// LFS64rm
    54255U,	// LGDT16m
    54255U,	// LGDT32m
    54255U,	// LGDT64m
    25301943U,	// LGS16rm
    25301943U,	// LGS32rm
    25301943U,	// LGS64rm
    54267U,	// LIDT16m
    54267U,	// LIDT32m
    54267U,	// LIDT64m
    9223U,	// LLDT16m
    5127U,	// LLDT16r
    9325U,	// LMSW16m
    5229U,	// LMSW16r
    139451U,	// LOCK_ADD16mi
    139451U,	// LOCK_ADD16mi8
    139451U,	// LOCK_ADD16mr
    147643U,	// LOCK_ADD32mi
    147643U,	// LOCK_ADD32mi8
    147643U,	// LOCK_ADD32mr
    151739U,	// LOCK_ADD64mi32
    151739U,	// LOCK_ADD64mi8
    151739U,	// LOCK_ADD64mr
    155835U,	// LOCK_ADD8mi
    155835U,	// LOCK_ADD8mr
    139500U,	// LOCK_AND16mi
    139500U,	// LOCK_AND16mi8
    139500U,	// LOCK_AND16mr
    147692U,	// LOCK_AND32mi
    147692U,	// LOCK_AND32mi8
    147692U,	// LOCK_AND32mr
    151788U,	// LOCK_AND64mi32
    151788U,	// LOCK_AND64mi8
    151788U,	// LOCK_AND64mr
    155884U,	// LOCK_AND8mi
    155884U,	// LOCK_AND8mr
    8328U,	// LOCK_DEC16m
    16520U,	// LOCK_DEC32m
    20616U,	// LOCK_DEC64m
    24712U,	// LOCK_DEC8m
    8355U,	// LOCK_INC16m
    16547U,	// LOCK_INC32m
    20643U,	// LOCK_INC64m
    24739U,	// LOCK_INC8m
    140130U,	// LOCK_OR16mi
    140130U,	// LOCK_OR16mi8
    140130U,	// LOCK_OR16mr
    148322U,	// LOCK_OR32mi
    148322U,	// LOCK_OR32mi8
    148322U,	// LOCK_OR32mr
    152418U,	// LOCK_OR64mi32
    152418U,	// LOCK_OR64mi8
    152418U,	// LOCK_OR64mr
    156514U,	// LOCK_OR8mi
    156514U,	// LOCK_OR8mr
    2977U,	// LOCK_PREFIX
    139383U,	// LOCK_SUB16mi
    139383U,	// LOCK_SUB16mi8
    139383U,	// LOCK_SUB16mr
    147575U,	// LOCK_SUB32mi
    147575U,	// LOCK_SUB32mi8
    147575U,	// LOCK_SUB32mr
    151671U,	// LOCK_SUB64mi32
    151671U,	// LOCK_SUB64mi8
    151671U,	// LOCK_SUB64mr
    155767U,	// LOCK_SUB8mi
    155767U,	// LOCK_SUB8mr
    140142U,	// LOCK_XOR16mi
    140142U,	// LOCK_XOR16mi8
    140142U,	// LOCK_XOR16mr
    148334U,	// LOCK_XOR32mi
    148334U,	// LOCK_XOR32mi8
    148334U,	// LOCK_XOR32mr
    152430U,	// LOCK_XOR64mi32
    152430U,	// LOCK_XOR64mi8
    152430U,	// LOCK_XOR64mr
    156526U,	// LOCK_XOR8mi
    156526U,	// LOCK_XOR8mr
    70900U,	// LODSB
    75296U,	// LODSL
    79561U,	// LODSQ
    83415U,	// LODSW
    29433U,	// LOOP
    29073U,	// LOOPE
    29050U,	// LOOPNE
    4578U,	// LRETIL
    4897U,	// LRETIQ
    4578U,	// LRETIW
    2930U,	// LRETL
    3116U,	// LRETQ
    2930U,	// LRETW
    19010165U,	// LSL16rm
    14815861U,	// LSL16rr
    12718709U,	// LSL32rm
    14815861U,	// LSL32rr
    16913013U,	// LSL64rm
    14815861U,	// LSL64rr
    25301972U,	// LSS16rm
    25301972U,	// LSS32rm
    25301972U,	// LSS64rm
    9097U,	// LTRm
    5001U,	// LTRr
    86202U,	// LXADD16
    90298U,	// LXADD32
    94394U,	// LXADD64
    98490U,	// LXADD8
    19010584U,	// LZCNT16rm
    14816280U,	// LZCNT16rr
    12719128U,	// LZCNT32rm
    14816280U,	// LZCNT32rr
    16913432U,	// LZCNT64rm
    14816280U,	// LZCNT64rr
    3029U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    627810U,	// MOV16ao16
    627810U,	// MOV16ao16_16
    140386U,	// MOV16mi
    140386U,	// MOV16mr
    140386U,	// MOV16ms
    103875U,	// MOV16o16a
    103875U,	// MOV16o16a_16
    14816354U,	// MOV16ri
    14816354U,	// MOV16ri_alt
    19010658U,	// MOV16rm
    14816354U,	// MOV16rr
    14816354U,	// MOV16rr_REV
    14816354U,	// MOV16rs
    19010658U,	// MOV16sm
    14816354U,	// MOV16sr
    762978U,	// MOV32ao32
    762978U,	// MOV32ao32_16
    14816354U,	// MOV32cr
    14816354U,	// MOV32dr
    148578U,	// MOV32mi
    148578U,	// MOV32mr
    140386U,	// MOV32ms
    108140U,	// MOV32o32a
    108140U,	// MOV32o32a_16
    0U,	// MOV32r0
    14816354U,	// MOV32rc
    14816354U,	// MOV32rd
    14816354U,	// MOV32ri
    0U,	// MOV32ri64
    14816354U,	// MOV32ri_alt
    12719202U,	// MOV32rm
    14816354U,	// MOV32rr
    14816354U,	// MOV32rr_REV
    14816354U,	// MOV32rs
    19010658U,	// MOV32sm
    14816354U,	// MOV32sr
    627610U,	// MOV64ao16
    762778U,	// MOV64ao32
    897946U,	// MOV64ao64
    1033114U,	// MOV64ao8
    14816354U,	// MOV64cr
    14816354U,	// MOV64dr
    152674U,	// MOV64mi32
    152674U,	// MOV64mr
    140386U,	// MOV64ms
    103853U,	// MOV64o16a
    108116U,	// MOV64o32a
    112351U,	// MOV64o64a
    116029U,	// MOV64o8a
    14816354U,	// MOV64rc
    14816354U,	// MOV64rd
    14816154U,	// MOV64ri
    14816354U,	// MOV64ri32
    16913506U,	// MOV64rm
    14816354U,	// MOV64rr
    14816354U,	// MOV64rr_REV
    14816354U,	// MOV64rs
    19010658U,	// MOV64sm
    14816354U,	// MOV64sr
    1033314U,	// MOV8ao8
    1033314U,	// MOV8ao8_16
    156770U,	// MOV8mi
    156770U,	// MOV8mr
    134374498U,	// MOV8mr_NOREX
    116051U,	// MOV8o8a
    116051U,	// MOV8o8a_16
    14816354U,	// MOV8ri
    14816354U,	// MOV8ri_alt
    21107810U,	// MOV8rm
    155325538U,	// MOV8rm_NOREX
    14816354U,	// MOV8rr
    149034082U,	// MOV8rr_NOREX
    14816354U,	// MOV8rr_REV
    139586U,	// MOVBE16mr
    19009858U,	// MOVBE16rm
    147778U,	// MOVBE32mr
    12718402U,	// MOVBE32rm
    151874U,	// MOVBE64mr
    16912706U,	// MOVBE64rm
    0U,	// MOVPC32r
    27451498U,	// MOVSB
    29552914U,	// MOVSL
    31707958U,	// MOVSQ
    33752205U,	// MOVSW
    21107902U,	// MOVSX16rm8
    14816446U,	// MOVSX16rr8
    19010750U,	// MOVSX32rm16
    21107902U,	// MOVSX32rm8
    14816446U,	// MOVSX32rr16
    14816446U,	// MOVSX32rr8
    14815513U,	// MOVSX64_NOREXrr32
    19010750U,	// MOVSX64rm16
    12718361U,	// MOVSX64rm32
    21107902U,	// MOVSX64rm8
    14816446U,	// MOVSX64rr16
    14815513U,	// MOVSX64rr32
    14816446U,	// MOVSX64rr8
    21107909U,	// MOVZX16rm8
    14816453U,	// MOVZX16rr8
    21107909U,	// MOVZX32_NOREXrm8
    14816453U,	// MOVZX32_NOREXrr8
    19010757U,	// MOVZX32rm16
    21107909U,	// MOVZX32rm8
    14816453U,	// MOVZX32rr16
    14816453U,	// MOVZX32rr8
    19010757U,	// MOVZX64rm16_Q
    21107909U,	// MOVZX64rm8_Q
    14816453U,	// MOVZX64rr16_Q
    14816453U,	// MOVZX64rr8_Q
    8833U,	// MUL16m
    4737U,	// MUL16r
    17025U,	// MUL32m
    4737U,	// MUL32r
    21121U,	// MUL64m
    4737U,	// MUL64r
    25217U,	// MUL8m
    4737U,	// MUL8r
    81925280U,	// MULX32rm
    350360736U,	// MULX32rr
    618796192U,	// MULX64rm
    350360736U,	// MULX64rr
    8680U,	// NEG16m
    4584U,	// NEG16r
    16872U,	// NEG32m
    4584U,	// NEG32r
    20968U,	// NEG64m
    4584U,	// NEG64r
    25064U,	// NEG8m
    4584U,	// NEG8r
    3095U,	// NOOP
    8948U,	// NOOP18_16m4
    8948U,	// NOOP18_16m5
    8948U,	// NOOP18_16m6
    8948U,	// NOOP18_16m7
    4852U,	// NOOP18_16r4
    4852U,	// NOOP18_16r5
    4852U,	// NOOP18_16r6
    4852U,	// NOOP18_16r7
    17140U,	// NOOP18_m4
    17140U,	// NOOP18_m5
    17140U,	// NOOP18_m6
    17140U,	// NOOP18_m7
    4852U,	// NOOP18_r4
    4852U,	// NOOP18_r5
    4852U,	// NOOP18_r6
    4852U,	// NOOP18_r7
    35795700U,	// NOOP19rr
    17140U,	// NOOPL
    17140U,	// NOOPL_19
    17140U,	// NOOPL_1a
    17140U,	// NOOPL_1b
    17140U,	// NOOPL_1c
    17140U,	// NOOPL_1d
    17140U,	// NOOPL_1e
    8948U,	// NOOPW
    8948U,	// NOOPW_19
    8948U,	// NOOPW_1a
    8948U,	// NOOPW_1b
    8948U,	// NOOPW_1c
    8948U,	// NOOPW_1d
    8948U,	// NOOPW_1e
    9259U,	// NOT16m
    5163U,	// NOT16r
    17451U,	// NOT32m
    5163U,	// NOT32r
    21547U,	// NOT64m
    5163U,	// NOT64r
    25643U,	// NOT8m
    5163U,	// NOT8r
    5541U,	// OR16i16
    140130U,	// OR16mi
    140130U,	// OR16mi8
    140130U,	// OR16mr
    2241378U,	// OR16ri
    2241378U,	// OR16ri8
    4338530U,	// OR16rm
    2241378U,	// OR16rr
    2233186U,	// OR16rr_REV
    5707U,	// OR32i32
    148322U,	// OR32mi
    148322U,	// OR32mi8
    148322U,	// OR32mr
    148322U,	// OR32mrLocked
    2241378U,	// OR32ri
    2241378U,	// OR32ri8
    6435682U,	// OR32rm
    2241378U,	// OR32rr
    2233186U,	// OR32rr_REV
    5846U,	// OR64i32
    152418U,	// OR64mi32
    152418U,	// OR64mi8
    152418U,	// OR64mr
    2241378U,	// OR64ri32
    2241378U,	// OR64ri8
    8532834U,	// OR64rm
    2241378U,	// OR64rr
    2233186U,	// OR64rr_REV
    5429U,	// OR8i8
    156514U,	// OR8mi
    156514U,	// OR8mr
    2241378U,	// OR8ri
    2241378U,	// OR8ri8
    10629986U,	// OR8rm
    2241378U,	// OR8rr
    2233186U,	// OR8rr_REV
    529489U,	// OUT16ir
    3402U,	// OUT16rr
    660561U,	// OUT32ir
    3456U,	// OUT32rr
    922705U,	// OUT8ir
    2982U,	// OUT8rr
    71415U,	// OUTSB
    75522U,	// OUTSL
    83725U,	// OUTSW
    81924811U,	// PDEP32rm
    350360267U,	// PDEP32rr
    618795723U,	// PDEP64rm
    350360267U,	// PDEP64rr
    81925206U,	// PEXT32rm
    350360662U,	// PEXT32rr
    618796118U,	// PEXT64rm
    350360662U,	// PEXT64rr
    4863U,	// POP16r
    8959U,	// POP16rmm
    4863U,	// POP16rmr
    4863U,	// POP32r
    17151U,	// POP32rmm
    4863U,	// POP32rmr
    4863U,	// POP64r
    21247U,	// POP64rmm
    4863U,	// POP64rmr
    3372U,	// POPA16
    3000U,	// POPA32
    3203U,	// POPDS16
    3203U,	// POPDS32
    3218U,	// POPES16
    3218U,	// POPES32
    2925U,	// POPF16
    2776U,	// POPF32
    3110U,	// POPF64
    3233U,	// POPFS16
    3233U,	// POPFS32
    3233U,	// POPFS64
    3248U,	// POPGS16
    3248U,	// POPGS32
    3248U,	// POPGS64
    3315U,	// POPSS16
    3315U,	// POPSS32
    4623U,	// PUSH16i8
    4623U,	// PUSH16r
    8719U,	// PUSH16rmm
    4623U,	// PUSH16rmr
    4623U,	// PUSH32i8
    4623U,	// PUSH32r
    16911U,	// PUSH32rmm
    4623U,	// PUSH32rmr
    4623U,	// PUSH64i16
    4623U,	// PUSH64i32
    4623U,	// PUSH64i8
    4623U,	// PUSH64r
    21007U,	// PUSH64rmm
    4623U,	// PUSH64rmr
    3365U,	// PUSHA16
    2993U,	// PUSHA32
    3187U,	// PUSHCS16
    3187U,	// PUSHCS32
    3195U,	// PUSHDS16
    3195U,	// PUSHDS32
    3210U,	// PUSHES16
    3210U,	// PUSHES32
    2919U,	// PUSHF16
    2769U,	// PUSHF32
    3103U,	// PUSHF64
    3225U,	// PUSHFS16
    3225U,	// PUSHFS32
    3225U,	// PUSHFS64
    3240U,	// PUSHGS16
    3240U,	// PUSHGS32
    3240U,	// PUSHGS64
    3307U,	// PUSHSS16
    3307U,	// PUSHSS32
    4623U,	// PUSHi16
    4623U,	// PUSHi32
    1057347U,	// RCL16m1
    1188419U,	// RCL16mCL
    139843U,	// RCL16mi
    1053251U,	// RCL16r1
    1184323U,	// RCL16rCL
    2232899U,	// RCL16ri
    1065539U,	// RCL32m1
    1196611U,	// RCL32mCL
    148035U,	// RCL32mi
    1053251U,	// RCL32r1
    1184323U,	// RCL32rCL
    2232899U,	// RCL32ri
    1069635U,	// RCL64m1
    1200707U,	// RCL64mCL
    152131U,	// RCL64mi
    1053251U,	// RCL64r1
    1184323U,	// RCL64rCL
    2232899U,	// RCL64ri
    1073731U,	// RCL8m1
    1204803U,	// RCL8mCL
    156227U,	// RCL8mi
    1053251U,	// RCL8r1
    1184323U,	// RCL8rCL
    2232899U,	// RCL8ri
    1057616U,	// RCR16m1
    1188688U,	// RCR16mCL
    140112U,	// RCR16mi
    1053520U,	// RCR16r1
    1184592U,	// RCR16rCL
    2233168U,	// RCR16ri
    1065808U,	// RCR32m1
    1196880U,	// RCR32mCL
    148304U,	// RCR32mi
    1053520U,	// RCR32r1
    1184592U,	// RCR32rCL
    2233168U,	// RCR32ri
    1069904U,	// RCR64m1
    1200976U,	// RCR64mCL
    152400U,	// RCR64mi
    1053520U,	// RCR64r1
    1184592U,	// RCR64rCL
    2233168U,	// RCR64ri
    1074000U,	// RCR8m1
    1205072U,	// RCR8mCL
    156496U,	// RCR8mi
    1053520U,	// RCR8r1
    1184592U,	// RCR8rCL
    2233168U,	// RCR8ri
    4504U,	// RDFSBASE
    4504U,	// RDFSBASE64
    4524U,	// RDGSBASE
    4524U,	// RDGSBASE64
    3157U,	// RDMSR
    2746U,	// RDPMC
    4329U,	// RDRAND16r
    4329U,	// RDRAND32r
    4329U,	// RDRAND64r
    4288U,	// RDSEED16r
    4288U,	// RDSEED32r
    4288U,	// RDSEED64r
    2759U,	// RDTSC
    3084U,	// RDTSCP
    2322U,	// RELEASE_MOV16mr
    2322U,	// RELEASE_MOV32mr
    2322U,	// RELEASE_MOV64mr
    2322U,	// RELEASE_MOV8mr
    2847U,	// REPNE_PREFIX
    2690U,	// REP_MOVSB_32
    2690U,	// REP_MOVSB_64
    2802U,	// REP_MOVSD_32
    2802U,	// REP_MOVSD_64
    3132U,	// REP_MOVSQ_64
    3392U,	// REP_MOVSW_32
    3392U,	// REP_MOVSW_64
    3091U,	// REP_PREFIX
    2680U,	// REP_STOSB_32
    2680U,	// REP_STOSB_64
    2792U,	// REP_STOSD_32
    2792U,	// REP_STOSD_64
    3122U,	// REP_STOSQ_64
    3382U,	// REP_STOSW_32
    3382U,	// REP_STOSW_64
    5139U,	// RETIL
    5139U,	// RETIQ
    5139U,	// RETIW
    3328U,	// RETL
    3328U,	// RETQ
    3328U,	// RETW
    2525U,	// REX64_PREFIX
    1057386U,	// ROL16m1
    1188458U,	// ROL16mCL
    139882U,	// ROL16mi
    1053290U,	// ROL16r1
    1184362U,	// ROL16rCL
    2232938U,	// ROL16ri
    1065578U,	// ROL32m1
    1196650U,	// ROL32mCL
    148074U,	// ROL32mi
    1053290U,	// ROL32r1
    1184362U,	// ROL32rCL
    2232938U,	// ROL32ri
    1069674U,	// ROL64m1
    1200746U,	// ROL64mCL
    152170U,	// ROL64mi
    1053290U,	// ROL64r1
    1184362U,	// ROL64rCL
    2232938U,	// ROL64ri
    1073770U,	// ROL8m1
    1204842U,	// ROL8mCL
    156266U,	// ROL8mi
    1053290U,	// ROL8r1
    1184362U,	// ROL8rCL
    2232938U,	// ROL8ri
    1057633U,	// ROR16m1
    1188705U,	// ROR16mCL
    140129U,	// ROR16mi
    1053537U,	// ROR16r1
    1184609U,	// ROR16rCL
    2233185U,	// ROR16ri
    1065825U,	// ROR32m1
    1196897U,	// ROR32mCL
    148321U,	// ROR32mi
    1053537U,	// ROR32r1
    1184609U,	// ROR32rCL
    2233185U,	// ROR32ri
    1069921U,	// ROR64m1
    1200993U,	// ROR64mCL
    152417U,	// ROR64mi
    1053537U,	// ROR64r1
    1184609U,	// ROR64rCL
    2233185U,	// ROR64ri
    1074017U,	// ROR8m1
    1205089U,	// ROR8mCL
    156513U,	// ROR8mi
    1053537U,	// ROR8r1
    1184609U,	// ROR8rCL
    2233185U,	// ROR8ri
    885134520U,	// RORX32mi
    350360760U,	// RORX32ri
    889328824U,	// RORX64mi
    350360760U,	// RORX64ri
    3044U,	// RSM
    2914U,	// SAHF
    1057342U,	// SAL16m1
    1188414U,	// SAL16mCL
    139838U,	// SAL16mi
    1053246U,	// SAL16r1
    1184318U,	// SAL16rCL
    2232894U,	// SAL16ri
    1065534U,	// SAL32m1
    1196606U,	// SAL32mCL
    148030U,	// SAL32mi
    1053246U,	// SAL32r1
    1184318U,	// SAL32rCL
    2232894U,	// SAL32ri
    1069630U,	// SAL64m1
    1200702U,	// SAL64mCL
    152126U,	// SAL64mi
    1053246U,	// SAL64r1
    1184318U,	// SAL64rCL
    2232894U,	// SAL64ri
    1073726U,	// SAL8m1
    1204798U,	// SAL8mCL
    156222U,	// SAL8mi
    1053246U,	// SAL8r1
    1184318U,	// SAL8rCL
    2232894U,	// SAL8ri
    2733U,	// SALC
    1057611U,	// SAR16m1
    1188683U,	// SAR16mCL
    140107U,	// SAR16mi
    1053515U,	// SAR16r1
    1184587U,	// SAR16rCL
    2233163U,	// SAR16ri
    1065803U,	// SAR32m1
    1196875U,	// SAR32mCL
    148299U,	// SAR32mi
    1053515U,	// SAR32r1
    1184587U,	// SAR32rCL
    2233163U,	// SAR32ri
    1069899U,	// SAR64m1
    1200971U,	// SAR64mCL
    152395U,	// SAR64mi
    1053515U,	// SAR64r1
    1184587U,	// SAR64rCL
    2233163U,	// SAR64ri
    1073995U,	// SAR8m1
    1205067U,	// SAR8mCL
    156491U,	// SAR8mi
    1053515U,	// SAR8r1
    1184587U,	// SAR8rCL
    2233163U,	// SAR8ri
    885134508U,	// SARX32rm
    350360748U,	// SARX32rr
    889328812U,	// SARX64rm
    350360748U,	// SARX64rr
    5468U,	// SBB16i16
    139341U,	// SBB16mi
    139341U,	// SBB16mi8
    139341U,	// SBB16mr
    2240589U,	// SBB16ri
    2240589U,	// SBB16ri8
    4337741U,	// SBB16rm
    2240589U,	// SBB16rr
    2232397U,	// SBB16rr_REV
    5602U,	// SBB32i32
    147533U,	// SBB32mi
    147533U,	// SBB32mi8
    147533U,	// SBB32mr
    2240589U,	// SBB32ri
    2240589U,	// SBB32ri8
    6434893U,	// SBB32rm
    2240589U,	// SBB32rr
    2232397U,	// SBB32rr_REV
    5750U,	// SBB64i32
    151629U,	// SBB64mi32
    151629U,	// SBB64mi8
    151629U,	// SBB64mr
    2240589U,	// SBB64ri32
    2240589U,	// SBB64ri8
    8532045U,	// SBB64rm
    2240589U,	// SBB64rr
    2232397U,	// SBB64rr_REV
    5344U,	// SBB8i8
    155725U,	// SBB8mi
    155725U,	// SBB8mr
    2240589U,	// SBB8ri
    10629197U,	// SBB8rm
    2240589U,	// SBB8rr
    2232397U,	// SBB8rr_REV
    58601U,	// SCASB
    62996U,	// SCASL
    120509U,	// SCASQ
    67020U,	// SCASW
    3262U,	// SEG_ALLOCA_32
    3262U,	// SEG_ALLOCA_64
    2879U,	// SEH_EndPrologue
    2865U,	// SEH_Epilogue
    6001U,	// SEH_PushFrame
    6046U,	// SEH_PushReg
    14817168U,	// SEH_SaveReg
    14817082U,	// SEH_SaveXMM
    14817153U,	// SEH_SetFrame
    5984U,	// SEH_StackAlloc
    24870U,	// SETAEm
    4390U,	// SETAEr
    24617U,	// SETAm
    4137U,	// SETAr
    24890U,	// SETBEm
    4410U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    24689U,	// SETBm
    4209U,	// SETBr
    25024U,	// SETEm
    4544U,	// SETEr
    24910U,	// SETGEm
    4430U,	// SETGEr
    25090U,	// SETGm
    4610U,	// SETGr
    24934U,	// SETLEm
    4454U,	// SETLEr
    25210U,	// SETLm
    4730U,	// SETLr
    24962U,	// SETNEm
    4482U,	// SETNEr
    25256U,	// SETNOm
    4776U,	// SETNOr
    25317U,	// SETNPm
    4837U,	// SETNPr
    25541U,	// SETNSm
    5061U,	// SETNSr
    25271U,	// SETOm
    4791U,	// SETOr
    25348U,	// SETPm
    4868U,	// SETPr
    25566U,	// SETSm
    5086U,	// SETSr
    54261U,	// SGDT16m
    54261U,	// SGDT32m
    54261U,	// SGDT64m
    1057352U,	// SHL16m1
    1188424U,	// SHL16mCL
    139848U,	// SHL16mi
    1053256U,	// SHL16r1
    1184328U,	// SHL16rCL
    2232904U,	// SHL16ri
    1065544U,	// SHL32m1
    1196616U,	// SHL32mCL
    148040U,	// SHL32mi
    1053256U,	// SHL32r1
    1184328U,	// SHL32rCL
    2232904U,	// SHL32ri
    1069640U,	// SHL64m1
    1200712U,	// SHL64mCL
    152136U,	// SHL64mi
    1053256U,	// SHL64r1
    1184328U,	// SHL64rCL
    2232904U,	// SHL64ri
    1073736U,	// SHL8m1
    1204808U,	// SHL8mCL
    156232U,	// SHL8mi
    1053256U,	// SHL8r1
    1184328U,	// SHL8rCL
    2232904U,	// SHL8ri
    201466074U,	// SHLD16mrCL
    872554714U,	// SHLD16mri8
    203559130U,	// SHLD16rrCL
    1143083226U,	// SHLD16rri8
    201474266U,	// SHLD32mrCL
    872562906U,	// SHLD32mri8
    203559130U,	// SHLD32rrCL
    1143083226U,	// SHLD32rri8
    201478362U,	// SHLD64mrCL
    872567002U,	// SHLD64mri8
    203559130U,	// SHLD64rrCL
    1143083226U,	// SHLD64rri8
    885134490U,	// SHLX32rm
    350360730U,	// SHLX32rr
    889328794U,	// SHLX64rm
    350360730U,	// SHLX64rr
    1057628U,	// SHR16m1
    1188700U,	// SHR16mCL
    140124U,	// SHR16mi
    1053532U,	// SHR16r1
    1184604U,	// SHR16rCL
    2233180U,	// SHR16ri
    1065820U,	// SHR32m1
    1196892U,	// SHR32mCL
    148316U,	// SHR32mi
    1053532U,	// SHR32r1
    1184604U,	// SHR32rCL
    2233180U,	// SHR32ri
    1069916U,	// SHR64m1
    1200988U,	// SHR64mCL
    152412U,	// SHR64mi
    1053532U,	// SHR64r1
    1184604U,	// SHR64rCL
    2233180U,	// SHR64ri
    1074012U,	// SHR8m1
    1205084U,	// SHR8mCL
    156508U,	// SHR8mi
    1053532U,	// SHR8r1
    1184604U,	// SHR8rCL
    2233180U,	// SHR8ri
    201466104U,	// SHRD16mrCL
    872554744U,	// SHRD16mri8
    203559160U,	// SHRD16rrCL
    1143083256U,	// SHRD16rri8
    201474296U,	// SHRD32mrCL
    872562936U,	// SHRD32mri8
    203559160U,	// SHRD32rrCL
    1143083256U,	// SHRD32rri8
    201478392U,	// SHRD64mrCL
    872567032U,	// SHRD64mri8
    203559160U,	// SHRD64rrCL
    1143083256U,	// SHRD64rri8
    885134514U,	// SHRX32rm
    350360754U,	// SHRX32rr
    889328818U,	// SHRX64rm
    350360754U,	// SHRX64rr
    54273U,	// SIDT16m
    54273U,	// SIDT32m
    54273U,	// SIDT64m
    3445U,	// SKINIT
    9229U,	// SLDT16m
    5133U,	// SLDT16r
    5133U,	// SLDT32r
    9229U,	// SLDT64m
    5133U,	// SLDT64r
    9331U,	// SMSW16m
    5235U,	// SMSW16r
    5235U,	// SMSW32r
    5235U,	// SMSW64r
    2711U,	// STAC
    2765U,	// STC
    2818U,	// STD
    2949U,	// STGI
    2958U,	// STI
    974940U,	// STOSB
    717060U,	// STOSL
    906024U,	// STOSQ
    590975U,	// STOSW
    5006U,	// STR16r
    5006U,	// STR32r
    5006U,	// STR64r
    9102U,	// STRm
    5477U,	// SUB16i16
    139383U,	// SUB16mi
    139383U,	// SUB16mi8
    139383U,	// SUB16mr
    2240631U,	// SUB16ri
    2240631U,	// SUB16ri8
    4337783U,	// SUB16rm
    2240631U,	// SUB16rr
    2232439U,	// SUB16rr_REV
    5612U,	// SUB32i32
    147575U,	// SUB32mi
    147575U,	// SUB32mi8
    147575U,	// SUB32mr
    2240631U,	// SUB32ri
    2240631U,	// SUB32ri8
    6434935U,	// SUB32rm
    2240631U,	// SUB32rr
    2232439U,	// SUB32rr_REV
    5760U,	// SUB64i32
    151671U,	// SUB64mi32
    151671U,	// SUB64mi8
    151671U,	// SUB64mr
    2240631U,	// SUB64ri32
    2240631U,	// SUB64ri8
    8532087U,	// SUB64rm
    2240631U,	// SUB64rr
    2232439U,	// SUB64rr_REV
    5375U,	// SUB8i8
    155767U,	// SUB8mi
    155767U,	// SUB8mr
    2240631U,	// SUB8ri
    2240631U,	// SUB8ri8
    10629239U,	// SUB8rm
    2240631U,	// SUB8rr
    2232439U,	// SUB8rr_REV
    3255U,	// SWAPGS
    3021U,	// SYSCALL
    3148U,	// SYSENTER
    3339U,	// SYSEXIT
    3339U,	// SYSEXIT64
    3332U,	// SYSRET
    3332U,	// SYSRET64
    12718235U,	// T1MSKC32rm
    14815387U,	// T1MSKC32rr
    16912539U,	// T1MSKC64rm
    14815387U,	// T1MSKC64rr
    1340123U,	// TAILJMPd
    1340123U,	// TAILJMPd64
    1327835U,	// TAILJMPm
    1331931U,	// TAILJMPm64
    0U,	// TAILJMPr
    1315547U,	// TAILJMPr64
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    5561U,	// TEST16i16
    140354U,	// TEST16mi
    140354U,	// TEST16mi_alt
    14816322U,	// TEST16ri
    14816322U,	// TEST16ri_alt
    140354U,	// TEST16rm
    14816322U,	// TEST16rr
    5729U,	// TEST32i32
    148546U,	// TEST32mi
    148546U,	// TEST32mi_alt
    14816322U,	// TEST32ri
    14816322U,	// TEST32ri_alt
    148546U,	// TEST32rm
    14816322U,	// TEST32rr
    5868U,	// TEST64i32
    152642U,	// TEST64mi32
    152642U,	// TEST64mi32_alt
    14816322U,	// TEST64ri32
    14816322U,	// TEST64ri32_alt
    152642U,	// TEST64rm
    14816322U,	// TEST64rr
    5449U,	// TEST8i8
    156738U,	// TEST8mi
    156738U,	// TEST8mi_alt
    14816322U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    14816322U,	// TEST8ri_alt
    156738U,	// TEST8rm
    14816322U,	// TEST8rr
    2391U,	// TLSCall_32
    2481U,	// TLSCall_64
    2404U,	// TLS_addr32
    2494U,	// TLS_addr64
    2417U,	// TLS_base_addr32
    2507U,	// TLS_base_addr64
    2435U,	// TRAP
    19010591U,	// TZCNT16rm
    14816287U,	// TZCNT16rr
    12719135U,	// TZCNT32rm
    14816287U,	// TZCNT32rr
    16913439U,	// TZCNT64rm
    14816287U,	// TZCNT64rr
    12718647U,	// TZMSK32rm
    14815799U,	// TZMSK32rr
    16912951U,	// TZMSK64rm
    14815799U,	// TZMSK64rr
    2645U,	// UD2B
    3041007384U,	// VAARG_64
    350361416U,	// VASTART_SAVE_XMM_REGS
    9075U,	// VERRm
    4979U,	// VERRr
    9319U,	// VERWm
    5223U,	// VERWr
    3014U,	// VMCALL
    21309U,	// VMCLEARm
    2752U,	// VMFUNC
    2935U,	// VMLAUNCH
    3413U,	// VMLOAD32
    3468U,	// VMLOAD64
    3006U,	// VMMCALL
    20704U,	// VMPTRLDm
    21576U,	// VMPTRSTm
    147634U,	// VMREAD32rm
    14815410U,	// VMREAD32rr
    151730U,	// VMREAD64rm
    14815410U,	// VMREAD64rr
    2838U,	// VMRESUME
    3435U,	// VMRUN32
    3490U,	// VMRUN64
    3424U,	// VMSAVE32
    3479U,	// VMSAVE64
    12718534U,	// VMWRITE32rm
    14815686U,	// VMWRITE32rr
    16912838U,	// VMWRITE64rm
    14815686U,	// VMWRITE64rr
    2902U,	// VMXOFF
    21144U,	// VMXON
    29266U,	// W64ALLOCA
    2822U,	// WBINVD
    3048U,	// WIN_ALLOCA
    2962U,	// WIN_FTOL_32
    2962U,	// WIN_FTOL_64
    4514U,	// WRFSBASE
    4514U,	// WRFSBASE64
    4534U,	// WRGSBASE
    4534U,	// WRGSBASE64
    3163U,	// WRMSR
    139450U,	// XADD16rm
    14815418U,	// XADD16rr
    147642U,	// XADD32rm
    14815418U,	// XADD32rr
    151738U,	// XADD64rm
    14815418U,	// XADD64rr
    155834U,	// XADD8rm
    14815418U,	// XADD8rr
    5513U,	// XCHG16ar
    86512U,	// XCHG16rm
    123376U,	// XCHG16rr
    5676U,	// XCHG32ar
    5676U,	// XCHG32ar64
    90608U,	// XCHG32rm
    123376U,	// XCHG32rr
    5800U,	// XCHG64ar
    94704U,	// XCHG64rm
    123376U,	// XCHG64rr
    98800U,	// XCHG8rm
    123376U,	// XCHG8rr
    2716U,	// XCRYPTCBC
    2660U,	// XCRYPTCFB
    3169U,	// XCRYPTCTR
    2650U,	// XCRYPTECB
    2670U,	// XCRYPTOFB
    3351U,	// XGETBV
    2700U,	// XLAT
    5540U,	// XOR16i16
    140142U,	// XOR16mi
    140142U,	// XOR16mi8
    140142U,	// XOR16mr
    2241390U,	// XOR16ri
    2241390U,	// XOR16ri8
    4338542U,	// XOR16rm
    2241390U,	// XOR16rr
    2233198U,	// XOR16rr_REV
    5706U,	// XOR32i32
    148334U,	// XOR32mi
    148334U,	// XOR32mi8
    148334U,	// XOR32mr
    2241390U,	// XOR32ri
    2241390U,	// XOR32ri8
    6435694U,	// XOR32rm
    2241390U,	// XOR32rr
    2233198U,	// XOR32rr_REV
    5845U,	// XOR64i32
    152430U,	// XOR64mi32
    152430U,	// XOR64mi8
    152430U,	// XOR64mr
    2241390U,	// XOR64ri32
    2241390U,	// XOR64ri8
    8532846U,	// XOR64rm
    2241390U,	// XOR64rr
    2233198U,	// XOR64rr_REV
    5428U,	// XOR8i8
    156526U,	// XOR8mi
    156526U,	// XOR8mr
    2241390U,	// XOR8ri
    2241390U,	// XOR8ri8
    10629998U,	// XOR8rm
    2241390U,	// XOR8rr
    2233198U,	// XOR8rr_REV
    54118U,	// XRSTOR
    53258U,	// XRSTOR64
    53711U,	// XSAVE
    53249U,	// XSAVE64
    54328U,	// XSAVEOPT
    53268U,	// XSAVEOPT64
    3358U,	// XSETBV
    2343U,	// XSHA1
    2538U,	// XSHA256
    2858U,	// XSTORE
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'x', 's', 'a', 'v', 'e', '6', '4', 9, 0,
  /* 9 */ 'x', 'r', 's', 't', 'o', 'r', '6', '4', 9, 0,
  /* 19 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', '6', '4', 9, 0,
  /* 31 */ 'l', 'e', 'a', 9, 0,
  /* 36 */ 'j', 'a', 9, 0,
  /* 40 */ 's', 'e', 't', 'a', 9, 0,
  /* 46 */ 'c', 'm', 'o', 'v', 'a', 9, 0,
  /* 53 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '1', '6', 'b', 9, 0,
  /* 65 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '8', 'b', 9, 0,
  /* 76 */ 's', 'b', 'b', 9, 0,
  /* 81 */ 'j', 'b', 9, 0,
  /* 85 */ 'i', 'n', 's', 'b', 9, 0,
  /* 91 */ 's', 't', 'o', 's', 'b', 9, 0,
  /* 98 */ 'c', 'm', 'p', 's', 'b', 9, 0,
  /* 105 */ 'm', 'o', 'v', 's', 'b', 9, 0,
  /* 112 */ 's', 'e', 't', 'b', 9, 0,
  /* 118 */ 's', 'u', 'b', 9, 0,
  /* 123 */ 'c', 'm', 'o', 'v', 'b', 9, 0,
  /* 130 */ 'a', 'd', 'c', 9, 0,
  /* 135 */ 'd', 'e', 'c', 9, 0,
  /* 140 */ 'b', 'l', 'c', 'i', 'c', 9, 0,
  /* 147 */ 'b', 'l', 's', 'i', 'c', 9, 0,
  /* 154 */ 't', '1', 'm', 's', 'k', 'c', 9, 0,
  /* 162 */ 'i', 'n', 'c', 9, 0,
  /* 167 */ 'b', 't', 'c', 9, 0,
  /* 172 */ 'a', 'a', 'd', 9, 0,
  /* 177 */ 'v', 'm', 'r', 'e', 'a', 'd', 9, 0,
  /* 185 */ 'x', 'a', 'd', 'd', 9, 0,
  /* 191 */ 'r', 'd', 's', 'e', 'e', 'd', 9, 0,
  /* 199 */ 'i', 'n', 'v', 'p', 'c', 'i', 'd', 9, 0,
  /* 208 */ 'i', 'n', 'v', 'v', 'p', 'i', 'd', 9, 0,
  /* 217 */ 's', 'h', 'l', 'd', 9, 0,
  /* 223 */ 'v', 'm', 'p', 't', 'r', 'l', 'd', 9, 0,
  /* 232 */ 'r', 'd', 'r', 'a', 'n', 'd', 9, 0,
  /* 240 */ 'b', 'o', 'u', 'n', 'd', 9, 0,
  /* 247 */ 's', 'h', 'r', 'd', 9, 0,
  /* 253 */ 'i', 'n', 's', 'd', 9, 0,
  /* 259 */ 's', 't', 'o', 's', 'd', 9, 0,
  /* 266 */ 'c', 'm', 'p', 's', 'd', 9, 0,
  /* 273 */ 'm', 'o', 'v', 's', 'd', 9, 0,
  /* 280 */ 'm', 'o', 'v', 's', 'x', 'd', 9, 0,
  /* 288 */ 'j', 'a', 'e', 9, 0,
  /* 293 */ 's', 'e', 't', 'a', 'e', 9, 0,
  /* 300 */ 'c', 'm', 'o', 'v', 'a', 'e', 9, 0,
  /* 308 */ 'j', 'b', 'e', 9, 0,
  /* 313 */ 's', 'e', 't', 'b', 'e', 9, 0,
  /* 320 */ 'c', 'm', 'o', 'v', 'b', 'e', 9, 0,
  /* 328 */ 'j', 'g', 'e', 9, 0,
  /* 333 */ 's', 'e', 't', 'g', 'e', 9, 0,
  /* 340 */ 'c', 'm', 'o', 'v', 'g', 'e', 9, 0,
  /* 348 */ 'j', 'e', 9, 0,
  /* 352 */ 'j', 'l', 'e', 9, 0,
  /* 357 */ 's', 'e', 't', 'l', 'e', 9, 0,
  /* 364 */ 'c', 'm', 'o', 'v', 'l', 'e', 9, 0,
  /* 372 */ 'j', 'n', 'e', 9, 0,
  /* 377 */ 'l', 'o', 'o', 'p', 'n', 'e', 9, 0,
  /* 385 */ 's', 'e', 't', 'n', 'e', 9, 0,
  /* 392 */ 'c', 'm', 'o', 'v', 'n', 'e', 9, 0,
  /* 400 */ 'l', 'o', 'o', 'p', 'e', 9, 0,
  /* 407 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 417 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 427 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 437 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 447 */ 's', 'e', 't', 'e', 9, 0,
  /* 453 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 9, 0,
  /* 462 */ 'x', 's', 'a', 'v', 'e', 9, 0,
  /* 469 */ 'c', 'm', 'o', 'v', 'e', 9, 0,
  /* 476 */ 'b', 's', 'f', 9, 0,
  /* 481 */ 'r', 'e', 't', 'f', 9, 0,
  /* 487 */ 'n', 'e', 'g', 9, 0,
  /* 492 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 9, 0,
  /* 501 */ 'j', 'g', 9, 0,
  /* 505 */ 'i', 'n', 'v', 'l', 'p', 'g', 9, 0,
  /* 513 */ 's', 'e', 't', 'g', 9, 0,
  /* 519 */ 'c', 'm', 'o', 'v', 'g', 9, 0,
  /* 526 */ 'p', 'u', 's', 'h', 9, 0,
  /* 532 */ 'b', 'l', 'c', 'i', 9, 0,
  /* 538 */ 'b', 'z', 'h', 'i', 9, 0,
  /* 544 */ 'b', 'l', 's', 'i', 9, 0,
  /* 550 */ 'b', 'l', 'c', 'm', 's', 'k', 9, 0,
  /* 558 */ 'b', 'l', 's', 'm', 's', 'k', 9, 0,
  /* 566 */ 't', 'z', 'm', 's', 'k', 9, 0,
  /* 573 */ 's', 'a', 'l', 9, 0,
  /* 578 */ 'r', 'c', 'l', 9, 0,
  /* 583 */ 's', 'h', 'l', 9, 0,
  /* 588 */ 'j', 'l', 9, 0,
  /* 592 */ 'l', 'c', 'a', 'l', 'l', 9, 0,
  /* 599 */ 'b', 'l', 'c', 'f', 'i', 'l', 'l', 9, 0,
  /* 608 */ 'b', 'l', 's', 'f', 'i', 'l', 'l', 9, 0,
  /* 617 */ 'r', 'o', 'l', 9, 0,
  /* 622 */ 'a', 'r', 'p', 'l', 9, 0,
  /* 628 */ 'l', 's', 'l', 9, 0,
  /* 633 */ 's', 'e', 't', 'l', 9, 0,
  /* 639 */ 'i', 'm', 'u', 'l', 9, 0,
  /* 645 */ 'c', 'm', 'o', 'v', 'l', 9, 0,
  /* 652 */ 'a', 'a', 'm', 9, 0,
  /* 657 */ 'a', 'n', 'd', 'n', 9, 0,
  /* 663 */ 'v', 'm', 'x', 'o', 'n', 9, 0,
  /* 670 */ 'j', 'o', 9, 0,
  /* 674 */ 'j', 'n', 'o', 9, 0,
  /* 679 */ 's', 'e', 't', 'n', 'o', 9, 0,
  /* 686 */ 'c', 'm', 'o', 'v', 'n', 'o', 9, 0,
  /* 694 */ 's', 'e', 't', 'o', 9, 0,
  /* 700 */ 'c', 'm', 'o', 'v', 'o', 9, 0,
  /* 707 */ 'b', 's', 'w', 'a', 'p', 9, 0,
  /* 714 */ 'p', 'd', 'e', 'p', 9, 0,
  /* 720 */ 'j', 'p', 9, 0,
  /* 724 */ 'c', 'm', 'p', 9, 0,
  /* 729 */ 'l', 'j', 'm', 'p', 9, 0,
  /* 735 */ 'j', 'n', 'p', 9, 0,
  /* 740 */ 's', 'e', 't', 'n', 'p', 9, 0,
  /* 747 */ 'c', 'm', 'o', 'v', 'n', 'p', 9, 0,
  /* 755 */ 'n', 'o', 'p', 9, 0,
  /* 760 */ 'l', 'o', 'o', 'p', 9, 0,
  /* 766 */ 'p', 'o', 'p', 9, 0,
  /* 771 */ 's', 'e', 't', 'p', 9, 0,
  /* 777 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 793 */ 'c', 'm', 'o', 'v', 'p', 9, 0,
  /* 800 */ 'r', 'e', 't', 'f', 'q', 9, 0,
  /* 807 */ 's', 't', 'o', 's', 'q', 9, 0,
  /* 814 */ 'c', 'm', 'p', 's', 'q', 9, 0,
  /* 821 */ 'm', 'o', 'v', 's', 'q', 9, 0,
  /* 828 */ 'v', 'm', 'c', 'l', 'e', 'a', 'r', 9, 0,
  /* 837 */ 'l', 'a', 'r', 9, 0,
  /* 842 */ 's', 'a', 'r', 9, 0,
  /* 847 */ 'r', 'c', 'r', 9, 0,
  /* 852 */ 'e', 'n', 't', 'e', 'r', 9, 0,
  /* 859 */ 's', 'h', 'r', 9, 0,
  /* 864 */ 'r', 'o', 'r', 9, 0,
  /* 869 */ 'x', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 877 */ 'x', 'o', 'r', 9, 0,
  /* 882 */ 'v', 'e', 'r', 'r', 9, 0,
  /* 888 */ 'b', 's', 'r', 9, 0,
  /* 893 */ 'b', 'l', 's', 'r', 9, 0,
  /* 899 */ 'b', 't', 'r', 9, 0,
  /* 904 */ 'l', 't', 'r', 9, 0,
  /* 909 */ 's', 't', 'r', 9, 0,
  /* 914 */ 'b', 'e', 'x', 't', 'r', 9, 0,
  /* 921 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 0,
  /* 929 */ 'b', 'l', 'c', 's', 9, 0,
  /* 935 */ 'l', 'd', 's', 9, 0,
  /* 940 */ 'l', 'e', 's', 9, 0,
  /* 945 */ 'l', 'f', 's', 9, 0,
  /* 950 */ 'l', 'g', 's', 9, 0,
  /* 955 */ 'j', 's', 9, 0,
  /* 959 */ 'j', 'n', 's', 9, 0,
  /* 964 */ 's', 'e', 't', 'n', 's', 9, 0,
  /* 971 */ 'c', 'm', 'o', 'v', 'n', 's', 9, 0,
  /* 979 */ 'l', 's', 's', 9, 0,
  /* 984 */ 'b', 't', 's', 9, 0,
  /* 989 */ 's', 'e', 't', 's', 9, 0,
  /* 995 */ 'c', 'm', 'o', 'v', 's', 9, 0,
  /* 1002 */ 'b', 't', 9, 0,
  /* 1006 */ 'l', 'g', 'd', 't', 9, 0,
  /* 1012 */ 's', 'g', 'd', 't', 9, 0,
  /* 1018 */ 'l', 'i', 'd', 't', 9, 0,
  /* 1024 */ 's', 'i', 'd', 't', 9, 0,
  /* 1030 */ 'l', 'l', 'd', 't', 9, 0,
  /* 1036 */ 's', 'l', 'd', 't', 9, 0,
  /* 1042 */ 'r', 'e', 't', 9, 0,
  /* 1047 */ 'l', 'z', 'c', 'n', 't', 9, 0,
  /* 1054 */ 't', 'z', 'c', 'n', 't', 9, 0,
  /* 1061 */ 'i', 'n', 't', 9, 0,
  /* 1066 */ 'n', 'o', 't', 9, 0,
  /* 1071 */ 'i', 'n', 'v', 'e', 'p', 't', 9, 0,
  /* 1079 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 9, 0,
  /* 1089 */ 't', 'e', 's', 't', 9, 0,
  /* 1095 */ 'v', 'm', 'p', 't', 'r', 's', 't', 9, 0,
  /* 1104 */ 'o', 'u', 't', 9, 0,
  /* 1109 */ 'p', 'e', 'x', 't', 9, 0,
  /* 1115 */ 'i', 'd', 'i', 'v', 9, 0,
  /* 1121 */ 'm', 'o', 'v', 9, 0,
  /* 1126 */ 'v', 'e', 'r', 'w', 9, 0,
  /* 1132 */ 'l', 'm', 's', 'w', 9, 0,
  /* 1138 */ 's', 'm', 's', 'w', 9, 0,
  /* 1144 */ 'i', 'n', 's', 'w', 9, 0,
  /* 1150 */ 's', 't', 'o', 's', 'w', 9, 0,
  /* 1157 */ 'c', 'm', 'p', 's', 'w', 9, 0,
  /* 1164 */ 'm', 'o', 'v', 's', 'w', 9, 0,
  /* 1171 */ 'a', 'd', 'c', 'x', 9, 0,
  /* 1177 */ 's', 'h', 'l', 'x', 9, 0,
  /* 1183 */ 'm', 'u', 'l', 'x', 9, 0,
  /* 1189 */ 'a', 'd', 'o', 'x', 9, 0,
  /* 1195 */ 's', 'a', 'r', 'x', 9, 0,
  /* 1201 */ 's', 'h', 'r', 'x', 9, 0,
  /* 1207 */ 'r', 'o', 'r', 'x', 9, 0,
  /* 1213 */ 'm', 'o', 'v', 's', 'x', 9, 0,
  /* 1220 */ 'm', 'o', 'v', 'z', 'x', 9, 0,
  /* 1227 */ 'j', 'e', 'c', 'x', 'z', 9, 0,
  /* 1234 */ 'j', 'c', 'x', 'z', 9, 0,
  /* 1240 */ 'j', 'r', 'c', 'x', 'z', 9, 0,
  /* 1247 */ 's', 'b', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1256 */ 's', 'c', 'a', 's', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1267 */ 'l', 'o', 'd', 's', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1278 */ 's', 'u', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1287 */ 'a', 'd', 'c', 9, 'a', 'l', ',', 32, 0,
  /* 1296 */ 'a', 'd', 'd', 9, 'a', 'l', ',', 32, 0,
  /* 1305 */ 'a', 'n', 'd', 9, 'a', 'l', ',', 32, 0,
  /* 1314 */ 'i', 'n', 9, 'a', 'l', ',', 32, 0,
  /* 1322 */ 'c', 'm', 'p', 9, 'a', 'l', ',', 32, 0,
  /* 1331 */ 'x', 'o', 'r', 9, 'a', 'l', ',', 32, 0,
  /* 1340 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'a', 'l', ',', 32, 0,
  /* 1352 */ 't', 'e', 's', 't', 9, 'a', 'l', ',', 32, 0,
  /* 1362 */ 'm', 'o', 'v', 9, 'a', 'l', ',', 32, 0,
  /* 1371 */ 's', 'b', 'b', 9, 'a', 'x', ',', 32, 0,
  /* 1380 */ 's', 'u', 'b', 9, 'a', 'x', ',', 32, 0,
  /* 1389 */ 'a', 'd', 'c', 9, 'a', 'x', ',', 32, 0,
  /* 1398 */ 'a', 'd', 'd', 9, 'a', 'x', ',', 32, 0,
  /* 1407 */ 'a', 'n', 'd', 9, 'a', 'x', ',', 32, 0,
  /* 1416 */ 'x', 'c', 'h', 'g', 9, 'a', 'x', ',', 32, 0,
  /* 1426 */ 'i', 'n', 9, 'a', 'x', ',', 32, 0,
  /* 1434 */ 'c', 'm', 'p', 9, 'a', 'x', ',', 32, 0,
  /* 1443 */ 'x', 'o', 'r', 9, 'a', 'x', ',', 32, 0,
  /* 1452 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'a', 'x', ',', 32, 0,
  /* 1464 */ 't', 'e', 's', 't', 9, 'a', 'x', ',', 32, 0,
  /* 1474 */ 'm', 'o', 'v', 9, 'a', 'x', ',', 32, 0,
  /* 1483 */ 's', 'c', 'a', 's', 'w', 9, 'a', 'x', ',', 32, 0,
  /* 1494 */ 'l', 'o', 'd', 's', 'w', 9, 'a', 'x', ',', 32, 0,
  /* 1505 */ 's', 'b', 'b', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1515 */ 's', 'u', 'b', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1525 */ 'a', 'd', 'c', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1535 */ 'a', 'd', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1545 */ 'a', 'n', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1555 */ 's', 'c', 'a', 's', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1567 */ 'l', 'o', 'd', 's', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1579 */ 'x', 'c', 'h', 'g', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1590 */ 'i', 'n', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1599 */ 'c', 'm', 'p', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1609 */ 'x', 'o', 'r', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1619 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1632 */ 't', 'e', 's', 't', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1643 */ 'm', 'o', 'v', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1653 */ 's', 'b', 'b', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1663 */ 's', 'u', 'b', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1673 */ 'a', 'd', 'c', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1683 */ 'a', 'd', 'd', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1693 */ 'a', 'n', 'd', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1703 */ 'x', 'c', 'h', 'g', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1714 */ 'c', 'm', 'p', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1724 */ 's', 'c', 'a', 's', 'q', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1736 */ 'l', 'o', 'd', 's', 'q', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1748 */ 'x', 'o', 'r', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1758 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1771 */ 't', 'e', 's', 't', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1782 */ 'o', 'u', 't', 's', 'b', 9, 'd', 'x', ',', 32, 0,
  /* 1793 */ 'o', 'u', 't', 's', 'd', 9, 'd', 'x', ',', 32, 0,
  /* 1804 */ 'o', 'u', 't', 's', 'w', 9, 'd', 'x', ',', 32, 0,
  /* 1815 */ '#', 'V', 'A', 'A', 'R', 'G', '_', '6', '4', 32, 0,
  /* 1826 */ 'r', 'e', 't', 9, '#', 'e', 'h', '_', 'r', 'e', 't', 'u', 'r', 'n', ',', 32, 'a', 'd', 'd', 'r', ':', 32, 0,
  /* 1849 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'X', 'M', 'M', 32, 0,
  /* 1863 */ '#', 'V', 'A', 'S', 'T', 'A', 'R', 'T', '_', 'S', 'A', 'V', 'E', '_', 'X', 'M', 'M', '_', 'R', 'E', 'G', 'S', 32, 0,
  /* 1887 */ '#', 'S', 'E', 'H', '_', 'S', 't', 'a', 'c', 'k', 'A', 'l', 'l', 'o', 'c', 32, 0,
  /* 1904 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 1920 */ '#', 'S', 'E', 'H', '_', 'S', 'e', 't', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 1935 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'R', 'e', 'g', 32, 0,
  /* 1949 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'R', 'e', 'g', 32, 0,
  /* 1963 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '3', '2', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 1983 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '1', '6', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2003 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '8', '0', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2023 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2043 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '1', '6', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2064 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2084 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2104 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2123 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2143 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2163 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2183 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2203 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2223 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2243 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2263 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2282 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '8', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2300 */ '#', 'A', 'C', 'Q', 'U', 'I', 'R', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2321 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2342 */ 'x', 's', 'h', 'a', '1', 0,
  /* 2348 */ 'i', 'n', 't', '1', 0,
  /* 2353 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 2372 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 2390 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '3', '2', 0,
  /* 2403 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 2416 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 2434 */ 'u', 'd', '2', 0,
  /* 2438 */ 'i', 'n', 't', '3', 0,
  /* 2443 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 2462 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 2480 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '6', '4', 0,
  /* 2493 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 2506 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 2524 */ 'r', 'e', 'x', '6', '4', 0,
  /* 2530 */ 'd', 'a', 't', 'a', '1', '6', 0,
  /* 2537 */ 'x', 's', 'h', 'a', '2', '5', '6', 0,
  /* 2545 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 2558 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 2565 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 2575 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 0,
  /* 2593 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 0,
  /* 2609 */ '#', 'M', 'E', 'M', 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 2621 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 2636 */ 'a', 'a', 'a', 0,
  /* 2640 */ 'd', 'a', 'a', 0,
  /* 2644 */ 'u', 'd', '2', 'b', 0,
  /* 2649 */ 'x', 'c', 'r', 'y', 'p', 't', 'e', 'c', 'b', 0,
  /* 2659 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'f', 'b', 0,
  /* 2669 */ 'x', 'c', 'r', 'y', 'p', 't', 'o', 'f', 'b', 0,
  /* 2679 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'b', 0,
  /* 2689 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'b', 0,
  /* 2699 */ 'x', 'l', 'a', 't', 'b', 0,
  /* 2705 */ 'c', 'l', 'a', 'c', 0,
  /* 2710 */ 's', 't', 'a', 'c', 0,
  /* 2715 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'b', 'c', 0,
  /* 2725 */ 'g', 'e', 't', 's', 'e', 'c', 0,
  /* 2732 */ 's', 'a', 'l', 'c', 0,
  /* 2737 */ 'c', 'l', 'c', 0,
  /* 2741 */ 'c', 'm', 'c', 0,
  /* 2745 */ 'r', 'd', 'p', 'm', 'c', 0,
  /* 2751 */ 'v', 'm', 'f', 'u', 'n', 'c', 0,
  /* 2758 */ 'r', 'd', 't', 's', 'c', 0,
  /* 2764 */ 's', 't', 'c', 0,
  /* 2768 */ 'p', 'u', 's', 'h', 'f', 'd', 0,
  /* 2775 */ 'p', 'o', 'p', 'f', 'd', 0,
  /* 2781 */ 'c', 'p', 'u', 'i', 'd', 0,
  /* 2787 */ 'c', 'l', 'd', 0,
  /* 2791 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'd', 0,
  /* 2801 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'd', 0,
  /* 2811 */ 'i', 'r', 'e', 't', 'd', 0,
  /* 2817 */ 's', 't', 'd', 0,
  /* 2821 */ 'w', 'b', 'i', 'n', 'v', 'd', 0,
  /* 2828 */ 'c', 'w', 'd', 0,
  /* 2832 */ 'c', 'w', 'd', 'e', 0,
  /* 2837 */ 'v', 'm', 'r', 'e', 's', 'u', 'm', 'e', 0,
  /* 2846 */ 'r', 'e', 'p', 'n', 'e', 0,
  /* 2852 */ 'c', 'd', 'q', 'e', 0,
  /* 2857 */ 'x', 's', 't', 'o', 'r', 'e', 0,
  /* 2864 */ '#', 'S', 'E', 'H', '_', 'E', 'p', 'i', 'l', 'o', 'g', 'u', 'e', 0,
  /* 2878 */ '#', 'S', 'E', 'H', '_', 'E', 'n', 'd', 'P', 'r', 'o', 'l', 'o', 'g', 'u', 'e', 0,
  /* 2895 */ 'l', 'e', 'a', 'v', 'e', 0,
  /* 2901 */ 'v', 'm', 'x', 'o', 'f', 'f', 0,
  /* 2908 */ 'l', 'a', 'h', 'f', 0,
  /* 2913 */ 's', 'a', 'h', 'f', 0,
  /* 2918 */ 'p', 'u', 's', 'h', 'f', 0,
  /* 2924 */ 'p', 'o', 'p', 'f', 0,
  /* 2929 */ 'r', 'e', 't', 'f', 0,
  /* 2934 */ 'v', 'm', 'l', 'a', 'u', 'n', 'c', 'h', 0,
  /* 2943 */ 'c', 'l', 'g', 'i', 0,
  /* 2948 */ 's', 't', 'g', 'i', 0,
  /* 2953 */ 'c', 'l', 'i', 0,
  /* 2957 */ 's', 't', 'i', 0,
  /* 2961 */ '#', 32, 'w', 'i', 'n', '3', '2', 32, 'f', 'p', 't', 'o', 'u', 'i', 0,
  /* 2976 */ 'l', 'o', 'c', 'k', 0,
  /* 2981 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'a', 'l', 0,
  /* 2992 */ 'p', 'u', 's', 'h', 'a', 'l', 0,
  /* 2999 */ 'p', 'o', 'p', 'a', 'l', 0,
  /* 3005 */ 'v', 'm', 'm', 'c', 'a', 'l', 'l', 0,
  /* 3013 */ 'v', 'm', 'c', 'a', 'l', 'l', 0,
  /* 3020 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 0,
  /* 3028 */ 'm', 'o', 'n', 't', 'm', 'u', 'l', 0,
  /* 3036 */ 'f', 's', 'e', 't', 'p', 'm', 0,
  /* 3043 */ 'r', 's', 'm', 0,
  /* 3047 */ '#', 32, 'd', 'y', 'n', 'a', 'm', 'i', 'c', 32, 's', 't', 'a', 'c', 'k', 32, 'a', 'l', 'l', 'o', 'c', 'a', 't', 'i', 'o', 'n', 0,
  /* 3074 */ 'c', 'q', 'o', 0,
  /* 3078 */ 'i', 'n', 't', 'o', 0,
  /* 3083 */ 'r', 'd', 't', 's', 'c', 'p', 0,
  /* 3090 */ 'r', 'e', 'p', 0,
  /* 3094 */ 'n', 'o', 'p', 0,
  /* 3098 */ 'c', 'd', 'q', 0,
  /* 3102 */ 'p', 'u', 's', 'h', 'f', 'q', 0,
  /* 3109 */ 'p', 'o', 'p', 'f', 'q', 0,
  /* 3115 */ 'r', 'e', 't', 'f', 'q', 0,
  /* 3121 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'q', 0,
  /* 3131 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'q', 0,
  /* 3141 */ 'i', 'r', 'e', 't', 'q', 0,
  /* 3147 */ 's', 'y', 's', 'e', 'n', 't', 'e', 'r', 0,
  /* 3156 */ 'r', 'd', 'm', 's', 'r', 0,
  /* 3162 */ 'w', 'r', 'm', 's', 'r', 0,
  /* 3168 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 't', 'r', 0,
  /* 3178 */ 'a', 'a', 's', 0,
  /* 3182 */ 'd', 'a', 's', 0,
  /* 3186 */ 'p', 'u', 's', 'h', 9, 'c', 's', 0,
  /* 3194 */ 'p', 'u', 's', 'h', 9, 'd', 's', 0,
  /* 3202 */ 'p', 'o', 'p', 9, 'd', 's', 0,
  /* 3209 */ 'p', 'u', 's', 'h', 9, 'e', 's', 0,
  /* 3217 */ 'p', 'o', 'p', 9, 'e', 's', 0,
  /* 3224 */ 'p', 'u', 's', 'h', 9, 'f', 's', 0,
  /* 3232 */ 'p', 'o', 'p', 9, 'f', 's', 0,
  /* 3239 */ 'p', 'u', 's', 'h', 9, 'g', 's', 0,
  /* 3247 */ 'p', 'o', 'p', 9, 'g', 's', 0,
  /* 3254 */ 's', 'w', 'a', 'p', 'g', 's', 0,
  /* 3261 */ '#', 32, 'v', 'a', 'r', 'i', 'a', 'b', 'l', 'e', 32, 's', 'i', 'z', 'e', 'd', 32, 'a', 'l', 'l', 'o', 'c', 'a', 32, 'f', 'o', 'r', 32, 's', 'e', 'g', 'm', 'e', 'n', 't', 'e', 'd', 32, 's', 't', 'a', 'c', 'k', 's', 0,
  /* 3306 */ 'p', 'u', 's', 'h', 9, 's', 's', 0,
  /* 3314 */ 'p', 'o', 'p', 9, 's', 's', 0,
  /* 3321 */ 'c', 'l', 't', 's', 0,
  /* 3326 */ 'i', 'r', 'e', 't', 0,
  /* 3331 */ 's', 'y', 's', 'r', 'e', 't', 0,
  /* 3338 */ 's', 'y', 's', 'e', 'x', 'i', 't', 0,
  /* 3346 */ 'h', 'l', 't', 0,
  /* 3350 */ 'x', 'g', 'e', 't', 'b', 'v', 0,
  /* 3357 */ 'x', 's', 'e', 't', 'b', 'v', 0,
  /* 3364 */ 'p', 'u', 's', 'h', 'a', 'w', 0,
  /* 3371 */ 'p', 'o', 'p', 'a', 'w', 0,
  /* 3377 */ 'c', 'b', 'w', 0,
  /* 3381 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'w', 0,
  /* 3391 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'w', 0,
  /* 3401 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'a', 'x', 0,
  /* 3412 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, 'e', 'a', 'x', 0,
  /* 3423 */ 'v', 'm', 's', 'a', 'v', 'e', 9, 'e', 'a', 'x', 0,
  /* 3434 */ 'v', 'm', 'r', 'u', 'n', 9, 'e', 'a', 'x', 0,
  /* 3444 */ 's', 'k', 'i', 'n', 'i', 't', 9, 'e', 'a', 'x', 0,
  /* 3455 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'e', 'a', 'x', 0,
  /* 3467 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, 'r', 'a', 'x', 0,
  /* 3478 */ 'v', 'm', 's', 'a', 'v', 'e', 9, 'r', 'a', 'x', 0,
  /* 3489 */ 'v', 'm', 'r', 'u', 'n', 9, 'r', 'a', 'x', 0,
  /* 3499 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, 'e', 'a', 'x', ',', 32, 'e', 'c', 'x', 0,
  /* 3516 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, 'r', 'a', 'x', ',', 32, 'e', 'c', 'x', 0,
  /* 3533 */ 'i', 'n', 9, 'a', 'l', ',', 32, 'd', 'x', 0,
  /* 3543 */ 'i', 'n', 9, 'a', 'x', ',', 32, 'd', 'x', 0,
  /* 3553 */ 'i', 'n', 9, 'e', 'a', 'x', ',', 32, 'd', 'x', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint32_t Bits = OpInfo[MCInst_getOpcode(MI)];
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif

  // Fragment 0 encoded into 5 bits for 31 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 12) & 31);
  switch ((Bits >> 12) & 31) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, AAA, AAS, ACQUIRE_MOV...
    return;
    break;
  case 1:
    // AAD8i8, AAM8i8, ADC16i16, ADC16rr_REV, ADC32i32, ADC32rr_REV, ADC64i32...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADD16mi, ADD16mi8, ADD16mr, AND16mi, AND16...
    printi16mem(MI, 0, O); 
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // ADC32mi, ADC32mi8, ADC32mr, ADD32mi, ADD32mi8, ADD32mr, AND32mi, AND32...
    printi32mem(MI, 0, O); 
    break;
  case 5:
    // ADC64mi32, ADC64mi8, ADC64mr, ADD64mi32, ADD64mi8, ADD64mr, AND64mi32,...
    printi64mem(MI, 0, O); 
    break;
  case 6:
    // ADC8mi, ADC8mr, ADD8mi, ADD8mr, AND8mi, AND8mr, CMP8mi, CMP8mr, CMPXCH...
    printi8mem(MI, 0, O); 
    break;
  case 7:
    // CALL64pcrel32, CALLpcrel16, CALLpcrel32, EH_SjLj_Setup, JAE_1, JAE_2, ...
    printPCRelImm(MI, 0, O); 
    break;
  case 8:
    // CMPSB
    printSrcIdx8(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx8(MI, 0, O); 
    return;
    break;
  case 9:
    // CMPSL
    printSrcIdx32(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx32(MI, 0, O); 
    return;
    break;
  case 10:
    // CMPSQ
    printSrcIdx64(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx64(MI, 0, O); 
    return;
    break;
  case 11:
    // CMPSW
    printSrcIdx16(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx16(MI, 0, O); 
    return;
    break;
  case 12:
    // CMPXCHG16B, LCMPXCHG16B
    printi128mem(MI, 0, O); 
    return;
    break;
  case 13:
    // FARCALL16m, FARCALL32m, FARCALL64, FARJMP16m, FARJMP32m, FARJMP64, LGD...
    printopaquemem(MI, 0, O); 
    return;
    break;
  case 14:
    // INSB, MOVSB, SCASB, STOSB
    printDstIdx8(MI, 0, O); 
    break;
  case 15:
    // INSL, MOVSL, SCASL, STOSL
    printDstIdx32(MI, 0, O); 
    break;
  case 16:
    // INSW, MOVSW, SCASW, STOSW
    printDstIdx16(MI, 0, O); 
    break;
  case 17:
    // LODSB, OUTSB
    printSrcIdx8(MI, 0, O); 
    return;
    break;
  case 18:
    // LODSL, OUTSL
    printSrcIdx32(MI, 0, O); 
    return;
    break;
  case 19:
    // LODSQ
    printSrcIdx64(MI, 0, O); 
    return;
    break;
  case 20:
    // LODSW, OUTSW
    printSrcIdx16(MI, 0, O); 
    return;
    break;
  case 21:
    // LXADD16, XCHG16rm
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 22:
    // LXADD32, XCHG32rm
    printi32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 23:
    // LXADD64, XCHG64rm
    printi64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 24:
    // LXADD8, XCHG8rm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 25:
    // MOV16ao16, MOV16ao16_16, MOV16o16a, MOV16o16a_16, MOV64ao16, MOV64o16a
    printMemOffs16(MI, 0, O); 
    break;
  case 26:
    // MOV32ao32, MOV32ao32_16, MOV32o32a, MOV32o32a_16, MOV64ao32, MOV64o32a
    printMemOffs32(MI, 0, O); 
    break;
  case 27:
    // MOV64ao64, MOV64o64a
    printMemOffs64(MI, 0, O); 
    break;
  case 28:
    // MOV64ao8, MOV64o8a, MOV8ao8, MOV8ao8_16, MOV8o8a, MOV8o8a_16
    printMemOffs8(MI, 0, O); 
    break;
  case 29:
    // MOVSQ, SCASQ, STOSQ
    printDstIdx64(MI, 0, O); 
    break;
  case 30:
    // XCHG16rr, XCHG32rr, XCHG64rr, XCHG8rr
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 4 bits for 11 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 17) & 15);
  switch ((Bits >> 17) & 15) {
  default:   // unreachable.
  case 0:
    // AAD8i8, AAM8i8, ADC16i16, ADC32i32, ADC64i32, ADC8i8, ADD16i16, ADD32i...
    return;
    break;
  case 1:
    // ADC16mi, ADC16mi8, ADC16mr, ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC16...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // FARCALL16i, FARCALL32i, FARJMP16i, FARJMP32i
    SStream_concat0(O, ":"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 3:
    // INSB, INSL, INSW
    SStream_concat0(O, ", dx"); 
    op_addReg(MI, X86_REG_DX);
    return;
    break;
  case 4:
    // MOV16ao16, MOV16ao16_16, MOV64ao16, OUT16ir, STOSW
    SStream_concat0(O, ", ax"); 
    op_addReg(MI, X86_REG_AX);
    return;
    break;
  case 5:
    // MOV32ao32, MOV32ao32_16, MOV64ao32, OUT32ir, STOSL
    SStream_concat0(O, ", eax"); 
    op_addReg(MI, X86_REG_EAX);
    return;
    break;
  case 6:
    // MOV64ao64, STOSQ
    SStream_concat0(O, ", rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 7:
    // MOV64ao8, MOV8ao8, MOV8ao8_16, OUT8ir, STOSB
    SStream_concat0(O, ", al"); 
    op_addReg(MI, X86_REG_AL);
    return;
    break;
  case 8:
    // RCL16m1, RCL16r1, RCL32m1, RCL32r1, RCL64m1, RCL64r1, RCL8m1, RCL8r1, ...
    SStream_concat0(O, ", 1"); 
    op_addImm(MI, 1);
    return;
    break;
  case 9:
    // RCL16mCL, RCL16rCL, RCL32mCL, RCL32rCL, RCL64mCL, RCL64rCL, RCL8mCL, R...
    SStream_concat0(O, ", cl"); 
    op_addReg(MI, X86_REG_CL);
    return;
    break;
  case 10:
    // TAILJMPd, TAILJMPd64, TAILJMPm, TAILJMPm64, TAILJMPr64
    return;
    break;
  }


  // Fragment 2 encoded into 5 bits for 18 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 21) & 31);
  switch ((Bits >> 21) & 31) {
  default:   // unreachable.
  case 0:
    // ADC16mi, ADC16mi8, ADC16mr, ADC32mi, ADC32mi8, ADC32mr, ADC64mi32, ADC...
    printOperand(MI, 5, O); 
    break;
  case 1:
    // ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, ADC32rr, A...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // ADC16rm, ADD16rm, AND16rm, CMOVA16rm, CMOVAE16rm, CMOVB16rm, CMOVBE16r...
    printi16mem(MI, 2, O); 
    return;
    break;
  case 3:
    // ADC32rm, ADCX32rm, ADD32rm, AND32rm, CMOVA32rm, CMOVAE32rm, CMOVB32rm,...
    printi32mem(MI, 2, O); 
    return;
    break;
  case 4:
    // ADC64rm, ADCX64rm, ADD64rm, AND64rm, CMOVA64rm, CMOVAE64rm, CMOVB64rm,...
    printi64mem(MI, 2, O); 
    return;
    break;
  case 5:
    // ADC8rm, ADD8rm, AND8rm, OR8rm, SBB8rm, SUB8rm, XOR8rm
    printi8mem(MI, 2, O); 
    return;
    break;
  case 6:
    // ADOX32rm, BEXTR32rm, BEXTRI32mi, BLCFILL32rm, BLCI32rm, BLCIC32rm, BLC...
    printi32mem(MI, 1, O); 
    break;
  case 7:
    // ADOX32rr, ADOX64rr, ANDN32rm, ANDN32rr, ANDN64rm, ANDN64rr, ARPL16rr, ...
    printOperand(MI, 1, O); 
    break;
  case 8:
    // ADOX64rm, BEXTR64rm, BEXTRI64mi, BLCFILL64rm, BLCI64rm, BLCIC64rm, BLC...
    printi64mem(MI, 1, O); 
    break;
  case 9:
    // BSF16rm, BSR16rm, CMP16rm, IMUL16rmi, IMUL16rmi8, LAR16rm, LAR32rm, LA...
    printi16mem(MI, 1, O); 
    break;
  case 10:
    // CMP8rm, MOV8rm, MOV8rm_NOREX, MOVSX16rm8, MOVSX32rm8, MOVSX64rm8, MOVZ...
    printi8mem(MI, 1, O); 
    break;
  case 11:
    // INVEPT32, INVEPT64, INVPCID32, INVPCID64, INVVPID32, INVVPID64
    printi128mem(MI, 1, O); 
    return;
    break;
  case 12:
    // LDS16rm, LDS32rm, LES16rm, LES32rm, LFS16rm, LFS32rm, LFS64rm, LGS16rm...
    printopaquemem(MI, 1, O); 
    return;
    break;
  case 13:
    // MOVSB
    printSrcIdx8(MI, 1, O); 
    return;
    break;
  case 14:
    // MOVSL
    printSrcIdx32(MI, 1, O); 
    return;
    break;
  case 15:
    // MOVSQ
    printSrcIdx64(MI, 1, O); 
    return;
    break;
  case 16:
    // MOVSW
    printSrcIdx16(MI, 1, O); 
    return;
    break;
  case 17:
    // NOOP19rr
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 2 bits for 4 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 26) & 3);
  switch ((Bits >> 26) & 3) {
  default:   // unreachable.
  case 0:
    // ADC16mi, ADC16mi8, ADC16mr, ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, A...
    return;
    break;
  case 1:
    // ANDN32rm, ANDN32rr, ANDN64rm, ANDN64rr, BEXTR32rm, BEXTR32rr, BEXTR64r...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // MOV8mr_NOREX, MOV8rm_NOREX, MOV8rr_NOREX
    return;
    break;
  case 3:
    // SHLD16mrCL, SHLD16rrCL, SHLD32mrCL, SHLD32rrCL, SHLD64mrCL, SHLD64rrCL...
    SStream_concat0(O, ", cl"); 
    op_addReg(MI, X86_REG_CL);
    return;
    break;
  }


  // Fragment 4 encoded into 3 bits for 5 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 28) & 7);
  switch ((Bits >> 28) & 7) {
  default:   // unreachable.
  case 0:
    // ANDN32rm, MULX32rm, PDEP32rm, PEXT32rm
    printi32mem(MI, 2, O); 
    return;
    break;
  case 1:
    // ANDN32rr, ANDN64rr, BEXTR32rr, BEXTR64rr, BEXTRI32ri, BEXTRI64ri, BZHI...
    printOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // ANDN64rm, MULX64rm, PDEP64rm, PEXT64rm
    printi64mem(MI, 2, O); 
    return;
    break;
  case 3:
    // BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZHI32rm, BZHI64rm, IMUL...
    printOperand(MI, 6, O); 
    break;
  case 4:
    // SHLD16rri8, SHLD32rri8, SHLD64rri8, SHRD16rri8, SHRD32rri8, SHRD64rri8
    printOperand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 1 bits for 2 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 31) & 1);
  if ((Bits >> 31) & 1) {
    // VAARG_64
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
  } else {
    // BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZHI32rm, BZHI64rm, IMUL...
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 234 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 's', 't', '(', '0', ')', 0,
  /* 6 */ 's', 't', '(', '1', ')', 0,
  /* 12 */ 's', 't', '(', '2', ')', 0,
  /* 18 */ 's', 't', '(', '3', ')', 0,
  /* 24 */ 's', 't', '(', '4', ')', 0,
  /* 30 */ 's', 't', '(', '5', ')', 0,
  /* 36 */ 's', 't', '(', '6', ')', 0,
  /* 42 */ 's', 't', '(', '7', ')', 0,
  /* 48 */ 'x', 'm', 'm', '1', '0', 0,
  /* 54 */ 'y', 'm', 'm', '1', '0', 0,
  /* 60 */ 'z', 'm', 'm', '1', '0', 0,
  /* 66 */ 'c', 'r', '1', '0', 0,
  /* 71 */ 'x', 'm', 'm', '2', '0', 0,
  /* 77 */ 'y', 'm', 'm', '2', '0', 0,
  /* 83 */ 'z', 'm', 'm', '2', '0', 0,
  /* 89 */ 'x', 'm', 'm', '3', '0', 0,
  /* 95 */ 'y', 'm', 'm', '3', '0', 0,
  /* 101 */ 'z', 'm', 'm', '3', '0', 0,
  /* 107 */ 'k', '0', 0,
  /* 110 */ 'x', 'm', 'm', '0', 0,
  /* 115 */ 'y', 'm', 'm', '0', 0,
  /* 120 */ 'z', 'm', 'm', '0', 0,
  /* 125 */ 'f', 'p', '0', 0,
  /* 129 */ 'c', 'r', '0', 0,
  /* 133 */ 'd', 'r', '0', 0,
  /* 137 */ 'x', 'm', 'm', '1', '1', 0,
  /* 143 */ 'y', 'm', 'm', '1', '1', 0,
  /* 149 */ 'z', 'm', 'm', '1', '1', 0,
  /* 155 */ 'c', 'r', '1', '1', 0,
  /* 160 */ 'x', 'm', 'm', '2', '1', 0,
  /* 166 */ 'y', 'm', 'm', '2', '1', 0,
  /* 172 */ 'z', 'm', 'm', '2', '1', 0,
  /* 178 */ 'x', 'm', 'm', '3', '1', 0,
  /* 184 */ 'y', 'm', 'm', '3', '1', 0,
  /* 190 */ 'z', 'm', 'm', '3', '1', 0,
  /* 196 */ 'k', '1', 0,
  /* 199 */ 'x', 'm', 'm', '1', 0,
  /* 204 */ 'y', 'm', 'm', '1', 0,
  /* 209 */ 'z', 'm', 'm', '1', 0,
  /* 214 */ 'f', 'p', '1', 0,
  /* 218 */ 'c', 'r', '1', 0,
  /* 222 */ 'd', 'r', '1', 0,
  /* 226 */ 'x', 'm', 'm', '1', '2', 0,
  /* 232 */ 'y', 'm', 'm', '1', '2', 0,
  /* 238 */ 'z', 'm', 'm', '1', '2', 0,
  /* 244 */ 'c', 'r', '1', '2', 0,
  /* 249 */ 'x', 'm', 'm', '2', '2', 0,
  /* 255 */ 'y', 'm', 'm', '2', '2', 0,
  /* 261 */ 'z', 'm', 'm', '2', '2', 0,
  /* 267 */ 'k', '2', 0,
  /* 270 */ 'x', 'm', 'm', '2', 0,
  /* 275 */ 'y', 'm', 'm', '2', 0,
  /* 280 */ 'z', 'm', 'm', '2', 0,
  /* 285 */ 'f', 'p', '2', 0,
  /* 289 */ 'c', 'r', '2', 0,
  /* 293 */ 'd', 'r', '2', 0,
  /* 297 */ 'x', 'm', 'm', '1', '3', 0,
  /* 303 */ 'y', 'm', 'm', '1', '3', 0,
  /* 309 */ 'z', 'm', 'm', '1', '3', 0,
  /* 315 */ 'c', 'r', '1', '3', 0,
  /* 320 */ 'x', 'm', 'm', '2', '3', 0,
  /* 326 */ 'y', 'm', 'm', '2', '3', 0,
  /* 332 */ 'z', 'm', 'm', '2', '3', 0,
  /* 338 */ 'k', '3', 0,
  /* 341 */ 'x', 'm', 'm', '3', 0,
  /* 346 */ 'y', 'm', 'm', '3', 0,
  /* 351 */ 'z', 'm', 'm', '3', 0,
  /* 356 */ 'f', 'p', '3', 0,
  /* 360 */ 'c', 'r', '3', 0,
  /* 364 */ 'd', 'r', '3', 0,
  /* 368 */ 'x', 'm', 'm', '1', '4', 0,
  /* 374 */ 'y', 'm', 'm', '1', '4', 0,
  /* 380 */ 'z', 'm', 'm', '1', '4', 0,
  /* 386 */ 'c', 'r', '1', '4', 0,
  /* 391 */ 'x', 'm', 'm', '2', '4', 0,
  /* 397 */ 'y', 'm', 'm', '2', '4', 0,
  /* 403 */ 'z', 'm', 'm', '2', '4', 0,
  /* 409 */ 'k', '4', 0,
  /* 412 */ 'x', 'm', 'm', '4', 0,
  /* 417 */ 'y', 'm', 'm', '4', 0,
  /* 422 */ 'z', 'm', 'm', '4', 0,
  /* 427 */ 'f', 'p', '4', 0,
  /* 431 */ 'c', 'r', '4', 0,
  /* 435 */ 'd', 'r', '4', 0,
  /* 439 */ 'x', 'm', 'm', '1', '5', 0,
  /* 445 */ 'y', 'm', 'm', '1', '5', 0,
  /* 451 */ 'z', 'm', 'm', '1', '5', 0,
  /* 457 */ 'c', 'r', '1', '5', 0,
  /* 462 */ 'x', 'm', 'm', '2', '5', 0,
  /* 468 */ 'y', 'm', 'm', '2', '5', 0,
  /* 474 */ 'z', 'm', 'm', '2', '5', 0,
  /* 480 */ 'k', '5', 0,
  /* 483 */ 'x', 'm', 'm', '5', 0,
  /* 488 */ 'y', 'm', 'm', '5', 0,
  /* 493 */ 'z', 'm', 'm', '5', 0,
  /* 498 */ 'f', 'p', '5', 0,
  /* 502 */ 'c', 'r', '5', 0,
  /* 506 */ 'd', 'r', '5', 0,
  /* 510 */ 'x', 'm', 'm', '1', '6', 0,
  /* 516 */ 'y', 'm', 'm', '1', '6', 0,
  /* 522 */ 'z', 'm', 'm', '1', '6', 0,
  /* 528 */ 'x', 'm', 'm', '2', '6', 0,
  /* 534 */ 'y', 'm', 'm', '2', '6', 0,
  /* 540 */ 'z', 'm', 'm', '2', '6', 0,
  /* 546 */ 'k', '6', 0,
  /* 549 */ 'x', 'm', 'm', '6', 0,
  /* 554 */ 'y', 'm', 'm', '6', 0,
  /* 559 */ 'z', 'm', 'm', '6', 0,
  /* 564 */ 'f', 'p', '6', 0,
  /* 568 */ 'c', 'r', '6', 0,
  /* 572 */ 'd', 'r', '6', 0,
  /* 576 */ 'x', 'm', 'm', '1', '7', 0,
  /* 582 */ 'y', 'm', 'm', '1', '7', 0,
  /* 588 */ 'z', 'm', 'm', '1', '7', 0,
  /* 594 */ 'x', 'm', 'm', '2', '7', 0,
  /* 600 */ 'y', 'm', 'm', '2', '7', 0,
  /* 606 */ 'z', 'm', 'm', '2', '7', 0,
  /* 612 */ 'k', '7', 0,
  /* 615 */ 'x', 'm', 'm', '7', 0,
  /* 620 */ 'y', 'm', 'm', '7', 0,
  /* 625 */ 'z', 'm', 'm', '7', 0,
  /* 630 */ 'f', 'p', '7', 0,
  /* 634 */ 'c', 'r', '7', 0,
  /* 638 */ 'd', 'r', '7', 0,
  /* 642 */ 'x', 'm', 'm', '1', '8', 0,
  /* 648 */ 'y', 'm', 'm', '1', '8', 0,
  /* 654 */ 'z', 'm', 'm', '1', '8', 0,
  /* 660 */ 'x', 'm', 'm', '2', '8', 0,
  /* 666 */ 'y', 'm', 'm', '2', '8', 0,
  /* 672 */ 'z', 'm', 'm', '2', '8', 0,
  /* 678 */ 'x', 'm', 'm', '8', 0,
  /* 683 */ 'y', 'm', 'm', '8', 0,
  /* 688 */ 'z', 'm', 'm', '8', 0,
  /* 693 */ 'c', 'r', '8', 0,
  /* 697 */ 'x', 'm', 'm', '1', '9', 0,
  /* 703 */ 'y', 'm', 'm', '1', '9', 0,
  /* 709 */ 'z', 'm', 'm', '1', '9', 0,
  /* 715 */ 'x', 'm', 'm', '2', '9', 0,
  /* 721 */ 'y', 'm', 'm', '2', '9', 0,
  /* 727 */ 'z', 'm', 'm', '2', '9', 0,
  /* 733 */ 'x', 'm', 'm', '9', 0,
  /* 738 */ 'y', 'm', 'm', '9', 0,
  /* 743 */ 'z', 'm', 'm', '9', 0,
  /* 748 */ 'c', 'r', '9', 0,
  /* 752 */ 'r', '1', '0', 'b', 0,
  /* 757 */ 'r', '1', '1', 'b', 0,
  /* 762 */ 'r', '1', '2', 'b', 0,
  /* 767 */ 'r', '1', '3', 'b', 0,
  /* 772 */ 'r', '1', '4', 'b', 0,
  /* 777 */ 'r', '1', '5', 'b', 0,
  /* 782 */ 'r', '8', 'b', 0,
  /* 786 */ 'r', '9', 'b', 0,
  /* 790 */ 'r', '1', '0', 'd', 0,
  /* 795 */ 'r', '1', '1', 'd', 0,
  /* 800 */ 'r', '1', '2', 'd', 0,
  /* 805 */ 'r', '1', '3', 'd', 0,
  /* 810 */ 'r', '1', '4', 'd', 0,
  /* 815 */ 'r', '1', '5', 'd', 0,
  /* 820 */ 'r', '8', 'd', 0,
  /* 824 */ 'r', '9', 'd', 0,
  /* 828 */ 'a', 'h', 0,
  /* 831 */ 'b', 'h', 0,
  /* 834 */ 'c', 'h', 0,
  /* 837 */ 'd', 'h', 0,
  /* 840 */ 'e', 'd', 'i', 0,
  /* 844 */ 'r', 'd', 'i', 0,
  /* 848 */ 'e', 's', 'i', 0,
  /* 852 */ 'r', 's', 'i', 0,
  /* 856 */ 'a', 'l', 0,
  /* 859 */ 'b', 'l', 0,
  /* 862 */ 'c', 'l', 0,
  /* 865 */ 'd', 'l', 0,
  /* 868 */ 'd', 'i', 'l', 0,
  /* 872 */ 's', 'i', 'l', 0,
  /* 876 */ 'b', 'p', 'l', 0,
  /* 880 */ 's', 'p', 'l', 0,
  /* 884 */ 'e', 'b', 'p', 0,
  /* 888 */ 'r', 'b', 'p', 0,
  /* 892 */ 'e', 'i', 'p', 0,
  /* 896 */ 'r', 'i', 'p', 0,
  /* 900 */ 'e', 's', 'p', 0,
  /* 904 */ 'r', 's', 'p', 0,
  /* 908 */ 'c', 's', 0,
  /* 911 */ 'd', 's', 0,
  /* 914 */ 'e', 's', 0,
  /* 917 */ 'f', 's', 0,
  /* 920 */ 'f', 'l', 'a', 'g', 's', 0,
  /* 926 */ 's', 's', 0,
  /* 929 */ 'r', '1', '0', 'w', 0,
  /* 934 */ 'r', '1', '1', 'w', 0,
  /* 939 */ 'r', '1', '2', 'w', 0,
  /* 944 */ 'r', '1', '3', 'w', 0,
  /* 949 */ 'r', '1', '4', 'w', 0,
  /* 954 */ 'r', '1', '5', 'w', 0,
  /* 959 */ 'r', '8', 'w', 0,
  /* 963 */ 'r', '9', 'w', 0,
  /* 967 */ 'f', 'p', 's', 'w', 0,
  /* 972 */ 'e', 'a', 'x', 0,
  /* 976 */ 'r', 'a', 'x', 0,
  /* 980 */ 'e', 'b', 'x', 0,
  /* 984 */ 'r', 'b', 'x', 0,
  /* 988 */ 'e', 'c', 'x', 0,
  /* 992 */ 'r', 'c', 'x', 0,
  /* 996 */ 'e', 'd', 'x', 0,
  /* 1000 */ 'r', 'd', 'x', 0,
  /* 1004 */ 'e', 'i', 'z', 0,
  /* 1008 */ 'r', 'i', 'z', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    828, 856, 973, 831, 859, 885, 876, 981, 834, 862, 908, 989, 837, 841, 
    868, 865, 911, 997, 972, 884, 980, 988, 840, 996, 920, 892, 1004, 914, 
    848, 900, 967, 917, 923, 893, 976, 888, 984, 992, 844, 1000, 896, 1008, 
    852, 904, 849, 872, 901, 880, 926, 129, 218, 289, 360, 431, 502, 568, 
    634, 693, 748, 66, 155, 244, 315, 386, 457, 133, 222, 293, 364, 435, 
    506, 572, 638, 125, 214, 285, 356, 427, 498, 564, 630, 107, 196, 267, 
    338, 409, 480, 546, 612, 111, 200, 271, 342, 413, 484, 550, 616, 694, 
    749, 67, 156, 245, 316, 387, 458, 0, 6, 12, 18, 24, 30, 36, 
    42, 110, 199, 270, 341, 412, 483, 549, 615, 678, 733, 48, 137, 226, 
    297, 368, 439, 510, 576, 642, 697, 71, 160, 249, 320, 391, 462, 528, 
    594, 660, 715, 89, 178, 115, 204, 275, 346, 417, 488, 554, 620, 683, 
    738, 54, 143, 232, 303, 374, 445, 516, 582, 648, 703, 77, 166, 255, 
    326, 397, 468, 534, 600, 666, 721, 95, 184, 120, 209, 280, 351, 422, 
    493, 559, 625, 688, 743, 60, 149, 238, 309, 380, 451, 522, 588, 654, 
    709, 83, 172, 261, 332, 403, 474, 540, 606, 672, 727, 101, 190, 782, 
    786, 752, 757, 762, 767, 772, 777, 820, 824, 790, 795, 800, 805, 810, 
    815, 959, 963, 929, 934, 939, 944, 949, 954, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case X86_AAD8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAD8i8 10)
      AsmString = "aad";
      break;
    }
    return NULL;
  case X86_AAM8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAM8i8 10)
      AsmString = "aam";
      break;
    }
    return NULL;
  case X86_XSTORE:
    if (MCInst_getNumOperands(MI) == 0) {
      // (XSTORE)
      AsmString = "xstorerng";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
