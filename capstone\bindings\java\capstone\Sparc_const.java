// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class Sparc_const {

	// Enums corresponding to Sparc condition codes, both icc's and fcc's.

	public static final int SPARC_CC_INVALID = 0;

	// Integer condition codes
	public static final int SPARC_CC_ICC_A = 8+256;
	public static final int SPARC_CC_ICC_N = 0+256;
	public static final int SPARC_CC_ICC_NE = 9+256;
	public static final int SPARC_CC_ICC_E = 1+256;
	public static final int SPARC_CC_ICC_G = 10+256;
	public static final int SPARC_CC_ICC_LE = 2+256;
	public static final int SPARC_CC_ICC_GE = 11+256;
	public static final int SPARC_CC_ICC_L = 3+256;
	public static final int SPARC_CC_ICC_GU = 12+256;
	public static final int SPARC_CC_ICC_LEU = 4+256;
	public static final int SPARC_CC_ICC_CC = 13+256;
	public static final int SPARC_CC_ICC_CS = 5+256;
	public static final int SPARC_CC_ICC_POS = 14+256;
	public static final int SPARC_CC_ICC_NEG = 6+256;
	public static final int SPARC_CC_ICC_VC = 15+256;
	public static final int SPARC_CC_ICC_VS = 7+256;

	// Floating condition codes
	public static final int SPARC_CC_FCC_A = 8+16+256;
	public static final int SPARC_CC_FCC_N = 0+16+256;
	public static final int SPARC_CC_FCC_U = 7+16+256;
	public static final int SPARC_CC_FCC_G = 6+16+256;
	public static final int SPARC_CC_FCC_UG = 5+16+256;
	public static final int SPARC_CC_FCC_L = 4+16+256;
	public static final int SPARC_CC_FCC_UL = 3+16+256;
	public static final int SPARC_CC_FCC_LG = 2+16+256;
	public static final int SPARC_CC_FCC_NE = 1+16+256;
	public static final int SPARC_CC_FCC_E = 9+16+256;
	public static final int SPARC_CC_FCC_UE = 10+16+256;
	public static final int SPARC_CC_FCC_GE = 11+16+256;
	public static final int SPARC_CC_FCC_UGE = 12+16+256;
	public static final int SPARC_CC_FCC_LE = 13+16+256;
	public static final int SPARC_CC_FCC_ULE = 14+16+256;
	public static final int SPARC_CC_FCC_O = 15+16+256;

	// Branch hint

	public static final int SPARC_HINT_INVALID = 0;
	public static final int SPARC_HINT_A = 1<<0;
	public static final int SPARC_HINT_PT = 1<<1;
	public static final int SPARC_HINT_PN = 1<<2;

	// Operand type for instruction's operands

	public static final int SPARC_OP_INVALID = 0;
	public static final int SPARC_OP_REG = 1;
	public static final int SPARC_OP_IMM = 2;
	public static final int SPARC_OP_MEM = 3;

	// SPARC registers

	public static final int SPARC_REG_INVALID = 0;
	public static final int SPARC_REG_F0 = 1;
	public static final int SPARC_REG_F1 = 2;
	public static final int SPARC_REG_F2 = 3;
	public static final int SPARC_REG_F3 = 4;
	public static final int SPARC_REG_F4 = 5;
	public static final int SPARC_REG_F5 = 6;
	public static final int SPARC_REG_F6 = 7;
	public static final int SPARC_REG_F7 = 8;
	public static final int SPARC_REG_F8 = 9;
	public static final int SPARC_REG_F9 = 10;
	public static final int SPARC_REG_F10 = 11;
	public static final int SPARC_REG_F11 = 12;
	public static final int SPARC_REG_F12 = 13;
	public static final int SPARC_REG_F13 = 14;
	public static final int SPARC_REG_F14 = 15;
	public static final int SPARC_REG_F15 = 16;
	public static final int SPARC_REG_F16 = 17;
	public static final int SPARC_REG_F17 = 18;
	public static final int SPARC_REG_F18 = 19;
	public static final int SPARC_REG_F19 = 20;
	public static final int SPARC_REG_F20 = 21;
	public static final int SPARC_REG_F21 = 22;
	public static final int SPARC_REG_F22 = 23;
	public static final int SPARC_REG_F23 = 24;
	public static final int SPARC_REG_F24 = 25;
	public static final int SPARC_REG_F25 = 26;
	public static final int SPARC_REG_F26 = 27;
	public static final int SPARC_REG_F27 = 28;
	public static final int SPARC_REG_F28 = 29;
	public static final int SPARC_REG_F29 = 30;
	public static final int SPARC_REG_F30 = 31;
	public static final int SPARC_REG_F31 = 32;
	public static final int SPARC_REG_F32 = 33;
	public static final int SPARC_REG_F34 = 34;
	public static final int SPARC_REG_F36 = 35;
	public static final int SPARC_REG_F38 = 36;
	public static final int SPARC_REG_F40 = 37;
	public static final int SPARC_REG_F42 = 38;
	public static final int SPARC_REG_F44 = 39;
	public static final int SPARC_REG_F46 = 40;
	public static final int SPARC_REG_F48 = 41;
	public static final int SPARC_REG_F50 = 42;
	public static final int SPARC_REG_F52 = 43;
	public static final int SPARC_REG_F54 = 44;
	public static final int SPARC_REG_F56 = 45;
	public static final int SPARC_REG_F58 = 46;
	public static final int SPARC_REG_F60 = 47;
	public static final int SPARC_REG_F62 = 48;
	public static final int SPARC_REG_FCC0 = 49;
	public static final int SPARC_REG_FCC1 = 50;
	public static final int SPARC_REG_FCC2 = 51;
	public static final int SPARC_REG_FCC3 = 52;
	public static final int SPARC_REG_FP = 53;
	public static final int SPARC_REG_G0 = 54;
	public static final int SPARC_REG_G1 = 55;
	public static final int SPARC_REG_G2 = 56;
	public static final int SPARC_REG_G3 = 57;
	public static final int SPARC_REG_G4 = 58;
	public static final int SPARC_REG_G5 = 59;
	public static final int SPARC_REG_G6 = 60;
	public static final int SPARC_REG_G7 = 61;
	public static final int SPARC_REG_I0 = 62;
	public static final int SPARC_REG_I1 = 63;
	public static final int SPARC_REG_I2 = 64;
	public static final int SPARC_REG_I3 = 65;
	public static final int SPARC_REG_I4 = 66;
	public static final int SPARC_REG_I5 = 67;
	public static final int SPARC_REG_I7 = 68;
	public static final int SPARC_REG_ICC = 69;
	public static final int SPARC_REG_L0 = 70;
	public static final int SPARC_REG_L1 = 71;
	public static final int SPARC_REG_L2 = 72;
	public static final int SPARC_REG_L3 = 73;
	public static final int SPARC_REG_L4 = 74;
	public static final int SPARC_REG_L5 = 75;
	public static final int SPARC_REG_L6 = 76;
	public static final int SPARC_REG_L7 = 77;
	public static final int SPARC_REG_O0 = 78;
	public static final int SPARC_REG_O1 = 79;
	public static final int SPARC_REG_O2 = 80;
	public static final int SPARC_REG_O3 = 81;
	public static final int SPARC_REG_O4 = 82;
	public static final int SPARC_REG_O5 = 83;
	public static final int SPARC_REG_O7 = 84;
	public static final int SPARC_REG_SP = 85;
	public static final int SPARC_REG_Y = 86;
	public static final int SPARC_REG_XCC = 87;
	public static final int SPARC_REG_ENDING = 88;
	public static final int SPARC_REG_O6 = SPARC_REG_SP;
	public static final int SPARC_REG_I6 = SPARC_REG_FP;

	// SPARC instruction

	public static final int SPARC_INS_INVALID = 0;
	public static final int SPARC_INS_ADDCC = 1;
	public static final int SPARC_INS_ADDX = 2;
	public static final int SPARC_INS_ADDXCC = 3;
	public static final int SPARC_INS_ADDXC = 4;
	public static final int SPARC_INS_ADDXCCC = 5;
	public static final int SPARC_INS_ADD = 6;
	public static final int SPARC_INS_ALIGNADDR = 7;
	public static final int SPARC_INS_ALIGNADDRL = 8;
	public static final int SPARC_INS_ANDCC = 9;
	public static final int SPARC_INS_ANDNCC = 10;
	public static final int SPARC_INS_ANDN = 11;
	public static final int SPARC_INS_AND = 12;
	public static final int SPARC_INS_ARRAY16 = 13;
	public static final int SPARC_INS_ARRAY32 = 14;
	public static final int SPARC_INS_ARRAY8 = 15;
	public static final int SPARC_INS_B = 16;
	public static final int SPARC_INS_JMP = 17;
	public static final int SPARC_INS_BMASK = 18;
	public static final int SPARC_INS_FB = 19;
	public static final int SPARC_INS_BRGEZ = 20;
	public static final int SPARC_INS_BRGZ = 21;
	public static final int SPARC_INS_BRLEZ = 22;
	public static final int SPARC_INS_BRLZ = 23;
	public static final int SPARC_INS_BRNZ = 24;
	public static final int SPARC_INS_BRZ = 25;
	public static final int SPARC_INS_BSHUFFLE = 26;
	public static final int SPARC_INS_CALL = 27;
	public static final int SPARC_INS_CASX = 28;
	public static final int SPARC_INS_CAS = 29;
	public static final int SPARC_INS_CMASK16 = 30;
	public static final int SPARC_INS_CMASK32 = 31;
	public static final int SPARC_INS_CMASK8 = 32;
	public static final int SPARC_INS_CMP = 33;
	public static final int SPARC_INS_EDGE16 = 34;
	public static final int SPARC_INS_EDGE16L = 35;
	public static final int SPARC_INS_EDGE16LN = 36;
	public static final int SPARC_INS_EDGE16N = 37;
	public static final int SPARC_INS_EDGE32 = 38;
	public static final int SPARC_INS_EDGE32L = 39;
	public static final int SPARC_INS_EDGE32LN = 40;
	public static final int SPARC_INS_EDGE32N = 41;
	public static final int SPARC_INS_EDGE8 = 42;
	public static final int SPARC_INS_EDGE8L = 43;
	public static final int SPARC_INS_EDGE8LN = 44;
	public static final int SPARC_INS_EDGE8N = 45;
	public static final int SPARC_INS_FABSD = 46;
	public static final int SPARC_INS_FABSQ = 47;
	public static final int SPARC_INS_FABSS = 48;
	public static final int SPARC_INS_FADDD = 49;
	public static final int SPARC_INS_FADDQ = 50;
	public static final int SPARC_INS_FADDS = 51;
	public static final int SPARC_INS_FALIGNDATA = 52;
	public static final int SPARC_INS_FAND = 53;
	public static final int SPARC_INS_FANDNOT1 = 54;
	public static final int SPARC_INS_FANDNOT1S = 55;
	public static final int SPARC_INS_FANDNOT2 = 56;
	public static final int SPARC_INS_FANDNOT2S = 57;
	public static final int SPARC_INS_FANDS = 58;
	public static final int SPARC_INS_FCHKSM16 = 59;
	public static final int SPARC_INS_FCMPD = 60;
	public static final int SPARC_INS_FCMPEQ16 = 61;
	public static final int SPARC_INS_FCMPEQ32 = 62;
	public static final int SPARC_INS_FCMPGT16 = 63;
	public static final int SPARC_INS_FCMPGT32 = 64;
	public static final int SPARC_INS_FCMPLE16 = 65;
	public static final int SPARC_INS_FCMPLE32 = 66;
	public static final int SPARC_INS_FCMPNE16 = 67;
	public static final int SPARC_INS_FCMPNE32 = 68;
	public static final int SPARC_INS_FCMPQ = 69;
	public static final int SPARC_INS_FCMPS = 70;
	public static final int SPARC_INS_FDIVD = 71;
	public static final int SPARC_INS_FDIVQ = 72;
	public static final int SPARC_INS_FDIVS = 73;
	public static final int SPARC_INS_FDMULQ = 74;
	public static final int SPARC_INS_FDTOI = 75;
	public static final int SPARC_INS_FDTOQ = 76;
	public static final int SPARC_INS_FDTOS = 77;
	public static final int SPARC_INS_FDTOX = 78;
	public static final int SPARC_INS_FEXPAND = 79;
	public static final int SPARC_INS_FHADDD = 80;
	public static final int SPARC_INS_FHADDS = 81;
	public static final int SPARC_INS_FHSUBD = 82;
	public static final int SPARC_INS_FHSUBS = 83;
	public static final int SPARC_INS_FITOD = 84;
	public static final int SPARC_INS_FITOQ = 85;
	public static final int SPARC_INS_FITOS = 86;
	public static final int SPARC_INS_FLCMPD = 87;
	public static final int SPARC_INS_FLCMPS = 88;
	public static final int SPARC_INS_FLUSHW = 89;
	public static final int SPARC_INS_FMEAN16 = 90;
	public static final int SPARC_INS_FMOVD = 91;
	public static final int SPARC_INS_FMOVQ = 92;
	public static final int SPARC_INS_FMOVRDGEZ = 93;
	public static final int SPARC_INS_FMOVRQGEZ = 94;
	public static final int SPARC_INS_FMOVRSGEZ = 95;
	public static final int SPARC_INS_FMOVRDGZ = 96;
	public static final int SPARC_INS_FMOVRQGZ = 97;
	public static final int SPARC_INS_FMOVRSGZ = 98;
	public static final int SPARC_INS_FMOVRDLEZ = 99;
	public static final int SPARC_INS_FMOVRQLEZ = 100;
	public static final int SPARC_INS_FMOVRSLEZ = 101;
	public static final int SPARC_INS_FMOVRDLZ = 102;
	public static final int SPARC_INS_FMOVRQLZ = 103;
	public static final int SPARC_INS_FMOVRSLZ = 104;
	public static final int SPARC_INS_FMOVRDNZ = 105;
	public static final int SPARC_INS_FMOVRQNZ = 106;
	public static final int SPARC_INS_FMOVRSNZ = 107;
	public static final int SPARC_INS_FMOVRDZ = 108;
	public static final int SPARC_INS_FMOVRQZ = 109;
	public static final int SPARC_INS_FMOVRSZ = 110;
	public static final int SPARC_INS_FMOVS = 111;
	public static final int SPARC_INS_FMUL8SUX16 = 112;
	public static final int SPARC_INS_FMUL8ULX16 = 113;
	public static final int SPARC_INS_FMUL8X16 = 114;
	public static final int SPARC_INS_FMUL8X16AL = 115;
	public static final int SPARC_INS_FMUL8X16AU = 116;
	public static final int SPARC_INS_FMULD = 117;
	public static final int SPARC_INS_FMULD8SUX16 = 118;
	public static final int SPARC_INS_FMULD8ULX16 = 119;
	public static final int SPARC_INS_FMULQ = 120;
	public static final int SPARC_INS_FMULS = 121;
	public static final int SPARC_INS_FNADDD = 122;
	public static final int SPARC_INS_FNADDS = 123;
	public static final int SPARC_INS_FNAND = 124;
	public static final int SPARC_INS_FNANDS = 125;
	public static final int SPARC_INS_FNEGD = 126;
	public static final int SPARC_INS_FNEGQ = 127;
	public static final int SPARC_INS_FNEGS = 128;
	public static final int SPARC_INS_FNHADDD = 129;
	public static final int SPARC_INS_FNHADDS = 130;
	public static final int SPARC_INS_FNOR = 131;
	public static final int SPARC_INS_FNORS = 132;
	public static final int SPARC_INS_FNOT1 = 133;
	public static final int SPARC_INS_FNOT1S = 134;
	public static final int SPARC_INS_FNOT2 = 135;
	public static final int SPARC_INS_FNOT2S = 136;
	public static final int SPARC_INS_FONE = 137;
	public static final int SPARC_INS_FONES = 138;
	public static final int SPARC_INS_FOR = 139;
	public static final int SPARC_INS_FORNOT1 = 140;
	public static final int SPARC_INS_FORNOT1S = 141;
	public static final int SPARC_INS_FORNOT2 = 142;
	public static final int SPARC_INS_FORNOT2S = 143;
	public static final int SPARC_INS_FORS = 144;
	public static final int SPARC_INS_FPACK16 = 145;
	public static final int SPARC_INS_FPACK32 = 146;
	public static final int SPARC_INS_FPACKFIX = 147;
	public static final int SPARC_INS_FPADD16 = 148;
	public static final int SPARC_INS_FPADD16S = 149;
	public static final int SPARC_INS_FPADD32 = 150;
	public static final int SPARC_INS_FPADD32S = 151;
	public static final int SPARC_INS_FPADD64 = 152;
	public static final int SPARC_INS_FPMERGE = 153;
	public static final int SPARC_INS_FPSUB16 = 154;
	public static final int SPARC_INS_FPSUB16S = 155;
	public static final int SPARC_INS_FPSUB32 = 156;
	public static final int SPARC_INS_FPSUB32S = 157;
	public static final int SPARC_INS_FQTOD = 158;
	public static final int SPARC_INS_FQTOI = 159;
	public static final int SPARC_INS_FQTOS = 160;
	public static final int SPARC_INS_FQTOX = 161;
	public static final int SPARC_INS_FSLAS16 = 162;
	public static final int SPARC_INS_FSLAS32 = 163;
	public static final int SPARC_INS_FSLL16 = 164;
	public static final int SPARC_INS_FSLL32 = 165;
	public static final int SPARC_INS_FSMULD = 166;
	public static final int SPARC_INS_FSQRTD = 167;
	public static final int SPARC_INS_FSQRTQ = 168;
	public static final int SPARC_INS_FSQRTS = 169;
	public static final int SPARC_INS_FSRA16 = 170;
	public static final int SPARC_INS_FSRA32 = 171;
	public static final int SPARC_INS_FSRC1 = 172;
	public static final int SPARC_INS_FSRC1S = 173;
	public static final int SPARC_INS_FSRC2 = 174;
	public static final int SPARC_INS_FSRC2S = 175;
	public static final int SPARC_INS_FSRL16 = 176;
	public static final int SPARC_INS_FSRL32 = 177;
	public static final int SPARC_INS_FSTOD = 178;
	public static final int SPARC_INS_FSTOI = 179;
	public static final int SPARC_INS_FSTOQ = 180;
	public static final int SPARC_INS_FSTOX = 181;
	public static final int SPARC_INS_FSUBD = 182;
	public static final int SPARC_INS_FSUBQ = 183;
	public static final int SPARC_INS_FSUBS = 184;
	public static final int SPARC_INS_FXNOR = 185;
	public static final int SPARC_INS_FXNORS = 186;
	public static final int SPARC_INS_FXOR = 187;
	public static final int SPARC_INS_FXORS = 188;
	public static final int SPARC_INS_FXTOD = 189;
	public static final int SPARC_INS_FXTOQ = 190;
	public static final int SPARC_INS_FXTOS = 191;
	public static final int SPARC_INS_FZERO = 192;
	public static final int SPARC_INS_FZEROS = 193;
	public static final int SPARC_INS_JMPL = 194;
	public static final int SPARC_INS_LDD = 195;
	public static final int SPARC_INS_LD = 196;
	public static final int SPARC_INS_LDQ = 197;
	public static final int SPARC_INS_LDSB = 198;
	public static final int SPARC_INS_LDSH = 199;
	public static final int SPARC_INS_LDSW = 200;
	public static final int SPARC_INS_LDUB = 201;
	public static final int SPARC_INS_LDUH = 202;
	public static final int SPARC_INS_LDX = 203;
	public static final int SPARC_INS_LZCNT = 204;
	public static final int SPARC_INS_MEMBAR = 205;
	public static final int SPARC_INS_MOVDTOX = 206;
	public static final int SPARC_INS_MOV = 207;
	public static final int SPARC_INS_MOVRGEZ = 208;
	public static final int SPARC_INS_MOVRGZ = 209;
	public static final int SPARC_INS_MOVRLEZ = 210;
	public static final int SPARC_INS_MOVRLZ = 211;
	public static final int SPARC_INS_MOVRNZ = 212;
	public static final int SPARC_INS_MOVRZ = 213;
	public static final int SPARC_INS_MOVSTOSW = 214;
	public static final int SPARC_INS_MOVSTOUW = 215;
	public static final int SPARC_INS_MULX = 216;
	public static final int SPARC_INS_NOP = 217;
	public static final int SPARC_INS_ORCC = 218;
	public static final int SPARC_INS_ORNCC = 219;
	public static final int SPARC_INS_ORN = 220;
	public static final int SPARC_INS_OR = 221;
	public static final int SPARC_INS_PDIST = 222;
	public static final int SPARC_INS_PDISTN = 223;
	public static final int SPARC_INS_POPC = 224;
	public static final int SPARC_INS_RD = 225;
	public static final int SPARC_INS_RESTORE = 226;
	public static final int SPARC_INS_RETT = 227;
	public static final int SPARC_INS_SAVE = 228;
	public static final int SPARC_INS_SDIVCC = 229;
	public static final int SPARC_INS_SDIVX = 230;
	public static final int SPARC_INS_SDIV = 231;
	public static final int SPARC_INS_SETHI = 232;
	public static final int SPARC_INS_SHUTDOWN = 233;
	public static final int SPARC_INS_SIAM = 234;
	public static final int SPARC_INS_SLLX = 235;
	public static final int SPARC_INS_SLL = 236;
	public static final int SPARC_INS_SMULCC = 237;
	public static final int SPARC_INS_SMUL = 238;
	public static final int SPARC_INS_SRAX = 239;
	public static final int SPARC_INS_SRA = 240;
	public static final int SPARC_INS_SRLX = 241;
	public static final int SPARC_INS_SRL = 242;
	public static final int SPARC_INS_STBAR = 243;
	public static final int SPARC_INS_STB = 244;
	public static final int SPARC_INS_STD = 245;
	public static final int SPARC_INS_ST = 246;
	public static final int SPARC_INS_STH = 247;
	public static final int SPARC_INS_STQ = 248;
	public static final int SPARC_INS_STX = 249;
	public static final int SPARC_INS_SUBCC = 250;
	public static final int SPARC_INS_SUBX = 251;
	public static final int SPARC_INS_SUBXCC = 252;
	public static final int SPARC_INS_SUB = 253;
	public static final int SPARC_INS_SWAP = 254;
	public static final int SPARC_INS_TADDCCTV = 255;
	public static final int SPARC_INS_TADDCC = 256;
	public static final int SPARC_INS_T = 257;
	public static final int SPARC_INS_TSUBCCTV = 258;
	public static final int SPARC_INS_TSUBCC = 259;
	public static final int SPARC_INS_UDIVCC = 260;
	public static final int SPARC_INS_UDIVX = 261;
	public static final int SPARC_INS_UDIV = 262;
	public static final int SPARC_INS_UMULCC = 263;
	public static final int SPARC_INS_UMULXHI = 264;
	public static final int SPARC_INS_UMUL = 265;
	public static final int SPARC_INS_UNIMP = 266;
	public static final int SPARC_INS_FCMPED = 267;
	public static final int SPARC_INS_FCMPEQ = 268;
	public static final int SPARC_INS_FCMPES = 269;
	public static final int SPARC_INS_WR = 270;
	public static final int SPARC_INS_XMULX = 271;
	public static final int SPARC_INS_XMULXHI = 272;
	public static final int SPARC_INS_XNORCC = 273;
	public static final int SPARC_INS_XNOR = 274;
	public static final int SPARC_INS_XORCC = 275;
	public static final int SPARC_INS_XOR = 276;
	public static final int SPARC_INS_RET = 277;
	public static final int SPARC_INS_RETL = 278;
	public static final int SPARC_INS_ENDING = 279;

	// Group of SPARC instructions

	public static final int SPARC_GRP_INVALID = 0;

	// Generic groups
	public static final int SPARC_GRP_JUMP = 1;

	// Architecture-specific groups
	public static final int SPARC_GRP_HARDQUAD = 128;
	public static final int SPARC_GRP_V9 = 129;
	public static final int SPARC_GRP_VIS = 130;
	public static final int SPARC_GRP_VIS2 = 131;
	public static final int SPARC_GRP_VIS3 = 132;
	public static final int SPARC_GRP_32BIT = 133;
	public static final int SPARC_GRP_64BIT = 134;
	public static final int SPARC_GRP_ENDING = 135;
}