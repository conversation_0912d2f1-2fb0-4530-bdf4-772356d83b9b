/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    4533U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    4526U,	// BUNDLE
    4589U,	// LIFETIME_START
    4513U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    4604U,	// AAA
    8459U,	// AAD8i8
    9418U,	// AAM8i8
    5175U,	// AAS
    4217U,	// ACQUIRE_MOV16rm
    4217U,	// ACQUIRE_MOV32rm
    4217U,	// ACQUIRE_MOV64rm
    4217U,	// ACQUIRE_MOV8rm
    534694U,	// ADC16i16
    1067174U,	// ADC16mi
    1067174U,	// ADC16mi8
    1067174U,	// ADC16mr
    1599654U,	// ADC16ri
    1599654U,	// ADC16ri8
    1607846U,	// ADC16rm
    1599654U,	// ADC16rr
    2123942U,	// ADC16rr_REV
    2630119U,	// ADC32i32
    3162599U,	// ADC32mi
    3162599U,	// ADC32mi8
    3162599U,	// ADC32mr
    1597927U,	// ADC32ri
    1597927U,	// ADC32ri8
    1614311U,	// ADC32rm
    1597927U,	// ADC32rr
    2122215U,	// ADC32rr_REV
    3679572U,	// ADC64i32
    4212052U,	// ADC64mi32
    4212052U,	// ADC64mi8
    4212052U,	// ADC64mr
    1598804U,	// ADC64ri32
    1598804U,	// ADC64ri8
    1623380U,	// ADC64rm
    1598804U,	// ADC64rr
    2123092U,	// ADC64rr_REV
    4726830U,	// ADC8i8
    5259310U,	// ADC8mi
    5259310U,	// ADC8mr
    1597486U,	// ADC8ri
    57390U,	// ADC8rm
    1597486U,	// ADC8rr
    2121774U,	// ADC8rr_REV
    2139289U,	// ADCX32rm
    2122905U,	// ADCX32rr
    2148322U,	// ADCX64rm
    2123746U,	// ADCX64rr
    534719U,	// ADD16i16
    1067199U,	// ADD16mi
    1067199U,	// ADD16mi8
    1067199U,	// ADD16mr
    1599679U,	// ADD16ri
    1599679U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    1607871U,	// ADD16rm
    1599679U,	// ADD16rr
    0U,	// ADD16rr_DB
    2123967U,	// ADD16rr_REV
    2630153U,	// ADD32i32
    3162633U,	// ADD32mi
    3162633U,	// ADD32mi8
    3162633U,	// ADD32mr
    1597961U,	// ADD32ri
    1597961U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    1614345U,	// ADD32rm
    1597961U,	// ADD32rr
    0U,	// ADD32rr_DB
    2122249U,	// ADD32rr_REV
    3679606U,	// ADD64i32
    4212086U,	// ADD64mi32
    4212086U,	// ADD64mi8
    4212086U,	// ADD64mr
    1598838U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    1598838U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    1623414U,	// ADD64rm
    1598838U,	// ADD64rr
    0U,	// ADD64rr_DB
    2123126U,	// ADD64rr_REV
    4726849U,	// ADD8i8
    5259329U,	// ADD8mi
    5259329U,	// ADD8mr
    1597505U,	// ADD8ri
    1597505U,	// ADD8ri8
    57409U,	// ADD8rm
    1597505U,	// ADD8rr
    2121793U,	// ADD8rr_REV
    4543U,	// ADJCALLSTACKDOWN32
    4543U,	// ADJCALLSTACKDOWN64
    4561U,	// ADJCALLSTACKUP32
    4561U,	// ADJCALLSTACKUP64
    66734U,	// ADOX32rm
    22619310U,	// ADOX32rr
    83959U,	// ADOX64rm
    22620151U,	// ADOX64rr
    534744U,	// AND16i16
    1067224U,	// AND16mi
    1067224U,	// AND16mi8
    1067224U,	// AND16mr
    1599704U,	// AND16ri
    1599704U,	// AND16ri8
    1607896U,	// AND16rm
    1599704U,	// AND16rr
    2123992U,	// AND16rr_REV
    2630178U,	// AND32i32
    3162658U,	// AND32mi
    3162658U,	// AND32mi8
    3162658U,	// AND32mr
    1597986U,	// AND32ri
    1597986U,	// AND32ri8
    1614370U,	// AND32rm
    1597986U,	// AND32rr
    2122274U,	// AND32rr_REV
    3679631U,	// AND64i32
    4212111U,	// AND64mi32
    4212111U,	// AND64mi8
    4212111U,	// AND64mr
    1598863U,	// AND64ri32
    1598863U,	// AND64ri8
    1623439U,	// AND64rm
    1598863U,	// AND64rr
    2123151U,	// AND64rr_REV
    4726855U,	// AND8i8
    5259335U,	// AND8mi
    5259335U,	// AND8mr
    1597511U,	// AND8ri
    1597511U,	// AND8ri8
    57415U,	// AND8rm
    1597511U,	// AND8rr
    2121799U,	// AND8rr_REV
    35169057U,	// ANDN32rm
    35152673U,	// ANDN32rr
    35178119U,	// ANDN64rm
    35153543U,	// ANDN64rr
    1065839U,	// ARPL16mr
    22618991U,	// ARPL16rr
    6382522U,	// BEXTR32rm
    35152826U,	// BEXTR32rr
    6907665U,	// BEXTR64rm
    35153681U,	// BEXTR64rr
    6383665U,	// BEXTRI32mi
    35153969U,	// BEXTRI32ri
    6907953U,	// BEXTRI64mi
    35153969U,	// BEXTRI64ri
    66284U,	// BLCFILL32rm
    22618860U,	// BLCFILL32rr
    82668U,	// BLCFILL64rm
    22618860U,	// BLCFILL64rr
    65952U,	// BLCI32rm
    22618528U,	// BLCI32rr
    82336U,	// BLCI64rm
    22618528U,	// BLCI64rr
    65781U,	// BLCIC32rm
    22618357U,	// BLCIC32rr
    82165U,	// BLCIC64rm
    22618357U,	// BLCIC64rr
    65958U,	// BLCMSK32rm
    22618534U,	// BLCMSK32rr
    82342U,	// BLCMSK64rm
    22618534U,	// BLCMSK64rr
    67640U,	// BLCS32rm
    22620216U,	// BLCS32rr
    84024U,	// BLCS64rm
    22620216U,	// BLCS64rr
    66293U,	// BLSFILL32rm
    22618869U,	// BLSFILL32rr
    82677U,	// BLSFILL64rm
    22618869U,	// BLSFILL64rr
    66246U,	// BLSI32rm
    22618822U,	// BLSI32rr
    83515U,	// BLSI64rm
    22619707U,	// BLSI64rr
    65788U,	// BLSIC32rm
    22618364U,	// BLSIC32rr
    82172U,	// BLSIC64rm
    22618364U,	// BLSIC64rr
    66257U,	// BLSMSK32rm
    22618833U,	// BLSMSK32rr
    83522U,	// BLSMSK64rm
    22619714U,	// BLSMSK64rr
    66471U,	// BLSR32rm
    22619047U,	// BLSR32rr
    83710U,	// BLSR64rm
    22619902U,	// BLSR64rr
    65835U,	// BOUNDS16rm
    82219U,	// BOUNDS32rm
    100634U,	// BSF16rm
    22620442U,	// BSF16rr
    66202U,	// BSF32rm
    22618778U,	// BSF32rr
    83471U,	// BSF64rm
    22619663U,	// BSF64rr
    100830U,	// BSR16rm
    22620638U,	// BSR16rr
    66465U,	// BSR32rm
    22619041U,	// BSR32rr
    83704U,	// BSR64rm
    22619896U,	// BSR64rr
    9022U,	// BSWAP32r
    9887U,	// BSWAP64r
    1067600U,	// BT16mi8
    1067600U,	// BT16mr
    22620752U,	// BT16ri8
    22620752U,	// BT16rr
    3163164U,	// BT32mi8
    3163164U,	// BT32mr
    22619164U,	// BT32ri8
    22619164U,	// BT32rr
    4212576U,	// BT64mi8
    4212576U,	// BT64mr
    22620000U,	// BT64ri8
    22620000U,	// BT64rr
    1067192U,	// BTC16mi8
    1067192U,	// BTC16mr
    22620344U,	// BTC16ri8
    22620344U,	// BTC16rr
    3162617U,	// BTC32mi8
    3162617U,	// BTC32mr
    22618617U,	// BTC32ri8
    22618617U,	// BTC32rr
    4212070U,	// BTC64mi8
    4212070U,	// BTC64mr
    22619494U,	// BTC64ri8
    22619494U,	// BTC64rr
    1067492U,	// BTR16mi8
    1067492U,	// BTR16mr
    22620644U,	// BTR16ri8
    22620644U,	// BTR16rr
    3163054U,	// BTR32mi8
    3163054U,	// BTR32mr
    22619054U,	// BTR32ri8
    22619054U,	// BTR32rr
    4212485U,	// BTR64mi8
    4212485U,	// BTR64mr
    22619909U,	// BTR64ri8
    22619909U,	// BTR64rr
    1067579U,	// BTS16mi8
    1067579U,	// BTS16mr
    22620731U,	// BTS16ri8
    22620731U,	// BTS16rr
    3163143U,	// BTS32mi8
    3163143U,	// BTS32mr
    22619143U,	// BTS32ri8
    22619143U,	// BTS32rr
    4212562U,	// BTS64mi8
    4212562U,	// BTS64mr
    22619986U,	// BTS64ri8
    22619986U,	// BTS64rr
    6382271U,	// BZHI32rm
    35152575U,	// BZHI32rr
    6907444U,	// BZHI64rm
    35153460U,	// BZHI64rr
    110790U,	// CALL16m
    12486U,	// CALL16r
    118948U,	// CALL32m
    12452U,	// CALL32r
    127157U,	// CALL64m
    132701U,	// CALL64pcrel32
    12469U,	// CALL64r
    133458U,	// CALLpcrel16
    131839U,	// CALLpcrel32
    5552U,	// CBW
    4746U,	// CDQ
    5139U,	// CDQE
    4673U,	// CLAC
    4705U,	// CLC
    4742U,	// CLD
    4852U,	// CLGI
    4862U,	// CLI
    5483U,	// CLTS
    4709U,	// CMC
    2132090U,	// CMOVA16rm
    2123898U,	// CMOVA16rr
    2138555U,	// CMOVA32rm
    2122171U,	// CMOVA32rr
    2147624U,	// CMOVA64rm
    2123048U,	// CMOVA64rr
    2132197U,	// CMOVAE16rm
    2124005U,	// CMOVAE16rr
    2138671U,	// CMOVAE32rm
    2122287U,	// CMOVAE32rr
    2147740U,	// CMOVAE64rm
    2123164U,	// CMOVAE64rr
    2132118U,	// CMOVB16rm
    2123926U,	// CMOVB16rr
    2138583U,	// CMOVB32rm
    2122199U,	// CMOVB32rr
    2147652U,	// CMOVB64rm
    2123076U,	// CMOVB64rr
    2132206U,	// CMOVBE16rm
    2124014U,	// CMOVBE16rr
    2138680U,	// CMOVBE32rm
    2122296U,	// CMOVBE32rr
    2147749U,	// CMOVBE64rm
    2123173U,	// CMOVBE64rr
    2132242U,	// CMOVE16rm
    2124050U,	// CMOVE16rr
    2138770U,	// CMOVE32rm
    2122386U,	// CMOVE32rr
    2147847U,	// CMOVE64rm
    2123271U,	// CMOVE64rr
    2132272U,	// CMOVG16rm
    2124080U,	// CMOVG16rr
    2138800U,	// CMOVG32rm
    2122416U,	// CMOVG32rr
    2147877U,	// CMOVG64rm
    2123301U,	// CMOVG64rr
    2132215U,	// CMOVGE16rm
    2124023U,	// CMOVGE16rr
    2138689U,	// CMOVGE32rm
    2122305U,	// CMOVGE32rr
    2147758U,	// CMOVGE64rm
    2123182U,	// CMOVGE64rr
    2132332U,	// CMOVL16rm
    2124140U,	// CMOVL16rr
    2138905U,	// CMOVL32rm
    2122521U,	// CMOVL32rr
    2147967U,	// CMOVL64rm
    2123391U,	// CMOVL64rr
    2132224U,	// CMOVLE16rm
    2124032U,	// CMOVLE16rr
    2138698U,	// CMOVLE32rm
    2122314U,	// CMOVLE32rr
    2147767U,	// CMOVLE64rm
    2123191U,	// CMOVLE64rr
    2132233U,	// CMOVNE16rm
    2124041U,	// CMOVNE16rr
    2138707U,	// CMOVNE32rm
    2122323U,	// CMOVNE32rr
    2147776U,	// CMOVNE64rm
    2123200U,	// CMOVNE64rr
    2132345U,	// CMOVNO16rm
    2124153U,	// CMOVNO16rr
    2138925U,	// CMOVNO32rm
    2122541U,	// CMOVNO32rr
    2147982U,	// CMOVNO64rm
    2123406U,	// CMOVNO64rr
    2132375U,	// CMOVNP16rm
    2124183U,	// CMOVNP16rr
    2138970U,	// CMOVNP32rm
    2122586U,	// CMOVNP32rr
    2148020U,	// CMOVNP64rm
    2123444U,	// CMOVNP64rr
    2132517U,	// CMOVNS16rm
    2124325U,	// CMOVNS16rr
    2139121U,	// CMOVNS32rm
    2122737U,	// CMOVNS32rr
    2148156U,	// CMOVNS64rm
    2123580U,	// CMOVNS64rr
    2132354U,	// CMOVO16rm
    2124162U,	// CMOVO16rr
    2138934U,	// CMOVO32rm
    2122550U,	// CMOVO32rr
    2147991U,	// CMOVO64rm
    2123415U,	// CMOVO64rr
    2132396U,	// CMOVP16rm
    2124204U,	// CMOVP16rr
    2138997U,	// CMOVP32rm
    2122613U,	// CMOVP32rr
    2148035U,	// CMOVP64rm
    2123459U,	// CMOVP64rr
    2132552U,	// CMOVS16rm
    2124360U,	// CMOVS16rr
    2139156U,	// CMOVS32rm
    2122772U,	// CMOVS32rr
    2148184U,	// CMOVS64rm
    2123608U,	// CMOVS64rr
    4021U,	// CMOV_FR32
    4180U,	// CMOV_FR64
    3900U,	// CMOV_GR16
    3880U,	// CMOV_GR32
    4199U,	// CMOV_GR8
    4001U,	// CMOV_RFP32
    4160U,	// CMOV_RFP64
    3920U,	// CMOV_RFP80
    3960U,	// CMOV_V16F32
    4040U,	// CMOV_V2F64
    4100U,	// CMOV_V2I64
    3940U,	// CMOV_V4F32
    4060U,	// CMOV_V4F64
    4120U,	// CMOV_V4I64
    3981U,	// CMOV_V8F32
    4080U,	// CMOV_V8F64
    4140U,	// CMOV_V8I64
    534922U,	// CMP16i16
    1067402U,	// CMP16mi
    1067402U,	// CMP16mi8
    1067402U,	// CMP16mr
    22620554U,	// CMP16ri
    22620554U,	// CMP16ri8
    100746U,	// CMP16rm
    22620554U,	// CMP16rr
    22620554U,	// CMP16rr_REV
    2630477U,	// CMP32i32
    3162957U,	// CMP32mi
    3162957U,	// CMP32mi8
    3162957U,	// CMP32mr
    22618957U,	// CMP32ri
    22618957U,	// CMP32ri8
    66381U,	// CMP32rm
    22618957U,	// CMP32rr
    22618957U,	// CMP32rr_REV
    3679918U,	// CMP64i32
    4212398U,	// CMP64mi32
    4212398U,	// CMP64mi8
    4212398U,	// CMP64mr
    22619822U,	// CMP64ri32
    22619822U,	// CMP64ri8
    83630U,	// CMP64rm
    22619822U,	// CMP64rr
    22619822U,	// CMP64rr_REV
    4726917U,	// CMP8i8
    5259397U,	// CMP8mi
    5259397U,	// CMP8mr
    22618245U,	// CMP8ri
    139397U,	// CMP8rm
    22618245U,	// CMP8rr
    22618245U,	// CMP8rr_REV
    56246464U,	// CMPSB
    73032698U,	// CMPSL
    89818949U,	// CMPSQ
    106605102U,	// CMPSW
    180235U,	// CMPXCHG16B
    1067302U,	// CMPXCHG16rm
    22620454U,	// CMPXCHG16rr
    3162790U,	// CMPXCHG32rm
    22618790U,	// CMPXCHG32rr
    4212251U,	// CMPXCHG64rm
    22619675U,	// CMPXCHG64rr
    122903U,	// CMPXCHG8B
    5259347U,	// CMPXCHG8rm
    22618195U,	// CMPXCHG8rr
    4736U,	// CPUID32
    4736U,	// CPUID64
    5057U,	// CQO
    4755U,	// CWD
    5001U,	// CWDE
    4608U,	// DAA
    5179U,	// DAS
    4498U,	// DATA16_PREFIX
    108716U,	// DEC16m
    10412U,	// DEC16r
    10412U,	// DEC32_16r
    8685U,	// DEC32_32r
    115181U,	// DEC32m
    8685U,	// DEC32r
    108716U,	// DEC64_16m
    10412U,	// DEC64_16r
    115181U,	// DEC64_32m
    8685U,	// DEC64_32r
    124250U,	// DEC64m
    9562U,	// DEC64r
    188468U,	// DEC8m
    8244U,	// DEC8r
    109220U,	// DIV16m
    10916U,	// DIV16r
    115830U,	// DIV32m
    9334U,	// DIV32r
    124863U,	// DIV64m
    10175U,	// DIV64r
    188649U,	// DIV8m
    8425U,	// DIV8r
    11935U,	// EH_RETURN
    11935U,	// EH_RETURN64
    4321U,	// EH_SjLj_LongJmp32
    4411U,	// EH_SjLj_LongJmp64
    4340U,	// EH_SjLj_SetJmp32
    4430U,	// EH_SjLj_SetJmp64
    132370U,	// EH_SjLj_Setup
    123217948U,	// ENTER
    7416145U,	// FARCALL16i
    200901U,	// FARCALL16m
    7414526U,	// FARCALL32i
    200867U,	// FARCALL32m
    200884U,	// FARCALL64
    7416208U,	// FARJMP16i
    200910U,	// FARJMP16m
    7414611U,	// FARJMP32i
    200876U,	// FARJMP32m
    200893U,	// FARJMP64
    5014U,	// FSETPM
    4693U,	// GETSEC
    5488U,	// HLT
    109219U,	// IDIV16m
    10915U,	// IDIV16r
    115829U,	// IDIV32m
    9333U,	// IDIV32r
    124862U,	// IDIV64m
    10174U,	// IDIV64r
    188648U,	// IDIV8m
    8424U,	// IDIV8r
    108901U,	// IMUL16m
    10597U,	// IMUL16r
    2132325U,	// IMUL16rm
    7956837U,	// IMUL16rmi
    7956837U,	// IMUL16rmi8
    2124133U,	// IMUL16rr
    35154277U,	// IMUL16rri
    35154277U,	// IMUL16rri8
    115474U,	// IMUL32m
    8978U,	// IMUL32r
    2138898U,	// IMUL32rm
    6382354U,	// IMUL32rmi
    6382354U,	// IMUL32rmi8
    2122514U,	// IMUL32rr
    35152658U,	// IMUL32rri
    35152658U,	// IMUL32rri8
    124536U,	// IMUL64m
    9848U,	// IMUL64r
    2147960U,	// IMUL64rm
    6907512U,	// IMUL64rmi32
    6907512U,	// IMUL64rmi8
    2123384U,	// IMUL64rr
    35153528U,	// IMUL64rri32
    35153528U,	// IMUL64rri8
    188537U,	// IMUL8m
    8313U,	// IMUL8r
    534900U,	// IN16ri
    5569U,	// IN16rr
    2630440U,	// IN32ri
    5648U,	// IN32rr
    4726912U,	// IN8ri
    4890U,	// IN8rr
    108722U,	// INC16m
    10418U,	// INC16r
    10418U,	// INC32_16r
    8691U,	// INC32_32r
    115187U,	// INC32m
    8691U,	// INC32r
    108722U,	// INC64_16m
    10418U,	// INC64_16r
    115187U,	// INC64_32m
    8691U,	// INC64_32r
    124256U,	// INC64m
    9568U,	// INC64r
    188474U,	// INC8m
    8250U,	// INC8r
    151146U,	// INSB
    159349U,	// INSL
    175744U,	// INSW
    10324U,	// INT
    4316U,	// INT1
    4406U,	// INT3
    5052U,	// INTO
    4762U,	// INVD
    206937U,	// INVEPT32
    206937U,	// INVEPT64
    188818U,	// INVLPG
    5629U,	// INVLPGA32
    5697U,	// INVLPGA64
    205072U,	// INVPCID32
    205072U,	// INVPCID64
    205081U,	// INVVPID32
    205081U,	// INVVPID64
    5557U,	// IRET16
    4972U,	// IRET32
    5110U,	// IRET64
    4577U,	// Int_MemBarrier
    131378U,	// JAE_1
    131378U,	// JAE_2
    131378U,	// JAE_4
    131073U,	// JA_1
    131073U,	// JA_2
    131073U,	// JA_4
    131390U,	// JBE_1
    131390U,	// JBE_2
    131390U,	// JBE_4
    131165U,	// JB_1
    131165U,	// JB_2
    131165U,	// JB_4
    133829U,	// JCXZ
    133822U,	// JECXZ_32
    133822U,	// JECXZ_64
    131414U,	// JE_1
    131414U,	// JE_2
    131414U,	// JE_4
    131402U,	// JGE_1
    131402U,	// JGE_2
    131402U,	// JGE_4
    131470U,	// JG_1
    131470U,	// JG_2
    131470U,	// JG_4
    131418U,	// JLE_1
    131418U,	// JLE_2
    131418U,	// JLE_4
    131789U,	// JL_1
    131789U,	// JL_2
    131789U,	// JL_4
    110799U,	// JMP16m
    12495U,	// JMP16r
    118957U,	// JMP32m
    12461U,	// JMP32r
    127166U,	// JMP64m
    12478U,	// JMP64r
    132336U,	// JMP_1
    132336U,	// JMP_2
    132336U,	// JMP_4
    131430U,	// JNE_1
    131430U,	// JNE_2
    131430U,	// JNE_4
    132314U,	// JNO_1
    132314U,	// JNO_2
    132314U,	// JNO_4
    132341U,	// JNP_1
    132341U,	// JNP_2
    132341U,	// JNP_4
    133186U,	// JNS_1
    133186U,	// JNS_2
    133186U,	// JNS_4
    132310U,	// JO_1
    132310U,	// JO_2
    132310U,	// JO_4
    132332U,	// JP_1
    132332U,	// JP_2
    132332U,	// JP_4
    133835U,	// JRCXZ
    133182U,	// JS_1
    133182U,	// JS_2
    133182U,	// JS_4
    4833U,	// LAHF
    100788U,	// LAR16rm
    22620596U,	// LAR16rr
    99197U,	// LAR32rm
    22619005U,	// LAR32rr
    100043U,	// LAR64rm
    22619851U,	// LAR64rr
    1067302U,	// LCMPXCHG16
    180235U,	// LCMPXCHG16B
    3162790U,	// LCMPXCHG32
    4212251U,	// LCMPXCHG64
    5259347U,	// LCMPXCHG8
    122903U,	// LCMPXCHG8B
    215558U,	// LDS16rm
    213970U,	// LDS32rm
    100468U,	// LEA16r
    65973U,	// LEA32r
    65973U,	// LEA64_32r
    83234U,	// LEA64r
    4820U,	// LEAVE
    4820U,	// LEAVE64
    215571U,	// LES16rm
    213983U,	// LES32rm
    215577U,	// LFS16rm
    213989U,	// LFS32rm
    214832U,	// LFS64rm
    199253U,	// LGDT16m
    197665U,	// LGDT32m
    198501U,	// LGDT64m
    215583U,	// LGS16rm
    213995U,	// LGS32rm
    214838U,	// LGS64rm
    199267U,	// LIDT16m
    197679U,	// LIDT32m
    198515U,	// LIDT64m
    109169U,	// LLDT16m
    10865U,	// LLDT16r
    109232U,	// LMSW16m
    10928U,	// LMSW16r
    1067199U,	// LOCK_ADD16mi
    1067199U,	// LOCK_ADD16mi8
    1067199U,	// LOCK_ADD16mr
    3162633U,	// LOCK_ADD32mi
    3162633U,	// LOCK_ADD32mi8
    3162633U,	// LOCK_ADD32mr
    4212086U,	// LOCK_ADD64mi32
    4212086U,	// LOCK_ADD64mi8
    4212086U,	// LOCK_ADD64mr
    5259329U,	// LOCK_ADD8mi
    5259329U,	// LOCK_ADD8mr
    1067224U,	// LOCK_AND16mi
    1067224U,	// LOCK_AND16mi8
    1067224U,	// LOCK_AND16mr
    3162658U,	// LOCK_AND32mi
    3162658U,	// LOCK_AND32mi8
    3162658U,	// LOCK_AND32mr
    4212111U,	// LOCK_AND64mi32
    4212111U,	// LOCK_AND64mi8
    4212111U,	// LOCK_AND64mr
    5259335U,	// LOCK_AND8mi
    5259335U,	// LOCK_AND8mr
    108716U,	// LOCK_DEC16m
    115181U,	// LOCK_DEC32m
    124250U,	// LOCK_DEC64m
    188468U,	// LOCK_DEC8m
    108722U,	// LOCK_INC16m
    115187U,	// LOCK_INC32m
    124256U,	// LOCK_INC64m
    188474U,	// LOCK_INC8m
    1067475U,	// LOCK_OR16mi
    1067475U,	// LOCK_OR16mi8
    1067475U,	// LOCK_OR16mr
    3163030U,	// LOCK_OR32mi
    3163030U,	// LOCK_OR32mi8
    3163030U,	// LOCK_OR32mr
    4212452U,	// LOCK_OR64mi32
    4212452U,	// LOCK_OR64mi8
    4212452U,	// LOCK_OR64mr
    5259422U,	// LOCK_OR8mi
    5259422U,	// LOCK_OR8mr
    4885U,	// LOCK_PREFIX
    1067152U,	// LOCK_SUB16mi
    1067152U,	// LOCK_SUB16mi8
    1067152U,	// LOCK_SUB16mr
    3162577U,	// LOCK_SUB32mi
    3162577U,	// LOCK_SUB32mi8
    3162577U,	// LOCK_SUB32mr
    4212030U,	// LOCK_SUB64mi32
    4212030U,	// LOCK_SUB64mi8
    4212030U,	// LOCK_SUB64mr
    5259304U,	// LOCK_SUB8mi
    5259304U,	// LOCK_SUB8mr
    1067480U,	// LOCK_XOR16mi
    1067480U,	// LOCK_XOR16mi8
    1067480U,	// LOCK_XOR16mr
    3163035U,	// LOCK_XOR32mi
    3163035U,	// LOCK_XOR32mi8
    3163035U,	// LOCK_XOR32mr
    4212466U,	// LOCK_XOR64mi32
    4212466U,	// LOCK_XOR64mi8
    4212466U,	// LOCK_XOR64mr
    5259427U,	// LOCK_XOR8mi
    5259427U,	// LOCK_XOR8mr
    4939961U,	// LODSB
    2851800U,	// LODSL
    239401U,	// LODSQ
    772620U,	// LODSW
    132358U,	// LOOP
    131450U,	// LOOPE
    131435U,	// LOOPNE
    9284U,	// LRETIL
    10120U,	// LRETIQ
    10879U,	// LRETIW
    4978U,	// LRETL
    5116U,	// LRETQ
    5563U,	// LRETW
    100703U,	// LSL16rm
    22620511U,	// LSL16rr
    66316U,	// LSL32rm
    22618892U,	// LSL32rr
    83562U,	// LSL64rm
    22619754U,	// LSL64rr
    215605U,	// LSS16rm
    214017U,	// LSS32rm
    214860U,	// LSS64rm
    109034U,	// LTRm
    10730U,	// LTRr
    140060862U,	// LXADD16
    156836360U,	// LXADD32
    173614453U,	// LXADD64
    190390336U,	// LXADD8
    100998U,	// LZCNT16rm
    22620806U,	// LZCNT16rr
    66641U,	// LZCNT32rm
    22619217U,	// LZCNT32rr
    83855U,	// LZCNT64rm
    22620047U,	// LZCNT64rr
    5006U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    257551U,	// MOV16ao16
    257551U,	// MOV16ao16_16
    1067690U,	// MOV16mi
    1067690U,	// MOV16mr
    1067690U,	// MOV16ms
    780970U,	// MOV16o16a
    780970U,	// MOV16o16a_16
    22620842U,	// MOV16ri
    22620842U,	// MOV16ri_alt
    101034U,	// MOV16rm
    22620842U,	// MOV16rr
    22620842U,	// MOV16rr_REV
    22620842U,	// MOV16rs
    101034U,	// MOV16sm
    22620842U,	// MOV16sr
    265794U,	// MOV32ao32
    265794U,	// MOV32ao32_16
    22619260U,	// MOV32cr
    22619260U,	// MOV32dr
    3163260U,	// MOV32mi
    3163260U,	// MOV32mr
    1066108U,	// MOV32ms
    2884732U,	// MOV32o32a
    2884732U,	// MOV32o32a_16
    0U,	// MOV32r0
    22619260U,	// MOV32rc
    22619260U,	// MOV32rd
    22619260U,	// MOV32ri
    0U,	// MOV32ri64
    22619260U,	// MOV32ri_alt
    66684U,	// MOV32rm
    22619260U,	// MOV32rr
    22619260U,	// MOV32rr_REV
    22619260U,	// MOV32rs
    99452U,	// MOV32sm
    22619260U,	// MOV32sr
    257514U,	// MOV64ao16
    265754U,	// MOV64ao32
    273998U,	// MOV64ao64
    281618U,	// MOV64ao8
    22620101U,	// MOV64cr
    22620101U,	// MOV64dr
    4212677U,	// MOV64mi32
    4212677U,	// MOV64mr
    1066949U,	// MOV64ms
    780797U,	// MOV64o16a
    2884553U,	// MOV64o32a
    3942176U,	// MOV64o64a
    4997296U,	// MOV64o8a
    22620101U,	// MOV64rc
    22620101U,	// MOV64rd
    22619936U,	// MOV64ri
    22620101U,	// MOV64ri32
    83909U,	// MOV64rm
    22620101U,	// MOV64rr
    22620101U,	// MOV64rr_REV
    22620101U,	// MOV64rs
    100293U,	// MOV64sm
    22620101U,	// MOV64sr
    281655U,	// MOV8ao8
    281655U,	// MOV8ao8_16
    5259503U,	// MOV8mi
    5259503U,	// MOV8mr
    206586095U,	// MOV8mr_NOREX
    4997359U,	// MOV8o8a
    4997359U,	// MOV8o8a_16
    22618351U,	// MOV8ri
    22618351U,	// MOV8ri_alt
    139503U,	// MOV8rm
    8528111U,	// MOV8rm_NOREX
    22618351U,	// MOV8rr
    559489263U,	// MOV8rr_NOREX
    22618351U,	// MOV8rr_REV
    1067247U,	// MOVBE16mr
    100591U,	// MOVBE16rm
    3162681U,	// MOVBE32mr
    66105U,	// MOVBE32rm
    4212134U,	// MOVBE64mr
    83366U,	// MOVBE64rm
    0U,	// MOVPC32r
    286926U,	// MOVSB
    295957U,	// MOVSL
    304985U,	// MOVSQ
    313929U,	// MOVSW
    141448U,	// MOVSX16rm8
    22620296U,	// MOVSX16rr8
    99465U,	// MOVSX32rm16
    139721U,	// MOVSX32rm8
    22619273U,	// MOVSX32rr16
    22618569U,	// MOVSX32rr8
    22619760U,	// MOVSX64_NOREXrr32
    100306U,	// MOVSX64rm16
    67184U,	// MOVSX64rm32
    140598U,	// MOVSX64rm8
    22620114U,	// MOVSX64rr16
    22619760U,	// MOVSX64rr32
    22619446U,	// MOVSX64rr8
    141470U,	// MOVZX16rm8
    22620318U,	// MOVZX16rr8
    139743U,	// MOVZX32_NOREXrm8
    22618591U,	// MOVZX32_NOREXrr8
    99473U,	// MOVZX32rm16
    139743U,	// MOVZX32rm8
    22619281U,	// MOVZX32rr16
    22618591U,	// MOVZX32rr8
    100314U,	// MOVZX64rm16_Q
    140620U,	// MOVZX64rm8_Q
    22620122U,	// MOVZX64rr16_Q
    22619468U,	// MOVZX64rr8_Q
    108902U,	// MUL16m
    10598U,	// MUL16r
    115475U,	// MUL32m
    8979U,	// MUL32r
    124537U,	// MUL64m
    9849U,	// MUL64r
    188538U,	// MUL8m
    8314U,	// MUL8r
    35169447U,	// MULX32rm
    35153063U,	// MULX32rr
    35178480U,	// MULX64rm
    35153904U,	// MULX64rr
    108832U,	// NEG16m
    10528U,	// NEG16r
    115360U,	// NEG32m
    8864U,	// NEG32r
    124437U,	// NEG64m
    9749U,	// NEG64r
    188493U,	// NEG8m
    8269U,	// NEG8r
    5073U,	// NOOP
    108960U,	// NOOP18_16m4
    108960U,	// NOOP18_16m5
    108960U,	// NOOP18_16m6
    108960U,	// NOOP18_16m7
    10656U,	// NOOP18_16r4
    10656U,	// NOOP18_16r5
    10656U,	// NOOP18_16r6
    10656U,	// NOOP18_16r7
    115555U,	// NOOP18_m4
    115555U,	// NOOP18_m5
    115555U,	// NOOP18_m6
    115555U,	// NOOP18_m7
    9059U,	// NOOP18_r4
    9059U,	// NOOP18_r5
    9059U,	// NOOP18_r6
    9059U,	// NOOP18_r7
    123217153U,	// NOOP19rr
    115555U,	// NOOPL
    115555U,	// NOOPL_19
    115555U,	// NOOPL_1a
    115555U,	// NOOPL_1b
    115555U,	// NOOPL_1c
    115555U,	// NOOPL_1d
    115555U,	// NOOPL_1e
    108960U,	// NOOPW
    108960U,	// NOOPW_19
    108960U,	// NOOPW_1a
    108960U,	// NOOPW_1b
    108960U,	// NOOPW_1c
    108960U,	// NOOPW_1d
    108960U,	// NOOPW_1e
    109206U,	// NOT16m
    10902U,	// NOT16r
    115809U,	// NOT32m
    9313U,	// NOT32r
    124831U,	// NOT64m
    10143U,	// NOT64r
    188635U,	// NOT8m
    8411U,	// NOT8r
    534995U,	// OR16i16
    1067475U,	// OR16mi
    1067475U,	// OR16mi8
    1067475U,	// OR16mr
    1599955U,	// OR16ri
    1599955U,	// OR16ri8
    1608147U,	// OR16rm
    1599955U,	// OR16rr
    2124243U,	// OR16rr_REV
    2630550U,	// OR32i32
    3163030U,	// OR32mi
    3163030U,	// OR32mi8
    3163030U,	// OR32mr
    3163030U,	// OR32mrLocked
    1598358U,	// OR32ri
    1598358U,	// OR32ri8
    1614742U,	// OR32rm
    1598358U,	// OR32rr
    2122646U,	// OR32rr_REV
    3679972U,	// OR64i32
    4212452U,	// OR64mi32
    4212452U,	// OR64mi8
    4212452U,	// OR64mr
    1599204U,	// OR64ri32
    1599204U,	// OR64ri8
    1623780U,	// OR64rm
    1599204U,	// OR64rr
    2123492U,	// OR64rr_REV
    4726942U,	// OR8i8
    5259422U,	// OR8mi
    5259422U,	// OR8mr
    1597598U,	// OR8ri
    1597598U,	// OR8ri8
    57502U,	// OR8rm
    1597598U,	// OR8rr
    2121886U,	// OR8rr_REV
    11780U,	// OUT16ir
    5730U,	// OUT16rr
    11830U,	// OUT32ir
    5744U,	// OUT32rr
    11308U,	// OUT8ir
    5716U,	// OUT8rr
    9134279U,	// OUTSB
    9143309U,	// OUTSL
    9161281U,	// OUTSW
    35169094U,	// PDEP32rm
    35152710U,	// PDEP32rr
    35178151U,	// PDEP64rm
    35153575U,	// PDEP64rr
    35169390U,	// PEXT32rm
    35153006U,	// PEXT32rr
    35178423U,	// PEXT64rm
    35153847U,	// PEXT64rr
    10662U,	// POP16r
    108966U,	// POP16rmm
    10662U,	// POP16rmr
    9065U,	// POP32r
    115561U,	// POP32rmm
    9065U,	// POP32rmr
    9917U,	// POP64r
    124605U,	// POP64rmm
    9917U,	// POP64rmr
    5513U,	// POPA16
    4910U,	// POPA32
    5232U,	// POPDS16
    5213U,	// POPDS32
    5270U,	// POPES16
    5251U,	// POPES32
    5526U,	// POPF16
    4923U,	// POPF32
    5084U,	// POPF64
    5327U,	// POPFS16
    5289U,	// POPFS32
    5308U,	// POPFS64
    5384U,	// POPGS16
    5346U,	// POPGS32
    5365U,	// POPGS64
    5474U,	// POPSS16
    5455U,	// POPSS32
    10552U,	// PUSH16i8
    10552U,	// PUSH16r
    108856U,	// PUSH16rmm
    10552U,	// PUSH16rmr
    8888U,	// PUSH32i8
    8888U,	// PUSH32r
    115384U,	// PUSH32rmm
    8888U,	// PUSH32rmr
    10552U,	// PUSH64i16
    9773U,	// PUSH64i32
    9773U,	// PUSH64i8
    9773U,	// PUSH64r
    124461U,	// PUSH64rmm
    9773U,	// PUSH64rmr
    5506U,	// PUSHA16
    4903U,	// PUSHA32
    5193U,	// PUSHCS16
    5183U,	// PUSHCS32
    5222U,	// PUSHDS16
    5203U,	// PUSHDS32
    5260U,	// PUSHES16
    5241U,	// PUSHES32
    5519U,	// PUSHF16
    4916U,	// PUSHF32
    5077U,	// PUSHF64
    5317U,	// PUSHFS16
    5279U,	// PUSHFS32
    5298U,	// PUSHFS64
    5374U,	// PUSHGS16
    5336U,	// PUSHGS32
    5355U,	// PUSHGS64
    5464U,	// PUSHSS16
    5445U,	// PUSHSS32
    10552U,	// PUSHi16
    8888U,	// PUSHi32
    109516U,	// RCL16m1
    109981U,	// RCL16mCL
    1067333U,	// RCL16mi
    11212U,	// RCL16r1
    11677U,	// RCL16rCL
    2124101U,	// RCL16ri
    118411U,	// RCL32m1
    117949U,	// RCL32mCL
    3162848U,	// RCL32mi
    11052U,	// RCL32r1
    11453U,	// RCL32rCL
    2122464U,	// RCL32ri
    125820U,	// RCL64m1
    126253U,	// RCL64mCL
    4212305U,	// RCL64mi
    11132U,	// RCL64r1
    11565U,	// RCL64rCL
    2123345U,	// RCL64ri
    191196U,	// RCL8m1
    191565U,	// RCL8mCL
    5259367U,	// RCL8mi
    10972U,	// RCL8r1
    11341U,	// RCL8rCL
    2121831U,	// RCL8ri
    109556U,	// RCR16m1
    110025U,	// RCR16mCL
    1067456U,	// RCR16mi
    11252U,	// RCR16r1
    11721U,	// RCR16rCL
    2124224U,	// RCR16ri
    117588U,	// RCR32m1
    117993U,	// RCR32mCL
    3163017U,	// RCR32mi
    11092U,	// RCR32r1
    11497U,	// RCR32rCL
    2122633U,	// RCR32ri
    125860U,	// RCR64m1
    126297U,	// RCR64mCL
    4212439U,	// RCR64mi
    11172U,	// RCR64r1
    11609U,	// RCR64rCL
    2123479U,	// RCR64ri
    191236U,	// RCR8m1
    191609U,	// RCR8mCL
    5259409U,	// RCR8mi
    11012U,	// RCR8r1
    11385U,	// RCR8rCL
    2121873U,	// RCR8ri
    8796U,	// RDFSBASE
    9673U,	// RDFSBASE64
    8818U,	// RDGSBASE
    9695U,	// RDGSBASE64
    5153U,	// RDMSR
    4713U,	// RDPMC
    10453U,	// RDRAND16r
    8735U,	// RDRAND32r
    9612U,	// RDRAND64r
    10437U,	// RDSEED16r
    8719U,	// RDSEED32r
    9596U,	// RDSEED64r
    4726U,	// RDTSC
    5062U,	// RDTSCP
    4238U,	// RELEASE_MOV16mr
    4238U,	// RELEASE_MOV32mr
    4238U,	// RELEASE_MOV64mr
    4238U,	// RELEASE_MOV8mr
    4776U,	// REPNE_PREFIX
    4657U,	// REP_MOVSB_32
    4657U,	// REP_MOVSB_64
    4962U,	// REP_MOVSD_32
    4962U,	// REP_MOVSD_64
    5100U,	// REP_MOVSQ_64
    5542U,	// REP_MOVSW_32
    5542U,	// REP_MOVSW_64
    5069U,	// REP_PREFIX
    4647U,	// REP_STOSB_32
    4647U,	// REP_STOSB_64
    4952U,	// REP_STOSD_32
    4952U,	// REP_STOSD_64
    5090U,	// REP_STOSQ_64
    5532U,	// REP_STOSW_32
    5532U,	// REP_STOSW_64
    9285U,	// RETIL
    10121U,	// RETIQ
    10880U,	// RETIW
    4973U,	// RETL
    5111U,	// RETQ
    5558U,	// RETW
    4492U,	// REX64_PREFIX
    109536U,	// ROL16m1
    110003U,	// ROL16mCL
    1067353U,	// ROL16mi
    11232U,	// ROL16r1
    11699U,	// ROL16rCL
    2124121U,	// ROL16ri
    117568U,	// ROL32m1
    117971U,	// ROL32mCL
    3162886U,	// ROL32mi
    11072U,	// ROL32r1
    11475U,	// ROL32rCL
    2122502U,	// ROL32ri
    125840U,	// ROL64m1
    126275U,	// ROL64mCL
    4212324U,	// ROL64mi
    11152U,	// ROL64r1
    11587U,	// ROL64rCL
    2123364U,	// ROL64ri
    191216U,	// ROL8m1
    191587U,	// ROL8mCL
    5259379U,	// ROL8mi
    10992U,	// ROL8r1
    11363U,	// ROL8rCL
    2121843U,	// ROL8ri
    109576U,	// ROR16m1
    110047U,	// ROR16mCL
    1067474U,	// ROR16mi
    11272U,	// ROR16r1
    11743U,	// ROR16rCL
    2124242U,	// ROR16ri
    117608U,	// ROR32m1
    118015U,	// ROR32mCL
    3163029U,	// ROR32mi
    11112U,	// ROR32r1
    11519U,	// ROR32rCL
    2122645U,	// ROR32ri
    125880U,	// ROR64m1
    126319U,	// ROR64mCL
    4212451U,	// ROR64mi
    11192U,	// ROR64r1
    11631U,	// ROR64rCL
    2123491U,	// ROR64ri
    191256U,	// ROR8m1
    191631U,	// ROR8mCL
    5259421U,	// ROR8mi
    11032U,	// ROR8r1
    11407U,	// ROR8rCL
    2121885U,	// ROR8ri
    6382787U,	// RORX32mi
    35153091U,	// RORX32ri
    6907916U,	// RORX64mi
    35153932U,	// RORX64ri
    5021U,	// RSM
    4838U,	// SAHF
    109506U,	// SAL16m1
    109970U,	// SAL16mCL
    1067327U,	// SAL16mi
    11202U,	// SAL16r1
    11666U,	// SAL16rCL
    2124095U,	// SAL16ri
    117538U,	// SAL32m1
    117938U,	// SAL32mCL
    3162842U,	// SAL32mi
    11042U,	// SAL32r1
    11442U,	// SAL32rCL
    2122458U,	// SAL32ri
    125810U,	// SAL64m1
    126242U,	// SAL64mCL
    4212299U,	// SAL64mi
    11122U,	// SAL64r1
    11554U,	// SAL64rCL
    2123339U,	// SAL64ri
    191186U,	// SAL8m1
    191554U,	// SAL8mCL
    5259361U,	// SAL8mi
    10962U,	// SAL8r1
    11330U,	// SAL8rCL
    2121825U,	// SAL8ri
    4700U,	// SALC
    109546U,	// SAR16m1
    110014U,	// SAR16mCL
    1067450U,	// SAR16mi
    11242U,	// SAR16r1
    11710U,	// SAR16rCL
    2124218U,	// SAR16ri
    117578U,	// SAR32m1
    117982U,	// SAR32mCL
    3163011U,	// SAR32mi
    11082U,	// SAR32r1
    11486U,	// SAR32rCL
    2122627U,	// SAR32ri
    125850U,	// SAR64m1
    126286U,	// SAR64mCL
    4212433U,	// SAR64mi
    11162U,	// SAR64r1
    11598U,	// SAR64rCL
    2123473U,	// SAR64ri
    191226U,	// SAR8m1
    191598U,	// SAR8mCL
    5259403U,	// SAR8mi
    11002U,	// SAR8r1
    11374U,	// SAR8rCL
    2121867U,	// SAR8ri
    6382773U,	// SARX32rm
    35153077U,	// SARX32rr
    6907902U,	// SARX64rm
    35153918U,	// SARX64rr
    534658U,	// SBB16i16
    1067138U,	// SBB16mi
    1067138U,	// SBB16mi8
    1067138U,	// SBB16mr
    1599618U,	// SBB16ri
    1599618U,	// SBB16ri8
    1607810U,	// SBB16rm
    1599618U,	// SBB16rr
    2123906U,	// SBB16rr_REV
    2630083U,	// SBB32i32
    3162563U,	// SBB32mi
    3162563U,	// SBB32mi8
    3162563U,	// SBB32mr
    1597891U,	// SBB32ri
    1597891U,	// SBB32ri8
    1614275U,	// SBB32rm
    1597891U,	// SBB32rr
    2122179U,	// SBB32rr_REV
    3679536U,	// SBB64i32
    4212016U,	// SBB64mi32
    4212016U,	// SBB64mi8
    4212016U,	// SBB64mr
    1598768U,	// SBB64ri32
    1598768U,	// SBB64ri8
    1623344U,	// SBB64rm
    1598768U,	// SBB64rr
    2123056U,	// SBB64rr_REV
    4726818U,	// SBB8i8
    5259298U,	// SBB8mi
    5259298U,	// SBB8mr
    1597474U,	// SBB8ri
    57378U,	// SBB8rm
    1597474U,	// SBB8rr
    2121762U,	// SBB8rr_REV
    4866217U,	// SCASB
    2778050U,	// SCASL
    3835673U,	// SCASQ
    698870U,	// SCASW
    5400U,	// SEG_ALLOCA_32
    5400U,	// SEG_ALLOCA_64
    4803U,	// SEH_EndPrologue
    4789U,	// SEH_Epilogue
    12013U,	// SEH_PushFrame
    12058U,	// SEH_PushReg
    123219724U,	// SEH_SaveReg
    123219638U,	// SEH_SaveXMM
    123219709U,	// SEH_SetFrame
    11996U,	// SEH_StackAlloc
    188727U,	// SETAEm
    8503U,	// SETAEr
    188421U,	// SETAm
    8197U,	// SETAr
    188739U,	// SETBEm
    8515U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    188629U,	// SETBm
    8405U,	// SETBr
    188801U,	// SETEm
    8577U,	// SETEr
    188751U,	// SETGEm
    8527U,	// SETGEr
    188826U,	// SETGm
    8602U,	// SETGr
    188767U,	// SETLEm
    8543U,	// SETLEr
    189515U,	// SETLm
    9291U,	// SETLr
    188787U,	// SETNEm
    8563U,	// SETNEr
    189663U,	// SETNOm
    9439U,	// SETNOr
    189690U,	// SETNPm
    9466U,	// SETNPr
    190535U,	// SETNSm
    10311U,	// SETNSr
    189670U,	// SETOm
    9446U,	// SETOr
    189708U,	// SETPm
    9484U,	// SETPr
    190542U,	// SETSm
    10318U,	// SETSr
    199260U,	// SGDT16m
    197672U,	// SGDT32m
    198508U,	// SGDT64m
    109526U,	// SHL16m1
    109992U,	// SHL16mCL
    1067339U,	// SHL16mi
    11222U,	// SHL16r1
    11688U,	// SHL16rCL
    2124107U,	// SHL16ri
    117558U,	// SHL32m1
    117960U,	// SHL32mCL
    3162854U,	// SHL32mi
    11062U,	// SHL32r1
    11464U,	// SHL32rCL
    2122470U,	// SHL32ri
    125830U,	// SHL64m1
    126264U,	// SHL64mCL
    4212311U,	// SHL64mi
    11142U,	// SHL64r1
    11576U,	// SHL64rCL
    2123351U,	// SHL64ri
    191206U,	// SHL8m1
    191576U,	// SHL8mCL
    5259373U,	// SHL8mi
    10982U,	// SHL8r1
    11352U,	// SHL8rCL
    2121837U,	// SHL8ri
    1068410U,	// SHLD16mrCL
    227633358U,	// SHLD16mri8
    2125178U,	// SHLD16rrCL
    321742U,	// SHLD16rri8
    3165338U,	// SHLD32mrCL
    244408856U,	// SHLD32mri8
    2124954U,	// SHLD32rrCL
    320024U,	// SHLD32rri8
    4214026U,	// SHLD64mrCL
    261186949U,	// SHLD64mri8
    2125066U,	// SHLD64rrCL
    320901U,	// SHLD64rri8
    6382752U,	// SHLX32rm
    35153056U,	// SHLX32rr
    6907881U,	// SHLX64rm
    35153897U,	// SHLX64rr
    109566U,	// SHR16m1
    110036U,	// SHR16mCL
    1067468U,	// SHR16mi
    11262U,	// SHR16r1
    11732U,	// SHR16rCL
    2124236U,	// SHR16ri
    117598U,	// SHR32m1
    118004U,	// SHR32mCL
    3163023U,	// SHR32mi
    11102U,	// SHR32r1
    11508U,	// SHR32rCL
    2122639U,	// SHR32ri
    125870U,	// SHR64m1
    126308U,	// SHR64mCL
    4212445U,	// SHR64mi
    11182U,	// SHR64r1
    11620U,	// SHR64rCL
    2123485U,	// SHR64ri
    191246U,	// SHR8m1
    191620U,	// SHR8mCL
    5259415U,	// SHR8mi
    11022U,	// SHR8r1
    11396U,	// SHR8rCL
    2121879U,	// SHR8ri
    1068422U,	// SHRD16mrCL
    227633374U,	// SHRD16mri8
    2125190U,	// SHRD16rrCL
    321758U,	// SHRD16rri8
    3165350U,	// SHRD32mrCL
    244408872U,	// SHRD32mri8
    2124966U,	// SHRD32rrCL
    320040U,	// SHRD32rri8
    4214038U,	// SHRD64mrCL
    261186965U,	// SHRD64mri8
    2125078U,	// SHRD64rrCL
    320917U,	// SHRD64rri8
    6382780U,	// SHRX32rm
    35153084U,	// SHRX32rr
    6907909U,	// SHRX64rm
    35153925U,	// SHRX64rr
    199274U,	// SIDT16m
    197686U,	// SIDT32m
    198522U,	// SIDT64m
    5617U,	// SKINIT
    109176U,	// SLDT16m
    10872U,	// SLDT16r
    9277U,	// SLDT32r
    108417U,	// SLDT64m
    10113U,	// SLDT64r
    109239U,	// SMSW16m
    10935U,	// SMSW16r
    9346U,	// SMSW32r
    10187U,	// SMSW64r
    4678U,	// STAC
    4732U,	// STC
    4751U,	// STD
    4857U,	// STGI
    4866U,	// STI
    150560U,	// STOSB
    159273U,	// STOSL
    167517U,	// STOSQ
    175608U,	// STOSW
    10736U,	// STR16r
    9140U,	// STR32r
    9995U,	// STR64r
    109040U,	// STRm
    534672U,	// SUB16i16
    1067152U,	// SUB16mi
    1067152U,	// SUB16mi8
    1067152U,	// SUB16mr
    1599632U,	// SUB16ri
    1599632U,	// SUB16ri8
    1607824U,	// SUB16rm
    1599632U,	// SUB16rr
    2123920U,	// SUB16rr_REV
    2630097U,	// SUB32i32
    3162577U,	// SUB32mi
    3162577U,	// SUB32mi8
    3162577U,	// SUB32mr
    1597905U,	// SUB32ri
    1597905U,	// SUB32ri8
    1614289U,	// SUB32rm
    1597905U,	// SUB32rr
    2122193U,	// SUB32rr_REV
    3679550U,	// SUB64i32
    4212030U,	// SUB64mi32
    4212030U,	// SUB64mi8
    4212030U,	// SUB64mr
    1598782U,	// SUB64ri32
    1598782U,	// SUB64ri8
    1623358U,	// SUB64rm
    1598782U,	// SUB64rr
    2123070U,	// SUB64rr_REV
    4726824U,	// SUB8i8
    5259304U,	// SUB8mi
    5259304U,	// SUB8mr
    1597480U,	// SUB8ri
    1597480U,	// SUB8ri8
    57384U,	// SUB8rm
    1597480U,	// SUB8rr
    2121768U,	// SUB8rr_REV
    5393U,	// SWAPGS
    4944U,	// SYSCALL
    5144U,	// SYSENTER
    4992U,	// SYSEXIT
    5130U,	// SYSEXIT64
    4984U,	// SYSRET
    5122U,	// SYSRET64
    65795U,	// T1MSKC32rm
    22618371U,	// T1MSKC32rr
    82179U,	// T1MSKC64rm
    22618371U,	// T1MSKC64rr
    10093808U,	// TAILJMPd
    10093808U,	// TAILJMPd64
    10080429U,	// TAILJMPm
    10088638U,	// TAILJMPm64
    0U,	// TAILJMPr
    9973950U,	// TAILJMPr64
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    535196U,	// TEST16i16
    1067676U,	// TEST16mi
    1067676U,	// TEST16mi_alt
    22620828U,	// TEST16ri
    22620828U,	// TEST16ri_alt
    1067676U,	// TEST16rm
    22620828U,	// TEST16rr
    2630759U,	// TEST32i32
    3163239U,	// TEST32mi
    3163239U,	// TEST32mi_alt
    22619239U,	// TEST32ri
    22619239U,	// TEST32ri_alt
    3163239U,	// TEST32rm
    22619239U,	// TEST32rr
    3680176U,	// TEST64i32
    4212656U,	// TEST64mi32
    4212656U,	// TEST64mi32_alt
    22620080U,	// TEST64ri32
    22620080U,	// TEST64ri32_alt
    4212656U,	// TEST64rm
    22620080U,	// TEST64rr
    4727009U,	// TEST8i8
    5259489U,	// TEST8mi
    5259489U,	// TEST8mi_alt
    22618337U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    22618337U,	// TEST8ri_alt
    5259489U,	// TEST8rm
    22618337U,	// TEST8rr
    4358U,	// TLSCall_32
    4448U,	// TLSCall_64
    4371U,	// TLS_addr32
    4461U,	// TLS_addr64
    4384U,	// TLS_base_addr32
    4474U,	// TLS_base_addr64
    4402U,	// TRAP
    101006U,	// TZCNT16rm
    22620814U,	// TZCNT16rr
    66649U,	// TZCNT32rm
    22619225U,	// TZCNT32rr
    83863U,	// TZCNT64rm
    22620055U,	// TZCNT64rr
    65966U,	// TZMSK32rm
    22618542U,	// TZMSK32rr
    82350U,	// TZMSK64rm
    22618542U,	// TZMSK64rr
    4612U,	// UD2B
    274214548U,	// VAARG_64
    1196961476U,	// VASTART_SAVE_XMM_REGS
    108587U,	// VERRm
    10283U,	// VERRr
    108998U,	// VERWm
    10694U,	// VERWr
    4937U,	// VMCALL
    124947U,	// VMCLEARm
    4719U,	// VMFUNC
    4843U,	// VMLAUNCH
    5582U,	// VMLOAD32
    5662U,	// VMLOAD64
    4929U,	// VMMCALL
    123170U,	// VMPTRLDm
    125035U,	// VMPTRSTm
    3162623U,	// VMREAD32rm
    22618623U,	// VMREAD32rr
    4212076U,	// VMREAD64rm
    22619500U,	// VMREAD64rr
    4767U,	// VMRESUME
    5606U,	// VMRUN32
    5686U,	// VMRUN64
    5594U,	// VMSAVE32
    5674U,	// VMSAVE64
    66184U,	// VMWRITE32rm
    22618760U,	// VMWRITE32rr
    83445U,	// VMWRITE64rm
    22619637U,	// VMWRITE64rr
    4826U,	// VMXOFF
    124111U,	// VMXON
    132701U,	// W64ALLOCA
    4760U,	// WBINVD
    5025U,	// WIN_ALLOCA
    4870U,	// WIN_FTOL_32
    4870U,	// WIN_FTOL_64
    8807U,	// WRFSBASE
    9684U,	// WRFSBASE64
    8829U,	// WRGSBASE
    9706U,	// WRGSBASE64
    5159U,	// WRMSR
    1067198U,	// XADD16rm
    22620350U,	// XADD16rr
    3162632U,	// XADD32rm
    22618632U,	// XADD32rr
    4212085U,	// XADD64rm
    22619509U,	// XADD64rr
    5259328U,	// XADD8rm
    22618176U,	// XADD8rr
    534825U,	// XCHG16ar
    140060969U,	// XCHG16rm
    291055913U,	// XCHG16rr
    2630313U,	// XCHG32ar
    2630313U,	// XCHG32ar64
    156836521U,	// XCHG32rm
    291054249U,	// XCHG32rr
    3679774U,	// XCHG64ar
    173614622U,	// XCHG64rm
    291055134U,	// XCHG64rr
    190390358U,	// XCHG8rm
    291053654U,	// XCHG8rr
    4683U,	// XCRYPTCBC
    4627U,	// XCRYPTCFB
    5165U,	// XCRYPTCTR
    4617U,	// XCRYPTECB
    4637U,	// XCRYPTOFB
    5492U,	// XGETBV
    4667U,	// XLAT
    535000U,	// XOR16i16
    1067480U,	// XOR16mi
    1067480U,	// XOR16mi8
    1067480U,	// XOR16mr
    1599960U,	// XOR16ri
    1599960U,	// XOR16ri8
    1608152U,	// XOR16rm
    1599960U,	// XOR16rr
    2124248U,	// XOR16rr_REV
    2630555U,	// XOR32i32
    3163035U,	// XOR32mi
    3163035U,	// XOR32mi8
    3163035U,	// XOR32mr
    1598363U,	// XOR32ri
    1598363U,	// XOR32ri8
    1614747U,	// XOR32rm
    1598363U,	// XOR32rr
    2122651U,	// XOR32rr_REV
    3679986U,	// XOR64i32
    4212466U,	// XOR64mi32
    4212466U,	// XOR64mi8
    4212466U,	// XOR64mr
    1599218U,	// XOR64ri32
    1599218U,	// XOR64ri8
    1623794U,	// XOR64rm
    1599218U,	// XOR64rr
    2123506U,	// XOR64rr_REV
    4726947U,	// XOR8i8
    5259427U,	// XOR8mi
    5259427U,	// XOR8mr
    1597603U,	// XOR8ri
    1597603U,	// XOR8ri8
    57507U,	// XOR8rm
    1597603U,	// XOR8rr
    2121891U,	// XOR8rr_REV
    198691U,	// XRSTOR
    198377U,	// XRSTOR64
    196999U,	// XSAVE
    198143U,	// XSAVE64
    198753U,	// XSAVEOPT
    198565U,	// XSAVEOPT64
    5499U,	// XSETBV
    4310U,	// XSHA1
    4505U,	// XSHA256
    4782U,	// XSTORE
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'j', 'a', 9, 0,
  /* 4 */ 's', 'e', 't', 'a', 9, 0,
  /* 10 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '1', '6', 'b', 9, 0,
  /* 22 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '8', 'b', 9, 0,
  /* 33 */ 's', 'b', 'b', 'b', 9, 0,
  /* 39 */ 's', 'u', 'b', 'b', 9, 0,
  /* 45 */ 'a', 'd', 'c', 'b', 9, 0,
  /* 51 */ 'd', 'e', 'c', 'b', 9, 0,
  /* 57 */ 'i', 'n', 'c', 'b', 9, 0,
  /* 63 */ 'x', 'a', 'd', 'd', 'b', 9, 0,
  /* 70 */ 'a', 'n', 'd', 'b', 9, 0,
  /* 76 */ 'n', 'e', 'g', 'b', 9, 0,
  /* 82 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'b', 9, 0,
  /* 92 */ 'j', 'b', 9, 0,
  /* 96 */ 's', 'a', 'l', 'b', 9, 0,
  /* 102 */ 'r', 'c', 'l', 'b', 9, 0,
  /* 108 */ 's', 'h', 'l', 'b', 9, 0,
  /* 114 */ 'r', 'o', 'l', 'b', 9, 0,
  /* 120 */ 'i', 'm', 'u', 'l', 'b', 9, 0,
  /* 127 */ 'i', 'n', 'b', 9, 0,
  /* 132 */ 'c', 'm', 'p', 'b', 9, 0,
  /* 138 */ 's', 'a', 'r', 'b', 9, 0,
  /* 144 */ 'r', 'c', 'r', 'b', 9, 0,
  /* 150 */ 's', 'h', 'r', 'b', 9, 0,
  /* 156 */ 'r', 'o', 'r', 'b', 9, 0,
  /* 162 */ 'x', 'o', 'r', 'b', 9, 0,
  /* 168 */ 's', 'c', 'a', 's', 'b', 9, 0,
  /* 175 */ 'm', 'o', 'v', 'a', 'b', 's', 'b', 9, 0,
  /* 184 */ 'l', 'o', 'd', 's', 'b', 9, 0,
  /* 191 */ 'c', 'm', 'p', 's', 'b', 9, 0,
  /* 198 */ 'o', 'u', 't', 's', 'b', 9, 0,
  /* 205 */ 'm', 'o', 'v', 's', 'b', 9, 0,
  /* 212 */ 's', 'e', 't', 'b', 9, 0,
  /* 218 */ 'n', 'o', 't', 'b', 9, 0,
  /* 224 */ 't', 'e', 's', 't', 'b', 9, 0,
  /* 231 */ 'i', 'd', 'i', 'v', 'b', 9, 0,
  /* 238 */ 'm', 'o', 'v', 'b', 9, 0,
  /* 244 */ 'b', 'l', 'c', 'i', 'c', 9, 0,
  /* 251 */ 'b', 'l', 's', 'i', 'c', 9, 0,
  /* 258 */ 't', '1', 'm', 's', 'k', 'c', 9, 0,
  /* 266 */ 'a', 'a', 'd', 9, 0,
  /* 271 */ 'i', 'n', 'v', 'p', 'c', 'i', 'd', 9, 0,
  /* 280 */ 'i', 'n', 'v', 'v', 'p', 'i', 'd', 9, 0,
  /* 289 */ 'v', 'm', 'p', 't', 'r', 'l', 'd', 9, 0,
  /* 298 */ 'b', 'o', 'u', 'n', 'd', 9, 0,
  /* 305 */ 'j', 'a', 'e', 9, 0,
  /* 310 */ 's', 'e', 't', 'a', 'e', 9, 0,
  /* 317 */ 'j', 'b', 'e', 9, 0,
  /* 322 */ 's', 'e', 't', 'b', 'e', 9, 0,
  /* 329 */ 'j', 'g', 'e', 9, 0,
  /* 334 */ 's', 'e', 't', 'g', 'e', 9, 0,
  /* 341 */ 'j', 'e', 9, 0,
  /* 345 */ 'j', 'l', 'e', 9, 0,
  /* 350 */ 's', 'e', 't', 'l', 'e', 9, 0,
  /* 357 */ 'j', 'n', 'e', 9, 0,
  /* 362 */ 'l', 'o', 'o', 'p', 'n', 'e', 9, 0,
  /* 370 */ 's', 'e', 't', 'n', 'e', 9, 0,
  /* 377 */ 'l', 'o', 'o', 'p', 'e', 9, 0,
  /* 384 */ 's', 'e', 't', 'e', 9, 0,
  /* 390 */ 'x', 's', 'a', 'v', 'e', 9, 0,
  /* 397 */ 'j', 'g', 9, 0,
  /* 401 */ 'i', 'n', 'v', 'l', 'p', 'g', 9, 0,
  /* 409 */ 's', 'e', 't', 'g', 9, 0,
  /* 415 */ 'b', 'l', 'c', 'i', 9, 0,
  /* 421 */ 'b', 'l', 'c', 'm', 's', 'k', 9, 0,
  /* 429 */ 't', 'z', 'm', 's', 'k', 9, 0,
  /* 436 */ 'l', 'e', 'a', 'l', 9, 0,
  /* 442 */ 'c', 'm', 'o', 'v', 'a', 'l', 9, 0,
  /* 450 */ 's', 'b', 'b', 'l', 9, 0,
  /* 456 */ 'm', 'o', 'v', 's', 'b', 'l', 9, 0,
  /* 464 */ 's', 'u', 'b', 'l', 9, 0,
  /* 470 */ 'c', 'm', 'o', 'v', 'b', 'l', 9, 0,
  /* 478 */ 'm', 'o', 'v', 'z', 'b', 'l', 9, 0,
  /* 486 */ 'a', 'd', 'c', 'l', 9, 0,
  /* 492 */ 'd', 'e', 'c', 'l', 9, 0,
  /* 498 */ 'i', 'n', 'c', 'l', 9, 0,
  /* 504 */ 'b', 't', 'c', 'l', 9, 0,
  /* 510 */ 'v', 'm', 'r', 'e', 'a', 'd', 'l', 9, 0,
  /* 519 */ 'x', 'a', 'd', 'd', 'l', 9, 0,
  /* 526 */ 'r', 'd', 's', 'e', 'e', 'd', 'l', 9, 0,
  /* 535 */ 's', 'h', 'l', 'd', 'l', 9, 0,
  /* 542 */ 'r', 'd', 'r', 'a', 'n', 'd', 'l', 9, 0,
  /* 551 */ 's', 'h', 'r', 'd', 'l', 9, 0,
  /* 558 */ 'c', 'm', 'o', 'v', 'a', 'e', 'l', 9, 0,
  /* 567 */ 'c', 'm', 'o', 'v', 'b', 'e', 'l', 9, 0,
  /* 576 */ 'c', 'm', 'o', 'v', 'g', 'e', 'l', 9, 0,
  /* 585 */ 'c', 'm', 'o', 'v', 'l', 'e', 'l', 9, 0,
  /* 594 */ 'c', 'm', 'o', 'v', 'n', 'e', 'l', 9, 0,
  /* 603 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 614 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 625 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 636 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 647 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 'l', 9, 0,
  /* 657 */ 'c', 'm', 'o', 'v', 'e', 'l', 9, 0,
  /* 665 */ 'b', 's', 'f', 'l', 9, 0,
  /* 671 */ 'n', 'e', 'g', 'l', 9, 0,
  /* 677 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'l', 9, 0,
  /* 687 */ 'c', 'm', 'o', 'v', 'g', 'l', 9, 0,
  /* 695 */ 'p', 'u', 's', 'h', 'l', 9, 0,
  /* 702 */ 'b', 'z', 'h', 'i', 'l', 9, 0,
  /* 709 */ 'b', 'l', 's', 'i', 'l', 9, 0,
  /* 716 */ 'j', 'l', 9, 0,
  /* 720 */ 'b', 'l', 's', 'm', 's', 'k', 'l', 9, 0,
  /* 729 */ 's', 'a', 'l', 'l', 9, 0,
  /* 735 */ 'r', 'c', 'l', 'l', 9, 0,
  /* 741 */ 's', 'h', 'l', 'l', 9, 0,
  /* 747 */ 'b', 'l', 'c', 'f', 'i', 'l', 'l', 9, 0,
  /* 756 */ 'b', 'l', 's', 'f', 'i', 'l', 'l', 9, 0,
  /* 765 */ 'l', 'c', 'a', 'l', 'l', 'l', 9, 0,
  /* 773 */ 'r', 'o', 'l', 'l', 9, 0,
  /* 779 */ 'l', 's', 'l', 'l', 9, 0,
  /* 785 */ 'i', 'm', 'u', 'l', 'l', 9, 0,
  /* 792 */ 'c', 'm', 'o', 'v', 'l', 'l', 9, 0,
  /* 800 */ 'a', 'n', 'd', 'n', 'l', 9, 0,
  /* 807 */ 'i', 'n', 'l', 9, 0,
  /* 812 */ 'c', 'm', 'o', 'v', 'n', 'o', 'l', 9, 0,
  /* 821 */ 'c', 'm', 'o', 'v', 'o', 'l', 9, 0,
  /* 829 */ 'b', 's', 'w', 'a', 'p', 'l', 9, 0,
  /* 837 */ 'p', 'd', 'e', 'p', 'l', 9, 0,
  /* 844 */ 'c', 'm', 'p', 'l', 9, 0,
  /* 850 */ 'l', 'j', 'm', 'p', 'l', 9, 0,
  /* 857 */ 'c', 'm', 'o', 'v', 'n', 'p', 'l', 9, 0,
  /* 866 */ 'n', 'o', 'p', 'l', 9, 0,
  /* 872 */ 'p', 'o', 'p', 'l', 9, 0,
  /* 878 */ 'a', 'r', 'p', 'l', 9, 0,
  /* 884 */ 'c', 'm', 'o', 'v', 'p', 'l', 9, 0,
  /* 892 */ 'l', 'a', 'r', 'l', 9, 0,
  /* 898 */ 's', 'a', 'r', 'l', 9, 0,
  /* 904 */ 'r', 'c', 'r', 'l', 9, 0,
  /* 910 */ 's', 'h', 'r', 'l', 9, 0,
  /* 916 */ 'r', 'o', 'r', 'l', 9, 0,
  /* 922 */ 'x', 'o', 'r', 'l', 9, 0,
  /* 928 */ 'b', 's', 'r', 'l', 9, 0,
  /* 934 */ 'b', 'l', 's', 'r', 'l', 9, 0,
  /* 941 */ 'b', 't', 'r', 'l', 9, 0,
  /* 947 */ 's', 't', 'r', 'l', 9, 0,
  /* 953 */ 'b', 'e', 'x', 't', 'r', 'l', 9, 0,
  /* 961 */ 's', 'c', 'a', 's', 'l', 9, 0,
  /* 968 */ 'm', 'o', 'v', 'a', 'b', 's', 'l', 9, 0,
  /* 977 */ 'l', 'd', 's', 'l', 9, 0,
  /* 983 */ 'l', 'o', 'd', 's', 'l', 9, 0,
  /* 990 */ 'l', 'e', 's', 'l', 9, 0,
  /* 996 */ 'l', 'f', 's', 'l', 9, 0,
  /* 1002 */ 'l', 'g', 's', 'l', 9, 0,
  /* 1008 */ 'c', 'm', 'o', 'v', 'n', 's', 'l', 9, 0,
  /* 1017 */ 'c', 'm', 'p', 's', 'l', 9, 0,
  /* 1024 */ 'l', 's', 's', 'l', 9, 0,
  /* 1030 */ 'b', 't', 's', 'l', 9, 0,
  /* 1036 */ 'o', 'u', 't', 's', 'l', 9, 0,
  /* 1043 */ 'c', 'm', 'o', 'v', 's', 'l', 9, 0,
  /* 1051 */ 'b', 't', 'l', 9, 0,
  /* 1056 */ 'l', 'g', 'd', 't', 'l', 9, 0,
  /* 1063 */ 's', 'g', 'd', 't', 'l', 9, 0,
  /* 1070 */ 'l', 'i', 'd', 't', 'l', 9, 0,
  /* 1077 */ 's', 'i', 'd', 't', 'l', 9, 0,
  /* 1084 */ 's', 'l', 'd', 't', 'l', 9, 0,
  /* 1091 */ 'l', 'r', 'e', 't', 'l', 9, 0,
  /* 1098 */ 's', 'e', 't', 'l', 9, 0,
  /* 1104 */ 'l', 'z', 'c', 'n', 't', 'l', 9, 0,
  /* 1112 */ 't', 'z', 'c', 'n', 't', 'l', 9, 0,
  /* 1120 */ 'n', 'o', 't', 'l', 9, 0,
  /* 1126 */ 't', 'e', 's', 't', 'l', 9, 0,
  /* 1133 */ 'p', 'e', 'x', 't', 'l', 9, 0,
  /* 1140 */ 'i', 'd', 'i', 'v', 'l', 9, 0,
  /* 1147 */ 'm', 'o', 'v', 'l', 9, 0,
  /* 1153 */ 's', 'm', 's', 'w', 'l', 9, 0,
  /* 1160 */ 'm', 'o', 'v', 's', 'w', 'l', 9, 0,
  /* 1168 */ 'm', 'o', 'v', 'z', 'w', 'l', 9, 0,
  /* 1176 */ 'a', 'd', 'c', 'x', 'l', 9, 0,
  /* 1183 */ 's', 'h', 'l', 'x', 'l', 9, 0,
  /* 1190 */ 'm', 'u', 'l', 'x', 'l', 9, 0,
  /* 1197 */ 'a', 'd', 'o', 'x', 'l', 9, 0,
  /* 1204 */ 's', 'a', 'r', 'x', 'l', 9, 0,
  /* 1211 */ 's', 'h', 'r', 'x', 'l', 9, 0,
  /* 1218 */ 'r', 'o', 'r', 'x', 'l', 9, 0,
  /* 1225 */ 'a', 'a', 'm', 9, 0,
  /* 1230 */ 'v', 'm', 'x', 'o', 'n', 9, 0,
  /* 1237 */ 'j', 'o', 9, 0,
  /* 1241 */ 'j', 'n', 'o', 9, 0,
  /* 1246 */ 's', 'e', 't', 'n', 'o', 9, 0,
  /* 1253 */ 's', 'e', 't', 'o', 9, 0,
  /* 1259 */ 'j', 'p', 9, 0,
  /* 1263 */ 'j', 'm', 'p', 9, 0,
  /* 1268 */ 'j', 'n', 'p', 9, 0,
  /* 1273 */ 's', 'e', 't', 'n', 'p', 9, 0,
  /* 1280 */ 'n', 'o', 'p', 9, 0,
  /* 1285 */ 'l', 'o', 'o', 'p', 9, 0,
  /* 1291 */ 's', 'e', 't', 'p', 9, 0,
  /* 1297 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 1313 */ 'l', 'e', 'a', 'q', 9, 0,
  /* 1319 */ 'c', 'm', 'o', 'v', 'a', 'q', 9, 0,
  /* 1327 */ 's', 'b', 'b', 'q', 9, 0,
  /* 1333 */ 'm', 'o', 'v', 's', 'b', 'q', 9, 0,
  /* 1341 */ 's', 'u', 'b', 'q', 9, 0,
  /* 1347 */ 'c', 'm', 'o', 'v', 'b', 'q', 9, 0,
  /* 1355 */ 'm', 'o', 'v', 'z', 'b', 'q', 9, 0,
  /* 1363 */ 'a', 'd', 'c', 'q', 9, 0,
  /* 1369 */ 'd', 'e', 'c', 'q', 9, 0,
  /* 1375 */ 'i', 'n', 'c', 'q', 9, 0,
  /* 1381 */ 'b', 't', 'c', 'q', 9, 0,
  /* 1387 */ 'v', 'm', 'r', 'e', 'a', 'd', 'q', 9, 0,
  /* 1396 */ 'x', 'a', 'd', 'd', 'q', 9, 0,
  /* 1403 */ 'r', 'd', 's', 'e', 'e', 'd', 'q', 9, 0,
  /* 1412 */ 's', 'h', 'l', 'd', 'q', 9, 0,
  /* 1419 */ 'r', 'd', 'r', 'a', 'n', 'd', 'q', 9, 0,
  /* 1428 */ 's', 'h', 'r', 'd', 'q', 9, 0,
  /* 1435 */ 'c', 'm', 'o', 'v', 'a', 'e', 'q', 9, 0,
  /* 1444 */ 'c', 'm', 'o', 'v', 'b', 'e', 'q', 9, 0,
  /* 1453 */ 'c', 'm', 'o', 'v', 'g', 'e', 'q', 9, 0,
  /* 1462 */ 'c', 'm', 'o', 'v', 'l', 'e', 'q', 9, 0,
  /* 1471 */ 'c', 'm', 'o', 'v', 'n', 'e', 'q', 9, 0,
  /* 1480 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1491 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1502 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1513 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1524 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 'q', 9, 0,
  /* 1534 */ 'x', 's', 'a', 'v', 'e', 'q', 9, 0,
  /* 1542 */ 'c', 'm', 'o', 'v', 'e', 'q', 9, 0,
  /* 1550 */ 'b', 's', 'f', 'q', 9, 0,
  /* 1556 */ 'n', 'e', 'g', 'q', 9, 0,
  /* 1562 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'q', 9, 0,
  /* 1572 */ 'c', 'm', 'o', 'v', 'g', 'q', 9, 0,
  /* 1580 */ 'p', 'u', 's', 'h', 'q', 9, 0,
  /* 1587 */ 'b', 'z', 'h', 'i', 'q', 9, 0,
  /* 1594 */ 'b', 'l', 's', 'i', 'q', 9, 0,
  /* 1601 */ 'b', 'l', 's', 'm', 's', 'k', 'q', 9, 0,
  /* 1610 */ 's', 'a', 'l', 'q', 9, 0,
  /* 1616 */ 'r', 'c', 'l', 'q', 9, 0,
  /* 1622 */ 's', 'h', 'l', 'q', 9, 0,
  /* 1628 */ 'c', 'a', 'l', 'l', 'q', 9, 0,
  /* 1635 */ 'r', 'o', 'l', 'q', 9, 0,
  /* 1641 */ 'l', 's', 'l', 'q', 9, 0,
  /* 1647 */ 'm', 'o', 'v', 's', 'l', 'q', 9, 0,
  /* 1655 */ 'i', 'm', 'u', 'l', 'q', 9, 0,
  /* 1662 */ 'c', 'm', 'o', 'v', 'l', 'q', 9, 0,
  /* 1670 */ 'a', 'n', 'd', 'n', 'q', 9, 0,
  /* 1677 */ 'c', 'm', 'o', 'v', 'n', 'o', 'q', 9, 0,
  /* 1686 */ 'c', 'm', 'o', 'v', 'o', 'q', 9, 0,
  /* 1694 */ 'b', 's', 'w', 'a', 'p', 'q', 9, 0,
  /* 1702 */ 'p', 'd', 'e', 'p', 'q', 9, 0,
  /* 1709 */ 'c', 'm', 'p', 'q', 9, 0,
  /* 1715 */ 'c', 'm', 'o', 'v', 'n', 'p', 'q', 9, 0,
  /* 1724 */ 'p', 'o', 'p', 'q', 9, 0,
  /* 1730 */ 'c', 'm', 'o', 'v', 'p', 'q', 9, 0,
  /* 1738 */ 'l', 'a', 'r', 'q', 9, 0,
  /* 1744 */ 's', 'a', 'r', 'q', 9, 0,
  /* 1750 */ 'r', 'c', 'r', 'q', 9, 0,
  /* 1756 */ 's', 'h', 'r', 'q', 9, 0,
  /* 1762 */ 'r', 'o', 'r', 'q', 9, 0,
  /* 1768 */ 'x', 'r', 's', 't', 'o', 'r', 'q', 9, 0,
  /* 1777 */ 'x', 'o', 'r', 'q', 9, 0,
  /* 1783 */ 'b', 's', 'r', 'q', 9, 0,
  /* 1789 */ 'b', 'l', 's', 'r', 'q', 9, 0,
  /* 1796 */ 'b', 't', 'r', 'q', 9, 0,
  /* 1802 */ 's', 't', 'r', 'q', 9, 0,
  /* 1808 */ 'b', 'e', 'x', 't', 'r', 'q', 9, 0,
  /* 1816 */ 's', 'c', 'a', 's', 'q', 9, 0,
  /* 1823 */ 'm', 'o', 'v', 'a', 'b', 's', 'q', 9, 0,
  /* 1832 */ 'l', 'o', 'd', 's', 'q', 9, 0,
  /* 1839 */ 'l', 'f', 's', 'q', 9, 0,
  /* 1845 */ 'l', 'g', 's', 'q', 9, 0,
  /* 1851 */ 'c', 'm', 'o', 'v', 'n', 's', 'q', 9, 0,
  /* 1860 */ 'c', 'm', 'p', 's', 'q', 9, 0,
  /* 1867 */ 'l', 's', 's', 'q', 9, 0,
  /* 1873 */ 'b', 't', 's', 'q', 9, 0,
  /* 1879 */ 'c', 'm', 'o', 'v', 's', 'q', 9, 0,
  /* 1887 */ 'b', 't', 'q', 9, 0,
  /* 1892 */ 'l', 'g', 'd', 't', 'q', 9, 0,
  /* 1899 */ 's', 'g', 'd', 't', 'q', 9, 0,
  /* 1906 */ 'l', 'i', 'd', 't', 'q', 9, 0,
  /* 1913 */ 's', 'i', 'd', 't', 'q', 9, 0,
  /* 1920 */ 's', 'l', 'd', 't', 'q', 9, 0,
  /* 1927 */ 'l', 'r', 'e', 't', 'q', 9, 0,
  /* 1934 */ 'l', 'z', 'c', 'n', 't', 'q', 9, 0,
  /* 1942 */ 't', 'z', 'c', 'n', 't', 'q', 9, 0,
  /* 1950 */ 'n', 'o', 't', 'q', 9, 0,
  /* 1956 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 'q', 9, 0,
  /* 1967 */ 't', 'e', 's', 't', 'q', 9, 0,
  /* 1974 */ 'p', 'e', 'x', 't', 'q', 9, 0,
  /* 1981 */ 'i', 'd', 'i', 'v', 'q', 9, 0,
  /* 1988 */ 'm', 'o', 'v', 'q', 9, 0,
  /* 1994 */ 's', 'm', 's', 'w', 'q', 9, 0,
  /* 2001 */ 'm', 'o', 'v', 's', 'w', 'q', 9, 0,
  /* 2009 */ 'm', 'o', 'v', 'z', 'w', 'q', 9, 0,
  /* 2017 */ 'a', 'd', 'c', 'x', 'q', 9, 0,
  /* 2024 */ 's', 'h', 'l', 'x', 'q', 9, 0,
  /* 2031 */ 'm', 'u', 'l', 'x', 'q', 9, 0,
  /* 2038 */ 'a', 'd', 'o', 'x', 'q', 9, 0,
  /* 2045 */ 's', 'a', 'r', 'x', 'q', 9, 0,
  /* 2052 */ 's', 'h', 'r', 'x', 'q', 9, 0,
  /* 2059 */ 'r', 'o', 'r', 'x', 'q', 9, 0,
  /* 2066 */ 'v', 'm', 'c', 'l', 'e', 'a', 'r', 9, 0,
  /* 2075 */ 'e', 'n', 't', 'e', 'r', 9, 0,
  /* 2082 */ 'x', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 2090 */ 'v', 'e', 'r', 'r', 9, 0,
  /* 2096 */ 'b', 'e', 'x', 't', 'r', 9, 0,
  /* 2103 */ 'b', 'l', 'c', 's', 9, 0,
  /* 2109 */ 'j', 's', 9, 0,
  /* 2113 */ 'j', 'n', 's', 9, 0,
  /* 2118 */ 's', 'e', 't', 'n', 's', 9, 0,
  /* 2125 */ 's', 'e', 't', 's', 9, 0,
  /* 2131 */ 'i', 'n', 't', 9, 0,
  /* 2136 */ 'i', 'n', 'v', 'e', 'p', 't', 9, 0,
  /* 2144 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 9, 0,
  /* 2154 */ 'v', 'm', 'p', 't', 'r', 's', 't', 9, 0,
  /* 2163 */ 'l', 'e', 'a', 'w', 9, 0,
  /* 2169 */ 'c', 'm', 'o', 'v', 'a', 'w', 9, 0,
  /* 2177 */ 's', 'b', 'b', 'w', 9, 0,
  /* 2183 */ 'm', 'o', 'v', 's', 'b', 'w', 9, 0,
  /* 2191 */ 's', 'u', 'b', 'w', 9, 0,
  /* 2197 */ 'c', 'm', 'o', 'v', 'b', 'w', 9, 0,
  /* 2205 */ 'm', 'o', 'v', 'z', 'b', 'w', 9, 0,
  /* 2213 */ 'a', 'd', 'c', 'w', 9, 0,
  /* 2219 */ 'd', 'e', 'c', 'w', 9, 0,
  /* 2225 */ 'i', 'n', 'c', 'w', 9, 0,
  /* 2231 */ 'b', 't', 'c', 'w', 9, 0,
  /* 2237 */ 'x', 'a', 'd', 'd', 'w', 9, 0,
  /* 2244 */ 'r', 'd', 's', 'e', 'e', 'd', 'w', 9, 0,
  /* 2253 */ 's', 'h', 'l', 'd', 'w', 9, 0,
  /* 2260 */ 'r', 'd', 'r', 'a', 'n', 'd', 'w', 9, 0,
  /* 2269 */ 's', 'h', 'r', 'd', 'w', 9, 0,
  /* 2276 */ 'c', 'm', 'o', 'v', 'a', 'e', 'w', 9, 0,
  /* 2285 */ 'c', 'm', 'o', 'v', 'b', 'e', 'w', 9, 0,
  /* 2294 */ 'c', 'm', 'o', 'v', 'g', 'e', 'w', 9, 0,
  /* 2303 */ 'c', 'm', 'o', 'v', 'l', 'e', 'w', 9, 0,
  /* 2312 */ 'c', 'm', 'o', 'v', 'n', 'e', 'w', 9, 0,
  /* 2321 */ 'c', 'm', 'o', 'v', 'e', 'w', 9, 0,
  /* 2329 */ 'b', 's', 'f', 'w', 9, 0,
  /* 2335 */ 'n', 'e', 'g', 'w', 9, 0,
  /* 2341 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'w', 9, 0,
  /* 2351 */ 'c', 'm', 'o', 'v', 'g', 'w', 9, 0,
  /* 2359 */ 'p', 'u', 's', 'h', 'w', 9, 0,
  /* 2366 */ 's', 'a', 'l', 'w', 9, 0,
  /* 2372 */ 'r', 'c', 'l', 'w', 9, 0,
  /* 2378 */ 's', 'h', 'l', 'w', 9, 0,
  /* 2384 */ 'l', 'c', 'a', 'l', 'l', 'w', 9, 0,
  /* 2392 */ 'r', 'o', 'l', 'w', 9, 0,
  /* 2398 */ 'l', 's', 'l', 'w', 9, 0,
  /* 2404 */ 'i', 'm', 'u', 'l', 'w', 9, 0,
  /* 2411 */ 'c', 'm', 'o', 'v', 'l', 'w', 9, 0,
  /* 2419 */ 'i', 'n', 'w', 9, 0,
  /* 2424 */ 'c', 'm', 'o', 'v', 'n', 'o', 'w', 9, 0,
  /* 2433 */ 'c', 'm', 'o', 'v', 'o', 'w', 9, 0,
  /* 2441 */ 'c', 'm', 'p', 'w', 9, 0,
  /* 2447 */ 'l', 'j', 'm', 'p', 'w', 9, 0,
  /* 2454 */ 'c', 'm', 'o', 'v', 'n', 'p', 'w', 9, 0,
  /* 2463 */ 'n', 'o', 'p', 'w', 9, 0,
  /* 2469 */ 'p', 'o', 'p', 'w', 9, 0,
  /* 2475 */ 'c', 'm', 'o', 'v', 'p', 'w', 9, 0,
  /* 2483 */ 'l', 'a', 'r', 'w', 9, 0,
  /* 2489 */ 's', 'a', 'r', 'w', 9, 0,
  /* 2495 */ 'r', 'c', 'r', 'w', 9, 0,
  /* 2501 */ 'v', 'e', 'r', 'w', 9, 0,
  /* 2507 */ 's', 'h', 'r', 'w', 9, 0,
  /* 2513 */ 'r', 'o', 'r', 'w', 9, 0,
  /* 2519 */ 'x', 'o', 'r', 'w', 9, 0,
  /* 2525 */ 'b', 's', 'r', 'w', 9, 0,
  /* 2531 */ 'b', 't', 'r', 'w', 9, 0,
  /* 2537 */ 'l', 't', 'r', 'w', 9, 0,
  /* 2543 */ 's', 't', 'r', 'w', 9, 0,
  /* 2549 */ 's', 'c', 'a', 's', 'w', 9, 0,
  /* 2556 */ 'm', 'o', 'v', 'a', 'b', 's', 'w', 9, 0,
  /* 2565 */ 'l', 'd', 's', 'w', 9, 0,
  /* 2571 */ 'l', 'o', 'd', 's', 'w', 9, 0,
  /* 2578 */ 'l', 'e', 's', 'w', 9, 0,
  /* 2584 */ 'l', 'f', 's', 'w', 9, 0,
  /* 2590 */ 'l', 'g', 's', 'w', 9, 0,
  /* 2596 */ 'c', 'm', 'o', 'v', 'n', 's', 'w', 9, 0,
  /* 2605 */ 'c', 'm', 'p', 's', 'w', 9, 0,
  /* 2612 */ 'l', 's', 's', 'w', 9, 0,
  /* 2618 */ 'b', 't', 's', 'w', 9, 0,
  /* 2624 */ 'o', 'u', 't', 's', 'w', 9, 0,
  /* 2631 */ 'c', 'm', 'o', 'v', 's', 'w', 9, 0,
  /* 2639 */ 'b', 't', 'w', 9, 0,
  /* 2644 */ 'l', 'g', 'd', 't', 'w', 9, 0,
  /* 2651 */ 's', 'g', 'd', 't', 'w', 9, 0,
  /* 2658 */ 'l', 'i', 'd', 't', 'w', 9, 0,
  /* 2665 */ 's', 'i', 'd', 't', 'w', 9, 0,
  /* 2672 */ 'l', 'l', 'd', 't', 'w', 9, 0,
  /* 2679 */ 's', 'l', 'd', 't', 'w', 9, 0,
  /* 2686 */ 'l', 'r', 'e', 't', 'w', 9, 0,
  /* 2693 */ 'l', 'z', 'c', 'n', 't', 'w', 9, 0,
  /* 2701 */ 't', 'z', 'c', 'n', 't', 'w', 9, 0,
  /* 2709 */ 'n', 'o', 't', 'w', 9, 0,
  /* 2715 */ 't', 'e', 's', 't', 'w', 9, 0,
  /* 2722 */ 'i', 'd', 'i', 'v', 'w', 9, 0,
  /* 2729 */ 'm', 'o', 'v', 'w', 9, 0,
  /* 2735 */ 'l', 'm', 's', 'w', 'w', 9, 0,
  /* 2742 */ 's', 'm', 's', 'w', 'w', 9, 0,
  /* 2749 */ 'j', 'e', 'c', 'x', 'z', 9, 0,
  /* 2756 */ 'j', 'c', 'x', 'z', 9, 0,
  /* 2762 */ 'j', 'r', 'c', 'x', 'z', 9, 0,
  /* 2769 */ 's', 'a', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2779 */ 'r', 'c', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2789 */ 's', 'h', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2799 */ 'r', 'o', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2809 */ 's', 'a', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2819 */ 'r', 'c', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2829 */ 's', 'h', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2839 */ 'r', 'o', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2849 */ 's', 'a', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2859 */ 'r', 'c', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2869 */ 's', 'h', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2879 */ 'r', 'o', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2889 */ 's', 'a', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 2899 */ 'r', 'c', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 2909 */ 's', 'h', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 2919 */ 'r', 'o', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 2929 */ 's', 'a', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 2939 */ 'r', 'c', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 2949 */ 's', 'h', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 2959 */ 'r', 'o', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 2969 */ 's', 'a', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 2979 */ 'r', 'c', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 2989 */ 's', 'h', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 2999 */ 'r', 'o', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 3009 */ 's', 'a', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3019 */ 'r', 'c', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3029 */ 's', 'h', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3039 */ 'r', 'o', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3049 */ 's', 'a', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3059 */ 'r', 'c', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3069 */ 's', 'h', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3079 */ 'r', 'o', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3089 */ 'm', 'o', 'v', 'a', 'b', 's', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3103 */ 's', 't', 'o', 's', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3115 */ 'o', 'u', 't', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3126 */ 'm', 'o', 'v', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3137 */ 's', 'a', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3148 */ 'r', 'c', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3159 */ 's', 'h', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3170 */ 'r', 'o', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3181 */ 's', 'a', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3192 */ 'r', 'c', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3203 */ 's', 'h', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3214 */ 'r', 'o', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3225 */ 's', 'h', 'l', 'd', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3237 */ 's', 'h', 'r', 'd', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3249 */ 's', 'a', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3260 */ 'r', 'c', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3271 */ 's', 'h', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3282 */ 'r', 'o', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3293 */ 's', 'a', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3304 */ 'r', 'c', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3315 */ 's', 'h', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3326 */ 'r', 'o', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3337 */ 's', 'h', 'l', 'd', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3349 */ 's', 'h', 'r', 'd', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3361 */ 's', 'a', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3372 */ 'r', 'c', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3383 */ 's', 'h', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3394 */ 'r', 'o', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3405 */ 's', 'a', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3416 */ 'r', 'c', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3427 */ 's', 'h', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3438 */ 'r', 'o', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3449 */ 's', 'h', 'l', 'd', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3461 */ 's', 'h', 'r', 'd', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3473 */ 's', 'a', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3484 */ 'r', 'c', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3495 */ 's', 'h', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3506 */ 'r', 'o', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3517 */ 's', 'a', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3528 */ 'r', 'c', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3539 */ 's', 'h', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3550 */ 'r', 'o', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3561 */ 'm', 'o', 'v', 'a', 'b', 's', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3575 */ 's', 't', 'o', 's', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3587 */ 'o', 'u', 't', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3598 */ 'm', 'o', 'v', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3609 */ 'm', 'o', 'v', 'a', 'b', 's', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3624 */ 's', 't', 'o', 's', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3637 */ 'o', 'u', 't', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3649 */ 'm', 'o', 'v', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3661 */ 'm', 'o', 'v', 'a', 'b', 's', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 3676 */ 's', 't', 'o', 's', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 3689 */ 'i', 'n', 's', 'b', 9, '%', 'd', 'x', ',', 32, 0,
  /* 3700 */ 'i', 'n', 's', 'l', 9, '%', 'd', 'x', ',', 32, 0,
  /* 3711 */ 'i', 'n', 's', 'w', 9, '%', 'd', 'x', ',', 32, 0,
  /* 3722 */ 'r', 'c', 'l', 'l', 9, '$', '1', 32, 0,
  /* 3731 */ '#', 'V', 'A', 'A', 'R', 'G', '_', '6', '4', 32, 0,
  /* 3742 */ 'r', 'e', 't', 9, '#', 'e', 'h', '_', 'r', 'e', 't', 'u', 'r', 'n', ',', 32, 'a', 'd', 'd', 'r', ':', 32, 0,
  /* 3765 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'X', 'M', 'M', 32, 0,
  /* 3779 */ '#', 'V', 'A', 'S', 'T', 'A', 'R', 'T', '_', 'S', 'A', 'V', 'E', '_', 'X', 'M', 'M', '_', 'R', 'E', 'G', 'S', 32, 0,
  /* 3803 */ '#', 'S', 'E', 'H', '_', 'S', 't', 'a', 'c', 'k', 'A', 'l', 'l', 'o', 'c', 32, 0,
  /* 3820 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 3836 */ '#', 'S', 'E', 'H', '_', 'S', 'e', 't', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 3851 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'R', 'e', 'g', 32, 0,
  /* 3865 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'R', 'e', 'g', 32, 0,
  /* 3879 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '3', '2', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 3899 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '1', '6', '*', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 3919 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '8', '0', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 3939 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 3959 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '1', '6', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 3980 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4000 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4020 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4039 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4059 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4079 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4099 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '2', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4119 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '4', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4139 */ '#', 'C', 'M', 'O', 'V', '_', 'V', '8', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4159 */ '#', 'C', 'M', 'O', 'V', '_', 'R', 'F', 'P', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4179 */ '#', 'C', 'M', 'O', 'V', '_', 'F', 'R', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4198 */ '#', 'C', 'M', 'O', 'V', '_', 'G', 'R', '8', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4216 */ '#', 'A', 'C', 'Q', 'U', 'I', 'R', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4237 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4258 */ 'l', 'c', 'a', 'l', 'l', 'l', 9, '*', 0,
  /* 4267 */ 'l', 'j', 'm', 'p', 'l', 9, '*', 0,
  /* 4275 */ 'l', 'c', 'a', 'l', 'l', 'q', 9, '*', 0,
  /* 4284 */ 'l', 'j', 'm', 'p', 'q', 9, '*', 0,
  /* 4292 */ 'l', 'c', 'a', 'l', 'l', 'w', 9, '*', 0,
  /* 4301 */ 'l', 'j', 'm', 'p', 'w', 9, '*', 0,
  /* 4309 */ 'x', 's', 'h', 'a', '1', 0,
  /* 4315 */ 'i', 'n', 't', '1', 0,
  /* 4320 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 4339 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 4357 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '3', '2', 0,
  /* 4370 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 4383 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 4401 */ 'u', 'd', '2', 0,
  /* 4405 */ 'i', 'n', 't', '3', 0,
  /* 4410 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 4429 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 4447 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '6', '4', 0,
  /* 4460 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 4473 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 4491 */ 'r', 'e', 'x', '6', '4', 0,
  /* 4497 */ 'd', 'a', 't', 'a', '1', '6', 0,
  /* 4504 */ 'x', 's', 'h', 'a', '2', '5', '6', 0,
  /* 4512 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 4525 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 4532 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 4542 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 0,
  /* 4560 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 0,
  /* 4576 */ '#', 'M', 'E', 'M', 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 4588 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 4603 */ 'a', 'a', 'a', 0,
  /* 4607 */ 'd', 'a', 'a', 0,
  /* 4611 */ 'u', 'd', '2', 'b', 0,
  /* 4616 */ 'x', 'c', 'r', 'y', 'p', 't', 'e', 'c', 'b', 0,
  /* 4626 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'f', 'b', 0,
  /* 4636 */ 'x', 'c', 'r', 'y', 'p', 't', 'o', 'f', 'b', 0,
  /* 4646 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'b', 0,
  /* 4656 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'b', 0,
  /* 4666 */ 'x', 'l', 'a', 't', 'b', 0,
  /* 4672 */ 'c', 'l', 'a', 'c', 0,
  /* 4677 */ 's', 't', 'a', 'c', 0,
  /* 4682 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'b', 'c', 0,
  /* 4692 */ 'g', 'e', 't', 's', 'e', 'c', 0,
  /* 4699 */ 's', 'a', 'l', 'c', 0,
  /* 4704 */ 'c', 'l', 'c', 0,
  /* 4708 */ 'c', 'm', 'c', 0,
  /* 4712 */ 'r', 'd', 'p', 'm', 'c', 0,
  /* 4718 */ 'v', 'm', 'f', 'u', 'n', 'c', 0,
  /* 4725 */ 'r', 'd', 't', 's', 'c', 0,
  /* 4731 */ 's', 't', 'c', 0,
  /* 4735 */ 'c', 'p', 'u', 'i', 'd', 0,
  /* 4741 */ 'c', 'l', 'd', 0,
  /* 4745 */ 'c', 'l', 't', 'd', 0,
  /* 4750 */ 's', 't', 'd', 0,
  /* 4754 */ 'c', 'w', 't', 'd', 0,
  /* 4759 */ 'w', 'b', 'i', 'n', 'v', 'd', 0,
  /* 4766 */ 'v', 'm', 'r', 'e', 's', 'u', 'm', 'e', 0,
  /* 4775 */ 'r', 'e', 'p', 'n', 'e', 0,
  /* 4781 */ 'x', 's', 't', 'o', 'r', 'e', 0,
  /* 4788 */ '#', 'S', 'E', 'H', '_', 'E', 'p', 'i', 'l', 'o', 'g', 'u', 'e', 0,
  /* 4802 */ '#', 'S', 'E', 'H', '_', 'E', 'n', 'd', 'P', 'r', 'o', 'l', 'o', 'g', 'u', 'e', 0,
  /* 4819 */ 'l', 'e', 'a', 'v', 'e', 0,
  /* 4825 */ 'v', 'm', 'x', 'o', 'f', 'f', 0,
  /* 4832 */ 'l', 'a', 'h', 'f', 0,
  /* 4837 */ 's', 'a', 'h', 'f', 0,
  /* 4842 */ 'v', 'm', 'l', 'a', 'u', 'n', 'c', 'h', 0,
  /* 4851 */ 'c', 'l', 'g', 'i', 0,
  /* 4856 */ 's', 't', 'g', 'i', 0,
  /* 4861 */ 'c', 'l', 'i', 0,
  /* 4865 */ 's', 't', 'i', 0,
  /* 4869 */ '#', 32, 'w', 'i', 'n', '3', '2', 32, 'f', 'p', 't', 'o', 'u', 'i', 0,
  /* 4884 */ 'l', 'o', 'c', 'k', 0,
  /* 4889 */ 'i', 'n', 'b', 9, '%', 'd', 'x', ',', 32, '%', 'a', 'l', 0,
  /* 4902 */ 'p', 'u', 's', 'h', 'a', 'l', 0,
  /* 4909 */ 'p', 'o', 'p', 'a', 'l', 0,
  /* 4915 */ 'p', 'u', 's', 'h', 'f', 'l', 0,
  /* 4922 */ 'p', 'o', 'p', 'f', 'l', 0,
  /* 4928 */ 'v', 'm', 'm', 'c', 'a', 'l', 'l', 0,
  /* 4936 */ 'v', 'm', 'c', 'a', 'l', 'l', 0,
  /* 4943 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 0,
  /* 4951 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'l', 0,
  /* 4961 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'l', 0,
  /* 4971 */ 'i', 'r', 'e', 't', 'l', 0,
  /* 4977 */ 'l', 'r', 'e', 't', 'l', 0,
  /* 4983 */ 's', 'y', 's', 'r', 'e', 't', 'l', 0,
  /* 4991 */ 's', 'y', 's', 'e', 'x', 'i', 't', 'l', 0,
  /* 5000 */ 'c', 'w', 't', 'l', 0,
  /* 5005 */ 'm', 'o', 'n', 't', 'm', 'u', 'l', 0,
  /* 5013 */ 'f', 's', 'e', 't', 'p', 'm', 0,
  /* 5020 */ 'r', 's', 'm', 0,
  /* 5024 */ '#', 32, 'd', 'y', 'n', 'a', 'm', 'i', 'c', 32, 's', 't', 'a', 'c', 'k', 32, 'a', 'l', 'l', 'o', 'c', 'a', 't', 'i', 'o', 'n', 0,
  /* 5051 */ 'i', 'n', 't', 'o', 0,
  /* 5056 */ 'c', 'q', 't', 'o', 0,
  /* 5061 */ 'r', 'd', 't', 's', 'c', 'p', 0,
  /* 5068 */ 'r', 'e', 'p', 0,
  /* 5072 */ 'n', 'o', 'p', 0,
  /* 5076 */ 'p', 'u', 's', 'h', 'f', 'q', 0,
  /* 5083 */ 'p', 'o', 'p', 'f', 'q', 0,
  /* 5089 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'q', 0,
  /* 5099 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'q', 0,
  /* 5109 */ 'i', 'r', 'e', 't', 'q', 0,
  /* 5115 */ 'l', 'r', 'e', 't', 'q', 0,
  /* 5121 */ 's', 'y', 's', 'r', 'e', 't', 'q', 0,
  /* 5129 */ 's', 'y', 's', 'e', 'x', 'i', 't', 'q', 0,
  /* 5138 */ 'c', 'l', 't', 'q', 0,
  /* 5143 */ 's', 'y', 's', 'e', 'n', 't', 'e', 'r', 0,
  /* 5152 */ 'r', 'd', 'm', 's', 'r', 0,
  /* 5158 */ 'w', 'r', 'm', 's', 'r', 0,
  /* 5164 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 't', 'r', 0,
  /* 5174 */ 'a', 'a', 's', 0,
  /* 5178 */ 'd', 'a', 's', 0,
  /* 5182 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'c', 's', 0,
  /* 5192 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'c', 's', 0,
  /* 5202 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'd', 's', 0,
  /* 5212 */ 'p', 'o', 'p', 'l', 9, '%', 'd', 's', 0,
  /* 5221 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'd', 's', 0,
  /* 5231 */ 'p', 'o', 'p', 'w', 9, '%', 'd', 's', 0,
  /* 5240 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'e', 's', 0,
  /* 5250 */ 'p', 'o', 'p', 'l', 9, '%', 'e', 's', 0,
  /* 5259 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'e', 's', 0,
  /* 5269 */ 'p', 'o', 'p', 'w', 9, '%', 'e', 's', 0,
  /* 5278 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'f', 's', 0,
  /* 5288 */ 'p', 'o', 'p', 'l', 9, '%', 'f', 's', 0,
  /* 5297 */ 'p', 'u', 's', 'h', 'q', 9, '%', 'f', 's', 0,
  /* 5307 */ 'p', 'o', 'p', 'q', 9, '%', 'f', 's', 0,
  /* 5316 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'f', 's', 0,
  /* 5326 */ 'p', 'o', 'p', 'w', 9, '%', 'f', 's', 0,
  /* 5335 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'g', 's', 0,
  /* 5345 */ 'p', 'o', 'p', 'l', 9, '%', 'g', 's', 0,
  /* 5354 */ 'p', 'u', 's', 'h', 'q', 9, '%', 'g', 's', 0,
  /* 5364 */ 'p', 'o', 'p', 'q', 9, '%', 'g', 's', 0,
  /* 5373 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'g', 's', 0,
  /* 5383 */ 'p', 'o', 'p', 'w', 9, '%', 'g', 's', 0,
  /* 5392 */ 's', 'w', 'a', 'p', 'g', 's', 0,
  /* 5399 */ '#', 32, 'v', 'a', 'r', 'i', 'a', 'b', 'l', 'e', 32, 's', 'i', 'z', 'e', 'd', 32, 'a', 'l', 'l', 'o', 'c', 'a', 32, 'f', 'o', 'r', 32, 's', 'e', 'g', 'm', 'e', 'n', 't', 'e', 'd', 32, 's', 't', 'a', 'c', 'k', 's', 0,
  /* 5444 */ 'p', 'u', 's', 'h', 'l', 9, '%', 's', 's', 0,
  /* 5454 */ 'p', 'o', 'p', 'l', 9, '%', 's', 's', 0,
  /* 5463 */ 'p', 'u', 's', 'h', 'w', 9, '%', 's', 's', 0,
  /* 5473 */ 'p', 'o', 'p', 'w', 9, '%', 's', 's', 0,
  /* 5482 */ 'c', 'l', 't', 's', 0,
  /* 5487 */ 'h', 'l', 't', 0,
  /* 5491 */ 'x', 'g', 'e', 't', 'b', 'v', 0,
  /* 5498 */ 'x', 's', 'e', 't', 'b', 'v', 0,
  /* 5505 */ 'p', 'u', 's', 'h', 'a', 'w', 0,
  /* 5512 */ 'p', 'o', 'p', 'a', 'w', 0,
  /* 5518 */ 'p', 'u', 's', 'h', 'f', 'w', 0,
  /* 5525 */ 'p', 'o', 'p', 'f', 'w', 0,
  /* 5531 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'w', 0,
  /* 5541 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'w', 0,
  /* 5551 */ 'c', 'b', 't', 'w', 0,
  /* 5556 */ 'i', 'r', 'e', 't', 'w', 0,
  /* 5562 */ 'l', 'r', 'e', 't', 'w', 0,
  /* 5568 */ 'i', 'n', 'w', 9, '%', 'd', 'x', ',', 32, '%', 'a', 'x', 0,
  /* 5581 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, '%', 'e', 'a', 'x', 0,
  /* 5593 */ 'v', 'm', 's', 'a', 'v', 'e', 9, '%', 'e', 'a', 'x', 0,
  /* 5605 */ 'v', 'm', 'r', 'u', 'n', 9, '%', 'e', 'a', 'x', 0,
  /* 5616 */ 's', 'k', 'i', 'n', 'i', 't', 9, '%', 'e', 'a', 'x', 0,
  /* 5628 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, '%', 'e', 'c', 'x', ',', 32, '%', 'e', 'a', 'x', 0,
  /* 5647 */ 'i', 'n', 'l', 9, '%', 'd', 'x', ',', 32, '%', 'e', 'a', 'x', 0,
  /* 5661 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, '%', 'r', 'a', 'x', 0,
  /* 5673 */ 'v', 'm', 's', 'a', 'v', 'e', 9, '%', 'r', 'a', 'x', 0,
  /* 5685 */ 'v', 'm', 'r', 'u', 'n', 9, '%', 'r', 'a', 'x', 0,
  /* 5696 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, '%', 'e', 'c', 'x', ',', 32, '%', 'r', 'a', 'x', 0,
  /* 5715 */ 'o', 'u', 't', 'b', 9, '%', 'a', 'l', ',', 32, '%', 'd', 'x', 0,
  /* 5729 */ 'o', 'u', 't', 'w', 9, '%', 'a', 'x', ',', 32, '%', 'd', 'x', 0,
  /* 5743 */ 'o', 'u', 't', 'l', 9, '%', 'e', 'a', 'x', ',', 32, '%', 'd', 'x', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint32_t Bits = OpInfo[MCInst_getOpcode(MI)];
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 8191)-1);
#endif

  // Fragment 0 encoded into 6 bits for 40 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 13) & 63);
  switch ((Bits >> 13) & 63) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, AAA, AAS, ACQUIRE_MOV...
    return;
    break;
  case 1:
    // AAD8i8, AAM8i8, ADC16i16, ADC32i32, ADC64i32, ADC8i8, ADD16i16, ADD32i...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADC32mi, ADC32mi8, ADC32mr, ADC64mi32, ADC...
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, ADC32rr, A...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // ADC16rm, ADD16rm, AND16rm, CMOVA16rm, CMOVAE16rm, CMOVB16rm, CMOVBE16r...
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // ADC32rm, ADCX32rm, ADD32rm, AND32rm, ANDN32rm, CMOVA32rm, CMOVAE32rm, ...
    printi32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // ADC64rm, ADCX64rm, ADD64rm, AND64rm, ANDN64rm, CMOVA64rm, CMOVAE64rm, ...
    printi64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 7:
    // ADC8rm, ADD8rm, AND8rm, OR8rm, SBB8rm, SUB8rm, XOR8rm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // ADOX32rm, BLCFILL32rm, BLCI32rm, BLCIC32rm, BLCMSK32rm, BLCS32rm, BLSF...
    printi32mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 9:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    printOperand(MI, 1, O); 
    break;
  case 10:
    // ADOX64rm, BLCFILL64rm, BLCI64rm, BLCIC64rm, BLCMSK64rm, BLCS64rm, BLSF...
    printi64mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 11:
    // BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZHI32rm, BZHI64rm, IMUL...
    printOperand(MI, 6, O); 
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // BSF16rm, BSR16rm, CMP16rm, LAR16rm, LAR32rm, LAR64rm, LEA16r, LSL16rm,...
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 13:
    // CALL16m, DEC16m, DEC64_16m, DIV16m, IDIV16m, IMUL16m, INC16m, INC64_16...
    printi16mem(MI, 0, O); 
    return;
    break;
  case 14:
    // CALL32m, DEC32m, DEC64_32m, DIV32m, IDIV32m, IMUL32m, INC32m, INC64_32...
    printi32mem(MI, 0, O); 
    break;
  case 15:
    // CALL64m, CMPXCHG8B, DEC64m, DIV64m, IDIV64m, IMUL64m, INC64m, JMP64m, ...
    printi64mem(MI, 0, O); 
    break;
  case 16:
    // CALL64pcrel32, CALLpcrel16, CALLpcrel32, EH_SjLj_Setup, JAE_1, JAE_2, ...
    printPCRelImm(MI, 0, O); 
    break;
  case 17:
    // CMP8rm, MOV8rm, MOV8rm_NOREX, MOVSX16rm8, MOVSX32rm8, MOVSX64rm8, MOVZ...
    printi8mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    break;
  case 18:
    // CMPSB, INSB, SCASB, STOSB
    printDstIdx8(MI, 0, O); 
    break;
  case 19:
    // CMPSL, INSL, SCASL, STOSL
    printDstIdx32(MI, 0, O); 
    break;
  case 20:
    // CMPSQ, SCASQ, STOSQ
    printDstIdx64(MI, 0, O); 
    break;
  case 21:
    // CMPSW, INSW, SCASW, STOSW
    printDstIdx16(MI, 0, O); 
    break;
  case 22:
    // CMPXCHG16B, LCMPXCHG16B
    printi128mem(MI, 0, O); 
    return;
    break;
  case 23:
    // DEC8m, DIV8m, IDIV8m, IMUL8m, INC8m, INVLPG, LOCK_DEC8m, LOCK_INC8m, M...
    printi8mem(MI, 0, O); 
    return;
    break;
  case 24:
    // FARCALL16m, FARCALL32m, FARCALL64, FARJMP16m, FARJMP32m, FARJMP64, LGD...
    printopaquemem(MI, 0, O); 
    return;
    break;
  case 25:
    // INVEPT32, INVEPT64, INVPCID32, INVPCID64, INVVPID32, INVVPID64
    printi128mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 26:
    // LDS16rm, LDS32rm, LES16rm, LES32rm, LFS16rm, LFS32rm, LFS64rm, LGS16rm...
    printopaquemem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 27:
    // LODSB, OUTSB
    printSrcIdx8(MI, 0, O); 
    break;
  case 28:
    // LODSL, OUTSL
    printSrcIdx32(MI, 0, O); 
    break;
  case 29:
    // LODSQ
    printSrcIdx64(MI, 0, O); 
    SStream_concat0(O, ", %rax"); 
	op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 30:
    // LODSW, OUTSW
    printSrcIdx16(MI, 0, O); 
    break;
  case 31:
    // MOV16ao16, MOV16ao16_16, MOV16o16a, MOV16o16a_16, MOV64ao16, MOV64o16a
    printMemOffs16(MI, 0, O); 
    break;
  case 32:
    // MOV32ao32, MOV32ao32_16, MOV32o32a, MOV32o32a_16, MOV64ao32, MOV64o32a
    printMemOffs32(MI, 0, O); 
    break;
  case 33:
    // MOV64ao64, MOV64o64a
    printMemOffs64(MI, 0, O); 
    break;
  case 34:
    // MOV64ao8, MOV64o8a, MOV8ao8, MOV8ao8_16, MOV8o8a, MOV8o8a_16
    printMemOffs8(MI, 0, O); 
    break;
  case 35:
    // MOVSB
    printSrcIdx8(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx8(MI, 0, O); 
    return;
    break;
  case 36:
    // MOVSL
    printSrcIdx32(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx32(MI, 0, O); 
    return;
    break;
  case 37:
    // MOVSQ
    printSrcIdx64(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx64(MI, 0, O); 
    return;
    break;
  case 38:
    // MOVSW
    printSrcIdx16(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx16(MI, 0, O); 
    return;
    break;
  case 39:
    // SHLD16rri8, SHLD32rri8, SHLD64rri8, SHRD16rri8, SHRD32rri8, SHRD64rri8
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 5 bits for 20 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 19) & 31);
  switch ((Bits >> 19) & 31) {
  default:   // unreachable.
  case 0:
    // AAD8i8, AAM8i8, BSWAP32r, BSWAP64r, CALL16r, CALL32m, CALL32r, CALL64m...
    return;
    break;
  case 1:
    // ADC16i16, ADD16i16, AND16i16, CMP16i16, IN16ri, LODSW, MOV16o16a, MOV1...
    SStream_concat0(O, ", %ax"); 
	op_addReg(MI, X86_REG_AX);
    return;
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADD16mi, ADD16mi8, ADD16mr, AND16mi, AND16...
    printi16mem(MI, 0, O); 
    return;
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // ADC16rr_REV, ADC32rr_REV, ADC64rr_REV, ADC8rr_REV, ADCX32rm, ADCX32rr,...
    printOperand(MI, 0, O); 
    return;
    break;
  case 5:
    // ADC32i32, ADD32i32, AND32i32, CMP32i32, IN32ri, LODSL, MOV32o32a, MOV3...
    SStream_concat0(O, ", %eax"); 
	op_addReg(MI, X86_REG_EAX);
    return;
    break;
  case 6:
    // ADC32mi, ADC32mi8, ADC32mr, ADD32mi, ADD32mi8, ADD32mr, AND32mi, AND32...
    printi32mem(MI, 0, O); 
    return;
    break;
  case 7:
    // ADC64i32, ADD64i32, AND64i32, CMP64i32, MOV64o64a, OR64i32, SBB64i32, ...
    SStream_concat0(O, ", %rax"); 
	op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 8:
    // ADC64mi32, ADC64mi8, ADC64mr, ADD64mi32, ADD64mi8, ADD64mr, AND64mi32,...
    printi64mem(MI, 0, O); 
    return;
    break;
  case 9:
    // ADC8i8, ADD8i8, AND8i8, CMP8i8, IN8ri, LODSB, MOV64o8a, MOV8o8a, MOV8o...
    SStream_concat0(O, ", %al"); 
	op_addReg(MI, X86_REG_AL);
    return;
    break;
  case 10:
    // ADC8mi, ADC8mr, ADD8mi, ADD8mr, AND8mi, AND8mr, CMP8mi, CMP8mr, CMPXCH...
    printi8mem(MI, 0, O); 
    break;
  case 11:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // BEXTR32rm, BEXTRI32mi, BZHI32rm, IMUL32rmi, IMUL32rmi8, RORX32mi, SARX...
    printi32mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 13:
    // BEXTR64rm, BEXTRI64mi, BZHI64rm, IMUL64rmi32, IMUL64rmi8, RORX64mi, SA...
    printi64mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 14:
    // FARCALL16i, FARCALL32i, FARJMP16i, FARJMP32i
    SStream_concat0(O, ":"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 15:
    // IMUL16rmi, IMUL16rmi8
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 16:
    // MOV8rm_NOREX
    return;
    break;
  case 17:
    // OUTSB, OUTSL, OUTSW
    SStream_concat0(O, ", %dx"); 
	op_addReg(MI, X86_REG_DX);
    return;
    break;
  case 18:
    // SHLD16mri8, SHLD32mri8, SHLD64mri8, SHRD16mri8, SHRD32mri8, SHRD64mri8
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // TAILJMPd, TAILJMPd64, TAILJMPm, TAILJMPm64, TAILJMPr64
    return;
    break;
  }


  // Fragment 2 encoded into 5 bits for 18 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 24) & 31);
  switch ((Bits >> 24) & 31) {
  default:   // unreachable.
  case 0:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    return;
    break;
  case 1:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ANDN32rm, ANDN32rr, ANDN64rm, ANDN64rr, BEXTR32rr, BEXTR64rr, BEXTRI32...
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 3:
    // CMPSB
    printSrcIdx8(MI, 1, O); 
    return;
    break;
  case 4:
    // CMPSL
    printSrcIdx32(MI, 1, O); 
    return;
    break;
  case 5:
    // CMPSQ
    printSrcIdx64(MI, 1, O); 
    return;
    break;
  case 6:
    // CMPSW
    printSrcIdx16(MI, 1, O); 
    return;
    break;
  case 7:
    // ENTER, NOOP19rr, SEH_SaveReg, SEH_SaveXMM, SEH_SetFrame, VASTART_SAVE_...
    printOperand(MI, 1, O); 
    break;
  case 8:
    // LXADD16, XCHG16rm
    printi16mem(MI, 2, O); 
    return;
    break;
  case 9:
    // LXADD32, XCHG32rm
    printi32mem(MI, 2, O); 
    return;
    break;
  case 10:
    // LXADD64, XCHG64rm
    printi64mem(MI, 2, O); 
    return;
    break;
  case 11:
    // LXADD8, XCHG8rm
    printi8mem(MI, 2, O); 
    return;
    break;
  case 12:
    // MOV8mr_NOREX
    return;
    break;
  case 13:
    // SHLD16mri8, SHRD16mri8
    printi16mem(MI, 0, O); 
    return;
    break;
  case 14:
    // SHLD32mri8, SHRD32mri8
    printi32mem(MI, 0, O); 
    return;
    break;
  case 15:
    // SHLD64mri8, SHRD64mri8
    printi64mem(MI, 0, O); 
    return;
    break;
  case 16:
    // VAARG_64
    printi8mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 6, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
    break;
  case 17:
    // XCHG16rr, XCHG32rr, XCHG64rr, XCHG8rr
    printOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 2 bits for 3 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 29) & 3);
  switch ((Bits >> 29) & 3) {
  default:   // unreachable.
  case 0:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    return;
    break;
  case 1:
    // MOV8rr_NOREX
    return;
    break;
  case 2:
    // VASTART_SAVE_XMM_REGS
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 234 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 's', 't', '(', '0', ')', 0,
  /* 6 */ 's', 't', '(', '1', ')', 0,
  /* 12 */ 's', 't', '(', '2', ')', 0,
  /* 18 */ 's', 't', '(', '3', ')', 0,
  /* 24 */ 's', 't', '(', '4', ')', 0,
  /* 30 */ 's', 't', '(', '5', ')', 0,
  /* 36 */ 's', 't', '(', '6', ')', 0,
  /* 42 */ 's', 't', '(', '7', ')', 0,
  /* 48 */ 'x', 'm', 'm', '1', '0', 0,
  /* 54 */ 'y', 'm', 'm', '1', '0', 0,
  /* 60 */ 'z', 'm', 'm', '1', '0', 0,
  /* 66 */ 'c', 'r', '1', '0', 0,
  /* 71 */ 'x', 'm', 'm', '2', '0', 0,
  /* 77 */ 'y', 'm', 'm', '2', '0', 0,
  /* 83 */ 'z', 'm', 'm', '2', '0', 0,
  /* 89 */ 'x', 'm', 'm', '3', '0', 0,
  /* 95 */ 'y', 'm', 'm', '3', '0', 0,
  /* 101 */ 'z', 'm', 'm', '3', '0', 0,
  /* 107 */ 'k', '0', 0,
  /* 110 */ 'x', 'm', 'm', '0', 0,
  /* 115 */ 'y', 'm', 'm', '0', 0,
  /* 120 */ 'z', 'm', 'm', '0', 0,
  /* 125 */ 'f', 'p', '0', 0,
  /* 129 */ 'c', 'r', '0', 0,
  /* 133 */ 'd', 'r', '0', 0,
  /* 137 */ 'x', 'm', 'm', '1', '1', 0,
  /* 143 */ 'y', 'm', 'm', '1', '1', 0,
  /* 149 */ 'z', 'm', 'm', '1', '1', 0,
  /* 155 */ 'c', 'r', '1', '1', 0,
  /* 160 */ 'x', 'm', 'm', '2', '1', 0,
  /* 166 */ 'y', 'm', 'm', '2', '1', 0,
  /* 172 */ 'z', 'm', 'm', '2', '1', 0,
  /* 178 */ 'x', 'm', 'm', '3', '1', 0,
  /* 184 */ 'y', 'm', 'm', '3', '1', 0,
  /* 190 */ 'z', 'm', 'm', '3', '1', 0,
  /* 196 */ 'k', '1', 0,
  /* 199 */ 'x', 'm', 'm', '1', 0,
  /* 204 */ 'y', 'm', 'm', '1', 0,
  /* 209 */ 'z', 'm', 'm', '1', 0,
  /* 214 */ 'f', 'p', '1', 0,
  /* 218 */ 'c', 'r', '1', 0,
  /* 222 */ 'd', 'r', '1', 0,
  /* 226 */ 'x', 'm', 'm', '1', '2', 0,
  /* 232 */ 'y', 'm', 'm', '1', '2', 0,
  /* 238 */ 'z', 'm', 'm', '1', '2', 0,
  /* 244 */ 'c', 'r', '1', '2', 0,
  /* 249 */ 'x', 'm', 'm', '2', '2', 0,
  /* 255 */ 'y', 'm', 'm', '2', '2', 0,
  /* 261 */ 'z', 'm', 'm', '2', '2', 0,
  /* 267 */ 'k', '2', 0,
  /* 270 */ 'x', 'm', 'm', '2', 0,
  /* 275 */ 'y', 'm', 'm', '2', 0,
  /* 280 */ 'z', 'm', 'm', '2', 0,
  /* 285 */ 'f', 'p', '2', 0,
  /* 289 */ 'c', 'r', '2', 0,
  /* 293 */ 'd', 'r', '2', 0,
  /* 297 */ 'x', 'm', 'm', '1', '3', 0,
  /* 303 */ 'y', 'm', 'm', '1', '3', 0,
  /* 309 */ 'z', 'm', 'm', '1', '3', 0,
  /* 315 */ 'c', 'r', '1', '3', 0,
  /* 320 */ 'x', 'm', 'm', '2', '3', 0,
  /* 326 */ 'y', 'm', 'm', '2', '3', 0,
  /* 332 */ 'z', 'm', 'm', '2', '3', 0,
  /* 338 */ 'k', '3', 0,
  /* 341 */ 'x', 'm', 'm', '3', 0,
  /* 346 */ 'y', 'm', 'm', '3', 0,
  /* 351 */ 'z', 'm', 'm', '3', 0,
  /* 356 */ 'f', 'p', '3', 0,
  /* 360 */ 'c', 'r', '3', 0,
  /* 364 */ 'd', 'r', '3', 0,
  /* 368 */ 'x', 'm', 'm', '1', '4', 0,
  /* 374 */ 'y', 'm', 'm', '1', '4', 0,
  /* 380 */ 'z', 'm', 'm', '1', '4', 0,
  /* 386 */ 'c', 'r', '1', '4', 0,
  /* 391 */ 'x', 'm', 'm', '2', '4', 0,
  /* 397 */ 'y', 'm', 'm', '2', '4', 0,
  /* 403 */ 'z', 'm', 'm', '2', '4', 0,
  /* 409 */ 'k', '4', 0,
  /* 412 */ 'x', 'm', 'm', '4', 0,
  /* 417 */ 'y', 'm', 'm', '4', 0,
  /* 422 */ 'z', 'm', 'm', '4', 0,
  /* 427 */ 'f', 'p', '4', 0,
  /* 431 */ 'c', 'r', '4', 0,
  /* 435 */ 'd', 'r', '4', 0,
  /* 439 */ 'x', 'm', 'm', '1', '5', 0,
  /* 445 */ 'y', 'm', 'm', '1', '5', 0,
  /* 451 */ 'z', 'm', 'm', '1', '5', 0,
  /* 457 */ 'c', 'r', '1', '5', 0,
  /* 462 */ 'x', 'm', 'm', '2', '5', 0,
  /* 468 */ 'y', 'm', 'm', '2', '5', 0,
  /* 474 */ 'z', 'm', 'm', '2', '5', 0,
  /* 480 */ 'k', '5', 0,
  /* 483 */ 'x', 'm', 'm', '5', 0,
  /* 488 */ 'y', 'm', 'm', '5', 0,
  /* 493 */ 'z', 'm', 'm', '5', 0,
  /* 498 */ 'f', 'p', '5', 0,
  /* 502 */ 'c', 'r', '5', 0,
  /* 506 */ 'd', 'r', '5', 0,
  /* 510 */ 'x', 'm', 'm', '1', '6', 0,
  /* 516 */ 'y', 'm', 'm', '1', '6', 0,
  /* 522 */ 'z', 'm', 'm', '1', '6', 0,
  /* 528 */ 'x', 'm', 'm', '2', '6', 0,
  /* 534 */ 'y', 'm', 'm', '2', '6', 0,
  /* 540 */ 'z', 'm', 'm', '2', '6', 0,
  /* 546 */ 'k', '6', 0,
  /* 549 */ 'x', 'm', 'm', '6', 0,
  /* 554 */ 'y', 'm', 'm', '6', 0,
  /* 559 */ 'z', 'm', 'm', '6', 0,
  /* 564 */ 'f', 'p', '6', 0,
  /* 568 */ 'c', 'r', '6', 0,
  /* 572 */ 'd', 'r', '6', 0,
  /* 576 */ 'x', 'm', 'm', '1', '7', 0,
  /* 582 */ 'y', 'm', 'm', '1', '7', 0,
  /* 588 */ 'z', 'm', 'm', '1', '7', 0,
  /* 594 */ 'x', 'm', 'm', '2', '7', 0,
  /* 600 */ 'y', 'm', 'm', '2', '7', 0,
  /* 606 */ 'z', 'm', 'm', '2', '7', 0,
  /* 612 */ 'k', '7', 0,
  /* 615 */ 'x', 'm', 'm', '7', 0,
  /* 620 */ 'y', 'm', 'm', '7', 0,
  /* 625 */ 'z', 'm', 'm', '7', 0,
  /* 630 */ 'f', 'p', '7', 0,
  /* 634 */ 'c', 'r', '7', 0,
  /* 638 */ 'd', 'r', '7', 0,
  /* 642 */ 'x', 'm', 'm', '1', '8', 0,
  /* 648 */ 'y', 'm', 'm', '1', '8', 0,
  /* 654 */ 'z', 'm', 'm', '1', '8', 0,
  /* 660 */ 'x', 'm', 'm', '2', '8', 0,
  /* 666 */ 'y', 'm', 'm', '2', '8', 0,
  /* 672 */ 'z', 'm', 'm', '2', '8', 0,
  /* 678 */ 'x', 'm', 'm', '8', 0,
  /* 683 */ 'y', 'm', 'm', '8', 0,
  /* 688 */ 'z', 'm', 'm', '8', 0,
  /* 693 */ 'c', 'r', '8', 0,
  /* 697 */ 'x', 'm', 'm', '1', '9', 0,
  /* 703 */ 'y', 'm', 'm', '1', '9', 0,
  /* 709 */ 'z', 'm', 'm', '1', '9', 0,
  /* 715 */ 'x', 'm', 'm', '2', '9', 0,
  /* 721 */ 'y', 'm', 'm', '2', '9', 0,
  /* 727 */ 'z', 'm', 'm', '2', '9', 0,
  /* 733 */ 'x', 'm', 'm', '9', 0,
  /* 738 */ 'y', 'm', 'm', '9', 0,
  /* 743 */ 'z', 'm', 'm', '9', 0,
  /* 748 */ 'c', 'r', '9', 0,
  /* 752 */ 'r', '1', '0', 'b', 0,
  /* 757 */ 'r', '1', '1', 'b', 0,
  /* 762 */ 'r', '1', '2', 'b', 0,
  /* 767 */ 'r', '1', '3', 'b', 0,
  /* 772 */ 'r', '1', '4', 'b', 0,
  /* 777 */ 'r', '1', '5', 'b', 0,
  /* 782 */ 'r', '8', 'b', 0,
  /* 786 */ 'r', '9', 'b', 0,
  /* 790 */ 'r', '1', '0', 'd', 0,
  /* 795 */ 'r', '1', '1', 'd', 0,
  /* 800 */ 'r', '1', '2', 'd', 0,
  /* 805 */ 'r', '1', '3', 'd', 0,
  /* 810 */ 'r', '1', '4', 'd', 0,
  /* 815 */ 'r', '1', '5', 'd', 0,
  /* 820 */ 'r', '8', 'd', 0,
  /* 824 */ 'r', '9', 'd', 0,
  /* 828 */ 'a', 'h', 0,
  /* 831 */ 'b', 'h', 0,
  /* 834 */ 'c', 'h', 0,
  /* 837 */ 'd', 'h', 0,
  /* 840 */ 'e', 'd', 'i', 0,
  /* 844 */ 'r', 'd', 'i', 0,
  /* 848 */ 'e', 's', 'i', 0,
  /* 852 */ 'r', 's', 'i', 0,
  /* 856 */ 'a', 'l', 0,
  /* 859 */ 'b', 'l', 0,
  /* 862 */ 'c', 'l', 0,
  /* 865 */ 'd', 'l', 0,
  /* 868 */ 'd', 'i', 'l', 0,
  /* 872 */ 's', 'i', 'l', 0,
  /* 876 */ 'b', 'p', 'l', 0,
  /* 880 */ 's', 'p', 'l', 0,
  /* 884 */ 'e', 'b', 'p', 0,
  /* 888 */ 'r', 'b', 'p', 0,
  /* 892 */ 'e', 'i', 'p', 0,
  /* 896 */ 'r', 'i', 'p', 0,
  /* 900 */ 'e', 's', 'p', 0,
  /* 904 */ 'r', 's', 'p', 0,
  /* 908 */ 'c', 's', 0,
  /* 911 */ 'd', 's', 0,
  /* 914 */ 'e', 's', 0,
  /* 917 */ 'f', 's', 0,
  /* 920 */ 'f', 'l', 'a', 'g', 's', 0,
  /* 926 */ 's', 's', 0,
  /* 929 */ 'r', '1', '0', 'w', 0,
  /* 934 */ 'r', '1', '1', 'w', 0,
  /* 939 */ 'r', '1', '2', 'w', 0,
  /* 944 */ 'r', '1', '3', 'w', 0,
  /* 949 */ 'r', '1', '4', 'w', 0,
  /* 954 */ 'r', '1', '5', 'w', 0,
  /* 959 */ 'r', '8', 'w', 0,
  /* 963 */ 'r', '9', 'w', 0,
  /* 967 */ 'f', 'p', 's', 'w', 0,
  /* 972 */ 'e', 'a', 'x', 0,
  /* 976 */ 'r', 'a', 'x', 0,
  /* 980 */ 'e', 'b', 'x', 0,
  /* 984 */ 'r', 'b', 'x', 0,
  /* 988 */ 'e', 'c', 'x', 0,
  /* 992 */ 'r', 'c', 'x', 0,
  /* 996 */ 'e', 'd', 'x', 0,
  /* 1000 */ 'r', 'd', 'x', 0,
  /* 1004 */ 'e', 'i', 'z', 0,
  /* 1008 */ 'r', 'i', 'z', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    828, 856, 973, 831, 859, 885, 876, 981, 834, 862, 908, 989, 837, 841, 
    868, 865, 911, 997, 972, 884, 980, 988, 840, 996, 920, 892, 1004, 914, 
    848, 900, 967, 917, 923, 893, 976, 888, 984, 992, 844, 1000, 896, 1008, 
    852, 904, 849, 872, 901, 880, 926, 129, 218, 289, 360, 431, 502, 568, 
    634, 693, 748, 66, 155, 244, 315, 386, 457, 133, 222, 293, 364, 435, 
    506, 572, 638, 125, 214, 285, 356, 427, 498, 564, 630, 107, 196, 267, 
    338, 409, 480, 546, 612, 111, 200, 271, 342, 413, 484, 550, 616, 694, 
    749, 67, 156, 245, 316, 387, 458, 0, 6, 12, 18, 24, 30, 36, 
    42, 110, 199, 270, 341, 412, 483, 549, 615, 678, 733, 48, 137, 226, 
    297, 368, 439, 510, 576, 642, 697, 71, 160, 249, 320, 391, 462, 528, 
    594, 660, 715, 89, 178, 115, 204, 275, 346, 417, 488, 554, 620, 683, 
    738, 54, 143, 232, 303, 374, 445, 516, 582, 648, 703, 77, 166, 255, 
    326, 397, 468, 534, 600, 666, 721, 95, 184, 120, 209, 280, 351, 422, 
    493, 559, 625, 688, 743, 60, 149, 238, 309, 380, 451, 522, 588, 654, 
    709, 83, 172, 261, 332, 403, 474, 540, 606, 672, 727, 101, 190, 782, 
    786, 752, 757, 762, 767, 772, 777, 820, 824, 790, 795, 800, 805, 810, 
    815, 959, 963, 929, 934, 939, 944, 949, 954, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case X86_AAD8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAD8i8 10)
      AsmString = "aad";
      break;
    }
    return NULL;
  case X86_AAM8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAM8i8 10)
      AsmString = "aam";
      break;
    }
    return NULL;
  case X86_XSTORE:
    if (MCInst_getNumOperands(MI) == 0) {
      // (XSTORE)
      AsmString = "xstorerng";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
