# CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_ATT
0x0f,0x0f,0xca,0xbf = pavgusb %mm2, %mm1
0x67,0x0f,0x0f,0x5c,0x16,0x09,0xbf = pavgusb 9(%esi,%edx), %mm3
0x0f,0x0f,0xca,0x1d = pf2id %mm2, %mm1
0x67,0x0f,0x0f,0x5c,0x16,0x09,0x1d = pf2id 9(%esi,%edx), %mm3
0x0f,0x0f,0xca,0xae = pfacc %mm2, %mm1
0x0f,0x0f,0xca,0x9e = pfadd %mm2, %mm1
0x0f,0x0f,0xca,0xb0 = pfcmpeq %mm2, %mm1
0x0f,0x0f,0xca,0x90 = pfcmpge %mm2, %mm1
0x0f,0x0f,0xca,0xa0 = pfcmpgt %mm2, %mm1
0x0f,0x0f,0xca,0xa4 = pfmax %mm2, %mm1
0x0f,0x0f,0xca,0x94 = pfmin %mm2, %mm1
0x0f,0x0f,0xca,0xb4 = pfmul %mm2, %mm1
0x0f,0x0f,0xca,0x96 = pfrcp %mm2, %mm1
0x0f,0x0f,0xca,0xa6 = pfrcpit1 %mm2, %mm1
0x0f,0x0f,0xca,0xb6 = pfrcpit2 %mm2, %mm1
0x0f,0x0f,0xca,0xa7 = pfrsqit1 %mm2, %mm1
0x0f,0x0f,0xca,0x97 = pfrsqrt %mm2, %mm1
0x0f,0x0f,0xca,0x9a = pfsub %mm2, %mm1
0x0f,0x0f,0xca,0xaa = pfsubr %mm2, %mm1
0x0f,0x0f,0xca,0x0d = pi2fd %mm2, %mm1
0x0f,0x0f,0xca,0xb7 = pmulhrw %mm2, %mm1
0x0f,0x0e = femms 
0x0f,0x0d,0x00 = prefetch (%eax)
0x0f,0x0f,0xca,0x1c = pf2iw %mm2, %mm1
0x0f,0x0f,0xca,0x0c = pi2fw %mm2, %mm1
0x0f,0x0f,0xca,0x8a = pfnacc %mm2, %mm1
0x0f,0x0f,0xca,0x8e = pfpnacc %mm2, %mm1
0x0f,0x0f,0xca,0xbb = pswapd %mm2, %mm1
