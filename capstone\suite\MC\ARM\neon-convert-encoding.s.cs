# CS_ARCH_ARM, CS_MODE_ARM, None
0x20,0x07,0xfb,0xf3 = vcvt.s32.f32 d16, d16
0xa0,0x07,0xfb,0xf3 = vcvt.u32.f32 d16, d16
0x20,0x06,0xfb,0xf3 = vcvt.f32.s32 d16, d16
0xa0,0x06,0xfb,0xf3 = vcvt.f32.u32 d16, d16
0x60,0x07,0xfb,0xf3 = vcvt.s32.f32 q8, q8
0xe0,0x07,0xfb,0xf3 = vcvt.u32.f32 q8, q8
0x60,0x06,0xfb,0xf3 = vcvt.f32.s32 q8, q8
0xe0,0x06,0xfb,0xf3 = vcvt.f32.u32 q8, q8
0x30,0x0f,0xff,0xf2 = vcvt.s32.f32 d16, d16, #1
0x20,0x07,0xfb,0xf3 = vcvt.s32.f32 d16, d16
0x30,0x0f,0xff,0xf3 = vcvt.u32.f32 d16, d16, #1
0xa0,0x07,0xfb,0xf3 = vcvt.u32.f32 d16, d16
0x30,0x0e,0xff,0xf2 = vcvt.f32.s32 d16, d16, #1
0x20,0x06,0xfb,0xf3 = vcvt.f32.s32 d16, d16
0x30,0x0e,0xff,0xf3 = vcvt.f32.u32 d16, d16, #1
0xa0,0x06,0xfb,0xf3 = vcvt.f32.u32 d16, d16
0x70,0x0f,0xff,0xf2 = vcvt.s32.f32 q8, q8, #1
0x60,0x07,0xfb,0xf3 = vcvt.s32.f32 q8, q8
0x70,0x0f,0xff,0xf3 = vcvt.u32.f32 q8, q8, #1
0xe0,0x07,0xfb,0xf3 = vcvt.u32.f32 q8, q8
0x70,0x0e,0xff,0xf2 = vcvt.f32.s32 q8, q8, #1
0x60,0x06,0xfb,0xf3 = vcvt.f32.s32 q8, q8
0x70,0x0e,0xff,0xf3 = vcvt.f32.u32 q8, q8, #1
0xe0,0x06,0xfb,0xf3 = vcvt.f32.u32 q8, q8
0x20,0x07,0xf6,0xf3 = vcvt.f32.f16 q8, d16
0x20,0x06,0xf6,0xf3 = vcvt.f16.f32 d16, q8
