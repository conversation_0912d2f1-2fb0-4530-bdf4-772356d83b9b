// Copyright (c) 2025, DdiMon EPT Hook Project. All rights reserved.
// Stub declarations for DdiMon functions used by HyperPlatform

#pragma once

#include "common.h"

// Forward declarations for DdiMon functions
EXTERN_C NTSTATUS DdimonHandleEptHookCheck(
    _In_ void* InputBuffer,
    _In_ ULONG InputBufferLength,
    _Out_ void* OutputBuffer,
    _In_ ULONG OutputBufferLength,
    _Out_ PULONG BytesReturned
);

EXTERN_C NTSTATUS DdimonHandleEptHookCreate(
    _In_ void* InputBuffer,
    _In_ ULONG InputBufferLength,
    _Out_ void* OutputBuffer,
    _In_ ULONG OutputBufferLength,
    _Out_ PULONG BytesReturned
);

EXTERN_C BOOLEAN DdimonpHandleDynamicEptViolation(
    _In_ struct _EptData* ept_data,
    _In_ void* fault_va
);

EXTERN_C NTSTATUS DdimonHandleEptReadMemory(
    _In_ void* InputBuffer,
    _In_ ULONG InputBufferLength,
    _Out_ void* OutputBuffer,
    _In_ ULONG OutputBufferLength,
    _Out_ PULONG BytesReturned
);

EXTERN_C NTSTATUS DdimonHandleEptWriteMemory(
    _In_ void* InputBuffer,
    _In_ ULONG InputBufferLength,
    _Out_ void* OutputBuffer,
    _In_ ULONG OutputBufferLength,
    _Out_ PULONG BytesReturned
);
