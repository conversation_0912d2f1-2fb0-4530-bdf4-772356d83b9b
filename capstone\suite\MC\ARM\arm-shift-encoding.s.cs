# CS_ARCH_ARM, CS_MODE_ARM, None
0x00,0x00,0x90,0xe7 = ldr r0, [r0, r0]
0x20,0x00,0x90,0xe7 = ldr r0, [r0, r0, lsr #32]
0x20,0x08,0x90,0xe7 = ldr r0, [r0, r0, lsr #16]
0x00,0x00,0x90,0xe7 = ldr r0, [r0, r0]
0x00,0x08,0x90,0xe7 = ldr r0, [r0, r0, lsl #16]
0x40,0x00,0x90,0xe7 = ldr r0, [r0, r0, asr #32]
0x40,0x08,0x90,0xe7 = ldr r0, [r0, r0, asr #16]
0x60,0x00,0x90,0xe7 = ldr r0, [r0, r0, rrx]
0x60,0x08,0x90,0xe7 = ldr r0, [r0, r0, ror #16]
0x00,0xf0,0xd0,0xf7 = pld [r0, r0]
0x20,0xf0,0xd0,0xf7 = pld [r0, r0, lsr #32]
0x20,0xf8,0xd0,0xf7 = pld [r0, r0, lsr #16]
0x00,0xf0,0xd0,0xf7 = pld [r0, r0]
0x00,0xf8,0xd0,0xf7 = pld [r0, r0, lsl #16]
0x40,0xf0,0xd0,0xf7 = pld [r0, r0, asr #32]
0x40,0xf8,0xd0,0xf7 = pld [r0, r0, asr #16]
0x60,0xf0,0xd0,0xf7 = pld [r0, r0, rrx]
0x60,0xf8,0xd0,0xf7 = pld [r0, r0, ror #16]
0x00,0x00,0x80,0xe7 = str r0, [r0, r0]
0x20,0x00,0x80,0xe7 = str r0, [r0, r0, lsr #32]
0x20,0x08,0x80,0xe7 = str r0, [r0, r0, lsr #16]
0x00,0x00,0x80,0xe7 = str r0, [r0, r0]
0x00,0x08,0x80,0xe7 = str r0, [r0, r0, lsl #16]
0x40,0x00,0x80,0xe7 = str r0, [r0, r0, asr #32]
0x40,0x08,0x80,0xe7 = str r0, [r0, r0, asr #16]
0x60,0x00,0x80,0xe7 = str r0, [r0, r0, rrx]
0x60,0x08,0x80,0xe7 = str r0, [r0, r0, ror #16]
0x62,0x00,0x91,0xe6 = ldr r0, [r1], r2, rrx
0x05,0x30,0x94,0xe6 = ldr r3, [r4], r5
0x08,0x60,0x87,0xe6 = str r6, [r7], r8
0x0b,0x90,0x8a,0xe6 = str r9, [r10], r11
0x0f,0xd0,0xae,0xe0 = adc sp, lr, pc
0x29,0x10,0xa8,0xe0 = adc r1, r8, r9, lsr #32
0x2f,0x28,0xa7,0xe0 = adc r2, r7, pc, lsr #16
0x0a,0x30,0xa6,0xe0 = adc r3, r6, r10
0x0e,0x48,0xa5,0xe0 = adc r4, r5, lr, lsl #16
0x4b,0x50,0xa4,0xe0 = adc r5, r4, r11, asr #32
0x4d,0x68,0xa3,0xe0 = adc r6, r3, sp, asr #16
0x6c,0x70,0xa2,0xe0 = adc r7, r2, r12, rrx
0x60,0x88,0xa1,0xe0 = adc r8, r1, r0, ror #16
0x0e,0x00,0x5d,0xe1 = cmp sp, lr
0x28,0x00,0x51,0xe1 = cmp r1, r8, lsr #32
0x27,0x08,0x52,0xe1 = cmp r2, r7, lsr #16
0x06,0x00,0x53,0xe1 = cmp r3, r6
0x05,0x08,0x54,0xe1 = cmp r4, r5, lsl #16
0x44,0x00,0x55,0xe1 = cmp r5, r4, asr #32
0x43,0x08,0x56,0xe1 = cmp r6, r3, asr #16
0x62,0x00,0x57,0xe1 = cmp r7, r2, rrx
0x61,0x08,0x58,0xe1 = cmp r8, r1, ror #16
