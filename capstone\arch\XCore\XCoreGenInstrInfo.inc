/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    XCore_PHI	= 0,
    XCore_INLINEASM	= 1,
    XCore_CFI_INSTRUCTION	= 2,
    XCore_EH_LABEL	= 3,
    XCore_GC_LABEL	= 4,
    XCore_KILL	= 5,
    XCore_EXTRACT_SUBREG	= 6,
    XCore_INSERT_SUBREG	= 7,
    XCore_IMPLICIT_DEF	= 8,
    XCore_SUBREG_TO_REG	= 9,
    XCore_COPY_TO_REGCLASS	= 10,
    XCore_DBG_VALUE	= 11,
    XCore_REG_SEQUENCE	= 12,
    XCore_COPY	= 13,
    XCore_BUNDLE	= 14,
    XCore_LIFETIME_START	= 15,
    XCore_LIFETIME_END	= 16,
    XCore_STACKMAP	= 17,
    XCore_PATCHPOINT	= 18,
    XCore_LOAD_STACK_GUARD	= 19,
    XCore_ADD_2rus	= 20,
    XCore_ADD_3r	= 21,
    XCore_ADJCALLSTACKDOWN	= 22,
    XCore_ADJCALLSTACKUP	= 23,
    XCore_ANDNOT_2r	= 24,
    XCore_AND_3r	= 25,
    XCore_ASHR_l2rus	= 26,
    XCore_ASHR_l3r	= 27,
    XCore_BAU_1r	= 28,
    XCore_BITREV_l2r	= 29,
    XCore_BLACP_lu10	= 30,
    XCore_BLACP_u10	= 31,
    XCore_BLAT_lu6	= 32,
    XCore_BLAT_u6	= 33,
    XCore_BLA_1r	= 34,
    XCore_BLRB_lu10	= 35,
    XCore_BLRB_u10	= 36,
    XCore_BLRF_lu10	= 37,
    XCore_BLRF_u10	= 38,
    XCore_BRBF_lru6	= 39,
    XCore_BRBF_ru6	= 40,
    XCore_BRBT_lru6	= 41,
    XCore_BRBT_ru6	= 42,
    XCore_BRBU_lu6	= 43,
    XCore_BRBU_u6	= 44,
    XCore_BRFF_lru6	= 45,
    XCore_BRFF_ru6	= 46,
    XCore_BRFT_lru6	= 47,
    XCore_BRFT_ru6	= 48,
    XCore_BRFU_lu6	= 49,
    XCore_BRFU_u6	= 50,
    XCore_BRU_1r	= 51,
    XCore_BR_JT	= 52,
    XCore_BR_JT32	= 53,
    XCore_BYTEREV_l2r	= 54,
    XCore_CHKCT_2r	= 55,
    XCore_CHKCT_rus	= 56,
    XCore_CLRE_0R	= 57,
    XCore_CLRPT_1R	= 58,
    XCore_CLRSR_branch_lu6	= 59,
    XCore_CLRSR_branch_u6	= 60,
    XCore_CLRSR_lu6	= 61,
    XCore_CLRSR_u6	= 62,
    XCore_CLZ_l2r	= 63,
    XCore_CRC8_l4r	= 64,
    XCore_CRC_l3r	= 65,
    XCore_DCALL_0R	= 66,
    XCore_DENTSP_0R	= 67,
    XCore_DGETREG_1r	= 68,
    XCore_DIVS_l3r	= 69,
    XCore_DIVU_l3r	= 70,
    XCore_DRESTSP_0R	= 71,
    XCore_DRET_0R	= 72,
    XCore_ECALLF_1r	= 73,
    XCore_ECALLT_1r	= 74,
    XCore_EDU_1r	= 75,
    XCore_EEF_2r	= 76,
    XCore_EET_2r	= 77,
    XCore_EEU_1r	= 78,
    XCore_EH_RETURN	= 79,
    XCore_ENDIN_2r	= 80,
    XCore_ENTSP_lu6	= 81,
    XCore_ENTSP_u6	= 82,
    XCore_EQ_2rus	= 83,
    XCore_EQ_3r	= 84,
    XCore_EXTDP_lu6	= 85,
    XCore_EXTDP_u6	= 86,
    XCore_EXTSP_lu6	= 87,
    XCore_EXTSP_u6	= 88,
    XCore_FRAME_TO_ARGS_OFFSET	= 89,
    XCore_FREER_1r	= 90,
    XCore_FREET_0R	= 91,
    XCore_GETD_l2r	= 92,
    XCore_GETED_0R	= 93,
    XCore_GETET_0R	= 94,
    XCore_GETID_0R	= 95,
    XCore_GETKEP_0R	= 96,
    XCore_GETKSP_0R	= 97,
    XCore_GETN_l2r	= 98,
    XCore_GETPS_l2r	= 99,
    XCore_GETR_rus	= 100,
    XCore_GETSR_lu6	= 101,
    XCore_GETSR_u6	= 102,
    XCore_GETST_2r	= 103,
    XCore_GETTS_2r	= 104,
    XCore_INCT_2r	= 105,
    XCore_INITCP_2r	= 106,
    XCore_INITDP_2r	= 107,
    XCore_INITLR_l2r	= 108,
    XCore_INITPC_2r	= 109,
    XCore_INITSP_2r	= 110,
    XCore_INPW_l2rus	= 111,
    XCore_INSHR_2r	= 112,
    XCore_INT_2r	= 113,
    XCore_IN_2r	= 114,
    XCore_Int_MemBarrier	= 115,
    XCore_KCALL_1r	= 116,
    XCore_KCALL_lu6	= 117,
    XCore_KCALL_u6	= 118,
    XCore_KENTSP_lu6	= 119,
    XCore_KENTSP_u6	= 120,
    XCore_KRESTSP_lu6	= 121,
    XCore_KRESTSP_u6	= 122,
    XCore_KRET_0R	= 123,
    XCore_LADD_l5r	= 124,
    XCore_LD16S_3r	= 125,
    XCore_LD8U_3r	= 126,
    XCore_LDA16B_l3r	= 127,
    XCore_LDA16F_l3r	= 128,
    XCore_LDAPB_lu10	= 129,
    XCore_LDAPB_u10	= 130,
    XCore_LDAPF_lu10	= 131,
    XCore_LDAPF_lu10_ba	= 132,
    XCore_LDAPF_u10	= 133,
    XCore_LDAWB_l2rus	= 134,
    XCore_LDAWB_l3r	= 135,
    XCore_LDAWCP_lu6	= 136,
    XCore_LDAWCP_u6	= 137,
    XCore_LDAWDP_lru6	= 138,
    XCore_LDAWDP_ru6	= 139,
    XCore_LDAWFI	= 140,
    XCore_LDAWF_l2rus	= 141,
    XCore_LDAWF_l3r	= 142,
    XCore_LDAWSP_lru6	= 143,
    XCore_LDAWSP_ru6	= 144,
    XCore_LDC_lru6	= 145,
    XCore_LDC_ru6	= 146,
    XCore_LDET_0R	= 147,
    XCore_LDIVU_l5r	= 148,
    XCore_LDSED_0R	= 149,
    XCore_LDSPC_0R	= 150,
    XCore_LDSSR_0R	= 151,
    XCore_LDWCP_lru6	= 152,
    XCore_LDWCP_lu10	= 153,
    XCore_LDWCP_ru6	= 154,
    XCore_LDWCP_u10	= 155,
    XCore_LDWDP_lru6	= 156,
    XCore_LDWDP_ru6	= 157,
    XCore_LDWFI	= 158,
    XCore_LDWSP_lru6	= 159,
    XCore_LDWSP_ru6	= 160,
    XCore_LDW_2rus	= 161,
    XCore_LDW_3r	= 162,
    XCore_LMUL_l6r	= 163,
    XCore_LSS_3r	= 164,
    XCore_LSUB_l5r	= 165,
    XCore_LSU_3r	= 166,
    XCore_MACCS_l4r	= 167,
    XCore_MACCU_l4r	= 168,
    XCore_MJOIN_1r	= 169,
    XCore_MKMSK_2r	= 170,
    XCore_MKMSK_rus	= 171,
    XCore_MSYNC_1r	= 172,
    XCore_MUL_l3r	= 173,
    XCore_NEG	= 174,
    XCore_NOT	= 175,
    XCore_OR_3r	= 176,
    XCore_OUTCT_2r	= 177,
    XCore_OUTCT_rus	= 178,
    XCore_OUTPW_l2rus	= 179,
    XCore_OUTSHR_2r	= 180,
    XCore_OUTT_2r	= 181,
    XCore_OUT_2r	= 182,
    XCore_PEEK_2r	= 183,
    XCore_REMS_l3r	= 184,
    XCore_REMU_l3r	= 185,
    XCore_RETSP_lu6	= 186,
    XCore_RETSP_u6	= 187,
    XCore_SELECT_CC	= 188,
    XCore_SETCLK_l2r	= 189,
    XCore_SETCP_1r	= 190,
    XCore_SETC_l2r	= 191,
    XCore_SETC_lru6	= 192,
    XCore_SETC_ru6	= 193,
    XCore_SETDP_1r	= 194,
    XCore_SETD_2r	= 195,
    XCore_SETEV_1r	= 196,
    XCore_SETKEP_0R	= 197,
    XCore_SETN_l2r	= 198,
    XCore_SETPSC_2r	= 199,
    XCore_SETPS_l2r	= 200,
    XCore_SETPT_2r	= 201,
    XCore_SETRDY_l2r	= 202,
    XCore_SETSP_1r	= 203,
    XCore_SETSR_branch_lu6	= 204,
    XCore_SETSR_branch_u6	= 205,
    XCore_SETSR_lu6	= 206,
    XCore_SETSR_u6	= 207,
    XCore_SETTW_l2r	= 208,
    XCore_SETV_1r	= 209,
    XCore_SEXT_2r	= 210,
    XCore_SEXT_rus	= 211,
    XCore_SHL_2rus	= 212,
    XCore_SHL_3r	= 213,
    XCore_SHR_2rus	= 214,
    XCore_SHR_3r	= 215,
    XCore_SSYNC_0r	= 216,
    XCore_ST16_l3r	= 217,
    XCore_ST8_l3r	= 218,
    XCore_STET_0R	= 219,
    XCore_STSED_0R	= 220,
    XCore_STSPC_0R	= 221,
    XCore_STSSR_0R	= 222,
    XCore_STWDP_lru6	= 223,
    XCore_STWDP_ru6	= 224,
    XCore_STWFI	= 225,
    XCore_STWSP_lru6	= 226,
    XCore_STWSP_ru6	= 227,
    XCore_STW_2rus	= 228,
    XCore_STW_l3r	= 229,
    XCore_SUB_2rus	= 230,
    XCore_SUB_3r	= 231,
    XCore_SYNCR_1r	= 232,
    XCore_TESTCT_2r	= 233,
    XCore_TESTLCL_l2r	= 234,
    XCore_TESTWCT_2r	= 235,
    XCore_TSETMR_2r	= 236,
    XCore_TSETR_3r	= 237,
    XCore_TSTART_1R	= 238,
    XCore_WAITEF_1R	= 239,
    XCore_WAITET_1R	= 240,
    XCore_WAITEU_0R	= 241,
    XCore_XOR_l3r	= 242,
    XCore_ZEXT_2r	= 243,
    XCore_ZEXT_rus	= 244,
    XCore_INSTRUCTION_LIST_END = 245
};

#endif // GET_INSTRINFO_ENUM
