﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Driver Files">
      <UniqueIdentifier>{8E41214B-6785-4CFE-B992-037D68949A14}</UniqueIdentifier>
      <Extensions>inf;inv;inx;mof;mc;</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ddi_mon.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\driver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\ept.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\log.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\performance.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\util.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\vm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\vmm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\kernel_stl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="shadow_hook.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\global_object.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\hotplug_callback.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\power_callback.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ddi_mon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\asm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\driver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\ept.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\ia32_type.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\perf_counter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\performance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\vm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\vmm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="shadow_hook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\global_object.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\hotplug_callback.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\power_callback.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <MASM Include="..\HyperPlatform\HyperPlatform\Arch\x64\x64.asm">
      <Filter>Source Files</Filter>
    </MASM>
    <MASM Include="..\HyperPlatform\HyperPlatform\Arch\x86\x86.asm">
      <Filter>Source Files</Filter>
    </MASM>
  </ItemGroup>
</Project>