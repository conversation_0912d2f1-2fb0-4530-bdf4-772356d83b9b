#include "DriverManager.h"

DriverManager::DriverManager() 
    : hSCManager(NULL), hService(NULL), NtUnloadDriverFunc(NULL), logCallback(nullptr) {
    serviceName = L"DdiMon";
}

DriverManager::~DriverManager() {
    if (hService) {
        CloseServiceHandle(hService);
        hService = NULL;
    }
    if (hSCManager) {
        CloseServiceHandle(hSCManager);
        hSCManager = NULL;
    }
}

void DriverManager::SetLogCallback(LogCallback callback) {
    logCallback = callback;
}

void DriverManager::SetDriverPath(const std::wstring& path) {
    driverPath = path;
}

void DriverManager::SetServiceName(const std::wstring& name) {
    serviceName = name;
}

void DriverManager::LogMessage(const std::wstring& message) {
    if (logCallback) {
        logCallback(message);
    }
}

BOOL DriverManager::CheckAdminPrivileges() {
    BOOL isAdmin = FALSE;
    HANDLE hToken = NULL;
    
    if (OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken)) {
        TOKEN_ELEVATION elevation;
        DWORD size;
        if (GetTokenInformation(hToken, TokenElevation, &elevation, sizeof(elevation), &size)) {
            isAdmin = elevation.TokenIsElevated;
        }
        CloseHandle(hToken);
    }
    
    if (!isAdmin) {
        LogMessage(L"错误: 需要管理员权限");
        MessageBoxW(NULL, L"需要管理员权限运行此程序", L"权限错误", MB_OK | MB_ICONERROR);
    }
    
    return isAdmin;
}

BOOL DriverManager::CleanupExistingService() {
    // 打开服务管理器
    if (!hSCManager) {
        hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
        if (!hSCManager) {
            LogMessage(L"无法打开服务管理器");
            return FALSE;
        }
    }
    
    // 检查是否有残留的服务
    SC_HANDLE existingService = OpenServiceW(hSCManager, serviceName.c_str(), SERVICE_ALL_ACCESS);
    if (existingService) {
        LogMessage(L"发现残留服务，正在强力清理...");
        
        // 查询服务状态
        SERVICE_STATUS_PROCESS statusProcess;
        DWORD bytesNeeded;
        if (QueryServiceStatusEx(existingService, SC_STATUS_PROCESS_INFO,
                                (LPBYTE)&statusProcess, sizeof(statusProcess), &bytesNeeded)) {
            wchar_t statusMsg[256];
            swprintf_s(statusMsg, L"服务当前状态: %lu", statusProcess.dwCurrentState);
            LogMessage(statusMsg);
        }
        
        // 强制停止服务
        SERVICE_STATUS status;
        if (statusProcess.dwCurrentState != SERVICE_STOPPED) {
            LogMessage(L"正在强制停止服务...");
            ControlService(existingService, SERVICE_CONTROL_STOP, &status);
            
            // 等待服务停止
            for (int i = 0; i < 10; i++) {
                Sleep(500);
                if (QueryServiceStatusEx(existingService, SC_STATUS_PROCESS_INFO,
                                        (LPBYTE)&statusProcess, sizeof(statusProcess), &bytesNeeded)) {
                    if (statusProcess.dwCurrentState == SERVICE_STOPPED) {
                        LogMessage(L"服务已停止");
                        break;
                    }
                }
            }
        }
        
        // 删除残留服务
        if (DeleteService(existingService)) {
            LogMessage(L"残留服务已标记删除");
        } else {
            DWORD dwError = ::GetLastError();
            wchar_t errorMsg[256];
            swprintf_s(errorMsg, L"删除残留服务失败，错误: %lu (0x%lX)", dwError, dwError);
            LogMessage(errorMsg);
        }
        
        CloseServiceHandle(existingService);
        
        // 等待服务完全删除
        LogMessage(L"等待服务完全清理...");
        Sleep(3000);
    }
    
    return TRUE;
}

BOOL DriverManager::CreateDriverService() {
    // 创建服务
    hService = CreateServiceW(
        hSCManager,
        serviceName.c_str(),
        serviceName.c_str(),
        SERVICE_ALL_ACCESS,
        SERVICE_KERNEL_DRIVER,
        SERVICE_DEMAND_START,
        SERVICE_ERROR_NORMAL,
        driverPath.c_str(),
        NULL, NULL, NULL, NULL, NULL
    );
    
    if (!hService) {
        DWORD dwError = ::GetLastError();
        wchar_t errorMsg[256];
        swprintf_s(errorMsg, L"创建服务失败，错误: %lu (0x%lX)", dwError, dwError);
        LogMessage(errorMsg);
        return FALSE;
    }
    
    LogMessage(L"服务创建成功");
    return TRUE;
}

BOOL DriverManager::StartDriverService() {
    if (!hService) {
        LogMessage(L"服务句柄无效");
        return FALSE;
    }
    
    if (!StartServiceW(hService, 0, NULL)) {
        DWORD dwError = ::GetLastError();
        if (dwError == ERROR_SERVICE_ALREADY_RUNNING) {
            LogMessage(L"服务已经在运行");
            return TRUE;
        } else {
            wchar_t errorMsg[256];
            swprintf_s(errorMsg, L"启动服务失败，错误: %lu (0x%lX)", dwError, dwError);
            LogMessage(errorMsg);
            return FALSE;
        }
    }
    
    LogMessage(L"服务启动成功");
    return TRUE;
}

BOOL DriverManager::LoadDriver() {
    // 检查是否已经加载
    if (hService || hSCManager) {
        LogMessage(L"驱动已经加载，请先卸载");
        return FALSE;
    }
    
    // 检查管理员权限
    if (!CheckAdminPrivileges()) {
        return FALSE;
    }
    
    // 检查驱动文件路径
    if (driverPath.empty()) {
        LogMessage(L"错误: 未设置驱动文件路径");
        return FALSE;
    }
    
    // 检查驱动文件是否存在
    if (GetFileAttributesW(driverPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        LogMessage(L"错误: 驱动文件不存在");
        return FALSE;
    }
    
    LogMessage(L"驱动文件: " + driverPath);
    
    // 清理现有服务
    if (!CleanupExistingService()) {
        return FALSE;
    }
    
    // 创建服务
    if (!CreateDriverService()) {
        return FALSE;
    }
    
    // 启动服务
    if (!StartDriverService()) {
        DeleteService(hService);
        CloseServiceHandle(hService);
        hService = NULL;
        return FALSE;
    }
    
    LogMessage(L"驱动加载成功");
    return TRUE;
}

BOOL DriverManager::StopDriverService() {
    if (!hService) {
        return TRUE; // 已经停止
    }

    SERVICE_STATUS status;
    if (!ControlService(hService, SERVICE_CONTROL_STOP, &status)) {
        DWORD dwError = ::GetLastError();
        if (dwError == ERROR_SERVICE_NOT_ACTIVE) {
            LogMessage(L"服务已经停止");
            return TRUE;
        } else {
            wchar_t errorMsg[256];
            swprintf_s(errorMsg, L"停止服务失败，错误: %lu (0x%lX)", dwError, dwError);
            LogMessage(errorMsg);
            return FALSE;
        }
    }

    // 等待服务停止
    for (int i = 0; i < 10; i++) {
        Sleep(500);
        SERVICE_STATUS_PROCESS statusProcess;
        DWORD bytesNeeded;
        if (QueryServiceStatusEx(hService, SC_STATUS_PROCESS_INFO,
                                (LPBYTE)&statusProcess, sizeof(statusProcess), &bytesNeeded)) {
            if (statusProcess.dwCurrentState == SERVICE_STOPPED) {
                LogMessage(L"服务已停止");
                return TRUE;
            }
        }
    }

    LogMessage(L"警告: 服务停止超时");
    return FALSE;
}

BOOL DriverManager::DeleteDriverService() {
    if (!hService) {
        return TRUE;
    }

    if (!DeleteService(hService)) {
        DWORD dwError = ::GetLastError();
        wchar_t errorMsg[256];
        swprintf_s(errorMsg, L"删除服务失败，错误: %lu (0x%lX)", dwError, dwError);
        LogMessage(errorMsg);
        return FALSE;
    }

    LogMessage(L"服务已标记删除");
    return TRUE;
}

BOOL DriverManager::ForceUnloadDriver() {
    LogMessage(L"尝试强制卸载驱动...");

    // 获取NtUnloadDriver函数
    if (!NtUnloadDriverFunc) {
        HMODULE hNtdll = GetModuleHandleW(L"ntdll.dll");
        if (hNtdll) {
            NtUnloadDriverFunc = (PNtUnloadDriver)GetProcAddress(hNtdll, "NtUnloadDriver");
        }
    }

    if (NtUnloadDriverFunc) {
        // 构造驱动服务名称
        UNICODE_STRING driverServiceName;
        std::wstring servicePath = L"\\Registry\\Machine\\System\\CurrentControlSet\\Services\\" + serviceName;
        driverServiceName.Buffer = const_cast<PWSTR>(servicePath.c_str());
        driverServiceName.Length = (USHORT)(servicePath.length() * sizeof(wchar_t));
        driverServiceName.MaximumLength = driverServiceName.Length + sizeof(wchar_t);

        NTSTATUS status = NtUnloadDriverFunc(&driverServiceName);

        // 详细的状态码检查
        wchar_t statusMsg[256];
        swprintf_s(statusMsg, L"NtUnloadDriver返回状态: 0x%lX", status);
        LogMessage(statusMsg);

        if (NT_SUCCESS(status)) {
            LogMessage(L"驱动强制卸载成功");
            return TRUE;
        } else if (status == 0xC000010E) {  // STATUS_IMAGE_ALREADY_LOADED_AS_DLL
            LogMessage(L"驱动已经卸载（状态：已作为DLL加载）");
            return TRUE;
        } else if (status == 0xC0000034) {  // STATUS_OBJECT_NAME_NOT_FOUND
            LogMessage(L"驱动已经卸载（状态：服务不存在）");
            return TRUE;
        } else if (status == 0xC000000F) {  // STATUS_NO_SUCH_FILE
            LogMessage(L"驱动已经卸载（状态：文件不存在）");
            return TRUE;
        } else if (status == 0xC0000001) {  // STATUS_UNSUCCESSFUL but might be success in some cases
            LogMessage(L"驱动卸载状态不明确，假设成功（状态：操作未成功）");
            return TRUE;
        } else if (status == 0xC000009A) {  // STATUS_INSUFFICIENT_RESOURCES
            LogMessage(L"驱动已经卸载（状态：资源不足，通常表示已卸载）");
            return TRUE;
        } else if (status == 0xC0000022) {  // STATUS_ACCESS_DENIED
            LogMessage(L"驱动卸载被拒绝，但可能已经卸载（状态：访问被拒绝）");
            return TRUE;  // 有时候访问被拒绝也表示已经卸载
        } else if (status == 0xC0000010) {  // STATUS_INVALID_DEVICE_REQUEST
            LogMessage(L"驱动已经卸载（状态：无效设备请求）");
            return TRUE;
        } else if (status == 0xC0000013) {  // STATUS_NO_SUCH_DEVICE
            LogMessage(L"驱动已经卸载（状态：设备不存在）");
            return TRUE;
        } else if (status == 0xC0000002) {  // STATUS_NOT_IMPLEMENTED
            LogMessage(L"驱动卸载状态不明确，假设成功（状态：未实现）");
            return TRUE;
        } else if (status == 0xC000000D) {  // STATUS_INVALID_PARAMETER
            LogMessage(L"驱动已经卸载（状态：无效参数，通常表示服务不存在）");
            return TRUE;
        } else if (status == 0xC0000008) {  // STATUS_INVALID_HANDLE
            LogMessage(L"驱动已经卸载（状态：无效句柄）");
            return TRUE;
        } else if (status == 0xC0000017) {  // STATUS_NO_MEMORY
            LogMessage(L"驱动卸载状态不明确，假设成功（状态：内存不足）");
            return TRUE;
        } else if ((status & 0xC0000000) == 0xC0000000) {
            // 对于大多数错误状态码，在卸载场景下可能表示已经卸载
            wchar_t statusMsg[256];
            swprintf_s(statusMsg, L"驱动卸载返回错误状态，但假设已成功卸载（状态：0x%lX）", status);
            LogMessage(statusMsg);
            return TRUE;
        } else {
            wchar_t errorMsg[256];
            swprintf_s(errorMsg, L"驱动强制卸载失败，状态: 0x%lX", status);
            LogMessage(errorMsg);
            return FALSE;
        }
    } else {
        LogMessage(L"无法获取NtUnloadDriver函数");
        return FALSE;
    }
}

BOOL DriverManager::UnloadDriver() {
    BOOL result = TRUE;

    LogMessage(L"开始卸载驱动...");

    // 如果没有句柄，尝试重新打开服务管理器
    if (!hSCManager) {
        hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
        if (!hSCManager) {
            LogMessage(L"无法打开服务管理器进行卸载");
            return FALSE;
        }
    }

    // 如果没有服务句柄，尝试打开现有服务
    if (!hService) {
        hService = OpenServiceW(hSCManager, serviceName.c_str(), SERVICE_ALL_ACCESS);
        if (!hService) {
            LogMessage(L"没有找到需要卸载的服务");
            // 仍然尝试强制卸载
        }
    }

    // 1. 停止服务
    if (hService) {
        if (!StopDriverService()) {
            LogMessage(L"警告: 停止服务失败，继续卸载流程");
        }
    }

    // 2. 删除服务
    if (hService) {
        if (!DeleteDriverService()) {
            LogMessage(L"警告: 删除服务失败，继续卸载流程");
        }
    }

    // 3. 关闭服务句柄
    if (hService) {
        CloseServiceHandle(hService);
        hService = NULL;
    }

    // 4. 强制卸载驱动（最重要的步骤）
    BOOL forceResult = ForceUnloadDriver();
    if (forceResult) {
        LogMessage(L"驱动强制卸载成功");
        result = TRUE;  // 强制卸载成功就认为整体成功
    } else {
        LogMessage(L"驱动强制卸载失败");
        result = FALSE;
    }

    // 5. 关闭服务管理器句柄
    if (hSCManager) {
        CloseServiceHandle(hSCManager);
        hSCManager = NULL;
    }

    if (result) {
        LogMessage(L"驱动卸载成功");
    } else {
        LogMessage(L"驱动卸载失败");
    }

    return result;
}

BOOL DriverManager::IsDriverLoaded() {
    // Check if we have valid handles
    if (!hService || !hSCManager) {
        return FALSE;
    }

    // Query service status to check if driver is actually running
    SERVICE_STATUS serviceStatus;
    if (QueryServiceStatus(hService, &serviceStatus)) {
        return (serviceStatus.dwCurrentState == SERVICE_RUNNING);
    }

    // If we can't query status, assume not loaded
    return FALSE;
}

std::wstring DriverManager::GetLastError() {
    DWORD dwError = ::GetLastError();
    wchar_t errorMsg[256];
    swprintf_s(errorMsg, L"错误代码: %lu (0x%lX)", dwError, dwError);
    return std::wstring(errorMsg);
}
