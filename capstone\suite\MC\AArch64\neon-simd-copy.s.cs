# CS_ARCH_ARM64, 0, None
0x22,0x1c,0x05,0x4e = ins v2.b[2], w1
0xc7,0x1d,0x1e,0x4e = ins v7.h[7], w14
0xd4,0x1f,0x04,0x4e = ins v20.s[0], w30
0xe1,0x1c,0x18,0x4e = ins v1.d[1], x7
0x22,0x1c,0x05,0x4e = ins v2.b[2], w1
0xc7,0x1d,0x1e,0x4e = ins v7.h[7], w14
0xd4,0x1f,0x04,0x4e = ins v20.s[0], w30
0xe1,0x1c,0x18,0x4e = ins v1.d[1], x7
0x01,0x2c,0x1f,0x0e = smov w1, v0.b[15]
0xce,0x2c,0x12,0x0e = smov w14, v6.h[4]
0x01,0x2c,0x1f,0x4e = smov x1, v0.b[15]
0xce,0x2c,0x12,0x4e = smov x14, v6.h[4]
0x34,0x2d,0x14,0x4e = smov x20, v9.s[2]
0x01,0x3c,0x1f,0x0e = umov w1, v0.b[15]
0xce,0x3c,0x12,0x0e = umov w14, v6.h[4]
0x34,0x3d,0x14,0x0e = umov w20, v9.s[2]
0x47,0x3e,0x18,0x4e = umov x7, v18.d[1]
0x34,0x3d,0x14,0x0e = umov w20, v9.s[2]
0x47,0x3e,0x18,0x4e = umov x7, v18.d[1]
0x61,0x34,0x1d,0x6e = ins v1.b[14], v3.b[6]
0xe6,0x54,0x1e,0x6e = ins v6.h[7], v7.h[5]
0xcf,0x46,0x1c,0x6e = ins v15.s[3], v22.s[2]
0x80,0x44,0x08,0x6e = ins v0.d[0], v4.d[1]
0x61,0x34,0x1d,0x6e = ins v1.b[14], v3.b[6]
0xe6,0x54,0x1e,0x6e = ins v6.h[7], v7.h[5]
0xcf,0x46,0x1c,0x6e = ins v15.s[3], v22.s[2]
0x80,0x44,0x08,0x6e = ins v0.d[0], v4.d[1]
0x41,0x04,0x05,0x0e = dup v1.8b, v2.b[2]
0xeb,0x04,0x1e,0x0e = dup v11.4h, v7.h[7]
0x91,0x06,0x04,0x0e = dup v17.2s, v20.s[0]
0x41,0x04,0x05,0x4e = dup v1.16b, v2.b[2]
0xeb,0x04,0x1e,0x4e = dup v11.8h, v7.h[7]
0x91,0x06,0x04,0x4e = dup v17.4s, v20.s[0]
0x25,0x04,0x18,0x4e = dup v5.2d, v1.d[1]
0x21,0x0c,0x01,0x0e = dup v1.8b, w1
0xcb,0x0d,0x02,0x0e = dup v11.4h, w14
0xd1,0x0f,0x04,0x0e = dup v17.2s, w30
0x41,0x0c,0x01,0x4e = dup v1.16b, w2
0x0b,0x0e,0x02,0x4e = dup v11.8h, w16
0x91,0x0f,0x04,0x4e = dup v17.4s, w28
0x05,0x0c,0x08,0x4e = dup v5.2d, x0
