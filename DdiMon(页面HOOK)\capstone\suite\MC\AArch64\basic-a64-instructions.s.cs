# CS_ARCH_ARM64, 0, None
0x82,0x00,0x25,0x8b = add x2, x4, w5, uxtb
0xf4,0x23,0x33,0x8b = add x20, sp, w19, uxth
0x2c,0x40,0x34,0x8b = add x12, x1, w20, uxtw
0x74,0x60,0x2d,0x8b = add x20, x3, x13, uxtx
0x31,0x83,0x34,0x8b = add x17, x25, w20, sxtb
0xb2,0xa1,0x33,0x8b = add x18, x13, w19, sxth
0x5f,0xc0,0x23,0x8b = add sp, x2, w3, sxtw
0xa3,0xe0,0x29,0x8b = add x3, x5, x9, sxtx
0xa2,0x00,0x27,0x0b = add w2, w5, w7, uxtb
0xf5,0x21,0x31,0x0b = add w21, w15, w17, uxth
0xbe,0x43,0x3f,0x0b = add w30, w29, wzr, uxtw
0x33,0x62,0x21,0x0b = add w19, w17, w1, uxtx
0xa2,0x80,0x21,0x0b = add w2, w5, w1, sxtb
0x3a,0xa2,0x33,0x0b = add w26, w17, w19, sxth
0x40,0xc0,0x23,0x0b = add w0, w2, w3, sxtw
0x62,0xe0,0x25,0x0b = add w2, w3, w5, sxtx
0x62,0x80,0x25,0x8b = add x2, x3, w5, sxtb
0x67,0x31,0x2d,0x8b = add x7, x11, w13, uxth #4
0x71,0x4a,0x37,0x0b = add w17, w19, w23, uxtw #2
0xfd,0x66,0x31,0x0b = add w29, w23, w17, uxtx #1
0x82,0x08,0x25,0xcb = sub x2, x4, w5, uxtb #2
0xf4,0x33,0x33,0xcb = sub x20, sp, w19, uxth #4
0x2c,0x40,0x34,0xcb = sub x12, x1, w20, uxtw
0x74,0x60,0x2d,0xcb = sub x20, x3, x13, uxtx
0x31,0x83,0x34,0xcb = sub x17, x25, w20, sxtb
0xb2,0xa1,0x33,0xcb = sub x18, x13, w19, sxth
0x5f,0xc0,0x23,0xcb = sub sp, x2, w3, sxtw
0xa3,0xe0,0x29,0xcb = sub x3, x5, x9, sxtx
0xa2,0x00,0x27,0x4b = sub w2, w5, w7, uxtb
0xf5,0x21,0x31,0x4b = sub w21, w15, w17, uxth
0xbe,0x43,0x3f,0x4b = sub w30, w29, wzr, uxtw
0x33,0x62,0x21,0x4b = sub w19, w17, w1, uxtx
0xa2,0x80,0x21,0x4b = sub w2, w5, w1, sxtb
0xfa,0xa3,0x33,0x4b = sub w26, wsp, w19, sxth
0x5f,0xc0,0x23,0x4b = sub wsp, w2, w3, sxtw
0x62,0xe0,0x25,0x4b = sub w2, w3, w5, sxtx
0x82,0x08,0x25,0xab = adds x2, x4, w5, uxtb #2
0xf4,0x33,0x33,0xab = adds x20, sp, w19, uxth #4
0x2c,0x40,0x34,0xab = adds x12, x1, w20, uxtw
0x74,0x60,0x2d,0xab = adds x20, x3, x13, uxtx
0x3f,0x8f,0x34,0xab = adds xzr, x25, w20, sxtb #3
0xf2,0xa3,0x33,0xab = adds x18, sp, w19, sxth
0x5f,0xc0,0x23,0xab = adds xzr, x2, w3, sxtw
0xa3,0xe8,0x29,0xab = adds x3, x5, x9, sxtx #2
0xa2,0x00,0x27,0x2b = adds w2, w5, w7, uxtb
0xf5,0x21,0x31,0x2b = adds w21, w15, w17, uxth
0xbe,0x43,0x3f,0x2b = adds w30, w29, wzr, uxtw
0x33,0x62,0x21,0x2b = adds w19, w17, w1, uxtx
0xa2,0x84,0x21,0x2b = adds w2, w5, w1, sxtb #1
0xfa,0xa3,0x33,0x2b = adds w26, wsp, w19, sxth
0x5f,0xc0,0x23,0x2b = adds wzr, w2, w3, sxtw
0x62,0xe0,0x25,0x2b = adds w2, w3, w5, sxtx
0x82,0x08,0x25,0xeb = subs x2, x4, w5, uxtb #2
0xf4,0x33,0x33,0xeb = subs x20, sp, w19, uxth #4
0x2c,0x40,0x34,0xeb = subs x12, x1, w20, uxtw
0x74,0x60,0x2d,0xeb = subs x20, x3, x13, uxtx
0x3f,0x8f,0x34,0xeb = subs xzr, x25, w20, sxtb #3
0xf2,0xa3,0x33,0xeb = subs x18, sp, w19, sxth
0x5f,0xc0,0x23,0xeb = subs xzr, x2, w3, sxtw
0xa3,0xe8,0x29,0xeb = subs x3, x5, x9, sxtx #2
0xa2,0x00,0x27,0x6b = subs w2, w5, w7, uxtb
0xf5,0x21,0x31,0x6b = subs w21, w15, w17, uxth
0xbe,0x43,0x3f,0x6b = subs w30, w29, wzr, uxtw
0x33,0x62,0x21,0x6b = subs w19, w17, w1, uxtx
0xa2,0x84,0x21,0x6b = subs w2, w5, w1, sxtb #1
0xfa,0xa3,0x33,0x6b = subs w26, wsp, w19, sxth
0x5f,0xc0,0x23,0x6b = subs wzr, w2, w3, sxtw
0x62,0xe0,0x25,0x6b = subs w2, w3, w5, sxtx
0x9f,0x08,0x25,0xeb = cmp x4, w5, uxtb #2
0xff,0x33,0x33,0xeb = cmp sp, w19, uxth #4
0x3f,0x40,0x34,0xeb = cmp x1, w20, uxtw
0x7f,0x60,0x2d,0xeb = cmp x3, x13, uxtx
0x3f,0x8f,0x34,0xeb = cmp x25, w20, sxtb #3
0xff,0xa3,0x33,0xeb = cmp sp, w19, sxth
0x5f,0xc0,0x23,0xeb = cmp x2, w3, sxtw
0xbf,0xe8,0x29,0xeb = cmp x5, x9, sxtx #2
0xbf,0x00,0x27,0x6b = cmp w5, w7, uxtb
0xff,0x21,0x31,0x6b = cmp w15, w17, uxth
0xbf,0x43,0x3f,0x6b = cmp w29, wzr, uxtw
0x3f,0x62,0x21,0x6b = cmp w17, w1, uxtx
0xbf,0x84,0x21,0x6b = cmp w5, w1, sxtb #1
0xff,0xa3,0x33,0x6b = cmp wsp, w19, sxth
0x5f,0xc0,0x23,0x6b = cmp w2, w3, sxtw
0x7f,0xe0,0x25,0x6b = cmp w3, w5, sxtx
0x9f,0x08,0x25,0xab = cmn x4, w5, uxtb #2
0xff,0x33,0x33,0xab = cmn sp, w19, uxth #4
0x3f,0x40,0x34,0xab = cmn x1, w20, uxtw
0x7f,0x60,0x2d,0xab = cmn x3, x13, uxtx
0x3f,0x8f,0x34,0xab = cmn x25, w20, sxtb #3
0xff,0xa3,0x33,0xab = cmn sp, w19, sxth
0x5f,0xc0,0x23,0xab = cmn x2, w3, sxtw
0xbf,0xe8,0x29,0xab = cmn x5, x9, sxtx #2
0xbf,0x00,0x27,0x2b = cmn w5, w7, uxtb
0xff,0x21,0x31,0x2b = cmn w15, w17, uxth
0xbf,0x43,0x3f,0x2b = cmn w29, wzr, uxtw
0x3f,0x62,0x21,0x2b = cmn w17, w1, uxtx
0xbf,0x84,0x21,0x2b = cmn w5, w1, sxtb #1
0xff,0xa3,0x33,0x2b = cmn wsp, w19, sxth
0x5f,0xc0,0x23,0x2b = cmn w2, w3, sxtw
0x7f,0xe0,0x25,0x2b = cmn w3, w5, sxtx
0x9f,0x0e,0x3d,0xeb = cmp x20, w29, uxtb #3
0x9f,0x71,0x2d,0xeb = cmp x12, x13, uxtx #4
0xff,0x03,0x21,0x6b = cmp wsp, w1, uxtb
0xff,0xc3,0x3f,0x2b = cmn wsp, wzr, sxtw
0x7f,0x70,0x27,0xcb = sub sp, x3, x7, lsl #4
0xe2,0x47,0x23,0x0b = add w2, wsp, w3, lsl #1
0xff,0x43,0x29,0x6b = cmp wsp, w9
0xff,0x53,0x23,0x2b = adds wzr, wsp, w3, lsl #4
0xe3,0x6b,0x29,0xeb = subs x3, sp, x9, lsl #2
0xa4,0x00,0x00,0x11 = add w4, w5, #0
0x62,0xfc,0x3f,0x11 = add w2, w3, #4095
0xbe,0x07,0x40,0x11 = add w30, w29, #1, lsl #12
0xad,0xfc,0x7f,0x11 = add w13, w5, #4095, lsl #12
0xe5,0x98,0x19,0x91 = add x5, x7, #1638
0xf4,0x87,0x0c,0x11 = add w20, wsp, #801
0xff,0x43,0x11,0x11 = add wsp, wsp, #1104
0xdf,0xd3,0x3f,0x11 = add wsp, w30, #4084
0x00,0x8f,0x04,0x91 = add x0, x24, #291
0x03,0xff,0x7f,0x91 = add x3, x24, #4095, lsl #12
0xe8,0xcb,0x10,0x91 = add x8, sp, #1074
0xbf,0xa3,0x3b,0x91 = add sp, x29, #3816
0xe0,0xb7,0x3f,0x51 = sub w0, wsp, #4077
0x84,0x8a,0x48,0x51 = sub w4, w20, #546, lsl #12
0xff,0x83,0x04,0xd1 = sub sp, sp, #288
0x7f,0x42,0x00,0x51 = sub wsp, w19, #16
0xed,0x8e,0x44,0x31 = adds w13, w23, #291, lsl #12
0x5f,0xfc,0x3f,0x31 = adds wzr, w2, #4095
0xf4,0x03,0x00,0x31 = adds w20, wsp, #0
0x7f,0x04,0x40,0xb1 = adds xzr, x3, #1, lsl #12
0xff,0x53,0x40,0xf1 = subs xzr, sp, #20, lsl #12
0xdf,0xff,0x3f,0xf1 = subs xzr, x30, #4095
0xe4,0xbb,0x3b,0xf1 = subs x4, sp, #3822
0x7f,0x8c,0x44,0x31 = cmn w3, #291, lsl #12
0xff,0x57,0x15,0x31 = cmn wsp, #1365
0xff,0x13,0x51,0xb1 = cmn sp, #1092, lsl #12
0x9f,0xb0,0x44,0xf1 = cmp x4, #300, lsl #12
0xff,0xd3,0x07,0x71 = cmp wsp, #500
0xff,0x23,0x03,0xf1 = cmp sp, #200
0xdf,0x03,0x00,0x91 = mov sp, x30
0x9f,0x02,0x00,0x11 = mov wsp, w20
0xeb,0x03,0x00,0x91 = mov x11, sp
0xf8,0x03,0x00,0x11 = mov w24, wsp
0xa3,0x00,0x07,0x0b = add w3, w5, w7
0x7f,0x00,0x05,0x0b = add wzr, w3, w5
0xf4,0x03,0x04,0x0b = add w20, wzr, w4
0xc4,0x00,0x1f,0x0b = add w4, w6, wzr
0xab,0x01,0x0f,0x0b = add w11, w13, w15
0x69,0x28,0x1f,0x0b = add w9, w3, wzr, lsl #10
0xb1,0x7f,0x14,0x0b = add w17, w29, w20, lsl #31
0xd5,0x02,0x57,0x0b = add w21, w22, w23, lsr #0
0x38,0x4b,0x5a,0x0b = add w24, w25, w26, lsr #18
0x9b,0x7f,0x5d,0x0b = add w27, w28, w29, lsr #31
0x62,0x00,0x84,0x0b = add w2, w3, w4, asr #0
0xc5,0x54,0x87,0x0b = add w5, w6, w7, asr #21
0x28,0x7d,0x8a,0x0b = add w8, w9, w10, asr #31
0xa3,0x00,0x07,0x8b = add x3, x5, x7
0x7f,0x00,0x05,0x8b = add xzr, x3, x5
0xf4,0x03,0x04,0x8b = add x20, xzr, x4
0xc4,0x00,0x1f,0x8b = add x4, x6, xzr
0xab,0x01,0x0f,0x8b = add x11, x13, x15
0x69,0x28,0x1f,0x8b = add x9, x3, xzr, lsl #10
0xb1,0xff,0x14,0x8b = add x17, x29, x20, lsl #63
0xd5,0x02,0x57,0x8b = add x21, x22, x23, lsr #0
0x38,0x4b,0x5a,0x8b = add x24, x25, x26, lsr #18
0x9b,0xff,0x5d,0x8b = add x27, x28, x29, lsr #63
0x62,0x00,0x84,0x8b = add x2, x3, x4, asr #0
0xc5,0x54,0x87,0x8b = add x5, x6, x7, asr #21
0x28,0xfd,0x8a,0x8b = add x8, x9, x10, asr #63
0xa3,0x00,0x07,0x2b = adds w3, w5, w7
0x7f,0x00,0x05,0x2b = adds wzr, w3, w5
0xf4,0x03,0x04,0x2b = adds w20, wzr, w4
0xc4,0x00,0x1f,0x2b = adds w4, w6, wzr
0xab,0x01,0x0f,0x2b = adds w11, w13, w15
0x69,0x28,0x1f,0x2b = adds w9, w3, wzr, lsl #10
0xb1,0x7f,0x14,0x2b = adds w17, w29, w20, lsl #31
0xd5,0x02,0x57,0x2b = adds w21, w22, w23, lsr #0
0x38,0x4b,0x5a,0x2b = adds w24, w25, w26, lsr #18
0x9b,0x7f,0x5d,0x2b = adds w27, w28, w29, lsr #31
0x62,0x00,0x84,0x2b = adds w2, w3, w4, asr #0
0xc5,0x54,0x87,0x2b = adds w5, w6, w7, asr #21
0x28,0x7d,0x8a,0x2b = adds w8, w9, w10, asr #31
0xa3,0x00,0x07,0xab = adds x3, x5, x7
0x7f,0x00,0x05,0xab = adds xzr, x3, x5
0xf4,0x03,0x04,0xab = adds x20, xzr, x4
0xc4,0x00,0x1f,0xab = adds x4, x6, xzr
0xab,0x01,0x0f,0xab = adds x11, x13, x15
0x69,0x28,0x1f,0xab = adds x9, x3, xzr, lsl #10
0xb1,0xff,0x14,0xab = adds x17, x29, x20, lsl #63
0xd5,0x02,0x57,0xab = adds x21, x22, x23, lsr #0
0x38,0x4b,0x5a,0xab = adds x24, x25, x26, lsr #18
0x9b,0xff,0x5d,0xab = adds x27, x28, x29, lsr #63
0x62,0x00,0x84,0xab = adds x2, x3, x4, asr #0
0xc5,0x54,0x87,0xab = adds x5, x6, x7, asr #21
0x28,0xfd,0x8a,0xab = adds x8, x9, x10, asr #63
0xa3,0x00,0x07,0x4b = sub w3, w5, w7
0x7f,0x00,0x05,0x4b = sub wzr, w3, w5
0xf4,0x03,0x04,0x4b = sub w20, wzr, w4
0xc4,0x00,0x1f,0x4b = sub w4, w6, wzr
0xab,0x01,0x0f,0x4b = sub w11, w13, w15
0x69,0x28,0x1f,0x4b = sub w9, w3, wzr, lsl #10
0xb1,0x7f,0x14,0x4b = sub w17, w29, w20, lsl #31
0xd5,0x02,0x57,0x4b = sub w21, w22, w23, lsr #0
0x38,0x4b,0x5a,0x4b = sub w24, w25, w26, lsr #18
0x9b,0x7f,0x5d,0x4b = sub w27, w28, w29, lsr #31
0x62,0x00,0x84,0x4b = sub w2, w3, w4, asr #0
0xc5,0x54,0x87,0x4b = sub w5, w6, w7, asr #21
0x28,0x7d,0x8a,0x4b = sub w8, w9, w10, asr #31
0xa3,0x00,0x07,0xcb = sub x3, x5, x7
0x7f,0x00,0x05,0xcb = sub xzr, x3, x5
0xf4,0x03,0x04,0xcb = sub x20, xzr, x4
0xc4,0x00,0x1f,0xcb = sub x4, x6, xzr
0xab,0x01,0x0f,0xcb = sub x11, x13, x15
0x69,0x28,0x1f,0xcb = sub x9, x3, xzr, lsl #10
0xb1,0xff,0x14,0xcb = sub x17, x29, x20, lsl #63
0xd5,0x02,0x57,0xcb = sub x21, x22, x23, lsr #0
0x38,0x4b,0x5a,0xcb = sub x24, x25, x26, lsr #18
0x9b,0xff,0x5d,0xcb = sub x27, x28, x29, lsr #63
0x62,0x00,0x84,0xcb = sub x2, x3, x4, asr #0
0xc5,0x54,0x87,0xcb = sub x5, x6, x7, asr #21
0x28,0xfd,0x8a,0xcb = sub x8, x9, x10, asr #63
0xa3,0x00,0x07,0x6b = subs w3, w5, w7
0x7f,0x00,0x05,0x6b = subs wzr, w3, w5
0xf4,0x03,0x04,0x6b = subs w20, wzr, w4
0xc4,0x00,0x1f,0x6b = subs w4, w6, wzr
0xab,0x01,0x0f,0x6b = subs w11, w13, w15
0x69,0x28,0x1f,0x6b = subs w9, w3, wzr, lsl #10
0xb1,0x7f,0x14,0x6b = subs w17, w29, w20, lsl #31
0xd5,0x02,0x57,0x6b = subs w21, w22, w23, lsr #0
0x38,0x4b,0x5a,0x6b = subs w24, w25, w26, lsr #18
0x9b,0x7f,0x5d,0x6b = subs w27, w28, w29, lsr #31
0x62,0x00,0x84,0x6b = subs w2, w3, w4, asr #0
0xc5,0x54,0x87,0x6b = subs w5, w6, w7, asr #21
0x28,0x7d,0x8a,0x6b = subs w8, w9, w10, asr #31
0xa3,0x00,0x07,0xeb = subs x3, x5, x7
0x7f,0x00,0x05,0xeb = subs xzr, x3, x5
0xf4,0x03,0x04,0xeb = subs x20, xzr, x4
0xc4,0x00,0x1f,0xeb = subs x4, x6, xzr
0xab,0x01,0x0f,0xeb = subs x11, x13, x15
0x69,0x28,0x1f,0xeb = subs x9, x3, xzr, lsl #10
0xb1,0xff,0x14,0xeb = subs x17, x29, x20, lsl #63
0xd5,0x02,0x57,0xeb = subs x21, x22, x23, lsr #0
0x38,0x4b,0x5a,0xeb = subs x24, x25, x26, lsr #18
0x9b,0xff,0x5d,0xeb = subs x27, x28, x29, lsr #63
0x62,0x00,0x84,0xeb = subs x2, x3, x4, asr #0
0xc5,0x54,0x87,0xeb = subs x5, x6, x7, asr #21
0x28,0xfd,0x8a,0xeb = subs x8, x9, x10, asr #63
0x1f,0x00,0x03,0x2b = cmn w0, w3
0xff,0x03,0x04,0x2b = cmn wzr, w4
0xbf,0x00,0x1f,0x2b = cmn w5, wzr
0xdf,0x00,0x07,0x2b = cmn w6, w7
0x1f,0x3d,0x09,0x2b = cmn w8, w9, lsl #15
0x5f,0x7d,0x0b,0x2b = cmn w10, w11, lsl #31
0x9f,0x01,0x4d,0x2b = cmn w12, w13, lsr #0
0xdf,0x55,0x4f,0x2b = cmn w14, w15, lsr #21
0x1f,0x7e,0x51,0x2b = cmn w16, w17, lsr #31
0x5f,0x02,0x93,0x2b = cmn w18, w19, asr #0
0x9f,0x5a,0x95,0x2b = cmn w20, w21, asr #22
0xdf,0x7e,0x97,0x2b = cmn w22, w23, asr #31
0x1f,0x00,0x03,0xab = cmn x0, x3
0xff,0x03,0x04,0xab = cmn xzr, x4
0xbf,0x00,0x1f,0xab = cmn x5, xzr
0xdf,0x00,0x07,0xab = cmn x6, x7
0x1f,0x3d,0x09,0xab = cmn x8, x9, lsl #15
0x5f,0xfd,0x0b,0xab = cmn x10, x11, lsl #63
0x9f,0x01,0x4d,0xab = cmn x12, x13, lsr #0
0xdf,0xa5,0x4f,0xab = cmn x14, x15, lsr #41
0x1f,0xfe,0x51,0xab = cmn x16, x17, lsr #63
0x5f,0x02,0x93,0xab = cmn x18, x19, asr #0
0x9f,0xde,0x95,0xab = cmn x20, x21, asr #55
0xdf,0xfe,0x97,0xab = cmn x22, x23, asr #63
0x1f,0x00,0x03,0x6b = cmp w0, w3
0xff,0x03,0x04,0x6b = cmp wzr, w4
0xbf,0x00,0x1f,0x6b = cmp w5, wzr
0xdf,0x00,0x07,0x6b = cmp w6, w7
0x1f,0x3d,0x09,0x6b = cmp w8, w9, lsl #15
0x5f,0x7d,0x0b,0x6b = cmp w10, w11, lsl #31
0x9f,0x01,0x4d,0x6b = cmp w12, w13, lsr #0
0xdf,0x55,0x4f,0x6b = cmp w14, w15, lsr #21
0x1f,0x7e,0x51,0x6b = cmp w16, w17, lsr #31
0x5f,0x02,0x93,0x6b = cmp w18, w19, asr #0
0x9f,0x5a,0x95,0x6b = cmp w20, w21, asr #22
0xdf,0x7e,0x97,0x6b = cmp w22, w23, asr #31
0x1f,0x00,0x03,0xeb = cmp x0, x3
0xff,0x03,0x04,0xeb = cmp xzr, x4
0xbf,0x00,0x1f,0xeb = cmp x5, xzr
0xdf,0x00,0x07,0xeb = cmp x6, x7
0x1f,0x3d,0x09,0xeb = cmp x8, x9, lsl #15
0x5f,0xfd,0x0b,0xeb = cmp x10, x11, lsl #63
0x9f,0x01,0x4d,0xeb = cmp x12, x13, lsr #0
0xdf,0xa5,0x4f,0xeb = cmp x14, x15, lsr #41
0x1f,0xfe,0x51,0xeb = cmp x16, x17, lsr #63
0x5f,0x02,0x93,0xeb = cmp x18, x19, asr #0
0x9f,0xde,0x95,0xeb = cmp x20, x21, asr #55
0xdf,0xfe,0x97,0xeb = cmp x22, x23, asr #63
0xfd,0x03,0x1e,0x4b = sub w29, wzr, w30
0xfe,0x03,0x1f,0x4b = sub w30, wzr, wzr
0xff,0x03,0x00,0x4b = sub wzr, wzr, w0
0xfc,0x03,0x1b,0x4b = sub w28, wzr, w27
0xfa,0x77,0x19,0x4b = sub w26, wzr, w25, lsl #29
0xf8,0x7f,0x17,0x4b = sub w24, wzr, w23, lsl #31
0xf6,0x03,0x55,0x4b = sub w22, wzr, w21, lsr #0
0xf4,0x07,0x53,0x4b = sub w20, wzr, w19, lsr #1
0xf2,0x7f,0x51,0x4b = sub w18, wzr, w17, lsr #31
0xf0,0x03,0x8f,0x4b = sub w16, wzr, w15, asr #0
0xee,0x33,0x8d,0x4b = sub w14, wzr, w13, asr #12
0xec,0x7f,0x8b,0x4b = sub w12, wzr, w11, asr #31
0xfd,0x03,0x1e,0xcb = sub x29, xzr, x30
0xfe,0x03,0x1f,0xcb = sub x30, xzr, xzr
0xff,0x03,0x00,0xcb = sub xzr, xzr, x0
0xfc,0x03,0x1b,0xcb = sub x28, xzr, x27
0xfa,0x77,0x19,0xcb = sub x26, xzr, x25, lsl #29
0xf8,0x7f,0x17,0xcb = sub x24, xzr, x23, lsl #31
0xf6,0x03,0x55,0xcb = sub x22, xzr, x21, lsr #0
0xf4,0x07,0x53,0xcb = sub x20, xzr, x19, lsr #1
0xf2,0x7f,0x51,0xcb = sub x18, xzr, x17, lsr #31
0xf0,0x03,0x8f,0xcb = sub x16, xzr, x15, asr #0
0xee,0x33,0x8d,0xcb = sub x14, xzr, x13, asr #12
0xec,0x7f,0x8b,0xcb = sub x12, xzr, x11, asr #31
0xfd,0x03,0x1e,0x6b = subs w29, wzr, w30
0xfe,0x03,0x1f,0x6b = subs w30, wzr, wzr
0xff,0x03,0x00,0x6b = subs wzr, wzr, w0
0xfc,0x03,0x1b,0x6b = subs w28, wzr, w27
0xfa,0x77,0x19,0x6b = subs w26, wzr, w25, lsl #29
0xf8,0x7f,0x17,0x6b = subs w24, wzr, w23, lsl #31
0xf6,0x03,0x55,0x6b = subs w22, wzr, w21, lsr #0
0xf4,0x07,0x53,0x6b = subs w20, wzr, w19, lsr #1
0xf2,0x7f,0x51,0x6b = subs w18, wzr, w17, lsr #31
0xf0,0x03,0x8f,0x6b = subs w16, wzr, w15, asr #0
0xee,0x33,0x8d,0x6b = subs w14, wzr, w13, asr #12
0xec,0x7f,0x8b,0x6b = subs w12, wzr, w11, asr #31
0xfd,0x03,0x1e,0xeb = subs x29, xzr, x30
0xfe,0x03,0x1f,0xeb = subs x30, xzr, xzr
0xff,0x03,0x00,0xeb = subs xzr, xzr, x0
0xfc,0x03,0x1b,0xeb = subs x28, xzr, x27
0xfa,0x77,0x19,0xeb = subs x26, xzr, x25, lsl #29
0xf8,0x7f,0x17,0xeb = subs x24, xzr, x23, lsl #31
0xf6,0x03,0x55,0xeb = subs x22, xzr, x21, lsr #0
0xf4,0x07,0x53,0xeb = subs x20, xzr, x19, lsr #1
0xf2,0x7f,0x51,0xeb = subs x18, xzr, x17, lsr #31
0xf0,0x03,0x8f,0xeb = subs x16, xzr, x15, asr #0
0xee,0x33,0x8d,0xeb = subs x14, xzr, x13, asr #12
0xec,0x7f,0x8b,0xeb = subs x12, xzr, x11, asr #31
0x7d,0x03,0x19,0x1a = adc w29, w27, w25
0x7f,0x00,0x04,0x1a = adc wzr, w3, w4
0xe9,0x03,0x0a,0x1a = adc w9, wzr, w10
0x14,0x00,0x1f,0x1a = adc w20, w0, wzr
0x7d,0x03,0x19,0x9a = adc x29, x27, x25
0x7f,0x00,0x04,0x9a = adc xzr, x3, x4
0xe9,0x03,0x0a,0x9a = adc x9, xzr, x10
0x14,0x00,0x1f,0x9a = adc x20, x0, xzr
0x7d,0x03,0x19,0x3a = adcs w29, w27, w25
0x7f,0x00,0x04,0x3a = adcs wzr, w3, w4
0xe9,0x03,0x0a,0x3a = adcs w9, wzr, w10
0x14,0x00,0x1f,0x3a = adcs w20, w0, wzr
0x7d,0x03,0x19,0xba = adcs x29, x27, x25
0x7f,0x00,0x04,0xba = adcs xzr, x3, x4
0xe9,0x03,0x0a,0xba = adcs x9, xzr, x10
0x14,0x00,0x1f,0xba = adcs x20, x0, xzr
0x7d,0x03,0x19,0x5a = sbc w29, w27, w25
0x7f,0x00,0x04,0x5a = sbc wzr, w3, w4
0xe9,0x03,0x0a,0x5a = ngc w9, w10
0x14,0x00,0x1f,0x5a = sbc w20, w0, wzr
0x7d,0x03,0x19,0xda = sbc x29, x27, x25
0x7f,0x00,0x04,0xda = sbc xzr, x3, x4
0xe9,0x03,0x0a,0xda = ngc x9, x10
0x14,0x00,0x1f,0xda = sbc x20, x0, xzr
0x7d,0x03,0x19,0x7a = sbcs w29, w27, w25
0x7f,0x00,0x04,0x7a = sbcs wzr, w3, w4
0xe9,0x03,0x0a,0x7a = ngcs w9, w10
0x14,0x00,0x1f,0x7a = sbcs w20, w0, wzr
0x7d,0x03,0x19,0xfa = sbcs x29, x27, x25
0x7f,0x00,0x04,0xfa = sbcs xzr, x3, x4
0xe9,0x03,0x0a,0xfa = ngcs x9, x10
0x14,0x00,0x1f,0xfa = sbcs x20, x0, xzr
0xe3,0x03,0x0c,0x5a = ngc w3, w12
0xff,0x03,0x09,0x5a = ngc wzr, w9
0xf7,0x03,0x1f,0x5a = ngc w23, wzr
0xfd,0x03,0x1e,0xda = ngc x29, x30
0xff,0x03,0x00,0xda = ngc xzr, x0
0xe0,0x03,0x1f,0xda = ngc x0, xzr
0xe3,0x03,0x0c,0x7a = ngcs w3, w12
0xff,0x03,0x09,0x7a = ngcs wzr, w9
0xf7,0x03,0x1f,0x7a = ngcs w23, wzr
0xfd,0x03,0x1e,0xfa = ngcs x29, x30
0xff,0x03,0x00,0xfa = ngcs xzr, x0
0xe0,0x03,0x1f,0xfa = ngcs x0, xzr
0x41,0x10,0x43,0x93 = sbfm x1, x2, #3, #4
0x83,0xfc,0x7f,0x93 = sbfm x3, x4, #63, #63
0xff,0x7f,0x1f,0x13 = sbfm wzr, wzr, #31, #31
0x2c,0x01,0x00,0x13 = sbfm w12, w9, #0, #0
0xa4,0x28,0x4c,0xd3 = ubfm x4, x5, #12, #10
0x9f,0x00,0x40,0xd3 = ubfm xzr, x4, #0, #0
0xe4,0x17,0x7f,0xd3 = ubfm x4, xzr, #63, #5
0xc5,0xfc,0x4c,0xd3 = ubfm x5, x6, #12, #63
0xa4,0x28,0x4c,0xb3 = bfm x4, x5, #12, #10
0x9f,0x00,0x40,0xb3 = bfm xzr, x4, #0, #0
0xe4,0x17,0x7f,0xb3 = bfm x4, xzr, #63, #5
0xc5,0xfc,0x4c,0xb3 = bfm x5, x6, #12, #63
0x41,0x1c,0x00,0x13 = sxtb w1, w2
0x7f,0x1c,0x40,0x93 = sxtb xzr, w3
0x49,0x3d,0x00,0x13 = sxth w9, w10
0x20,0x3c,0x40,0x93 = sxth x0, w1
0xc3,0x7f,0x40,0x93 = sxtw x3, w30
0x41,0x1c,0x00,0x53 = uxtb w1, w2
0x7f,0x1c,0x00,0x53 = uxtb xzr, w3
0x49,0x3d,0x00,0x53 = uxth w9, w10
0x20,0x3c,0x00,0x53 = uxth x0, w1
0x43,0x7c,0x00,0x13 = asr w3, w2, #0
0x49,0x7d,0x1f,0x13 = asr w9, w10, #31
0xb4,0xfe,0x7f,0x93 = asr x20, x21, #63
0xe1,0x7f,0x03,0x13 = asr w1, wzr, #3
0x43,0x7c,0x00,0x53 = lsr w3, w2, #0
0x49,0x7d,0x1f,0x53 = lsr w9, w10, #31
0xb4,0xfe,0x7f,0xd3 = lsr x20, x21, #63
0xff,0x7f,0x03,0x53 = lsr wzr, wzr, #3
0x43,0x7c,0x00,0x53 = lsl w3, w2, #0
0x49,0x01,0x01,0x53 = lsl w9, w10, #31
0xb4,0x02,0x41,0xd3 = lsl x20, x21, #63
0xe1,0x73,0x1d,0x53 = lsl w1, wzr, #3
0x49,0x01,0x00,0x13 = sbfiz w9, w10, #0, #1
0x62,0x00,0x41,0x93 = sbfiz x2, x3, #63, #1
0x93,0xfe,0x40,0x93 = sbfiz x19, x20, #0, #64
0x49,0xe9,0x7b,0x93 = sbfiz x9, x10, #5, #59
0x49,0x7d,0x00,0x13 = sbfiz w9, w10, #0, #32
0x8b,0x01,0x01,0x13 = sbfiz w11, w12, #31, #1
0xcd,0x09,0x03,0x13 = sbfiz w13, w14, #29, #3
0xff,0x2b,0x76,0x93 = sbfiz xzr, xzr, #10, #11
0x49,0x01,0x00,0x13 = sbfx w9, w10, #0, #1
0x62,0xfc,0x7f,0x93 = sbfx x2, x3, #63, #1
0x93,0xfe,0x40,0x93 = sbfx x19, x20, #0, #64
0x49,0xfd,0x45,0x93 = sbfx x9, x10, #5, #59
0x49,0x7d,0x00,0x13 = sbfx w9, w10, #0, #32
0x8b,0x7d,0x1f,0x13 = sbfx w11, w12, #31, #1
0xcd,0x7d,0x1d,0x13 = sbfx w13, w14, #29, #3
0xff,0x53,0x4a,0x93 = sbfx xzr, xzr, #10, #11
0x49,0x01,0x00,0x33 = bfi w9, w10, #0, #1
0x62,0x00,0x41,0xb3 = bfi x2, x3, #63, #1
0x93,0xfe,0x40,0xb3 = bfi x19, x20, #0, #64
0x49,0xe9,0x7b,0xb3 = bfi x9, x10, #5, #59
0x49,0x7d,0x00,0x33 = bfi w9, w10, #0, #32
0x8b,0x01,0x01,0x33 = bfi w11, w12, #31, #1
0xcd,0x09,0x03,0x33 = bfi w13, w14, #29, #3
0xff,0x2b,0x76,0xb3 = bfi xzr, xzr, #10, #11
0x49,0x01,0x00,0x33 = bfxil w9, w10, #0, #1
0x62,0xfc,0x7f,0xb3 = bfxil x2, x3, #63, #1
0x93,0xfe,0x40,0xb3 = bfxil x19, x20, #0, #64
0x49,0xfd,0x45,0xb3 = bfxil x9, x10, #5, #59
0x49,0x7d,0x00,0x33 = bfxil w9, w10, #0, #32
0x8b,0x7d,0x1f,0x33 = bfxil w11, w12, #31, #1
0xcd,0x7d,0x1d,0x33 = bfxil w13, w14, #29, #3
0xff,0x53,0x4a,0xb3 = bfxil xzr, xzr, #10, #11
0x49,0x01,0x00,0x53 = ubfiz w9, w10, #0, #1
0x62,0x00,0x41,0xd3 = ubfiz x2, x3, #63, #1
0x93,0xfe,0x40,0xd3 = ubfiz x19, x20, #0, #64
0x49,0xe9,0x7b,0xd3 = ubfiz x9, x10, #5, #59
0x49,0x7d,0x00,0x53 = ubfiz w9, w10, #0, #32
0x8b,0x01,0x01,0x53 = ubfiz w11, w12, #31, #1
0xcd,0x09,0x03,0x53 = ubfiz w13, w14, #29, #3
0xff,0x2b,0x76,0xd3 = ubfiz xzr, xzr, #10, #11
0x49,0x01,0x00,0x53 = ubfx w9, w10, #0, #1
0x62,0xfc,0x7f,0xd3 = ubfx x2, x3, #63, #1
0x93,0xfe,0x40,0xd3 = ubfx x19, x20, #0, #64
0x49,0xfd,0x45,0xd3 = ubfx x9, x10, #5, #59
0x49,0x7d,0x00,0x53 = ubfx w9, w10, #0, #32
0x8b,0x7d,0x1f,0x53 = ubfx w11, w12, #31, #1
0xcd,0x7d,0x1d,0x53 = ubfx w13, w14, #29, #3
0xff,0x53,0x4a,0xd3 = ubfx xzr, xzr, #10, #11
0x05,0x00,0x00,0x34 = cbz w5, #0
0xe3,0xff,0xff,0xb5 = cbnz x3, #-4
0xf4,0xff,0x7f,0x34 = cbz w20, #1048572
0x1f,0x00,0x80,0xb5 = cbnz xzr, #-1048576
0x00,0x00,0x00,0x54 = b.eq #0
0xeb,0xff,0xff,0x54 = b.lt #-4
0xe3,0xff,0x7f,0x54 = b.lo #1048572
0x20,0x08,0x5f,0x7a = ccmp w1, #31, #0, eq
0x6f,0x28,0x40,0x7a = ccmp w3, #0, #15, hs
0xed,0x2b,0x4f,0x7a = ccmp wzr, #15, #13, hs
0x20,0xd9,0x5f,0xfa = ccmp x9, #31, #0, le
0x6f,0xc8,0x40,0xfa = ccmp x3, #0, #15, gt
0xe7,0x1b,0x45,0xfa = ccmp xzr, #5, #7, ne
0x20,0x08,0x5f,0x3a = ccmn w1, #31, #0, eq
0x6f,0x28,0x40,0x3a = ccmn w3, #0, #15, hs
0xed,0x2b,0x4f,0x3a = ccmn wzr, #15, #13, hs
0x20,0xd9,0x5f,0xba = ccmn x9, #31, #0, le
0x6f,0xc8,0x40,0xba = ccmn x3, #0, #15, gt
0xe7,0x1b,0x45,0xba = ccmn xzr, #5, #7, ne
0x20,0x00,0x5f,0x7a = ccmp w1, wzr, #0, eq
0x6f,0x20,0x40,0x7a = ccmp w3, w0, #15, hs
0xed,0x23,0x4f,0x7a = ccmp wzr, w15, #13, hs
0x20,0xd1,0x5f,0xfa = ccmp x9, xzr, #0, le
0x6f,0xc0,0x40,0xfa = ccmp x3, x0, #15, gt
0xe7,0x13,0x45,0xfa = ccmp xzr, x5, #7, ne
0x20,0x00,0x5f,0x3a = ccmn w1, wzr, #0, eq
0x6f,0x20,0x40,0x3a = ccmn w3, w0, #15, hs
0xed,0x23,0x4f,0x3a = ccmn wzr, w15, #13, hs
0x20,0xd1,0x5f,0xba = ccmn x9, xzr, #0, le
0x6f,0xc0,0x40,0xba = ccmn x3, x0, #15, gt
0xe7,0x13,0x45,0xba = ccmn xzr, x5, #7, ne
0x01,0x10,0x93,0x1a = csel w1, w0, w19, ne
0xbf,0x00,0x89,0x1a = csel wzr, w5, w9, eq
0xe9,0xc3,0x9e,0x1a = csel w9, wzr, w30, gt
0x81,0x43,0x9f,0x1a = csel w1, w28, wzr, mi
0xf3,0xb2,0x9d,0x9a = csel x19, x23, x29, lt
0x7f,0xa0,0x84,0x9a = csel xzr, x3, x4, ge
0xe5,0x23,0x86,0x9a = csel x5, xzr, x6, hs
0x07,0x31,0x9f,0x9a = csel x7, x8, xzr, lo
0x01,0x14,0x93,0x1a = csinc w1, w0, w19, ne
0xbf,0x04,0x89,0x1a = csinc wzr, w5, w9, eq
0xe9,0xc7,0x9e,0x1a = csinc w9, wzr, w30, gt
0x81,0x47,0x9f,0x1a = csinc w1, w28, wzr, mi
0xf3,0xb6,0x9d,0x9a = csinc x19, x23, x29, lt
0x7f,0xa4,0x84,0x9a = csinc xzr, x3, x4, ge
0xe5,0x27,0x86,0x9a = csinc x5, xzr, x6, hs
0x07,0x35,0x9f,0x9a = csinc x7, x8, xzr, lo
0x01,0x10,0x93,0x5a = csinv w1, w0, w19, ne
0xbf,0x00,0x89,0x5a = csinv wzr, w5, w9, eq
0xe9,0xc3,0x9e,0x5a = csinv w9, wzr, w30, gt
0x81,0x43,0x9f,0x5a = csinv w1, w28, wzr, mi
0xf3,0xb2,0x9d,0xda = csinv x19, x23, x29, lt
0x7f,0xa0,0x84,0xda = csinv xzr, x3, x4, ge
0xe5,0x23,0x86,0xda = csinv x5, xzr, x6, hs
0x07,0x31,0x9f,0xda = csinv x7, x8, xzr, lo
0x01,0x14,0x93,0x5a = csneg w1, w0, w19, ne
0xbf,0x04,0x89,0x5a = csneg wzr, w5, w9, eq
0xe9,0xc7,0x9e,0x5a = csneg w9, wzr, w30, gt
0x81,0x47,0x9f,0x5a = csneg w1, w28, wzr, mi
0xf3,0xb6,0x9d,0xda = csneg x19, x23, x29, lt
0x7f,0xa4,0x84,0xda = csneg xzr, x3, x4, ge
0xe5,0x27,0x86,0xda = csneg x5, xzr, x6, hs
0x07,0x35,0x9f,0xda = csneg x7, x8, xzr, lo
0xe3,0x17,0x9f,0x1a = csinc w3, wzr, wzr, ne
0xe9,0x47,0x9f,0x9a = csinc x9, xzr, xzr, mi
0xf4,0x03,0x9f,0x5a = csinv w20, wzr, wzr, eq
0xfe,0xb3,0x9f,0xda = csinv x30, xzr, xzr, lt
0xa3,0xd4,0x85,0x1a = csinc w3, w5, w5, le
0x9f,0xc4,0x84,0x1a = csinc wzr, w4, w4, gt
0xe9,0xa7,0x9f,0x1a = csinc w9, wzr, wzr, ge
0xa3,0xd4,0x85,0x9a = csinc x3, x5, x5, le
0x9f,0xc4,0x84,0x9a = csinc xzr, x4, x4, gt
0xe9,0xa7,0x9f,0x9a = csinc x9, xzr, xzr, ge
0xa3,0xd0,0x85,0x5a = csinv w3, w5, w5, le
0x9f,0xc0,0x84,0x5a = csinv wzr, w4, w4, gt
0xe9,0xa3,0x9f,0x5a = csinv w9, wzr, wzr, ge
0xa3,0xd0,0x85,0xda = csinv x3, x5, x5, le
0x9f,0xc0,0x84,0xda = csinv xzr, x4, x4, gt
0xe9,0xa3,0x9f,0xda = csinv x9, xzr, xzr, ge
0xa3,0xd4,0x85,0x5a = csneg w3, w5, w5, le
0x9f,0xc4,0x84,0x5a = csneg wzr, w4, w4, gt
0xe9,0xa7,0x9f,0x5a = csneg w9, wzr, wzr, ge
0xa3,0xd4,0x85,0xda = csneg x3, x5, x5, le
0x9f,0xc4,0x84,0xda = csneg xzr, x4, x4, gt
0xe9,0xa7,0x9f,0xda = csneg x9, xzr, xzr, ge
0xe0,0x00,0xc0,0x5a = rbit w0, w7
0x72,0x00,0xc0,0xda = rbit x18, x3
0x31,0x04,0xc0,0x5a = rev16 w17, w1
0x45,0x04,0xc0,0xda = rev16 x5, x2
0x12,0x08,0xc0,0x5a = rev w18, w0
0x34,0x08,0xc0,0xda = rev32 x20, x1
0xf4,0x0b,0xc0,0xda = rev32 x20, xzr
0x56,0x0c,0xc0,0xda = rev x22, x2
0xf2,0x0f,0xc0,0xda = rev x18, xzr
0xe7,0x0b,0xc0,0x5a = rev w7, wzr
0x78,0x10,0xc0,0x5a = clz w24, w3
0x9a,0x10,0xc0,0xda = clz x26, x4
0xa3,0x14,0xc0,0x5a = cls w3, w5
0xb4,0x14,0xc0,0xda = cls x20, x5
0xf8,0x13,0xc0,0x5a = clz w24, wzr
0xf6,0x0f,0xc0,0xda = rev x22, xzr
0xe5,0x40,0xd4,0x1a = crc32b w5, w7, w20
0xfc,0x47,0xde,0x1a = crc32h w28, wzr, w30
0x20,0x48,0xc2,0x1a = crc32w w0, w1, w2
0x27,0x4d,0xd4,0x9a = crc32x w7, w9, x20
0xa9,0x50,0xc4,0x1a = crc32cb w9, w5, w4
0x2d,0x56,0xd9,0x1a = crc32ch w13, w17, w25
0x7f,0x58,0xc5,0x1a = crc32cw wzr, w3, w5
0x12,0x5e,0xdf,0x9a = crc32cx w18, w16, xzr
0xe0,0x08,0xca,0x1a = udiv w0, w7, w10
0xc9,0x0a,0xc4,0x9a = udiv x9, x22, x4
0xac,0x0e,0xc0,0x1a = sdiv w12, w21, w0
0x4d,0x0c,0xc1,0x9a = sdiv x13, x2, x1
0x8b,0x21,0xcd,0x1a = lsl w11, w12, w13
0xee,0x21,0xd0,0x9a = lsl x14, x15, x16
0x51,0x26,0xd3,0x1a = lsr w17, w18, w19
0xb4,0x26,0xd6,0x9a = lsr x20, x21, x22
0x17,0x2b,0xd9,0x1a = asr w23, w24, w25
0x7a,0x2b,0xdc,0x9a = asr x26, x27, x28
0x20,0x2c,0xc2,0x1a = ror w0, w1, w2
0x83,0x2c,0xc5,0x9a = ror x3, x4, x5
0xe6,0x20,0xc8,0x1a = lsl w6, w7, w8
0x49,0x21,0xcb,0x9a = lsl x9, x10, x11
0xac,0x25,0xce,0x1a = lsr w12, w13, w14
0x0f,0x26,0xd1,0x9a = lsr x15, x16, x17
0x72,0x2a,0xd4,0x1a = asr w18, w19, w20
0xd5,0x2a,0xd7,0x9a = asr x21, x22, x23
0x38,0x2f,0xda,0x1a = ror w24, w25, w26
0x9b,0x2f,0xdd,0x9a = ror x27, x28, x29
0x61,0x10,0x07,0x1b = madd w1, w3, w7, w4
0x1f,0x2c,0x09,0x1b = madd wzr, w0, w9, w11
0xed,0x13,0x04,0x1b = madd w13, wzr, w4, w4
0xd3,0x77,0x1f,0x1b = madd w19, w30, wzr, w29
0xa4,0x7c,0x06,0x1b = mul w4, w5, w6
0x61,0x10,0x07,0x9b = madd x1, x3, x7, x4
0x1f,0x2c,0x09,0x9b = madd xzr, x0, x9, x11
0xed,0x13,0x04,0x9b = madd x13, xzr, x4, x4
0xd3,0x77,0x1f,0x9b = madd x19, x30, xzr, x29
0xa4,0x7c,0x06,0x9b = mul x4, x5, x6
0x61,0x90,0x07,0x1b = msub w1, w3, w7, w4
0x1f,0xac,0x09,0x1b = msub wzr, w0, w9, w11
0xed,0x93,0x04,0x1b = msub w13, wzr, w4, w4
0xd3,0xf7,0x1f,0x1b = msub w19, w30, wzr, w29
0xa4,0xfc,0x06,0x1b = mneg w4, w5, w6
0x61,0x90,0x07,0x9b = msub x1, x3, x7, x4
0x1f,0xac,0x09,0x9b = msub xzr, x0, x9, x11
0xed,0x93,0x04,0x9b = msub x13, xzr, x4, x4
0xd3,0xf7,0x1f,0x9b = msub x19, x30, xzr, x29
0xa4,0xfc,0x06,0x9b = mneg x4, x5, x6
0xa3,0x24,0x22,0x9b = smaddl x3, w5, w2, x9
0x5f,0x31,0x2b,0x9b = smaddl xzr, w10, w11, x12
0xed,0x3f,0x2e,0x9b = smaddl x13, wzr, w14, x15
0x30,0x4a,0x3f,0x9b = smaddl x16, w17, wzr, x18
0x93,0x7e,0x35,0x9b = smull x19, w20, w21
0xa3,0xa4,0x22,0x9b = smsubl x3, w5, w2, x9
0x5f,0xb1,0x2b,0x9b = smsubl xzr, w10, w11, x12
0xed,0xbf,0x2e,0x9b = smsubl x13, wzr, w14, x15
0x30,0xca,0x3f,0x9b = smsubl x16, w17, wzr, x18
0x93,0xfe,0x35,0x9b = smnegl x19, w20, w21
0xa3,0x24,0xa2,0x9b = umaddl x3, w5, w2, x9
0x5f,0x31,0xab,0x9b = umaddl xzr, w10, w11, x12
0xed,0x3f,0xae,0x9b = umaddl x13, wzr, w14, x15
0x30,0x4a,0xbf,0x9b = umaddl x16, w17, wzr, x18
0x93,0x7e,0xb5,0x9b = umull x19, w20, w21
0xa3,0xa4,0xa2,0x9b = umsubl x3, w5, w2, x9
0x5f,0xb1,0xab,0x9b = umsubl xzr, w10, w11, x12
0xed,0xbf,0xae,0x9b = umsubl x13, wzr, w14, x15
0x30,0xca,0xbf,0x9b = umsubl x16, w17, wzr, x18
0x93,0xfe,0xb5,0x9b = umnegl x19, w20, w21
0xbe,0x7f,0x5c,0x9b = smulh x30, x29, x28
0x7f,0x7f,0x5a,0x9b = smulh xzr, x27, x26
0xf9,0x7f,0x58,0x9b = smulh x25, xzr, x24
0xd7,0x7e,0x5f,0x9b = smulh x23, x22, xzr
0xbe,0x7f,0xdc,0x9b = umulh x30, x29, x28
0x7f,0x7f,0xda,0x9b = umulh xzr, x27, x26
0xf9,0x7f,0xd8,0x9b = umulh x25, xzr, x24
0xd7,0x7e,0xdf,0x9b = umulh x23, x22, xzr
0x83,0x7c,0x05,0x1b = mul w3, w4, w5
0xdf,0x7c,0x07,0x1b = mul wzr, w6, w7
0xe8,0x7f,0x09,0x1b = mul w8, wzr, w9
0x6a,0x7d,0x1f,0x1b = mul w10, w11, wzr
0xac,0x7d,0x0e,0x9b = mul x12, x13, x14
0xff,0x7d,0x10,0x9b = mul xzr, x15, x16
0xf1,0x7f,0x12,0x9b = mul x17, xzr, x18
0x93,0x7e,0x1f,0x9b = mul x19, x20, xzr
0xd5,0xfe,0x17,0x1b = mneg w21, w22, w23
0x1f,0xff,0x19,0x1b = mneg wzr, w24, w25
0xfa,0xff,0x1b,0x1b = mneg w26, wzr, w27
0xbc,0xff,0x1f,0x1b = mneg w28, w29, wzr
0xab,0x7d,0x31,0x9b = smull x11, w13, w17
0xab,0x7d,0xb1,0x9b = umull x11, w13, w17
0xab,0xfd,0x31,0x9b = smnegl x11, w13, w17
0xab,0xfd,0xb1,0x9b = umnegl x11, w13, w17
0x01,0x00,0x00,0xd4 = svc #0
0xe1,0xff,0x1f,0xd4 = svc #65535
0x22,0x00,0x00,0xd4 = hvc #1
0x03,0xdc,0x05,0xd4 = smc #12000
0x80,0x01,0x20,0xd4 = brk #12
0x60,0x0f,0x40,0xd4 = hlt #123
0x41,0x05,0xa0,0xd4 = dcps1 #42
0x22,0x01,0xa0,0xd4 = dcps2 #9
0x03,0x7d,0xa0,0xd4 = dcps3 #1000
0x01,0x00,0xa0,0xd4 = dcps1 
0x02,0x00,0xa0,0xd4 = dcps2 
0x03,0x00,0xa0,0xd4 = dcps3 
0xa3,0x00,0x87,0x13 = extr w3, w5, w7, #0
0xab,0x7d,0x91,0x13 = extr w11, w13, w17, #31
0xa3,0x3c,0xc7,0x93 = extr x3, x5, x7, #15
0xab,0xfd,0xd1,0x93 = extr x11, x13, x17, #63
0xf3,0x62,0xd7,0x93 = extr x19, x23, x23, #24
0xfd,0xff,0xdf,0x93 = extr x29, xzr, xzr, #63
0xa9,0x7d,0x8d,0x13 = extr w9, w13, w13, #31
0x60,0x20,0x25,0x1e = fcmp s3, s5
0xe8,0x23,0x20,0x1e = fcmp s31, #0.0
0xb0,0x23,0x3e,0x1e = fcmpe s29, s30
0xf8,0x21,0x20,0x1e = fcmpe s15, #0.0
0x80,0x20,0x6c,0x1e = fcmp d4, d12
0xe8,0x22,0x60,0x1e = fcmp d23, #0.0
0x50,0x23,0x76,0x1e = fcmpe d26, d22
0xb8,0x23,0x60,0x1e = fcmpe d29, #0.0
0x20,0x04,0x3f,0x1e = fccmp s1, s31, #0, eq
0x6f,0x24,0x20,0x1e = fccmp s3, s0, #15, hs
0xed,0x27,0x2f,0x1e = fccmp s31, s15, #13, hs
0x20,0xd5,0x7f,0x1e = fccmp d9, d31, #0, le
0x6f,0xc4,0x60,0x1e = fccmp d3, d0, #15, gt
0xe7,0x17,0x65,0x1e = fccmp d31, d5, #7, ne
0x30,0x04,0x3f,0x1e = fccmpe s1, s31, #0, eq
0x7f,0x24,0x20,0x1e = fccmpe s3, s0, #15, hs
0xfd,0x27,0x2f,0x1e = fccmpe s31, s15, #13, hs
0x30,0xd5,0x7f,0x1e = fccmpe d9, d31, #0, le
0x7f,0xc4,0x60,0x1e = fccmpe d3, d0, #15, gt
0xf7,0x17,0x65,0x1e = fccmpe d31, d5, #7, ne
0x83,0x5e,0x29,0x1e = fcsel s3, s20, s9, pl
0x49,0x4d,0x6b,0x1e = fcsel d9, d10, d11, mi
0x20,0x40,0x20,0x1e = fmov s0, s1
0x62,0xc0,0x20,0x1e = fabs s2, s3
0xa4,0x40,0x21,0x1e = fneg s4, s5
0xe6,0xc0,0x21,0x1e = fsqrt s6, s7
0x28,0xc1,0x22,0x1e = fcvt d8, s9
0x6a,0xc1,0x23,0x1e = fcvt h10, s11
0xac,0x41,0x24,0x1e = frintn s12, s13
0xee,0xc1,0x24,0x1e = frintp s14, s15
0x30,0x42,0x25,0x1e = frintm s16, s17
0x72,0xc2,0x25,0x1e = frintz s18, s19
0xb4,0x42,0x26,0x1e = frinta s20, s21
0xf6,0x42,0x27,0x1e = frintx s22, s23
0x38,0xc3,0x27,0x1e = frinti s24, s25
0x20,0x40,0x60,0x1e = fmov d0, d1
0x62,0xc0,0x60,0x1e = fabs d2, d3
0xa4,0x40,0x61,0x1e = fneg d4, d5
0xe6,0xc0,0x61,0x1e = fsqrt d6, d7
0x28,0x41,0x62,0x1e = fcvt s8, d9
0x6a,0xc1,0x63,0x1e = fcvt h10, d11
0xac,0x41,0x64,0x1e = frintn d12, d13
0xee,0xc1,0x64,0x1e = frintp d14, d15
0x30,0x42,0x65,0x1e = frintm d16, d17
0x72,0xc2,0x65,0x1e = frintz d18, d19
0xb4,0x42,0x66,0x1e = frinta d20, d21
0xf6,0x42,0x67,0x1e = frintx d22, d23
0x38,0xc3,0x67,0x1e = frinti d24, d25
0x7a,0x43,0xe2,0x1e = fcvt s26, h27
0xbc,0xc3,0xe2,0x1e = fcvt d28, h29
0x74,0x0a,0x31,0x1e = fmul s20, s19, s17
0x41,0x18,0x23,0x1e = fdiv s1, s2, s3
0xa4,0x28,0x26,0x1e = fadd s4, s5, s6
0x07,0x39,0x29,0x1e = fsub s7, s8, s9
0x6a,0x49,0x2c,0x1e = fmax s10, s11, s12
0xcd,0x59,0x2f,0x1e = fmin s13, s14, s15
0x30,0x6a,0x32,0x1e = fmaxnm s16, s17, s18
0x93,0x7a,0x35,0x1e = fminnm s19, s20, s21
0xf6,0x8a,0x38,0x1e = fnmul s22, s23, s24
0x74,0x0a,0x71,0x1e = fmul d20, d19, d17
0x41,0x18,0x63,0x1e = fdiv d1, d2, d3
0xa4,0x28,0x66,0x1e = fadd d4, d5, d6
0x07,0x39,0x69,0x1e = fsub d7, d8, d9
0x6a,0x49,0x6c,0x1e = fmax d10, d11, d12
0xcd,0x59,0x6f,0x1e = fmin d13, d14, d15
0x30,0x6a,0x72,0x1e = fmaxnm d16, d17, d18
0x93,0x7a,0x75,0x1e = fminnm d19, d20, d21
0xf6,0x8a,0x78,0x1e = fnmul d22, d23, d24
0xa3,0x7c,0x06,0x1f = fmadd s3, s5, s6, s31
0xa3,0x5d,0x40,0x1f = fmadd d3, d13, d0, d23
0xa3,0xfc,0x06,0x1f = fmsub s3, s5, s6, s31
0xa3,0xdd,0x40,0x1f = fmsub d3, d13, d0, d23
0xa3,0x7c,0x26,0x1f = fnmadd s3, s5, s6, s31
0xa3,0x5d,0x60,0x1f = fnmadd d3, d13, d0, d23
0xa3,0xfc,0x26,0x1f = fnmsub s3, s5, s6, s31
0xa3,0xdd,0x60,0x1f = fnmsub d3, d13, d0, d23
0xa3,0xfc,0x18,0x1e = fcvtzs w3, s5, #1
0x9f,0xce,0x18,0x1e = fcvtzs wzr, s20, #13
0x13,0x80,0x18,0x1e = fcvtzs w19, s0, #32
0xa3,0xfc,0x18,0x9e = fcvtzs x3, s5, #1
0xcc,0x4f,0x18,0x9e = fcvtzs x12, s30, #45
0x13,0x00,0x18,0x9e = fcvtzs x19, s0, #64
0xa3,0xfc,0x58,0x1e = fcvtzs w3, d5, #1
0x9f,0xce,0x58,0x1e = fcvtzs wzr, d20, #13
0x13,0x80,0x58,0x1e = fcvtzs w19, d0, #32
0xa3,0xfc,0x58,0x9e = fcvtzs x3, d5, #1
0xcc,0x4f,0x58,0x9e = fcvtzs x12, d30, #45
0x13,0x00,0x58,0x9e = fcvtzs x19, d0, #64
0xa3,0xfc,0x19,0x1e = fcvtzu w3, s5, #1
0x9f,0xce,0x19,0x1e = fcvtzu wzr, s20, #13
0x13,0x80,0x19,0x1e = fcvtzu w19, s0, #32
0xa3,0xfc,0x19,0x9e = fcvtzu x3, s5, #1
0xcc,0x4f,0x19,0x9e = fcvtzu x12, s30, #45
0x13,0x00,0x19,0x9e = fcvtzu x19, s0, #64
0xa3,0xfc,0x59,0x1e = fcvtzu w3, d5, #1
0x9f,0xce,0x59,0x1e = fcvtzu wzr, d20, #13
0x13,0x80,0x59,0x1e = fcvtzu w19, d0, #32
0xa3,0xfc,0x59,0x9e = fcvtzu x3, d5, #1
0xcc,0x4f,0x59,0x9e = fcvtzu x12, d30, #45
0x13,0x00,0x59,0x9e = fcvtzu x19, d0, #64
0x77,0xfe,0x02,0x1e = scvtf s23, w19, #1
0xff,0xb3,0x02,0x1e = scvtf s31, wzr, #20
0x0e,0x80,0x02,0x1e = scvtf s14, w0, #32
0x77,0xfe,0x02,0x9e = scvtf s23, x19, #1
0xff,0xb3,0x02,0x9e = scvtf s31, xzr, #20
0x0e,0x00,0x02,0x9e = scvtf s14, x0, #64
0x77,0xfe,0x42,0x1e = scvtf d23, w19, #1
0xff,0xb3,0x42,0x1e = scvtf d31, wzr, #20
0x0e,0x80,0x42,0x1e = scvtf d14, w0, #32
0x77,0xfe,0x42,0x9e = scvtf d23, x19, #1
0xff,0xb3,0x42,0x9e = scvtf d31, xzr, #20
0x0e,0x00,0x42,0x9e = scvtf d14, x0, #64
0x77,0xfe,0x03,0x1e = ucvtf s23, w19, #1
0xff,0xb3,0x03,0x1e = ucvtf s31, wzr, #20
0x0e,0x80,0x03,0x1e = ucvtf s14, w0, #32
0x77,0xfe,0x03,0x9e = ucvtf s23, x19, #1
0xff,0xb3,0x03,0x9e = ucvtf s31, xzr, #20
0x0e,0x00,0x03,0x9e = ucvtf s14, x0, #64
0x77,0xfe,0x43,0x1e = ucvtf d23, w19, #1
0xff,0xb3,0x43,0x1e = ucvtf d31, wzr, #20
0x0e,0x80,0x43,0x1e = ucvtf d14, w0, #32
0x77,0xfe,0x43,0x9e = ucvtf d23, x19, #1
0xff,0xb3,0x43,0x9e = ucvtf d31, xzr, #20
0x0e,0x00,0x43,0x9e = ucvtf d14, x0, #64
0xe3,0x03,0x20,0x1e = fcvtns w3, s31
0x9f,0x01,0x20,0x9e = fcvtns xzr, s12
0x9f,0x01,0x21,0x1e = fcvtnu wzr, s12
0x00,0x00,0x21,0x9e = fcvtnu x0, s0
0x3f,0x01,0x28,0x1e = fcvtps wzr, s9
0x8c,0x02,0x28,0x9e = fcvtps x12, s20
0xfe,0x02,0x29,0x1e = fcvtpu w30, s23
0x7d,0x00,0x29,0x9e = fcvtpu x29, s3
0x62,0x00,0x30,0x1e = fcvtms w2, s3
0xa4,0x00,0x30,0x9e = fcvtms x4, s5
0xe6,0x00,0x31,0x1e = fcvtmu w6, s7
0x28,0x01,0x31,0x9e = fcvtmu x8, s9
0x6a,0x01,0x38,0x1e = fcvtzs w10, s11
0xac,0x01,0x38,0x9e = fcvtzs x12, s13
0xee,0x01,0x39,0x1e = fcvtzu w14, s15
0x0f,0x02,0x39,0x9e = fcvtzu x15, s16
0x51,0x02,0x22,0x1e = scvtf s17, w18
0x93,0x02,0x22,0x9e = scvtf s19, x20
0xd5,0x02,0x23,0x1e = ucvtf s21, w22
0x17,0x03,0x22,0x9e = scvtf s23, x24
0x59,0x03,0x24,0x1e = fcvtas w25, s26
0x9b,0x03,0x24,0x9e = fcvtas x27, s28
0xdd,0x03,0x25,0x1e = fcvtau w29, s30
0x1f,0x00,0x25,0x9e = fcvtau xzr, s0
0xe3,0x03,0x60,0x1e = fcvtns w3, d31
0x9f,0x01,0x60,0x9e = fcvtns xzr, d12
0x9f,0x01,0x61,0x1e = fcvtnu wzr, d12
0x00,0x00,0x61,0x9e = fcvtnu x0, d0
0x3f,0x01,0x68,0x1e = fcvtps wzr, d9
0x8c,0x02,0x68,0x9e = fcvtps x12, d20
0xfe,0x02,0x69,0x1e = fcvtpu w30, d23
0x7d,0x00,0x69,0x9e = fcvtpu x29, d3
0x62,0x00,0x70,0x1e = fcvtms w2, d3
0xa4,0x00,0x70,0x9e = fcvtms x4, d5
0xe6,0x00,0x71,0x1e = fcvtmu w6, d7
0x28,0x01,0x71,0x9e = fcvtmu x8, d9
0x6a,0x01,0x78,0x1e = fcvtzs w10, d11
0xac,0x01,0x78,0x9e = fcvtzs x12, d13
0xee,0x01,0x79,0x1e = fcvtzu w14, d15
0x0f,0x02,0x79,0x9e = fcvtzu x15, d16
0x51,0x02,0x62,0x1e = scvtf d17, w18
0x93,0x02,0x62,0x9e = scvtf d19, x20
0xd5,0x02,0x63,0x1e = ucvtf d21, w22
0x17,0x03,0x63,0x9e = ucvtf d23, x24
0x59,0x03,0x64,0x1e = fcvtas w25, d26
0x9b,0x03,0x64,0x9e = fcvtas x27, d28
0xdd,0x03,0x65,0x1e = fcvtau w29, d30
0x1f,0x00,0x65,0x9e = fcvtau xzr, d0
0x23,0x01,0x26,0x1e = fmov w3, s9
0x69,0x00,0x27,0x1e = fmov s9, w3
0xf4,0x03,0x66,0x9e = fmov x20, d31
0xe1,0x01,0x67,0x9e = fmov d1, x15
0x83,0x01,0xae,0x9e = fmov x3, v12.d[1]
0x61,0x02,0xaf,0x9e = fmov v1.d[1], x19
0xe3,0x03,0xaf,0x9e = fmov v3.d[1], xzr
0x02,0x10,0x28,0x1e = fmov s2, #0.12500000
0x03,0x10,0x2e,0x1e = fmov s3, #1.00000000
0x1e,0x10,0x66,0x1e = fmov d30, #16.00000000
0x04,0x30,0x2e,0x1e = fmov s4, #1.06250000
0x0a,0xf0,0x6f,0x1e = fmov d10, #1.93750000
0x0c,0x10,0x3e,0x1e = fmov s12, #-1.00000000
0x10,0x30,0x64,0x1e = fmov d16, #8.50000000
0xe0,0xff,0x7f,0x18 = ldr w0, #1048572
0x0a,0x00,0x80,0x58 = ldr x10, #-1048576
0x02,0x10,0x28,0x1e = fmov s2, #0.12500000
0x03,0x10,0x2e,0x1e = fmov s3, #1.00000000
0x1e,0x10,0x66,0x1e = fmov d30, #16.00000000
0x04,0x30,0x2e,0x1e = fmov s4, #1.06250000
0x0a,0xf0,0x6f,0x1e = fmov d10, #1.93750000
0x0c,0x10,0x3e,0x1e = fmov s12, #-1.00000000
0x10,0x30,0x64,0x1e = fmov d16, #8.50000000
0x62,0x7c,0x01,0x08 = stxrb w1, w2, [x3]
0x83,0x7c,0x02,0x48 = stxrh w2, w3, [x4]
0xe4,0x7f,0x1f,0x88 = stxr wzr, w4, [sp]
0xe6,0x7c,0x05,0xc8 = stxr w5, x6, [x7]
0x27,0x7d,0x5f,0x08 = ldxrb w7, [x9]
0x5f,0x7d,0x5f,0x48 = ldxrh wzr, [x10]
0xe9,0x7f,0x5f,0x88 = ldxr w9, [sp]
0x6a,0x7d,0x5f,0xc8 = ldxr x10, [x11]
0xcc,0x35,0x2b,0x88 = stxp w11, w12, w13, [x14]
0xf7,0x39,0x3f,0xc8 = stxp wzr, x23, x14, [x15]
0xec,0x7f,0x7f,0x88 = ldxp w12, wzr, [sp]
0xed,0x39,0x7f,0xc8 = ldxp x13, x14, [x15]
0x0f,0xfe,0x0e,0x08 = stlxrb w14, w15, [x16]
0x30,0xfe,0x0f,0x48 = stlxrh w15, w16, [x17]
0xf1,0xff,0x1f,0x88 = stlxr wzr, w17, [sp]
0x93,0xfe,0x12,0xc8 = stlxr w18, x19, [x20]
0xb3,0xfe,0x5f,0x08 = ldaxrb w19, [x21]
0xf4,0xff,0x5f,0x48 = ldaxrh w20, [sp]
0xdf,0xfe,0x5f,0x88 = ldaxr wzr, [x22]
0xf5,0xfe,0x5f,0xc8 = ldaxr x21, [x23]
0x16,0xdf,0x3f,0x88 = stlxp wzr, w22, w23, [x24]
0xfa,0xef,0x39,0xc8 = stlxp w25, x26, x27, [sp]
0xfa,0xff,0x7f,0x88 = ldaxp w26, wzr, [sp]
0xdb,0xf3,0x7f,0xc8 = ldaxp x27, x28, [x30]
0xfb,0xff,0x9f,0x08 = stlrb w27, [sp]
0x1c,0xfc,0x9f,0x48 = stlrh w28, [x0]
0x3f,0xfc,0x9f,0x88 = stlr wzr, [x1]
0x5e,0xfc,0x9f,0xc8 = stlr x30, [x2]
0xfd,0xff,0xdf,0x08 = ldarb w29, [sp]
0x1e,0xfc,0xdf,0x48 = ldarh w30, [x0]
0x3f,0xfc,0xdf,0x88 = ldar wzr, [x1]
0x41,0xfc,0xdf,0xc8 = ldar x1, [x2]
0x16,0xdf,0x3f,0x88 = stlxp wzr, w22, w23, [x24]
0xe9,0x03,0x00,0x38 = sturb w9, [sp]
0x9f,0xf1,0x0f,0x78 = sturh wzr, [x12, #255]
0x10,0x00,0x10,0xb8 = stur w16, [x0, #-256]
0xdc,0x11,0x00,0xf8 = stur x28, [x14, #1]
0x81,0xf2,0x4f,0x38 = ldurb w1, [x20, #255]
0x34,0xf0,0x4f,0x78 = ldurh w20, [x1, #255]
0xec,0xf3,0x4f,0xb8 = ldur w12, [sp, #255]
0x9f,0xf1,0x4f,0xf8 = ldur xzr, [x12, #255]
0xe9,0x00,0x90,0x38 = ldursb x9, [x7, #-256]
0x71,0x02,0x90,0x78 = ldursh x17, [x19, #-256]
0xf4,0x01,0x90,0xb8 = ldursw x20, [x15, #-256]
0x4d,0x00,0x80,0xb8 = ldursw x13, [x2]
0xe2,0x03,0x90,0xf8 = prfum pldl2keep, [sp, #-256]
0x33,0x00,0xd0,0x38 = ldursb w19, [x1, #-256]
0xaf,0x02,0xd0,0x78 = ldursh w15, [x21, #-256]
0xe0,0x13,0x00,0x3c = stur b0, [sp, #1]
0x8c,0xf1,0x1f,0x7c = stur h12, [x12, #-1]
0x0f,0xf0,0x0f,0xbc = stur s15, [x0, #255]
0xbf,0x90,0x01,0xfc = stur d31, [x5, #25]
0xa9,0x00,0x80,0x3c = stur q9, [x5]
0xe3,0x03,0x40,0x3c = ldur b3, [sp]
0x85,0x00,0x50,0x7c = ldur h5, [x4, #-256]
0x87,0xf1,0x5f,0xbc = ldur s7, [x12, #-1]
0x6b,0x42,0x40,0xfc = ldur d11, [x19, #4]
0x2d,0x20,0xc0,0x3c = ldur q13, [x1, #2]
0x00,0x00,0x40,0xf9 = ldr x0, [x0]
0xa4,0x03,0x40,0xf9 = ldr x4, [x29]
0x9e,0xfd,0x7f,0xf9 = ldr x30, [x12, #32760]
0xf4,0x07,0x40,0xf9 = ldr x20, [sp, #8]
0xff,0x03,0x40,0xf9 = ldr xzr, [sp]
0xe2,0x03,0x40,0xb9 = ldr w2, [sp]
0xf1,0xff,0x7f,0xb9 = ldr w17, [sp, #16380]
0x4d,0x04,0x40,0xb9 = ldr w13, [x2, #4]
0xa2,0x04,0x80,0xb9 = ldrsw x2, [x5, #4]
0xf7,0xff,0xbf,0xb9 = ldrsw x23, [sp, #16380]
0x82,0x00,0x40,0x79 = ldrh w2, [x4]
0xd7,0xfc,0xff,0x79 = ldrsh w23, [x6, #8190]
0xff,0x07,0xc0,0x79 = ldrsh wzr, [sp, #2]
0x5d,0x04,0x80,0x79 = ldrsh x29, [x2, #2]
0x7a,0xe4,0x41,0x39 = ldrb w26, [x3, #121]
0x4c,0x00,0x40,0x39 = ldrb w12, [x2]
0xfb,0xff,0xff,0x39 = ldrsb w27, [sp, #4095]
0xff,0x01,0x80,0x39 = ldrsb xzr, [x15]
0xfe,0x03,0x00,0xf9 = str x30, [sp]
0x94,0xfc,0x3f,0xb9 = str w20, [x4, #16380]
0x54,0x1d,0x00,0x79 = strh w20, [x10, #14]
0xf1,0xff,0x3f,0x79 = strh w17, [sp, #8190]
0x77,0xfc,0x3f,0x39 = strb w23, [x3, #4095]
0x5f,0x00,0x00,0x39 = strb wzr, [x2]
0xe0,0x07,0x80,0xf9 = prfm pldl1keep, [sp, #8]
0x61,0x00,0x80,0xf9 = prfm pldl1strm, [x3, #0]
0xa2,0x08,0x80,0xf9 = prfm pldl2keep, [x5, #16]
0x43,0x00,0x80,0xf9 = prfm pldl2strm, [x2, #0]
0xa4,0x00,0x80,0xf9 = prfm pldl3keep, [x5, #0]
0xc5,0x00,0x80,0xf9 = prfm pldl3strm, [x6, #0]
0xe8,0x07,0x80,0xf9 = prfm plil1keep, [sp, #8]
0x69,0x00,0x80,0xf9 = prfm plil1strm, [x3, #0]
0xaa,0x08,0x80,0xf9 = prfm plil2keep, [x5, #16]
0x4b,0x00,0x80,0xf9 = prfm plil2strm, [x2, #0]
0xac,0x00,0x80,0xf9 = prfm plil3keep, [x5, #0]
0xcd,0x00,0x80,0xf9 = prfm plil3strm, [x6, #0]
0xf0,0x07,0x80,0xf9 = prfm pstl1keep, [sp, #8]
0x71,0x00,0x80,0xf9 = prfm pstl1strm, [x3, #0]
0xb2,0x08,0x80,0xf9 = prfm pstl2keep, [x5, #16]
0x53,0x00,0x80,0xf9 = prfm pstl2strm, [x2, #0]
0xb4,0x00,0x80,0xf9 = prfm pstl3keep, [x5, #0]
0xd5,0x00,0x80,0xf9 = prfm pstl3strm, [x6, #0]
0xef,0x03,0x80,0xf9 = prfm #15, [sp, #0]
0xff,0xff,0x7f,0x3d = ldr b31, [sp, #4095]
0x54,0xfc,0x7f,0x7d = ldr h20, [x2, #8190]
0x6a,0xfe,0x7f,0xbd = ldr s10, [x19, #16380]
0x43,0xfd,0x7f,0xfd = ldr d3, [x10, #32760]
0xec,0xff,0xbf,0x3d = str q12, [sp, #65520]
0xe3,0x6b,0x65,0x38 = ldrb w3, [sp, x5]
0x69,0x7b,0x66,0x38 = ldrb w9, [x27, x6, lsl #0]
0xca,0x6b,0xe7,0x38 = ldrsb w10, [x30, x7]
0xab,0xeb,0x63,0x38 = ldrb w11, [x29, x3, sxtx]
0x8c,0xfb,0x3f,0x38 = strb w12, [x28, xzr, sxtx #0]
0x4e,0x4b,0x66,0x38 = ldrb w14, [x26, w6, uxtw]
0x2f,0x5b,0xe7,0x38 = ldrsb w15, [x25, w7, uxtw #0]
0xf1,0xca,0x69,0x38 = ldrb w17, [x23, w9, sxtw]
0xd2,0xda,0xaa,0x38 = ldrsb x18, [x22, w10, sxtw #0]
0xe3,0x6b,0xe5,0x78 = ldrsh w3, [sp, x5]
0x69,0x6b,0xe6,0x78 = ldrsh w9, [x27, x6]
0xca,0x7b,0x67,0x78 = ldrh w10, [x30, x7, lsl #1]
0xab,0xeb,0x23,0x78 = strh w11, [x29, x3, sxtx]
0x8c,0xeb,0x7f,0x78 = ldrh w12, [x28, xzr, sxtx]
0x6d,0xfb,0xa5,0x78 = ldrsh x13, [x27, x5, sxtx #1]
0x4e,0x4b,0x66,0x78 = ldrh w14, [x26, w6, uxtw]
0x2f,0x4b,0x67,0x78 = ldrh w15, [x25, w7, uxtw]
0x10,0x5b,0xe8,0x78 = ldrsh w16, [x24, w8, uxtw #1]
0xf1,0xca,0x69,0x78 = ldrh w17, [x23, w9, sxtw]
0xd2,0xca,0x6a,0x78 = ldrh w18, [x22, w10, sxtw]
0xb3,0xda,0x3f,0x78 = strh w19, [x21, wzr, sxtw #1]
0xe3,0x6b,0x65,0xb8 = ldr w3, [sp, x5]
0x69,0x6b,0x66,0xbc = ldr s9, [x27, x6]
0xca,0x7b,0x67,0xb8 = ldr w10, [x30, x7, lsl #2]
0xab,0xeb,0x63,0xb8 = ldr w11, [x29, x3, sxtx]
0x8c,0xeb,0x3f,0xbc = str s12, [x28, xzr, sxtx]
0x6d,0xfb,0x25,0xb8 = str w13, [x27, x5, sxtx #2]
0x4e,0x4b,0x26,0xb8 = str w14, [x26, w6, uxtw]
0x2f,0x4b,0x67,0xb8 = ldr w15, [x25, w7, uxtw]
0x10,0x5b,0x68,0xb8 = ldr w16, [x24, w8, uxtw #2]
0xf1,0xca,0xa9,0xb8 = ldrsw x17, [x23, w9, sxtw]
0xd2,0xca,0x6a,0xb8 = ldr w18, [x22, w10, sxtw]
0xb3,0xda,0xbf,0xb8 = ldrsw x19, [x21, wzr, sxtw #2]
0xe3,0x6b,0x65,0xf8 = ldr x3, [sp, x5]
0x69,0x6b,0x26,0xf8 = str x9, [x27, x6]
0xca,0x7b,0x67,0xfc = ldr d10, [x30, x7, lsl #3]
0xab,0xeb,0x23,0xf8 = str x11, [x29, x3, sxtx]
0x8c,0xeb,0x7f,0xf8 = ldr x12, [x28, xzr, sxtx]
0x6d,0xfb,0x65,0xf8 = ldr x13, [x27, x5, sxtx #3]
0x40,0x4b,0xa6,0xf8 = prfm pldl1keep, [x26, w6, uxtw]
0x2f,0x4b,0x67,0xf8 = ldr x15, [x25, w7, uxtw]
0x10,0x5b,0x68,0xf8 = ldr x16, [x24, w8, uxtw #3]
0xf1,0xca,0x69,0xf8 = ldr x17, [x23, w9, sxtw]
0xd2,0xca,0x6a,0xf8 = ldr x18, [x22, w10, sxtw]
0xb3,0xda,0x3f,0xfc = str d19, [x21, wzr, sxtw #3]
0x06,0x68,0xa5,0xf8 = prfm #6, [x0, x5, lsl #0]
0xe3,0x6b,0xe5,0x3c = ldr q3, [sp, x5]
0x69,0x6b,0xe6,0x3c = ldr q9, [x27, x6]
0xca,0x7b,0xe7,0x3c = ldr q10, [x30, x7, lsl #4]
0xab,0xeb,0xa3,0x3c = str q11, [x29, x3, sxtx]
0x8c,0xeb,0xbf,0x3c = str q12, [x28, xzr, sxtx]
0x6d,0xfb,0xa5,0x3c = str q13, [x27, x5, sxtx #4]
0x4e,0x4b,0xe6,0x3c = ldr q14, [x26, w6, uxtw]
0x2f,0x4b,0xe7,0x3c = ldr q15, [x25, w7, uxtw]
0x10,0x5b,0xe8,0x3c = ldr q16, [x24, w8, uxtw #4]
0xf1,0xca,0xe9,0x3c = ldr q17, [x23, w9, sxtw]
0xd2,0xca,0xaa,0x3c = str q18, [x22, w10, sxtw]
0xb3,0xda,0xff,0x3c = ldr q19, [x21, wzr, sxtw #4]
0x49,0xf4,0x0f,0x38 = strb w9, [x2], #255
0x6a,0x14,0x00,0x38 = strb w10, [x3], #1
0x6a,0x04,0x10,0x38 = strb w10, [x3], #-256
0x49,0xf4,0x0f,0x78 = strh w9, [x2], #255
0x49,0x14,0x00,0x78 = strh w9, [x2], #1
0x6a,0x04,0x10,0x78 = strh w10, [x3], #-256
0xf3,0xf7,0x0f,0xb8 = str w19, [sp], #255
0xd4,0x17,0x00,0xb8 = str w20, [x30], #1
0x95,0x05,0x10,0xb8 = str w21, [x12], #-256
0x3f,0xf5,0x0f,0xf8 = str xzr, [x9], #255
0x62,0x14,0x00,0xf8 = str x2, [x3], #1
0x93,0x05,0x10,0xf8 = str x19, [x12], #-256
0x49,0xf4,0x4f,0x38 = ldrb w9, [x2], #255
0x6a,0x14,0x40,0x38 = ldrb w10, [x3], #1
0x6a,0x04,0x50,0x38 = ldrb w10, [x3], #-256
0x49,0xf4,0x4f,0x78 = ldrh w9, [x2], #255
0x49,0x14,0x40,0x78 = ldrh w9, [x2], #1
0x6a,0x04,0x50,0x78 = ldrh w10, [x3], #-256
0xf3,0xf7,0x4f,0xb8 = ldr w19, [sp], #255
0xd4,0x17,0x40,0xb8 = ldr w20, [x30], #1
0x95,0x05,0x50,0xb8 = ldr w21, [x12], #-256
0x3f,0xf5,0x4f,0xf8 = ldr xzr, [x9], #255
0x62,0x14,0x40,0xf8 = ldr x2, [x3], #1
0x93,0x05,0x50,0xf8 = ldr x19, [x12], #-256
0x3f,0xf5,0x8f,0x38 = ldrsb xzr, [x9], #255
0x62,0x14,0x80,0x38 = ldrsb x2, [x3], #1
0x93,0x05,0x90,0x38 = ldrsb x19, [x12], #-256
0x3f,0xf5,0x8f,0x78 = ldrsh xzr, [x9], #255
0x62,0x14,0x80,0x78 = ldrsh x2, [x3], #1
0x93,0x05,0x90,0x78 = ldrsh x19, [x12], #-256
0x3f,0xf5,0x8f,0xb8 = ldrsw xzr, [x9], #255
0x62,0x14,0x80,0xb8 = ldrsw x2, [x3], #1
0x93,0x05,0x90,0xb8 = ldrsw x19, [x12], #-256
0x3f,0xf5,0xcf,0x38 = ldrsb wzr, [x9], #255
0x62,0x14,0xc0,0x38 = ldrsb w2, [x3], #1
0x93,0x05,0xd0,0x38 = ldrsb w19, [x12], #-256
0x3f,0xf5,0xcf,0x78 = ldrsh wzr, [x9], #255
0x62,0x14,0xc0,0x78 = ldrsh w2, [x3], #1
0x93,0x05,0xd0,0x78 = ldrsh w19, [x12], #-256
0x00,0xf4,0x0f,0x3c = str b0, [x0], #255
0x63,0x14,0x00,0x3c = str b3, [x3], #1
0xe5,0x07,0x10,0x3c = str b5, [sp], #-256
0x4a,0xf5,0x0f,0x7c = str h10, [x10], #255
0xed,0x16,0x00,0x7c = str h13, [x23], #1
0xef,0x07,0x10,0x7c = str h15, [sp], #-256
0x94,0xf6,0x0f,0xbc = str s20, [x20], #255
0xf7,0x16,0x00,0xbc = str s23, [x23], #1
0x19,0x04,0x10,0xbc = str s25, [x0], #-256
0x94,0xf6,0x0f,0xfc = str d20, [x20], #255
0xf7,0x16,0x00,0xfc = str d23, [x23], #1
0x19,0x04,0x10,0xfc = str d25, [x0], #-256
0x00,0xf4,0x4f,0x3c = ldr b0, [x0], #255
0x63,0x14,0x40,0x3c = ldr b3, [x3], #1
0xe5,0x07,0x50,0x3c = ldr b5, [sp], #-256
0x4a,0xf5,0x4f,0x7c = ldr h10, [x10], #255
0xed,0x16,0x40,0x7c = ldr h13, [x23], #1
0xef,0x07,0x50,0x7c = ldr h15, [sp], #-256
0x94,0xf6,0x4f,0xbc = ldr s20, [x20], #255
0xf7,0x16,0x40,0xbc = ldr s23, [x23], #1
0x19,0x04,0x50,0xbc = ldr s25, [x0], #-256
0x94,0xf6,0x4f,0xfc = ldr d20, [x20], #255
0xf7,0x16,0x40,0xfc = ldr d23, [x23], #1
0x19,0x04,0x50,0xfc = ldr d25, [x0], #-256
0x34,0xf4,0xcf,0x3c = ldr q20, [x1], #255
0x37,0x15,0xc0,0x3c = ldr q23, [x9], #1
0x99,0x06,0xd0,0x3c = ldr q25, [x20], #-256
0x2a,0xf4,0x8f,0x3c = str q10, [x1], #255
0xf6,0x17,0x80,0x3c = str q22, [sp], #1
0x95,0x06,0x90,0x3c = str q21, [x20], #-256
0x83,0x0c,0x40,0xf8 = ldr x3, [x4, #0]!
0xff,0x0f,0x40,0xf8 = ldr xzr, [sp, #0]!
0x49,0xfc,0x0f,0x38 = strb w9, [x2, #255]!
0x6a,0x1c,0x00,0x38 = strb w10, [x3, #1]!
0x6a,0x0c,0x10,0x38 = strb w10, [x3, #-256]!
0x49,0xfc,0x0f,0x78 = strh w9, [x2, #255]!
0x49,0x1c,0x00,0x78 = strh w9, [x2, #1]!
0x6a,0x0c,0x10,0x78 = strh w10, [x3, #-256]!
0xf3,0xff,0x0f,0xb8 = str w19, [sp, #255]!
0xd4,0x1f,0x00,0xb8 = str w20, [x30, #1]!
0x95,0x0d,0x10,0xb8 = str w21, [x12, #-256]!
0x3f,0xfd,0x0f,0xf8 = str xzr, [x9, #255]!
0x62,0x1c,0x00,0xf8 = str x2, [x3, #1]!
0x93,0x0d,0x10,0xf8 = str x19, [x12, #-256]!
0x49,0xfc,0x4f,0x38 = ldrb w9, [x2, #255]!
0x6a,0x1c,0x40,0x38 = ldrb w10, [x3, #1]!
0x6a,0x0c,0x50,0x38 = ldrb w10, [x3, #-256]!
0x49,0xfc,0x4f,0x78 = ldrh w9, [x2, #255]!
0x49,0x1c,0x40,0x78 = ldrh w9, [x2, #1]!
0x6a,0x0c,0x50,0x78 = ldrh w10, [x3, #-256]!
0xf3,0xff,0x4f,0xb8 = ldr w19, [sp, #255]!
0xd4,0x1f,0x40,0xb8 = ldr w20, [x30, #1]!
0x95,0x0d,0x50,0xb8 = ldr w21, [x12, #-256]!
0x3f,0xfd,0x4f,0xf8 = ldr xzr, [x9, #255]!
0x62,0x1c,0x40,0xf8 = ldr x2, [x3, #1]!
0x93,0x0d,0x50,0xf8 = ldr x19, [x12, #-256]!
0x3f,0xfd,0x8f,0x38 = ldrsb xzr, [x9, #255]!
0x62,0x1c,0x80,0x38 = ldrsb x2, [x3, #1]!
0x93,0x0d,0x90,0x38 = ldrsb x19, [x12, #-256]!
0x3f,0xfd,0x8f,0x78 = ldrsh xzr, [x9, #255]!
0x62,0x1c,0x80,0x78 = ldrsh x2, [x3, #1]!
0x93,0x0d,0x90,0x78 = ldrsh x19, [x12, #-256]!
0x3f,0xfd,0x8f,0xb8 = ldrsw xzr, [x9, #255]!
0x62,0x1c,0x80,0xb8 = ldrsw x2, [x3, #1]!
0x93,0x0d,0x90,0xb8 = ldrsw x19, [x12, #-256]!
0x3f,0xfd,0xcf,0x38 = ldrsb wzr, [x9, #255]!
0x62,0x1c,0xc0,0x38 = ldrsb w2, [x3, #1]!
0x93,0x0d,0xd0,0x38 = ldrsb w19, [x12, #-256]!
0x3f,0xfd,0xcf,0x78 = ldrsh wzr, [x9, #255]!
0x62,0x1c,0xc0,0x78 = ldrsh w2, [x3, #1]!
0x93,0x0d,0xd0,0x78 = ldrsh w19, [x12, #-256]!
0x00,0xfc,0x0f,0x3c = str b0, [x0, #255]!
0x63,0x1c,0x00,0x3c = str b3, [x3, #1]!
0xe5,0x0f,0x10,0x3c = str b5, [sp, #-256]!
0x4a,0xfd,0x0f,0x7c = str h10, [x10, #255]!
0xed,0x1e,0x00,0x7c = str h13, [x23, #1]!
0xef,0x0f,0x10,0x7c = str h15, [sp, #-256]!
0x94,0xfe,0x0f,0xbc = str s20, [x20, #255]!
0xf7,0x1e,0x00,0xbc = str s23, [x23, #1]!
0x19,0x0c,0x10,0xbc = str s25, [x0, #-256]!
0x94,0xfe,0x0f,0xfc = str d20, [x20, #255]!
0xf7,0x1e,0x00,0xfc = str d23, [x23, #1]!
0x19,0x0c,0x10,0xfc = str d25, [x0, #-256]!
0x00,0xfc,0x4f,0x3c = ldr b0, [x0, #255]!
0x63,0x1c,0x40,0x3c = ldr b3, [x3, #1]!
0xe5,0x0f,0x50,0x3c = ldr b5, [sp, #-256]!
0x4a,0xfd,0x4f,0x7c = ldr h10, [x10, #255]!
0xed,0x1e,0x40,0x7c = ldr h13, [x23, #1]!
0xef,0x0f,0x50,0x7c = ldr h15, [sp, #-256]!
0x94,0xfe,0x4f,0xbc = ldr s20, [x20, #255]!
0xf7,0x1e,0x40,0xbc = ldr s23, [x23, #1]!
0x19,0x0c,0x50,0xbc = ldr s25, [x0, #-256]!
0x94,0xfe,0x4f,0xfc = ldr d20, [x20, #255]!
0xf7,0x1e,0x40,0xfc = ldr d23, [x23, #1]!
0x19,0x0c,0x50,0xfc = ldr d25, [x0, #-256]!
0x34,0xfc,0xcf,0x3c = ldr q20, [x1, #255]!
0x37,0x1d,0xc0,0x3c = ldr q23, [x9, #1]!
0x99,0x0e,0xd0,0x3c = ldr q25, [x20, #-256]!
0x2a,0xfc,0x8f,0x3c = str q10, [x1, #255]!
0xf6,0x1f,0x80,0x3c = str q22, [sp, #1]!
0x95,0x0e,0x90,0x3c = str q21, [x20, #-256]!
0xe9,0x0b,0x00,0x38 = sttrb w9, [sp]
0x9f,0xf9,0x0f,0x78 = sttrh wzr, [x12, #255]
0x10,0x08,0x10,0xb8 = sttr w16, [x0, #-256]
0xdc,0x19,0x00,0xf8 = sttr x28, [x14, #1]
0x81,0xfa,0x4f,0x38 = ldtrb w1, [x20, #255]
0x34,0xf8,0x4f,0x78 = ldtrh w20, [x1, #255]
0xec,0xfb,0x4f,0xb8 = ldtr w12, [sp, #255]
0x9f,0xf9,0x4f,0xf8 = ldtr xzr, [x12, #255]
0xe9,0x08,0x90,0x38 = ldtrsb x9, [x7, #-256]
0x71,0x0a,0x90,0x78 = ldtrsh x17, [x19, #-256]
0xf4,0x09,0x90,0xb8 = ldtrsw x20, [x15, #-256]
0x33,0x08,0xd0,0x38 = ldtrsb w19, [x1, #-256]
0xaf,0x0a,0xd0,0x78 = ldtrsh w15, [x21, #-256]
0xe3,0x17,0x40,0x29 = ldp w3, w5, [sp]
0xff,0xa7,0x1f,0x29 = stp wzr, w9, [sp, #252]
0xe2,0x7f,0x60,0x29 = ldp w2, wzr, [sp, #-256]
0xe9,0xab,0x40,0x29 = ldp w9, w10, [sp, #4]
0xe9,0xab,0x40,0x69 = ldpsw x9, x10, [sp, #4]
0x49,0x28,0x60,0x69 = ldpsw x9, x10, [x2, #-256]
0xf4,0xfb,0x5f,0x69 = ldpsw x20, x30, [sp, #252]
0x55,0xf4,0x5f,0xa9 = ldp x21, x29, [x2, #504]
0x76,0x5c,0x60,0xa9 = ldp x22, x23, [x3, #-512]
0x98,0xe4,0x40,0xa9 = ldp x24, x25, [x4, #8]
0xfd,0xf3,0x5f,0x2d = ldp s29, s28, [sp, #252]
0xfb,0x6b,0x20,0x2d = stp s27, s26, [sp, #-256]
0x61,0x88,0x45,0x2d = ldp s1, s2, [x3, #44]
0x23,0x95,0x1f,0x6d = stp d3, d5, [x9, #504]
0x47,0x2d,0x20,0x6d = stp d7, d11, [x10, #-512]
0xc2,0x8f,0x7f,0x6d = ldp d2, d3, [x30, #-8]
0xe3,0x17,0x00,0xad = stp q3, q5, [sp]
0xf1,0xcf,0x1f,0xad = stp q17, q19, [sp, #1008]
0x37,0x74,0x60,0xad = ldp q23, q29, [x1, #-1024]
0xe3,0x17,0xc0,0x28 = ldp w3, w5, [sp], #0
0xff,0xa7,0x9f,0x28 = stp wzr, w9, [sp], #252
0xe2,0x7f,0xe0,0x28 = ldp w2, wzr, [sp], #-256
0xe9,0xab,0xc0,0x28 = ldp w9, w10, [sp], #4
0xe9,0xab,0xc0,0x68 = ldpsw x9, x10, [sp], #4
0x49,0x28,0xe0,0x68 = ldpsw x9, x10, [x2], #-256
0xf4,0xfb,0xdf,0x68 = ldpsw x20, x30, [sp], #252
0x55,0xf4,0xdf,0xa8 = ldp x21, x29, [x2], #504
0x76,0x5c,0xe0,0xa8 = ldp x22, x23, [x3], #-512
0x98,0xe4,0xc0,0xa8 = ldp x24, x25, [x4], #8
0xfd,0xf3,0xdf,0x2c = ldp s29, s28, [sp], #252
0xfb,0x6b,0xa0,0x2c = stp s27, s26, [sp], #-256
0x61,0x88,0xc5,0x2c = ldp s1, s2, [x3], #44
0x23,0x95,0x9f,0x6c = stp d3, d5, [x9], #504
0x47,0x2d,0xa0,0x6c = stp d7, d11, [x10], #-512
0xc2,0x8f,0xff,0x6c = ldp d2, d3, [x30], #-8
0xe3,0x17,0x80,0xac = stp q3, q5, [sp], #0
0xf1,0xcf,0x9f,0xac = stp q17, q19, [sp], #1008
0x37,0x74,0xe0,0xac = ldp q23, q29, [x1], #-1024
0xe3,0x17,0xc0,0x29 = ldp w3, w5, [sp, #0]!
0xff,0xa7,0x9f,0x29 = stp wzr, w9, [sp, #252]!
0xe2,0x7f,0xe0,0x29 = ldp w2, wzr, [sp, #-256]!
0xe9,0xab,0xc0,0x29 = ldp w9, w10, [sp, #4]!
0xe9,0xab,0xc0,0x69 = ldpsw x9, x10, [sp, #4]!
0x49,0x28,0xe0,0x69 = ldpsw x9, x10, [x2, #-256]!
0xf4,0xfb,0xdf,0x69 = ldpsw x20, x30, [sp, #252]!
0x55,0xf4,0xdf,0xa9 = ldp x21, x29, [x2, #504]!
0x76,0x5c,0xe0,0xa9 = ldp x22, x23, [x3, #-512]!
0x98,0xe4,0xc0,0xa9 = ldp x24, x25, [x4, #8]!
0xfd,0xf3,0xdf,0x2d = ldp s29, s28, [sp, #252]!
0xfb,0x6b,0xa0,0x2d = stp s27, s26, [sp, #-256]!
0x61,0x88,0xc5,0x2d = ldp s1, s2, [x3, #44]!
0x23,0x95,0x9f,0x6d = stp d3, d5, [x9, #504]!
0x47,0x2d,0xa0,0x6d = stp d7, d11, [x10, #-512]!
0xc2,0x8f,0xff,0x6d = ldp d2, d3, [x30, #-8]!
0xe3,0x17,0x80,0xad = stp q3, q5, [sp, #0]!
0xf1,0xcf,0x9f,0xad = stp q17, q19, [sp, #1008]!
0x37,0x74,0xe0,0xad = ldp q23, q29, [x1, #-1024]!
0xe3,0x17,0x40,0x28 = ldnp w3, w5, [sp]
0xff,0xa7,0x1f,0x28 = stnp wzr, w9, [sp, #252]
0xe2,0x7f,0x60,0x28 = ldnp w2, wzr, [sp, #-256]
0xe9,0xab,0x40,0x28 = ldnp w9, w10, [sp, #4]
0x55,0xf4,0x5f,0xa8 = ldnp x21, x29, [x2, #504]
0x76,0x5c,0x60,0xa8 = ldnp x22, x23, [x3, #-512]
0x98,0xe4,0x40,0xa8 = ldnp x24, x25, [x4, #8]
0xfd,0xf3,0x5f,0x2c = ldnp s29, s28, [sp, #252]
0xfb,0x6b,0x20,0x2c = stnp s27, s26, [sp, #-256]
0x61,0x88,0x45,0x2c = ldnp s1, s2, [x3, #44]
0x23,0x95,0x1f,0x6c = stnp d3, d5, [x9, #504]
0x47,0x2d,0x20,0x6c = stnp d7, d11, [x10, #-512]
0xc2,0x8f,0x7f,0x6c = ldnp d2, d3, [x30, #-8]
0xe3,0x17,0x00,0xac = stnp q3, q5, [sp]
0xf1,0xcf,0x1f,0xac = stnp q17, q19, [sp, #1008]
0x37,0x74,0x60,0xac = ldnp q23, q29, [x1, #-1024]
0x23,0x3d,0x10,0x32 = orr w3, w9, #0xffff0000
0x5f,0x29,0x03,0x32 = orr wsp, w10, #0xe00000ff
0x49,0x25,0x00,0x32 = orr w9, w10, #0x3ff
0xee,0x81,0x01,0x12 = and w14, w15, #0x80008000
0xac,0xad,0x0a,0x12 = and w12, w13, #0xffc3ffc3
0xeb,0x87,0x00,0x12 = and w11, wzr, #0x30003
0xc3,0xc8,0x03,0x52 = eor w3, w6, #0xe0e0e0e0
0xff,0xc7,0x00,0x52 = eor wsp, wzr, #0x3030303
0x30,0xc6,0x01,0x52 = eor w16, w17, #0x81818181
0x5f,0xe6,0x02,0x72 = ands wzr, w18, #0xcccccccc
0x93,0xe6,0x00,0x72 = ands w19, w20, #0x33333333
0xd5,0xe6,0x01,0x72 = ands w21, w22, #0x99999999
0x7f,0xf0,0x01,0x72 = ands wzr, w3, #0xaaaaaaaa
0xff,0xf3,0x00,0x72 = ands wzr, wzr, #0x55555555
0xa3,0x84,0x66,0xd2 = eor x3, x5, #0xffffffffc000000
0x49,0xb9,0x40,0x92 = and x9, x10, #0x7fffffffffff
0x8b,0x31,0x41,0xb2 = orr x11, x12, #0x8000000000000fff
0x23,0x3d,0x10,0xb2 = orr x3, x9, #0xffff0000ffff0000
0x5f,0x29,0x03,0xb2 = orr sp, x10, #0xe00000ffe00000ff
0x49,0x25,0x00,0xb2 = orr x9, x10, #0x3ff000003ff
0xee,0x81,0x01,0x92 = and x14, x15, #0x8000800080008000
0xac,0xad,0x0a,0x92 = and x12, x13, #0xffc3ffc3ffc3ffc3
0xeb,0x87,0x00,0x92 = and x11, xzr, #0x3000300030003
0xc3,0xc8,0x03,0xd2 = eor x3, x6, #0xe0e0e0e0e0e0e0e0
0xff,0xc7,0x00,0xd2 = eor sp, xzr, #0x303030303030303
0x30,0xc6,0x01,0xd2 = eor x16, x17, #0x8181818181818181
0x5f,0xe6,0x02,0xf2 = ands xzr, x18, #0xcccccccccccccccc
0x93,0xe6,0x00,0xf2 = ands x19, x20, #0x3333333333333333
0xd5,0xe6,0x01,0xf2 = ands x21, x22, #0x9999999999999999
0x7f,0xf0,0x01,0xf2 = ands xzr, x3, #0xaaaaaaaaaaaaaaaa
0xff,0xf3,0x00,0xf2 = ands xzr, xzr, #0x5555555555555555
0xe3,0x8f,0x00,0x32 = orr w3, wzr, #0xf000f
0xea,0xf3,0x01,0xb2 = orr x10, xzr, #0xaaaaaaaaaaaaaaaa
0xec,0x02,0x15,0x0a = and w12, w23, w21
0xf0,0x05,0x01,0x0a = and w16, w15, w1, lsl #1
0x89,0x7c,0x0a,0x0a = and w9, w4, w10, lsl #31
0xc3,0x03,0x0b,0x0a = and w3, w30, w11
0xa3,0xfc,0x07,0x8a = and x3, x5, x7, lsl #63
0xc5,0x11,0x93,0x8a = and x5, x14, x19, asr #4
0x23,0x7e,0xd3,0x0a = and w3, w17, w19, ror #31
0x40,0x44,0x5f,0x0a = and w0, w2, wzr, lsr #17
0xc3,0x03,0x8b,0x0a = and w3, w30, w11, asr #0
0x9f,0x00,0x1a,0x8a = and xzr, x4, x26
0xe3,0x03,0xd4,0x0a = and w3, wzr, w20, ror #0
0x87,0xfe,0x9f,0x8a = and x7, x20, xzr, asr #63
0x8d,0xbe,0x2e,0x8a = bic x13, x20, x14, lsl #47
0xe2,0x00,0x29,0x0a = bic w2, w7, w9
0xe2,0x7c,0x80,0x2a = orr w2, w7, w0, asr #31
0x28,0x31,0x0a,0xaa = orr x8, x9, x10, lsl #12
0xa3,0x00,0xa7,0xaa = orn x3, x5, x7, asr #0
0xa2,0x00,0x3d,0x2a = orn w2, w5, w29
0xe7,0x07,0x09,0x6a = ands w7, wzr, w9, lsl #1
0xa3,0xfc,0xd4,0xea = ands x3, x5, x20, ror #63
0xa3,0x00,0x27,0x6a = bics w3, w5, w7
0xe3,0x07,0x23,0xea = bics x3, xzr, x3, lsl #1
0x7f,0x7c,0x07,0x6a = tst w3, w7, lsl #31
0x5f,0x00,0x94,0xea = tst x2, x20, asr #0
0xe3,0x03,0x06,0xaa = mov x3, x6
0xe3,0x03,0x1f,0xaa = mov x3, xzr
0xff,0x03,0x02,0x2a = mov wzr, w2
0xe3,0x03,0x05,0x2a = mov w3, w5
0xe1,0xff,0x9f,0x52 = movz w1, #65535
0x02,0x00,0xa0,0x52 = movz w2, #0, lsl #16
0x42,0x9a,0x80,0x12 = movn w2, #1234
0x42,0x9a,0xc0,0xd2 = movz x2, #1234, lsl #32
0x3f,0x1c,0xe2,0xf2 = movk xzr, #4321, lsl #48
0x1e,0x00,0x00,0xb0 = adrp x30, #4096
0x14,0x00,0x00,0x10 = adr x20, #0
0xe9,0xff,0xff,0x70 = adr x9, #-1
0xe5,0xff,0x7f,0x70 = adr x5, #1048575
0xe9,0xff,0x7f,0x70 = adr x9, #1048575
0x02,0x00,0x80,0x10 = adr x2, #-1048576
0xe9,0xff,0x7f,0xf0 = adrp x9, #4294963200
0x14,0x00,0x80,0x90 = adrp x20, #-4294967296
0x1f,0x20,0x03,0xd5 = nop 
0xff,0x2f,0x03,0xd5 = hint #127
0x1f,0x20,0x03,0xd5 = nop 
0x3f,0x20,0x03,0xd5 = yield 
0x5f,0x20,0x03,0xd5 = wfe 
0x7f,0x20,0x03,0xd5 = wfi 
0x9f,0x20,0x03,0xd5 = sev 
0xbf,0x20,0x03,0xd5 = sevl 
0x5f,0x3f,0x03,0xd5 = clrex 
0x5f,0x30,0x03,0xd5 = clrex #0
0x5f,0x37,0x03,0xd5 = clrex #7
0x5f,0x3f,0x03,0xd5 = clrex 
0x9f,0x30,0x03,0xd5 = dsb #0
0x9f,0x3c,0x03,0xd5 = dsb #12
0x9f,0x3f,0x03,0xd5 = dsb sy
0x9f,0x31,0x03,0xd5 = dsb oshld
0x9f,0x32,0x03,0xd5 = dsb oshst
0x9f,0x33,0x03,0xd5 = dsb osh
0x9f,0x35,0x03,0xd5 = dsb nshld
0x9f,0x36,0x03,0xd5 = dsb nshst
0x9f,0x37,0x03,0xd5 = dsb nsh
0x9f,0x39,0x03,0xd5 = dsb ishld
0x9f,0x3a,0x03,0xd5 = dsb ishst
0x9f,0x3b,0x03,0xd5 = dsb ish
0x9f,0x3d,0x03,0xd5 = dsb ld
0x9f,0x3e,0x03,0xd5 = dsb st
0x9f,0x3f,0x03,0xd5 = dsb sy
0xbf,0x30,0x03,0xd5 = dmb #0
0xbf,0x3c,0x03,0xd5 = dmb #12
0xbf,0x3f,0x03,0xd5 = dmb sy
0xbf,0x31,0x03,0xd5 = dmb oshld
0xbf,0x32,0x03,0xd5 = dmb oshst
0xbf,0x33,0x03,0xd5 = dmb osh
0xbf,0x35,0x03,0xd5 = dmb nshld
0xbf,0x36,0x03,0xd5 = dmb nshst
0xbf,0x37,0x03,0xd5 = dmb nsh
0xbf,0x39,0x03,0xd5 = dmb ishld
0xbf,0x3a,0x03,0xd5 = dmb ishst
0xbf,0x3b,0x03,0xd5 = dmb ish
0xbf,0x3d,0x03,0xd5 = dmb ld
0xbf,0x3e,0x03,0xd5 = dmb st
0xbf,0x3f,0x03,0xd5 = dmb sy
0xdf,0x3f,0x03,0xd5 = isb 
0xdf,0x3f,0x03,0xd5 = isb 
0xdf,0x3c,0x03,0xd5 = isb #12
0xbf,0x40,0x00,0xd5 = msr spsel, #0
0xdf,0x4f,0x03,0xd5 = msr daifset, #15
0xff,0x4c,0x03,0xd5 = msr daifclr, #12
0xe5,0x59,0x0f,0xd5 = sys #7, c5, c9, #7, x5
0x5f,0xff,0x08,0xd5 = sys #0, c15, c15, #2, xzr
0xe9,0x59,0x2f,0xd5 = sysl x9, #7, c5, c9, #7
0x41,0xff,0x28,0xd5 = sysl x1, #0, c15, c15, #2
0x1f,0x71,0x08,0xd5 = ic ialluis
0x1f,0x75,0x08,0xd5 = ic iallu
0x29,0x75,0x0b,0xd5 = ic ivau, x9
0x2c,0x74,0x0b,0xd5 = dc zva, x12
0x3f,0x76,0x08,0xd5 = dc ivac, xzr
0x42,0x76,0x08,0xd5 = dc isw, x2
0x29,0x7a,0x0b,0xd5 = dc cvac, x9
0x4a,0x7a,0x08,0xd5 = dc csw, x10
0x20,0x7b,0x0b,0xd5 = dc cvau, x0
0x23,0x7e,0x0b,0xd5 = dc civac, x3
0x5e,0x7e,0x08,0xd5 = dc cisw, x30
0x13,0x78,0x08,0xd5 = at s1e1r, x19
0x13,0x78,0x0c,0xd5 = at s1e2r, x19
0x13,0x78,0x0e,0xd5 = at s1e3r, x19
0x33,0x78,0x08,0xd5 = at s1e1w, x19
0x33,0x78,0x0c,0xd5 = at s1e2w, x19
0x33,0x78,0x0e,0xd5 = at s1e3w, x19
0x53,0x78,0x08,0xd5 = at s1e0r, x19
0x73,0x78,0x08,0xd5 = at s1e0w, x19
0x94,0x78,0x0c,0xd5 = at s12e1r, x20
0xb4,0x78,0x0c,0xd5 = at s12e1w, x20
0xd4,0x78,0x0c,0xd5 = at s12e0r, x20
0xf4,0x78,0x0c,0xd5 = at s12e0w, x20
0x24,0x80,0x0c,0xd5 = tlbi ipas2e1is, x4
0xa9,0x80,0x0c,0xd5 = tlbi ipas2le1is, x9
0x1f,0x83,0x08,0xd5 = tlbi vmalle1is
0x1f,0x83,0x0c,0xd5 = tlbi alle2is
0x1f,0x83,0x0e,0xd5 = tlbi alle3is
0x21,0x83,0x08,0xd5 = tlbi vae1is, x1
0x22,0x83,0x0c,0xd5 = tlbi vae2is, x2
0x23,0x83,0x0e,0xd5 = tlbi vae3is, x3
0x45,0x83,0x08,0xd5 = tlbi aside1is, x5
0x69,0x83,0x08,0xd5 = tlbi vaae1is, x9
0x9f,0x83,0x0c,0xd5 = tlbi alle1is
0xaa,0x83,0x08,0xd5 = tlbi vale1is, x10
0xab,0x83,0x0c,0xd5 = tlbi vale2is, x11
0xad,0x83,0x0e,0xd5 = tlbi vale3is, x13
0xdf,0x83,0x0c,0xd5 = tlbi vmalls12e1is
0xee,0x83,0x08,0xd5 = tlbi vaale1is, x14
0x2f,0x84,0x0c,0xd5 = tlbi ipas2e1, x15
0xb0,0x84,0x0c,0xd5 = tlbi ipas2le1, x16
0x1f,0x87,0x08,0xd5 = tlbi vmalle1
0x1f,0x87,0x0c,0xd5 = tlbi alle2
0x1f,0x87,0x0e,0xd5 = tlbi alle3
0x31,0x87,0x08,0xd5 = tlbi vae1, x17
0x32,0x87,0x0c,0xd5 = tlbi vae2, x18
0x33,0x87,0x0e,0xd5 = tlbi vae3, x19
0x54,0x87,0x08,0xd5 = tlbi aside1, x20
0x75,0x87,0x08,0xd5 = tlbi vaae1, x21
0x9f,0x87,0x0c,0xd5 = tlbi alle1
0xb6,0x87,0x08,0xd5 = tlbi vale1, x22
0xb7,0x87,0x0c,0xd5 = tlbi vale2, x23
0xb8,0x87,0x0e,0xd5 = tlbi vale3, x24
0xdf,0x87,0x0c,0xd5 = tlbi vmalls12e1
0xf9,0x87,0x08,0xd5 = tlbi vaale1, x25
0x0c,0x00,0x12,0xd5 = msr teecr32_el1, x12
0x4c,0x00,0x10,0xd5 = msr osdtrrx_el1, x12
0x0c,0x02,0x10,0xd5 = msr mdccint_el1, x12
0x4c,0x02,0x10,0xd5 = msr mdscr_el1, x12
0x4c,0x03,0x10,0xd5 = msr osdtrtx_el1, x12
0x0c,0x04,0x13,0xd5 = msr dbgdtr_el0, x12
0x0c,0x05,0x13,0xd5 = msr dbgdtrtx_el0, x12
0x4c,0x06,0x10,0xd5 = msr oseccr_el1, x12
0x0c,0x07,0x14,0xd5 = msr dbgvcr32_el2, x12
0x8c,0x00,0x10,0xd5 = msr dbgbvr0_el1, x12
0x8c,0x01,0x10,0xd5 = msr dbgbvr1_el1, x12
0x8c,0x02,0x10,0xd5 = msr dbgbvr2_el1, x12
0x8c,0x03,0x10,0xd5 = msr dbgbvr3_el1, x12
0x8c,0x04,0x10,0xd5 = msr dbgbvr4_el1, x12
0x8c,0x05,0x10,0xd5 = msr dbgbvr5_el1, x12
0x8c,0x06,0x10,0xd5 = msr dbgbvr6_el1, x12
0x8c,0x07,0x10,0xd5 = msr dbgbvr7_el1, x12
0x8c,0x08,0x10,0xd5 = msr dbgbvr8_el1, x12
0x8c,0x09,0x10,0xd5 = msr dbgbvr9_el1, x12
0x8c,0x0a,0x10,0xd5 = msr dbgbvr10_el1, x12
0x8c,0x0b,0x10,0xd5 = msr dbgbvr11_el1, x12
0x8c,0x0c,0x10,0xd5 = msr dbgbvr12_el1, x12
0x8c,0x0d,0x10,0xd5 = msr dbgbvr13_el1, x12
0x8c,0x0e,0x10,0xd5 = msr dbgbvr14_el1, x12
0x8c,0x0f,0x10,0xd5 = msr dbgbvr15_el1, x12
0xac,0x00,0x10,0xd5 = msr dbgbcr0_el1, x12
0xac,0x01,0x10,0xd5 = msr dbgbcr1_el1, x12
0xac,0x02,0x10,0xd5 = msr dbgbcr2_el1, x12
0xac,0x03,0x10,0xd5 = msr dbgbcr3_el1, x12
0xac,0x04,0x10,0xd5 = msr dbgbcr4_el1, x12
0xac,0x05,0x10,0xd5 = msr dbgbcr5_el1, x12
0xac,0x06,0x10,0xd5 = msr dbgbcr6_el1, x12
0xac,0x07,0x10,0xd5 = msr dbgbcr7_el1, x12
0xac,0x08,0x10,0xd5 = msr dbgbcr8_el1, x12
0xac,0x09,0x10,0xd5 = msr dbgbcr9_el1, x12
0xac,0x0a,0x10,0xd5 = msr dbgbcr10_el1, x12
0xac,0x0b,0x10,0xd5 = msr dbgbcr11_el1, x12
0xac,0x0c,0x10,0xd5 = msr dbgbcr12_el1, x12
0xac,0x0d,0x10,0xd5 = msr dbgbcr13_el1, x12
0xac,0x0e,0x10,0xd5 = msr dbgbcr14_el1, x12
0xac,0x0f,0x10,0xd5 = msr dbgbcr15_el1, x12
0xcc,0x00,0x10,0xd5 = msr dbgwvr0_el1, x12
0xcc,0x01,0x10,0xd5 = msr dbgwvr1_el1, x12
0xcc,0x02,0x10,0xd5 = msr dbgwvr2_el1, x12
0xcc,0x03,0x10,0xd5 = msr dbgwvr3_el1, x12
0xcc,0x04,0x10,0xd5 = msr dbgwvr4_el1, x12
0xcc,0x05,0x10,0xd5 = msr dbgwvr5_el1, x12
0xcc,0x06,0x10,0xd5 = msr dbgwvr6_el1, x12
0xcc,0x07,0x10,0xd5 = msr dbgwvr7_el1, x12
0xcc,0x08,0x10,0xd5 = msr dbgwvr8_el1, x12
0xcc,0x09,0x10,0xd5 = msr dbgwvr9_el1, x12
0xcc,0x0a,0x10,0xd5 = msr dbgwvr10_el1, x12
0xcc,0x0b,0x10,0xd5 = msr dbgwvr11_el1, x12
0xcc,0x0c,0x10,0xd5 = msr dbgwvr12_el1, x12
0xcc,0x0d,0x10,0xd5 = msr dbgwvr13_el1, x12
0xcc,0x0e,0x10,0xd5 = msr dbgwvr14_el1, x12
0xcc,0x0f,0x10,0xd5 = msr dbgwvr15_el1, x12
0xec,0x00,0x10,0xd5 = msr dbgwcr0_el1, x12
0xec,0x01,0x10,0xd5 = msr dbgwcr1_el1, x12
0xec,0x02,0x10,0xd5 = msr dbgwcr2_el1, x12
0xec,0x03,0x10,0xd5 = msr dbgwcr3_el1, x12
0xec,0x04,0x10,0xd5 = msr dbgwcr4_el1, x12
0xec,0x05,0x10,0xd5 = msr dbgwcr5_el1, x12
0xec,0x06,0x10,0xd5 = msr dbgwcr6_el1, x12
0xec,0x07,0x10,0xd5 = msr dbgwcr7_el1, x12
0xec,0x08,0x10,0xd5 = msr dbgwcr8_el1, x12
0xec,0x09,0x10,0xd5 = msr dbgwcr9_el1, x12
0xec,0x0a,0x10,0xd5 = msr dbgwcr10_el1, x12
0xec,0x0b,0x10,0xd5 = msr dbgwcr11_el1, x12
0xec,0x0c,0x10,0xd5 = msr dbgwcr12_el1, x12
0xec,0x0d,0x10,0xd5 = msr dbgwcr13_el1, x12
0xec,0x0e,0x10,0xd5 = msr dbgwcr14_el1, x12
0xec,0x0f,0x10,0xd5 = msr dbgwcr15_el1, x12
0x0c,0x10,0x12,0xd5 = msr teehbr32_el1, x12
0x8c,0x10,0x10,0xd5 = msr oslar_el1, x12
0x8c,0x13,0x10,0xd5 = msr osdlr_el1, x12
0x8c,0x14,0x10,0xd5 = msr dbgprcr_el1, x12
0xcc,0x78,0x10,0xd5 = msr dbgclaimset_el1, x12
0xcc,0x79,0x10,0xd5 = msr dbgclaimclr_el1, x12
0x0c,0x00,0x1a,0xd5 = msr csselr_el1, x12
0x0c,0x00,0x1c,0xd5 = msr vpidr_el2, x12
0xac,0x00,0x1c,0xd5 = msr vmpidr_el2, x12
0x0c,0x10,0x18,0xd5 = msr sctlr_el1, x12
0x0c,0x10,0x1c,0xd5 = msr sctlr_el2, x12
0x0c,0x10,0x1e,0xd5 = msr sctlr_el3, x12
0x2c,0x10,0x18,0xd5 = msr actlr_el1, x12
0x2c,0x10,0x1c,0xd5 = msr actlr_el2, x12
0x2c,0x10,0x1e,0xd5 = msr actlr_el3, x12
0x4c,0x10,0x18,0xd5 = msr cpacr_el1, x12
0x0c,0x11,0x1c,0xd5 = msr hcr_el2, x12
0x0c,0x11,0x1e,0xd5 = msr scr_el3, x12
0x2c,0x11,0x1c,0xd5 = msr mdcr_el2, x12
0x2c,0x11,0x1e,0xd5 = msr sder32_el3, x12
0x4c,0x11,0x1c,0xd5 = msr cptr_el2, x12
0x4c,0x11,0x1e,0xd5 = msr cptr_el3, x12
0x6c,0x11,0x1c,0xd5 = msr hstr_el2, x12
0xec,0x11,0x1c,0xd5 = msr hacr_el2, x12
0x2c,0x13,0x1e,0xd5 = msr mdcr_el3, x12
0x0c,0x20,0x18,0xd5 = msr ttbr0_el1, x12
0x0c,0x20,0x1c,0xd5 = msr ttbr0_el2, x12
0x0c,0x20,0x1e,0xd5 = msr ttbr0_el3, x12
0x2c,0x20,0x18,0xd5 = msr ttbr1_el1, x12
0x4c,0x20,0x18,0xd5 = msr tcr_el1, x12
0x4c,0x20,0x1c,0xd5 = msr tcr_el2, x12
0x4c,0x20,0x1e,0xd5 = msr tcr_el3, x12
0x0c,0x21,0x1c,0xd5 = msr vttbr_el2, x12
0x4c,0x21,0x1c,0xd5 = msr vtcr_el2, x12
0x0c,0x30,0x1c,0xd5 = msr dacr32_el2, x12
0x0c,0x40,0x18,0xd5 = msr spsr_el1, x12
0x0c,0x40,0x1c,0xd5 = msr spsr_el2, x12
0x0c,0x40,0x1e,0xd5 = msr spsr_el3, x12
0x2c,0x40,0x18,0xd5 = msr elr_el1, x12
0x2c,0x40,0x1c,0xd5 = msr elr_el2, x12
0x2c,0x40,0x1e,0xd5 = msr elr_el3, x12
0x0c,0x41,0x18,0xd5 = msr sp_el0, x12
0x0c,0x41,0x1c,0xd5 = msr sp_el1, x12
0x0c,0x41,0x1e,0xd5 = msr sp_el2, x12
0x0c,0x42,0x18,0xd5 = msr spsel, x12
0x0c,0x42,0x1b,0xd5 = msr nzcv, x12
0x2c,0x42,0x1b,0xd5 = msr daif, x12
0x4c,0x42,0x18,0xd5 = msr currentel, x12
0x0c,0x43,0x1c,0xd5 = msr spsr_irq, x12
0x2c,0x43,0x1c,0xd5 = msr spsr_abt, x12
0x4c,0x43,0x1c,0xd5 = msr spsr_und, x12
0x6c,0x43,0x1c,0xd5 = msr spsr_fiq, x12
0x0c,0x44,0x1b,0xd5 = msr fpcr, x12
0x2c,0x44,0x1b,0xd5 = msr fpsr, x12
0x0c,0x45,0x1b,0xd5 = msr dspsr_el0, x12
0x2c,0x45,0x1b,0xd5 = msr dlr_el0, x12
0x2c,0x50,0x1c,0xd5 = msr ifsr32_el2, x12
0x0c,0x51,0x18,0xd5 = msr afsr0_el1, x12
0x0c,0x51,0x1c,0xd5 = msr afsr0_el2, x12
0x0c,0x51,0x1e,0xd5 = msr afsr0_el3, x12
0x2c,0x51,0x18,0xd5 = msr afsr1_el1, x12
0x2c,0x51,0x1c,0xd5 = msr afsr1_el2, x12
0x2c,0x51,0x1e,0xd5 = msr afsr1_el3, x12
0x0c,0x52,0x18,0xd5 = msr esr_el1, x12
0x0c,0x52,0x1c,0xd5 = msr esr_el2, x12
0x0c,0x52,0x1e,0xd5 = msr esr_el3, x12
0x0c,0x53,0x1c,0xd5 = msr fpexc32_el2, x12
0x0c,0x60,0x18,0xd5 = msr far_el1, x12
0x0c,0x60,0x1c,0xd5 = msr far_el2, x12
0x0c,0x60,0x1e,0xd5 = msr far_el3, x12
0x8c,0x60,0x1c,0xd5 = msr hpfar_el2, x12
0x0c,0x74,0x18,0xd5 = msr par_el1, x12
0x0c,0x9c,0x1b,0xd5 = msr pmcr_el0, x12
0x2c,0x9c,0x1b,0xd5 = msr pmcntenset_el0, x12
0x4c,0x9c,0x1b,0xd5 = msr pmcntenclr_el0, x12
0x6c,0x9c,0x1b,0xd5 = msr pmovsclr_el0, x12
0xac,0x9c,0x1b,0xd5 = msr pmselr_el0, x12
0x0c,0x9d,0x1b,0xd5 = msr pmccntr_el0, x12
0x2c,0x9d,0x1b,0xd5 = msr pmxevtyper_el0, x12
0x4c,0x9d,0x1b,0xd5 = msr pmxevcntr_el0, x12
0x0c,0x9e,0x1b,0xd5 = msr pmuserenr_el0, x12
0x2c,0x9e,0x18,0xd5 = msr pmintenset_el1, x12
0x4c,0x9e,0x18,0xd5 = msr pmintenclr_el1, x12
0x6c,0x9e,0x1b,0xd5 = msr pmovsset_el0, x12
0x0c,0xa2,0x18,0xd5 = msr mair_el1, x12
0x0c,0xa2,0x1c,0xd5 = msr mair_el2, x12
0x0c,0xa2,0x1e,0xd5 = msr mair_el3, x12
0x0c,0xa3,0x18,0xd5 = msr amair_el1, x12
0x0c,0xa3,0x1c,0xd5 = msr amair_el2, x12
0x0c,0xa3,0x1e,0xd5 = msr amair_el3, x12
0x0c,0xc0,0x18,0xd5 = msr vbar_el1, x12
0x0c,0xc0,0x1c,0xd5 = msr vbar_el2, x12
0x0c,0xc0,0x1e,0xd5 = msr vbar_el3, x12
0x4c,0xc0,0x18,0xd5 = msr rmr_el1, x12
0x4c,0xc0,0x1c,0xd5 = msr rmr_el2, x12
0x4c,0xc0,0x1e,0xd5 = msr rmr_el3, x12
0x2c,0xd0,0x18,0xd5 = msr contextidr_el1, x12
0x4c,0xd0,0x1b,0xd5 = msr tpidr_el0, x12
0x4c,0xd0,0x1c,0xd5 = msr tpidr_el2, x12
0x4c,0xd0,0x1e,0xd5 = msr tpidr_el3, x12
0x6c,0xd0,0x1b,0xd5 = msr tpidrro_el0, x12
0x8c,0xd0,0x18,0xd5 = msr tpidr_el1, x12
0x0c,0xe0,0x1b,0xd5 = msr cntfrq_el0, x12
0x6c,0xe0,0x1c,0xd5 = msr cntvoff_el2, x12
0x0c,0xe1,0x18,0xd5 = msr cntkctl_el1, x12
0x0c,0xe1,0x1c,0xd5 = msr cnthctl_el2, x12
0x0c,0xe2,0x1b,0xd5 = msr cntp_tval_el0, x12
0x0c,0xe2,0x1c,0xd5 = msr cnthp_tval_el2, x12
0x0c,0xe2,0x1f,0xd5 = msr cntps_tval_el1, x12
0x2c,0xe2,0x1b,0xd5 = msr cntp_ctl_el0, x12
0x2c,0xe2,0x1c,0xd5 = msr cnthp_ctl_el2, x12
0x2c,0xe2,0x1f,0xd5 = msr cntps_ctl_el1, x12
0x4c,0xe2,0x1b,0xd5 = msr cntp_cval_el0, x12
0x4c,0xe2,0x1c,0xd5 = msr cnthp_cval_el2, x12
0x4c,0xe2,0x1f,0xd5 = msr cntps_cval_el1, x12
0x0c,0xe3,0x1b,0xd5 = msr cntv_tval_el0, x12
0x2c,0xe3,0x1b,0xd5 = msr cntv_ctl_el0, x12
0x4c,0xe3,0x1b,0xd5 = msr cntv_cval_el0, x12
0x0c,0xe8,0x1b,0xd5 = msr pmevcntr0_el0, x12
0x2c,0xe8,0x1b,0xd5 = msr pmevcntr1_el0, x12
0x4c,0xe8,0x1b,0xd5 = msr pmevcntr2_el0, x12
0x6c,0xe8,0x1b,0xd5 = msr pmevcntr3_el0, x12
0x8c,0xe8,0x1b,0xd5 = msr pmevcntr4_el0, x12
0xac,0xe8,0x1b,0xd5 = msr pmevcntr5_el0, x12
0xcc,0xe8,0x1b,0xd5 = msr pmevcntr6_el0, x12
0xec,0xe8,0x1b,0xd5 = msr pmevcntr7_el0, x12
0x0c,0xe9,0x1b,0xd5 = msr pmevcntr8_el0, x12
0x2c,0xe9,0x1b,0xd5 = msr pmevcntr9_el0, x12
0x4c,0xe9,0x1b,0xd5 = msr pmevcntr10_el0, x12
0x6c,0xe9,0x1b,0xd5 = msr pmevcntr11_el0, x12
0x8c,0xe9,0x1b,0xd5 = msr pmevcntr12_el0, x12
0xac,0xe9,0x1b,0xd5 = msr pmevcntr13_el0, x12
0xcc,0xe9,0x1b,0xd5 = msr pmevcntr14_el0, x12
0xec,0xe9,0x1b,0xd5 = msr pmevcntr15_el0, x12
0x0c,0xea,0x1b,0xd5 = msr pmevcntr16_el0, x12
0x2c,0xea,0x1b,0xd5 = msr pmevcntr17_el0, x12
0x4c,0xea,0x1b,0xd5 = msr pmevcntr18_el0, x12
0x6c,0xea,0x1b,0xd5 = msr pmevcntr19_el0, x12
0x8c,0xea,0x1b,0xd5 = msr pmevcntr20_el0, x12
0xac,0xea,0x1b,0xd5 = msr pmevcntr21_el0, x12
0xcc,0xea,0x1b,0xd5 = msr pmevcntr22_el0, x12
0xec,0xea,0x1b,0xd5 = msr pmevcntr23_el0, x12
0x0c,0xeb,0x1b,0xd5 = msr pmevcntr24_el0, x12
0x2c,0xeb,0x1b,0xd5 = msr pmevcntr25_el0, x12
0x4c,0xeb,0x1b,0xd5 = msr pmevcntr26_el0, x12
0x6c,0xeb,0x1b,0xd5 = msr pmevcntr27_el0, x12
0x8c,0xeb,0x1b,0xd5 = msr pmevcntr28_el0, x12
0xac,0xeb,0x1b,0xd5 = msr pmevcntr29_el0, x12
0xcc,0xeb,0x1b,0xd5 = msr pmevcntr30_el0, x12
0xec,0xef,0x1b,0xd5 = msr pmccfiltr_el0, x12
0x0c,0xec,0x1b,0xd5 = msr pmevtyper0_el0, x12
0x2c,0xec,0x1b,0xd5 = msr pmevtyper1_el0, x12
0x4c,0xec,0x1b,0xd5 = msr pmevtyper2_el0, x12
0x6c,0xec,0x1b,0xd5 = msr pmevtyper3_el0, x12
0x8c,0xec,0x1b,0xd5 = msr pmevtyper4_el0, x12
0xac,0xec,0x1b,0xd5 = msr pmevtyper5_el0, x12
0xcc,0xec,0x1b,0xd5 = msr pmevtyper6_el0, x12
0xec,0xec,0x1b,0xd5 = msr pmevtyper7_el0, x12
0x0c,0xed,0x1b,0xd5 = msr pmevtyper8_el0, x12
0x2c,0xed,0x1b,0xd5 = msr pmevtyper9_el0, x12
0x4c,0xed,0x1b,0xd5 = msr pmevtyper10_el0, x12
0x6c,0xed,0x1b,0xd5 = msr pmevtyper11_el0, x12
0x8c,0xed,0x1b,0xd5 = msr pmevtyper12_el0, x12
0xac,0xed,0x1b,0xd5 = msr pmevtyper13_el0, x12
0xcc,0xed,0x1b,0xd5 = msr pmevtyper14_el0, x12
0xec,0xed,0x1b,0xd5 = msr pmevtyper15_el0, x12
0x0c,0xee,0x1b,0xd5 = msr pmevtyper16_el0, x12
0x2c,0xee,0x1b,0xd5 = msr pmevtyper17_el0, x12
0x4c,0xee,0x1b,0xd5 = msr pmevtyper18_el0, x12
0x6c,0xee,0x1b,0xd5 = msr pmevtyper19_el0, x12
0x8c,0xee,0x1b,0xd5 = msr pmevtyper20_el0, x12
0xac,0xee,0x1b,0xd5 = msr pmevtyper21_el0, x12
0xcc,0xee,0x1b,0xd5 = msr pmevtyper22_el0, x12
0xec,0xee,0x1b,0xd5 = msr pmevtyper23_el0, x12
0x0c,0xef,0x1b,0xd5 = msr pmevtyper24_el0, x12
0x2c,0xef,0x1b,0xd5 = msr pmevtyper25_el0, x12
0x4c,0xef,0x1b,0xd5 = msr pmevtyper26_el0, x12
0x6c,0xef,0x1b,0xd5 = msr pmevtyper27_el0, x12
0x8c,0xef,0x1b,0xd5 = msr pmevtyper28_el0, x12
0xac,0xef,0x1b,0xd5 = msr pmevtyper29_el0, x12
0xcc,0xef,0x1b,0xd5 = msr pmevtyper30_el0, x12
0x09,0x00,0x32,0xd5 = mrs x9, teecr32_el1
0x49,0x00,0x30,0xd5 = mrs x9, osdtrrx_el1
0x09,0x01,0x33,0xd5 = mrs x9, mdccsr_el0
0x09,0x02,0x30,0xd5 = mrs x9, mdccint_el1
0x49,0x02,0x30,0xd5 = mrs x9, mdscr_el1
0x49,0x03,0x30,0xd5 = mrs x9, osdtrtx_el1
0x09,0x04,0x33,0xd5 = mrs x9, dbgdtr_el0
0x09,0x05,0x33,0xd5 = mrs x9, dbgdtrrx_el0
0x49,0x06,0x30,0xd5 = mrs x9, oseccr_el1
0x09,0x07,0x34,0xd5 = mrs x9, dbgvcr32_el2
0x89,0x00,0x30,0xd5 = mrs x9, dbgbvr0_el1
0x89,0x01,0x30,0xd5 = mrs x9, dbgbvr1_el1
0x89,0x02,0x30,0xd5 = mrs x9, dbgbvr2_el1
0x89,0x03,0x30,0xd5 = mrs x9, dbgbvr3_el1
0x89,0x04,0x30,0xd5 = mrs x9, dbgbvr4_el1
0x89,0x05,0x30,0xd5 = mrs x9, dbgbvr5_el1
0x89,0x06,0x30,0xd5 = mrs x9, dbgbvr6_el1
0x89,0x07,0x30,0xd5 = mrs x9, dbgbvr7_el1
0x89,0x08,0x30,0xd5 = mrs x9, dbgbvr8_el1
0x89,0x09,0x30,0xd5 = mrs x9, dbgbvr9_el1
0x89,0x0a,0x30,0xd5 = mrs x9, dbgbvr10_el1
0x89,0x0b,0x30,0xd5 = mrs x9, dbgbvr11_el1
0x89,0x0c,0x30,0xd5 = mrs x9, dbgbvr12_el1
0x89,0x0d,0x30,0xd5 = mrs x9, dbgbvr13_el1
0x89,0x0e,0x30,0xd5 = mrs x9, dbgbvr14_el1
0x89,0x0f,0x30,0xd5 = mrs x9, dbgbvr15_el1
0xa9,0x00,0x30,0xd5 = mrs x9, dbgbcr0_el1
0xa9,0x01,0x30,0xd5 = mrs x9, dbgbcr1_el1
0xa9,0x02,0x30,0xd5 = mrs x9, dbgbcr2_el1
0xa9,0x03,0x30,0xd5 = mrs x9, dbgbcr3_el1
0xa9,0x04,0x30,0xd5 = mrs x9, dbgbcr4_el1
0xa9,0x05,0x30,0xd5 = mrs x9, dbgbcr5_el1
0xa9,0x06,0x30,0xd5 = mrs x9, dbgbcr6_el1
0xa9,0x07,0x30,0xd5 = mrs x9, dbgbcr7_el1
0xa9,0x08,0x30,0xd5 = mrs x9, dbgbcr8_el1
0xa9,0x09,0x30,0xd5 = mrs x9, dbgbcr9_el1
0xa9,0x0a,0x30,0xd5 = mrs x9, dbgbcr10_el1
0xa9,0x0b,0x30,0xd5 = mrs x9, dbgbcr11_el1
0xa9,0x0c,0x30,0xd5 = mrs x9, dbgbcr12_el1
0xa9,0x0d,0x30,0xd5 = mrs x9, dbgbcr13_el1
0xa9,0x0e,0x30,0xd5 = mrs x9, dbgbcr14_el1
0xa9,0x0f,0x30,0xd5 = mrs x9, dbgbcr15_el1
0xc9,0x00,0x30,0xd5 = mrs x9, dbgwvr0_el1
0xc9,0x01,0x30,0xd5 = mrs x9, dbgwvr1_el1
0xc9,0x02,0x30,0xd5 = mrs x9, dbgwvr2_el1
0xc9,0x03,0x30,0xd5 = mrs x9, dbgwvr3_el1
0xc9,0x04,0x30,0xd5 = mrs x9, dbgwvr4_el1
0xc9,0x05,0x30,0xd5 = mrs x9, dbgwvr5_el1
0xc9,0x06,0x30,0xd5 = mrs x9, dbgwvr6_el1
0xc9,0x07,0x30,0xd5 = mrs x9, dbgwvr7_el1
0xc9,0x08,0x30,0xd5 = mrs x9, dbgwvr8_el1
0xc9,0x09,0x30,0xd5 = mrs x9, dbgwvr9_el1
0xc9,0x0a,0x30,0xd5 = mrs x9, dbgwvr10_el1
0xc9,0x0b,0x30,0xd5 = mrs x9, dbgwvr11_el1
0xc9,0x0c,0x30,0xd5 = mrs x9, dbgwvr12_el1
0xc9,0x0d,0x30,0xd5 = mrs x9, dbgwvr13_el1
0xc9,0x0e,0x30,0xd5 = mrs x9, dbgwvr14_el1
0xc9,0x0f,0x30,0xd5 = mrs x9, dbgwvr15_el1
0xe9,0x00,0x30,0xd5 = mrs x9, dbgwcr0_el1
0xe9,0x01,0x30,0xd5 = mrs x9, dbgwcr1_el1
0xe9,0x02,0x30,0xd5 = mrs x9, dbgwcr2_el1
0xe9,0x03,0x30,0xd5 = mrs x9, dbgwcr3_el1
0xe9,0x04,0x30,0xd5 = mrs x9, dbgwcr4_el1
0xe9,0x05,0x30,0xd5 = mrs x9, dbgwcr5_el1
0xe9,0x06,0x30,0xd5 = mrs x9, dbgwcr6_el1
0xe9,0x07,0x30,0xd5 = mrs x9, dbgwcr7_el1
0xe9,0x08,0x30,0xd5 = mrs x9, dbgwcr8_el1
0xe9,0x09,0x30,0xd5 = mrs x9, dbgwcr9_el1
0xe9,0x0a,0x30,0xd5 = mrs x9, dbgwcr10_el1
0xe9,0x0b,0x30,0xd5 = mrs x9, dbgwcr11_el1
0xe9,0x0c,0x30,0xd5 = mrs x9, dbgwcr12_el1
0xe9,0x0d,0x30,0xd5 = mrs x9, dbgwcr13_el1
0xe9,0x0e,0x30,0xd5 = mrs x9, dbgwcr14_el1
0xe9,0x0f,0x30,0xd5 = mrs x9, dbgwcr15_el1
0x09,0x10,0x30,0xd5 = mrs x9, mdrar_el1
0x09,0x10,0x32,0xd5 = mrs x9, teehbr32_el1
0x89,0x11,0x30,0xd5 = mrs x9, oslsr_el1
0x89,0x13,0x30,0xd5 = mrs x9, osdlr_el1
0x89,0x14,0x30,0xd5 = mrs x9, dbgprcr_el1
0xc9,0x78,0x30,0xd5 = mrs x9, dbgclaimset_el1
0xc9,0x79,0x30,0xd5 = mrs x9, dbgclaimclr_el1
0xc9,0x7e,0x30,0xd5 = mrs x9, dbgauthstatus_el1
0x09,0x00,0x38,0xd5 = mrs x9, midr_el1
0x09,0x00,0x39,0xd5 = mrs x9, ccsidr_el1
0x09,0x00,0x3a,0xd5 = mrs x9, csselr_el1
0x09,0x00,0x3c,0xd5 = mrs x9, vpidr_el2
0x29,0x00,0x39,0xd5 = mrs x9, clidr_el1
0x29,0x00,0x3b,0xd5 = mrs x9, ctr_el0
0xa9,0x00,0x38,0xd5 = mrs x9, mpidr_el1
0xa9,0x00,0x3c,0xd5 = mrs x9, vmpidr_el2
0xc9,0x00,0x38,0xd5 = mrs x9, revidr_el1
0xe9,0x00,0x39,0xd5 = mrs x9, aidr_el1
0xe9,0x00,0x3b,0xd5 = mrs x9, dczid_el0
0x09,0x01,0x38,0xd5 = mrs x9, id_pfr0_el1
0x29,0x01,0x38,0xd5 = mrs x9, id_pfr1_el1
0x49,0x01,0x38,0xd5 = mrs x9, id_dfr0_el1
0x69,0x01,0x38,0xd5 = mrs x9, id_afr0_el1
0x89,0x01,0x38,0xd5 = mrs x9, id_mmfr0_el1
0xa9,0x01,0x38,0xd5 = mrs x9, id_mmfr1_el1
0xc9,0x01,0x38,0xd5 = mrs x9, id_mmfr2_el1
0xe9,0x01,0x38,0xd5 = mrs x9, id_mmfr3_el1
0x09,0x02,0x38,0xd5 = mrs x9, id_isar0_el1
0x29,0x02,0x38,0xd5 = mrs x9, id_isar1_el1
0x49,0x02,0x38,0xd5 = mrs x9, id_isar2_el1
0x69,0x02,0x38,0xd5 = mrs x9, id_isar3_el1
0x89,0x02,0x38,0xd5 = mrs x9, id_isar4_el1
0xa9,0x02,0x38,0xd5 = mrs x9, id_isar5_el1
0x09,0x03,0x38,0xd5 = mrs x9, mvfr0_el1
0x29,0x03,0x38,0xd5 = mrs x9, mvfr1_el1
0x49,0x03,0x38,0xd5 = mrs x9, mvfr2_el1
0x09,0x04,0x38,0xd5 = mrs x9, id_aa64pfr0_el1
0x29,0x04,0x38,0xd5 = mrs x9, id_aa64pfr1_el1
0x09,0x05,0x38,0xd5 = mrs x9, id_aa64dfr0_el1
0x29,0x05,0x38,0xd5 = mrs x9, id_aa64dfr1_el1
0x89,0x05,0x38,0xd5 = mrs x9, id_aa64afr0_el1
0xa9,0x05,0x38,0xd5 = mrs x9, id_aa64afr1_el1
0x09,0x06,0x38,0xd5 = mrs x9, id_aa64isar0_el1
0x29,0x06,0x38,0xd5 = mrs x9, id_aa64isar1_el1
0x09,0x07,0x38,0xd5 = mrs x9, id_aa64mmfr0_el1
0x29,0x07,0x38,0xd5 = mrs x9, id_aa64mmfr1_el1
0x09,0x10,0x38,0xd5 = mrs x9, sctlr_el1
0x09,0x10,0x3c,0xd5 = mrs x9, sctlr_el2
0x09,0x10,0x3e,0xd5 = mrs x9, sctlr_el3
0x29,0x10,0x38,0xd5 = mrs x9, actlr_el1
0x29,0x10,0x3c,0xd5 = mrs x9, actlr_el2
0x29,0x10,0x3e,0xd5 = mrs x9, actlr_el3
0x49,0x10,0x38,0xd5 = mrs x9, cpacr_el1
0x09,0x11,0x3c,0xd5 = mrs x9, hcr_el2
0x09,0x11,0x3e,0xd5 = mrs x9, scr_el3
0x29,0x11,0x3c,0xd5 = mrs x9, mdcr_el2
0x29,0x11,0x3e,0xd5 = mrs x9, sder32_el3
0x49,0x11,0x3c,0xd5 = mrs x9, cptr_el2
0x49,0x11,0x3e,0xd5 = mrs x9, cptr_el3
0x69,0x11,0x3c,0xd5 = mrs x9, hstr_el2
0xe9,0x11,0x3c,0xd5 = mrs x9, hacr_el2
0x29,0x13,0x3e,0xd5 = mrs x9, mdcr_el3
0x09,0x20,0x38,0xd5 = mrs x9, ttbr0_el1
0x09,0x20,0x3c,0xd5 = mrs x9, ttbr0_el2
0x09,0x20,0x3e,0xd5 = mrs x9, ttbr0_el3
0x29,0x20,0x38,0xd5 = mrs x9, ttbr1_el1
0x49,0x20,0x38,0xd5 = mrs x9, tcr_el1
0x49,0x20,0x3c,0xd5 = mrs x9, tcr_el2
0x49,0x20,0x3e,0xd5 = mrs x9, tcr_el3
0x09,0x21,0x3c,0xd5 = mrs x9, vttbr_el2
0x49,0x21,0x3c,0xd5 = mrs x9, vtcr_el2
0x09,0x30,0x3c,0xd5 = mrs x9, dacr32_el2
0x09,0x40,0x38,0xd5 = mrs x9, spsr_el1
0x09,0x40,0x3c,0xd5 = mrs x9, spsr_el2
0x09,0x40,0x3e,0xd5 = mrs x9, spsr_el3
0x29,0x40,0x38,0xd5 = mrs x9, elr_el1
0x29,0x40,0x3c,0xd5 = mrs x9, elr_el2
0x29,0x40,0x3e,0xd5 = mrs x9, elr_el3
0x09,0x41,0x38,0xd5 = mrs x9, sp_el0
0x09,0x41,0x3c,0xd5 = mrs x9, sp_el1
0x09,0x41,0x3e,0xd5 = mrs x9, sp_el2
0x09,0x42,0x38,0xd5 = mrs x9, spsel
0x09,0x42,0x3b,0xd5 = mrs x9, nzcv
0x29,0x42,0x3b,0xd5 = mrs x9, daif
0x49,0x42,0x38,0xd5 = mrs x9, currentel
0x09,0x43,0x3c,0xd5 = mrs x9, spsr_irq
0x29,0x43,0x3c,0xd5 = mrs x9, spsr_abt
0x49,0x43,0x3c,0xd5 = mrs x9, spsr_und
0x69,0x43,0x3c,0xd5 = mrs x9, spsr_fiq
0x09,0x44,0x3b,0xd5 = mrs x9, fpcr
0x29,0x44,0x3b,0xd5 = mrs x9, fpsr
0x09,0x45,0x3b,0xd5 = mrs x9, dspsr_el0
0x29,0x45,0x3b,0xd5 = mrs x9, dlr_el0
0x29,0x50,0x3c,0xd5 = mrs x9, ifsr32_el2
0x09,0x51,0x38,0xd5 = mrs x9, afsr0_el1
0x09,0x51,0x3c,0xd5 = mrs x9, afsr0_el2
0x09,0x51,0x3e,0xd5 = mrs x9, afsr0_el3
0x29,0x51,0x38,0xd5 = mrs x9, afsr1_el1
0x29,0x51,0x3c,0xd5 = mrs x9, afsr1_el2
0x29,0x51,0x3e,0xd5 = mrs x9, afsr1_el3
0x09,0x52,0x38,0xd5 = mrs x9, esr_el1
0x09,0x52,0x3c,0xd5 = mrs x9, esr_el2
0x09,0x52,0x3e,0xd5 = mrs x9, esr_el3
0x09,0x53,0x3c,0xd5 = mrs x9, fpexc32_el2
0x09,0x60,0x38,0xd5 = mrs x9, far_el1
0x09,0x60,0x3c,0xd5 = mrs x9, far_el2
0x09,0x60,0x3e,0xd5 = mrs x9, far_el3
0x89,0x60,0x3c,0xd5 = mrs x9, hpfar_el2
0x09,0x74,0x38,0xd5 = mrs x9, par_el1
0x09,0x9c,0x3b,0xd5 = mrs x9, pmcr_el0
0x29,0x9c,0x3b,0xd5 = mrs x9, pmcntenset_el0
0x49,0x9c,0x3b,0xd5 = mrs x9, pmcntenclr_el0
0x69,0x9c,0x3b,0xd5 = mrs x9, pmovsclr_el0
0xa9,0x9c,0x3b,0xd5 = mrs x9, pmselr_el0
0xc9,0x9c,0x3b,0xd5 = mrs x9, pmceid0_el0
0xe9,0x9c,0x3b,0xd5 = mrs x9, pmceid1_el0
0x09,0x9d,0x3b,0xd5 = mrs x9, pmccntr_el0
0x29,0x9d,0x3b,0xd5 = mrs x9, pmxevtyper_el0
0x49,0x9d,0x3b,0xd5 = mrs x9, pmxevcntr_el0
0x09,0x9e,0x3b,0xd5 = mrs x9, pmuserenr_el0
0x29,0x9e,0x38,0xd5 = mrs x9, pmintenset_el1
0x49,0x9e,0x38,0xd5 = mrs x9, pmintenclr_el1
0x69,0x9e,0x3b,0xd5 = mrs x9, pmovsset_el0
0x09,0xa2,0x38,0xd5 = mrs x9, mair_el1
0x09,0xa2,0x3c,0xd5 = mrs x9, mair_el2
0x09,0xa2,0x3e,0xd5 = mrs x9, mair_el3
0x09,0xa3,0x38,0xd5 = mrs x9, amair_el1
0x09,0xa3,0x3c,0xd5 = mrs x9, amair_el2
0x09,0xa3,0x3e,0xd5 = mrs x9, amair_el3
0x09,0xc0,0x38,0xd5 = mrs x9, vbar_el1
0x09,0xc0,0x3c,0xd5 = mrs x9, vbar_el2
0x09,0xc0,0x3e,0xd5 = mrs x9, vbar_el3
0x29,0xc0,0x38,0xd5 = mrs x9, rvbar_el1
0x29,0xc0,0x3c,0xd5 = mrs x9, rvbar_el2
0x29,0xc0,0x3e,0xd5 = mrs x9, rvbar_el3
0x49,0xc0,0x38,0xd5 = mrs x9, rmr_el1
0x49,0xc0,0x3c,0xd5 = mrs x9, rmr_el2
0x49,0xc0,0x3e,0xd5 = mrs x9, rmr_el3
0x09,0xc1,0x38,0xd5 = mrs x9, isr_el1
0x29,0xd0,0x38,0xd5 = mrs x9, contextidr_el1
0x49,0xd0,0x3b,0xd5 = mrs x9, tpidr_el0
0x49,0xd0,0x3c,0xd5 = mrs x9, tpidr_el2
0x49,0xd0,0x3e,0xd5 = mrs x9, tpidr_el3
0x69,0xd0,0x3b,0xd5 = mrs x9, tpidrro_el0
0x89,0xd0,0x38,0xd5 = mrs x9, tpidr_el1
0x09,0xe0,0x3b,0xd5 = mrs x9, cntfrq_el0
0x29,0xe0,0x3b,0xd5 = mrs x9, cntpct_el0
0x49,0xe0,0x3b,0xd5 = mrs x9, cntvct_el0
0x69,0xe0,0x3c,0xd5 = mrs x9, cntvoff_el2
0x09,0xe1,0x38,0xd5 = mrs x9, cntkctl_el1
0x09,0xe1,0x3c,0xd5 = mrs x9, cnthctl_el2
0x09,0xe2,0x3b,0xd5 = mrs x9, cntp_tval_el0
0x09,0xe2,0x3c,0xd5 = mrs x9, cnthp_tval_el2
0x09,0xe2,0x3f,0xd5 = mrs x9, cntps_tval_el1
0x29,0xe2,0x3b,0xd5 = mrs x9, cntp_ctl_el0
0x29,0xe2,0x3c,0xd5 = mrs x9, cnthp_ctl_el2
0x29,0xe2,0x3f,0xd5 = mrs x9, cntps_ctl_el1
0x49,0xe2,0x3b,0xd5 = mrs x9, cntp_cval_el0
0x49,0xe2,0x3c,0xd5 = mrs x9, cnthp_cval_el2
0x49,0xe2,0x3f,0xd5 = mrs x9, cntps_cval_el1
0x09,0xe3,0x3b,0xd5 = mrs x9, cntv_tval_el0
0x29,0xe3,0x3b,0xd5 = mrs x9, cntv_ctl_el0
0x49,0xe3,0x3b,0xd5 = mrs x9, cntv_cval_el0
0x09,0xe8,0x3b,0xd5 = mrs x9, pmevcntr0_el0
0x29,0xe8,0x3b,0xd5 = mrs x9, pmevcntr1_el0
0x49,0xe8,0x3b,0xd5 = mrs x9, pmevcntr2_el0
0x69,0xe8,0x3b,0xd5 = mrs x9, pmevcntr3_el0
0x89,0xe8,0x3b,0xd5 = mrs x9, pmevcntr4_el0
0xa9,0xe8,0x3b,0xd5 = mrs x9, pmevcntr5_el0
0xc9,0xe8,0x3b,0xd5 = mrs x9, pmevcntr6_el0
0xe9,0xe8,0x3b,0xd5 = mrs x9, pmevcntr7_el0
0x09,0xe9,0x3b,0xd5 = mrs x9, pmevcntr8_el0
0x29,0xe9,0x3b,0xd5 = mrs x9, pmevcntr9_el0
0x49,0xe9,0x3b,0xd5 = mrs x9, pmevcntr10_el0
0x69,0xe9,0x3b,0xd5 = mrs x9, pmevcntr11_el0
0x89,0xe9,0x3b,0xd5 = mrs x9, pmevcntr12_el0
0xa9,0xe9,0x3b,0xd5 = mrs x9, pmevcntr13_el0
0xc9,0xe9,0x3b,0xd5 = mrs x9, pmevcntr14_el0
0xe9,0xe9,0x3b,0xd5 = mrs x9, pmevcntr15_el0
0x09,0xea,0x3b,0xd5 = mrs x9, pmevcntr16_el0
0x29,0xea,0x3b,0xd5 = mrs x9, pmevcntr17_el0
0x49,0xea,0x3b,0xd5 = mrs x9, pmevcntr18_el0
0x69,0xea,0x3b,0xd5 = mrs x9, pmevcntr19_el0
0x89,0xea,0x3b,0xd5 = mrs x9, pmevcntr20_el0
0xa9,0xea,0x3b,0xd5 = mrs x9, pmevcntr21_el0
0xc9,0xea,0x3b,0xd5 = mrs x9, pmevcntr22_el0
0xe9,0xea,0x3b,0xd5 = mrs x9, pmevcntr23_el0
0x09,0xeb,0x3b,0xd5 = mrs x9, pmevcntr24_el0
0x29,0xeb,0x3b,0xd5 = mrs x9, pmevcntr25_el0
0x49,0xeb,0x3b,0xd5 = mrs x9, pmevcntr26_el0
0x69,0xeb,0x3b,0xd5 = mrs x9, pmevcntr27_el0
0x89,0xeb,0x3b,0xd5 = mrs x9, pmevcntr28_el0
0xa9,0xeb,0x3b,0xd5 = mrs x9, pmevcntr29_el0
0xc9,0xeb,0x3b,0xd5 = mrs x9, pmevcntr30_el0
0xe9,0xef,0x3b,0xd5 = mrs x9, pmccfiltr_el0
0x09,0xec,0x3b,0xd5 = mrs x9, pmevtyper0_el0
0x29,0xec,0x3b,0xd5 = mrs x9, pmevtyper1_el0
0x49,0xec,0x3b,0xd5 = mrs x9, pmevtyper2_el0
0x69,0xec,0x3b,0xd5 = mrs x9, pmevtyper3_el0
0x89,0xec,0x3b,0xd5 = mrs x9, pmevtyper4_el0
0xa9,0xec,0x3b,0xd5 = mrs x9, pmevtyper5_el0
0xc9,0xec,0x3b,0xd5 = mrs x9, pmevtyper6_el0
0xe9,0xec,0x3b,0xd5 = mrs x9, pmevtyper7_el0
0x09,0xed,0x3b,0xd5 = mrs x9, pmevtyper8_el0
0x29,0xed,0x3b,0xd5 = mrs x9, pmevtyper9_el0
0x49,0xed,0x3b,0xd5 = mrs x9, pmevtyper10_el0
0x69,0xed,0x3b,0xd5 = mrs x9, pmevtyper11_el0
0x89,0xed,0x3b,0xd5 = mrs x9, pmevtyper12_el0
0xa9,0xed,0x3b,0xd5 = mrs x9, pmevtyper13_el0
0xc9,0xed,0x3b,0xd5 = mrs x9, pmevtyper14_el0
0xe9,0xed,0x3b,0xd5 = mrs x9, pmevtyper15_el0
0x09,0xee,0x3b,0xd5 = mrs x9, pmevtyper16_el0
0x29,0xee,0x3b,0xd5 = mrs x9, pmevtyper17_el0
0x49,0xee,0x3b,0xd5 = mrs x9, pmevtyper18_el0
0x69,0xee,0x3b,0xd5 = mrs x9, pmevtyper19_el0
0x89,0xee,0x3b,0xd5 = mrs x9, pmevtyper20_el0
0xa9,0xee,0x3b,0xd5 = mrs x9, pmevtyper21_el0
0xc9,0xee,0x3b,0xd5 = mrs x9, pmevtyper22_el0
0xe9,0xee,0x3b,0xd5 = mrs x9, pmevtyper23_el0
0x09,0xef,0x3b,0xd5 = mrs x9, pmevtyper24_el0
0x29,0xef,0x3b,0xd5 = mrs x9, pmevtyper25_el0
0x49,0xef,0x3b,0xd5 = mrs x9, pmevtyper26_el0
0x69,0xef,0x3b,0xd5 = mrs x9, pmevtyper27_el0
0x89,0xef,0x3b,0xd5 = mrs x9, pmevtyper28_el0
0xa9,0xef,0x3b,0xd5 = mrs x9, pmevtyper29_el0
0xc9,0xef,0x3b,0xd5 = mrs x9, pmevtyper30_el0
0xac,0xf1,0x3f,0xd5 = mrs x12, s3_7_c15_c1_5
0xed,0xbf,0x3a,0xd5 = mrs x13, s3_2_c11_c15_7
0x0c,0xf0,0x18,0xd5 = msr s3_0_c15_c0_0, x12
0xe5,0xbd,0x1f,0xd5 = msr s3_7_c11_c13_7, x5
0x01,0x00,0x00,0x14 = b #4
0x00,0x00,0x00,0x94 = bl #0
0xff,0xff,0xff,0x15 = b #134217724
0x00,0x00,0x00,0x96 = bl #-134217728
0x80,0x02,0x1f,0xd6 = br x20
0xe0,0x03,0x3f,0xd6 = blr xzr
0x40,0x01,0x5f,0xd6 = ret x10
0xc0,0x03,0x5f,0xd6 = ret 
0xe0,0x03,0x9f,0xd6 = eret 
0xe0,0x03,0xbf,0xd6 = drps 
