/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    9110U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    9103U,	// BUNDLE
    9120U,	// LIFETIME_START
    9090U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    21425U,	// ABSQ_S_PH
    17795U,	// ABSQ_S_QB
    24564U,	// ABSQ_S_W
    33574461U,	// ADD
    18064U,	// ADDIUPC
    33575662U,	// ADDQH_PH
    33575779U,	// ADDQH_R_PH
    33578671U,	// ADDQH_R_W
    33578274U,	// ADDQH_W
    33575736U,	// ADDQ_PH
    33575835U,	// ADDQ_S_PH
    33578976U,	// ADDQ_S_W
    33572524U,	// ADDSC
    33571204U,	// ADDS_A_B
    33572649U,	// ADDS_A_D
    33574607U,	// ADDS_A_H
    33577982U,	// ADDS_A_W
    33571672U,	// ADDS_S_B
    33573738U,	// ADDS_S_D
    33575164U,	// ADDS_S_H
    33579026U,	// ADDS_S_W
    33571887U,	// ADDS_U_B
    33574205U,	// ADDS_U_D
    33575442U,	// ADDS_U_H
    33579444U,	// ADDS_U_W
    33572095U,	// ADDUH_QB
    33572203U,	// ADDUH_R_QB
    33575934U,	// ADDU_PH
    33572308U,	// ADDU_QB
    33575879U,	// ADDU_S_PH
    33572249U,	// ADDU_S_QB
    570442365U,	// ADDVI_B
    570444081U,	// ADDVI_D
    570445735U,	// ADDVI_H
    570449319U,	// ADDVI_W
    33571965U,	// ADDV_B
    33574305U,	// ADDV_D
    33575520U,	// ADDV_H
    33579544U,	// ADDV_W
    33572563U,	// ADDWC
    33571186U,	// ADD_A_B
    33572630U,	// ADD_A_D
    33574589U,	// ADD_A_H
    33577963U,	// ADD_A_W
    33574461U,	// ADD_MM
    33576147U,	// ADDi
    33576147U,	// ADDi_MM
    33577725U,	// ADDiu
    33577725U,	// ADDiu_MM
    33577679U,	// ADDu
    33577679U,	// ADDu_MM
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    33576610U,	// ALIGN
    18056U,	// ALUIPC
    33574483U,	// AND
    33574483U,	// AND64
    570442224U,	// ANDI_B
    33574483U,	// AND_MM
    33577807U,	// AND_V
    0U,	// AND_V_D_PSEUDO
    0U,	// AND_V_H_PSEUDO
    0U,	// AND_V_W_PSEUDO
    1107317977U,	// ANDi
    1107317977U,	// ANDi64
    1107317977U,	// ANDi_MM
    1107316321U,	// APPEND
    33571566U,	// ASUB_S_B
    33573568U,	// ASUB_S_D
    33574996U,	// ASUB_S_H
    33578806U,	// ASUB_S_W
    33571781U,	// ASUB_U_B
    33574035U,	// ASUB_U_D
    33575284U,	// ASUB_U_H
    33579274U,	// ASUB_U_W
    0U,	// ATOMIC_CMP_SWAP_I16
    0U,	// ATOMIC_CMP_SWAP_I32
    0U,	// ATOMIC_CMP_SWAP_I64
    0U,	// ATOMIC_CMP_SWAP_I8
    0U,	// ATOMIC_LOAD_ADD_I16
    0U,	// ATOMIC_LOAD_ADD_I32
    0U,	// ATOMIC_LOAD_ADD_I64
    0U,	// ATOMIC_LOAD_ADD_I8
    0U,	// ATOMIC_LOAD_AND_I16
    0U,	// ATOMIC_LOAD_AND_I32
    0U,	// ATOMIC_LOAD_AND_I64
    0U,	// ATOMIC_LOAD_AND_I8
    0U,	// ATOMIC_LOAD_NAND_I16
    0U,	// ATOMIC_LOAD_NAND_I32
    0U,	// ATOMIC_LOAD_NAND_I64
    0U,	// ATOMIC_LOAD_NAND_I8
    0U,	// ATOMIC_LOAD_OR_I16
    0U,	// ATOMIC_LOAD_OR_I32
    0U,	// ATOMIC_LOAD_OR_I64
    0U,	// ATOMIC_LOAD_OR_I8
    0U,	// ATOMIC_LOAD_SUB_I16
    0U,	// ATOMIC_LOAD_SUB_I32
    0U,	// ATOMIC_LOAD_SUB_I64
    0U,	// ATOMIC_LOAD_SUB_I8
    0U,	// ATOMIC_LOAD_XOR_I16
    0U,	// ATOMIC_LOAD_XOR_I32
    0U,	// ATOMIC_LOAD_XOR_I64
    0U,	// ATOMIC_LOAD_XOR_I8
    0U,	// ATOMIC_SWAP_I16
    0U,	// ATOMIC_SWAP_I32
    0U,	// ATOMIC_SWAP_I64
    0U,	// ATOMIC_SWAP_I8
    33576257U,	// AUI
    18049U,	// AUIPC
    33571652U,	// AVER_S_B
    33573718U,	// AVER_S_D
    33575134U,	// AVER_S_H
    33579006U,	// AVER_S_W
    33571867U,	// AVER_U_B
    33574185U,	// AVER_U_D
    33575422U,	// AVER_U_H
    33579424U,	// AVER_U_W
    33571594U,	// AVE_S_B
    33573650U,	// AVE_S_D
    33575066U,	// AVE_S_H
    33578888U,	// AVE_S_W
    33571809U,	// AVE_U_B
    33574117U,	// AVE_U_D
    33575354U,	// AVE_U_H
    33579356U,	// AVE_U_W
    23293U,	// AddiuRxImmX16
    154365U,	// AddiuRxPcImmX16
    69229309U,	// AddiuRxRxImm16
    2120445U,	// AddiuRxRxImmX16
    4217597U,	// AddiuRxRyOffMemX16
    287481U,	// AddiuSpImm16
    418553U,	// AddiuSpImmX16
    33577679U,	// AdduRxRyRz16
    2117203U,	// AndRxRxRy16
    0U,	// B
    33577678U,	// BADDu
    415079U,	// BAL
    411192U,	// BALC
    1107318433U,	// BALIGN
    0U,	// BAL_BR
    411171U,	// BC
    20116U,	// BC0F
    21976U,	// BC0FL
    23169U,	// BC0T
    22105U,	// BC0TL
    25447U,	// BC1EQZ
    20122U,	// BC1F
    21983U,	// BC1FL
    20122U,	// BC1F_MM
    25431U,	// BC1NEZ
    23175U,	// BC1T
    22112U,	// BC1TL
    23175U,	// BC1T_MM
    25455U,	// BC2EQZ
    20128U,	// BC2F
    21990U,	// BC2FL
    25439U,	// BC2NEZ
    23181U,	// BC2T
    22119U,	// BC2TL
    20134U,	// BC3F
    21997U,	// BC3FL
    23187U,	// BC3T
    22126U,	// BC3TL
    570442293U,	// BCLRI_B
    570444025U,	// BCLRI_D
    570445679U,	// BCLRI_H
    570449263U,	// BCLRI_W
    33571533U,	// BCLR_B
    33573492U,	// BCLR_D
    33574963U,	// BCLR_H
    33578722U,	// BCLR_W
    33576764U,	// BEQ
    33576764U,	// BEQ64
    33572518U,	// BEQC
    33576525U,	// BEQL
    18016U,	// BEQZALC
    18159U,	// BEQZC
    18159U,	// BEQZC_MM
    33576764U,	// BEQ_MM
    33572391U,	// BGEC
    33572537U,	// BGEUC
    25214U,	// BGEZ
    25214U,	// BGEZ64
    21873U,	// BGEZAL
    17989U,	// BGEZALC
    22069U,	// BGEZALL
    23144U,	// BGEZALS_MM
    21873U,	// BGEZAL_MM
    18138U,	// BGEZC
    22149U,	// BGEZL
    25214U,	// BGEZ_MM
    25274U,	// BGTZ
    25274U,	// BGTZ64
    18025U,	// BGTZALC
    18166U,	// BGTZC
    22163U,	// BGTZL
    25274U,	// BGTZ_MM
    1646281242U,	// BINSLI_B
    1646282974U,	// BINSLI_D
    1646284628U,	// BINSLI_H
    1646288212U,	// BINSLI_W
    2183152301U,	// BINSL_B
    2183154086U,	// BINSL_D
    2183155654U,	// BINSL_H
    2183159282U,	// BINSL_W
    1646281303U,	// BINSRI_B
    1646283019U,	// BINSRI_D
    1646284673U,	// BINSRI_H
    1646288257U,	// BINSRI_W
    2183152349U,	// BINSR_B
    2183154342U,	// BINSR_D
    2183155779U,	// BINSR_H
    2183159572U,	// BINSR_W
    23447U,	// BITREV
    22225U,	// BITSWAP
    25220U,	// BLEZ
    25220U,	// BLEZ64
    17998U,	// BLEZALC
    18145U,	// BLEZC
    22156U,	// BLEZL
    25220U,	// BLEZ_MM
    33572531U,	// BLTC
    33572544U,	// BLTUC
    25280U,	// BLTZ
    25280U,	// BLTZ64
    21881U,	// BLTZAL
    18034U,	// BLTZALC
    22078U,	// BLTZALL
    23153U,	// BLTZALS_MM
    21881U,	// BLTZAL_MM
    18173U,	// BLTZC
    22170U,	// BLTZL
    25280U,	// BLTZ_MM
    1646281358U,	// BMNZI_B
    2183158664U,	// BMNZ_V
    1646281350U,	// BMZI_B
    2183158650U,	// BMZ_V
    33574527U,	// BNE
    33574527U,	// BNE64
    33572397U,	// BNEC
    570442232U,	// BNEGI_B
    570443973U,	// BNEGI_D
    570445627U,	// BNEGI_H
    570449211U,	// BNEGI_W
    33571288U,	// BNEG_B
    33573037U,	// BNEG_D
    33574691U,	// BNEG_H
    33578194U,	// BNEG_W
    33576402U,	// BNEL
    18007U,	// BNEZALC
    18152U,	// BNEZC
    18152U,	// BNEZC_MM
    33574527U,	// BNE_MM
    33572551U,	// BNVC
    17573U,	// BNZ_B
    19998U,	// BNZ_D
    21128U,	// BNZ_H
    23425U,	// BNZ_V
    25177U,	// BNZ_W
    33572557U,	// BOVC
    409767U,	// BPOSGE32
    0U,	// BPOSGE32_PSEUDO
    21838U,	// BREAK
    21838U,	// BREAK_MM
    1646281217U,	// BSELI_B
    0U,	// BSEL_D_PSEUDO
    0U,	// BSEL_FD_PSEUDO
    0U,	// BSEL_FW_PSEUDO
    0U,	// BSEL_H_PSEUDO
    2183158622U,	// BSEL_V
    0U,	// BSEL_W_PSEUDO
    570442347U,	// BSETI_B
    570444063U,	// BSETI_D
    570445717U,	// BSETI_H
    570449301U,	// BSETI_W
    33571749U,	// BSET_B
    33573854U,	// BSET_D
    33575252U,	// BSET_H
    33579180U,	// BSET_W
    17567U,	// BZ_B
    19982U,	// BZ_D
    21122U,	// BZ_H
    23412U,	// BZ_V
    25171U,	// BZ_W
    100688549U,	// BeqzRxImm16
    25253U,	// BeqzRxImmX16
    278904U,	// Bimm16
    409976U,	// BimmX16
    100688522U,	// BnezRxImm16
    25226U,	// BnezRxImmX16
    9082U,	// Break16
    549555U,	// Bteqz16
    134239985U,	// BteqzT8CmpX16
    134239510U,	// BteqzT8CmpiX16
    134240927U,	// BteqzT8SltX16
    134239540U,	// BteqzT8SltiX16
    134241035U,	// BteqzT8SltiuX16
    134241071U,	// BteqzT8SltuX16
    418483U,	// BteqzX16
    549528U,	// Btnez16
    167794417U,	// BtnezT8CmpX16
    167793942U,	// BtnezT8CmpiX16
    167795359U,	// BtnezT8SltX16
    167793972U,	// BtnezT8SltiX16
    167795467U,	// BtnezT8SltiuX16
    167795503U,	// BtnezT8SltuX16
    418456U,	// BtnezX16
    0U,	// BuildPairF64
    0U,	// BuildPairF64_64
    36472U,	// CACHE
    36472U,	// CACHE_R6
    18768U,	// CEIL_L_D64
    22751U,	// CEIL_L_S
    19944U,	// CEIL_W_D32
    19944U,	// CEIL_W_D64
    19944U,	// CEIL_W_MM
    23073U,	// CEIL_W_S
    23073U,	// CEIL_W_S_MM
    33571364U,	// CEQI_B
    33573096U,	// CEQI_D
    33574750U,	// CEQI_H
    33578334U,	// CEQI_W
    33571518U,	// CEQ_B
    33573399U,	// CEQ_D
    33574941U,	// CEQ_H
    33578610U,	// CEQ_W
    16437U,	// CFC1
    16437U,	// CFC1_MM
    16738U,	// CFCMSA
    1107321649U,	// CINS
    1107321605U,	// CINS32
    19404U,	// CLASS_D
    22925U,	// CLASS_S
    33571603U,	// CLEI_S_B
    33573659U,	// CLEI_S_D
    33575075U,	// CLEI_S_H
    33578897U,	// CLEI_S_W
    570442730U,	// CLEI_U_B
    570445038U,	// CLEI_U_D
    570446275U,	// CLEI_U_H
    570450277U,	// CLEI_U_W
    33571585U,	// CLE_S_B
    33573641U,	// CLE_S_D
    33575057U,	// CLE_S_H
    33578879U,	// CLE_S_W
    33571800U,	// CLE_U_B
    33574108U,	// CLE_U_D
    33575345U,	// CLE_U_H
    33579347U,	// CLE_U_W
    22200U,	// CLO
    22200U,	// CLO_MM
    22200U,	// CLO_R6
    33571623U,	// CLTI_S_B
    33573679U,	// CLTI_S_D
    33575095U,	// CLTI_S_H
    33578917U,	// CLTI_S_W
    570442750U,	// CLTI_U_B
    570445058U,	// CLTI_U_D
    570446295U,	// CLTI_U_H
    570450297U,	// CLTI_U_W
    33571691U,	// CLT_S_B
    33573757U,	// CLT_S_D
    33575183U,	// CLT_S_H
    33579045U,	// CLT_S_W
    33571918U,	// CLT_U_B
    33574236U,	// CLT_U_D
    33575473U,	// CLT_U_H
    33579475U,	// CLT_U_W
    25248U,	// CLZ
    25248U,	// CLZ_MM
    25248U,	// CLZ_R6
    33572141U,	// CMPGDU_EQ_QB
    33572046U,	// CMPGDU_LE_QB
    33572260U,	// CMPGDU_LT_QB
    33572155U,	// CMPGU_EQ_QB
    33572060U,	// CMPGU_LE_QB
    33572274U,	// CMPGU_LT_QB
    17736U,	// CMPU_EQ_QB
    17641U,	// CMPU_LE_QB
    17855U,	// CMPU_LT_QB
    33573388U,	// CMP_EQ_D
    21313U,	// CMP_EQ_PH
    33577288U,	// CMP_EQ_S
    33572958U,	// CMP_F_D
    33577099U,	// CMP_F_S
    33572802U,	// CMP_LE_D
    21209U,	// CMP_LE_PH
    33577020U,	// CMP_LE_S
    33573879U,	// CMP_LT_D
    21482U,	// CMP_LT_PH
    33577383U,	// CMP_LT_S
    33572976U,	// CMP_SAF_D
    33577109U,	// CMP_SAF_S
    33573415U,	// CMP_SEQ_D
    33577307U,	// CMP_SEQ_S
    33572839U,	// CMP_SLE_D
    33577049U,	// CMP_SLE_S
    33573906U,	// CMP_SLT_D
    33577402U,	// CMP_SLT_S
    33573463U,	// CMP_SUEQ_D
    33577338U,	// CMP_SUEQ_S
    33572887U,	// CMP_SULE_D
    33577080U,	// CMP_SULE_S
    33573954U,	// CMP_SULT_D
    33577433U,	// CMP_SULT_S
    33573345U,	// CMP_SUN_D
    33577261U,	// CMP_SUN_S
    33573443U,	// CMP_UEQ_D
    33577327U,	// CMP_UEQ_S
    33572867U,	// CMP_ULE_D
    33577069U,	// CMP_ULE_S
    33573934U,	// CMP_ULT_D
    33577422U,	// CMP_ULT_S
    33573327U,	// CMP_UN_D
    33577251U,	// CMP_UN_S
    9168U,	// CONSTPOOL_ENTRY
    0U,	// COPY_FD_PSEUDO
    0U,	// COPY_FW_PSEUDO
    738214802U,	// COPY_S_B
    738216890U,	// COPY_S_D
    738218305U,	// COPY_S_H
    738222189U,	// COPY_S_W
    738215017U,	// COPY_U_B
    738217357U,	// COPY_U_D
    738218572U,	// COPY_U_H
    738222596U,	// COPY_U_W
    704592U,	// CTC1
    704592U,	// CTC1_MM
    16746U,	// CTCMSA
    22553U,	// CVT_D32_S
    23610U,	// CVT_D32_W
    23610U,	// CVT_D32_W_MM
    21845U,	// CVT_D64_L
    22553U,	// CVT_D64_S
    23610U,	// CVT_D64_W
    22553U,	// CVT_D_S_MM
    18789U,	// CVT_L_D64
    18789U,	// CVT_L_D64_MM
    22772U,	// CVT_L_S
    22772U,	// CVT_L_S_MM
    19127U,	// CVT_S_D32
    19127U,	// CVT_S_D32_MM
    19127U,	// CVT_S_D64
    21854U,	// CVT_S_L
    24365U,	// CVT_S_W
    24365U,	// CVT_S_W_MM
    19965U,	// CVT_W_D32
    19965U,	// CVT_W_D64
    19965U,	// CVT_W_MM
    23094U,	// CVT_W_S
    23094U,	// CVT_W_S_MM
    18948U,	// C_EQ_D32
    18948U,	// C_EQ_D64
    22848U,	// C_EQ_S
    18519U,	// C_F_D32
    18519U,	// C_F_D64
    22660U,	// C_F_S
    18362U,	// C_LE_D32
    18362U,	// C_LE_D64
    22580U,	// C_LE_S
    19439U,	// C_LT_D32
    19439U,	// C_LT_D64
    22943U,	// C_LT_S
    18353U,	// C_NGE_D32
    18353U,	// C_NGE_D64
    22571U,	// C_NGE_S
    18388U,	// C_NGLE_D32
    18388U,	// C_NGLE_D64
    22598U,	// C_NGLE_S
    18805U,	// C_NGL_D32
    18805U,	// C_NGL_D64
    22788U,	// C_NGL_S
    19430U,	// C_NGT_D32
    19430U,	// C_NGT_D64
    22934U,	// C_NGT_S
    18398U,	// C_OLE_D32
    18398U,	// C_OLE_D64
    22608U,	// C_OLE_S
    19465U,	// C_OLT_D32
    19465U,	// C_OLT_D64
    22961U,	// C_OLT_S
    18974U,	// C_SEQ_D32
    18974U,	// C_SEQ_D64
    22866U,	// C_SEQ_S
    18589U,	// C_SF_D32
    18589U,	// C_SF_D64
    22706U,	// C_SF_S
    19002U,	// C_UEQ_D32
    19002U,	// C_UEQ_D64
    22886U,	// C_UEQ_S
    18426U,	// C_ULE_D32
    18426U,	// C_ULE_D64
    22628U,	// C_ULE_S
    19493U,	// C_ULT_D32
    19493U,	// C_ULT_D64
    22981U,	// C_ULT_S
    18887U,	// C_UN_D32
    18887U,	// C_UN_D64
    22811U,	// C_UN_S
    22257U,	// CmpRxRy16
    234902806U,	// CmpiRxImm16
    21782U,	// CmpiRxImmX16
    418587U,	// Constant32
    33574460U,	// DADD
    33576146U,	// DADDi
    33577724U,	// DADDiu
    33577685U,	// DADDu
    7132401U,	// DAHI
    33576617U,	// DALIGN
    7132462U,	// DATI
    33576256U,	// DAUI
    22224U,	// DBITSWAP
    22199U,	// DCLO
    22199U,	// DCLO_R6
    25247U,	// DCLZ
    25247U,	// DCLZ_R6
    33577887U,	// DDIV
    33577795U,	// DDIVU
    9194U,	// DERET
    9194U,	// DERET_MM
    1107321667U,	// DEXT
    1107321642U,	// DEXTM
    1107321680U,	// DEXTU
    414933U,	// DI
    1107321655U,	// DINS
    1107321635U,	// DINSM
    1107321673U,	// DINSU
    33577888U,	// DIV
    33577796U,	// DIVU
    33571712U,	// DIV_S_B
    33573800U,	// DIV_S_D
    33575204U,	// DIV_S_H
    33579088U,	// DIV_S_W
    33571927U,	// DIV_U_B
    33574267U,	// DIV_U_D
    33575482U,	// DIV_U_H
    33579506U,	// DIV_U_W
    414933U,	// DI_MM
    33571164U,	// DLSA
    33571164U,	// DLSA_R6
    1107312649U,	// DMFC0
    16443U,	// DMFC1
    1107312854U,	// DMFC2
    33574505U,	// DMOD
    33577699U,	// DMODU
    1107312656U,	// DMTC0
    704598U,	// DMTC1
    1107312861U,	// DMTC2
    33576140U,	// DMUH
    33577717U,	// DMUHU
    33576565U,	// DMUL
    23209U,	// DMULT
    23355U,	// DMULTu
    33577761U,	// DMULU
    33576565U,	// DMUL_R6
    33573708U,	// DOTP_S_D
    33575124U,	// DOTP_S_H
    33578956U,	// DOTP_S_W
    33574175U,	// DOTP_U_D
    33575412U,	// DOTP_U_H
    33579414U,	// DOTP_U_W
    2183154421U,	// DPADD_S_D
    2183155837U,	// DPADD_S_H
    2183159659U,	// DPADD_S_W
    2183154888U,	// DPADD_U_D
    2183156125U,	// DPADD_U_H
    2183160127U,	// DPADD_U_W
    33575993U,	// DPAQX_SA_W_PH
    33576076U,	// DPAQX_S_W_PH
    33578416U,	// DPAQ_SA_L_W
    33576035U,	// DPAQ_S_W_PH
    33576321U,	// DPAU_H_QBL
    33576779U,	// DPAU_H_QBR
    33576114U,	// DPAX_W_PH
    33575983U,	// DPA_W_PH
    22262U,	// DPOP
    33576008U,	// DPSQX_SA_W_PH
    33576090U,	// DPSQX_S_W_PH
    33578429U,	// DPSQ_SA_L_W
    33576063U,	// DPSQ_S_W_PH
    2183154388U,	// DPSUB_S_D
    2183155816U,	// DPSUB_S_H
    2183159626U,	// DPSUB_S_W
    2183154855U,	// DPSUB_U_D
    2183156104U,	// DPSUB_U_H
    2183160094U,	// DPSUB_U_W
    33576333U,	// DPSU_H_QBL
    33576791U,	// DPSU_H_QBR
    33576125U,	// DPSX_W_PH
    33576104U,	// DPS_W_PH
    1107318760U,	// DROTR
    1107312833U,	// DROTR32
    33577931U,	// DROTRV
    21135U,	// DSBH
    25324U,	// DSDIV
    20040U,	// DSHD
    1107318343U,	// DSLL
    1107312817U,	// DSLL32
    268457543U,	// DSLL64_32
    33577893U,	// DSLLV
    1107312982U,	// DSRA
    1107312799U,	// DSRA32
    33577872U,	// DSRAV
    1107318355U,	// DSRL
    1107312825U,	// DSRL32
    33577900U,	// DSRLV
    33572375U,	// DSUB
    33577664U,	// DSUBu
    25310U,	// DUDIV
    25325U,	// DivRxRy16
    25311U,	// DivuRxRy16
    9152U,	// EHB
    414945U,	// EI
    414945U,	// EI_MM
    9195U,	// ERET
    9195U,	// ERET_MM
    1107321668U,	// EXT
    1107318582U,	// EXTP
    1107318497U,	// EXTPDP
    33577915U,	// EXTPDPV
    33577924U,	// EXTPV
    33579149U,	// EXTRV_RS_W
    33578703U,	// EXTRV_R_W
    33575213U,	// EXTRV_S_H
    33579586U,	// EXTRV_W
    1107320962U,	// EXTR_RS_W
    1107320506U,	// EXTR_R_W
    1107316968U,	// EXTR_S_H
    1107320605U,	// EXTR_W
    1107321661U,	// EXTS
    1107321613U,	// EXTS32
    1107321668U,	// EXT_MM
    0U,	// ExtractElementF64
    0U,	// ExtractElementF64_64
    0U,	// FABS_D
    19396U,	// FABS_D32
    19396U,	// FABS_D64
    19396U,	// FABS_MM
    22918U,	// FABS_S
    22918U,	// FABS_S_MM
    0U,	// FABS_W
    33572734U,	// FADD_D
    33572735U,	// FADD_D32
    33572735U,	// FADD_D64
    33572735U,	// FADD_MM
    33576996U,	// FADD_S
    33576996U,	// FADD_S_MM
    33578051U,	// FADD_W
    33572968U,	// FCAF_D
    33578170U,	// FCAF_W
    33573398U,	// FCEQ_D
    33578609U,	// FCEQ_W
    19403U,	// FCLASS_D
    24729U,	// FCLASS_W
    33572812U,	// FCLE_D
    33578093U,	// FCLE_W
    33573889U,	// FCLT_D
    33579188U,	// FCLT_W
    992119U,	// FCMP_D32
    992119U,	// FCMP_D32_MM
    992119U,	// FCMP_D64
    1123191U,	// FCMP_S32
    1123191U,	// FCMP_S32_MM
    33572908U,	// FCNE_D
    33578127U,	// FCNE_W
    33573508U,	// FCOR_D
    33578738U,	// FCOR_W
    33573454U,	// FCUEQ_D
    33578625U,	// FCUEQ_W
    33572878U,	// FCULE_D
    33578109U,	// FCULE_W
    33573945U,	// FCULT_D
    33579204U,	// FCULT_W
    33572924U,	// FCUNE_D
    33578143U,	// FCUNE_W
    33573337U,	// FCUN_D
    33578515U,	// FCUN_W
    33574331U,	// FDIV_D
    33574332U,	// FDIV_D32
    33574332U,	// FDIV_D64
    33574332U,	// FDIV_MM
    33577469U,	// FDIV_S
    33577469U,	// FDIV_S_MM
    33579570U,	// FDIV_W
    33574871U,	// FEXDO_H
    33578531U,	// FEXDO_W
    33572621U,	// FEXP2_D
    0U,	// FEXP2_D_1_PSEUDO
    33577954U,	// FEXP2_W
    0U,	// FEXP2_W_1_PSEUDO
    18829U,	// FEXUPL_D
    24025U,	// FEXUPL_W
    19092U,	// FEXUPR_D
    24322U,	// FEXUPR_W
    19334U,	// FFINT_S_D
    24622U,	// FFINT_S_W
    19813U,	// FFINT_U_D
    25052U,	// FFINT_U_W
    18839U,	// FFQL_D
    24035U,	// FFQL_W
    19102U,	// FFQR_D
    24332U,	// FFQR_W
    17047U,	// FILL_B
    18814U,	// FILL_D
    0U,	// FILL_FD_PSEUDO
    0U,	// FILL_FW_PSEUDO
    20400U,	// FILL_H
    24010U,	// FILL_W
    18180U,	// FLOG2_D
    23513U,	// FLOG2_W
    18778U,	// FLOOR_L_D64
    22761U,	// FLOOR_L_S
    19954U,	// FLOOR_W_D32
    19954U,	// FLOOR_W_D64
    19954U,	// FLOOR_W_MM
    23083U,	// FLOOR_W_S
    23083U,	// FLOOR_W_S_MM
    2183153542U,	// FMADD_D
    2183158859U,	// FMADD_W
    33572659U,	// FMAX_A_D
    33577992U,	// FMAX_A_W
    33574406U,	// FMAX_D
    33579595U,	// FMAX_W
    33572639U,	// FMIN_A_D
    33577972U,	// FMIN_A_W
    33573311U,	// FMIN_D
    33578507U,	// FMIN_W
    19915U,	// FMOV_D32
    19915U,	// FMOV_D32_MM
    19915U,	// FMOV_D64
    23044U,	// FMOV_S
    23044U,	// FMOV_S_MM
    2183153500U,	// FMSUB_D
    2183158817U,	// FMSUB_W
    33573295U,	// FMUL_D
    33573296U,	// FMUL_D32
    33573296U,	// FMUL_D64
    33573296U,	// FMUL_MM
    33577229U,	// FMUL_S
    33577229U,	// FMUL_S_MM
    33578491U,	// FMUL_W
    18606U,	// FNEG_D32
    18606U,	// FNEG_D64
    18606U,	// FNEG_MM
    22722U,	// FNEG_S
    22722U,	// FNEG_S_MM
    18940U,	// FRCP_D
    24108U,	// FRCP_W
    19551U,	// FRINT_D
    24798U,	// FRINT_W
    19579U,	// FRSQRT_D
    24826U,	// FRSQRT_W
    33572987U,	// FSAF_D
    33578178U,	// FSAF_W
    33573426U,	// FSEQ_D
    33578617U,	// FSEQ_W
    33572850U,	// FSLE_D
    33578101U,	// FSLE_W
    33573917U,	// FSLT_D
    33579196U,	// FSLT_W
    33572916U,	// FSNE_D
    33578135U,	// FSNE_W
    33573516U,	// FSOR_D
    33578746U,	// FSOR_W
    19570U,	// FSQRT_D
    19571U,	// FSQRT_D32
    19571U,	// FSQRT_D64
    19571U,	// FSQRT_MM
    23021U,	// FSQRT_S
    23021U,	// FSQRT_S_MM
    24817U,	// FSQRT_W
    33572692U,	// FSUB_D
    33572693U,	// FSUB_D32
    33572693U,	// FSUB_D64
    33572693U,	// FSUB_MM
    33576978U,	// FSUB_S
    33576978U,	// FSUB_S_MM
    33578009U,	// FSUB_W
    33573475U,	// FSUEQ_D
    33578634U,	// FSUEQ_W
    33572899U,	// FSULE_D
    33578118U,	// FSULE_W
    33573966U,	// FSULT_D
    33579213U,	// FSULT_W
    33572933U,	// FSUNE_D
    33578152U,	// FSUNE_W
    33573356U,	// FSUN_D
    33578523U,	// FSUN_W
    19345U,	// FTINT_S_D
    24633U,	// FTINT_S_W
    19824U,	// FTINT_U_D
    25063U,	// FTINT_U_W
    33574948U,	// FTQ_H
    33578643U,	// FTQ_W
    19167U,	// FTRUNC_S_D
    24405U,	// FTRUNC_S_W
    19634U,	// FTRUNC_U_D
    24873U,	// FTRUNC_U_W
    304108813U,	// GotPrologue16
    33573611U,	// HADD_S_D
    33575027U,	// HADD_S_H
    33578849U,	// HADD_S_W
    33574078U,	// HADD_U_D
    33575315U,	// HADD_U_H
    33579317U,	// HADD_U_W
    33573578U,	// HSUB_S_D
    33575006U,	// HSUB_S_H
    33578816U,	// HSUB_S_W
    33574045U,	// HSUB_U_D
    33575294U,	// HSUB_U_H
    33579284U,	// HSUB_U_W
    33571982U,	// ILVEV_B
    33574322U,	// ILVEV_D
    33575537U,	// ILVEV_H
    33579561U,	// ILVEV_W
    33571510U,	// ILVL_B
    33573303U,	// ILVL_D
    33574863U,	// ILVL_H
    33578499U,	// ILVL_W
    33571262U,	// ILVOD_B
    33572776U,	// ILVOD_D
    33574665U,	// ILVOD_H
    33578084U,	// ILVOD_W
    33571558U,	// ILVR_B
    33573551U,	// ILVR_D
    33574988U,	// ILVR_H
    33578789U,	// ILVR_W
    1107321650U,	// INS
    9585589U,	// INSERT_B
    0U,	// INSERT_B_VIDX_PSEUDO
    9587816U,	// INSERT_D
    0U,	// INSERT_D_VIDX_PSEUDO
    0U,	// INSERT_FD_PSEUDO
    0U,	// INSERT_FD_VIDX_PSEUDO
    0U,	// INSERT_FW_PSEUDO
    0U,	// INSERT_FW_VIDX_PSEUDO
    9589092U,	// INSERT_H
    0U,	// INSERT_H_VIDX_PSEUDO
    9593063U,	// INSERT_W
    0U,	// INSERT_W_VIDX_PSEUDO
    2120659U,	// INSV
    11682247U,	// INSVE_B
    11683918U,	// INSVE_D
    11685650U,	// INSVE_H
    11689137U,	// INSVE_W
    1107321650U,	// INS_MM
    415051U,	// J
    415084U,	// JAL
    22488U,	// JALR
    415704U,	// JALR16_MM
    22488U,	// JALR64
    0U,	// JALR64Pseudo
    0U,	// JALRPseudo
    23162U,	// JALRS_MM
    17592U,	// JALR_HB
    22488U,	// JALR_MM
    416354U,	// JALS_MM
    418413U,	// JALX
    415084U,	// JAL_MM
    17982U,	// JIALC
    17971U,	// JIC
    415700U,	// JR
    415700U,	// JR64
    415531U,	// JRADDIUSP
    410801U,	// JR_HB
    410801U,	// JR_HB_R6
    415700U,	// JR_MM
    415051U,	// J_MM
    1332588U,	// Jal16
    1463660U,	// JalB16
    9144U,	// JrRa16
    9135U,	// JrcRa16
    418514U,	// JrcRx16
    409601U,	// JumpLinkReg16
    12600513U,	// LB
    12600513U,	// LB64
    337666675U,	// LBUX
    12600513U,	// LB_MM
    12606139U,	// LBu
    12606139U,	// LBu64
    12606139U,	// LBu_MM
    12602959U,	// LD
    12599337U,	// LDC1
    12599337U,	// LDC164
    12599337U,	// LDC1_MM
    12599498U,	// LDC2
    12599498U,	// LDC2_R6
    12599548U,	// LDC3
    16873U,	// LDI_B
    18622U,	// LDI_D
    20276U,	// LDI_H
    23860U,	// LDI_W
    12604872U,	// LDL
    18043U,	// LDPC
    12605330U,	// LDR
    337657961U,	// LDXC1
    337657961U,	// LDXC164
    12599727U,	// LD_B
    12601241U,	// LD_D
    12603130U,	// LD_H
    12606549U,	// LD_W
    4217597U,	// LEA_ADDiu
    4217596U,	// LEA_ADDiu64
    4217597U,	// LEA_ADDiu_MM
    12604064U,	// LH
    12604064U,	// LH64
    337666664U,	// LHX
    12604064U,	// LH_MM
    12606192U,	// LHu
    12606192U,	// LHu64
    12606192U,	// LHu_MM
    12604977U,	// LL
    12602958U,	// LLD
    12602958U,	// LLD_R6
    12604977U,	// LL_MM
    12604977U,	// LL_R6
    12599303U,	// LOAD_ACC128
    12599303U,	// LOAD_ACC64
    12599303U,	// LOAD_ACC64DSP
    12605180U,	// LOAD_CCOND_DSP
    0U,	// LONG_BRANCH_ADDiu
    0U,	// LONG_BRANCH_DADDiu
    0U,	// LONG_BRANCH_LUi
    33571165U,	// LSA
    33571165U,	// LSA_R6
    337657975U,	// LUXC1
    337657975U,	// LUXC164
    337657975U,	// LUXC1_MM
    14701894U,	// LUi
    14701894U,	// LUi64
    14701894U,	// LUi_MM
    12608096U,	// LW
    12608096U,	// LW64
    12599389U,	// LWC1
    12599389U,	// LWC1_MM
    12599524U,	// LWC2
    12599524U,	// LWC2_R6
    12599560U,	// LWC3
    12605051U,	// LWL
    12605051U,	// LWL64
    12605051U,	// LWL_MM
    18080U,	// LWPC
    12605430U,	// LWR
    12605430U,	// LWR64
    12605430U,	// LWR_MM
    18073U,	// LWUPC
    12606282U,	// LWU_MM
    337666681U,	// LWX
    337657989U,	// LWXC1
    337657989U,	// LWXC1_MM
    12608096U,	// LW_MM
    12606282U,	// LWu
    12600513U,	// LbRxRyOffMemX16
    12606139U,	// LbuRxRyOffMemX16
    12604064U,	// LhRxRyOffMemX16
    12606192U,	// LhuRxRyOffMemX16
    234902797U,	// LiRxImm16
    21763U,	// LiRxImmAlignX16
    21773U,	// LiRxImmX16
    14696736U,	// LoadAddr32Imm
    12599584U,	// LoadAddr32Reg
    14701837U,	// LoadImm32Reg
    21777U,	// LoadImm64Reg
    1598048U,	// LwConstant32
    67134048U,	// LwRxPcTcp16
    25184U,	// LwRxPcTcpX16
    12608096U,	// LwRxRyOffMemX16
    371221088U,	// LwRxSpImmX16
    20034U,	// MADD
    2183153804U,	// MADDF_D
    2183157929U,	// MADDF_S
    2183155720U,	// MADDR_Q_H
    2183159388U,	// MADDR_Q_W
    23260U,	// MADDU
    33577692U,	// MADDU_DSP
    23260U,	// MADDU_MM
    2183152764U,	// MADDV_B
    2183155104U,	// MADDV_D
    2183156319U,	// MADDV_H
    2183160343U,	// MADDV_W
    33572743U,	// MADD_D32
    33572743U,	// MADD_D32_MM
    33572743U,	// MADD_D64
    33574466U,	// MADD_DSP
    20034U,	// MADD_MM
    2183155690U,	// MADD_Q_H
    2183159358U,	// MADD_Q_W
    33576995U,	// MADD_S
    33576995U,	// MADD_S_MM
    33576436U,	// MAQ_SA_W_PHL
    33576860U,	// MAQ_SA_W_PHR
    33576464U,	// MAQ_S_W_PHL
    33576888U,	// MAQ_S_W_PHR
    33572684U,	// MAXA_D
    33576968U,	// MAXA_S
    33571633U,	// MAXI_S_B
    33573689U,	// MAXI_S_D
    33575105U,	// MAXI_S_H
    33578927U,	// MAXI_S_W
    570442760U,	// MAXI_U_B
    570445068U,	// MAXI_U_D
    570446305U,	// MAXI_U_H
    570450307U,	// MAXI_U_W
    33571214U,	// MAX_A_B
    33572660U,	// MAX_A_D
    33574617U,	// MAX_A_H
    33577993U,	// MAX_A_W
    33574407U,	// MAX_D
    33577535U,	// MAX_S
    33571721U,	// MAX_S_B
    33573809U,	// MAX_S_D
    33575224U,	// MAX_S_H
    33579108U,	// MAX_S_W
    33571936U,	// MAX_U_B
    33574276U,	// MAX_U_D
    33575491U,	// MAX_U_H
    33579515U,	// MAX_U_W
    1107312650U,	// MFC0
    16444U,	// MFC1
    16444U,	// MFC1_MM
    1107312855U,	// MFC2
    16450U,	// MFHC1_D32
    16450U,	// MFHC1_D64
    16450U,	// MFHC1_MM
    414967U,	// MFHI
    414967U,	// MFHI16_MM
    414967U,	// MFHI64
    21751U,	// MFHI_DSP
    414967U,	// MFHI_MM
    415421U,	// MFLO
    415421U,	// MFLO16_MM
    415421U,	// MFLO64
    22205U,	// MFLO_DSP
    415421U,	// MFLO_MM
    33572669U,	// MINA_D
    33576960U,	// MINA_S
    33571613U,	// MINI_S_B
    33573669U,	// MINI_S_D
    33575085U,	// MINI_S_H
    33578907U,	// MINI_S_W
    570442740U,	// MINI_U_B
    570445048U,	// MINI_U_D
    570446285U,	// MINI_U_H
    570450287U,	// MINI_U_W
    33571195U,	// MIN_A_B
    33572640U,	// MIN_A_D
    33574598U,	// MIN_A_H
    33577973U,	// MIN_A_W
    33573312U,	// MIN_D
    33577236U,	// MIN_S
    33571643U,	// MIN_S_B
    33573699U,	// MIN_S_D
    33575115U,	// MIN_S_H
    33578947U,	// MIN_S_W
    33571858U,	// MIN_U_B
    33574166U,	// MIN_U_D
    33575403U,	// MIN_U_H
    33579405U,	// MIN_U_W
    0U,	// MIPSeh_return32
    0U,	// MIPSeh_return64
    33574506U,	// MOD
    33572373U,	// MODSUB
    33577700U,	// MODU
    33571576U,	// MOD_S_B
    33573632U,	// MOD_S_D
    33575048U,	// MOD_S_H
    33578870U,	// MOD_S_W
    33571791U,	// MOD_U_B
    33574099U,	// MOD_U_D
    33575336U,	// MOD_U_H
    33579338U,	// MOD_U_W
    20110U,	// MOVE16_MM
    23382U,	// MOVE_V
    33573029U,	// MOVF_D32
    33573029U,	// MOVF_D32_MM
    33573029U,	// MOVF_D64
    33574578U,	// MOVF_I
    33574578U,	// MOVF_I64
    33574578U,	// MOVF_I_MM
    33577146U,	// MOVF_S
    33577146U,	// MOVF_S_MM
    33573364U,	// MOVN_I64_D64
    33576625U,	// MOVN_I64_I
    33576625U,	// MOVN_I64_I64
    33577272U,	// MOVN_I64_S
    33573364U,	// MOVN_I_D32
    33573364U,	// MOVN_I_D32_MM
    33573364U,	// MOVN_I_D64
    33576625U,	// MOVN_I_I
    33576625U,	// MOVN_I_I64
    33576625U,	// MOVN_I_MM
    33577272U,	// MOVN_I_S
    33577272U,	// MOVN_I_S_MM
    33574027U,	// MOVT_D32
    33574027U,	// MOVT_D32_MM
    33574027U,	// MOVT_D64
    33577653U,	// MOVT_I
    33577653U,	// MOVT_I64
    33577653U,	// MOVT_I_MM
    33577461U,	// MOVT_S
    33577461U,	// MOVT_S_MM
    33574447U,	// MOVZ_I64_D64
    33579718U,	// MOVZ_I64_I
    33579718U,	// MOVZ_I64_I64
    33577562U,	// MOVZ_I64_S
    33574447U,	// MOVZ_I_D32
    33574447U,	// MOVZ_I_D32_MM
    33574447U,	// MOVZ_I_D64
    33579718U,	// MOVZ_I_I
    33579718U,	// MOVZ_I_I64
    33579718U,	// MOVZ_I_MM
    33577562U,	// MOVZ_I_S
    33577562U,	// MOVZ_I_S_MM
    17949U,	// MSUB
    2183153795U,	// MSUBF_D
    2183157920U,	// MSUBF_S
    2183155709U,	// MSUBR_Q_H
    2183159377U,	// MSUBR_Q_W
    23239U,	// MSUBU
    33577671U,	// MSUBU_DSP
    23239U,	// MSUBU_MM
    2183152755U,	// MSUBV_B
    2183155095U,	// MSUBV_D
    2183156310U,	// MSUBV_H
    2183160334U,	// MSUBV_W
    33572701U,	// MSUB_D32
    33572701U,	// MSUB_D32_MM
    33572701U,	// MSUB_D64
    33572381U,	// MSUB_DSP
    17949U,	// MSUB_MM
    2183155680U,	// MSUB_Q_H
    2183159348U,	// MSUB_Q_W
    33576977U,	// MSUB_S
    33576977U,	// MSUB_S_MM
    1107312657U,	// MTC0
    704599U,	// MTC1
    704599U,	// MTC1_MM
    1107312862U,	// MTC2
    81993U,	// MTHC1_D32
    81993U,	// MTHC1_D64
    704585U,	// MTHC1_MM
    414973U,	// MTHI
    414973U,	// MTHI64
    709885U,	// MTHI_DSP
    414973U,	// MTHI_MM
    710377U,	// MTHLIP
    415434U,	// MTLO
    415434U,	// MTLO64
    710346U,	// MTLO_DSP
    415434U,	// MTLO_MM
    409629U,	// MTM0
    409747U,	// MTM1
    409840U,	// MTM2
    409635U,	// MTP0
    409753U,	// MTP1
    409846U,	// MTP2
    33576141U,	// MUH
    33577718U,	// MUHU
    33576566U,	// MUL
    33576477U,	// MULEQ_S_W_PHL
    33576901U,	// MULEQ_S_W_PHR
    33576345U,	// MULEU_S_PH_QBL
    33576803U,	// MULEU_S_PH_QBR
    33575902U,	// MULQ_RS_PH
    33579127U,	// MULQ_RS_W
    33575846U,	// MULQ_S_PH
    33578986U,	// MULQ_S_W
    33574931U,	// MULR_Q_H
    33578599U,	// MULR_Q_W
    33576048U,	// MULSAQ_S_W_PH
    33576023U,	// MULSA_W_PH
    23210U,	// MULT
    33577788U,	// MULTU_DSP
    33577642U,	// MULT_DSP
    23210U,	// MULT_MM
    23356U,	// MULTu
    23356U,	// MULTu_MM
    33577755U,	// MULU
    33571991U,	// MULV_B
    33574339U,	// MULV_D
    33575546U,	// MULV_H
    33579578U,	// MULV_W
    33576566U,	// MUL_MM
    33575719U,	// MUL_PH
    33574900U,	// MUL_Q_H
    33578568U,	// MUL_Q_W
    33576566U,	// MUL_R6
    33575814U,	// MUL_S_PH
    414967U,	// Mfhi16
    415421U,	// Mflo16
    20110U,	// Move32R16
    20110U,	// MoveR3216
    23210U,	// MultRxRy16
    17619626U,	// MultRxRyRz16
    23356U,	// MultuRxRy16
    17619772U,	// MultuRxRyRz16
    16798U,	// NLOC_B
    18286U,	// NLOC_D
    20201U,	// NLOC_H
    23594U,	// NLOC_W
    16806U,	// NLZC_B
    18294U,	// NLZC_D
    20209U,	// NLZC_H
    23602U,	// NLZC_W
    33572751U,	// NMADD_D32
    33572751U,	// NMADD_D32_MM
    33572751U,	// NMADD_D64
    33576994U,	// NMADD_S
    33576994U,	// NMADD_S_MM
    33572709U,	// NMSUB_D32
    33572709U,	// NMSUB_D32_MM
    33572709U,	// NMSUB_D64
    33576976U,	// NMSUB_S
    33576976U,	// NMSUB_S_MM
    0U,	// NOP
    33576926U,	// NOR
    33576926U,	// NOR64
    570442311U,	// NORI_B
    33576926U,	// NOR_MM
    33577830U,	// NOR_V
    0U,	// NOR_V_D_PSEUDO
    0U,	// NOR_V_H_PSEUDO
    0U,	// NOR_V_W_PSEUDO
    20152U,	// NegRxRy16
    23216U,	// NotRxRy16
    33576927U,	// OR
    33576927U,	// OR64
    570442312U,	// ORI_B
    33576927U,	// OR_MM
    33577831U,	// OR_V
    0U,	// OR_V_D_PSEUDO
    0U,	// OR_V_H_PSEUDO
    0U,	// OR_V_W_PSEUDO
    1107318057U,	// ORi
    1107318057U,	// ORi64
    1107318057U,	// ORi_MM
    2119647U,	// OrRxRxRy16
    33575708U,	// PACKRL_PH
    9156U,	// PAUSE
    33571973U,	// PCKEV_B
    33574313U,	// PCKEV_D
    33575528U,	// PCKEV_H
    33579552U,	// PCKEV_W
    33571253U,	// PCKOD_B
    33572767U,	// PCKOD_D
    33574656U,	// PCKOD_H
    33578075U,	// PCKOD_W
    17325U,	// PCNT_B
    19543U,	// PCNT_D
    20828U,	// PCNT_H
    24790U,	// PCNT_W
    33575672U,	// PICK_PH
    33572105U,	// PICK_QB
    22263U,	// POP
    21944U,	// PRECEQU_PH_QBL
    16676U,	// PRECEQU_PH_QBLA
    22402U,	// PRECEQU_PH_QBR
    16709U,	// PRECEQU_PH_QBRA
    22018U,	// PRECEQ_W_PHL
    22442U,	// PRECEQ_W_PHR
    21929U,	// PRECEU_PH_QBL
    16660U,	// PRECEU_PH_QBLA
    22387U,	// PRECEU_PH_QBR
    16693U,	// PRECEU_PH_QBRA
    33575624U,	// PRECRQU_S_QB_PH
    33578218U,	// PRECRQ_PH_W
    33575597U,	// PRECRQ_QB_PH
    33578249U,	// PRECRQ_RS_PH_W
    33575611U,	// PRECR_QB_PH
    1107320026U,	// PRECR_SRA_PH_W
    1107320055U,	// PRECR_SRA_R_PH_W
    36524U,	// PREF
    36524U,	// PREF_R6
    1107316312U,	// PREPEND
    0U,	// PseudoCMPU_EQ_QB
    0U,	// PseudoCMPU_LE_QB
    0U,	// PseudoCMPU_LT_QB
    0U,	// PseudoCMP_EQ_PH
    0U,	// PseudoCMP_LE_PH
    0U,	// PseudoCMP_LT_PH
    16391U,	// PseudoCVT_D32_W
    16391U,	// PseudoCVT_D64_L
    16391U,	// PseudoCVT_D64_W
    16391U,	// PseudoCVT_S_L
    16391U,	// PseudoCVT_S_W
    0U,	// PseudoDMULT
    0U,	// PseudoDMULTu
    0U,	// PseudoDSDIV
    0U,	// PseudoDUDIV
    0U,	// PseudoIndirectBranch
    0U,	// PseudoIndirectBranch64
    0U,	// PseudoMADD
    0U,	// PseudoMADDU
    0U,	// PseudoMFHI
    0U,	// PseudoMFHI64
    0U,	// PseudoMFLO
    0U,	// PseudoMFLO64
    0U,	// PseudoMSUB
    0U,	// PseudoMSUBU
    0U,	// PseudoMTLOHI
    0U,	// PseudoMTLOHI64
    0U,	// PseudoMTLOHI_DSP
    0U,	// PseudoMULT
    0U,	// PseudoMULTu
    0U,	// PseudoPICK_PH
    0U,	// PseudoPICK_QB
    0U,	// PseudoReturn
    0U,	// PseudoReturn64
    0U,	// PseudoSDIV
    0U,	// PseudoUDIV
    17925U,	// RADDU_W_QB
    14702365U,	// RDDSP
    22511U,	// RDHWR
    22511U,	// RDHWR64
    21531U,	// REPLV_PH
    17905U,	// REPLV_QB
    14701322U,	// REPL_PH
    14697755U,	// REPL_QB
    19552U,	// RINT_D
    23013U,	// RINT_S
    1107318761U,	// ROTR
    33577932U,	// ROTRV
    33577932U,	// ROTRV_MM
    1107318761U,	// ROTR_MM
    18757U,	// ROUND_L_D64
    22740U,	// ROUND_L_S
    19933U,	// ROUND_W_D32
    19933U,	// ROUND_W_D64
    19933U,	// ROUND_W_MM
    23062U,	// ROUND_W_S
    23062U,	// ROUND_W_S_MM
    0U,	// Restore16
    0U,	// RestoreX16
    0U,	// RetRA
    0U,	// RetRA16
    1107313506U,	// SAT_S_B
    1107315572U,	// SAT_S_D
    570446086U,	// SAT_S_H
    1107320860U,	// SAT_S_W
    1107313733U,	// SAT_U_B
    1107316051U,	// SAT_U_D
    570446376U,	// SAT_U_H
    1107321290U,	// SAT_U_W
    12600849U,	// SB
    12600849U,	// SB64
    12600849U,	// SB_MM
    1754799U,	// SC
    1756727U,	// SCD
    1756727U,	// SCD_R6
    1754799U,	// SC_MM
    1754799U,	// SC_R6
    12602991U,	// SD
    415450U,	// SDBBP
    415450U,	// SDBBP_R6
    12599343U,	// SDC1
    12599343U,	// SDC164
    12599343U,	// SDC1_MM
    12599504U,	// SDC2
    12599504U,	// SDC2_R6
    12599554U,	// SDC3
    25325U,	// SDIV
    25325U,	// SDIV_MM
    12604877U,	// SDL
    12605335U,	// SDR
    337657968U,	// SDXC1
    337657968U,	// SDXC164
    17580U,	// SEB
    17580U,	// SEB64
    17580U,	// SEB_MM
    21147U,	// SEH
    21147U,	// SEH64
    21147U,	// SEH_MM
    33579691U,	// SELEQZ
    33579691U,	// SELEQZ64
    33574437U,	// SELEQZ_D
    33577552U,	// SELEQZ_S
    33579664U,	// SELNEZ
    33579664U,	// SELNEZ64
    33574420U,	// SELNEZ_D
    33577542U,	// SELNEZ_S
    2183154030U,	// SEL_D
    2183158013U,	// SEL_S
    33576769U,	// SEQ
    33576220U,	// SEQi
    12604616U,	// SH
    12604616U,	// SH64
    570442193U,	// SHF_B
    570445596U,	// SHF_H
    570449099U,	// SHF_W
    22211U,	// SHILO
    23475U,	// SHILOV
    33575953U,	// SHLLV_PH
    33572327U,	// SHLLV_QB
    33575890U,	// SHLLV_S_PH
    33579097U,	// SHLLV_S_W
    1107317505U,	// SHLL_PH
    1107313938U,	// SHLL_QB
    1107317627U,	// SHLL_S_PH
    1107320761U,	// SHLL_S_W
    33575943U,	// SHRAV_PH
    33572317U,	// SHRAV_QB
    33575791U,	// SHRAV_R_PH
    33572215U,	// SHRAV_R_QB
    33578692U,	// SHRAV_R_W
    1107317412U,	// SHRA_PH
    1107313861U,	// SHRA_QB
    1107317580U,	// SHRA_R_PH
    1107314004U,	// SHRA_R_QB
    1107320474U,	// SHRA_R_W
    33575973U,	// SHRLV_PH
    33572347U,	// SHRLV_QB
    1107317523U,	// SHRL_PH
    1107313956U,	// SHRL_QB
    12604616U,	// SH_MM
    1814053352U,	// SLDI_B
    1814055101U,	// SLDI_D
    1814056755U,	// SLDI_H
    1814060339U,	// SLDI_W
    2350924206U,	// SLD_B
    2350925720U,	// SLD_D
    2350927609U,	// SLD_H
    2350931028U,	// SLD_W
    1107318344U,	// SLL
    402675272U,	// SLL64_32
    402675272U,	// SLL64_64
    570442250U,	// SLLI_B
    570443982U,	// SLLI_D
    570445636U,	// SLLI_H
    570449220U,	// SLLI_W
    33577894U,	// SLLV
    33577894U,	// SLLV_MM
    33571487U,	// SLL_B
    33573254U,	// SLL_D
    33574840U,	// SLL_H
    1107318344U,	// SLL_MM
    33578450U,	// SLL_W
    33577631U,	// SLT
    33577631U,	// SLT64
    33577631U,	// SLT_MM
    33576244U,	// SLTi
    33576244U,	// SLTi64
    33576244U,	// SLTi_MM
    33577739U,	// SLTiu
    33577739U,	// SLTiu64
    33577739U,	// SLTiu_MM
    33577775U,	// SLTu
    33577775U,	// SLTu64
    33577775U,	// SLTu_MM
    33574532U,	// SNE
    33576165U,	// SNEi
    0U,	// SNZ_B_PSEUDO
    0U,	// SNZ_D_PSEUDO
    0U,	// SNZ_H_PSEUDO
    0U,	// SNZ_V_PSEUDO
    0U,	// SNZ_W_PSEUDO
    738214497U,	// SPLATI_B
    738216213U,	// SPLATI_D
    738217867U,	// SPLATI_H
    738221451U,	// SPLATI_W
    201343900U,	// SPLAT_B
    201346005U,	// SPLAT_D
    201347403U,	// SPLAT_H
    201351331U,	// SPLAT_W
    1107312983U,	// SRA
    570442208U,	// SRAI_B
    570443957U,	// SRAI_D
    570445611U,	// SRAI_H
    570449195U,	// SRAI_W
    1107313196U,	// SRARI_B
    1107314928U,	// SRARI_D
    570445670U,	// SRARI_H
    1107320166U,	// SRARI_W
    33571525U,	// SRAR_B
    33573484U,	// SRAR_D
    33574955U,	// SRAR_H
    33578714U,	// SRAR_W
    33577873U,	// SRAV
    33577873U,	// SRAV_MM
    33571223U,	// SRA_B
    33572677U,	// SRA_D
    33574626U,	// SRA_H
    1107312983U,	// SRA_MM
    33578002U,	// SRA_W
    1107318356U,	// SRL
    570442258U,	// SRLI_B
    570443990U,	// SRLI_D
    570445644U,	// SRLI_H
    570449228U,	// SRLI_W
    1107313214U,	// SRLRI_B
    1107314946U,	// SRLRI_D
    570445688U,	// SRLRI_H
    1107320184U,	// SRLRI_W
    33571541U,	// SRLR_B
    33573500U,	// SRLR_D
    33574971U,	// SRLR_H
    33578730U,	// SRLR_W
    33577901U,	// SRLV
    33577901U,	// SRLV_MM
    33571494U,	// SRL_B
    33573279U,	// SRL_D
    33574847U,	// SRL_H
    1107318356U,	// SRL_MM
    33578475U,	// SRL_W
    9177U,	// SSNOP
    12599303U,	// STORE_ACC128
    12599303U,	// STORE_ACC64
    12599303U,	// STORE_ACC64DSP
    12605196U,	// STORE_CCOND_DSP
    12600255U,	// ST_B
    12602501U,	// ST_D
    12603758U,	// ST_H
    12607748U,	// ST_W
    33572376U,	// SUB
    33575652U,	// SUBQH_PH
    33575767U,	// SUBQH_R_PH
    33578660U,	// SUBQH_R_W
    33578265U,	// SUBQH_W
    33575727U,	// SUBQ_PH
    33575824U,	// SUBQ_S_PH
    33578966U,	// SUBQ_S_W
    33571897U,	// SUBSUS_U_B
    33574215U,	// SUBSUS_U_D
    33575452U,	// SUBSUS_U_H
    33579454U,	// SUBSUS_U_W
    33571700U,	// SUBSUU_S_B
    33573788U,	// SUBSUU_S_D
    33575192U,	// SUBSUU_S_H
    33579076U,	// SUBSUU_S_W
    33571662U,	// SUBS_S_B
    33573728U,	// SUBS_S_D
    33575154U,	// SUBS_S_H
    33579016U,	// SUBS_S_W
    33571877U,	// SUBS_U_B
    33574195U,	// SUBS_U_D
    33575432U,	// SUBS_U_H
    33579434U,	// SUBS_U_W
    33572085U,	// SUBUH_QB
    33572191U,	// SUBUH_R_QB
    33575925U,	// SUBU_PH
    33572299U,	// SUBU_QB
    33575868U,	// SUBU_S_PH
    33572238U,	// SUBU_S_QB
    570442356U,	// SUBVI_B
    570444072U,	// SUBVI_D
    570445726U,	// SUBVI_H
    570449310U,	// SUBVI_W
    33571956U,	// SUBV_B
    33574296U,	// SUBV_D
    33575511U,	// SUBV_H
    33579535U,	// SUBV_W
    33572376U,	// SUB_MM
    33577665U,	// SUBu
    33577665U,	// SUBu_MM
    337657982U,	// SUXC1
    337657982U,	// SUXC164
    337657982U,	// SUXC1_MM
    12608100U,	// SW
    12608100U,	// SW64
    12599395U,	// SWC1
    12599395U,	// SWC1_MM
    12599530U,	// SWC2
    12599530U,	// SWC2_R6
    12599566U,	// SWC3
    12605056U,	// SWL
    12605056U,	// SWL64
    12605056U,	// SWL_MM
    12605435U,	// SWR
    12605435U,	// SWR64
    12605435U,	// SWR_MM
    337657996U,	// SWXC1
    337657996U,	// SWXC1_MM
    12608100U,	// SW_MM
    418581U,	// SYNC
    418581U,	// SYNC_MM
    415276U,	// SYSCALL
    415276U,	// SYSCALL_MM
    0U,	// SZ_B_PSEUDO
    0U,	// SZ_D_PSEUDO
    0U,	// SZ_H_PSEUDO
    0U,	// SZ_V_PSEUDO
    0U,	// SZ_W_PSEUDO
    0U,	// Save16
    0U,	// SaveX16
    12600849U,	// SbRxRyOffMemX16
    418508U,	// SebRx16
    418520U,	// SehRx16
    1942181U,	// SelBeqZ
    1942154U,	// SelBneZ
    455186161U,	// SelTBteqZCmp
    455185686U,	// SelTBteqZCmpi
    455187103U,	// SelTBteqZSlt
    455185716U,	// SelTBteqZSlti
    455187211U,	// SelTBteqZSltiu
    455187247U,	// SelTBteqZSltu
    488740593U,	// SelTBtneZCmp
    488740118U,	// SelTBtneZCmpi
    488741535U,	// SelTBtneZSlt
    488740148U,	// SelTBtneZSlti
    488741643U,	// SelTBtneZSltiu
    488741679U,	// SelTBtneZSltu
    12604616U,	// ShRxRyOffMemX16
    1107318344U,	// SllX16
    2120614U,	// SllvRxRy16
    21813919U,	// SltCCRxRy16
    23199U,	// SltRxRy16
    21812532U,	// SltiCCRxImmX16
    234902836U,	// SltiRxImm16
    21812U,	// SltiRxImmX16
    21814027U,	// SltiuCCRxImmX16
    234904331U,	// SltiuRxImm16
    23307U,	// SltiuRxImmX16
    21814063U,	// SltuCCRxRy16
    23343U,	// SltuRxRy16
    21814063U,	// SltuRxRyRz16
    1107312983U,	// SraX16
    2120593U,	// SravRxRy16
    1107318356U,	// SrlX16
    2120621U,	// SrlvRxRy16
    33577665U,	// SubuRxRyRz16
    12608100U,	// SwRxRyOffMemX16
    371221092U,	// SwRxSpImmX16
    0U,	// TAILCALL
    0U,	// TAILCALL64_R
    0U,	// TAILCALL_R
    1107318598U,	// TEQ
    14701858U,	// TEQI
    14701858U,	// TEQI_MM
    1107318598U,	// TEQ_MM
    1107316339U,	// TGE
    14701791U,	// TGEI
    14703364U,	// TGEIU
    14703364U,	// TGEIU_MM
    14701791U,	// TGEI_MM
    1107319530U,	// TGEU
    1107319530U,	// TGEU_MM
    1107316339U,	// TGE_MM
    9172U,	// TLBP
    9172U,	// TLBP_MM
    9183U,	// TLBR
    9183U,	// TLBR_MM
    9162U,	// TLBWI
    9162U,	// TLBWI_MM
    9188U,	// TLBWR
    9188U,	// TLBWR_MM
    1107319460U,	// TLT
    14701882U,	// TLTI
    14703378U,	// TLTIU_MM
    14701882U,	// TLTI_MM
    1107319605U,	// TLTU
    1107319605U,	// TLTU_MM
    1107319460U,	// TLT_MM
    1107316361U,	// TNE
    14701803U,	// TNEI
    14701803U,	// TNEI_MM
    1107316361U,	// TNE_MM
    0U,	// TRAP
    18746U,	// TRUNC_L_D64
    22729U,	// TRUNC_L_S
    19922U,	// TRUNC_W_D32
    19922U,	// TRUNC_W_D64
    19922U,	// TRUNC_W_MM
    23051U,	// TRUNC_W_S
    23051U,	// TRUNC_W_S_MM
    14703378U,	// TTLTIU
    25311U,	// UDIV
    25311U,	// UDIV_MM
    33577753U,	// V3MULU
    33570839U,	// VMM0
    33577768U,	// VMULU
    2183152080U,	// VSHF_B
    2183153813U,	// VSHF_D
    2183155483U,	// VSHF_H
    2183158986U,	// VSHF_W
    9200U,	// WAIT
    416409U,	// WAIT_MM
    14702372U,	// WRDSP
    21141U,	// WSBH
    21141U,	// WSBH_MM
    33576931U,	// XOR
    33576931U,	// XOR64
    570442319U,	// XORI_B
    33576931U,	// XOR_MM
    33577837U,	// XOR_V
    0U,	// XOR_V_D_PSEUDO
    0U,	// XOR_V_H_PSEUDO
    0U,	// XOR_V_W_PSEUDO
    1107318056U,	// XORi
    1107318056U,	// XORi64
    1107318056U,	// XORi_MM
    2119651U,	// XorRxRxRy16
    0U
  };

  static const uint8_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// ABSQ_S_PH
    0U,	// ABSQ_S_QB
    0U,	// ABSQ_S_W
    0U,	// ADD
    0U,	// ADDIUPC
    0U,	// ADDQH_PH
    0U,	// ADDQH_R_PH
    0U,	// ADDQH_R_W
    0U,	// ADDQH_W
    0U,	// ADDQ_PH
    0U,	// ADDQ_S_PH
    0U,	// ADDQ_S_W
    0U,	// ADDSC
    0U,	// ADDS_A_B
    0U,	// ADDS_A_D
    0U,	// ADDS_A_H
    0U,	// ADDS_A_W
    0U,	// ADDS_S_B
    0U,	// ADDS_S_D
    0U,	// ADDS_S_H
    0U,	// ADDS_S_W
    0U,	// ADDS_U_B
    0U,	// ADDS_U_D
    0U,	// ADDS_U_H
    0U,	// ADDS_U_W
    0U,	// ADDUH_QB
    0U,	// ADDUH_R_QB
    0U,	// ADDU_PH
    0U,	// ADDU_QB
    0U,	// ADDU_S_PH
    0U,	// ADDU_S_QB
    0U,	// ADDVI_B
    0U,	// ADDVI_D
    0U,	// ADDVI_H
    0U,	// ADDVI_W
    0U,	// ADDV_B
    0U,	// ADDV_D
    0U,	// ADDV_H
    0U,	// ADDV_W
    0U,	// ADDWC
    0U,	// ADD_A_B
    0U,	// ADD_A_D
    0U,	// ADD_A_H
    0U,	// ADD_A_W
    0U,	// ADD_MM
    0U,	// ADDi
    0U,	// ADDi_MM
    0U,	// ADDiu
    0U,	// ADDiu_MM
    0U,	// ADDu
    0U,	// ADDu_MM
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    1U,	// ALIGN
    0U,	// ALUIPC
    0U,	// AND
    0U,	// AND64
    0U,	// ANDI_B
    0U,	// AND_MM
    0U,	// AND_V
    0U,	// AND_V_D_PSEUDO
    0U,	// AND_V_H_PSEUDO
    0U,	// AND_V_W_PSEUDO
    0U,	// ANDi
    0U,	// ANDi64
    0U,	// ANDi_MM
    0U,	// APPEND
    0U,	// ASUB_S_B
    0U,	// ASUB_S_D
    0U,	// ASUB_S_H
    0U,	// ASUB_S_W
    0U,	// ASUB_U_B
    0U,	// ASUB_U_D
    0U,	// ASUB_U_H
    0U,	// ASUB_U_W
    0U,	// ATOMIC_CMP_SWAP_I16
    0U,	// ATOMIC_CMP_SWAP_I32
    0U,	// ATOMIC_CMP_SWAP_I64
    0U,	// ATOMIC_CMP_SWAP_I8
    0U,	// ATOMIC_LOAD_ADD_I16
    0U,	// ATOMIC_LOAD_ADD_I32
    0U,	// ATOMIC_LOAD_ADD_I64
    0U,	// ATOMIC_LOAD_ADD_I8
    0U,	// ATOMIC_LOAD_AND_I16
    0U,	// ATOMIC_LOAD_AND_I32
    0U,	// ATOMIC_LOAD_AND_I64
    0U,	// ATOMIC_LOAD_AND_I8
    0U,	// ATOMIC_LOAD_NAND_I16
    0U,	// ATOMIC_LOAD_NAND_I32
    0U,	// ATOMIC_LOAD_NAND_I64
    0U,	// ATOMIC_LOAD_NAND_I8
    0U,	// ATOMIC_LOAD_OR_I16
    0U,	// ATOMIC_LOAD_OR_I32
    0U,	// ATOMIC_LOAD_OR_I64
    0U,	// ATOMIC_LOAD_OR_I8
    0U,	// ATOMIC_LOAD_SUB_I16
    0U,	// ATOMIC_LOAD_SUB_I32
    0U,	// ATOMIC_LOAD_SUB_I64
    0U,	// ATOMIC_LOAD_SUB_I8
    0U,	// ATOMIC_LOAD_XOR_I16
    0U,	// ATOMIC_LOAD_XOR_I32
    0U,	// ATOMIC_LOAD_XOR_I64
    0U,	// ATOMIC_LOAD_XOR_I8
    0U,	// ATOMIC_SWAP_I16
    0U,	// ATOMIC_SWAP_I32
    0U,	// ATOMIC_SWAP_I64
    0U,	// ATOMIC_SWAP_I8
    0U,	// AUI
    0U,	// AUIPC
    0U,	// AVER_S_B
    0U,	// AVER_S_D
    0U,	// AVER_S_H
    0U,	// AVER_S_W
    0U,	// AVER_U_B
    0U,	// AVER_U_D
    0U,	// AVER_U_H
    0U,	// AVER_U_W
    0U,	// AVE_S_B
    0U,	// AVE_S_D
    0U,	// AVE_S_H
    0U,	// AVE_S_W
    0U,	// AVE_U_B
    0U,	// AVE_U_D
    0U,	// AVE_U_H
    0U,	// AVE_U_W
    0U,	// AddiuRxImmX16
    0U,	// AddiuRxPcImmX16
    0U,	// AddiuRxRxImm16
    0U,	// AddiuRxRxImmX16
    0U,	// AddiuRxRyOffMemX16
    0U,	// AddiuSpImm16
    0U,	// AddiuSpImmX16
    0U,	// AdduRxRyRz16
    0U,	// AndRxRxRy16
    0U,	// B
    0U,	// BADDu
    0U,	// BAL
    0U,	// BALC
    0U,	// BALIGN
    0U,	// BAL_BR
    0U,	// BC
    0U,	// BC0F
    0U,	// BC0FL
    0U,	// BC0T
    0U,	// BC0TL
    0U,	// BC1EQZ
    0U,	// BC1F
    0U,	// BC1FL
    0U,	// BC1F_MM
    0U,	// BC1NEZ
    0U,	// BC1T
    0U,	// BC1TL
    0U,	// BC1T_MM
    0U,	// BC2EQZ
    0U,	// BC2F
    0U,	// BC2FL
    0U,	// BC2NEZ
    0U,	// BC2T
    0U,	// BC2TL
    0U,	// BC3F
    0U,	// BC3FL
    0U,	// BC3T
    0U,	// BC3TL
    0U,	// BCLRI_B
    0U,	// BCLRI_D
    0U,	// BCLRI_H
    0U,	// BCLRI_W
    0U,	// BCLR_B
    0U,	// BCLR_D
    0U,	// BCLR_H
    0U,	// BCLR_W
    0U,	// BEQ
    0U,	// BEQ64
    0U,	// BEQC
    0U,	// BEQL
    0U,	// BEQZALC
    0U,	// BEQZC
    0U,	// BEQZC_MM
    0U,	// BEQ_MM
    0U,	// BGEC
    0U,	// BGEUC
    0U,	// BGEZ
    0U,	// BGEZ64
    0U,	// BGEZAL
    0U,	// BGEZALC
    0U,	// BGEZALL
    0U,	// BGEZALS_MM
    0U,	// BGEZAL_MM
    0U,	// BGEZC
    0U,	// BGEZL
    0U,	// BGEZ_MM
    0U,	// BGTZ
    0U,	// BGTZ64
    0U,	// BGTZALC
    0U,	// BGTZC
    0U,	// BGTZL
    0U,	// BGTZ_MM
    0U,	// BINSLI_B
    0U,	// BINSLI_D
    0U,	// BINSLI_H
    0U,	// BINSLI_W
    0U,	// BINSL_B
    0U,	// BINSL_D
    0U,	// BINSL_H
    0U,	// BINSL_W
    0U,	// BINSRI_B
    0U,	// BINSRI_D
    0U,	// BINSRI_H
    0U,	// BINSRI_W
    0U,	// BINSR_B
    0U,	// BINSR_D
    0U,	// BINSR_H
    0U,	// BINSR_W
    0U,	// BITREV
    0U,	// BITSWAP
    0U,	// BLEZ
    0U,	// BLEZ64
    0U,	// BLEZALC
    0U,	// BLEZC
    0U,	// BLEZL
    0U,	// BLEZ_MM
    0U,	// BLTC
    0U,	// BLTUC
    0U,	// BLTZ
    0U,	// BLTZ64
    0U,	// BLTZAL
    0U,	// BLTZALC
    0U,	// BLTZALL
    0U,	// BLTZALS_MM
    0U,	// BLTZAL_MM
    0U,	// BLTZC
    0U,	// BLTZL
    0U,	// BLTZ_MM
    0U,	// BMNZI_B
    0U,	// BMNZ_V
    0U,	// BMZI_B
    0U,	// BMZ_V
    0U,	// BNE
    0U,	// BNE64
    0U,	// BNEC
    0U,	// BNEGI_B
    0U,	// BNEGI_D
    0U,	// BNEGI_H
    0U,	// BNEGI_W
    0U,	// BNEG_B
    0U,	// BNEG_D
    0U,	// BNEG_H
    0U,	// BNEG_W
    0U,	// BNEL
    0U,	// BNEZALC
    0U,	// BNEZC
    0U,	// BNEZC_MM
    0U,	// BNE_MM
    0U,	// BNVC
    0U,	// BNZ_B
    0U,	// BNZ_D
    0U,	// BNZ_H
    0U,	// BNZ_V
    0U,	// BNZ_W
    0U,	// BOVC
    0U,	// BPOSGE32
    0U,	// BPOSGE32_PSEUDO
    0U,	// BREAK
    0U,	// BREAK_MM
    0U,	// BSELI_B
    0U,	// BSEL_D_PSEUDO
    0U,	// BSEL_FD_PSEUDO
    0U,	// BSEL_FW_PSEUDO
    0U,	// BSEL_H_PSEUDO
    0U,	// BSEL_V
    0U,	// BSEL_W_PSEUDO
    0U,	// BSETI_B
    0U,	// BSETI_D
    0U,	// BSETI_H
    0U,	// BSETI_W
    0U,	// BSET_B
    0U,	// BSET_D
    0U,	// BSET_H
    0U,	// BSET_W
    0U,	// BZ_B
    0U,	// BZ_D
    0U,	// BZ_H
    0U,	// BZ_V
    0U,	// BZ_W
    0U,	// BeqzRxImm16
    0U,	// BeqzRxImmX16
    0U,	// Bimm16
    0U,	// BimmX16
    0U,	// BnezRxImm16
    0U,	// BnezRxImmX16
    0U,	// Break16
    0U,	// Bteqz16
    0U,	// BteqzT8CmpX16
    0U,	// BteqzT8CmpiX16
    0U,	// BteqzT8SltX16
    0U,	// BteqzT8SltiX16
    0U,	// BteqzT8SltiuX16
    0U,	// BteqzT8SltuX16
    0U,	// BteqzX16
    0U,	// Btnez16
    0U,	// BtnezT8CmpX16
    0U,	// BtnezT8CmpiX16
    0U,	// BtnezT8SltX16
    0U,	// BtnezT8SltiX16
    0U,	// BtnezT8SltiuX16
    0U,	// BtnezT8SltuX16
    0U,	// BtnezX16
    0U,	// BuildPairF64
    0U,	// BuildPairF64_64
    0U,	// CACHE
    0U,	// CACHE_R6
    0U,	// CEIL_L_D64
    0U,	// CEIL_L_S
    0U,	// CEIL_W_D32
    0U,	// CEIL_W_D64
    0U,	// CEIL_W_MM
    0U,	// CEIL_W_S
    0U,	// CEIL_W_S_MM
    0U,	// CEQI_B
    0U,	// CEQI_D
    0U,	// CEQI_H
    0U,	// CEQI_W
    0U,	// CEQ_B
    0U,	// CEQ_D
    0U,	// CEQ_H
    0U,	// CEQ_W
    0U,	// CFC1
    0U,	// CFC1_MM
    0U,	// CFCMSA
    1U,	// CINS
    1U,	// CINS32
    0U,	// CLASS_D
    0U,	// CLASS_S
    0U,	// CLEI_S_B
    0U,	// CLEI_S_D
    0U,	// CLEI_S_H
    0U,	// CLEI_S_W
    0U,	// CLEI_U_B
    0U,	// CLEI_U_D
    0U,	// CLEI_U_H
    0U,	// CLEI_U_W
    0U,	// CLE_S_B
    0U,	// CLE_S_D
    0U,	// CLE_S_H
    0U,	// CLE_S_W
    0U,	// CLE_U_B
    0U,	// CLE_U_D
    0U,	// CLE_U_H
    0U,	// CLE_U_W
    0U,	// CLO
    0U,	// CLO_MM
    0U,	// CLO_R6
    0U,	// CLTI_S_B
    0U,	// CLTI_S_D
    0U,	// CLTI_S_H
    0U,	// CLTI_S_W
    0U,	// CLTI_U_B
    0U,	// CLTI_U_D
    0U,	// CLTI_U_H
    0U,	// CLTI_U_W
    0U,	// CLT_S_B
    0U,	// CLT_S_D
    0U,	// CLT_S_H
    0U,	// CLT_S_W
    0U,	// CLT_U_B
    0U,	// CLT_U_D
    0U,	// CLT_U_H
    0U,	// CLT_U_W
    0U,	// CLZ
    0U,	// CLZ_MM
    0U,	// CLZ_R6
    0U,	// CMPGDU_EQ_QB
    0U,	// CMPGDU_LE_QB
    0U,	// CMPGDU_LT_QB
    0U,	// CMPGU_EQ_QB
    0U,	// CMPGU_LE_QB
    0U,	// CMPGU_LT_QB
    0U,	// CMPU_EQ_QB
    0U,	// CMPU_LE_QB
    0U,	// CMPU_LT_QB
    0U,	// CMP_EQ_D
    0U,	// CMP_EQ_PH
    0U,	// CMP_EQ_S
    0U,	// CMP_F_D
    0U,	// CMP_F_S
    0U,	// CMP_LE_D
    0U,	// CMP_LE_PH
    0U,	// CMP_LE_S
    0U,	// CMP_LT_D
    0U,	// CMP_LT_PH
    0U,	// CMP_LT_S
    0U,	// CMP_SAF_D
    0U,	// CMP_SAF_S
    0U,	// CMP_SEQ_D
    0U,	// CMP_SEQ_S
    0U,	// CMP_SLE_D
    0U,	// CMP_SLE_S
    0U,	// CMP_SLT_D
    0U,	// CMP_SLT_S
    0U,	// CMP_SUEQ_D
    0U,	// CMP_SUEQ_S
    0U,	// CMP_SULE_D
    0U,	// CMP_SULE_S
    0U,	// CMP_SULT_D
    0U,	// CMP_SULT_S
    0U,	// CMP_SUN_D
    0U,	// CMP_SUN_S
    0U,	// CMP_UEQ_D
    0U,	// CMP_UEQ_S
    0U,	// CMP_ULE_D
    0U,	// CMP_ULE_S
    0U,	// CMP_ULT_D
    0U,	// CMP_ULT_S
    0U,	// CMP_UN_D
    0U,	// CMP_UN_S
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_FD_PSEUDO
    0U,	// COPY_FW_PSEUDO
    2U,	// COPY_S_B
    2U,	// COPY_S_D
    2U,	// COPY_S_H
    2U,	// COPY_S_W
    2U,	// COPY_U_B
    2U,	// COPY_U_D
    2U,	// COPY_U_H
    2U,	// COPY_U_W
    0U,	// CTC1
    0U,	// CTC1_MM
    0U,	// CTCMSA
    0U,	// CVT_D32_S
    0U,	// CVT_D32_W
    0U,	// CVT_D32_W_MM
    0U,	// CVT_D64_L
    0U,	// CVT_D64_S
    0U,	// CVT_D64_W
    0U,	// CVT_D_S_MM
    0U,	// CVT_L_D64
    0U,	// CVT_L_D64_MM
    0U,	// CVT_L_S
    0U,	// CVT_L_S_MM
    0U,	// CVT_S_D32
    0U,	// CVT_S_D32_MM
    0U,	// CVT_S_D64
    0U,	// CVT_S_L
    0U,	// CVT_S_W
    0U,	// CVT_S_W_MM
    0U,	// CVT_W_D32
    0U,	// CVT_W_D64
    0U,	// CVT_W_MM
    0U,	// CVT_W_S
    0U,	// CVT_W_S_MM
    0U,	// C_EQ_D32
    0U,	// C_EQ_D64
    0U,	// C_EQ_S
    0U,	// C_F_D32
    0U,	// C_F_D64
    0U,	// C_F_S
    0U,	// C_LE_D32
    0U,	// C_LE_D64
    0U,	// C_LE_S
    0U,	// C_LT_D32
    0U,	// C_LT_D64
    0U,	// C_LT_S
    0U,	// C_NGE_D32
    0U,	// C_NGE_D64
    0U,	// C_NGE_S
    0U,	// C_NGLE_D32
    0U,	// C_NGLE_D64
    0U,	// C_NGLE_S
    0U,	// C_NGL_D32
    0U,	// C_NGL_D64
    0U,	// C_NGL_S
    0U,	// C_NGT_D32
    0U,	// C_NGT_D64
    0U,	// C_NGT_S
    0U,	// C_OLE_D32
    0U,	// C_OLE_D64
    0U,	// C_OLE_S
    0U,	// C_OLT_D32
    0U,	// C_OLT_D64
    0U,	// C_OLT_S
    0U,	// C_SEQ_D32
    0U,	// C_SEQ_D64
    0U,	// C_SEQ_S
    0U,	// C_SF_D32
    0U,	// C_SF_D64
    0U,	// C_SF_S
    0U,	// C_UEQ_D32
    0U,	// C_UEQ_D64
    0U,	// C_UEQ_S
    0U,	// C_ULE_D32
    0U,	// C_ULE_D64
    0U,	// C_ULE_S
    0U,	// C_ULT_D32
    0U,	// C_ULT_D64
    0U,	// C_ULT_S
    0U,	// C_UN_D32
    0U,	// C_UN_D64
    0U,	// C_UN_S
    0U,	// CmpRxRy16
    0U,	// CmpiRxImm16
    0U,	// CmpiRxImmX16
    0U,	// Constant32
    0U,	// DADD
    0U,	// DADDi
    0U,	// DADDiu
    0U,	// DADDu
    0U,	// DAHI
    1U,	// DALIGN
    0U,	// DATI
    0U,	// DAUI
    0U,	// DBITSWAP
    0U,	// DCLO
    0U,	// DCLO_R6
    0U,	// DCLZ
    0U,	// DCLZ_R6
    0U,	// DDIV
    0U,	// DDIVU
    0U,	// DERET
    0U,	// DERET_MM
    5U,	// DEXT
    5U,	// DEXTM
    5U,	// DEXTU
    0U,	// DI
    5U,	// DINS
    5U,	// DINSM
    5U,	// DINSU
    0U,	// DIV
    0U,	// DIVU
    0U,	// DIV_S_B
    0U,	// DIV_S_D
    0U,	// DIV_S_H
    0U,	// DIV_S_W
    0U,	// DIV_U_B
    0U,	// DIV_U_D
    0U,	// DIV_U_H
    0U,	// DIV_U_W
    0U,	// DI_MM
    1U,	// DLSA
    1U,	// DLSA_R6
    0U,	// DMFC0
    0U,	// DMFC1
    0U,	// DMFC2
    0U,	// DMOD
    0U,	// DMODU
    0U,	// DMTC0
    0U,	// DMTC1
    0U,	// DMTC2
    0U,	// DMUH
    0U,	// DMUHU
    0U,	// DMUL
    0U,	// DMULT
    0U,	// DMULTu
    0U,	// DMULU
    0U,	// DMUL_R6
    0U,	// DOTP_S_D
    0U,	// DOTP_S_H
    0U,	// DOTP_S_W
    0U,	// DOTP_U_D
    0U,	// DOTP_U_H
    0U,	// DOTP_U_W
    0U,	// DPADD_S_D
    0U,	// DPADD_S_H
    0U,	// DPADD_S_W
    0U,	// DPADD_U_D
    0U,	// DPADD_U_H
    0U,	// DPADD_U_W
    0U,	// DPAQX_SA_W_PH
    0U,	// DPAQX_S_W_PH
    0U,	// DPAQ_SA_L_W
    0U,	// DPAQ_S_W_PH
    0U,	// DPAU_H_QBL
    0U,	// DPAU_H_QBR
    0U,	// DPAX_W_PH
    0U,	// DPA_W_PH
    0U,	// DPOP
    0U,	// DPSQX_SA_W_PH
    0U,	// DPSQX_S_W_PH
    0U,	// DPSQ_SA_L_W
    0U,	// DPSQ_S_W_PH
    0U,	// DPSUB_S_D
    0U,	// DPSUB_S_H
    0U,	// DPSUB_S_W
    0U,	// DPSUB_U_D
    0U,	// DPSUB_U_H
    0U,	// DPSUB_U_W
    0U,	// DPSU_H_QBL
    0U,	// DPSU_H_QBR
    0U,	// DPSX_W_PH
    0U,	// DPS_W_PH
    0U,	// DROTR
    0U,	// DROTR32
    0U,	// DROTRV
    0U,	// DSBH
    0U,	// DSDIV
    0U,	// DSHD
    0U,	// DSLL
    0U,	// DSLL32
    0U,	// DSLL64_32
    0U,	// DSLLV
    0U,	// DSRA
    0U,	// DSRA32
    0U,	// DSRAV
    0U,	// DSRL
    0U,	// DSRL32
    0U,	// DSRLV
    0U,	// DSUB
    0U,	// DSUBu
    0U,	// DUDIV
    0U,	// DivRxRy16
    0U,	// DivuRxRy16
    0U,	// EHB
    0U,	// EI
    0U,	// EI_MM
    0U,	// ERET
    0U,	// ERET_MM
    5U,	// EXT
    0U,	// EXTP
    0U,	// EXTPDP
    0U,	// EXTPDPV
    0U,	// EXTPV
    0U,	// EXTRV_RS_W
    0U,	// EXTRV_R_W
    0U,	// EXTRV_S_H
    0U,	// EXTRV_W
    0U,	// EXTR_RS_W
    0U,	// EXTR_R_W
    0U,	// EXTR_S_H
    0U,	// EXTR_W
    1U,	// EXTS
    1U,	// EXTS32
    5U,	// EXT_MM
    0U,	// ExtractElementF64
    0U,	// ExtractElementF64_64
    0U,	// FABS_D
    0U,	// FABS_D32
    0U,	// FABS_D64
    0U,	// FABS_MM
    0U,	// FABS_S
    0U,	// FABS_S_MM
    0U,	// FABS_W
    0U,	// FADD_D
    0U,	// FADD_D32
    0U,	// FADD_D64
    0U,	// FADD_MM
    0U,	// FADD_S
    0U,	// FADD_S_MM
    0U,	// FADD_W
    0U,	// FCAF_D
    0U,	// FCAF_W
    0U,	// FCEQ_D
    0U,	// FCEQ_W
    0U,	// FCLASS_D
    0U,	// FCLASS_W
    0U,	// FCLE_D
    0U,	// FCLE_W
    0U,	// FCLT_D
    0U,	// FCLT_W
    0U,	// FCMP_D32
    0U,	// FCMP_D32_MM
    0U,	// FCMP_D64
    0U,	// FCMP_S32
    0U,	// FCMP_S32_MM
    0U,	// FCNE_D
    0U,	// FCNE_W
    0U,	// FCOR_D
    0U,	// FCOR_W
    0U,	// FCUEQ_D
    0U,	// FCUEQ_W
    0U,	// FCULE_D
    0U,	// FCULE_W
    0U,	// FCULT_D
    0U,	// FCULT_W
    0U,	// FCUNE_D
    0U,	// FCUNE_W
    0U,	// FCUN_D
    0U,	// FCUN_W
    0U,	// FDIV_D
    0U,	// FDIV_D32
    0U,	// FDIV_D64
    0U,	// FDIV_MM
    0U,	// FDIV_S
    0U,	// FDIV_S_MM
    0U,	// FDIV_W
    0U,	// FEXDO_H
    0U,	// FEXDO_W
    0U,	// FEXP2_D
    0U,	// FEXP2_D_1_PSEUDO
    0U,	// FEXP2_W
    0U,	// FEXP2_W_1_PSEUDO
    0U,	// FEXUPL_D
    0U,	// FEXUPL_W
    0U,	// FEXUPR_D
    0U,	// FEXUPR_W
    0U,	// FFINT_S_D
    0U,	// FFINT_S_W
    0U,	// FFINT_U_D
    0U,	// FFINT_U_W
    0U,	// FFQL_D
    0U,	// FFQL_W
    0U,	// FFQR_D
    0U,	// FFQR_W
    0U,	// FILL_B
    0U,	// FILL_D
    0U,	// FILL_FD_PSEUDO
    0U,	// FILL_FW_PSEUDO
    0U,	// FILL_H
    0U,	// FILL_W
    0U,	// FLOG2_D
    0U,	// FLOG2_W
    0U,	// FLOOR_L_D64
    0U,	// FLOOR_L_S
    0U,	// FLOOR_W_D32
    0U,	// FLOOR_W_D64
    0U,	// FLOOR_W_MM
    0U,	// FLOOR_W_S
    0U,	// FLOOR_W_S_MM
    0U,	// FMADD_D
    0U,	// FMADD_W
    0U,	// FMAX_A_D
    0U,	// FMAX_A_W
    0U,	// FMAX_D
    0U,	// FMAX_W
    0U,	// FMIN_A_D
    0U,	// FMIN_A_W
    0U,	// FMIN_D
    0U,	// FMIN_W
    0U,	// FMOV_D32
    0U,	// FMOV_D32_MM
    0U,	// FMOV_D64
    0U,	// FMOV_S
    0U,	// FMOV_S_MM
    0U,	// FMSUB_D
    0U,	// FMSUB_W
    0U,	// FMUL_D
    0U,	// FMUL_D32
    0U,	// FMUL_D64
    0U,	// FMUL_MM
    0U,	// FMUL_S
    0U,	// FMUL_S_MM
    0U,	// FMUL_W
    0U,	// FNEG_D32
    0U,	// FNEG_D64
    0U,	// FNEG_MM
    0U,	// FNEG_S
    0U,	// FNEG_S_MM
    0U,	// FRCP_D
    0U,	// FRCP_W
    0U,	// FRINT_D
    0U,	// FRINT_W
    0U,	// FRSQRT_D
    0U,	// FRSQRT_W
    0U,	// FSAF_D
    0U,	// FSAF_W
    0U,	// FSEQ_D
    0U,	// FSEQ_W
    0U,	// FSLE_D
    0U,	// FSLE_W
    0U,	// FSLT_D
    0U,	// FSLT_W
    0U,	// FSNE_D
    0U,	// FSNE_W
    0U,	// FSOR_D
    0U,	// FSOR_W
    0U,	// FSQRT_D
    0U,	// FSQRT_D32
    0U,	// FSQRT_D64
    0U,	// FSQRT_MM
    0U,	// FSQRT_S
    0U,	// FSQRT_S_MM
    0U,	// FSQRT_W
    0U,	// FSUB_D
    0U,	// FSUB_D32
    0U,	// FSUB_D64
    0U,	// FSUB_MM
    0U,	// FSUB_S
    0U,	// FSUB_S_MM
    0U,	// FSUB_W
    0U,	// FSUEQ_D
    0U,	// FSUEQ_W
    0U,	// FSULE_D
    0U,	// FSULE_W
    0U,	// FSULT_D
    0U,	// FSULT_W
    0U,	// FSUNE_D
    0U,	// FSUNE_W
    0U,	// FSUN_D
    0U,	// FSUN_W
    0U,	// FTINT_S_D
    0U,	// FTINT_S_W
    0U,	// FTINT_U_D
    0U,	// FTINT_U_W
    0U,	// FTQ_H
    0U,	// FTQ_W
    0U,	// FTRUNC_S_D
    0U,	// FTRUNC_S_W
    0U,	// FTRUNC_U_D
    0U,	// FTRUNC_U_W
    0U,	// GotPrologue16
    0U,	// HADD_S_D
    0U,	// HADD_S_H
    0U,	// HADD_S_W
    0U,	// HADD_U_D
    0U,	// HADD_U_H
    0U,	// HADD_U_W
    0U,	// HSUB_S_D
    0U,	// HSUB_S_H
    0U,	// HSUB_S_W
    0U,	// HSUB_U_D
    0U,	// HSUB_U_H
    0U,	// HSUB_U_W
    0U,	// ILVEV_B
    0U,	// ILVEV_D
    0U,	// ILVEV_H
    0U,	// ILVEV_W
    0U,	// ILVL_B
    0U,	// ILVL_D
    0U,	// ILVL_H
    0U,	// ILVL_W
    0U,	// ILVOD_B
    0U,	// ILVOD_D
    0U,	// ILVOD_H
    0U,	// ILVOD_W
    0U,	// ILVR_B
    0U,	// ILVR_D
    0U,	// ILVR_H
    0U,	// ILVR_W
    5U,	// INS
    0U,	// INSERT_B
    0U,	// INSERT_B_VIDX_PSEUDO
    0U,	// INSERT_D
    0U,	// INSERT_D_VIDX_PSEUDO
    0U,	// INSERT_FD_PSEUDO
    0U,	// INSERT_FD_VIDX_PSEUDO
    0U,	// INSERT_FW_PSEUDO
    0U,	// INSERT_FW_VIDX_PSEUDO
    0U,	// INSERT_H
    0U,	// INSERT_H_VIDX_PSEUDO
    0U,	// INSERT_W
    0U,	// INSERT_W_VIDX_PSEUDO
    0U,	// INSV
    0U,	// INSVE_B
    0U,	// INSVE_D
    0U,	// INSVE_H
    0U,	// INSVE_W
    5U,	// INS_MM
    0U,	// J
    0U,	// JAL
    0U,	// JALR
    0U,	// JALR16_MM
    0U,	// JALR64
    0U,	// JALR64Pseudo
    0U,	// JALRPseudo
    0U,	// JALRS_MM
    0U,	// JALR_HB
    0U,	// JALR_MM
    0U,	// JALS_MM
    0U,	// JALX
    0U,	// JAL_MM
    0U,	// JIALC
    0U,	// JIC
    0U,	// JR
    0U,	// JR64
    0U,	// JRADDIUSP
    0U,	// JR_HB
    0U,	// JR_HB_R6
    0U,	// JR_MM
    0U,	// J_MM
    0U,	// Jal16
    0U,	// JalB16
    0U,	// JrRa16
    0U,	// JrcRa16
    0U,	// JrcRx16
    0U,	// JumpLinkReg16
    0U,	// LB
    0U,	// LB64
    0U,	// LBUX
    0U,	// LB_MM
    0U,	// LBu
    0U,	// LBu64
    0U,	// LBu_MM
    0U,	// LD
    0U,	// LDC1
    0U,	// LDC164
    0U,	// LDC1_MM
    0U,	// LDC2
    0U,	// LDC2_R6
    0U,	// LDC3
    0U,	// LDI_B
    0U,	// LDI_D
    0U,	// LDI_H
    0U,	// LDI_W
    0U,	// LDL
    0U,	// LDPC
    0U,	// LDR
    0U,	// LDXC1
    0U,	// LDXC164
    0U,	// LD_B
    0U,	// LD_D
    0U,	// LD_H
    0U,	// LD_W
    0U,	// LEA_ADDiu
    0U,	// LEA_ADDiu64
    0U,	// LEA_ADDiu_MM
    0U,	// LH
    0U,	// LH64
    0U,	// LHX
    0U,	// LH_MM
    0U,	// LHu
    0U,	// LHu64
    0U,	// LHu_MM
    0U,	// LL
    0U,	// LLD
    0U,	// LLD_R6
    0U,	// LL_MM
    0U,	// LL_R6
    0U,	// LOAD_ACC128
    0U,	// LOAD_ACC64
    0U,	// LOAD_ACC64DSP
    0U,	// LOAD_CCOND_DSP
    0U,	// LONG_BRANCH_ADDiu
    0U,	// LONG_BRANCH_DADDiu
    0U,	// LONG_BRANCH_LUi
    1U,	// LSA
    1U,	// LSA_R6
    0U,	// LUXC1
    0U,	// LUXC164
    0U,	// LUXC1_MM
    0U,	// LUi
    0U,	// LUi64
    0U,	// LUi_MM
    0U,	// LW
    0U,	// LW64
    0U,	// LWC1
    0U,	// LWC1_MM
    0U,	// LWC2
    0U,	// LWC2_R6
    0U,	// LWC3
    0U,	// LWL
    0U,	// LWL64
    0U,	// LWL_MM
    0U,	// LWPC
    0U,	// LWR
    0U,	// LWR64
    0U,	// LWR_MM
    0U,	// LWUPC
    0U,	// LWU_MM
    0U,	// LWX
    0U,	// LWXC1
    0U,	// LWXC1_MM
    0U,	// LW_MM
    0U,	// LWu
    0U,	// LbRxRyOffMemX16
    0U,	// LbuRxRyOffMemX16
    0U,	// LhRxRyOffMemX16
    0U,	// LhuRxRyOffMemX16
    0U,	// LiRxImm16
    0U,	// LiRxImmAlignX16
    0U,	// LiRxImmX16
    0U,	// LoadAddr32Imm
    0U,	// LoadAddr32Reg
    0U,	// LoadImm32Reg
    0U,	// LoadImm64Reg
    0U,	// LwConstant32
    0U,	// LwRxPcTcp16
    0U,	// LwRxPcTcpX16
    0U,	// LwRxRyOffMemX16
    0U,	// LwRxSpImmX16
    0U,	// MADD
    0U,	// MADDF_D
    0U,	// MADDF_S
    0U,	// MADDR_Q_H
    0U,	// MADDR_Q_W
    0U,	// MADDU
    0U,	// MADDU_DSP
    0U,	// MADDU_MM
    0U,	// MADDV_B
    0U,	// MADDV_D
    0U,	// MADDV_H
    0U,	// MADDV_W
    5U,	// MADD_D32
    5U,	// MADD_D32_MM
    5U,	// MADD_D64
    0U,	// MADD_DSP
    0U,	// MADD_MM
    0U,	// MADD_Q_H
    0U,	// MADD_Q_W
    5U,	// MADD_S
    5U,	// MADD_S_MM
    0U,	// MAQ_SA_W_PHL
    0U,	// MAQ_SA_W_PHR
    0U,	// MAQ_S_W_PHL
    0U,	// MAQ_S_W_PHR
    0U,	// MAXA_D
    0U,	// MAXA_S
    0U,	// MAXI_S_B
    0U,	// MAXI_S_D
    0U,	// MAXI_S_H
    0U,	// MAXI_S_W
    0U,	// MAXI_U_B
    0U,	// MAXI_U_D
    0U,	// MAXI_U_H
    0U,	// MAXI_U_W
    0U,	// MAX_A_B
    0U,	// MAX_A_D
    0U,	// MAX_A_H
    0U,	// MAX_A_W
    0U,	// MAX_D
    0U,	// MAX_S
    0U,	// MAX_S_B
    0U,	// MAX_S_D
    0U,	// MAX_S_H
    0U,	// MAX_S_W
    0U,	// MAX_U_B
    0U,	// MAX_U_D
    0U,	// MAX_U_H
    0U,	// MAX_U_W
    0U,	// MFC0
    0U,	// MFC1
    0U,	// MFC1_MM
    0U,	// MFC2
    0U,	// MFHC1_D32
    0U,	// MFHC1_D64
    0U,	// MFHC1_MM
    0U,	// MFHI
    0U,	// MFHI16_MM
    0U,	// MFHI64
    0U,	// MFHI_DSP
    0U,	// MFHI_MM
    0U,	// MFLO
    0U,	// MFLO16_MM
    0U,	// MFLO64
    0U,	// MFLO_DSP
    0U,	// MFLO_MM
    0U,	// MINA_D
    0U,	// MINA_S
    0U,	// MINI_S_B
    0U,	// MINI_S_D
    0U,	// MINI_S_H
    0U,	// MINI_S_W
    0U,	// MINI_U_B
    0U,	// MINI_U_D
    0U,	// MINI_U_H
    0U,	// MINI_U_W
    0U,	// MIN_A_B
    0U,	// MIN_A_D
    0U,	// MIN_A_H
    0U,	// MIN_A_W
    0U,	// MIN_D
    0U,	// MIN_S
    0U,	// MIN_S_B
    0U,	// MIN_S_D
    0U,	// MIN_S_H
    0U,	// MIN_S_W
    0U,	// MIN_U_B
    0U,	// MIN_U_D
    0U,	// MIN_U_H
    0U,	// MIN_U_W
    0U,	// MIPSeh_return32
    0U,	// MIPSeh_return64
    0U,	// MOD
    0U,	// MODSUB
    0U,	// MODU
    0U,	// MOD_S_B
    0U,	// MOD_S_D
    0U,	// MOD_S_H
    0U,	// MOD_S_W
    0U,	// MOD_U_B
    0U,	// MOD_U_D
    0U,	// MOD_U_H
    0U,	// MOD_U_W
    0U,	// MOVE16_MM
    0U,	// MOVE_V
    0U,	// MOVF_D32
    0U,	// MOVF_D32_MM
    0U,	// MOVF_D64
    0U,	// MOVF_I
    0U,	// MOVF_I64
    0U,	// MOVF_I_MM
    0U,	// MOVF_S
    0U,	// MOVF_S_MM
    0U,	// MOVN_I64_D64
    0U,	// MOVN_I64_I
    0U,	// MOVN_I64_I64
    0U,	// MOVN_I64_S
    0U,	// MOVN_I_D32
    0U,	// MOVN_I_D32_MM
    0U,	// MOVN_I_D64
    0U,	// MOVN_I_I
    0U,	// MOVN_I_I64
    0U,	// MOVN_I_MM
    0U,	// MOVN_I_S
    0U,	// MOVN_I_S_MM
    0U,	// MOVT_D32
    0U,	// MOVT_D32_MM
    0U,	// MOVT_D64
    0U,	// MOVT_I
    0U,	// MOVT_I64
    0U,	// MOVT_I_MM
    0U,	// MOVT_S
    0U,	// MOVT_S_MM
    0U,	// MOVZ_I64_D64
    0U,	// MOVZ_I64_I
    0U,	// MOVZ_I64_I64
    0U,	// MOVZ_I64_S
    0U,	// MOVZ_I_D32
    0U,	// MOVZ_I_D32_MM
    0U,	// MOVZ_I_D64
    0U,	// MOVZ_I_I
    0U,	// MOVZ_I_I64
    0U,	// MOVZ_I_MM
    0U,	// MOVZ_I_S
    0U,	// MOVZ_I_S_MM
    0U,	// MSUB
    0U,	// MSUBF_D
    0U,	// MSUBF_S
    0U,	// MSUBR_Q_H
    0U,	// MSUBR_Q_W
    0U,	// MSUBU
    0U,	// MSUBU_DSP
    0U,	// MSUBU_MM
    0U,	// MSUBV_B
    0U,	// MSUBV_D
    0U,	// MSUBV_H
    0U,	// MSUBV_W
    5U,	// MSUB_D32
    5U,	// MSUB_D32_MM
    5U,	// MSUB_D64
    0U,	// MSUB_DSP
    0U,	// MSUB_MM
    0U,	// MSUB_Q_H
    0U,	// MSUB_Q_W
    5U,	// MSUB_S
    5U,	// MSUB_S_MM
    0U,	// MTC0
    0U,	// MTC1
    0U,	// MTC1_MM
    0U,	// MTC2
    0U,	// MTHC1_D32
    0U,	// MTHC1_D64
    0U,	// MTHC1_MM
    0U,	// MTHI
    0U,	// MTHI64
    0U,	// MTHI_DSP
    0U,	// MTHI_MM
    0U,	// MTHLIP
    0U,	// MTLO
    0U,	// MTLO64
    0U,	// MTLO_DSP
    0U,	// MTLO_MM
    0U,	// MTM0
    0U,	// MTM1
    0U,	// MTM2
    0U,	// MTP0
    0U,	// MTP1
    0U,	// MTP2
    0U,	// MUH
    0U,	// MUHU
    0U,	// MUL
    0U,	// MULEQ_S_W_PHL
    0U,	// MULEQ_S_W_PHR
    0U,	// MULEU_S_PH_QBL
    0U,	// MULEU_S_PH_QBR
    0U,	// MULQ_RS_PH
    0U,	// MULQ_RS_W
    0U,	// MULQ_S_PH
    0U,	// MULQ_S_W
    0U,	// MULR_Q_H
    0U,	// MULR_Q_W
    0U,	// MULSAQ_S_W_PH
    0U,	// MULSA_W_PH
    0U,	// MULT
    0U,	// MULTU_DSP
    0U,	// MULT_DSP
    0U,	// MULT_MM
    0U,	// MULTu
    0U,	// MULTu_MM
    0U,	// MULU
    0U,	// MULV_B
    0U,	// MULV_D
    0U,	// MULV_H
    0U,	// MULV_W
    0U,	// MUL_MM
    0U,	// MUL_PH
    0U,	// MUL_Q_H
    0U,	// MUL_Q_W
    0U,	// MUL_R6
    0U,	// MUL_S_PH
    0U,	// Mfhi16
    0U,	// Mflo16
    0U,	// Move32R16
    0U,	// MoveR3216
    0U,	// MultRxRy16
    0U,	// MultRxRyRz16
    0U,	// MultuRxRy16
    0U,	// MultuRxRyRz16
    0U,	// NLOC_B
    0U,	// NLOC_D
    0U,	// NLOC_H
    0U,	// NLOC_W
    0U,	// NLZC_B
    0U,	// NLZC_D
    0U,	// NLZC_H
    0U,	// NLZC_W
    5U,	// NMADD_D32
    5U,	// NMADD_D32_MM
    5U,	// NMADD_D64
    5U,	// NMADD_S
    5U,	// NMADD_S_MM
    5U,	// NMSUB_D32
    5U,	// NMSUB_D32_MM
    5U,	// NMSUB_D64
    5U,	// NMSUB_S
    5U,	// NMSUB_S_MM
    0U,	// NOP
    0U,	// NOR
    0U,	// NOR64
    0U,	// NORI_B
    0U,	// NOR_MM
    0U,	// NOR_V
    0U,	// NOR_V_D_PSEUDO
    0U,	// NOR_V_H_PSEUDO
    0U,	// NOR_V_W_PSEUDO
    0U,	// NegRxRy16
    0U,	// NotRxRy16
    0U,	// OR
    0U,	// OR64
    0U,	// ORI_B
    0U,	// OR_MM
    0U,	// OR_V
    0U,	// OR_V_D_PSEUDO
    0U,	// OR_V_H_PSEUDO
    0U,	// OR_V_W_PSEUDO
    0U,	// ORi
    0U,	// ORi64
    0U,	// ORi_MM
    0U,	// OrRxRxRy16
    0U,	// PACKRL_PH
    0U,	// PAUSE
    0U,	// PCKEV_B
    0U,	// PCKEV_D
    0U,	// PCKEV_H
    0U,	// PCKEV_W
    0U,	// PCKOD_B
    0U,	// PCKOD_D
    0U,	// PCKOD_H
    0U,	// PCKOD_W
    0U,	// PCNT_B
    0U,	// PCNT_D
    0U,	// PCNT_H
    0U,	// PCNT_W
    0U,	// PICK_PH
    0U,	// PICK_QB
    0U,	// POP
    0U,	// PRECEQU_PH_QBL
    0U,	// PRECEQU_PH_QBLA
    0U,	// PRECEQU_PH_QBR
    0U,	// PRECEQU_PH_QBRA
    0U,	// PRECEQ_W_PHL
    0U,	// PRECEQ_W_PHR
    0U,	// PRECEU_PH_QBL
    0U,	// PRECEU_PH_QBLA
    0U,	// PRECEU_PH_QBR
    0U,	// PRECEU_PH_QBRA
    0U,	// PRECRQU_S_QB_PH
    0U,	// PRECRQ_PH_W
    0U,	// PRECRQ_QB_PH
    0U,	// PRECRQ_RS_PH_W
    0U,	// PRECR_QB_PH
    0U,	// PRECR_SRA_PH_W
    0U,	// PRECR_SRA_R_PH_W
    0U,	// PREF
    0U,	// PREF_R6
    0U,	// PREPEND
    0U,	// PseudoCMPU_EQ_QB
    0U,	// PseudoCMPU_LE_QB
    0U,	// PseudoCMPU_LT_QB
    0U,	// PseudoCMP_EQ_PH
    0U,	// PseudoCMP_LE_PH
    0U,	// PseudoCMP_LT_PH
    0U,	// PseudoCVT_D32_W
    0U,	// PseudoCVT_D64_L
    0U,	// PseudoCVT_D64_W
    0U,	// PseudoCVT_S_L
    0U,	// PseudoCVT_S_W
    0U,	// PseudoDMULT
    0U,	// PseudoDMULTu
    0U,	// PseudoDSDIV
    0U,	// PseudoDUDIV
    0U,	// PseudoIndirectBranch
    0U,	// PseudoIndirectBranch64
    0U,	// PseudoMADD
    0U,	// PseudoMADDU
    0U,	// PseudoMFHI
    0U,	// PseudoMFHI64
    0U,	// PseudoMFLO
    0U,	// PseudoMFLO64
    0U,	// PseudoMSUB
    0U,	// PseudoMSUBU
    0U,	// PseudoMTLOHI
    0U,	// PseudoMTLOHI64
    0U,	// PseudoMTLOHI_DSP
    0U,	// PseudoMULT
    0U,	// PseudoMULTu
    0U,	// PseudoPICK_PH
    0U,	// PseudoPICK_QB
    0U,	// PseudoReturn
    0U,	// PseudoReturn64
    0U,	// PseudoSDIV
    0U,	// PseudoUDIV
    0U,	// RADDU_W_QB
    0U,	// RDDSP
    0U,	// RDHWR
    0U,	// RDHWR64
    0U,	// REPLV_PH
    0U,	// REPLV_QB
    0U,	// REPL_PH
    0U,	// REPL_QB
    0U,	// RINT_D
    0U,	// RINT_S
    0U,	// ROTR
    0U,	// ROTRV
    0U,	// ROTRV_MM
    0U,	// ROTR_MM
    0U,	// ROUND_L_D64
    0U,	// ROUND_L_S
    0U,	// ROUND_W_D32
    0U,	// ROUND_W_D64
    0U,	// ROUND_W_MM
    0U,	// ROUND_W_S
    0U,	// ROUND_W_S_MM
    0U,	// Restore16
    0U,	// RestoreX16
    0U,	// RetRA
    0U,	// RetRA16
    0U,	// SAT_S_B
    0U,	// SAT_S_D
    0U,	// SAT_S_H
    0U,	// SAT_S_W
    0U,	// SAT_U_B
    0U,	// SAT_U_D
    0U,	// SAT_U_H
    0U,	// SAT_U_W
    0U,	// SB
    0U,	// SB64
    0U,	// SB_MM
    0U,	// SC
    0U,	// SCD
    0U,	// SCD_R6
    0U,	// SC_MM
    0U,	// SC_R6
    0U,	// SD
    0U,	// SDBBP
    0U,	// SDBBP_R6
    0U,	// SDC1
    0U,	// SDC164
    0U,	// SDC1_MM
    0U,	// SDC2
    0U,	// SDC2_R6
    0U,	// SDC3
    0U,	// SDIV
    0U,	// SDIV_MM
    0U,	// SDL
    0U,	// SDR
    0U,	// SDXC1
    0U,	// SDXC164
    0U,	// SEB
    0U,	// SEB64
    0U,	// SEB_MM
    0U,	// SEH
    0U,	// SEH64
    0U,	// SEH_MM
    0U,	// SELEQZ
    0U,	// SELEQZ64
    0U,	// SELEQZ_D
    0U,	// SELEQZ_S
    0U,	// SELNEZ
    0U,	// SELNEZ64
    0U,	// SELNEZ_D
    0U,	// SELNEZ_S
    0U,	// SEL_D
    0U,	// SEL_S
    0U,	// SEQ
    0U,	// SEQi
    0U,	// SH
    0U,	// SH64
    0U,	// SHF_B
    0U,	// SHF_H
    0U,	// SHF_W
    0U,	// SHILO
    0U,	// SHILOV
    0U,	// SHLLV_PH
    0U,	// SHLLV_QB
    0U,	// SHLLV_S_PH
    0U,	// SHLLV_S_W
    0U,	// SHLL_PH
    0U,	// SHLL_QB
    0U,	// SHLL_S_PH
    0U,	// SHLL_S_W
    0U,	// SHRAV_PH
    0U,	// SHRAV_QB
    0U,	// SHRAV_R_PH
    0U,	// SHRAV_R_QB
    0U,	// SHRAV_R_W
    0U,	// SHRA_PH
    0U,	// SHRA_QB
    0U,	// SHRA_R_PH
    0U,	// SHRA_R_QB
    0U,	// SHRA_R_W
    0U,	// SHRLV_PH
    0U,	// SHRLV_QB
    0U,	// SHRL_PH
    0U,	// SHRL_QB
    0U,	// SH_MM
    2U,	// SLDI_B
    2U,	// SLDI_D
    2U,	// SLDI_H
    2U,	// SLDI_W
    2U,	// SLD_B
    2U,	// SLD_D
    2U,	// SLD_H
    2U,	// SLD_W
    0U,	// SLL
    0U,	// SLL64_32
    0U,	// SLL64_64
    0U,	// SLLI_B
    0U,	// SLLI_D
    0U,	// SLLI_H
    0U,	// SLLI_W
    0U,	// SLLV
    0U,	// SLLV_MM
    0U,	// SLL_B
    0U,	// SLL_D
    0U,	// SLL_H
    0U,	// SLL_MM
    0U,	// SLL_W
    0U,	// SLT
    0U,	// SLT64
    0U,	// SLT_MM
    0U,	// SLTi
    0U,	// SLTi64
    0U,	// SLTi_MM
    0U,	// SLTiu
    0U,	// SLTiu64
    0U,	// SLTiu_MM
    0U,	// SLTu
    0U,	// SLTu64
    0U,	// SLTu_MM
    0U,	// SNE
    0U,	// SNEi
    0U,	// SNZ_B_PSEUDO
    0U,	// SNZ_D_PSEUDO
    0U,	// SNZ_H_PSEUDO
    0U,	// SNZ_V_PSEUDO
    0U,	// SNZ_W_PSEUDO
    2U,	// SPLATI_B
    2U,	// SPLATI_D
    2U,	// SPLATI_H
    2U,	// SPLATI_W
    2U,	// SPLAT_B
    2U,	// SPLAT_D
    2U,	// SPLAT_H
    2U,	// SPLAT_W
    0U,	// SRA
    0U,	// SRAI_B
    0U,	// SRAI_D
    0U,	// SRAI_H
    0U,	// SRAI_W
    0U,	// SRARI_B
    0U,	// SRARI_D
    0U,	// SRARI_H
    0U,	// SRARI_W
    0U,	// SRAR_B
    0U,	// SRAR_D
    0U,	// SRAR_H
    0U,	// SRAR_W
    0U,	// SRAV
    0U,	// SRAV_MM
    0U,	// SRA_B
    0U,	// SRA_D
    0U,	// SRA_H
    0U,	// SRA_MM
    0U,	// SRA_W
    0U,	// SRL
    0U,	// SRLI_B
    0U,	// SRLI_D
    0U,	// SRLI_H
    0U,	// SRLI_W
    0U,	// SRLRI_B
    0U,	// SRLRI_D
    0U,	// SRLRI_H
    0U,	// SRLRI_W
    0U,	// SRLR_B
    0U,	// SRLR_D
    0U,	// SRLR_H
    0U,	// SRLR_W
    0U,	// SRLV
    0U,	// SRLV_MM
    0U,	// SRL_B
    0U,	// SRL_D
    0U,	// SRL_H
    0U,	// SRL_MM
    0U,	// SRL_W
    0U,	// SSNOP
    0U,	// STORE_ACC128
    0U,	// STORE_ACC64
    0U,	// STORE_ACC64DSP
    0U,	// STORE_CCOND_DSP
    0U,	// ST_B
    0U,	// ST_D
    0U,	// ST_H
    0U,	// ST_W
    0U,	// SUB
    0U,	// SUBQH_PH
    0U,	// SUBQH_R_PH
    0U,	// SUBQH_R_W
    0U,	// SUBQH_W
    0U,	// SUBQ_PH
    0U,	// SUBQ_S_PH
    0U,	// SUBQ_S_W
    0U,	// SUBSUS_U_B
    0U,	// SUBSUS_U_D
    0U,	// SUBSUS_U_H
    0U,	// SUBSUS_U_W
    0U,	// SUBSUU_S_B
    0U,	// SUBSUU_S_D
    0U,	// SUBSUU_S_H
    0U,	// SUBSUU_S_W
    0U,	// SUBS_S_B
    0U,	// SUBS_S_D
    0U,	// SUBS_S_H
    0U,	// SUBS_S_W
    0U,	// SUBS_U_B
    0U,	// SUBS_U_D
    0U,	// SUBS_U_H
    0U,	// SUBS_U_W
    0U,	// SUBUH_QB
    0U,	// SUBUH_R_QB
    0U,	// SUBU_PH
    0U,	// SUBU_QB
    0U,	// SUBU_S_PH
    0U,	// SUBU_S_QB
    0U,	// SUBVI_B
    0U,	// SUBVI_D
    0U,	// SUBVI_H
    0U,	// SUBVI_W
    0U,	// SUBV_B
    0U,	// SUBV_D
    0U,	// SUBV_H
    0U,	// SUBV_W
    0U,	// SUB_MM
    0U,	// SUBu
    0U,	// SUBu_MM
    0U,	// SUXC1
    0U,	// SUXC164
    0U,	// SUXC1_MM
    0U,	// SW
    0U,	// SW64
    0U,	// SWC1
    0U,	// SWC1_MM
    0U,	// SWC2
    0U,	// SWC2_R6
    0U,	// SWC3
    0U,	// SWL
    0U,	// SWL64
    0U,	// SWL_MM
    0U,	// SWR
    0U,	// SWR64
    0U,	// SWR_MM
    0U,	// SWXC1
    0U,	// SWXC1_MM
    0U,	// SW_MM
    0U,	// SYNC
    0U,	// SYNC_MM
    0U,	// SYSCALL
    0U,	// SYSCALL_MM
    0U,	// SZ_B_PSEUDO
    0U,	// SZ_D_PSEUDO
    0U,	// SZ_H_PSEUDO
    0U,	// SZ_V_PSEUDO
    0U,	// SZ_W_PSEUDO
    0U,	// Save16
    0U,	// SaveX16
    0U,	// SbRxRyOffMemX16
    0U,	// SebRx16
    0U,	// SehRx16
    0U,	// SelBeqZ
    0U,	// SelBneZ
    0U,	// SelTBteqZCmp
    0U,	// SelTBteqZCmpi
    0U,	// SelTBteqZSlt
    0U,	// SelTBteqZSlti
    0U,	// SelTBteqZSltiu
    0U,	// SelTBteqZSltu
    0U,	// SelTBtneZCmp
    0U,	// SelTBtneZCmpi
    0U,	// SelTBtneZSlt
    0U,	// SelTBtneZSlti
    0U,	// SelTBtneZSltiu
    0U,	// SelTBtneZSltu
    0U,	// ShRxRyOffMemX16
    0U,	// SllX16
    0U,	// SllvRxRy16
    0U,	// SltCCRxRy16
    0U,	// SltRxRy16
    0U,	// SltiCCRxImmX16
    0U,	// SltiRxImm16
    0U,	// SltiRxImmX16
    0U,	// SltiuCCRxImmX16
    0U,	// SltiuRxImm16
    0U,	// SltiuRxImmX16
    0U,	// SltuCCRxRy16
    0U,	// SltuRxRy16
    0U,	// SltuRxRyRz16
    0U,	// SraX16
    0U,	// SravRxRy16
    0U,	// SrlX16
    0U,	// SrlvRxRy16
    0U,	// SubuRxRyRz16
    0U,	// SwRxRyOffMemX16
    0U,	// SwRxSpImmX16
    0U,	// TAILCALL
    0U,	// TAILCALL64_R
    0U,	// TAILCALL_R
    0U,	// TEQ
    0U,	// TEQI
    0U,	// TEQI_MM
    0U,	// TEQ_MM
    0U,	// TGE
    0U,	// TGEI
    0U,	// TGEIU
    0U,	// TGEIU_MM
    0U,	// TGEI_MM
    0U,	// TGEU
    0U,	// TGEU_MM
    0U,	// TGE_MM
    0U,	// TLBP
    0U,	// TLBP_MM
    0U,	// TLBR
    0U,	// TLBR_MM
    0U,	// TLBWI
    0U,	// TLBWI_MM
    0U,	// TLBWR
    0U,	// TLBWR_MM
    0U,	// TLT
    0U,	// TLTI
    0U,	// TLTIU_MM
    0U,	// TLTI_MM
    0U,	// TLTU
    0U,	// TLTU_MM
    0U,	// TLT_MM
    0U,	// TNE
    0U,	// TNEI
    0U,	// TNEI_MM
    0U,	// TNE_MM
    0U,	// TRAP
    0U,	// TRUNC_L_D64
    0U,	// TRUNC_L_S
    0U,	// TRUNC_W_D32
    0U,	// TRUNC_W_D64
    0U,	// TRUNC_W_MM
    0U,	// TRUNC_W_S
    0U,	// TRUNC_W_S_MM
    0U,	// TTLTIU
    0U,	// UDIV
    0U,	// UDIV_MM
    0U,	// V3MULU
    0U,	// VMM0
    0U,	// VMULU
    0U,	// VSHF_B
    0U,	// VSHF_D
    0U,	// VSHF_H
    0U,	// VSHF_W
    0U,	// WAIT
    0U,	// WAIT_MM
    0U,	// WRDSP
    0U,	// WSBH
    0U,	// WSBH_MM
    0U,	// XOR
    0U,	// XOR64
    0U,	// XORI_B
    0U,	// XOR_MM
    0U,	// XOR_V
    0U,	// XOR_V_D_PSEUDO
    0U,	// XOR_V_H_PSEUDO
    0U,	// XOR_V_W_PSEUDO
    0U,	// XORi
    0U,	// XORi64
    0U,	// XORi_MM
    0U,	// XorRxRxRy16
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'j', 'a', 'l', 'r', 'c', 32, 9, 0,
  /* 8 */ 'd', 'm', 'f', 'c', '0', 9, 0,
  /* 15 */ 'd', 'm', 't', 'c', '0', 9, 0,
  /* 22 */ 'v', 'm', 'm', '0', 9, 0,
  /* 28 */ 'm', 't', 'm', '0', 9, 0,
  /* 34 */ 'm', 't', 'p', '0', 9, 0,
  /* 40 */ 'l', 'd', 'c', '1', 9, 0,
  /* 46 */ 's', 'd', 'c', '1', 9, 0,
  /* 52 */ 'c', 'f', 'c', '1', 9, 0,
  /* 58 */ 'd', 'm', 'f', 'c', '1', 9, 0,
  /* 65 */ 'm', 'f', 'h', 'c', '1', 9, 0,
  /* 72 */ 'm', 't', 'h', 'c', '1', 9, 0,
  /* 79 */ 'c', 't', 'c', '1', 9, 0,
  /* 85 */ 'd', 'm', 't', 'c', '1', 9, 0,
  /* 92 */ 'l', 'w', 'c', '1', 9, 0,
  /* 98 */ 's', 'w', 'c', '1', 9, 0,
  /* 104 */ 'l', 'd', 'x', 'c', '1', 9, 0,
  /* 111 */ 's', 'd', 'x', 'c', '1', 9, 0,
  /* 118 */ 'l', 'u', 'x', 'c', '1', 9, 0,
  /* 125 */ 's', 'u', 'x', 'c', '1', 9, 0,
  /* 132 */ 'l', 'w', 'x', 'c', '1', 9, 0,
  /* 139 */ 's', 'w', 'x', 'c', '1', 9, 0,
  /* 146 */ 'm', 't', 'm', '1', 9, 0,
  /* 152 */ 'm', 't', 'p', '1', 9, 0,
  /* 158 */ 'd', 's', 'r', 'a', '3', '2', 9, 0,
  /* 166 */ 'b', 'p', 'o', 's', 'g', 'e', '3', '2', 9, 0,
  /* 176 */ 'd', 's', 'l', 'l', '3', '2', 9, 0,
  /* 184 */ 'd', 's', 'r', 'l', '3', '2', 9, 0,
  /* 192 */ 'd', 'r', 'o', 't', 'r', '3', '2', 9, 0,
  /* 201 */ 'l', 'd', 'c', '2', 9, 0,
  /* 207 */ 's', 'd', 'c', '2', 9, 0,
  /* 213 */ 'd', 'm', 'f', 'c', '2', 9, 0,
  /* 220 */ 'd', 'm', 't', 'c', '2', 9, 0,
  /* 227 */ 'l', 'w', 'c', '2', 9, 0,
  /* 233 */ 's', 'w', 'c', '2', 9, 0,
  /* 239 */ 'm', 't', 'm', '2', 9, 0,
  /* 245 */ 'm', 't', 'p', '2', 9, 0,
  /* 251 */ 'l', 'd', 'c', '3', 9, 0,
  /* 257 */ 's', 'd', 'c', '3', 9, 0,
  /* 263 */ 'l', 'w', 'c', '3', 9, 0,
  /* 269 */ 's', 'w', 'c', '3', 9, 0,
  /* 275 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 'a', 9, 0,
  /* 291 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 'a', 9, 0,
  /* 308 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 'a', 9, 0,
  /* 324 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 'a', 9, 0,
  /* 341 */ 'd', 's', 'r', 'a', 9, 0,
  /* 347 */ 'd', 'l', 's', 'a', 9, 0,
  /* 353 */ 'c', 'f', 'c', 'm', 's', 'a', 9, 0,
  /* 361 */ 'c', 't', 'c', 'm', 's', 'a', 9, 0,
  /* 369 */ 'a', 'd', 'd', '_', 'a', '.', 'b', 9, 0,
  /* 378 */ 'm', 'i', 'n', '_', 'a', '.', 'b', 9, 0,
  /* 387 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'b', 9, 0,
  /* 397 */ 'm', 'a', 'x', '_', 'a', '.', 'b', 9, 0,
  /* 406 */ 's', 'r', 'a', '.', 'b', 9, 0,
  /* 413 */ 'n', 'l', 'o', 'c', '.', 'b', 9, 0,
  /* 421 */ 'n', 'l', 'z', 'c', '.', 'b', 9, 0,
  /* 429 */ 's', 'l', 'd', '.', 'b', 9, 0,
  /* 436 */ 'p', 'c', 'k', 'o', 'd', '.', 'b', 9, 0,
  /* 445 */ 'i', 'l', 'v', 'o', 'd', '.', 'b', 9, 0,
  /* 454 */ 'i', 'n', 's', 'v', 'e', '.', 'b', 9, 0,
  /* 463 */ 'v', 's', 'h', 'f', '.', 'b', 9, 0,
  /* 471 */ 'b', 'n', 'e', 'g', '.', 'b', 9, 0,
  /* 479 */ 's', 'r', 'a', 'i', '.', 'b', 9, 0,
  /* 487 */ 's', 'l', 'd', 'i', '.', 'b', 9, 0,
  /* 495 */ 'a', 'n', 'd', 'i', '.', 'b', 9, 0,
  /* 503 */ 'b', 'n', 'e', 'g', 'i', '.', 'b', 9, 0,
  /* 512 */ 'b', 's', 'e', 'l', 'i', '.', 'b', 9, 0,
  /* 521 */ 's', 'l', 'l', 'i', '.', 'b', 9, 0,
  /* 529 */ 's', 'r', 'l', 'i', '.', 'b', 9, 0,
  /* 537 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'b', 9, 0,
  /* 547 */ 'c', 'e', 'q', 'i', '.', 'b', 9, 0,
  /* 555 */ 's', 'r', 'a', 'r', 'i', '.', 'b', 9, 0,
  /* 564 */ 'b', 'c', 'l', 'r', 'i', '.', 'b', 9, 0,
  /* 573 */ 's', 'r', 'l', 'r', 'i', '.', 'b', 9, 0,
  /* 582 */ 'n', 'o', 'r', 'i', '.', 'b', 9, 0,
  /* 590 */ 'x', 'o', 'r', 'i', '.', 'b', 9, 0,
  /* 598 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'b', 9, 0,
  /* 608 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'b', 9, 0,
  /* 618 */ 'b', 's', 'e', 't', 'i', '.', 'b', 9, 0,
  /* 627 */ 's', 'u', 'b', 'v', 'i', '.', 'b', 9, 0,
  /* 636 */ 'a', 'd', 'd', 'v', 'i', '.', 'b', 9, 0,
  /* 645 */ 'b', 'm', 'z', 'i', '.', 'b', 9, 0,
  /* 653 */ 'b', 'm', 'n', 'z', 'i', '.', 'b', 9, 0,
  /* 662 */ 'f', 'i', 'l', 'l', '.', 'b', 9, 0,
  /* 670 */ 's', 'l', 'l', '.', 'b', 9, 0,
  /* 677 */ 's', 'r', 'l', '.', 'b', 9, 0,
  /* 684 */ 'b', 'i', 'n', 's', 'l', '.', 'b', 9, 0,
  /* 693 */ 'i', 'l', 'v', 'l', '.', 'b', 9, 0,
  /* 701 */ 'c', 'e', 'q', '.', 'b', 9, 0,
  /* 708 */ 's', 'r', 'a', 'r', '.', 'b', 9, 0,
  /* 716 */ 'b', 'c', 'l', 'r', '.', 'b', 9, 0,
  /* 724 */ 's', 'r', 'l', 'r', '.', 'b', 9, 0,
  /* 732 */ 'b', 'i', 'n', 's', 'r', '.', 'b', 9, 0,
  /* 741 */ 'i', 'l', 'v', 'r', '.', 'b', 9, 0,
  /* 749 */ 'a', 's', 'u', 'b', '_', 's', '.', 'b', 9, 0,
  /* 759 */ 'm', 'o', 'd', '_', 's', '.', 'b', 9, 0,
  /* 768 */ 'c', 'l', 'e', '_', 's', '.', 'b', 9, 0,
  /* 777 */ 'a', 'v', 'e', '_', 's', '.', 'b', 9, 0,
  /* 786 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'b', 9, 0,
  /* 796 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'b', 9, 0,
  /* 806 */ 'c', 'l', 't', 'i', '_', 's', '.', 'b', 9, 0,
  /* 816 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'b', 9, 0,
  /* 826 */ 'm', 'i', 'n', '_', 's', '.', 'b', 9, 0,
  /* 835 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'b', 9, 0,
  /* 845 */ 's', 'u', 'b', 's', '_', 's', '.', 'b', 9, 0,
  /* 855 */ 'a', 'd', 'd', 's', '_', 's', '.', 'b', 9, 0,
  /* 865 */ 's', 'a', 't', '_', 's', '.', 'b', 9, 0,
  /* 874 */ 'c', 'l', 't', '_', 's', '.', 'b', 9, 0,
  /* 883 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'b', 9, 0,
  /* 895 */ 'd', 'i', 'v', '_', 's', '.', 'b', 9, 0,
  /* 904 */ 'm', 'a', 'x', '_', 's', '.', 'b', 9, 0,
  /* 913 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'b', 9, 0,
  /* 923 */ 's', 'p', 'l', 'a', 't', '.', 'b', 9, 0,
  /* 932 */ 'b', 's', 'e', 't', '.', 'b', 9, 0,
  /* 940 */ 'p', 'c', 'n', 't', '.', 'b', 9, 0,
  /* 948 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'b', 9, 0,
  /* 958 */ 's', 't', '.', 'b', 9, 0,
  /* 964 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'b', 9, 0,
  /* 974 */ 'm', 'o', 'd', '_', 'u', '.', 'b', 9, 0,
  /* 983 */ 'c', 'l', 'e', '_', 'u', '.', 'b', 9, 0,
  /* 992 */ 'a', 'v', 'e', '_', 'u', '.', 'b', 9, 0,
  /* 1001 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1011 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1021 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1031 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1041 */ 'm', 'i', 'n', '_', 'u', '.', 'b', 9, 0,
  /* 1050 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'b', 9, 0,
  /* 1060 */ 's', 'u', 'b', 's', '_', 'u', '.', 'b', 9, 0,
  /* 1070 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'b', 9, 0,
  /* 1080 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'b', 9, 0,
  /* 1092 */ 's', 'a', 't', '_', 'u', '.', 'b', 9, 0,
  /* 1101 */ 'c', 'l', 't', '_', 'u', '.', 'b', 9, 0,
  /* 1110 */ 'd', 'i', 'v', '_', 'u', '.', 'b', 9, 0,
  /* 1119 */ 'm', 'a', 'x', '_', 'u', '.', 'b', 9, 0,
  /* 1128 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'b', 9, 0,
  /* 1138 */ 'm', 's', 'u', 'b', 'v', '.', 'b', 9, 0,
  /* 1147 */ 'm', 'a', 'd', 'd', 'v', '.', 'b', 9, 0,
  /* 1156 */ 'p', 'c', 'k', 'e', 'v', '.', 'b', 9, 0,
  /* 1165 */ 'i', 'l', 'v', 'e', 'v', '.', 'b', 9, 0,
  /* 1174 */ 'm', 'u', 'l', 'v', '.', 'b', 9, 0,
  /* 1182 */ 'b', 'z', '.', 'b', 9, 0,
  /* 1188 */ 'b', 'n', 'z', '.', 'b', 9, 0,
  /* 1195 */ 's', 'e', 'b', 9, 0,
  /* 1200 */ 'j', 'r', '.', 'h', 'b', 9, 0,
  /* 1207 */ 'j', 'a', 'l', 'r', '.', 'h', 'b', 9, 0,
  /* 1216 */ 'l', 'b', 9, 0,
  /* 1220 */ 's', 'h', 'r', 'a', '.', 'q', 'b', 9, 0,
  /* 1229 */ 'c', 'm', 'p', 'g', 'd', 'u', '.', 'l', 'e', '.', 'q', 'b', 9, 0,
  /* 1243 */ 'c', 'm', 'p', 'g', 'u', '.', 'l', 'e', '.', 'q', 'b', 9, 0,
  /* 1256 */ 'c', 'm', 'p', 'u', '.', 'l', 'e', '.', 'q', 'b', 9, 0,
  /* 1268 */ 's', 'u', 'b', 'u', 'h', '.', 'q', 'b', 9, 0,
  /* 1278 */ 'a', 'd', 'd', 'u', 'h', '.', 'q', 'b', 9, 0,
  /* 1288 */ 'p', 'i', 'c', 'k', '.', 'q', 'b', 9, 0,
  /* 1297 */ 's', 'h', 'l', 'l', '.', 'q', 'b', 9, 0,
  /* 1306 */ 'r', 'e', 'p', 'l', '.', 'q', 'b', 9, 0,
  /* 1315 */ 's', 'h', 'r', 'l', '.', 'q', 'b', 9, 0,
  /* 1324 */ 'c', 'm', 'p', 'g', 'd', 'u', '.', 'e', 'q', '.', 'q', 'b', 9, 0,
  /* 1338 */ 'c', 'm', 'p', 'g', 'u', '.', 'e', 'q', '.', 'q', 'b', 9, 0,
  /* 1351 */ 'c', 'm', 'p', 'u', '.', 'e', 'q', '.', 'q', 'b', 9, 0,
  /* 1363 */ 's', 'h', 'r', 'a', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1374 */ 's', 'u', 'b', 'u', 'h', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1386 */ 'a', 'd', 'd', 'u', 'h', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1398 */ 's', 'h', 'r', 'a', 'v', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1410 */ 'a', 'b', 's', 'q', '_', 's', '.', 'q', 'b', 9, 0,
  /* 1421 */ 's', 'u', 'b', 'u', '_', 's', '.', 'q', 'b', 9, 0,
  /* 1432 */ 'a', 'd', 'd', 'u', '_', 's', '.', 'q', 'b', 9, 0,
  /* 1443 */ 'c', 'm', 'p', 'g', 'd', 'u', '.', 'l', 't', '.', 'q', 'b', 9, 0,
  /* 1457 */ 'c', 'm', 'p', 'g', 'u', '.', 'l', 't', '.', 'q', 'b', 9, 0,
  /* 1470 */ 'c', 'm', 'p', 'u', '.', 'l', 't', '.', 'q', 'b', 9, 0,
  /* 1482 */ 's', 'u', 'b', 'u', '.', 'q', 'b', 9, 0,
  /* 1491 */ 'a', 'd', 'd', 'u', '.', 'q', 'b', 9, 0,
  /* 1500 */ 's', 'h', 'r', 'a', 'v', '.', 'q', 'b', 9, 0,
  /* 1510 */ 's', 'h', 'l', 'l', 'v', '.', 'q', 'b', 9, 0,
  /* 1520 */ 'r', 'e', 'p', 'l', 'v', '.', 'q', 'b', 9, 0,
  /* 1530 */ 's', 'h', 'r', 'l', 'v', '.', 'q', 'b', 9, 0,
  /* 1540 */ 'r', 'a', 'd', 'd', 'u', '.', 'w', '.', 'q', 'b', 9, 0,
  /* 1552 */ 's', 'b', 9, 0,
  /* 1556 */ 'm', 'o', 'd', 's', 'u', 'b', 9, 0,
  /* 1564 */ 'm', 's', 'u', 'b', 9, 0,
  /* 1570 */ 'b', 'c', 9, 0,
  /* 1574 */ 'b', 'g', 'e', 'c', 9, 0,
  /* 1580 */ 'b', 'n', 'e', 'c', 9, 0,
  /* 1586 */ 'j', 'i', 'c', 9, 0,
  /* 1591 */ 'b', 'a', 'l', 'c', 9, 0,
  /* 1597 */ 'j', 'i', 'a', 'l', 'c', 9, 0,
  /* 1604 */ 'b', 'g', 'e', 'z', 'a', 'l', 'c', 9, 0,
  /* 1613 */ 'b', 'l', 'e', 'z', 'a', 'l', 'c', 9, 0,
  /* 1622 */ 'b', 'n', 'e', 'z', 'a', 'l', 'c', 9, 0,
  /* 1631 */ 'b', 'e', 'q', 'z', 'a', 'l', 'c', 9, 0,
  /* 1640 */ 'b', 'g', 't', 'z', 'a', 'l', 'c', 9, 0,
  /* 1649 */ 'b', 'l', 't', 'z', 'a', 'l', 'c', 9, 0,
  /* 1658 */ 'l', 'd', 'p', 'c', 9, 0,
  /* 1664 */ 'a', 'u', 'i', 'p', 'c', 9, 0,
  /* 1671 */ 'a', 'l', 'u', 'i', 'p', 'c', 9, 0,
  /* 1679 */ 'a', 'd', 'd', 'i', 'u', 'p', 'c', 9, 0,
  /* 1688 */ 'l', 'w', 'u', 'p', 'c', 9, 0,
  /* 1695 */ 'l', 'w', 'p', 'c', 9, 0,
  /* 1701 */ 'b', 'e', 'q', 'c', 9, 0,
  /* 1707 */ 'a', 'd', 'd', 's', 'c', 9, 0,
  /* 1714 */ 'b', 'l', 't', 'c', 9, 0,
  /* 1720 */ 'b', 'g', 'e', 'u', 'c', 9, 0,
  /* 1727 */ 'b', 'l', 't', 'u', 'c', 9, 0,
  /* 1734 */ 'b', 'n', 'v', 'c', 9, 0,
  /* 1740 */ 'b', 'o', 'v', 'c', 9, 0,
  /* 1746 */ 'a', 'd', 'd', 'w', 'c', 9, 0,
  /* 1753 */ 'b', 'g', 'e', 'z', 'c', 9, 0,
  /* 1760 */ 'b', 'l', 'e', 'z', 'c', 9, 0,
  /* 1767 */ 'b', 'n', 'e', 'z', 'c', 9, 0,
  /* 1774 */ 'b', 'e', 'q', 'z', 'c', 9, 0,
  /* 1781 */ 'b', 'g', 't', 'z', 'c', 9, 0,
  /* 1788 */ 'b', 'l', 't', 'z', 'c', 9, 0,
  /* 1795 */ 'f', 'l', 'o', 'g', '2', '.', 'd', 9, 0,
  /* 1804 */ 'f', 'e', 'x', 'p', '2', '.', 'd', 9, 0,
  /* 1813 */ 'a', 'd', 'd', '_', 'a', '.', 'd', 9, 0,
  /* 1822 */ 'f', 'm', 'i', 'n', '_', 'a', '.', 'd', 9, 0,
  /* 1832 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'd', 9, 0,
  /* 1842 */ 'f', 'm', 'a', 'x', '_', 'a', '.', 'd', 9, 0,
  /* 1852 */ 'm', 'i', 'n', 'a', '.', 'd', 9, 0,
  /* 1860 */ 's', 'r', 'a', '.', 'd', 9, 0,
  /* 1867 */ 'm', 'a', 'x', 'a', '.', 'd', 9, 0,
  /* 1875 */ 'f', 's', 'u', 'b', '.', 'd', 9, 0,
  /* 1883 */ 'f', 'm', 's', 'u', 'b', '.', 'd', 9, 0,
  /* 1892 */ 'n', 'm', 's', 'u', 'b', '.', 'd', 9, 0,
  /* 1901 */ 'n', 'l', 'o', 'c', '.', 'd', 9, 0,
  /* 1909 */ 'n', 'l', 'z', 'c', '.', 'd', 9, 0,
  /* 1917 */ 'f', 'a', 'd', 'd', '.', 'd', 9, 0,
  /* 1925 */ 'f', 'm', 'a', 'd', 'd', '.', 'd', 9, 0,
  /* 1934 */ 'n', 'm', 'a', 'd', 'd', '.', 'd', 9, 0,
  /* 1943 */ 's', 'l', 'd', '.', 'd', 9, 0,
  /* 1950 */ 'p', 'c', 'k', 'o', 'd', '.', 'd', 9, 0,
  /* 1959 */ 'i', 'l', 'v', 'o', 'd', '.', 'd', 9, 0,
  /* 1968 */ 'c', '.', 'n', 'g', 'e', '.', 'd', 9, 0,
  /* 1977 */ 'c', '.', 'l', 'e', '.', 'd', 9, 0,
  /* 1985 */ 'c', 'm', 'p', '.', 'l', 'e', '.', 'd', 9, 0,
  /* 1995 */ 'f', 'c', 'l', 'e', '.', 'd', 9, 0,
  /* 2003 */ 'c', '.', 'n', 'g', 'l', 'e', '.', 'd', 9, 0,
  /* 2013 */ 'c', '.', 'o', 'l', 'e', '.', 'd', 9, 0,
  /* 2022 */ 'c', 'm', 'p', '.', 's', 'l', 'e', '.', 'd', 9, 0,
  /* 2033 */ 'f', 's', 'l', 'e', '.', 'd', 9, 0,
  /* 2041 */ 'c', '.', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2050 */ 'c', 'm', 'p', '.', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2061 */ 'f', 'c', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2070 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2082 */ 'f', 's', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2091 */ 'f', 'c', 'n', 'e', '.', 'd', 9, 0,
  /* 2099 */ 'f', 's', 'n', 'e', '.', 'd', 9, 0,
  /* 2107 */ 'f', 'c', 'u', 'n', 'e', '.', 'd', 9, 0,
  /* 2116 */ 'f', 's', 'u', 'n', 'e', '.', 'd', 9, 0,
  /* 2125 */ 'i', 'n', 's', 'v', 'e', '.', 'd', 9, 0,
  /* 2134 */ 'c', '.', 'f', '.', 'd', 9, 0,
  /* 2141 */ 'c', 'm', 'p', '.', 'a', 'f', '.', 'd', 9, 0,
  /* 2151 */ 'f', 'c', 'a', 'f', '.', 'd', 9, 0,
  /* 2159 */ 'c', 'm', 'p', '.', 's', 'a', 'f', '.', 'd', 9, 0,
  /* 2170 */ 'f', 's', 'a', 'f', '.', 'd', 9, 0,
  /* 2178 */ 'm', 's', 'u', 'b', 'f', '.', 'd', 9, 0,
  /* 2187 */ 'm', 'a', 'd', 'd', 'f', '.', 'd', 9, 0,
  /* 2196 */ 'v', 's', 'h', 'f', '.', 'd', 9, 0,
  /* 2204 */ 'c', '.', 's', 'f', '.', 'd', 9, 0,
  /* 2212 */ 'm', 'o', 'v', 'f', '.', 'd', 9, 0,
  /* 2220 */ 'b', 'n', 'e', 'g', '.', 'd', 9, 0,
  /* 2228 */ 's', 'r', 'a', 'i', '.', 'd', 9, 0,
  /* 2236 */ 's', 'l', 'd', 'i', '.', 'd', 9, 0,
  /* 2244 */ 'b', 'n', 'e', 'g', 'i', '.', 'd', 9, 0,
  /* 2253 */ 's', 'l', 'l', 'i', '.', 'd', 9, 0,
  /* 2261 */ 's', 'r', 'l', 'i', '.', 'd', 9, 0,
  /* 2269 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'd', 9, 0,
  /* 2279 */ 'c', 'e', 'q', 'i', '.', 'd', 9, 0,
  /* 2287 */ 's', 'r', 'a', 'r', 'i', '.', 'd', 9, 0,
  /* 2296 */ 'b', 'c', 'l', 'r', 'i', '.', 'd', 9, 0,
  /* 2305 */ 's', 'r', 'l', 'r', 'i', '.', 'd', 9, 0,
  /* 2314 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'd', 9, 0,
  /* 2324 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'd', 9, 0,
  /* 2334 */ 'b', 's', 'e', 't', 'i', '.', 'd', 9, 0,
  /* 2343 */ 's', 'u', 'b', 'v', 'i', '.', 'd', 9, 0,
  /* 2352 */ 'a', 'd', 'd', 'v', 'i', '.', 'd', 9, 0,
  /* 2361 */ 't', 'r', 'u', 'n', 'c', '.', 'l', '.', 'd', 9, 0,
  /* 2372 */ 'r', 'o', 'u', 'n', 'd', '.', 'l', '.', 'd', 9, 0,
  /* 2383 */ 'c', 'e', 'i', 'l', '.', 'l', '.', 'd', 9, 0,
  /* 2393 */ 'f', 'l', 'o', 'o', 'r', '.', 'l', '.', 'd', 9, 0,
  /* 2404 */ 'c', 'v', 't', '.', 'l', '.', 'd', 9, 0,
  /* 2413 */ 's', 'e', 'l', '.', 'd', 9, 0,
  /* 2420 */ 'c', '.', 'n', 'g', 'l', '.', 'd', 9, 0,
  /* 2429 */ 'f', 'i', 'l', 'l', '.', 'd', 9, 0,
  /* 2437 */ 's', 'l', 'l', '.', 'd', 9, 0,
  /* 2444 */ 'f', 'e', 'x', 'u', 'p', 'l', '.', 'd', 9, 0,
  /* 2454 */ 'f', 'f', 'q', 'l', '.', 'd', 9, 0,
  /* 2462 */ 's', 'r', 'l', '.', 'd', 9, 0,
  /* 2469 */ 'b', 'i', 'n', 's', 'l', '.', 'd', 9, 0,
  /* 2478 */ 'f', 'm', 'u', 'l', '.', 'd', 9, 0,
  /* 2486 */ 'i', 'l', 'v', 'l', '.', 'd', 9, 0,
  /* 2494 */ 'f', 'm', 'i', 'n', '.', 'd', 9, 0,
  /* 2502 */ 'c', '.', 'u', 'n', '.', 'd', 9, 0,
  /* 2510 */ 'c', 'm', 'p', '.', 'u', 'n', '.', 'd', 9, 0,
  /* 2520 */ 'f', 'c', 'u', 'n', '.', 'd', 9, 0,
  /* 2528 */ 'c', 'm', 'p', '.', 's', 'u', 'n', '.', 'd', 9, 0,
  /* 2539 */ 'f', 's', 'u', 'n', '.', 'd', 9, 0,
  /* 2547 */ 'm', 'o', 'v', 'n', '.', 'd', 9, 0,
  /* 2555 */ 'f', 'r', 'c', 'p', '.', 'd', 9, 0,
  /* 2563 */ 'c', '.', 'e', 'q', '.', 'd', 9, 0,
  /* 2571 */ 'c', 'm', 'p', '.', 'e', 'q', '.', 'd', 9, 0,
  /* 2581 */ 'f', 'c', 'e', 'q', '.', 'd', 9, 0,
  /* 2589 */ 'c', '.', 's', 'e', 'q', '.', 'd', 9, 0,
  /* 2598 */ 'c', 'm', 'p', '.', 's', 'e', 'q', '.', 'd', 9, 0,
  /* 2609 */ 'f', 's', 'e', 'q', '.', 'd', 9, 0,
  /* 2617 */ 'c', '.', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2626 */ 'c', 'm', 'p', '.', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2637 */ 'f', 'c', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2646 */ 'c', 'm', 'p', '.', 's', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2658 */ 'f', 's', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2667 */ 's', 'r', 'a', 'r', '.', 'd', 9, 0,
  /* 2675 */ 'b', 'c', 'l', 'r', '.', 'd', 9, 0,
  /* 2683 */ 's', 'r', 'l', 'r', '.', 'd', 9, 0,
  /* 2691 */ 'f', 'c', 'o', 'r', '.', 'd', 9, 0,
  /* 2699 */ 'f', 's', 'o', 'r', '.', 'd', 9, 0,
  /* 2707 */ 'f', 'e', 'x', 'u', 'p', 'r', '.', 'd', 9, 0,
  /* 2717 */ 'f', 'f', 'q', 'r', '.', 'd', 9, 0,
  /* 2725 */ 'b', 'i', 'n', 's', 'r', '.', 'd', 9, 0,
  /* 2734 */ 'i', 'l', 'v', 'r', '.', 'd', 9, 0,
  /* 2742 */ 'c', 'v', 't', '.', 's', '.', 'd', 9, 0,
  /* 2751 */ 'a', 's', 'u', 'b', '_', 's', '.', 'd', 9, 0,
  /* 2761 */ 'h', 's', 'u', 'b', '_', 's', '.', 'd', 9, 0,
  /* 2771 */ 'd', 'p', 's', 'u', 'b', '_', 's', '.', 'd', 9, 0,
  /* 2782 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 's', '.', 'd', 9, 0,
  /* 2794 */ 'h', 'a', 'd', 'd', '_', 's', '.', 'd', 9, 0,
  /* 2804 */ 'd', 'p', 'a', 'd', 'd', '_', 's', '.', 'd', 9, 0,
  /* 2815 */ 'm', 'o', 'd', '_', 's', '.', 'd', 9, 0,
  /* 2824 */ 'c', 'l', 'e', '_', 's', '.', 'd', 9, 0,
  /* 2833 */ 'a', 'v', 'e', '_', 's', '.', 'd', 9, 0,
  /* 2842 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'd', 9, 0,
  /* 2852 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'd', 9, 0,
  /* 2862 */ 'c', 'l', 't', 'i', '_', 's', '.', 'd', 9, 0,
  /* 2872 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'd', 9, 0,
  /* 2882 */ 'm', 'i', 'n', '_', 's', '.', 'd', 9, 0,
  /* 2891 */ 'd', 'o', 't', 'p', '_', 's', '.', 'd', 9, 0,
  /* 2901 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'd', 9, 0,
  /* 2911 */ 's', 'u', 'b', 's', '_', 's', '.', 'd', 9, 0,
  /* 2921 */ 'a', 'd', 'd', 's', '_', 's', '.', 'd', 9, 0,
  /* 2931 */ 's', 'a', 't', '_', 's', '.', 'd', 9, 0,
  /* 2940 */ 'c', 'l', 't', '_', 's', '.', 'd', 9, 0,
  /* 2949 */ 'f', 'f', 'i', 'n', 't', '_', 's', '.', 'd', 9, 0,
  /* 2960 */ 'f', 't', 'i', 'n', 't', '_', 's', '.', 'd', 9, 0,
  /* 2971 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'd', 9, 0,
  /* 2983 */ 'd', 'i', 'v', '_', 's', '.', 'd', 9, 0,
  /* 2992 */ 'm', 'a', 'x', '_', 's', '.', 'd', 9, 0,
  /* 3001 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'd', 9, 0,
  /* 3011 */ 'a', 'b', 's', '.', 'd', 9, 0,
  /* 3018 */ 'f', 'c', 'l', 'a', 's', 's', '.', 'd', 9, 0,
  /* 3028 */ 's', 'p', 'l', 'a', 't', '.', 'd', 9, 0,
  /* 3037 */ 'b', 's', 'e', 't', '.', 'd', 9, 0,
  /* 3045 */ 'c', '.', 'n', 'g', 't', '.', 'd', 9, 0,
  /* 3054 */ 'c', '.', 'l', 't', '.', 'd', 9, 0,
  /* 3062 */ 'c', 'm', 'p', '.', 'l', 't', '.', 'd', 9, 0,
  /* 3072 */ 'f', 'c', 'l', 't', '.', 'd', 9, 0,
  /* 3080 */ 'c', '.', 'o', 'l', 't', '.', 'd', 9, 0,
  /* 3089 */ 'c', 'm', 'p', '.', 's', 'l', 't', '.', 'd', 9, 0,
  /* 3100 */ 'f', 's', 'l', 't', '.', 'd', 9, 0,
  /* 3108 */ 'c', '.', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3117 */ 'c', 'm', 'p', '.', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3128 */ 'f', 'c', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3137 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3149 */ 'f', 's', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3158 */ 'p', 'c', 'n', 't', '.', 'd', 9, 0,
  /* 3166 */ 'f', 'r', 'i', 'n', 't', '.', 'd', 9, 0,
  /* 3175 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'd', 9, 0,
  /* 3185 */ 'f', 's', 'q', 'r', 't', '.', 'd', 9, 0,
  /* 3194 */ 'f', 'r', 's', 'q', 'r', 't', '.', 'd', 9, 0,
  /* 3204 */ 's', 't', '.', 'd', 9, 0,
  /* 3210 */ 'm', 'o', 'v', 't', '.', 'd', 9, 0,
  /* 3218 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'd', 9, 0,
  /* 3228 */ 'h', 's', 'u', 'b', '_', 'u', '.', 'd', 9, 0,
  /* 3238 */ 'd', 'p', 's', 'u', 'b', '_', 'u', '.', 'd', 9, 0,
  /* 3249 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 'u', '.', 'd', 9, 0,
  /* 3261 */ 'h', 'a', 'd', 'd', '_', 'u', '.', 'd', 9, 0,
  /* 3271 */ 'd', 'p', 'a', 'd', 'd', '_', 'u', '.', 'd', 9, 0,
  /* 3282 */ 'm', 'o', 'd', '_', 'u', '.', 'd', 9, 0,
  /* 3291 */ 'c', 'l', 'e', '_', 'u', '.', 'd', 9, 0,
  /* 3300 */ 'a', 'v', 'e', '_', 'u', '.', 'd', 9, 0,
  /* 3309 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3319 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3329 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3339 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3349 */ 'm', 'i', 'n', '_', 'u', '.', 'd', 9, 0,
  /* 3358 */ 'd', 'o', 't', 'p', '_', 'u', '.', 'd', 9, 0,
  /* 3368 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'd', 9, 0,
  /* 3378 */ 's', 'u', 'b', 's', '_', 'u', '.', 'd', 9, 0,
  /* 3388 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'd', 9, 0,
  /* 3398 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'd', 9, 0,
  /* 3410 */ 's', 'a', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3419 */ 'c', 'l', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3428 */ 'f', 'f', 'i', 'n', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3439 */ 'f', 't', 'i', 'n', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3450 */ 'd', 'i', 'v', '_', 'u', '.', 'd', 9, 0,
  /* 3459 */ 'm', 'a', 'x', '_', 'u', '.', 'd', 9, 0,
  /* 3468 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'd', 9, 0,
  /* 3478 */ 'm', 's', 'u', 'b', 'v', '.', 'd', 9, 0,
  /* 3487 */ 'm', 'a', 'd', 'd', 'v', '.', 'd', 9, 0,
  /* 3496 */ 'p', 'c', 'k', 'e', 'v', '.', 'd', 9, 0,
  /* 3505 */ 'i', 'l', 'v', 'e', 'v', '.', 'd', 9, 0,
  /* 3514 */ 'f', 'd', 'i', 'v', '.', 'd', 9, 0,
  /* 3522 */ 'm', 'u', 'l', 'v', '.', 'd', 9, 0,
  /* 3530 */ 'm', 'o', 'v', '.', 'd', 9, 0,
  /* 3537 */ 't', 'r', 'u', 'n', 'c', '.', 'w', '.', 'd', 9, 0,
  /* 3548 */ 'r', 'o', 'u', 'n', 'd', '.', 'w', '.', 'd', 9, 0,
  /* 3559 */ 'c', 'e', 'i', 'l', '.', 'w', '.', 'd', 9, 0,
  /* 3569 */ 'f', 'l', 'o', 'o', 'r', '.', 'w', '.', 'd', 9, 0,
  /* 3580 */ 'c', 'v', 't', '.', 'w', '.', 'd', 9, 0,
  /* 3589 */ 'f', 'm', 'a', 'x', '.', 'd', 9, 0,
  /* 3597 */ 'b', 'z', '.', 'd', 9, 0,
  /* 3603 */ 's', 'e', 'l', 'n', 'e', 'z', '.', 'd', 9, 0,
  /* 3613 */ 'b', 'n', 'z', '.', 'd', 9, 0,
  /* 3620 */ 's', 'e', 'l', 'e', 'q', 'z', '.', 'd', 9, 0,
  /* 3630 */ 'm', 'o', 'v', 'z', '.', 'd', 9, 0,
  /* 3638 */ 's', 'c', 'd', 9, 0,
  /* 3643 */ 'd', 'a', 'd', 'd', 9, 0,
  /* 3649 */ 'm', 'a', 'd', 'd', 9, 0,
  /* 3655 */ 'd', 's', 'h', 'd', 9, 0,
  /* 3661 */ 'l', 'l', 'd', 9, 0,
  /* 3666 */ 'a', 'n', 'd', 9, 0,
  /* 3671 */ 'p', 'r', 'e', 'p', 'e', 'n', 'd', 9, 0,
  /* 3680 */ 'a', 'p', 'p', 'e', 'n', 'd', 9, 0,
  /* 3688 */ 'd', 'm', 'o', 'd', 9, 0,
  /* 3694 */ 's', 'd', 9, 0,
  /* 3698 */ 't', 'g', 'e', 9, 0,
  /* 3703 */ 'c', 'a', 'c', 'h', 'e', 9, 0,
  /* 3710 */ 'b', 'n', 'e', 9, 0,
  /* 3715 */ 's', 'n', 'e', 9, 0,
  /* 3720 */ 't', 'n', 'e', 9, 0,
  /* 3725 */ 'm', 'o', 'v', 'e', 9, 0,
  /* 3731 */ 'b', 'c', '0', 'f', 9, 0,
  /* 3737 */ 'b', 'c', '1', 'f', 9, 0,
  /* 3743 */ 'b', 'c', '2', 'f', 9, 0,
  /* 3749 */ 'b', 'c', '3', 'f', 9, 0,
  /* 3755 */ 'p', 'r', 'e', 'f', 9, 0,
  /* 3761 */ 'm', 'o', 'v', 'f', 9, 0,
  /* 3767 */ 'n', 'e', 'g', 9, 0,
  /* 3772 */ 'a', 'd', 'd', '_', 'a', '.', 'h', 9, 0,
  /* 3781 */ 'm', 'i', 'n', '_', 'a', '.', 'h', 9, 0,
  /* 3790 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'h', 9, 0,
  /* 3800 */ 'm', 'a', 'x', '_', 'a', '.', 'h', 9, 0,
  /* 3809 */ 's', 'r', 'a', '.', 'h', 9, 0,
  /* 3816 */ 'n', 'l', 'o', 'c', '.', 'h', 9, 0,
  /* 3824 */ 'n', 'l', 'z', 'c', '.', 'h', 9, 0,
  /* 3832 */ 's', 'l', 'd', '.', 'h', 9, 0,
  /* 3839 */ 'p', 'c', 'k', 'o', 'd', '.', 'h', 9, 0,
  /* 3848 */ 'i', 'l', 'v', 'o', 'd', '.', 'h', 9, 0,
  /* 3857 */ 'i', 'n', 's', 'v', 'e', '.', 'h', 9, 0,
  /* 3866 */ 'v', 's', 'h', 'f', '.', 'h', 9, 0,
  /* 3874 */ 'b', 'n', 'e', 'g', '.', 'h', 9, 0,
  /* 3882 */ 's', 'r', 'a', 'i', '.', 'h', 9, 0,
  /* 3890 */ 's', 'l', 'd', 'i', '.', 'h', 9, 0,
  /* 3898 */ 'b', 'n', 'e', 'g', 'i', '.', 'h', 9, 0,
  /* 3907 */ 's', 'l', 'l', 'i', '.', 'h', 9, 0,
  /* 3915 */ 's', 'r', 'l', 'i', '.', 'h', 9, 0,
  /* 3923 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'h', 9, 0,
  /* 3933 */ 'c', 'e', 'q', 'i', '.', 'h', 9, 0,
  /* 3941 */ 's', 'r', 'a', 'r', 'i', '.', 'h', 9, 0,
  /* 3950 */ 'b', 'c', 'l', 'r', 'i', '.', 'h', 9, 0,
  /* 3959 */ 's', 'r', 'l', 'r', 'i', '.', 'h', 9, 0,
  /* 3968 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'h', 9, 0,
  /* 3978 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'h', 9, 0,
  /* 3988 */ 'b', 's', 'e', 't', 'i', '.', 'h', 9, 0,
  /* 3997 */ 's', 'u', 'b', 'v', 'i', '.', 'h', 9, 0,
  /* 4006 */ 'a', 'd', 'd', 'v', 'i', '.', 'h', 9, 0,
  /* 4015 */ 'f', 'i', 'l', 'l', '.', 'h', 9, 0,
  /* 4023 */ 's', 'l', 'l', '.', 'h', 9, 0,
  /* 4030 */ 's', 'r', 'l', '.', 'h', 9, 0,
  /* 4037 */ 'b', 'i', 'n', 's', 'l', '.', 'h', 9, 0,
  /* 4046 */ 'i', 'l', 'v', 'l', '.', 'h', 9, 0,
  /* 4054 */ 'f', 'e', 'x', 'd', 'o', '.', 'h', 9, 0,
  /* 4063 */ 'm', 's', 'u', 'b', '_', 'q', '.', 'h', 9, 0,
  /* 4073 */ 'm', 'a', 'd', 'd', '_', 'q', '.', 'h', 9, 0,
  /* 4083 */ 'm', 'u', 'l', '_', 'q', '.', 'h', 9, 0,
  /* 4092 */ 'm', 's', 'u', 'b', 'r', '_', 'q', '.', 'h', 9, 0,
  /* 4103 */ 'm', 'a', 'd', 'd', 'r', '_', 'q', '.', 'h', 9, 0,
  /* 4114 */ 'm', 'u', 'l', 'r', '_', 'q', '.', 'h', 9, 0,
  /* 4124 */ 'c', 'e', 'q', '.', 'h', 9, 0,
  /* 4131 */ 'f', 't', 'q', '.', 'h', 9, 0,
  /* 4138 */ 's', 'r', 'a', 'r', '.', 'h', 9, 0,
  /* 4146 */ 'b', 'c', 'l', 'r', '.', 'h', 9, 0,
  /* 4154 */ 's', 'r', 'l', 'r', '.', 'h', 9, 0,
  /* 4162 */ 'b', 'i', 'n', 's', 'r', '.', 'h', 9, 0,
  /* 4171 */ 'i', 'l', 'v', 'r', '.', 'h', 9, 0,
  /* 4179 */ 'a', 's', 'u', 'b', '_', 's', '.', 'h', 9, 0,
  /* 4189 */ 'h', 's', 'u', 'b', '_', 's', '.', 'h', 9, 0,
  /* 4199 */ 'd', 'p', 's', 'u', 'b', '_', 's', '.', 'h', 9, 0,
  /* 4210 */ 'h', 'a', 'd', 'd', '_', 's', '.', 'h', 9, 0,
  /* 4220 */ 'd', 'p', 'a', 'd', 'd', '_', 's', '.', 'h', 9, 0,
  /* 4231 */ 'm', 'o', 'd', '_', 's', '.', 'h', 9, 0,
  /* 4240 */ 'c', 'l', 'e', '_', 's', '.', 'h', 9, 0,
  /* 4249 */ 'a', 'v', 'e', '_', 's', '.', 'h', 9, 0,
  /* 4258 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4268 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4278 */ 'c', 'l', 't', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4288 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4298 */ 'm', 'i', 'n', '_', 's', '.', 'h', 9, 0,
  /* 4307 */ 'd', 'o', 't', 'p', '_', 's', '.', 'h', 9, 0,
  /* 4317 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'h', 9, 0,
  /* 4327 */ 'e', 'x', 't', 'r', '_', 's', '.', 'h', 9, 0,
  /* 4337 */ 's', 'u', 'b', 's', '_', 's', '.', 'h', 9, 0,
  /* 4347 */ 'a', 'd', 'd', 's', '_', 's', '.', 'h', 9, 0,
  /* 4357 */ 's', 'a', 't', '_', 's', '.', 'h', 9, 0,
  /* 4366 */ 'c', 'l', 't', '_', 's', '.', 'h', 9, 0,
  /* 4375 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'h', 9, 0,
  /* 4387 */ 'd', 'i', 'v', '_', 's', '.', 'h', 9, 0,
  /* 4396 */ 'e', 'x', 't', 'r', 'v', '_', 's', '.', 'h', 9, 0,
  /* 4407 */ 'm', 'a', 'x', '_', 's', '.', 'h', 9, 0,
  /* 4416 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'h', 9, 0,
  /* 4426 */ 's', 'p', 'l', 'a', 't', '.', 'h', 9, 0,
  /* 4435 */ 'b', 's', 'e', 't', '.', 'h', 9, 0,
  /* 4443 */ 'p', 'c', 'n', 't', '.', 'h', 9, 0,
  /* 4451 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'h', 9, 0,
  /* 4461 */ 's', 't', '.', 'h', 9, 0,
  /* 4467 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'h', 9, 0,
  /* 4477 */ 'h', 's', 'u', 'b', '_', 'u', '.', 'h', 9, 0,
  /* 4487 */ 'd', 'p', 's', 'u', 'b', '_', 'u', '.', 'h', 9, 0,
  /* 4498 */ 'h', 'a', 'd', 'd', '_', 'u', '.', 'h', 9, 0,
  /* 4508 */ 'd', 'p', 'a', 'd', 'd', '_', 'u', '.', 'h', 9, 0,
  /* 4519 */ 'm', 'o', 'd', '_', 'u', '.', 'h', 9, 0,
  /* 4528 */ 'c', 'l', 'e', '_', 'u', '.', 'h', 9, 0,
  /* 4537 */ 'a', 'v', 'e', '_', 'u', '.', 'h', 9, 0,
  /* 4546 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4556 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4566 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4576 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4586 */ 'm', 'i', 'n', '_', 'u', '.', 'h', 9, 0,
  /* 4595 */ 'd', 'o', 't', 'p', '_', 'u', '.', 'h', 9, 0,
  /* 4605 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'h', 9, 0,
  /* 4615 */ 's', 'u', 'b', 's', '_', 'u', '.', 'h', 9, 0,
  /* 4625 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'h', 9, 0,
  /* 4635 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'h', 9, 0,
  /* 4647 */ 's', 'a', 't', '_', 'u', '.', 'h', 9, 0,
  /* 4656 */ 'c', 'l', 't', '_', 'u', '.', 'h', 9, 0,
  /* 4665 */ 'd', 'i', 'v', '_', 'u', '.', 'h', 9, 0,
  /* 4674 */ 'm', 'a', 'x', '_', 'u', '.', 'h', 9, 0,
  /* 4683 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'h', 9, 0,
  /* 4693 */ 'm', 's', 'u', 'b', 'v', '.', 'h', 9, 0,
  /* 4702 */ 'm', 'a', 'd', 'd', 'v', '.', 'h', 9, 0,
  /* 4711 */ 'p', 'c', 'k', 'e', 'v', '.', 'h', 9, 0,
  /* 4720 */ 'i', 'l', 'v', 'e', 'v', '.', 'h', 9, 0,
  /* 4729 */ 'm', 'u', 'l', 'v', '.', 'h', 9, 0,
  /* 4737 */ 'b', 'z', '.', 'h', 9, 0,
  /* 4743 */ 'b', 'n', 'z', '.', 'h', 9, 0,
  /* 4750 */ 'd', 's', 'b', 'h', 9, 0,
  /* 4756 */ 'w', 's', 'b', 'h', 9, 0,
  /* 4762 */ 's', 'e', 'h', 9, 0,
  /* 4767 */ 'l', 'h', 9, 0,
  /* 4771 */ 's', 'h', 'r', 'a', '.', 'p', 'h', 9, 0,
  /* 4780 */ 'p', 'r', 'e', 'c', 'r', 'q', '.', 'q', 'b', '.', 'p', 'h', 9, 0,
  /* 4794 */ 'p', 'r', 'e', 'c', 'r', '.', 'q', 'b', '.', 'p', 'h', 9, 0,
  /* 4807 */ 'p', 'r', 'e', 'c', 'r', 'q', 'u', '_', 's', '.', 'q', 'b', '.', 'p', 'h', 9, 0,
  /* 4824 */ 'c', 'm', 'p', '.', 'l', 'e', '.', 'p', 'h', 9, 0,
  /* 4835 */ 's', 'u', 'b', 'q', 'h', '.', 'p', 'h', 9, 0,
  /* 4845 */ 'a', 'd', 'd', 'q', 'h', '.', 'p', 'h', 9, 0,
  /* 4855 */ 'p', 'i', 'c', 'k', '.', 'p', 'h', 9, 0,
  /* 4864 */ 's', 'h', 'l', 'l', '.', 'p', 'h', 9, 0,
  /* 4873 */ 'r', 'e', 'p', 'l', '.', 'p', 'h', 9, 0,
  /* 4882 */ 's', 'h', 'r', 'l', '.', 'p', 'h', 9, 0,
  /* 4891 */ 'p', 'a', 'c', 'k', 'r', 'l', '.', 'p', 'h', 9, 0,
  /* 4902 */ 'm', 'u', 'l', '.', 'p', 'h', 9, 0,
  /* 4910 */ 's', 'u', 'b', 'q', '.', 'p', 'h', 9, 0,
  /* 4919 */ 'a', 'd', 'd', 'q', '.', 'p', 'h', 9, 0,
  /* 4928 */ 'c', 'm', 'p', '.', 'e', 'q', '.', 'p', 'h', 9, 0,
  /* 4939 */ 's', 'h', 'r', 'a', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 4950 */ 's', 'u', 'b', 'q', 'h', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 4962 */ 'a', 'd', 'd', 'q', 'h', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 4974 */ 's', 'h', 'r', 'a', 'v', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 4986 */ 's', 'h', 'l', 'l', '_', 's', '.', 'p', 'h', 9, 0,
  /* 4997 */ 'm', 'u', 'l', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5007 */ 's', 'u', 'b', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5018 */ 'a', 'd', 'd', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5029 */ 'm', 'u', 'l', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5040 */ 'a', 'b', 's', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5051 */ 's', 'u', 'b', 'u', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5062 */ 'a', 'd', 'd', 'u', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5073 */ 's', 'h', 'l', 'l', 'v', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5085 */ 'm', 'u', 'l', 'q', '_', 'r', 's', '.', 'p', 'h', 9, 0,
  /* 5097 */ 'c', 'm', 'p', '.', 'l', 't', '.', 'p', 'h', 9, 0,
  /* 5108 */ 's', 'u', 'b', 'u', '.', 'p', 'h', 9, 0,
  /* 5117 */ 'a', 'd', 'd', 'u', '.', 'p', 'h', 9, 0,
  /* 5126 */ 's', 'h', 'r', 'a', 'v', '.', 'p', 'h', 9, 0,
  /* 5136 */ 's', 'h', 'l', 'l', 'v', '.', 'p', 'h', 9, 0,
  /* 5146 */ 'r', 'e', 'p', 'l', 'v', '.', 'p', 'h', 9, 0,
  /* 5156 */ 's', 'h', 'r', 'l', 'v', '.', 'p', 'h', 9, 0,
  /* 5166 */ 'd', 'p', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5176 */ 'd', 'p', 'a', 'q', 'x', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5191 */ 'd', 'p', 's', 'q', 'x', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5206 */ 'm', 'u', 'l', 's', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5218 */ 'd', 'p', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5231 */ 'm', 'u', 'l', 's', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5246 */ 'd', 'p', 's', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5259 */ 'd', 'p', 'a', 'q', 'x', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5273 */ 'd', 'p', 's', 'q', 'x', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5287 */ 'd', 'p', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5297 */ 'd', 'p', 'a', 'x', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5308 */ 'd', 'p', 's', 'x', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5319 */ 's', 'h', 9, 0,
  /* 5323 */ 'd', 'm', 'u', 'h', 9, 0,
  /* 5329 */ 'd', 'a', 'd', 'd', 'i', 9, 0,
  /* 5336 */ 'a', 'n', 'd', 'i', 9, 0,
  /* 5342 */ 't', 'g', 'e', 'i', 9, 0,
  /* 5348 */ 's', 'n', 'e', 'i', 9, 0,
  /* 5354 */ 't', 'n', 'e', 'i', 9, 0,
  /* 5360 */ 'd', 'a', 'h', 'i', 9, 0,
  /* 5366 */ 'm', 'f', 'h', 'i', 9, 0,
  /* 5372 */ 'm', 't', 'h', 'i', 9, 0,
  /* 5378 */ '.', 'a', 'l', 'i', 'g', 'n', 32, '2', 10, 9, 'l', 'i', 9, 0,
  /* 5392 */ 'd', 'l', 'i', 9, 0,
  /* 5397 */ 'c', 'm', 'p', 'i', 9, 0,
  /* 5403 */ 's', 'e', 'q', 'i', 9, 0,
  /* 5409 */ 't', 'e', 'q', 'i', 9, 0,
  /* 5415 */ 'x', 'o', 'r', 'i', 9, 0,
  /* 5421 */ 'd', 'a', 't', 'i', 9, 0,
  /* 5427 */ 's', 'l', 't', 'i', 9, 0,
  /* 5433 */ 't', 'l', 't', 'i', 9, 0,
  /* 5439 */ 'd', 'a', 'u', 'i', 9, 0,
  /* 5445 */ 'l', 'u', 'i', 9, 0,
  /* 5450 */ 'j', 9, 0,
  /* 5453 */ 'b', 'r', 'e', 'a', 'k', 9, 0,
  /* 5460 */ 'c', 'v', 't', '.', 'd', '.', 'l', 9, 0,
  /* 5469 */ 'c', 'v', 't', '.', 's', '.', 'l', 9, 0,
  /* 5478 */ 'b', 'a', 'l', 9, 0,
  /* 5483 */ 'j', 'a', 'l', 9, 0,
  /* 5488 */ 'b', 'g', 'e', 'z', 'a', 'l', 9, 0,
  /* 5496 */ 'b', 'l', 't', 'z', 'a', 'l', 9, 0,
  /* 5504 */ 'd', 'p', 'a', 'u', '.', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5516 */ 'd', 'p', 's', 'u', '.', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5528 */ 'm', 'u', 'l', 'e', 'u', '_', 's', '.', 'p', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5544 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5559 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5575 */ 'l', 'd', 'l', 9, 0,
  /* 5580 */ 's', 'd', 'l', 9, 0,
  /* 5585 */ 'b', 'n', 'e', 'l', 9, 0,
  /* 5591 */ 'b', 'c', '0', 'f', 'l', 9, 0,
  /* 5598 */ 'b', 'c', '1', 'f', 'l', 9, 0,
  /* 5605 */ 'b', 'c', '2', 'f', 'l', 9, 0,
  /* 5612 */ 'b', 'c', '3', 'f', 'l', 9, 0,
  /* 5619 */ 'm', 'a', 'q', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5633 */ 'p', 'r', 'e', 'c', 'e', 'q', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5647 */ 'm', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5660 */ 'm', 'u', 'l', 'e', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5675 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 9, 0,
  /* 5684 */ 'b', 'g', 'e', 'z', 'a', 'l', 'l', 9, 0,
  /* 5693 */ 'b', 'l', 't', 'z', 'a', 'l', 'l', 9, 0,
  /* 5702 */ 'd', 's', 'l', 'l', 9, 0,
  /* 5708 */ 'b', 'e', 'q', 'l', 9, 0,
  /* 5714 */ 'd', 's', 'r', 'l', 9, 0,
  /* 5720 */ 'b', 'c', '0', 't', 'l', 9, 0,
  /* 5727 */ 'b', 'c', '1', 't', 'l', 9, 0,
  /* 5734 */ 'b', 'c', '2', 't', 'l', 9, 0,
  /* 5741 */ 'b', 'c', '3', 't', 'l', 9, 0,
  /* 5748 */ 'd', 'm', 'u', 'l', 9, 0,
  /* 5754 */ 'l', 'w', 'l', 9, 0,
  /* 5759 */ 's', 'w', 'l', 9, 0,
  /* 5764 */ 'b', 'g', 'e', 'z', 'l', 9, 0,
  /* 5771 */ 'b', 'l', 'e', 'z', 'l', 9, 0,
  /* 5778 */ 'b', 'g', 't', 'z', 'l', 9, 0,
  /* 5785 */ 'b', 'l', 't', 'z', 'l', 9, 0,
  /* 5792 */ 'b', 'a', 'l', 'i', 'g', 'n', 9, 0,
  /* 5800 */ 'd', 'a', 'l', 'i', 'g', 'n', 9, 0,
  /* 5808 */ 'm', 'o', 'v', 'n', 9, 0,
  /* 5814 */ 'd', 'c', 'l', 'o', 9, 0,
  /* 5820 */ 'm', 'f', 'l', 'o', 9, 0,
  /* 5826 */ 's', 'h', 'i', 'l', 'o', 9, 0,
  /* 5833 */ 'm', 't', 'l', 'o', 9, 0,
  /* 5839 */ 'd', 'b', 'i', 't', 's', 'w', 'a', 'p', 9, 0,
  /* 5849 */ 's', 'd', 'b', 'b', 'p', 9, 0,
  /* 5856 */ 'e', 'x', 't', 'p', 'd', 'p', 9, 0,
  /* 5864 */ 'm', 't', 'h', 'l', 'i', 'p', 9, 0,
  /* 5872 */ 'c', 'm', 'p', 9, 0,
  /* 5877 */ 'd', 'p', 'o', 'p', 9, 0,
  /* 5883 */ 'l', 'o', 'a', 'd', '_', 'c', 'c', 'o', 'n', 'd', '_', 'd', 's', 'p', 9, 0,
  /* 5899 */ 's', 't', 'o', 'r', 'e', '_', 'c', 'c', 'o', 'n', 'd', '_', 'd', 's', 'p', 9, 0,
  /* 5916 */ 'r', 'd', 'd', 's', 'p', 9, 0,
  /* 5923 */ 'w', 'r', 'd', 's', 'p', 9, 0,
  /* 5930 */ 'j', 'r', 'a', 'd', 'd', 'i', 'u', 's', 'p', 9, 0,
  /* 5941 */ 'e', 'x', 't', 'p', 9, 0,
  /* 5947 */ 'b', 'e', 'q', 9, 0,
  /* 5952 */ 's', 'e', 'q', 9, 0,
  /* 5957 */ 't', 'e', 'q', 9, 0,
  /* 5962 */ 'd', 'p', 'a', 'u', '.', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 5974 */ 'd', 'p', 's', 'u', '.', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 5986 */ 'm', 'u', 'l', 'e', 'u', '_', 's', '.', 'p', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6002 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6017 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6033 */ 'l', 'd', 'r', 9, 0,
  /* 6038 */ 's', 'd', 'r', 9, 0,
  /* 6043 */ 'm', 'a', 'q', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6057 */ 'p', 'r', 'e', 'c', 'e', 'q', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6071 */ 'm', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6084 */ 'm', 'u', 'l', 'e', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6099 */ 'j', 'r', 9, 0,
  /* 6103 */ 'j', 'a', 'l', 'r', 9, 0,
  /* 6109 */ 'n', 'o', 'r', 9, 0,
  /* 6114 */ 'x', 'o', 'r', 9, 0,
  /* 6119 */ 'd', 'r', 'o', 't', 'r', 9, 0,
  /* 6126 */ 'r', 'd', 'h', 'w', 'r', 9, 0,
  /* 6133 */ 'l', 'w', 'r', 9, 0,
  /* 6138 */ 's', 'w', 'r', 9, 0,
  /* 6143 */ 'm', 'i', 'n', 'a', '.', 's', 9, 0,
  /* 6151 */ 'm', 'a', 'x', 'a', '.', 's', 9, 0,
  /* 6159 */ 'n', 'm', 's', 'u', 'b', '.', 's', 9, 0,
  /* 6168 */ 'c', 'v', 't', '.', 'd', '.', 's', 9, 0,
  /* 6177 */ 'n', 'm', 'a', 'd', 'd', '.', 's', 9, 0,
  /* 6186 */ 'c', '.', 'n', 'g', 'e', '.', 's', 9, 0,
  /* 6195 */ 'c', '.', 'l', 'e', '.', 's', 9, 0,
  /* 6203 */ 'c', 'm', 'p', '.', 'l', 'e', '.', 's', 9, 0,
  /* 6213 */ 'c', '.', 'n', 'g', 'l', 'e', '.', 's', 9, 0,
  /* 6223 */ 'c', '.', 'o', 'l', 'e', '.', 's', 9, 0,
  /* 6232 */ 'c', 'm', 'p', '.', 's', 'l', 'e', '.', 's', 9, 0,
  /* 6243 */ 'c', '.', 'u', 'l', 'e', '.', 's', 9, 0,
  /* 6252 */ 'c', 'm', 'p', '.', 'u', 'l', 'e', '.', 's', 9, 0,
  /* 6263 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 'e', '.', 's', 9, 0,
  /* 6275 */ 'c', '.', 'f', '.', 's', 9, 0,
  /* 6282 */ 'c', 'm', 'p', '.', 'a', 'f', '.', 's', 9, 0,
  /* 6292 */ 'c', 'm', 'p', '.', 's', 'a', 'f', '.', 's', 9, 0,
  /* 6303 */ 'm', 's', 'u', 'b', 'f', '.', 's', 9, 0,
  /* 6312 */ 'm', 'a', 'd', 'd', 'f', '.', 's', 9, 0,
  /* 6321 */ 'c', '.', 's', 'f', '.', 's', 9, 0,
  /* 6329 */ 'm', 'o', 'v', 'f', '.', 's', 9, 0,
  /* 6337 */ 'n', 'e', 'g', '.', 's', 9, 0,
  /* 6344 */ 't', 'r', 'u', 'n', 'c', '.', 'l', '.', 's', 9, 0,
  /* 6355 */ 'r', 'o', 'u', 'n', 'd', '.', 'l', '.', 's', 9, 0,
  /* 6366 */ 'c', 'e', 'i', 'l', '.', 'l', '.', 's', 9, 0,
  /* 6376 */ 'f', 'l', 'o', 'o', 'r', '.', 'l', '.', 's', 9, 0,
  /* 6387 */ 'c', 'v', 't', '.', 'l', '.', 's', 9, 0,
  /* 6396 */ 's', 'e', 'l', '.', 's', 9, 0,
  /* 6403 */ 'c', '.', 'n', 'g', 'l', '.', 's', 9, 0,
  /* 6412 */ 'm', 'u', 'l', '.', 's', 9, 0,
  /* 6419 */ 'm', 'i', 'n', '.', 's', 9, 0,
  /* 6426 */ 'c', '.', 'u', 'n', '.', 's', 9, 0,
  /* 6434 */ 'c', 'm', 'p', '.', 'u', 'n', '.', 's', 9, 0,
  /* 6444 */ 'c', 'm', 'p', '.', 's', 'u', 'n', '.', 's', 9, 0,
  /* 6455 */ 'm', 'o', 'v', 'n', '.', 's', 9, 0,
  /* 6463 */ 'c', '.', 'e', 'q', '.', 's', 9, 0,
  /* 6471 */ 'c', 'm', 'p', '.', 'e', 'q', '.', 's', 9, 0,
  /* 6481 */ 'c', '.', 's', 'e', 'q', '.', 's', 9, 0,
  /* 6490 */ 'c', 'm', 'p', '.', 's', 'e', 'q', '.', 's', 9, 0,
  /* 6501 */ 'c', '.', 'u', 'e', 'q', '.', 's', 9, 0,
  /* 6510 */ 'c', 'm', 'p', '.', 'u', 'e', 'q', '.', 's', 9, 0,
  /* 6521 */ 'c', 'm', 'p', '.', 's', 'u', 'e', 'q', '.', 's', 9, 0,
  /* 6533 */ 'a', 'b', 's', '.', 's', 9, 0,
  /* 6540 */ 'c', 'l', 'a', 's', 's', '.', 's', 9, 0,
  /* 6549 */ 'c', '.', 'n', 'g', 't', '.', 's', 9, 0,
  /* 6558 */ 'c', '.', 'l', 't', '.', 's', 9, 0,
  /* 6566 */ 'c', 'm', 'p', '.', 'l', 't', '.', 's', 9, 0,
  /* 6576 */ 'c', '.', 'o', 'l', 't', '.', 's', 9, 0,
  /* 6585 */ 'c', 'm', 'p', '.', 's', 'l', 't', '.', 's', 9, 0,
  /* 6596 */ 'c', '.', 'u', 'l', 't', '.', 's', 9, 0,
  /* 6605 */ 'c', 'm', 'p', '.', 'u', 'l', 't', '.', 's', 9, 0,
  /* 6616 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 't', '.', 's', 9, 0,
  /* 6628 */ 'r', 'i', 'n', 't', '.', 's', 9, 0,
  /* 6636 */ 's', 'q', 'r', 't', '.', 's', 9, 0,
  /* 6644 */ 'm', 'o', 'v', 't', '.', 's', 9, 0,
  /* 6652 */ 'd', 'i', 'v', '.', 's', 9, 0,
  /* 6659 */ 'm', 'o', 'v', '.', 's', 9, 0,
  /* 6666 */ 't', 'r', 'u', 'n', 'c', '.', 'w', '.', 's', 9, 0,
  /* 6677 */ 'r', 'o', 'u', 'n', 'd', '.', 'w', '.', 's', 9, 0,
  /* 6688 */ 'c', 'e', 'i', 'l', '.', 'w', '.', 's', 9, 0,
  /* 6698 */ 'f', 'l', 'o', 'o', 'r', '.', 'w', '.', 's', 9, 0,
  /* 6709 */ 'c', 'v', 't', '.', 'w', '.', 's', 9, 0,
  /* 6718 */ 'm', 'a', 'x', '.', 's', 9, 0,
  /* 6725 */ 's', 'e', 'l', 'n', 'e', 'z', '.', 's', 9, 0,
  /* 6735 */ 's', 'e', 'l', 'e', 'q', 'z', '.', 's', 9, 0,
  /* 6745 */ 'm', 'o', 'v', 'z', '.', 's', 9, 0,
  /* 6753 */ 'j', 'a', 'l', 's', 9, 0,
  /* 6759 */ 'b', 'g', 'e', 'z', 'a', 'l', 's', 9, 0,
  /* 6768 */ 'b', 'l', 't', 'z', 'a', 'l', 's', 9, 0,
  /* 6777 */ 'j', 'a', 'l', 'r', 's', 9, 0,
  /* 6784 */ 'b', 'c', '0', 't', 9, 0,
  /* 6790 */ 'b', 'c', '1', 't', 9, 0,
  /* 6796 */ 'b', 'c', '2', 't', 9, 0,
  /* 6802 */ 'b', 'c', '3', 't', 9, 0,
  /* 6808 */ 'w', 'a', 'i', 't', 9, 0,
  /* 6814 */ 's', 'l', 't', 9, 0,
  /* 6819 */ 't', 'l', 't', 9, 0,
  /* 6824 */ 'd', 'm', 'u', 'l', 't', 9, 0,
  /* 6831 */ 'n', 'o', 't', 9, 0,
  /* 6836 */ 'm', 'o', 'v', 't', 9, 0,
  /* 6842 */ 'l', 'b', 'u', 9, 0,
  /* 6847 */ 'd', 's', 'u', 'b', 'u', 9, 0,
  /* 6854 */ 'm', 's', 'u', 'b', 'u', 9, 0,
  /* 6861 */ 'b', 'a', 'd', 'd', 'u', 9, 0,
  /* 6868 */ 'd', 'a', 'd', 'd', 'u', 9, 0,
  /* 6875 */ 'm', 'a', 'd', 'd', 'u', 9, 0,
  /* 6882 */ 'd', 'm', 'o', 'd', 'u', 9, 0,
  /* 6889 */ 't', 'g', 'e', 'u', 9, 0,
  /* 6895 */ 'l', 'h', 'u', 9, 0,
  /* 6900 */ 'd', 'm', 'u', 'h', 'u', 9, 0,
  /* 6907 */ 'd', 'a', 'd', 'd', 'i', 'u', 9, 0,
  /* 6915 */ 't', 'g', 'e', 'i', 'u', 9, 0,
  /* 6922 */ 's', 'l', 't', 'i', 'u', 9, 0,
  /* 6929 */ 't', 'l', 't', 'i', 'u', 9, 0,
  /* 6936 */ 'v', '3', 'm', 'u', 'l', 'u', 9, 0,
  /* 6944 */ 'd', 'm', 'u', 'l', 'u', 9, 0,
  /* 6951 */ 'v', 'm', 'u', 'l', 'u', 9, 0,
  /* 6958 */ 's', 'l', 't', 'u', 9, 0,
  /* 6964 */ 't', 'l', 't', 'u', 9, 0,
  /* 6970 */ 'd', 'm', 'u', 'l', 't', 'u', 9, 0,
  /* 6978 */ 'd', 'd', 'i', 'v', 'u', 9, 0,
  /* 6985 */ 'l', 'w', 'u', 9, 0,
  /* 6990 */ 'a', 'n', 'd', '.', 'v', 9, 0,
  /* 6997 */ 'm', 'o', 'v', 'e', '.', 'v', 9, 0,
  /* 7005 */ 'b', 's', 'e', 'l', '.', 'v', 9, 0,
  /* 7013 */ 'n', 'o', 'r', '.', 'v', 9, 0,
  /* 7020 */ 'x', 'o', 'r', '.', 'v', 9, 0,
  /* 7027 */ 'b', 'z', '.', 'v', 9, 0,
  /* 7033 */ 'b', 'm', 'z', '.', 'v', 9, 0,
  /* 7040 */ 'b', 'n', 'z', '.', 'v', 9, 0,
  /* 7047 */ 'b', 'm', 'n', 'z', '.', 'v', 9, 0,
  /* 7055 */ 'd', 's', 'r', 'a', 'v', 9, 0,
  /* 7062 */ 'b', 'i', 't', 'r', 'e', 'v', 9, 0,
  /* 7070 */ 'd', 'd', 'i', 'v', 9, 0,
  /* 7076 */ 'd', 's', 'l', 'l', 'v', 9, 0,
  /* 7083 */ 'd', 's', 'r', 'l', 'v', 9, 0,
  /* 7090 */ 's', 'h', 'i', 'l', 'o', 'v', 9, 0,
  /* 7098 */ 'e', 'x', 't', 'p', 'd', 'p', 'v', 9, 0,
  /* 7107 */ 'e', 'x', 't', 'p', 'v', 9, 0,
  /* 7114 */ 'd', 'r', 'o', 't', 'r', 'v', 9, 0,
  /* 7122 */ 'i', 'n', 's', 'v', 9, 0,
  /* 7128 */ 'f', 'l', 'o', 'g', '2', '.', 'w', 9, 0,
  /* 7137 */ 'f', 'e', 'x', 'p', '2', '.', 'w', 9, 0,
  /* 7146 */ 'a', 'd', 'd', '_', 'a', '.', 'w', 9, 0,
  /* 7155 */ 'f', 'm', 'i', 'n', '_', 'a', '.', 'w', 9, 0,
  /* 7165 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'w', 9, 0,
  /* 7175 */ 'f', 'm', 'a', 'x', '_', 'a', '.', 'w', 9, 0,
  /* 7185 */ 's', 'r', 'a', '.', 'w', 9, 0,
  /* 7192 */ 'f', 's', 'u', 'b', '.', 'w', 9, 0,
  /* 7200 */ 'f', 'm', 's', 'u', 'b', '.', 'w', 9, 0,
  /* 7209 */ 'n', 'l', 'o', 'c', '.', 'w', 9, 0,
  /* 7217 */ 'n', 'l', 'z', 'c', '.', 'w', 9, 0,
  /* 7225 */ 'c', 'v', 't', '.', 'd', '.', 'w', 9, 0,
  /* 7234 */ 'f', 'a', 'd', 'd', '.', 'w', 9, 0,
  /* 7242 */ 'f', 'm', 'a', 'd', 'd', '.', 'w', 9, 0,
  /* 7251 */ 's', 'l', 'd', '.', 'w', 9, 0,
  /* 7258 */ 'p', 'c', 'k', 'o', 'd', '.', 'w', 9, 0,
  /* 7267 */ 'i', 'l', 'v', 'o', 'd', '.', 'w', 9, 0,
  /* 7276 */ 'f', 'c', 'l', 'e', '.', 'w', 9, 0,
  /* 7284 */ 'f', 's', 'l', 'e', '.', 'w', 9, 0,
  /* 7292 */ 'f', 'c', 'u', 'l', 'e', '.', 'w', 9, 0,
  /* 7301 */ 'f', 's', 'u', 'l', 'e', '.', 'w', 9, 0,
  /* 7310 */ 'f', 'c', 'n', 'e', '.', 'w', 9, 0,
  /* 7318 */ 'f', 's', 'n', 'e', '.', 'w', 9, 0,
  /* 7326 */ 'f', 'c', 'u', 'n', 'e', '.', 'w', 9, 0,
  /* 7335 */ 'f', 's', 'u', 'n', 'e', '.', 'w', 9, 0,
  /* 7344 */ 'i', 'n', 's', 'v', 'e', '.', 'w', 9, 0,
  /* 7353 */ 'f', 'c', 'a', 'f', '.', 'w', 9, 0,
  /* 7361 */ 'f', 's', 'a', 'f', '.', 'w', 9, 0,
  /* 7369 */ 'v', 's', 'h', 'f', '.', 'w', 9, 0,
  /* 7377 */ 'b', 'n', 'e', 'g', '.', 'w', 9, 0,
  /* 7385 */ 'p', 'r', 'e', 'c', 'r', '_', 's', 'r', 'a', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7401 */ 'p', 'r', 'e', 'c', 'r', 'q', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7414 */ 'p', 'r', 'e', 'c', 'r', '_', 's', 'r', 'a', '_', 'r', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7432 */ 'p', 'r', 'e', 'c', 'r', 'q', '_', 'r', 's', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7448 */ 's', 'u', 'b', 'q', 'h', '.', 'w', 9, 0,
  /* 7457 */ 'a', 'd', 'd', 'q', 'h', '.', 'w', 9, 0,
  /* 7466 */ 's', 'r', 'a', 'i', '.', 'w', 9, 0,
  /* 7474 */ 's', 'l', 'd', 'i', '.', 'w', 9, 0,
  /* 7482 */ 'b', 'n', 'e', 'g', 'i', '.', 'w', 9, 0,
  /* 7491 */ 's', 'l', 'l', 'i', '.', 'w', 9, 0,
  /* 7499 */ 's', 'r', 'l', 'i', '.', 'w', 9, 0,
  /* 7507 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'w', 9, 0,
  /* 7517 */ 'c', 'e', 'q', 'i', '.', 'w', 9, 0,
  /* 7525 */ 's', 'r', 'a', 'r', 'i', '.', 'w', 9, 0,
  /* 7534 */ 'b', 'c', 'l', 'r', 'i', '.', 'w', 9, 0,
  /* 7543 */ 's', 'r', 'l', 'r', 'i', '.', 'w', 9, 0,
  /* 7552 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'w', 9, 0,
  /* 7562 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'w', 9, 0,
  /* 7572 */ 'b', 's', 'e', 't', 'i', '.', 'w', 9, 0,
  /* 7581 */ 's', 'u', 'b', 'v', 'i', '.', 'w', 9, 0,
  /* 7590 */ 'a', 'd', 'd', 'v', 'i', '.', 'w', 9, 0,
  /* 7599 */ 'd', 'p', 'a', 'q', '_', 's', 'a', '.', 'l', '.', 'w', 9, 0,
  /* 7612 */ 'd', 'p', 's', 'q', '_', 's', 'a', '.', 'l', '.', 'w', 9, 0,
  /* 7625 */ 'f', 'i', 'l', 'l', '.', 'w', 9, 0,
  /* 7633 */ 's', 'l', 'l', '.', 'w', 9, 0,
  /* 7640 */ 'f', 'e', 'x', 'u', 'p', 'l', '.', 'w', 9, 0,
  /* 7650 */ 'f', 'f', 'q', 'l', '.', 'w', 9, 0,
  /* 7658 */ 's', 'r', 'l', '.', 'w', 9, 0,
  /* 7665 */ 'b', 'i', 'n', 's', 'l', '.', 'w', 9, 0,
  /* 7674 */ 'f', 'm', 'u', 'l', '.', 'w', 9, 0,
  /* 7682 */ 'i', 'l', 'v', 'l', '.', 'w', 9, 0,
  /* 7690 */ 'f', 'm', 'i', 'n', '.', 'w', 9, 0,
  /* 7698 */ 'f', 'c', 'u', 'n', '.', 'w', 9, 0,
  /* 7706 */ 'f', 's', 'u', 'n', '.', 'w', 9, 0,
  /* 7714 */ 'f', 'e', 'x', 'd', 'o', '.', 'w', 9, 0,
  /* 7723 */ 'f', 'r', 'c', 'p', '.', 'w', 9, 0,
  /* 7731 */ 'm', 's', 'u', 'b', '_', 'q', '.', 'w', 9, 0,
  /* 7741 */ 'm', 'a', 'd', 'd', '_', 'q', '.', 'w', 9, 0,
  /* 7751 */ 'm', 'u', 'l', '_', 'q', '.', 'w', 9, 0,
  /* 7760 */ 'm', 's', 'u', 'b', 'r', '_', 'q', '.', 'w', 9, 0,
  /* 7771 */ 'm', 'a', 'd', 'd', 'r', '_', 'q', '.', 'w', 9, 0,
  /* 7782 */ 'm', 'u', 'l', 'r', '_', 'q', '.', 'w', 9, 0,
  /* 7792 */ 'f', 'c', 'e', 'q', '.', 'w', 9, 0,
  /* 7800 */ 'f', 's', 'e', 'q', '.', 'w', 9, 0,
  /* 7808 */ 'f', 'c', 'u', 'e', 'q', '.', 'w', 9, 0,
  /* 7817 */ 'f', 's', 'u', 'e', 'q', '.', 'w', 9, 0,
  /* 7826 */ 'f', 't', 'q', '.', 'w', 9, 0,
  /* 7833 */ 's', 'h', 'r', 'a', '_', 'r', '.', 'w', 9, 0,
  /* 7843 */ 's', 'u', 'b', 'q', 'h', '_', 'r', '.', 'w', 9, 0,
  /* 7854 */ 'a', 'd', 'd', 'q', 'h', '_', 'r', '.', 'w', 9, 0,
  /* 7865 */ 'e', 'x', 't', 'r', '_', 'r', '.', 'w', 9, 0,
  /* 7875 */ 's', 'h', 'r', 'a', 'v', '_', 'r', '.', 'w', 9, 0,
  /* 7886 */ 'e', 'x', 't', 'r', 'v', '_', 'r', '.', 'w', 9, 0,
  /* 7897 */ 's', 'r', 'a', 'r', '.', 'w', 9, 0,
  /* 7905 */ 'b', 'c', 'l', 'r', '.', 'w', 9, 0,
  /* 7913 */ 's', 'r', 'l', 'r', '.', 'w', 9, 0,
  /* 7921 */ 'f', 'c', 'o', 'r', '.', 'w', 9, 0,
  /* 7929 */ 'f', 's', 'o', 'r', '.', 'w', 9, 0,
  /* 7937 */ 'f', 'e', 'x', 'u', 'p', 'r', '.', 'w', 9, 0,
  /* 7947 */ 'f', 'f', 'q', 'r', '.', 'w', 9, 0,
  /* 7955 */ 'b', 'i', 'n', 's', 'r', '.', 'w', 9, 0,
  /* 7964 */ 'e', 'x', 't', 'r', '.', 'w', 9, 0,
  /* 7972 */ 'i', 'l', 'v', 'r', '.', 'w', 9, 0,
  /* 7980 */ 'c', 'v', 't', '.', 's', '.', 'w', 9, 0,
  /* 7989 */ 'a', 's', 'u', 'b', '_', 's', '.', 'w', 9, 0,
  /* 7999 */ 'h', 's', 'u', 'b', '_', 's', '.', 'w', 9, 0,
  /* 8009 */ 'd', 'p', 's', 'u', 'b', '_', 's', '.', 'w', 9, 0,
  /* 8020 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 's', '.', 'w', 9, 0,
  /* 8032 */ 'h', 'a', 'd', 'd', '_', 's', '.', 'w', 9, 0,
  /* 8042 */ 'd', 'p', 'a', 'd', 'd', '_', 's', '.', 'w', 9, 0,
  /* 8053 */ 'm', 'o', 'd', '_', 's', '.', 'w', 9, 0,
  /* 8062 */ 'c', 'l', 'e', '_', 's', '.', 'w', 9, 0,
  /* 8071 */ 'a', 'v', 'e', '_', 's', '.', 'w', 9, 0,
  /* 8080 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8090 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8100 */ 'c', 'l', 't', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8110 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8120 */ 's', 'h', 'l', 'l', '_', 's', '.', 'w', 9, 0,
  /* 8130 */ 'm', 'i', 'n', '_', 's', '.', 'w', 9, 0,
  /* 8139 */ 'd', 'o', 't', 'p', '_', 's', '.', 'w', 9, 0,
  /* 8149 */ 's', 'u', 'b', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8159 */ 'a', 'd', 'd', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8169 */ 'm', 'u', 'l', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8179 */ 'a', 'b', 's', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8189 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'w', 9, 0,
  /* 8199 */ 's', 'u', 'b', 's', '_', 's', '.', 'w', 9, 0,
  /* 8209 */ 'a', 'd', 'd', 's', '_', 's', '.', 'w', 9, 0,
  /* 8219 */ 's', 'a', 't', '_', 's', '.', 'w', 9, 0,
  /* 8228 */ 'c', 'l', 't', '_', 's', '.', 'w', 9, 0,
  /* 8237 */ 'f', 'f', 'i', 'n', 't', '_', 's', '.', 'w', 9, 0,
  /* 8248 */ 'f', 't', 'i', 'n', 't', '_', 's', '.', 'w', 9, 0,
  /* 8259 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'w', 9, 0,
  /* 8271 */ 'd', 'i', 'v', '_', 's', '.', 'w', 9, 0,
  /* 8280 */ 's', 'h', 'l', 'l', 'v', '_', 's', '.', 'w', 9, 0,
  /* 8291 */ 'm', 'a', 'x', '_', 's', '.', 'w', 9, 0,
  /* 8300 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'w', 9, 0,
  /* 8310 */ 'm', 'u', 'l', 'q', '_', 'r', 's', '.', 'w', 9, 0,
  /* 8321 */ 'e', 'x', 't', 'r', '_', 'r', 's', '.', 'w', 9, 0,
  /* 8332 */ 'e', 'x', 't', 'r', 'v', '_', 'r', 's', '.', 'w', 9, 0,
  /* 8344 */ 'f', 'c', 'l', 'a', 's', 's', '.', 'w', 9, 0,
  /* 8354 */ 's', 'p', 'l', 'a', 't', '.', 'w', 9, 0,
  /* 8363 */ 'b', 's', 'e', 't', '.', 'w', 9, 0,
  /* 8371 */ 'f', 'c', 'l', 't', '.', 'w', 9, 0,
  /* 8379 */ 'f', 's', 'l', 't', '.', 'w', 9, 0,
  /* 8387 */ 'f', 'c', 'u', 'l', 't', '.', 'w', 9, 0,
  /* 8396 */ 'f', 's', 'u', 'l', 't', '.', 'w', 9, 0,
  /* 8405 */ 'p', 'c', 'n', 't', '.', 'w', 9, 0,
  /* 8413 */ 'f', 'r', 'i', 'n', 't', '.', 'w', 9, 0,
  /* 8422 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'w', 9, 0,
  /* 8432 */ 'f', 's', 'q', 'r', 't', '.', 'w', 9, 0,
  /* 8441 */ 'f', 'r', 's', 'q', 'r', 't', '.', 'w', 9, 0,
  /* 8451 */ 's', 't', '.', 'w', 9, 0,
  /* 8457 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'w', 9, 0,
  /* 8467 */ 'h', 's', 'u', 'b', '_', 'u', '.', 'w', 9, 0,
  /* 8477 */ 'd', 'p', 's', 'u', 'b', '_', 'u', '.', 'w', 9, 0,
  /* 8488 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 'u', '.', 'w', 9, 0,
  /* 8500 */ 'h', 'a', 'd', 'd', '_', 'u', '.', 'w', 9, 0,
  /* 8510 */ 'd', 'p', 'a', 'd', 'd', '_', 'u', '.', 'w', 9, 0,
  /* 8521 */ 'm', 'o', 'd', '_', 'u', '.', 'w', 9, 0,
  /* 8530 */ 'c', 'l', 'e', '_', 'u', '.', 'w', 9, 0,
  /* 8539 */ 'a', 'v', 'e', '_', 'u', '.', 'w', 9, 0,
  /* 8548 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8558 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8568 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8578 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8588 */ 'm', 'i', 'n', '_', 'u', '.', 'w', 9, 0,
  /* 8597 */ 'd', 'o', 't', 'p', '_', 'u', '.', 'w', 9, 0,
  /* 8607 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'w', 9, 0,
  /* 8617 */ 's', 'u', 'b', 's', '_', 'u', '.', 'w', 9, 0,
  /* 8627 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'w', 9, 0,
  /* 8637 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'w', 9, 0,
  /* 8649 */ 's', 'a', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8658 */ 'c', 'l', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8667 */ 'f', 'f', 'i', 'n', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8678 */ 'f', 't', 'i', 'n', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8689 */ 'd', 'i', 'v', '_', 'u', '.', 'w', 9, 0,
  /* 8698 */ 'm', 'a', 'x', '_', 'u', '.', 'w', 9, 0,
  /* 8707 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'w', 9, 0,
  /* 8717 */ 'm', 's', 'u', 'b', 'v', '.', 'w', 9, 0,
  /* 8726 */ 'm', 'a', 'd', 'd', 'v', '.', 'w', 9, 0,
  /* 8735 */ 'p', 'c', 'k', 'e', 'v', '.', 'w', 9, 0,
  /* 8744 */ 'i', 'l', 'v', 'e', 'v', '.', 'w', 9, 0,
  /* 8753 */ 'f', 'd', 'i', 'v', '.', 'w', 9, 0,
  /* 8761 */ 'm', 'u', 'l', 'v', '.', 'w', 9, 0,
  /* 8769 */ 'e', 'x', 't', 'r', 'v', '.', 'w', 9, 0,
  /* 8778 */ 'f', 'm', 'a', 'x', '.', 'w', 9, 0,
  /* 8786 */ 'b', 'z', '.', 'w', 9, 0,
  /* 8792 */ 'b', 'n', 'z', '.', 'w', 9, 0,
  /* 8799 */ 'l', 'w', 9, 0,
  /* 8803 */ 's', 'w', 9, 0,
  /* 8807 */ 'l', 'h', 'x', 9, 0,
  /* 8812 */ 'j', 'a', 'l', 'x', 9, 0,
  /* 8818 */ 'l', 'b', 'u', 'x', 9, 0,
  /* 8824 */ 'l', 'w', 'x', 9, 0,
  /* 8829 */ 'b', 'g', 'e', 'z', 9, 0,
  /* 8835 */ 'b', 'l', 'e', 'z', 9, 0,
  /* 8841 */ 'b', 'n', 'e', 'z', 9, 0,
  /* 8847 */ 's', 'e', 'l', 'n', 'e', 'z', 9, 0,
  /* 8855 */ 'b', 't', 'n', 'e', 'z', 9, 0,
  /* 8862 */ 'd', 'c', 'l', 'z', 9, 0,
  /* 8868 */ 'b', 'e', 'q', 'z', 9, 0,
  /* 8874 */ 's', 'e', 'l', 'e', 'q', 'z', 9, 0,
  /* 8882 */ 'b', 't', 'e', 'q', 'z', 9, 0,
  /* 8889 */ 'b', 'g', 't', 'z', 9, 0,
  /* 8895 */ 'b', 'l', 't', 'z', 9, 0,
  /* 8901 */ 'm', 'o', 'v', 'z', 9, 0,
  /* 8907 */ 's', 'e', 'b', 9, 32, 0,
  /* 8913 */ 'j', 'r', 'c', 9, 32, 0,
  /* 8919 */ 's', 'e', 'h', 9, 32, 0,
  /* 8925 */ 'd', 'd', 'i', 'v', 'u', 9, '$', 'z', 'e', 'r', 'o', ',', 32, 0,
  /* 8939 */ 'd', 'd', 'i', 'v', 9, '$', 'z', 'e', 'r', 'o', ',', 32, 0,
  /* 8952 */ 'a', 'd', 'd', 'i', 'u', 9, '$', 's', 'p', ',', 32, 0,
  /* 8964 */ 'c', 'i', 'n', 's', '3', '2', 32, 0,
  /* 8972 */ 'e', 'x', 't', 's', '3', '2', 32, 0,
  /* 8980 */ 's', 'y', 'n', 'c', 32, 0,
  /* 8986 */ 9, '.', 'w', 'o', 'r', 'd', 32, 0,
  /* 8994 */ 'd', 'i', 'n', 's', 'm', 32, 0,
  /* 9001 */ 'd', 'e', 'x', 't', 'm', 32, 0,
  /* 9008 */ 'c', 'i', 'n', 's', 32, 0,
  /* 9014 */ 'd', 'i', 'n', 's', 32, 0,
  /* 9020 */ 'e', 'x', 't', 's', 32, 0,
  /* 9026 */ 'd', 'e', 'x', 't', 32, 0,
  /* 9032 */ 'd', 'i', 'n', 's', 'u', 32, 0,
  /* 9039 */ 'd', 'e', 'x', 't', 'u', 32, 0,
  /* 9046 */ 'b', 'c', '1', 'n', 'e', 'z', 32, 0,
  /* 9054 */ 'b', 'c', '2', 'n', 'e', 'z', 32, 0,
  /* 9062 */ 'b', 'c', '1', 'e', 'q', 'z', 32, 0,
  /* 9070 */ 'b', 'c', '2', 'e', 'q', 'z', 32, 0,
  /* 9078 */ 'c', '.', 0,
  /* 9081 */ 'b', 'r', 'e', 'a', 'k', 32, '0', 0,
  /* 9089 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 9102 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 9109 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 9119 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 9134 */ 'j', 'r', 'c', 9, 32, '$', 'r', 'a', 0,
  /* 9143 */ 'j', 'r', 9, 32, '$', 'r', 'a', 0,
  /* 9151 */ 'e', 'h', 'b', 0,
  /* 9155 */ 'p', 'a', 'u', 's', 'e', 0,
  /* 9161 */ 't', 'l', 'b', 'w', 'i', 0,
  /* 9167 */ 'f', 'o', 'o', 0,
  /* 9171 */ 't', 'l', 'b', 'p', 0,
  /* 9176 */ 's', 's', 'n', 'o', 'p', 0,
  /* 9182 */ 't', 'l', 'b', 'r', 0,
  /* 9187 */ 't', 'l', 'b', 'w', 'r', 0,
  /* 9193 */ 'd', 'e', 'r', 'e', 't', 0,
  /* 9199 */ 'w', 'a', 'i', 't', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint64_t Bits1 = OpInfo[MCInst_getOpcode(MI)];
  uint64_t Bits2 = OpInfo2[MCInst_getOpcode(MI)];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 16383)-1);
#endif


  // Fragment 0 encoded into 3 bits for 7 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 14) & 7);
  switch ((Bits >> 14) & 7) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, Break16, CONSTPOOL_EN...
    return;
    break;
  case 1:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADD, ADDIUPC, ADDQH_PH, ADDQH_R_PH, AD...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // CACHE, CACHE_R6, PREF, PREF_R6
    printUnsignedImm(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printMemOperand(MI, 0, O); 
    return;
    break;
  case 3:
    // CTC1, CTC1_MM, DAHI, DATI, DMTC1, MTC1, MTC1_MM, MTHC1_MM, MTHI_DSP, M...
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // FCMP_D32, FCMP_D32_MM, FCMP_D64, FCMP_S32, FCMP_S32_MM
    printFCCOperand(MI, 2, O); 
    break;
  case 5:
    // MTHC1_D32, MTHC1_D64
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // SelBeqZ, SelBneZ, SelTBteqZCmp, SelTBteqZCmpi, SelTBteqZSlt, SelTBteqZ...
    printOperand(MI, 3, O); 
    break;
  }


  // Fragment 1 encoded into 4 bits for 15 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 17) & 15);
  switch ((Bits >> 17) & 15) {
  default:   // unreachable.
  case 0:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADD, ADDIUPC, ADDQH_PH, ADDQH_R_PH, AD...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // AddiuRxPcImmX16
    SStream_concat0(O, ", $pc, "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 2:
    // AddiuSpImm16, Bimm16
    SStream_concat0(O, " # 16 bit inst"); 
    return;
    break;
  case 3:
    // AddiuSpImmX16, BAL, BALC, BC, BPOSGE32, BimmX16, BteqzX16, BtnezX16, C...
    return;
    break;
  case 4:
    // Bteqz16, Btnez16
    SStream_concat0(O, "  # 16 bit inst"); 
    return;
    break;
  case 5:
    // CTC1, CTC1_MM, DMTC1, MTC1, MTC1_MM, MTHC1_MM, MTHI_DSP, MTHLIP, MTLO_...
    printOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // DAHI, DATI, MultRxRyRz16, MultuRxRyRz16, SltCCRxRy16, SltiCCRxImmX16, ...
    printOperand(MI, 2, O); 
    break;
  case 7:
    // FCMP_D32, FCMP_D32_MM, FCMP_D64
    SStream_concat0(O, ".d\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // FCMP_S32, FCMP_S32_MM
    SStream_concat0(O, ".s\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 9:
    // INSERT_B, INSERT_D, INSERT_H, INSERT_W, INSVE_B, INSVE_D, INSVE_H, INS...
    SStream_concat0(O, "["); 
    break;
  case 10:
    // Jal16
    SStream_concat0(O, "\n\tnop"); 
    return;
    break;
  case 11:
    // JalB16
    SStream_concat0(O, "\t# branch\n\tnop"); 
    return;
    break;
  case 12:
    // LwConstant32
    SStream_concat0(O, ", 1f\n\tb\t2f\n\t.align\t2\n1: \t.word\t"); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, "\n2:"); 
    return;
    break;
  case 13:
    // SC, SCD, SCD_R6, SC_MM, SC_R6
    printMemOperand(MI, 2, O); 
    return;
    break;
  case 14:
    // SelBeqZ, SelBneZ
    SStream_concat0(O, ", .+4\n\t\n\tmove "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 2 encoded into 4 bits for 11 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 21) & 15);
  switch ((Bits >> 21) & 15) {
  default:   // unreachable.
  case 0:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADD, ADDIUPC, ADDQH_PH, ADDQH_R_PH, AD...
    printOperand(MI, 1, O); 
    break;
  case 1:
    // AddiuRxRxImm16, AddiuRxRxImmX16, AndRxRxRy16, BINSLI_B, BINSLI_D, BINS...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // AddiuRxRyOffMemX16, LEA_ADDiu, LEA_ADDiu64, LEA_ADDiu_MM
    printMemOperandEA(MI, 1, O); 
    return;
    break;
  case 3:
    // DAHI, DATI
    return;
    break;
  case 4:
    // INSERT_B, INSERT_D, INSERT_H, INSERT_W
    printUnsignedImm(MI, 3, O); 
    SStream_concat0(O, "], "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 5:
    // INSVE_B, INSVE_D, INSVE_H, INSVE_W
    printUnsignedImm(MI, 2, O); 
    SStream_concat0(O, "], "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, "["); 
    printUnsignedImm(MI, 4, O); 
    SStream_concat0(O, "]"); 
    return;
    break;
  case 6:
    // LB, LB64, LB_MM, LBu, LBu64, LBu_MM, LD, LDC1, LDC164, LDC1_MM, LDC2, ...
    printMemOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // LUi, LUi64, LUi_MM, LoadAddr32Imm, LoadImm32Reg, RDDSP, REPL_PH, REPL_...
    printUnsignedImm(MI, 1, O); 
    return;
    break;
  case 8:
    // MultRxRyRz16, MultuRxRyRz16
    SStream_concat0(O, "\n\tmflo\t"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 9:
    // SelTBteqZCmp, SelTBteqZCmpi, SelTBteqZSlt, SelTBteqZSlti, SelTBteqZSlt...
    printOperand(MI, 4, O); 
    break;
  case 10:
    // SltCCRxRy16, SltiCCRxImmX16, SltiuCCRxImmX16, SltuCCRxRy16, SltuRxRyRz...
    SStream_concat0(O, "\n\tmove\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", $t8"); 
    return;
    break;
  }


  // Fragment 3 encoded into 4 bits for 15 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 25) & 15);
  switch ((Bits >> 25) & 15) {
  default:   // unreachable.
  case 0:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADDIUPC, ALUIPC, AUIPC, AddiuRxImmX16,...
    return;
    break;
  case 1:
    // ADD, ADDQH_PH, ADDQH_R_PH, ADDQH_R_W, ADDQH_W, ADDQ_PH, ADDQ_S_PH, ADD...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // AddiuRxRxImm16, LwRxPcTcp16
    SStream_concat0(O, "\t# 16 bit inst"); 
    return;
    break;
  case 3:
    // BeqzRxImm16, BnezRxImm16
    SStream_concat0(O, "  # 16 bit inst"); 
    return;
    break;
  case 4:
    // BteqzT8CmpX16, BteqzT8CmpiX16, BteqzT8SltX16, BteqzT8SltiX16, BteqzT8S...
    SStream_concat0(O, "\n\tbteqz\t"); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 5:
    // BtnezT8CmpX16, BtnezT8CmpiX16, BtnezT8SltX16, BtnezT8SltiX16, BtnezT8S...
    SStream_concat0(O, "\n\tbtnez\t"); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // COPY_S_B, COPY_S_D, COPY_S_H, COPY_S_W, COPY_U_B, COPY_U_D, COPY_U_H, ...
    SStream_concat0(O, "["); 
    break;
  case 7:
    // CmpiRxImm16, LiRxImm16, SltiRxImm16, SltiuRxImm16
    SStream_concat0(O, " \t# 16 bit inst"); 
    return;
    break;
  case 8:
    // DSLL64_32
    SStream_concat0(O, ", 32"); 
    return;
    break;
  case 9:
    // GotPrologue16
    SStream_concat0(O, "\n\taddiu\t"); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", $pc, "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, "\n "); 
    return;
    break;
  case 10:
    // LBUX, LDXC1, LDXC164, LHX, LUXC1, LUXC164, LUXC1_MM, LWX, LWXC1, LWXC1...
    SStream_concat0(O, "("); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ")"); 
    return;
    break;
  case 11:
    // LwRxSpImmX16, SwRxSpImmX16
    SStream_concat0(O, " ( "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, " ); "); 
    return;
    break;
  case 12:
    // SLL64_32, SLL64_64
    SStream_concat0(O, ", 0"); 
    return;
    break;
  case 13:
    // SelTBteqZCmp, SelTBteqZCmpi, SelTBteqZSlt, SelTBteqZSlti, SelTBteqZSlt...
    SStream_concat0(O, "\n\tbteqz\t.+4\n\tmove "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 14:
    // SelTBtneZCmp, SelTBtneZCmpi, SelTBtneZSlt, SelTBtneZSlti, SelTBtneZSlt...
    SStream_concat0(O, "\n\tbtnez\t.+4\n\tmove "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 3 bits for 5 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 29) & 7);
  switch ((Bits >> 29) & 7) {
  default:   // unreachable.
  case 0:
    // ADD, ADDQH_PH, ADDQH_R_PH, ADDQH_R_W, ADDQH_W, ADDQ_PH, ADDQ_S_PH, ADD...
    printOperand(MI, 2, O); 
    break;
  case 1:
    // ADDVI_B, ADDVI_D, ADDVI_H, ADDVI_W, ANDI_B, BCLRI_B, BCLRI_D, BCLRI_H,...
    printUnsignedImm8(MI, 2, O); 
    break;
  case 2:
    // ANDi, ANDi64, ANDi_MM, APPEND, BALIGN, CINS, CINS32, DEXT, DEXTM, DEXT...
    printUnsignedImm(MI, 2, O); 
    break;
  case 3:
    // BINSLI_B, BINSLI_D, BINSLI_H, BINSLI_W, BINSRI_B, BINSRI_D, BINSRI_H, ...
    printUnsignedImm8(MI, 3, O); 
    break;
  case 4:
    // BINSL_B, BINSL_D, BINSL_H, BINSL_W, BINSR_B, BINSR_D, BINSR_H, BINSR_W...
    printOperand(MI, 3, O); 
    break;
  }


  // Fragment 5 encoded into 2 bits for 3 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 32) & 3);
  switch ((Bits >> 32) & 3) {
  default:   // unreachable.
  case 0:
    // ADD, ADDQH_PH, ADDQH_R_PH, ADDQH_R_W, ADDQH_W, ADDQ_PH, ADDQ_S_PH, ADD...
    return;
    break;
  case 1:
    // ALIGN, CINS, CINS32, DALIGN, DEXT, DEXTM, DEXTU, DINS, DINSM, DINSU, D...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // COPY_S_B, COPY_S_D, COPY_S_H, COPY_S_W, COPY_U_B, COPY_U_D, COPY_U_H, ...
    SStream_concat0(O, "]"); 
    return;
    break;
  }


  // Fragment 6 encoded into 1 bits for 2 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 34) & 1);
  if ((Bits >> 34) & 1) {
    // DEXT, DEXTM, DEXTU, DINS, DINSM, DINSU, EXT, EXT_MM, INS, INS_MM, MADD...
    printOperand(MI, 3, O); 
    return;
  } else {
    // ALIGN, CINS, CINS32, DALIGN, DLSA, DLSA_R6, EXTS, EXTS32, LSA, LSA_R6
    printUnsignedImm(MI, 3, O); 
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 394 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'f', '1', '0', 0,
  /* 4 */ 'w', '1', '0', 0,
  /* 8 */ 'f', '2', '0', 0,
  /* 12 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '0', 0,
  /* 25 */ 'w', '2', '0', 0,
  /* 29 */ 'f', '3', '0', 0,
  /* 33 */ 'w', '3', '0', 0,
  /* 37 */ 'a', '0', 0,
  /* 40 */ 'a', 'c', '0', 0,
  /* 44 */ 'f', 'c', 'c', '0', 0,
  /* 49 */ 'f', '0', 0,
  /* 52 */ 'k', '0', 0,
  /* 55 */ 'm', 'p', 'l', '0', 0,
  /* 60 */ 'p', '0', 0,
  /* 63 */ 's', '0', 0,
  /* 66 */ 't', '0', 0,
  /* 69 */ 'v', '0', 0,
  /* 72 */ 'w', '0', 0,
  /* 75 */ 'f', '1', '1', 0,
  /* 79 */ 'w', '1', '1', 0,
  /* 83 */ 'f', '2', '1', 0,
  /* 87 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '1', 0,
  /* 100 */ 'w', '2', '1', 0,
  /* 104 */ 'f', '3', '1', 0,
  /* 108 */ 'w', '3', '1', 0,
  /* 112 */ 'a', '1', 0,
  /* 115 */ 'a', 'c', '1', 0,
  /* 119 */ 'f', 'c', 'c', '1', 0,
  /* 124 */ 'f', '1', 0,
  /* 127 */ 'k', '1', 0,
  /* 130 */ 'm', 'p', 'l', '1', 0,
  /* 135 */ 'p', '1', 0,
  /* 138 */ 's', '1', 0,
  /* 141 */ 't', '1', 0,
  /* 144 */ 'v', '1', 0,
  /* 147 */ 'w', '1', 0,
  /* 150 */ 'f', '1', '2', 0,
  /* 154 */ 'w', '1', '2', 0,
  /* 158 */ 'f', '2', '2', 0,
  /* 162 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '2', 0,
  /* 175 */ 'w', '2', '2', 0,
  /* 179 */ 'a', '2', 0,
  /* 182 */ 'a', 'c', '2', 0,
  /* 186 */ 'f', 'c', 'c', '2', 0,
  /* 191 */ 'f', '2', 0,
  /* 194 */ 'm', 'p', 'l', '2', 0,
  /* 199 */ 'p', '2', 0,
  /* 202 */ 's', '2', 0,
  /* 205 */ 't', '2', 0,
  /* 208 */ 'w', '2', 0,
  /* 211 */ 'f', '1', '3', 0,
  /* 215 */ 'w', '1', '3', 0,
  /* 219 */ 'f', '2', '3', 0,
  /* 223 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '3', 0,
  /* 236 */ 'w', '2', '3', 0,
  /* 240 */ 'a', '3', 0,
  /* 243 */ 'a', 'c', '3', 0,
  /* 247 */ 'f', 'c', 'c', '3', 0,
  /* 252 */ 'f', '3', 0,
  /* 255 */ 's', '3', 0,
  /* 258 */ 't', '3', 0,
  /* 261 */ 'w', '3', 0,
  /* 264 */ 'f', '1', '4', 0,
  /* 268 */ 'w', '1', '4', 0,
  /* 272 */ 'f', '2', '4', 0,
  /* 276 */ 'w', '2', '4', 0,
  /* 280 */ 'f', 'c', 'c', '4', 0,
  /* 285 */ 'f', '4', 0,
  /* 288 */ 's', '4', 0,
  /* 291 */ 't', '4', 0,
  /* 294 */ 'w', '4', 0,
  /* 297 */ 'f', '1', '5', 0,
  /* 301 */ 'w', '1', '5', 0,
  /* 305 */ 'f', '2', '5', 0,
  /* 309 */ 'w', '2', '5', 0,
  /* 313 */ 'f', 'c', 'c', '5', 0,
  /* 318 */ 'f', '5', 0,
  /* 321 */ 's', '5', 0,
  /* 324 */ 't', '5', 0,
  /* 327 */ 'w', '5', 0,
  /* 330 */ 'f', '1', '6', 0,
  /* 334 */ 'w', '1', '6', 0,
  /* 338 */ 'f', '2', '6', 0,
  /* 342 */ 'w', '2', '6', 0,
  /* 346 */ 'f', 'c', 'c', '6', 0,
  /* 351 */ 'f', '6', 0,
  /* 354 */ 's', '6', 0,
  /* 357 */ 't', '6', 0,
  /* 360 */ 'w', '6', 0,
  /* 363 */ 'f', '1', '7', 0,
  /* 367 */ 'w', '1', '7', 0,
  /* 371 */ 'f', '2', '7', 0,
  /* 375 */ 'w', '2', '7', 0,
  /* 379 */ 'f', 'c', 'c', '7', 0,
  /* 384 */ 'f', '7', 0,
  /* 387 */ 's', '7', 0,
  /* 390 */ 't', '7', 0,
  /* 393 */ 'w', '7', 0,
  /* 396 */ 'f', '1', '8', 0,
  /* 400 */ 'w', '1', '8', 0,
  /* 404 */ 'f', '2', '8', 0,
  /* 408 */ 'w', '2', '8', 0,
  /* 412 */ 'f', '8', 0,
  /* 415 */ 't', '8', 0,
  /* 418 */ 'w', '8', 0,
  /* 421 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '1', '6', '_', '1', '9', 0,
  /* 437 */ 'f', '1', '9', 0,
  /* 441 */ 'w', '1', '9', 0,
  /* 445 */ 'f', '2', '9', 0,
  /* 449 */ 'w', '2', '9', 0,
  /* 453 */ 'f', '9', 0,
  /* 456 */ 't', '9', 0,
  /* 459 */ 'w', '9', 0,
  /* 462 */ 'D', 'S', 'P', 'E', 'F', 'I', 0,
  /* 469 */ 'r', 'a', 0,
  /* 472 */ 'p', 'c', 0,
  /* 475 */ 'D', 'S', 'P', 'C', 'C', 'o', 'n', 'd', 0,
  /* 484 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', 0,
  /* 495 */ 'h', 'i', 0,
  /* 498 */ 'l', 'o', 0,
  /* 501 */ 'z', 'e', 'r', 'o', 0,
  /* 506 */ 'f', 'p', 0,
  /* 509 */ 'g', 'p', 0,
  /* 512 */ 's', 'p', 0,
  /* 515 */ 'D', 'S', 'P', 'P', 'o', 's', 0,
  /* 522 */ 'a', 't', 0,
  /* 525 */ 'D', 'S', 'P', 'S', 'C', 'o', 'u', 'n', 't', 0,
  /* 535 */ 'D', 'S', 'P', 'C', 'a', 'r', 'r', 'y', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    522, 475, 535, 462, 484, 515, 525, 506, 509, 152, 77, 2, 332, 266, 
    299, 213, 365, 472, 469, 512, 501, 37, 112, 179, 240, 40, 115, 182, 
    243, 522, 45, 120, 187, 248, 281, 314, 347, 380, 2, 77, 152, 213, 
    266, 299, 332, 365, 398, 435, 2, 77, 152, 213, 266, 299, 332, 365, 
    398, 435, 1, 76, 151, 212, 265, 298, 331, 364, 397, 434, 9, 84, 
    159, 220, 273, 306, 339, 372, 405, 446, 30, 105, 1, 76, 151, 212, 
    265, 298, 331, 364, 397, 434, 9, 84, 159, 220, 273, 306, 339, 372, 
    405, 446, 30, 105, 49, 191, 285, 351, 412, 0, 150, 264, 330, 396, 
    8, 158, 272, 338, 404, 29, 12, 87, 162, 223, 49, 124, 191, 252, 
    285, 318, 351, 384, 412, 453, 0, 75, 150, 211, 264, 297, 330, 363, 
    396, 437, 8, 83, 158, 219, 272, 305, 338, 371, 404, 445, 29, 104, 
    44, 119, 186, 247, 280, 313, 346, 379, 2, 77, 152, 213, 266, 299, 
    332, 365, 398, 435, 1, 76, 151, 212, 265, 298, 331, 364, 397, 434, 
    9, 84, 159, 220, 273, 306, 339, 372, 405, 446, 30, 105, 506, 49, 
    124, 191, 252, 285, 318, 351, 384, 412, 453, 0, 75, 150, 211, 264, 
    297, 330, 363, 396, 437, 8, 83, 158, 219, 272, 305, 338, 371, 404, 
    445, 29, 104, 509, 40, 115, 182, 243, 2, 77, 152, 213, 266, 299, 
    332, 365, 398, 435, 1, 76, 151, 212, 265, 298, 331, 364, 397, 434, 
    9, 84, 159, 220, 273, 306, 339, 372, 405, 446, 30, 105, 52, 127, 
    40, 115, 182, 243, 55, 130, 194, 60, 135, 199, 469, 63, 138, 202, 
    255, 288, 321, 354, 387, 512, 66, 141, 205, 258, 291, 324, 357, 390, 
    415, 456, 69, 144, 72, 147, 208, 261, 294, 327, 360, 393, 418, 459, 
    4, 79, 154, 215, 268, 301, 334, 367, 400, 441, 25, 100, 175, 236, 
    276, 309, 342, 375, 408, 449, 33, 108, 501, 37, 112, 179, 240, 40, 
    49, 124, 191, 252, 285, 318, 351, 384, 412, 453, 0, 75, 150, 211, 
    264, 297, 330, 363, 396, 437, 8, 83, 158, 219, 272, 305, 338, 371, 
    404, 445, 29, 104, 421, 495, 52, 127, 498, 63, 138, 202, 255, 288, 
    321, 354, 387, 66, 141, 205, 258, 291, 324, 357, 390, 415, 456, 69, 
    144, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case Mips_ADDu:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == Mips_ZERO) {
      // (ADDu GPR32Opnd:$dst, GPR32Opnd:$src, ZERO)
      AsmString = "move $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_BC0F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0F CC0, brtarget:$offset)
      AsmString = "bc0f $\x02";
      break;
    }
    return NULL;
  case Mips_BC0FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0FL CC0, brtarget:$offset)
      AsmString = "bc0fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC0T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0T CC0, brtarget:$offset)
      AsmString = "bc0t $\x02";
      break;
    }
    return NULL;
  case Mips_BC0TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0TL CC0, brtarget:$offset)
      AsmString = "bc0tl $\x02";
      break;
    }
    return NULL;
  case Mips_BC1F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1F FCC0, brtarget:$offset)
      AsmString = "bc1f $\x02";
      break;
    }
    return NULL;
  case Mips_BC1FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1FL FCC0, brtarget:$offset)
      AsmString = "bc1fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC1T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1T FCC0, brtarget:$offset)
      AsmString = "bc1t $\x02";
      break;
    }
    return NULL;
  case Mips_BC1TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1TL FCC0, brtarget:$offset)
      AsmString = "bc1tl $\x02";
      break;
    }
    return NULL;
  case Mips_BC2F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2F CC0, brtarget:$offset)
      AsmString = "bc2f $\x02";
      break;
    }
    return NULL;
  case Mips_BC2FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2FL CC0, brtarget:$offset)
      AsmString = "bc2fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC2T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2T CC0, brtarget:$offset)
      AsmString = "bc2t $\x02";
      break;
    }
    return NULL;
  case Mips_BC2TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2TL CC0, brtarget:$offset)
      AsmString = "bc2tl $\x02";
      break;
    }
    return NULL;
  case Mips_BC3F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3F CC0, brtarget:$offset)
      AsmString = "bc3f $\x02";
      break;
    }
    return NULL;
  case Mips_BC3FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3FL CC0, brtarget:$offset)
      AsmString = "bc3fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC3T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3T CC0, brtarget:$offset)
      AsmString = "bc3t $\x02";
      break;
    }
    return NULL;
  case Mips_BC3TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3TL CC0, brtarget:$offset)
      AsmString = "bc3tl $\x02";
      break;
    }
    return NULL;
  case Mips_BREAK:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BREAK 0, 0)
      AsmString = "break";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BREAK uimm10:$imm, 0)
      AsmString = "break $\x01";
      break;
    }
    return NULL;
  case Mips_DADDu:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == Mips_ZERO_64) {
      // (DADDu GPR64Opnd:$dst, GPR64Opnd:$src, ZERO_64)
      AsmString = "move $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_DI:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO) {
      // (DI ZERO)
      AsmString = "di";
      break;
    }
    return NULL;
  case Mips_EI:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO) {
      // (EI ZERO)
      AsmString = "ei";
      break;
    }
    return NULL;
  case Mips_JALR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1)) {
      // (JALR ZERO, GPR32Opnd:$rs)
      AsmString = "jr $\x02";
      break;
    }
    return NULL;
  case Mips_JALR64:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO_64 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR64RegClassID, 1)) {
      // (JALR64 ZERO_64, GPR64Opnd:$rs)
      AsmString = "jr $\x02";
      break;
    }
    return NULL;
  case Mips_JALR_HB:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_RA &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1)) {
      // (JALR_HB RA, GPR32Opnd:$rs)
      AsmString = "jalr.hb $\x02";
      break;
    }
    return NULL;
  case Mips_SDBBP:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SDBBP 0)
      AsmString = "sdbbp";
      break;
    }
    return NULL;
  case Mips_SDBBP_R6:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SDBBP_R6 0)
      AsmString = "sdbbp";
      break;
    }
    return NULL;
  case Mips_SLL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SLL ZERO, ZERO, 0)
      AsmString = "nop";
      break;
    }
    return NULL;
  case Mips_SUB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 2)) {
      // (SUB GPR32Opnd:$rt, ZERO, GPR32Opnd:$rs)
      AsmString = "neg $\x01, $\x03";
      break;
    }
    return NULL;
  case Mips_SUBu:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 2)) {
      // (SUBu GPR32Opnd:$rt, ZERO, GPR32Opnd:$rs)
      AsmString = "negu $\x01, $\x03";
      break;
    }
    return NULL;
  case Mips_SYNC:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SYNC 0)
      AsmString = "sync";
      break;
    }
    return NULL;
  case Mips_SYSCALL:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SYSCALL 0)
      AsmString = "syscall";
      break;
    }
    return NULL;
  case Mips_TEQ:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TEQ GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "teq $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TGE:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TGE GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tge $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TGEU:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TGEU GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tgeu $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TLT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLT GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tlt $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TLTU:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLTU GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tltu $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TNE:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TNE GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tne $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_WAIT_MM:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (WAIT_MM 0)
      AsmString = "wait";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
