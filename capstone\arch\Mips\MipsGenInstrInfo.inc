/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    Mips_PHI	= 0,
    Mips_INLINEASM	= 1,
    Mips_CFI_INSTRUCTION	= 2,
    Mips_EH_LABEL	= 3,
    Mips_GC_LABEL	= 4,
    Mips_KILL	= 5,
    Mips_EXTRACT_SUBREG	= 6,
    Mips_INSERT_SUBREG	= 7,
    Mips_IMPLICIT_DEF	= 8,
    Mips_SUBREG_TO_REG	= 9,
    Mips_COPY_TO_REGCLASS	= 10,
    Mips_DBG_VALUE	= 11,
    Mips_REG_SEQUENCE	= 12,
    Mips_COPY	= 13,
    Mips_BUNDLE	= 14,
    Mips_LIFETIME_START	= 15,
    Mips_LIFETIME_END	= 16,
    Mips_STACKMAP	= 17,
    Mips_PATCHPOINT	= 18,
    Mips_LOAD_STACK_GUARD	= 19,
    Mips_ABSQ_S_PH	= 20,
    Mips_ABSQ_S_QB	= 21,
    Mips_ABSQ_S_W	= 22,
    Mips_ADD	= 23,
    Mips_ADDIUPC	= 24,
    Mips_ADDQH_PH	= 25,
    Mips_ADDQH_R_PH	= 26,
    Mips_ADDQH_R_W	= 27,
    Mips_ADDQH_W	= 28,
    Mips_ADDQ_PH	= 29,
    Mips_ADDQ_S_PH	= 30,
    Mips_ADDQ_S_W	= 31,
    Mips_ADDSC	= 32,
    Mips_ADDS_A_B	= 33,
    Mips_ADDS_A_D	= 34,
    Mips_ADDS_A_H	= 35,
    Mips_ADDS_A_W	= 36,
    Mips_ADDS_S_B	= 37,
    Mips_ADDS_S_D	= 38,
    Mips_ADDS_S_H	= 39,
    Mips_ADDS_S_W	= 40,
    Mips_ADDS_U_B	= 41,
    Mips_ADDS_U_D	= 42,
    Mips_ADDS_U_H	= 43,
    Mips_ADDS_U_W	= 44,
    Mips_ADDUH_QB	= 45,
    Mips_ADDUH_R_QB	= 46,
    Mips_ADDU_PH	= 47,
    Mips_ADDU_QB	= 48,
    Mips_ADDU_S_PH	= 49,
    Mips_ADDU_S_QB	= 50,
    Mips_ADDVI_B	= 51,
    Mips_ADDVI_D	= 52,
    Mips_ADDVI_H	= 53,
    Mips_ADDVI_W	= 54,
    Mips_ADDV_B	= 55,
    Mips_ADDV_D	= 56,
    Mips_ADDV_H	= 57,
    Mips_ADDV_W	= 58,
    Mips_ADDWC	= 59,
    Mips_ADD_A_B	= 60,
    Mips_ADD_A_D	= 61,
    Mips_ADD_A_H	= 62,
    Mips_ADD_A_W	= 63,
    Mips_ADD_MM	= 64,
    Mips_ADDi	= 65,
    Mips_ADDi_MM	= 66,
    Mips_ADDiu	= 67,
    Mips_ADDiu_MM	= 68,
    Mips_ADDu	= 69,
    Mips_ADDu_MM	= 70,
    Mips_ADJCALLSTACKDOWN	= 71,
    Mips_ADJCALLSTACKUP	= 72,
    Mips_ALIGN	= 73,
    Mips_ALUIPC	= 74,
    Mips_AND	= 75,
    Mips_AND64	= 76,
    Mips_ANDI_B	= 77,
    Mips_AND_MM	= 78,
    Mips_AND_V	= 79,
    Mips_AND_V_D_PSEUDO	= 80,
    Mips_AND_V_H_PSEUDO	= 81,
    Mips_AND_V_W_PSEUDO	= 82,
    Mips_ANDi	= 83,
    Mips_ANDi64	= 84,
    Mips_ANDi_MM	= 85,
    Mips_APPEND	= 86,
    Mips_ASUB_S_B	= 87,
    Mips_ASUB_S_D	= 88,
    Mips_ASUB_S_H	= 89,
    Mips_ASUB_S_W	= 90,
    Mips_ASUB_U_B	= 91,
    Mips_ASUB_U_D	= 92,
    Mips_ASUB_U_H	= 93,
    Mips_ASUB_U_W	= 94,
    Mips_ATOMIC_CMP_SWAP_I16	= 95,
    Mips_ATOMIC_CMP_SWAP_I32	= 96,
    Mips_ATOMIC_CMP_SWAP_I64	= 97,
    Mips_ATOMIC_CMP_SWAP_I8	= 98,
    Mips_ATOMIC_LOAD_ADD_I16	= 99,
    Mips_ATOMIC_LOAD_ADD_I32	= 100,
    Mips_ATOMIC_LOAD_ADD_I64	= 101,
    Mips_ATOMIC_LOAD_ADD_I8	= 102,
    Mips_ATOMIC_LOAD_AND_I16	= 103,
    Mips_ATOMIC_LOAD_AND_I32	= 104,
    Mips_ATOMIC_LOAD_AND_I64	= 105,
    Mips_ATOMIC_LOAD_AND_I8	= 106,
    Mips_ATOMIC_LOAD_NAND_I16	= 107,
    Mips_ATOMIC_LOAD_NAND_I32	= 108,
    Mips_ATOMIC_LOAD_NAND_I64	= 109,
    Mips_ATOMIC_LOAD_NAND_I8	= 110,
    Mips_ATOMIC_LOAD_OR_I16	= 111,
    Mips_ATOMIC_LOAD_OR_I32	= 112,
    Mips_ATOMIC_LOAD_OR_I64	= 113,
    Mips_ATOMIC_LOAD_OR_I8	= 114,
    Mips_ATOMIC_LOAD_SUB_I16	= 115,
    Mips_ATOMIC_LOAD_SUB_I32	= 116,
    Mips_ATOMIC_LOAD_SUB_I64	= 117,
    Mips_ATOMIC_LOAD_SUB_I8	= 118,
    Mips_ATOMIC_LOAD_XOR_I16	= 119,
    Mips_ATOMIC_LOAD_XOR_I32	= 120,
    Mips_ATOMIC_LOAD_XOR_I64	= 121,
    Mips_ATOMIC_LOAD_XOR_I8	= 122,
    Mips_ATOMIC_SWAP_I16	= 123,
    Mips_ATOMIC_SWAP_I32	= 124,
    Mips_ATOMIC_SWAP_I64	= 125,
    Mips_ATOMIC_SWAP_I8	= 126,
    Mips_AUI	= 127,
    Mips_AUIPC	= 128,
    Mips_AVER_S_B	= 129,
    Mips_AVER_S_D	= 130,
    Mips_AVER_S_H	= 131,
    Mips_AVER_S_W	= 132,
    Mips_AVER_U_B	= 133,
    Mips_AVER_U_D	= 134,
    Mips_AVER_U_H	= 135,
    Mips_AVER_U_W	= 136,
    Mips_AVE_S_B	= 137,
    Mips_AVE_S_D	= 138,
    Mips_AVE_S_H	= 139,
    Mips_AVE_S_W	= 140,
    Mips_AVE_U_B	= 141,
    Mips_AVE_U_D	= 142,
    Mips_AVE_U_H	= 143,
    Mips_AVE_U_W	= 144,
    Mips_AddiuRxImmX16	= 145,
    Mips_AddiuRxPcImmX16	= 146,
    Mips_AddiuRxRxImm16	= 147,
    Mips_AddiuRxRxImmX16	= 148,
    Mips_AddiuRxRyOffMemX16	= 149,
    Mips_AddiuSpImm16	= 150,
    Mips_AddiuSpImmX16	= 151,
    Mips_AdduRxRyRz16	= 152,
    Mips_AndRxRxRy16	= 153,
    Mips_B	= 154,
    Mips_BADDu	= 155,
    Mips_BAL	= 156,
    Mips_BALC	= 157,
    Mips_BALIGN	= 158,
    Mips_BAL_BR	= 159,
    Mips_BC	= 160,
    Mips_BC0F	= 161,
    Mips_BC0FL	= 162,
    Mips_BC0T	= 163,
    Mips_BC0TL	= 164,
    Mips_BC1EQZ	= 165,
    Mips_BC1F	= 166,
    Mips_BC1FL	= 167,
    Mips_BC1F_MM	= 168,
    Mips_BC1NEZ	= 169,
    Mips_BC1T	= 170,
    Mips_BC1TL	= 171,
    Mips_BC1T_MM	= 172,
    Mips_BC2EQZ	= 173,
    Mips_BC2F	= 174,
    Mips_BC2FL	= 175,
    Mips_BC2NEZ	= 176,
    Mips_BC2T	= 177,
    Mips_BC2TL	= 178,
    Mips_BC3F	= 179,
    Mips_BC3FL	= 180,
    Mips_BC3T	= 181,
    Mips_BC3TL	= 182,
    Mips_BCLRI_B	= 183,
    Mips_BCLRI_D	= 184,
    Mips_BCLRI_H	= 185,
    Mips_BCLRI_W	= 186,
    Mips_BCLR_B	= 187,
    Mips_BCLR_D	= 188,
    Mips_BCLR_H	= 189,
    Mips_BCLR_W	= 190,
    Mips_BEQ	= 191,
    Mips_BEQ64	= 192,
    Mips_BEQC	= 193,
    Mips_BEQL	= 194,
    Mips_BEQZALC	= 195,
    Mips_BEQZC	= 196,
    Mips_BEQZC_MM	= 197,
    Mips_BEQ_MM	= 198,
    Mips_BGEC	= 199,
    Mips_BGEUC	= 200,
    Mips_BGEZ	= 201,
    Mips_BGEZ64	= 202,
    Mips_BGEZAL	= 203,
    Mips_BGEZALC	= 204,
    Mips_BGEZALL	= 205,
    Mips_BGEZALS_MM	= 206,
    Mips_BGEZAL_MM	= 207,
    Mips_BGEZC	= 208,
    Mips_BGEZL	= 209,
    Mips_BGEZ_MM	= 210,
    Mips_BGTZ	= 211,
    Mips_BGTZ64	= 212,
    Mips_BGTZALC	= 213,
    Mips_BGTZC	= 214,
    Mips_BGTZL	= 215,
    Mips_BGTZ_MM	= 216,
    Mips_BINSLI_B	= 217,
    Mips_BINSLI_D	= 218,
    Mips_BINSLI_H	= 219,
    Mips_BINSLI_W	= 220,
    Mips_BINSL_B	= 221,
    Mips_BINSL_D	= 222,
    Mips_BINSL_H	= 223,
    Mips_BINSL_W	= 224,
    Mips_BINSRI_B	= 225,
    Mips_BINSRI_D	= 226,
    Mips_BINSRI_H	= 227,
    Mips_BINSRI_W	= 228,
    Mips_BINSR_B	= 229,
    Mips_BINSR_D	= 230,
    Mips_BINSR_H	= 231,
    Mips_BINSR_W	= 232,
    Mips_BITREV	= 233,
    Mips_BITSWAP	= 234,
    Mips_BLEZ	= 235,
    Mips_BLEZ64	= 236,
    Mips_BLEZALC	= 237,
    Mips_BLEZC	= 238,
    Mips_BLEZL	= 239,
    Mips_BLEZ_MM	= 240,
    Mips_BLTC	= 241,
    Mips_BLTUC	= 242,
    Mips_BLTZ	= 243,
    Mips_BLTZ64	= 244,
    Mips_BLTZAL	= 245,
    Mips_BLTZALC	= 246,
    Mips_BLTZALL	= 247,
    Mips_BLTZALS_MM	= 248,
    Mips_BLTZAL_MM	= 249,
    Mips_BLTZC	= 250,
    Mips_BLTZL	= 251,
    Mips_BLTZ_MM	= 252,
    Mips_BMNZI_B	= 253,
    Mips_BMNZ_V	= 254,
    Mips_BMZI_B	= 255,
    Mips_BMZ_V	= 256,
    Mips_BNE	= 257,
    Mips_BNE64	= 258,
    Mips_BNEC	= 259,
    Mips_BNEGI_B	= 260,
    Mips_BNEGI_D	= 261,
    Mips_BNEGI_H	= 262,
    Mips_BNEGI_W	= 263,
    Mips_BNEG_B	= 264,
    Mips_BNEG_D	= 265,
    Mips_BNEG_H	= 266,
    Mips_BNEG_W	= 267,
    Mips_BNEL	= 268,
    Mips_BNEZALC	= 269,
    Mips_BNEZC	= 270,
    Mips_BNEZC_MM	= 271,
    Mips_BNE_MM	= 272,
    Mips_BNVC	= 273,
    Mips_BNZ_B	= 274,
    Mips_BNZ_D	= 275,
    Mips_BNZ_H	= 276,
    Mips_BNZ_V	= 277,
    Mips_BNZ_W	= 278,
    Mips_BOVC	= 279,
    Mips_BPOSGE32	= 280,
    Mips_BPOSGE32_PSEUDO	= 281,
    Mips_BREAK	= 282,
    Mips_BREAK_MM	= 283,
    Mips_BSELI_B	= 284,
    Mips_BSEL_D_PSEUDO	= 285,
    Mips_BSEL_FD_PSEUDO	= 286,
    Mips_BSEL_FW_PSEUDO	= 287,
    Mips_BSEL_H_PSEUDO	= 288,
    Mips_BSEL_V	= 289,
    Mips_BSEL_W_PSEUDO	= 290,
    Mips_BSETI_B	= 291,
    Mips_BSETI_D	= 292,
    Mips_BSETI_H	= 293,
    Mips_BSETI_W	= 294,
    Mips_BSET_B	= 295,
    Mips_BSET_D	= 296,
    Mips_BSET_H	= 297,
    Mips_BSET_W	= 298,
    Mips_BZ_B	= 299,
    Mips_BZ_D	= 300,
    Mips_BZ_H	= 301,
    Mips_BZ_V	= 302,
    Mips_BZ_W	= 303,
    Mips_BeqzRxImm16	= 304,
    Mips_BeqzRxImmX16	= 305,
    Mips_Bimm16	= 306,
    Mips_BimmX16	= 307,
    Mips_BnezRxImm16	= 308,
    Mips_BnezRxImmX16	= 309,
    Mips_Break16	= 310,
    Mips_Bteqz16	= 311,
    Mips_BteqzT8CmpX16	= 312,
    Mips_BteqzT8CmpiX16	= 313,
    Mips_BteqzT8SltX16	= 314,
    Mips_BteqzT8SltiX16	= 315,
    Mips_BteqzT8SltiuX16	= 316,
    Mips_BteqzT8SltuX16	= 317,
    Mips_BteqzX16	= 318,
    Mips_Btnez16	= 319,
    Mips_BtnezT8CmpX16	= 320,
    Mips_BtnezT8CmpiX16	= 321,
    Mips_BtnezT8SltX16	= 322,
    Mips_BtnezT8SltiX16	= 323,
    Mips_BtnezT8SltiuX16	= 324,
    Mips_BtnezT8SltuX16	= 325,
    Mips_BtnezX16	= 326,
    Mips_BuildPairF64	= 327,
    Mips_BuildPairF64_64	= 328,
    Mips_CACHE	= 329,
    Mips_CACHE_R6	= 330,
    Mips_CEIL_L_D64	= 331,
    Mips_CEIL_L_S	= 332,
    Mips_CEIL_W_D32	= 333,
    Mips_CEIL_W_D64	= 334,
    Mips_CEIL_W_MM	= 335,
    Mips_CEIL_W_S	= 336,
    Mips_CEIL_W_S_MM	= 337,
    Mips_CEQI_B	= 338,
    Mips_CEQI_D	= 339,
    Mips_CEQI_H	= 340,
    Mips_CEQI_W	= 341,
    Mips_CEQ_B	= 342,
    Mips_CEQ_D	= 343,
    Mips_CEQ_H	= 344,
    Mips_CEQ_W	= 345,
    Mips_CFC1	= 346,
    Mips_CFC1_MM	= 347,
    Mips_CFCMSA	= 348,
    Mips_CINS	= 349,
    Mips_CINS32	= 350,
    Mips_CLASS_D	= 351,
    Mips_CLASS_S	= 352,
    Mips_CLEI_S_B	= 353,
    Mips_CLEI_S_D	= 354,
    Mips_CLEI_S_H	= 355,
    Mips_CLEI_S_W	= 356,
    Mips_CLEI_U_B	= 357,
    Mips_CLEI_U_D	= 358,
    Mips_CLEI_U_H	= 359,
    Mips_CLEI_U_W	= 360,
    Mips_CLE_S_B	= 361,
    Mips_CLE_S_D	= 362,
    Mips_CLE_S_H	= 363,
    Mips_CLE_S_W	= 364,
    Mips_CLE_U_B	= 365,
    Mips_CLE_U_D	= 366,
    Mips_CLE_U_H	= 367,
    Mips_CLE_U_W	= 368,
    Mips_CLO	= 369,
    Mips_CLO_MM	= 370,
    Mips_CLO_R6	= 371,
    Mips_CLTI_S_B	= 372,
    Mips_CLTI_S_D	= 373,
    Mips_CLTI_S_H	= 374,
    Mips_CLTI_S_W	= 375,
    Mips_CLTI_U_B	= 376,
    Mips_CLTI_U_D	= 377,
    Mips_CLTI_U_H	= 378,
    Mips_CLTI_U_W	= 379,
    Mips_CLT_S_B	= 380,
    Mips_CLT_S_D	= 381,
    Mips_CLT_S_H	= 382,
    Mips_CLT_S_W	= 383,
    Mips_CLT_U_B	= 384,
    Mips_CLT_U_D	= 385,
    Mips_CLT_U_H	= 386,
    Mips_CLT_U_W	= 387,
    Mips_CLZ	= 388,
    Mips_CLZ_MM	= 389,
    Mips_CLZ_R6	= 390,
    Mips_CMPGDU_EQ_QB	= 391,
    Mips_CMPGDU_LE_QB	= 392,
    Mips_CMPGDU_LT_QB	= 393,
    Mips_CMPGU_EQ_QB	= 394,
    Mips_CMPGU_LE_QB	= 395,
    Mips_CMPGU_LT_QB	= 396,
    Mips_CMPU_EQ_QB	= 397,
    Mips_CMPU_LE_QB	= 398,
    Mips_CMPU_LT_QB	= 399,
    Mips_CMP_EQ_D	= 400,
    Mips_CMP_EQ_PH	= 401,
    Mips_CMP_EQ_S	= 402,
    Mips_CMP_F_D	= 403,
    Mips_CMP_F_S	= 404,
    Mips_CMP_LE_D	= 405,
    Mips_CMP_LE_PH	= 406,
    Mips_CMP_LE_S	= 407,
    Mips_CMP_LT_D	= 408,
    Mips_CMP_LT_PH	= 409,
    Mips_CMP_LT_S	= 410,
    Mips_CMP_SAF_D	= 411,
    Mips_CMP_SAF_S	= 412,
    Mips_CMP_SEQ_D	= 413,
    Mips_CMP_SEQ_S	= 414,
    Mips_CMP_SLE_D	= 415,
    Mips_CMP_SLE_S	= 416,
    Mips_CMP_SLT_D	= 417,
    Mips_CMP_SLT_S	= 418,
    Mips_CMP_SUEQ_D	= 419,
    Mips_CMP_SUEQ_S	= 420,
    Mips_CMP_SULE_D	= 421,
    Mips_CMP_SULE_S	= 422,
    Mips_CMP_SULT_D	= 423,
    Mips_CMP_SULT_S	= 424,
    Mips_CMP_SUN_D	= 425,
    Mips_CMP_SUN_S	= 426,
    Mips_CMP_UEQ_D	= 427,
    Mips_CMP_UEQ_S	= 428,
    Mips_CMP_ULE_D	= 429,
    Mips_CMP_ULE_S	= 430,
    Mips_CMP_ULT_D	= 431,
    Mips_CMP_ULT_S	= 432,
    Mips_CMP_UN_D	= 433,
    Mips_CMP_UN_S	= 434,
    Mips_CONSTPOOL_ENTRY	= 435,
    Mips_COPY_FD_PSEUDO	= 436,
    Mips_COPY_FW_PSEUDO	= 437,
    Mips_COPY_S_B	= 438,
    Mips_COPY_S_D	= 439,
    Mips_COPY_S_H	= 440,
    Mips_COPY_S_W	= 441,
    Mips_COPY_U_B	= 442,
    Mips_COPY_U_D	= 443,
    Mips_COPY_U_H	= 444,
    Mips_COPY_U_W	= 445,
    Mips_CTC1	= 446,
    Mips_CTC1_MM	= 447,
    Mips_CTCMSA	= 448,
    Mips_CVT_D32_S	= 449,
    Mips_CVT_D32_W	= 450,
    Mips_CVT_D32_W_MM	= 451,
    Mips_CVT_D64_L	= 452,
    Mips_CVT_D64_S	= 453,
    Mips_CVT_D64_W	= 454,
    Mips_CVT_D_S_MM	= 455,
    Mips_CVT_L_D64	= 456,
    Mips_CVT_L_D64_MM	= 457,
    Mips_CVT_L_S	= 458,
    Mips_CVT_L_S_MM	= 459,
    Mips_CVT_S_D32	= 460,
    Mips_CVT_S_D32_MM	= 461,
    Mips_CVT_S_D64	= 462,
    Mips_CVT_S_L	= 463,
    Mips_CVT_S_W	= 464,
    Mips_CVT_S_W_MM	= 465,
    Mips_CVT_W_D32	= 466,
    Mips_CVT_W_D64	= 467,
    Mips_CVT_W_MM	= 468,
    Mips_CVT_W_S	= 469,
    Mips_CVT_W_S_MM	= 470,
    Mips_C_EQ_D32	= 471,
    Mips_C_EQ_D64	= 472,
    Mips_C_EQ_S	= 473,
    Mips_C_F_D32	= 474,
    Mips_C_F_D64	= 475,
    Mips_C_F_S	= 476,
    Mips_C_LE_D32	= 477,
    Mips_C_LE_D64	= 478,
    Mips_C_LE_S	= 479,
    Mips_C_LT_D32	= 480,
    Mips_C_LT_D64	= 481,
    Mips_C_LT_S	= 482,
    Mips_C_NGE_D32	= 483,
    Mips_C_NGE_D64	= 484,
    Mips_C_NGE_S	= 485,
    Mips_C_NGLE_D32	= 486,
    Mips_C_NGLE_D64	= 487,
    Mips_C_NGLE_S	= 488,
    Mips_C_NGL_D32	= 489,
    Mips_C_NGL_D64	= 490,
    Mips_C_NGL_S	= 491,
    Mips_C_NGT_D32	= 492,
    Mips_C_NGT_D64	= 493,
    Mips_C_NGT_S	= 494,
    Mips_C_OLE_D32	= 495,
    Mips_C_OLE_D64	= 496,
    Mips_C_OLE_S	= 497,
    Mips_C_OLT_D32	= 498,
    Mips_C_OLT_D64	= 499,
    Mips_C_OLT_S	= 500,
    Mips_C_SEQ_D32	= 501,
    Mips_C_SEQ_D64	= 502,
    Mips_C_SEQ_S	= 503,
    Mips_C_SF_D32	= 504,
    Mips_C_SF_D64	= 505,
    Mips_C_SF_S	= 506,
    Mips_C_UEQ_D32	= 507,
    Mips_C_UEQ_D64	= 508,
    Mips_C_UEQ_S	= 509,
    Mips_C_ULE_D32	= 510,
    Mips_C_ULE_D64	= 511,
    Mips_C_ULE_S	= 512,
    Mips_C_ULT_D32	= 513,
    Mips_C_ULT_D64	= 514,
    Mips_C_ULT_S	= 515,
    Mips_C_UN_D32	= 516,
    Mips_C_UN_D64	= 517,
    Mips_C_UN_S	= 518,
    Mips_CmpRxRy16	= 519,
    Mips_CmpiRxImm16	= 520,
    Mips_CmpiRxImmX16	= 521,
    Mips_Constant32	= 522,
    Mips_DADD	= 523,
    Mips_DADDi	= 524,
    Mips_DADDiu	= 525,
    Mips_DADDu	= 526,
    Mips_DAHI	= 527,
    Mips_DALIGN	= 528,
    Mips_DATI	= 529,
    Mips_DAUI	= 530,
    Mips_DBITSWAP	= 531,
    Mips_DCLO	= 532,
    Mips_DCLO_R6	= 533,
    Mips_DCLZ	= 534,
    Mips_DCLZ_R6	= 535,
    Mips_DDIV	= 536,
    Mips_DDIVU	= 537,
    Mips_DERET	= 538,
    Mips_DERET_MM	= 539,
    Mips_DEXT	= 540,
    Mips_DEXTM	= 541,
    Mips_DEXTU	= 542,
    Mips_DI	= 543,
    Mips_DINS	= 544,
    Mips_DINSM	= 545,
    Mips_DINSU	= 546,
    Mips_DIV	= 547,
    Mips_DIVU	= 548,
    Mips_DIV_S_B	= 549,
    Mips_DIV_S_D	= 550,
    Mips_DIV_S_H	= 551,
    Mips_DIV_S_W	= 552,
    Mips_DIV_U_B	= 553,
    Mips_DIV_U_D	= 554,
    Mips_DIV_U_H	= 555,
    Mips_DIV_U_W	= 556,
    Mips_DI_MM	= 557,
    Mips_DLSA	= 558,
    Mips_DLSA_R6	= 559,
    Mips_DMFC0	= 560,
    Mips_DMFC1	= 561,
    Mips_DMFC2	= 562,
    Mips_DMOD	= 563,
    Mips_DMODU	= 564,
    Mips_DMTC0	= 565,
    Mips_DMTC1	= 566,
    Mips_DMTC2	= 567,
    Mips_DMUH	= 568,
    Mips_DMUHU	= 569,
    Mips_DMUL	= 570,
    Mips_DMULT	= 571,
    Mips_DMULTu	= 572,
    Mips_DMULU	= 573,
    Mips_DMUL_R6	= 574,
    Mips_DOTP_S_D	= 575,
    Mips_DOTP_S_H	= 576,
    Mips_DOTP_S_W	= 577,
    Mips_DOTP_U_D	= 578,
    Mips_DOTP_U_H	= 579,
    Mips_DOTP_U_W	= 580,
    Mips_DPADD_S_D	= 581,
    Mips_DPADD_S_H	= 582,
    Mips_DPADD_S_W	= 583,
    Mips_DPADD_U_D	= 584,
    Mips_DPADD_U_H	= 585,
    Mips_DPADD_U_W	= 586,
    Mips_DPAQX_SA_W_PH	= 587,
    Mips_DPAQX_S_W_PH	= 588,
    Mips_DPAQ_SA_L_W	= 589,
    Mips_DPAQ_S_W_PH	= 590,
    Mips_DPAU_H_QBL	= 591,
    Mips_DPAU_H_QBR	= 592,
    Mips_DPAX_W_PH	= 593,
    Mips_DPA_W_PH	= 594,
    Mips_DPOP	= 595,
    Mips_DPSQX_SA_W_PH	= 596,
    Mips_DPSQX_S_W_PH	= 597,
    Mips_DPSQ_SA_L_W	= 598,
    Mips_DPSQ_S_W_PH	= 599,
    Mips_DPSUB_S_D	= 600,
    Mips_DPSUB_S_H	= 601,
    Mips_DPSUB_S_W	= 602,
    Mips_DPSUB_U_D	= 603,
    Mips_DPSUB_U_H	= 604,
    Mips_DPSUB_U_W	= 605,
    Mips_DPSU_H_QBL	= 606,
    Mips_DPSU_H_QBR	= 607,
    Mips_DPSX_W_PH	= 608,
    Mips_DPS_W_PH	= 609,
    Mips_DROTR	= 610,
    Mips_DROTR32	= 611,
    Mips_DROTRV	= 612,
    Mips_DSBH	= 613,
    Mips_DSDIV	= 614,
    Mips_DSHD	= 615,
    Mips_DSLL	= 616,
    Mips_DSLL32	= 617,
    Mips_DSLL64_32	= 618,
    Mips_DSLLV	= 619,
    Mips_DSRA	= 620,
    Mips_DSRA32	= 621,
    Mips_DSRAV	= 622,
    Mips_DSRL	= 623,
    Mips_DSRL32	= 624,
    Mips_DSRLV	= 625,
    Mips_DSUB	= 626,
    Mips_DSUBu	= 627,
    Mips_DUDIV	= 628,
    Mips_DivRxRy16	= 629,
    Mips_DivuRxRy16	= 630,
    Mips_EHB	= 631,
    Mips_EI	= 632,
    Mips_EI_MM	= 633,
    Mips_ERET	= 634,
    Mips_ERET_MM	= 635,
    Mips_EXT	= 636,
    Mips_EXTP	= 637,
    Mips_EXTPDP	= 638,
    Mips_EXTPDPV	= 639,
    Mips_EXTPV	= 640,
    Mips_EXTRV_RS_W	= 641,
    Mips_EXTRV_R_W	= 642,
    Mips_EXTRV_S_H	= 643,
    Mips_EXTRV_W	= 644,
    Mips_EXTR_RS_W	= 645,
    Mips_EXTR_R_W	= 646,
    Mips_EXTR_S_H	= 647,
    Mips_EXTR_W	= 648,
    Mips_EXTS	= 649,
    Mips_EXTS32	= 650,
    Mips_EXT_MM	= 651,
    Mips_ExtractElementF64	= 652,
    Mips_ExtractElementF64_64	= 653,
    Mips_FABS_D	= 654,
    Mips_FABS_D32	= 655,
    Mips_FABS_D64	= 656,
    Mips_FABS_MM	= 657,
    Mips_FABS_S	= 658,
    Mips_FABS_S_MM	= 659,
    Mips_FABS_W	= 660,
    Mips_FADD_D	= 661,
    Mips_FADD_D32	= 662,
    Mips_FADD_D64	= 663,
    Mips_FADD_MM	= 664,
    Mips_FADD_S	= 665,
    Mips_FADD_S_MM	= 666,
    Mips_FADD_W	= 667,
    Mips_FCAF_D	= 668,
    Mips_FCAF_W	= 669,
    Mips_FCEQ_D	= 670,
    Mips_FCEQ_W	= 671,
    Mips_FCLASS_D	= 672,
    Mips_FCLASS_W	= 673,
    Mips_FCLE_D	= 674,
    Mips_FCLE_W	= 675,
    Mips_FCLT_D	= 676,
    Mips_FCLT_W	= 677,
    Mips_FCMP_D32	= 678,
    Mips_FCMP_D32_MM	= 679,
    Mips_FCMP_D64	= 680,
    Mips_FCMP_S32	= 681,
    Mips_FCMP_S32_MM	= 682,
    Mips_FCNE_D	= 683,
    Mips_FCNE_W	= 684,
    Mips_FCOR_D	= 685,
    Mips_FCOR_W	= 686,
    Mips_FCUEQ_D	= 687,
    Mips_FCUEQ_W	= 688,
    Mips_FCULE_D	= 689,
    Mips_FCULE_W	= 690,
    Mips_FCULT_D	= 691,
    Mips_FCULT_W	= 692,
    Mips_FCUNE_D	= 693,
    Mips_FCUNE_W	= 694,
    Mips_FCUN_D	= 695,
    Mips_FCUN_W	= 696,
    Mips_FDIV_D	= 697,
    Mips_FDIV_D32	= 698,
    Mips_FDIV_D64	= 699,
    Mips_FDIV_MM	= 700,
    Mips_FDIV_S	= 701,
    Mips_FDIV_S_MM	= 702,
    Mips_FDIV_W	= 703,
    Mips_FEXDO_H	= 704,
    Mips_FEXDO_W	= 705,
    Mips_FEXP2_D	= 706,
    Mips_FEXP2_D_1_PSEUDO	= 707,
    Mips_FEXP2_W	= 708,
    Mips_FEXP2_W_1_PSEUDO	= 709,
    Mips_FEXUPL_D	= 710,
    Mips_FEXUPL_W	= 711,
    Mips_FEXUPR_D	= 712,
    Mips_FEXUPR_W	= 713,
    Mips_FFINT_S_D	= 714,
    Mips_FFINT_S_W	= 715,
    Mips_FFINT_U_D	= 716,
    Mips_FFINT_U_W	= 717,
    Mips_FFQL_D	= 718,
    Mips_FFQL_W	= 719,
    Mips_FFQR_D	= 720,
    Mips_FFQR_W	= 721,
    Mips_FILL_B	= 722,
    Mips_FILL_D	= 723,
    Mips_FILL_FD_PSEUDO	= 724,
    Mips_FILL_FW_PSEUDO	= 725,
    Mips_FILL_H	= 726,
    Mips_FILL_W	= 727,
    Mips_FLOG2_D	= 728,
    Mips_FLOG2_W	= 729,
    Mips_FLOOR_L_D64	= 730,
    Mips_FLOOR_L_S	= 731,
    Mips_FLOOR_W_D32	= 732,
    Mips_FLOOR_W_D64	= 733,
    Mips_FLOOR_W_MM	= 734,
    Mips_FLOOR_W_S	= 735,
    Mips_FLOOR_W_S_MM	= 736,
    Mips_FMADD_D	= 737,
    Mips_FMADD_W	= 738,
    Mips_FMAX_A_D	= 739,
    Mips_FMAX_A_W	= 740,
    Mips_FMAX_D	= 741,
    Mips_FMAX_W	= 742,
    Mips_FMIN_A_D	= 743,
    Mips_FMIN_A_W	= 744,
    Mips_FMIN_D	= 745,
    Mips_FMIN_W	= 746,
    Mips_FMOV_D32	= 747,
    Mips_FMOV_D32_MM	= 748,
    Mips_FMOV_D64	= 749,
    Mips_FMOV_S	= 750,
    Mips_FMOV_S_MM	= 751,
    Mips_FMSUB_D	= 752,
    Mips_FMSUB_W	= 753,
    Mips_FMUL_D	= 754,
    Mips_FMUL_D32	= 755,
    Mips_FMUL_D64	= 756,
    Mips_FMUL_MM	= 757,
    Mips_FMUL_S	= 758,
    Mips_FMUL_S_MM	= 759,
    Mips_FMUL_W	= 760,
    Mips_FNEG_D32	= 761,
    Mips_FNEG_D64	= 762,
    Mips_FNEG_MM	= 763,
    Mips_FNEG_S	= 764,
    Mips_FNEG_S_MM	= 765,
    Mips_FRCP_D	= 766,
    Mips_FRCP_W	= 767,
    Mips_FRINT_D	= 768,
    Mips_FRINT_W	= 769,
    Mips_FRSQRT_D	= 770,
    Mips_FRSQRT_W	= 771,
    Mips_FSAF_D	= 772,
    Mips_FSAF_W	= 773,
    Mips_FSEQ_D	= 774,
    Mips_FSEQ_W	= 775,
    Mips_FSLE_D	= 776,
    Mips_FSLE_W	= 777,
    Mips_FSLT_D	= 778,
    Mips_FSLT_W	= 779,
    Mips_FSNE_D	= 780,
    Mips_FSNE_W	= 781,
    Mips_FSOR_D	= 782,
    Mips_FSOR_W	= 783,
    Mips_FSQRT_D	= 784,
    Mips_FSQRT_D32	= 785,
    Mips_FSQRT_D64	= 786,
    Mips_FSQRT_MM	= 787,
    Mips_FSQRT_S	= 788,
    Mips_FSQRT_S_MM	= 789,
    Mips_FSQRT_W	= 790,
    Mips_FSUB_D	= 791,
    Mips_FSUB_D32	= 792,
    Mips_FSUB_D64	= 793,
    Mips_FSUB_MM	= 794,
    Mips_FSUB_S	= 795,
    Mips_FSUB_S_MM	= 796,
    Mips_FSUB_W	= 797,
    Mips_FSUEQ_D	= 798,
    Mips_FSUEQ_W	= 799,
    Mips_FSULE_D	= 800,
    Mips_FSULE_W	= 801,
    Mips_FSULT_D	= 802,
    Mips_FSULT_W	= 803,
    Mips_FSUNE_D	= 804,
    Mips_FSUNE_W	= 805,
    Mips_FSUN_D	= 806,
    Mips_FSUN_W	= 807,
    Mips_FTINT_S_D	= 808,
    Mips_FTINT_S_W	= 809,
    Mips_FTINT_U_D	= 810,
    Mips_FTINT_U_W	= 811,
    Mips_FTQ_H	= 812,
    Mips_FTQ_W	= 813,
    Mips_FTRUNC_S_D	= 814,
    Mips_FTRUNC_S_W	= 815,
    Mips_FTRUNC_U_D	= 816,
    Mips_FTRUNC_U_W	= 817,
    Mips_GotPrologue16	= 818,
    Mips_HADD_S_D	= 819,
    Mips_HADD_S_H	= 820,
    Mips_HADD_S_W	= 821,
    Mips_HADD_U_D	= 822,
    Mips_HADD_U_H	= 823,
    Mips_HADD_U_W	= 824,
    Mips_HSUB_S_D	= 825,
    Mips_HSUB_S_H	= 826,
    Mips_HSUB_S_W	= 827,
    Mips_HSUB_U_D	= 828,
    Mips_HSUB_U_H	= 829,
    Mips_HSUB_U_W	= 830,
    Mips_ILVEV_B	= 831,
    Mips_ILVEV_D	= 832,
    Mips_ILVEV_H	= 833,
    Mips_ILVEV_W	= 834,
    Mips_ILVL_B	= 835,
    Mips_ILVL_D	= 836,
    Mips_ILVL_H	= 837,
    Mips_ILVL_W	= 838,
    Mips_ILVOD_B	= 839,
    Mips_ILVOD_D	= 840,
    Mips_ILVOD_H	= 841,
    Mips_ILVOD_W	= 842,
    Mips_ILVR_B	= 843,
    Mips_ILVR_D	= 844,
    Mips_ILVR_H	= 845,
    Mips_ILVR_W	= 846,
    Mips_INS	= 847,
    Mips_INSERT_B	= 848,
    Mips_INSERT_B_VIDX_PSEUDO	= 849,
    Mips_INSERT_D	= 850,
    Mips_INSERT_D_VIDX_PSEUDO	= 851,
    Mips_INSERT_FD_PSEUDO	= 852,
    Mips_INSERT_FD_VIDX_PSEUDO	= 853,
    Mips_INSERT_FW_PSEUDO	= 854,
    Mips_INSERT_FW_VIDX_PSEUDO	= 855,
    Mips_INSERT_H	= 856,
    Mips_INSERT_H_VIDX_PSEUDO	= 857,
    Mips_INSERT_W	= 858,
    Mips_INSERT_W_VIDX_PSEUDO	= 859,
    Mips_INSV	= 860,
    Mips_INSVE_B	= 861,
    Mips_INSVE_D	= 862,
    Mips_INSVE_H	= 863,
    Mips_INSVE_W	= 864,
    Mips_INS_MM	= 865,
    Mips_J	= 866,
    Mips_JAL	= 867,
    Mips_JALR	= 868,
    Mips_JALR16_MM	= 869,
    Mips_JALR64	= 870,
    Mips_JALR64Pseudo	= 871,
    Mips_JALRPseudo	= 872,
    Mips_JALRS_MM	= 873,
    Mips_JALR_HB	= 874,
    Mips_JALR_MM	= 875,
    Mips_JALS_MM	= 876,
    Mips_JALX	= 877,
    Mips_JAL_MM	= 878,
    Mips_JIALC	= 879,
    Mips_JIC	= 880,
    Mips_JR	= 881,
    Mips_JR64	= 882,
    Mips_JRADDIUSP	= 883,
    Mips_JR_HB	= 884,
    Mips_JR_HB_R6	= 885,
    Mips_JR_MM	= 886,
    Mips_J_MM	= 887,
    Mips_Jal16	= 888,
    Mips_JalB16	= 889,
    Mips_JrRa16	= 890,
    Mips_JrcRa16	= 891,
    Mips_JrcRx16	= 892,
    Mips_JumpLinkReg16	= 893,
    Mips_LB	= 894,
    Mips_LB64	= 895,
    Mips_LBUX	= 896,
    Mips_LB_MM	= 897,
    Mips_LBu	= 898,
    Mips_LBu64	= 899,
    Mips_LBu_MM	= 900,
    Mips_LD	= 901,
    Mips_LDC1	= 902,
    Mips_LDC164	= 903,
    Mips_LDC1_MM	= 904,
    Mips_LDC2	= 905,
    Mips_LDC2_R6	= 906,
    Mips_LDC3	= 907,
    Mips_LDI_B	= 908,
    Mips_LDI_D	= 909,
    Mips_LDI_H	= 910,
    Mips_LDI_W	= 911,
    Mips_LDL	= 912,
    Mips_LDPC	= 913,
    Mips_LDR	= 914,
    Mips_LDXC1	= 915,
    Mips_LDXC164	= 916,
    Mips_LD_B	= 917,
    Mips_LD_D	= 918,
    Mips_LD_H	= 919,
    Mips_LD_W	= 920,
    Mips_LEA_ADDiu	= 921,
    Mips_LEA_ADDiu64	= 922,
    Mips_LEA_ADDiu_MM	= 923,
    Mips_LH	= 924,
    Mips_LH64	= 925,
    Mips_LHX	= 926,
    Mips_LH_MM	= 927,
    Mips_LHu	= 928,
    Mips_LHu64	= 929,
    Mips_LHu_MM	= 930,
    Mips_LL	= 931,
    Mips_LLD	= 932,
    Mips_LLD_R6	= 933,
    Mips_LL_MM	= 934,
    Mips_LL_R6	= 935,
    Mips_LOAD_ACC128	= 936,
    Mips_LOAD_ACC64	= 937,
    Mips_LOAD_ACC64DSP	= 938,
    Mips_LOAD_CCOND_DSP	= 939,
    Mips_LONG_BRANCH_ADDiu	= 940,
    Mips_LONG_BRANCH_DADDiu	= 941,
    Mips_LONG_BRANCH_LUi	= 942,
    Mips_LSA	= 943,
    Mips_LSA_R6	= 944,
    Mips_LUXC1	= 945,
    Mips_LUXC164	= 946,
    Mips_LUXC1_MM	= 947,
    Mips_LUi	= 948,
    Mips_LUi64	= 949,
    Mips_LUi_MM	= 950,
    Mips_LW	= 951,
    Mips_LW64	= 952,
    Mips_LWC1	= 953,
    Mips_LWC1_MM	= 954,
    Mips_LWC2	= 955,
    Mips_LWC2_R6	= 956,
    Mips_LWC3	= 957,
    Mips_LWL	= 958,
    Mips_LWL64	= 959,
    Mips_LWL_MM	= 960,
    Mips_LWPC	= 961,
    Mips_LWR	= 962,
    Mips_LWR64	= 963,
    Mips_LWR_MM	= 964,
    Mips_LWUPC	= 965,
    Mips_LWU_MM	= 966,
    Mips_LWX	= 967,
    Mips_LWXC1	= 968,
    Mips_LWXC1_MM	= 969,
    Mips_LW_MM	= 970,
    Mips_LWu	= 971,
    Mips_LbRxRyOffMemX16	= 972,
    Mips_LbuRxRyOffMemX16	= 973,
    Mips_LhRxRyOffMemX16	= 974,
    Mips_LhuRxRyOffMemX16	= 975,
    Mips_LiRxImm16	= 976,
    Mips_LiRxImmAlignX16	= 977,
    Mips_LiRxImmX16	= 978,
    Mips_LoadAddr32Imm	= 979,
    Mips_LoadAddr32Reg	= 980,
    Mips_LoadImm32Reg	= 981,
    Mips_LoadImm64Reg	= 982,
    Mips_LwConstant32	= 983,
    Mips_LwRxPcTcp16	= 984,
    Mips_LwRxPcTcpX16	= 985,
    Mips_LwRxRyOffMemX16	= 986,
    Mips_LwRxSpImmX16	= 987,
    Mips_MADD	= 988,
    Mips_MADDF_D	= 989,
    Mips_MADDF_S	= 990,
    Mips_MADDR_Q_H	= 991,
    Mips_MADDR_Q_W	= 992,
    Mips_MADDU	= 993,
    Mips_MADDU_DSP	= 994,
    Mips_MADDU_MM	= 995,
    Mips_MADDV_B	= 996,
    Mips_MADDV_D	= 997,
    Mips_MADDV_H	= 998,
    Mips_MADDV_W	= 999,
    Mips_MADD_D32	= 1000,
    Mips_MADD_D32_MM	= 1001,
    Mips_MADD_D64	= 1002,
    Mips_MADD_DSP	= 1003,
    Mips_MADD_MM	= 1004,
    Mips_MADD_Q_H	= 1005,
    Mips_MADD_Q_W	= 1006,
    Mips_MADD_S	= 1007,
    Mips_MADD_S_MM	= 1008,
    Mips_MAQ_SA_W_PHL	= 1009,
    Mips_MAQ_SA_W_PHR	= 1010,
    Mips_MAQ_S_W_PHL	= 1011,
    Mips_MAQ_S_W_PHR	= 1012,
    Mips_MAXA_D	= 1013,
    Mips_MAXA_S	= 1014,
    Mips_MAXI_S_B	= 1015,
    Mips_MAXI_S_D	= 1016,
    Mips_MAXI_S_H	= 1017,
    Mips_MAXI_S_W	= 1018,
    Mips_MAXI_U_B	= 1019,
    Mips_MAXI_U_D	= 1020,
    Mips_MAXI_U_H	= 1021,
    Mips_MAXI_U_W	= 1022,
    Mips_MAX_A_B	= 1023,
    Mips_MAX_A_D	= 1024,
    Mips_MAX_A_H	= 1025,
    Mips_MAX_A_W	= 1026,
    Mips_MAX_D	= 1027,
    Mips_MAX_S	= 1028,
    Mips_MAX_S_B	= 1029,
    Mips_MAX_S_D	= 1030,
    Mips_MAX_S_H	= 1031,
    Mips_MAX_S_W	= 1032,
    Mips_MAX_U_B	= 1033,
    Mips_MAX_U_D	= 1034,
    Mips_MAX_U_H	= 1035,
    Mips_MAX_U_W	= 1036,
    Mips_MFC0	= 1037,
    Mips_MFC1	= 1038,
    Mips_MFC1_MM	= 1039,
    Mips_MFC2	= 1040,
    Mips_MFHC1_D32	= 1041,
    Mips_MFHC1_D64	= 1042,
    Mips_MFHC1_MM	= 1043,
    Mips_MFHI	= 1044,
    Mips_MFHI16_MM	= 1045,
    Mips_MFHI64	= 1046,
    Mips_MFHI_DSP	= 1047,
    Mips_MFHI_MM	= 1048,
    Mips_MFLO	= 1049,
    Mips_MFLO16_MM	= 1050,
    Mips_MFLO64	= 1051,
    Mips_MFLO_DSP	= 1052,
    Mips_MFLO_MM	= 1053,
    Mips_MINA_D	= 1054,
    Mips_MINA_S	= 1055,
    Mips_MINI_S_B	= 1056,
    Mips_MINI_S_D	= 1057,
    Mips_MINI_S_H	= 1058,
    Mips_MINI_S_W	= 1059,
    Mips_MINI_U_B	= 1060,
    Mips_MINI_U_D	= 1061,
    Mips_MINI_U_H	= 1062,
    Mips_MINI_U_W	= 1063,
    Mips_MIN_A_B	= 1064,
    Mips_MIN_A_D	= 1065,
    Mips_MIN_A_H	= 1066,
    Mips_MIN_A_W	= 1067,
    Mips_MIN_D	= 1068,
    Mips_MIN_S	= 1069,
    Mips_MIN_S_B	= 1070,
    Mips_MIN_S_D	= 1071,
    Mips_MIN_S_H	= 1072,
    Mips_MIN_S_W	= 1073,
    Mips_MIN_U_B	= 1074,
    Mips_MIN_U_D	= 1075,
    Mips_MIN_U_H	= 1076,
    Mips_MIN_U_W	= 1077,
    Mips_MIPSeh_return32	= 1078,
    Mips_MIPSeh_return64	= 1079,
    Mips_MOD	= 1080,
    Mips_MODSUB	= 1081,
    Mips_MODU	= 1082,
    Mips_MOD_S_B	= 1083,
    Mips_MOD_S_D	= 1084,
    Mips_MOD_S_H	= 1085,
    Mips_MOD_S_W	= 1086,
    Mips_MOD_U_B	= 1087,
    Mips_MOD_U_D	= 1088,
    Mips_MOD_U_H	= 1089,
    Mips_MOD_U_W	= 1090,
    Mips_MOVE16_MM	= 1091,
    Mips_MOVE_V	= 1092,
    Mips_MOVF_D32	= 1093,
    Mips_MOVF_D32_MM	= 1094,
    Mips_MOVF_D64	= 1095,
    Mips_MOVF_I	= 1096,
    Mips_MOVF_I64	= 1097,
    Mips_MOVF_I_MM	= 1098,
    Mips_MOVF_S	= 1099,
    Mips_MOVF_S_MM	= 1100,
    Mips_MOVN_I64_D64	= 1101,
    Mips_MOVN_I64_I	= 1102,
    Mips_MOVN_I64_I64	= 1103,
    Mips_MOVN_I64_S	= 1104,
    Mips_MOVN_I_D32	= 1105,
    Mips_MOVN_I_D32_MM	= 1106,
    Mips_MOVN_I_D64	= 1107,
    Mips_MOVN_I_I	= 1108,
    Mips_MOVN_I_I64	= 1109,
    Mips_MOVN_I_MM	= 1110,
    Mips_MOVN_I_S	= 1111,
    Mips_MOVN_I_S_MM	= 1112,
    Mips_MOVT_D32	= 1113,
    Mips_MOVT_D32_MM	= 1114,
    Mips_MOVT_D64	= 1115,
    Mips_MOVT_I	= 1116,
    Mips_MOVT_I64	= 1117,
    Mips_MOVT_I_MM	= 1118,
    Mips_MOVT_S	= 1119,
    Mips_MOVT_S_MM	= 1120,
    Mips_MOVZ_I64_D64	= 1121,
    Mips_MOVZ_I64_I	= 1122,
    Mips_MOVZ_I64_I64	= 1123,
    Mips_MOVZ_I64_S	= 1124,
    Mips_MOVZ_I_D32	= 1125,
    Mips_MOVZ_I_D32_MM	= 1126,
    Mips_MOVZ_I_D64	= 1127,
    Mips_MOVZ_I_I	= 1128,
    Mips_MOVZ_I_I64	= 1129,
    Mips_MOVZ_I_MM	= 1130,
    Mips_MOVZ_I_S	= 1131,
    Mips_MOVZ_I_S_MM	= 1132,
    Mips_MSUB	= 1133,
    Mips_MSUBF_D	= 1134,
    Mips_MSUBF_S	= 1135,
    Mips_MSUBR_Q_H	= 1136,
    Mips_MSUBR_Q_W	= 1137,
    Mips_MSUBU	= 1138,
    Mips_MSUBU_DSP	= 1139,
    Mips_MSUBU_MM	= 1140,
    Mips_MSUBV_B	= 1141,
    Mips_MSUBV_D	= 1142,
    Mips_MSUBV_H	= 1143,
    Mips_MSUBV_W	= 1144,
    Mips_MSUB_D32	= 1145,
    Mips_MSUB_D32_MM	= 1146,
    Mips_MSUB_D64	= 1147,
    Mips_MSUB_DSP	= 1148,
    Mips_MSUB_MM	= 1149,
    Mips_MSUB_Q_H	= 1150,
    Mips_MSUB_Q_W	= 1151,
    Mips_MSUB_S	= 1152,
    Mips_MSUB_S_MM	= 1153,
    Mips_MTC0	= 1154,
    Mips_MTC1	= 1155,
    Mips_MTC1_MM	= 1156,
    Mips_MTC2	= 1157,
    Mips_MTHC1_D32	= 1158,
    Mips_MTHC1_D64	= 1159,
    Mips_MTHC1_MM	= 1160,
    Mips_MTHI	= 1161,
    Mips_MTHI64	= 1162,
    Mips_MTHI_DSP	= 1163,
    Mips_MTHI_MM	= 1164,
    Mips_MTHLIP	= 1165,
    Mips_MTLO	= 1166,
    Mips_MTLO64	= 1167,
    Mips_MTLO_DSP	= 1168,
    Mips_MTLO_MM	= 1169,
    Mips_MTM0	= 1170,
    Mips_MTM1	= 1171,
    Mips_MTM2	= 1172,
    Mips_MTP0	= 1173,
    Mips_MTP1	= 1174,
    Mips_MTP2	= 1175,
    Mips_MUH	= 1176,
    Mips_MUHU	= 1177,
    Mips_MUL	= 1178,
    Mips_MULEQ_S_W_PHL	= 1179,
    Mips_MULEQ_S_W_PHR	= 1180,
    Mips_MULEU_S_PH_QBL	= 1181,
    Mips_MULEU_S_PH_QBR	= 1182,
    Mips_MULQ_RS_PH	= 1183,
    Mips_MULQ_RS_W	= 1184,
    Mips_MULQ_S_PH	= 1185,
    Mips_MULQ_S_W	= 1186,
    Mips_MULR_Q_H	= 1187,
    Mips_MULR_Q_W	= 1188,
    Mips_MULSAQ_S_W_PH	= 1189,
    Mips_MULSA_W_PH	= 1190,
    Mips_MULT	= 1191,
    Mips_MULTU_DSP	= 1192,
    Mips_MULT_DSP	= 1193,
    Mips_MULT_MM	= 1194,
    Mips_MULTu	= 1195,
    Mips_MULTu_MM	= 1196,
    Mips_MULU	= 1197,
    Mips_MULV_B	= 1198,
    Mips_MULV_D	= 1199,
    Mips_MULV_H	= 1200,
    Mips_MULV_W	= 1201,
    Mips_MUL_MM	= 1202,
    Mips_MUL_PH	= 1203,
    Mips_MUL_Q_H	= 1204,
    Mips_MUL_Q_W	= 1205,
    Mips_MUL_R6	= 1206,
    Mips_MUL_S_PH	= 1207,
    Mips_Mfhi16	= 1208,
    Mips_Mflo16	= 1209,
    Mips_Move32R16	= 1210,
    Mips_MoveR3216	= 1211,
    Mips_MultRxRy16	= 1212,
    Mips_MultRxRyRz16	= 1213,
    Mips_MultuRxRy16	= 1214,
    Mips_MultuRxRyRz16	= 1215,
    Mips_NLOC_B	= 1216,
    Mips_NLOC_D	= 1217,
    Mips_NLOC_H	= 1218,
    Mips_NLOC_W	= 1219,
    Mips_NLZC_B	= 1220,
    Mips_NLZC_D	= 1221,
    Mips_NLZC_H	= 1222,
    Mips_NLZC_W	= 1223,
    Mips_NMADD_D32	= 1224,
    Mips_NMADD_D32_MM	= 1225,
    Mips_NMADD_D64	= 1226,
    Mips_NMADD_S	= 1227,
    Mips_NMADD_S_MM	= 1228,
    Mips_NMSUB_D32	= 1229,
    Mips_NMSUB_D32_MM	= 1230,
    Mips_NMSUB_D64	= 1231,
    Mips_NMSUB_S	= 1232,
    Mips_NMSUB_S_MM	= 1233,
    Mips_NOP	= 1234,
    Mips_NOR	= 1235,
    Mips_NOR64	= 1236,
    Mips_NORI_B	= 1237,
    Mips_NOR_MM	= 1238,
    Mips_NOR_V	= 1239,
    Mips_NOR_V_D_PSEUDO	= 1240,
    Mips_NOR_V_H_PSEUDO	= 1241,
    Mips_NOR_V_W_PSEUDO	= 1242,
    Mips_NegRxRy16	= 1243,
    Mips_NotRxRy16	= 1244,
    Mips_OR	= 1245,
    Mips_OR64	= 1246,
    Mips_ORI_B	= 1247,
    Mips_OR_MM	= 1248,
    Mips_OR_V	= 1249,
    Mips_OR_V_D_PSEUDO	= 1250,
    Mips_OR_V_H_PSEUDO	= 1251,
    Mips_OR_V_W_PSEUDO	= 1252,
    Mips_ORi	= 1253,
    Mips_ORi64	= 1254,
    Mips_ORi_MM	= 1255,
    Mips_OrRxRxRy16	= 1256,
    Mips_PACKRL_PH	= 1257,
    Mips_PAUSE	= 1258,
    Mips_PCKEV_B	= 1259,
    Mips_PCKEV_D	= 1260,
    Mips_PCKEV_H	= 1261,
    Mips_PCKEV_W	= 1262,
    Mips_PCKOD_B	= 1263,
    Mips_PCKOD_D	= 1264,
    Mips_PCKOD_H	= 1265,
    Mips_PCKOD_W	= 1266,
    Mips_PCNT_B	= 1267,
    Mips_PCNT_D	= 1268,
    Mips_PCNT_H	= 1269,
    Mips_PCNT_W	= 1270,
    Mips_PICK_PH	= 1271,
    Mips_PICK_QB	= 1272,
    Mips_POP	= 1273,
    Mips_PRECEQU_PH_QBL	= 1274,
    Mips_PRECEQU_PH_QBLA	= 1275,
    Mips_PRECEQU_PH_QBR	= 1276,
    Mips_PRECEQU_PH_QBRA	= 1277,
    Mips_PRECEQ_W_PHL	= 1278,
    Mips_PRECEQ_W_PHR	= 1279,
    Mips_PRECEU_PH_QBL	= 1280,
    Mips_PRECEU_PH_QBLA	= 1281,
    Mips_PRECEU_PH_QBR	= 1282,
    Mips_PRECEU_PH_QBRA	= 1283,
    Mips_PRECRQU_S_QB_PH	= 1284,
    Mips_PRECRQ_PH_W	= 1285,
    Mips_PRECRQ_QB_PH	= 1286,
    Mips_PRECRQ_RS_PH_W	= 1287,
    Mips_PRECR_QB_PH	= 1288,
    Mips_PRECR_SRA_PH_W	= 1289,
    Mips_PRECR_SRA_R_PH_W	= 1290,
    Mips_PREF	= 1291,
    Mips_PREF_R6	= 1292,
    Mips_PREPEND	= 1293,
    Mips_PseudoCMPU_EQ_QB	= 1294,
    Mips_PseudoCMPU_LE_QB	= 1295,
    Mips_PseudoCMPU_LT_QB	= 1296,
    Mips_PseudoCMP_EQ_PH	= 1297,
    Mips_PseudoCMP_LE_PH	= 1298,
    Mips_PseudoCMP_LT_PH	= 1299,
    Mips_PseudoCVT_D32_W	= 1300,
    Mips_PseudoCVT_D64_L	= 1301,
    Mips_PseudoCVT_D64_W	= 1302,
    Mips_PseudoCVT_S_L	= 1303,
    Mips_PseudoCVT_S_W	= 1304,
    Mips_PseudoDMULT	= 1305,
    Mips_PseudoDMULTu	= 1306,
    Mips_PseudoDSDIV	= 1307,
    Mips_PseudoDUDIV	= 1308,
    Mips_PseudoIndirectBranch	= 1309,
    Mips_PseudoIndirectBranch64	= 1310,
    Mips_PseudoMADD	= 1311,
    Mips_PseudoMADDU	= 1312,
    Mips_PseudoMFHI	= 1313,
    Mips_PseudoMFHI64	= 1314,
    Mips_PseudoMFLO	= 1315,
    Mips_PseudoMFLO64	= 1316,
    Mips_PseudoMSUB	= 1317,
    Mips_PseudoMSUBU	= 1318,
    Mips_PseudoMTLOHI	= 1319,
    Mips_PseudoMTLOHI64	= 1320,
    Mips_PseudoMTLOHI_DSP	= 1321,
    Mips_PseudoMULT	= 1322,
    Mips_PseudoMULTu	= 1323,
    Mips_PseudoPICK_PH	= 1324,
    Mips_PseudoPICK_QB	= 1325,
    Mips_PseudoReturn	= 1326,
    Mips_PseudoReturn64	= 1327,
    Mips_PseudoSDIV	= 1328,
    Mips_PseudoUDIV	= 1329,
    Mips_RADDU_W_QB	= 1330,
    Mips_RDDSP	= 1331,
    Mips_RDHWR	= 1332,
    Mips_RDHWR64	= 1333,
    Mips_REPLV_PH	= 1334,
    Mips_REPLV_QB	= 1335,
    Mips_REPL_PH	= 1336,
    Mips_REPL_QB	= 1337,
    Mips_RINT_D	= 1338,
    Mips_RINT_S	= 1339,
    Mips_ROTR	= 1340,
    Mips_ROTRV	= 1341,
    Mips_ROTRV_MM	= 1342,
    Mips_ROTR_MM	= 1343,
    Mips_ROUND_L_D64	= 1344,
    Mips_ROUND_L_S	= 1345,
    Mips_ROUND_W_D32	= 1346,
    Mips_ROUND_W_D64	= 1347,
    Mips_ROUND_W_MM	= 1348,
    Mips_ROUND_W_S	= 1349,
    Mips_ROUND_W_S_MM	= 1350,
    Mips_Restore16	= 1351,
    Mips_RestoreX16	= 1352,
    Mips_RetRA	= 1353,
    Mips_RetRA16	= 1354,
    Mips_SAT_S_B	= 1355,
    Mips_SAT_S_D	= 1356,
    Mips_SAT_S_H	= 1357,
    Mips_SAT_S_W	= 1358,
    Mips_SAT_U_B	= 1359,
    Mips_SAT_U_D	= 1360,
    Mips_SAT_U_H	= 1361,
    Mips_SAT_U_W	= 1362,
    Mips_SB	= 1363,
    Mips_SB64	= 1364,
    Mips_SB_MM	= 1365,
    Mips_SC	= 1366,
    Mips_SCD	= 1367,
    Mips_SCD_R6	= 1368,
    Mips_SC_MM	= 1369,
    Mips_SC_R6	= 1370,
    Mips_SD	= 1371,
    Mips_SDBBP	= 1372,
    Mips_SDBBP_R6	= 1373,
    Mips_SDC1	= 1374,
    Mips_SDC164	= 1375,
    Mips_SDC1_MM	= 1376,
    Mips_SDC2	= 1377,
    Mips_SDC2_R6	= 1378,
    Mips_SDC3	= 1379,
    Mips_SDIV	= 1380,
    Mips_SDIV_MM	= 1381,
    Mips_SDL	= 1382,
    Mips_SDR	= 1383,
    Mips_SDXC1	= 1384,
    Mips_SDXC164	= 1385,
    Mips_SEB	= 1386,
    Mips_SEB64	= 1387,
    Mips_SEB_MM	= 1388,
    Mips_SEH	= 1389,
    Mips_SEH64	= 1390,
    Mips_SEH_MM	= 1391,
    Mips_SELEQZ	= 1392,
    Mips_SELEQZ64	= 1393,
    Mips_SELEQZ_D	= 1394,
    Mips_SELEQZ_S	= 1395,
    Mips_SELNEZ	= 1396,
    Mips_SELNEZ64	= 1397,
    Mips_SELNEZ_D	= 1398,
    Mips_SELNEZ_S	= 1399,
    Mips_SEL_D	= 1400,
    Mips_SEL_S	= 1401,
    Mips_SEQ	= 1402,
    Mips_SEQi	= 1403,
    Mips_SH	= 1404,
    Mips_SH64	= 1405,
    Mips_SHF_B	= 1406,
    Mips_SHF_H	= 1407,
    Mips_SHF_W	= 1408,
    Mips_SHILO	= 1409,
    Mips_SHILOV	= 1410,
    Mips_SHLLV_PH	= 1411,
    Mips_SHLLV_QB	= 1412,
    Mips_SHLLV_S_PH	= 1413,
    Mips_SHLLV_S_W	= 1414,
    Mips_SHLL_PH	= 1415,
    Mips_SHLL_QB	= 1416,
    Mips_SHLL_S_PH	= 1417,
    Mips_SHLL_S_W	= 1418,
    Mips_SHRAV_PH	= 1419,
    Mips_SHRAV_QB	= 1420,
    Mips_SHRAV_R_PH	= 1421,
    Mips_SHRAV_R_QB	= 1422,
    Mips_SHRAV_R_W	= 1423,
    Mips_SHRA_PH	= 1424,
    Mips_SHRA_QB	= 1425,
    Mips_SHRA_R_PH	= 1426,
    Mips_SHRA_R_QB	= 1427,
    Mips_SHRA_R_W	= 1428,
    Mips_SHRLV_PH	= 1429,
    Mips_SHRLV_QB	= 1430,
    Mips_SHRL_PH	= 1431,
    Mips_SHRL_QB	= 1432,
    Mips_SH_MM	= 1433,
    Mips_SLDI_B	= 1434,
    Mips_SLDI_D	= 1435,
    Mips_SLDI_H	= 1436,
    Mips_SLDI_W	= 1437,
    Mips_SLD_B	= 1438,
    Mips_SLD_D	= 1439,
    Mips_SLD_H	= 1440,
    Mips_SLD_W	= 1441,
    Mips_SLL	= 1442,
    Mips_SLL64_32	= 1443,
    Mips_SLL64_64	= 1444,
    Mips_SLLI_B	= 1445,
    Mips_SLLI_D	= 1446,
    Mips_SLLI_H	= 1447,
    Mips_SLLI_W	= 1448,
    Mips_SLLV	= 1449,
    Mips_SLLV_MM	= 1450,
    Mips_SLL_B	= 1451,
    Mips_SLL_D	= 1452,
    Mips_SLL_H	= 1453,
    Mips_SLL_MM	= 1454,
    Mips_SLL_W	= 1455,
    Mips_SLT	= 1456,
    Mips_SLT64	= 1457,
    Mips_SLT_MM	= 1458,
    Mips_SLTi	= 1459,
    Mips_SLTi64	= 1460,
    Mips_SLTi_MM	= 1461,
    Mips_SLTiu	= 1462,
    Mips_SLTiu64	= 1463,
    Mips_SLTiu_MM	= 1464,
    Mips_SLTu	= 1465,
    Mips_SLTu64	= 1466,
    Mips_SLTu_MM	= 1467,
    Mips_SNE	= 1468,
    Mips_SNEi	= 1469,
    Mips_SNZ_B_PSEUDO	= 1470,
    Mips_SNZ_D_PSEUDO	= 1471,
    Mips_SNZ_H_PSEUDO	= 1472,
    Mips_SNZ_V_PSEUDO	= 1473,
    Mips_SNZ_W_PSEUDO	= 1474,
    Mips_SPLATI_B	= 1475,
    Mips_SPLATI_D	= 1476,
    Mips_SPLATI_H	= 1477,
    Mips_SPLATI_W	= 1478,
    Mips_SPLAT_B	= 1479,
    Mips_SPLAT_D	= 1480,
    Mips_SPLAT_H	= 1481,
    Mips_SPLAT_W	= 1482,
    Mips_SRA	= 1483,
    Mips_SRAI_B	= 1484,
    Mips_SRAI_D	= 1485,
    Mips_SRAI_H	= 1486,
    Mips_SRAI_W	= 1487,
    Mips_SRARI_B	= 1488,
    Mips_SRARI_D	= 1489,
    Mips_SRARI_H	= 1490,
    Mips_SRARI_W	= 1491,
    Mips_SRAR_B	= 1492,
    Mips_SRAR_D	= 1493,
    Mips_SRAR_H	= 1494,
    Mips_SRAR_W	= 1495,
    Mips_SRAV	= 1496,
    Mips_SRAV_MM	= 1497,
    Mips_SRA_B	= 1498,
    Mips_SRA_D	= 1499,
    Mips_SRA_H	= 1500,
    Mips_SRA_MM	= 1501,
    Mips_SRA_W	= 1502,
    Mips_SRL	= 1503,
    Mips_SRLI_B	= 1504,
    Mips_SRLI_D	= 1505,
    Mips_SRLI_H	= 1506,
    Mips_SRLI_W	= 1507,
    Mips_SRLRI_B	= 1508,
    Mips_SRLRI_D	= 1509,
    Mips_SRLRI_H	= 1510,
    Mips_SRLRI_W	= 1511,
    Mips_SRLR_B	= 1512,
    Mips_SRLR_D	= 1513,
    Mips_SRLR_H	= 1514,
    Mips_SRLR_W	= 1515,
    Mips_SRLV	= 1516,
    Mips_SRLV_MM	= 1517,
    Mips_SRL_B	= 1518,
    Mips_SRL_D	= 1519,
    Mips_SRL_H	= 1520,
    Mips_SRL_MM	= 1521,
    Mips_SRL_W	= 1522,
    Mips_SSNOP	= 1523,
    Mips_STORE_ACC128	= 1524,
    Mips_STORE_ACC64	= 1525,
    Mips_STORE_ACC64DSP	= 1526,
    Mips_STORE_CCOND_DSP	= 1527,
    Mips_ST_B	= 1528,
    Mips_ST_D	= 1529,
    Mips_ST_H	= 1530,
    Mips_ST_W	= 1531,
    Mips_SUB	= 1532,
    Mips_SUBQH_PH	= 1533,
    Mips_SUBQH_R_PH	= 1534,
    Mips_SUBQH_R_W	= 1535,
    Mips_SUBQH_W	= 1536,
    Mips_SUBQ_PH	= 1537,
    Mips_SUBQ_S_PH	= 1538,
    Mips_SUBQ_S_W	= 1539,
    Mips_SUBSUS_U_B	= 1540,
    Mips_SUBSUS_U_D	= 1541,
    Mips_SUBSUS_U_H	= 1542,
    Mips_SUBSUS_U_W	= 1543,
    Mips_SUBSUU_S_B	= 1544,
    Mips_SUBSUU_S_D	= 1545,
    Mips_SUBSUU_S_H	= 1546,
    Mips_SUBSUU_S_W	= 1547,
    Mips_SUBS_S_B	= 1548,
    Mips_SUBS_S_D	= 1549,
    Mips_SUBS_S_H	= 1550,
    Mips_SUBS_S_W	= 1551,
    Mips_SUBS_U_B	= 1552,
    Mips_SUBS_U_D	= 1553,
    Mips_SUBS_U_H	= 1554,
    Mips_SUBS_U_W	= 1555,
    Mips_SUBUH_QB	= 1556,
    Mips_SUBUH_R_QB	= 1557,
    Mips_SUBU_PH	= 1558,
    Mips_SUBU_QB	= 1559,
    Mips_SUBU_S_PH	= 1560,
    Mips_SUBU_S_QB	= 1561,
    Mips_SUBVI_B	= 1562,
    Mips_SUBVI_D	= 1563,
    Mips_SUBVI_H	= 1564,
    Mips_SUBVI_W	= 1565,
    Mips_SUBV_B	= 1566,
    Mips_SUBV_D	= 1567,
    Mips_SUBV_H	= 1568,
    Mips_SUBV_W	= 1569,
    Mips_SUB_MM	= 1570,
    Mips_SUBu	= 1571,
    Mips_SUBu_MM	= 1572,
    Mips_SUXC1	= 1573,
    Mips_SUXC164	= 1574,
    Mips_SUXC1_MM	= 1575,
    Mips_SW	= 1576,
    Mips_SW64	= 1577,
    Mips_SWC1	= 1578,
    Mips_SWC1_MM	= 1579,
    Mips_SWC2	= 1580,
    Mips_SWC2_R6	= 1581,
    Mips_SWC3	= 1582,
    Mips_SWL	= 1583,
    Mips_SWL64	= 1584,
    Mips_SWL_MM	= 1585,
    Mips_SWR	= 1586,
    Mips_SWR64	= 1587,
    Mips_SWR_MM	= 1588,
    Mips_SWXC1	= 1589,
    Mips_SWXC1_MM	= 1590,
    Mips_SW_MM	= 1591,
    Mips_SYNC	= 1592,
    Mips_SYNC_MM	= 1593,
    Mips_SYSCALL	= 1594,
    Mips_SYSCALL_MM	= 1595,
    Mips_SZ_B_PSEUDO	= 1596,
    Mips_SZ_D_PSEUDO	= 1597,
    Mips_SZ_H_PSEUDO	= 1598,
    Mips_SZ_V_PSEUDO	= 1599,
    Mips_SZ_W_PSEUDO	= 1600,
    Mips_Save16	= 1601,
    Mips_SaveX16	= 1602,
    Mips_SbRxRyOffMemX16	= 1603,
    Mips_SebRx16	= 1604,
    Mips_SehRx16	= 1605,
    Mips_SelBeqZ	= 1606,
    Mips_SelBneZ	= 1607,
    Mips_SelTBteqZCmp	= 1608,
    Mips_SelTBteqZCmpi	= 1609,
    Mips_SelTBteqZSlt	= 1610,
    Mips_SelTBteqZSlti	= 1611,
    Mips_SelTBteqZSltiu	= 1612,
    Mips_SelTBteqZSltu	= 1613,
    Mips_SelTBtneZCmp	= 1614,
    Mips_SelTBtneZCmpi	= 1615,
    Mips_SelTBtneZSlt	= 1616,
    Mips_SelTBtneZSlti	= 1617,
    Mips_SelTBtneZSltiu	= 1618,
    Mips_SelTBtneZSltu	= 1619,
    Mips_ShRxRyOffMemX16	= 1620,
    Mips_SllX16	= 1621,
    Mips_SllvRxRy16	= 1622,
    Mips_SltCCRxRy16	= 1623,
    Mips_SltRxRy16	= 1624,
    Mips_SltiCCRxImmX16	= 1625,
    Mips_SltiRxImm16	= 1626,
    Mips_SltiRxImmX16	= 1627,
    Mips_SltiuCCRxImmX16	= 1628,
    Mips_SltiuRxImm16	= 1629,
    Mips_SltiuRxImmX16	= 1630,
    Mips_SltuCCRxRy16	= 1631,
    Mips_SltuRxRy16	= 1632,
    Mips_SltuRxRyRz16	= 1633,
    Mips_SraX16	= 1634,
    Mips_SravRxRy16	= 1635,
    Mips_SrlX16	= 1636,
    Mips_SrlvRxRy16	= 1637,
    Mips_SubuRxRyRz16	= 1638,
    Mips_SwRxRyOffMemX16	= 1639,
    Mips_SwRxSpImmX16	= 1640,
    Mips_TAILCALL	= 1641,
    Mips_TAILCALL64_R	= 1642,
    Mips_TAILCALL_R	= 1643,
    Mips_TEQ	= 1644,
    Mips_TEQI	= 1645,
    Mips_TEQI_MM	= 1646,
    Mips_TEQ_MM	= 1647,
    Mips_TGE	= 1648,
    Mips_TGEI	= 1649,
    Mips_TGEIU	= 1650,
    Mips_TGEIU_MM	= 1651,
    Mips_TGEI_MM	= 1652,
    Mips_TGEU	= 1653,
    Mips_TGEU_MM	= 1654,
    Mips_TGE_MM	= 1655,
    Mips_TLBP	= 1656,
    Mips_TLBP_MM	= 1657,
    Mips_TLBR	= 1658,
    Mips_TLBR_MM	= 1659,
    Mips_TLBWI	= 1660,
    Mips_TLBWI_MM	= 1661,
    Mips_TLBWR	= 1662,
    Mips_TLBWR_MM	= 1663,
    Mips_TLT	= 1664,
    Mips_TLTI	= 1665,
    Mips_TLTIU_MM	= 1666,
    Mips_TLTI_MM	= 1667,
    Mips_TLTU	= 1668,
    Mips_TLTU_MM	= 1669,
    Mips_TLT_MM	= 1670,
    Mips_TNE	= 1671,
    Mips_TNEI	= 1672,
    Mips_TNEI_MM	= 1673,
    Mips_TNE_MM	= 1674,
    Mips_TRAP	= 1675,
    Mips_TRUNC_L_D64	= 1676,
    Mips_TRUNC_L_S	= 1677,
    Mips_TRUNC_W_D32	= 1678,
    Mips_TRUNC_W_D64	= 1679,
    Mips_TRUNC_W_MM	= 1680,
    Mips_TRUNC_W_S	= 1681,
    Mips_TRUNC_W_S_MM	= 1682,
    Mips_TTLTIU	= 1683,
    Mips_UDIV	= 1684,
    Mips_UDIV_MM	= 1685,
    Mips_V3MULU	= 1686,
    Mips_VMM0	= 1687,
    Mips_VMULU	= 1688,
    Mips_VSHF_B	= 1689,
    Mips_VSHF_D	= 1690,
    Mips_VSHF_H	= 1691,
    Mips_VSHF_W	= 1692,
    Mips_WAIT	= 1693,
    Mips_WAIT_MM	= 1694,
    Mips_WRDSP	= 1695,
    Mips_WSBH	= 1696,
    Mips_WSBH_MM	= 1697,
    Mips_XOR	= 1698,
    Mips_XOR64	= 1699,
    Mips_XORI_B	= 1700,
    Mips_XOR_MM	= 1701,
    Mips_XOR_V	= 1702,
    Mips_XOR_V_D_PSEUDO	= 1703,
    Mips_XOR_V_H_PSEUDO	= 1704,
    Mips_XOR_V_W_PSEUDO	= 1705,
    Mips_XORi	= 1706,
    Mips_XORi64	= 1707,
    Mips_XORi_MM	= 1708,
    Mips_XorRxRxRy16	= 1709,
    Mips_INSTRUCTION_LIST_END = 1710
};

#endif // GET_INSTRINFO_ENUM
