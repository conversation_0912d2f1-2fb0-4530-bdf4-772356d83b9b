// Copyright (c) 2025, DdiMon EPT Process Hiding. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Implements EPT-based process hiding functionality.

#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "../HyperPlatform/HyperPlatform/util.h"
#include "../HyperPlatform/HyperPlatform/ept.h"
#include "shadow_hook.h"
#include "ddi_mon_ioctl.h"
#include "process_hiding.h"
#include <ntimage.h>
#include <intrin.h>

extern "C" {

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding EPT Hook implementation
//

// Global process hiding hook state
static volatile BOOLEAN g_ProcessHidingHooksActive = FALSE;
static PVOID g_NtQuerySystemInformationOriginal = nullptr;
static PVOID g_PsGetNextProcessOriginal = nullptr;

// Shadow hook data for process hiding
static SharedShadowHookData* g_ProcessHidingSharedData = nullptr;
static ShadowHookTarget g_NtQuerySystemInformationTarget = {};
static ShadowHookTarget g_PsGetNextProcessTarget = {};

// Hook function prototypes
typedef NTSTATUS (NTAPI *PFN_NT_QUERY_SYSTEM_INFORMATION)(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
);

typedef PEPROCESS (NTAPI *PFN_PS_GET_NEXT_PROCESS)(
    PEPROCESS Process
);

// Forward declarations
static NTSTATUS NTAPI HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
);

static PEPROCESS NTAPI HookedPsGetNextProcess(
    PEPROCESS Process
);

static NTSTATUS ProcessHidingpFindKernelFunctions(void);
static NTSTATUS ProcessHidingpInstallHooks(void);
static VOID ProcessHidingpRemoveHooks(void);
static BOOLEAN ProcessHidingpShouldHideProcess(ULONG ProcessId);
static NTSTATUS ProcessHidingpFilterProcessList(PVOID SystemInformation, ULONG Length);

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding initialization and cleanup
//

// Initialize process hiding hooks
NTSTATUS ProcessHidingInitialize(void) {
    HYPERPLATFORM_LOG_INFO("Initializing process hiding hooks...");

    if (g_ProcessHidingHooksActive) {
        HYPERPLATFORM_LOG_INFO("Process hiding hooks already active");
        return STATUS_SUCCESS;
    }

    // Find kernel functions
    NTSTATUS status = ProcessHidingpFindKernelFunctions();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("Failed to find kernel functions: 0x%08X", status);
        return status;
    }

    // Install EPT hooks
    status = ProcessHidingpInstallHooks();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("Failed to install process hiding hooks: 0x%08X", status);
        return status;
    }

    g_ProcessHidingHooksActive = TRUE;
    HYPERPLATFORM_LOG_INFO("Process hiding hooks initialized successfully");
    return STATUS_SUCCESS;
}

// Cleanup process hiding hooks
VOID ProcessHidingTerminate(void) {
    if (!g_ProcessHidingHooksActive) {
        return;
    }

    HYPERPLATFORM_LOG_INFO("Terminating process hiding hooks...");

    // Remove EPT hooks
    ProcessHidingpRemoveHooks();

    g_ProcessHidingHooksActive = FALSE;
    HYPERPLATFORM_LOG_INFO("Process hiding hooks terminated");
}

// Check if process hiding hooks are active
BOOLEAN ProcessHidingIsActive(void) {
    return g_ProcessHidingHooksActive;
}

////////////////////////////////////////////////////////////////////////////////
//
// Kernel function discovery
//

// Find required kernel functions
static NTSTATUS ProcessHidingpFindKernelFunctions(void) {
    HYPERPLATFORM_LOG_INFO("Finding kernel functions for process hiding...");

    // Get NtQuerySystemInformation address
    UNICODE_STRING ntQuerySystemInformationName;
    RtlInitUnicodeString(&ntQuerySystemInformationName, L"NtQuerySystemInformation");
    
    g_NtQuerySystemInformationOriginal = MmGetSystemRoutineAddress(&ntQuerySystemInformationName);
    if (!g_NtQuerySystemInformationOriginal) {
        HYPERPLATFORM_LOG_ERROR("Failed to find NtQuerySystemInformation");
        return STATUS_PROCEDURE_NOT_FOUND;
    }

    HYPERPLATFORM_LOG_INFO("Found NtQuerySystemInformation at 0x%p", g_NtQuerySystemInformationOriginal);

    // Get PsGetNextProcess address
    UNICODE_STRING psGetNextProcessName;
    RtlInitUnicodeString(&psGetNextProcessName, L"PsGetNextProcess");
    
    g_PsGetNextProcessOriginal = MmGetSystemRoutineAddress(&psGetNextProcessName);
    if (!g_PsGetNextProcessOriginal) {
        HYPERPLATFORM_LOG_ERROR("Failed to find PsGetNextProcess");
        return STATUS_PROCEDURE_NOT_FOUND;
    }

    HYPERPLATFORM_LOG_INFO("Found PsGetNextProcess at 0x%p", g_PsGetNextProcessOriginal);

    return STATUS_SUCCESS;
}

////////////////////////////////////////////////////////////////////////////////
//
// EPT Hook installation and removal
//

// Install EPT hooks for process hiding
static NTSTATUS ProcessHidingpInstallHooks(void) {
    HYPERPLATFORM_LOG_INFO("Installing EPT hooks for process hiding...");

    // Allocate shared shadow hook data
    g_ProcessHidingSharedData = ShAllocateSharedShaowHookData();
    if (!g_ProcessHidingSharedData) {
        HYPERPLATFORM_LOG_ERROR("Failed to allocate shared shadow hook data");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    // Setup NtQuerySystemInformation hook
    RtlInitUnicodeString(&g_NtQuerySystemInformationTarget.target_name, L"NtQuerySystemInformation");
    g_NtQuerySystemInformationTarget.handler = HookedNtQuerySystemInformation;
    g_NtQuerySystemInformationTarget.original_call = nullptr;

    if (!ShInstallHook(g_ProcessHidingSharedData, g_NtQuerySystemInformationOriginal, &g_NtQuerySystemInformationTarget)) {
        HYPERPLATFORM_LOG_ERROR("Failed to install NtQuerySystemInformation hook");
        ShFreeSharedShadowHookData(g_ProcessHidingSharedData);
        g_ProcessHidingSharedData = nullptr;
        return STATUS_UNSUCCESSFUL;
    }

    HYPERPLATFORM_LOG_INFO("NtQuerySystemInformation hook installed successfully");

    // Setup PsGetNextProcess hook
    RtlInitUnicodeString(&g_PsGetNextProcessTarget.target_name, L"PsGetNextProcess");
    g_PsGetNextProcessTarget.handler = HookedPsGetNextProcess;
    g_PsGetNextProcessTarget.original_call = nullptr;

    if (!ShInstallHook(g_ProcessHidingSharedData, g_PsGetNextProcessOriginal, &g_PsGetNextProcessTarget)) {
        HYPERPLATFORM_LOG_ERROR("Failed to install PsGetNextProcess hook");
        ShFreeSharedShadowHookData(g_ProcessHidingSharedData);
        g_ProcessHidingSharedData = nullptr;
        return STATUS_UNSUCCESSFUL;
    }

    HYPERPLATFORM_LOG_INFO("PsGetNextProcess hook installed successfully");

    // Enable the hooks
    NTSTATUS status = ShEnableHooks();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("Failed to enable shadow hooks: 0x%08X", status);
        ShFreeSharedShadowHookData(g_ProcessHidingSharedData);
        g_ProcessHidingSharedData = nullptr;
        return status;
    }

    HYPERPLATFORM_LOG_INFO("Process hiding hooks installation completed successfully");
    return STATUS_SUCCESS;
}

// Remove EPT hooks
static VOID ProcessHidingpRemoveHooks(void) {
    HYPERPLATFORM_LOG_INFO("Removing process hiding EPT hooks...");

    if (g_ProcessHidingSharedData) {
        // Disable hooks first
        ShDisableHooks();

        // Free shared data
        ShFreeSharedShadowHookData(g_ProcessHidingSharedData);
        g_ProcessHidingSharedData = nullptr;
    }

    HYPERPLATFORM_LOG_INFO("Process hiding hooks removal completed");
}

////////////////////////////////////////////////////////////////////////////////
//
// Hook function implementations (placeholder for future implementation)
//

// Hooked NtQuerySystemInformation function
static NTSTATUS NTAPI HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
) {
    HYPERPLATFORM_LOG_DEBUG("HookedNtQuerySystemInformation called - Class: %lu", SystemInformationClass);

    // Call original function first
    auto original_func = reinterpret_cast<PFN_NT_QUERY_SYSTEM_INFORMATION>(g_NtQuerySystemInformationTarget.original_call);
    NTSTATUS status = original_func(SystemInformationClass, SystemInformation, SystemInformationLength, ReturnLength);

    // Only filter SystemProcessInformation (class 5)
    if (NT_SUCCESS(status) && SystemInformationClass == 5 && SystemInformation) {
        __try {
            // Filter the process list to hide our processes
            ProcessHidingpFilterProcessList(SystemInformation, SystemInformationLength);
        } __except(EXCEPTION_EXECUTE_HANDLER) {
            HYPERPLATFORM_LOG_ERROR("Exception in process list filtering: 0x%08X", GetExceptionCode());
        }
    }

    return status;
}

// Hooked PsGetNextProcess function
static PEPROCESS NTAPI HookedPsGetNextProcess(
    PEPROCESS Process
) {
    HYPERPLATFORM_LOG_DEBUG("HookedPsGetNextProcess called");

    // Call original function
    auto original_func = reinterpret_cast<PFN_PS_GET_NEXT_PROCESS>(g_PsGetNextProcessTarget.original_call);
    PEPROCESS nextProcess = original_func(Process);

    // Skip hidden processes
    while (nextProcess) {
        ULONG processId = (ULONG)(ULONG_PTR)PsGetProcessId(nextProcess);

        if (!ProcessHidingpShouldHideProcess(processId)) {
            // This process is not hidden, return it
            break;
        }

        HYPERPLATFORM_LOG_DEBUG("Skipping hidden process PID: %lu", processId);

        // This process is hidden, get the next one
        PEPROCESS currentProcess = nextProcess;
        nextProcess = original_func(currentProcess);

        // Dereference the hidden process
        ObDereferenceObject(currentProcess);
    }

    return nextProcess;
}

////////////////////////////////////////////////////////////////////////////////
//
// Helper functions (placeholder for future implementation)
//

// Check if a process should be hidden
static BOOLEAN ProcessHidingpShouldHideProcess(ULONG ProcessId) {
    // Use the existing function from ddi_mon_ioctl.cpp
    extern BOOLEAN DdimonpIsProcessHidden(ULONG ProcessId);
    return DdimonpIsProcessHidden(ProcessId);
}

// Filter process list to hide specified processes
static NTSTATUS ProcessHidingpFilterProcessList(PVOID SystemInformation, ULONG Length) {
    UNREFERENCED_PARAMETER(Length);

    if (!SystemInformation) {
        return STATUS_INVALID_PARAMETER;
    }

    __try {
        PSYSTEM_PROCESS_INFORMATION current = (PSYSTEM_PROCESS_INFORMATION)SystemInformation;
        PSYSTEM_PROCESS_INFORMATION previous = nullptr;

        while (current) {
            ULONG processId = (ULONG)(ULONG_PTR)current->UniqueProcessId;

            // Check if this process should be hidden
            if (ProcessHidingpShouldHideProcess(processId)) {
                HYPERPLATFORM_LOG_DEBUG("Filtering out hidden process PID: %lu", processId);

                if (previous) {
                    // Skip this entry by updating the previous entry's NextEntryOffset
                    if (current->NextEntryOffset == 0) {
                        // This is the last entry, terminate the list here
                        previous->NextEntryOffset = 0;
                    } else {
                        // Point to the next entry after the hidden one
                        previous->NextEntryOffset += current->NextEntryOffset;
                    }
                } else {
                    // This is the first entry and it's hidden
                    if (current->NextEntryOffset == 0) {
                        // Only entry in the list, clear it
                        RtlZeroMemory(current, sizeof(SYSTEM_PROCESS_INFORMATION));
                        return STATUS_SUCCESS;
                    } else {
                        // Move the next entry to the beginning
                        PSYSTEM_PROCESS_INFORMATION next = (PSYSTEM_PROCESS_INFORMATION)
                            ((PUCHAR)current + current->NextEntryOffset);
                        ULONG nextSize = next->NextEntryOffset == 0 ?
                            (Length - current->NextEntryOffset) : next->NextEntryOffset;

                        RtlMoveMemory(current, next, nextSize);
                        continue; // Don't advance current, check this position again
                    }
                }
            } else {
                // This process is not hidden, keep it
                previous = current;
            }

            // Move to next entry
            if (current->NextEntryOffset == 0) {
                break;
            }
            current = (PSYSTEM_PROCESS_INFORMATION)((PUCHAR)current + current->NextEntryOffset);
        }

    } __except(EXCEPTION_EXECUTE_HANDLER) {
        HYPERPLATFORM_LOG_ERROR("Exception in ProcessHidingpFilterProcessList: 0x%08X", GetExceptionCode());
        return STATUS_UNSUCCESSFUL;
    }

    return STATUS_SUCCESS;
}

}  // extern "C"
