# CS_ARCH_ARM, CS_MODE_THUMB, None
0x41,0xf1,0x04,0x00 = adc r0, r1, #4
0x51,0xf1,0x00,0x00 = adcs r0, r1, #0
0x42,0xf1,0xff,0x01 = adc r1, r2, #255
0x47,0xf1,0x55,0x13 = adc r3, r7, #5570645
0x4c,0xf1,0xaa,0x28 = adc r8, r12, #2852170240
0x47,0xf1,0xa5,0x39 = adc r9, r7, #2779096485
0x43,0xf1,0x07,0x45 = adc r5, r3, #2264924160
0x42,0xf1,0xff,0x44 = adc r4, r2, #2139095040
0x42,0xf5,0xd0,0x64 = adc r4, r2, #1664
0x45,0xeb,0x06,0x04 = adc.w r4, r5, r6
0x55,0xeb,0x06,0x04 = adcs.w r4, r5, r6
0x41,0xeb,0x03,0x09 = adc.w r9, r1, r3
0x51,0xeb,0x03,0x09 = adcs.w r9, r1, r3
0x41,0xeb,0x33,0x10 = adc.w r0, r1, r3, ror #4
0x51,0xeb,0xc3,0x10 = adcs.w r0, r1, r3, lsl #7
0x41,0xeb,0xd3,0x70 = adc.w r0, r1, r3, lsr #31
0x51,0xeb,0x23,0x00 = adcs.w r0, r1, r3, asr #32
0x0d,0xeb,0x0c,0x02 = add.w r2, sp, r12
0x0a,0xbf = itet eq
0x03,0xf2,0xff,0x35 = addwne r5, r3, #1023
0x05,0xf2,0x25,0x14 = addweq r4, r5, #293
0x0d,0xf5,0x80,0x62 = add.w r2, sp, #1024
0x08,0xf5,0x7f,0x42 = add.w r2, r8, #65280
0x03,0xf2,0x01,0x12 = addw r2, r3, #257
0x03,0xf2,0x01,0x12 = addw r2, r3, #257
0x06,0xf5,0x80,0x7c = add.w r12, r6, #256
0x06,0xf2,0x00,0x1c = addw r12, r6, #256
0x12,0xf5,0xf8,0x71 = adds.w r1, r2, #496
0x02,0xf1,0x01,0x02 = add.w r2, r2, #1
0x00,0xf1,0x20,0x00 = add.w r0, r0, #32
0x38,0x32 = adds r2, #56
0x38,0x32 = adds r2, #56
0x07,0xf1,0xcb,0x31 = add.w r1, r7, #3419130827
0x0d,0xf1,0xff,0x7d = add.w sp, sp, #33423360
0xb2,0xf1,0x10,0x02 = subs.w r2, r2, #16
0xb2,0xf1,0x10,0x02 = subs.w r2, r2, #16
0xa2,0xf2,0x10,0x02 = subw r2, r2, #16
0xa2,0xf2,0x10,0x02 = subw r2, r2, #16
0xa2,0xf2,0x10,0x02 = subw r2, r2, #16
0x02,0xeb,0x08,0x01 = add.w r1, r2, r8
0x09,0xeb,0x22,0x05 = add.w r5, r9, r2, asr #32
0x13,0xeb,0xc1,0x77 = adds.w r7, r3, r1, lsl #31
0x13,0xeb,0x56,0x60 = adds.w r0, r3, r6, lsr #25
0x08,0xeb,0x31,0x34 = add.w r4, r8, r1, ror #12
0xc2,0x44 = add r10, r8
0xc2,0x44 = add r10, r8
0xaf,0xf6,0xc6,0x4b = subw r11, pc, #3270
0x0f,0xf2,0x03,0x02 = adr.w r2, #3
0xaf,0xf2,0x3a,0x3b = adr.w r11, #-826
0xaf,0xf2,0x00,0x01 = adr.w r1, #-0
0x05,0xf4,0x7f,0x22 = and r2, r5, #1044480
0x1c,0xf0,0x0f,0x03 = ands r3, r12, #15
0x01,0xf0,0xff,0x01 = and r1, r1, #255
0x01,0xf0,0xff,0x01 = and r1, r1, #255
0x04,0xf0,0xff,0x35 = and r5, r4, #4294967295
0x19,0xf0,0xff,0x31 = ands r1, r9, #4294967295
0x09,0xea,0x08,0x04 = and.w r4, r9, r8
0x04,0xea,0xe8,0x01 = and.w r1, r4, r8, asr #3
0x11,0xea,0x47,0x02 = ands.w r2, r1, r7, lsl #1
0x15,0xea,0x12,0x54 = ands.w r4, r5, r2, lsr #20
0x0c,0xea,0x71,0x49 = and.w r9, r12, r1, ror #17
0x4f,0xea,0x23,0x32 = asr.w r2, r3, #12
0x5f,0xea,0x23,0x08 = asrs.w r8, r3, #32
0x5f,0xea,0x63,0x02 = asrs.w r2, r3, #1
0x4f,0xea,0x23,0x12 = asr.w r2, r3, #4
0x5f,0xea,0xec,0x32 = asrs.w r2, r12, #15
0x4f,0xea,0xe3,0x43 = asr.w r3, r3, #19
0x5f,0xea,0xa8,0x08 = asrs.w r8, r8, #2
0x5f,0xea,0x67,0x17 = asrs.w r7, r7, #5
0x4f,0xea,0x6c,0x5c = asr.w r12, r12, #21
0x44,0xfa,0x02,0xf3 = asr.w r3, r4, r2
0x41,0xfa,0x02,0xf1 = asr.w r1, r1, r2
0x54,0xfa,0x08,0xf3 = asrs.w r3, r4, r8
0x08,0xbf = it eq
0x13,0xf5,0xce,0xa9 = bmi.w #-183396
0x6f,0xf3,0xd3,0x05 = bfc r5, #3, #17
0x38,0xbf = it lo
0x6f,0xf3,0xd3,0x05 = bfclo r5, #3, #17
0x62,0xf3,0xd3,0x05 = bfi r5, r2, #3, #17
0x18,0xbf = it ne
0x62,0xf3,0xd3,0x05 = bfine r5, r2, #3, #17
0x21,0xf0,0x0f,0x0a = bic r10, r1, #15
0x22,0xf0,0xff,0x35 = bic r5, r2, #4294967295
0x3a,0xf0,0xff,0x3b = bics r11, r10, #4294967295
0x23,0xea,0x06,0x0c = bic.w r12, r3, r6
0x22,0xea,0x06,0x3b = bic.w r11, r2, r6, lsl #12
0x24,0xea,0xd1,0x28 = bic.w r8, r4, r1, lsr #11
0x25,0xea,0xd7,0x37 = bic.w r7, r5, r7, lsr #15
0x27,0xea,0x29,0x06 = bic.w r6, r7, r9, asr #32
0x26,0xea,0x78,0x05 = bic.w r5, r6, r8, ror #1
0x21,0xf0,0x0f,0x01 = bic r1, r1, #15
0x21,0xea,0x01,0x01 = bic.w r1, r1, r1
0x24,0xea,0xc2,0x74 = bic.w r4, r4, r2, lsl #31
0x26,0xea,0x13,0x36 = bic.w r6, r6, r3, lsr #12
0x27,0xea,0xd4,0x17 = bic.w r7, r7, r4, lsr #7
0x28,0xea,0xe5,0x38 = bic.w r8, r8, r5, asr #15
0x2c,0xea,0x76,0x7c = bic.w r12, r12, r6, ror #29
0x58,0xbf = it pl
0xea,0xbe = bkpt #234
0xc5,0xf3,0x00,0x8f = bxj r5
0x18,0xbf = it ne
0xc7,0xf3,0x00,0x8f = bxjne r7
0x1f,0xb9 = cbnz r7, #6
0x37,0xb9 = cbnz r7, #12
0x11,0xee,0x81,0x17 = cdp p7, #1, c1, c1, c1, #4
0x11,0xfe,0x81,0x17 = cdp2 p7, #1, c1, c1, c1, #4
0xbf,0xf3,0x2f,0x8f = clrex 
0x18,0xbf = it ne
0xb2,0xfa,0x82,0xf1 = clz r1, r2
0x08,0xbf = it eq
0xb2,0xfa,0x82,0xf1 = clzeq r1, r2
0x11,0xf1,0x0f,0x0f = cmn.w r1, #15
0x18,0xeb,0x06,0x0f = cmn.w r8, r6
0x11,0xeb,0x86,0x2f = cmn.w r1, r6, lsl #10
0x11,0xeb,0x96,0x2f = cmn.w r1, r6, lsr #10
0x1d,0xeb,0x96,0x2f = cmn.w sp, r6, lsr #10
0x11,0xeb,0xa6,0x2f = cmn.w r1, r6, asr #10
0x11,0xeb,0xb6,0x2f = cmn.w r1, r6, ror #10
0xb5,0xf5,0x7f,0x4f = cmp.w r5, #65280
0xb4,0xeb,0x0c,0x0f = cmp.w r4, r12
0xb9,0xeb,0x06,0x3f = cmp.w r9, r6, lsl #12
0xb3,0xeb,0xd7,0x7f = cmp.w r3, r7, lsr #31
0xbd,0xeb,0x56,0x0f = cmp.w sp, r6, lsr #1
0xb2,0xeb,0x25,0x6f = cmp.w r2, r5, asr #24
0xb1,0xeb,0xf4,0x3f = cmp.w r1, r4, ror #15
0x12,0xf1,0x02,0x0f = cmn.w r2, #2
0xb9,0xf1,0x01,0x0f = cmp.w r9, #1
0x61,0xb6 = cpsie f
0x74,0xb6 = cpsid a
0xaf,0xf3,0x20,0x84 = cpsie.w f
0xaf,0xf3,0x80,0x86 = cpsid.w a
0xaf,0xf3,0x43,0x85 = cpsie i, #3
0xaf,0xf3,0x43,0x85 = cpsie i, #3
0xaf,0xf3,0x29,0x87 = cpsid f, #9
0xaf,0xf3,0x29,0x87 = cpsid f, #9
0xaf,0xf3,0x00,0x81 = cps #0
0xaf,0xf3,0x00,0x81 = cps #0
0xaf,0xf3,0xf5,0x80 = dbg #5
0xaf,0xf3,0xf0,0x80 = dbg #0
0xaf,0xf3,0xff,0x80 = dbg #15
0xbf,0xf3,0x5f,0x8f = dmb sy
0xbf,0xf3,0x5e,0x8f = dmb st
0xbf,0xf3,0x5d,0x8f = dmb #0xd
0xbf,0xf3,0x5c,0x8f = dmb #0xc
0xbf,0xf3,0x5b,0x8f = dmb ish
0xbf,0xf3,0x5a,0x8f = dmb ishst
0xbf,0xf3,0x59,0x8f = dmb #0x9
0xbf,0xf3,0x58,0x8f = dmb #0x8
0xbf,0xf3,0x57,0x8f = dmb nsh
0xbf,0xf3,0x56,0x8f = dmb nshst
0xbf,0xf3,0x55,0x8f = dmb #0x5
0xbf,0xf3,0x54,0x8f = dmb #0x4
0xbf,0xf3,0x53,0x8f = dmb osh
0xbf,0xf3,0x52,0x8f = dmb oshst
0xbf,0xf3,0x51,0x8f = dmb #0x1
0xbf,0xf3,0x50,0x8f = dmb #0x0
0xbf,0xf3,0x5f,0x8f = dmb sy
0xbf,0xf3,0x5e,0x8f = dmb st
0xbf,0xf3,0x5b,0x8f = dmb ish
0xbf,0xf3,0x5b,0x8f = dmb ish
0xbf,0xf3,0x5a,0x8f = dmb ishst
0xbf,0xf3,0x5a,0x8f = dmb ishst
0xbf,0xf3,0x57,0x8f = dmb nsh
0xbf,0xf3,0x57,0x8f = dmb nsh
0xbf,0xf3,0x56,0x8f = dmb nshst
0xbf,0xf3,0x56,0x8f = dmb nshst
0xbf,0xf3,0x53,0x8f = dmb osh
0xbf,0xf3,0x52,0x8f = dmb oshst
0xbf,0xf3,0x5f,0x8f = dmb sy
0xbf,0xf3,0x4f,0x8f = dsb sy
0xbf,0xf3,0x4e,0x8f = dsb st
0xbf,0xf3,0x4d,0x8f = dsb #0xd
0xbf,0xf3,0x4c,0x8f = dsb #0xc
0xbf,0xf3,0x4b,0x8f = dsb ish
0xbf,0xf3,0x4a,0x8f = dsb ishst
0xbf,0xf3,0x49,0x8f = dsb #0x9
0xbf,0xf3,0x48,0x8f = dsb #0x8
0xbf,0xf3,0x47,0x8f = dsb nsh
0xbf,0xf3,0x46,0x8f = dsb nshst
0xbf,0xf3,0x45,0x8f = dsb #0x5
0xbf,0xf3,0x44,0x8f = dsb #0x4
0xbf,0xf3,0x43,0x8f = dsb osh
0xbf,0xf3,0x42,0x8f = dsb oshst
0xbf,0xf3,0x41,0x8f = dsb #0x1
0xbf,0xf3,0x40,0x8f = dsb #0x0
0xbf,0xf3,0x4f,0x8f = dsb sy
0xbf,0xf3,0x4e,0x8f = dsb st
0xbf,0xf3,0x4b,0x8f = dsb ish
0xbf,0xf3,0x4b,0x8f = dsb ish
0xbf,0xf3,0x4a,0x8f = dsb ishst
0xbf,0xf3,0x4a,0x8f = dsb ishst
0xbf,0xf3,0x47,0x8f = dsb nsh
0xbf,0xf3,0x47,0x8f = dsb nsh
0xbf,0xf3,0x46,0x8f = dsb nshst
0xbf,0xf3,0x46,0x8f = dsb nshst
0xbf,0xf3,0x43,0x8f = dsb osh
0xbf,0xf3,0x42,0x8f = dsb oshst
0xbf,0xf3,0x4f,0x8f = dsb sy
0x85,0xf4,0x70,0x44 = eor r4, r5, #61440
0x85,0xea,0x06,0x04 = eor.w r4, r5, r6
0x85,0xea,0x46,0x14 = eor.w r4, r5, r6, lsl #5
0x85,0xea,0x56,0x14 = eor.w r4, r5, r6, lsr #5
0x85,0xea,0x56,0x14 = eor.w r4, r5, r6, lsr #5
0x85,0xea,0x66,0x14 = eor.w r4, r5, r6, asr #5
0x85,0xea,0x76,0x14 = eor.w r4, r5, r6, ror #5
0xbf,0xf3,0x6f,0x8f = isb sy
0xbf,0xf3,0x6f,0x8f = isb sy
0xbf,0xf3,0x6f,0x8f = isb sy
0xbf,0xf3,0x61,0x8f = isb #0x1
0x0d,0xbf = iteet eq
0x88,0x18 = addeq r0, r1, r2
0x00,0xbf = nopne 
0xf5,0x1b = subne r5, r6, r7
0x0d,0xbf = iteet eq
0x88,0x18 = addeq r0, r1, r2
0x00,0xbf = nopne 
0xf5,0x1b = subne r5, r6, r7
0x91,0xfd,0x01,0x80 = ldc2 p0, c8, [r1, #4]
0x92,0xfd,0x00,0x71 = ldc2 p1, c7, [r2]
0x13,0xfd,0x38,0x62 = ldc2 p2, c6, [r3, #-224]
0x34,0xfd,0x1e,0x53 = ldc2 p3, c5, [r4, #-120]!
0xb5,0xfc,0x04,0x44 = ldc2 p4, c4, [r5], #16
0x36,0xfc,0x12,0x35 = ldc2 p5, c3, [r6], #-72
0xd7,0xfd,0x01,0x26 = ldc2l p6, c2, [r7, #4]
0xd8,0xfd,0x00,0x17 = ldc2l p7, c1, [r8]
0x59,0xfd,0x38,0x08 = ldc2l p8, c0, [r9, #-224]
0x7a,0xfd,0x1e,0x19 = ldc2l p9, c1, [r10, #-120]!
0xfb,0xfc,0x04,0x20 = ldc2l p0, c2, [r11], #16
0x7c,0xfc,0x12,0x31 = ldc2l p1, c3, [r12], #-72
0x90,0xed,0x01,0x4c = ldc p12, c4, [r0, #4]
0x91,0xed,0x00,0x5d = ldc p13, c5, [r1]
0x12,0xed,0x38,0x6e = ldc p14, c6, [r2, #-224]
0x33,0xed,0x1e,0x7f = ldc p15, c7, [r3, #-120]!
0xb4,0xec,0x04,0x85 = ldc p5, c8, [r4], #16
0x35,0xec,0x12,0x94 = ldc p4, c9, [r5], #-72
0xd6,0xed,0x01,0xa3 = ldcl p3, c10, [r6, #4]
0xd7,0xed,0x00,0xb2 = ldcl p2, c11, [r7]
0x58,0xed,0x38,0xc1 = ldcl p1, c12, [r8, #-224]
0x79,0xed,0x1e,0xd0 = ldcl p0, c13, [r9, #-120]!
0xfa,0xec,0x04,0xe6 = ldcl p6, c14, [r10], #16
0x7b,0xec,0x12,0xf7 = ldcl p7, c15, [r11], #-72
0x91,0xfc,0x19,0x82 = ldc2 p2, c8, [r1], {25}
0x94,0xe8,0x30,0x03 = ldm.w r4, {r4, r5, r8, r9}
0x94,0xe8,0x60,0x00 = ldm.w r4, {r5, r6}
0xb5,0xe8,0x08,0x01 = ldm.w r5!, {r3, r8}
0x94,0xe8,0x30,0x03 = ldm.w r4, {r4, r5, r8, r9}
0x94,0xe8,0x60,0x00 = ldm.w r4, {r5, r6}
0xb5,0xe8,0x08,0x01 = ldm.w r5!, {r3, r8}
0xb5,0xe8,0x06,0x00 = ldm.w r5!, {r1, r2}
0x92,0xe8,0x06,0x00 = ldm.w r2, {r1, r2}
0x94,0xe8,0x30,0x03 = ldm.w r4, {r4, r5, r8, r9}
0x94,0xe8,0x60,0x00 = ldm.w r4, {r5, r6}
0xb5,0xe8,0x08,0x01 = ldm.w r5!, {r3, r8}
0x94,0xe8,0x30,0x03 = ldm.w r4, {r4, r5, r8, r9}
0x94,0xe8,0x60,0x00 = ldm.w r4, {r5, r6}
0xb5,0xe8,0x08,0x01 = ldm.w r5!, {r3, r8}
0xb5,0xe8,0x08,0x01 = ldm.w r5!, {r3, r8}
0xbd,0xe8,0xf0,0x8f = pop.w {r4, r5, r6, r7, r8, r9, r10, r11, pc}
0x14,0xe9,0x30,0x03 = ldmdb r4, {r4, r5, r8, r9}
0x14,0xe9,0x60,0x00 = ldmdb r4, {r5, r6}
0x35,0xe9,0x08,0x01 = ldmdb r5!, {r3, r8}
0x35,0xe9,0x08,0x01 = ldmdb r5!, {r3, r8}
0x14,0xe9,0x60,0x00 = ldmdb r4, {r5, r6}
0x35,0xe9,0x08,0x01 = ldmdb r5!, {r3, r8}
0x55,0xf8,0x04,0x5c = ldr r5, [r5, #-4]
0x35,0x6a = ldr r5, [r6, #32]
0xd6,0xf8,0x21,0x50 = ldr.w r5, [r6, #33]
0xd6,0xf8,0x01,0x51 = ldr.w r5, [r6, #257]
0xd7,0xf8,0x01,0xf1 = ldr.w pc, [r7, #257]
0x54,0xf8,0xff,0x2f = ldr r2, [r4, #255]!
0x5d,0xf8,0x04,0x8f = ldr r8, [sp, #4]!
0x5d,0xf8,0x04,0xed = ldr lr, [sp, #-4]!
0x54,0xf8,0xff,0x2b = ldr r2, [r4], #255
0x5d,0xf8,0x04,0x8b = ldr r8, [sp], #4
0x5d,0xf8,0x04,0xe9 = ldr lr, [sp], #-4
0x02,0x4f = ldr r7, [pc, #8]
0x02,0x4f = ldr r7, [pc, #8]
0xdf,0xf8,0x08,0x70 = ldr.w r7, [pc, #8]
0xff,0x4c = ldr r4, [pc, #1020]
0x5f,0xf8,0xfc,0x33 = ldr.w r3, [pc, #-1020]
0xdf,0xf8,0x00,0x64 = ldr.w r6, [pc, #1024]
0x5f,0xf8,0x00,0x04 = ldr.w r0, [pc, #-1024]
0xdf,0xf8,0xff,0x2f = ldr.w r2, [pc, #4095]
0x5f,0xf8,0xff,0x1f = ldr.w r1, [pc, #-4095]
0xdf,0xf8,0x84,0x80 = ldr.w r8, [pc, #132]
0xdf,0xf8,0x00,0xf1 = ldr.w pc, [pc, #256]
0x5f,0xf8,0x90,0xf1 = ldr.w pc, [pc, #-400]
0x1f,0xf8,0x00,0x90 = ldrb.w r9, [pc, #-0]
0x1f,0xf9,0x00,0xb0 = ldrsb.w r11, [pc, #-0]
0x3f,0xf8,0x00,0xa0 = ldrh.w r10, [pc, #-0]
0x3f,0xf9,0x00,0x10 = ldrsh.w r1, [pc, #-0]
0x5f,0xf8,0x00,0x50 = ldr.w r5, [pc, #-0]
0x58,0xf8,0x01,0x10 = ldr.w r1, [r8, r1]
0x55,0xf8,0x02,0x40 = ldr.w r4, [r5, r2]
0x50,0xf8,0x32,0x60 = ldr.w r6, [r0, r2, lsl #3]
0x58,0xf8,0x22,0x80 = ldr.w r8, [r8, r2, lsl #2]
0x5d,0xf8,0x12,0x70 = ldr.w r7, [sp, r2, lsl #1]
0x5d,0xf8,0x02,0x70 = ldr.w r7, [sp, r2]
0x15,0xf8,0x04,0x5c = ldrb r5, [r5, #-4]
0x96,0xf8,0x20,0x50 = ldrb.w r5, [r6, #32]
0x96,0xf8,0x21,0x50 = ldrb.w r5, [r6, #33]
0x96,0xf8,0x01,0x51 = ldrb.w r5, [r6, #257]
0x97,0xf8,0x01,0xe1 = ldrb.w lr, [r7, #257]
0x18,0xf8,0xff,0x5f = ldrb r5, [r8, #255]!
0x15,0xf8,0x04,0x2f = ldrb r2, [r5, #4]!
0x14,0xf8,0x04,0x1d = ldrb r1, [r4, #-4]!
0x13,0xf8,0xff,0xeb = ldrb lr, [r3], #255
0x12,0xf8,0x04,0x9b = ldrb r9, [r2], #4
0x1d,0xf8,0x04,0x39 = ldrb r3, [sp], #-4
0x18,0xf8,0x01,0x10 = ldrb.w r1, [r8, r1]
0x15,0xf8,0x02,0x40 = ldrb.w r4, [r5, r2]
0x10,0xf8,0x32,0x60 = ldrb.w r6, [r0, r2, lsl #3]
0x18,0xf8,0x22,0x80 = ldrb.w r8, [r8, r2, lsl #2]
0x1d,0xf8,0x12,0x70 = ldrb.w r7, [sp, r2, lsl #1]
0x1d,0xf8,0x02,0x70 = ldrb.w r7, [sp, r2]
0x12,0xf8,0x00,0x1e = ldrbt r1, [r2]
0x18,0xf8,0x00,0x1e = ldrbt r1, [r8]
0x18,0xf8,0x03,0x1e = ldrbt r1, [r8, #3]
0x18,0xf8,0xff,0x1e = ldrbt r1, [r8, #255]
0xd6,0xe9,0x06,0x35 = ldrd r3, r5, [r6, #24]
0xf6,0xe9,0x06,0x35 = ldrd r3, r5, [r6, #24]!
0xf6,0xe8,0x01,0x35 = ldrd r3, r5, [r6], #4
0x76,0xe8,0x02,0x35 = ldrd r3, r5, [r6], #-8
0xd6,0xe9,0x00,0x35 = ldrd r3, r5, [r6]
0xd3,0xe9,0x00,0x81 = ldrd r8, r1, [r3]
0x52,0xe9,0x00,0x01 = ldrd r0, r1, [r2, #-0]
0x72,0xe9,0x00,0x01 = ldrd r0, r1, [r2, #-0]!
0x72,0xe8,0x00,0x01 = ldrd r0, r1, [r2], #-0
0x54,0xe8,0x00,0x1f = ldrex r1, [r4]
0x54,0xe8,0x00,0x8f = ldrex r8, [r4]
0x5d,0xe8,0x20,0x2f = ldrex r2, [sp, #128]
0xd7,0xe8,0x4f,0x5f = ldrexb r5, [r7]
0xdc,0xe8,0x5f,0x9f = ldrexh r9, [r12]
0xd4,0xe8,0x7f,0x93 = ldrexd r9, r3, [r4]
0x35,0xf8,0x04,0x5c = ldrh r5, [r5, #-4]
0x35,0x8c = ldrh r5, [r6, #32]
0xb6,0xf8,0x21,0x50 = ldrh.w r5, [r6, #33]
0xb6,0xf8,0x01,0x51 = ldrh.w r5, [r6, #257]
0xb7,0xf8,0x01,0xe1 = ldrh.w lr, [r7, #257]
0x38,0xf8,0xff,0x5f = ldrh r5, [r8, #255]!
0x35,0xf8,0x04,0x2f = ldrh r2, [r5, #4]!
0x34,0xf8,0x04,0x1d = ldrh r1, [r4, #-4]!
0x33,0xf8,0xff,0xeb = ldrh lr, [r3], #255
0x32,0xf8,0x04,0x9b = ldrh r9, [r2], #4
0x3d,0xf8,0x04,0x39 = ldrh r3, [sp], #-4
0x38,0xf8,0x01,0x10 = ldrh.w r1, [r8, r1]
0x35,0xf8,0x02,0x40 = ldrh.w r4, [r5, r2]
0x30,0xf8,0x32,0x60 = ldrh.w r6, [r0, r2, lsl #3]
0x38,0xf8,0x22,0x80 = ldrh.w r8, [r8, r2, lsl #2]
0x3d,0xf8,0x12,0x70 = ldrh.w r7, [sp, r2, lsl #1]
0x3d,0xf8,0x02,0x70 = ldrh.w r7, [sp, r2]
0x32,0xf8,0x00,0x1e = ldrht r1, [r2]
0x38,0xf8,0x00,0x1e = ldrht r1, [r8]
0x38,0xf8,0x03,0x1e = ldrht r1, [r8, #3]
0x38,0xf8,0xff,0x1e = ldrht r1, [r8, #255]
0x15,0xf9,0x04,0x5c = ldrsb r5, [r5, #-4]
0x96,0xf9,0x20,0x50 = ldrsb.w r5, [r6, #32]
0x96,0xf9,0x21,0x50 = ldrsb.w r5, [r6, #33]
0x96,0xf9,0x01,0x51 = ldrsb.w r5, [r6, #257]
0x97,0xf9,0x01,0xe1 = ldrsb.w lr, [r7, #257]
0x18,0xf9,0x01,0x10 = ldrsb.w r1, [r8, r1]
0x15,0xf9,0x02,0x40 = ldrsb.w r4, [r5, r2]
0x10,0xf9,0x32,0x60 = ldrsb.w r6, [r0, r2, lsl #3]
0x18,0xf9,0x22,0x80 = ldrsb.w r8, [r8, r2, lsl #2]
0x1d,0xf9,0x12,0x70 = ldrsb.w r7, [sp, r2, lsl #1]
0x1d,0xf9,0x02,0x70 = ldrsb.w r7, [sp, r2]
0x18,0xf9,0xff,0x5f = ldrsb r5, [r8, #255]!
0x15,0xf9,0x04,0x2f = ldrsb r2, [r5, #4]!
0x14,0xf9,0x04,0x1d = ldrsb r1, [r4, #-4]!
0x13,0xf9,0xff,0xeb = ldrsb lr, [r3], #255
0x12,0xf9,0x04,0x9b = ldrsb r9, [r2], #4
0x1d,0xf9,0x04,0x39 = ldrsb r3, [sp], #-4
0x12,0xf9,0x00,0x1e = ldrsbt r1, [r2]
0x18,0xf9,0x00,0x1e = ldrsbt r1, [r8]
0x18,0xf9,0x03,0x1e = ldrsbt r1, [r8, #3]
0x18,0xf9,0xff,0x1e = ldrsbt r1, [r8, #255]
0x35,0xf9,0x04,0x5c = ldrsh r5, [r5, #-4]
0xb6,0xf9,0x20,0x50 = ldrsh.w r5, [r6, #32]
0xb6,0xf9,0x21,0x50 = ldrsh.w r5, [r6, #33]
0xb6,0xf9,0x01,0x51 = ldrsh.w r5, [r6, #257]
0xb7,0xf9,0x01,0xe1 = ldrsh.w lr, [r7, #257]
0x38,0xf9,0x01,0x10 = ldrsh.w r1, [r8, r1]
0x35,0xf9,0x02,0x40 = ldrsh.w r4, [r5, r2]
0x30,0xf9,0x32,0x60 = ldrsh.w r6, [r0, r2, lsl #3]
0x38,0xf9,0x22,0x80 = ldrsh.w r8, [r8, r2, lsl #2]
0x3d,0xf9,0x12,0x70 = ldrsh.w r7, [sp, r2, lsl #1]
0x3d,0xf9,0x02,0x70 = ldrsh.w r7, [sp, r2]
0x38,0xf9,0xff,0x5f = ldrsh r5, [r8, #255]!
0x35,0xf9,0x04,0x2f = ldrsh r2, [r5, #4]!
0x34,0xf9,0x04,0x1d = ldrsh r1, [r4, #-4]!
0x33,0xf9,0xff,0xeb = ldrsh lr, [r3], #255
0x32,0xf9,0x04,0x9b = ldrsh r9, [r2], #4
0x3d,0xf9,0x04,0x39 = ldrsh r3, [sp], #-4
0x32,0xf9,0x00,0x1e = ldrsht r1, [r2]
0x38,0xf9,0x00,0x1e = ldrsht r1, [r8]
0x38,0xf9,0x03,0x1e = ldrsht r1, [r8, #3]
0x38,0xf9,0xff,0x1e = ldrsht r1, [r8, #255]
0x52,0xf8,0x00,0x1e = ldrt r1, [r2]
0x56,0xf8,0x00,0x2e = ldrt r2, [r6]
0x57,0xf8,0x03,0x3e = ldrt r3, [r7, #3]
0x59,0xf8,0xff,0x4e = ldrt r4, [r9, #255]
0x4f,0xea,0x03,0x32 = lsl.w r2, r3, #12
0x5f,0xea,0xc3,0x78 = lsls.w r8, r3, #31
0x5f,0xea,0x43,0x02 = lsls.w r2, r3, #1
0x4f,0xea,0x03,0x12 = lsl.w r2, r3, #4
0x5f,0xea,0xcc,0x32 = lsls.w r2, r12, #15
0x4f,0xea,0xc3,0x43 = lsl.w r3, r3, #19
0x5f,0xea,0x88,0x08 = lsls.w r8, r8, #2
0x5f,0xea,0x47,0x17 = lsls.w r7, r7, #5
0x4f,0xea,0x4c,0x5c = lsl.w r12, r12, #21
0x04,0xfa,0x02,0xf3 = lsl.w r3, r4, r2
0x01,0xfa,0x02,0xf1 = lsl.w r1, r1, r2
0x14,0xfa,0x08,0xf3 = lsls.w r3, r4, r8
0x4f,0xea,0x13,0x32 = lsr.w r2, r3, #12
0x5f,0xea,0x13,0x08 = lsrs.w r8, r3, #32
0x5f,0xea,0x53,0x02 = lsrs.w r2, r3, #1
0x4f,0xea,0x13,0x12 = lsr.w r2, r3, #4
0x5f,0xea,0xdc,0x32 = lsrs.w r2, r12, #15
0x4f,0xea,0xd3,0x43 = lsr.w r3, r3, #19
0x5f,0xea,0x98,0x08 = lsrs.w r8, r8, #2
0x5f,0xea,0x57,0x17 = lsrs.w r7, r7, #5
0x4f,0xea,0x5c,0x5c = lsr.w r12, r12, #21
0x24,0xfa,0x02,0xf3 = lsr.w r3, r4, r2
0x21,0xfa,0x02,0xf1 = lsr.w r1, r1, r2
0x34,0xfa,0x08,0xf3 = lsrs.w r3, r4, r8
0x21,0xee,0x91,0x57 = mcr p7, #1, r5, c1, c1, #4
0x21,0xfe,0x91,0x57 = mcr2 p7, #1, r5, c1, c1, #4
0x00,0xee,0x15,0x4e = mcr p14, #0, r4, c0, c5, #0
0x41,0xfe,0x13,0x24 = mcr2 p4, #2, r2, c1, c3, #0
0x44,0xec,0xf1,0x57 = mcrr p7, #15, r5, r4, c1
0x44,0xfc,0xf1,0x57 = mcrr2 p7, #15, r5, r4, c1
0x02,0xfb,0x03,0x41 = mla r1, r2, r3, r4
0x02,0xfb,0x13,0x41 = mls r1, r2, r3, r4
0x15,0x21 = movs r1, #21
0x5f,0xf0,0x15,0x01 = movs.w r1, #21
0x5f,0xf0,0x15,0x08 = movs.w r8, #21
0x4f,0xf6,0xff,0x70 = movw r0, #65535
0x4a,0xf6,0x01,0x31 = movw r1, #43777
0x4a,0xf6,0x10,0x31 = movw r1, #43792
0x4f,0xf0,0x7f,0x70 = mov.w r0, #66846720
0x4f,0xf0,0x7f,0x70 = mov.w r0, #66846720
0x5f,0xf0,0x7f,0x70 = movs.w r0, #66846720
0x06,0xbf = itte eq
0x5f,0xf0,0x0c,0x01 = movseq.w r1, #12
0x0c,0x21 = moveq r1, #12
0x4f,0xf0,0x0c,0x01 = movne.w r1, #12
0x4f,0xf4,0xe1,0x76 = mov.w r6, #450
0x38,0xbf = it lo
0x4f,0xf0,0xff,0x31 = movlo.w r1, #-1
0x6f,0xf0,0x02,0x03 = mvn r3, #2
0x4a,0xf6,0xcd,0x3b = movw r11, #43981
0x01,0x20 = movs r0, #1
0x18,0xbf = it ne
0x0f,0x23 = movne r3, #15
0x04,0xbf = itt eq
0xff,0x20 = moveq r0, #255
0x40,0xf2,0x00,0x11 = movweq r1, #256
0x4f,0xea,0x02,0x46 = lsl.w r6, r2, #16
0x4f,0xea,0x12,0x46 = lsr.w r6, r2, #16
0x16,0x10 = asrs r6, r2, #32
0x5f,0xea,0x72,0x16 = rors.w r6, r2, #5
0xac,0x40 = lsls r4, r5
0xec,0x40 = lsrs r4, r5
0x2c,0x41 = asrs r4, r5
0xec,0x41 = rors r4, r5
0x04,0xfa,0x05,0xf4 = lsl.w r4, r4, r5
0x74,0xfa,0x08,0xf4 = rors.w r4, r4, r8
0x35,0xfa,0x06,0xf4 = lsrs.w r4, r5, r6
0x01,0xbf = itttt eq
0xac,0x40 = lsleq r4, r5
0xec,0x40 = lsreq r4, r5
0x2c,0x41 = asreq r4, r5
0xec,0x41 = roreq r4, r5
0x4f,0xea,0x34,0x04 = rrx r4, r4
0xc0,0xf2,0x07,0x03 = movt r3, #7
0xcf,0xf6,0xff,0x76 = movt r6, #65535
0x08,0xbf = it eq
0xc0,0xf6,0xf0,0x74 = movteq r4, #4080
0x11,0xee,0x92,0x1e = mrc p14, #0, r1, c1, c2, #4
0xff,0xee,0xd6,0xff = mrc p15, #7, apsr_nzcv, c15, c6, #6
0x32,0xee,0x12,0x19 = mrc p9, #1, r1, c2, c2, #0
0x73,0xfe,0x14,0x3c = mrc2 p12, #3, r3, c3, c4, #0
0x11,0xfe,0x92,0x1e = mrc2 p14, #0, r1, c1, c2, #4
0xff,0xfe,0x30,0xf8 = mrc2 p8, #7, apsr_nzcv, c15, c0, #1
0x54,0xec,0x11,0x57 = mrrc p7, #1, r5, r4, c1
0x54,0xfc,0x11,0x57 = mrrc2 p7, #1, r5, r4, c1
0xef,0xf3,0x00,0x88 = mrs r8, apsr
0xef,0xf3,0x00,0x88 = mrs r8, apsr
0xff,0xf3,0x00,0x88 = mrs r8, spsr
0x81,0xf3,0x00,0x88 = msr APSR_nzcvq, r1
0x82,0xf3,0x00,0x84 = msr APSR_g, r2
0x83,0xf3,0x00,0x88 = msr APSR_nzcvq, r3
0x84,0xf3,0x00,0x88 = msr APSR_nzcvq, r4
0x85,0xf3,0x00,0x8c = msr APSR_nzcvqg, r5
0x86,0xf3,0x00,0x89 = msr CPSR_fc, r6
0x87,0xf3,0x00,0x81 = msr CPSR_c, r7
0x88,0xf3,0x00,0x82 = msr CPSR_x, r8
0x89,0xf3,0x00,0x89 = msr CPSR_fc, r9
0x8b,0xf3,0x00,0x89 = msr CPSR_fc, r11
0x8c,0xf3,0x00,0x8e = msr CPSR_fsx, r12
0x90,0xf3,0x00,0x89 = msr SPSR_fc, r0
0x95,0xf3,0x00,0x8f = msr SPSR_fsxc, r5
0x88,0xf3,0x00,0x8f = msr CPSR_fsxc, r8
0x83,0xf3,0x00,0x89 = msr CPSR_fc, r3
0x63,0x43 = muls r3, r4, r3
0x04,0xfb,0x03,0xf3 = mul r3, r4, r3
0x04,0xfb,0x06,0xf3 = mul r3, r4, r6
0x08,0xbf = it eq
0x04,0xfb,0x05,0xf3 = muleq r3, r4, r5
0xd8,0xbf = it le
0x04,0xfb,0x08,0xf4 = mulle r4, r4, r8
0x06,0xfb,0x05,0xf5 = mul r5, r6, r5
0x7f,0xf0,0x15,0x08 = mvns r8, #21
0x6f,0xf0,0x7f,0x70 = mvn r0, #66846720
0x7f,0xf0,0x7f,0x70 = mvns r0, #66846720
0x06,0xbf = itte eq
0x7f,0xf0,0x0c,0x01 = mvnseq r1, #12
0x6f,0xf0,0x0c,0x01 = mvneq r1, #12
0x6f,0xf0,0x0c,0x01 = mvnne r1, #12
0x6f,0xea,0x03,0x02 = mvn.w r2, r3
0xda,0x43 = mvns r2, r3
0x6f,0xea,0xc6,0x45 = mvn.w r5, r6, lsl #19
0x6f,0xea,0x56,0x25 = mvn.w r5, r6, lsr #9
0x6f,0xea,0x26,0x15 = mvn.w r5, r6, asr #4
0x6f,0xea,0xb6,0x15 = mvn.w r5, r6, ror #6
0x6f,0xea,0x36,0x05 = mvn.w r5, r6, rrx
0x08,0xbf = it eq
0xda,0x43 = mvneq r2, r3
0xc2,0xf1,0x00,0x05 = rsb.w r5, r2, #0
0xc8,0xf1,0x00,0x05 = rsb.w r5, r8, #0
0xaf,0xf3,0x00,0x80 = nop.w 
0x65,0xf4,0x70,0x44 = orn r4, r5, #61440
0x65,0xea,0x06,0x04 = orn r4, r5, r6
0x75,0xea,0x06,0x04 = orns r4, r5, r6
0x65,0xea,0x46,0x14 = orn r4, r5, r6, lsl #5
0x75,0xea,0x56,0x14 = orns r4, r5, r6, lsr #5
0x65,0xea,0x56,0x14 = orn r4, r5, r6, lsr #5
0x75,0xea,0x66,0x14 = orns r4, r5, r6, asr #5
0x65,0xea,0x76,0x14 = orn r4, r5, r6, ror #5
0x45,0xf4,0x70,0x44 = orr r4, r5, #61440
0x45,0xea,0x06,0x04 = orr.w r4, r5, r6
0x45,0xea,0x46,0x14 = orr.w r4, r5, r6, lsl #5
0x55,0xea,0x56,0x14 = orrs.w r4, r5, r6, lsr #5
0x45,0xea,0x56,0x14 = orr.w r4, r5, r6, lsr #5
0x55,0xea,0x66,0x14 = orrs.w r4, r5, r6, asr #5
0x45,0xea,0x76,0x14 = orr.w r4, r5, r6, ror #5
0xc2,0xea,0x03,0x02 = pkhbt r2, r2, r3
0xc2,0xea,0xc3,0x72 = pkhbt r2, r2, r3, lsl #31
0xc2,0xea,0x03,0x02 = pkhbt r2, r2, r3
0xc2,0xea,0xc3,0x32 = pkhbt r2, r2, r3, lsl #15
0xc2,0xea,0x03,0x02 = pkhbt r2, r2, r3
0xc2,0xea,0xe3,0x72 = pkhtb r2, r2, r3, asr #31
0xc2,0xea,0xe3,0x32 = pkhtb r2, r2, r3, asr #15
0x15,0xf8,0x04,0xfc = pld [r5, #-4]
0x96,0xf8,0x20,0xf0 = pld [r6, #32]
0x96,0xf8,0x21,0xf0 = pld [r6, #33]
0x96,0xf8,0x01,0xf1 = pld [r6, #257]
0x97,0xf8,0x01,0xf1 = pld [r7, #257]
0x91,0xf8,0x00,0xf0 = pld [r1]
0x11,0xf8,0x00,0xfc = pld [r1, #-0]
0x1f,0xf8,0xff,0xff = pld [pc, #-4095]
0x18,0xf8,0x01,0xf0 = pld [r8, r1]
0x15,0xf8,0x02,0xf0 = pld [r5, r2]
0x10,0xf8,0x32,0xf0 = pld [r0, r2, lsl #3]
0x18,0xf8,0x22,0xf0 = pld [r8, r2, lsl #2]
0x1d,0xf8,0x12,0xf0 = pld [sp, r2, lsl #1]
0x1d,0xf8,0x02,0xf0 = pld [sp, r2]
0x15,0xf9,0x04,0xfc = pli [r5, #-4]
0x96,0xf9,0x20,0xf0 = pli [r6, #32]
0x96,0xf9,0x21,0xf0 = pli [r6, #33]
0x96,0xf9,0x01,0xf1 = pli [r6, #257]
0x97,0xf9,0x01,0xf1 = pli [r7, #257]
0x9f,0xf9,0xff,0xff = pli [pc, #4095]
0x1f,0xf9,0xff,0xff = pli [pc, #-4095]
0x18,0xf9,0x01,0xf0 = pli [r8, r1]
0x15,0xf9,0x02,0xf0 = pli [r5, r2]
0x10,0xf9,0x32,0xf0 = pli [r0, r2, lsl #3]
0x18,0xf9,0x22,0xf0 = pli [r8, r2, lsl #2]
0x1d,0xf9,0x12,0xf0 = pli [sp, r2, lsl #1]
0x1d,0xf9,0x02,0xf0 = pli [sp, r2]
0xbd,0xe8,0x04,0x02 = pop.w {r2, r9}
0x2d,0xe9,0x04,0x02 = push.w {r2, r9}
0x83,0xfa,0x82,0xf1 = qadd r1, r2, r3
0x92,0xfa,0x13,0xf1 = qadd16 r1, r2, r3
0x82,0xfa,0x13,0xf1 = qadd8 r1, r2, r3
0xc6,0xbf = itte gt
0x83,0xfa,0x82,0xf1 = qaddgt r1, r2, r3
0x92,0xfa,0x13,0xf1 = qadd16gt r1, r2, r3
0x82,0xfa,0x13,0xf1 = qadd8le r1, r2, r3
0x88,0xfa,0x97,0xf6 = qdadd r6, r7, r8
0x88,0xfa,0xb7,0xf6 = qdsub r6, r7, r8
0x84,0xbf = itt hi
0x88,0xfa,0x97,0xf6 = qdaddhi r6, r7, r8
0x88,0xfa,0xb7,0xf6 = qdsubhi r6, r7, r8
0xec,0xfa,0x10,0xf9 = qsax r9, r12, r0
0x08,0xbf = it eq
0xec,0xfa,0x10,0xf9 = qsaxeq r9, r12, r0
0x83,0xfa,0xa2,0xf1 = qsub r1, r2, r3
0xd2,0xfa,0x13,0xf1 = qsub16 r1, r2, r3
0xc2,0xfa,0x13,0xf1 = qsub8 r1, r2, r3
0xd6,0xbf = itet le
0x83,0xfa,0xa2,0xf1 = qsuble r1, r2, r3
0xd2,0xfa,0x13,0xf1 = qsub16gt r1, r2, r3
0xc2,0xfa,0x13,0xf1 = qsub8le r1, r2, r3
0x92,0xfa,0xa2,0xf1 = rbit r1, r2
0x18,0xbf = it ne
0x92,0xfa,0xa2,0xf1 = rbitne r1, r2
0x92,0xfa,0x82,0xf1 = rev.w r1, r2
0x98,0xfa,0x88,0xf2 = rev.w r2, r8
0x1c,0xbf = itt ne
0x11,0xba = revne r1, r2
0x98,0xfa,0x88,0xf1 = revne.w r1, r8
0x92,0xfa,0x92,0xf1 = rev16.w r1, r2
0x98,0xfa,0x98,0xf2 = rev16.w r2, r8
0x1c,0xbf = itt ne
0x51,0xba = rev16ne r1, r2
0x98,0xfa,0x98,0xf1 = rev16ne.w r1, r8
0x92,0xfa,0xb2,0xf1 = revsh.w r1, r2
0x98,0xfa,0xb8,0xf2 = revsh.w r2, r8
0x1c,0xbf = itt ne
0xd1,0xba = revshne r1, r2
0x98,0xfa,0xb8,0xf1 = revshne.w r1, r8
0x4f,0xea,0x33,0x32 = ror.w r2, r3, #12
0x5f,0xea,0xf3,0x78 = rors.w r8, r3, #31
0x5f,0xea,0x73,0x02 = rors.w r2, r3, #1
0x4f,0xea,0x33,0x12 = ror.w r2, r3, #4
0x5f,0xea,0xfc,0x32 = rors.w r2, r12, #15
0x4f,0xea,0xf3,0x43 = ror.w r3, r3, #19
0x5f,0xea,0xb8,0x08 = rors.w r8, r8, #2
0x5f,0xea,0x77,0x17 = rors.w r7, r7, #5
0x4f,0xea,0x7c,0x5c = ror.w r12, r12, #21
0x64,0xfa,0x02,0xf3 = ror.w r3, r4, r2
0x61,0xfa,0x02,0xf1 = ror.w r1, r1, r2
0x74,0xfa,0x08,0xf3 = rors.w r3, r4, r8
0x4f,0xea,0x32,0x01 = rrx r1, r2
0x5f,0xea,0x32,0x01 = rrxs r1, r2
0xb4,0xbf = ite lt
0x4f,0xea,0x3c,0x09 = rrxlt r9, r12
0x5f,0xea,0x33,0x08 = rrxsge r8, r3
0xc5,0xf5,0x7f,0x22 = rsb.w r2, r5, #1044480
0xdc,0xf1,0x0f,0x03 = rsbs.w r3, r12, #15
0xc1,0xf1,0xff,0x01 = rsb.w r1, r1, #255
0xc1,0xf1,0xff,0x01 = rsb.w r1, r1, #255
0xcb,0xf1,0x00,0x0b = rsb.w r11, r11, #0
0xc9,0xf1,0x00,0x09 = rsb.w r9, r9, #0
0x4b,0x42 = rsbs r3, r1, #0
0xc1,0xf1,0x00,0x03 = rsb.w r3, r1, #0
0xc4,0xeb,0x08,0x04 = rsb r4, r4, r8
0xc9,0xeb,0x08,0x04 = rsb r4, r9, r8
0xc4,0xeb,0xe8,0x01 = rsb r1, r4, r8, asr #3
0xd1,0xeb,0x47,0x02 = rsbs r2, r1, r7, lsl #1
0x94,0xfa,0x08,0xf3 = sadd16 r3, r4, r8
0x18,0xbf = it ne
0x94,0xfa,0x08,0xf3 = sadd16ne r3, r4, r8
0x84,0xfa,0x08,0xf3 = sadd8 r3, r4, r8
0x18,0xbf = it ne
0x84,0xfa,0x08,0xf3 = sadd8ne r3, r4, r8
0xa2,0xfa,0x07,0xf9 = sasx r9, r2, r7
0x18,0xbf = it ne
0xa5,0xfa,0x06,0xf2 = sasxne r2, r5, r6
0xa2,0xfa,0x07,0xf9 = sasx r9, r2, r7
0x18,0xbf = it ne
0xa5,0xfa,0x06,0xf2 = sasxne r2, r5, r6
0x61,0xf1,0x04,0x00 = sbc r0, r1, #4
0x71,0xf1,0x00,0x00 = sbcs r0, r1, #0
0x62,0xf1,0xff,0x01 = sbc r1, r2, #255
0x67,0xf1,0x55,0x13 = sbc r3, r7, #5570645
0x6c,0xf1,0xaa,0x28 = sbc r8, r12, #2852170240
0x67,0xf1,0xa5,0x39 = sbc r9, r7, #2779096485
0x63,0xf1,0x07,0x45 = sbc r5, r3, #2264924160
0x62,0xf1,0xff,0x44 = sbc r4, r2, #2139095040
0x62,0xf5,0xd0,0x64 = sbc r4, r2, #1664
0x65,0xeb,0x06,0x04 = sbc.w r4, r5, r6
0x75,0xeb,0x06,0x04 = sbcs.w r4, r5, r6
0x61,0xeb,0x03,0x09 = sbc.w r9, r1, r3
0x71,0xeb,0x03,0x09 = sbcs.w r9, r1, r3
0x61,0xeb,0x33,0x10 = sbc.w r0, r1, r3, ror #4
0x71,0xeb,0xc3,0x10 = sbcs.w r0, r1, r3, lsl #7
0x61,0xeb,0xd3,0x70 = sbc.w r0, r1, r3, lsr #31
0x71,0xeb,0x23,0x00 = sbcs.w r0, r1, r3, asr #32
0x45,0xf3,0x00,0x44 = sbfx r4, r5, #16, #1
0xc8,0xbf = it gt
0x45,0xf3,0x0f,0x44 = sbfxgt r4, r5, #16, #16
0xa9,0xfa,0x82,0xf5 = sel r5, r9, r2
0xd8,0xbf = it le
0xa9,0xfa,0x82,0xf5 = selle r5, r9, r2
0xaf,0xf3,0x04,0x80 = sev.w 
0x08,0xbf = it eq
0xaf,0xf3,0x04,0x80 = seveq.w 
0x92,0xfa,0x03,0xf1 = sadd16 r1, r2, r3
0x82,0xfa,0x03,0xf1 = sadd8 r1, r2, r3
0xcc,0xbf = ite gt
0x92,0xfa,0x03,0xf1 = sadd16gt r1, r2, r3
0x82,0xfa,0x03,0xf1 = sadd8le r1, r2, r3
0xa8,0xfa,0x22,0xf4 = shasx r4, r8, r2
0xc8,0xbf = it gt
0xa8,0xfa,0x22,0xf4 = shasxgt r4, r8, r2
0xa8,0xfa,0x22,0xf4 = shasx r4, r8, r2
0xc8,0xbf = it gt
0xa8,0xfa,0x22,0xf4 = shasxgt r4, r8, r2
0xe8,0xfa,0x22,0xf4 = shsax r4, r8, r2
0xc8,0xbf = it gt
0xe8,0xfa,0x22,0xf4 = shsaxgt r4, r8, r2
0xe8,0xfa,0x22,0xf4 = shsax r4, r8, r2
0xc8,0xbf = it gt
0xe8,0xfa,0x22,0xf4 = shsaxgt r4, r8, r2
0xd8,0xfa,0x22,0xf4 = shsub16 r4, r8, r2
0xc8,0xfa,0x22,0xf4 = shsub8 r4, r8, r2
0xc4,0xbf = itt gt
0xd8,0xfa,0x22,0xf4 = shsub16gt r4, r8, r2
0xc8,0xfa,0x22,0xf4 = shsub8gt r4, r8, r2
0x11,0xfb,0x09,0x03 = smlabb r3, r1, r9, r0
0x16,0xfb,0x14,0x15 = smlabt r5, r6, r4, r1
0x12,0xfb,0x23,0x24 = smlatb r4, r2, r3, r2
0x13,0xfb,0x38,0x48 = smlatt r8, r3, r8, r4
0xcb,0xbf = itete gt
0x11,0xfb,0x09,0x03 = smlabbgt r3, r1, r9, r0
0x16,0xfb,0x14,0x15 = smlabtle r5, r6, r4, r1
0x12,0xfb,0x23,0x24 = smlatbgt r4, r2, r3, r2
0x13,0xfb,0x38,0x48 = smlattle r8, r3, r8, r4
0x23,0xfb,0x05,0x82 = smlad r2, r3, r5, r8
0x23,0xfb,0x15,0x82 = smladx r2, r3, r5, r8
0x84,0xbf = itt hi
0x23,0xfb,0x05,0x82 = smladhi r2, r3, r5, r8
0x23,0xfb,0x15,0x82 = smladxhi r2, r3, r5, r8
0xc5,0xfb,0x08,0x23 = smlal r2, r3, r5, r8
0x08,0xbf = it eq
0xc5,0xfb,0x08,0x23 = smlaleq r2, r3, r5, r8
0xc9,0xfb,0x80,0x31 = smlalbb r3, r1, r9, r0
0xc4,0xfb,0x91,0x56 = smlalbt r5, r6, r4, r1
0xc3,0xfb,0xa2,0x42 = smlaltb r4, r2, r3, r2
0xc8,0xfb,0xb4,0x83 = smlaltt r8, r3, r8, r4
0xad,0xbf = iteet ge
0xc9,0xfb,0x80,0x31 = smlalbbge r3, r1, r9, r0
0xc4,0xfb,0x91,0x56 = smlalbtlt r5, r6, r4, r1
0xc3,0xfb,0xa2,0x42 = smlaltblt r4, r2, r3, r2
0xc8,0xfb,0xb4,0x83 = smlalttge r8, r3, r8, r4
0xc5,0xfb,0xc8,0x23 = smlald r2, r3, r5, r8
0xc5,0xfb,0xd8,0x23 = smlaldx r2, r3, r5, r8
0x0c,0xbf = ite eq
0xc5,0xfb,0xc8,0x23 = smlaldeq r2, r3, r5, r8
0xc5,0xfb,0xd8,0x23 = smlaldxne r2, r3, r5, r8
0x33,0xfb,0x0a,0x82 = smlawb r2, r3, r10, r8
0x33,0xfb,0x15,0x98 = smlawt r8, r3, r5, r9
0x0c,0xbf = ite eq
0x37,0xfb,0x05,0x82 = smlawbeq r2, r7, r5, r8
0x33,0xfb,0x10,0x81 = smlawtne r1, r3, r0, r8
0x43,0xfb,0x05,0x82 = smlsd r2, r3, r5, r8
0x43,0xfb,0x15,0x82 = smlsdx r2, r3, r5, r8
0xd4,0xbf = ite le
0x43,0xfb,0x05,0x82 = smlsdle r2, r3, r5, r8
0x43,0xfb,0x15,0x82 = smlsdxgt r2, r3, r5, r8
0xd5,0xfb,0xc1,0x29 = smlsld r2, r9, r5, r1
0xd2,0xfb,0xd8,0x4b = smlsldx r4, r11, r2, r8
0xac,0xbf = ite ge
0xd5,0xfb,0xc6,0x82 = smlsldge r8, r2, r5, r6
0xd3,0xfb,0xd8,0x10 = smlsldxlt r1, r0, r3, r8
0x52,0xfb,0x03,0x41 = smmla r1, r2, r3, r4
0x53,0xfb,0x12,0x14 = smmlar r4, r3, r2, r1
0x34,0xbf = ite lo
0x52,0xfb,0x03,0x41 = smmlalo r1, r2, r3, r4
0x53,0xfb,0x12,0x14 = smmlarhs r4, r3, r2, r1
0x62,0xfb,0x03,0x41 = smmls r1, r2, r3, r4
0x63,0xfb,0x12,0x14 = smmlsr r4, r3, r2, r1
0x34,0xbf = ite lo
0x62,0xfb,0x03,0x41 = smmlslo r1, r2, r3, r4
0x63,0xfb,0x12,0x14 = smmlsrhs r4, r3, r2, r1
0x53,0xfb,0x04,0xf2 = smmul r2, r3, r4
0x52,0xfb,0x11,0xf3 = smmulr r3, r2, r1
0x34,0xbf = ite lo
0x53,0xfb,0x04,0xf2 = smmullo r2, r3, r4
0x52,0xfb,0x11,0xf3 = smmulrhs r3, r2, r1
0x23,0xfb,0x04,0xf2 = smuad r2, r3, r4
0x22,0xfb,0x11,0xf3 = smuadx r3, r2, r1
0xb4,0xbf = ite lt
0x23,0xfb,0x04,0xf2 = smuadlt r2, r3, r4
0x22,0xfb,0x11,0xf3 = smuadxge r3, r2, r1
0x19,0xfb,0x00,0xf3 = smulbb r3, r9, r0
0x14,0xfb,0x11,0xf5 = smulbt r5, r4, r1
0x12,0xfb,0x22,0xf4 = smultb r4, r2, r2
0x13,0xfb,0x34,0xf8 = smultt r8, r3, r4
0xab,0xbf = itete ge
0x19,0xfb,0x00,0xf1 = smulbbge r1, r9, r0
0x16,0xfb,0x14,0xf5 = smulbtlt r5, r6, r4
0x13,0xfb,0x22,0xf2 = smultbge r2, r3, r2
0x13,0xfb,0x34,0xf8 = smulttlt r8, r3, r4
0x80,0xfb,0x01,0x39 = smull r3, r9, r0, r1
0x08,0xbf = it eq
0x84,0xfb,0x05,0x83 = smulleq r8, r3, r4, r5
0x39,0xfb,0x00,0xf3 = smulwb r3, r9, r0
0x39,0xfb,0x12,0xf3 = smulwt r3, r9, r2
0xcc,0xbf = ite gt
0x39,0xfb,0x00,0xf3 = smulwbgt r3, r9, r0
0x39,0xfb,0x12,0xf3 = smulwtle r3, r9, r2
0x40,0xfb,0x01,0xf3 = smusd r3, r0, r1
0x49,0xfb,0x12,0xf3 = smusdx r3, r9, r2
0x0c,0xbf = ite eq
0x43,0xfb,0x02,0xf8 = smusdeq r8, r3, r2
0x44,0xfb,0x13,0xf7 = smusdxne r7, r4, r3
0x0d,0xe8,0x01,0xc0 = srsdb sp, #1
0x8d,0xe9,0x00,0xc0 = srsia sp, #0
0x2d,0xe8,0x13,0xc0 = srsdb sp!, #19
0xad,0xe9,0x02,0xc0 = srsia sp!, #2
0x8d,0xe9,0x0a,0xc0 = srsia sp, #10
0x0d,0xe8,0x09,0xc0 = srsdb sp, #9
0xad,0xe9,0x05,0xc0 = srsia sp!, #5
0x2d,0xe8,0x05,0xc0 = srsdb sp!, #5
0x8d,0xe9,0x05,0xc0 = srsia sp, #5
0xad,0xe9,0x05,0xc0 = srsia sp!, #5
0x0d,0xe8,0x01,0xc0 = srsdb sp, #1
0x8d,0xe9,0x00,0xc0 = srsia sp, #0
0x2d,0xe8,0x13,0xc0 = srsdb sp!, #19
0xad,0xe9,0x02,0xc0 = srsia sp!, #2
0x8d,0xe9,0x0a,0xc0 = srsia sp, #10
0x0d,0xe8,0x09,0xc0 = srsdb sp, #9
0xad,0xe9,0x05,0xc0 = srsia sp!, #5
0x2d,0xe8,0x05,0xc0 = srsdb sp!, #5
0x8d,0xe9,0x05,0xc0 = srsia sp, #5
0xad,0xe9,0x05,0xc0 = srsia sp!, #5
0x0a,0xf3,0x00,0x08 = ssat r8, #1, r10
0x0a,0xf3,0x00,0x08 = ssat r8, #1, r10
0x0a,0xf3,0xc0,0x78 = ssat r8, #1, r10, lsl #31
0x2a,0xf3,0x40,0x08 = ssat r8, #1, r10, asr #1
0x27,0xf3,0x00,0x02 = ssat16 r2, #1, r7
0x25,0xf3,0x0f,0x03 = ssat16 r3, #16, r5
0xe3,0xfa,0x04,0xf2 = ssax r2, r3, r4
0xb8,0xbf = it lt
0xe3,0xfa,0x04,0xf2 = ssaxlt r2, r3, r4
0xe3,0xfa,0x04,0xf2 = ssax r2, r3, r4
0xb8,0xbf = it lt
0xe3,0xfa,0x04,0xf2 = ssaxlt r2, r3, r4
0xd0,0xfa,0x06,0xf1 = ssub16 r1, r0, r6
0xc2,0xfa,0x04,0xf9 = ssub8 r9, r2, r4
0x14,0xbf = ite ne
0xd3,0xfa,0x02,0xf5 = ssub16ne r5, r3, r2
0xc1,0xfa,0x02,0xf5 = ssub8eq r5, r1, r2
0x81,0xfd,0x01,0x80 = stc2 p0, c8, [r1, #4]
0x82,0xfd,0x00,0x71 = stc2 p1, c7, [r2]
0x03,0xfd,0x38,0x62 = stc2 p2, c6, [r3, #-224]
0x24,0xfd,0x1e,0x53 = stc2 p3, c5, [r4, #-120]!
0xa5,0xfc,0x04,0x44 = stc2 p4, c4, [r5], #16
0x26,0xfc,0x12,0x35 = stc2 p5, c3, [r6], #-72
0xc7,0xfd,0x01,0x26 = stc2l p6, c2, [r7, #4]
0xc8,0xfd,0x00,0x17 = stc2l p7, c1, [r8]
0x49,0xfd,0x38,0x08 = stc2l p8, c0, [r9, #-224]
0x6a,0xfd,0x1e,0x19 = stc2l p9, c1, [r10, #-120]!
0xeb,0xfc,0x04,0x20 = stc2l p0, c2, [r11], #16
0x6c,0xfc,0x12,0x31 = stc2l p1, c3, [r12], #-72
0x80,0xed,0x01,0x4c = stc p12, c4, [r0, #4]
0x81,0xed,0x00,0x5d = stc p13, c5, [r1]
0x02,0xed,0x38,0x6e = stc p14, c6, [r2, #-224]
0x23,0xed,0x1e,0x7f = stc p15, c7, [r3, #-120]!
0xa4,0xec,0x04,0x85 = stc p5, c8, [r4], #16
0x25,0xec,0x12,0x94 = stc p4, c9, [r5], #-72
0xc6,0xed,0x01,0xa3 = stcl p3, c10, [r6, #4]
0xc7,0xed,0x00,0xb2 = stcl p2, c11, [r7]
0x48,0xed,0x38,0xc1 = stcl p1, c12, [r8, #-224]
0x69,0xed,0x1e,0xd0 = stcl p0, c13, [r9, #-120]!
0xea,0xec,0x04,0xe6 = stcl p6, c14, [r10], #16
0x6b,0xec,0x12,0xf7 = stcl p7, c15, [r11], #-72
0x81,0xfc,0x19,0x82 = stc2 p2, c8, [r1], {25}
0x84,0xe8,0x30,0x03 = stm.w r4, {r4, r5, r8, r9}
0x84,0xe8,0x60,0x00 = stm.w r4, {r5, r6}
0xa5,0xe8,0x08,0x01 = stm.w r5!, {r3, r8}
0x84,0xe8,0x30,0x03 = stm.w r4, {r4, r5, r8, r9}
0x84,0xe8,0x60,0x00 = stm.w r4, {r5, r6}
0xa5,0xe8,0x08,0x01 = stm.w r5!, {r3, r8}
0xa5,0xe8,0x06,0x00 = stm.w r5!, {r1, r2}
0x82,0xe8,0x06,0x00 = stm.w r2, {r1, r2}
0x84,0xe8,0x30,0x03 = stm.w r4, {r4, r5, r8, r9}
0x84,0xe8,0x60,0x00 = stm.w r4, {r5, r6}
0xa5,0xe8,0x08,0x01 = stm.w r5!, {r3, r8}
0x84,0xe8,0x30,0x03 = stm.w r4, {r4, r5, r8, r9}
0x84,0xe8,0x60,0x00 = stm.w r4, {r5, r6}
0xa5,0xe8,0x08,0x01 = stm.w r5!, {r3, r8}
0xa5,0xe8,0x08,0x01 = stm.w r5!, {r3, r8}
0x04,0xe9,0x30,0x03 = stmdb r4, {r4, r5, r8, r9}
0x04,0xe9,0x60,0x00 = stmdb r4, {r5, r6}
0x25,0xe9,0x08,0x01 = stmdb r5!, {r3, r8}
0xa5,0xe8,0x08,0x01 = stm.w r5!, {r3, r8}
0x05,0xe9,0x03,0x00 = stmdb r5, {r0, r1}
0x45,0xf8,0x04,0x5c = str r5, [r5, #-4]
0x35,0x62 = str r5, [r6, #32]
0xc6,0xf8,0x21,0x50 = str.w r5, [r6, #33]
0xc6,0xf8,0x01,0x51 = str.w r5, [r6, #257]
0xc7,0xf8,0x01,0xf1 = str.w pc, [r7, #257]
0x44,0xf8,0xff,0x2f = str r2, [r4, #255]!
0x4d,0xf8,0x04,0x8f = str r8, [sp, #4]!
0x4d,0xf8,0x04,0xed = str lr, [sp, #-4]!
0x44,0xf8,0xff,0x2b = str r2, [r4], #255
0x4d,0xf8,0x04,0x8b = str r8, [sp], #4
0x4d,0xf8,0x04,0xe9 = str lr, [sp], #-4
0x48,0xf8,0x01,0x10 = str.w r1, [r8, r1]
0x45,0xf8,0x02,0x40 = str.w r4, [r5, r2]
0x40,0xf8,0x32,0x60 = str.w r6, [r0, r2, lsl #3]
0x48,0xf8,0x22,0x80 = str.w r8, [r8, r2, lsl #2]
0x4d,0xf8,0x12,0x70 = str.w r7, [sp, r2, lsl #1]
0x4d,0xf8,0x02,0x70 = str.w r7, [sp, r2]
0x05,0xf8,0x04,0x5c = strb r5, [r5, #-4]
0x86,0xf8,0x20,0x50 = strb.w r5, [r6, #32]
0x86,0xf8,0x21,0x50 = strb.w r5, [r6, #33]
0x86,0xf8,0x01,0x51 = strb.w r5, [r6, #257]
0x87,0xf8,0x01,0xe1 = strb.w lr, [r7, #257]
0x08,0xf8,0xff,0x5f = strb r5, [r8, #255]!
0x05,0xf8,0x04,0x2f = strb r2, [r5, #4]!
0x04,0xf8,0x04,0x1d = strb r1, [r4, #-4]!
0x03,0xf8,0xff,0xeb = strb lr, [r3], #255
0x02,0xf8,0x04,0x9b = strb r9, [r2], #4
0x0d,0xf8,0x04,0x39 = strb r3, [sp], #-4
0x08,0xf8,0x00,0x4d = strb r4, [r8, #-0]!
0x00,0xf8,0x00,0x19 = strb r1, [r0], #-0
0x08,0xf8,0x01,0x10 = strb.w r1, [r8, r1]
0x05,0xf8,0x02,0x40 = strb.w r4, [r5, r2]
0x00,0xf8,0x32,0x60 = strb.w r6, [r0, r2, lsl #3]
0x08,0xf8,0x22,0x80 = strb.w r8, [r8, r2, lsl #2]
0x0d,0xf8,0x12,0x70 = strb.w r7, [sp, r2, lsl #1]
0x0d,0xf8,0x02,0x70 = strb.w r7, [sp, r2]
0x02,0xf8,0x00,0x1e = strbt r1, [r2]
0x08,0xf8,0x00,0x1e = strbt r1, [r8]
0x08,0xf8,0x03,0x1e = strbt r1, [r8, #3]
0x08,0xf8,0xff,0x1e = strbt r1, [r8, #255]
0xc6,0xe9,0x06,0x35 = strd r3, r5, [r6, #24]
0xe6,0xe9,0x06,0x35 = strd r3, r5, [r6, #24]!
0xe6,0xe8,0x01,0x35 = strd r3, r5, [r6], #4
0x66,0xe8,0x02,0x35 = strd r3, r5, [r6], #-8
0xc6,0xe9,0x00,0x35 = strd r3, r5, [r6]
0xc3,0xe9,0x00,0x81 = strd r8, r1, [r3]
0x42,0xe9,0x00,0x01 = strd r0, r1, [r2, #-0]
0x62,0xe9,0x00,0x01 = strd r0, r1, [r2, #-0]!
0x62,0xe8,0x00,0x01 = strd r0, r1, [r2], #-0
0x44,0xe8,0x00,0x81 = strex r1, r8, [r4]
0x44,0xe8,0x00,0x28 = strex r8, r2, [r4]
0x4d,0xe8,0x20,0xc2 = strex r2, r12, [sp, #128]
0xc7,0xe8,0x45,0x1f = strexb r5, r1, [r7]
0xcc,0xe8,0x59,0x7f = strexh r9, r7, [r12]
0xc4,0xe8,0x79,0x36 = strexd r9, r3, r6, [r4]
0x25,0xf8,0x04,0x5c = strh r5, [r5, #-4]
0x35,0x84 = strh r5, [r6, #32]
0xa6,0xf8,0x21,0x50 = strh.w r5, [r6, #33]
0xa6,0xf8,0x01,0x51 = strh.w r5, [r6, #257]
0xa7,0xf8,0x01,0xe1 = strh.w lr, [r7, #257]
0x28,0xf8,0xff,0x5f = strh r5, [r8, #255]!
0x25,0xf8,0x04,0x2f = strh r2, [r5, #4]!
0x24,0xf8,0x04,0x1d = strh r1, [r4, #-4]!
0x23,0xf8,0xff,0xeb = strh lr, [r3], #255
0x22,0xf8,0x04,0x9b = strh r9, [r2], #4
0x2d,0xf8,0x04,0x39 = strh r3, [sp], #-4
0x28,0xf8,0x01,0x10 = strh.w r1, [r8, r1]
0x25,0xf8,0x02,0x40 = strh.w r4, [r5, r2]
0x20,0xf8,0x32,0x60 = strh.w r6, [r0, r2, lsl #3]
0x28,0xf8,0x22,0x80 = strh.w r8, [r8, r2, lsl #2]
0x2d,0xf8,0x12,0x70 = strh.w r7, [sp, r2, lsl #1]
0x2d,0xf8,0x02,0x70 = strh.w r7, [sp, r2]
0x22,0xf8,0x00,0x1e = strht r1, [r2]
0x28,0xf8,0x00,0x1e = strht r1, [r8]
0x28,0xf8,0x03,0x1e = strht r1, [r8, #3]
0x28,0xf8,0xff,0x1e = strht r1, [r8, #255]
0x42,0xf8,0x00,0x1e = strt r1, [r2]
0x48,0xf8,0x00,0x1e = strt r1, [r8]
0x48,0xf8,0x03,0x1e = strt r1, [r8, #3]
0x48,0xf8,0xff,0x1e = strt r1, [r8, #255]
0x0a,0xbf = itet eq
0x11,0x1f = subeq r1, r2, #4
0xa3,0xf2,0xff,0x35 = subwne r5, r3, #1023
0xa5,0xf2,0x25,0x14 = subweq r4, r5, #293
0xad,0xf5,0x80,0x62 = sub.w r2, sp, #1024
0xa8,0xf5,0x7f,0x42 = sub.w r2, r8, #65280
0xa3,0xf2,0x01,0x12 = subw r2, r3, #257
0xa3,0xf2,0x01,0x12 = subw r2, r3, #257
0xa6,0xf5,0x80,0x7c = sub.w r12, r6, #256
0xa6,0xf2,0x00,0x1c = subw r12, r6, #256
0xb2,0xf5,0xf8,0x71 = subs.w r1, r2, #496
0xa2,0xf1,0x01,0x02 = sub.w r2, r2, #1
0xa0,0xf1,0x20,0x00 = sub.w r0, r0, #32
0x38,0x3a = subs r2, #56
0x38,0x3a = subs r2, #56
0xa5,0xeb,0x06,0x04 = sub.w r4, r5, r6
0xa5,0xeb,0x46,0x14 = sub.w r4, r5, r6, lsl #5
0xa5,0xeb,0x56,0x14 = sub.w r4, r5, r6, lsr #5
0xa5,0xeb,0x56,0x14 = sub.w r4, r5, r6, lsr #5
0xa5,0xeb,0x66,0x14 = sub.w r4, r5, r6, asr #5
0xa5,0xeb,0x76,0x14 = sub.w r4, r5, r6, ror #5
0xa2,0xeb,0x3c,0x05 = sub.w r5, r2, r12, rrx
0xad,0xeb,0x0c,0x02 = sub.w r2, sp, r12
0xad,0xeb,0x0c,0x0d = sub.w sp, sp, r12
0xad,0xeb,0x0c,0x0d = sub.w sp, sp, r12
0xad,0xeb,0x0c,0x02 = sub.w r2, sp, r12
0xad,0xeb,0x0c,0x0d = sub.w sp, sp, r12
0xad,0xeb,0x0c,0x0d = sub.w sp, sp, r12
0x00,0xdf = svc #0
0x0c,0xbf = ite eq
0xff,0xdf = svceq #255
0x21,0xdf = svcne #33
0x43,0xfa,0x84,0xf2 = sxtab r2, r3, r4
0x45,0xfa,0x86,0xf4 = sxtab r4, r5, r6
0xb8,0xbf = it lt
0x42,0xfa,0x99,0xf6 = sxtablt r6, r2, r9, ror #8
0x41,0xfa,0xa4,0xf5 = sxtab r5, r1, r4, ror #16
0x48,0xfa,0xb3,0xf7 = sxtab r7, r8, r3, ror #24
0x22,0xfa,0x87,0xf6 = sxtab16 r6, r2, r7
0x25,0xfa,0x98,0xf3 = sxtab16 r3, r5, r8, ror #8
0x22,0xfa,0xa1,0xf3 = sxtab16 r3, r2, r1, ror #16
0x14,0xbf = ite ne
0x21,0xfa,0x84,0xf0 = sxtab16ne r0, r1, r4
0x22,0xfa,0xb3,0xf1 = sxtab16eq r1, r2, r3, ror #24
0x03,0xfa,0x89,0xf1 = sxtah r1, r3, r9
0x08,0xfa,0x93,0xf3 = sxtah r3, r8, r3, ror #8
0x03,0xfa,0xb3,0xf9 = sxtah r9, r3, r3, ror #24
0x8c,0xbf = ite hi
0x01,0xfa,0x86,0xf6 = sxtahhi r6, r1, r6
0x02,0xfa,0xa4,0xf2 = sxtahls r2, r2, r4, ror #16
0x75,0xb2 = sxtb r5, r6
0x4f,0xfa,0x99,0xf6 = sxtb.w r6, r9, ror #8
0x4f,0xfa,0xb3,0xf8 = sxtb.w r8, r3, ror #24
0xac,0xbf = ite ge
0x62,0xb2 = sxtbge r2, r4
0x4f,0xfa,0xa1,0xf5 = sxtblt.w r5, r1, ror #16
0x4f,0xfa,0x88,0xf7 = sxtb.w r7, r8
0x2f,0xfa,0x84,0xf1 = sxtb16 r1, r4
0x2f,0xfa,0x87,0xf6 = sxtb16 r6, r7
0x2f,0xfa,0xa1,0xf3 = sxtb16 r3, r1, ror #16
0x2c,0xbf = ite hs
0x2f,0xfa,0x95,0xf3 = sxtb16hs r3, r5, ror #8
0x2f,0xfa,0xb3,0xf2 = sxtb16lo r2, r3, ror #24
0x31,0xb2 = sxth r1, r6
0x0f,0xfa,0x98,0xf3 = sxth.w r3, r8, ror #8
0x0f,0xfa,0xb3,0xf9 = sxth.w r9, r3, ror #24
0x1c,0xbf = itt ne
0x0f,0xfa,0x89,0xf3 = sxthne.w r3, r9
0x0f,0xfa,0xa2,0xf2 = sxthne.w r2, r2, ror #16
0x0f,0xfa,0x88,0xf7 = sxth.w r7, r8
0x75,0xb2 = sxtb r5, r6
0x4f,0xfa,0x99,0xf6 = sxtb.w r6, r9, ror #8
0x4f,0xfa,0xb3,0xf8 = sxtb.w r8, r3, ror #24
0xac,0xbf = ite ge
0x62,0xb2 = sxtbge r2, r4
0x4f,0xfa,0xa1,0xf5 = sxtblt.w r5, r1, ror #16
0x2f,0xfa,0x84,0xf1 = sxtb16 r1, r4
0x2f,0xfa,0x87,0xf6 = sxtb16 r6, r7
0x2f,0xfa,0xa1,0xf3 = sxtb16 r3, r1, ror #16
0x2c,0xbf = ite hs
0x2f,0xfa,0x95,0xf3 = sxtb16hs r3, r5, ror #8
0x2f,0xfa,0xb3,0xf2 = sxtb16lo r2, r3, ror #24
0x31,0xb2 = sxth r1, r6
0x0f,0xfa,0x98,0xf3 = sxth.w r3, r8, ror #8
0x0f,0xfa,0xb3,0xf9 = sxth.w r9, r3, ror #24
0x1c,0xbf = itt ne
0x0f,0xfa,0x89,0xf3 = sxthne.w r3, r9
0x0f,0xfa,0xa2,0xf2 = sxthne.w r2, r2, ror #16
0xd3,0xe8,0x08,0xf0 = tbb [r3, r8]
0xd3,0xe8,0x18,0xf0 = tbh [r3, r8, lsl #1]
0x08,0xbf = it eq
0xd3,0xe8,0x08,0xf0 = tbbeq [r3, r8]
0x28,0xbf = it hs
0xd3,0xe8,0x18,0xf0 = tbhhs [r3, r8, lsl #1]
0x95,0xf4,0x70,0x4f = teq.w r5, #61440
0x94,0xea,0x05,0x0f = teq.w r4, r5
0x94,0xea,0x45,0x1f = teq.w r4, r5, lsl #5
0x94,0xea,0x55,0x1f = teq.w r4, r5, lsr #5
0x94,0xea,0x55,0x1f = teq.w r4, r5, lsr #5
0x94,0xea,0x65,0x1f = teq.w r4, r5, asr #5
0x94,0xea,0x75,0x1f = teq.w r4, r5, ror #5
0x15,0xf4,0x70,0x4f = tst.w r5, #61440
0x2a,0x42 = tst r2, r5
0x13,0xea,0x4c,0x1f = tst.w r3, r12, lsl #5
0x14,0xea,0x1b,0x1f = tst.w r4, r11, lsr #4
0x15,0xea,0x1a,0x3f = tst.w r5, r10, lsr #12
0x16,0xea,0xa9,0x7f = tst.w r6, r9, asr #30
0x17,0xea,0xb8,0x0f = tst.w r7, r8, ror #2
0x92,0xfa,0x43,0xf1 = uadd16 r1, r2, r3
0x82,0xfa,0x43,0xf1 = uadd8 r1, r2, r3
0xcc,0xbf = ite gt
0x92,0xfa,0x43,0xf1 = uadd16gt r1, r2, r3
0x82,0xfa,0x43,0xf1 = uadd8le r1, r2, r3
0xac,0xfa,0x40,0xf9 = uasx r9, r12, r0
0x08,0xbf = it eq
0xac,0xfa,0x40,0xf9 = uasxeq r9, r12, r0
0xac,0xfa,0x40,0xf9 = uasx r9, r12, r0
0x08,0xbf = it eq
0xac,0xfa,0x40,0xf9 = uasxeq r9, r12, r0
0xc5,0xf3,0x00,0x44 = ubfx r4, r5, #16, #1
0xc8,0xbf = it gt
0xc5,0xf3,0x0f,0x44 = ubfxgt r4, r5, #16, #16
0x98,0xfa,0x62,0xf4 = uhadd16 r4, r8, r2
0x88,0xfa,0x62,0xf4 = uhadd8 r4, r8, r2
0xc4,0xbf = itt gt
0x98,0xfa,0x62,0xf4 = uhadd16gt r4, r8, r2
0x88,0xfa,0x62,0xf4 = uhadd8gt r4, r8, r2
0xa1,0xfa,0x65,0xf4 = uhasx r4, r1, r5
0xe6,0xfa,0x66,0xf5 = uhsax r5, r6, r6
0xc4,0xbf = itt gt
0xa9,0xfa,0x68,0xf6 = uhasxgt r6, r9, r8
0xe8,0xfa,0x6c,0xf7 = uhsaxgt r7, r8, r12
0xa1,0xfa,0x65,0xf4 = uhasx r4, r1, r5
0xe6,0xfa,0x66,0xf5 = uhsax r5, r6, r6
0xc4,0xbf = itt gt
0xa9,0xfa,0x68,0xf6 = uhasxgt r6, r9, r8
0xe8,0xfa,0x6c,0xf7 = uhsaxgt r7, r8, r12
0xd8,0xfa,0x63,0xf5 = uhsub16 r5, r8, r3
0xc7,0xfa,0x66,0xf1 = uhsub8 r1, r7, r6
0xbc,0xbf = itt lt
0xd9,0xfa,0x6c,0xf4 = uhsub16lt r4, r9, r12
0xc1,0xfa,0x65,0xf3 = uhsub8lt r3, r1, r5
0xe5,0xfb,0x66,0x34 = umaal r3, r4, r5, r6
0xb8,0xbf = it lt
0xe5,0xfb,0x66,0x34 = umaallt r3, r4, r5, r6
0xe6,0xfb,0x08,0x24 = umlal r2, r4, r6, r8
0xc8,0xbf = it gt
0xe2,0xfb,0x06,0x61 = umlalgt r6, r1, r2, r6
0xa6,0xfb,0x08,0x24 = umull r2, r4, r6, r8
0xc8,0xbf = it gt
0xa2,0xfb,0x06,0x61 = umullgt r6, r1, r2, r6
0x92,0xfa,0x53,0xf1 = uqadd16 r1, r2, r3
0x84,0xfa,0x58,0xf3 = uqadd8 r3, r4, r8
0xcc,0xbf = ite gt
0x97,0xfa,0x59,0xf4 = uqadd16gt r4, r7, r9
0x81,0xfa,0x52,0xf8 = uqadd8le r8, r1, r2
0xa2,0xfa,0x53,0xf1 = uqasx r1, r2, r3
0xe4,0xfa,0x58,0xf3 = uqsax r3, r4, r8
0xcc,0xbf = ite gt
0xa7,0xfa,0x59,0xf4 = uqasxgt r4, r7, r9
0xe1,0xfa,0x52,0xf8 = uqsaxle r8, r1, r2
0xa2,0xfa,0x53,0xf1 = uqasx r1, r2, r3
0xe4,0xfa,0x58,0xf3 = uqsax r3, r4, r8
0xcc,0xbf = ite gt
0xa7,0xfa,0x59,0xf4 = uqasxgt r4, r7, r9
0xe1,0xfa,0x52,0xf8 = uqsaxle r8, r1, r2
0xc2,0xfa,0x59,0xf8 = uqsub8 r8, r2, r9
0xd9,0xfa,0x57,0xf1 = uqsub16 r1, r9, r7
0xcc,0xbf = ite gt
0xc1,0xfa,0x56,0xf3 = uqsub8gt r3, r1, r6
0xd6,0xfa,0x54,0xf4 = uqsub16le r4, r6, r4
0x79,0xfb,0x07,0xf1 = usad8 r1, r9, r7
0x72,0xfb,0x09,0xc8 = usada8 r8, r2, r9, r12
0xcc,0xbf = ite gt
0x71,0xfb,0x06,0x93 = usada8gt r3, r1, r6, r9
0x76,0xfb,0x04,0xf4 = usad8le r4, r6, r4
0x8a,0xf3,0x01,0x08 = usat r8, #1, r10
0x8a,0xf3,0x04,0x08 = usat r8, #4, r10
0x8a,0xf3,0xc5,0x78 = usat r8, #5, r10, lsl #31
0xaa,0xf3,0x50,0x08 = usat r8, #16, r10, asr #1
0xa7,0xf3,0x02,0x02 = usat16 r2, #2, r7
0xa5,0xf3,0x0f,0x03 = usat16 r3, #15, r5
0xe3,0xfa,0x44,0xf2 = usax r2, r3, r4
0x18,0xbf = it ne
0xe1,0xfa,0x49,0xf6 = usaxne r6, r1, r9
0xe3,0xfa,0x44,0xf2 = usax r2, r3, r4
0x18,0xbf = it ne
0xe1,0xfa,0x49,0xf6 = usaxne r6, r1, r9
0xd2,0xfa,0x47,0xf4 = usub16 r4, r2, r7
0xc8,0xfa,0x45,0xf1 = usub8 r1, r8, r5
0x8c,0xbf = ite hi
0xd1,0xfa,0x43,0xf1 = usub16hi r1, r1, r3
0xc2,0xfa,0x43,0xf9 = usub8ls r9, r2, r3
0x53,0xfa,0x84,0xf2 = uxtab r2, r3, r4
0x55,0xfa,0x86,0xf4 = uxtab r4, r5, r6
0xb8,0xbf = it lt
0x52,0xfa,0x99,0xf6 = uxtablt r6, r2, r9, ror #8
0x51,0xfa,0xa4,0xf5 = uxtab r5, r1, r4, ror #16
0x58,0xfa,0xb3,0xf7 = uxtab r7, r8, r3, ror #24
0xa8,0xbf = it ge
0x31,0xfa,0x84,0xf0 = uxtab16ge r0, r1, r4
0x32,0xfa,0x87,0xf6 = uxtab16 r6, r2, r7
0x35,0xfa,0x98,0xf3 = uxtab16 r3, r5, r8, ror #8
0x32,0xfa,0xa1,0xf3 = uxtab16 r3, r2, r1, ror #16
0x08,0xbf = it eq
0x32,0xfa,0xb3,0xf1 = uxtab16eq r1, r2, r3, ror #24
0x13,0xfa,0x89,0xf1 = uxtah r1, r3, r9
0x88,0xbf = it hi
0x11,0xfa,0x86,0xf6 = uxtahhi r6, r1, r6
0x18,0xfa,0x93,0xf3 = uxtah r3, r8, r3, ror #8
0x38,0xbf = it lo
0x12,0xfa,0xa4,0xf2 = uxtahlo r2, r2, r4, ror #16
0x13,0xfa,0xb3,0xf9 = uxtah r9, r3, r3, ror #24
0xa8,0xbf = it ge
0xe2,0xb2 = uxtbge r2, r4
0xf5,0xb2 = uxtb r5, r6
0x5f,0xfa,0x99,0xf6 = uxtb.w r6, r9, ror #8
0x38,0xbf = it lo
0x5f,0xfa,0xa1,0xf5 = uxtblo.w r5, r1, ror #16
0x5f,0xfa,0xb3,0xf8 = uxtb.w r8, r3, ror #24
0x5f,0xfa,0x88,0xf7 = uxtb.w r7, r8
0x3f,0xfa,0x84,0xf1 = uxtb16 r1, r4
0x3f,0xfa,0x87,0xf6 = uxtb16 r6, r7
0x28,0xbf = it hs
0x3f,0xfa,0x95,0xf3 = uxtb16hs r3, r5, ror #8
0x3f,0xfa,0xa1,0xf3 = uxtb16 r3, r1, ror #16
0xa8,0xbf = it ge
0x3f,0xfa,0xb3,0xf2 = uxtb16ge r2, r3, ror #24
0x18,0xbf = it ne
0x1f,0xfa,0x89,0xf3 = uxthne.w r3, r9
0xb1,0xb2 = uxth r1, r6
0x1f,0xfa,0x98,0xf3 = uxth.w r3, r8, ror #8
0xd8,0xbf = it le
0x1f,0xfa,0xa2,0xf2 = uxthle.w r2, r2, ror #16
0x1f,0xfa,0xb3,0xf9 = uxth.w r9, r3, ror #24
0x1f,0xfa,0x88,0xf7 = uxth.w r7, r8
0x20,0xbf = wfe 
0x30,0xbf = wfi 
0x10,0xbf = yield 
0xb6,0xbf = itet lt
0x20,0xbf = wfelt 
0x30,0xbf = wfige 
0x10,0xbf = yieldlt 
0xaf,0xf3,0x04,0x80 = sev.w 
0xaf,0xf3,0x03,0x80 = wfi.w 
0xaf,0xf3,0x02,0x80 = wfe.w 
0xaf,0xf3,0x01,0x80 = yield.w 
0xaf,0xf3,0x00,0x80 = nop.w 
0x40,0xbf = sev 
0x30,0xbf = wfi 
0x20,0xbf = wfe 
0x10,0xbf = yield 
0x00,0xbf = nop 
0xb6,0xbf = itet lt
0xf0,0xbf = hintlt #15
0xaf,0xf3,0x10,0x80 = hintge.w #16
0xaf,0xf3,0xef,0x80 = hintlt.w #239
0x70,0xbf = hint #7
0xaf,0xf3,0x07,0x80 = hint.w #7
0x9f,0xf8,0x16,0xb0 = ldrb.w r11, [pc, #22]
0xbf,0xf8,0x16,0xb0 = ldrh.w r11, [pc, #22]
0x9f,0xf9,0x16,0xb0 = ldrsb.w r11, [pc, #22]
0xbf,0xf9,0x16,0xb0 = ldrsh.w r11, [pc, #22]
0xdf,0xf8,0x16,0xb0 = ldr.w r11, [pc, #22]
0x9f,0xf8,0x16,0xb0 = ldrb.w r11, [pc, #22]
0xbf,0xf8,0x16,0xb0 = ldrh.w r11, [pc, #22]
0x9f,0xf9,0x16,0xb0 = ldrsb.w r11, [pc, #22]
0xbf,0xf9,0x16,0xb0 = ldrsh.w r11, [pc, #22]
0x5f,0xf8,0x16,0xb0 = ldr.w r11, [pc, #-22]
0x1f,0xf8,0x16,0xb0 = ldrb.w r11, [pc, #-22]
0x3f,0xf8,0x16,0xb0 = ldrh.w r11, [pc, #-22]
0x1f,0xf9,0x16,0xb0 = ldrsb.w r11, [pc, #-22]
0x3f,0xf9,0x16,0xb0 = ldrsh.w r11, [pc, #-22]
0x5f,0xf8,0x16,0xb0 = ldr.w r11, [pc, #-22]
0x1f,0xf8,0x16,0xb0 = ldrb.w r11, [pc, #-22]
0x3f,0xf8,0x16,0xb0 = ldrh.w r11, [pc, #-22]
0x1f,0xf9,0x16,0xb0 = ldrsb.w r11, [pc, #-22]
0x3f,0xf9,0x16,0xb0 = ldrsh.w r11, [pc, #-22]
0x03,0x49 = ldr r1, [pc, #12]
0xde,0xf3,0x04,0x8f = subs pc, lr, #4
