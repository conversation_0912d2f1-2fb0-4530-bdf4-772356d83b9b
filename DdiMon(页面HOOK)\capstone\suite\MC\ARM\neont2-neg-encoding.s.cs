# CS_ARCH_ARM, CS_MODE_THUMB, None
0xf1,0xff,0xa0,0x03 = vneg.s8 d16, d16
0xf5,0xff,0xa0,0x03 = vneg.s16 d16, d16
0xf9,0xff,0xa0,0x03 = vneg.s32 d16, d16
0xf9,0xff,0xa0,0x07 = vneg.f32 d16, d16
0xf1,0xff,0xe0,0x03 = vneg.s8 q8, q8
0xf5,0xff,0xe0,0x03 = vneg.s16 q8, q8
0xf9,0xff,0xe0,0x03 = vneg.s32 q8, q8
0xf9,0xff,0xe0,0x07 = vneg.f32 q8, q8
0xf0,0xff,0xa0,0x07 = vqneg.s8 d16, d16
0xf4,0xff,0xa0,0x07 = vqneg.s16 d16, d16
0xf8,0xff,0xa0,0x07 = vqneg.s32 d16, d16
0xf0,0xff,0xe0,0x07 = vqneg.s8 q8, q8
0xf4,0xff,0xe0,0x07 = vqneg.s16 q8, q8
0xf8,0xff,0xe0,0x07 = vqneg.s32 q8, q8
