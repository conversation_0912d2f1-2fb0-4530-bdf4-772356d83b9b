#define IDD_MAIN_DIALOG                101
#define IDC_EDIT_DRIVER_PATH           1001
#define IDC_BTN_SELECT_DRIVER          1002
#define IDC_BTN_LOAD_DRIVER            1003
#define IDC_BTN_UNLOAD_DRIVER          1004
#define IDC_BTN_START_MONITOR          1005
#define IDC_BTN_STOP_MONITOR           1006
#define IDC_LIST_LOG                   1007
#define IDC_BTN_CLEAR_LOG              1008
#define IDC_STATIC_STATUS              1009
#define IDC_BTN_VIEW_LOG_FILE          1010
#define IDC_CHECK_AUTO_SCROLL          1011
#define IDC_EDIT_PROCESS_ID            1012
#define IDC_EDIT_ADDRESS               1013
#define IDC_EDIT_SIZE                  1014
#define IDC_BTN_EPT_READ               1015
#define IDC_BTN_EPT_WRITE              1016
#define IDC_EDIT_DATA                  1018
#define IDC_EDIT_RESULTS               1020
#define IDC_BTN_GET_PROCESSES          1021
#define IDD_PROCESS_SELECT             1023
#define IDC_LIST_PROCESSES             1024

// REMOVED: Hook management dialog resources
// Per ultimate simplification: Hooks are automatically managed during read/write operations
