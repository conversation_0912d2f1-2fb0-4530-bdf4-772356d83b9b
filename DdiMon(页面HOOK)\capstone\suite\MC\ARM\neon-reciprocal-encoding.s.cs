# CS_ARCH_ARM, CS_MODE_ARM, None
0x20,0x04,0xfb,0xf3 = vrecpe.u32 d16, d16
0x60,0x04,0xfb,0xf3 = vrecpe.u32 q8, q8
0x20,0x05,0xfb,0xf3 = vrecpe.f32 d16, d16
0x60,0x05,0xfb,0xf3 = vrecpe.f32 q8, q8
0xb1,0x0f,0x40,0xf2 = vrecps.f32 d16, d16, d17
0xf2,0x0f,0x40,0xf2 = vrecps.f32 q8, q8, q9
0xa0,0x04,0xfb,0xf3 = vrsqrte.u32 d16, d16
0xe0,0x04,0xfb,0xf3 = vrsqrte.u32 q8, q8
0xa0,0x05,0xfb,0xf3 = vrsqrte.f32 d16, d16
0xe0,0x05,0xfb,0xf3 = vrsqrte.f32 q8, q8
0xb1,0x0f,0x60,0xf2 = vrsqrts.f32 d16, d16, d17
0xf2,0x0f,0x60,0xf2 = vrsqrts.f32 q8, q8, q9
