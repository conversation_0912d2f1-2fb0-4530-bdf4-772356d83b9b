Issues to be solved in next versions


[Core]

- X86 can already handle all the malware tricks we are aware of. If you find
  any such instruction sequence that Capstone disassembles wrongly or fails
  completely, please report. Fixing this issue is always the top priority of
  our project.

- More optimization for better performance.


[Bindings]

- OCaml binding is working, but still needs to support the core API better.
