﻿<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="Rules for HyperPlatform" Description="Code analysis rules for HyperPlatform.vcxproj." ToolsVersion="15.0">
  <IncludeAll Action="Warning" />
  <Rules AnalyzerId="Microsoft.Analyzers.NativeCodeAnalysis" RuleNamespace="Microsoft.Rules.Native">
    <Rule Id="C26400" Action="None" />
    <Rule Id="C26401" Action="None" />
    <Rule Id="C26402" Action="None" />
    <Rule Id="C26403" Action="None" />
    <Rule Id="C26404" Action="None" />
    <Rule Id="C26405" Action="None" />
    <Rule Id="C26406" Action="None" />
    <Rule Id="C26407" Action="None" />
    <Rule Id="C26408" Action="None" />
    <Rule Id="C26409" Action="None" />
    <Rule Id="C26410" Action="None" />
    <Rule Id="C26411" Action="None" />
    <Rule Id="C26414" Action="None" />
    <Rule Id="C26415" Action="None" />
    <Rule Id="C26416" Action="None" />
    <Rule Id="C26417" Action="None" />
    <Rule Id="C26418" Action="None" />
    <Rule Id="C26426" Action="None" />
    <Rule Id="C26427" Action="None" />
    <Rule Id="C26429" Action="None" />
    <Rule Id="C26430" Action="None" />
    <Rule Id="C26431" Action="None" />
    <Rule Id="C26432" Action="None" />
    <Rule Id="C26433" Action="None" />
    <Rule Id="C26434" Action="None" />
    <Rule Id="C26435" Action="None" />
    <Rule Id="C26436" Action="None" />
    <Rule Id="C26437" Action="None" />
    <Rule Id="C26438" Action="None" />
    <Rule Id="C26439" Action="None" />
    <Rule Id="C26440" Action="None" />
    <Rule Id="C26441" Action="None" />
    <Rule Id="C26443" Action="None" />
    <Rule Id="C26444" Action="None" />
    <Rule Id="C26445" Action="None" />
    <Rule Id="C26446" Action="None" />
    <Rule Id="C26447" Action="None" />
    <Rule Id="C26448" Action="None" />
    <Rule Id="C26449" Action="None" />
    <Rule Id="C26450" Action="None" />
    <Rule Id="C26451" Action="None" />
    <Rule Id="C26452" Action="None" />
    <Rule Id="C26453" Action="None" />
    <Rule Id="C26454" Action="None" />
    <Rule Id="C26455" Action="None" />
    <Rule Id="C26456" Action="None" />
    <Rule Id="C26459" Action="None" />
    <Rule Id="C26460" Action="None" />
    <Rule Id="C26461" Action="None" />
    <Rule Id="C26462" Action="None" />
    <Rule Id="C26463" Action="None" />
    <Rule Id="C26464" Action="None" />
    <Rule Id="C26465" Action="None" />
    <Rule Id="C26466" Action="None" />
    <Rule Id="C26471" Action="Warning" />
    <Rule Id="C26472" Action="None" />
    <Rule Id="C26473" Action="None" />
    <Rule Id="C26474" Action="None" />
    <Rule Id="C26475" Action="None" />
    <Rule Id="C26476" Action="None" />
    <Rule Id="C26477" Action="None" />
    <Rule Id="C26481" Action="None" />
    <Rule Id="C26482" Action="None" />
    <Rule Id="C26483" Action="None" />
    <Rule Id="C26485" Action="None" />
    <Rule Id="C26486" Action="None" />
    <Rule Id="C26487" Action="None" />
    <Rule Id="C26489" Action="None" />
    <Rule Id="C26490" Action="None" />
    <Rule Id="C26491" Action="None" />
    <Rule Id="C26492" Action="None" />
    <Rule Id="C26493" Action="None" />
    <Rule Id="C26494" Action="None" />
    <Rule Id="C26495" Action="None" />
    <Rule Id="C26496" Action="None" />
    <Rule Id="C26497" Action="None" />
    <Rule Id="C26498" Action="None" />
  </Rules>
</RuleSet>