# CS_ARCH_PPC, CS_MODE_BIG_ENDIAN, CS_OPT_SYNTAX_NOREGNAME
0x4d,0x82,0x00,0x20 = beqlr 0
0x4d,0x86,0x00,0x20 = beqlr 1
0x4d,0x8a,0x00,0x20 = beqlr 2
0x4d,0x8e,0x00,0x20 = beqlr 3
0x4d,0x92,0x00,0x20 = beqlr 4
0x4d,0x96,0x00,0x20 = beqlr 5
0x4d,0x9a,0x00,0x20 = beqlr 6
0x4d,0x9e,0x00,0x20 = beqlr 7
0x4d,0x80,0x00,0x20 = bclr 12, 0, 0
0x4d,0x81,0x00,0x20 = bclr 12, 1, 0
0x4d,0x82,0x00,0x20 = bclr 12, 2, 0
0x4d,0x83,0x00,0x20 = bclr 12, 3, 0
0x4d,0x83,0x00,0x20 = bclr 12, 3, 0
0x4d,0x84,0x00,0x20 = bclr 12, 4, 0
0x4d,0x85,0x00,0x20 = bclr 12, 5, 0
0x4d,0x86,0x00,0x20 = bclr 12, 6, 0
0x4d,0x87,0x00,0x20 = bclr 12, 7, 0
0x4d,0x87,0x00,0x20 = bclr 12, 7, 0
0x4d,0x88,0x00,0x20 = bclr 12, 8, 0
0x4d,0x89,0x00,0x20 = bclr 12, 9, 0
0x4d,0x8a,0x00,0x20 = bclr 12, 10, 0
0x4d,0x8b,0x00,0x20 = bclr 12, 11, 0
0x4d,0x8b,0x00,0x20 = bclr 12, 11, 0
0x4d,0x8c,0x00,0x20 = bclr 12, 12, 0
0x4d,0x8d,0x00,0x20 = bclr 12, 13, 0
0x4d,0x8e,0x00,0x20 = bclr 12, 14, 0
0x4d,0x8f,0x00,0x20 = bclr 12, 15, 0
0x4d,0x8f,0x00,0x20 = bclr 12, 15, 0
0x4d,0x90,0x00,0x20 = bclr 12, 16, 0
0x4d,0x91,0x00,0x20 = bclr 12, 17, 0
0x4d,0x92,0x00,0x20 = bclr 12, 18, 0
0x4d,0x93,0x00,0x20 = bclr 12, 19, 0
0x4d,0x93,0x00,0x20 = bclr 12, 19, 0
0x4d,0x94,0x00,0x20 = bclr 12, 20, 0
0x4d,0x95,0x00,0x20 = bclr 12, 21, 0
0x4d,0x96,0x00,0x20 = bclr 12, 22, 0
0x4d,0x97,0x00,0x20 = bclr 12, 23, 0
0x4d,0x97,0x00,0x20 = bclr 12, 23, 0
0x4d,0x98,0x00,0x20 = bclr 12, 24, 0
0x4d,0x99,0x00,0x20 = bclr 12, 25, 0
0x4d,0x9a,0x00,0x20 = bclr 12, 26, 0
0x4d,0x9b,0x00,0x20 = bclr 12, 27, 0
0x4d,0x9b,0x00,0x20 = bclr 12, 27, 0
0x4d,0x9c,0x00,0x20 = bclr 12, 28, 0
0x4d,0x9d,0x00,0x20 = bclr 12, 29, 0
0x4d,0x9e,0x00,0x20 = bclr 12, 30, 0
0x4d,0x9f,0x00,0x20 = bclr 12, 31, 0
0x4d,0x9f,0x00,0x20 = bclr 12, 31, 0
0x4e,0x80,0x00,0x20 = blr 
0x4e,0x80,0x04,0x20 = bctr 
0x4e,0x80,0x00,0x21 = blrl 
0x4e,0x80,0x04,0x21 = bctrl 
0x4d,0x82,0x00,0x20 = bclr 12, 2, 0
0x4d,0x82,0x04,0x20 = bcctr 12, 2, 0
0x4d,0x82,0x00,0x21 = bclrl 12, 2, 0
0x4d,0x82,0x04,0x21 = bcctrl 12, 2, 0
0x4d,0xe2,0x00,0x20 = bclr 15, 2, 0
0x4d,0xe2,0x04,0x20 = bcctr 15, 2, 0
0x4d,0xe2,0x00,0x21 = bclrl 15, 2, 0
0x4d,0xe2,0x04,0x21 = bcctrl 15, 2, 0
0x4d,0xc2,0x00,0x20 = bclr 14, 2, 0
0x4d,0xc2,0x04,0x20 = bcctr 14, 2, 0
0x4d,0xc2,0x00,0x21 = bclrl 14, 2, 0
0x4d,0xc2,0x04,0x21 = bcctrl 14, 2, 0
0x4c,0x82,0x00,0x20 = bclr 4, 2, 0
0x4c,0x82,0x04,0x20 = bcctr 4, 2, 0
0x4c,0x82,0x00,0x21 = bclrl 4, 2, 0
0x4c,0x82,0x04,0x21 = bcctrl 4, 2, 0
0x4c,0xe2,0x00,0x20 = bclr 7, 2, 0
0x4c,0xe2,0x04,0x20 = bcctr 7, 2, 0
0x4c,0xe2,0x00,0x21 = bclrl 7, 2, 0
0x4c,0xe2,0x04,0x21 = bcctrl 7, 2, 0
0x4c,0xc2,0x00,0x20 = bclr 6, 2, 0
0x4c,0xc2,0x04,0x20 = bcctr 6, 2, 0
0x4c,0xc2,0x00,0x21 = bclrl 6, 2, 0
0x4c,0xc2,0x04,0x21 = bcctrl 6, 2, 0
0x4e,0x00,0x00,0x20 = bdnzlr 
0x4e,0x00,0x00,0x21 = bdnzlrl 
0x4f,0x20,0x00,0x20 = bdnzlr+ 
0x4f,0x20,0x00,0x21 = bdnzlrl+ 
0x4f,0x00,0x00,0x20 = bdnzlr- 
0x4f,0x00,0x00,0x21 = bdnzlrl- 
0x4d,0x02,0x00,0x20 = bclr 8, 2, 0
0x4d,0x02,0x00,0x21 = bclrl 8, 2, 0
0x4c,0x02,0x00,0x20 = bclr 0, 2, 0
0x4c,0x02,0x00,0x21 = bclrl 0, 2, 0
0x4e,0x40,0x00,0x20 = bdzlr 
0x4e,0x40,0x00,0x21 = bdzlrl 
0x4f,0x60,0x00,0x20 = bdzlr+ 
0x4f,0x60,0x00,0x21 = bdzlrl+ 
0x4f,0x40,0x00,0x20 = bdzlr- 
0x4f,0x40,0x00,0x21 = bdzlrl- 
0x4d,0x42,0x00,0x20 = bclr 10, 2, 0
0x4d,0x42,0x00,0x21 = bclrl 10, 2, 0
0x4c,0x42,0x00,0x20 = bclr 2, 2, 0
0x4c,0x42,0x00,0x21 = bclrl 2, 2, 0
0x4d,0x88,0x00,0x20 = bltlr 2
0x4d,0x80,0x00,0x20 = bltlr 0
0x4d,0x88,0x04,0x20 = bltctr 2
0x4d,0x80,0x04,0x20 = bltctr 0
0x4d,0x88,0x00,0x21 = bltlrl 2
0x4d,0x80,0x00,0x21 = bltlrl 0
0x4d,0x88,0x04,0x21 = bltctrl 2
0x4d,0x80,0x04,0x21 = bltctrl 0
0x4d,0xe8,0x00,0x20 = bltlr+ 2
0x4d,0xe0,0x00,0x20 = bltlr+ 0
0x4d,0xe8,0x04,0x20 = bltctr+ 2
0x4d,0xe0,0x04,0x20 = bltctr+ 0
0x4d,0xe8,0x00,0x21 = bltlrl+ 2
0x4d,0xe0,0x00,0x21 = bltlrl+ 0
0x4d,0xe8,0x04,0x21 = bltctrl+ 2
0x4d,0xe0,0x04,0x21 = bltctrl+ 0
0x4d,0xc8,0x00,0x20 = bltlr- 2
0x4d,0xc0,0x00,0x20 = bltlr- 0
0x4d,0xc8,0x04,0x20 = bltctr- 2
0x4d,0xc0,0x04,0x20 = bltctr- 0
0x4d,0xc8,0x00,0x21 = bltlrl- 2
0x4d,0xc0,0x00,0x21 = bltlrl- 0
0x4d,0xc8,0x04,0x21 = bltctrl- 2
0x4d,0xc0,0x04,0x21 = bltctrl- 0
0x4c,0x89,0x00,0x20 = blelr 2
0x4c,0x81,0x00,0x20 = blelr 0
0x4c,0x89,0x04,0x20 = blectr 2
0x4c,0x81,0x04,0x20 = blectr 0
0x4c,0x89,0x00,0x21 = blelrl 2
0x4c,0x81,0x00,0x21 = blelrl 0
0x4c,0x89,0x04,0x21 = blectrl 2
0x4c,0x81,0x04,0x21 = blectrl 0
0x4c,0xe9,0x00,0x20 = blelr+ 2
0x4c,0xe1,0x00,0x20 = blelr+ 0
0x4c,0xe9,0x04,0x20 = blectr+ 2
0x4c,0xe1,0x04,0x20 = blectr+ 0
0x4c,0xe9,0x00,0x21 = blelrl+ 2
0x4c,0xe1,0x00,0x21 = blelrl+ 0
0x4c,0xe9,0x04,0x21 = blectrl+ 2
0x4c,0xe1,0x04,0x21 = blectrl+ 0
0x4c,0xc9,0x00,0x20 = blelr- 2
0x4c,0xc1,0x00,0x20 = blelr- 0
0x4c,0xc9,0x04,0x20 = blectr- 2
0x4c,0xc1,0x04,0x20 = blectr- 0
0x4c,0xc9,0x00,0x21 = blelrl- 2
0x4c,0xc1,0x00,0x21 = blelrl- 0
0x4c,0xc9,0x04,0x21 = blectrl- 2
0x4c,0xc1,0x04,0x21 = blectrl- 0
0x4d,0x8a,0x00,0x20 = beqlr 2
0x4d,0x82,0x00,0x20 = beqlr 0
0x4d,0x8a,0x04,0x20 = beqctr 2
0x4d,0x82,0x04,0x20 = beqctr 0
0x4d,0x8a,0x00,0x21 = beqlrl 2
0x4d,0x82,0x00,0x21 = beqlrl 0
0x4d,0x8a,0x04,0x21 = beqctrl 2
0x4d,0x82,0x04,0x21 = beqctrl 0
0x4d,0xea,0x00,0x20 = beqlr+ 2
0x4d,0xe2,0x00,0x20 = beqlr+ 0
0x4d,0xea,0x04,0x20 = beqctr+ 2
0x4d,0xe2,0x04,0x20 = beqctr+ 0
0x4d,0xea,0x00,0x21 = beqlrl+ 2
0x4d,0xe2,0x00,0x21 = beqlrl+ 0
0x4d,0xea,0x04,0x21 = beqctrl+ 2
0x4d,0xe2,0x04,0x21 = beqctrl+ 0
0x4d,0xca,0x00,0x20 = beqlr- 2
0x4d,0xc2,0x00,0x20 = beqlr- 0
0x4d,0xca,0x04,0x20 = beqctr- 2
0x4d,0xc2,0x04,0x20 = beqctr- 0
0x4d,0xca,0x00,0x21 = beqlrl- 2
0x4d,0xc2,0x00,0x21 = beqlrl- 0
0x4d,0xca,0x04,0x21 = beqctrl- 2
0x4d,0xc2,0x04,0x21 = beqctrl- 0
0x4c,0x88,0x00,0x20 = bgelr 2
0x4c,0x80,0x00,0x20 = bgelr 0
0x4c,0x88,0x04,0x20 = bgectr 2
0x4c,0x80,0x04,0x20 = bgectr 0
0x4c,0x88,0x00,0x21 = bgelrl 2
0x4c,0x80,0x00,0x21 = bgelrl 0
0x4c,0x88,0x04,0x21 = bgectrl 2
0x4c,0x80,0x04,0x21 = bgectrl 0
0x4c,0xe8,0x00,0x20 = bgelr+ 2
0x4c,0xe0,0x00,0x20 = bgelr+ 0
0x4c,0xe8,0x04,0x20 = bgectr+ 2
0x4c,0xe0,0x04,0x20 = bgectr+ 0
0x4c,0xe8,0x00,0x21 = bgelrl+ 2
0x4c,0xe0,0x00,0x21 = bgelrl+ 0
0x4c,0xe8,0x04,0x21 = bgectrl+ 2
0x4c,0xe0,0x04,0x21 = bgectrl+ 0
0x4c,0xc8,0x00,0x20 = bgelr- 2
0x4c,0xc0,0x00,0x20 = bgelr- 0
0x4c,0xc8,0x04,0x20 = bgectr- 2
0x4c,0xc0,0x04,0x20 = bgectr- 0
0x4c,0xc8,0x00,0x21 = bgelrl- 2
0x4c,0xc0,0x00,0x21 = bgelrl- 0
0x4c,0xc8,0x04,0x21 = bgectrl- 2
0x4c,0xc0,0x04,0x21 = bgectrl- 0
0x4d,0x89,0x00,0x20 = bgtlr 2
0x4d,0x81,0x00,0x20 = bgtlr 0
0x4d,0x89,0x04,0x20 = bgtctr 2
0x4d,0x81,0x04,0x20 = bgtctr 0
0x4d,0x89,0x00,0x21 = bgtlrl 2
0x4d,0x81,0x00,0x21 = bgtlrl 0
0x4d,0x89,0x04,0x21 = bgtctrl 2
0x4d,0x81,0x04,0x21 = bgtctrl 0
0x4d,0xe9,0x00,0x20 = bgtlr+ 2
0x4d,0xe1,0x00,0x20 = bgtlr+ 0
0x4d,0xe9,0x04,0x20 = bgtctr+ 2
0x4d,0xe1,0x04,0x20 = bgtctr+ 0
0x4d,0xe9,0x00,0x21 = bgtlrl+ 2
0x4d,0xe1,0x00,0x21 = bgtlrl+ 0
0x4d,0xe9,0x04,0x21 = bgtctrl+ 2
0x4d,0xe1,0x04,0x21 = bgtctrl+ 0
0x4d,0xc9,0x00,0x20 = bgtlr- 2
0x4d,0xc1,0x00,0x20 = bgtlr- 0
0x4d,0xc9,0x04,0x20 = bgtctr- 2
0x4d,0xc1,0x04,0x20 = bgtctr- 0
0x4d,0xc9,0x00,0x21 = bgtlrl- 2
0x4d,0xc1,0x00,0x21 = bgtlrl- 0
0x4d,0xc9,0x04,0x21 = bgtctrl- 2
0x4d,0xc1,0x04,0x21 = bgtctrl- 0
0x4c,0x88,0x00,0x20 = bgelr 2
0x4c,0x80,0x00,0x20 = bgelr 0
0x4c,0x88,0x04,0x20 = bgectr 2
0x4c,0x80,0x04,0x20 = bgectr 0
0x4c,0x88,0x00,0x21 = bgelrl 2
0x4c,0x80,0x00,0x21 = bgelrl 0
0x4c,0x88,0x04,0x21 = bgectrl 2
0x4c,0x80,0x04,0x21 = bgectrl 0
0x4c,0xe8,0x00,0x20 = bgelr+ 2
0x4c,0xe0,0x00,0x20 = bgelr+ 0
0x4c,0xe8,0x04,0x20 = bgectr+ 2
0x4c,0xe0,0x04,0x20 = bgectr+ 0
0x4c,0xe8,0x00,0x21 = bgelrl+ 2
0x4c,0xe0,0x00,0x21 = bgelrl+ 0
0x4c,0xe8,0x04,0x21 = bgectrl+ 2
0x4c,0xe0,0x04,0x21 = bgectrl+ 0
0x4c,0xc8,0x00,0x20 = bgelr- 2
0x4c,0xc0,0x00,0x20 = bgelr- 0
0x4c,0xc8,0x04,0x20 = bgectr- 2
0x4c,0xc0,0x04,0x20 = bgectr- 0
0x4c,0xc8,0x00,0x21 = bgelrl- 2
0x4c,0xc0,0x00,0x21 = bgelrl- 0
0x4c,0xc8,0x04,0x21 = bgectrl- 2
0x4c,0xc0,0x04,0x21 = bgectrl- 0
0x4c,0x8a,0x00,0x20 = bnelr 2
0x4c,0x82,0x00,0x20 = bnelr 0
0x4c,0x8a,0x04,0x20 = bnectr 2
0x4c,0x82,0x04,0x20 = bnectr 0
0x4c,0x8a,0x00,0x21 = bnelrl 2
0x4c,0x82,0x00,0x21 = bnelrl 0
0x4c,0x8a,0x04,0x21 = bnectrl 2
0x4c,0x82,0x04,0x21 = bnectrl 0
0x4c,0xea,0x00,0x20 = bnelr+ 2
0x4c,0xe2,0x00,0x20 = bnelr+ 0
0x4c,0xea,0x04,0x20 = bnectr+ 2
0x4c,0xe2,0x04,0x20 = bnectr+ 0
0x4c,0xea,0x00,0x21 = bnelrl+ 2
0x4c,0xe2,0x00,0x21 = bnelrl+ 0
0x4c,0xea,0x04,0x21 = bnectrl+ 2
0x4c,0xe2,0x04,0x21 = bnectrl+ 0
0x4c,0xca,0x00,0x20 = bnelr- 2
0x4c,0xc2,0x00,0x20 = bnelr- 0
0x4c,0xca,0x04,0x20 = bnectr- 2
0x4c,0xc2,0x04,0x20 = bnectr- 0
0x4c,0xca,0x00,0x21 = bnelrl- 2
0x4c,0xc2,0x00,0x21 = bnelrl- 0
0x4c,0xca,0x04,0x21 = bnectrl- 2
0x4c,0xc2,0x04,0x21 = bnectrl- 0
0x4c,0x89,0x00,0x20 = blelr 2
0x4c,0x81,0x00,0x20 = blelr 0
0x4c,0x89,0x04,0x20 = blectr 2
0x4c,0x81,0x04,0x20 = blectr 0
0x4c,0x89,0x00,0x21 = blelrl 2
0x4c,0x81,0x00,0x21 = blelrl 0
0x4c,0x89,0x04,0x21 = blectrl 2
0x4c,0x81,0x04,0x21 = blectrl 0
0x4c,0xe9,0x00,0x20 = blelr+ 2
0x4c,0xe1,0x00,0x20 = blelr+ 0
0x4c,0xe9,0x04,0x20 = blectr+ 2
0x4c,0xe1,0x04,0x20 = blectr+ 0
0x4c,0xe9,0x00,0x21 = blelrl+ 2
0x4c,0xe1,0x00,0x21 = blelrl+ 0
0x4c,0xe9,0x04,0x21 = blectrl+ 2
0x4c,0xe1,0x04,0x21 = blectrl+ 0
0x4c,0xc9,0x00,0x20 = blelr- 2
0x4c,0xc1,0x00,0x20 = blelr- 0
0x4c,0xc9,0x04,0x20 = blectr- 2
0x4c,0xc1,0x04,0x20 = blectr- 0
0x4c,0xc9,0x00,0x21 = blelrl- 2
0x4c,0xc1,0x00,0x21 = blelrl- 0
0x4c,0xc9,0x04,0x21 = blectrl- 2
0x4c,0xc1,0x04,0x21 = blectrl- 0
0x4d,0x8b,0x00,0x20 = bunlr 2
0x4d,0x83,0x00,0x20 = bunlr 0
0x4d,0x8b,0x04,0x20 = bunctr 2
0x4d,0x83,0x04,0x20 = bunctr 0
0x4d,0x8b,0x00,0x21 = bunlrl 2
0x4d,0x83,0x00,0x21 = bunlrl 0
0x4d,0x8b,0x04,0x21 = bunctrl 2
0x4d,0x83,0x04,0x21 = bunctrl 0
0x4d,0xeb,0x00,0x20 = bunlr+ 2
0x4d,0xe3,0x00,0x20 = bunlr+ 0
0x4d,0xeb,0x04,0x20 = bunctr+ 2
0x4d,0xe3,0x04,0x20 = bunctr+ 0
0x4d,0xeb,0x00,0x21 = bunlrl+ 2
0x4d,0xe3,0x00,0x21 = bunlrl+ 0
0x4d,0xeb,0x04,0x21 = bunctrl+ 2
0x4d,0xe3,0x04,0x21 = bunctrl+ 0
0x4d,0xcb,0x00,0x20 = bunlr- 2
0x4d,0xc3,0x00,0x20 = bunlr- 0
0x4d,0xcb,0x04,0x20 = bunctr- 2
0x4d,0xc3,0x04,0x20 = bunctr- 0
0x4d,0xcb,0x00,0x21 = bunlrl- 2
0x4d,0xc3,0x00,0x21 = bunlrl- 0
0x4d,0xcb,0x04,0x21 = bunctrl- 2
0x4d,0xc3,0x04,0x21 = bunctrl- 0
0x4c,0x8b,0x00,0x20 = bnulr 2
0x4c,0x83,0x00,0x20 = bnulr 0
0x4c,0x8b,0x04,0x20 = bnuctr 2
0x4c,0x83,0x04,0x20 = bnuctr 0
0x4c,0x8b,0x00,0x21 = bnulrl 2
0x4c,0x83,0x00,0x21 = bnulrl 0
0x4c,0x8b,0x04,0x21 = bnuctrl 2
0x4c,0x83,0x04,0x21 = bnuctrl 0
0x4c,0xeb,0x00,0x20 = bnulr+ 2
0x4c,0xe3,0x00,0x20 = bnulr+ 0
0x4c,0xeb,0x04,0x20 = bnuctr+ 2
0x4c,0xe3,0x04,0x20 = bnuctr+ 0
0x4c,0xeb,0x00,0x21 = bnulrl+ 2
0x4c,0xe3,0x00,0x21 = bnulrl+ 0
0x4c,0xeb,0x04,0x21 = bnuctrl+ 2
0x4c,0xe3,0x04,0x21 = bnuctrl+ 0
0x4c,0xcb,0x00,0x20 = bnulr- 2
0x4c,0xc3,0x00,0x20 = bnulr- 0
0x4c,0xcb,0x04,0x20 = bnuctr- 2
0x4c,0xc3,0x04,0x20 = bnuctr- 0
0x4c,0xcb,0x00,0x21 = bnulrl- 2
0x4c,0xc3,0x00,0x21 = bnulrl- 0
0x4c,0xcb,0x04,0x21 = bnuctrl- 2
0x4c,0xc3,0x04,0x21 = bnuctrl- 0
0x4d,0x8b,0x00,0x20 = bunlr 2
0x4d,0x83,0x00,0x20 = bunlr 0
0x4d,0x8b,0x04,0x20 = bunctr 2
0x4d,0x83,0x04,0x20 = bunctr 0
0x4d,0x8b,0x00,0x21 = bunlrl 2
0x4d,0x83,0x00,0x21 = bunlrl 0
0x4d,0x8b,0x04,0x21 = bunctrl 2
0x4d,0x83,0x04,0x21 = bunctrl 0
0x4d,0xeb,0x00,0x20 = bunlr+ 2
0x4d,0xe3,0x00,0x20 = bunlr+ 0
0x4d,0xeb,0x04,0x20 = bunctr+ 2
0x4d,0xe3,0x04,0x20 = bunctr+ 0
0x4d,0xeb,0x00,0x21 = bunlrl+ 2
0x4d,0xe3,0x00,0x21 = bunlrl+ 0
0x4d,0xeb,0x04,0x21 = bunctrl+ 2
0x4d,0xe3,0x04,0x21 = bunctrl+ 0
0x4d,0xcb,0x00,0x20 = bunlr- 2
0x4d,0xc3,0x00,0x20 = bunlr- 0
0x4d,0xcb,0x04,0x20 = bunctr- 2
0x4d,0xc3,0x04,0x20 = bunctr- 0
0x4d,0xcb,0x00,0x21 = bunlrl- 2
0x4d,0xc3,0x00,0x21 = bunlrl- 0
0x4d,0xcb,0x04,0x21 = bunctrl- 2
0x4d,0xc3,0x04,0x21 = bunctrl- 0
0x4c,0x8b,0x00,0x20 = bnulr 2
0x4c,0x83,0x00,0x20 = bnulr 0
0x4c,0x8b,0x04,0x20 = bnuctr 2
0x4c,0x83,0x04,0x20 = bnuctr 0
0x4c,0x8b,0x00,0x21 = bnulrl 2
0x4c,0x83,0x00,0x21 = bnulrl 0
0x4c,0x8b,0x04,0x21 = bnuctrl 2
0x4c,0x83,0x04,0x21 = bnuctrl 0
0x4c,0xeb,0x00,0x20 = bnulr+ 2
0x4c,0xe3,0x00,0x20 = bnulr+ 0
0x4c,0xeb,0x04,0x20 = bnuctr+ 2
0x4c,0xe3,0x04,0x20 = bnuctr+ 0
0x4c,0xeb,0x00,0x21 = bnulrl+ 2
0x4c,0xe3,0x00,0x21 = bnulrl+ 0
0x4c,0xeb,0x04,0x21 = bnuctrl+ 2
0x4c,0xe3,0x04,0x21 = bnuctrl+ 0
0x4c,0xcb,0x00,0x20 = bnulr- 2
0x4c,0xc3,0x00,0x20 = bnulr- 0
0x4c,0xcb,0x04,0x20 = bnuctr- 2
0x4c,0xc3,0x04,0x20 = bnuctr- 0
0x4c,0xcb,0x00,0x21 = bnulrl- 2
0x4c,0xc3,0x00,0x21 = bnulrl- 0
0x4c,0xcb,0x04,0x21 = bnuctrl- 2
0x4c,0xc3,0x04,0x21 = bnuctrl- 0
0x4c,0x42,0x12,0x42 = creqv 2, 2, 2
0x4c,0x42,0x11,0x82 = crxor 2, 2, 2
0x4c,0x43,0x1b,0x82 = cror 2, 3, 3
0x4c,0x43,0x18,0x42 = crnor 2, 3, 3
0x38,0x43,0xff,0x80 = addi 2, 3, -128
0x3c,0x43,0xff,0x80 = addis 2, 3, -128
0x30,0x43,0xff,0x80 = addic 2, 3, -128
0x34,0x43,0xff,0x80 = addic. 2, 3, -128
0x7c,0x44,0x18,0x50 = subf 2, 4, 3
0x7c,0x44,0x18,0x51 = subf. 2, 4, 3
0x7c,0x44,0x18,0x10 = subfc 2, 4, 3
0x7c,0x44,0x18,0x11 = subfc. 2, 4, 3
0x2d,0x23,0x00,0x80 = cmpdi 2, 3, 128
0x2c,0x23,0x00,0x80 = cmpdi 0, 3, 128
0x7d,0x23,0x20,0x00 = cmpd 2, 3, 4
0x7c,0x23,0x20,0x00 = cmpd 0, 3, 4
0x29,0x23,0x00,0x80 = cmpldi 2, 3, 128
0x28,0x23,0x00,0x80 = cmpldi 0, 3, 128
0x7d,0x23,0x20,0x40 = cmpld 2, 3, 4
0x7c,0x23,0x20,0x40 = cmpld 0, 3, 4
0x2d,0x03,0x00,0x80 = cmpwi 2, 3, 128
0x2c,0x03,0x00,0x80 = cmpwi 0, 3, 128
0x7d,0x03,0x20,0x00 = cmpw 2, 3, 4
0x7c,0x03,0x20,0x00 = cmpw 0, 3, 4
0x29,0x03,0x00,0x80 = cmplwi 2, 3, 128
0x28,0x03,0x00,0x80 = cmplwi 0, 3, 128
0x7d,0x03,0x20,0x40 = cmplw 2, 3, 4
0x7c,0x03,0x20,0x40 = cmplw 0, 3, 4
0x0e,0x03,0x00,0x04 = twi 16, 3, 4
0x7e,0x03,0x20,0x08 = tw 16, 3, 4
0x0a,0x03,0x00,0x04 = tdi 16, 3, 4
0x7e,0x03,0x20,0x88 = td 16, 3, 4
0x0e,0x83,0x00,0x04 = twi 20, 3, 4
0x7e,0x83,0x20,0x08 = tw 20, 3, 4
0x0a,0x83,0x00,0x04 = tdi 20, 3, 4
0x7e,0x83,0x20,0x88 = td 20, 3, 4
0x0c,0x83,0x00,0x04 = twi 4, 3, 4
0x7c,0x83,0x20,0x08 = tw 4, 3, 4
0x08,0x83,0x00,0x04 = tdi 4, 3, 4
0x7c,0x83,0x20,0x88 = td 4, 3, 4
0x0d,0x83,0x00,0x04 = twi 12, 3, 4
0x7d,0x83,0x20,0x08 = tw 12, 3, 4
0x09,0x83,0x00,0x04 = tdi 12, 3, 4
0x7d,0x83,0x20,0x88 = td 12, 3, 4
0x0d,0x03,0x00,0x04 = twi 8, 3, 4
0x7d,0x03,0x20,0x08 = tw 8, 3, 4
0x09,0x03,0x00,0x04 = tdi 8, 3, 4
0x7d,0x03,0x20,0x88 = td 8, 3, 4
0x0d,0x83,0x00,0x04 = twi 12, 3, 4
0x7d,0x83,0x20,0x08 = tw 12, 3, 4
0x09,0x83,0x00,0x04 = tdi 12, 3, 4
0x7d,0x83,0x20,0x88 = td 12, 3, 4
0x0f,0x03,0x00,0x04 = twi 24, 3, 4
0x7f,0x03,0x20,0x08 = tw 24, 3, 4
0x0b,0x03,0x00,0x04 = tdi 24, 3, 4
0x7f,0x03,0x20,0x88 = td 24, 3, 4
0x0e,0x83,0x00,0x04 = twi 20, 3, 4
0x7e,0x83,0x20,0x08 = tw 20, 3, 4
0x0a,0x83,0x00,0x04 = tdi 20, 3, 4
0x7e,0x83,0x20,0x88 = td 20, 3, 4
0x0c,0x43,0x00,0x04 = twi 2, 3, 4
0x7c,0x43,0x20,0x08 = tw 2, 3, 4
0x08,0x43,0x00,0x04 = tdi 2, 3, 4
0x7c,0x43,0x20,0x88 = td 2, 3, 4
0x0c,0xc3,0x00,0x04 = twi 6, 3, 4
0x7c,0xc3,0x20,0x08 = tw 6, 3, 4
0x08,0xc3,0x00,0x04 = tdi 6, 3, 4
0x7c,0xc3,0x20,0x88 = td 6, 3, 4
0x0c,0xa3,0x00,0x04 = twi 5, 3, 4
0x7c,0xa3,0x20,0x08 = tw 5, 3, 4
0x08,0xa3,0x00,0x04 = tdi 5, 3, 4
0x7c,0xa3,0x20,0x88 = td 5, 3, 4
0x0c,0x23,0x00,0x04 = twi 1, 3, 4
0x7c,0x23,0x20,0x08 = tw 1, 3, 4
0x08,0x23,0x00,0x04 = tdi 1, 3, 4
0x7c,0x23,0x20,0x88 = td 1, 3, 4
0x0c,0xa3,0x00,0x04 = twi 5, 3, 4
0x7c,0xa3,0x20,0x08 = tw 5, 3, 4
0x08,0xa3,0x00,0x04 = tdi 5, 3, 4
0x7c,0xa3,0x20,0x88 = td 5, 3, 4
0x0c,0xc3,0x00,0x04 = twi 6, 3, 4
0x7c,0xc3,0x20,0x08 = tw 6, 3, 4
0x08,0xc3,0x00,0x04 = tdi 6, 3, 4
0x7c,0xc3,0x20,0x88 = td 6, 3, 4
0x0f,0xe3,0x00,0x04 = twi 31, 3, 4
0x7f,0xe3,0x20,0x08 = tw 31, 3, 4
0x0b,0xe3,0x00,0x04 = tdi 31, 3, 4
0x7f,0xe3,0x20,0x88 = td 31, 3, 4
0x7f,0xe0,0x00,0x08 = trap 
0x78,0x62,0x28,0xc4 = rldicr 2, 3, 5, 3
0x78,0x62,0x28,0xc5 = rldicr. 2, 3, 5, 3
0x78,0x62,0x4f,0x20 = rldicl 2, 3, 9, 60
0x78,0x62,0x4f,0x21 = rldicl. 2, 3, 9, 60
0x78,0x62,0xb9,0x4e = rldimi 2, 3, 55, 5
0x78,0x62,0xb9,0x4f = rldimi. 2, 3, 55, 5
0x78,0x62,0x20,0x00 = rldicl 2, 3, 4, 0
0x78,0x62,0x20,0x01 = rldicl. 2, 3, 4, 0
0x78,0x62,0xe0,0x02 = rldicl 2, 3, 60, 0
0x78,0x62,0xe0,0x03 = rldicl. 2, 3, 60, 0
0x78,0x62,0x20,0x10 = rldcl 2, 3, 4, 0
0x78,0x62,0x20,0x11 = rldcl. 2, 3, 4, 0
0x78,0x62,0x26,0xe4 = sldi 2, 3, 4
0x78,0x62,0x26,0xe5 = rldicr. 2, 3, 4, 59
0x78,0x62,0xe1,0x02 = rldicl 2, 3, 60, 4
0x78,0x62,0xe1,0x03 = rldicl. 2, 3, 60, 4
0x78,0x62,0x01,0x00 = rldicl 2, 3, 0, 4
0x78,0x62,0x01,0x01 = rldicl. 2, 3, 0, 4
0x78,0x62,0x06,0xe4 = rldicr 2, 3, 0, 59
0x78,0x62,0x06,0xe5 = rldicr. 2, 3, 0, 59
0x78,0x62,0x20,0x48 = rldic 2, 3, 4, 1
0x78,0x62,0x20,0x49 = rldic. 2, 3, 4, 1
0x54,0x62,0x28,0x06 = rlwinm 2, 3, 5, 0, 3
0x54,0x62,0x28,0x07 = rlwinm. 2, 3, 5, 0, 3
0x54,0x62,0x4f,0x3e = rlwinm 2, 3, 9, 28, 31
0x54,0x62,0x4f,0x3f = rlwinm. 2, 3, 9, 28, 31
0x50,0x62,0xd9,0x50 = rlwimi 2, 3, 27, 5, 8
0x50,0x62,0xd9,0x51 = rlwimi. 2, 3, 27, 5, 8
0x50,0x62,0xb9,0x50 = rlwimi 2, 3, 23, 5, 8
0x50,0x62,0xb9,0x51 = rlwimi. 2, 3, 23, 5, 8
0x54,0x62,0x20,0x3e = rlwinm 2, 3, 4, 0, 31
0x54,0x62,0x20,0x3f = rlwinm. 2, 3, 4, 0, 31
0x54,0x62,0xe0,0x3e = rlwinm 2, 3, 28, 0, 31
0x54,0x62,0xe0,0x3f = rlwinm. 2, 3, 28, 0, 31
0x5c,0x62,0x20,0x3e = rlwnm 2, 3, 4, 0, 31
0x5c,0x62,0x20,0x3f = rlwnm. 2, 3, 4, 0, 31
0x54,0x62,0x20,0x36 = slwi 2, 3, 4
0x54,0x62,0x20,0x37 = rlwinm. 2, 3, 4, 0, 27
0x54,0x62,0xe1,0x3e = srwi 2, 3, 4
0x54,0x62,0xe1,0x3f = rlwinm. 2, 3, 28, 4, 31
0x54,0x62,0x01,0x3e = rlwinm 2, 3, 0, 4, 31
0x54,0x62,0x01,0x3f = rlwinm. 2, 3, 0, 4, 31
0x54,0x62,0x00,0x36 = rlwinm 2, 3, 0, 0, 27
0x54,0x62,0x00,0x37 = rlwinm. 2, 3, 0, 0, 27
0x54,0x62,0x20,0x76 = rlwinm 2, 3, 4, 1, 27
0x54,0x62,0x20,0x77 = rlwinm. 2, 3, 4, 1, 27
0x7c,0x41,0x03,0xa6 = mtspr 1, 2
0x7c,0x41,0x02,0xa6 = mfspr 2, 1
0x7c,0x48,0x03,0xa6 = mtlr 2
0x7c,0x48,0x02,0xa6 = mflr 2
0x7c,0x49,0x03,0xa6 = mtctr 2
0x7c,0x49,0x02,0xa6 = mfctr 2
0x60,0x00,0x00,0x00 = nop 
0x68,0x00,0x00,0x00 = xori 0, 0, 0
0x38,0x40,0x00,0x80 = li 2, 128
0x3c,0x40,0x00,0x80 = lis 2, 128
0x7c,0x62,0x1b,0x78 = mr 2, 3
0x7c,0x62,0x1b,0x79 = or. 2, 3, 3
0x7c,0x62,0x18,0xf8 = nor 2, 3, 3
0x7c,0x62,0x18,0xf9 = nor. 2, 3, 3
0x7c,0x4f,0xf1,0x20 = mtcrf 255, 2
