# CS_ARCH_MIPS, CS_MODE_MIPS64+CS_MODE_BIG_ENDIAN, None
0x02,0x04,0x80,0x20 = add $16, $16, $4
0x02,0x06,0x80,0x20 = add $16, $16, $6
0x02,0x07,0x80,0x20 = add $16, $16, $7
0x02,0x08,0x80,0x20 = add $16, $16, $8
0x02,0x09,0x80,0x20 = add $16, $16, $9
0x02,0x0a,0x80,0x20 = add $16, $16, $10
0x02,0x0b,0x80,0x20 = add $16, $16, $11
0x02,0x0c,0x80,0x20 = add $16, $16, $12
0x02,0x0d,0x80,0x20 = add $16, $16, $13
0x02,0x0e,0x80,0x20 = add $16, $16, $14
0x02,0x0f,0x80,0x20 = add $16, $16, $15
