# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_BIG_ENDIAN+CS_MODE_MICRO, None
0x00,0xe6,0x49,0x10 = add $9, $6, $7
0x11,0x26,0x45,0x67 = addi $9, $6, 17767
0x31,0x26,0xc5,0x67 = addiu $9, $6, -15001
0x11,0x26,0x45,0x67 = addi $9, $6, 17767
0x31,0x26,0xc5,0x67 = addiu $9, $6, -15001
0x00,0xe6,0x49,0x50 = addu $9, $6, $7
0x00,0xe6,0x49,0x90 = sub $9, $6, $7
0x00,0xa3,0x21,0xd0 = subu $4, $3, $5
0x00,0xe0,0x31,0x90 = neg $6, $7
0x00,0xe0,0x31,0xd0 = negu $6, $7
0x00,0x08,0x39,0x50 = move $7, $8
0x00,0xa3,0x1b,0x50 = slt $3, $3, $5
0x90,0x63,0x00,0x67 = slti $3, $3, 103
0x90,0x63,0x00,0x67 = slti $3, $3, 103
0xb0,0x63,0x00,0x67 = sltiu $3, $3, 103
0x00,0xa3,0x1b,0x90 = sltu $3, $3, $5
0x41,0xa9,0x45,0x67 = lui $9, 17767
0x00,0xe6,0x4a,0x50 = and $9, $6, $7
0xd1,0x26,0x45,0x67 = andi $9, $6, 17767
0xd1,0x26,0x45,0x67 = andi $9, $6, 17767
0x00,0xa4,0x1a,0x90 = or $3, $4, $5
0x51,0x26,0x45,0x67 = ori $9, $6, 17767
0x00,0xa3,0x1b,0x10 = xor $3, $3, $5
0x71,0x26,0x45,0x67 = xori $9, $6, 17767
0x71,0x26,0x45,0x67 = xori $9, $6, 17767
0x00,0xe6,0x4a,0xd0 = nor $9, $6, $7
0x00,0x08,0x3a,0xd0 = not $7, $8
0x00,0xe6,0x4a,0x10 = mul $9, $6, $7
0x00,0xe9,0x8b,0x3c = mult $9, $7
0x00,0xe9,0x9b,0x3c = multu $9, $7
0x00,0xe9,0xab,0x3c = div $zero, $9, $7
0x00,0xe9,0xbb,0x3c = divu $zero, $9, $7
