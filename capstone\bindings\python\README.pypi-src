NOTE: This PyPi package "capstone" includes source code of the core of Capstone.
So installing this would also compile the core with C compiler (either "gcc" or
"msvc" on Windows).

On Windows, MSVC needs compiler environmental setup, you would need to either
run "pip install capstone" or "python setup.py install" from "Developer Command
Prompt".

For Windows, if you do not want to compile the core, try the "capstone-windows"
package instead, which already includes the prebuilt "capstone.dll" inside.

    https://pypi.python.org/pypi/capstone-windows

--------------------------------------------------------------------------------

Capstone is a disassembly framework with the target of becoming the ultimate
disasm engine for binary analysis and reversing in the security community.

Created by <PERSON><PERSON><PERSON>, then developed and maintained by a small community,
Capstone offers some unparalleled features:

- Support multiple hardware architectures: ARM, ARM64 (ARMv8), Mips, PPC, Sparc,
  SystemZ, XCore and X86 (including X86_64).

- Having clean/simple/lightweight/intuitive architecture-neutral API.

- Provide details on disassembled instruction (called “decomposer” by others).

- Provide semantics of the disassembled instruction, such as list of implicit
  registers read & written.

- Implemented in pure C language, with lightweight wrappers for C++, C#, Go,
  Java, NodeJS, Ocaml, Python, Ruby & Vala ready (available in main code,
  or provided externally by the community).

- Native support for all popular platforms: Windows, Mac OSX, iOS, Android,
  Linux, *BSD, Solaris, etc.

- Thread-safe by design.

- Special support for embedding into firmware or OS kernel.

- High performance & suitable for malware analysis (capable of handling various
  X86 malware tricks).

- Distributed under the open source BSD license.

Further information is available at http://www.capstone-engine.org


[License]

This project is released under the BSD license. If you redistribute the binary
or source code of Capstone, please attach file LICENSE.TXT with your products.
