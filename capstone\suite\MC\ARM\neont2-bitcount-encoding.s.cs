# CS_ARCH_ARM, CS_MODE_THUMB, None
0xf0,0xff,0x20,0x05 = vcnt.8 d16, d16
0xf0,0xff,0x60,0x05 = vcnt.8 q8, q8
0xf0,0xff,0xa0,0x04 = vclz.i8 d16, d16
0xf4,0xff,0xa0,0x04 = vclz.i16 d16, d16
0xf8,0xff,0xa0,0x04 = vclz.i32 d16, d16
0xf0,0xff,0xe0,0x04 = vclz.i8 q8, q8
0xf4,0xff,0xe0,0x04 = vclz.i16 q8, q8
0xf8,0xff,0xe0,0x04 = vclz.i32 q8, q8
0xf0,0xff,0x20,0x04 = vcls.s8 d16, d16
0xf4,0xff,0x20,0x04 = vcls.s16 d16, d16
0xf8,0xff,0x20,0x04 = vcls.s32 d16, d16
0xf0,0xff,0x60,0x04 = vcls.s8 q8, q8
0xf4,0xff,0x60,0x04 = vcls.s16 q8, q8
0xf8,0xff,0x60,0x04 = vcls.s32 q8, q8
