# CS_ARCH_ARM, CS_MODE_THUMB+CS_MODE_V8, None
0xd4,0xe8,0xcf,0x3f = ldaexb r3, [r4]
0xd5,0xe8,0xdf,0x2f = ldaexh r2, [r5]
0xd7,0xe8,0xef,0x1f = ldaex r1, [r7]
0xd8,0xe8,0xff,0x67 = ldaexd r6, r7, [r8]
0xc4,0xe8,0xc1,0x3f = stlexb r1, r3, [r4]
0xc5,0xe8,0xd4,0x2f = stlexh r4, r2, [r5]
0xc7,0xe8,0xe2,0x1f = stlex r2, r1, [r7]
0xc8,0xe8,0xf6,0x23 = stlexd r6, r2, r3, [r8]
0xd6,0xe8,0xaf,0x5f = lda r5, [r6]
0xd6,0xe8,0x8f,0x5f = ldab r5, [r6]
0xd9,0xe8,0x9f,0xcf = ldah r12, [r9]
0xc0,0xe8,0xaf,0x3f = stl r3, [r0]
0xc1,0xe8,0x8f,0x2f = stlb r2, [r1]
0xc3,0xe8,0x9f,0x2f = stlh r2, [r3]
