// Copyright (c) 2025, <PERSON><PERSON><PERSON>on EPT Process Hiding. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Declares EPT-based process hiding functionality.

#pragma once

#include "../HyperPlatform/HyperPlatform/common.h"

extern "C" {

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding function declarations
//

/// @brief Initialize process hiding hooks
/// @return STATUS_SUCCESS on success, error code otherwise
NTSTATUS ProcessHidingInitialize(void);

/// @brief Terminate process hiding hooks
VOID ProcessHidingTerminate(void);

/// @brief Check if process hiding hooks are active
/// @return TRUE if hooks are active, FALSE otherwise
BOOLEAN ProcessHidingIsActive(void);

////////////////////////////////////////////////////////////////////////////////
//
// System process information structures
//

// System process information structure (partial definition)
typedef struct _SYSTEM_PROCESS_INFORMATION {
    ULONG NextEntryOffset;
    ULONG NumberOfThreads;
    LARGE_INTEGER WorkingSetPrivateSize;
    ULONG HardFaultCount;
    ULONG NumberOfThreadsHighWatermark;
    ULONGLONG CycleTime;
    LARGE_INTEGER CreateTime;
    LARGE_INTEGER UserTime;
    LARGE_INTEGER KernelTime;
    UNICODE_STRING ImageName;
    KPRIORITY BasePriority;
    HANDLE UniqueProcessId;
    HANDLE InheritedFromUniqueProcessId;
    ULONG HandleCount;
    ULONG SessionId;
    ULONG_PTR UniqueProcessKey;
    SIZE_T PeakVirtualSize;
    SIZE_T VirtualSize;
    ULONG PageFaultCount;
    SIZE_T PeakWorkingSetSize;
    SIZE_T WorkingSetSize;
    SIZE_T QuotaPeakPagedPoolUsage;
    SIZE_T QuotaPagedPoolUsage;
    SIZE_T QuotaPeakNonPagedPoolUsage;
    SIZE_T QuotaNonPagedPoolUsage;
    SIZE_T PagefileUsage;
    SIZE_T PeakPagefileUsage;
    SIZE_T PrivatePageCount;
    LARGE_INTEGER ReadOperationCount;
    LARGE_INTEGER WriteOperationCount;
    LARGE_INTEGER OtherOperationCount;
    LARGE_INTEGER ReadTransferCount;
    LARGE_INTEGER WriteTransferCount;
    LARGE_INTEGER OtherTransferCount;
} SYSTEM_PROCESS_INFORMATION, *PSYSTEM_PROCESS_INFORMATION;

}  // extern "C"
