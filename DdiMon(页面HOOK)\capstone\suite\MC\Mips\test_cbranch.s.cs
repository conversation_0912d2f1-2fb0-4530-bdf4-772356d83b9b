# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_BIG_ENDIAN, None
0x47,0x80,0x00,0x01 = bnz.b $w0, 4
0x47,0xa1,0x00,0x04 = bnz.h $w1, 16
0x47,0xc2,0x00,0x20 = bnz.w $w2, 128
0x47,0xe3,0xff,0xe0 = bnz.d $w3, -128
0x45,0xe0,0x00,0x01 = bnz.v $w0, 4
0x47,0x00,0x00,0x20 = bz.b $w0, 128
0x47,0x21,0x00,0x40 = bz.h $w1, 256
0x47,0x42,0x00,0x80 = bz.w $w2, 512
0x47,0x63,0xff,0x00 = bz.d $w3, -1024
0x45,0x60,0x00,0x01 = bz.v $w0, 4
