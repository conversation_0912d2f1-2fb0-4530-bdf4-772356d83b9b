# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_BIG_ENDIAN, None
0x7b,0x20,0x66,0x9e = fclass.w $w26, $w12
0x7b,0x21,0x8e,0x1e = fclass.d $w24, $w17
0x7b,0x30,0x02,0x1e = fexupl.w $w8, $w0
0x7b,0x31,0xec,0x5e = fexupl.d $w17, $w29
0x7b,0x32,0x23,0x5e = fexupr.w $w13, $w4
0x7b,0x33,0x11,0x5e = fexupr.d $w5, $w2
0x7b,0x3c,0xed,0x1e = ffint_s.w $w20, $w29
0x7b,0x3d,0x7b,0x1e = ffint_s.d $w12, $w15
0x7b,0x3e,0xd9,0xde = ffint_u.w $w7, $w27
0x7b,0x3f,0x84,0xde = ffint_u.d $w19, $w16
0x7b,0x34,0x6f,0xde = ffql.w $w31, $w13
0x7b,0x35,0x6b,0x1e = ffql.d $w12, $w13
0x7b,0x36,0xf6,0xde = ffqr.w $w27, $w30
0x7b,0x37,0x7f,0x9e = ffqr.d $w30, $w15
0x7b,0x2e,0xfe,0x5e = flog2.w $w25, $w31
0x7b,0x2f,0x54,0x9e = flog2.d $w18, $w10
0x7b,0x2c,0x79,0xde = frint.w $w7, $w15
0x7b,0x2d,0xb5,0x5e = frint.d $w21, $w22
0x7b,0x2a,0x04,0xde = frcp.w $w19, $w0
0x7b,0x2b,0x71,0x1e = frcp.d $w4, $w14
0x7b,0x28,0x8b,0x1e = frsqrt.w $w12, $w17
0x7b,0x29,0x5d,0xde = frsqrt.d $w23, $w11
0x7b,0x26,0x58,0x1e = fsqrt.w $w0, $w11
0x7b,0x27,0x63,0xde = fsqrt.d $w15, $w12
0x7b,0x38,0x2f,0x9e = ftint_s.w $w30, $w5
0x7b,0x39,0xb9,0x5e = ftint_s.d $w5, $w23
0x7b,0x3a,0x75,0x1e = ftint_u.w $w20, $w14
0x7b,0x3b,0xad,0xde = ftint_u.d $w23, $w21
0x7b,0x22,0x8f,0x5e = ftrunc_s.w $w29, $w17
0x7b,0x23,0xdb,0x1e = ftrunc_s.d $w12, $w27
0x7b,0x24,0x7c,0x5e = ftrunc_u.w $w17, $w15
0x7b,0x25,0xd9,0x5e = ftrunc_u.d $w5, $w27
