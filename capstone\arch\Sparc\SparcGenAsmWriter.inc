/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */

#include <stdio.h>	// debug
#include <platform.h>


/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    2452U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    2445U,	// BUNDLE
    2462U,	// LIFETIME_START
    2432U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    4688U,	// ADDCCri
    4688U,	// ADDCCrr
    5925U,	// ADDCri
    5925U,	// ADDCrr
    4772U,	// ADDEri
    4772U,	// ADDErr
    4786U,	// ADDXC
    4678U,	// ADDXCCC
    4808U,	// ADDXri
    4808U,	// ADDXrr
    4808U,	// ADDri
    4808U,	// ADDrr
    74166U,	// ADJCALLSTACKDOWN
    74185U,	// ADJCALLSTACKUP
    5497U,	// ALIGNADDR
    5127U,	// ALIGNADDRL
    4695U,	// ANDCCri
    4695U,	// ANDCCrr
    4718U,	// ANDNCCri
    4718U,	// ANDNCCrr
    5182U,	// ANDNri
    5182U,	// ANDNrr
    5182U,	// ANDXNrr
    4876U,	// ANDXri
    4876U,	// ANDXrr
    4876U,	// ANDri
    4876U,	// ANDrr
    4502U,	// ARRAY16
    4255U,	// ARRAY32
    4526U,	// ARRAY8
    0U,	// ATOMIC_LOAD_ADD_32
    0U,	// ATOMIC_LOAD_ADD_64
    0U,	// ATOMIC_LOAD_AND_32
    0U,	// ATOMIC_LOAD_AND_64
    0U,	// ATOMIC_LOAD_MAX_32
    0U,	// ATOMIC_LOAD_MAX_64
    0U,	// ATOMIC_LOAD_MIN_32
    0U,	// ATOMIC_LOAD_MIN_64
    0U,	// ATOMIC_LOAD_NAND_32
    0U,	// ATOMIC_LOAD_NAND_64
    0U,	// ATOMIC_LOAD_OR_32
    0U,	// ATOMIC_LOAD_OR_64
    0U,	// ATOMIC_LOAD_SUB_32
    0U,	// ATOMIC_LOAD_SUB_64
    0U,	// ATOMIC_LOAD_UMAX_32
    0U,	// ATOMIC_LOAD_UMAX_64
    0U,	// ATOMIC_LOAD_UMIN_32
    0U,	// ATOMIC_LOAD_UMIN_64
    0U,	// ATOMIC_LOAD_XOR_32
    0U,	// ATOMIC_LOAD_XOR_64
    0U,	// ATOMIC_SWAP_64
    74271U,	// BA
    1194492U,	// BCOND
    1260028U,	// BCONDA
    17659U,	// BINDri
    17659U,	// BINDrr
    5065U,	// BMASK
    145915U,	// BPFCC
    211451U,	// BPFCCA
    276987U,	// BPFCCANT
    342523U,	// BPFCCNT
    2106465U,	// BPGEZapn
    2105838U,	// BPGEZapt
    2106532U,	// BPGEZnapn
    2107288U,	// BPGEZnapt
    2106489U,	// BPGZapn
    2105856U,	// BPGZapt
    2106552U,	// BPGZnapn
    2107384U,	// BPGZnapt
    1456636U,	// BPICC
    473596U,	// BPICCA
    539132U,	// BPICCANT
    604668U,	// BPICCNT
    2106477U,	// BPLEZapn
    2105847U,	// BPLEZapt
    2106542U,	// BPLEZnapn
    2107337U,	// BPLEZnapt
    2106500U,	// BPLZapn
    2105864U,	// BPLZapt
    2106561U,	// BPLZnapn
    2107428U,	// BPLZnapt
    2106511U,	// BPNZapn
    2105872U,	// BPNZapt
    2106570U,	// BPNZnapn
    2107472U,	// BPNZnapt
    1718780U,	// BPXCC
    735740U,	// BPXCCA
    801276U,	// BPXCCANT
    866812U,	// BPXCCNT
    2106522U,	// BPZapn
    2105880U,	// BPZapt
    2106579U,	// BPZnapn
    2107505U,	// BPZnapt
    4983U,	// BSHUFFLE
    74742U,	// CALL
    17398U,	// CALLri
    17398U,	// CALLrr
    924148U,	// CASXrr
    924129U,	// CASrr
    74001U,	// CMASK16
    73833U,	// CMASK32
    74150U,	// CMASK8
    2106607U,	// CMPri
    2106607U,	// CMPrr
    4332U,	// EDGE16
    5081U,	// EDGE16L
    5198U,	// EDGE16LN
    5165U,	// EDGE16N
    4164U,	// EDGE32
    5072U,	// EDGE32L
    5188U,	// EDGE32LN
    5156U,	// EDGE32N
    4511U,	// EDGE8
    5090U,	// EDGE8L
    5208U,	// EDGE8LN
    5174U,	// EDGE8N
    1053516U,	// FABSD
    1054031U,	// FABSQ
    1054376U,	// FABSS
    4813U,	// FADDD
    5383U,	// FADDQ
    5645U,	// FADDS
    4648U,	// FALIGNADATA
    4875U,	// FAND
    4112U,	// FANDNOT1
    5544U,	// FANDNOT1S
    4271U,	// FANDNOT2
    5591U,	// FANDNOT2S
    5677U,	// FANDS
    1194491U,	// FBCOND
    1260027U,	// FBCONDA
    4394U,	// FCHKSM16
    2106173U,	// FCMPD
    4413U,	// FCMPEQ16
    4226U,	// FCMPEQ32
    4432U,	// FCMPGT16
    4245U,	// FCMPGT32
    4340U,	// FCMPLE16
    4172U,	// FCMPLE32
    4350U,	// FCMPNE16
    4182U,	// FCMPNE32
    2106696U,	// FCMPQ
    2107005U,	// FCMPS
    4960U,	// FDIVD
    5475U,	// FDIVQ
    5815U,	// FDIVS
    5405U,	// FDMULQ
    1053620U,	// FDTOI
    1053996U,	// FDTOQ
    1054305U,	// FDTOS
    1054536U,	// FDTOX
    1053464U,	// FEXPAND
    4820U,	// FHADDD
    5652U,	// FHADDS
    4800U,	// FHSUBD
    5637U,	// FHSUBS
    1053473U,	// FITOD
    1054003U,	// FITOQ
    1054312U,	// FITOS
    6300484U,	// FLCMPD
    6301316U,	// FLCMPS
    2606U,	// FLUSHW
    4404U,	// FMEAN16
    1053543U,	// FMOVD
    1006078U,	// FMOVD_FCC
    23484926U,	// FMOVD_ICC
    23747070U,	// FMOVD_XCC
    1054058U,	// FMOVQ
    1006102U,	// FMOVQ_FCC
    23484950U,	// FMOVQ_ICC
    23747094U,	// FMOVQ_XCC
    6018U,	// FMOVRGEZD
    6029U,	// FMOVRGEZQ
    6056U,	// FMOVRGEZS
    6116U,	// FMOVRGZD
    6126U,	// FMOVRGZQ
    6150U,	// FMOVRGZS
    6067U,	// FMOVRLEZD
    6078U,	// FMOVRLEZQ
    6105U,	// FMOVRLEZS
    6160U,	// FMOVRLZD
    6170U,	// FMOVRLZQ
    6194U,	// FMOVRLZS
    6204U,	// FMOVRNZD
    6214U,	// FMOVRNZQ
    6238U,	// FMOVRNZS
    6009U,	// FMOVRZD
    6248U,	// FMOVRZQ
    6269U,	// FMOVRZS
    1054398U,	// FMOVS
    1006114U,	// FMOVS_FCC
    23484962U,	// FMOVS_ICC
    23747106U,	// FMOVS_XCC
    4490U,	// FMUL8SUX16
    4465U,	// FMUL8ULX16
    4442U,	// FMUL8X16
    5098U,	// FMUL8X16AL
    5849U,	// FMUL8X16AU
    4860U,	// FMULD
    4477U,	// FMULD8SUX16
    4452U,	// FMULD8ULX16
    5413U,	// FMULQ
    5714U,	// FMULS
    4837U,	// FNADDD
    5669U,	// FNADDS
    4881U,	// FNAND
    5684U,	// FNANDS
    1053429U,	// FNEGD
    1053974U,	// FNEGQ
    1054283U,	// FNEGS
    4828U,	// FNHADDD
    5660U,	// FNHADDS
    4828U,	// FNMULD
    5660U,	// FNMULS
    5513U,	// FNOR
    5778U,	// FNORS
    1052698U,	// FNOT1
    1054131U,	// FNOT1S
    1052857U,	// FNOT2
    1054178U,	// FNOT2S
    5660U,	// FNSMULD
    74625U,	// FONE
    75324U,	// FONES
    5508U,	// FOR
    4129U,	// FORNOT1
    5563U,	// FORNOT1S
    4288U,	// FORNOT2
    5610U,	// FORNOT2S
    5772U,	// FORS
    1052936U,	// FPACK16
    4192U,	// FPACK32
    1054507U,	// FPACKFIX
    4323U,	// FPADD16
    5620U,	// FPADD16S
    4155U,	// FPADD32
    5573U,	// FPADD32S
    4297U,	// FPADD64
    4974U,	// FPMERGE
    4314U,	// FPSUB16
    4580U,	// FPSUB16S
    4146U,	// FPSUB32
    4570U,	// FPSUB32S
    1053480U,	// FQTOD
    1053627U,	// FQTOI
    1054319U,	// FQTOS
    1054552U,	// FQTOX
    4423U,	// FSLAS16
    4236U,	// FSLAS32
    4378U,	// FSLL16
    4210U,	// FSLL32
    4867U,	// FSMULD
    1053523U,	// FSQRTD
    1054038U,	// FSQRTQ
    1054383U,	// FSQRTS
    4306U,	// FSRA16
    4138U,	// FSRA32
    1052681U,	// FSRC1
    1054112U,	// FSRC1S
    1052840U,	// FSRC2
    1054159U,	// FSRC2S
    4386U,	// FSRL16
    4218U,	// FSRL32
    1053487U,	// FSTOD
    1053634U,	// FSTOI
    1054010U,	// FSTOQ
    1054559U,	// FSTOX
    4793U,	// FSUBD
    5376U,	// FSUBQ
    5630U,	// FSUBS
    5519U,	// FXNOR
    5785U,	// FXNORS
    5526U,	// FXOR
    5793U,	// FXORS
    1053494U,	// FXTOD
    1054017U,	// FXTOQ
    1054326U,	// FXTOS
    74984U,	// FZERO
    75353U,	// FZEROS
    24584U,	// GETPCX
    1078273U,	// JMPLri
    1078273U,	// JMPLrr
    1997243U,	// LDDFri
    1997243U,	// LDDFrr
    1997249U,	// LDFri
    1997249U,	// LDFrr
    1997275U,	// LDQFri
    1997275U,	// LDQFrr
    1997229U,	// LDSBri
    1997229U,	// LDSBrr
    1997254U,	// LDSHri
    1997254U,	// LDSHrr
    1997287U,	// LDSWri
    1997287U,	// LDSWrr
    1997236U,	// LDUBri
    1997236U,	// LDUBrr
    1997261U,	// LDUHri
    1997261U,	// LDUHrr
    1997294U,	// LDXri
    1997294U,	// LDXrr
    1997249U,	// LDri
    1997249U,	// LDrr
    33480U,	// LEAX_ADDri
    33480U,	// LEA_ADDri
    1054405U,	// LZCNT
    75121U,	// MEMBARi
    1054543U,	// MOVDTOX
    1006122U,	// MOVFCCri
    1006122U,	// MOVFCCrr
    23484970U,	// MOVICCri
    23484970U,	// MOVICCrr
    6047U,	// MOVRGEZri
    6047U,	// MOVRGEZrr
    6142U,	// MOVRGZri
    6142U,	// MOVRGZrr
    6096U,	// MOVRLEZri
    6096U,	// MOVRLEZrr
    6186U,	// MOVRLZri
    6186U,	// MOVRLZrr
    6230U,	// MOVRNZri
    6230U,	// MOVRNZrr
    6262U,	// MOVRRZri
    6262U,	// MOVRRZrr
    1054469U,	// MOVSTOSW
    1054479U,	// MOVSTOUW
    1054543U,	// MOVWTOS
    23747114U,	// MOVXCCri
    23747114U,	// MOVXCCrr
    1054543U,	// MOVXTOD
    5954U,	// MULXri
    5954U,	// MULXrr
    2578U,	// NOP
    4735U,	// ORCCri
    4735U,	// ORCCrr
    4726U,	// ORNCCri
    4726U,	// ORNCCrr
    5339U,	// ORNri
    5339U,	// ORNrr
    5339U,	// ORXNrr
    5509U,	// ORXri
    5509U,	// ORXrr
    5509U,	// ORri
    5509U,	// ORrr
    5836U,	// PDIST
    5344U,	// PDISTN
    1053356U,	// POPCrr
    73729U,	// RDY
    4999U,	// RESTOREri
    4999U,	// RESTORErr
    76132U,	// RET
    76141U,	// RETL
    18131U,	// RETTri
    18131U,	// RETTrr
    5008U,	// SAVEri
    5008U,	// SAVErr
    4748U,	// SDIVCCri
    4748U,	// SDIVCCrr
    5995U,	// SDIVXri
    5995U,	// SDIVXrr
    5861U,	// SDIVri
    5861U,	// SDIVrr
    2182U,	// SELECT_CC_DFP_FCC
    2293U,	// SELECT_CC_DFP_ICC
    2238U,	// SELECT_CC_FP_FCC
    2349U,	// SELECT_CC_FP_ICC
    2265U,	// SELECT_CC_Int_FCC
    2376U,	// SELECT_CC_Int_ICC
    2210U,	// SELECT_CC_QFP_FCC
    2321U,	// SELECT_CC_QFP_ICC
    1053595U,	// SETHIXi
    1053595U,	// SETHIi
    2569U,	// SHUTDOWN
    2564U,	// SIAM
    5941U,	// SLLXri
    5941U,	// SLLXrr
    5116U,	// SLLri
    5116U,	// SLLrr
    4702U,	// SMULCCri
    4702U,	// SMULCCrr
    5144U,	// SMULri
    5144U,	// SMULrr
    5913U,	// SRAXri
    5913U,	// SRAXrr
    4643U,	// SRAri
    4643U,	// SRArr
    5947U,	// SRLXri
    5947U,	// SRLXrr
    5139U,	// SRLri
    5139U,	// SRLrr
    2588U,	// STBAR
    37428U,	// STBri
    37428U,	// STBrr
    37723U,	// STDFri
    37723U,	// STDFrr
    38607U,	// STFri
    38607U,	// STFrr
    37782U,	// STHri
    37782U,	// STHrr
    38238U,	// STQFri
    38238U,	// STQFrr
    38758U,	// STXri
    38758U,	// STXrr
    38607U,	// STri
    38607U,	// STrr
    4671U,	// SUBCCri
    4671U,	// SUBCCrr
    5919U,	// SUBCri
    5919U,	// SUBCrr
    4764U,	// SUBEri
    4764U,	// SUBErr
    4665U,	// SUBXri
    4665U,	// SUBXrr
    4665U,	// SUBri
    4665U,	// SUBrr
    1997268U,	// SWAPri
    1997268U,	// SWAPrr
    2422U,	// TA3
    2427U,	// TA5
    5883U,	// TADDCCTVri
    5883U,	// TADDCCTVrr
    4687U,	// TADDCCri
    4687U,	// TADDCCrr
    9873960U,	// TICCri
    9873960U,	// TICCrr
    37753544U,	// TLS_ADDXrr
    37753544U,	// TLS_ADDrr
    2106358U,	// TLS_CALL
    39746030U,	// TLS_LDXrr
    39745985U,	// TLS_LDrr
    5873U,	// TSUBCCTVri
    5873U,	// TSUBCCTVrr
    4670U,	// TSUBCCri
    4670U,	// TSUBCCrr
    10136104U,	// TXCCri
    10136104U,	// TXCCrr
    4756U,	// UDIVCCri
    4756U,	// UDIVCCrr
    6002U,	// UDIVXri
    6002U,	// UDIVXrr
    5867U,	// UDIVri
    5867U,	// UDIVrr
    4710U,	// UMULCCri
    4710U,	// UMULCCrr
    5026U,	// UMULXHI
    5150U,	// UMULri
    5150U,	// UMULrr
    74996U,	// UNIMP
    6300477U,	// V9FCMPD
    6300397U,	// V9FCMPED
    6300942U,	// V9FCMPEQ
    6301251U,	// V9FCMPES
    6301000U,	// V9FCMPQ
    6301309U,	// V9FCMPS
    47614U,	// V9FMOVD_FCC
    47638U,	// V9FMOVQ_FCC
    47650U,	// V9FMOVS_FCC
    47658U,	// V9MOVFCCri
    47658U,	// V9MOVFCCrr
    14689692U,	// WRYri
    14689692U,	// WRYrr
    5953U,	// XMULX
    5035U,	// XMULXHI
    4733U,	// XNORCCri
    4733U,	// XNORCCrr
    5520U,	// XNORXrr
    5520U,	// XNORri
    5520U,	// XNORrr
    4741U,	// XORCCri
    4741U,	// XORCCrr
    5527U,	// XORXri
    5527U,	// XORXrr
    5527U,	// XORri
    5527U,	// XORrr
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'r', 'd', 32, '%', 'y', ',', 32, 0,
  /* 8 */ 'f', 's', 'r', 'c', '1', 32, 0,
  /* 15 */ 'f', 'a', 'n', 'd', 'n', 'o', 't', '1', 32, 0,
  /* 25 */ 'f', 'n', 'o', 't', '1', 32, 0,
  /* 32 */ 'f', 'o', 'r', 'n', 'o', 't', '1', 32, 0,
  /* 41 */ 'f', 's', 'r', 'a', '3', '2', 32, 0,
  /* 49 */ 'f', 'p', 's', 'u', 'b', '3', '2', 32, 0,
  /* 58 */ 'f', 'p', 'a', 'd', 'd', '3', '2', 32, 0,
  /* 67 */ 'e', 'd', 'g', 'e', '3', '2', 32, 0,
  /* 75 */ 'f', 'c', 'm', 'p', 'l', 'e', '3', '2', 32, 0,
  /* 85 */ 'f', 'c', 'm', 'p', 'n', 'e', '3', '2', 32, 0,
  /* 95 */ 'f', 'p', 'a', 'c', 'k', '3', '2', 32, 0,
  /* 104 */ 'c', 'm', 'a', 's', 'k', '3', '2', 32, 0,
  /* 113 */ 'f', 's', 'l', 'l', '3', '2', 32, 0,
  /* 121 */ 'f', 's', 'r', 'l', '3', '2', 32, 0,
  /* 129 */ 'f', 'c', 'm', 'p', 'e', 'q', '3', '2', 32, 0,
  /* 139 */ 'f', 's', 'l', 'a', 's', '3', '2', 32, 0,
  /* 148 */ 'f', 'c', 'm', 'p', 'g', 't', '3', '2', 32, 0,
  /* 158 */ 'a', 'r', 'r', 'a', 'y', '3', '2', 32, 0,
  /* 167 */ 'f', 's', 'r', 'c', '2', 32, 0,
  /* 174 */ 'f', 'a', 'n', 'd', 'n', 'o', 't', '2', 32, 0,
  /* 184 */ 'f', 'n', 'o', 't', '2', 32, 0,
  /* 191 */ 'f', 'o', 'r', 'n', 'o', 't', '2', 32, 0,
  /* 200 */ 'f', 'p', 'a', 'd', 'd', '6', '4', 32, 0,
  /* 209 */ 'f', 's', 'r', 'a', '1', '6', 32, 0,
  /* 217 */ 'f', 'p', 's', 'u', 'b', '1', '6', 32, 0,
  /* 226 */ 'f', 'p', 'a', 'd', 'd', '1', '6', 32, 0,
  /* 235 */ 'e', 'd', 'g', 'e', '1', '6', 32, 0,
  /* 243 */ 'f', 'c', 'm', 'p', 'l', 'e', '1', '6', 32, 0,
  /* 253 */ 'f', 'c', 'm', 'p', 'n', 'e', '1', '6', 32, 0,
  /* 263 */ 'f', 'p', 'a', 'c', 'k', '1', '6', 32, 0,
  /* 272 */ 'c', 'm', 'a', 's', 'k', '1', '6', 32, 0,
  /* 281 */ 'f', 's', 'l', 'l', '1', '6', 32, 0,
  /* 289 */ 'f', 's', 'r', 'l', '1', '6', 32, 0,
  /* 297 */ 'f', 'c', 'h', 'k', 's', 'm', '1', '6', 32, 0,
  /* 307 */ 'f', 'm', 'e', 'a', 'n', '1', '6', 32, 0,
  /* 316 */ 'f', 'c', 'm', 'p', 'e', 'q', '1', '6', 32, 0,
  /* 326 */ 'f', 's', 'l', 'a', 's', '1', '6', 32, 0,
  /* 335 */ 'f', 'c', 'm', 'p', 'g', 't', '1', '6', 32, 0,
  /* 345 */ 'f', 'm', 'u', 'l', '8', 'x', '1', '6', 32, 0,
  /* 355 */ 'f', 'm', 'u', 'l', 'd', '8', 'u', 'l', 'x', '1', '6', 32, 0,
  /* 368 */ 'f', 'm', 'u', 'l', '8', 'u', 'l', 'x', '1', '6', 32, 0,
  /* 380 */ 'f', 'm', 'u', 'l', 'd', '8', 's', 'u', 'x', '1', '6', 32, 0,
  /* 393 */ 'f', 'm', 'u', 'l', '8', 's', 'u', 'x', '1', '6', 32, 0,
  /* 405 */ 'a', 'r', 'r', 'a', 'y', '1', '6', 32, 0,
  /* 414 */ 'e', 'd', 'g', 'e', '8', 32, 0,
  /* 421 */ 'c', 'm', 'a', 's', 'k', '8', 32, 0,
  /* 429 */ 'a', 'r', 'r', 'a', 'y', '8', 32, 0,
  /* 437 */ '!', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 32, 0,
  /* 456 */ '!', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 32, 0,
  /* 473 */ 'f', 'p', 's', 'u', 'b', '3', '2', 'S', 32, 0,
  /* 483 */ 'f', 'p', 's', 'u', 'b', '1', '6', 'S', 32, 0,
  /* 493 */ 'b', 'r', 'g', 'e', 'z', ',', 'a', 32, 0,
  /* 502 */ 'b', 'r', 'l', 'e', 'z', ',', 'a', 32, 0,
  /* 511 */ 'b', 'r', 'g', 'z', ',', 'a', 32, 0,
  /* 519 */ 'b', 'r', 'l', 'z', ',', 'a', 32, 0,
  /* 527 */ 'b', 'r', 'n', 'z', ',', 'a', 32, 0,
  /* 535 */ 'b', 'r', 'z', ',', 'a', 32, 0,
  /* 542 */ 'b', 'a', 32, 0,
  /* 546 */ 's', 'r', 'a', 32, 0,
  /* 551 */ 'f', 'a', 'l', 'i', 'g', 'n', 'd', 'a', 't', 'a', 32, 0,
  /* 563 */ 's', 't', 'b', 32, 0,
  /* 568 */ 's', 'u', 'b', 32, 0,
  /* 573 */ 't', 's', 'u', 'b', 'c', 'c', 32, 0,
  /* 581 */ 'a', 'd', 'd', 'x', 'c', 'c', 'c', 32, 0,
  /* 590 */ 't', 'a', 'd', 'd', 'c', 'c', 32, 0,
  /* 598 */ 'a', 'n', 'd', 'c', 'c', 32, 0,
  /* 605 */ 's', 'm', 'u', 'l', 'c', 'c', 32, 0,
  /* 613 */ 'u', 'm', 'u', 'l', 'c', 'c', 32, 0,
  /* 621 */ 'a', 'n', 'd', 'n', 'c', 'c', 32, 0,
  /* 629 */ 'o', 'r', 'n', 'c', 'c', 32, 0,
  /* 636 */ 'x', 'n', 'o', 'r', 'c', 'c', 32, 0,
  /* 644 */ 'x', 'o', 'r', 'c', 'c', 32, 0,
  /* 651 */ 's', 'd', 'i', 'v', 'c', 'c', 32, 0,
  /* 659 */ 'u', 'd', 'i', 'v', 'c', 'c', 32, 0,
  /* 667 */ 's', 'u', 'b', 'x', 'c', 'c', 32, 0,
  /* 675 */ 'a', 'd', 'd', 'x', 'c', 'c', 32, 0,
  /* 683 */ 'p', 'o', 'p', 'c', 32, 0,
  /* 689 */ 'a', 'd', 'd', 'x', 'c', 32, 0,
  /* 696 */ 'f', 's', 'u', 'b', 'd', 32, 0,
  /* 703 */ 'f', 'h', 's', 'u', 'b', 'd', 32, 0,
  /* 711 */ 'a', 'd', 'd', 32, 0,
  /* 716 */ 'f', 'a', 'd', 'd', 'd', 32, 0,
  /* 723 */ 'f', 'h', 'a', 'd', 'd', 'd', 32, 0,
  /* 731 */ 'f', 'n', 'h', 'a', 'd', 'd', 'd', 32, 0,
  /* 740 */ 'f', 'n', 'a', 'd', 'd', 'd', 32, 0,
  /* 748 */ 'f', 'c', 'm', 'p', 'e', 'd', 32, 0,
  /* 756 */ 'f', 'n', 'e', 'g', 'd', 32, 0,
  /* 763 */ 'f', 'm', 'u', 'l', 'd', 32, 0,
  /* 770 */ 'f', 's', 'm', 'u', 'l', 'd', 32, 0,
  /* 778 */ 'f', 'a', 'n', 'd', 32, 0,
  /* 784 */ 'f', 'n', 'a', 'n', 'd', 32, 0,
  /* 791 */ 'f', 'e', 'x', 'p', 'a', 'n', 'd', 32, 0,
  /* 800 */ 'f', 'i', 't', 'o', 'd', 32, 0,
  /* 807 */ 'f', 'q', 't', 'o', 'd', 32, 0,
  /* 814 */ 'f', 's', 't', 'o', 'd', 32, 0,
  /* 821 */ 'f', 'x', 't', 'o', 'd', 32, 0,
  /* 828 */ 'f', 'c', 'm', 'p', 'd', 32, 0,
  /* 835 */ 'f', 'l', 'c', 'm', 'p', 'd', 32, 0,
  /* 843 */ 'f', 'a', 'b', 's', 'd', 32, 0,
  /* 850 */ 'f', 's', 'q', 'r', 't', 'd', 32, 0,
  /* 858 */ 's', 't', 'd', 32, 0,
  /* 863 */ 'f', 'd', 'i', 'v', 'd', 32, 0,
  /* 870 */ 'f', 'm', 'o', 'v', 'd', 32, 0,
  /* 877 */ 'f', 'p', 'm', 'e', 'r', 'g', 'e', 32, 0,
  /* 886 */ 'b', 's', 'h', 'u', 'f', 'f', 'l', 'e', 32, 0,
  /* 896 */ 'f', 'o', 'n', 'e', 32, 0,
  /* 902 */ 'r', 'e', 's', 't', 'o', 'r', 'e', 32, 0,
  /* 911 */ 's', 'a', 'v', 'e', 32, 0,
  /* 917 */ 's', 't', 'h', 32, 0,
  /* 922 */ 's', 'e', 't', 'h', 'i', 32, 0,
  /* 929 */ 'u', 'm', 'u', 'l', 'x', 'h', 'i', 32, 0,
  /* 938 */ 'x', 'm', 'u', 'l', 'x', 'h', 'i', 32, 0,
  /* 947 */ 'f', 'd', 't', 'o', 'i', 32, 0,
  /* 954 */ 'f', 'q', 't', 'o', 'i', 32, 0,
  /* 961 */ 'f', 's', 't', 'o', 'i', 32, 0,
  /* 968 */ 'b', 'm', 'a', 's', 'k', 32, 0,
  /* 975 */ 'e', 'd', 'g', 'e', '3', '2', 'l', 32, 0,
  /* 984 */ 'e', 'd', 'g', 'e', '1', '6', 'l', 32, 0,
  /* 993 */ 'e', 'd', 'g', 'e', '8', 'l', 32, 0,
  /* 1001 */ 'f', 'm', 'u', 'l', '8', 'x', '1', '6', 'a', 'l', 32, 0,
  /* 1013 */ 'c', 'a', 'l', 'l', 32, 0,
  /* 1019 */ 's', 'l', 'l', 32, 0,
  /* 1024 */ 'j', 'm', 'p', 'l', 32, 0,
  /* 1030 */ 'a', 'l', 'i', 'g', 'n', 'a', 'd', 'd', 'r', 'l', 32, 0,
  /* 1042 */ 's', 'r', 'l', 32, 0,
  /* 1047 */ 's', 'm', 'u', 'l', 32, 0,
  /* 1053 */ 'u', 'm', 'u', 'l', 32, 0,
  /* 1059 */ 'e', 'd', 'g', 'e', '3', '2', 'n', 32, 0,
  /* 1068 */ 'e', 'd', 'g', 'e', '1', '6', 'n', 32, 0,
  /* 1077 */ 'e', 'd', 'g', 'e', '8', 'n', 32, 0,
  /* 1085 */ 'a', 'n', 'd', 'n', 32, 0,
  /* 1091 */ 'e', 'd', 'g', 'e', '3', '2', 'l', 'n', 32, 0,
  /* 1101 */ 'e', 'd', 'g', 'e', '1', '6', 'l', 'n', 32, 0,
  /* 1111 */ 'e', 'd', 'g', 'e', '8', 'l', 'n', 32, 0,
  /* 1120 */ 'b', 'r', 'g', 'e', 'z', ',', 'a', ',', 'p', 'n', 32, 0,
  /* 1132 */ 'b', 'r', 'l', 'e', 'z', ',', 'a', ',', 'p', 'n', 32, 0,
  /* 1144 */ 'b', 'r', 'g', 'z', ',', 'a', ',', 'p', 'n', 32, 0,
  /* 1155 */ 'b', 'r', 'l', 'z', ',', 'a', ',', 'p', 'n', 32, 0,
  /* 1166 */ 'b', 'r', 'n', 'z', ',', 'a', ',', 'p', 'n', 32, 0,
  /* 1177 */ 'b', 'r', 'z', ',', 'a', ',', 'p', 'n', 32, 0,
  /* 1187 */ 'b', 'r', 'g', 'e', 'z', ',', 'p', 'n', 32, 0,
  /* 1197 */ 'b', 'r', 'l', 'e', 'z', ',', 'p', 'n', 32, 0,
  /* 1207 */ 'b', 'r', 'g', 'z', ',', 'p', 'n', 32, 0,
  /* 1216 */ 'b', 'r', 'l', 'z', ',', 'p', 'n', 32, 0,
  /* 1225 */ 'b', 'r', 'n', 'z', ',', 'p', 'n', 32, 0,
  /* 1234 */ 'b', 'r', 'z', ',', 'p', 'n', 32, 0,
  /* 1242 */ 'o', 'r', 'n', 32, 0,
  /* 1247 */ 'p', 'd', 'i', 's', 't', 'n', 32, 0,
  /* 1255 */ 'f', 'z', 'e', 'r', 'o', 32, 0,
  /* 1262 */ 'c', 'm', 'p', 32, 0,
  /* 1267 */ 'u', 'n', 'i', 'm', 'p', 32, 0,
  /* 1274 */ 'j', 'm', 'p', 32, 0,
  /* 1279 */ 'f', 's', 'u', 'b', 'q', 32, 0,
  /* 1286 */ 'f', 'a', 'd', 'd', 'q', 32, 0,
  /* 1293 */ 'f', 'c', 'm', 'p', 'e', 'q', 32, 0,
  /* 1301 */ 'f', 'n', 'e', 'g', 'q', 32, 0,
  /* 1308 */ 'f', 'd', 'm', 'u', 'l', 'q', 32, 0,
  /* 1316 */ 'f', 'm', 'u', 'l', 'q', 32, 0,
  /* 1323 */ 'f', 'd', 't', 'o', 'q', 32, 0,
  /* 1330 */ 'f', 'i', 't', 'o', 'q', 32, 0,
  /* 1337 */ 'f', 's', 't', 'o', 'q', 32, 0,
  /* 1344 */ 'f', 'x', 't', 'o', 'q', 32, 0,
  /* 1351 */ 'f', 'c', 'm', 'p', 'q', 32, 0,
  /* 1358 */ 'f', 'a', 'b', 's', 'q', 32, 0,
  /* 1365 */ 'f', 's', 'q', 'r', 't', 'q', 32, 0,
  /* 1373 */ 's', 't', 'q', 32, 0,
  /* 1378 */ 'f', 'd', 'i', 'v', 'q', 32, 0,
  /* 1385 */ 'f', 'm', 'o', 'v', 'q', 32, 0,
  /* 1392 */ 'm', 'e', 'm', 'b', 'a', 'r', 32, 0,
  /* 1400 */ 'a', 'l', 'i', 'g', 'n', 'a', 'd', 'd', 'r', 32, 0,
  /* 1411 */ 'f', 'o', 'r', 32, 0,
  /* 1416 */ 'f', 'n', 'o', 'r', 32, 0,
  /* 1422 */ 'f', 'x', 'n', 'o', 'r', 32, 0,
  /* 1429 */ 'f', 'x', 'o', 'r', 32, 0,
  /* 1435 */ 'w', 'r', 32, 0,
  /* 1439 */ 'f', 's', 'r', 'c', '1', 's', 32, 0,
  /* 1447 */ 'f', 'a', 'n', 'd', 'n', 'o', 't', '1', 's', 32, 0,
  /* 1458 */ 'f', 'n', 'o', 't', '1', 's', 32, 0,
  /* 1466 */ 'f', 'o', 'r', 'n', 'o', 't', '1', 's', 32, 0,
  /* 1476 */ 'f', 'p', 'a', 'd', 'd', '3', '2', 's', 32, 0,
  /* 1486 */ 'f', 's', 'r', 'c', '2', 's', 32, 0,
  /* 1494 */ 'f', 'a', 'n', 'd', 'n', 'o', 't', '2', 's', 32, 0,
  /* 1505 */ 'f', 'n', 'o', 't', '2', 's', 32, 0,
  /* 1513 */ 'f', 'o', 'r', 'n', 'o', 't', '2', 's', 32, 0,
  /* 1523 */ 'f', 'p', 'a', 'd', 'd', '1', '6', 's', 32, 0,
  /* 1533 */ 'f', 's', 'u', 'b', 's', 32, 0,
  /* 1540 */ 'f', 'h', 's', 'u', 'b', 's', 32, 0,
  /* 1548 */ 'f', 'a', 'd', 'd', 's', 32, 0,
  /* 1555 */ 'f', 'h', 'a', 'd', 'd', 's', 32, 0,
  /* 1563 */ 'f', 'n', 'h', 'a', 'd', 'd', 's', 32, 0,
  /* 1572 */ 'f', 'n', 'a', 'd', 'd', 's', 32, 0,
  /* 1580 */ 'f', 'a', 'n', 'd', 's', 32, 0,
  /* 1587 */ 'f', 'n', 'a', 'n', 'd', 's', 32, 0,
  /* 1595 */ 'f', 'o', 'n', 'e', 's', 32, 0,
  /* 1602 */ 'f', 'c', 'm', 'p', 'e', 's', 32, 0,
  /* 1610 */ 'f', 'n', 'e', 'g', 's', 32, 0,
  /* 1617 */ 'f', 'm', 'u', 'l', 's', 32, 0,
  /* 1624 */ 'f', 'z', 'e', 'r', 'o', 's', 32, 0,
  /* 1632 */ 'f', 'd', 't', 'o', 's', 32, 0,
  /* 1639 */ 'f', 'i', 't', 'o', 's', 32, 0,
  /* 1646 */ 'f', 'q', 't', 'o', 's', 32, 0,
  /* 1653 */ 'f', 'x', 't', 'o', 's', 32, 0,
  /* 1660 */ 'f', 'c', 'm', 'p', 's', 32, 0,
  /* 1667 */ 'f', 'l', 'c', 'm', 'p', 's', 32, 0,
  /* 1675 */ 'f', 'o', 'r', 's', 32, 0,
  /* 1681 */ 'f', 'n', 'o', 'r', 's', 32, 0,
  /* 1688 */ 'f', 'x', 'n', 'o', 'r', 's', 32, 0,
  /* 1696 */ 'f', 'x', 'o', 'r', 's', 32, 0,
  /* 1703 */ 'f', 'a', 'b', 's', 's', 32, 0,
  /* 1710 */ 'f', 's', 'q', 'r', 't', 's', 32, 0,
  /* 1718 */ 'f', 'd', 'i', 'v', 's', 32, 0,
  /* 1725 */ 'f', 'm', 'o', 'v', 's', 32, 0,
  /* 1732 */ 'l', 'z', 'c', 'n', 't', 32, 0,
  /* 1739 */ 'p', 'd', 'i', 's', 't', 32, 0,
  /* 1746 */ 'r', 'e', 't', 't', 32, 0,
  /* 1752 */ 'f', 'm', 'u', 'l', '8', 'x', '1', '6', 'a', 'u', 32, 0,
  /* 1764 */ 's', 'd', 'i', 'v', 32, 0,
  /* 1770 */ 'u', 'd', 'i', 'v', 32, 0,
  /* 1776 */ 't', 's', 'u', 'b', 'c', 'c', 't', 'v', 32, 0,
  /* 1786 */ 't', 'a', 'd', 'd', 'c', 'c', 't', 'v', 32, 0,
  /* 1796 */ 'm', 'o', 'v', 's', 't', 'o', 's', 'w', 32, 0,
  /* 1806 */ 'm', 'o', 'v', 's', 't', 'o', 'u', 'w', 32, 0,
  /* 1816 */ 's', 'r', 'a', 'x', 32, 0,
  /* 1822 */ 's', 'u', 'b', 'x', 32, 0,
  /* 1828 */ 'a', 'd', 'd', 'x', 32, 0,
  /* 1834 */ 'f', 'p', 'a', 'c', 'k', 'f', 'i', 'x', 32, 0,
  /* 1844 */ 's', 'l', 'l', 'x', 32, 0,
  /* 1850 */ 's', 'r', 'l', 'x', 32, 0,
  /* 1856 */ 'x', 'm', 'u', 'l', 'x', 32, 0,
  /* 1863 */ 'f', 'd', 't', 'o', 'x', 32, 0,
  /* 1870 */ 'm', 'o', 'v', 'd', 't', 'o', 'x', 32, 0,
  /* 1879 */ 'f', 'q', 't', 'o', 'x', 32, 0,
  /* 1886 */ 'f', 's', 't', 'o', 'x', 32, 0,
  /* 1893 */ 's', 't', 'x', 32, 0,
  /* 1898 */ 's', 'd', 'i', 'v', 'x', 32, 0,
  /* 1905 */ 'u', 'd', 'i', 'v', 'x', 32, 0,
  /* 1912 */ 'f', 'm', 'o', 'v', 'r', 'd', 'z', 32, 0,
  /* 1921 */ 'f', 'm', 'o', 'v', 'r', 'd', 'g', 'e', 'z', 32, 0,
  /* 1932 */ 'f', 'm', 'o', 'v', 'r', 'q', 'g', 'e', 'z', 32, 0,
  /* 1943 */ 'b', 'r', 'g', 'e', 'z', 32, 0,
  /* 1950 */ 'm', 'o', 'v', 'r', 'g', 'e', 'z', 32, 0,
  /* 1959 */ 'f', 'm', 'o', 'v', 'r', 's', 'g', 'e', 'z', 32, 0,
  /* 1970 */ 'f', 'm', 'o', 'v', 'r', 'd', 'l', 'e', 'z', 32, 0,
  /* 1981 */ 'f', 'm', 'o', 'v', 'r', 'q', 'l', 'e', 'z', 32, 0,
  /* 1992 */ 'b', 'r', 'l', 'e', 'z', 32, 0,
  /* 1999 */ 'm', 'o', 'v', 'r', 'l', 'e', 'z', 32, 0,
  /* 2008 */ 'f', 'm', 'o', 'v', 'r', 's', 'l', 'e', 'z', 32, 0,
  /* 2019 */ 'f', 'm', 'o', 'v', 'r', 'd', 'g', 'z', 32, 0,
  /* 2029 */ 'f', 'm', 'o', 'v', 'r', 'q', 'g', 'z', 32, 0,
  /* 2039 */ 'b', 'r', 'g', 'z', 32, 0,
  /* 2045 */ 'm', 'o', 'v', 'r', 'g', 'z', 32, 0,
  /* 2053 */ 'f', 'm', 'o', 'v', 'r', 's', 'g', 'z', 32, 0,
  /* 2063 */ 'f', 'm', 'o', 'v', 'r', 'd', 'l', 'z', 32, 0,
  /* 2073 */ 'f', 'm', 'o', 'v', 'r', 'q', 'l', 'z', 32, 0,
  /* 2083 */ 'b', 'r', 'l', 'z', 32, 0,
  /* 2089 */ 'm', 'o', 'v', 'r', 'l', 'z', 32, 0,
  /* 2097 */ 'f', 'm', 'o', 'v', 'r', 's', 'l', 'z', 32, 0,
  /* 2107 */ 'f', 'm', 'o', 'v', 'r', 'd', 'n', 'z', 32, 0,
  /* 2117 */ 'f', 'm', 'o', 'v', 'r', 'q', 'n', 'z', 32, 0,
  /* 2127 */ 'b', 'r', 'n', 'z', 32, 0,
  /* 2133 */ 'm', 'o', 'v', 'r', 'n', 'z', 32, 0,
  /* 2141 */ 'f', 'm', 'o', 'v', 'r', 's', 'n', 'z', 32, 0,
  /* 2151 */ 'f', 'm', 'o', 'v', 'r', 'q', 'z', 32, 0,
  /* 2160 */ 'b', 'r', 'z', 32, 0,
  /* 2165 */ 'm', 'o', 'v', 'r', 'z', 32, 0,
  /* 2172 */ 'f', 'm', 'o', 'v', 'r', 's', 'z', 32, 0,
  /* 2181 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'D', 'F', 'P', '_', 'F', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2209 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'Q', 'F', 'P', '_', 'F', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2237 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'F', 'P', '_', 'F', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2264 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'I', 'n', 't', '_', 'F', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2292 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'D', 'F', 'P', '_', 'I', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2320 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'Q', 'F', 'P', '_', 'I', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2348 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'F', 'P', '_', 'I', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2375 */ ';', 32, 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'I', 'n', 't', '_', 'I', 'C', 'C', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2403 */ 'j', 'm', 'p', 32, '%', 'i', '7', '+', 0,
  /* 2412 */ 'j', 'm', 'p', 32, '%', 'o', '7', '+', 0,
  /* 2421 */ 't', 'a', 32, '3', 0,
  /* 2426 */ 't', 'a', 32, '5', 0,
  /* 2431 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 2444 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 2451 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 2461 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 2476 */ 'l', 'd', 's', 'b', 32, '[', 0,
  /* 2483 */ 'l', 'd', 'u', 'b', 32, '[', 0,
  /* 2490 */ 'l', 'd', 'd', 32, '[', 0,
  /* 2496 */ 'l', 'd', 32, '[', 0,
  /* 2501 */ 'l', 'd', 's', 'h', 32, '[', 0,
  /* 2508 */ 'l', 'd', 'u', 'h', 32, '[', 0,
  /* 2515 */ 's', 'w', 'a', 'p', 32, '[', 0,
  /* 2522 */ 'l', 'd', 'q', 32, '[', 0,
  /* 2528 */ 'c', 'a', 's', 32, '[', 0,
  /* 2534 */ 'l', 'd', 's', 'w', 32, '[', 0,
  /* 2541 */ 'l', 'd', 'x', 32, '[', 0,
  /* 2547 */ 'c', 'a', 's', 'x', 32, '[', 0,
  /* 2554 */ 'f', 'b', 0,
  /* 2557 */ 'f', 'm', 'o', 'v', 'd', 0,
  /* 2563 */ 's', 'i', 'a', 'm', 0,
  /* 2568 */ 's', 'h', 'u', 't', 'd', 'o', 'w', 'n', 0,
  /* 2577 */ 'n', 'o', 'p', 0,
  /* 2581 */ 'f', 'm', 'o', 'v', 'q', 0,
  /* 2587 */ 's', 't', 'b', 'a', 'r', 0,
  /* 2593 */ 'f', 'm', 'o', 'v', 's', 0,
  /* 2599 */ 't', 0,
  /* 2601 */ 'm', 'o', 'v', 0,
  /* 2605 */ 'f', 'l', 'u', 's', 'h', 'w', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint32_t Bits = OpInfo[MCInst_getOpcode(MI)];
#ifndef CAPSTONE_DIET
  // assert(Bits != 0 && "Cannot print this instruction.");
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif


  // Fragment 0 encoded into 4 bits for 12 unique commands.
  // printf("Frag-0: %u\n", (Bits >> 12) & 15);
  switch ((Bits >> 12) & 15) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, FLUSHW, NOP, SELECT_C...
    return;
    break;
  case 1:
    // ADDCCri, ADDCCrr, ADDCri, ADDCrr, ADDEri, ADDErr, ADDXC, ADDXCCC, ADDX...
    printOperand(MI, 1, O); 
    break;
  case 2:
    // ADJCALLSTACKDOWN, ADJCALLSTACKUP, BA, BPGEZapn, BPGEZapt, BPGEZnapn, B...
    printOperand(MI, 0, O); 
    break;
  case 3:
    // BCOND, BCONDA, BPFCC, BPFCCA, BPFCCANT, BPFCCNT, BPICC, BPICCA, BPICCA...
    printCCOperand(MI, 1, O); 
    break;
  case 4:
    // BINDri, BINDrr, CALLri, CALLrr, RETTri, RETTrr
    printMemOperand(MI, 0, O, NULL); 
    return;
    break;
  case 5:
    // FMOVD_FCC, FMOVD_ICC, FMOVD_XCC, FMOVQ_FCC, FMOVQ_ICC, FMOVQ_XCC, FMOV...
    printCCOperand(MI, 3, O); 
    break;
  case 6:
    // GETPCX
    printGetPCX(MI, 0, O); 
    return;
    break;
  case 7:
    // JMPLri, JMPLrr, LDDFri, LDDFrr, LDFri, LDFrr, LDQFri, LDQFrr, LDSBri, ...
    printMemOperand(MI, 1, O, NULL); 
    break;
  case 8:
    // LEAX_ADDri, LEA_ADDri
    printMemOperand(MI, 1, O, "arith"); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 9:
    // STBri, STBrr, STDFri, STDFrr, STFri, STFrr, STHri, STHrr, STQFri, STQF...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", ["); 
    printMemOperand(MI, 0, O, NULL); 
    SStream_concat0(O, "]"); 
    return;
    break;
  case 10:
    // TICCri, TICCrr, TXCCri, TXCCrr
    printCCOperand(MI, 2, O); 
    break;
  case 11:
    // V9FMOVD_FCC, V9FMOVQ_FCC, V9FMOVS_FCC, V9MOVFCCri, V9MOVFCCrr
    printCCOperand(MI, 4, O); 
    SStream_concat0(O, " "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 4 bits for 16 unique commands.
  // printf("Frag-1: %u\n", (Bits >> 16) & 15);
  switch ((Bits >> 16) & 15) {
  default:   // unreachable.
  case 0:
    // ADDCCri, ADDCCrr, ADDCri, ADDCrr, ADDEri, ADDErr, ADDXC, ADDXCCC, ADDX...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // ADJCALLSTACKDOWN, ADJCALLSTACKUP, BA, CALL, CMASK16, CMASK32, CMASK8, ...
    return;
    break;
  case 2:
    // BCOND, BPFCC, FBCOND
    SStream_concat0(O, " "); 
    break;
  case 3:
    // BCONDA, BPFCCA, FBCONDA
    SStream_concat0(O, ",a ");
	Sparc_add_hint(MI, SPARC_HINT_A);
    break;
  case 4:
    // BPFCCANT
    SStream_concat0(O, ",a,pn ");
	Sparc_add_hint(MI, SPARC_HINT_A + SPARC_HINT_PN);
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 5:
    // BPFCCNT
    SStream_concat0(O, ",pn ");
	Sparc_add_hint(MI, SPARC_HINT_PN);
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // BPICC, FMOVD_ICC, FMOVQ_ICC, FMOVS_ICC, MOVICCri, MOVICCrr, TICCri, TI...
    SStream_concat0(O, " %icc, ");
	Sparc_add_reg(MI, SPARC_REG_ICC);
    break;
  case 7:
    // BPICCA
    SStream_concat0(O, ",a %icc, ");
	Sparc_add_hint(MI, SPARC_HINT_A);
	Sparc_add_reg(MI, SPARC_REG_ICC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 8:
    // BPICCANT
    SStream_concat0(O, ",a,pn %icc, ");
	Sparc_add_hint(MI, SPARC_HINT_A + SPARC_HINT_PN);
	Sparc_add_reg(MI, SPARC_REG_ICC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 9:
    // BPICCNT
    SStream_concat0(O, ",pn %icc, ");
	Sparc_add_hint(MI, SPARC_HINT_PN);
	Sparc_add_reg(MI, SPARC_REG_ICC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 10:
    // BPXCC, FMOVD_XCC, FMOVQ_XCC, FMOVS_XCC, MOVXCCri, MOVXCCrr, TXCCri, TX...
    SStream_concat0(O, " %xcc, ");
	Sparc_add_reg(MI, SPARC_REG_XCC);
    break;
  case 11:
    // BPXCCA
    SStream_concat0(O, ",a %xcc, ");
	Sparc_add_hint(MI, SPARC_HINT_A);
	Sparc_add_reg(MI, SPARC_REG_XCC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 12:
    // BPXCCANT
    SStream_concat0(O, ",a,pn %xcc, ");
	Sparc_add_hint(MI, SPARC_HINT_A + SPARC_HINT_PN);
	Sparc_add_reg(MI, SPARC_REG_XCC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 13:
    // BPXCCNT
    SStream_concat0(O, ",pn %xcc, ");
	Sparc_add_hint(MI, SPARC_HINT_PN);
	Sparc_add_reg(MI, SPARC_REG_XCC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 14:
    // CASXrr, CASrr, LDDFri, LDDFrr, LDFri, LDFrr, LDQFri, LDQFrr, LDSBri, L...
    SStream_concat0(O, "], "); 
    break;
  case 15:
    // FMOVD_FCC, FMOVQ_FCC, FMOVS_FCC, MOVFCCri, MOVFCCrr
    SStream_concat0(O, " %fcc0, ");
	Sparc_add_reg(MI, SPARC_REG_FCC0);
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 2 encoded into 2 bits for 3 unique commands.
  // printf("Frag-2: %u\n", (Bits >> 20) & 3);
  switch ((Bits >> 20) & 3) {
  default:   // unreachable.
  case 0:
    // ADDCCri, ADDCCrr, ADDCri, ADDCrr, ADDEri, ADDErr, ADDXC, ADDXCCC, ADDX...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    break;
  case 1:
    // BCOND, BCONDA, BPICC, BPXCC, FABSD, FABSQ, FABSS, FBCOND, FBCONDA, FDT...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // BPGEZapn, BPGEZapt, BPGEZnapn, BPGEZnapt, BPGZapn, BPGZapt, BPGZnapn, ...
    printOperand(MI, 1, O); 
    break;
  }


  // Fragment 3 encoded into 2 bits for 4 unique commands.
  // printf("Frag-3: %u\n", (Bits >> 22) & 3);
  switch ((Bits >> 22) & 3) {
  default:   // unreachable.
  case 0:
    // ADDCCri, ADDCCrr, ADDCri, ADDCrr, ADDEri, ADDErr, ADDXC, ADDXCCC, ADDX...
    return;
    break;
  case 1:
    // FLCMPD, FLCMPS, FMOVD_ICC, FMOVD_XCC, FMOVQ_ICC, FMOVQ_XCC, FMOVS_ICC,...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // TICCri, TICCrr, TXCCri, TXCCrr
    SStream_concat0(O, " + "); 	// qq
    printOperand(MI, 1, O); 
    return;
    break;
  case 3:
    // WRYri, WRYrr
    SStream_concat0(O, ", %y");
	Sparc_add_reg(MI, SPARC_REG_Y);
    return;
    break;
  }


  // Fragment 4 encoded into 2 bits for 3 unique commands.
  // printf("Frag-4: %u\n", (Bits >> 24) & 3);
  switch ((Bits >> 24) & 3) {
  default:   // unreachable.
  case 0:
    // FLCMPD, FLCMPS, V9FCMPD, V9FCMPED, V9FCMPEQ, V9FCMPES, V9FCMPQ, V9FCMP...
    printOperand(MI, 2, O); 
    return;
    break;
  case 1:
    // FMOVD_ICC, FMOVD_XCC, FMOVQ_ICC, FMOVQ_XCC, FMOVS_ICC, FMOVS_XCC, MOVI...
    printOperand(MI, 0, O); 
    return;
    break;
  case 2:
    // TLS_ADDXrr, TLS_ADDrr, TLS_LDXrr, TLS_LDrr
    printOperand(MI, 3, O); 
    return;
    break;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 119 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'f', '1', '0', 0,
  /* 4 */ 'f', '2', '0', 0,
  /* 8 */ 'f', '3', '0', 0,
  /* 12 */ 'f', '4', '0', 0,
  /* 16 */ 'f', '5', '0', 0,
  /* 20 */ 'f', '6', '0', 0,
  /* 24 */ 'f', 'c', 'c', '0', 0,
  /* 29 */ 'f', '0', 0,
  /* 32 */ 'g', '0', 0,
  /* 35 */ 'i', '0', 0,
  /* 38 */ 'l', '0', 0,
  /* 41 */ 'o', '0', 0,
  /* 44 */ 'f', '1', '1', 0,
  /* 48 */ 'f', '2', '1', 0,
  /* 52 */ 'f', '3', '1', 0,
  /* 56 */ 'f', 'c', 'c', '1', 0,
  /* 61 */ 'f', '1', 0,
  /* 64 */ 'g', '1', 0,
  /* 67 */ 'i', '1', 0,
  /* 70 */ 'l', '1', 0,
  /* 73 */ 'o', '1', 0,
  /* 76 */ 'f', '1', '2', 0,
  /* 80 */ 'f', '2', '2', 0,
  /* 84 */ 'f', '3', '2', 0,
  /* 88 */ 'f', '4', '2', 0,
  /* 92 */ 'f', '5', '2', 0,
  /* 96 */ 'f', '6', '2', 0,
  /* 100 */ 'f', 'c', 'c', '2', 0,
  /* 105 */ 'f', '2', 0,
  /* 108 */ 'g', '2', 0,
  /* 111 */ 'i', '2', 0,
  /* 114 */ 'l', '2', 0,
  /* 117 */ 'o', '2', 0,
  /* 120 */ 'f', '1', '3', 0,
  /* 124 */ 'f', '2', '3', 0,
  /* 128 */ 'f', 'c', 'c', '3', 0,
  /* 133 */ 'f', '3', 0,
  /* 136 */ 'g', '3', 0,
  /* 139 */ 'i', '3', 0,
  /* 142 */ 'l', '3', 0,
  /* 145 */ 'o', '3', 0,
  /* 148 */ 'f', '1', '4', 0,
  /* 152 */ 'f', '2', '4', 0,
  /* 156 */ 'f', '3', '4', 0,
  /* 160 */ 'f', '4', '4', 0,
  /* 164 */ 'f', '5', '4', 0,
  /* 168 */ 'f', '4', 0,
  /* 171 */ 'g', '4', 0,
  /* 174 */ 'i', '4', 0,
  /* 177 */ 'l', '4', 0,
  /* 180 */ 'o', '4', 0,
  /* 183 */ 'f', '1', '5', 0,
  /* 187 */ 'f', '2', '5', 0,
  /* 191 */ 'f', '5', 0,
  /* 194 */ 'g', '5', 0,
  /* 197 */ 'i', '5', 0,
  /* 200 */ 'l', '5', 0,
  /* 203 */ 'o', '5', 0,
  /* 206 */ 'f', '1', '6', 0,
  /* 210 */ 'f', '2', '6', 0,
  /* 214 */ 'f', '3', '6', 0,
  /* 218 */ 'f', '4', '6', 0,
  /* 222 */ 'f', '5', '6', 0,
  /* 226 */ 'f', '6', 0,
  /* 229 */ 'g', '6', 0,
  /* 232 */ 'l', '6', 0,
  /* 235 */ 'f', '1', '7', 0,
  /* 239 */ 'f', '2', '7', 0,
  /* 243 */ 'f', '7', 0,
  /* 246 */ 'g', '7', 0,
  /* 249 */ 'i', '7', 0,
  /* 252 */ 'l', '7', 0,
  /* 255 */ 'o', '7', 0,
  /* 258 */ 'f', '1', '8', 0,
  /* 262 */ 'f', '2', '8', 0,
  /* 266 */ 'f', '3', '8', 0,
  /* 270 */ 'f', '4', '8', 0,
  /* 274 */ 'f', '5', '8', 0,
  /* 278 */ 'f', '8', 0,
  /* 281 */ 'f', '1', '9', 0,
  /* 285 */ 'f', '2', '9', 0,
  /* 289 */ 'f', '9', 0,
  /* 292 */ 'i', 'c', 'c', 0,
  /* 296 */ 'f', 'p', 0,
  /* 299 */ 's', 'p', 0,
  /* 302 */ 'y', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    292, 302, 29, 105, 168, 226, 278, 0, 76, 148, 206, 258, 4, 80, 
    152, 210, 262, 8, 84, 156, 214, 266, 12, 88, 160, 218, 270, 16, 
    92, 164, 222, 274, 20, 96, 29, 61, 105, 133, 168, 191, 226, 243, 
    278, 289, 0, 44, 76, 120, 148, 183, 206, 235, 258, 281, 4, 48, 
    80, 124, 152, 187, 210, 239, 262, 285, 8, 52, 24, 56, 100, 128, 
    32, 64, 108, 136, 171, 194, 229, 246, 35, 67, 111, 139, 174, 197, 
    296, 249, 38, 70, 114, 142, 177, 200, 232, 252, 41, 73, 117, 145, 
    180, 203, 299, 255, 29, 168, 278, 76, 206, 4, 152, 262, 84, 214, 
    12, 160, 270, 92, 222, 20, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case SP_BCOND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8) {
      // (BCOND brtarget:$imm, 8)
      AsmString = "ba $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BCOND brtarget:$imm, 0)
      AsmString = "bn $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9) {
      // (BCOND brtarget:$imm, 9)
      AsmString = "bne $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (BCOND brtarget:$imm, 1)
      AsmString = "be $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10) {
      // (BCOND brtarget:$imm, 10)
      AsmString = "bg $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2) {
      // (BCOND brtarget:$imm, 2)
      AsmString = "ble $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11) {
      // (BCOND brtarget:$imm, 11)
      AsmString = "bge $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3) {
      // (BCOND brtarget:$imm, 3)
      AsmString = "bl $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12) {
      // (BCOND brtarget:$imm, 12)
      AsmString = "bgu $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (BCOND brtarget:$imm, 4)
      AsmString = "bleu $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13) {
      // (BCOND brtarget:$imm, 13)
      AsmString = "bcc $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (BCOND brtarget:$imm, 5)
      AsmString = "bcs $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14) {
      // (BCOND brtarget:$imm, 14)
      AsmString = "bpos $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6) {
      // (BCOND brtarget:$imm, 6)
      AsmString = "bneg $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15) {
      // (BCOND brtarget:$imm, 15)
      AsmString = "bvc $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7) {
      // (BCOND brtarget:$imm, 7)
      AsmString = "bvs $\x01";
      break;
    }
    return NULL;
  case SP_BCONDA:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8) {
      // (BCONDA brtarget:$imm, 8)
      AsmString = "ba,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BCONDA brtarget:$imm, 0)
      AsmString = "bn,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9) {
      // (BCONDA brtarget:$imm, 9)
      AsmString = "bne,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (BCONDA brtarget:$imm, 1)
      AsmString = "be,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10) {
      // (BCONDA brtarget:$imm, 10)
      AsmString = "bg,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2) {
      // (BCONDA brtarget:$imm, 2)
      AsmString = "ble,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11) {
      // (BCONDA brtarget:$imm, 11)
      AsmString = "bge,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3) {
      // (BCONDA brtarget:$imm, 3)
      AsmString = "bl,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12) {
      // (BCONDA brtarget:$imm, 12)
      AsmString = "bgu,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (BCONDA brtarget:$imm, 4)
      AsmString = "bleu,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13) {
      // (BCONDA brtarget:$imm, 13)
      AsmString = "bcc,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (BCONDA brtarget:$imm, 5)
      AsmString = "bcs,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14) {
      // (BCONDA brtarget:$imm, 14)
      AsmString = "bpos,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6) {
      // (BCONDA brtarget:$imm, 6)
      AsmString = "bneg,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15) {
      // (BCONDA brtarget:$imm, 15)
      AsmString = "bvc,a $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7) {
      // (BCONDA brtarget:$imm, 7)
      AsmString = "bvs,a $\x01";
      break;
    }
    return NULL;
  case SP_BPFCCANT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 0, FCCRegs:$cc)
      AsmString = "fba,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 8, FCCRegs:$cc)
      AsmString = "fbn,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 7, FCCRegs:$cc)
      AsmString = "fbu,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 6, FCCRegs:$cc)
      AsmString = "fbg,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 5, FCCRegs:$cc)
      AsmString = "fbug,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 4, FCCRegs:$cc)
      AsmString = "fbl,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 3, FCCRegs:$cc)
      AsmString = "fbul,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 2, FCCRegs:$cc)
      AsmString = "fblg,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 1, FCCRegs:$cc)
      AsmString = "fbne,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 9, FCCRegs:$cc)
      AsmString = "fbe,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 10, FCCRegs:$cc)
      AsmString = "fbue,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 11, FCCRegs:$cc)
      AsmString = "fbge,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 12, FCCRegs:$cc)
      AsmString = "fbuge,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 13, FCCRegs:$cc)
      AsmString = "fble,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 14, FCCRegs:$cc)
      AsmString = "fbule,a,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCANT brtarget:$imm, 15, FCCRegs:$cc)
      AsmString = "fbo,a,pn $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_BPFCCNT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 0, FCCRegs:$cc)
      AsmString = "fba,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 8, FCCRegs:$cc)
      AsmString = "fbn,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 7, FCCRegs:$cc)
      AsmString = "fbu,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 6, FCCRegs:$cc)
      AsmString = "fbg,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 5, FCCRegs:$cc)
      AsmString = "fbug,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 4, FCCRegs:$cc)
      AsmString = "fbl,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 3, FCCRegs:$cc)
      AsmString = "fbul,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 2, FCCRegs:$cc)
      AsmString = "fblg,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 1, FCCRegs:$cc)
      AsmString = "fbne,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 9, FCCRegs:$cc)
      AsmString = "fbe,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 10, FCCRegs:$cc)
      AsmString = "fbue,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 11, FCCRegs:$cc)
      AsmString = "fbge,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 12, FCCRegs:$cc)
      AsmString = "fbuge,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 13, FCCRegs:$cc)
      AsmString = "fble,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 14, FCCRegs:$cc)
      AsmString = "fbule,pn $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 2)) {
      // (BPFCCNT brtarget:$imm, 15, FCCRegs:$cc)
      AsmString = "fbo,pn $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_BPICCANT:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8) {
      // (BPICCANT brtarget:$imm, 8)
      AsmString = "ba,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BPICCANT brtarget:$imm, 0)
      AsmString = "bn,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9) {
      // (BPICCANT brtarget:$imm, 9)
      AsmString = "bne,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (BPICCANT brtarget:$imm, 1)
      AsmString = "be,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10) {
      // (BPICCANT brtarget:$imm, 10)
      AsmString = "bg,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2) {
      // (BPICCANT brtarget:$imm, 2)
      AsmString = "ble,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11) {
      // (BPICCANT brtarget:$imm, 11)
      AsmString = "bge,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3) {
      // (BPICCANT brtarget:$imm, 3)
      AsmString = "bl,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12) {
      // (BPICCANT brtarget:$imm, 12)
      AsmString = "bgu,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (BPICCANT brtarget:$imm, 4)
      AsmString = "bleu,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13) {
      // (BPICCANT brtarget:$imm, 13)
      AsmString = "bcc,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (BPICCANT brtarget:$imm, 5)
      AsmString = "bcs,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14) {
      // (BPICCANT brtarget:$imm, 14)
      AsmString = "bpos,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6) {
      // (BPICCANT brtarget:$imm, 6)
      AsmString = "bneg,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15) {
      // (BPICCANT brtarget:$imm, 15)
      AsmString = "bvc,a,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7) {
      // (BPICCANT brtarget:$imm, 7)
      AsmString = "bvs,a,pn %icc, $\x01";
      break;
    }
    return NULL;
  case SP_BPICCNT:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8) {
      // (BPICCNT brtarget:$imm, 8)
      AsmString = "ba,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BPICCNT brtarget:$imm, 0)
      AsmString = "bn,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9) {
      // (BPICCNT brtarget:$imm, 9)
      AsmString = "bne,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (BPICCNT brtarget:$imm, 1)
      AsmString = "be,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10) {
      // (BPICCNT brtarget:$imm, 10)
      AsmString = "bg,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2) {
      // (BPICCNT brtarget:$imm, 2)
      AsmString = "ble,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11) {
      // (BPICCNT brtarget:$imm, 11)
      AsmString = "bge,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3) {
      // (BPICCNT brtarget:$imm, 3)
      AsmString = "bl,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12) {
      // (BPICCNT brtarget:$imm, 12)
      AsmString = "bgu,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (BPICCNT brtarget:$imm, 4)
      AsmString = "bleu,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13) {
      // (BPICCNT brtarget:$imm, 13)
      AsmString = "bcc,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (BPICCNT brtarget:$imm, 5)
      AsmString = "bcs,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14) {
      // (BPICCNT brtarget:$imm, 14)
      AsmString = "bpos,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6) {
      // (BPICCNT brtarget:$imm, 6)
      AsmString = "bneg,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15) {
      // (BPICCNT brtarget:$imm, 15)
      AsmString = "bvc,pn %icc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7) {
      // (BPICCNT brtarget:$imm, 7)
      AsmString = "bvs,pn %icc, $\x01";
      break;
    }
    return NULL;
  case SP_BPXCCANT:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8) {
      // (BPXCCANT brtarget:$imm, 8)
      AsmString = "ba,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BPXCCANT brtarget:$imm, 0)
      AsmString = "bn,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9) {
      // (BPXCCANT brtarget:$imm, 9)
      AsmString = "bne,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (BPXCCANT brtarget:$imm, 1)
      AsmString = "be,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10) {
      // (BPXCCANT brtarget:$imm, 10)
      AsmString = "bg,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2) {
      // (BPXCCANT brtarget:$imm, 2)
      AsmString = "ble,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11) {
      // (BPXCCANT brtarget:$imm, 11)
      AsmString = "bge,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3) {
      // (BPXCCANT brtarget:$imm, 3)
      AsmString = "bl,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12) {
      // (BPXCCANT brtarget:$imm, 12)
      AsmString = "bgu,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (BPXCCANT brtarget:$imm, 4)
      AsmString = "bleu,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13) {
      // (BPXCCANT brtarget:$imm, 13)
      AsmString = "bcc,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (BPXCCANT brtarget:$imm, 5)
      AsmString = "bcs,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14) {
      // (BPXCCANT brtarget:$imm, 14)
      AsmString = "bpos,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6) {
      // (BPXCCANT brtarget:$imm, 6)
      AsmString = "bneg,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15) {
      // (BPXCCANT brtarget:$imm, 15)
      AsmString = "bvc,a,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7) {
      // (BPXCCANT brtarget:$imm, 7)
      AsmString = "bvs,a,pn %xcc, $\x01";
      break;
    }
    return NULL;
  case SP_BPXCCNT:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 8) {
      // (BPXCCNT brtarget:$imm, 8)
      AsmString = "ba,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BPXCCNT brtarget:$imm, 0)
      AsmString = "bn,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 9) {
      // (BPXCCNT brtarget:$imm, 9)
      AsmString = "bne,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (BPXCCNT brtarget:$imm, 1)
      AsmString = "be,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 10) {
      // (BPXCCNT brtarget:$imm, 10)
      AsmString = "bg,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 2) {
      // (BPXCCNT brtarget:$imm, 2)
      AsmString = "ble,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 11) {
      // (BPXCCNT brtarget:$imm, 11)
      AsmString = "bge,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 3) {
      // (BPXCCNT brtarget:$imm, 3)
      AsmString = "bl,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 12) {
      // (BPXCCNT brtarget:$imm, 12)
      AsmString = "bgu,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (BPXCCNT brtarget:$imm, 4)
      AsmString = "bleu,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 13) {
      // (BPXCCNT brtarget:$imm, 13)
      AsmString = "bcc,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (BPXCCNT brtarget:$imm, 5)
      AsmString = "bcs,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 14) {
      // (BPXCCNT brtarget:$imm, 14)
      AsmString = "bpos,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 6) {
      // (BPXCCNT brtarget:$imm, 6)
      AsmString = "bneg,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 15) {
      // (BPXCCNT brtarget:$imm, 15)
      AsmString = "bvc,pn %xcc, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 7) {
      // (BPXCCNT brtarget:$imm, 7)
      AsmString = "bvs,pn %xcc, $\x01";
      break;
    }
    return NULL;
  case SP_FMOVD_ICC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 8)
      AsmString = "fmovda %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 0)
      AsmString = "fmovdn %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 9)
      AsmString = "fmovdne %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 1)
      AsmString = "fmovde %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 10)
      AsmString = "fmovdg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 2)
      AsmString = "fmovdle %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 11)
      AsmString = "fmovdge %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 3)
      AsmString = "fmovdl %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 12)
      AsmString = "fmovdgu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 4)
      AsmString = "fmovdleu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 13)
      AsmString = "fmovdcc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 5)
      AsmString = "fmovdcs %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 14)
      AsmString = "fmovdpos %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 6)
      AsmString = "fmovdneg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 15)
      AsmString = "fmovdvc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (FMOVD_ICC DFPRegs:$rd, DFPRegs:$rs2, 7)
      AsmString = "fmovdvs %icc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_FMOVD_XCC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 8)
      AsmString = "fmovda %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 0)
      AsmString = "fmovdn %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 9)
      AsmString = "fmovdne %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 1)
      AsmString = "fmovde %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 10)
      AsmString = "fmovdg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 2)
      AsmString = "fmovdle %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 11)
      AsmString = "fmovdge %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 3)
      AsmString = "fmovdl %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 12)
      AsmString = "fmovdgu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 4)
      AsmString = "fmovdleu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 13)
      AsmString = "fmovdcc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 5)
      AsmString = "fmovdcs %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 14)
      AsmString = "fmovdpos %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 6)
      AsmString = "fmovdneg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 15)
      AsmString = "fmovdvc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (FMOVD_XCC DFPRegs:$rd, DFPRegs:$rs2, 7)
      AsmString = "fmovdvs %xcc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_FMOVQ_ICC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 8)
      AsmString = "fmovqa %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 0)
      AsmString = "fmovqn %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 9)
      AsmString = "fmovqne %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 1)
      AsmString = "fmovqe %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 10)
      AsmString = "fmovqg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 2)
      AsmString = "fmovqle %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 11)
      AsmString = "fmovqge %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 3)
      AsmString = "fmovql %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 12)
      AsmString = "fmovqgu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 4)
      AsmString = "fmovqleu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 13)
      AsmString = "fmovqcc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 5)
      AsmString = "fmovqcs %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 14)
      AsmString = "fmovqpos %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 6)
      AsmString = "fmovqneg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 15)
      AsmString = "fmovqvc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (FMOVQ_ICC QFPRegs:$rd, QFPRegs:$rs2, 7)
      AsmString = "fmovqvs %icc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_FMOVQ_XCC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 8)
      AsmString = "fmovqa %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 0)
      AsmString = "fmovqn %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 9)
      AsmString = "fmovqne %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 1)
      AsmString = "fmovqe %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 10)
      AsmString = "fmovqg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 2)
      AsmString = "fmovqle %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 11)
      AsmString = "fmovqge %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 3)
      AsmString = "fmovql %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 12)
      AsmString = "fmovqgu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 4)
      AsmString = "fmovqleu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 13)
      AsmString = "fmovqcc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 5)
      AsmString = "fmovqcs %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 14)
      AsmString = "fmovqpos %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 6)
      AsmString = "fmovqneg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 15)
      AsmString = "fmovqvc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (FMOVQ_XCC QFPRegs:$rd, QFPRegs:$rs2, 7)
      AsmString = "fmovqvs %xcc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_FMOVS_ICC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 8)
      AsmString = "fmovsa %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 0)
      AsmString = "fmovsn %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 9)
      AsmString = "fmovsne %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 1)
      AsmString = "fmovse %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 10)
      AsmString = "fmovsg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 2)
      AsmString = "fmovsle %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 11)
      AsmString = "fmovsge %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 3)
      AsmString = "fmovsl %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 12)
      AsmString = "fmovsgu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 4)
      AsmString = "fmovsleu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 13)
      AsmString = "fmovscc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 5)
      AsmString = "fmovscs %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 14)
      AsmString = "fmovspos %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 6)
      AsmString = "fmovsneg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 15)
      AsmString = "fmovsvc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (FMOVS_ICC FPRegs:$rd, FPRegs:$rs2, 7)
      AsmString = "fmovsvs %icc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_FMOVS_XCC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 8)
      AsmString = "fmovsa %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 0)
      AsmString = "fmovsn %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 9)
      AsmString = "fmovsne %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 1)
      AsmString = "fmovse %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 10)
      AsmString = "fmovsg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 2)
      AsmString = "fmovsle %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 11)
      AsmString = "fmovsge %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 3)
      AsmString = "fmovsl %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 12)
      AsmString = "fmovsgu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 4)
      AsmString = "fmovsleu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 13)
      AsmString = "fmovscc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 5)
      AsmString = "fmovscs %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 14)
      AsmString = "fmovspos %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 6)
      AsmString = "fmovsneg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 15)
      AsmString = "fmovsvc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (FMOVS_XCC FPRegs:$rd, FPRegs:$rs2, 7)
      AsmString = "fmovsvs %xcc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_MOVICCri:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 8)
      AsmString = "mova %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 0)
      AsmString = "movn %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 9)
      AsmString = "movne %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 1)
      AsmString = "move %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 10)
      AsmString = "movg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 2)
      AsmString = "movle %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 11)
      AsmString = "movge %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 3)
      AsmString = "movl %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 12)
      AsmString = "movgu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 4)
      AsmString = "movleu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 13)
      AsmString = "movcc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 5)
      AsmString = "movcs %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 14)
      AsmString = "movpos %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 6)
      AsmString = "movneg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 15)
      AsmString = "movvc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (MOVICCri IntRegs:$rd, i32imm:$simm11, 7)
      AsmString = "movvs %icc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_MOVICCrr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 8)
      AsmString = "mova %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 0)
      AsmString = "movn %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 9)
      AsmString = "movne %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 1)
      AsmString = "move %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 10)
      AsmString = "movg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 2)
      AsmString = "movle %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 11)
      AsmString = "movge %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 3)
      AsmString = "movl %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 12)
      AsmString = "movgu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 4)
      AsmString = "movleu %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 13)
      AsmString = "movcc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 5)
      AsmString = "movcs %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 14)
      AsmString = "movpos %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 6)
      AsmString = "movneg %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 15)
      AsmString = "movvc %icc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (MOVICCrr IntRegs:$rd, IntRegs:$rs2, 7)
      AsmString = "movvs %icc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_MOVXCCri:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 8)
      AsmString = "mova %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 0)
      AsmString = "movn %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 9)
      AsmString = "movne %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 1)
      AsmString = "move %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 10)
      AsmString = "movg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 2)
      AsmString = "movle %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 11)
      AsmString = "movge %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 3)
      AsmString = "movl %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 12)
      AsmString = "movgu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 4)
      AsmString = "movleu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 13)
      AsmString = "movcc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 5)
      AsmString = "movcs %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 14)
      AsmString = "movpos %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 6)
      AsmString = "movneg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 15)
      AsmString = "movvc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (MOVXCCri IntRegs:$rd, i32imm:$simm11, 7)
      AsmString = "movvs %xcc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_MOVXCCrr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 8)
      AsmString = "mova %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 0)
      AsmString = "movn %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 9)
      AsmString = "movne %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 1)
      AsmString = "move %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 10)
      AsmString = "movg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 2)
      AsmString = "movle %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 11)
      AsmString = "movge %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 3)
      AsmString = "movl %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 12)
      AsmString = "movgu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 4)
      AsmString = "movleu %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 13)
      AsmString = "movcc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 5)
      AsmString = "movcs %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 14)
      AsmString = "movpos %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 6)
      AsmString = "movneg %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 15)
      AsmString = "movvc %xcc, $\x02, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (MOVXCCrr IntRegs:$rd, IntRegs:$rs2, 7)
      AsmString = "movvs %xcc, $\x02, $\x01";
      break;
    }
    return NULL;
  case SP_ORri:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == SP_G0) {
      // (ORri IntRegs:$rd, G0, i32imm:$simm13)
      AsmString = "mov $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_ORrr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2)) {
      // (ORrr IntRegs:$rd, G0, IntRegs:$rs2)
      AsmString = "mov $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_RESTORErr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == SP_G0 &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == SP_G0) {
      // (RESTORErr G0, G0, G0)
      AsmString = "restore";
      break;
    }
    return NULL;
  case SP_RET:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8) {
      // (RET 8)
      AsmString = "ret";
      break;
    }
    return NULL;
  case SP_RETL:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8) {
      // (RETL 8)
      AsmString = "retl";
      break;
    }
    return NULL;
  case SP_TXCCri:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 8)
      AsmString = "ta %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (TXCCri G0, i32imm:$imm, 8)
      AsmString = "ta %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 0)
      AsmString = "tn %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TXCCri G0, i32imm:$imm, 0)
      AsmString = "tn %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 9)
      AsmString = "tne %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (TXCCri G0, i32imm:$imm, 9)
      AsmString = "tne %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 1)
      AsmString = "te %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TXCCri G0, i32imm:$imm, 1)
      AsmString = "te %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 10)
      AsmString = "tg %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (TXCCri G0, i32imm:$imm, 10)
      AsmString = "tg %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 2)
      AsmString = "tle %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (TXCCri G0, i32imm:$imm, 2)
      AsmString = "tle %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 11)
      AsmString = "tge %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (TXCCri G0, i32imm:$imm, 11)
      AsmString = "tge %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 3)
      AsmString = "tl %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (TXCCri G0, i32imm:$imm, 3)
      AsmString = "tl %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 12)
      AsmString = "tgu %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (TXCCri G0, i32imm:$imm, 12)
      AsmString = "tgu %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 4)
      AsmString = "tleu %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (TXCCri G0, i32imm:$imm, 4)
      AsmString = "tleu %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 13)
      AsmString = "tcc %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (TXCCri G0, i32imm:$imm, 13)
      AsmString = "tcc %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 5)
      AsmString = "tcs %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (TXCCri G0, i32imm:$imm, 5)
      AsmString = "tcs %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 14)
      AsmString = "tpos %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (TXCCri G0, i32imm:$imm, 14)
      AsmString = "tpos %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 6)
      AsmString = "tneg %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (TXCCri G0, i32imm:$imm, 6)
      AsmString = "tneg %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 15)
      AsmString = "tvc %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (TXCCri G0, i32imm:$imm, 15)
      AsmString = "tvc %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (TXCCri IntRegs:$rs1, i32imm:$imm, 7)
      AsmString = "tvs %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (TXCCri G0, i32imm:$imm, 7)
      AsmString = "tvs %xcc, $\x02";
      break;
    }
    return NULL;
  case SP_TXCCrr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 8)
      AsmString = "ta %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 8) {
      // (TXCCrr G0, IntRegs:$rs2, 8)
      AsmString = "ta %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 0)
      AsmString = "tn %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TXCCrr G0, IntRegs:$rs2, 0)
      AsmString = "tn %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 9)
      AsmString = "tne %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 9) {
      // (TXCCrr G0, IntRegs:$rs2, 9)
      AsmString = "tne %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 1)
      AsmString = "te %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TXCCrr G0, IntRegs:$rs2, 1)
      AsmString = "te %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 10)
      AsmString = "tg %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 10) {
      // (TXCCrr G0, IntRegs:$rs2, 10)
      AsmString = "tg %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 2)
      AsmString = "tle %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 2) {
      // (TXCCrr G0, IntRegs:$rs2, 2)
      AsmString = "tle %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 11)
      AsmString = "tge %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 11) {
      // (TXCCrr G0, IntRegs:$rs2, 11)
      AsmString = "tge %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 3)
      AsmString = "tl %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 3) {
      // (TXCCrr G0, IntRegs:$rs2, 3)
      AsmString = "tl %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 12)
      AsmString = "tgu %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 12) {
      // (TXCCrr G0, IntRegs:$rs2, 12)
      AsmString = "tgu %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 4)
      AsmString = "tleu %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 4) {
      // (TXCCrr G0, IntRegs:$rs2, 4)
      AsmString = "tleu %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 13)
      AsmString = "tcc %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 13) {
      // (TXCCrr G0, IntRegs:$rs2, 13)
      AsmString = "tcc %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 5)
      AsmString = "tcs %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 5) {
      // (TXCCrr G0, IntRegs:$rs2, 5)
      AsmString = "tcs %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 14)
      AsmString = "tpos %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14) {
      // (TXCCrr G0, IntRegs:$rs2, 14)
      AsmString = "tpos %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 6)
      AsmString = "tneg %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 6) {
      // (TXCCrr G0, IntRegs:$rs2, 6)
      AsmString = "tneg %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 15)
      AsmString = "tvc %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 15) {
      // (TXCCrr G0, IntRegs:$rs2, 15)
      AsmString = "tvc %xcc, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (TXCCrr IntRegs:$rs1, IntRegs:$rs2, 7)
      AsmString = "tvs %xcc, $\x01 + $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_G0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 7) {
      // (TXCCrr G0, IntRegs:$rs2, 7)
      AsmString = "tvs %xcc, $\x02";
      break;
    }
    return NULL;
  case SP_V9FCMPD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_FCC0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2)) {
      // (V9FCMPD FCC0, DFPRegs:$rs1, DFPRegs:$rs2)
      AsmString = "fcmpd $\x02, $\x03";
      break;
    }
    return NULL;
  case SP_V9FCMPED:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_FCC0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2)) {
      // (V9FCMPED FCC0, DFPRegs:$rs1, DFPRegs:$rs2)
      AsmString = "fcmped $\x02, $\x03";
      break;
    }
    return NULL;
  case SP_V9FCMPEQ:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_FCC0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2)) {
      // (V9FCMPEQ FCC0, QFPRegs:$rs1, QFPRegs:$rs2)
      AsmString = "fcmpeq $\x02, $\x03";
      break;
    }
    return NULL;
  case SP_V9FCMPES:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_FCC0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2)) {
      // (V9FCMPES FCC0, FPRegs:$rs1, FPRegs:$rs2)
      AsmString = "fcmpes $\x02, $\x03";
      break;
    }
    return NULL;
  case SP_V9FCMPQ:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_FCC0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2)) {
      // (V9FCMPQ FCC0, QFPRegs:$rs1, QFPRegs:$rs2)
      AsmString = "fcmpq $\x02, $\x03";
      break;
    }
    return NULL;
  case SP_V9FCMPS:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == SP_FCC0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2)) {
      // (V9FCMPS FCC0, FPRegs:$rs1, FPRegs:$rs2)
      AsmString = "fcmps $\x02, $\x03";
      break;
    }
    return NULL;
  case SP_V9FMOVD_FCC:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 0)
      AsmString = "fmovda $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 8) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 8)
      AsmString = "fmovdn $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 7)
      AsmString = "fmovdu $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 6) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 6)
      AsmString = "fmovdg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 5) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 5)
      AsmString = "fmovdug $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 4) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 4)
      AsmString = "fmovdl $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 3)
      AsmString = "fmovdul $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 2)
      AsmString = "fmovdlg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 1) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 1)
      AsmString = "fmovdne $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 9) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 9)
      AsmString = "fmovde $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 10) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 10)
      AsmString = "fmovdue $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 11) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 11)
      AsmString = "fmovdge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 12) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 12)
      AsmString = "fmovduge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 13) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 13)
      AsmString = "fmovdle $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 14)
      AsmString = "fmovdule $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_DFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (V9FMOVD_FCC DFPRegs:$rd, FCCRegs:$cc, DFPRegs:$rs2, 15)
      AsmString = "fmovdo $\x02, $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_V9FMOVQ_FCC:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 0)
      AsmString = "fmovqa $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 8) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 8)
      AsmString = "fmovqn $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 7)
      AsmString = "fmovqu $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 6) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 6)
      AsmString = "fmovqg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 5) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 5)
      AsmString = "fmovqug $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 4) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 4)
      AsmString = "fmovql $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 3)
      AsmString = "fmovqul $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 2)
      AsmString = "fmovqlg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 1) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 1)
      AsmString = "fmovqne $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 9) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 9)
      AsmString = "fmovqe $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 10) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 10)
      AsmString = "fmovque $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 11) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 11)
      AsmString = "fmovqge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 12) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 12)
      AsmString = "fmovquge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 13) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 13)
      AsmString = "fmovqle $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 14)
      AsmString = "fmovqule $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_QFPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (V9FMOVQ_FCC QFPRegs:$rd, FCCRegs:$cc, QFPRegs:$rs2, 15)
      AsmString = "fmovqo $\x02, $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_V9FMOVS_FCC:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 0)
      AsmString = "fmovsa $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 8) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 8)
      AsmString = "fmovsn $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 7)
      AsmString = "fmovsu $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 6) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 6)
      AsmString = "fmovsg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 5) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 5)
      AsmString = "fmovsug $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 4) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 4)
      AsmString = "fmovsl $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 3)
      AsmString = "fmovsul $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 2)
      AsmString = "fmovslg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 1) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 1)
      AsmString = "fmovsne $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 9) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 9)
      AsmString = "fmovse $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 10) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 10)
      AsmString = "fmovsue $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 11) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 11)
      AsmString = "fmovsge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 12) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 12)
      AsmString = "fmovsuge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 13) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 13)
      AsmString = "fmovsle $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 14)
      AsmString = "fmovsule $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_FPRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (V9FMOVS_FCC FPRegs:$rd, FCCRegs:$cc, FPRegs:$rs2, 15)
      AsmString = "fmovso $\x02, $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_V9MOVFCCri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 0)
      AsmString = "mova $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 8) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 8)
      AsmString = "movn $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 7)
      AsmString = "movu $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 6) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 6)
      AsmString = "movg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 5) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 5)
      AsmString = "movug $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 4) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 4)
      AsmString = "movl $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 3)
      AsmString = "movul $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 2)
      AsmString = "movlg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 1) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 1)
      AsmString = "movne $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 9) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 9)
      AsmString = "move $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 10) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 10)
      AsmString = "movue $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 11) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 11)
      AsmString = "movge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 12) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 12)
      AsmString = "movuge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 13) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 13)
      AsmString = "movle $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 14)
      AsmString = "movule $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (V9MOVFCCri IntRegs:$rd, FCCRegs:$cc, i32imm:$simm11, 15)
      AsmString = "movo $\x02, $\x03, $\x01";
      break;
    }
    return NULL;
  case SP_V9MOVFCCrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 0)
      AsmString = "mova $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 8) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 8)
      AsmString = "movn $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 7)
      AsmString = "movu $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 6) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 6)
      AsmString = "movg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 5) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 5)
      AsmString = "movug $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 4) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 4)
      AsmString = "movl $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 3)
      AsmString = "movul $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 2)
      AsmString = "movlg $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 1) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 1)
      AsmString = "movne $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 9) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 9)
      AsmString = "move $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 10) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 10)
      AsmString = "movue $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 11) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 11)
      AsmString = "movge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 12) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 12)
      AsmString = "movuge $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 13) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 13)
      AsmString = "movle $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 14)
      AsmString = "movule $\x02, $\x03, $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(SP_FCCRegsRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(SP_IntRegsRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (V9MOVFCCrr IntRegs:$rd, FCCRegs:$cc, IntRegs:$rs2, 15)
      AsmString = "movo $\x02, $\x03, $\x01";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    if (strstr(AsmOps, "icc"))
      Sparc_addReg(MI, SPARC_REG_ICC);
    if (strstr(AsmOps, "xcc"))
      Sparc_addReg(MI, SPARC_REG_XCC);
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
