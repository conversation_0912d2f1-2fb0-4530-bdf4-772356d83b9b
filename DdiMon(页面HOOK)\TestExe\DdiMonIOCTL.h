// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// User-mode interface for DdiMon IOCTL operations.

#ifndef DDIMON_IOCTL_USER_H_
#define DDIMON_IOCTL_USER_H_

#include <windows.h>

// Define NT_SUCCESS macro for user mode
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL Codes (same as kernel mode)
//

// Device type for DdiMon
#define DDIMON_DEVICE_TYPE 0x8000

// IOCTL codes for EPT memory operations
#define IOCTL_DDIMON_EPT_READ_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_EPT_WRITE_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_CHECK_EPT_HOOK \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_CREATE_EPT_HOOK \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_CREATE_EPT_HOOK_EX \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 新增：钩子管理IOCTL代码
#define IOCTL_DDIMON_REMOVE_EPT_HOOK \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_LIST_EPT_HOOKS \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_REMOVE_ALL_HOOKS \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 为了兼容性，添加简化的IOCTL代码别名
#define IOCTL_EPT_HOOK_CREATE IOCTL_DDIMON_CREATE_EPT_HOOK
#define IOCTL_EPT_HOOK_REMOVE IOCTL_DDIMON_REMOVE_EPT_HOOK
#define IOCTL_EPT_HOOK_LIST IOCTL_DDIMON_LIST_EPT_HOOKS

////////////////////////////////////////////////////////////////////////////////
//
// Data Structures (same as kernel mode)
//

// EPT memory read request
#pragma pack(push, 1)
typedef struct _EPT_READ_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to read from
    ULONG Size;                     // Number of bytes to read
} EPT_READ_MEMORY_REQUEST, *PEPT_READ_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory read response
typedef struct _EPT_READ_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesRead;                // Actual bytes read
    UCHAR Data[1];                  // Variable length data buffer
} EPT_READ_MEMORY_RESPONSE, *PEPT_READ_MEMORY_RESPONSE;

// EPT memory write request
#pragma pack(push, 1)
typedef struct _EPT_WRITE_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to write to
    ULONG Size;                     // Number of bytes to write
    UCHAR Data[1];                  // Variable length data buffer
} EPT_WRITE_MEMORY_REQUEST, *PEPT_WRITE_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory write response
typedef struct _EPT_WRITE_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesWritten;             // Actual bytes written
} EPT_WRITE_MEMORY_RESPONSE, *PEPT_WRITE_MEMORY_RESPONSE;

// EPT hook check request
typedef struct _EPT_HOOK_CHECK_REQUEST {
    ULONG64 VirtualAddress;         // Virtual address to check
} EPT_HOOK_CHECK_REQUEST, *PEPT_HOOK_CHECK_REQUEST;

// EPT hook check response
typedef struct _EPT_HOOK_CHECK_RESPONSE {
    NTSTATUS Status;                // Operation status
    BOOLEAN IsHooked;               // TRUE if address is hooked
    ULONG64 ShadowPagePA;           // Physical address of shadow page (if hooked)
} EPT_HOOK_CHECK_RESPONSE, *PEPT_HOOK_CHECK_RESPONSE;

// EPT hook creation request (updated to include ProcessId)
#pragma pack(push, 1)
typedef struct _EPT_HOOK_CREATE_REQUEST {
    ULONG64 VirtualAddress;         // Virtual address to hook
    BOOLEAN EnableExecOnly;         // TRUE for execute-only, FALSE for RW access
    ULONG ProcessId;                // Target process ID
} EPT_HOOK_CREATE_REQUEST, *PEPT_HOOK_CREATE_REQUEST;
#pragma pack(pop)

// EPT hook creation request with process ID (extended structure)
#pragma pack(push, 1)
typedef struct _EPT_HOOK_CREATE_REQUEST_EX {
    ULONG64 VirtualAddress;         // Virtual address to hook
    BOOLEAN EnableExecOnly;         // TRUE for execute-only, FALSE for RW access
    ULONG ProcessId;                // Target process ID (new field)
} EPT_HOOK_CREATE_REQUEST_EX, *PEPT_HOOK_CREATE_REQUEST_EX;
#pragma pack(pop)

// EPT hook creation response
#pragma pack(push, 1)
typedef struct _EPT_HOOK_CREATE_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG64 HookId;                 // Unique hook identifier
    ULONG64 ShadowPagePA;           // Physical address of shadow page
} EPT_HOOK_CREATE_RESPONSE, *PEPT_HOOK_CREATE_RESPONSE;
#pragma pack(pop)

// 新增：钩子删除请求
#pragma pack(push, 1)
typedef struct _EPT_HOOK_REMOVE_REQUEST {
    ULONG64 HookId;                 // Hook ID to remove
} EPT_HOOK_REMOVE_REQUEST, *PEPT_HOOK_REMOVE_REQUEST;
#pragma pack(pop)

// 新增：钩子删除响应
#pragma pack(push, 1)
typedef struct _EPT_HOOK_REMOVE_RESPONSE {
    NTSTATUS Status;                // Operation status
} EPT_HOOK_REMOVE_RESPONSE, *PEPT_HOOK_REMOVE_RESPONSE;
#pragma pack(pop)

// 新增：钩子信息结构（用于列表）
#pragma pack(push, 1)
typedef struct _EPT_HOOK_INFO {
    ULONG64 HookId;                 // Hook identifier
    ULONG64 ProcessId;              // Target process ID
    ULONG64 TargetAddress;          // Target address (page-aligned)
    ULONG64 ShadowPagePA;           // Shadow page physical address
    BOOLEAN IsActive;               // Hook activation status
    LARGE_INTEGER CreateTime;       // Creation timestamp
} EPT_HOOK_INFO, *PEPT_HOOK_INFO;
#pragma pack(pop)

// 新增：钩子列表请求
#pragma pack(push, 1)
typedef struct _EPT_HOOK_LIST_REQUEST {
    ULONG MaxCount;                 // Maximum number of hooks to return
} EPT_HOOK_LIST_REQUEST, *PEPT_HOOK_LIST_REQUEST;
#pragma pack(pop)

// 新增：钩子列表响应
#pragma pack(push, 1)
typedef struct _EPT_HOOK_LIST_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG HookCount;                // Actual number of hooks returned
    EPT_HOOK_INFO Hooks[1];         // Variable length array of hook info
} EPT_HOOK_LIST_RESPONSE, *PEPT_HOOK_LIST_RESPONSE;
#pragma pack(pop)

////////////////////////////////////////////////////////////////////////////////
//
// Helper Functions
//

class DdiMonIOCTL {
private:
    HANDLE m_hDevice;
    
public:
    DdiMonIOCTL() : m_hDevice(INVALID_HANDLE_VALUE) {}
    
    ~DdiMonIOCTL() {
        Close();
    }
    
    // Open connection to DdiMon driver
    bool Open() {
        if (m_hDevice != INVALID_HANDLE_VALUE) {
            return true; // Already open
        }
        
        m_hDevice = CreateFileW(L"\\\\.\\DdiMon",
                               GENERIC_READ | GENERIC_WRITE,
                               0,
                               NULL,
                               OPEN_EXISTING,
                               FILE_ATTRIBUTE_NORMAL,
                               NULL);
        
        return (m_hDevice != INVALID_HANDLE_VALUE);
    }
    
    // Close connection to DdiMon driver
    void Close() {
        if (m_hDevice != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hDevice);
            m_hDevice = INVALID_HANDLE_VALUE;
        }
    }
    
    // Check if connection is open
    bool IsOpen() const {
        return (m_hDevice != INVALID_HANDLE_VALUE);
    }
    
    // Read memory from EPT shadow page
    bool EptReadMemory(ULONG ProcessId, ULONG64 VirtualAddress, PVOID Buffer, ULONG Size, PULONG BytesRead) {
        if (!IsOpen()) return false;
        
        EPT_READ_MEMORY_REQUEST request = {0};
        request.ProcessId = ProcessId;
        request.VirtualAddress = VirtualAddress;
        request.Size = Size;
        
        ULONG responseSize = sizeof(EPT_READ_MEMORY_RESPONSE) + Size;
        PEPT_READ_MEMORY_RESPONSE response = (PEPT_READ_MEMORY_RESPONSE)malloc(responseSize);
        if (!response) return false;
        
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_EPT_READ_MEMORY,
                                       &request, sizeof(request),
                                       response, responseSize,
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response->Status)) {
            memcpy(Buffer, response->Data, response->BytesRead);
            if (BytesRead) *BytesRead = response->BytesRead;
            free(response);
            return true;
        }
        
        free(response);
        return false;
    }
    
    // Write memory to EPT shadow page
    bool EptWriteMemory(ULONG ProcessId, ULONG64 VirtualAddress, PVOID Buffer, ULONG Size, PULONG BytesWritten) {
        if (!IsOpen()) return false;
        
        ULONG requestSize = sizeof(EPT_WRITE_MEMORY_REQUEST) + Size;
        PEPT_WRITE_MEMORY_REQUEST request = (PEPT_WRITE_MEMORY_REQUEST)malloc(requestSize);
        if (!request) return false;
        
        request->ProcessId = ProcessId;
        request->VirtualAddress = VirtualAddress;
        request->Size = Size;
        memcpy(request->Data, Buffer, Size);
        
        EPT_WRITE_MEMORY_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_EPT_WRITE_MEMORY,
                                       request, requestSize,
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response.Status)) {
            if (BytesWritten) *BytesWritten = response.BytesWritten;
            free(request);
            return true;
        }
        
        free(request);
        return false;
    }
    
    // Check if address is hooked by EPT
    bool CheckEptHook(ULONG64 VirtualAddress, bool* IsHooked, ULONG64* ShadowPagePA = nullptr) {
        if (!IsOpen()) return false;
        
        EPT_HOOK_CHECK_REQUEST request = {0};
        request.VirtualAddress = VirtualAddress;
        
        EPT_HOOK_CHECK_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_CHECK_EPT_HOOK,
                                       &request, sizeof(request),
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);
        
        if (result && NT_SUCCESS(response.Status)) {
            *IsHooked = (response.IsHooked != FALSE);
            if (ShadowPagePA) *ShadowPagePA = response.ShadowPagePA;
            return true;
        }
        
        return false;
    }

    // Create EPT hook for address
    bool CreateEptHook(ULONG64 VirtualAddress, bool EnableExecOnly, ULONG64* HookId = nullptr, ULONG64* ShadowPagePA = nullptr) {
        if (!IsOpen()) return false;

        EPT_HOOK_CREATE_REQUEST request = {0};
        request.VirtualAddress = VirtualAddress;
        request.EnableExecOnly = EnableExecOnly ? TRUE : FALSE;

        EPT_HOOK_CREATE_RESPONSE response = {0};
        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IOCTL_DDIMON_CREATE_EPT_HOOK,
                                       &request, sizeof(request),
                                       &response, sizeof(response),
                                       &bytesReturned,
                                       NULL);

        if (result && NT_SUCCESS(response.Status)) {
            if (HookId) *HookId = response.HookId;
            if (ShadowPagePA) *ShadowPagePA = response.ShadowPagePA;
            return true;
        }

        return false;
    }

    // 通用DeviceIoControl方法
    bool DeviceIoControl(DWORD IoControlCode,
                        PVOID InputBuffer, DWORD InputBufferSize,
                        PVOID OutputBuffer, DWORD OutputBufferSize,
                        PDWORD BytesReturned) {
        if (!IsOpen()) return false;

        DWORD bytesReturned = 0;
        BOOL result = ::DeviceIoControl(m_hDevice,
                                       IoControlCode,
                                       InputBuffer, InputBufferSize,
                                       OutputBuffer, OutputBufferSize,
                                       &bytesReturned,
                                       NULL);

        if (BytesReturned) {
            *BytesReturned = bytesReturned;
        }

        return (result == TRUE);
    }
};

#endif  // DDIMON_IOCTL_USER_H_
