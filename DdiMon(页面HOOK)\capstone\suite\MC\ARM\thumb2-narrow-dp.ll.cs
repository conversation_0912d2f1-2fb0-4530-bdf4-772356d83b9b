# CS_ARCH_ARM, CS_MODE_THUMB, 
0x12,0xea,0x01,0x00 = ands.w r0, r2, r1
0x0a,0x40 = ands r2, r1
0x0a,0x40 = ands r2, r1
0x10,0xea,0x01,0x00 = ands.w r0, r0, r1
0x11,0xea,0x03,0x03 = ands.w r3, r1, r3
0x01,0xea,0x00,0x00 = and.w r0, r1, r0
0x0f,0x40 = ands r7, r1
0x0f,0x40 = ands r7, r1
0x11,0xea,0x08,0x08 = ands.w r8, r1, r8
0x18,0xea,0x01,0x08 = ands.w r8, r8, r1
0x18,0xea,0x00,0x00 = ands.w r0, r8, r0
0x11,0xea,0x08,0x01 = ands.w r1, r1, r8
0x12,0xea,0x41,0x02 = ands.w r2, r2, r1, lsl #1
0x11,0xea,0x50,0x00 = ands.w r0, r1, r0, lsr #1
0x08,0xbf = it eq
0x02,0xea,0x01,0x00 = andeq.w r0, r2, r1
0x08,0xbf = it eq
0x0b,0x40 = andeq r3, r1
0x08,0xbf = it eq
0x0b,0x40 = andeq r3, r1
0x08,0xbf = it eq
0x00,0xea,0x01,0x00 = andeq.w r0, r0, r1
0x08,0xbf = it eq
0x01,0xea,0x02,0x02 = andeq.w r2, r1, r2
0x08,0xbf = it eq
0x11,0xea,0x00,0x00 = andseq.w r0, r1, r0
0x08,0xbf = it eq
0x0f,0x40 = andeq r7, r1
0x08,0xbf = it eq
0x0f,0x40 = andeq r7, r1
0x08,0xbf = it eq
0x01,0xea,0x08,0x08 = andeq.w r8, r1, r8
0x08,0xbf = it eq
0x08,0xea,0x01,0x08 = andeq.w r8, r8, r1
0x08,0xbf = it eq
0x08,0xea,0x04,0x04 = andeq.w r4, r8, r4
0x08,0xbf = it eq
0x04,0xea,0x08,0x04 = andeq.w r4, r4, r8
0x08,0xbf = it eq
0x00,0xea,0x41,0x00 = andeq.w r0, r0, r1, lsl #1
0x08,0xbf = it eq
0x01,0xea,0x55,0x05 = andeq.w r5, r1, r5, lsr #1
0x92,0xea,0x01,0x00 = eors.w r0, r2, r1
0x4d,0x40 = eors r5, r1
0x4d,0x40 = eors r5, r1
0x90,0xea,0x01,0x00 = eors.w r0, r0, r1
0x91,0xea,0x02,0x02 = eors.w r2, r1, r2
0x81,0xea,0x01,0x01 = eor.w r1, r1, r1
0x4f,0x40 = eors r7, r1
0x4f,0x40 = eors r7, r1
0x91,0xea,0x08,0x08 = eors.w r8, r1, r8
0x98,0xea,0x01,0x08 = eors.w r8, r8, r1
0x98,0xea,0x06,0x06 = eors.w r6, r8, r6
0x90,0xea,0x08,0x00 = eors.w r0, r0, r8
0x92,0xea,0x41,0x02 = eors.w r2, r2, r1, lsl #1
0x91,0xea,0x50,0x00 = eors.w r0, r1, r0, lsr #1
0x08,0xbf = it eq
0x82,0xea,0x01,0x03 = eoreq.w r3, r2, r1
0x08,0xbf = it eq
0x48,0x40 = eoreq r0, r1
0x08,0xbf = it eq
0x4a,0x40 = eoreq r2, r1
0x08,0xbf = it eq
0x83,0xea,0x01,0x03 = eoreq.w r3, r3, r1
0x08,0xbf = it eq
0x81,0xea,0x00,0x00 = eoreq.w r0, r1, r0
0x08,0xbf = it eq
0x91,0xea,0x01,0x01 = eorseq.w r1, r1, r1
0x08,0xbf = it eq
0x4f,0x40 = eoreq r7, r1
0x08,0xbf = it eq
0x4f,0x40 = eoreq r7, r1
0x08,0xbf = it eq
0x81,0xea,0x08,0x08 = eoreq.w r8, r1, r8
0x08,0xbf = it eq
0x88,0xea,0x01,0x08 = eoreq.w r8, r8, r1
0x08,0xbf = it eq
0x88,0xea,0x00,0x00 = eoreq.w r0, r8, r0
0x08,0xbf = it eq
0x83,0xea,0x08,0x03 = eoreq.w r3, r3, r8
0x08,0xbf = it eq
0x84,0xea,0x41,0x04 = eoreq.w r4, r4, r1, lsl #1
0x08,0xbf = it eq
0x81,0xea,0x50,0x00 = eoreq.w r0, r1, r0, lsr #1
0x12,0xfa,0x01,0xf0 = lsls.w r0, r2, r1
0x8a,0x40 = lsls r2, r1
0x11,0xfa,0x02,0xf2 = lsls.w r2, r1, r2
0x10,0xfa,0x01,0xf0 = lsls.w r0, r0, r1
0x11,0xfa,0x04,0xf4 = lsls.w r4, r1, r4
0x01,0xfa,0x04,0xf4 = lsl.w r4, r1, r4
0x8f,0x40 = lsls r7, r1
0x11,0xfa,0x08,0xf8 = lsls.w r8, r1, r8
0x18,0xfa,0x01,0xf8 = lsls.w r8, r8, r1
0x18,0xfa,0x03,0xf3 = lsls.w r3, r8, r3
0x15,0xfa,0x08,0xf5 = lsls.w r5, r5, r8
0x08,0xbf = it eq
0x02,0xfa,0x01,0xf0 = lsleq.w r0, r2, r1
0x08,0xbf = it eq
0x8a,0x40 = lsleq r2, r1
0x08,0xbf = it eq
0x01,0xfa,0x02,0xf2 = lsleq.w r2, r1, r2
0x08,0xbf = it eq
0x00,0xfa,0x01,0xf0 = lsleq.w r0, r0, r1
0x08,0xbf = it eq
0x01,0xfa,0x03,0xf3 = lsleq.w r3, r1, r3
0x08,0xbf = it eq
0x11,0xfa,0x04,0xf4 = lslseq.w r4, r1, r4
0x08,0xbf = it eq
0x8f,0x40 = lsleq r7, r1
0x08,0xbf = it eq
0x01,0xfa,0x08,0xf8 = lsleq.w r8, r1, r8
0x08,0xbf = it eq
0x08,0xfa,0x01,0xf8 = lsleq.w r8, r8, r1
0x08,0xbf = it eq
0x08,0xfa,0x00,0xf0 = lsleq.w r0, r8, r0
0x08,0xbf = it eq
0x03,0xfa,0x08,0xf3 = lsleq.w r3, r3, r8
0x32,0xfa,0x01,0xf6 = lsrs.w r6, r2, r1
0xca,0x40 = lsrs r2, r1
0x31,0xfa,0x02,0xf2 = lsrs.w r2, r1, r2
0x32,0xfa,0x01,0xf2 = lsrs.w r2, r2, r1
0x31,0xfa,0x03,0xf3 = lsrs.w r3, r1, r3
0x21,0xfa,0x04,0xf4 = lsr.w r4, r1, r4
0xcf,0x40 = lsrs r7, r1
0x31,0xfa,0x08,0xf8 = lsrs.w r8, r1, r8
0x38,0xfa,0x01,0xf8 = lsrs.w r8, r8, r1
0x38,0xfa,0x02,0xf2 = lsrs.w r2, r8, r2
0x35,0xfa,0x08,0xf5 = lsrs.w r5, r5, r8
0x08,0xbf = it eq
0x22,0xfa,0x01,0xf6 = lsreq.w r6, r2, r1
0x08,0xbf = it eq
0xcf,0x40 = lsreq r7, r1
0x08,0xbf = it eq
0x21,0xfa,0x07,0xf7 = lsreq.w r7, r1, r7
0x08,0xbf = it eq
0x27,0xfa,0x01,0xf7 = lsreq.w r7, r7, r1
0x08,0xbf = it eq
0x21,0xfa,0x02,0xf2 = lsreq.w r2, r1, r2
0x08,0xbf = it eq
0x31,0xfa,0x00,0xf0 = lsrseq.w r0, r1, r0
0x08,0xbf = it eq
0xcf,0x40 = lsreq r7, r1
0x08,0xbf = it eq
0x21,0xfa,0x08,0xf8 = lsreq.w r8, r1, r8
0x08,0xbf = it eq
0x28,0xfa,0x01,0xf8 = lsreq.w r8, r8, r1
0x08,0xbf = it eq
0x28,0xfa,0x01,0xf1 = lsreq.w r1, r8, r1
0x08,0xbf = it eq
0x24,0xfa,0x08,0xf4 = lsreq.w r4, r4, r8
0x56,0xfa,0x05,0xf7 = asrs.w r7, r6, r5
0x08,0x41 = asrs r0, r1
0x51,0xfa,0x00,0xf0 = asrs.w r0, r1, r0
0x53,0xfa,0x01,0xf3 = asrs.w r3, r3, r1
0x51,0xfa,0x01,0xf1 = asrs.w r1, r1, r1
0x41,0xfa,0x00,0xf0 = asr.w r0, r1, r0
0x0f,0x41 = asrs r7, r1
0x51,0xfa,0x08,0xf8 = asrs.w r8, r1, r8
0x58,0xfa,0x01,0xf8 = asrs.w r8, r8, r1
0x58,0xfa,0x05,0xf5 = asrs.w r5, r8, r5
0x55,0xfa,0x08,0xf5 = asrs.w r5, r5, r8
0x08,0xbf = it eq
0x42,0xfa,0x01,0xf0 = asreq.w r0, r2, r1
0x08,0xbf = it eq
0x0a,0x41 = asreq r2, r1
0x08,0xbf = it eq
0x42,0xfa,0x01,0xf1 = asreq.w r1, r2, r1
0x08,0xbf = it eq
0x44,0xfa,0x01,0xf4 = asreq.w r4, r4, r1
0x08,0xbf = it eq
0x41,0xfa,0x06,0xf6 = asreq.w r6, r1, r6
0x08,0xbf = it eq
0x51,0xfa,0x03,0xf3 = asrseq.w r3, r1, r3
0x08,0xbf = it eq
0x0f,0x41 = asreq r7, r1
0x08,0xbf = it eq
0x41,0xfa,0x08,0xf8 = asreq.w r8, r1, r8
0x08,0xbf = it eq
0x48,0xfa,0x01,0xf8 = asreq.w r8, r8, r1
0x08,0xbf = it eq
0x48,0xfa,0x01,0xf1 = asreq.w r1, r8, r1
0x08,0xbf = it eq
0x43,0xfa,0x08,0xf3 = asreq.w r3, r3, r8
0x52,0xeb,0x01,0x05 = adcs.w r5, r2, r1
0x4d,0x41 = adcs r5, r1
0x4b,0x41 = adcs r3, r1
0x52,0xeb,0x01,0x02 = adcs.w r2, r2, r1
0x51,0xeb,0x03,0x03 = adcs.w r3, r1, r3
0x41,0xeb,0x00,0x00 = adc.w r0, r1, r0
0x4f,0x41 = adcs r7, r1
0x4f,0x41 = adcs r7, r1
0x51,0xeb,0x08,0x08 = adcs.w r8, r1, r8
0x58,0xeb,0x01,0x08 = adcs.w r8, r8, r1
0x58,0xeb,0x05,0x05 = adcs.w r5, r8, r5
0x52,0xeb,0x08,0x02 = adcs.w r2, r2, r8
0x53,0xeb,0x41,0x03 = adcs.w r3, r3, r1, lsl #1
0x51,0xeb,0x54,0x04 = adcs.w r4, r1, r4, lsr #1
0x08,0xbf = it eq
0x42,0xeb,0x03,0x01 = adceq.w r1, r2, r3
0x08,0xbf = it eq
0x49,0x41 = adceq r1, r1
0x08,0xbf = it eq
0x4b,0x41 = adceq r3, r1
0x08,0xbf = it eq
0x43,0xeb,0x01,0x03 = adceq.w r3, r3, r1
0x08,0xbf = it eq
0x41,0xeb,0x00,0x00 = adceq.w r0, r1, r0
0x08,0xbf = it eq
0x51,0xeb,0x03,0x03 = adcseq.w r3, r1, r3
0x08,0xbf = it eq
0x4f,0x41 = adceq r7, r1
0x08,0xbf = it eq
0x4f,0x41 = adceq r7, r1
0x08,0xbf = it eq
0x41,0xeb,0x08,0x08 = adceq.w r8, r1, r8
0x08,0xbf = it eq
0x48,0xeb,0x01,0x08 = adceq.w r8, r8, r1
0x08,0xbf = it eq
0x48,0xeb,0x03,0x03 = adceq.w r3, r8, r3
0x08,0xbf = it eq
0x41,0xeb,0x08,0x01 = adceq.w r1, r1, r8
0x08,0xbf = it eq
0x42,0xeb,0x41,0x02 = adceq.w r2, r2, r1, lsl #1
0x08,0xbf = it eq
0x41,0xeb,0x51,0x01 = adceq.w r1, r1, r1, lsr #1
0x72,0xeb,0x01,0x03 = sbcs.w r3, r2, r1
0x8c,0x41 = sbcs r4, r1
0x74,0xeb,0x01,0x01 = sbcs.w r1, r4, r1
0x74,0xeb,0x01,0x04 = sbcs.w r4, r4, r1
0x71,0xeb,0x02,0x02 = sbcs.w r2, r1, r2
0x61,0xeb,0x00,0x00 = sbc.w r0, r1, r0
0x8f,0x41 = sbcs r7, r1
0x71,0xeb,0x08,0x08 = sbcs.w r8, r1, r8
0x78,0xeb,0x01,0x08 = sbcs.w r8, r8, r1
0x78,0xeb,0x04,0x04 = sbcs.w r4, r8, r4
0x73,0xeb,0x08,0x03 = sbcs.w r3, r3, r8
0x72,0xeb,0x41,0x02 = sbcs.w r2, r2, r1, lsl #1
0x71,0xeb,0x55,0x05 = sbcs.w r5, r1, r5, lsr #1
0x08,0xbf = it eq
0x62,0xeb,0x01,0x05 = sbceq.w r5, r2, r1
0x08,0xbf = it eq
0x8d,0x41 = sbceq r5, r1
0x08,0xbf = it eq
0x65,0xeb,0x01,0x01 = sbceq.w r1, r5, r1
0x08,0xbf = it eq
0x65,0xeb,0x01,0x05 = sbceq.w r5, r5, r1
0x08,0xbf = it eq
0x61,0xeb,0x00,0x00 = sbceq.w r0, r1, r0
0x08,0xbf = it eq
0x71,0xeb,0x02,0x02 = sbcseq.w r2, r1, r2
0x08,0xbf = it eq
0x8f,0x41 = sbceq r7, r1
0x08,0xbf = it eq
0x61,0xeb,0x08,0x08 = sbceq.w r8, r1, r8
0x08,0xbf = it eq
0x68,0xeb,0x01,0x08 = sbceq.w r8, r8, r1
0x08,0xbf = it eq
0x68,0xeb,0x07,0x07 = sbceq.w r7, r8, r7
0x08,0xbf = it eq
0x67,0xeb,0x08,0x07 = sbceq.w r7, r7, r8
0x08,0xbf = it eq
0x62,0xeb,0x41,0x02 = sbceq.w r2, r2, r1, lsl #1
0x08,0xbf = it eq
0x61,0xeb,0x55,0x05 = sbceq.w r5, r1, r5, lsr #1
0x72,0xfa,0x01,0xf3 = rors.w r3, r2, r1
0xc8,0x41 = rors r0, r1
0x70,0xfa,0x01,0xf1 = rors.w r1, r0, r1
0x72,0xfa,0x01,0xf2 = rors.w r2, r2, r1
0x71,0xfa,0x02,0xf2 = rors.w r2, r1, r2
0x61,0xfa,0x05,0xf5 = ror.w r5, r1, r5
0xcf,0x41 = rors r7, r1
0x71,0xfa,0x08,0xf8 = rors.w r8, r1, r8
0x78,0xfa,0x01,0xf8 = rors.w r8, r8, r1
0x78,0xfa,0x06,0xf6 = rors.w r6, r8, r6
0x76,0xfa,0x08,0xf6 = rors.w r6, r6, r8
0x08,0xbf = it eq
0x62,0xfa,0x01,0xf4 = roreq.w r4, r2, r1
0x08,0xbf = it eq
0xcc,0x41 = roreq r4, r1
0x08,0xbf = it eq
0x64,0xfa,0x01,0xf1 = roreq.w r1, r4, r1
0x08,0xbf = it eq
0x64,0xfa,0x01,0xf4 = roreq.w r4, r4, r1
0x08,0xbf = it eq
0x61,0xfa,0x00,0xf0 = roreq.w r0, r1, r0
0x08,0xbf = it eq
0x71,0xfa,0x00,0xf0 = rorseq.w r0, r1, r0
0x08,0xbf = it eq
0xcf,0x41 = roreq r7, r1
0x08,0xbf = it eq
0x61,0xfa,0x08,0xf8 = roreq.w r8, r1, r8
0x08,0xbf = it eq
0x68,0xfa,0x01,0xf8 = roreq.w r8, r8, r1
0x08,0xbf = it eq
0x68,0xfa,0x03,0xf3 = roreq.w r3, r8, r3
0x08,0xbf = it eq
0x61,0xfa,0x08,0xf1 = roreq.w r1, r1, r8
0x52,0xea,0x01,0x07 = orrs.w r7, r2, r1
0x0a,0x43 = orrs r2, r1
0x0b,0x43 = orrs r3, r1
0x54,0xea,0x01,0x04 = orrs.w r4, r4, r1
0x51,0xea,0x05,0x05 = orrs.w r5, r1, r5
0x41,0xea,0x02,0x02 = orr.w r2, r1, r2
0x0f,0x43 = orrs r7, r1
0x0f,0x43 = orrs r7, r1
0x51,0xea,0x08,0x08 = orrs.w r8, r1, r8
0x58,0xea,0x01,0x08 = orrs.w r8, r8, r1
0x58,0xea,0x01,0x01 = orrs.w r1, r8, r1
0x50,0xea,0x08,0x00 = orrs.w r0, r0, r8
0x51,0xea,0x41,0x01 = orrs.w r1, r1, r1, lsl #1
0x51,0xea,0x50,0x00 = orrs.w r0, r1, r0, lsr #1
0x08,0xbf = it eq
0x42,0xea,0x01,0x00 = orreq.w r0, r2, r1
0x08,0xbf = it eq
0x0d,0x43 = orreq r5, r1
0x08,0xbf = it eq
0x0d,0x43 = orreq r5, r1
0x08,0xbf = it eq
0x42,0xea,0x01,0x02 = orreq.w r2, r2, r1
0x08,0xbf = it eq
0x41,0xea,0x03,0x03 = orreq.w r3, r1, r3
0x08,0xbf = it eq
0x51,0xea,0x04,0x04 = orrseq.w r4, r1, r4
0x08,0xbf = it eq
0x0f,0x43 = orreq r7, r1
0x08,0xbf = it eq
0x0f,0x43 = orreq r7, r1
0x08,0xbf = it eq
0x41,0xea,0x08,0x08 = orreq.w r8, r1, r8
0x08,0xbf = it eq
0x48,0xea,0x01,0x08 = orreq.w r8, r8, r1
0x08,0xbf = it eq
0x48,0xea,0x00,0x00 = orreq.w r0, r8, r0
0x08,0xbf = it eq
0x40,0xea,0x08,0x00 = orreq.w r0, r0, r8
0x08,0xbf = it eq
0x42,0xea,0x41,0x02 = orreq.w r2, r2, r1, lsl #1
0x08,0xbf = it eq
0x41,0xea,0x52,0x02 = orreq.w r2, r1, r2, lsr #1
0x32,0xea,0x01,0x03 = bics.w r3, r2, r1
0x8a,0x43 = bics r2, r1
0x32,0xea,0x01,0x01 = bics.w r1, r2, r1
0x32,0xea,0x01,0x02 = bics.w r2, r2, r1
0x31,0xea,0x00,0x00 = bics.w r0, r1, r0
0x21,0xea,0x00,0x00 = bic.w r0, r1, r0
0x8f,0x43 = bics r7, r1
0x31,0xea,0x08,0x08 = bics.w r8, r1, r8
0x38,0xea,0x01,0x08 = bics.w r8, r8, r1
0x38,0xea,0x07,0x07 = bics.w r7, r8, r7
0x35,0xea,0x08,0x05 = bics.w r5, r5, r8
0x33,0xea,0x41,0x03 = bics.w r3, r3, r1, lsl #1
0x31,0xea,0x54,0x04 = bics.w r4, r1, r4, lsr #1
0x08,0xbf = it eq
0x22,0xea,0x01,0x00 = biceq.w r0, r2, r1
0x08,0xbf = it eq
0x8d,0x43 = biceq r5, r1
0x08,0xbf = it eq
0x25,0xea,0x01,0x01 = biceq.w r1, r5, r1
0x08,0xbf = it eq
0x24,0xea,0x01,0x04 = biceq.w r4, r4, r1
0x08,0xbf = it eq
0x21,0xea,0x02,0x02 = biceq.w r2, r1, r2
0x08,0xbf = it eq
0x31,0xea,0x05,0x05 = bicseq.w r5, r1, r5
0x08,0xbf = it eq
0x8f,0x43 = biceq r7, r1
0x08,0xbf = it eq
0x21,0xea,0x08,0x08 = biceq.w r8, r1, r8
0x08,0xbf = it eq
0x28,0xea,0x01,0x08 = biceq.w r8, r8, r1
0x08,0xbf = it eq
0x28,0xea,0x00,0x00 = biceq.w r0, r8, r0
0x08,0xbf = it eq
0x22,0xea,0x08,0x02 = biceq.w r2, r2, r8
0x08,0xbf = it eq
0x24,0xea,0x41,0x04 = biceq.w r4, r4, r1, lsl #1
0x08,0xbf = it eq
0x21,0xea,0x55,0x05 = biceq.w r5, r1, r5, lsr #1
