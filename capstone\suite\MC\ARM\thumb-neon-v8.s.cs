# CS_ARCH_ARM, CS_MODE_THUMB+CS_MODE_V8, None
0x05,0xff,0x11,0x4f = vmaxnm.f32 d4, d5, d1
0x08,0xff,0x5c,0x4f = vmaxnm.f32 q2, q4, q6
0x24,0xff,0x3e,0x5f = vminnm.f32 d5, d4, d30
0x2a,0xff,0xd4,0x0f = vminnm.f32 q0, q13, q2
0xbb,0xff,0x06,0x40 = vcvta.s32.f32 d4, d6
0xbb,0xff,0x8a,0xc0 = vcvta.u32.f32 d12, d10
0xbb,0xff,0x4c,0x80 = vcvta.s32.f32 q4, q6
0xbb,0xff,0xe4,0x80 = vcvta.u32.f32 q4, q10
0xbb,0xff,0x2e,0x13 = vcvtm.s32.f32 d1, d30
0xbb,0xff,0x8a,0xc3 = vcvtm.u32.f32 d12, d10
0xbb,0xff,0x64,0x23 = vcvtm.s32.f32 q1, q10
0xfb,0xff,0xc2,0xa3 = vcvtm.u32.f32 q13, q1
0xbb,0xff,0x21,0xf1 = vcvtn.s32.f32 d15, d17
0xbb,0xff,0x83,0x51 = vcvtn.u32.f32 d5, d3
0xbb,0xff,0x60,0x61 = vcvtn.s32.f32 q3, q8
0xbb,0xff,0xc6,0xa1 = vcvtn.u32.f32 q5, q3
0xbb,0xff,0x25,0xb2 = vcvtp.s32.f32 d11, d21
0xbb,0xff,0xa7,0xe2 = vcvtp.u32.f32 d14, d23
0xbb,0xff,0x6e,0x82 = vcvtp.s32.f32 q4, q15
0xfb,0xff,0xe0,0x22 = vcvtp.u32.f32 q9, q8
0xba,0xff,0x00,0x34 = vrintn.f32 d3, d0
0xba,0xff,0x48,0x24 = vrintn.f32 q1, q4
0xba,0xff,0x8c,0x54 = vrintx.f32 d5, d12
0xba,0xff,0xc6,0x04 = vrintx.f32 q0, q3
0xba,0xff,0x00,0x35 = vrinta.f32 d3, d0
0xfa,0xff,0x44,0x05 = vrinta.f32 q8, q2
0xba,0xff,0xa2,0xc5 = vrintz.f32 d12, d18
0xfa,0xff,0xc8,0x25 = vrintz.f32 q9, q4
0xba,0xff,0x80,0x36 = vrintm.f32 d3, d0
0xba,0xff,0xc8,0x26 = vrintm.f32 q1, q4
0xba,0xff,0x80,0x37 = vrintp.f32 d3, d0
0xba,0xff,0xc8,0x27 = vrintp.f32 q1, q4
0xba,0xff,0x00,0x34 = vrintn.f32 d3, d0
0xba,0xff,0xc6,0x04 = vrintx.f32 q0, q3
0xba,0xff,0x00,0x35 = vrinta.f32 d3, d0
0xfa,0xff,0xc8,0x25 = vrintz.f32 q9, q4
0xba,0xff,0xc8,0x27 = vrintp.f32 q1, q4
