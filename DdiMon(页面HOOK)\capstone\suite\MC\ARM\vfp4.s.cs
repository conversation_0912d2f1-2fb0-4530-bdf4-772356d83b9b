# CS_ARCH_ARM, CS_MODE_ARM, None
0xa1,0x0b,0xe2,0xee = vfma.f64 d16, d18, d17
0x00,0x1a,0xa2,0xee = vfma.f32 s2, s4, s0
0xb1,0x0c,0x42,0xf2 = vfma.f32 d16, d18, d17
0x50,0x4c,0x08,0xf2 = vfma.f32 q2, q4, q0
0xe1,0x0b,0xd2,0xee = vfnma.f64 d16, d18, d17
0x40,0x1a,0x92,0xee = vfnma.f32 s2, s4, s0
0xe1,0x0b,0xe2,0xee = vfms.f64 d16, d18, d17
0x40,0x1a,0xa2,0xee = vfms.f32 s2, s4, s0
0xb1,0x0c,0x62,0xf2 = vfms.f32 d16, d18, d17
0x50,0x4c,0x28,0xf2 = vfms.f32 q2, q4, q0
0xa1,0x0b,0xd2,0xee = vfnms.f64 d16, d18, d17
0x00,0x1a,0x92,0xee = vfnms.f32 s2, s4, s0
