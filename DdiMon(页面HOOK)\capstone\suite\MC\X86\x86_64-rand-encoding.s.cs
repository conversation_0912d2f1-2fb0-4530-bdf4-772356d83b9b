# CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_ATT
0x66,0x0f,0xc7,0xf0 = rdrandw %ax
0x0f,0xc7,0xf0 = rdrandl %eax
0x48,0x0f,0xc7,0xf0 = rdrandq %rax
0x66,0x41,0x0f,0xc7,0xf3 = rdrandw %r11w
0x41,0x0f,0xc7,0xf3 = rdrandl %r11d
0x49,0x0f,0xc7,0xf3 = rdrandq %r11
0x66,0x0f,0xc7,0xf8 = rdseedw %ax
0x0f,0xc7,0xf8 = rdseedl %eax
0x48,0x0f,0xc7,0xf8 = rdseedq %rax
0x66,0x41,0x0f,0xc7,0xfb = rdseedw %r11w
0x41,0x0f,0xc7,0xfb = rdseedl %r11d
0x49,0x0f,0xc7,0xfb = rdseedq %r11
