/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Subtarget Enumeration Source Fragment                                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2013-2014 */


#ifdef GET_SUBTARGETINFO_ENUM
#undef GET_SUBTARGETINFO_ENUM

#define PPC_DeprecatedDST (1ULL << 0)
#define PPC_DeprecatedMFTB (1ULL << 1)
#define PPC_Directive32 (1ULL << 2)
#define PPC_Directive64 (1ULL << 3)
#define PPC_Directive440 (1ULL << 4)
#define PPC_Directive601 (1ULL << 5)
#define PPC_Directive602 (1ULL << 6)
#define PPC_Directive603 (1ULL << 7)
#define PPC_Directive604 (1ULL << 8)
#define PPC_Directive620 (1ULL << 9)
#define PPC_Directive750 (1ULL << 10)
#define PPC_Directive970 (1ULL << 11)
#define PPC_Directive7400 (1ULL << 12)
#define PPC_DirectiveA2 (1ULL << 13)
#define PPC_DirectiveE500mc (1ULL << 14)
#define PPC_DirectiveE5500 (1ULL << 15)
#define PPC_DirectivePwr3 (1ULL << 16)
#define PPC_DirectivePwr4 (1ULL << 17)
#define PPC_DirectivePwr5 (1ULL << 18)
#define PPC_DirectivePwr5x (1ULL << 19)
#define PPC_DirectivePwr6 (1ULL << 20)
#define PPC_DirectivePwr6x (1ULL << 21)
#define PPC_DirectivePwr7 (1ULL << 22)
#define PPC_DirectivePwr8 (1ULL << 23)
#define PPC_Feature64Bit (1ULL << 24)
#define PPC_Feature64BitRegs (1ULL << 25)
#define PPC_FeatureAltivec (1ULL << 26)
#define PPC_FeatureBookE (1ULL << 27)
#define PPC_FeatureCRBits (1ULL << 28)
#define PPC_FeatureE500 (1ULL << 29)
#define PPC_FeatureELFv1 (1ULL << 30)
#define PPC_FeatureELFv2 (1ULL << 31)
#define PPC_FeatureFCPSGN (1ULL << 32)
#define PPC_FeatureFPCVT (1ULL << 33)
#define PPC_FeatureFPRND (1ULL << 34)
#define PPC_FeatureFRE (1ULL << 35)
#define PPC_FeatureFRES (1ULL << 36)
#define PPC_FeatureFRSQRTE (1ULL << 37)
#define PPC_FeatureFRSQRTES (1ULL << 38)
#define PPC_FeatureFSqrt (1ULL << 39)
#define PPC_FeatureISEL (1ULL << 40)
#define PPC_FeatureLDBRX (1ULL << 41)
#define PPC_FeatureLFIWAX (1ULL << 42)
#define PPC_FeatureMFOCRF (1ULL << 43)
#define PPC_FeaturePOPCNTD (1ULL << 44)
#define PPC_FeaturePPC4xx (1ULL << 45)
#define PPC_FeaturePPC6xx (1ULL << 46)
#define PPC_FeatureQPX (1ULL << 47)
#define PPC_FeatureRecipPrec (1ULL << 48)
#define PPC_FeatureSPE (1ULL << 49)
#define PPC_FeatureSTFIWX (1ULL << 50)
#define PPC_FeatureVSX (1ULL << 51)

#endif // GET_SUBTARGETINFO_ENUM

