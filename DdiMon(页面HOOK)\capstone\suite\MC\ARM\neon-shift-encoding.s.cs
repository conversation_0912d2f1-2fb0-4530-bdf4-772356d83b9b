# CS_ARCH_ARM, CS_MODE_ARM, None
0xa1,0x04,0x40,0xf3 = vshl.u8 d16, d17, d16
0xa1,0x04,0x50,0xf3 = vshl.u16 d16, d17, d16
0xa1,0x04,0x60,0xf3 = vshl.u32 d16, d17, d16
0xa1,0x04,0x70,0xf3 = vshl.u64 d16, d17, d16
0x30,0x05,0xcf,0xf2 = vshl.i8 d16, d16, #7
0x30,0x05,0xdf,0xf2 = vshl.i16 d16, d16, #15
0x30,0x05,0xff,0xf2 = vshl.i32 d16, d16, #31
0xb0,0x05,0xff,0xf2 = vshl.i64 d16, d16, #63
0xe2,0x04,0x40,0xf3 = vshl.u8 q8, q9, q8
0xe2,0x04,0x50,0xf3 = vshl.u16 q8, q9, q8
0xe2,0x04,0x60,0xf3 = vshl.u32 q8, q9, q8
0xe2,0x04,0x70,0xf3 = vshl.u64 q8, q9, q8
0x70,0x05,0xcf,0xf2 = vshl.i8 q8, q8, #7
0x70,0x05,0xdf,0xf2 = vshl.i16 q8, q8, #15
0x70,0x05,0xff,0xf2 = vshl.i32 q8, q8, #31
0xf0,0x05,0xff,0xf2 = vshl.i64 q8, q8, #63
0x30,0x00,0xc9,0xf3 = vshr.u8 d16, d16, #7
0x30,0x00,0xd1,0xf3 = vshr.u16 d16, d16, #15
0x30,0x00,0xe1,0xf3 = vshr.u32 d16, d16, #31
0xb0,0x00,0xc1,0xf3 = vshr.u64 d16, d16, #63
0x70,0x00,0xc9,0xf3 = vshr.u8 q8, q8, #7
0x70,0x00,0xd1,0xf3 = vshr.u16 q8, q8, #15
0x70,0x00,0xe1,0xf3 = vshr.u32 q8, q8, #31
0xf0,0x00,0xc1,0xf3 = vshr.u64 q8, q8, #63
0x30,0x00,0xc9,0xf2 = vshr.s8 d16, d16, #7
0x30,0x00,0xd1,0xf2 = vshr.s16 d16, d16, #15
0x30,0x00,0xe1,0xf2 = vshr.s32 d16, d16, #31
0xb0,0x00,0xc1,0xf2 = vshr.s64 d16, d16, #63
0x70,0x00,0xc9,0xf2 = vshr.s8 q8, q8, #7
0x70,0x00,0xd1,0xf2 = vshr.s16 q8, q8, #15
0x70,0x00,0xe1,0xf2 = vshr.s32 q8, q8, #31
0xf0,0x00,0xc1,0xf2 = vshr.s64 q8, q8, #63
0x30,0x00,0xc9,0xf3 = vshr.u8 d16, d16, #7
0x30,0x00,0xd1,0xf3 = vshr.u16 d16, d16, #15
0x30,0x00,0xe1,0xf3 = vshr.u32 d16, d16, #31
0xb0,0x00,0xc1,0xf3 = vshr.u64 d16, d16, #63
0x70,0x00,0xc9,0xf3 = vshr.u8 q8, q8, #7
0x70,0x00,0xd1,0xf3 = vshr.u16 q8, q8, #15
0x70,0x00,0xe1,0xf3 = vshr.u32 q8, q8, #31
0xf0,0x00,0xc1,0xf3 = vshr.u64 q8, q8, #63
0x30,0x00,0xc9,0xf2 = vshr.s8 d16, d16, #7
0x30,0x00,0xd1,0xf2 = vshr.s16 d16, d16, #15
0x30,0x00,0xe1,0xf2 = vshr.s32 d16, d16, #31
0xb0,0x00,0xc1,0xf2 = vshr.s64 d16, d16, #63
0x70,0x00,0xc9,0xf2 = vshr.s8 q8, q8, #7
0x70,0x00,0xd1,0xf2 = vshr.s16 q8, q8, #15
0x70,0x00,0xe1,0xf2 = vshr.s32 q8, q8, #31
0xf0,0x00,0xc1,0xf2 = vshr.s64 q8, q8, #63
0x16,0x01,0xc9,0xf2 = vsra.s8 d16, d6, #7
0x32,0xa1,0xd1,0xf2 = vsra.s16 d26, d18, #15
0x1a,0xb1,0xa1,0xf2 = vsra.s32 d11, d10, #31
0xb3,0xc1,0x81,0xf2 = vsra.s64 d12, d19, #63
0x70,0x21,0x89,0xf2 = vsra.s8 q1, q8, #7
0x5e,0x41,0x91,0xf2 = vsra.s16 q2, q7, #15
0x5c,0x61,0xa1,0xf2 = vsra.s32 q3, q6, #31
0xda,0x81,0x81,0xf2 = vsra.s64 q4, q5, #63
0x30,0x01,0xc9,0xf2 = vsra.s8 d16, d16, #7
0x1f,0xf1,0x91,0xf2 = vsra.s16 d15, d15, #15
0x1e,0xe1,0xa1,0xf2 = vsra.s32 d14, d14, #31
0x9d,0xd1,0x81,0xf2 = vsra.s64 d13, d13, #63
0x58,0x81,0x89,0xf2 = vsra.s8 q4, q4, #7
0x5a,0xa1,0x91,0xf2 = vsra.s16 q5, q5, #15
0x5c,0xc1,0xa1,0xf2 = vsra.s32 q6, q6, #31
0xde,0xe1,0x81,0xf2 = vsra.s64 q7, q7, #63
0x16,0x01,0xc9,0xf3 = vsra.u8 d16, d6, #7
0x32,0xa1,0xd1,0xf3 = vsra.u16 d26, d18, #15
0x1a,0xb1,0xa1,0xf3 = vsra.u32 d11, d10, #31
0xb3,0xc1,0x81,0xf3 = vsra.u64 d12, d19, #63
0x70,0x21,0x89,0xf3 = vsra.u8 q1, q8, #7
0x5e,0x41,0x91,0xf3 = vsra.u16 q2, q7, #15
0x5c,0x61,0xa1,0xf3 = vsra.u32 q3, q6, #31
0xda,0x81,0x81,0xf3 = vsra.u64 q4, q5, #63
0x30,0x01,0xc9,0xf3 = vsra.u8 d16, d16, #7
0x1f,0xf1,0x91,0xf3 = vsra.u16 d15, d15, #15
0x1e,0xe1,0xa1,0xf3 = vsra.u32 d14, d14, #31
0x9d,0xd1,0x81,0xf3 = vsra.u64 d13, d13, #63
0x58,0x81,0x89,0xf3 = vsra.u8 q4, q4, #7
0x5a,0xa1,0x91,0xf3 = vsra.u16 q5, q5, #15
0x5c,0xc1,0xa1,0xf3 = vsra.u32 q6, q6, #31
0xde,0xe1,0x81,0xf3 = vsra.u64 q7, q7, #63
0x16,0x04,0xc9,0xf3 = vsri.8 d16, d6, #7
0x32,0xa4,0xd1,0xf3 = vsri.16 d26, d18, #15
0x1a,0xb4,0xa1,0xf3 = vsri.32 d11, d10, #31
0xb3,0xc4,0x81,0xf3 = vsri.64 d12, d19, #63
0x70,0x24,0x89,0xf3 = vsri.8 q1, q8, #7
0x5e,0x44,0x91,0xf3 = vsri.16 q2, q7, #15
0x5c,0x64,0xa1,0xf3 = vsri.32 q3, q6, #31
0xda,0x84,0x81,0xf3 = vsri.64 q4, q5, #63
0x30,0x04,0xc9,0xf3 = vsri.8 d16, d16, #7
0x1f,0xf4,0x91,0xf3 = vsri.16 d15, d15, #15
0x1e,0xe4,0xa1,0xf3 = vsri.32 d14, d14, #31
0x9d,0xd4,0x81,0xf3 = vsri.64 d13, d13, #63
0x58,0x84,0x89,0xf3 = vsri.8 q4, q4, #7
0x5a,0xa4,0x91,0xf3 = vsri.16 q5, q5, #15
0x5c,0xc4,0xa1,0xf3 = vsri.32 q6, q6, #31
0xde,0xe4,0x81,0xf3 = vsri.64 q7, q7, #63
0x16,0x05,0xcf,0xf3 = vsli.8 d16, d6, #7
0x32,0xa5,0xdf,0xf3 = vsli.16 d26, d18, #15
0x1a,0xb5,0xbf,0xf3 = vsli.32 d11, d10, #31
0xb3,0xc5,0xbf,0xf3 = vsli.64 d12, d19, #63
0x70,0x25,0x8f,0xf3 = vsli.8 q1, q8, #7
0x5e,0x45,0x9f,0xf3 = vsli.16 q2, q7, #15
0x5c,0x65,0xbf,0xf3 = vsli.32 q3, q6, #31
0xda,0x85,0xbf,0xf3 = vsli.64 q4, q5, #63
0x30,0x05,0xcf,0xf3 = vsli.8 d16, d16, #7
0x1f,0xf5,0x9f,0xf3 = vsli.16 d15, d15, #15
0x1e,0xe5,0xbf,0xf3 = vsli.32 d14, d14, #31
0x9d,0xd5,0xbf,0xf3 = vsli.64 d13, d13, #63
0x58,0x85,0x8f,0xf3 = vsli.8 q4, q4, #7
0x5a,0xa5,0x9f,0xf3 = vsli.16 q5, q5, #15
0x5c,0xc5,0xbf,0xf3 = vsli.32 q6, q6, #31
0xde,0xe5,0xbf,0xf3 = vsli.64 q7, q7, #63
0x30,0x0a,0xcf,0xf2 = vshll.s8 q8, d16, #7
0x30,0x0a,0xdf,0xf2 = vshll.s16 q8, d16, #15
0x30,0x0a,0xff,0xf2 = vshll.s32 q8, d16, #31
0x30,0x0a,0xcf,0xf3 = vshll.u8 q8, d16, #7
0x30,0x0a,0xdf,0xf3 = vshll.u16 q8, d16, #15
0x30,0x0a,0xff,0xf3 = vshll.u32 q8, d16, #31
0x20,0x03,0xf2,0xf3 = vshll.i8 q8, d16, #8
0x20,0x03,0xf6,0xf3 = vshll.i16 q8, d16, #16
0x20,0x03,0xfa,0xf3 = vshll.i32 q8, d16, #32
0x30,0x08,0xc8,0xf2 = vshrn.i16 d16, q8, #8
0x30,0x08,0xd0,0xf2 = vshrn.i32 d16, q8, #16
0x30,0x08,0xe0,0xf2 = vshrn.i64 d16, q8, #32
0xa1,0x05,0x40,0xf2 = vrshl.s8 d16, d17, d16
0xa1,0x05,0x50,0xf2 = vrshl.s16 d16, d17, d16
0xa1,0x05,0x60,0xf2 = vrshl.s32 d16, d17, d16
0xa1,0x05,0x70,0xf2 = vrshl.s64 d16, d17, d16
0xa1,0x05,0x40,0xf3 = vrshl.u8 d16, d17, d16
0xa1,0x05,0x50,0xf3 = vrshl.u16 d16, d17, d16
0xa1,0x05,0x60,0xf3 = vrshl.u32 d16, d17, d16
0xa1,0x05,0x70,0xf3 = vrshl.u64 d16, d17, d16
0xe2,0x05,0x40,0xf2 = vrshl.s8 q8, q9, q8
0xe2,0x05,0x50,0xf2 = vrshl.s16 q8, q9, q8
0xe2,0x05,0x60,0xf2 = vrshl.s32 q8, q9, q8
0xe2,0x05,0x70,0xf2 = vrshl.s64 q8, q9, q8
0xe2,0x05,0x40,0xf3 = vrshl.u8 q8, q9, q8
0xe2,0x05,0x50,0xf3 = vrshl.u16 q8, q9, q8
0xe2,0x05,0x60,0xf3 = vrshl.u32 q8, q9, q8
0xe2,0x05,0x70,0xf3 = vrshl.u64 q8, q9, q8
0x30,0x02,0xc8,0xf2 = vrshr.s8 d16, d16, #8
0x30,0x02,0xd0,0xf2 = vrshr.s16 d16, d16, #16
0x30,0x02,0xe0,0xf2 = vrshr.s32 d16, d16, #32
0xb0,0x02,0xc0,0xf2 = vrshr.s64 d16, d16, #64
0x30,0x02,0xc8,0xf3 = vrshr.u8 d16, d16, #8
0x30,0x02,0xd0,0xf3 = vrshr.u16 d16, d16, #16
0x30,0x02,0xe0,0xf3 = vrshr.u32 d16, d16, #32
0xb0,0x02,0xc0,0xf3 = vrshr.u64 d16, d16, #64
0x70,0x02,0xc8,0xf2 = vrshr.s8 q8, q8, #8
0x70,0x02,0xd0,0xf2 = vrshr.s16 q8, q8, #16
0x70,0x02,0xe0,0xf2 = vrshr.s32 q8, q8, #32
0xf0,0x02,0xc0,0xf2 = vrshr.s64 q8, q8, #64
0x70,0x02,0xc8,0xf3 = vrshr.u8 q8, q8, #8
0x70,0x02,0xd0,0xf3 = vrshr.u16 q8, q8, #16
0x70,0x02,0xe0,0xf3 = vrshr.u32 q8, q8, #32
0xf0,0x02,0xc0,0xf3 = vrshr.u64 q8, q8, #64
0x70,0x08,0xc8,0xf2 = vrshrn.i16 d16, q8, #8
0x70,0x08,0xd0,0xf2 = vrshrn.i32 d16, q8, #16
0x70,0x08,0xe0,0xf2 = vrshrn.i64 d16, q8, #32
0x70,0x09,0xcc,0xf2 = vqrshrn.s16 d16, q8, #4
0x70,0x09,0xd3,0xf2 = vqrshrn.s32 d16, q8, #13
0x70,0x09,0xf3,0xf2 = vqrshrn.s64 d16, q8, #13
0x70,0x09,0xcc,0xf3 = vqrshrn.u16 d16, q8, #4
0x70,0x09,0xd3,0xf3 = vqrshrn.u32 d16, q8, #13
0x70,0x09,0xf3,0xf3 = vqrshrn.u64 d16, q8, #13
0x48,0x84,0x0a,0xf2 = vshl.s8 q4, q4, q5
0x48,0x84,0x1a,0xf2 = vshl.s16 q4, q4, q5
0x48,0x84,0x2a,0xf2 = vshl.s32 q4, q4, q5
0x48,0x84,0x3a,0xf2 = vshl.s64 q4, q4, q5
0x48,0x84,0x0a,0xf3 = vshl.u8 q4, q4, q5
0x48,0x84,0x1a,0xf3 = vshl.u16 q4, q4, q5
0x48,0x84,0x2a,0xf3 = vshl.u32 q4, q4, q5
0x48,0x84,0x3a,0xf3 = vshl.u64 q4, q4, q5
0x04,0x44,0x05,0xf2 = vshl.s8 d4, d4, d5
0x04,0x44,0x15,0xf2 = vshl.s16 d4, d4, d5
0x04,0x44,0x25,0xf2 = vshl.s32 d4, d4, d5
0x04,0x44,0x35,0xf2 = vshl.s64 d4, d4, d5
0x04,0x44,0x05,0xf3 = vshl.u8 d4, d4, d5
0x04,0x44,0x15,0xf3 = vshl.u16 d4, d4, d5
0x04,0x44,0x25,0xf3 = vshl.u32 d4, d4, d5
0x04,0x44,0x35,0xf3 = vshl.u64 d4, d4, d5
0x58,0x85,0x8a,0xf2 = vshl.i8 q4, q4, #2
0x58,0x85,0x9e,0xf2 = vshl.i16 q4, q4, #14
0x58,0x85,0xbb,0xf2 = vshl.i32 q4, q4, #27
0xd8,0x85,0xa3,0xf2 = vshl.i64 q4, q4, #35
0x14,0x45,0x8e,0xf2 = vshl.i8 d4, d4, #6
0x14,0x45,0x9a,0xf2 = vshl.i16 d4, d4, #10
0x14,0x45,0xb1,0xf2 = vshl.i32 d4, d4, #17
0x94,0x45,0xab,0xf2 = vshl.i64 d4, d4, #43
0x0b,0xb5,0x04,0xf2 = vrshl.s8 d11, d11, d4
0x0c,0xc5,0x15,0xf2 = vrshl.s16 d12, d12, d5
0x0d,0xd5,0x26,0xf2 = vrshl.s32 d13, d13, d6
0x0e,0xe5,0x37,0xf2 = vrshl.s64 d14, d14, d7
0x0f,0xf5,0x08,0xf3 = vrshl.u8 d15, d15, d8
0x20,0x05,0x59,0xf3 = vrshl.u16 d16, d16, d9
0x21,0x15,0x6a,0xf3 = vrshl.u32 d17, d17, d10
0x22,0x25,0x7b,0xf3 = vrshl.u64 d18, d18, d11
0xc2,0x25,0x00,0xf2 = vrshl.s8 q1, q1, q8
0xc4,0x45,0x1e,0xf2 = vrshl.s16 q2, q2, q15
0xc6,0x65,0x2c,0xf2 = vrshl.s32 q3, q3, q14
0xc8,0x85,0x3a,0xf2 = vrshl.s64 q4, q4, q13
0xca,0xa5,0x08,0xf3 = vrshl.u8 q5, q5, q12
0xcc,0xc5,0x16,0xf3 = vrshl.u16 q6, q6, q11
0xce,0xe5,0x24,0xf3 = vrshl.u32 q7, q7, q10
0xe0,0x05,0x72,0xf3 = vrshl.u64 q8, q8, q9
0x1f,0xf0,0x88,0xf2 = vshr.s8 d15, d15, #8
0x1c,0xc0,0x90,0xf2 = vshr.s16 d12, d12, #16
0x1d,0xd0,0xa0,0xf2 = vshr.s32 d13, d13, #32
0x9e,0xe0,0x80,0xf2 = vshr.s64 d14, d14, #64
0x30,0x00,0xc8,0xf3 = vshr.u8 d16, d16, #8
0x31,0x10,0xd0,0xf3 = vshr.u16 d17, d17, #16
0x16,0x60,0xa0,0xf3 = vshr.u32 d6, d6, #32
0x9a,0xa0,0x80,0xf3 = vshr.u64 d10, d10, #64
0x52,0x20,0x88,0xf2 = vshr.s8 q1, q1, #8
0x54,0x40,0x90,0xf2 = vshr.s16 q2, q2, #16
0x56,0x60,0xa0,0xf2 = vshr.s32 q3, q3, #32
0xd8,0x80,0x80,0xf2 = vshr.s64 q4, q4, #64
0x5a,0xa0,0x88,0xf3 = vshr.u8 q5, q5, #8
0x5c,0xc0,0x90,0xf3 = vshr.u16 q6, q6, #16
0x5e,0xe0,0xa0,0xf3 = vshr.u32 q7, q7, #32
0xf0,0x00,0xc0,0xf3 = vshr.u64 q8, q8, #64
0x1f,0xf2,0x88,0xf2 = vrshr.s8 d15, d15, #8
0x1c,0xc2,0x90,0xf2 = vrshr.s16 d12, d12, #16
0x1d,0xd2,0xa0,0xf2 = vrshr.s32 d13, d13, #32
0x9e,0xe2,0x80,0xf2 = vrshr.s64 d14, d14, #64
0x30,0x02,0xc8,0xf3 = vrshr.u8 d16, d16, #8
0x31,0x12,0xd0,0xf3 = vrshr.u16 d17, d17, #16
0x16,0x62,0xa0,0xf3 = vrshr.u32 d6, d6, #32
0x9a,0xa2,0x80,0xf3 = vrshr.u64 d10, d10, #64
0x52,0x22,0x88,0xf2 = vrshr.s8 q1, q1, #8
0x54,0x42,0x90,0xf2 = vrshr.s16 q2, q2, #16
0x56,0x62,0xa0,0xf2 = vrshr.s32 q3, q3, #32
0xd8,0x82,0x80,0xf2 = vrshr.s64 q4, q4, #64
0x5a,0xa2,0x88,0xf3 = vrshr.u8 q5, q5, #8
0x5c,0xc2,0x90,0xf3 = vrshr.u16 q6, q6, #16
0x5e,0xe2,0xa0,0xf3 = vrshr.u32 q7, q7, #32
0xf0,0x02,0xc0,0xf3 = vrshr.u64 q8, q8, #64
