# CS_ARCH_ARM, CS_MODE_THUMB, None
0x42,0xef,0xa1,0x09 = vmla.i8 d16, d18, d17
0x52,0xef,0xa1,0x09 = vmla.i16 d16, d18, d17
0x62,0xef,0xa1,0x09 = vmla.i32 d16, d18, d17
0x42,0xef,0xb1,0x0d = vmla.f32 d16, d18, d17
0x40,0xef,0xe4,0x29 = vmla.i8 q9, q8, q10
0x50,0xef,0xe4,0x29 = vmla.i16 q9, q8, q10
0x60,0xef,0xe4,0x29 = vmla.i32 q9, q8, q10
0x40,0xef,0xf4,0x2d = vmla.f32 q9, q8, q10
0xe0,0xff,0xc3,0x80 = vmla.i32 q12, q8, d3[0]
0xc3,0xef,0xa2,0x08 = vmlal.s8 q8, d19, d18
0xd3,0xef,0xa2,0x08 = vmlal.s16 q8, d19, d18
0xe3,0xef,0xa2,0x08 = vmlal.s32 q8, d19, d18
0xc3,0xff,0xa2,0x08 = vmlal.u8 q8, d19, d18
0xd3,0xff,0xa2,0x08 = vmlal.u16 q8, d19, d18
0xe3,0xff,0xa2,0x08 = vmlal.u32 q8, d19, d18
0xa5,0xef,0x4a,0x02 = vmlal.s32 q0, d5, d10[0]
0xd3,0xef,0xa2,0x09 = vqdmlal.s16 q8, d19, d18
0xe3,0xef,0xa2,0x09 = vqdmlal.s32 q8, d19, d18
0xdb,0xef,0x47,0x63 = vqdmlal.s16 q11, d11, d7[0]
0xdb,0xef,0x4f,0x63 = vqdmlal.s16 q11, d11, d7[1]
0xdb,0xef,0x67,0x63 = vqdmlal.s16 q11, d11, d7[2]
0xdb,0xef,0x6f,0x63 = vqdmlal.s16 q11, d11, d7[3]
0x42,0xff,0xa1,0x09 = vmls.i8 d16, d18, d17
0x52,0xff,0xa1,0x09 = vmls.i16 d16, d18, d17
0x62,0xff,0xa1,0x09 = vmls.i32 d16, d18, d17
0x62,0xef,0xb1,0x0d = vmls.f32 d16, d18, d17
0x40,0xff,0xe4,0x29 = vmls.i8 q9, q8, q10
0x50,0xff,0xe4,0x29 = vmls.i16 q9, q8, q10
0x60,0xff,0xe4,0x29 = vmls.i32 q9, q8, q10
0x60,0xef,0xf4,0x2d = vmls.f32 q9, q8, q10
0x98,0xff,0xe6,0x84 = vmls.i16 q4, q12, d6[2]
0xc3,0xef,0xa2,0x0a = vmlsl.s8 q8, d19, d18
0xd3,0xef,0xa2,0x0a = vmlsl.s16 q8, d19, d18
0xe3,0xef,0xa2,0x0a = vmlsl.s32 q8, d19, d18
0xc3,0xff,0xa2,0x0a = vmlsl.u8 q8, d19, d18
0xd3,0xff,0xa2,0x0a = vmlsl.u16 q8, d19, d18
0xe3,0xff,0xa2,0x0a = vmlsl.u32 q8, d19, d18
0xd9,0xff,0xe9,0x66 = vmlsl.u16 q11, d25, d1[3]
0xd3,0xef,0xa2,0x0b = vqdmlsl.s16 q8, d19, d18
0xe3,0xef,0xa2,0x0b = vqdmlsl.s32 q8, d19, d18
