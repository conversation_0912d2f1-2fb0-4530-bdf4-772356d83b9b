Capstone source is organized as followings.


.                   <- core engine + README + COMPILE.TXT etc
├── arch            <- code handling disasm engine for each arch
│   ├── AArch64     <- ARM64 (aka ARMv8) engine
│   ├── ARM         <- ARM engine
│   ├── Mips        <- Mips engine
│   ├── PowerPC     <- PowerPC engine
│   ├── Sparc       <- Sparc engine
│   ├── SystemZ     <- SystemZ engine
│   ├── X86         <- X86 engine
│   └── XCore       <- XCore engine
├── bindings        <- all bindings are under this dir
│   ├── java        <- Java bindings + test code
│   ├── ocaml       <- Ocaml bindings + test code
│   └── python      <- Python bindings + test code
├── contrib         <- Code contributed by community to help Capstone integration
├── docs            <- Documentation
├── include         <- API headers in C language (*.h)
├── msvc            <- Microsoft Visual Studio support (for Windows compile)
├── packages        <- Packages for Linux/OSX/BSD.
├── windows         <- Windows support (for Windows kernel driver compile)
├── suite           <- Development test tools - for Capstone developers only
├── tests           <- Test code (in C language)
└── xcode           <- Xcode support (for MacOSX compile)


Follow instructions in COMPILE.TXT for how to compile and run test code.

Note: if you find some strange bugs, it is recommended to firstly clean
the code and try to recompile/reinstall again. This can be done with:

	$ ./make.sh
	$ sudo ./make.sh install

At the same time, for Java/Ocaml/Python bindings, be sure to always use
the bindings coming with the core to avoid potential incompatibility issue
with older versions.
See bindings/<language>/README for detail instructions on how to compile &
install the bindings.
