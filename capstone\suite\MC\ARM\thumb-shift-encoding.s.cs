# CS_ARCH_ARM, CS_MODE_THUMB, 
0x6e,0xeb,0x00,0x0c = sbc.w r12, lr, r0
0x68,0xeb,0x19,0x01 = sbc.w r1, r8, r9, lsr #32
0x67,0xeb,0x1f,0x42 = sbc.w r2, r7, pc, lsr #16
0x66,0xeb,0x0a,0x03 = sbc.w r3, r6, r10
0x65,0xeb,0x0e,0x44 = sbc.w r4, r5, lr, lsl #16
0x64,0xeb,0x2b,0x05 = sbc.w r5, r4, r11, asr #32
0x63,0xeb,0x2d,0x46 = sbc.w r6, r3, sp, asr #16
0x62,0xeb,0x3c,0x07 = sbc.w r7, r2, r12, rrx
0x61,0xeb,0x30,0x48 = sbc.w r8, r1, r0, ror #16
0x0e,0xea,0x00,0x0c = and.w r12, lr, r0
0x08,0xea,0x19,0x01 = and.w r1, r8, r9, lsr #32
0x07,0xea,0x1f,0x42 = and.w r2, r7, pc, lsr #16
0x06,0xea,0x0a,0x03 = and.w r3, r6, r10
0x05,0xea,0x0e,0x44 = and.w r4, r5, lr, lsl #16
0x04,0xea,0x2b,0x05 = and.w r5, r4, r11, asr #32
0x03,0xea,0x2d,0x46 = and.w r6, r3, sp, asr #16
0x02,0xea,0x3c,0x07 = and.w r7, r2, r12, rrx
0x01,0xea,0x30,0x48 = and.w r8, r1, r0, ror #16
