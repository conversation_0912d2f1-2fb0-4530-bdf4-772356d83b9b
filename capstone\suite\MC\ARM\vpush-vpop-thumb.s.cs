# CS_ARCH_ARM, CS_MODE_THUMB, 
0x2d,0xed,0x0a,0x8b = vpush {d8, d9, d10, d11, d12}
0x2d,0xed,0x05,0x4a = vpush {s8, s9, s10, s11, s12}
0xbd,0xec,0x0a,0x8b = vpop {d8, d9, d10, d11, d12}
0xbd,0xec,0x05,0x4a = vpop {s8, s9, s10, s11, s12}
0x2d,0xed,0x0a,0x8b = vpush {d8, d9, d10, d11, d12}
0x2d,0xed,0x05,0x4a = vpush {s8, s9, s10, s11, s12}
0xbd,0xec,0x0a,0x8b = vpop {d8, d9, d10, d11, d12}
0xbd,0xec,0x05,0x4a = vpop {s8, s9, s10, s11, s12}
