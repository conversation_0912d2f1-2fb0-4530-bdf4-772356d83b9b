// Copyright (c) 2025, Ddi<PERSON>on EPT Process Hiding. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Implements EPT-based process hiding functionality.

// Phase 5: Real EPT Hook implementation
#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "process_hiding.h"

// Forward declarations to avoid header conflicts
struct ShadowHookTarget {
    UNICODE_STRING target_name;
    void* handler;
    void* original_call;
};

// External function declarations
extern "C" bool ShInstallHook(SharedShadowHookData* shared_sh_data, void* address, ShadowHookTarget* target);
extern "C" NTSTATUS ShEnableHooks(void);
extern "C" NTSTATUS ShDisableHooks(void);

extern "C" {

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding EPT Hook implementation
//

// Global process hiding hook state
static volatile BOOLEAN g_ProcessHidingHooksActive = FALSE;
static PVOID g_NtQuerySystemInformationOriginal = nullptr;
static ShadowHookTarget g_NtQuerySystemInformationTarget = {};

// Shared data pointer (passed from DdiMon initialization)
static SharedShadowHookData* g_ProcessHidingSharedData = nullptr;

// Hook function prototypes
typedef NTSTATUS (NTAPI *PFN_NT_QUERY_SYSTEM_INFORMATION)(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
);

// Forward declarations
static NTSTATUS ProcessHidingpFindKernelFunctions(void);
static NTSTATUS ProcessHidingpInstallHooks(void);
static VOID ProcessHidingpRemoveHooks(void);
static BOOLEAN ProcessHidingpShouldHideProcess(ULONG ProcessId);
static NTSTATUS ProcessHidingpFilterProcessList(PVOID SystemInformation, ULONG Length);

static NTSTATUS NTAPI HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
);

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding initialization and cleanup
//

// Set shared shadow hook data (called from DdiMon initialization)
NTSTATUS ProcessHidingSetSharedData(SharedShadowHookData* shared_data) {
    HYPERPLATFORM_LOG_INFO("=== ProcessHidingSetSharedData Entry ===");
    HYPERPLATFORM_LOG_INFO("Received shared data pointer: 0x%p", shared_data);

    if (!shared_data) {
        HYPERPLATFORM_LOG_ERROR("CRITICAL: Received NULL shared data pointer!");
        return STATUS_INVALID_PARAMETER;
    }

    g_ProcessHidingSharedData = shared_data;
    HYPERPLATFORM_LOG_INFO("Process hiding shared data set successfully to 0x%p", shared_data);
    HYPERPLATFORM_LOG_INFO("=== ProcessHidingSetSharedData Success ===");
    return STATUS_SUCCESS;
}

// Initialize process hiding hooks - Phase 5 implementation
NTSTATUS ProcessHidingInitialize(void) {
    HYPERPLATFORM_LOG_INFO("=== Process Hiding Phase 5 Initialization Starting ===");
    HYPERPLATFORM_LOG_INFO("Initializing process hiding hooks (Phase 5 - Real EPT Hooks)...");

    if (g_ProcessHidingHooksActive) {
        HYPERPLATFORM_LOG_INFO("Process hiding hooks already active - skipping initialization");
        return STATUS_SUCCESS;
    }

    // Check if shared data is available
    HYPERPLATFORM_LOG_INFO("Checking shared data availability...");
    HYPERPLATFORM_LOG_INFO("Shared data pointer: 0x%p", g_ProcessHidingSharedData);
    if (!g_ProcessHidingSharedData) {
        HYPERPLATFORM_LOG_ERROR("CRITICAL: Shared shadow hook data not available!");
        return STATUS_UNSUCCESSFUL;
    }
    HYPERPLATFORM_LOG_INFO("Shared data check passed");

    // Find kernel functions
    HYPERPLATFORM_LOG_INFO("Phase 5 Step A: Finding kernel functions...");
    NTSTATUS status = ProcessHidingpFindKernelFunctions();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("FAILED Step A: Failed to find kernel functions: 0x%08X", status);
        return status;
    }
    HYPERPLATFORM_LOG_INFO("SUCCESS Step A: Kernel functions found");

    // Install EPT hooks
    HYPERPLATFORM_LOG_INFO("Phase 5 Step B: Installing EPT hooks...");
    status = ProcessHidingpInstallHooks();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("FAILED Step B: Failed to install process hiding hooks: 0x%08X", status);
        return status;
    }
    HYPERPLATFORM_LOG_INFO("SUCCESS Step B: EPT hooks installed");

    g_ProcessHidingHooksActive = TRUE;
    HYPERPLATFORM_LOG_INFO("=== Process Hiding Phase 5 Initialization Completed Successfully ===");
    HYPERPLATFORM_LOG_INFO("Process hiding hooks initialized successfully with real EPT hooks");
    return STATUS_SUCCESS;
}

// Cleanup process hiding hooks
VOID ProcessHidingTerminate(void) {
    if (!g_ProcessHidingHooksActive) {
        return;
    }

    HYPERPLATFORM_LOG_INFO("Terminating process hiding hooks...");

    // Remove EPT hooks
    ProcessHidingpRemoveHooks();

    g_ProcessHidingHooksActive = FALSE;
    HYPERPLATFORM_LOG_INFO("Process hiding hooks terminated");
}

// Check if process hiding hooks are active
BOOLEAN ProcessHidingIsActive(void) {
    return g_ProcessHidingHooksActive;
}

////////////////////////////////////////////////////////////////////////////////
//
// Kernel function discovery
//

// Find required kernel functions
static NTSTATUS ProcessHidingpFindKernelFunctions(void) {
    HYPERPLATFORM_LOG_INFO("=== Kernel Function Discovery Starting ===");
    HYPERPLATFORM_LOG_INFO("Finding kernel functions for process hiding...");

    // Get NtQuerySystemInformation address
    HYPERPLATFORM_LOG_INFO("Looking up NtQuerySystemInformation...");
    UNICODE_STRING ntQuerySystemInformationName;
    RtlInitUnicodeString(&ntQuerySystemInformationName, L"NtQuerySystemInformation");

    g_NtQuerySystemInformationOriginal = MmGetSystemRoutineAddress(&ntQuerySystemInformationName);
    if (!g_NtQuerySystemInformationOriginal) {
        HYPERPLATFORM_LOG_ERROR("CRITICAL: Failed to find NtQuerySystemInformation via MmGetSystemRoutineAddress");
        return STATUS_PROCEDURE_NOT_FOUND;
    }

    HYPERPLATFORM_LOG_INFO("SUCCESS: Found NtQuerySystemInformation at 0x%p", g_NtQuerySystemInformationOriginal);
    HYPERPLATFORM_LOG_INFO("=== Kernel Function Discovery Completed ===");
    return STATUS_SUCCESS;
}

////////////////////////////////////////////////////////////////////////////////
//
// EPT Hook installation and removal (Simplified approach)
//

// Install EPT hooks for process hiding
static NTSTATUS ProcessHidingpInstallHooks(void) {
    HYPERPLATFORM_LOG_INFO("=== EPT Hook Installation Phase 5 Starting ===");
    HYPERPLATFORM_LOG_INFO("Installing EPT hooks for process hiding (Phase 5 - Real Hook Installation)...");

    // Phase 5: 真正安装EPT Hook
    HYPERPLATFORM_LOG_INFO("Hook Install Step 1: Verifying shared data...");
    if (!g_ProcessHidingSharedData) {
        HYPERPLATFORM_LOG_ERROR("CRITICAL: Shared shadow hook data not available in hook installation");
        return STATUS_UNSUCCESSFUL;
    }
    HYPERPLATFORM_LOG_INFO("Shared data verified: 0x%p", g_ProcessHidingSharedData);

    // 设置Hook目标
    HYPERPLATFORM_LOG_INFO("Hook Install Step 2: Setting up hook target...");
    RtlInitUnicodeString(&g_NtQuerySystemInformationTarget.target_name, L"NtQuerySystemInformation");
    g_NtQuerySystemInformationTarget.handler = HookedNtQuerySystemInformation;
    g_NtQuerySystemInformationTarget.original_call = nullptr;

    HYPERPLATFORM_LOG_INFO("Hook target configured:");
    HYPERPLATFORM_LOG_INFO("  Target function: NtQuerySystemInformation at 0x%p", g_NtQuerySystemInformationOriginal);
    HYPERPLATFORM_LOG_INFO("  Hook handler: 0x%p", HookedNtQuerySystemInformation);

    // 安装真正的EPT Hook
    HYPERPLATFORM_LOG_INFO("Hook Install Step 3: Installing EPT hook...");
    HYPERPLATFORM_LOG_INFO("About to call ShInstallHook with:");
    HYPERPLATFORM_LOG_INFO("  shared_data: 0x%p", g_ProcessHidingSharedData);
    HYPERPLATFORM_LOG_INFO("  target_address: 0x%p", g_NtQuerySystemInformationOriginal);
    HYPERPLATFORM_LOG_INFO("  target_struct: 0x%p", &g_NtQuerySystemInformationTarget);

    // 添加安全检查
    if (!g_ProcessHidingSharedData || !g_NtQuerySystemInformationOriginal) {
        HYPERPLATFORM_LOG_ERROR("CRITICAL: Invalid parameters for ShInstallHook");
        return STATUS_UNSUCCESSFUL;
    }

    // Phase 6: Attempt safe hook installation with enhanced validation
    HYPERPLATFORM_LOG_INFO("Phase 6: Attempting safe ShInstallHook call with enhanced validation...");

    // Additional safety checks before calling ShInstallHook
    HYPERPLATFORM_LOG_INFO("Performing enhanced safety checks...");

    // Check if target address is in valid kernel range
    if (!MmIsAddressValid(g_NtQuerySystemInformationOriginal)) {
        HYPERPLATFORM_LOG_ERROR("CRITICAL: Target address is not valid for memory access");
        return STATUS_UNSUCCESSFUL;
    }

    // Check if target address is properly aligned
    if ((ULONG_PTR)g_NtQuerySystemInformationOriginal & 0xF) {
        HYPERPLATFORM_LOG_INFO("WARNING: Target address is not 16-byte aligned, but proceeding");
    }

    HYPERPLATFORM_LOG_INFO("Enhanced safety checks passed, calling ShInstallHook...");

    // Try the actual hook installation
    bool hook_result = false;
    __try {
        hook_result = ShInstallHook(g_ProcessHidingSharedData, g_NtQuerySystemInformationOriginal, &g_NtQuerySystemInformationTarget);
        HYPERPLATFORM_LOG_INFO("ShInstallHook completed successfully, returned: %s", hook_result ? "TRUE" : "FALSE");
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        HYPERPLATFORM_LOG_ERROR("EXCEPTION: ShInstallHook caused an exception: 0x%08X", GetExceptionCode());
        return STATUS_UNSUCCESSFUL;
    }

    if (!hook_result) {
        HYPERPLATFORM_LOG_ERROR("FAILED: ShInstallHook returned FALSE for NtQuerySystemInformation");
        return STATUS_UNSUCCESSFUL;
    }

    HYPERPLATFORM_LOG_INFO("SUCCESS: NtQuerySystemInformation EPT hook installed successfully");
    HYPERPLATFORM_LOG_INFO("Hook details after installation:");
    HYPERPLATFORM_LOG_INFO("  Handler: 0x%p", g_NtQuerySystemInformationTarget.handler);
    HYPERPLATFORM_LOG_INFO("  Original call: 0x%p", g_NtQuerySystemInformationTarget.original_call);

    // 启用Hook
    HYPERPLATFORM_LOG_INFO("Hook Install Step 4: Enabling all shadow hooks...");

    // Phase 6: Attempt safe hook activation
    NTSTATUS status = STATUS_SUCCESS;
    if (hook_result) {
        HYPERPLATFORM_LOG_INFO("Phase 6: Attempting safe ShEnableHooks call...");
        __try {
            status = ShEnableHooks();
            HYPERPLATFORM_LOG_INFO("ShEnableHooks completed, returned status: 0x%08X", status);
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            HYPERPLATFORM_LOG_ERROR("EXCEPTION: ShEnableHooks caused an exception: 0x%08X", GetExceptionCode());
            status = STATUS_UNSUCCESSFUL;
        }
    } else {
        HYPERPLATFORM_LOG_ERROR("Skipping ShEnableHooks because ShInstallHook failed");
        status = STATUS_UNSUCCESSFUL;
    }

    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("FAILED: ShEnableHooks failed with status: 0x%08X", status);
        return status;
    }

    HYPERPLATFORM_LOG_INFO("SUCCESS: Shadow hooks enabled successfully");
    HYPERPLATFORM_LOG_INFO("=== EPT Hook Installation Phase 5 Completed Successfully ===");
    HYPERPLATFORM_LOG_INFO("Process hiding hooks installation completed (Phase 5 - Real Hooks Active)");
    return STATUS_SUCCESS;
}

// Remove EPT hooks
static VOID ProcessHidingpRemoveHooks(void) {
    HYPERPLATFORM_LOG_INFO("Removing process hiding EPT hooks...");

    if (g_ProcessHidingHooksActive) {
        // 禁用所有Hook
        NTSTATUS status = ShDisableHooks();
        if (NT_SUCCESS(status)) {
            HYPERPLATFORM_LOG_INFO("Shadow hooks disabled successfully");
        } else {
            HYPERPLATFORM_LOG_ERROR("Failed to disable shadow hooks: 0x%08X", status);
        }
    }

    HYPERPLATFORM_LOG_INFO("Process hiding hooks removal completed (Phase 5 - Real Hooks Disabled)");
}

////////////////////////////////////////////////////////////////////////////////
//
// Hook function implementations (Prepared for future use)
//

// Hooked NtQuerySystemInformation function
static NTSTATUS NTAPI HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
) {
    HYPERPLATFORM_LOG_DEBUG("HookedNtQuerySystemInformation called - Class: %lu", SystemInformationClass);

    // Call original function first
    auto original_func = reinterpret_cast<PFN_NT_QUERY_SYSTEM_INFORMATION>(g_NtQuerySystemInformationOriginal);
    NTSTATUS status = original_func(SystemInformationClass, SystemInformation, SystemInformationLength, ReturnLength);

    // Only filter SystemProcessInformation (class 5)
    if (NT_SUCCESS(status) && SystemInformationClass == 5 && SystemInformation) {
        __try {
            // Filter the process list to hide our processes
            ProcessHidingpFilterProcessList(SystemInformation, SystemInformationLength);
        } __except(EXCEPTION_EXECUTE_HANDLER) {
            HYPERPLATFORM_LOG_ERROR("Exception in process list filtering: 0x%08X", GetExceptionCode());
        }
    }

    return status;
}

////////////////////////////////////////////////////////////////////////////////
//
// Helper functions
//

// Check if a process should be hidden
static BOOLEAN ProcessHidingpShouldHideProcess(ULONG ProcessId) {
    // Use the existing function from ddi_mon_ioctl.cpp
    extern BOOLEAN DdimonpIsProcessHidden(ULONG ProcessId);
    return DdimonpIsProcessHidden(ProcessId);
}

// Filter process list to hide specified processes
static NTSTATUS ProcessHidingpFilterProcessList(PVOID SystemInformation, ULONG Length) {
    UNREFERENCED_PARAMETER(Length);

    if (!SystemInformation) {
        return STATUS_INVALID_PARAMETER;
    }

    __try {
        HYPERPLATFORM_LOG_DEBUG("Process list filtering called - Phase 3 implementation");

        // Phase 3: Implement actual process list filtering
        PSYSTEM_PROCESS_INFORMATION current = (PSYSTEM_PROCESS_INFORMATION)SystemInformation;
        PSYSTEM_PROCESS_INFORMATION previous = nullptr;
        ULONG hiddenCount = 0;

        while (current) {
            ULONG processId = (ULONG)(ULONG_PTR)current->UniqueProcessId;

            // Check if this process should be hidden
            if (ProcessHidingpShouldHideProcess(processId)) {
                HYPERPLATFORM_LOG_INFO("Filtering out hidden process PID: %lu", processId);
                hiddenCount++;

                if (previous) {
                    // Skip this entry by updating the previous entry's NextEntryOffset
                    if (current->NextEntryOffset == 0) {
                        // This is the last entry, terminate the list here
                        previous->NextEntryOffset = 0;
                        break;
                    } else {
                        // Point to the next entry after the hidden one
                        previous->NextEntryOffset += current->NextEntryOffset;
                        current = (PSYSTEM_PROCESS_INFORMATION)((PUCHAR)current + current->NextEntryOffset);
                        continue;
                    }
                } else {
                    // This is the first entry and it's hidden
                    if (current->NextEntryOffset == 0) {
                        // Only entry in the list, clear it
                        RtlZeroMemory(current, sizeof(SYSTEM_PROCESS_INFORMATION));
                        HYPERPLATFORM_LOG_INFO("Cleared entire process list (only hidden process)");
                        return STATUS_SUCCESS;
                    } else {
                        // Move the next entry to the beginning
                        PSYSTEM_PROCESS_INFORMATION next = (PSYSTEM_PROCESS_INFORMATION)
                            ((PUCHAR)current + current->NextEntryOffset);
                        ULONG moveSize = Length - current->NextEntryOffset;

                        RtlMoveMemory(current, next, moveSize);
                        continue; // Don't advance current, check this position again
                    }
                }
            } else {
                // This process is not hidden, keep it
                previous = current;
            }

            // Move to next entry
            if (current->NextEntryOffset == 0) {
                break;
            }
            current = (PSYSTEM_PROCESS_INFORMATION)((PUCHAR)current + current->NextEntryOffset);
        }

        if (hiddenCount > 0) {
            HYPERPLATFORM_LOG_INFO("Process list filtering completed - %lu processes hidden", hiddenCount);
        }

    } __except(EXCEPTION_EXECUTE_HANDLER) {
        HYPERPLATFORM_LOG_ERROR("Exception in ProcessHidingpFilterProcessList: 0x%08X", GetExceptionCode());
        return STATUS_UNSUCCESSFUL;
    }

    return STATUS_SUCCESS;
}

}  // extern "C"
