// Copyright (c) 2025, DdiMon EPT Process Hiding. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Implements EPT-based process hiding functionality.

#include "../HyperPlatform/HyperPlatform/common.h"
#include "../HyperPlatform/HyperPlatform/log.h"
#include "../HyperPlatform/HyperPlatform/util.h"
#include "../HyperPlatform/HyperPlatform/ept.h"
#include "shadow_hook.h"
#include "ddi_mon_ioctl.h"
#include "process_hiding.h"
#include <ntimage.h>
#include <intrin.h>

extern "C" {

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding EPT Hook implementation
//

// Global process hiding hook state
static volatile BOOLEAN g_ProcessHidingHooksActive = FALSE;
static PVOID g_NtQuerySystemInformationOriginal = nullptr;
static PVOID g_PsGetNextProcessOriginal = nullptr;

// Hook function prototypes
typedef NTSTATUS (NTAPI *PFN_NT_QUERY_SYSTEM_INFORMATION)(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
);

typedef PEPROCESS (NTAPI *PFN_PS_GET_NEXT_PROCESS)(
    PEPROCESS Process
);

// Forward declarations
static NTSTATUS NTAPI HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
);

static PEPROCESS NTAPI HookedPsGetNextProcess(
    PEPROCESS Process
);

static NTSTATUS ProcessHidingpFindKernelFunctions(void);
static NTSTATUS ProcessHidingpInstallHooks(void);
static VOID ProcessHidingpRemoveHooks(void);
static BOOLEAN ProcessHidingpShouldHideProcess(ULONG ProcessId);
static NTSTATUS ProcessHidingpFilterProcessList(PVOID SystemInformation, ULONG Length);

////////////////////////////////////////////////////////////////////////////////
//
// Process hiding initialization and cleanup
//

// Initialize process hiding hooks
NTSTATUS ProcessHidingInitialize(void) {
    HYPERPLATFORM_LOG_INFO("Initializing process hiding hooks...");

    if (g_ProcessHidingHooksActive) {
        HYPERPLATFORM_LOG_INFO("Process hiding hooks already active");
        return STATUS_SUCCESS;
    }

    // Find kernel functions
    NTSTATUS status = ProcessHidingpFindKernelFunctions();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("Failed to find kernel functions: 0x%08X", status);
        return status;
    }

    // Install EPT hooks
    status = ProcessHidingpInstallHooks();
    if (!NT_SUCCESS(status)) {
        HYPERPLATFORM_LOG_ERROR("Failed to install process hiding hooks: 0x%08X", status);
        return status;
    }

    g_ProcessHidingHooksActive = TRUE;
    HYPERPLATFORM_LOG_INFO("Process hiding hooks initialized successfully");
    return STATUS_SUCCESS;
}

// Cleanup process hiding hooks
VOID ProcessHidingTerminate(void) {
    if (!g_ProcessHidingHooksActive) {
        return;
    }

    HYPERPLATFORM_LOG_INFO("Terminating process hiding hooks...");

    // Remove EPT hooks
    ProcessHidingpRemoveHooks();

    g_ProcessHidingHooksActive = FALSE;
    HYPERPLATFORM_LOG_INFO("Process hiding hooks terminated");
}

// Check if process hiding hooks are active
BOOLEAN ProcessHidingIsActive(void) {
    return g_ProcessHidingHooksActive;
}

////////////////////////////////////////////////////////////////////////////////
//
// Kernel function discovery
//

// Find required kernel functions
static NTSTATUS ProcessHidingpFindKernelFunctions(void) {
    HYPERPLATFORM_LOG_INFO("Finding kernel functions for process hiding...");

    // Get NtQuerySystemInformation address
    UNICODE_STRING ntQuerySystemInformationName;
    RtlInitUnicodeString(&ntQuerySystemInformationName, L"NtQuerySystemInformation");
    
    g_NtQuerySystemInformationOriginal = MmGetSystemRoutineAddress(&ntQuerySystemInformationName);
    if (!g_NtQuerySystemInformationOriginal) {
        HYPERPLATFORM_LOG_ERROR("Failed to find NtQuerySystemInformation");
        return STATUS_PROCEDURE_NOT_FOUND;
    }

    HYPERPLATFORM_LOG_INFO("Found NtQuerySystemInformation at 0x%p", g_NtQuerySystemInformationOriginal);

    // Get PsGetNextProcess address
    UNICODE_STRING psGetNextProcessName;
    RtlInitUnicodeString(&psGetNextProcessName, L"PsGetNextProcess");
    
    g_PsGetNextProcessOriginal = MmGetSystemRoutineAddress(&psGetNextProcessName);
    if (!g_PsGetNextProcessOriginal) {
        HYPERPLATFORM_LOG_ERROR("Failed to find PsGetNextProcess");
        return STATUS_PROCEDURE_NOT_FOUND;
    }

    HYPERPLATFORM_LOG_INFO("Found PsGetNextProcess at 0x%p", g_PsGetNextProcessOriginal);

    return STATUS_SUCCESS;
}

////////////////////////////////////////////////////////////////////////////////
//
// EPT Hook installation and removal
//

// Install EPT hooks for process hiding
static NTSTATUS ProcessHidingpInstallHooks(void) {
    HYPERPLATFORM_LOG_INFO("Installing EPT hooks for process hiding...");

    // For now, we'll use a simplified approach without actual EPT hooks
    // This is a placeholder for future EPT hook implementation
    HYPERPLATFORM_LOG_INFO("Process hiding hooks installation completed (placeholder)");

    return STATUS_SUCCESS;
}

// Remove EPT hooks
static VOID ProcessHidingpRemoveHooks(void) {
    HYPERPLATFORM_LOG_INFO("Removing process hiding EPT hooks...");

    // Placeholder for future EPT hook removal implementation
    HYPERPLATFORM_LOG_INFO("Process hiding hooks removal completed (placeholder)");
}

////////////////////////////////////////////////////////////////////////////////
//
// Hook function implementations (placeholder for future implementation)
//

// TODO: Implement actual EPT hooks for process hiding
// For now, these are placeholders that will be implemented in future versions

/*
// Hooked NtQuerySystemInformation function
static NTSTATUS NTAPI HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
) {
    // Future implementation will filter process information here
    return STATUS_SUCCESS;
}

// Hooked PsGetNextProcess function
static PEPROCESS NTAPI HookedPsGetNextProcess(
    PEPROCESS Process
) {
    // Future implementation will skip hidden processes here
    return nullptr;
}
*/

////////////////////////////////////////////////////////////////////////////////
//
// Helper functions (placeholder for future implementation)
//

// Check if a process should be hidden
static BOOLEAN ProcessHidingpShouldHideProcess(ULONG ProcessId) {
    // Use the existing function from ddi_mon_ioctl.cpp
    extern BOOLEAN DdimonpIsProcessHidden(ULONG ProcessId);
    return DdimonpIsProcessHidden(ProcessId);
}

// TODO: Implement actual process list filtering
// This will be implemented when EPT hooks are added
static NTSTATUS ProcessHidingpFilterProcessList(PVOID SystemInformation, ULONG Length) {
    UNREFERENCED_PARAMETER(SystemInformation);
    UNREFERENCED_PARAMETER(Length);

    // Placeholder - future implementation will filter the process list
    return STATUS_SUCCESS;
}

}  // extern "C"
