/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014 */

#include <stdio.h>	// debug
#include <platform.h>

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    9032U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    9025U,	// BUNDLE
    9323U,	// LIFETIME_START
    9012U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    18692U,	// ADD4
    18692U,	// ADD4TLS
    16794U,	// ADD4o
    18692U,	// ADD8
    18692U,	// ADD8TLS
    18692U,	// ADD8TLS_
    16794U,	// ADD8o
    18547U,	// ADDC
    18547U,	// ADDC8
    16734U,	// ADDC8o
    16734U,	// ADDCo
    18865U,	// ADDE
    18865U,	// ADDE8
    16884U,	// ADDE8o
    16884U,	// ADDEo
    2147503046U,	// ADDI
    2147503046U,	// ADDI8
    2147502240U,	// ADDIC
    2147502240U,	// ADDIC8
    2147500412U,	// ADDICo
    2147505661U,	// ADDIS
    2147505661U,	// ADDIS8
    8958U,	// ADDISdtprelHA
    7931U,	// ADDISdtprelHA32
    8941U,	// ADDISgotTprelHA
    8913U,	// ADDIStlsgdHA
    8927U,	// ADDIStlsldHA
    8901U,	// ADDIStocHA
    9137U,	// ADDIdtprelL
    8134U,	// ADDIdtprelL32
    9100U,	// ADDItlsgdL
    8091U,	// ADDItlsgdL32
    9112U,	// ADDItlsldL
    8105U,	// ADDItlsldL32
    9090U,	// ADDItocL
    134236653U,	// ADDME
    134236653U,	// ADDME8
    134234627U,	// ADDME8o
    134234627U,	// ADDMEo
    134236713U,	// ADDZE
    134236713U,	// ADDZE8
    134234660U,	// ADDZE8o
    134234660U,	// ADDZEo
    296418U,	// ADJCALLSTACKDOWN
    8947189U,	// ADJCALLSTACKUP
    18788U,	// AND
    18788U,	// AND8
    16856U,	// AND8o
    18556U,	// ANDC
    18556U,	// ANDC8
    16741U,	// ANDC8o
    16741U,	// ANDCo
    17497U,	// ANDISo
    17497U,	// ANDISo8
    17032U,	// ANDIo
    17032U,	// ANDIo8
    9258U,	// ANDIo_1_EQ_BIT
    8851U,	// ANDIo_1_EQ_BIT8
    9274U,	// ANDIo_1_GT_BIT
    8868U,	// ANDIo_1_GT_BIT8
    16856U,	// ANDo
    285754771U,	// ATOMIC_CMP_SWAP_I16
    285754749U,	// ATOMIC_CMP_SWAP_I32
    8297U,	// ATOMIC_CMP_SWAP_I64
    8769U,	// ATOMIC_CMP_SWAP_I8
    8516U,	// ATOMIC_LOAD_ADD_I16
    7969U,	// ATOMIC_LOAD_ADD_I32
    8237U,	// ATOMIC_LOAD_ADD_I64
    8708U,	// ATOMIC_LOAD_ADD_I8
    8559U,	// ATOMIC_LOAD_AND_I16
    8012U,	// ATOMIC_LOAD_AND_I32
    8396U,	// ATOMIC_LOAD_AND_I64
    8749U,	// ATOMIC_LOAD_AND_I8
    8537U,	// ATOMIC_LOAD_NAND_I16
    7990U,	// ATOMIC_LOAD_NAND_I32
    8258U,	// ATOMIC_LOAD_NAND_I64
    8728U,	// ATOMIC_LOAD_NAND_I8
    8618U,	// ATOMIC_LOAD_OR_I16
    8071U,	// ATOMIC_LOAD_OR_I32
    8339U,	// ATOMIC_LOAD_OR_I64
    8808U,	// ATOMIC_LOAD_OR_I8
    8495U,	// ATOMIC_LOAD_SUB_I16
    7948U,	// ATOMIC_LOAD_SUB_I32
    8216U,	// ATOMIC_LOAD_SUB_I64
    8674U,	// ATOMIC_LOAD_SUB_I8
    8597U,	// ATOMIC_LOAD_XOR_I16
    8050U,	// ATOMIC_LOAD_XOR_I32
    8318U,	// ATOMIC_LOAD_XOR_I64
    8789U,	// ATOMIC_LOAD_XOR_I8
    8580U,	// ATOMIC_SWAP_I16
    8033U,	// ATOMIC_SWAP_I32
    8280U,	// ATOMIC_SWAP_I64
    8885U,	// ATOMIC_SWAP_I8
    313227U,	// B
    329359U,	// BA
    25182305U,	// BC
    877702U,	// BCC
    1139846U,	// BCCA
    1401990U,	// BCCCTR
    1401990U,	// BCCCTR8
    1664134U,	// BCCCTRL
    1664134U,	// BCCCTRL8
    1926278U,	// BCCL
    2188422U,	// BCCLA
    2450566U,	// BCCLR
    2712710U,	// BCCLRL
    2900115U,	// BCCTR
    2900115U,	// BCCTR8
    2900171U,	// BCCTR8n
    2900093U,	// BCCTRL
    2900093U,	// BCCTRL8
    2900151U,	// BCCTRL8n
    2900151U,	// BCCTRLn
    2900171U,	// BCCTRn
    25182313U,	// BCL
    2900105U,	// BCLR
    2900082U,	// BCLRL
    2900141U,	// BCLRLn
    2900162U,	// BCLRn
    311373U,	// BCLalways
    25182373U,	// BCLn
    9489U,	// BCTR
    9489U,	// BCTR8
    9451U,	// BCTRL
    9451U,	// BCTRL8
    25182366U,	// BCn
    319102U,	// BDNZ
    319102U,	// BDNZ8
    329600U,	// BDNZA
    327929U,	// BDNZAm
    327721U,	// BDNZAp
    314838U,	// BDNZL
    329574U,	// BDNZLA
    327913U,	// BDNZLAm
    327705U,	// BDNZLAp
    9482U,	// BDNZLR
    9482U,	// BDNZLR8
    9443U,	// BDNZLRL
    7883U,	// BDNZLRLm
    7851U,	// BDNZLRLp
    7899U,	// BDNZLRm
    7867U,	// BDNZLRp
    311560U,	// BDNZLm
    311352U,	// BDNZLp
    311574U,	// BDNZm
    311366U,	// BDNZp
    319044U,	// BDZ
    319044U,	// BDZ8
    329594U,	// BDZA
    327922U,	// BDZAm
    327714U,	// BDZAp
    314832U,	// BDZL
    329567U,	// BDZLA
    327905U,	// BDZLAm
    327697U,	// BDZLAp
    9476U,	// BDZLR
    9476U,	// BDZLR8
    9436U,	// BDZLRL
    7875U,	// BDZLRLm
    7843U,	// BDZLRLp
    7892U,	// BDZLRm
    7860U,	// BDZLRp
    311553U,	// BDZLm
    311345U,	// BDZLp
    311568U,	// BDZm
    311360U,	// BDZp
    314728U,	// BL
    314728U,	// BL8
    3198312U,	// BL8_NOP
    3247464U,	// BL8_NOP_TLS
    363880U,	// BL8_TLS
    363880U,	// BL8_TLS_
    329556U,	// BLA
    329556U,	// BLA8
    3213140U,	// BLA8_NOP
    9472U,	// BLR
    9431U,	// BLRL
    363880U,	// BL_TLS
    18641U,	// BRINC
    2147503060U,	// CLRLSLDI
    2147500660U,	// CLRLSLDIo
    19725U,	// CLRLSLWI
    17101U,	// CLRLSLWIo
    2147503095U,	// CLRRDI
    2147500687U,	// CLRRDIo
    19766U,	// CLRRWI
    17130U,	// CLRRWIo
    18823U,	// CMPD
    2147503088U,	// CMPDI
    18773U,	// CMPLD
    19404U,	// CMPLDI
    23074U,	// CMPLW
    19709U,	// CMPLWI
    23314U,	// CMPW
    2147503407U,	// CMPWI
    134236585U,	// CNTLZD
    134234603U,	// CNTLZDo
    134241216U,	// CNTLZW
    134235423U,	// CNTLZWo
    8481U,	// CR6SET
    8467U,	// CR6UNSET
    18809U,	// CRAND
    18562U,	// CRANDC
    22623U,	// CREQV
    18793U,	// CRNAND
    21565U,	// CRNOR
    21579U,	// CROR
    18654U,	// CRORC
    2181060703U,	// CRSET
    2181059679U,	// CRUNSET
    21599U,	// CRXOR
    116365U,	// DCBA
    117304U,	// DCBF
    117663U,	// DCBI
    120708U,	// DCBST
    120689U,	// DCBT
    120720U,	// DCBTST
    122425U,	// DCBZ
    118217U,	// DCBZL
    134237105U,	// DCCCI
    18851U,	// DIVD
    22497U,	// DIVDU
    17566U,	// DIVDUo
    16868U,	// DIVDo
    23480U,	// DIVW
    22592U,	// DIVWU
    17583U,	// DIVWUo
    17688U,	// DIVWo
    398889U,	// DSS
    9424U,	// DSSALL
    444749707U,	// DST
    444749707U,	// DST64
    444749720U,	// DSTST
    444749720U,	// DSTST64
    444749733U,	// DSTSTT
    444749733U,	// DSTSTT64
    444749727U,	// DSTT
    444749727U,	// DSTT64
    8973U,	// DYNALLOC
    8638U,	// DYNALLOC8
    8149U,	// EH_SjLj_LongJmp32
    8359U,	// EH_SjLj_LongJmp64
    8168U,	// EH_SjLj_SetJmp32
    8378U,	// EH_SjLj_SetJmp64
    311297U,	// EH_SjLj_Setup
    9457U,	// EIEIO
    22625U,	// EQV
    22625U,	// EQV8
    17598U,	// EQV8o
    17598U,	// EQVo
    134239421U,	// EVABS
    50354682U,	// EVADDIW
    134240417U,	// EVADDSMIAAW
    134240549U,	// EVADDSSIAAW
    134240483U,	// EVADDUMIAAW
    134240615U,	// EVADDUSIAAW
    22960U,	// EVADDW
    18816U,	// EVAND
    18570U,	// EVANDC
    21448U,	// EVCMPEQ
    22080U,	// EVCMPGTS
    22548U,	// EVCMPGTU
    22090U,	// EVCMPLTS
    22558U,	// EVCMPLTU
    134241072U,	// EVCNTLSW
    134241214U,	// EVCNTLZW
    22223U,	// EVDIVWS
    22590U,	// EVDIVWU
    22630U,	// EVEQV
    134236152U,	// EVEXTSB
    134236995U,	// EVEXTSH
    58738968U,	// EVLDD
    23580U,	// EVLDDX
    58739402U,	// EVLDH
    23665U,	// EVLDHX
    58743224U,	// EVLDW
    24049U,	// EVLDWX
    58742576U,	// EVLHHESPLAT
    23852U,	// EVLHHESPLATX
    58742601U,	// EVLHHOSSPLAT
    23879U,	// EVLHHOSSPLATX
    58742615U,	// EVLHHOUSPLAT
    23894U,	// EVLHHOUSPLATX
    58739150U,	// EVLWHE
    23636U,	// EVLWHEX
    58742304U,	// EVLWHOS
    23842U,	// EVLWHOSX
    58742775U,	// EVLWHOU
    23985U,	// EVLWHOUX
    58742589U,	// EVLWHSPLAT
    23866U,	// EVLWHSPLATX
    58742629U,	// EVLWWSPLAT
    23909U,	// EVLWWSPLATX
    19503U,	// EVMERGEHI
    20297U,	// EVMERGEHILO
    20286U,	// EVMERGELO
    19514U,	// EVMERGELOHI
    17939U,	// EVMHEGSMFAA
    20118U,	// EVMHEGSMFAN
    17987U,	// EVMHEGSMIAA
    20166U,	// EVMHEGSMIAN
    18024U,	// EVMHEGUMIAA
    20203U,	// EVMHEGUMIAN
    19012U,	// EVMHESMF
    18072U,	// EVMHESMFA
    22637U,	// EVMHESMFAAW
    23106U,	// EVMHESMFANW
    19557U,	// EVMHESMI
    18163U,	// EVMHESMIA
    22702U,	// EVMHESMIAAW
    23158U,	// EVMHESMIANW
    19087U,	// EVMHESSF
    18115U,	// EVMHESSFA
    22663U,	// EVMHESSFAAW
    23132U,	// EVMHESSFANW
    22834U,	// EVMHESSIAAW
    23236U,	// EVMHESSIANW
    19596U,	// EVMHEUMI
    18206U,	// EVMHEUMIA
    22768U,	// EVMHEUMIAAW
    23197U,	// EVMHEUMIANW
    22900U,	// EVMHEUSIAAW
    23275U,	// EVMHEUSIANW
    17952U,	// EVMHOGSMFAA
    20131U,	// EVMHOGSMFAN
    18000U,	// EVMHOGSMIAA
    20179U,	// EVMHOGSMIAN
    18037U,	// EVMHOGUMIAA
    20216U,	// EVMHOGUMIAN
    19032U,	// EVMHOSMF
    18094U,	// EVMHOSMFA
    22650U,	// EVMHOSMFAAW
    23119U,	// EVMHOSMFANW
    19577U,	// EVMHOSMI
    18185U,	// EVMHOSMIA
    22742U,	// EVMHOSMIAAW
    23184U,	// EVMHOSMIANW
    19107U,	// EVMHOSSF
    18137U,	// EVMHOSSFA
    22676U,	// EVMHOSSFAAW
    23145U,	// EVMHOSSFANW
    22874U,	// EVMHOSSIAAW
    23262U,	// EVMHOSSIANW
    19626U,	// EVMHOUMI
    18239U,	// EVMHOUMIA
    22808U,	// EVMHOUMIAAW
    23223U,	// EVMHOUMIANW
    22940U,	// EVMHOUSIAAW
    23301U,	// EVMHOUSIANW
    134236014U,	// EVMRA
    19022U,	// EVMWHSMF
    18083U,	// EVMWHSMFA
    19567U,	// EVMWHSMI
    18174U,	// EVMWHSMIA
    19097U,	// EVMWHSSF
    18126U,	// EVMWHSSFA
    19606U,	// EVMWHUMI
    18217U,	// EVMWHUMIA
    22729U,	// EVMWLSMIAAW
    23171U,	// EVMWLSMIANW
    22861U,	// EVMWLSSIAAW
    23249U,	// EVMWLSSIANW
    19616U,	// EVMWLUMI
    18228U,	// EVMWLUMIA
    22795U,	// EVMWLUMIAAW
    23210U,	// EVMWLUMIANW
    22927U,	// EVMWLUSIAAW
    23288U,	// EVMWLUSIANW
    19042U,	// EVMWSMF
    18105U,	// EVMWSMFA
    17965U,	// EVMWSMFAA
    20144U,	// EVMWSMFAN
    19587U,	// EVMWSMI
    18196U,	// EVMWSMIA
    18013U,	// EVMWSMIAA
    20192U,	// EVMWSMIAN
    19117U,	// EVMWSSF
    18148U,	// EVMWSSFA
    17976U,	// EVMWSSFAA
    20155U,	// EVMWSSFAN
    19636U,	// EVMWUMI
    18250U,	// EVMWUMIA
    18050U,	// EVMWUMIAA
    20229U,	// EVMWUMIAN
    18801U,	// EVNAND
    134236860U,	// EVNEG
    21572U,	// EVNOR
    21585U,	// EVOR
    18661U,	// EVORC
    23081U,	// EVRLW
    19717U,	// EVRLWI
    134240703U,	// EVRNDW
    23088U,	// EVSLW
    19743U,	// EVSLWI
    134237220U,	// EVSPLATFI
    134237411U,	// EVSPLATI
    22032U,	// EVSRWIS
    22510U,	// EVSRWIU
    22160U,	// EVSRWS
    22576U,	// EVSRWU
    58738975U,	// EVSTDD
    23588U,	// EVSTDDX
    58739409U,	// EVSTDH
    23673U,	// EVSTDHX
    58743239U,	// EVSTDW
    24057U,	// EVSTDWX
    58739158U,	// EVSTWHE
    23645U,	// EVSTWHEX
    58740533U,	// EVSTWHO
    23703U,	// EVSTWHOX
    58739232U,	// EVSTWWE
    23655U,	// EVSTWWEX
    58740578U,	// EVSTWWO
    23713U,	// EVSTWWOX
    134240443U,	// EVSUBFSMIAAW
    134240575U,	// EVSUBFSSIAAW
    134240509U,	// EVSUBFUMIAAW
    134240641U,	// EVSUBFUSIAAW
    22991U,	// EVSUBFW
    67131864U,	// EVSUBIFW
    21606U,	// EVXOR
    2147503070U,	// EXTLDI
    2147500671U,	// EXTLDIo
    19751U,	// EXTLWI
    17121U,	// EXTLWIo
    2147503119U,	// EXTRDI
    2147500714U,	// EXTRDIo
    19790U,	// EXTRWI
    17157U,	// EXTRWIo
    134236154U,	// EXTSB
    134236154U,	// EXTSB8
    134236154U,	// EXTSB8_32_64
    134234408U,	// EXTSB8o
    134234408U,	// EXTSBo
    134236997U,	// EXTSH
    134236997U,	// EXTSH8
    134236997U,	// EXTSH8_32_64
    134234702U,	// EXTSH8o
    134234702U,	// EXTSHo
    134241100U,	// EXTSW
    134241100U,	// EXTSW_32_64
    134235386U,	// EXTSW_32_64o
    134235386U,	// EXTSWo
    134239408U,	// FABSD
    134235129U,	// FABSDo
    134239408U,	// FABSS
    134235129U,	// FABSSo
    18691U,	// FADD
    21780U,	// FADDS
    17443U,	// FADDSo
    16793U,	// FADDo
    0U,	// FADDrtz
    134236473U,	// FCFID
    134239532U,	// FCFIDS
    134235198U,	// FCFIDSo
    134240206U,	// FCFIDU
    134239836U,	// FCFIDUS
    134235250U,	// FCFIDUSo
    134235285U,	// FCFIDUo
    134234553U,	// FCFIDo
    22528U,	// FCMPUD
    22528U,	// FCMPUS
    20240U,	// FCPSGND
    17221U,	// FCPSGNDo
    20240U,	// FCPSGNS
    17221U,	// FCPSGNSo
    134236480U,	// FCTID
    134241924U,	// FCTIDUZ
    134235472U,	// FCTIDUZo
    134241865U,	// FCTIDZ
    134235456U,	// FCTIDZo
    134234561U,	// FCTIDo
    134240771U,	// FCTIW
    134241933U,	// FCTIWUZ
    134235482U,	// FCTIWUZo
    134241942U,	// FCTIWZ
    134235492U,	// FCTIWZo
    134235347U,	// FCTIWo
    22617U,	// FDIV
    22153U,	// FDIVS
    17532U,	// FDIVSo
    17591U,	// FDIVo
    18697U,	// FMADD
    21787U,	// FMADDS
    17451U,	// FMADDSo
    16800U,	// FMADDo
    134239273U,	// FMR
    134235111U,	// FMRo
    18510U,	// FMSUB
    21763U,	// FMSUBS
    17424U,	// FMSUBSo
    16706U,	// FMSUBo
    19894U,	// FMUL
    22041U,	// FMULS
    17505U,	// FMULSo
    17190U,	// FMULo
    134239414U,	// FNABSD
    134235136U,	// FNABSDo
    134239414U,	// FNABSS
    134235136U,	// FNABSSo
    134236854U,	// FNEGD
    134234684U,	// FNEGDo
    134236854U,	// FNEGS
    134234684U,	// FNEGSo
    18704U,	// FNMADD
    21795U,	// FNMADDS
    17460U,	// FNMADDSo
    16808U,	// FNMADDo
    18517U,	// FNMSUB
    21771U,	// FNMSUBS
    17433U,	// FNMSUBSo
    16714U,	// FNMSUBo
    134236675U,	// FRE
    134239612U,	// FRES
    134235207U,	// FRESo
    134234644U,	// FREo
    134237782U,	// FRIMD
    134234925U,	// FRIMDo
    134237782U,	// FRIMS
    134234925U,	// FRIMSo
    134237983U,	// FRIND
    134234958U,	// FRINDo
    134237983U,	// FRINS
    134234958U,	// FRINSo
    134238831U,	// FRIPD
    134235044U,	// FRIPDo
    134238831U,	// FRIPS
    134235044U,	// FRIPSo
    134241912U,	// FRIZD
    134235465U,	// FRIZDo
    134241912U,	// FRIZS
    134235465U,	// FRIZSo
    134239069U,	// FRSP
    134235075U,	// FRSPo
    134236688U,	// FRSQRTE
    134239618U,	// FRSQRTES
    134235214U,	// FRSQRTESo
    134234650U,	// FRSQRTEo
    19848U,	// FSELD
    17183U,	// FSELDo
    19848U,	// FSELS
    17183U,	// FSELSo
    134240125U,	// FSQRT
    134239828U,	// FSQRTS
    134235241U,	// FSQRTSo
    134235268U,	// FSQRTo
    18504U,	// FSUB
    21756U,	// FSUBS
    17416U,	// FSUBSo
    16699U,	// FSUBo
    9206U,	// GETtlsADDR
    8202U,	// GETtlsADDR32
    9192U,	// GETtlsldADDR
    8186U,	// GETtlsldADDR32
    9150U,	// GetGBRO
    117669U,	// ICBI
    134237112U,	// ICCCI
    19735U,	// INSLWI
    17112U,	// INSLWIo
    2147503103U,	// INSRDI
    2147500696U,	// INSRDIo
    19774U,	// INSRWI
    17139U,	// INSRWIo
    19854U,	// ISEL
    19854U,	// ISEL8
    9360U,	// ISYNC
    75515733U,	// LA
    58738517U,	// LAx
    58744383U,	// LBZ
    58744383U,	// LBZ8
    83908679U,	// LBZU
    83908679U,	// LBZU8
    92298705U,	// LBZUX
    92298705U,	// LBZUX8
    151019039U,	// LBZX
    151019039U,	// LBZX8
    58739018U,	// LD
    151018692U,	// LDARX
    151018706U,	// LDBRX
    83908566U,	// LDU
    92298646U,	// LDUX
    151018554U,	// LDX
    9124U,	// LDgotTprelL
    8119U,	// LDgotTprelL32
    163930U,	// LDinto_toc
    9372U,	// LDtoc
    9313U,	// LDtocCPT
    9072U,	// LDtocJTI
    9082U,	// LDtocL
    58738983U,	// LFD
    83908537U,	// LFDU
    92298631U,	// LFDUX
    151018541U,	// LFDX
    151018489U,	// LFIWAX
    151019051U,	// LFIWZX
    58742162U,	// LFS
    83908615U,	// LFSU
    92298683U,	// LFSUX
    151018773U,	// LFSX
    58738414U,	// LHA
    58738414U,	// LHA8
    83908525U,	// LHAU
    83908525U,	// LHAU8
    92298610U,	// LHAUX
    92298610U,	// LHAUX8
    151018474U,	// LHAX
    151018474U,	// LHAX8
    151018721U,	// LHBRX
    58744401U,	// LHZ
    58744401U,	// LHZ8
    83908685U,	// LHZU
    83908685U,	// LHZU8
    92298712U,	// LHZUX
    92298712U,	// LHZUX8
    151019045U,	// LHZX
    151019045U,	// LHZX8
    100682826U,	// LI
    100682826U,	// LI8
    100685316U,	// LIS
    100685316U,	// LIS8
    58743351U,	// LMW
    19798U,	// LSWI
    151018503U,	// LVEBX
    151018626U,	// LVEHX
    151019010U,	// LVEWX
    151014832U,	// LVSL
    151016597U,	// LVSR
    151018982U,	// LVX
    151014844U,	// LVXL
    58738549U,	// LWA
    151018699U,	// LWARX
    92298617U,	// LWAUX
    151018497U,	// LWAX
    151018497U,	// LWAX_32
    58738549U,	// LWA_32
    151018736U,	// LWBRX
    58744478U,	// LWZ
    58744478U,	// LWZ8
    83908691U,	// LWZU
    83908691U,	// LWZU8
    92298719U,	// LWZUX
    92298719U,	// LWZUX8
    151019059U,	// LWZX
    151019059U,	// LWZX8
    9379U,	// LWZtoc
    151018559U,	// LXSDX
    151018440U,	// LXVD2X
    151018758U,	// LXVDSX
    151018457U,	// LXVW4X
    398302U,	// MBAR
    134236779U,	// MCRF
    283641U,	// MFCR
    283641U,	// MFCR8
    283810U,	// MFCTR
    283810U,	// MFCTR8
    134239204U,	// MFDCR
    284044U,	// MFFS
    283677U,	// MFLR
    283677U,	// MFLR8
    283777U,	// MFMSR
    109070961U,	// MFOCRF
    109070961U,	// MFOCRF8
    134239341U,	// MFSPR
    117462139U,	// MFSR
    134237989U,	// MFSRIN
    134236169U,	// MFTB
    3429485U,	// MFTB8
    3691629U,	// MFVRSAVE
    3691629U,	// MFVRSAVEv
    283655U,	// MFVSCR
    9366U,	// MSYNC
    134236801U,	// MTCRF
    134236801U,	// MTCRF8
    283817U,	// MTCTR
    283817U,	// MTCTR8
    283817U,	// MTCTR8loop
    283817U,	// MTCTRloop
    167924722U,	// MTDCR
    394605U,	// MTFSB0
    394613U,	// MTFSB1
    134236808U,	// MTFSF
    283683U,	// MTLR
    283683U,	// MTLR8
    134239368U,	// MTMSR
    134236557U,	// MTMSRD
    182905U,	// MTOCRF
    182905U,	// MTOCRF8
    134239348U,	// MTSPR
    201871U,	// MTSR
    134237997U,	// MTSRIN
    278741U,	// MTVRSAVE
    409813U,	// MTVRSAVEv
    283663U,	// MTVSCR
    18738U,	// MULHD
    22470U,	// MULHDU
    17548U,	// MULHDUo
    16817U,	// MULHDo
    23027U,	// MULHW
    22568U,	// MULHWU
    17574U,	// MULHWUo
    17611U,	// MULHWo
    18766U,	// MULLD
    16841U,	// MULLDo
    2147503182U,	// MULLI
    2147503182U,	// MULLI8
    23067U,	// MULLW
    17627U,	// MULLWo
    9218U,	// MovePCtoLR
    8838U,	// MovePCtoLR8
    18795U,	// NAND
    18795U,	// NAND8
    16855U,	// NAND8o
    16855U,	// NANDo
    134236855U,	// NEG
    134236855U,	// NEG8
    134234685U,	// NEG8o
    134234685U,	// NEGo
    9468U,	// NOP
    7907U,	// NOP_GT_PWR6
    7919U,	// NOP_GT_PWR7
    21560U,	// NOR
    21560U,	// NOR8
    17389U,	// NOR8o
    17389U,	// NORo
    21553U,	// OR
    21553U,	// OR8
    17390U,	// OR8o
    18656U,	// ORC
    18656U,	// ORC8
    16780U,	// ORC8o
    16780U,	// ORCo
    19678U,	// ORI
    19678U,	// ORI8
    22026U,	// ORIS
    22026U,	// ORIS8
    17390U,	// ORo
    134236565U,	// POPCNTD
    134241132U,	// POPCNTW
    9290U,	// PPC32GOT
    9300U,	// PPC32PICGOT
    9170U,	// RESTORE_CR
    9230U,	// RESTORE_CRBIT
    9042U,	// RESTORE_VRSAVE
    9404U,	// RFCI
    9415U,	// RFDI
    9420U,	// RFI
    9387U,	// RFID
    9409U,	// RFMCI
    19825U,	// RLDCL
    17166U,	// RLDCLo
    21483U,	// RLDCR
    17366U,	// RLDCRo
    2147502247U,	// RLDIC
    2147503480U,	// RLDICL
    2147503480U,	// RLDICL_32_64
    2147500822U,	// RLDICLo
    2147505151U,	// RLDICR
    2147501022U,	// RLDICRo
    2147500420U,	// RLDICo
    578833493U,	// RLDIMI
    578831027U,	// RLDIMIo
    713051229U,	// RLWIMI
    713051229U,	// RLWIMI8
    713048764U,	// RLWIMI8o
    713048764U,	// RLWIMIo
    20060U,	// RLWINM
    20060U,	// RLWINM8
    17204U,	// RLWINM8o
    17204U,	// RLWINMo
    20068U,	// RLWNM
    17213U,	// RLWNMo
    2147503111U,	// ROTRDI
    2147500705U,	// ROTRDIo
    19782U,	// ROTRWI
    17148U,	// ROTRWIo
    280812U,	// SC
    8417U,	// SELECT_CC_F4
    8649U,	// SELECT_CC_F8
    8442U,	// SELECT_CC_I4
    8694U,	// SELECT_CC_I8
    8983U,	// SELECT_CC_VRRC
    8431U,	// SELECT_F4
    8663U,	// SELECT_F8
    8456U,	// SELECT_I4
    8827U,	// SELECT_I8
    8999U,	// SELECT_VRRC
    9338U,	// SLBIA
    281055U,	// SLBIE
    134236599U,	// SLBMFEE
    134236680U,	// SLBMTE
    18780U,	// SLD
    2147503064U,	// SLDI
    2147500664U,	// SLDIo
    16849U,	// SLDo
    23090U,	// SLW
    19729U,	// SLWI
    17105U,	// SLWIo
    17635U,	// SLWo
    9182U,	// SPILL_CR
    9245U,	// SPILL_CRBIT
    9058U,	// SPILL_VRSAVE
    18685U,	// SRAD
    2147503039U,	// SRADI
    2147500652U,	// SRADIo
    16786U,	// SRADo
    22954U,	// SRAW
    19693U,	// SRAWI
    17093U,	// SRAWIo
    17604U,	// SRAWo
    18832U,	// SRD
    2147503105U,	// SRDI
    2147500698U,	// SRDIo
    16862U,	// SRDo
    23321U,	// SRW
    19776U,	// SRWI
    17141U,	// SRWIo
    17641U,	// SRWo
    58738711U,	// STB
    58738711U,	// STB8
    84039603U,	// STBU
    84039603U,	// STBU8
    92429696U,	// STBUX
    92429696U,	// STBUX8
    151018518U,	// STBX
    151018518U,	// STBX8
    58739102U,	// STD
    151018713U,	// STDBRX
    151012648U,	// STDCX
    84039643U,	// STDU
    92429724U,	// STDUX
    151018574U,	// STDX
    58738988U,	// STFD
    84039615U,	// STFDU
    92429710U,	// STFDUX
    151018547U,	// STFDX
    151019025U,	// STFIWX
    58742167U,	// STFS
    84039693U,	// STFSU
    92429762U,	// STFSUX
    151018779U,	// STFSX
    58739548U,	// STH
    58739548U,	// STH8
    151018728U,	// STHBRX
    84039656U,	// STHU
    84039656U,	// STHU8
    92429738U,	// STHUX
    92429738U,	// STHUX8
    151018641U,	// STHX
    151018641U,	// STHX8
    58743356U,	// STMW
    19804U,	// STSWI
    151018510U,	// STVEBX
    151018633U,	// STVEHX
    151019017U,	// STVEWX
    151018987U,	// STVX
    151014850U,	// STVXL
    58743669U,	// STW
    58743669U,	// STW8
    151018743U,	// STWBRX
    151012656U,	// STWCX
    84039736U,	// STWU
    84039736U,	// STWU8
    92429770U,	// STWUX
    92429770U,	// STWUX8
    151019033U,	// STWX
    151019033U,	// STWX8
    151018566U,	// STXSDX
    151018448U,	// STXVD2X
    151018465U,	// STXVW4X
    19006U,	// SUBF
    19006U,	// SUBF8
    16949U,	// SUBF8o
    18578U,	// SUBFC
    18578U,	// SUBFC8
    16748U,	// SUBFC8o
    16748U,	// SUBFCo
    18887U,	// SUBFE
    18887U,	// SUBFE8
    16891U,	// SUBFE8o
    16891U,	// SUBFEo
    2147502254U,	// SUBFIC
    2147502254U,	// SUBFIC8
    134236660U,	// SUBFME
    134236660U,	// SUBFME8
    134234635U,	// SUBFME8o
    134234635U,	// SUBFMEo
    134236720U,	// SUBFZE
    134236720U,	// SUBFZE8
    134234668U,	// SUBFZE8o
    134234668U,	// SUBFZEo
    16949U,	// SUBFo
    2147503019U,	// SUBI
    2147502233U,	// SUBIC
    2147500404U,	// SUBICo
    2147505654U,	// SUBIS
    280792U,	// SYNC
    313227U,	// TAILB
    313227U,	// TAILB8
    329359U,	// TAILBA
    329359U,	// TAILBA8
    9489U,	// TAILBCTR
    9489U,	// TAILBCTR8
    134809094U,	// TCRETURNai
    134809001U,	// TCRETURNai8
    134793456U,	// TCRETURNdi
    134792631U,	// TCRETURNdi8
    134763473U,	// TCRETURNri
    134759877U,	// TCRETURNri8
    133530U,	// TD
    2147617815U,	// TDI
    9344U,	// TLBIA
    4082150U,	// TLBIE
    281984U,	// TLBIEL
    134241264U,	// TLBIVAX
    280903U,	// TLBLD
    281671U,	// TLBLI
    9392U,	// TLBRE
    18940U,	// TLBRE2
    134241535U,	// TLBSX
    23807U,	// TLBSX2
    17720U,	// TLBSX2D
    9352U,	// TLBSYNC
    9398U,	// TLBWE
    18969U,	// TLBWE2
    9463U,	// TRAP
    138079U,	// TW
    2147618147U,	// TWI
    134235603U,	// UPDATE_VRSAVE
    9159U,	// UpdateGBR
    23427U,	// VADDCUW
    20978U,	// VADDFP
    21719U,	// VADDSBS
    21959U,	// VADDSHS
    22187U,	// VADDSWS
    19952U,	// VADDUBM
    21747U,	// VADDUBS
    20001U,	// VADDUHM
    21987U,	// VADDUHS
    20109U,	// VADDUWM
    22214U,	// VADDUWS
    18817U,	// VAND
    18571U,	// VANDC
    18361U,	// VAVGSB
    19204U,	// VAVGSH
    23326U,	// VAVGSW
    18469U,	// VAVGUB
    19306U,	// VAVGUH
    23436U,	// VAVGUW
    847273230U,	// VCFSX
    939547918U,	// VCFSX_0
    847273379U,	// VCFUX
    939548067U,	// VCFUX_0
    20942U,	// VCMPBFP
    17273U,	// VCMPBFPo
    21041U,	// VCMPEQFP
    17294U,	// VCMPEQFPo
    18494U,	// VCMPEQUB
    16688U,	// VCMPEQUBo
    19331U,	// VCMPEQUH
    16982U,	// VCMPEQUHo
    23452U,	// VCMPEQUW
    17666U,	// VCMPEQUWo
    20995U,	// VCMPGEFP
    17283U,	// VCMPGEFPo
    21051U,	// VCMPGTFP
    17305U,	// VCMPGTFPo
    18414U,	// VCMPGTSB
    16669U,	// VCMPGTSBo
    19257U,	// VCMPGTSH
    16963U,	// VCMPGTSHo
    23362U,	// VCMPGTSW
    17647U,	// VCMPGTSWo
    18525U,	// VCMPGTUB
    16723U,	// VCMPGTUBo
    19341U,	// VCMPGTUH
    16993U,	// VCMPGTUHo
    23462U,	// VCMPGTUW
    17677U,	// VCMPGTUWo
    847271712U,	// VCTSXS
    939546400U,	// VCTSXS_0
    847271720U,	// VCTUXS
    939546408U,	// VCTUXS_0
    134238740U,	// VEXPTEFP
    134238714U,	// VLOGEFP
    20969U,	// VMADDFP
    21061U,	// VMAXFP
    18433U,	// VMAXSB
    19276U,	// VMAXSH
    23379U,	// VMAXSW
    18535U,	// VMAXUB
    19351U,	// VMAXUH
    23472U,	// VMAXUW
    21936U,	// VMHADDSHS
    21947U,	// VMHRADDSHS
    21033U,	// VMINFP
    18397U,	// VMINSB
    19240U,	// VMINSH
    23354U,	// VMINSW
    18477U,	// VMINUB
    19314U,	// VMINUH
    23444U,	// VMINUW
    19990U,	// VMLADDUHM
    18318U,	// VMRGHB
    19161U,	// VMRGHH
    23010U,	// VMRGHW
    18326U,	// VMRGLB
    19169U,	// VMRGLH
    23050U,	// VMRGLW
    19933U,	// VMSUMMBM
    19971U,	// VMSUMSHM
    21968U,	// VMSUMSHS
    19961U,	// VMSUMUBM
    20010U,	// VMSUMUHM
    21996U,	// VMSUMUHS
    18352U,	// VMULESB
    19195U,	// VMULESH
    18460U,	// VMULEUB
    19297U,	// VMULEUH
    18405U,	// VMULOSB
    19248U,	// VMULOSH
    18485U,	// VMULOUB
    19322U,	// VMULOUH
    20951U,	// VNMSUBFP
    21573U,	// VNOR
    21586U,	// VOR
    20075U,	// VPERM
    23732U,	// VPKPX
    22062U,	// VPKSHSS
    22117U,	// VPKSHUS
    22071U,	// VPKSWSS
    22135U,	// VPKSWUS
    20082U,	// VPKUHUM
    22126U,	// VPKUHUS
    20091U,	// VPKUWUM
    22144U,	// VPKUWUS
    134238733U,	// VREFP
    134237748U,	// VRFIM
    134237976U,	// VRFIN
    134238797U,	// VRFIP
    134241878U,	// VRFIZ
    18334U,	// VRLB
    19177U,	// VRLH
    23082U,	// VRLW
    134238750U,	// VRSQRTEFP
    19860U,	// VSEL
    19889U,	// VSL
    18340U,	// VSLB
    19645U,	// VSLDOI
    19183U,	// VSLH
    20310U,	// VSLO
    23089U,	// VSLW
    847267855U,	// VSPLTB
    847268692U,	// VSPLTH
    125847498U,	// VSPLTISB
    125848341U,	// VSPLTISH
    125852454U,	// VSPLTISW
    847272795U,	// VSPLTW
    21654U,	// VSR
    18311U,	// VSRAB
    19139U,	// VSRAH
    22953U,	// VSRAW
    18346U,	// VSRB
    19189U,	// VSRH
    20316U,	// VSRO
    23320U,	// VSRW
    23418U,	// VSUBCUW
    20961U,	// VSUBFP
    21710U,	// VSUBSBS
    21927U,	// VSUBSHS
    22178U,	// VSUBSWS
    19943U,	// VSUBUBM
    21738U,	// VSUBUBS
    19981U,	// VSUBUHM
    21978U,	// VSUBUHS
    20100U,	// VSUBUWM
    22205U,	// VSUBUWS
    22168U,	// VSUM2SWS
    21700U,	// VSUM4SBS
    21917U,	// VSUM4SHS
    21728U,	// VSUM4UBS
    22196U,	// VSUMSWS
    134241451U,	// VUPKHPX
    134236097U,	// VUPKHSB
    134236940U,	// VUPKHSH
    134241467U,	// VUPKLPX
    134236116U,	// VUPKLSB
    134236959U,	// VUPKLSH
    21607U,	// VXOR
    2181059687U,	// V_SET0
    2181059687U,	// V_SET0B
    2181059687U,	// V_SET0H
    4217638U,	// V_SETALLONES
    4217638U,	// V_SETALLONESB
    4217638U,	// V_SETALLONESH
    284535U,	// WAIT
    281024U,	// WRTEE
    281628U,	// WRTEEI
    21594U,	// XOR
    21594U,	// XOR8
    17395U,	// XOR8o
    19677U,	// XORI
    19677U,	// XORI8
    22025U,	// XORIS
    22025U,	// XORIS8
    17395U,	// XORo
    134238511U,	// XSABSDP
    20441U,	// XSADDDP
    20722U,	// XSCMPODP
    20854U,	// XSCMPUDP
    20682U,	// XSCPSGNDP
    134239038U,	// XSCVDPSP
    134239540U,	// XSCVDPSXDS
    134239960U,	// XSCVDPSXWS
    134239576U,	// XSCVDPUXDS
    134239996U,	// XSCVDPUXWS
    134238460U,	// XSCVSPDP
    134238187U,	// XSCVSXDDP
    134238209U,	// XSCVUXDDP
    20864U,	// XSDIVDP
    1115705265U,	// XSMADDADP
    1115705524U,	// XSMADDMDP
    20924U,	// XSMAXDP
    20704U,	// XSMINDP
    1115705219U,	// XSMSUBADP
    1115705478U,	// XSMSUBMDP
    20572U,	// XSMULDP
    134238491U,	// XSNABSDP
    134238282U,	// XSNEGDP
    1115705241U,	// XSNMADDADP
    1115705500U,	// XSNMADDMDP
    1115705195U,	// XSNMSUBADP
    1115705454U,	// XSNMSUBMDP
    134237381U,	// XSRDPI
    134236342U,	// XSRDPIC
    134237755U,	// XSRDPIM
    134238804U,	// XSRDPIP
    134241885U,	// XSRDPIZ
    134238242U,	// XSREDP
    134238258U,	// XSRSQRTEDP
    134238540U,	// XSSQRTDP
    20423U,	// XSSUBDP
    20873U,	// XSTDIVDP
    134238550U,	// XSTSQRTDP
    134238520U,	// XVABSDP
    134239085U,	// XVABSSP
    20450U,	// XVADDDP
    21164U,	// XVADDSP
    20752U,	// XVCMPEQDP
    17249U,	// XVCMPEQDPo
    21330U,	// XVCMPEQSP
    17335U,	// XVCMPEQSPo
    20503U,	// XVCMPGEDP
    17237U,	// XVCMPGEDPo
    21195U,	// XVCMPGESP
    17323U,	// XVCMPGESPo
    20801U,	// XVCMPGTDP
    17261U,	// XVCMPGTDPo
    21366U,	// XVCMPGTSP
    17354U,	// XVCMPGTSPo
    20693U,	// XVCPSGNDP
    21290U,	// XVCPSGNSP
    134239048U,	// XVCVDPSP
    134239552U,	// XVCVDPSXDS
    134239972U,	// XVCVDPSXWS
    134239588U,	// XVCVDPUXDS
    134240008U,	// XVCVDPUXWS
    134238470U,	// XVCVSPDP
    134239564U,	// XVCVSPSXDS
    134239984U,	// XVCVSPSXWS
    134239600U,	// XVCVSPUXDS
    134240020U,	// XVCVSPUXWS
    134238198U,	// XVCVSXDDP
    134238901U,	// XVCVSXDSP
    134238630U,	// XVCVSXWDP
    134239145U,	// XVCVSXWSP
    134238220U,	// XVCVUXDDP
    134238912U,	// XVCVUXDSP
    134238641U,	// XVCVUXWDP
    134239156U,	// XVCVUXWSP
    20893U,	// XVDIVDP
    21408U,	// XVDIVSP
    1115705276U,	// XVMADDADP
    1115706008U,	// XVMADDASP
    1115705535U,	// XVMADDMDP
    1115706143U,	// XVMADDMSP
    20933U,	// XVMAXDP
    21439U,	// XVMAXSP
    20713U,	// XVMINDP
    21301U,	// XVMINSP
    1115705230U,	// XVMSUBADP
    1115705985U,	// XVMSUBASP
    1115705489U,	// XVMSUBMDP
    1115706120U,	// XVMSUBMSP
    20581U,	// XVMULDP
    21235U,	// XVMULSP
    134238501U,	// XVNABSDP
    134239075U,	// XVNABSSP
    134238291U,	// XVNEGDP
    134238954U,	// XVNEGSP
    1115705253U,	// XVNMADDADP
    1115705996U,	// XVNMADDASP
    1115705512U,	// XVNMADDMDP
    1115706131U,	// XVNMADDMSP
    1115705207U,	// XVNMSUBADP
    1115705973U,	// XVNMSUBASP
    1115705466U,	// XVNMSUBMDP
    1115706108U,	// XVNMSUBMSP
    134237389U,	// XVRDPI
    134236351U,	// XVRDPIC
    134237764U,	// XVRDPIM
    134238813U,	// XVRDPIP
    134241894U,	// XVRDPIZ
    134238250U,	// XVREDP
    134238934U,	// XVRESP
    134237397U,	// XVRSPI
    134236360U,	// XVRSPIC
    134237773U,	// XVRSPIM
    134238822U,	// XVRSPIP
    134241903U,	// XVRSPIZ
    134238270U,	// XVRSQRTEDP
    134238942U,	// XVRSQRTESP
    134238572U,	// XVSQRTDP
    134239116U,	// XVSQRTSP
    20432U,	// XVSUBDP
    21155U,	// XVSUBSP
    20883U,	// XVTDIVDP
    21398U,	// XVTDIVSP
    134238561U,	// XVTSQRTDP
    134239105U,	// XVTSQRTSP
    18785U,	// XXLAND
    18553U,	// XXLANDC
    21557U,	// XXLNOR
    21550U,	// XXLOR
    21550U,	// XXLORf
    21591U,	// XXLXOR
    23018U,	// XXMRGHW
    23058U,	// XXMRGLW
    19430U,	// XXPERMDI
    19866U,	// XXSEL
    19700U,	// XXSLDWI
    23395U,	// XXSPLTW
    2147616879U,	// gBC
    132755U,	// gBCA
    136347U,	// gBCCTR
    134568U,	// gBCCTRL
    2147618156U,	// gBCL
    132953U,	// gBCLA
    136215U,	// gBCLR
    134561U,	// gBCLRL
    0U
  };

  static const uint8_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// ADD4
    0U,	// ADD4TLS
    0U,	// ADD4o
    0U,	// ADD8
    0U,	// ADD8TLS
    0U,	// ADD8TLS_
    0U,	// ADD8o
    0U,	// ADDC
    0U,	// ADDC8
    0U,	// ADDC8o
    0U,	// ADDCo
    0U,	// ADDE
    0U,	// ADDE8
    0U,	// ADDE8o
    0U,	// ADDEo
    0U,	// ADDI
    0U,	// ADDI8
    0U,	// ADDIC
    0U,	// ADDIC8
    0U,	// ADDICo
    0U,	// ADDIS
    0U,	// ADDIS8
    0U,	// ADDISdtprelHA
    0U,	// ADDISdtprelHA32
    0U,	// ADDISgotTprelHA
    0U,	// ADDIStlsgdHA
    0U,	// ADDIStlsldHA
    0U,	// ADDIStocHA
    0U,	// ADDIdtprelL
    0U,	// ADDIdtprelL32
    0U,	// ADDItlsgdL
    0U,	// ADDItlsgdL32
    0U,	// ADDItlsldL
    0U,	// ADDItlsldL32
    0U,	// ADDItocL
    0U,	// ADDME
    0U,	// ADDME8
    0U,	// ADDME8o
    0U,	// ADDMEo
    0U,	// ADDZE
    0U,	// ADDZE8
    0U,	// ADDZE8o
    0U,	// ADDZEo
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    0U,	// AND
    0U,	// AND8
    0U,	// AND8o
    0U,	// ANDC
    0U,	// ANDC8
    0U,	// ANDC8o
    0U,	// ANDCo
    1U,	// ANDISo
    1U,	// ANDISo8
    1U,	// ANDIo
    1U,	// ANDIo8
    0U,	// ANDIo_1_EQ_BIT
    0U,	// ANDIo_1_EQ_BIT8
    0U,	// ANDIo_1_GT_BIT
    0U,	// ANDIo_1_GT_BIT8
    0U,	// ANDo
    0U,	// ATOMIC_CMP_SWAP_I16
    0U,	// ATOMIC_CMP_SWAP_I32
    0U,	// ATOMIC_CMP_SWAP_I64
    0U,	// ATOMIC_CMP_SWAP_I8
    0U,	// ATOMIC_LOAD_ADD_I16
    0U,	// ATOMIC_LOAD_ADD_I32
    0U,	// ATOMIC_LOAD_ADD_I64
    0U,	// ATOMIC_LOAD_ADD_I8
    0U,	// ATOMIC_LOAD_AND_I16
    0U,	// ATOMIC_LOAD_AND_I32
    0U,	// ATOMIC_LOAD_AND_I64
    0U,	// ATOMIC_LOAD_AND_I8
    0U,	// ATOMIC_LOAD_NAND_I16
    0U,	// ATOMIC_LOAD_NAND_I32
    0U,	// ATOMIC_LOAD_NAND_I64
    0U,	// ATOMIC_LOAD_NAND_I8
    0U,	// ATOMIC_LOAD_OR_I16
    0U,	// ATOMIC_LOAD_OR_I32
    0U,	// ATOMIC_LOAD_OR_I64
    0U,	// ATOMIC_LOAD_OR_I8
    0U,	// ATOMIC_LOAD_SUB_I16
    0U,	// ATOMIC_LOAD_SUB_I32
    0U,	// ATOMIC_LOAD_SUB_I64
    0U,	// ATOMIC_LOAD_SUB_I8
    0U,	// ATOMIC_LOAD_XOR_I16
    0U,	// ATOMIC_LOAD_XOR_I32
    0U,	// ATOMIC_LOAD_XOR_I64
    0U,	// ATOMIC_LOAD_XOR_I8
    0U,	// ATOMIC_SWAP_I16
    0U,	// ATOMIC_SWAP_I32
    0U,	// ATOMIC_SWAP_I64
    0U,	// ATOMIC_SWAP_I8
    0U,	// B
    0U,	// BA
    0U,	// BC
    0U,	// BCC
    0U,	// BCCA
    0U,	// BCCCTR
    0U,	// BCCCTR8
    0U,	// BCCCTRL
    0U,	// BCCCTRL8
    0U,	// BCCL
    0U,	// BCCLA
    0U,	// BCCLR
    0U,	// BCCLRL
    0U,	// BCCTR
    0U,	// BCCTR8
    0U,	// BCCTR8n
    0U,	// BCCTRL
    0U,	// BCCTRL8
    0U,	// BCCTRL8n
    0U,	// BCCTRLn
    0U,	// BCCTRn
    0U,	// BCL
    0U,	// BCLR
    0U,	// BCLRL
    0U,	// BCLRLn
    0U,	// BCLRn
    0U,	// BCLalways
    0U,	// BCLn
    0U,	// BCTR
    0U,	// BCTR8
    0U,	// BCTRL
    0U,	// BCTRL8
    0U,	// BCn
    0U,	// BDNZ
    0U,	// BDNZ8
    0U,	// BDNZA
    0U,	// BDNZAm
    0U,	// BDNZAp
    0U,	// BDNZL
    0U,	// BDNZLA
    0U,	// BDNZLAm
    0U,	// BDNZLAp
    0U,	// BDNZLR
    0U,	// BDNZLR8
    0U,	// BDNZLRL
    0U,	// BDNZLRLm
    0U,	// BDNZLRLp
    0U,	// BDNZLRm
    0U,	// BDNZLRp
    0U,	// BDNZLm
    0U,	// BDNZLp
    0U,	// BDNZm
    0U,	// BDNZp
    0U,	// BDZ
    0U,	// BDZ8
    0U,	// BDZA
    0U,	// BDZAm
    0U,	// BDZAp
    0U,	// BDZL
    0U,	// BDZLA
    0U,	// BDZLAm
    0U,	// BDZLAp
    0U,	// BDZLR
    0U,	// BDZLR8
    0U,	// BDZLRL
    0U,	// BDZLRLm
    0U,	// BDZLRLp
    0U,	// BDZLRm
    0U,	// BDZLRp
    0U,	// BDZLm
    0U,	// BDZLp
    0U,	// BDZm
    0U,	// BDZp
    0U,	// BL
    0U,	// BL8
    0U,	// BL8_NOP
    0U,	// BL8_NOP_TLS
    0U,	// BL8_TLS
    0U,	// BL8_TLS_
    0U,	// BLA
    0U,	// BLA8
    0U,	// BLA8_NOP
    0U,	// BLR
    0U,	// BLRL
    0U,	// BL_TLS
    0U,	// BRINC
    9U,	// CLRLSLDI
    9U,	// CLRLSLDIo
    26U,	// CLRLSLWI
    26U,	// CLRLSLWIo
    1U,	// CLRRDI
    1U,	// CLRRDIo
    2U,	// CLRRWI
    2U,	// CLRRWIo
    0U,	// CMPD
    0U,	// CMPDI
    0U,	// CMPLD
    1U,	// CMPLDI
    0U,	// CMPLW
    1U,	// CMPLWI
    0U,	// CMPW
    0U,	// CMPWI
    0U,	// CNTLZD
    0U,	// CNTLZDo
    0U,	// CNTLZW
    0U,	// CNTLZWo
    0U,	// CR6SET
    0U,	// CR6UNSET
    0U,	// CRAND
    0U,	// CRANDC
    0U,	// CREQV
    0U,	// CRNAND
    0U,	// CRNOR
    0U,	// CROR
    0U,	// CRORC
    2U,	// CRSET
    2U,	// CRUNSET
    0U,	// CRXOR
    0U,	// DCBA
    0U,	// DCBF
    0U,	// DCBI
    0U,	// DCBST
    0U,	// DCBT
    0U,	// DCBTST
    0U,	// DCBZ
    0U,	// DCBZL
    0U,	// DCCCI
    0U,	// DIVD
    0U,	// DIVDU
    0U,	// DIVDUo
    0U,	// DIVDo
    0U,	// DIVW
    0U,	// DIVWU
    0U,	// DIVWUo
    0U,	// DIVWo
    0U,	// DSS
    0U,	// DSSALL
    0U,	// DST
    0U,	// DST64
    0U,	// DSTST
    0U,	// DSTST64
    0U,	// DSTSTT
    0U,	// DSTSTT64
    0U,	// DSTT
    0U,	// DSTT64
    0U,	// DYNALLOC
    0U,	// DYNALLOC8
    0U,	// EH_SjLj_LongJmp32
    0U,	// EH_SjLj_LongJmp64
    0U,	// EH_SjLj_SetJmp32
    0U,	// EH_SjLj_SetJmp64
    0U,	// EH_SjLj_Setup
    0U,	// EIEIO
    0U,	// EQV
    0U,	// EQV8
    0U,	// EQV8o
    0U,	// EQVo
    0U,	// EVABS
    0U,	// EVADDIW
    0U,	// EVADDSMIAAW
    0U,	// EVADDSSIAAW
    0U,	// EVADDUMIAAW
    0U,	// EVADDUSIAAW
    0U,	// EVADDW
    0U,	// EVAND
    0U,	// EVANDC
    0U,	// EVCMPEQ
    0U,	// EVCMPGTS
    0U,	// EVCMPGTU
    0U,	// EVCMPLTS
    0U,	// EVCMPLTU
    0U,	// EVCNTLSW
    0U,	// EVCNTLZW
    0U,	// EVDIVWS
    0U,	// EVDIVWU
    0U,	// EVEQV
    0U,	// EVEXTSB
    0U,	// EVEXTSH
    0U,	// EVLDD
    0U,	// EVLDDX
    0U,	// EVLDH
    0U,	// EVLDHX
    0U,	// EVLDW
    0U,	// EVLDWX
    0U,	// EVLHHESPLAT
    0U,	// EVLHHESPLATX
    0U,	// EVLHHOSSPLAT
    0U,	// EVLHHOSSPLATX
    0U,	// EVLHHOUSPLAT
    0U,	// EVLHHOUSPLATX
    0U,	// EVLWHE
    0U,	// EVLWHEX
    0U,	// EVLWHOS
    0U,	// EVLWHOSX
    0U,	// EVLWHOU
    0U,	// EVLWHOUX
    0U,	// EVLWHSPLAT
    0U,	// EVLWHSPLATX
    0U,	// EVLWWSPLAT
    0U,	// EVLWWSPLATX
    0U,	// EVMERGEHI
    0U,	// EVMERGEHILO
    0U,	// EVMERGELO
    0U,	// EVMERGELOHI
    0U,	// EVMHEGSMFAA
    0U,	// EVMHEGSMFAN
    0U,	// EVMHEGSMIAA
    0U,	// EVMHEGSMIAN
    0U,	// EVMHEGUMIAA
    0U,	// EVMHEGUMIAN
    0U,	// EVMHESMF
    0U,	// EVMHESMFA
    0U,	// EVMHESMFAAW
    0U,	// EVMHESMFANW
    0U,	// EVMHESMI
    0U,	// EVMHESMIA
    0U,	// EVMHESMIAAW
    0U,	// EVMHESMIANW
    0U,	// EVMHESSF
    0U,	// EVMHESSFA
    0U,	// EVMHESSFAAW
    0U,	// EVMHESSFANW
    0U,	// EVMHESSIAAW
    0U,	// EVMHESSIANW
    0U,	// EVMHEUMI
    0U,	// EVMHEUMIA
    0U,	// EVMHEUMIAAW
    0U,	// EVMHEUMIANW
    0U,	// EVMHEUSIAAW
    0U,	// EVMHEUSIANW
    0U,	// EVMHOGSMFAA
    0U,	// EVMHOGSMFAN
    0U,	// EVMHOGSMIAA
    0U,	// EVMHOGSMIAN
    0U,	// EVMHOGUMIAA
    0U,	// EVMHOGUMIAN
    0U,	// EVMHOSMF
    0U,	// EVMHOSMFA
    0U,	// EVMHOSMFAAW
    0U,	// EVMHOSMFANW
    0U,	// EVMHOSMI
    0U,	// EVMHOSMIA
    0U,	// EVMHOSMIAAW
    0U,	// EVMHOSMIANW
    0U,	// EVMHOSSF
    0U,	// EVMHOSSFA
    0U,	// EVMHOSSFAAW
    0U,	// EVMHOSSFANW
    0U,	// EVMHOSSIAAW
    0U,	// EVMHOSSIANW
    0U,	// EVMHOUMI
    0U,	// EVMHOUMIA
    0U,	// EVMHOUMIAAW
    0U,	// EVMHOUMIANW
    0U,	// EVMHOUSIAAW
    0U,	// EVMHOUSIANW
    0U,	// EVMRA
    0U,	// EVMWHSMF
    0U,	// EVMWHSMFA
    0U,	// EVMWHSMI
    0U,	// EVMWHSMIA
    0U,	// EVMWHSSF
    0U,	// EVMWHSSFA
    0U,	// EVMWHUMI
    0U,	// EVMWHUMIA
    0U,	// EVMWLSMIAAW
    0U,	// EVMWLSMIANW
    0U,	// EVMWLSSIAAW
    0U,	// EVMWLSSIANW
    0U,	// EVMWLUMI
    0U,	// EVMWLUMIA
    0U,	// EVMWLUMIAAW
    0U,	// EVMWLUMIANW
    0U,	// EVMWLUSIAAW
    0U,	// EVMWLUSIANW
    0U,	// EVMWSMF
    0U,	// EVMWSMFA
    0U,	// EVMWSMFAA
    0U,	// EVMWSMFAN
    0U,	// EVMWSMI
    0U,	// EVMWSMIA
    0U,	// EVMWSMIAA
    0U,	// EVMWSMIAN
    0U,	// EVMWSSF
    0U,	// EVMWSSFA
    0U,	// EVMWSSFAA
    0U,	// EVMWSSFAN
    0U,	// EVMWUMI
    0U,	// EVMWUMIA
    0U,	// EVMWUMIAA
    0U,	// EVMWUMIAN
    0U,	// EVNAND
    0U,	// EVNEG
    0U,	// EVNOR
    0U,	// EVOR
    0U,	// EVORC
    0U,	// EVRLW
    2U,	// EVRLWI
    0U,	// EVRNDW
    0U,	// EVSLW
    2U,	// EVSLWI
    0U,	// EVSPLATFI
    0U,	// EVSPLATI
    2U,	// EVSRWIS
    2U,	// EVSRWIU
    0U,	// EVSRWS
    0U,	// EVSRWU
    0U,	// EVSTDD
    0U,	// EVSTDDX
    0U,	// EVSTDH
    0U,	// EVSTDHX
    0U,	// EVSTDW
    0U,	// EVSTDWX
    0U,	// EVSTWHE
    0U,	// EVSTWHEX
    0U,	// EVSTWHO
    0U,	// EVSTWHOX
    0U,	// EVSTWWE
    0U,	// EVSTWWEX
    0U,	// EVSTWWO
    0U,	// EVSTWWOX
    0U,	// EVSUBFSMIAAW
    0U,	// EVSUBFSSIAAW
    0U,	// EVSUBFUMIAAW
    0U,	// EVSUBFUSIAAW
    0U,	// EVSUBFW
    0U,	// EVSUBIFW
    0U,	// EVXOR
    9U,	// EXTLDI
    9U,	// EXTLDIo
    26U,	// EXTLWI
    26U,	// EXTLWIo
    9U,	// EXTRDI
    9U,	// EXTRDIo
    26U,	// EXTRWI
    26U,	// EXTRWIo
    0U,	// EXTSB
    0U,	// EXTSB8
    0U,	// EXTSB8_32_64
    0U,	// EXTSB8o
    0U,	// EXTSBo
    0U,	// EXTSH
    0U,	// EXTSH8
    0U,	// EXTSH8_32_64
    0U,	// EXTSH8o
    0U,	// EXTSHo
    0U,	// EXTSW
    0U,	// EXTSW_32_64
    0U,	// EXTSW_32_64o
    0U,	// EXTSWo
    0U,	// FABSD
    0U,	// FABSDo
    0U,	// FABSS
    0U,	// FABSSo
    0U,	// FADD
    0U,	// FADDS
    0U,	// FADDSo
    0U,	// FADDo
    0U,	// FADDrtz
    0U,	// FCFID
    0U,	// FCFIDS
    0U,	// FCFIDSo
    0U,	// FCFIDU
    0U,	// FCFIDUS
    0U,	// FCFIDUSo
    0U,	// FCFIDUo
    0U,	// FCFIDo
    0U,	// FCMPUD
    0U,	// FCMPUS
    0U,	// FCPSGND
    0U,	// FCPSGNDo
    0U,	// FCPSGNS
    0U,	// FCPSGNSo
    0U,	// FCTID
    0U,	// FCTIDUZ
    0U,	// FCTIDUZo
    0U,	// FCTIDZ
    0U,	// FCTIDZo
    0U,	// FCTIDo
    0U,	// FCTIW
    0U,	// FCTIWUZ
    0U,	// FCTIWUZo
    0U,	// FCTIWZ
    0U,	// FCTIWZo
    0U,	// FCTIWo
    0U,	// FDIV
    0U,	// FDIVS
    0U,	// FDIVSo
    0U,	// FDIVo
    40U,	// FMADD
    40U,	// FMADDS
    40U,	// FMADDSo
    40U,	// FMADDo
    0U,	// FMR
    0U,	// FMRo
    40U,	// FMSUB
    40U,	// FMSUBS
    40U,	// FMSUBSo
    40U,	// FMSUBo
    0U,	// FMUL
    0U,	// FMULS
    0U,	// FMULSo
    0U,	// FMULo
    0U,	// FNABSD
    0U,	// FNABSDo
    0U,	// FNABSS
    0U,	// FNABSSo
    0U,	// FNEGD
    0U,	// FNEGDo
    0U,	// FNEGS
    0U,	// FNEGSo
    40U,	// FNMADD
    40U,	// FNMADDS
    40U,	// FNMADDSo
    40U,	// FNMADDo
    40U,	// FNMSUB
    40U,	// FNMSUBS
    40U,	// FNMSUBSo
    40U,	// FNMSUBo
    0U,	// FRE
    0U,	// FRES
    0U,	// FRESo
    0U,	// FREo
    0U,	// FRIMD
    0U,	// FRIMDo
    0U,	// FRIMS
    0U,	// FRIMSo
    0U,	// FRIND
    0U,	// FRINDo
    0U,	// FRINS
    0U,	// FRINSo
    0U,	// FRIPD
    0U,	// FRIPDo
    0U,	// FRIPS
    0U,	// FRIPSo
    0U,	// FRIZD
    0U,	// FRIZDo
    0U,	// FRIZS
    0U,	// FRIZSo
    0U,	// FRSP
    0U,	// FRSPo
    0U,	// FRSQRTE
    0U,	// FRSQRTES
    0U,	// FRSQRTESo
    0U,	// FRSQRTEo
    40U,	// FSELD
    40U,	// FSELDo
    40U,	// FSELS
    40U,	// FSELSo
    0U,	// FSQRT
    0U,	// FSQRTS
    0U,	// FSQRTSo
    0U,	// FSQRTo
    0U,	// FSUB
    0U,	// FSUBS
    0U,	// FSUBSo
    0U,	// FSUBo
    0U,	// GETtlsADDR
    0U,	// GETtlsADDR32
    0U,	// GETtlsldADDR
    0U,	// GETtlsldADDR32
    0U,	// GetGBRO
    0U,	// ICBI
    0U,	// ICCCI
    26U,	// INSLWI
    26U,	// INSLWIo
    9U,	// INSRDI
    9U,	// INSRDIo
    26U,	// INSRWI
    26U,	// INSRWIo
    40U,	// ISEL
    40U,	// ISEL8
    0U,	// ISYNC
    0U,	// LA
    0U,	// LAx
    0U,	// LBZ
    0U,	// LBZ8
    0U,	// LBZU
    0U,	// LBZU8
    0U,	// LBZUX
    0U,	// LBZUX8
    0U,	// LBZX
    0U,	// LBZX8
    0U,	// LD
    0U,	// LDARX
    0U,	// LDBRX
    0U,	// LDU
    0U,	// LDUX
    0U,	// LDX
    0U,	// LDgotTprelL
    0U,	// LDgotTprelL32
    0U,	// LDinto_toc
    0U,	// LDtoc
    0U,	// LDtocCPT
    0U,	// LDtocJTI
    0U,	// LDtocL
    0U,	// LFD
    0U,	// LFDU
    0U,	// LFDUX
    0U,	// LFDX
    0U,	// LFIWAX
    0U,	// LFIWZX
    0U,	// LFS
    0U,	// LFSU
    0U,	// LFSUX
    0U,	// LFSX
    0U,	// LHA
    0U,	// LHA8
    0U,	// LHAU
    0U,	// LHAU8
    0U,	// LHAUX
    0U,	// LHAUX8
    0U,	// LHAX
    0U,	// LHAX8
    0U,	// LHBRX
    0U,	// LHZ
    0U,	// LHZ8
    0U,	// LHZU
    0U,	// LHZU8
    0U,	// LHZUX
    0U,	// LHZUX8
    0U,	// LHZX
    0U,	// LHZX8
    0U,	// LI
    0U,	// LI8
    0U,	// LIS
    0U,	// LIS8
    0U,	// LMW
    2U,	// LSWI
    0U,	// LVEBX
    0U,	// LVEHX
    0U,	// LVEWX
    0U,	// LVSL
    0U,	// LVSR
    0U,	// LVX
    0U,	// LVXL
    0U,	// LWA
    0U,	// LWARX
    0U,	// LWAUX
    0U,	// LWAX
    0U,	// LWAX_32
    0U,	// LWA_32
    0U,	// LWBRX
    0U,	// LWZ
    0U,	// LWZ8
    0U,	// LWZU
    0U,	// LWZU8
    0U,	// LWZUX
    0U,	// LWZUX8
    0U,	// LWZX
    0U,	// LWZX8
    0U,	// LWZtoc
    0U,	// LXSDX
    0U,	// LXVD2X
    0U,	// LXVDSX
    0U,	// LXVW4X
    0U,	// MBAR
    0U,	// MCRF
    0U,	// MFCR
    0U,	// MFCR8
    0U,	// MFCTR
    0U,	// MFCTR8
    0U,	// MFDCR
    0U,	// MFFS
    0U,	// MFLR
    0U,	// MFLR8
    0U,	// MFMSR
    0U,	// MFOCRF
    0U,	// MFOCRF8
    0U,	// MFSPR
    0U,	// MFSR
    0U,	// MFSRIN
    0U,	// MFTB
    0U,	// MFTB8
    0U,	// MFVRSAVE
    0U,	// MFVRSAVEv
    0U,	// MFVSCR
    0U,	// MSYNC
    0U,	// MTCRF
    0U,	// MTCRF8
    0U,	// MTCTR
    0U,	// MTCTR8
    0U,	// MTCTR8loop
    0U,	// MTCTRloop
    0U,	// MTDCR
    0U,	// MTFSB0
    0U,	// MTFSB1
    0U,	// MTFSF
    0U,	// MTLR
    0U,	// MTLR8
    0U,	// MTMSR
    0U,	// MTMSRD
    0U,	// MTOCRF
    0U,	// MTOCRF8
    0U,	// MTSPR
    0U,	// MTSR
    0U,	// MTSRIN
    0U,	// MTVRSAVE
    0U,	// MTVRSAVEv
    0U,	// MTVSCR
    0U,	// MULHD
    0U,	// MULHDU
    0U,	// MULHDUo
    0U,	// MULHDo
    0U,	// MULHW
    0U,	// MULHWU
    0U,	// MULHWUo
    0U,	// MULHWo
    0U,	// MULLD
    0U,	// MULLDo
    0U,	// MULLI
    0U,	// MULLI8
    0U,	// MULLW
    0U,	// MULLWo
    0U,	// MovePCtoLR
    0U,	// MovePCtoLR8
    0U,	// NAND
    0U,	// NAND8
    0U,	// NAND8o
    0U,	// NANDo
    0U,	// NEG
    0U,	// NEG8
    0U,	// NEG8o
    0U,	// NEGo
    0U,	// NOP
    0U,	// NOP_GT_PWR6
    0U,	// NOP_GT_PWR7
    0U,	// NOR
    0U,	// NOR8
    0U,	// NOR8o
    0U,	// NORo
    0U,	// OR
    0U,	// OR8
    0U,	// OR8o
    0U,	// ORC
    0U,	// ORC8
    0U,	// ORC8o
    0U,	// ORCo
    1U,	// ORI
    1U,	// ORI8
    1U,	// ORIS
    1U,	// ORIS8
    0U,	// ORo
    0U,	// POPCNTD
    0U,	// POPCNTW
    0U,	// PPC32GOT
    0U,	// PPC32PICGOT
    0U,	// RESTORE_CR
    0U,	// RESTORE_CRBIT
    0U,	// RESTORE_VRSAVE
    0U,	// RFCI
    0U,	// RFDI
    0U,	// RFI
    0U,	// RFID
    0U,	// RFMCI
    8U,	// RLDCL
    8U,	// RLDCLo
    8U,	// RLDCR
    8U,	// RLDCRo
    9U,	// RLDIC
    9U,	// RLDICL
    9U,	// RLDICL_32_64
    9U,	// RLDICLo
    9U,	// RLDICR
    9U,	// RLDICRo
    9U,	// RLDICo
    0U,	// RLDIMI
    0U,	// RLDIMIo
    0U,	// RLWIMI
    0U,	// RLWIMI8
    0U,	// RLWIMI8o
    0U,	// RLWIMIo
    90U,	// RLWINM
    90U,	// RLWINM8
    90U,	// RLWINM8o
    90U,	// RLWINMo
    88U,	// RLWNM
    88U,	// RLWNMo
    1U,	// ROTRDI
    1U,	// ROTRDIo
    2U,	// ROTRWI
    2U,	// ROTRWIo
    0U,	// SC
    0U,	// SELECT_CC_F4
    0U,	// SELECT_CC_F8
    0U,	// SELECT_CC_I4
    0U,	// SELECT_CC_I8
    0U,	// SELECT_CC_VRRC
    0U,	// SELECT_F4
    0U,	// SELECT_F8
    0U,	// SELECT_I4
    0U,	// SELECT_I8
    0U,	// SELECT_VRRC
    0U,	// SLBIA
    0U,	// SLBIE
    0U,	// SLBMFEE
    0U,	// SLBMTE
    0U,	// SLD
    1U,	// SLDI
    1U,	// SLDIo
    0U,	// SLDo
    0U,	// SLW
    2U,	// SLWI
    2U,	// SLWIo
    0U,	// SLWo
    0U,	// SPILL_CR
    0U,	// SPILL_CRBIT
    0U,	// SPILL_VRSAVE
    0U,	// SRAD
    1U,	// SRADI
    1U,	// SRADIo
    0U,	// SRADo
    0U,	// SRAW
    2U,	// SRAWI
    2U,	// SRAWIo
    0U,	// SRAWo
    0U,	// SRD
    1U,	// SRDI
    1U,	// SRDIo
    0U,	// SRDo
    0U,	// SRW
    2U,	// SRWI
    2U,	// SRWIo
    0U,	// SRWo
    0U,	// STB
    0U,	// STB8
    0U,	// STBU
    0U,	// STBU8
    0U,	// STBUX
    0U,	// STBUX8
    0U,	// STBX
    0U,	// STBX8
    0U,	// STD
    0U,	// STDBRX
    0U,	// STDCX
    0U,	// STDU
    0U,	// STDUX
    0U,	// STDX
    0U,	// STFD
    0U,	// STFDU
    0U,	// STFDUX
    0U,	// STFDX
    0U,	// STFIWX
    0U,	// STFS
    0U,	// STFSU
    0U,	// STFSUX
    0U,	// STFSX
    0U,	// STH
    0U,	// STH8
    0U,	// STHBRX
    0U,	// STHU
    0U,	// STHU8
    0U,	// STHUX
    0U,	// STHUX8
    0U,	// STHX
    0U,	// STHX8
    0U,	// STMW
    2U,	// STSWI
    0U,	// STVEBX
    0U,	// STVEHX
    0U,	// STVEWX
    0U,	// STVX
    0U,	// STVXL
    0U,	// STW
    0U,	// STW8
    0U,	// STWBRX
    0U,	// STWCX
    0U,	// STWU
    0U,	// STWU8
    0U,	// STWUX
    0U,	// STWUX8
    0U,	// STWX
    0U,	// STWX8
    0U,	// STXSDX
    0U,	// STXVD2X
    0U,	// STXVW4X
    0U,	// SUBF
    0U,	// SUBF8
    0U,	// SUBF8o
    0U,	// SUBFC
    0U,	// SUBFC8
    0U,	// SUBFC8o
    0U,	// SUBFCo
    0U,	// SUBFE
    0U,	// SUBFE8
    0U,	// SUBFE8o
    0U,	// SUBFEo
    0U,	// SUBFIC
    0U,	// SUBFIC8
    0U,	// SUBFME
    0U,	// SUBFME8
    0U,	// SUBFME8o
    0U,	// SUBFMEo
    0U,	// SUBFZE
    0U,	// SUBFZE8
    0U,	// SUBFZE8o
    0U,	// SUBFZEo
    0U,	// SUBFo
    0U,	// SUBI
    0U,	// SUBIC
    0U,	// SUBICo
    0U,	// SUBIS
    0U,	// SYNC
    0U,	// TAILB
    0U,	// TAILB8
    0U,	// TAILBA
    0U,	// TAILBA8
    0U,	// TAILBCTR
    0U,	// TAILBCTR8
    0U,	// TCRETURNai
    0U,	// TCRETURNai8
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi8
    0U,	// TCRETURNri
    0U,	// TCRETURNri8
    0U,	// TD
    0U,	// TDI
    0U,	// TLBIA
    0U,	// TLBIE
    0U,	// TLBIEL
    0U,	// TLBIVAX
    0U,	// TLBLD
    0U,	// TLBLI
    0U,	// TLBRE
    0U,	// TLBRE2
    0U,	// TLBSX
    0U,	// TLBSX2
    0U,	// TLBSX2D
    0U,	// TLBSYNC
    0U,	// TLBWE
    0U,	// TLBWE2
    0U,	// TRAP
    0U,	// TW
    0U,	// TWI
    0U,	// UPDATE_VRSAVE
    0U,	// UpdateGBR
    0U,	// VADDCUW
    0U,	// VADDFP
    0U,	// VADDSBS
    0U,	// VADDSHS
    0U,	// VADDSWS
    0U,	// VADDUBM
    0U,	// VADDUBS
    0U,	// VADDUHM
    0U,	// VADDUHS
    0U,	// VADDUWM
    0U,	// VADDUWS
    0U,	// VAND
    0U,	// VANDC
    0U,	// VAVGSB
    0U,	// VAVGSH
    0U,	// VAVGSW
    0U,	// VAVGUB
    0U,	// VAVGUH
    0U,	// VAVGUW
    0U,	// VCFSX
    0U,	// VCFSX_0
    0U,	// VCFUX
    0U,	// VCFUX_0
    0U,	// VCMPBFP
    0U,	// VCMPBFPo
    0U,	// VCMPEQFP
    0U,	// VCMPEQFPo
    0U,	// VCMPEQUB
    0U,	// VCMPEQUBo
    0U,	// VCMPEQUH
    0U,	// VCMPEQUHo
    0U,	// VCMPEQUW
    0U,	// VCMPEQUWo
    0U,	// VCMPGEFP
    0U,	// VCMPGEFPo
    0U,	// VCMPGTFP
    0U,	// VCMPGTFPo
    0U,	// VCMPGTSB
    0U,	// VCMPGTSBo
    0U,	// VCMPGTSH
    0U,	// VCMPGTSHo
    0U,	// VCMPGTSW
    0U,	// VCMPGTSWo
    0U,	// VCMPGTUB
    0U,	// VCMPGTUBo
    0U,	// VCMPGTUH
    0U,	// VCMPGTUHo
    0U,	// VCMPGTUW
    0U,	// VCMPGTUWo
    0U,	// VCTSXS
    0U,	// VCTSXS_0
    0U,	// VCTUXS
    0U,	// VCTUXS_0
    0U,	// VEXPTEFP
    0U,	// VLOGEFP
    40U,	// VMADDFP
    0U,	// VMAXFP
    0U,	// VMAXSB
    0U,	// VMAXSH
    0U,	// VMAXSW
    0U,	// VMAXUB
    0U,	// VMAXUH
    0U,	// VMAXUW
    40U,	// VMHADDSHS
    40U,	// VMHRADDSHS
    0U,	// VMINFP
    0U,	// VMINSB
    0U,	// VMINSH
    0U,	// VMINSW
    0U,	// VMINUB
    0U,	// VMINUH
    0U,	// VMINUW
    40U,	// VMLADDUHM
    0U,	// VMRGHB
    0U,	// VMRGHH
    0U,	// VMRGHW
    0U,	// VMRGLB
    0U,	// VMRGLH
    0U,	// VMRGLW
    40U,	// VMSUMMBM
    40U,	// VMSUMSHM
    40U,	// VMSUMSHS
    40U,	// VMSUMUBM
    40U,	// VMSUMUHM
    40U,	// VMSUMUHS
    0U,	// VMULESB
    0U,	// VMULESH
    0U,	// VMULEUB
    0U,	// VMULEUH
    0U,	// VMULOSB
    0U,	// VMULOSH
    0U,	// VMULOUB
    0U,	// VMULOUH
    40U,	// VNMSUBFP
    0U,	// VNOR
    0U,	// VOR
    40U,	// VPERM
    0U,	// VPKPX
    0U,	// VPKSHSS
    0U,	// VPKSHUS
    0U,	// VPKSWSS
    0U,	// VPKSWUS
    0U,	// VPKUHUM
    0U,	// VPKUHUS
    0U,	// VPKUWUM
    0U,	// VPKUWUS
    0U,	// VREFP
    0U,	// VRFIM
    0U,	// VRFIN
    0U,	// VRFIP
    0U,	// VRFIZ
    0U,	// VRLB
    0U,	// VRLH
    0U,	// VRLW
    0U,	// VRSQRTEFP
    40U,	// VSEL
    0U,	// VSL
    0U,	// VSLB
    24U,	// VSLDOI
    0U,	// VSLH
    0U,	// VSLO
    0U,	// VSLW
    0U,	// VSPLTB
    0U,	// VSPLTH
    0U,	// VSPLTISB
    0U,	// VSPLTISH
    0U,	// VSPLTISW
    0U,	// VSPLTW
    0U,	// VSR
    0U,	// VSRAB
    0U,	// VSRAH
    0U,	// VSRAW
    0U,	// VSRB
    0U,	// VSRH
    0U,	// VSRO
    0U,	// VSRW
    0U,	// VSUBCUW
    0U,	// VSUBFP
    0U,	// VSUBSBS
    0U,	// VSUBSHS
    0U,	// VSUBSWS
    0U,	// VSUBUBM
    0U,	// VSUBUBS
    0U,	// VSUBUHM
    0U,	// VSUBUHS
    0U,	// VSUBUWM
    0U,	// VSUBUWS
    0U,	// VSUM2SWS
    0U,	// VSUM4SBS
    0U,	// VSUM4SHS
    0U,	// VSUM4UBS
    0U,	// VSUMSWS
    0U,	// VUPKHPX
    0U,	// VUPKHSB
    0U,	// VUPKHSH
    0U,	// VUPKLPX
    0U,	// VUPKLSB
    0U,	// VUPKLSH
    0U,	// VXOR
    2U,	// V_SET0
    2U,	// V_SET0B
    2U,	// V_SET0H
    0U,	// V_SETALLONES
    0U,	// V_SETALLONESB
    0U,	// V_SETALLONESH
    0U,	// WAIT
    0U,	// WRTEE
    0U,	// WRTEEI
    0U,	// XOR
    0U,	// XOR8
    0U,	// XOR8o
    1U,	// XORI
    1U,	// XORI8
    1U,	// XORIS
    1U,	// XORIS8
    0U,	// XORo
    0U,	// XSABSDP
    0U,	// XSADDDP
    0U,	// XSCMPODP
    0U,	// XSCMPUDP
    0U,	// XSCPSGNDP
    0U,	// XSCVDPSP
    0U,	// XSCVDPSXDS
    0U,	// XSCVDPSXWS
    0U,	// XSCVDPUXDS
    0U,	// XSCVDPUXWS
    0U,	// XSCVSPDP
    0U,	// XSCVSXDDP
    0U,	// XSCVUXDDP
    0U,	// XSDIVDP
    0U,	// XSMADDADP
    0U,	// XSMADDMDP
    0U,	// XSMAXDP
    0U,	// XSMINDP
    0U,	// XSMSUBADP
    0U,	// XSMSUBMDP
    0U,	// XSMULDP
    0U,	// XSNABSDP
    0U,	// XSNEGDP
    0U,	// XSNMADDADP
    0U,	// XSNMADDMDP
    0U,	// XSNMSUBADP
    0U,	// XSNMSUBMDP
    0U,	// XSRDPI
    0U,	// XSRDPIC
    0U,	// XSRDPIM
    0U,	// XSRDPIP
    0U,	// XSRDPIZ
    0U,	// XSREDP
    0U,	// XSRSQRTEDP
    0U,	// XSSQRTDP
    0U,	// XSSUBDP
    0U,	// XSTDIVDP
    0U,	// XSTSQRTDP
    0U,	// XVABSDP
    0U,	// XVABSSP
    0U,	// XVADDDP
    0U,	// XVADDSP
    0U,	// XVCMPEQDP
    0U,	// XVCMPEQDPo
    0U,	// XVCMPEQSP
    0U,	// XVCMPEQSPo
    0U,	// XVCMPGEDP
    0U,	// XVCMPGEDPo
    0U,	// XVCMPGESP
    0U,	// XVCMPGESPo
    0U,	// XVCMPGTDP
    0U,	// XVCMPGTDPo
    0U,	// XVCMPGTSP
    0U,	// XVCMPGTSPo
    0U,	// XVCPSGNDP
    0U,	// XVCPSGNSP
    0U,	// XVCVDPSP
    0U,	// XVCVDPSXDS
    0U,	// XVCVDPSXWS
    0U,	// XVCVDPUXDS
    0U,	// XVCVDPUXWS
    0U,	// XVCVSPDP
    0U,	// XVCVSPSXDS
    0U,	// XVCVSPSXWS
    0U,	// XVCVSPUXDS
    0U,	// XVCVSPUXWS
    0U,	// XVCVSXDDP
    0U,	// XVCVSXDSP
    0U,	// XVCVSXWDP
    0U,	// XVCVSXWSP
    0U,	// XVCVUXDDP
    0U,	// XVCVUXDSP
    0U,	// XVCVUXWDP
    0U,	// XVCVUXWSP
    0U,	// XVDIVDP
    0U,	// XVDIVSP
    0U,	// XVMADDADP
    0U,	// XVMADDASP
    0U,	// XVMADDMDP
    0U,	// XVMADDMSP
    0U,	// XVMAXDP
    0U,	// XVMAXSP
    0U,	// XVMINDP
    0U,	// XVMINSP
    0U,	// XVMSUBADP
    0U,	// XVMSUBASP
    0U,	// XVMSUBMDP
    0U,	// XVMSUBMSP
    0U,	// XVMULDP
    0U,	// XVMULSP
    0U,	// XVNABSDP
    0U,	// XVNABSSP
    0U,	// XVNEGDP
    0U,	// XVNEGSP
    0U,	// XVNMADDADP
    0U,	// XVNMADDASP
    0U,	// XVNMADDMDP
    0U,	// XVNMADDMSP
    0U,	// XVNMSUBADP
    0U,	// XVNMSUBASP
    0U,	// XVNMSUBMDP
    0U,	// XVNMSUBMSP
    0U,	// XVRDPI
    0U,	// XVRDPIC
    0U,	// XVRDPIM
    0U,	// XVRDPIP
    0U,	// XVRDPIZ
    0U,	// XVREDP
    0U,	// XVRESP
    0U,	// XVRSPI
    0U,	// XVRSPIC
    0U,	// XVRSPIM
    0U,	// XVRSPIP
    0U,	// XVRSPIZ
    0U,	// XVRSQRTEDP
    0U,	// XVRSQRTESP
    0U,	// XVSQRTDP
    0U,	// XVSQRTSP
    0U,	// XVSUBDP
    0U,	// XVSUBSP
    0U,	// XVTDIVDP
    0U,	// XVTDIVSP
    0U,	// XVTSQRTDP
    0U,	// XVTSQRTSP
    0U,	// XXLAND
    0U,	// XXLANDC
    0U,	// XXLNOR
    0U,	// XXLOR
    0U,	// XXLORf
    0U,	// XXLXOR
    0U,	// XXMRGHW
    0U,	// XXMRGLW
    56U,	// XXPERMDI
    40U,	// XXSEL
    56U,	// XXSLDWI
    3U,	// XXSPLTW
    3U,	// gBC
    4U,	// gBCA
    0U,	// gBCCTR
    0U,	// gBCCTRL
    3U,	// gBCL
    4U,	// gBCLA
    0U,	// gBCLR
    0U,	// gBCLRL
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 16 */ 'b', 'd', 'z', 'l', 'a', '+', 32, 0,
  /* 24 */ 'b', 'd', 'n', 'z', 'l', 'a', '+', 32, 0,
  /* 33 */ 'b', 'd', 'z', 'a', '+', 32, 0,
  /* 40 */ 'b', 'd', 'n', 'z', 'a', '+', 32, 0,
  /* 48 */ 'b', 'd', 'z', 'l', '+', 32, 0,
  /* 55 */ 'b', 'd', 'n', 'z', 'l', '+', 32, 0,
  /* 63 */ 'b', 'd', 'z', '+', 32, 0,
  /* 69 */ 'b', 'd', 'n', 'z', '+', 32, 0,
  /* 76 */ 'b', 'c', 'l', 32, '2', '0', ',', 32, '3', '1', ',', 32, 0,
  /* 89 */ 'l', 'd', 32, '2', ',', 32, 0,
  /* 96 */ 'b', 'c', 32, '1', '2', ',', 32, 0,
  /* 104 */ 'b', 'c', 'l', 32, '1', '2', ',', 32, 0,
  /* 113 */ 'b', 'c', 'l', 'r', 'l', 32, '1', '2', ',', 32, 0,
  /* 124 */ 'b', 'c', 'c', 't', 'r', 'l', 32, '1', '2', ',', 32, 0,
  /* 136 */ 'b', 'c', 'l', 'r', 32, '1', '2', ',', 32, 0,
  /* 146 */ 'b', 'c', 'c', 't', 'r', 32, '1', '2', ',', 32, 0,
  /* 157 */ 'b', 'c', 32, '4', ',', 32, 0,
  /* 164 */ 'b', 'c', 'l', 32, '4', ',', 32, 0,
  /* 172 */ 'b', 'c', 'l', 'r', 'l', 32, '4', ',', 32, 0,
  /* 182 */ 'b', 'c', 'c', 't', 'r', 'l', 32, '4', ',', 32, 0,
  /* 193 */ 'b', 'c', 'l', 'r', 32, '4', ',', 32, 0,
  /* 202 */ 'b', 'c', 'c', 't', 'r', 32, '4', ',', 32, 0,
  /* 212 */ 'm', 't', 's', 'p', 'r', 32, '2', '5', '6', ',', 32, 0,
  /* 224 */ 'b', 'd', 'z', 'l', 'a', '-', 32, 0,
  /* 232 */ 'b', 'd', 'n', 'z', 'l', 'a', '-', 32, 0,
  /* 241 */ 'b', 'd', 'z', 'a', '-', 32, 0,
  /* 248 */ 'b', 'd', 'n', 'z', 'a', '-', 32, 0,
  /* 256 */ 'b', 'd', 'z', 'l', '-', 32, 0,
  /* 263 */ 'b', 'd', 'n', 'z', 'l', '-', 32, 0,
  /* 271 */ 'b', 'd', 'z', '-', 32, 0,
  /* 277 */ 'b', 'd', 'n', 'z', '-', 32, 0,
  /* 284 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'b', '.', 32, 0,
  /* 295 */ 'e', 'x', 't', 's', 'b', '.', 32, 0,
  /* 303 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'b', '.', 32, 0,
  /* 314 */ 'f', 's', 'u', 'b', '.', 32, 0,
  /* 321 */ 'f', 'm', 's', 'u', 'b', '.', 32, 0,
  /* 329 */ 'f', 'n', 'm', 's', 'u', 'b', '.', 32, 0,
  /* 338 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'b', '.', 32, 0,
  /* 349 */ 'a', 'd', 'd', 'c', '.', 32, 0,
  /* 356 */ 'a', 'n', 'd', 'c', '.', 32, 0,
  /* 363 */ 's', 'u', 'b', 'f', 'c', '.', 32, 0,
  /* 371 */ 's', 'u', 'b', 'i', 'c', '.', 32, 0,
  /* 379 */ 'a', 'd', 'd', 'i', 'c', '.', 32, 0,
  /* 387 */ 'r', 'l', 'd', 'i', 'c', '.', 32, 0,
  /* 395 */ 'o', 'r', 'c', '.', 32, 0,
  /* 401 */ 's', 'r', 'a', 'd', '.', 32, 0,
  /* 408 */ 'f', 'a', 'd', 'd', '.', 32, 0,
  /* 415 */ 'f', 'm', 'a', 'd', 'd', '.', 32, 0,
  /* 423 */ 'f', 'n', 'm', 'a', 'd', 'd', '.', 32, 0,
  /* 432 */ 'm', 'u', 'l', 'h', 'd', '.', 32, 0,
  /* 440 */ 'f', 'c', 'f', 'i', 'd', '.', 32, 0,
  /* 448 */ 'f', 'c', 't', 'i', 'd', '.', 32, 0,
  /* 456 */ 'm', 'u', 'l', 'l', 'd', '.', 32, 0,
  /* 464 */ 's', 'l', 'd', '.', 32, 0,
  /* 470 */ 'n', 'a', 'n', 'd', '.', 32, 0,
  /* 477 */ 's', 'r', 'd', '.', 32, 0,
  /* 483 */ 'd', 'i', 'v', 'd', '.', 32, 0,
  /* 490 */ 'c', 'n', 't', 'l', 'z', 'd', '.', 32, 0,
  /* 499 */ 'a', 'd', 'd', 'e', '.', 32, 0,
  /* 506 */ 's', 'u', 'b', 'f', 'e', '.', 32, 0,
  /* 514 */ 'a', 'd', 'd', 'm', 'e', '.', 32, 0,
  /* 522 */ 's', 'u', 'b', 'f', 'm', 'e', '.', 32, 0,
  /* 531 */ 'f', 'r', 'e', '.', 32, 0,
  /* 537 */ 'f', 'r', 's', 'q', 'r', 't', 'e', '.', 32, 0,
  /* 547 */ 'a', 'd', 'd', 'z', 'e', '.', 32, 0,
  /* 555 */ 's', 'u', 'b', 'f', 'z', 'e', '.', 32, 0,
  /* 564 */ 's', 'u', 'b', 'f', '.', 32, 0,
  /* 571 */ 'f', 'n', 'e', 'g', '.', 32, 0,
  /* 578 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'h', '.', 32, 0,
  /* 589 */ 'e', 'x', 't', 's', 'h', '.', 32, 0,
  /* 597 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'h', '.', 32, 0,
  /* 608 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'h', '.', 32, 0,
  /* 619 */ 's', 'r', 'a', 'd', 'i', '.', 32, 0,
  /* 627 */ 'c', 'l', 'r', 'l', 's', 'l', 'd', 'i', '.', 32, 0,
  /* 638 */ 'e', 'x', 't', 'l', 'd', 'i', '.', 32, 0,
  /* 647 */ 'a', 'n', 'd', 'i', '.', 32, 0,
  /* 654 */ 'c', 'l', 'r', 'r', 'd', 'i', '.', 32, 0,
  /* 663 */ 'i', 'n', 's', 'r', 'd', 'i', '.', 32, 0,
  /* 672 */ 'r', 'o', 't', 'r', 'd', 'i', '.', 32, 0,
  /* 681 */ 'e', 'x', 't', 'r', 'd', 'i', '.', 32, 0,
  /* 690 */ 'r', 'l', 'd', 'i', 'm', 'i', '.', 32, 0,
  /* 699 */ 'r', 'l', 'w', 'i', 'm', 'i', '.', 32, 0,
  /* 708 */ 's', 'r', 'a', 'w', 'i', '.', 32, 0,
  /* 716 */ 'c', 'l', 'r', 'l', 's', 'l', 'w', 'i', '.', 32, 0,
  /* 727 */ 'i', 'n', 's', 'l', 'w', 'i', '.', 32, 0,
  /* 736 */ 'e', 'x', 't', 'l', 'w', 'i', '.', 32, 0,
  /* 745 */ 'c', 'l', 'r', 'r', 'w', 'i', '.', 32, 0,
  /* 754 */ 'i', 'n', 's', 'r', 'w', 'i', '.', 32, 0,
  /* 763 */ 'r', 'o', 't', 'r', 'w', 'i', '.', 32, 0,
  /* 772 */ 'e', 'x', 't', 'r', 'w', 'i', '.', 32, 0,
  /* 781 */ 'r', 'l', 'd', 'c', 'l', '.', 32, 0,
  /* 789 */ 'r', 'l', 'd', 'i', 'c', 'l', '.', 32, 0,
  /* 798 */ 'f', 's', 'e', 'l', '.', 32, 0,
  /* 805 */ 'f', 'm', 'u', 'l', '.', 32, 0,
  /* 812 */ 'f', 'r', 'i', 'm', '.', 32, 0,
  /* 819 */ 'r', 'l', 'w', 'i', 'n', 'm', '.', 32, 0,
  /* 828 */ 'r', 'l', 'w', 'n', 'm', '.', 32, 0,
  /* 836 */ 'f', 'c', 'p', 's', 'g', 'n', '.', 32, 0,
  /* 845 */ 'f', 'r', 'i', 'n', '.', 32, 0,
  /* 852 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 'd', 'p', '.', 32, 0,
  /* 864 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 'd', 'p', '.', 32, 0,
  /* 876 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 'd', 'p', '.', 32, 0,
  /* 888 */ 'v', 'c', 'm', 'p', 'b', 'f', 'p', '.', 32, 0,
  /* 898 */ 'v', 'c', 'm', 'p', 'g', 'e', 'f', 'p', '.', 32, 0,
  /* 909 */ 'v', 'c', 'm', 'p', 'e', 'q', 'f', 'p', '.', 32, 0,
  /* 920 */ 'v', 'c', 'm', 'p', 'g', 't', 'f', 'p', '.', 32, 0,
  /* 931 */ 'f', 'r', 'i', 'p', '.', 32, 0,
  /* 938 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 's', 'p', '.', 32, 0,
  /* 950 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 's', 'p', '.', 32, 0,
  /* 962 */ 'f', 'r', 's', 'p', '.', 32, 0,
  /* 969 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 's', 'p', '.', 32, 0,
  /* 981 */ 'r', 'l', 'd', 'c', 'r', '.', 32, 0,
  /* 989 */ 'r', 'l', 'd', 'i', 'c', 'r', '.', 32, 0,
  /* 998 */ 'f', 'm', 'r', '.', 32, 0,
  /* 1004 */ 'n', 'o', 'r', '.', 32, 0,
  /* 1010 */ 'x', 'o', 'r', '.', 32, 0,
  /* 1016 */ 'f', 'a', 'b', 's', '.', 32, 0,
  /* 1023 */ 'f', 'n', 'a', 'b', 's', '.', 32, 0,
  /* 1031 */ 'f', 's', 'u', 'b', 's', '.', 32, 0,
  /* 1039 */ 'f', 'm', 's', 'u', 'b', 's', '.', 32, 0,
  /* 1048 */ 'f', 'n', 'm', 's', 'u', 'b', 's', '.', 32, 0,
  /* 1058 */ 'f', 'a', 'd', 'd', 's', '.', 32, 0,
  /* 1066 */ 'f', 'm', 'a', 'd', 'd', 's', '.', 32, 0,
  /* 1075 */ 'f', 'n', 'm', 'a', 'd', 'd', 's', '.', 32, 0,
  /* 1085 */ 'f', 'c', 'f', 'i', 'd', 's', '.', 32, 0,
  /* 1094 */ 'f', 'r', 'e', 's', '.', 32, 0,
  /* 1101 */ 'f', 'r', 's', 'q', 'r', 't', 'e', 's', '.', 32, 0,
  /* 1112 */ 'a', 'n', 'd', 'i', 's', '.', 32, 0,
  /* 1120 */ 'f', 'm', 'u', 'l', 's', '.', 32, 0,
  /* 1128 */ 'f', 's', 'q', 'r', 't', 's', '.', 32, 0,
  /* 1137 */ 'f', 'c', 'f', 'i', 'd', 'u', 's', '.', 32, 0,
  /* 1147 */ 'f', 'd', 'i', 'v', 's', '.', 32, 0,
  /* 1155 */ 'f', 's', 'q', 'r', 't', '.', 32, 0,
  /* 1163 */ 'm', 'u', 'l', 'h', 'd', 'u', '.', 32, 0,
  /* 1172 */ 'f', 'c', 'f', 'i', 'd', 'u', '.', 32, 0,
  /* 1181 */ 'd', 'i', 'v', 'd', 'u', '.', 32, 0,
  /* 1189 */ 'm', 'u', 'l', 'h', 'w', 'u', '.', 32, 0,
  /* 1198 */ 'd', 'i', 'v', 'w', 'u', '.', 32, 0,
  /* 1206 */ 'f', 'd', 'i', 'v', '.', 32, 0,
  /* 1213 */ 'e', 'q', 'v', '.', 32, 0,
  /* 1219 */ 's', 'r', 'a', 'w', '.', 32, 0,
  /* 1226 */ 'm', 'u', 'l', 'h', 'w', '.', 32, 0,
  /* 1234 */ 'f', 'c', 't', 'i', 'w', '.', 32, 0,
  /* 1242 */ 'm', 'u', 'l', 'l', 'w', '.', 32, 0,
  /* 1250 */ 's', 'l', 'w', '.', 32, 0,
  /* 1256 */ 's', 'r', 'w', '.', 32, 0,
  /* 1262 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'w', '.', 32, 0,
  /* 1273 */ 'e', 'x', 't', 's', 'w', '.', 32, 0,
  /* 1281 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'w', '.', 32, 0,
  /* 1292 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'w', '.', 32, 0,
  /* 1303 */ 'd', 'i', 'v', 'w', '.', 32, 0,
  /* 1310 */ 'c', 'n', 't', 'l', 'z', 'w', '.', 32, 0,
  /* 1319 */ 's', 't', 'd', 'c', 'x', '.', 32, 0,
  /* 1327 */ 's', 't', 'w', 'c', 'x', '.', 32, 0,
  /* 1335 */ 't', 'l', 'b', 's', 'x', '.', 32, 0,
  /* 1343 */ 'f', 'c', 't', 'i', 'd', 'z', '.', 32, 0,
  /* 1352 */ 'f', 'r', 'i', 'z', '.', 32, 0,
  /* 1359 */ 'f', 'c', 't', 'i', 'd', 'u', 'z', '.', 32, 0,
  /* 1369 */ 'f', 'c', 't', 'i', 'w', 'u', 'z', '.', 32, 0,
  /* 1379 */ 'f', 'c', 't', 'i', 'w', 'z', '.', 32, 0,
  /* 1388 */ 'm', 't', 'f', 's', 'b', '0', 32, 0,
  /* 1396 */ 'm', 't', 'f', 's', 'b', '1', 32, 0,
  /* 1404 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '3', '2', 32, 0,
  /* 1426 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '1', '6', 32, 0,
  /* 1448 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'a', '8', 32, 0,
  /* 1462 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'd', '8', 32, 0,
  /* 1476 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'r', '8', 32, 0,
  /* 1490 */ 'U', 'P', 'D', 'A', 'T', 'E', '_', 'V', 'R', 'S', 'A', 'V', 'E', 32, 0,
  /* 1505 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 32, 0,
  /* 1524 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 32, 0,
  /* 1541 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'a', 32, 0,
  /* 1554 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'f', 'a', 'a', 32, 0,
  /* 1567 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'f', 'a', 'a', 32, 0,
  /* 1580 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 'a', 'a', 32, 0,
  /* 1591 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 'a', 'a', 32, 0,
  /* 1602 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'i', 'a', 'a', 32, 0,
  /* 1615 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'i', 'a', 'a', 32, 0,
  /* 1628 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 'a', 'a', 32, 0,
  /* 1639 */ 'e', 'v', 'm', 'h', 'e', 'g', 'u', 'm', 'i', 'a', 'a', 32, 0,
  /* 1652 */ 'e', 'v', 'm', 'h', 'o', 'g', 'u', 'm', 'i', 'a', 'a', 32, 0,
  /* 1665 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 'a', 'a', 32, 0,
  /* 1676 */ 'd', 'c', 'b', 'a', 32, 0,
  /* 1682 */ 'b', 'c', 'a', 32, 0,
  /* 1687 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 'a', 32, 0,
  /* 1698 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'f', 'a', 32, 0,
  /* 1709 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 'a', 32, 0,
  /* 1720 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 'a', 32, 0,
  /* 1730 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 'a', 32, 0,
  /* 1741 */ 'e', 'v', 'm', 'w', 'h', 's', 's', 'f', 'a', 32, 0,
  /* 1752 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 'a', 32, 0,
  /* 1763 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 'a', 32, 0,
  /* 1773 */ 'l', 'h', 'a', 32, 0,
  /* 1778 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 'a', 32, 0,
  /* 1789 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'i', 'a', 32, 0,
  /* 1800 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 'a', 32, 0,
  /* 1811 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 'a', 32, 0,
  /* 1821 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 'a', 32, 0,
  /* 1832 */ 'e', 'v', 'm', 'w', 'h', 'u', 'm', 'i', 'a', 32, 0,
  /* 1843 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 'a', 32, 0,
  /* 1854 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 'a', 32, 0,
  /* 1865 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 'a', 32, 0,
  /* 1875 */ 'b', 'l', 'a', 32, 0,
  /* 1880 */ 'b', 'c', 'l', 'a', 32, 0,
  /* 1886 */ 'b', 'd', 'z', 'l', 'a', 32, 0,
  /* 1893 */ 'b', 'd', 'n', 'z', 'l', 'a', 32, 0,
  /* 1901 */ 'e', 'v', 'm', 'r', 'a', 32, 0,
  /* 1908 */ 'l', 'w', 'a', 32, 0,
  /* 1913 */ 'b', 'd', 'z', 'a', 32, 0,
  /* 1919 */ 'b', 'd', 'n', 'z', 'a', 32, 0,
  /* 1926 */ 'v', 's', 'r', 'a', 'b', 32, 0,
  /* 1933 */ 'v', 'm', 'r', 'g', 'h', 'b', 32, 0,
  /* 1941 */ 'v', 'm', 'r', 'g', 'l', 'b', 32, 0,
  /* 1949 */ 'v', 'r', 'l', 'b', 32, 0,
  /* 1955 */ 'v', 's', 'l', 'b', 32, 0,
  /* 1961 */ 'v', 's', 'r', 'b', 32, 0,
  /* 1967 */ 'v', 'm', 'u', 'l', 'e', 's', 'b', 32, 0,
  /* 1976 */ 'v', 'a', 'v', 'g', 's', 'b', 32, 0,
  /* 1984 */ 'v', 'u', 'p', 'k', 'h', 's', 'b', 32, 0,
  /* 1993 */ 'v', 's', 'p', 'l', 't', 'i', 's', 'b', 32, 0,
  /* 2003 */ 'v', 'u', 'p', 'k', 'l', 's', 'b', 32, 0,
  /* 2012 */ 'v', 'm', 'i', 'n', 's', 'b', 32, 0,
  /* 2020 */ 'v', 'm', 'u', 'l', 'o', 's', 'b', 32, 0,
  /* 2029 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'b', 32, 0,
  /* 2039 */ 'e', 'v', 'e', 'x', 't', 's', 'b', 32, 0,
  /* 2048 */ 'v', 'm', 'a', 'x', 's', 'b', 32, 0,
  /* 2056 */ 'm', 'f', 't', 'b', 32, 0,
  /* 2062 */ 'v', 's', 'p', 'l', 't', 'b', 32, 0,
  /* 2070 */ 's', 't', 'b', 32, 0,
  /* 2075 */ 'v', 'm', 'u', 'l', 'e', 'u', 'b', 32, 0,
  /* 2084 */ 'v', 'a', 'v', 'g', 'u', 'b', 32, 0,
  /* 2092 */ 'v', 'm', 'i', 'n', 'u', 'b', 32, 0,
  /* 2100 */ 'v', 'm', 'u', 'l', 'o', 'u', 'b', 32, 0,
  /* 2109 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'b', 32, 0,
  /* 2119 */ 'f', 's', 'u', 'b', 32, 0,
  /* 2125 */ 'f', 'm', 's', 'u', 'b', 32, 0,
  /* 2132 */ 'f', 'n', 'm', 's', 'u', 'b', 32, 0,
  /* 2140 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'b', 32, 0,
  /* 2150 */ 'v', 'm', 'a', 'x', 'u', 'b', 32, 0,
  /* 2158 */ 'b', 'c', 32, 0,
  /* 2162 */ 'a', 'd', 'd', 'c', 32, 0,
  /* 2168 */ 'x', 'x', 'l', 'a', 'n', 'd', 'c', 32, 0,
  /* 2177 */ 'c', 'r', 'a', 'n', 'd', 'c', 32, 0,
  /* 2185 */ 'e', 'v', 'a', 'n', 'd', 'c', 32, 0,
  /* 2193 */ 's', 'u', 'b', 'f', 'c', 32, 0,
  /* 2200 */ 's', 'u', 'b', 'i', 'c', 32, 0,
  /* 2207 */ 'a', 'd', 'd', 'i', 'c', 32, 0,
  /* 2214 */ 'r', 'l', 'd', 'i', 'c', 32, 0,
  /* 2221 */ 's', 'u', 'b', 'f', 'i', 'c', 32, 0,
  /* 2229 */ 'x', 's', 'r', 'd', 'p', 'i', 'c', 32, 0,
  /* 2238 */ 'x', 'v', 'r', 'd', 'p', 'i', 'c', 32, 0,
  /* 2247 */ 'x', 'v', 'r', 's', 'p', 'i', 'c', 32, 0,
  /* 2256 */ 'b', 'r', 'i', 'n', 'c', 32, 0,
  /* 2263 */ 's', 'y', 'n', 'c', 32, 0,
  /* 2269 */ 'c', 'r', 'o', 'r', 'c', 32, 0,
  /* 2276 */ 'e', 'v', 'o', 'r', 'c', 32, 0,
  /* 2283 */ 's', 'c', 32, 0,
  /* 2287 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'd', 32, 0,
  /* 2300 */ 's', 'r', 'a', 'd', 32, 0,
  /* 2306 */ 'f', 'a', 'd', 'd', 32, 0,
  /* 2312 */ 'f', 'm', 'a', 'd', 'd', 32, 0,
  /* 2319 */ 'f', 'n', 'm', 'a', 'd', 'd', 32, 0,
  /* 2327 */ 'e', 'v', 'l', 'd', 'd', 32, 0,
  /* 2334 */ 'e', 'v', 's', 't', 'd', 'd', 32, 0,
  /* 2342 */ 'l', 'f', 'd', 32, 0,
  /* 2347 */ 's', 't', 'f', 'd', 32, 0,
  /* 2353 */ 'm', 'u', 'l', 'h', 'd', 32, 0,
  /* 2360 */ 'f', 'c', 'f', 'i', 'd', 32, 0,
  /* 2367 */ 'f', 'c', 't', 'i', 'd', 32, 0,
  /* 2374 */ 't', 'l', 'b', 'l', 'd', 32, 0,
  /* 2381 */ 'm', 'u', 'l', 'l', 'd', 32, 0,
  /* 2388 */ 'c', 'm', 'p', 'l', 'd', 32, 0,
  /* 2395 */ 's', 'l', 'd', 32, 0,
  /* 2400 */ 'x', 'x', 'l', 'a', 'n', 'd', 32, 0,
  /* 2408 */ 'c', 'r', 'n', 'a', 'n', 'd', 32, 0,
  /* 2416 */ 'e', 'v', 'n', 'a', 'n', 'd', 32, 0,
  /* 2424 */ 'c', 'r', 'a', 'n', 'd', 32, 0,
  /* 2431 */ 'e', 'v', 'a', 'n', 'd', 32, 0,
  /* 2438 */ 'c', 'm', 'p', 'd', 32, 0,
  /* 2444 */ 'm', 't', 'm', 's', 'r', 'd', 32, 0,
  /* 2452 */ 'p', 'o', 'p', 'c', 'n', 't', 'd', 32, 0,
  /* 2461 */ 's', 't', 'd', 32, 0,
  /* 2466 */ 'd', 'i', 'v', 'd', 32, 0,
  /* 2472 */ 'c', 'n', 't', 'l', 'z', 'd', 32, 0,
  /* 2480 */ 'a', 'd', 'd', 'e', 32, 0,
  /* 2486 */ 's', 'l', 'b', 'm', 'f', 'e', 'e', 32, 0,
  /* 2495 */ 'w', 'r', 't', 'e', 'e', 32, 0,
  /* 2502 */ 's', 'u', 'b', 'f', 'e', 32, 0,
  /* 2509 */ 'e', 'v', 'l', 'w', 'h', 'e', 32, 0,
  /* 2517 */ 'e', 'v', 's', 't', 'w', 'h', 'e', 32, 0,
  /* 2526 */ 's', 'l', 'b', 'i', 'e', 32, 0,
  /* 2533 */ 't', 'l', 'b', 'i', 'e', 32, 0,
  /* 2540 */ 'a', 'd', 'd', 'm', 'e', 32, 0,
  /* 2547 */ 's', 'u', 'b', 'f', 'm', 'e', 32, 0,
  /* 2555 */ 't', 'l', 'b', 'r', 'e', 32, 0,
  /* 2562 */ 'f', 'r', 'e', 32, 0,
  /* 2567 */ 's', 'l', 'b', 'm', 't', 'e', 32, 0,
  /* 2575 */ 'f', 'r', 's', 'q', 'r', 't', 'e', 32, 0,
  /* 2584 */ 't', 'l', 'b', 'w', 'e', 32, 0,
  /* 2591 */ 'e', 'v', 's', 't', 'w', 'w', 'e', 32, 0,
  /* 2600 */ 'a', 'd', 'd', 'z', 'e', 32, 0,
  /* 2607 */ 's', 'u', 'b', 'f', 'z', 'e', 32, 0,
  /* 2615 */ 'd', 'c', 'b', 'f', 32, 0,
  /* 2621 */ 's', 'u', 'b', 'f', 32, 0,
  /* 2627 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 32, 0,
  /* 2637 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'f', 32, 0,
  /* 2647 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 32, 0,
  /* 2657 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 32, 0,
  /* 2666 */ 'm', 'c', 'r', 'f', 32, 0,
  /* 2672 */ 'm', 'f', 'o', 'c', 'r', 'f', 32, 0,
  /* 2680 */ 'm', 't', 'o', 'c', 'r', 'f', 32, 0,
  /* 2688 */ 'm', 't', 'c', 'r', 'f', 32, 0,
  /* 2695 */ 'm', 't', 'f', 's', 'f', 32, 0,
  /* 2702 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 32, 0,
  /* 2712 */ 'e', 'v', 'm', 'w', 'h', 's', 's', 'f', 32, 0,
  /* 2722 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 32, 0,
  /* 2732 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 32, 0,
  /* 2741 */ 'f', 'n', 'e', 'g', 32, 0,
  /* 2747 */ 'e', 'v', 'n', 'e', 'g', 32, 0,
  /* 2754 */ 'v', 's', 'r', 'a', 'h', 32, 0,
  /* 2761 */ 'e', 'v', 'l', 'd', 'h', 32, 0,
  /* 2768 */ 'e', 'v', 's', 't', 'd', 'h', 32, 0,
  /* 2776 */ 'v', 'm', 'r', 'g', 'h', 'h', 32, 0,
  /* 2784 */ 'v', 'm', 'r', 'g', 'l', 'h', 32, 0,
  /* 2792 */ 'v', 'r', 'l', 'h', 32, 0,
  /* 2798 */ 'v', 's', 'l', 'h', 32, 0,
  /* 2804 */ 'v', 's', 'r', 'h', 32, 0,
  /* 2810 */ 'v', 'm', 'u', 'l', 'e', 's', 'h', 32, 0,
  /* 2819 */ 'v', 'a', 'v', 'g', 's', 'h', 32, 0,
  /* 2827 */ 'v', 'u', 'p', 'k', 'h', 's', 'h', 32, 0,
  /* 2836 */ 'v', 's', 'p', 'l', 't', 'i', 's', 'h', 32, 0,
  /* 2846 */ 'v', 'u', 'p', 'k', 'l', 's', 'h', 32, 0,
  /* 2855 */ 'v', 'm', 'i', 'n', 's', 'h', 32, 0,
  /* 2863 */ 'v', 'm', 'u', 'l', 'o', 's', 'h', 32, 0,
  /* 2872 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'h', 32, 0,
  /* 2882 */ 'e', 'v', 'e', 'x', 't', 's', 'h', 32, 0,
  /* 2891 */ 'v', 'm', 'a', 'x', 's', 'h', 32, 0,
  /* 2899 */ 'v', 's', 'p', 'l', 't', 'h', 32, 0,
  /* 2907 */ 's', 't', 'h', 32, 0,
  /* 2912 */ 'v', 'm', 'u', 'l', 'e', 'u', 'h', 32, 0,
  /* 2921 */ 'v', 'a', 'v', 'g', 'u', 'h', 32, 0,
  /* 2929 */ 'v', 'm', 'i', 'n', 'u', 'h', 32, 0,
  /* 2937 */ 'v', 'm', 'u', 'l', 'o', 'u', 'h', 32, 0,
  /* 2946 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'h', 32, 0,
  /* 2956 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'h', 32, 0,
  /* 2966 */ 'v', 'm', 'a', 'x', 'u', 'h', 32, 0,
  /* 2974 */ 'd', 'c', 'b', 'i', 32, 0,
  /* 2980 */ 'i', 'c', 'b', 'i', 32, 0,
  /* 2986 */ 's', 'u', 'b', 'i', 32, 0,
  /* 2992 */ 'd', 'c', 'c', 'c', 'i', 32, 0,
  /* 2999 */ 'i', 'c', 'c', 'c', 'i', 32, 0,
  /* 3006 */ 's', 'r', 'a', 'd', 'i', 32, 0,
  /* 3013 */ 'a', 'd', 'd', 'i', 32, 0,
  /* 3019 */ 'c', 'm', 'p', 'l', 'd', 'i', 32, 0,
  /* 3027 */ 'c', 'l', 'r', 'l', 's', 'l', 'd', 'i', 32, 0,
  /* 3037 */ 'e', 'x', 't', 'l', 'd', 'i', 32, 0,
  /* 3045 */ 'x', 'x', 'p', 'e', 'r', 'm', 'd', 'i', 32, 0,
  /* 3055 */ 'c', 'm', 'p', 'd', 'i', 32, 0,
  /* 3062 */ 'c', 'l', 'r', 'r', 'd', 'i', 32, 0,
  /* 3070 */ 'i', 'n', 's', 'r', 'd', 'i', 32, 0,
  /* 3078 */ 'r', 'o', 't', 'r', 'd', 'i', 32, 0,
  /* 3086 */ 'e', 'x', 't', 'r', 'd', 'i', 32, 0,
  /* 3094 */ 't', 'd', 'i', 32, 0,
  /* 3099 */ 'w', 'r', 't', 'e', 'e', 'i', 32, 0,
  /* 3107 */ 'e', 'v', 's', 'p', 'l', 'a', 't', 'f', 'i', 32, 0,
  /* 3118 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'h', 'i', 32, 0,
  /* 3129 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'l', 'o', 'h', 'i', 32, 0,
  /* 3142 */ 't', 'l', 'b', 'l', 'i', 32, 0,
  /* 3149 */ 'm', 'u', 'l', 'l', 'i', 32, 0,
  /* 3156 */ 'r', 'l', 'd', 'i', 'm', 'i', 32, 0,
  /* 3164 */ 'r', 'l', 'w', 'i', 'm', 'i', 32, 0,
  /* 3172 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 32, 0,
  /* 3182 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'i', 32, 0,
  /* 3192 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 32, 0,
  /* 3202 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 32, 0,
  /* 3211 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 32, 0,
  /* 3221 */ 'e', 'v', 'm', 'w', 'h', 'u', 'm', 'i', 32, 0,
  /* 3231 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 32, 0,
  /* 3241 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 32, 0,
  /* 3251 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 32, 0,
  /* 3260 */ 'v', 's', 'l', 'd', 'o', 'i', 32, 0,
  /* 3268 */ 'x', 's', 'r', 'd', 'p', 'i', 32, 0,
  /* 3276 */ 'x', 'v', 'r', 'd', 'p', 'i', 32, 0,
  /* 3284 */ 'x', 'v', 'r', 's', 'p', 'i', 32, 0,
  /* 3292 */ 'x', 'o', 'r', 'i', 32, 0,
  /* 3298 */ 'e', 'v', 's', 'p', 'l', 'a', 't', 'i', 32, 0,
  /* 3308 */ 's', 'r', 'a', 'w', 'i', 32, 0,
  /* 3315 */ 'x', 'x', 's', 'l', 'd', 'w', 'i', 32, 0,
  /* 3324 */ 'c', 'm', 'p', 'l', 'w', 'i', 32, 0,
  /* 3332 */ 'e', 'v', 'r', 'l', 'w', 'i', 32, 0,
  /* 3340 */ 'c', 'l', 'r', 'l', 's', 'l', 'w', 'i', 32, 0,
  /* 3350 */ 'i', 'n', 's', 'l', 'w', 'i', 32, 0,
  /* 3358 */ 'e', 'v', 's', 'l', 'w', 'i', 32, 0,
  /* 3366 */ 'e', 'x', 't', 'l', 'w', 'i', 32, 0,
  /* 3374 */ 'c', 'm', 'p', 'w', 'i', 32, 0,
  /* 3381 */ 'c', 'l', 'r', 'r', 'w', 'i', 32, 0,
  /* 3389 */ 'i', 'n', 's', 'r', 'w', 'i', 32, 0,
  /* 3397 */ 'r', 'o', 't', 'r', 'w', 'i', 32, 0,
  /* 3405 */ 'e', 'x', 't', 'r', 'w', 'i', 32, 0,
  /* 3413 */ 'l', 's', 'w', 'i', 32, 0,
  /* 3419 */ 's', 't', 's', 'w', 'i', 32, 0,
  /* 3426 */ 't', 'w', 'i', 32, 0,
  /* 3431 */ 'b', 'l', 32, 0,
  /* 3435 */ 'b', 'c', 'l', 32, 0,
  /* 3440 */ 'r', 'l', 'd', 'c', 'l', 32, 0,
  /* 3447 */ 'r', 'l', 'd', 'i', 'c', 'l', 32, 0,
  /* 3455 */ 't', 'l', 'b', 'i', 'e', 'l', 32, 0,
  /* 3463 */ 'f', 's', 'e', 'l', 32, 0,
  /* 3469 */ 'i', 's', 'e', 'l', 32, 0,
  /* 3475 */ 'v', 's', 'e', 'l', 32, 0,
  /* 3481 */ 'x', 'x', 's', 'e', 'l', 32, 0,
  /* 3488 */ 'b', 'c', 'l', 'r', 'l', 32, 0,
  /* 3495 */ 'b', 'c', 'c', 't', 'r', 'l', 32, 0,
  /* 3503 */ 'l', 'v', 's', 'l', 32, 0,
  /* 3509 */ 'f', 'm', 'u', 'l', 32, 0,
  /* 3515 */ 'l', 'v', 'x', 'l', 32, 0,
  /* 3521 */ 's', 't', 'v', 'x', 'l', 32, 0,
  /* 3528 */ 'd', 'c', 'b', 'z', 'l', 32, 0,
  /* 3535 */ 'b', 'd', 'z', 'l', 32, 0,
  /* 3541 */ 'b', 'd', 'n', 'z', 'l', 32, 0,
  /* 3548 */ 'v', 'm', 's', 'u', 'm', 'm', 'b', 'm', 32, 0,
  /* 3558 */ 'v', 's', 'u', 'b', 'u', 'b', 'm', 32, 0,
  /* 3567 */ 'v', 'a', 'd', 'd', 'u', 'b', 'm', 32, 0,
  /* 3576 */ 'v', 'm', 's', 'u', 'm', 'u', 'b', 'm', 32, 0,
  /* 3586 */ 'v', 'm', 's', 'u', 'm', 's', 'h', 'm', 32, 0,
  /* 3596 */ 'v', 's', 'u', 'b', 'u', 'h', 'm', 32, 0,
  /* 3605 */ 'v', 'm', 'l', 'a', 'd', 'd', 'u', 'h', 'm', 32, 0,
  /* 3616 */ 'v', 'a', 'd', 'd', 'u', 'h', 'm', 32, 0,
  /* 3625 */ 'v', 'm', 's', 'u', 'm', 'u', 'h', 'm', 32, 0,
  /* 3635 */ 'v', 'r', 'f', 'i', 'm', 32, 0,
  /* 3642 */ 'x', 's', 'r', 'd', 'p', 'i', 'm', 32, 0,
  /* 3651 */ 'x', 'v', 'r', 'd', 'p', 'i', 'm', 32, 0,
  /* 3660 */ 'x', 'v', 'r', 's', 'p', 'i', 'm', 32, 0,
  /* 3669 */ 'f', 'r', 'i', 'm', 32, 0,
  /* 3675 */ 'r', 'l', 'w', 'i', 'n', 'm', 32, 0,
  /* 3683 */ 'r', 'l', 'w', 'n', 'm', 32, 0,
  /* 3690 */ 'v', 'p', 'e', 'r', 'm', 32, 0,
  /* 3697 */ 'v', 'p', 'k', 'u', 'h', 'u', 'm', 32, 0,
  /* 3706 */ 'v', 'p', 'k', 'u', 'w', 'u', 'm', 32, 0,
  /* 3715 */ 'v', 's', 'u', 'b', 'u', 'w', 'm', 32, 0,
  /* 3724 */ 'v', 'a', 'd', 'd', 'u', 'w', 'm', 32, 0,
  /* 3733 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'f', 'a', 'n', 32, 0,
  /* 3746 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'f', 'a', 'n', 32, 0,
  /* 3759 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 'a', 'n', 32, 0,
  /* 3770 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 'a', 'n', 32, 0,
  /* 3781 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'i', 'a', 'n', 32, 0,
  /* 3794 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'i', 'a', 'n', 32, 0,
  /* 3807 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 'a', 'n', 32, 0,
  /* 3818 */ 'e', 'v', 'm', 'h', 'e', 'g', 'u', 'm', 'i', 'a', 'n', 32, 0,
  /* 3831 */ 'e', 'v', 'm', 'h', 'o', 'g', 'u', 'm', 'i', 'a', 'n', 32, 0,
  /* 3844 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 'a', 'n', 32, 0,
  /* 3855 */ 'f', 'c', 'p', 's', 'g', 'n', 32, 0,
  /* 3863 */ 'v', 'r', 'f', 'i', 'n', 32, 0,
  /* 3870 */ 'f', 'r', 'i', 'n', 32, 0,
  /* 3876 */ 'm', 'f', 's', 'r', 'i', 'n', 32, 0,
  /* 3884 */ 'm', 't', 's', 'r', 'i', 'n', 32, 0,
  /* 3892 */ 'e', 'v', 's', 't', 'w', 'h', 'o', 32, 0,
  /* 3901 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'l', 'o', 32, 0,
  /* 3912 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'h', 'i', 'l', 'o', 32, 0,
  /* 3925 */ 'v', 's', 'l', 'o', 32, 0,
  /* 3931 */ 'v', 's', 'r', 'o', 32, 0,
  /* 3937 */ 'e', 'v', 's', 't', 'w', 'w', 'o', 32, 0,
  /* 3946 */ 'x', 's', 'n', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 3958 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 3970 */ 'x', 's', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 3981 */ 'x', 'v', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 3992 */ 'x', 's', 'n', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4004 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4016 */ 'x', 's', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4027 */ 'x', 'v', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4038 */ 'x', 's', 's', 'u', 'b', 'd', 'p', 32, 0,
  /* 4047 */ 'x', 'v', 's', 'u', 'b', 'd', 'p', 32, 0,
  /* 4056 */ 'x', 's', 'a', 'd', 'd', 'd', 'p', 32, 0,
  /* 4065 */ 'x', 'v', 'a', 'd', 'd', 'd', 'p', 32, 0,
  /* 4074 */ 'x', 's', 'c', 'v', 's', 'x', 'd', 'd', 'p', 32, 0,
  /* 4085 */ 'x', 'v', 'c', 'v', 's', 'x', 'd', 'd', 'p', 32, 0,
  /* 4096 */ 'x', 's', 'c', 'v', 'u', 'x', 'd', 'd', 'p', 32, 0,
  /* 4107 */ 'x', 'v', 'c', 'v', 'u', 'x', 'd', 'd', 'p', 32, 0,
  /* 4118 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 'd', 'p', 32, 0,
  /* 4129 */ 'x', 's', 'r', 'e', 'd', 'p', 32, 0,
  /* 4137 */ 'x', 'v', 'r', 'e', 'd', 'p', 32, 0,
  /* 4145 */ 'x', 's', 'r', 's', 'q', 'r', 't', 'e', 'd', 'p', 32, 0,
  /* 4157 */ 'x', 'v', 'r', 's', 'q', 'r', 't', 'e', 'd', 'p', 32, 0,
  /* 4169 */ 'x', 's', 'n', 'e', 'g', 'd', 'p', 32, 0,
  /* 4178 */ 'x', 'v', 'n', 'e', 'g', 'd', 'p', 32, 0,
  /* 4187 */ 'x', 's', 'm', 'u', 'l', 'd', 'p', 32, 0,
  /* 4196 */ 'x', 'v', 'm', 'u', 'l', 'd', 'p', 32, 0,
  /* 4205 */ 'x', 's', 'n', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 4217 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 4229 */ 'x', 's', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 4240 */ 'x', 'v', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 4251 */ 'x', 's', 'n', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 4263 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 4275 */ 'x', 's', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 4286 */ 'x', 'v', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 4297 */ 'x', 's', 'c', 'p', 's', 'g', 'n', 'd', 'p', 32, 0,
  /* 4308 */ 'x', 'v', 'c', 'p', 's', 'g', 'n', 'd', 'p', 32, 0,
  /* 4319 */ 'x', 's', 'm', 'i', 'n', 'd', 'p', 32, 0,
  /* 4328 */ 'x', 'v', 'm', 'i', 'n', 'd', 'p', 32, 0,
  /* 4337 */ 'x', 's', 'c', 'm', 'p', 'o', 'd', 'p', 32, 0,
  /* 4347 */ 'x', 's', 'c', 'v', 's', 'p', 'd', 'p', 32, 0,
  /* 4357 */ 'x', 'v', 'c', 'v', 's', 'p', 'd', 'p', 32, 0,
  /* 4367 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 'd', 'p', 32, 0,
  /* 4378 */ 'x', 's', 'n', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 4388 */ 'x', 'v', 'n', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 4398 */ 'x', 's', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 4407 */ 'x', 'v', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 4416 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 'd', 'p', 32, 0,
  /* 4427 */ 'x', 's', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 4437 */ 'x', 's', 't', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 4448 */ 'x', 'v', 't', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 4459 */ 'x', 'v', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 4469 */ 'x', 's', 'c', 'm', 'p', 'u', 'd', 'p', 32, 0,
  /* 4479 */ 'x', 's', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 4488 */ 'x', 's', 't', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 4498 */ 'x', 'v', 't', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 4508 */ 'x', 'v', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 4517 */ 'x', 'v', 'c', 'v', 's', 'x', 'w', 'd', 'p', 32, 0,
  /* 4528 */ 'x', 'v', 'c', 'v', 'u', 'x', 'w', 'd', 'p', 32, 0,
  /* 4539 */ 'x', 's', 'm', 'a', 'x', 'd', 'p', 32, 0,
  /* 4548 */ 'x', 'v', 'm', 'a', 'x', 'd', 'p', 32, 0,
  /* 4557 */ 'v', 'c', 'm', 'p', 'b', 'f', 'p', 32, 0,
  /* 4566 */ 'v', 'n', 'm', 's', 'u', 'b', 'f', 'p', 32, 0,
  /* 4576 */ 'v', 's', 'u', 'b', 'f', 'p', 32, 0,
  /* 4584 */ 'v', 'm', 'a', 'd', 'd', 'f', 'p', 32, 0,
  /* 4593 */ 'v', 'a', 'd', 'd', 'f', 'p', 32, 0,
  /* 4601 */ 'v', 'l', 'o', 'g', 'e', 'f', 'p', 32, 0,
  /* 4610 */ 'v', 'c', 'm', 'p', 'g', 'e', 'f', 'p', 32, 0,
  /* 4620 */ 'v', 'r', 'e', 'f', 'p', 32, 0,
  /* 4627 */ 'v', 'e', 'x', 'p', 't', 'e', 'f', 'p', 32, 0,
  /* 4637 */ 'v', 'r', 's', 'q', 'r', 't', 'e', 'f', 'p', 32, 0,
  /* 4648 */ 'v', 'm', 'i', 'n', 'f', 'p', 32, 0,
  /* 4656 */ 'v', 'c', 'm', 'p', 'e', 'q', 'f', 'p', 32, 0,
  /* 4666 */ 'v', 'c', 'm', 'p', 'g', 't', 'f', 'p', 32, 0,
  /* 4676 */ 'v', 'm', 'a', 'x', 'f', 'p', 32, 0,
  /* 4684 */ 'v', 'r', 'f', 'i', 'p', 32, 0,
  /* 4691 */ 'x', 's', 'r', 'd', 'p', 'i', 'p', 32, 0,
  /* 4700 */ 'x', 'v', 'r', 'd', 'p', 'i', 'p', 32, 0,
  /* 4709 */ 'x', 'v', 'r', 's', 'p', 'i', 'p', 32, 0,
  /* 4718 */ 'f', 'r', 'i', 'p', 32, 0,
  /* 4724 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'a', 's', 'p', 32, 0,
  /* 4736 */ 'x', 'v', 'm', 's', 'u', 'b', 'a', 's', 'p', 32, 0,
  /* 4747 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'a', 's', 'p', 32, 0,
  /* 4759 */ 'x', 'v', 'm', 'a', 'd', 'd', 'a', 's', 'p', 32, 0,
  /* 4770 */ 'x', 'v', 's', 'u', 'b', 's', 'p', 32, 0,
  /* 4779 */ 'x', 'v', 'a', 'd', 'd', 's', 'p', 32, 0,
  /* 4788 */ 'x', 'v', 'c', 'v', 's', 'x', 'd', 's', 'p', 32, 0,
  /* 4799 */ 'x', 'v', 'c', 'v', 'u', 'x', 'd', 's', 'p', 32, 0,
  /* 4810 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 's', 'p', 32, 0,
  /* 4821 */ 'x', 'v', 'r', 'e', 's', 'p', 32, 0,
  /* 4829 */ 'x', 'v', 'r', 's', 'q', 'r', 't', 'e', 's', 'p', 32, 0,
  /* 4841 */ 'x', 'v', 'n', 'e', 'g', 's', 'p', 32, 0,
  /* 4850 */ 'x', 'v', 'm', 'u', 'l', 's', 'p', 32, 0,
  /* 4859 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'm', 's', 'p', 32, 0,
  /* 4871 */ 'x', 'v', 'm', 's', 'u', 'b', 'm', 's', 'p', 32, 0,
  /* 4882 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'm', 's', 'p', 32, 0,
  /* 4894 */ 'x', 'v', 'm', 'a', 'd', 'd', 'm', 's', 'p', 32, 0,
  /* 4905 */ 'x', 'v', 'c', 'p', 's', 'g', 'n', 's', 'p', 32, 0,
  /* 4916 */ 'x', 'v', 'm', 'i', 'n', 's', 'p', 32, 0,
  /* 4925 */ 'x', 's', 'c', 'v', 'd', 'p', 's', 'p', 32, 0,
  /* 4935 */ 'x', 'v', 'c', 'v', 'd', 'p', 's', 'p', 32, 0,
  /* 4945 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 's', 'p', 32, 0,
  /* 4956 */ 'f', 'r', 's', 'p', 32, 0,
  /* 4962 */ 'x', 'v', 'n', 'a', 'b', 's', 's', 'p', 32, 0,
  /* 4972 */ 'x', 'v', 'a', 'b', 's', 's', 'p', 32, 0,
  /* 4981 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 's', 'p', 32, 0,
  /* 4992 */ 'x', 'v', 't', 's', 'q', 'r', 't', 's', 'p', 32, 0,
  /* 5003 */ 'x', 'v', 's', 'q', 'r', 't', 's', 'p', 32, 0,
  /* 5013 */ 'x', 'v', 't', 'd', 'i', 'v', 's', 'p', 32, 0,
  /* 5023 */ 'x', 'v', 'd', 'i', 'v', 's', 'p', 32, 0,
  /* 5032 */ 'x', 'v', 'c', 'v', 's', 'x', 'w', 's', 'p', 32, 0,
  /* 5043 */ 'x', 'v', 'c', 'v', 'u', 'x', 'w', 's', 'p', 32, 0,
  /* 5054 */ 'x', 'v', 'm', 'a', 'x', 's', 'p', 32, 0,
  /* 5063 */ 'e', 'v', 'c', 'm', 'p', 'e', 'q', 32, 0,
  /* 5072 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'r', 32, 0,
  /* 5085 */ 'm', 'b', 'a', 'r', 32, 0,
  /* 5091 */ 'm', 'f', 'd', 'c', 'r', 32, 0,
  /* 5098 */ 'r', 'l', 'd', 'c', 'r', 32, 0,
  /* 5105 */ 'm', 't', 'd', 'c', 'r', 32, 0,
  /* 5112 */ 'm', 'f', 'c', 'r', 32, 0,
  /* 5118 */ 'r', 'l', 'd', 'i', 'c', 'r', 32, 0,
  /* 5126 */ 'm', 'f', 'v', 's', 'c', 'r', 32, 0,
  /* 5134 */ 'm', 't', 'v', 's', 'c', 'r', 32, 0,
  /* 5142 */ 'b', 'c', 'l', 'r', 32, 0,
  /* 5148 */ 'm', 'f', 'l', 'r', 32, 0,
  /* 5154 */ 'm', 't', 'l', 'r', 32, 0,
  /* 5160 */ 'f', 'm', 'r', 32, 0,
  /* 5165 */ 'x', 'x', 'l', 'o', 'r', 32, 0,
  /* 5172 */ 'x', 'x', 'l', 'n', 'o', 'r', 32, 0,
  /* 5180 */ 'c', 'r', 'n', 'o', 'r', 32, 0,
  /* 5187 */ 'e', 'v', 'n', 'o', 'r', 32, 0,
  /* 5194 */ 'c', 'r', 'o', 'r', 32, 0,
  /* 5200 */ 'e', 'v', 'o', 'r', 32, 0,
  /* 5206 */ 'x', 'x', 'l', 'x', 'o', 'r', 32, 0,
  /* 5214 */ 'c', 'r', 'x', 'o', 'r', 32, 0,
  /* 5221 */ 'e', 'v', 'x', 'o', 'r', 32, 0,
  /* 5228 */ 'm', 'f', 's', 'p', 'r', 32, 0,
  /* 5235 */ 'm', 't', 's', 'p', 'r', 32, 0,
  /* 5242 */ 'm', 'f', 's', 'r', 32, 0,
  /* 5248 */ 'm', 'f', 'm', 's', 'r', 32, 0,
  /* 5255 */ 'm', 't', 'm', 's', 'r', 32, 0,
  /* 5262 */ 'm', 't', 's', 'r', 32, 0,
  /* 5268 */ 'l', 'v', 's', 'r', 32, 0,
  /* 5274 */ 'b', 'c', 'c', 't', 'r', 32, 0,
  /* 5281 */ 'm', 'f', 'c', 't', 'r', 32, 0,
  /* 5288 */ 'm', 't', 'c', 't', 'r', 32, 0,
  /* 5295 */ 'f', 'a', 'b', 's', 32, 0,
  /* 5301 */ 'f', 'n', 'a', 'b', 's', 32, 0,
  /* 5308 */ 'e', 'v', 'a', 'b', 's', 32, 0,
  /* 5315 */ 'v', 's', 'u', 'm', '4', 's', 'b', 's', 32, 0,
  /* 5325 */ 'v', 's', 'u', 'b', 's', 'b', 's', 32, 0,
  /* 5334 */ 'v', 'a', 'd', 'd', 's', 'b', 's', 32, 0,
  /* 5343 */ 'v', 's', 'u', 'm', '4', 'u', 'b', 's', 32, 0,
  /* 5353 */ 'v', 's', 'u', 'b', 'u', 'b', 's', 32, 0,
  /* 5362 */ 'v', 'a', 'd', 'd', 'u', 'b', 's', 32, 0,
  /* 5371 */ 'f', 's', 'u', 'b', 's', 32, 0,
  /* 5378 */ 'f', 'm', 's', 'u', 'b', 's', 32, 0,
  /* 5386 */ 'f', 'n', 'm', 's', 'u', 'b', 's', 32, 0,
  /* 5395 */ 'f', 'a', 'd', 'd', 's', 32, 0,
  /* 5402 */ 'f', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 5410 */ 'f', 'n', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 5419 */ 'f', 'c', 'f', 'i', 'd', 's', 32, 0,
  /* 5427 */ 'x', 's', 'c', 'v', 'd', 'p', 's', 'x', 'd', 's', 32, 0,
  /* 5439 */ 'x', 'v', 'c', 'v', 'd', 'p', 's', 'x', 'd', 's', 32, 0,
  /* 5451 */ 'x', 'v', 'c', 'v', 's', 'p', 's', 'x', 'd', 's', 32, 0,
  /* 5463 */ 'x', 's', 'c', 'v', 'd', 'p', 'u', 'x', 'd', 's', 32, 0,
  /* 5475 */ 'x', 'v', 'c', 'v', 'd', 'p', 'u', 'x', 'd', 's', 32, 0,
  /* 5487 */ 'x', 'v', 'c', 'v', 's', 'p', 'u', 'x', 'd', 's', 32, 0,
  /* 5499 */ 'f', 'r', 'e', 's', 32, 0,
  /* 5505 */ 'f', 'r', 's', 'q', 'r', 't', 'e', 's', 32, 0,
  /* 5515 */ 'm', 'f', 'f', 's', 32, 0,
  /* 5521 */ 'l', 'f', 's', 32, 0,
  /* 5526 */ 's', 't', 'f', 's', 32, 0,
  /* 5532 */ 'v', 's', 'u', 'm', '4', 's', 'h', 's', 32, 0,
  /* 5542 */ 'v', 's', 'u', 'b', 's', 'h', 's', 32, 0,
  /* 5551 */ 'v', 'm', 'h', 'a', 'd', 'd', 's', 'h', 's', 32, 0,
  /* 5562 */ 'v', 'm', 'h', 'r', 'a', 'd', 'd', 's', 'h', 's', 32, 0,
  /* 5574 */ 'v', 'a', 'd', 'd', 's', 'h', 's', 32, 0,
  /* 5583 */ 'v', 'm', 's', 'u', 'm', 's', 'h', 's', 32, 0,
  /* 5593 */ 'v', 's', 'u', 'b', 'u', 'h', 's', 32, 0,
  /* 5602 */ 'v', 'a', 'd', 'd', 'u', 'h', 's', 32, 0,
  /* 5611 */ 'v', 'm', 's', 'u', 'm', 'u', 'h', 's', 32, 0,
  /* 5621 */ 's', 'u', 'b', 'i', 's', 32, 0,
  /* 5628 */ 'a', 'd', 'd', 'i', 's', 32, 0,
  /* 5635 */ 'l', 'i', 's', 32, 0,
  /* 5640 */ 'x', 'o', 'r', 'i', 's', 32, 0,
  /* 5647 */ 'e', 'v', 's', 'r', 'w', 'i', 's', 32, 0,
  /* 5656 */ 'f', 'm', 'u', 'l', 's', 32, 0,
  /* 5663 */ 'e', 'v', 'l', 'w', 'h', 'o', 's', 32, 0,
  /* 5672 */ 'd', 's', 's', 32, 0,
  /* 5677 */ 'v', 'p', 'k', 's', 'h', 's', 's', 32, 0,
  /* 5686 */ 'v', 'p', 'k', 's', 'w', 's', 's', 32, 0,
  /* 5695 */ 'e', 'v', 'c', 'm', 'p', 'g', 't', 's', 32, 0,
  /* 5705 */ 'e', 'v', 'c', 'm', 'p', 'l', 't', 's', 32, 0,
  /* 5715 */ 'f', 's', 'q', 'r', 't', 's', 32, 0,
  /* 5723 */ 'f', 'c', 'f', 'i', 'd', 'u', 's', 32, 0,
  /* 5732 */ 'v', 'p', 'k', 's', 'h', 'u', 's', 32, 0,
  /* 5741 */ 'v', 'p', 'k', 'u', 'h', 'u', 's', 32, 0,
  /* 5750 */ 'v', 'p', 'k', 's', 'w', 'u', 's', 32, 0,
  /* 5759 */ 'v', 'p', 'k', 'u', 'w', 'u', 's', 32, 0,
  /* 5768 */ 'f', 'd', 'i', 'v', 's', 32, 0,
  /* 5775 */ 'e', 'v', 's', 'r', 'w', 's', 32, 0,
  /* 5783 */ 'v', 's', 'u', 'm', '2', 's', 'w', 's', 32, 0,
  /* 5793 */ 'v', 's', 'u', 'b', 's', 'w', 's', 32, 0,
  /* 5802 */ 'v', 'a', 'd', 'd', 's', 'w', 's', 32, 0,
  /* 5811 */ 'v', 's', 'u', 'm', 's', 'w', 's', 32, 0,
  /* 5820 */ 'v', 's', 'u', 'b', 'u', 'w', 's', 32, 0,
  /* 5829 */ 'v', 'a', 'd', 'd', 'u', 'w', 's', 32, 0,
  /* 5838 */ 'e', 'v', 'd', 'i', 'v', 'w', 's', 32, 0,
  /* 5847 */ 'x', 's', 'c', 'v', 'd', 'p', 's', 'x', 'w', 's', 32, 0,
  /* 5859 */ 'x', 'v', 'c', 'v', 'd', 'p', 's', 'x', 'w', 's', 32, 0,
  /* 5871 */ 'x', 'v', 'c', 'v', 's', 'p', 's', 'x', 'w', 's', 32, 0,
  /* 5883 */ 'x', 's', 'c', 'v', 'd', 'p', 'u', 'x', 'w', 's', 32, 0,
  /* 5895 */ 'x', 'v', 'c', 'v', 'd', 'p', 'u', 'x', 'w', 's', 32, 0,
  /* 5907 */ 'x', 'v', 'c', 'v', 's', 'p', 'u', 'x', 'w', 's', 32, 0,
  /* 5919 */ 'v', 'c', 't', 's', 'x', 's', 32, 0,
  /* 5927 */ 'v', 'c', 't', 'u', 'x', 's', 32, 0,
  /* 5935 */ 'e', 'v', 'l', 'h', 'h', 'e', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 5948 */ 'e', 'v', 'l', 'w', 'h', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 5960 */ 'e', 'v', 'l', 'h', 'h', 'o', 's', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 5974 */ 'e', 'v', 'l', 'h', 'h', 'o', 'u', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 5988 */ 'e', 'v', 'l', 'w', 'w', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 6000 */ 'd', 'c', 'b', 't', 32, 0,
  /* 6006 */ 'w', 'a', 'i', 't', 32, 0,
  /* 6012 */ 'f', 's', 'q', 'r', 't', 32, 0,
  /* 6019 */ 'd', 'c', 'b', 's', 't', 32, 0,
  /* 6026 */ 'd', 's', 't', 32, 0,
  /* 6031 */ 'd', 'c', 'b', 't', 's', 't', 32, 0,
  /* 6039 */ 'd', 's', 't', 's', 't', 32, 0,
  /* 6046 */ 'd', 's', 't', 't', 32, 0,
  /* 6052 */ 'd', 's', 't', 's', 't', 't', 32, 0,
  /* 6060 */ 'l', 'h', 'a', 'u', 32, 0,
  /* 6066 */ 's', 't', 'b', 'u', 32, 0,
  /* 6072 */ 'l', 'f', 'd', 'u', 32, 0,
  /* 6078 */ 's', 't', 'f', 'd', 'u', 32, 0,
  /* 6085 */ 'm', 'u', 'l', 'h', 'd', 'u', 32, 0,
  /* 6093 */ 'f', 'c', 'f', 'i', 'd', 'u', 32, 0,
  /* 6101 */ 'l', 'd', 'u', 32, 0,
  /* 6106 */ 's', 't', 'd', 'u', 32, 0,
  /* 6112 */ 'd', 'i', 'v', 'd', 'u', 32, 0,
  /* 6119 */ 's', 't', 'h', 'u', 32, 0,
  /* 6125 */ 'e', 'v', 's', 'r', 'w', 'i', 'u', 32, 0,
  /* 6134 */ 'e', 'v', 'l', 'w', 'h', 'o', 'u', 32, 0,
  /* 6143 */ 'f', 'c', 'm', 'p', 'u', 32, 0,
  /* 6150 */ 'l', 'f', 's', 'u', 32, 0,
  /* 6156 */ 's', 't', 'f', 's', 'u', 32, 0,
  /* 6163 */ 'e', 'v', 'c', 'm', 'p', 'g', 't', 'u', 32, 0,
  /* 6173 */ 'e', 'v', 'c', 'm', 'p', 'l', 't', 'u', 32, 0,
  /* 6183 */ 'm', 'u', 'l', 'h', 'w', 'u', 32, 0,
  /* 6191 */ 'e', 'v', 's', 'r', 'w', 'u', 32, 0,
  /* 6199 */ 's', 't', 'w', 'u', 32, 0,
  /* 6205 */ 'e', 'v', 'd', 'i', 'v', 'w', 'u', 32, 0,
  /* 6214 */ 'l', 'b', 'z', 'u', 32, 0,
  /* 6220 */ 'l', 'h', 'z', 'u', 32, 0,
  /* 6226 */ 'l', 'w', 'z', 'u', 32, 0,
  /* 6232 */ 'f', 'd', 'i', 'v', 32, 0,
  /* 6238 */ 'c', 'r', 'e', 'q', 'v', 32, 0,
  /* 6245 */ 'e', 'v', 'e', 'q', 'v', 32, 0,
  /* 6252 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 'a', 'a', 'w', 32, 0,
  /* 6265 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 'a', 'a', 'w', 32, 0,
  /* 6278 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 'a', 'a', 'w', 32, 0,
  /* 6291 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 'a', 'a', 'w', 32, 0,
  /* 6304 */ 'e', 'v', 'a', 'd', 'd', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6317 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6330 */ 'e', 'v', 's', 'u', 'b', 'f', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6344 */ 'e', 'v', 'm', 'w', 'l', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6357 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6370 */ 'e', 'v', 'a', 'd', 'd', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6383 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6396 */ 'e', 'v', 's', 'u', 'b', 'f', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6410 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6423 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 6436 */ 'e', 'v', 'a', 'd', 'd', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6449 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6462 */ 'e', 'v', 's', 'u', 'b', 'f', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6476 */ 'e', 'v', 'm', 'w', 'l', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6489 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6502 */ 'e', 'v', 'a', 'd', 'd', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6515 */ 'e', 'v', 'm', 'h', 'e', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6528 */ 'e', 'v', 's', 'u', 'b', 'f', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6542 */ 'e', 'v', 'm', 'w', 'l', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6555 */ 'e', 'v', 'm', 'h', 'o', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 6568 */ 'v', 's', 'r', 'a', 'w', 32, 0,
  /* 6575 */ 'e', 'v', 'a', 'd', 'd', 'w', 32, 0,
  /* 6583 */ 'e', 'v', 'l', 'd', 'w', 32, 0,
  /* 6590 */ 'e', 'v', 'r', 'n', 'd', 'w', 32, 0,
  /* 6598 */ 'e', 'v', 's', 't', 'd', 'w', 32, 0,
  /* 6606 */ 'e', 'v', 's', 'u', 'b', 'f', 'w', 32, 0,
  /* 6615 */ 'e', 'v', 's', 'u', 'b', 'i', 'f', 'w', 32, 0,
  /* 6625 */ 'v', 'm', 'r', 'g', 'h', 'w', 32, 0,
  /* 6633 */ 'x', 'x', 'm', 'r', 'g', 'h', 'w', 32, 0,
  /* 6642 */ 'm', 'u', 'l', 'h', 'w', 32, 0,
  /* 6649 */ 'e', 'v', 'a', 'd', 'd', 'i', 'w', 32, 0,
  /* 6658 */ 'f', 'c', 't', 'i', 'w', 32, 0,
  /* 6665 */ 'v', 'm', 'r', 'g', 'l', 'w', 32, 0,
  /* 6673 */ 'x', 'x', 'm', 'r', 'g', 'l', 'w', 32, 0,
  /* 6682 */ 'm', 'u', 'l', 'l', 'w', 32, 0,
  /* 6689 */ 'c', 'm', 'p', 'l', 'w', 32, 0,
  /* 6696 */ 'e', 'v', 'r', 'l', 'w', 32, 0,
  /* 6703 */ 'e', 'v', 's', 'l', 'w', 32, 0,
  /* 6710 */ 'l', 'm', 'w', 32, 0,
  /* 6715 */ 's', 't', 'm', 'w', 32, 0,
  /* 6721 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 'a', 'n', 'w', 32, 0,
  /* 6734 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 'a', 'n', 'w', 32, 0,
  /* 6747 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 'a', 'n', 'w', 32, 0,
  /* 6760 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 'a', 'n', 'w', 32, 0,
  /* 6773 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 6786 */ 'e', 'v', 'm', 'w', 'l', 's', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 6799 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 6812 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 6825 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 6838 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 6851 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 6864 */ 'e', 'v', 'm', 'w', 'l', 's', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 6877 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 6890 */ 'e', 'v', 'm', 'h', 'e', 'u', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 6903 */ 'e', 'v', 'm', 'w', 'l', 'u', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 6916 */ 'e', 'v', 'm', 'h', 'o', 'u', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 6929 */ 'c', 'm', 'p', 'w', 32, 0,
  /* 6935 */ 'v', 's', 'r', 'w', 32, 0,
  /* 6941 */ 'v', 'a', 'v', 'g', 's', 'w', 32, 0,
  /* 6949 */ 'v', 's', 'p', 'l', 't', 'i', 's', 'w', 32, 0,
  /* 6959 */ 'e', 'v', 'c', 'n', 't', 'l', 's', 'w', 32, 0,
  /* 6969 */ 'v', 'm', 'i', 'n', 's', 'w', 32, 0,
  /* 6977 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'w', 32, 0,
  /* 6987 */ 'e', 'x', 't', 's', 'w', 32, 0,
  /* 6994 */ 'v', 'm', 'a', 'x', 's', 'w', 32, 0,
  /* 7002 */ 'v', 's', 'p', 'l', 't', 'w', 32, 0,
  /* 7010 */ 'x', 'x', 's', 'p', 'l', 't', 'w', 32, 0,
  /* 7019 */ 'p', 'o', 'p', 'c', 'n', 't', 'w', 32, 0,
  /* 7028 */ 's', 't', 'w', 32, 0,
  /* 7033 */ 'v', 's', 'u', 'b', 'c', 'u', 'w', 32, 0,
  /* 7042 */ 'v', 'a', 'd', 'd', 'c', 'u', 'w', 32, 0,
  /* 7051 */ 'v', 'a', 'v', 'g', 'u', 'w', 32, 0,
  /* 7059 */ 'v', 'm', 'i', 'n', 'u', 'w', 32, 0,
  /* 7067 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'w', 32, 0,
  /* 7077 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'w', 32, 0,
  /* 7087 */ 'v', 'm', 'a', 'x', 'u', 'w', 32, 0,
  /* 7095 */ 'd', 'i', 'v', 'w', 32, 0,
  /* 7101 */ 'e', 'v', 'c', 'n', 't', 'l', 'z', 'w', 32, 0,
  /* 7111 */ 'l', 'x', 'v', 'd', '2', 'x', 32, 0,
  /* 7119 */ 's', 't', 'x', 'v', 'd', '2', 'x', 32, 0,
  /* 7128 */ 'l', 'x', 'v', 'w', '4', 'x', 32, 0,
  /* 7136 */ 's', 't', 'x', 'v', 'w', '4', 'x', 32, 0,
  /* 7145 */ 'l', 'h', 'a', 'x', 32, 0,
  /* 7151 */ 't', 'l', 'b', 'i', 'v', 'a', 'x', 32, 0,
  /* 7160 */ 'l', 'f', 'i', 'w', 'a', 'x', 32, 0,
  /* 7168 */ 'l', 'w', 'a', 'x', 32, 0,
  /* 7174 */ 'l', 'v', 'e', 'b', 'x', 32, 0,
  /* 7181 */ 's', 't', 'v', 'e', 'b', 'x', 32, 0,
  /* 7189 */ 's', 't', 'b', 'x', 32, 0,
  /* 7195 */ 'e', 'v', 'l', 'd', 'd', 'x', 32, 0,
  /* 7203 */ 'e', 'v', 's', 't', 'd', 'd', 'x', 32, 0,
  /* 7212 */ 'l', 'f', 'd', 'x', 32, 0,
  /* 7218 */ 's', 't', 'f', 'd', 'x', 32, 0,
  /* 7225 */ 'l', 'd', 'x', 32, 0,
  /* 7230 */ 'l', 'x', 's', 'd', 'x', 32, 0,
  /* 7237 */ 's', 't', 'x', 's', 'd', 'x', 32, 0,
  /* 7245 */ 's', 't', 'd', 'x', 32, 0,
  /* 7251 */ 'e', 'v', 'l', 'w', 'h', 'e', 'x', 32, 0,
  /* 7260 */ 'e', 'v', 's', 't', 'w', 'h', 'e', 'x', 32, 0,
  /* 7270 */ 'e', 'v', 's', 't', 'w', 'w', 'e', 'x', 32, 0,
  /* 7280 */ 'e', 'v', 'l', 'd', 'h', 'x', 32, 0,
  /* 7288 */ 'e', 'v', 's', 't', 'd', 'h', 'x', 32, 0,
  /* 7297 */ 'l', 'v', 'e', 'h', 'x', 32, 0,
  /* 7304 */ 's', 't', 'v', 'e', 'h', 'x', 32, 0,
  /* 7312 */ 's', 't', 'h', 'x', 32, 0,
  /* 7318 */ 'e', 'v', 's', 't', 'w', 'h', 'o', 'x', 32, 0,
  /* 7328 */ 'e', 'v', 's', 't', 'w', 'w', 'o', 'x', 32, 0,
  /* 7338 */ 'v', 'u', 'p', 'k', 'h', 'p', 'x', 32, 0,
  /* 7347 */ 'v', 'p', 'k', 'p', 'x', 32, 0,
  /* 7354 */ 'v', 'u', 'p', 'k', 'l', 'p', 'x', 32, 0,
  /* 7363 */ 'l', 'd', 'a', 'r', 'x', 32, 0,
  /* 7370 */ 'l', 'w', 'a', 'r', 'x', 32, 0,
  /* 7377 */ 'l', 'd', 'b', 'r', 'x', 32, 0,
  /* 7384 */ 's', 't', 'd', 'b', 'r', 'x', 32, 0,
  /* 7392 */ 'l', 'h', 'b', 'r', 'x', 32, 0,
  /* 7399 */ 's', 't', 'h', 'b', 'r', 'x', 32, 0,
  /* 7407 */ 'l', 'w', 'b', 'r', 'x', 32, 0,
  /* 7414 */ 's', 't', 'w', 'b', 'r', 'x', 32, 0,
  /* 7422 */ 't', 'l', 'b', 's', 'x', 32, 0,
  /* 7429 */ 'l', 'x', 'v', 'd', 's', 'x', 32, 0,
  /* 7437 */ 'v', 'c', 'f', 's', 'x', 32, 0,
  /* 7444 */ 'l', 'f', 's', 'x', 32, 0,
  /* 7450 */ 's', 't', 'f', 's', 'x', 32, 0,
  /* 7457 */ 'e', 'v', 'l', 'w', 'h', 'o', 's', 'x', 32, 0,
  /* 7467 */ 'e', 'v', 'l', 'h', 'h', 'e', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 7481 */ 'e', 'v', 'l', 'w', 'h', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 7494 */ 'e', 'v', 'l', 'h', 'h', 'o', 's', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 7509 */ 'e', 'v', 'l', 'h', 'h', 'o', 'u', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 7524 */ 'e', 'v', 'l', 'w', 'w', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 7537 */ 'l', 'h', 'a', 'u', 'x', 32, 0,
  /* 7544 */ 'l', 'w', 'a', 'u', 'x', 32, 0,
  /* 7551 */ 's', 't', 'b', 'u', 'x', 32, 0,
  /* 7558 */ 'l', 'f', 'd', 'u', 'x', 32, 0,
  /* 7565 */ 's', 't', 'f', 'd', 'u', 'x', 32, 0,
  /* 7573 */ 'l', 'd', 'u', 'x', 32, 0,
  /* 7579 */ 's', 't', 'd', 'u', 'x', 32, 0,
  /* 7586 */ 'v', 'c', 'f', 'u', 'x', 32, 0,
  /* 7593 */ 's', 't', 'h', 'u', 'x', 32, 0,
  /* 7600 */ 'e', 'v', 'l', 'w', 'h', 'o', 'u', 'x', 32, 0,
  /* 7610 */ 'l', 'f', 's', 'u', 'x', 32, 0,
  /* 7617 */ 's', 't', 'f', 's', 'u', 'x', 32, 0,
  /* 7625 */ 's', 't', 'w', 'u', 'x', 32, 0,
  /* 7632 */ 'l', 'b', 'z', 'u', 'x', 32, 0,
  /* 7639 */ 'l', 'h', 'z', 'u', 'x', 32, 0,
  /* 7646 */ 'l', 'w', 'z', 'u', 'x', 32, 0,
  /* 7653 */ 'l', 'v', 'x', 32, 0,
  /* 7658 */ 's', 't', 'v', 'x', 32, 0,
  /* 7664 */ 'e', 'v', 'l', 'd', 'w', 'x', 32, 0,
  /* 7672 */ 'e', 'v', 's', 't', 'd', 'w', 'x', 32, 0,
  /* 7681 */ 'l', 'v', 'e', 'w', 'x', 32, 0,
  /* 7688 */ 's', 't', 'v', 'e', 'w', 'x', 32, 0,
  /* 7696 */ 's', 't', 'f', 'i', 'w', 'x', 32, 0,
  /* 7704 */ 's', 't', 'w', 'x', 32, 0,
  /* 7710 */ 'l', 'b', 'z', 'x', 32, 0,
  /* 7716 */ 'l', 'h', 'z', 'x', 32, 0,
  /* 7722 */ 'l', 'f', 'i', 'w', 'z', 'x', 32, 0,
  /* 7730 */ 'l', 'w', 'z', 'x', 32, 0,
  /* 7736 */ 'd', 'c', 'b', 'z', 32, 0,
  /* 7742 */ 'l', 'b', 'z', 32, 0,
  /* 7747 */ 'b', 'd', 'z', 32, 0,
  /* 7752 */ 'f', 'c', 't', 'i', 'd', 'z', 32, 0,
  /* 7760 */ 'l', 'h', 'z', 32, 0,
  /* 7765 */ 'v', 'r', 'f', 'i', 'z', 32, 0,
  /* 7772 */ 'x', 's', 'r', 'd', 'p', 'i', 'z', 32, 0,
  /* 7781 */ 'x', 'v', 'r', 'd', 'p', 'i', 'z', 32, 0,
  /* 7790 */ 'x', 'v', 'r', 's', 'p', 'i', 'z', 32, 0,
  /* 7799 */ 'f', 'r', 'i', 'z', 32, 0,
  /* 7805 */ 'b', 'd', 'n', 'z', 32, 0,
  /* 7811 */ 'f', 'c', 't', 'i', 'd', 'u', 'z', 32, 0,
  /* 7820 */ 'f', 'c', 't', 'i', 'w', 'u', 'z', 32, 0,
  /* 7829 */ 'f', 'c', 't', 'i', 'w', 'z', 32, 0,
  /* 7837 */ 'l', 'w', 'z', 32, 0,
  /* 7842 */ 'b', 'd', 'z', 'l', 'r', 'l', '+', 0,
  /* 7850 */ 'b', 'd', 'n', 'z', 'l', 'r', 'l', '+', 0,
  /* 7859 */ 'b', 'd', 'z', 'l', 'r', '+', 0,
  /* 7866 */ 'b', 'd', 'n', 'z', 'l', 'r', '+', 0,
  /* 7874 */ 'b', 'd', 'z', 'l', 'r', 'l', '-', 0,
  /* 7882 */ 'b', 'd', 'n', 'z', 'l', 'r', 'l', '-', 0,
  /* 7891 */ 'b', 'd', 'z', 'l', 'r', '-', 0,
  /* 7898 */ 'b', 'd', 'n', 'z', 'l', 'r', '-', 0,
  /* 7906 */ 'o', 'r', 'i', 32, '1', ',', 32, '1', ',', 32, '0', 0,
  /* 7918 */ 'o', 'r', 'i', 32, '2', ',', 32, '2', ',', 32, '0', 0,
  /* 7930 */ '#', 'A', 'D', 'D', 'I', 'S', 'd', 't', 'p', 'r', 'e', 'l', 'H', 'A', '3', '2', 0,
  /* 7947 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '3', '2', 0,
  /* 7968 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '3', '2', 0,
  /* 7989 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '3', '2', 0,
  /* 8011 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'I', '3', '2', 0,
  /* 8032 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'I', '3', '2', 0,
  /* 8049 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '3', '2', 0,
  /* 8070 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '3', '2', 0,
  /* 8090 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'g', 'd', 'L', '3', '2', 0,
  /* 8104 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'l', 'd', 'L', '3', '2', 0,
  /* 8118 */ '#', 'L', 'D', 'g', 'o', 't', 'T', 'p', 'r', 'e', 'l', 'L', '3', '2', 0,
  /* 8133 */ '#', 'A', 'D', 'D', 'I', 'd', 't', 'p', 'r', 'e', 'l', 'L', '3', '2', 0,
  /* 8148 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 8167 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 8185 */ '#', 'G', 'E', 'T', 't', 'l', 's', 'l', 'd', 'A', 'D', 'D', 'R', '3', '2', 0,
  /* 8201 */ '#', 'G', 'E', 'T', 't', 'l', 's', 'A', 'D', 'D', 'R', '3', '2', 0,
  /* 8215 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '6', '4', 0,
  /* 8236 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '6', '4', 0,
  /* 8257 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '6', '4', 0,
  /* 8279 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'I', '6', '4', 0,
  /* 8296 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '6', '4', 0,
  /* 8317 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '6', '4', 0,
  /* 8338 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '6', '4', 0,
  /* 8358 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 8377 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 8395 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'i', '6', '4', 0,
  /* 8416 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'F', '4', 0,
  /* 8430 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'F', '4', 0,
  /* 8441 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'I', '4', 0,
  /* 8455 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'I', '4', 0,
  /* 8466 */ 'c', 'r', 'x', 'o', 'r', 32, '6', ',', 32, '6', ',', 32, '6', 0,
  /* 8480 */ 'c', 'r', 'e', 'q', 'v', 32, '6', ',', 32, '6', ',', 32, '6', 0,
  /* 8494 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '1', '6', 0,
  /* 8515 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '1', '6', 0,
  /* 8536 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '1', '6', 0,
  /* 8558 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'I', '1', '6', 0,
  /* 8579 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'I', '1', '6', 0,
  /* 8596 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '1', '6', 0,
  /* 8617 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '1', '6', 0,
  /* 8637 */ '#', 'D', 'Y', 'N', 'A', 'L', 'L', 'O', 'C', '8', 0,
  /* 8648 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'F', '8', 0,
  /* 8662 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'F', '8', 0,
  /* 8673 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '8', 0,
  /* 8693 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'I', '8', 0,
  /* 8707 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '8', 0,
  /* 8727 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '8', 0,
  /* 8748 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'I', '8', 0,
  /* 8768 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '8', 0,
  /* 8788 */ 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '8', 0,
  /* 8807 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '8', 0,
  /* 8826 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'I', '8', 0,
  /* 8837 */ '#', 'M', 'o', 'v', 'e', 'P', 'C', 't', 'o', 'L', 'R', '8', 0,
  /* 8850 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'E', 'Q', '_', 'B', 'I', 'T', '8', 0,
  /* 8867 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'G', 'T', '_', 'B', 'I', 'T', '8', 0,
  /* 8884 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'i', '8', 0,
  /* 8900 */ '#', 'A', 'D', 'D', 'I', 'S', 't', 'o', 'c', 'H', 'A', 0,
  /* 8912 */ '#', 'A', 'D', 'D', 'I', 'S', 't', 'l', 's', 'g', 'd', 'H', 'A', 0,
  /* 8926 */ '#', 'A', 'D', 'D', 'I', 'S', 't', 'l', 's', 'l', 'd', 'H', 'A', 0,
  /* 8940 */ '#', 'A', 'D', 'D', 'I', 'S', 'g', 'o', 't', 'T', 'p', 'r', 'e', 'l', 'H', 'A', 0,
  /* 8957 */ '#', 'A', 'D', 'D', 'I', 'S', 'd', 't', 'p', 'r', 'e', 'l', 'H', 'A', 0,
  /* 8972 */ '#', 'D', 'Y', 'N', 'A', 'L', 'L', 'O', 'C', 0,
  /* 8982 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'V', 'R', 'R', 'C', 0,
  /* 8998 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'V', 'R', 'R', 'C', 0,
  /* 9011 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 9024 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 9031 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 9041 */ '#', 'R', 'E', 'S', 'T', 'O', 'R', 'E', '_', 'V', 'R', 'S', 'A', 'V', 'E', 0,
  /* 9057 */ '#', 'S', 'P', 'I', 'L', 'L', '_', 'V', 'R', 'S', 'A', 'V', 'E', 0,
  /* 9071 */ '#', 'L', 'D', 't', 'o', 'c', 'J', 'T', 'I', 0,
  /* 9081 */ '#', 'L', 'D', 't', 'o', 'c', 'L', 0,
  /* 9089 */ '#', 'A', 'D', 'D', 'I', 't', 'o', 'c', 'L', 0,
  /* 9099 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'g', 'd', 'L', 0,
  /* 9111 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'l', 'd', 'L', 0,
  /* 9123 */ '#', 'L', 'D', 'g', 'o', 't', 'T', 'p', 'r', 'e', 'l', 'L', 0,
  /* 9136 */ '#', 'A', 'D', 'D', 'I', 'd', 't', 'p', 'r', 'e', 'l', 'L', 0,
  /* 9149 */ '#', 'G', 'e', 't', 'G', 'B', 'R', 'O', 0,
  /* 9158 */ '#', 'U', 'p', 'd', 'a', 't', 'e', 'G', 'B', 'R', 0,
  /* 9169 */ '#', 'R', 'E', 'S', 'T', 'O', 'R', 'E', '_', 'C', 'R', 0,
  /* 9181 */ '#', 'S', 'P', 'I', 'L', 'L', '_', 'C', 'R', 0,
  /* 9191 */ '#', 'G', 'E', 'T', 't', 'l', 's', 'l', 'd', 'A', 'D', 'D', 'R', 0,
  /* 9205 */ '#', 'G', 'E', 'T', 't', 'l', 's', 'A', 'D', 'D', 'R', 0,
  /* 9217 */ '#', 'M', 'o', 'v', 'e', 'P', 'C', 't', 'o', 'L', 'R', 0,
  /* 9229 */ '#', 'R', 'E', 'S', 'T', 'O', 'R', 'E', '_', 'C', 'R', 'B', 'I', 'T', 0,
  /* 9244 */ '#', 'S', 'P', 'I', 'L', 'L', '_', 'C', 'R', 'B', 'I', 'T', 0,
  /* 9257 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'E', 'Q', '_', 'B', 'I', 'T', 0,
  /* 9273 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'G', 'T', '_', 'B', 'I', 'T', 0,
  /* 9289 */ '#', 'P', 'P', 'C', '3', '2', 'G', 'O', 'T', 0,
  /* 9299 */ '#', 'P', 'P', 'C', '3', '2', 'P', 'I', 'C', 'G', 'O', 'T', 0,
  /* 9312 */ '#', 'L', 'D', 't', 'o', 'c', 'C', 'P', 'T', 0,
  /* 9322 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 9337 */ 's', 'l', 'b', 'i', 'a', 0,
  /* 9343 */ 't', 'l', 'b', 'i', 'a', 0,
  /* 9349 */ 'b', 0,
  /* 9351 */ 't', 'l', 'b', 's', 'y', 'n', 'c', 0,
  /* 9359 */ 'i', 's', 'y', 'n', 'c', 0,
  /* 9365 */ 'm', 's', 'y', 'n', 'c', 0,
  /* 9371 */ '#', 'L', 'D', 't', 'o', 'c', 0,
  /* 9378 */ '#', 'L', 'W', 'Z', 't', 'o', 'c', 0,
  /* 9386 */ 'r', 'f', 'i', 'd', 0,
  /* 9391 */ 't', 'l', 'b', 'r', 'e', 0,
  /* 9397 */ 't', 'l', 'b', 'w', 'e', 0,
  /* 9403 */ 'r', 'f', 'c', 'i', 0,
  /* 9408 */ 'r', 'f', 'm', 'c', 'i', 0,
  /* 9414 */ 'r', 'f', 'd', 'i', 0,
  /* 9419 */ 'r', 'f', 'i', 0,
  /* 9423 */ 'd', 's', 's', 'a', 'l', 'l', 0,
  /* 9430 */ 'b', 'l', 'r', 'l', 0,
  /* 9435 */ 'b', 'd', 'z', 'l', 'r', 'l', 0,
  /* 9442 */ 'b', 'd', 'n', 'z', 'l', 'r', 'l', 0,
  /* 9450 */ 'b', 'c', 't', 'r', 'l', 0,
  /* 9456 */ 'e', 'i', 'e', 'i', 'o', 0,
  /* 9462 */ 't', 'r', 'a', 'p', 0,
  /* 9467 */ 'n', 'o', 'p', 0,
  /* 9471 */ 'b', 'l', 'r', 0,
  /* 9475 */ 'b', 'd', 'z', 'l', 'r', 0,
  /* 9481 */ 'b', 'd', 'n', 'z', 'l', 'r', 0,
  /* 9488 */ 'b', 'c', 't', 'r', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint64_t Bits1 = OpInfo[MCInst_getOpcode(MI)];
  uint64_t Bits2 = OpInfo2[MCInst_getOpcode(MI)];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 16383)-1);
#endif


  // Fragment 0 encoded into 4 bits for 13 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 14) & 15);
  switch ((Bits >> 14) & 15) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, ADDISdtprelHA, ADDISd...
    return;
    break;
  case 1:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADJCALLSTACKDOWN, ADJCALLSTACKUP
    printU16ImmOperand(MI, 0, O); 
    break;
  case 3:
    // B, BCLalways, BDNZ, BDNZ8, BDNZL, BDNZLm, BDNZLp, BDNZm, BDNZp, BDZ, B...
    printBranchOperand(MI, 0, O); 
    break;
  case 4:
    // BA, BDNZA, BDNZAm, BDNZAp, BDNZLA, BDNZLAm, BDNZLAp, BDZA, BDZAm, BDZA...
    printAbsBranchOperand(MI, 0, O); 
    break;
  case 5:
    // BCC, BCCA, BCCCTR, BCCCTR8, BCCCTRL, BCCCTRL8, BCCL, BCCLA, BCCLR, BCC...
    printPredicateOperand(MI, 0, O, "cc"); 
    break;
  case 6:
    // BL8_NOP_TLS, BL8_TLS, BL8_TLS_, BL_TLS
    printTLSCall(MI, 0, O); 
    break;
  case 7:
    // DCBA, DCBF, DCBI, DCBST, DCBT, DCBTST, DCBZ, DCBZL, ICBI
    printMemRegReg(MI, 0, O); 
    return;
    break;
  case 8:
    // DSS, MBAR, MTFSB0, MTFSB1, TD, TDI, TW, TWI, gBC, gBCA, gBCCTR, gBCCTR...
    printU5ImmOperand(MI, 0, O); 
    break;
  case 9:
    // DST, DST64, DSTST, DSTST64, DSTSTT, DSTSTT64, DSTT, DSTT64, MTDCR, MTV...
    printOperand(MI, 1, O); 
    break;
  case 10:
    // LDinto_toc
    printMemRegImm(MI, 0, O); 
    return;
    break;
  case 11:
    // MTOCRF, MTOCRF8
    printcrbitm(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 12:
    // MTSR
    printU4ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 5 bits for 17 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 18) & 31);
  switch ((Bits >> 18) & 31) {
  default:   // unreachable.
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // ADJCALLSTACKDOWN, B, BA, BCLalways, BDNZ, BDNZ8, BDNZA, BDNZAm, BDNZAp...
    return;
    break;
  case 2:
    // ADJCALLSTACKUP, ATOMIC_CMP_SWAP_I16, ATOMIC_CMP_SWAP_I32, TCRETURNai, ...
    SStream_concat0(O, " "); 
    break;
  case 3:
    // BCC
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    SStream_concat0(O, ", "); 
    printBranchOperand(MI, 2, O); 
    return;
    break;
  case 4:
    // BCCA
    SStream_concat0(O, "a");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    SStream_concat0(O, ", "); 
    printAbsBranchOperand(MI, 2, O); 
    return;
    break;
  case 5:
    // BCCCTR, BCCCTR8
    SStream_concat0(O, "ctr");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    return;
    break;
  case 6:
    // BCCCTRL, BCCCTRL8
    SStream_concat0(O, "ctrl");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    return;
    break;
  case 7:
    // BCCL
    SStream_concat0(O, "l");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    SStream_concat0(O, ", "); 
    printBranchOperand(MI, 2, O); 
    return;
    break;
  case 8:
    // BCCLA
    SStream_concat0(O, "la");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    SStream_concat0(O, ", "); 
    printAbsBranchOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // BCCLR
    SStream_concat0(O, "lr");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    return;
    break;
  case 10:
    // BCCLRL
    SStream_concat0(O, "lrl");
    printPredicateOperand(MI, 0, O, "pm");
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg");
    return;
    break;
  case 11:
    // BCCTR, BCCTR8, BCCTR8n, BCCTRL, BCCTRL8, BCCTRL8n, BCCTRLn, BCCTRn, BC...
    SStream_concat0(O, ", 0"); 
    return;
    break;
  case 12:
    // BL8_NOP, BL8_NOP_TLS, BLA8_NOP
    // SStream_concat0(O, "\n\tnop"); 	// qq
    return;
    break;
  case 13:
    // MFTB8
    SStream_concat0(O, ", 268");
	op_addImm(MI, 268);
    return;
    break;
  case 14:
    // MFVRSAVE, MFVRSAVEv
    SStream_concat0(O, ", 256");
	op_addImm(MI, 256);
    return;
    break;
  case 15:
    // TLBIE
    SStream_concat0(O, ","); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 16:
    // V_SETALLONES, V_SETALLONESB, V_SETALLONESH
    SStream_concat0(O, ", -1");
	op_addImm(MI, -1);
    return;
    break;
  }


  // Fragment 2 encoded into 4 bits for 16 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 23) & 15);
  switch ((Bits >> 23) & 15) {
  default:   // unreachable.
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    printOperand(MI, 1, O); 
    break;
  case 1:
    // ADJCALLSTACKUP
    printU16ImmOperand(MI, 1, O); 
    return;
    break;
  case 2:
    // ATOMIC_CMP_SWAP_I16, ATOMIC_CMP_SWAP_I32, LBZX, LBZX8, LDARX, LDBRX, L...
    printMemRegReg(MI, 1, O); 
    break;
  case 3:
    // BC, BCL, BCLn, BCn
    printBranchOperand(MI, 1, O); 
    return;
    break;
  case 4:
    // CRSET, CRUNSET, MTDCR, V_SET0, V_SET0B, V_SET0H
    printOperand(MI, 0, O); 
    break;
  case 5:
    // DST, DST64, DSTST, DSTST64, DSTSTT, DSTSTT64, DSTT, DSTT64, RLDIMI, RL...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // EVADDIW
    printU5ImmOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // EVLDD, EVLDH, EVLDW, EVLHHESPLAT, EVLHHOSSPLAT, EVLHHOUSPLAT, EVLWHE, ...
    printMemRegImm(MI, 1, O); 
    return;
    break;
  case 8:
    // EVSUBIFW
    printU5ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // LA
    printS16ImmOperand(MI, 2, O); 
    SStream_concat0(O, "("); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ")"); 
    return;
    break;
  case 10:
    // LBZU, LBZU8, LDU, LFDU, LFSU, LHAU, LHAU8, LHZU, LHZU8, LWZU, LWZU8, S...
    printMemRegImm(MI, 2, O); 
    return;
    break;
  case 11:
    // LBZUX, LBZUX8, LDUX, LFDUX, LFSUX, LHAUX, LHAUX8, LHZUX, LHZUX8, LWAUX...
    printMemRegReg(MI, 2, O); 
    return;
    break;
  case 12:
    // LI, LI8, LIS, LIS8
    printS16ImmOperand(MI, 1, O); 
    return;
    break;
  case 13:
    // MFOCRF, MFOCRF8
    printcrbitm(MI, 1, O); 
    return;
    break;
  case 14:
    // MFSR
    printU4ImmOperand(MI, 1, O); 
    return;
    break;
  case 15:
    // VSPLTISB, VSPLTISH, VSPLTISW
    printS5ImmOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 4 bits for 9 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 27) & 15);
  switch ((Bits >> 27) & 15) {
  default:   // unreachable.
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // ADDME, ADDME8, ADDME8o, ADDMEo, ADDZE, ADDZE8, ADDZE8o, ADDZEo, CNTLZD...
    return;
    break;
  case 2:
    // ATOMIC_CMP_SWAP_I16, ATOMIC_CMP_SWAP_I32
    SStream_concat0(O, " "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, " "); 
    printOperand(MI, 4, O); 
    return;
    break;
  case 3:
    // DST, DST64, DSTST, DSTST64, DSTSTT, DSTSTT64, DSTT, DSTT64
    printU5ImmOperand(MI, 0, O); 
    return;
    break;
  case 4:
    // RLDIMI, RLDIMIo
    printU6ImmOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printU6ImmOperand(MI, 4, O); 
    return;
    break;
  case 5:
    // RLWIMI, RLWIMI8, RLWIMI8o, RLWIMIo
    printU5ImmOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printU5ImmOperand(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printU5ImmOperand(MI, 5, O); 
    return;
    break;
  case 6:
    // VCFSX, VCFUX, VCTSXS, VCTUXS, VSPLTB, VSPLTH, VSPLTW
    printU5ImmOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // VCFSX_0, VCFUX_0, VCTSXS_0, VCTUXS_0
    SStream_concat0(O, ", 0"); 
    return;
    break;
  case 8:
    // XSMADDADP, XSMADDMDP, XSMSUBADP, XSMSUBMDP, XSNMADDADP, XSNMADDMDP, XS...
    printOperand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 4 bits for 9 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 31) & 15);
  switch ((Bits >> 31) & 15) {
  default:   // unreachable.
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    printOperand(MI, 2, O); 
    break;
  case 1:
    // ADDI, ADDI8, ADDIC, ADDIC8, ADDICo, ADDIS, ADDIS8, CMPDI, CMPWI, MULLI...
    printS16ImmOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // ANDISo, ANDISo8, ANDIo, ANDIo8, CMPLDI, CMPLWI, ORI, ORI8, ORIS, ORIS8...
    printU16ImmOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // CLRLSLDI, CLRLSLDIo, CLRRDI, CLRRDIo, EXTLDI, EXTLDIo, EXTRDI, EXTRDIo...
    printU6ImmOperand(MI, 2, O); 
    break;
  case 4:
    // CLRLSLWI, CLRLSLWIo, CLRRWI, CLRRWIo, EVRLWI, EVSLWI, EVSRWIS, EVSRWIU...
    printU5ImmOperand(MI, 2, O); 
    break;
  case 5:
    // CRSET, CRUNSET, V_SET0, V_SET0B, V_SET0H
    printOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // XXSPLTW
    printU2ImmOperand(MI, 2, O); 
    return;
    break;
  case 7:
    // gBC, gBCL
    printBranchOperand(MI, 2, O); 
    return;
    break;
  case 8:
    // gBCA, gBCLA
    printAbsBranchOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 1 bits for 2 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 35) & 1);
  if ((Bits >> 35) & 1) {
    // CLRLSLDI, CLRLSLDIo, CLRLSLWI, CLRLSLWIo, EXTLDI, EXTLDIo, EXTLWI, EXT...
    SStream_concat0(O, ", "); 
  } else {
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    return;
  }


  // Fragment 6 encoded into 2 bits for 4 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 36) & 3);
  switch ((Bits >> 36) & 3) {
  default:   // unreachable.
  case 0:
    // CLRLSLDI, CLRLSLDIo, EXTLDI, EXTLDIo, EXTRDI, EXTRDIo, INSRDI, INSRDIo...
    printU6ImmOperand(MI, 3, O); 
    return;
    break;
  case 1:
    // CLRLSLWI, CLRLSLWIo, EXTLWI, EXTLWIo, EXTRWI, EXTRWIo, INSLWI, INSLWIo...
    printU5ImmOperand(MI, 3, O); 
    break;
  case 2:
    // FMADD, FMADDS, FMADDSo, FMADDo, FMSUB, FMSUBS, FMSUBSo, FMSUBo, FNMADD...
    printOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // XXPERMDI, XXSLDWI
    printU2ImmOperand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 7 encoded into 1 bits for 2 unique commands.
  //printf("Frag-7: %"PRIu64"\n", (Bits >> 38) & 1);
  if ((Bits >> 38) & 1) {
    // RLWINM, RLWINM8, RLWINM8o, RLWINMo, RLWNM, RLWNMo
    SStream_concat0(O, ", "); 
    printU5ImmOperand(MI, 4, O); 
    return;
  } else {
    // CLRLSLWI, CLRLSLWIo, EXTLWI, EXTLWIo, EXTRWI, EXTRWIo, INSLWI, INSLWIo...
    return;
  }
}


#ifndef CAPSTONE_DIET
/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 279 && "Invalid register number!");

  static char AsmStrs[] = {
  /* 0 */ '*', '*', 'R', 'O', 'U', 'N', 'D', 'I', 'N', 'G', 32, 'M', 'O', 'D', 'E', '*', '*', 0,
  /* 18 */ '*', '*', 'F', 'R', 'A', 'M', 'E', 32, 'P', 'O', 'I', 'N', 'T', 'E', 'R', '*', '*', 0,
  /* 36 */ '*', '*', 'B', 'A', 'S', 'E', 32, 'P', 'O', 'I', 'N', 'T', 'E', 'R', '*', '*', 0,
  /* 53 */ 'f', '1', '0', 0,
  /* 57 */ 'r', '1', '0', 0,
  /* 61 */ 'v', 's', '1', '0', 0,
  /* 66 */ 'v', '1', '0', 0,
  /* 70 */ 'f', '2', '0', 0,
  /* 74 */ 'r', '2', '0', 0,
  /* 78 */ 'v', 's', '2', '0', 0,
  /* 83 */ 'v', '2', '0', 0,
  /* 87 */ 'f', '3', '0', 0,
  /* 91 */ 'r', '3', '0', 0,
  /* 95 */ 'v', 's', '3', '0', 0,
  /* 100 */ 'v', '3', '0', 0,
  /* 104 */ 'v', 's', '4', '0', 0,
  /* 109 */ 'v', 's', '5', '0', 0,
  /* 114 */ 'v', 's', '6', '0', 0,
  /* 119 */ 'f', '0', 0,
  /* 122 */ 'c', 'r', '0', 0,
  /* 126 */ 'v', 's', '0', 0,
  /* 130 */ 'v', '0', 0,
  /* 133 */ 'f', '1', '1', 0,
  /* 137 */ 'r', '1', '1', 0,
  /* 141 */ 'v', 's', '1', '1', 0,
  /* 146 */ 'v', '1', '1', 0,
  /* 150 */ 'f', '2', '1', 0,
  /* 154 */ 'r', '2', '1', 0,
  /* 158 */ 'v', 's', '2', '1', 0,
  /* 163 */ 'v', '2', '1', 0,
  /* 167 */ 'f', '3', '1', 0,
  /* 171 */ 'r', '3', '1', 0,
  /* 175 */ 'v', 's', '3', '1', 0,
  /* 180 */ 'v', '3', '1', 0,
  /* 184 */ 'v', 's', '4', '1', 0,
  /* 189 */ 'v', 's', '5', '1', 0,
  /* 194 */ 'v', 's', '6', '1', 0,
  /* 199 */ 'f', '1', 0,
  /* 202 */ 'c', 'r', '1', 0,
  /* 206 */ 'v', 's', '1', 0,
  /* 210 */ 'v', '1', 0,
  /* 213 */ 'f', '1', '2', 0,
  /* 217 */ 'r', '1', '2', 0,
  /* 221 */ 'v', 's', '1', '2', 0,
  /* 226 */ 'v', '1', '2', 0,
  /* 230 */ 'f', '2', '2', 0,
  /* 234 */ 'r', '2', '2', 0,
  /* 238 */ 'v', 's', '2', '2', 0,
  /* 243 */ 'v', '2', '2', 0,
  /* 247 */ 'v', 's', '3', '2', 0,
  /* 252 */ 'v', 's', '4', '2', 0,
  /* 257 */ 'v', 's', '5', '2', 0,
  /* 262 */ 'v', 's', '6', '2', 0,
  /* 267 */ 'f', '2', 0,
  /* 270 */ 'c', 'r', '2', 0,
  /* 274 */ 'v', 's', '2', 0,
  /* 278 */ 'v', '2', 0,
  /* 281 */ 'f', '1', '3', 0,
  /* 285 */ 'r', '1', '3', 0,
  /* 289 */ 'v', 's', '1', '3', 0,
  /* 294 */ 'v', '1', '3', 0,
  /* 298 */ 'f', '2', '3', 0,
  /* 302 */ 'r', '2', '3', 0,
  /* 306 */ 'v', 's', '2', '3', 0,
  /* 311 */ 'v', '2', '3', 0,
  /* 315 */ 'v', 's', '3', '3', 0,
  /* 320 */ 'v', 's', '4', '3', 0,
  /* 325 */ 'v', 's', '5', '3', 0,
  /* 330 */ 'v', 's', '6', '3', 0,
  /* 335 */ 'f', '3', 0,
  /* 338 */ 'c', 'r', '3', 0,
  /* 342 */ 'v', 's', '3', 0,
  /* 346 */ 'v', '3', 0,
  /* 349 */ 'f', '1', '4', 0,
  /* 353 */ 'r', '1', '4', 0,
  /* 357 */ 'v', 's', '1', '4', 0,
  /* 362 */ 'v', '1', '4', 0,
  /* 366 */ 'f', '2', '4', 0,
  /* 370 */ 'r', '2', '4', 0,
  /* 374 */ 'v', 's', '2', '4', 0,
  /* 379 */ 'v', '2', '4', 0,
  /* 383 */ 'v', 's', '3', '4', 0,
  /* 388 */ 'v', 's', '4', '4', 0,
  /* 393 */ 'v', 's', '5', '4', 0,
  /* 398 */ 'f', '4', 0,
  /* 401 */ 'c', 'r', '4', 0,
  /* 405 */ 'v', 's', '4', 0,
  /* 409 */ 'v', '4', 0,
  /* 412 */ 'f', '1', '5', 0,
  /* 416 */ 'r', '1', '5', 0,
  /* 420 */ 'v', 's', '1', '5', 0,
  /* 425 */ 'v', '1', '5', 0,
  /* 429 */ 'f', '2', '5', 0,
  /* 433 */ 'r', '2', '5', 0,
  /* 437 */ 'v', 's', '2', '5', 0,
  /* 442 */ 'v', '2', '5', 0,
  /* 446 */ 'v', 's', '3', '5', 0,
  /* 451 */ 'v', 's', '4', '5', 0,
  /* 456 */ 'v', 's', '5', '5', 0,
  /* 461 */ 'f', '5', 0,
  /* 464 */ 'c', 'r', '5', 0,
  /* 468 */ 'v', 's', '5', 0,
  /* 472 */ 'v', '5', 0,
  /* 475 */ 'f', '1', '6', 0,
  /* 479 */ 'r', '1', '6', 0,
  /* 483 */ 'v', 's', '1', '6', 0,
  /* 488 */ 'v', '1', '6', 0,
  /* 492 */ 'f', '2', '6', 0,
  /* 496 */ 'r', '2', '6', 0,
  /* 500 */ 'v', 's', '2', '6', 0,
  /* 505 */ 'v', '2', '6', 0,
  /* 509 */ 'v', 's', '3', '6', 0,
  /* 514 */ 'v', 's', '4', '6', 0,
  /* 519 */ 'v', 's', '5', '6', 0,
  /* 524 */ 'f', '6', 0,
  /* 527 */ 'c', 'r', '6', 0,
  /* 531 */ 'v', 's', '6', 0,
  /* 535 */ 'v', '6', 0,
  /* 538 */ 'f', '1', '7', 0,
  /* 542 */ 'r', '1', '7', 0,
  /* 546 */ 'v', 's', '1', '7', 0,
  /* 551 */ 'v', '1', '7', 0,
  /* 555 */ 'f', '2', '7', 0,
  /* 559 */ 'r', '2', '7', 0,
  /* 563 */ 'v', 's', '2', '7', 0,
  /* 568 */ 'v', '2', '7', 0,
  /* 572 */ 'v', 's', '3', '7', 0,
  /* 577 */ 'v', 's', '4', '7', 0,
  /* 582 */ 'v', 's', '5', '7', 0,
  /* 587 */ 'f', '7', 0,
  /* 590 */ 'c', 'r', '7', 0,
  /* 594 */ 'v', 's', '7', 0,
  /* 598 */ 'v', '7', 0,
  /* 601 */ 'f', '1', '8', 0,
  /* 605 */ 'r', '1', '8', 0,
  /* 609 */ 'v', 's', '1', '8', 0,
  /* 614 */ 'v', '1', '8', 0,
  /* 618 */ 'f', '2', '8', 0,
  /* 622 */ 'r', '2', '8', 0,
  /* 626 */ 'v', 's', '2', '8', 0,
  /* 631 */ 'v', '2', '8', 0,
  /* 635 */ 'v', 's', '3', '8', 0,
  /* 640 */ 'v', 's', '4', '8', 0,
  /* 645 */ 'v', 's', '5', '8', 0,
  /* 650 */ 'f', '8', 0,
  /* 653 */ 'r', '8', 0,
  /* 656 */ 'v', 's', '8', 0,
  /* 660 */ 'v', '8', 0,
  /* 663 */ 'f', '1', '9', 0,
  /* 667 */ 'r', '1', '9', 0,
  /* 671 */ 'v', 's', '1', '9', 0,
  /* 676 */ 'v', '1', '9', 0,
  /* 680 */ 'f', '2', '9', 0,
  /* 684 */ 'r', '2', '9', 0,
  /* 688 */ 'v', 's', '2', '9', 0,
  /* 693 */ 'v', '2', '9', 0,
  /* 697 */ 'v', 's', '3', '9', 0,
  /* 702 */ 'v', 's', '4', '9', 0,
  /* 707 */ 'v', 's', '5', '9', 0,
  /* 712 */ 'f', '9', 0,
  /* 715 */ 'r', '9', 0,
  /* 718 */ 'v', 's', '9', 0,
  /* 722 */ 'v', '9', 0,
  /* 725 */ 'c', 'a', 0,
  /* 728 */ 'c', 'c', 0,
  /* 731 */ 'v', 'r', 's', 'a', 'v', 'e', 0,
  /* 738 */ 'l', 'r', 0,
  /* 741 */ 'c', 't', 'r', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    36, 725, 728, 741, 18, 738, 0, 731, 55, 36, 122, 202, 270, 338, 
    401, 464, 527, 590, 741, 119, 199, 267, 335, 398, 461, 524, 587, 650, 
    712, 53, 133, 213, 281, 349, 412, 475, 538, 601, 663, 70, 150, 230, 
    298, 366, 429, 492, 555, 618, 680, 87, 167, 18, 738, 123, 203, 271, 
    339, 402, 465, 528, 591, 653, 715, 57, 137, 217, 285, 353, 416, 479, 
    542, 605, 667, 74, 154, 234, 302, 370, 433, 496, 559, 622, 684, 91, 
    171, 130, 210, 278, 346, 409, 472, 535, 598, 660, 722, 66, 146, 226, 
    294, 362, 425, 488, 551, 614, 676, 83, 163, 243, 311, 379, 442, 505, 
    568, 631, 693, 100, 180, 247, 315, 383, 446, 509, 572, 635, 697, 104, 
    184, 252, 320, 388, 451, 514, 577, 640, 702, 109, 189, 257, 325, 393, 
    456, 519, 582, 645, 707, 114, 194, 262, 330, 247, 315, 383, 446, 509, 
    572, 635, 697, 104, 184, 252, 320, 388, 451, 514, 577, 640, 702, 109, 
    189, 257, 325, 393, 456, 519, 582, 645, 707, 114, 194, 262, 330, 126, 
    206, 274, 342, 405, 468, 531, 594, 656, 718, 61, 141, 221, 289, 357, 
    420, 483, 546, 609, 671, 78, 158, 238, 306, 374, 437, 500, 563, 626, 
    688, 95, 175, 123, 203, 271, 339, 402, 465, 528, 591, 653, 715, 57, 
    137, 217, 285, 353, 416, 479, 542, 605, 667, 74, 154, 234, 302, 370, 
    433, 496, 559, 622, 684, 91, 171, 55, 215, 477, 54, 350, 602, 231, 
    493, 88, 135, 414, 665, 282, 539, 151, 430, 681, 55, 351, 603, 214, 
    476, 71, 367, 619, 283, 540, 134, 413, 664, 299, 556, 168, 
  };

  //assert (*(AsmStrs+RegAsmOffset[RegNo-1]) &&
  //       "Invalid alt name index for register!");
  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //	  printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
  return NULL;
}
#endif

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printBranchOperand(MI, OpIdx, OS);
    break;
  case 1:
    printAbsBranchOperand(MI, OpIdx, OS);
    break;
  case 2:
    printS16ImmOperand(MI, OpIdx, OS);
    break;
  case 3:
    printU16ImmOperand(MI, OpIdx, OS);
    break;
  case 4:
    printU6ImmOperand(MI, OpIdx, OS);
    break;
  case 5:
    printU5ImmOperand(MI, OpIdx, OS);
    break;
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case PPC_BCC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 12, crrc:$cc, condbrtarget:$dst)
      AsmString = "blt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 12, CR0, condbrtarget:$dst)
      AsmString = "blt $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 14, crrc:$cc, condbrtarget:$dst)
      AsmString = "blt- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 14, CR0, condbrtarget:$dst)
      AsmString = "blt- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 15, crrc:$cc, condbrtarget:$dst)
      AsmString = "blt+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 15, CR0, condbrtarget:$dst)
      AsmString = "blt+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 44, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 44, CR0, condbrtarget:$dst)
      AsmString = "bgt $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 46, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgt- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 46, CR0, condbrtarget:$dst)
      AsmString = "bgt- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 47, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgt+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 47, CR0, condbrtarget:$dst)
      AsmString = "bgt+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 76, crrc:$cc, condbrtarget:$dst)
      AsmString = "beq $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 76, CR0, condbrtarget:$dst)
      AsmString = "beq $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 78, crrc:$cc, condbrtarget:$dst)
      AsmString = "beq- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 78, CR0, condbrtarget:$dst)
      AsmString = "beq- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 79, crrc:$cc, condbrtarget:$dst)
      AsmString = "beq+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 79, CR0, condbrtarget:$dst)
      AsmString = "beq+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 68, crrc:$cc, condbrtarget:$dst)
      AsmString = "bne $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 68, CR0, condbrtarget:$dst)
      AsmString = "bne $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 70, crrc:$cc, condbrtarget:$dst)
      AsmString = "bne- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 70, CR0, condbrtarget:$dst)
      AsmString = "bne- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 71, crrc:$cc, condbrtarget:$dst)
      AsmString = "bne+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 71, CR0, condbrtarget:$dst)
      AsmString = "bne+ $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_BCCA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 12, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "blta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 12, CR0, abscondbrtarget:$dst)
      AsmString = "blta $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 14, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "blta- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 14, CR0, abscondbrtarget:$dst)
      AsmString = "blta- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 15, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "blta+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 15, CR0, abscondbrtarget:$dst)
      AsmString = "blta+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 44, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 44, CR0, abscondbrtarget:$dst)
      AsmString = "bgta $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 46, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgta- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 46, CR0, abscondbrtarget:$dst)
      AsmString = "bgta- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 47, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgta+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 47, CR0, abscondbrtarget:$dst)
      AsmString = "bgta+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 76, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqa $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 76, CR0, abscondbrtarget:$dst)
      AsmString = "beqa $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 78, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqa- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 78, CR0, abscondbrtarget:$dst)
      AsmString = "beqa- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 79, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqa+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 79, CR0, abscondbrtarget:$dst)
      AsmString = "beqa+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 68, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnea $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 68, CR0, abscondbrtarget:$dst)
      AsmString = "bnea $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 70, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnea- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 70, CR0, abscondbrtarget:$dst)
      AsmString = "bnea- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 71, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnea+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 71, CR0, abscondbrtarget:$dst)
      AsmString = "bnea+ $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_BCCCTR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 12, crrc:$cc)
      AsmString = "bltctr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 12, CR0)
      AsmString = "bltctr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 14, crrc:$cc)
      AsmString = "bltctr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 14, CR0)
      AsmString = "bltctr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 15, crrc:$cc)
      AsmString = "bltctr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 15, CR0)
      AsmString = "bltctr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 44, crrc:$cc)
      AsmString = "bgtctr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 44, CR0)
      AsmString = "bgtctr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 46, crrc:$cc)
      AsmString = "bgtctr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 46, CR0)
      AsmString = "bgtctr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 47, crrc:$cc)
      AsmString = "bgtctr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 47, CR0)
      AsmString = "bgtctr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 76, crrc:$cc)
      AsmString = "beqctr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 76, CR0)
      AsmString = "beqctr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 78, crrc:$cc)
      AsmString = "beqctr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 78, CR0)
      AsmString = "beqctr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 79, crrc:$cc)
      AsmString = "beqctr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 79, CR0)
      AsmString = "beqctr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 68, crrc:$cc)
      AsmString = "bnectr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 68, CR0)
      AsmString = "bnectr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 70, crrc:$cc)
      AsmString = "bnectr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 70, CR0)
      AsmString = "bnectr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 71, crrc:$cc)
      AsmString = "bnectr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 71, CR0)
      AsmString = "bnectr+";
      break;
    }
    return NULL;
  case PPC_BCCCTRL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 12, crrc:$cc)
      AsmString = "bltctrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 12, CR0)
      AsmString = "bltctrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 14, crrc:$cc)
      AsmString = "bltctrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 14, CR0)
      AsmString = "bltctrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 15, crrc:$cc)
      AsmString = "bltctrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 15, CR0)
      AsmString = "bltctrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 44, crrc:$cc)
      AsmString = "bgtctrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 44, CR0)
      AsmString = "bgtctrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 46, crrc:$cc)
      AsmString = "bgtctrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 46, CR0)
      AsmString = "bgtctrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 47, crrc:$cc)
      AsmString = "bgtctrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 47, CR0)
      AsmString = "bgtctrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 76, crrc:$cc)
      AsmString = "beqctrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 76, CR0)
      AsmString = "beqctrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 78, crrc:$cc)
      AsmString = "beqctrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 78, CR0)
      AsmString = "beqctrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 79, crrc:$cc)
      AsmString = "beqctrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 79, CR0)
      AsmString = "beqctrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 68, crrc:$cc)
      AsmString = "bnectrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 68, CR0)
      AsmString = "bnectrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 70, crrc:$cc)
      AsmString = "bnectrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 70, CR0)
      AsmString = "bnectrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 71, crrc:$cc)
      AsmString = "bnectrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 71, CR0)
      AsmString = "bnectrl+";
      break;
    }
    return NULL;
  case PPC_BCCL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 12, crrc:$cc, condbrtarget:$dst)
      AsmString = "bltl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 12, CR0, condbrtarget:$dst)
      AsmString = "bltl $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 14, crrc:$cc, condbrtarget:$dst)
      AsmString = "bltl- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 14, CR0, condbrtarget:$dst)
      AsmString = "bltl- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 15, crrc:$cc, condbrtarget:$dst)
      AsmString = "bltl+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 15, CR0, condbrtarget:$dst)
      AsmString = "bltl+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 44, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgtl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 44, CR0, condbrtarget:$dst)
      AsmString = "bgtl $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 46, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgtl- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 46, CR0, condbrtarget:$dst)
      AsmString = "bgtl- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 47, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgtl+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 47, CR0, condbrtarget:$dst)
      AsmString = "bgtl+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 76, crrc:$cc, condbrtarget:$dst)
      AsmString = "beql $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 76, CR0, condbrtarget:$dst)
      AsmString = "beql $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 78, crrc:$cc, condbrtarget:$dst)
      AsmString = "beql- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 78, CR0, condbrtarget:$dst)
      AsmString = "beql- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 79, crrc:$cc, condbrtarget:$dst)
      AsmString = "beql+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 79, CR0, condbrtarget:$dst)
      AsmString = "beql+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 68, crrc:$cc, condbrtarget:$dst)
      AsmString = "bnel $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 68, CR0, condbrtarget:$dst)
      AsmString = "bnel $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 70, crrc:$cc, condbrtarget:$dst)
      AsmString = "bnel- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 70, CR0, condbrtarget:$dst)
      AsmString = "bnel- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 71, crrc:$cc, condbrtarget:$dst)
      AsmString = "bnel+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 71, CR0, condbrtarget:$dst)
      AsmString = "bnel+ $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_BCCLA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 12, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bltla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 12, CR0, abscondbrtarget:$dst)
      AsmString = "bltla $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 14, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bltla- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 14, CR0, abscondbrtarget:$dst)
      AsmString = "bltla- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 15, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bltla+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 15, CR0, abscondbrtarget:$dst)
      AsmString = "bltla+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 44, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgtla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 44, CR0, abscondbrtarget:$dst)
      AsmString = "bgtla $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 46, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgtla- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 46, CR0, abscondbrtarget:$dst)
      AsmString = "bgtla- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 47, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgtla+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 47, CR0, abscondbrtarget:$dst)
      AsmString = "bgtla+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 76, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 76, CR0, abscondbrtarget:$dst)
      AsmString = "beqla $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 78, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqla- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 78, CR0, abscondbrtarget:$dst)
      AsmString = "beqla- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 79, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqla+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 79, CR0, abscondbrtarget:$dst)
      AsmString = "beqla+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 68, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnela $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 68, CR0, abscondbrtarget:$dst)
      AsmString = "bnela $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 70, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnela- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 70, CR0, abscondbrtarget:$dst)
      AsmString = "bnela- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 71, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnela+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 71, CR0, abscondbrtarget:$dst)
      AsmString = "bnela+ $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_BCCLR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 12, crrc:$cc)
      AsmString = "bltlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 12, CR0)
      AsmString = "bltlr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 14, crrc:$cc)
      AsmString = "bltlr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 14, CR0)
      AsmString = "bltlr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 15, crrc:$cc)
      AsmString = "bltlr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 15, CR0)
      AsmString = "bltlr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 44, crrc:$cc)
      AsmString = "bgtlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 44, CR0)
      AsmString = "bgtlr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 46, crrc:$cc)
      AsmString = "bgtlr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 46, CR0)
      AsmString = "bgtlr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 47, crrc:$cc)
      AsmString = "bgtlr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 47, CR0)
      AsmString = "bgtlr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 76, crrc:$cc)
      AsmString = "beqlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 76, CR0)
      AsmString = "beqlr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 78, crrc:$cc)
      AsmString = "beqlr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 78, CR0)
      AsmString = "beqlr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 79, crrc:$cc)
      AsmString = "beqlr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 79, CR0)
      AsmString = "beqlr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 68, crrc:$cc)
      AsmString = "bnelr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 68, CR0)
      AsmString = "bnelr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 70, crrc:$cc)
      AsmString = "bnelr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 70, CR0)
      AsmString = "bnelr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 71, crrc:$cc)
      AsmString = "bnelr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 71, CR0)
      AsmString = "bnelr+";
      break;
    }
    return NULL;
  case PPC_BCCLRL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 12, crrc:$cc)
      AsmString = "bltlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 12, CR0)
      AsmString = "bltlrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 14, crrc:$cc)
      AsmString = "bltlrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 14, CR0)
      AsmString = "bltlrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 15, crrc:$cc)
      AsmString = "bltlrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 15, CR0)
      AsmString = "bltlrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 44, crrc:$cc)
      AsmString = "bgtlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 44, CR0)
      AsmString = "bgtlrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 46, crrc:$cc)
      AsmString = "bgtlrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 46, CR0)
      AsmString = "bgtlrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 47, crrc:$cc)
      AsmString = "bgtlrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 47, CR0)
      AsmString = "bgtlrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 76, crrc:$cc)
      AsmString = "beqlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 76, CR0)
      AsmString = "beqlrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 78, crrc:$cc)
      AsmString = "beqlrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 78, CR0)
      AsmString = "beqlrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 79, crrc:$cc)
      AsmString = "beqlrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 79, CR0)
      AsmString = "beqlrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 68, crrc:$cc)
      AsmString = "bnelrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 68, CR0)
      AsmString = "bnelrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 70, crrc:$cc)
      AsmString = "bnelrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 70, CR0)
      AsmString = "bnelrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 71, crrc:$cc)
      AsmString = "bnelrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 71, CR0)
      AsmString = "bnelrl+";
      break;
    }
    return NULL;
  case PPC_CMPD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (CMPD CR0, g8rc:$rA, g8rc:$rB)
      AsmString = "cmpd $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPDI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (CMPDI CR0, g8rc:$rA, s16imm64:$imm)
      AsmString = "cmpdi $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_CMPLD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (CMPLD CR0, g8rc:$rA, g8rc:$rB)
      AsmString = "cmpld $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPLDI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (CMPLDI CR0, g8rc:$rA, u16imm64:$imm)
      AsmString = "cmpldi $\x02, $\xFF\x03\x04";
      break;
    }
    return NULL;
  case PPC_CMPLW:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (CMPLW CR0, gprc:$rA, gprc:$rB)
      AsmString = "cmplw $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPLWI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (CMPLWI CR0, gprc:$rA, u16imm:$imm)
      AsmString = "cmplwi $\x02, $\xFF\x03\x04";
      break;
    }
    return NULL;
  case PPC_CMPW:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (CMPW CR0, gprc:$rA, gprc:$rB)
      AsmString = "cmpw $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPWI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (CMPWI CR0, gprc:$rA, s16imm:$imm)
      AsmString = "cmpwi $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_CREQV:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (CREQV crbitrc:$bx, crbitrc:$bx, crbitrc:$bx)
      AsmString = "crset $\x01";
      break;
    }
    return NULL;
  case PPC_CRNOR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (CRNOR crbitrc:$bx, crbitrc:$by, crbitrc:$by)
      AsmString = "crnot $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_CROR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (CROR crbitrc:$bx, crbitrc:$by, crbitrc:$by)
      AsmString = "crmove $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_CRXOR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (CRXOR crbitrc:$bx, crbitrc:$bx, crbitrc:$bx)
      AsmString = "crclr $\x01";
      break;
    }
    return NULL;
  case PPC_MBAR:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (MBAR 0)
      AsmString = "mbar";
      break;
    }
    return NULL;
  case PPC_MFDCR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 128) {
      // (MFDCR gprc:$Rx, 128)
      AsmString = "mfbr0 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 129) {
      // (MFDCR gprc:$Rx, 129)
      AsmString = "mfbr1 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 130) {
      // (MFDCR gprc:$Rx, 130)
      AsmString = "mfbr2 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 131) {
      // (MFDCR gprc:$Rx, 131)
      AsmString = "mfbr3 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 132) {
      // (MFDCR gprc:$Rx, 132)
      AsmString = "mfbr4 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 133) {
      // (MFDCR gprc:$Rx, 133)
      AsmString = "mfbr5 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 134) {
      // (MFDCR gprc:$Rx, 134)
      AsmString = "mfbr6 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 135) {
      // (MFDCR gprc:$Rx, 135)
      AsmString = "mfbr7 $\x01";
      break;
    }
    return NULL;
  case PPC_MFSPR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (MFSPR gprc:$Rx, 1)
      AsmString = "mfxer $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (MFSPR gprc:$Rx, 4)
      AsmString = "mfrtcu $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (MFSPR gprc:$Rx, 5)
      AsmString = "mfrtcl $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 17) {
      // (MFSPR gprc:$Rx, 17)
      AsmString = "mfdscr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 18) {
      // (MFSPR gprc:$Rx, 18)
      AsmString = "mfdsisr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 19) {
      // (MFSPR gprc:$Rx, 19)
      AsmString = "mfdar $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 990) {
      // (MFSPR gprc:$Rx, 990)
      AsmString = "mfsrr2 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 991) {
      // (MFSPR gprc:$Rx, 991)
      AsmString = "mfsrr3 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 28) {
      // (MFSPR gprc:$Rx, 28)
      AsmString = "mfcfar $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 29) {
      // (MFSPR gprc:$Rx, 29)
      AsmString = "mfamr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 48) {
      // (MFSPR gprc:$Rx, 48)
      AsmString = "mfpid $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 989) {
      // (MFSPR gprc:$Rx, 989)
      AsmString = "mftblo $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 988) {
      // (MFSPR gprc:$Rx, 988)
      AsmString = "mftbhi $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 536) {
      // (MFSPR gprc:$Rx, 536)
      AsmString = "mfdbatu $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 538) {
      // (MFSPR gprc:$Rx, 538)
      AsmString = "mfdbatu $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 540) {
      // (MFSPR gprc:$Rx, 540)
      AsmString = "mfdbatu $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 542) {
      // (MFSPR gprc:$Rx, 542)
      AsmString = "mfdbatu $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 537) {
      // (MFSPR gprc:$Rx, 537)
      AsmString = "mfdbatl $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 539) {
      // (MFSPR gprc:$Rx, 539)
      AsmString = "mfdbatl $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 541) {
      // (MFSPR gprc:$Rx, 541)
      AsmString = "mfdbatl $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 543) {
      // (MFSPR gprc:$Rx, 543)
      AsmString = "mfdbatl $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 528) {
      // (MFSPR gprc:$Rx, 528)
      AsmString = "mfibatu $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 530) {
      // (MFSPR gprc:$Rx, 530)
      AsmString = "mfibatu $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 532) {
      // (MFSPR gprc:$Rx, 532)
      AsmString = "mfibatu $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 534) {
      // (MFSPR gprc:$Rx, 534)
      AsmString = "mfibatu $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 529) {
      // (MFSPR gprc:$Rx, 529)
      AsmString = "mfibatl $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 531) {
      // (MFSPR gprc:$Rx, 531)
      AsmString = "mfibatl $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 533) {
      // (MFSPR gprc:$Rx, 533)
      AsmString = "mfibatl $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 535) {
      // (MFSPR gprc:$Rx, 535)
      AsmString = "mfibatl $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1018) {
      // (MFSPR gprc:$Rx, 1018)
      AsmString = "mfdccr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1019) {
      // (MFSPR gprc:$Rx, 1019)
      AsmString = "mficcr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 981) {
      // (MFSPR gprc:$Rx, 981)
      AsmString = "mfdear $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 980) {
      // (MFSPR gprc:$Rx, 980)
      AsmString = "mfesr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 512) {
      // (MFSPR gprc:$Rx, 512)
      AsmString = "mfspefscr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 986) {
      // (MFSPR gprc:$Rx, 986)
      AsmString = "mftcr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 280) {
      // (MFSPR gprc:$RT, 280)
      AsmString = "mfasr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 287) {
      // (MFSPR gprc:$RT, 287)
      AsmString = "mfpvr $\x01";
      break;
    }
    return NULL;
  case PPC_MFTB:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 269) {
      // (MFTB gprc:$Rx, 269)
      AsmString = "mftbu $\x01";
      break;
    }
    return NULL;
  case PPC_MTCRF8:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 255 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (MTCRF8 255, g8rc:$rA)
      AsmString = "mtcr $\x02";
      break;
    }
    return NULL;
  case PPC_MTDCR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 128) {
      // (MTDCR gprc:$Rx, 128)
      AsmString = "mtbr0 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 129) {
      // (MTDCR gprc:$Rx, 129)
      AsmString = "mtbr1 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 130) {
      // (MTDCR gprc:$Rx, 130)
      AsmString = "mtbr2 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 131) {
      // (MTDCR gprc:$Rx, 131)
      AsmString = "mtbr3 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 132) {
      // (MTDCR gprc:$Rx, 132)
      AsmString = "mtbr4 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 133) {
      // (MTDCR gprc:$Rx, 133)
      AsmString = "mtbr5 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 134) {
      // (MTDCR gprc:$Rx, 134)
      AsmString = "mtbr6 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 135) {
      // (MTDCR gprc:$Rx, 135)
      AsmString = "mtbr7 $\x01";
      break;
    }
    return NULL;
  case PPC_MTMSR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (MTMSR gprc:$RS, 0)
      AsmString = "mtmsr $\x01";
      break;
    }
    return NULL;
  case PPC_MTMSRD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (MTMSRD gprc:$RS, 0)
      AsmString = "mtmsrd $\x01";
      break;
    }
    return NULL;
  case PPC_MTSPR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 1, gprc:$Rx)
      AsmString = "mtxer $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 17 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 17, gprc:$Rx)
      AsmString = "mtdscr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 18 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 18, gprc:$Rx)
      AsmString = "mtdsisr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 19 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 19, gprc:$Rx)
      AsmString = "mtdar $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 990 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 990, gprc:$Rx)
      AsmString = "mtsrr2 $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 991 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 991, gprc:$Rx)
      AsmString = "mtsrr3 $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 28 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 28, gprc:$Rx)
      AsmString = "mtcfar $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 29 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 29, gprc:$Rx)
      AsmString = "mtamr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 48 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 48, gprc:$Rx)
      AsmString = "mtpid $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 284 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 284, gprc:$Rx)
      AsmString = "mttbl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 285 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 285, gprc:$Rx)
      AsmString = "mttbu $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 989 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 989, gprc:$Rx)
      AsmString = "mttblo $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 988 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 988, gprc:$Rx)
      AsmString = "mttbhi $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 536 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 536, gprc:$Rx)
      AsmString = "mtdbatu 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 538 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 538, gprc:$Rx)
      AsmString = "mtdbatu 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 540 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 540, gprc:$Rx)
      AsmString = "mtdbatu 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 542 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 542, gprc:$Rx)
      AsmString = "mtdbatu 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 537 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 537, gprc:$Rx)
      AsmString = "mtdbatl 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 539 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 539, gprc:$Rx)
      AsmString = "mtdbatl 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 541 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 541, gprc:$Rx)
      AsmString = "mtdbatl 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 543 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 543, gprc:$Rx)
      AsmString = "mtdbatl 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 528 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 528, gprc:$Rx)
      AsmString = "mtibatu 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 530 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 530, gprc:$Rx)
      AsmString = "mtibatu 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 532 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 532, gprc:$Rx)
      AsmString = "mtibatu 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 534 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 534, gprc:$Rx)
      AsmString = "mtibatu 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 529 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 529, gprc:$Rx)
      AsmString = "mtibatl 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 531 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 531, gprc:$Rx)
      AsmString = "mtibatl 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 533 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 533, gprc:$Rx)
      AsmString = "mtibatl 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 535 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 535, gprc:$Rx)
      AsmString = "mtibatl 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1018 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 1018, gprc:$Rx)
      AsmString = "mtdccr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1019 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 1019, gprc:$Rx)
      AsmString = "mticcr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 981 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 981, gprc:$Rx)
      AsmString = "mtdear $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 980 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 980, gprc:$Rx)
      AsmString = "mtesr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 512 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 512, gprc:$Rx)
      AsmString = "mtspefscr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 986 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 986, gprc:$Rx)
      AsmString = "mttcr $\x02";
      break;
    }
    return NULL;
  case PPC_NOR8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (NOR8 g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "not $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_NOR8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (NOR8o g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "not. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_OR8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (OR8 g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "mr $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_OR8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (OR8o g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "mr. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_RLDCL:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDCL g8rc:$rA, g8rc:$rS, gprc:$rB, 0)
      AsmString = "rotld $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_RLDCLo:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDCLo g8rc:$rA, g8rc:$rS, gprc:$rB, 0)
      AsmString = "rotld. $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_RLDICL:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDICL g8rc:$rA, g8rc:$rS, u6imm:$n, 0)
      AsmString = "rotldi $\x01, $\x02, $\xFF\x03\x05";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (RLDICL g8rc:$rA, g8rc:$rS, 0, u6imm:$n)
      AsmString = "clrldi $\x01, $\x02, $\xFF\x04\x05";
      break;
    }
    return NULL;
  case PPC_RLDICLo:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDICLo g8rc:$rA, g8rc:$rS, u6imm:$n, 0)
      AsmString = "rotldi. $\x01, $\x02, $\xFF\x03\x05";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (RLDICLo g8rc:$rA, g8rc:$rS, 0, u6imm:$n)
      AsmString = "clrldi. $\x01, $\x02, $\xFF\x04\x05";
      break;
    }
    return NULL;
  case PPC_RLWINM:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINM gprc:$rA, gprc:$rS, u5imm:$n, 0, 31)
      AsmString = "rotlwi $\x01, $\x02, $\xFF\x03\x06";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINM gprc:$rA, gprc:$rS, 0, u5imm:$n, 31)
      AsmString = "clrlwi $\x01, $\x02, $\xFF\x04\x06";
      break;
    }
    return NULL;
  case PPC_RLWINMo:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINMo gprc:$rA, gprc:$rS, u5imm:$n, 0, 31)
      AsmString = "rotlwi. $\x01, $\x02, $\xFF\x03\x06";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINMo gprc:$rA, gprc:$rS, 0, u5imm:$n, 31)
      AsmString = "clrlwi. $\x01, $\x02, $\xFF\x04\x06";
      break;
    }
    return NULL;
  case PPC_RLWNM:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWNM gprc:$rA, gprc:$rS, gprc:$rB, 0, 31)
      AsmString = "rotlw $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_RLWNMo:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWNMo gprc:$rA, gprc:$rS, gprc:$rB, 0, 31)
      AsmString = "rotlw. $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_SC:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SC 0)
      AsmString = "sc";
      break;
    }
    return NULL;
  case PPC_SUBF8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBF8 g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "sub $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SUBF8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBF8o g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "sub. $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SUBFC8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBFC8 g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "subc $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SUBFC8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBFC8o g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "subc. $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SYNC:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (SYNC 1)
      AsmString = "lwsync";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (SYNC 2)
      AsmString = "ptesync";
      break;
    }
    return NULL;
  case PPC_TD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 16, g8rc:$rA, g8rc:$rB)
      AsmString = "tdlt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 4, g8rc:$rA, g8rc:$rB)
      AsmString = "tdeq $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 8, g8rc:$rA, g8rc:$rB)
      AsmString = "tdgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 24, g8rc:$rA, g8rc:$rB)
      AsmString = "tdne $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 2, g8rc:$rA, g8rc:$rB)
      AsmString = "tdllt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 1, g8rc:$rA, g8rc:$rB)
      AsmString = "tdlgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 31, g8rc:$rA, g8rc:$rB)
      AsmString = "tdu $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_TDI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 16, g8rc:$rA, s16imm:$imm)
      AsmString = "tdlti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 4, g8rc:$rA, s16imm:$imm)
      AsmString = "tdeqi $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 8, g8rc:$rA, s16imm:$imm)
      AsmString = "tdgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 24, g8rc:$rA, s16imm:$imm)
      AsmString = "tdnei $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 2, g8rc:$rA, s16imm:$imm)
      AsmString = "tdllti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 1, g8rc:$rA, s16imm:$imm)
      AsmString = "tdlgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 31, g8rc:$rA, s16imm:$imm)
      AsmString = "tdui $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_TLBIE:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_R0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TLBIE R0, gprc:$RB)
      AsmString = "tlbie $\x02";
      break;
    }
    return NULL;
  case PPC_TLBRE2:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLBRE2 gprc:$RS, gprc:$A, 0)
      AsmString = "tlbrehi $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TLBRE2 gprc:$RS, gprc:$A, 1)
      AsmString = "tlbrelo $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_TLBWE2:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLBWE2 gprc:$RS, gprc:$A, 0)
      AsmString = "tlbwehi $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TLBWE2 gprc:$RS, gprc:$A, 1)
      AsmString = "tlbwelo $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_TW:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 16, gprc:$rA, gprc:$rB)
      AsmString = "twlt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 4, gprc:$rA, gprc:$rB)
      AsmString = "tweq $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 8, gprc:$rA, gprc:$rB)
      AsmString = "twgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 24, gprc:$rA, gprc:$rB)
      AsmString = "twne $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 2, gprc:$rA, gprc:$rB)
      AsmString = "twllt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 1, gprc:$rA, gprc:$rB)
      AsmString = "twlgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 31, gprc:$rA, gprc:$rB)
      AsmString = "twu $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_TWI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 16, gprc:$rA, s16imm:$imm)
      AsmString = "twlti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 4, gprc:$rA, s16imm:$imm)
      AsmString = "tweqi $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 8, gprc:$rA, s16imm:$imm)
      AsmString = "twgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 24, gprc:$rA, s16imm:$imm)
      AsmString = "twnei $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 2, gprc:$rA, s16imm:$imm)
      AsmString = "twllti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 1, gprc:$rA, s16imm:$imm)
      AsmString = "twlgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 31, gprc:$rA, s16imm:$imm)
      AsmString = "twui $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_WAIT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (WAIT 0)
      AsmString = "wait";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (WAIT 1)
      AsmString = "waitrsv";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (WAIT 2)
      AsmString = "waitimpl";
      break;
    }
    return NULL;
  case PPC_XORI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_R0 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_R0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (XORI R0, R0, 0)
      AsmString = "xnop";
      break;
    }
    return NULL;
  case PPC_XVCPSGNDP:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (XVCPSGNDP vsrc:$XT, vsrc:$XB, vsrc:$XB)
      AsmString = "xvmovdp $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_XVCPSGNSP:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (XVCPSGNSP vsrc:$XT, vsrc:$XB, vsrc:$XB)
      AsmString = "xvmovsp $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_XXPERMDI:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (XXPERMDI vsrc:$XT, vsrc:$XB, vsrc:$XB, 0)
      AsmString = "xxspltd $\x01, $\x02, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (XXPERMDI vsrc:$XT, vsrc:$XB, vsrc:$XB, 3)
      AsmString = "xxspltd $\x01, $\x02, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (XXPERMDI vsrc:$XT, vsrc:$XA, vsrc:$XB, 0)
      AsmString = "xxmrghd $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (XXPERMDI vsrc:$XT, vsrc:$XA, vsrc:$XB, 3)
      AsmString = "xxmrgld $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (XXPERMDI vsrc:$XT, vsrc:$XB, vsrc:$XB, 2)
      AsmString = "xxswapd $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_gBC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 8, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnzt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 0, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnzf $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 10, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdzt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 2, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdzf $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_gBCA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 8, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnzta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 0, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnzfa $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 10, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdzta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 2, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdzfa $\x02, $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_gBCCTR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCCTR u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bcctr $\xFF\x01\x06, $\x02";
      break;
    }
    return NULL;
  case PPC_gBCCTRL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCCTRL u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bcctrl $\xFF\x01\x06, $\x02";
      break;
    }
    return NULL;
  case PPC_gBCL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 8, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnztl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 0, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnzfl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 10, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdztl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 2, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdzfl $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_gBCLA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 8, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnztla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 0, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnzfla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 10, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdztla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 2, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdzfla $\x02, $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_gBCLR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bclr $\xFF\x01\x06, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 8, crbitrc:$bi, 0)
      AsmString = "bdnztlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 0, crbitrc:$bi, 0)
      AsmString = "bdnzflr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 10, crbitrc:$bi, 0)
      AsmString = "bdztlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 2, crbitrc:$bi, 0)
      AsmString = "bdzflr $\x02";
      break;
    }
    return NULL;
  case PPC_gBCLRL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bclrl $\xFF\x01\x06, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 8, crbitrc:$bi, 0)
      AsmString = "bdnztlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 0, crbitrc:$bi, 0)
      AsmString = "bdnzflrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 10, crbitrc:$bi, 0)
      AsmString = "bdztlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 2, crbitrc:$bi, 0)
      AsmString = "bdzflrl $\x02";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
