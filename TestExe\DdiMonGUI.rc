#include "resource.h"
#include <windows.h>

IDD_MAIN_DIALOG DIALOGEX 0, 0, 720, 500
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "DdiMon Manager"
FONT 9, "MS Shell Dlg"
BEGIN
    GROUPBOX        "Driver Management", 2001, 7, 7, 506, 60
    LTEXT           "Driver Path:", 2002, 15, 25, 35, 8
    EDITTEXT        IDC_EDIT_DRIVER_PATH, 55, 23, 350, 14, ES_AUTOHSCROLL
    PUSHBUTTON      "Select...", IDC_BTN_SELECT_DRIVER, 415, 22, 40, 16
    P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>      "Load Driver", IDC_BTN_LOAD_DRIVER, 465, 22, 40, 16
    PUSHB<PERSON>TON      "Unload Driver", IDC_BTN_UNLOAD_DRIVER, 465, 42, 40, 16
    
    GROUPBOX        "Monitor Control", 2003, 7, 75, 506, 40
    PUSHBUTTON      "Start Monitor", IDC_BTN_START_MONITOR, 15, 90, 60, 20
    PUSHBUTTON      "Stop Monitor", IDC_BTN_STOP_MONITOR, 85, 90, 60, 20
    PUSHBUTTON      "View Log File", IDC_BTN_VIEW_LOG_FILE, 155, 90, 80, 20
    CONTROL         "Auto Scroll", IDC_CHECK_AUTO_SCROLL, "Button", 0x50010003, 245, 93, 50, 14
    
    GROUPBOX        "EPT Memory Operations", 2005, 520, 7, 193, 120
    LTEXT           "Process ID:", 2006, 528, 25, 35, 8
    EDITTEXT        IDC_EDIT_PROCESS_ID, 570, 23, 60, 14, ES_AUTOHSCROLL | ES_NUMBER
    PUSHBUTTON      "Get Processes", 1021, 635, 23, 60, 14
    LTEXT           "Memory Address:", 2007, 528, 45, 50, 8
    EDITTEXT        IDC_EDIT_ADDRESS, 580, 43, 90, 14, ES_AUTOHSCROLL
    LTEXT           "Size:", 2008, 528, 65, 20, 8
    EDITTEXT        IDC_EDIT_SIZE, 570, 63, 60, 14, ES_AUTOHSCROLL | ES_NUMBER

    PUSHBUTTON      "EPT Read", IDC_BTN_EPT_READ, 528, 85, 60, 20
    PUSHBUTTON      "EPT Write", IDC_BTN_EPT_WRITE, 598, 85, 60, 20

    LTEXT           "Data (Hex):", 2009, 528, 105, 40, 8
    EDITTEXT        IDC_EDIT_DATA, 570, 103, 100, 14, ES_AUTOHSCROLL

    GROUPBOX        "Process Hiding", 2012, 520, 135, 193, 120
    LTEXT           "Process ID:", 2013, 528, 153, 35, 8
    EDITTEXT        IDC_EDIT_HIDE_PROCESS_ID, 570, 151, 60, 14, ES_AUTOHSCROLL | ES_NUMBER
    PUSHBUTTON      "Hide Process", IDC_BTN_HIDE_PROCESS, 528, 175, 60, 20
    PUSHBUTTON      "Show Process", IDC_BTN_SHOW_PROCESS, 598, 175, 60, 20
    PUSHBUTTON      "List Hidden", IDC_BTN_LIST_HIDDEN, 668, 175, 60, 20
    LTEXT           "Hidden Processes:", 2014, 528, 200, 60, 8
    LISTBOX         IDC_LIST_HIDDEN_PROCESSES, 528, 215, 178, 35, LBS_STANDARD | WS_VSCROLL

    GROUPBOX        "Log Information", 2004, 7, 125, 506, 240
    EDITTEXT        IDC_LIST_LOG, 15, 140, 490, 200, ES_MULTILINE | ES_READONLY | ES_AUTOVSCROLL | ES_AUTOHSCROLL | WS_VSCROLL | WS_HSCROLL
    PUSHBUTTON      "Clear Log", IDC_BTN_CLEAR_LOG, 455, 345, 50, 16
    
    GROUPBOX        "EPT Operation Results", 2011, 520, 265, 193, 100
    EDITTEXT        IDC_EDIT_RESULTS, 528, 280, 178, 80, ES_MULTILINE | ES_AUTOVSCROLL | ES_READONLY | WS_VSCROLL
    
    LTEXT           "Status: Ready", IDC_STATIC_STATUS, 7, 475, 200, 8
    DEFPUSHBUTTON   "Exit", IDCANCEL, 663, 475, 50, 18
END

// REMOVED: Hook management dialog - per ultimate simplification
// Hooks are now automatically managed during read/write operations

IDD_PROCESS_SELECT DIALOGEX 0, 0, 400, 300
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Select Process"
FONT 9, "MS Shell Dlg"
BEGIN
    LTEXT           "Double-click a process to select:", 2025, 7, 7, 150, 8
    LISTBOX         2024, 7, 20, 386, 250, LBS_NOTIFY | WS_VSCROLL | WS_TABSTOP
    DEFPUSHBUTTON   "Cancel", IDCANCEL, 343, 279, 50, 14
END
