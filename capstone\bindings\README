This directory contains bindings & test code for Python, Java & OCaml.
See <language>/README for how to compile & install each binding.

More bindings created & maintained by the community are available as followings.

- Gapstone: Go binding (by <PERSON>).

	https://github.com/bnagy/gapstone

- Crabstone: Ruby binding (by <PERSON>).

	https://github.com/bnagy/crabstone

- Capstone-Vala: Vala binding (by <PERSON><PERSON>).

	https://github.com/radare/capstone-vala

- Node-Capstone: NodeJS binding (by <PERSON>).

	https://github.com/parasyte/node-capstone

- CCcapstone: C++ binding (by <PERSON>).

	https://github.com/zer0mem/cccapstone

- LuaCapstone: Lua binding (by <PERSON>).

	https://github.com/Dax89/LuaCapstone

- Capstone-RS: Rust binding (by <PERSON><PERSON>).

	https://github.com/richo/capstone-rs

- Capstone.NET: .NET framework binding (by <PERSON>).

	https://github.com/9ee1/Capstone.NET

- CapstoneJ: High level Java wrapper for Capstone-java (by <PERSON><PERSON>).

	https://github.com/kevemueller/capstonej

- Hapstone: Haskell binding (by iba<PERSON>kin)

    https://github.com/ibabushkin/hapstone

- Emacs-capstone: Emacs (elisp) binding (by Bas Alberts)

    https://github.com/collarchoke/emacs-capstone

- C# binding (by Matt Graeber). Note: this is only for Capstone v2.0.

	https://github.com/mattifestation/capstone

