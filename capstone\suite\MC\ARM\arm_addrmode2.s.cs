# CS_ARCH_ARM, CS_MODE_ARM, None
0x02,0x10,0xb0,0xe6 = ldrt r1, [r0], r2
0xa2,0x11,0xb0,0xe6 = ldrt r1, [r0], r2, lsr #3
0x04,0x10,0xb0,0xe4 = ldrt r1, [r0], #4
0x02,0x10,0xf0,0xe6 = ldrbt r1, [r0], r2
0xa2,0x11,0xf0,0xe6 = ldrbt r1, [r0], r2, lsr #3
0x04,0x10,0xf0,0xe4 = ldrbt r1, [r0], #4
0x02,0x10,0xa0,0xe6 = strt r1, [r0], r2
0xa2,0x11,0xa0,0xe6 = strt r1, [r0], r2, lsr #3
0x04,0x10,0xa0,0xe4 = strt r1, [r0], #4
0x02,0x10,0xe0,0xe6 = strbt r1, [r0], r2
0xa2,0x11,0xe0,0xe6 = strbt r1, [r0], r2, lsr #3
0x04,0x10,0xe0,0xe4 = strbt r1, [r0], #4
0xa2,0x11,0xb0,0xe7 = ldr r1, [r0, r2, lsr #3]!
0xa2,0x11,0xf0,0xe7 = ldrb r1, [r0, r2, lsr #3]!
