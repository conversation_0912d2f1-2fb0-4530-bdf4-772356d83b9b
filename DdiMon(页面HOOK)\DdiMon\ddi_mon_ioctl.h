// Copyright (c) 2025, DdiMon EPT Memory Operations. All rights reserved.
// Use of this source code is governed by a MIT-style license.

/// @file
/// @brief Declares IOCTL interfaces for DdiMon EPT memory operations.

#ifndef DDIMON_IOCTL_H_
#define DDIMON_IOCTL_H_

#include <ntddk.h>

////////////////////////////////////////////////////////////////////////////////
//
// IOCTL Codes
//

// Device type for DdiMon
#define DDIMON_DEVICE_TYPE 0x8000

// IOCTL codes for EPT memory operations
#define IOCTL_DDIMON_EPT_READ_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_EPT_WRITE_MEMORY \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x801, METH<PERSON>_BUFFERED, FILE_ANY_ACCESS)

#define IOC<PERSON>_DDIMON_CHECK_EPT_HOOK \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_CREATE_EPT_HOOK \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_CREATE_EPT_HOOK_EX \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_REMOVE_EPT_HOOK \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_LIST_EPT_HOOKS \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_DDIMON_REMOVE_ALL_HOOKS \
    CTL_CODE(DDIMON_DEVICE_TYPE, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)

////////////////////////////////////////////////////////////////////////////////
//
// Data Structures
//

// EPT memory read request
#pragma pack(push, 1)
typedef struct _EPT_READ_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to read from
    ULONG Size;                     // Number of bytes to read
} EPT_READ_MEMORY_REQUEST, *PEPT_READ_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory read response
typedef struct _EPT_READ_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesRead;                // Actual bytes read
    UCHAR Data[1];                  // Variable length data buffer
} EPT_READ_MEMORY_RESPONSE, *PEPT_READ_MEMORY_RESPONSE;

// EPT memory write request
#pragma pack(push, 1)
typedef struct _EPT_WRITE_MEMORY_REQUEST {
    ULONG ProcessId;                // Target process ID
    ULONG64 VirtualAddress;         // Virtual address to write to
    ULONG Size;                     // Number of bytes to write
    UCHAR Data[1];                  // Variable length data buffer
} EPT_WRITE_MEMORY_REQUEST, *PEPT_WRITE_MEMORY_REQUEST;
#pragma pack(pop)

// EPT memory write response
typedef struct _EPT_WRITE_MEMORY_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG BytesWritten;             // Actual bytes written
} EPT_WRITE_MEMORY_RESPONSE, *PEPT_WRITE_MEMORY_RESPONSE;

// EPT hook check request
typedef struct _EPT_HOOK_CHECK_REQUEST {
    ULONG64 VirtualAddress;         // Virtual address to check
} EPT_HOOK_CHECK_REQUEST, *PEPT_HOOK_CHECK_REQUEST;

// EPT hook check response
typedef struct _EPT_HOOK_CHECK_RESPONSE {
    NTSTATUS Status;                // Operation status
    BOOLEAN IsHooked;               // TRUE if address is hooked
    ULONG64 ShadowPagePA;           // Physical address of shadow page (if hooked)
} EPT_HOOK_CHECK_RESPONSE, *PEPT_HOOK_CHECK_RESPONSE;

// EPT hook creation request (updated to include ProcessId)
#pragma pack(push, 1)
typedef struct _EPT_HOOK_CREATE_REQUEST {
    ULONG64 VirtualAddress;         // Virtual address to hook
    BOOLEAN EnableExecOnly;         // TRUE for execute-only, FALSE for RW access
    ULONG ProcessId;                // Target process ID
} EPT_HOOK_CREATE_REQUEST, *PEPT_HOOK_CREATE_REQUEST;
#pragma pack(pop)

// EPT hook creation request with process ID (extended structure)
#pragma pack(push, 1)
typedef struct _EPT_HOOK_CREATE_REQUEST_EX {
    ULONG64 VirtualAddress;         // Virtual address to hook
    BOOLEAN EnableExecOnly;         // TRUE for execute-only, FALSE for RW access
    ULONG ProcessId;                // Target process ID (new field)
} EPT_HOOK_CREATE_REQUEST_EX, *PEPT_HOOK_CREATE_REQUEST_EX;
#pragma pack(pop)

// EPT hook creation response
#pragma pack(push, 1)
typedef struct _EPT_HOOK_CREATE_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG64 HookId;                 // Unique hook identifier
    ULONG64 ShadowPagePA;           // Physical address of shadow page
} EPT_HOOK_CREATE_RESPONSE, *PEPT_HOOK_CREATE_RESPONSE;
#pragma pack(pop)

// EPT hook removal request
typedef struct _EPT_HOOK_REMOVE_REQUEST {
    ULONG64 HookId;                 // Hook ID to remove
} EPT_HOOK_REMOVE_REQUEST, *PEPT_HOOK_REMOVE_REQUEST;

// EPT hook removal response
typedef struct _EPT_HOOK_REMOVE_RESPONSE {
    NTSTATUS Status;                // Operation status
} EPT_HOOK_REMOVE_RESPONSE, *PEPT_HOOK_REMOVE_RESPONSE;

// EPT hook info for listing
typedef struct _EPT_HOOK_INFO {
    ULONG64 HookId;                 // Hook identifier
    ULONG64 ProcessId;              // Target process ID
    ULONG64 TargetAddress;          // Target address (page-aligned)
    ULONG64 ShadowPagePA;           // Shadow page physical address
    BOOLEAN IsActive;               // Hook activation status
    LARGE_INTEGER CreateTime;       // Creation timestamp
} EPT_HOOK_INFO, *PEPT_HOOK_INFO;

// EPT hook list request
#pragma pack(push, 1)
typedef struct _EPT_HOOK_LIST_REQUEST {
    ULONG MaxCount;                 // Maximum number of hooks to return
} EPT_HOOK_LIST_REQUEST, *PEPT_HOOK_LIST_REQUEST;
#pragma pack(pop)

// EPT hook list response
typedef struct _EPT_HOOK_LIST_RESPONSE {
    NTSTATUS Status;                // Operation status
    ULONG HookCount;                // Actual number of hooks returned
    EPT_HOOK_INFO Hooks[1];         // Variable length array of hook info
} EPT_HOOK_LIST_RESPONSE, *PEPT_HOOK_LIST_RESPONSE;

////////////////////////////////////////////////////////////////////////////////
//
// Function Prototypes
//

#ifdef __cplusplus
extern "C" {
#endif

// IOCTL handler functions
_IRQL_requires_max_(PASSIVE_LEVEL) NTSTATUS
DdimonHandleEptReadMemory(_In_ PVOID InputBuffer, _In_ ULONG InputBufferLength,
                         _Out_ PVOID OutputBuffer, _In_ ULONG OutputBufferLength,
                         _Out_ PULONG BytesReturned);

_IRQL_requires_max_(PASSIVE_LEVEL) NTSTATUS
DdimonHandleEptWriteMemory(_In_ PVOID InputBuffer, _In_ ULONG InputBufferLength,
                          _Out_ PVOID OutputBuffer, _In_ ULONG OutputBufferLength,
                          _Out_ PULONG BytesReturned);

// REMOVED: All manual hook management handlers
// Per ultimate simplification: Only read/write memory IOCTLs are supported
// Hooks are automatically managed during read/write operations

// REMOVED: Individual EPT hook remove handler
// Per new design: User manages hook points via EptWriteMemory API

_IRQL_requires_max_(PASSIVE_LEVEL) NTSTATUS
DdimonHandleEptHookRemoveAll(_In_ PVOID InputBuffer, _In_ ULONG InputBufferLength,
                            _Out_ PVOID OutputBuffer, _In_ ULONG OutputBufferLength,
                            _Out_ PULONG BytesReturned);

// Dynamic EPT hook violation handler
BOOLEAN DdimonpHandleDynamicEptViolation(struct _EptData* ept_data, void* fault_va);

// Dynamic EPT hooks cleanup function
VOID DdimonpCleanupDynamicHooks();

// REMOVED: Individual page hook removal function
// Per new design: User manages hook points via EptWriteMemory API

_IRQL_requires_max_(PASSIVE_LEVEL) EXTERN_C NTSTATUS
    DdimonpRemoveAllPageHooks(void);

#ifdef __cplusplus
}
#endif

#endif  // DDIMON_IOCTL_H_
