﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B20D17DD-453E-4420-B691-4EB4B9AE3A15}</ProjectGuid>
    <TemplateGuid>{497e31cb-056b-4f31-abb8-447fd55ee5a5}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>DdiMon</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(VC_IncludePath);$(IncludePath)</IncludePath>
    <EnableInf2cat>false</EnableInf2cat>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(VC_IncludePath);$(IncludePath)</IncludePath>
    <EnableInf2cat>false</EnableInf2cat>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(VC_IncludePath);$(IncludePath)</IncludePath>
    <EnableInf2cat>false</EnableInf2cat>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(VC_IncludePath);$(IncludePath)</IncludePath>
    <EnableInf2cat>false</EnableInf2cat>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>$(SolutionDir)capstone\include;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <SupportJustMyCode>false</SupportJustMyCode>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(OutDir)capstone_static_winkernel.lib;ntstrsafe.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <DriverSign>
      <FileDigestAlgorithm>SHA256</FileDigestAlgorithm>
    </DriverSign>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>$(SolutionDir)capstone\include;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(OutDir)capstone_static_winkernel.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <DriverSign>
      <FileDigestAlgorithm>SHA256</FileDigestAlgorithm>
    </DriverSign>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>$(SolutionDir)capstone\include;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <SupportJustMyCode>false</SupportJustMyCode>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(OutDir)capstone_static_winkernel.lib;ntstrsafe.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <DriverSign>
      <FileDigestAlgorithm>SHA256</FileDigestAlgorithm>
    </DriverSign>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>$(SolutionDir)capstone\include;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(OutDir)capstone_static_winkernel.lib;ntstrsafe.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <DriverSign>
      <FileDigestAlgorithm>SHA256</FileDigestAlgorithm>
    </DriverSign>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\HyperPlatform\HyperPlatform\driver.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\ept.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\global_object.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\hotplug_callback.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\kernel_stl.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\log.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\performance.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\power_callback.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\util.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\vm.cpp" />
    <ClCompile Include="..\HyperPlatform\HyperPlatform\vmm.cpp" />
    <ClCompile Include="ddi_mon.cpp" />
    <ClCompile Include="ddi_mon_ioctl.cpp" />
    <ClCompile Include="process_hiding.cpp" />
    <ClCompile Include="shadow_hook.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\HyperPlatform\HyperPlatform\asm.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\common.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\driver.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\ept.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\global_object.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\hotplug_callback.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\ia32_type.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\log.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\performance.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\perf_counter.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\power_callback.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\util.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\vm.h" />
    <ClInclude Include="..\HyperPlatform\HyperPlatform\vmm.h" />
    <ClInclude Include="ddi_mon.h" />
    <ClInclude Include="ddi_mon_ioctl.h" />
    <!-- <ClInclude Include="process_hiding.h" /> -->
    <ClInclude Include="shadow_hook.h" />
  </ItemGroup>
  <ItemGroup>
    <MASM Include="..\HyperPlatform\HyperPlatform\Arch\x64\x64.asm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </MASM>
    <MASM Include="..\HyperPlatform\HyperPlatform\Arch\x86\x86.asm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</UseSafeExceptionHandlers>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</UseSafeExceptionHandlers>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</UseSafeExceptionHandlers>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</UseSafeExceptionHandlers>
    </MASM>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>