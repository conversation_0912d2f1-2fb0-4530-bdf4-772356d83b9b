// 简化的VT-x测试驱动
// 用于检测系统是否支持VT-x而不实际启动hypervisor

#include <ntddk.h>
#include <intrin.h>

// 驱动卸载函数
VOID DriverUnload(PDRIVER_OBJECT DriverObject)
{
    UNREFERENCED_PARAMETER(DriverObject);
    KdPrint(("[TestVT] Driver unloaded\n"));
}

// 检查CPUID是否支持VT-x
BOOLEAN CheckVTXSupport()
{
    int cpuInfo[4];
    __cpuid(cpuInfo, 1);
    
    // 检查ECX第5位（VMX支持位）
    BOOLEAN vmxSupported = (cpuInfo[2] & (1 << 5)) != 0;
    
    KdPrint(("[TestVT] CPUID(1).ECX = 0x%08X\n", cpuInfo[2]));
    KdPrint(("[TestVT] VMX Support: %s\n", vmxSupported ? "YES" : "NO"));
    
    return vmxSupported;
}

// 检查IA32_FEATURE_CONTROL MSR
BOOLEAN CheckFeatureControl()
{
    __try {
        ULONG64 featureControl = __readmsr(0x3A); // IA32_FEATURE_CONTROL
        
        BOOLEAN locked = (featureControl & 0x1) != 0;
        BOOLEAN vmxEnabled = (featureControl & 0x4) != 0;
        
        KdPrint(("[TestVT] IA32_FEATURE_CONTROL = 0x%016llX\n", featureControl));
        KdPrint(("[TestVT] Locked: %s, VMX Enabled: %s\n", 
                locked ? "YES" : "NO", vmxEnabled ? "YES" : "NO"));
        
        if (locked && !vmxEnabled) {
            KdPrint(("[TestVT] ERROR: VMX is locked but not enabled in BIOS\n"));
            return FALSE;
        }
        
        return TRUE;
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        KdPrint(("[TestVT] ERROR: Failed to read IA32_FEATURE_CONTROL MSR\n"));
        return FALSE;
    }
}

// 检查CR4.VMXE位
BOOLEAN CheckCR4VMXE()
{
    ULONG_PTR cr4 = __readcr4();
    BOOLEAN vmxeSet = (cr4 & (1ULL << 13)) != 0; // CR4.VMXE is bit 13
    
    KdPrint(("[TestVT] CR4 = 0x%016llX\n", cr4));
    KdPrint(("[TestVT] CR4.VMXE: %s\n", vmxeSet ? "SET" : "NOT SET"));
    
    return TRUE; // 这个位在VMX启动前通常是0，这是正常的
}

// 驱动入口点
NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
    UNREFERENCED_PARAMETER(RegistryPath);
    
    KdPrint(("[TestVT] Driver loaded - Testing VT-x support\n"));
    
    DriverObject->DriverUnload = DriverUnload;
    
    // 检查VT-x支持
    if (!CheckVTXSupport()) {
        KdPrint(("[TestVT] RESULT: VT-x NOT supported by CPU\n"));
        return STATUS_NOT_SUPPORTED;
    }
    
    // 检查Feature Control MSR
    if (!CheckFeatureControl()) {
        KdPrint(("[TestVT] RESULT: VT-x not properly enabled in BIOS\n"));
        return STATUS_NOT_SUPPORTED;
    }
    
    // 检查CR4寄存器
    CheckCR4VMXE();
    
    KdPrint(("[TestVT] RESULT: System appears to support VT-x\n"));
    KdPrint(("[TestVT] You can now try loading the full DdiMon driver\n"));
    
    return STATUS_SUCCESS;
}
