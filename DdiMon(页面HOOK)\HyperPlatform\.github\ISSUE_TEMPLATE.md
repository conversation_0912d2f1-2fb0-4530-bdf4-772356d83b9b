(Please use this issue template for a bug report. You need not use it for any other type of issues.)

### Description

(Please describe)

### Expected behavior

(Please describe)

### Actual behavior

(Please describe)

### Steps to reproduce the problem

(Please describe if applicable)

### Specifications
- Commit (eg, 2099af7): (Please fill in)

- OS version (ie, output of the "ver" command, or N/A): (Please fill in)

- Architecture: (Please choose: x86, x64, Both, or N/A)

- Hardware (eg, Physical, VMware 15, Bochs, or N/A): (Please fill in)

- Details: (Please describe)

--
NOTE: The Details section may include related log files, crash dump files,
impact/importance of the issue, proposed fix and anything you may think useful
for fixing the issue.

It is hugely helpful and speeds up investigation of a bug check or a hang issue
if the following files are provided:
- a Debug build version of a compiled SYS file and a PDB file
- a full set of source code used to build the SYS file if it has been modified
- a log file (i.e., C:\Windows\HyperPlatform.log)
- a system crash dump file (i.e., C:\Windows\MEMORY.DMP)

GitHub does not support attaching of any binary files into issues. You may use
any free file hosting services, however. Here are some of those services:
- OneDrive
- DropBox
- GoogleDrive
- Sendspace
- Mega.nz
