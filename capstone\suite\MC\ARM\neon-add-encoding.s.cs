# CS_ARCH_ARM, CS_MODE_ARM, None
0xa0,0x08,0x41,0xf2 = vadd.i8 d16, d17, d16
0xa0,0x08,0x51,0xf2 = vadd.i16 d16, d17, d16
0xa0,0x08,0x71,0xf2 = vadd.i64 d16, d17, d16
0xa0,0x08,0x61,0xf2 = vadd.i32 d16, d17, d16
0xa1,0x0d,0x40,0xf2 = vadd.f32 d16, d16, d17
0xe2,0x0d,0x40,0xf2 = vadd.f32 q8, q8, q9
0xa0,0x00,0xc1,0xf2 = vaddl.s8 q8, d17, d16
0xa0,0x00,0xd1,0xf2 = vaddl.s16 q8, d17, d16
0xa0,0x00,0xe1,0xf2 = vaddl.s32 q8, d17, d16
0xa0,0x00,0xc1,0xf3 = vaddl.u8 q8, d17, d16
0xa0,0x00,0xd1,0xf3 = vaddl.u16 q8, d17, d16
0xa0,0x00,0xe1,0xf3 = vaddl.u32 q8, d17, d16
0xa2,0x01,0xc0,0xf2 = vaddw.s8 q8, q8, d18
0xa2,0x01,0xd0,0xf2 = vaddw.s16 q8, q8, d18
0xa2,0x01,0xe0,0xf2 = vaddw.s32 q8, q8, d18
0xa2,0x01,0xc0,0xf3 = vaddw.u8 q8, q8, d18
0xa2,0x01,0xd0,0xf3 = vaddw.u16 q8, q8, d18
0xa2,0x01,0xe0,0xf3 = vaddw.u32 q8, q8, d18
0xa1,0x00,0x40,0xf2 = vhadd.s8 d16, d16, d17
0xa1,0x00,0x50,0xf2 = vhadd.s16 d16, d16, d17
0xa1,0x00,0x60,0xf2 = vhadd.s32 d16, d16, d17
0xa1,0x00,0x40,0xf3 = vhadd.u8 d16, d16, d17
0xa1,0x00,0x50,0xf3 = vhadd.u16 d16, d16, d17
0xa1,0x00,0x60,0xf3 = vhadd.u32 d16, d16, d17
0xe2,0x00,0x40,0xf2 = vhadd.s8 q8, q8, q9
0xe2,0x00,0x50,0xf2 = vhadd.s16 q8, q8, q9
0xe2,0x00,0x60,0xf2 = vhadd.s32 q8, q8, q9
0xe2,0x00,0x40,0xf3 = vhadd.u8 q8, q8, q9
0xe2,0x00,0x50,0xf3 = vhadd.u16 q8, q8, q9
0xe2,0x00,0x60,0xf3 = vhadd.u32 q8, q8, q9
0x28,0xb0,0x0b,0xf2 = vhadd.s8 d11, d11, d24
0x27,0xc0,0x1c,0xf2 = vhadd.s16 d12, d12, d23
0x26,0xd0,0x2d,0xf2 = vhadd.s32 d13, d13, d22
0x25,0xe0,0x0e,0xf3 = vhadd.u8 d14, d14, d21
0x24,0xf0,0x1f,0xf3 = vhadd.u16 d15, d15, d20
0xa3,0x00,0x60,0xf3 = vhadd.u32 d16, d16, d19
0x68,0x20,0x02,0xf2 = vhadd.s8 q1, q1, q12
0x66,0x40,0x14,0xf2 = vhadd.s16 q2, q2, q11
0x64,0x60,0x26,0xf2 = vhadd.s32 q3, q3, q10
0x62,0x80,0x08,0xf3 = vhadd.u8 q4, q4, q9
0x60,0xa0,0x1a,0xf3 = vhadd.u16 q5, q5, q8
0x4e,0xc0,0x2c,0xf3 = vhadd.u32 q6, q6, q7
0xa1,0x01,0x40,0xf2 = vrhadd.s8 d16, d16, d17
0xa1,0x01,0x50,0xf2 = vrhadd.s16 d16, d16, d17
0xa1,0x01,0x60,0xf2 = vrhadd.s32 d16, d16, d17
0xa1,0x01,0x40,0xf3 = vrhadd.u8 d16, d16, d17
0xa1,0x01,0x50,0xf3 = vrhadd.u16 d16, d16, d17
0xa1,0x01,0x60,0xf3 = vrhadd.u32 d16, d16, d17
0xe2,0x01,0x40,0xf2 = vrhadd.s8 q8, q8, q9
0xe2,0x01,0x50,0xf2 = vrhadd.s16 q8, q8, q9
0xe2,0x01,0x60,0xf2 = vrhadd.s32 q8, q8, q9
0xe2,0x01,0x40,0xf3 = vrhadd.u8 q8, q8, q9
0xe2,0x01,0x50,0xf3 = vrhadd.u16 q8, q8, q9
0xe2,0x01,0x60,0xf3 = vrhadd.u32 q8, q8, q9
0xa1,0x01,0x40,0xf2 = vrhadd.s8 d16, d16, d17
0xa1,0x01,0x50,0xf2 = vrhadd.s16 d16, d16, d17
0xa1,0x01,0x60,0xf2 = vrhadd.s32 d16, d16, d17
0xa1,0x01,0x40,0xf3 = vrhadd.u8 d16, d16, d17
0xa1,0x01,0x50,0xf3 = vrhadd.u16 d16, d16, d17
0xa1,0x01,0x60,0xf3 = vrhadd.u32 d16, d16, d17
0xe2,0x01,0x40,0xf2 = vrhadd.s8 q8, q8, q9
0xe2,0x01,0x50,0xf2 = vrhadd.s16 q8, q8, q9
0xe2,0x01,0x60,0xf2 = vrhadd.s32 q8, q8, q9
0xe2,0x01,0x40,0xf3 = vrhadd.u8 q8, q8, q9
0xe2,0x01,0x50,0xf3 = vrhadd.u16 q8, q8, q9
0xe2,0x01,0x60,0xf3 = vrhadd.u32 q8, q8, q9
0xb1,0x00,0x40,0xf2 = vqadd.s8 d16, d16, d17
0xb1,0x00,0x50,0xf2 = vqadd.s16 d16, d16, d17
0xb1,0x00,0x60,0xf2 = vqadd.s32 d16, d16, d17
0xb1,0x00,0x70,0xf2 = vqadd.s64 d16, d16, d17
0xb1,0x00,0x40,0xf3 = vqadd.u8 d16, d16, d17
0xb1,0x00,0x50,0xf3 = vqadd.u16 d16, d16, d17
0xb1,0x00,0x60,0xf3 = vqadd.u32 d16, d16, d17
0xb1,0x00,0x70,0xf3 = vqadd.u64 d16, d16, d17
0xf2,0x00,0x40,0xf2 = vqadd.s8 q8, q8, q9
0xf2,0x00,0x50,0xf2 = vqadd.s16 q8, q8, q9
0xf2,0x00,0x60,0xf2 = vqadd.s32 q8, q8, q9
0xf2,0x00,0x70,0xf2 = vqadd.s64 q8, q8, q9
0xf2,0x00,0x40,0xf3 = vqadd.u8 q8, q8, q9
0xf2,0x00,0x50,0xf3 = vqadd.u16 q8, q8, q9
0xf2,0x00,0x60,0xf3 = vqadd.u32 q8, q8, q9
0xf2,0x00,0x70,0xf3 = vqadd.u64 q8, q8, q9
0xb1,0x00,0x40,0xf2 = vqadd.s8 d16, d16, d17
0xb1,0x00,0x50,0xf2 = vqadd.s16 d16, d16, d17
0xb1,0x00,0x60,0xf2 = vqadd.s32 d16, d16, d17
0xb1,0x00,0x70,0xf2 = vqadd.s64 d16, d16, d17
0xb1,0x00,0x40,0xf3 = vqadd.u8 d16, d16, d17
0xb1,0x00,0x50,0xf3 = vqadd.u16 d16, d16, d17
0xb1,0x00,0x60,0xf3 = vqadd.u32 d16, d16, d17
0xb1,0x00,0x70,0xf3 = vqadd.u64 d16, d16, d17
0xf2,0x00,0x40,0xf2 = vqadd.s8 q8, q8, q9
0xf2,0x00,0x50,0xf2 = vqadd.s16 q8, q8, q9
0xf2,0x00,0x60,0xf2 = vqadd.s32 q8, q8, q9
0xf2,0x00,0x70,0xf2 = vqadd.s64 q8, q8, q9
0xf2,0x00,0x40,0xf3 = vqadd.u8 q8, q8, q9
0xf2,0x00,0x50,0xf3 = vqadd.u16 q8, q8, q9
0xf2,0x00,0x60,0xf3 = vqadd.u32 q8, q8, q9
0xf2,0x00,0x70,0xf3 = vqadd.u64 q8, q8, q9
0xa2,0x04,0xc0,0xf2 = vaddhn.i16 d16, q8, q9
0xa2,0x04,0xd0,0xf2 = vaddhn.i32 d16, q8, q9
0xa2,0x04,0xe0,0xf2 = vaddhn.i64 d16, q8, q9
0xa2,0x04,0xc0,0xf3 = vraddhn.i16 d16, q8, q9
0xa2,0x04,0xd0,0xf3 = vraddhn.i32 d16, q8, q9
0xa2,0x04,0xe0,0xf3 = vraddhn.i64 d16, q8, q9
0x05,0x68,0x06,0xf2 = vadd.i8 d6, d6, d5
0x01,0x78,0x17,0xf2 = vadd.i16 d7, d7, d1
0x02,0x88,0x28,0xf2 = vadd.i32 d8, d8, d2
0x03,0x98,0x39,0xf2 = vadd.i64 d9, d9, d3
0x4a,0xc8,0x0c,0xf2 = vadd.i8 q6, q6, q5
0x42,0xe8,0x1e,0xf2 = vadd.i16 q7, q7, q1
0xc4,0x08,0x60,0xf2 = vadd.i32 q8, q8, q2
0xc6,0x28,0x72,0xf2 = vadd.i64 q9, q9, q3
0x05,0xc1,0x8c,0xf2 = vaddw.s8 q6, q6, d5
0x01,0xe1,0x9e,0xf2 = vaddw.s16 q7, q7, d1
0x82,0x01,0xe0,0xf2 = vaddw.s32 q8, q8, d2
0x05,0xc1,0x8c,0xf3 = vaddw.u8 q6, q6, d5
0x01,0xe1,0x9e,0xf3 = vaddw.u16 q7, q7, d1
0x82,0x01,0xe0,0xf3 = vaddw.u32 q8, q8, d2
