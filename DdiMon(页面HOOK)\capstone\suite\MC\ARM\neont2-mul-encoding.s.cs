# CS_ARCH_ARM, CS_MODE_THUMB, None
0x40,0xef,0xb1,0x09 = vmul.i8 d16, d16, d17
0x50,0xef,0xb1,0x09 = vmul.i16 d16, d16, d17
0x60,0xef,0xb1,0x09 = vmul.i32 d16, d16, d17
0x40,0xff,0xb1,0x0d = vmul.f32 d16, d16, d17
0x40,0xef,0xf2,0x09 = vmul.i8 q8, q8, q9
0x50,0xef,0xf2,0x09 = vmul.i16 q8, q8, q9
0x60,0xef,0xf2,0x09 = vmul.i32 q8, q8, q9
0x40,0xff,0xf2,0x0d = vmul.f32 q8, q8, q9
0x40,0xff,0xb1,0x09 = vmul.p8 d16, d16, d17
0x40,0xff,0xf2,0x09 = vmul.p8 q8, q8, q9
0xd8,0xef,0x68,0x28 = vmul.i16 d18, d8, d0[3]
0x50,0xef,0xa1,0x0b = vqdmulh.s16 d16, d16, d17
0x60,0xef,0xa1,0x0b = vqdmulh.s32 d16, d16, d17
0x50,0xef,0xe2,0x0b = vqdmulh.s16 q8, q8, q9
0x60,0xef,0xe2,0x0b = vqdmulh.s32 q8, q8, q9
0x92,0xef,0x43,0xbc = vqdmulh.s16 d11, d2, d3[0]
0x50,0xff,0xa1,0x0b = vqrdmulh.s16 d16, d16, d17
0x60,0xff,0xa1,0x0b = vqrdmulh.s32 d16, d16, d17
0x50,0xff,0xe2,0x0b = vqrdmulh.s16 q8, q8, q9
0x60,0xff,0xe2,0x0b = vqrdmulh.s32 q8, q8, q9
0xc0,0xef,0xa1,0x0c = vmull.s8 q8, d16, d17
0xd0,0xef,0xa1,0x0c = vmull.s16 q8, d16, d17
0xe0,0xef,0xa1,0x0c = vmull.s32 q8, d16, d17
0xc0,0xff,0xa1,0x0c = vmull.u8 q8, d16, d17
0xd0,0xff,0xa1,0x0c = vmull.u16 q8, d16, d17
0xe0,0xff,0xa1,0x0c = vmull.u32 q8, d16, d17
0xc0,0xef,0xa1,0x0e = vmull.p8 q8, d16, d17
0xd0,0xef,0xa1,0x0d = vqdmull.s16 q8, d16, d17
0xe0,0xef,0xa1,0x0d = vqdmull.s32 q8, d16, d17
0x97,0xef,0x49,0x2b = vqdmull.s16 q1, d7, d1[1]
