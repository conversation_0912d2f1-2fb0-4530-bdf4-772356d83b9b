# CS_ARCH_ARM, CS_MODE_THUMB, None
0xfb,0xff,0x20,0x07 = vcvt.s32.f32 d16, d16
0xfb,0xff,0xa0,0x07 = vcvt.u32.f32 d16, d16
0xfb,0xff,0x20,0x06 = vcvt.f32.s32 d16, d16
0xfb,0xff,0xa0,0x06 = vcvt.f32.u32 d16, d16
0xfb,0xff,0x60,0x07 = vcvt.s32.f32 q8, q8
0xfb,0xff,0xe0,0x07 = vcvt.u32.f32 q8, q8
0xfb,0xff,0x60,0x06 = vcvt.f32.s32 q8, q8
0xfb,0xff,0xe0,0x06 = vcvt.f32.u32 q8, q8
0xff,0xef,0x30,0x0f = vcvt.s32.f32 d16, d16, #1
0xff,0xff,0x30,0x0f = vcvt.u32.f32 d16, d16, #1
0xff,0xef,0x30,0x0e = vcvt.f32.s32 d16, d16, #1
0xff,0xff,0x30,0x0e = vcvt.f32.u32 d16, d16, #1
0xff,0xef,0x70,0x0f = vcvt.s32.f32 q8, q8, #1
0xff,0xff,0x70,0x0f = vcvt.u32.f32 q8, q8, #1
0xff,0xef,0x70,0x0e = vcvt.f32.s32 q8, q8, #1
0xff,0xff,0x70,0x0e = vcvt.f32.u32 q8, q8, #1
0xf6,0xff,0x20,0x07 = vcvt.f32.f16 q8, d16
0xf6,0xff,0x20,0x06 = vcvt.f16.f32 d16, q8
