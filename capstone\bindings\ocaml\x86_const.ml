(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [x86_const.ml] *)

(* X86 registers *)

let _X86_REG_INVALID = 0;;
let _X86_REG_AH = 1;;
let _X86_REG_AL = 2;;
let _X86_REG_AX = 3;;
let _X86_REG_BH = 4;;
let _X86_REG_BL = 5;;
let _X86_REG_BP = 6;;
let _X86_REG_BPL = 7;;
let _X86_REG_BX = 8;;
let _X86_REG_CH = 9;;
let _X86_REG_CL = 10;;
let _X86_REG_CS = 11;;
let _X86_REG_CX = 12;;
let _X86_REG_DH = 13;;
let _X86_REG_DI = 14;;
let _X86_REG_DIL = 15;;
let _X86_REG_DL = 16;;
let _X86_REG_DS = 17;;
let _X86_REG_DX = 18;;
let _X86_REG_EAX = 19;;
let _X86_REG_EBP = 20;;
let _X86_REG_EBX = 21;;
let _X86_REG_ECX = 22;;
let _X86_REG_EDI = 23;;
let _X86_REG_EDX = 24;;
let _X86_REG_EFLAGS = 25;;
let _X86_REG_EIP = 26;;
let _X86_REG_EIZ = 27;;
let _X86_REG_ES = 28;;
let _X86_REG_ESI = 29;;
let _X86_REG_ESP = 30;;
let _X86_REG_FPSW = 31;;
let _X86_REG_FS = 32;;
let _X86_REG_GS = 33;;
let _X86_REG_IP = 34;;
let _X86_REG_RAX = 35;;
let _X86_REG_RBP = 36;;
let _X86_REG_RBX = 37;;
let _X86_REG_RCX = 38;;
let _X86_REG_RDI = 39;;
let _X86_REG_RDX = 40;;
let _X86_REG_RIP = 41;;
let _X86_REG_RIZ = 42;;
let _X86_REG_RSI = 43;;
let _X86_REG_RSP = 44;;
let _X86_REG_SI = 45;;
let _X86_REG_SIL = 46;;
let _X86_REG_SP = 47;;
let _X86_REG_SPL = 48;;
let _X86_REG_SS = 49;;
let _X86_REG_CR0 = 50;;
let _X86_REG_CR1 = 51;;
let _X86_REG_CR2 = 52;;
let _X86_REG_CR3 = 53;;
let _X86_REG_CR4 = 54;;
let _X86_REG_CR5 = 55;;
let _X86_REG_CR6 = 56;;
let _X86_REG_CR7 = 57;;
let _X86_REG_CR8 = 58;;
let _X86_REG_CR9 = 59;;
let _X86_REG_CR10 = 60;;
let _X86_REG_CR11 = 61;;
let _X86_REG_CR12 = 62;;
let _X86_REG_CR13 = 63;;
let _X86_REG_CR14 = 64;;
let _X86_REG_CR15 = 65;;
let _X86_REG_DR0 = 66;;
let _X86_REG_DR1 = 67;;
let _X86_REG_DR2 = 68;;
let _X86_REG_DR3 = 69;;
let _X86_REG_DR4 = 70;;
let _X86_REG_DR5 = 71;;
let _X86_REG_DR6 = 72;;
let _X86_REG_DR7 = 73;;
let _X86_REG_FP0 = 74;;
let _X86_REG_FP1 = 75;;
let _X86_REG_FP2 = 76;;
let _X86_REG_FP3 = 77;;
let _X86_REG_FP4 = 78;;
let _X86_REG_FP5 = 79;;
let _X86_REG_FP6 = 80;;
let _X86_REG_FP7 = 81;;
let _X86_REG_K0 = 82;;
let _X86_REG_K1 = 83;;
let _X86_REG_K2 = 84;;
let _X86_REG_K3 = 85;;
let _X86_REG_K4 = 86;;
let _X86_REG_K5 = 87;;
let _X86_REG_K6 = 88;;
let _X86_REG_K7 = 89;;
let _X86_REG_MM0 = 90;;
let _X86_REG_MM1 = 91;;
let _X86_REG_MM2 = 92;;
let _X86_REG_MM3 = 93;;
let _X86_REG_MM4 = 94;;
let _X86_REG_MM5 = 95;;
let _X86_REG_MM6 = 96;;
let _X86_REG_MM7 = 97;;
let _X86_REG_R8 = 98;;
let _X86_REG_R9 = 99;;
let _X86_REG_R10 = 100;;
let _X86_REG_R11 = 101;;
let _X86_REG_R12 = 102;;
let _X86_REG_R13 = 103;;
let _X86_REG_R14 = 104;;
let _X86_REG_R15 = 105;;
let _X86_REG_ST0 = 106;;
let _X86_REG_ST1 = 107;;
let _X86_REG_ST2 = 108;;
let _X86_REG_ST3 = 109;;
let _X86_REG_ST4 = 110;;
let _X86_REG_ST5 = 111;;
let _X86_REG_ST6 = 112;;
let _X86_REG_ST7 = 113;;
let _X86_REG_XMM0 = 114;;
let _X86_REG_XMM1 = 115;;
let _X86_REG_XMM2 = 116;;
let _X86_REG_XMM3 = 117;;
let _X86_REG_XMM4 = 118;;
let _X86_REG_XMM5 = 119;;
let _X86_REG_XMM6 = 120;;
let _X86_REG_XMM7 = 121;;
let _X86_REG_XMM8 = 122;;
let _X86_REG_XMM9 = 123;;
let _X86_REG_XMM10 = 124;;
let _X86_REG_XMM11 = 125;;
let _X86_REG_XMM12 = 126;;
let _X86_REG_XMM13 = 127;;
let _X86_REG_XMM14 = 128;;
let _X86_REG_XMM15 = 129;;
let _X86_REG_XMM16 = 130;;
let _X86_REG_XMM17 = 131;;
let _X86_REG_XMM18 = 132;;
let _X86_REG_XMM19 = 133;;
let _X86_REG_XMM20 = 134;;
let _X86_REG_XMM21 = 135;;
let _X86_REG_XMM22 = 136;;
let _X86_REG_XMM23 = 137;;
let _X86_REG_XMM24 = 138;;
let _X86_REG_XMM25 = 139;;
let _X86_REG_XMM26 = 140;;
let _X86_REG_XMM27 = 141;;
let _X86_REG_XMM28 = 142;;
let _X86_REG_XMM29 = 143;;
let _X86_REG_XMM30 = 144;;
let _X86_REG_XMM31 = 145;;
let _X86_REG_YMM0 = 146;;
let _X86_REG_YMM1 = 147;;
let _X86_REG_YMM2 = 148;;
let _X86_REG_YMM3 = 149;;
let _X86_REG_YMM4 = 150;;
let _X86_REG_YMM5 = 151;;
let _X86_REG_YMM6 = 152;;
let _X86_REG_YMM7 = 153;;
let _X86_REG_YMM8 = 154;;
let _X86_REG_YMM9 = 155;;
let _X86_REG_YMM10 = 156;;
let _X86_REG_YMM11 = 157;;
let _X86_REG_YMM12 = 158;;
let _X86_REG_YMM13 = 159;;
let _X86_REG_YMM14 = 160;;
let _X86_REG_YMM15 = 161;;
let _X86_REG_YMM16 = 162;;
let _X86_REG_YMM17 = 163;;
let _X86_REG_YMM18 = 164;;
let _X86_REG_YMM19 = 165;;
let _X86_REG_YMM20 = 166;;
let _X86_REG_YMM21 = 167;;
let _X86_REG_YMM22 = 168;;
let _X86_REG_YMM23 = 169;;
let _X86_REG_YMM24 = 170;;
let _X86_REG_YMM25 = 171;;
let _X86_REG_YMM26 = 172;;
let _X86_REG_YMM27 = 173;;
let _X86_REG_YMM28 = 174;;
let _X86_REG_YMM29 = 175;;
let _X86_REG_YMM30 = 176;;
let _X86_REG_YMM31 = 177;;
let _X86_REG_ZMM0 = 178;;
let _X86_REG_ZMM1 = 179;;
let _X86_REG_ZMM2 = 180;;
let _X86_REG_ZMM3 = 181;;
let _X86_REG_ZMM4 = 182;;
let _X86_REG_ZMM5 = 183;;
let _X86_REG_ZMM6 = 184;;
let _X86_REG_ZMM7 = 185;;
let _X86_REG_ZMM8 = 186;;
let _X86_REG_ZMM9 = 187;;
let _X86_REG_ZMM10 = 188;;
let _X86_REG_ZMM11 = 189;;
let _X86_REG_ZMM12 = 190;;
let _X86_REG_ZMM13 = 191;;
let _X86_REG_ZMM14 = 192;;
let _X86_REG_ZMM15 = 193;;
let _X86_REG_ZMM16 = 194;;
let _X86_REG_ZMM17 = 195;;
let _X86_REG_ZMM18 = 196;;
let _X86_REG_ZMM19 = 197;;
let _X86_REG_ZMM20 = 198;;
let _X86_REG_ZMM21 = 199;;
let _X86_REG_ZMM22 = 200;;
let _X86_REG_ZMM23 = 201;;
let _X86_REG_ZMM24 = 202;;
let _X86_REG_ZMM25 = 203;;
let _X86_REG_ZMM26 = 204;;
let _X86_REG_ZMM27 = 205;;
let _X86_REG_ZMM28 = 206;;
let _X86_REG_ZMM29 = 207;;
let _X86_REG_ZMM30 = 208;;
let _X86_REG_ZMM31 = 209;;
let _X86_REG_R8B = 210;;
let _X86_REG_R9B = 211;;
let _X86_REG_R10B = 212;;
let _X86_REG_R11B = 213;;
let _X86_REG_R12B = 214;;
let _X86_REG_R13B = 215;;
let _X86_REG_R14B = 216;;
let _X86_REG_R15B = 217;;
let _X86_REG_R8D = 218;;
let _X86_REG_R9D = 219;;
let _X86_REG_R10D = 220;;
let _X86_REG_R11D = 221;;
let _X86_REG_R12D = 222;;
let _X86_REG_R13D = 223;;
let _X86_REG_R14D = 224;;
let _X86_REG_R15D = 225;;
let _X86_REG_R8W = 226;;
let _X86_REG_R9W = 227;;
let _X86_REG_R10W = 228;;
let _X86_REG_R11W = 229;;
let _X86_REG_R12W = 230;;
let _X86_REG_R13W = 231;;
let _X86_REG_R14W = 232;;
let _X86_REG_R15W = 233;;
let _X86_REG_ENDING = 234;;

(* Operand type for instruction's operands *)

let _X86_OP_INVALID = 0;;
let _X86_OP_REG = 1;;
let _X86_OP_IMM = 2;;
let _X86_OP_MEM = 3;;
let _X86_OP_FP = 4;;

(* AVX broadcast type *)

let _X86_AVX_BCAST_INVALID = 0;;
let _X86_AVX_BCAST_2 = 1;;
let _X86_AVX_BCAST_4 = 2;;
let _X86_AVX_BCAST_8 = 3;;
let _X86_AVX_BCAST_16 = 4;;

(* SSE Code Condition type *)

let _X86_SSE_CC_INVALID = 0;;
let _X86_SSE_CC_EQ = 1;;
let _X86_SSE_CC_LT = 2;;
let _X86_SSE_CC_LE = 3;;
let _X86_SSE_CC_UNORD = 4;;
let _X86_SSE_CC_NEQ = 5;;
let _X86_SSE_CC_NLT = 6;;
let _X86_SSE_CC_NLE = 7;;
let _X86_SSE_CC_ORD = 8;;
let _X86_SSE_CC_EQ_UQ = 9;;
let _X86_SSE_CC_NGE = 10;;
let _X86_SSE_CC_NGT = 11;;
let _X86_SSE_CC_FALSE = 12;;
let _X86_SSE_CC_NEQ_OQ = 13;;
let _X86_SSE_CC_GE = 14;;
let _X86_SSE_CC_GT = 15;;
let _X86_SSE_CC_TRUE = 16;;

(* AVX Code Condition type *)

let _X86_AVX_CC_INVALID = 0;;
let _X86_AVX_CC_EQ = 1;;
let _X86_AVX_CC_LT = 2;;
let _X86_AVX_CC_LE = 3;;
let _X86_AVX_CC_UNORD = 4;;
let _X86_AVX_CC_NEQ = 5;;
let _X86_AVX_CC_NLT = 6;;
let _X86_AVX_CC_NLE = 7;;
let _X86_AVX_CC_ORD = 8;;
let _X86_AVX_CC_EQ_UQ = 9;;
let _X86_AVX_CC_NGE = 10;;
let _X86_AVX_CC_NGT = 11;;
let _X86_AVX_CC_FALSE = 12;;
let _X86_AVX_CC_NEQ_OQ = 13;;
let _X86_AVX_CC_GE = 14;;
let _X86_AVX_CC_GT = 15;;
let _X86_AVX_CC_TRUE = 16;;
let _X86_AVX_CC_EQ_OS = 17;;
let _X86_AVX_CC_LT_OQ = 18;;
let _X86_AVX_CC_LE_OQ = 19;;
let _X86_AVX_CC_UNORD_S = 20;;
let _X86_AVX_CC_NEQ_US = 21;;
let _X86_AVX_CC_NLT_UQ = 22;;
let _X86_AVX_CC_NLE_UQ = 23;;
let _X86_AVX_CC_ORD_S = 24;;
let _X86_AVX_CC_EQ_US = 25;;
let _X86_AVX_CC_NGE_UQ = 26;;
let _X86_AVX_CC_NGT_UQ = 27;;
let _X86_AVX_CC_FALSE_OS = 28;;
let _X86_AVX_CC_NEQ_OS = 29;;
let _X86_AVX_CC_GE_OQ = 30;;
let _X86_AVX_CC_GT_OQ = 31;;
let _X86_AVX_CC_TRUE_US = 32;;

(* AVX static rounding mode type *)

let _X86_AVX_RM_INVALID = 0;;
let _X86_AVX_RM_RN = 1;;
let _X86_AVX_RM_RD = 2;;
let _X86_AVX_RM_RU = 3;;
let _X86_AVX_RM_RZ = 4;;

(* Instruction prefixes - to be used in cs_x86.prefix[] *)
let _X86_PREFIX_LOCK = 0xf0;;
let _X86_PREFIX_REP = 0xf3;;
let _X86_PREFIX_REPNE = 0xf2;;
let _X86_PREFIX_CS = 0x2e;;
let _X86_PREFIX_SS = 0x36;;
let _X86_PREFIX_DS = 0x3e;;
let _X86_PREFIX_ES = 0x26;;
let _X86_PREFIX_FS = 0x64;;
let _X86_PREFIX_GS = 0x65;;
let _X86_PREFIX_OPSIZE = 0x66;;
let _X86_PREFIX_ADDRSIZE = 0x67;;

(* X86 instructions *)

let _X86_INS_INVALID = 0;;
let _X86_INS_AAA = 1;;
let _X86_INS_AAD = 2;;
let _X86_INS_AAM = 3;;
let _X86_INS_AAS = 4;;
let _X86_INS_FABS = 5;;
let _X86_INS_ADC = 6;;
let _X86_INS_ADCX = 7;;
let _X86_INS_ADD = 8;;
let _X86_INS_ADDPD = 9;;
let _X86_INS_ADDPS = 10;;
let _X86_INS_ADDSD = 11;;
let _X86_INS_ADDSS = 12;;
let _X86_INS_ADDSUBPD = 13;;
let _X86_INS_ADDSUBPS = 14;;
let _X86_INS_FADD = 15;;
let _X86_INS_FIADD = 16;;
let _X86_INS_FADDP = 17;;
let _X86_INS_ADOX = 18;;
let _X86_INS_AESDECLAST = 19;;
let _X86_INS_AESDEC = 20;;
let _X86_INS_AESENCLAST = 21;;
let _X86_INS_AESENC = 22;;
let _X86_INS_AESIMC = 23;;
let _X86_INS_AESKEYGENASSIST = 24;;
let _X86_INS_AND = 25;;
let _X86_INS_ANDN = 26;;
let _X86_INS_ANDNPD = 27;;
let _X86_INS_ANDNPS = 28;;
let _X86_INS_ANDPD = 29;;
let _X86_INS_ANDPS = 30;;
let _X86_INS_ARPL = 31;;
let _X86_INS_BEXTR = 32;;
let _X86_INS_BLCFILL = 33;;
let _X86_INS_BLCI = 34;;
let _X86_INS_BLCIC = 35;;
let _X86_INS_BLCMSK = 36;;
let _X86_INS_BLCS = 37;;
let _X86_INS_BLENDPD = 38;;
let _X86_INS_BLENDPS = 39;;
let _X86_INS_BLENDVPD = 40;;
let _X86_INS_BLENDVPS = 41;;
let _X86_INS_BLSFILL = 42;;
let _X86_INS_BLSI = 43;;
let _X86_INS_BLSIC = 44;;
let _X86_INS_BLSMSK = 45;;
let _X86_INS_BLSR = 46;;
let _X86_INS_BOUND = 47;;
let _X86_INS_BSF = 48;;
let _X86_INS_BSR = 49;;
let _X86_INS_BSWAP = 50;;
let _X86_INS_BT = 51;;
let _X86_INS_BTC = 52;;
let _X86_INS_BTR = 53;;
let _X86_INS_BTS = 54;;
let _X86_INS_BZHI = 55;;
let _X86_INS_CALL = 56;;
let _X86_INS_CBW = 57;;
let _X86_INS_CDQ = 58;;
let _X86_INS_CDQE = 59;;
let _X86_INS_FCHS = 60;;
let _X86_INS_CLAC = 61;;
let _X86_INS_CLC = 62;;
let _X86_INS_CLD = 63;;
let _X86_INS_CLFLUSH = 64;;
let _X86_INS_CLGI = 65;;
let _X86_INS_CLI = 66;;
let _X86_INS_CLTS = 67;;
let _X86_INS_CMC = 68;;
let _X86_INS_CMOVA = 69;;
let _X86_INS_CMOVAE = 70;;
let _X86_INS_CMOVB = 71;;
let _X86_INS_CMOVBE = 72;;
let _X86_INS_FCMOVBE = 73;;
let _X86_INS_FCMOVB = 74;;
let _X86_INS_CMOVE = 75;;
let _X86_INS_FCMOVE = 76;;
let _X86_INS_CMOVG = 77;;
let _X86_INS_CMOVGE = 78;;
let _X86_INS_CMOVL = 79;;
let _X86_INS_CMOVLE = 80;;
let _X86_INS_FCMOVNBE = 81;;
let _X86_INS_FCMOVNB = 82;;
let _X86_INS_CMOVNE = 83;;
let _X86_INS_FCMOVNE = 84;;
let _X86_INS_CMOVNO = 85;;
let _X86_INS_CMOVNP = 86;;
let _X86_INS_FCMOVNU = 87;;
let _X86_INS_CMOVNS = 88;;
let _X86_INS_CMOVO = 89;;
let _X86_INS_CMOVP = 90;;
let _X86_INS_FCMOVU = 91;;
let _X86_INS_CMOVS = 92;;
let _X86_INS_CMP = 93;;
let _X86_INS_CMPPD = 94;;
let _X86_INS_CMPPS = 95;;
let _X86_INS_CMPSB = 96;;
let _X86_INS_CMPSD = 97;;
let _X86_INS_CMPSQ = 98;;
let _X86_INS_CMPSS = 99;;
let _X86_INS_CMPSW = 100;;
let _X86_INS_CMPXCHG16B = 101;;
let _X86_INS_CMPXCHG = 102;;
let _X86_INS_CMPXCHG8B = 103;;
let _X86_INS_COMISD = 104;;
let _X86_INS_COMISS = 105;;
let _X86_INS_FCOMP = 106;;
let _X86_INS_FCOMPI = 107;;
let _X86_INS_FCOMI = 108;;
let _X86_INS_FCOM = 109;;
let _X86_INS_FCOS = 110;;
let _X86_INS_CPUID = 111;;
let _X86_INS_CQO = 112;;
let _X86_INS_CRC32 = 113;;
let _X86_INS_CVTDQ2PD = 114;;
let _X86_INS_CVTDQ2PS = 115;;
let _X86_INS_CVTPD2DQ = 116;;
let _X86_INS_CVTPD2PS = 117;;
let _X86_INS_CVTPS2DQ = 118;;
let _X86_INS_CVTPS2PD = 119;;
let _X86_INS_CVTSD2SI = 120;;
let _X86_INS_CVTSD2SS = 121;;
let _X86_INS_CVTSI2SD = 122;;
let _X86_INS_CVTSI2SS = 123;;
let _X86_INS_CVTSS2SD = 124;;
let _X86_INS_CVTSS2SI = 125;;
let _X86_INS_CVTTPD2DQ = 126;;
let _X86_INS_CVTTPS2DQ = 127;;
let _X86_INS_CVTTSD2SI = 128;;
let _X86_INS_CVTTSS2SI = 129;;
let _X86_INS_CWD = 130;;
let _X86_INS_CWDE = 131;;
let _X86_INS_DAA = 132;;
let _X86_INS_DAS = 133;;
let _X86_INS_DATA16 = 134;;
let _X86_INS_DEC = 135;;
let _X86_INS_DIV = 136;;
let _X86_INS_DIVPD = 137;;
let _X86_INS_DIVPS = 138;;
let _X86_INS_FDIVR = 139;;
let _X86_INS_FIDIVR = 140;;
let _X86_INS_FDIVRP = 141;;
let _X86_INS_DIVSD = 142;;
let _X86_INS_DIVSS = 143;;
let _X86_INS_FDIV = 144;;
let _X86_INS_FIDIV = 145;;
let _X86_INS_FDIVP = 146;;
let _X86_INS_DPPD = 147;;
let _X86_INS_DPPS = 148;;
let _X86_INS_RET = 149;;
let _X86_INS_ENCLS = 150;;
let _X86_INS_ENCLU = 151;;
let _X86_INS_ENTER = 152;;
let _X86_INS_EXTRACTPS = 153;;
let _X86_INS_EXTRQ = 154;;
let _X86_INS_F2XM1 = 155;;
let _X86_INS_LCALL = 156;;
let _X86_INS_LJMP = 157;;
let _X86_INS_FBLD = 158;;
let _X86_INS_FBSTP = 159;;
let _X86_INS_FCOMPP = 160;;
let _X86_INS_FDECSTP = 161;;
let _X86_INS_FEMMS = 162;;
let _X86_INS_FFREE = 163;;
let _X86_INS_FICOM = 164;;
let _X86_INS_FICOMP = 165;;
let _X86_INS_FINCSTP = 166;;
let _X86_INS_FLDCW = 167;;
let _X86_INS_FLDENV = 168;;
let _X86_INS_FLDL2E = 169;;
let _X86_INS_FLDL2T = 170;;
let _X86_INS_FLDLG2 = 171;;
let _X86_INS_FLDLN2 = 172;;
let _X86_INS_FLDPI = 173;;
let _X86_INS_FNCLEX = 174;;
let _X86_INS_FNINIT = 175;;
let _X86_INS_FNOP = 176;;
let _X86_INS_FNSTCW = 177;;
let _X86_INS_FNSTSW = 178;;
let _X86_INS_FPATAN = 179;;
let _X86_INS_FPREM = 180;;
let _X86_INS_FPREM1 = 181;;
let _X86_INS_FPTAN = 182;;
let _X86_INS_FRNDINT = 183;;
let _X86_INS_FRSTOR = 184;;
let _X86_INS_FNSAVE = 185;;
let _X86_INS_FSCALE = 186;;
let _X86_INS_FSETPM = 187;;
let _X86_INS_FSINCOS = 188;;
let _X86_INS_FNSTENV = 189;;
let _X86_INS_FXAM = 190;;
let _X86_INS_FXRSTOR = 191;;
let _X86_INS_FXRSTOR64 = 192;;
let _X86_INS_FXSAVE = 193;;
let _X86_INS_FXSAVE64 = 194;;
let _X86_INS_FXTRACT = 195;;
let _X86_INS_FYL2X = 196;;
let _X86_INS_FYL2XP1 = 197;;
let _X86_INS_MOVAPD = 198;;
let _X86_INS_MOVAPS = 199;;
let _X86_INS_ORPD = 200;;
let _X86_INS_ORPS = 201;;
let _X86_INS_VMOVAPD = 202;;
let _X86_INS_VMOVAPS = 203;;
let _X86_INS_XORPD = 204;;
let _X86_INS_XORPS = 205;;
let _X86_INS_GETSEC = 206;;
let _X86_INS_HADDPD = 207;;
let _X86_INS_HADDPS = 208;;
let _X86_INS_HLT = 209;;
let _X86_INS_HSUBPD = 210;;
let _X86_INS_HSUBPS = 211;;
let _X86_INS_IDIV = 212;;
let _X86_INS_FILD = 213;;
let _X86_INS_IMUL = 214;;
let _X86_INS_IN = 215;;
let _X86_INS_INC = 216;;
let _X86_INS_INSB = 217;;
let _X86_INS_INSERTPS = 218;;
let _X86_INS_INSERTQ = 219;;
let _X86_INS_INSD = 220;;
let _X86_INS_INSW = 221;;
let _X86_INS_INT = 222;;
let _X86_INS_INT1 = 223;;
let _X86_INS_INT3 = 224;;
let _X86_INS_INTO = 225;;
let _X86_INS_INVD = 226;;
let _X86_INS_INVEPT = 227;;
let _X86_INS_INVLPG = 228;;
let _X86_INS_INVLPGA = 229;;
let _X86_INS_INVPCID = 230;;
let _X86_INS_INVVPID = 231;;
let _X86_INS_IRET = 232;;
let _X86_INS_IRETD = 233;;
let _X86_INS_IRETQ = 234;;
let _X86_INS_FISTTP = 235;;
let _X86_INS_FIST = 236;;
let _X86_INS_FISTP = 237;;
let _X86_INS_UCOMISD = 238;;
let _X86_INS_UCOMISS = 239;;
let _X86_INS_VCMP = 240;;
let _X86_INS_VCOMISD = 241;;
let _X86_INS_VCOMISS = 242;;
let _X86_INS_VCVTSD2SS = 243;;
let _X86_INS_VCVTSI2SD = 244;;
let _X86_INS_VCVTSI2SS = 245;;
let _X86_INS_VCVTSS2SD = 246;;
let _X86_INS_VCVTTSD2SI = 247;;
let _X86_INS_VCVTTSD2USI = 248;;
let _X86_INS_VCVTTSS2SI = 249;;
let _X86_INS_VCVTTSS2USI = 250;;
let _X86_INS_VCVTUSI2SD = 251;;
let _X86_INS_VCVTUSI2SS = 252;;
let _X86_INS_VUCOMISD = 253;;
let _X86_INS_VUCOMISS = 254;;
let _X86_INS_JAE = 255;;
let _X86_INS_JA = 256;;
let _X86_INS_JBE = 257;;
let _X86_INS_JB = 258;;
let _X86_INS_JCXZ = 259;;
let _X86_INS_JECXZ = 260;;
let _X86_INS_JE = 261;;
let _X86_INS_JGE = 262;;
let _X86_INS_JG = 263;;
let _X86_INS_JLE = 264;;
let _X86_INS_JL = 265;;
let _X86_INS_JMP = 266;;
let _X86_INS_JNE = 267;;
let _X86_INS_JNO = 268;;
let _X86_INS_JNP = 269;;
let _X86_INS_JNS = 270;;
let _X86_INS_JO = 271;;
let _X86_INS_JP = 272;;
let _X86_INS_JRCXZ = 273;;
let _X86_INS_JS = 274;;
let _X86_INS_KANDB = 275;;
let _X86_INS_KANDD = 276;;
let _X86_INS_KANDNB = 277;;
let _X86_INS_KANDND = 278;;
let _X86_INS_KANDNQ = 279;;
let _X86_INS_KANDNW = 280;;
let _X86_INS_KANDQ = 281;;
let _X86_INS_KANDW = 282;;
let _X86_INS_KMOVB = 283;;
let _X86_INS_KMOVD = 284;;
let _X86_INS_KMOVQ = 285;;
let _X86_INS_KMOVW = 286;;
let _X86_INS_KNOTB = 287;;
let _X86_INS_KNOTD = 288;;
let _X86_INS_KNOTQ = 289;;
let _X86_INS_KNOTW = 290;;
let _X86_INS_KORB = 291;;
let _X86_INS_KORD = 292;;
let _X86_INS_KORQ = 293;;
let _X86_INS_KORTESTW = 294;;
let _X86_INS_KORW = 295;;
let _X86_INS_KSHIFTLW = 296;;
let _X86_INS_KSHIFTRW = 297;;
let _X86_INS_KUNPCKBW = 298;;
let _X86_INS_KXNORB = 299;;
let _X86_INS_KXNORD = 300;;
let _X86_INS_KXNORQ = 301;;
let _X86_INS_KXNORW = 302;;
let _X86_INS_KXORB = 303;;
let _X86_INS_KXORD = 304;;
let _X86_INS_KXORQ = 305;;
let _X86_INS_KXORW = 306;;
let _X86_INS_LAHF = 307;;
let _X86_INS_LAR = 308;;
let _X86_INS_LDDQU = 309;;
let _X86_INS_LDMXCSR = 310;;
let _X86_INS_LDS = 311;;
let _X86_INS_FLDZ = 312;;
let _X86_INS_FLD1 = 313;;
let _X86_INS_FLD = 314;;
let _X86_INS_LEA = 315;;
let _X86_INS_LEAVE = 316;;
let _X86_INS_LES = 317;;
let _X86_INS_LFENCE = 318;;
let _X86_INS_LFS = 319;;
let _X86_INS_LGDT = 320;;
let _X86_INS_LGS = 321;;
let _X86_INS_LIDT = 322;;
let _X86_INS_LLDT = 323;;
let _X86_INS_LMSW = 324;;
let _X86_INS_OR = 325;;
let _X86_INS_SUB = 326;;
let _X86_INS_XOR = 327;;
let _X86_INS_LODSB = 328;;
let _X86_INS_LODSD = 329;;
let _X86_INS_LODSQ = 330;;
let _X86_INS_LODSW = 331;;
let _X86_INS_LOOP = 332;;
let _X86_INS_LOOPE = 333;;
let _X86_INS_LOOPNE = 334;;
let _X86_INS_RETF = 335;;
let _X86_INS_RETFQ = 336;;
let _X86_INS_LSL = 337;;
let _X86_INS_LSS = 338;;
let _X86_INS_LTR = 339;;
let _X86_INS_XADD = 340;;
let _X86_INS_LZCNT = 341;;
let _X86_INS_MASKMOVDQU = 342;;
let _X86_INS_MAXPD = 343;;
let _X86_INS_MAXPS = 344;;
let _X86_INS_MAXSD = 345;;
let _X86_INS_MAXSS = 346;;
let _X86_INS_MFENCE = 347;;
let _X86_INS_MINPD = 348;;
let _X86_INS_MINPS = 349;;
let _X86_INS_MINSD = 350;;
let _X86_INS_MINSS = 351;;
let _X86_INS_CVTPD2PI = 352;;
let _X86_INS_CVTPI2PD = 353;;
let _X86_INS_CVTPI2PS = 354;;
let _X86_INS_CVTPS2PI = 355;;
let _X86_INS_CVTTPD2PI = 356;;
let _X86_INS_CVTTPS2PI = 357;;
let _X86_INS_EMMS = 358;;
let _X86_INS_MASKMOVQ = 359;;
let _X86_INS_MOVD = 360;;
let _X86_INS_MOVDQ2Q = 361;;
let _X86_INS_MOVNTQ = 362;;
let _X86_INS_MOVQ2DQ = 363;;
let _X86_INS_MOVQ = 364;;
let _X86_INS_PABSB = 365;;
let _X86_INS_PABSD = 366;;
let _X86_INS_PABSW = 367;;
let _X86_INS_PACKSSDW = 368;;
let _X86_INS_PACKSSWB = 369;;
let _X86_INS_PACKUSWB = 370;;
let _X86_INS_PADDB = 371;;
let _X86_INS_PADDD = 372;;
let _X86_INS_PADDQ = 373;;
let _X86_INS_PADDSB = 374;;
let _X86_INS_PADDSW = 375;;
let _X86_INS_PADDUSB = 376;;
let _X86_INS_PADDUSW = 377;;
let _X86_INS_PADDW = 378;;
let _X86_INS_PALIGNR = 379;;
let _X86_INS_PANDN = 380;;
let _X86_INS_PAND = 381;;
let _X86_INS_PAVGB = 382;;
let _X86_INS_PAVGW = 383;;
let _X86_INS_PCMPEQB = 384;;
let _X86_INS_PCMPEQD = 385;;
let _X86_INS_PCMPEQW = 386;;
let _X86_INS_PCMPGTB = 387;;
let _X86_INS_PCMPGTD = 388;;
let _X86_INS_PCMPGTW = 389;;
let _X86_INS_PEXTRW = 390;;
let _X86_INS_PHADDSW = 391;;
let _X86_INS_PHADDW = 392;;
let _X86_INS_PHADDD = 393;;
let _X86_INS_PHSUBD = 394;;
let _X86_INS_PHSUBSW = 395;;
let _X86_INS_PHSUBW = 396;;
let _X86_INS_PINSRW = 397;;
let _X86_INS_PMADDUBSW = 398;;
let _X86_INS_PMADDWD = 399;;
let _X86_INS_PMAXSW = 400;;
let _X86_INS_PMAXUB = 401;;
let _X86_INS_PMINSW = 402;;
let _X86_INS_PMINUB = 403;;
let _X86_INS_PMOVMSKB = 404;;
let _X86_INS_PMULHRSW = 405;;
let _X86_INS_PMULHUW = 406;;
let _X86_INS_PMULHW = 407;;
let _X86_INS_PMULLW = 408;;
let _X86_INS_PMULUDQ = 409;;
let _X86_INS_POR = 410;;
let _X86_INS_PSADBW = 411;;
let _X86_INS_PSHUFB = 412;;
let _X86_INS_PSHUFW = 413;;
let _X86_INS_PSIGNB = 414;;
let _X86_INS_PSIGND = 415;;
let _X86_INS_PSIGNW = 416;;
let _X86_INS_PSLLD = 417;;
let _X86_INS_PSLLQ = 418;;
let _X86_INS_PSLLW = 419;;
let _X86_INS_PSRAD = 420;;
let _X86_INS_PSRAW = 421;;
let _X86_INS_PSRLD = 422;;
let _X86_INS_PSRLQ = 423;;
let _X86_INS_PSRLW = 424;;
let _X86_INS_PSUBB = 425;;
let _X86_INS_PSUBD = 426;;
let _X86_INS_PSUBQ = 427;;
let _X86_INS_PSUBSB = 428;;
let _X86_INS_PSUBSW = 429;;
let _X86_INS_PSUBUSB = 430;;
let _X86_INS_PSUBUSW = 431;;
let _X86_INS_PSUBW = 432;;
let _X86_INS_PUNPCKHBW = 433;;
let _X86_INS_PUNPCKHDQ = 434;;
let _X86_INS_PUNPCKHWD = 435;;
let _X86_INS_PUNPCKLBW = 436;;
let _X86_INS_PUNPCKLDQ = 437;;
let _X86_INS_PUNPCKLWD = 438;;
let _X86_INS_PXOR = 439;;
let _X86_INS_MONITOR = 440;;
let _X86_INS_MONTMUL = 441;;
let _X86_INS_MOV = 442;;
let _X86_INS_MOVABS = 443;;
let _X86_INS_MOVBE = 444;;
let _X86_INS_MOVDDUP = 445;;
let _X86_INS_MOVDQA = 446;;
let _X86_INS_MOVDQU = 447;;
let _X86_INS_MOVHLPS = 448;;
let _X86_INS_MOVHPD = 449;;
let _X86_INS_MOVHPS = 450;;
let _X86_INS_MOVLHPS = 451;;
let _X86_INS_MOVLPD = 452;;
let _X86_INS_MOVLPS = 453;;
let _X86_INS_MOVMSKPD = 454;;
let _X86_INS_MOVMSKPS = 455;;
let _X86_INS_MOVNTDQA = 456;;
let _X86_INS_MOVNTDQ = 457;;
let _X86_INS_MOVNTI = 458;;
let _X86_INS_MOVNTPD = 459;;
let _X86_INS_MOVNTPS = 460;;
let _X86_INS_MOVNTSD = 461;;
let _X86_INS_MOVNTSS = 462;;
let _X86_INS_MOVSB = 463;;
let _X86_INS_MOVSD = 464;;
let _X86_INS_MOVSHDUP = 465;;
let _X86_INS_MOVSLDUP = 466;;
let _X86_INS_MOVSQ = 467;;
let _X86_INS_MOVSS = 468;;
let _X86_INS_MOVSW = 469;;
let _X86_INS_MOVSX = 470;;
let _X86_INS_MOVSXD = 471;;
let _X86_INS_MOVUPD = 472;;
let _X86_INS_MOVUPS = 473;;
let _X86_INS_MOVZX = 474;;
let _X86_INS_MPSADBW = 475;;
let _X86_INS_MUL = 476;;
let _X86_INS_MULPD = 477;;
let _X86_INS_MULPS = 478;;
let _X86_INS_MULSD = 479;;
let _X86_INS_MULSS = 480;;
let _X86_INS_MULX = 481;;
let _X86_INS_FMUL = 482;;
let _X86_INS_FIMUL = 483;;
let _X86_INS_FMULP = 484;;
let _X86_INS_MWAIT = 485;;
let _X86_INS_NEG = 486;;
let _X86_INS_NOP = 487;;
let _X86_INS_NOT = 488;;
let _X86_INS_OUT = 489;;
let _X86_INS_OUTSB = 490;;
let _X86_INS_OUTSD = 491;;
let _X86_INS_OUTSW = 492;;
let _X86_INS_PACKUSDW = 493;;
let _X86_INS_PAUSE = 494;;
let _X86_INS_PAVGUSB = 495;;
let _X86_INS_PBLENDVB = 496;;
let _X86_INS_PBLENDW = 497;;
let _X86_INS_PCLMULQDQ = 498;;
let _X86_INS_PCMPEQQ = 499;;
let _X86_INS_PCMPESTRI = 500;;
let _X86_INS_PCMPESTRM = 501;;
let _X86_INS_PCMPGTQ = 502;;
let _X86_INS_PCMPISTRI = 503;;
let _X86_INS_PCMPISTRM = 504;;
let _X86_INS_PDEP = 505;;
let _X86_INS_PEXT = 506;;
let _X86_INS_PEXTRB = 507;;
let _X86_INS_PEXTRD = 508;;
let _X86_INS_PEXTRQ = 509;;
let _X86_INS_PF2ID = 510;;
let _X86_INS_PF2IW = 511;;
let _X86_INS_PFACC = 512;;
let _X86_INS_PFADD = 513;;
let _X86_INS_PFCMPEQ = 514;;
let _X86_INS_PFCMPGE = 515;;
let _X86_INS_PFCMPGT = 516;;
let _X86_INS_PFMAX = 517;;
let _X86_INS_PFMIN = 518;;
let _X86_INS_PFMUL = 519;;
let _X86_INS_PFNACC = 520;;
let _X86_INS_PFPNACC = 521;;
let _X86_INS_PFRCPIT1 = 522;;
let _X86_INS_PFRCPIT2 = 523;;
let _X86_INS_PFRCP = 524;;
let _X86_INS_PFRSQIT1 = 525;;
let _X86_INS_PFRSQRT = 526;;
let _X86_INS_PFSUBR = 527;;
let _X86_INS_PFSUB = 528;;
let _X86_INS_PHMINPOSUW = 529;;
let _X86_INS_PI2FD = 530;;
let _X86_INS_PI2FW = 531;;
let _X86_INS_PINSRB = 532;;
let _X86_INS_PINSRD = 533;;
let _X86_INS_PINSRQ = 534;;
let _X86_INS_PMAXSB = 535;;
let _X86_INS_PMAXSD = 536;;
let _X86_INS_PMAXUD = 537;;
let _X86_INS_PMAXUW = 538;;
let _X86_INS_PMINSB = 539;;
let _X86_INS_PMINSD = 540;;
let _X86_INS_PMINUD = 541;;
let _X86_INS_PMINUW = 542;;
let _X86_INS_PMOVSXBD = 543;;
let _X86_INS_PMOVSXBQ = 544;;
let _X86_INS_PMOVSXBW = 545;;
let _X86_INS_PMOVSXDQ = 546;;
let _X86_INS_PMOVSXWD = 547;;
let _X86_INS_PMOVSXWQ = 548;;
let _X86_INS_PMOVZXBD = 549;;
let _X86_INS_PMOVZXBQ = 550;;
let _X86_INS_PMOVZXBW = 551;;
let _X86_INS_PMOVZXDQ = 552;;
let _X86_INS_PMOVZXWD = 553;;
let _X86_INS_PMOVZXWQ = 554;;
let _X86_INS_PMULDQ = 555;;
let _X86_INS_PMULHRW = 556;;
let _X86_INS_PMULLD = 557;;
let _X86_INS_POP = 558;;
let _X86_INS_POPAW = 559;;
let _X86_INS_POPAL = 560;;
let _X86_INS_POPCNT = 561;;
let _X86_INS_POPF = 562;;
let _X86_INS_POPFD = 563;;
let _X86_INS_POPFQ = 564;;
let _X86_INS_PREFETCH = 565;;
let _X86_INS_PREFETCHNTA = 566;;
let _X86_INS_PREFETCHT0 = 567;;
let _X86_INS_PREFETCHT1 = 568;;
let _X86_INS_PREFETCHT2 = 569;;
let _X86_INS_PREFETCHW = 570;;
let _X86_INS_PSHUFD = 571;;
let _X86_INS_PSHUFHW = 572;;
let _X86_INS_PSHUFLW = 573;;
let _X86_INS_PSLLDQ = 574;;
let _X86_INS_PSRLDQ = 575;;
let _X86_INS_PSWAPD = 576;;
let _X86_INS_PTEST = 577;;
let _X86_INS_PUNPCKHQDQ = 578;;
let _X86_INS_PUNPCKLQDQ = 579;;
let _X86_INS_PUSH = 580;;
let _X86_INS_PUSHAW = 581;;
let _X86_INS_PUSHAL = 582;;
let _X86_INS_PUSHF = 583;;
let _X86_INS_PUSHFD = 584;;
let _X86_INS_PUSHFQ = 585;;
let _X86_INS_RCL = 586;;
let _X86_INS_RCPPS = 587;;
let _X86_INS_RCPSS = 588;;
let _X86_INS_RCR = 589;;
let _X86_INS_RDFSBASE = 590;;
let _X86_INS_RDGSBASE = 591;;
let _X86_INS_RDMSR = 592;;
let _X86_INS_RDPMC = 593;;
let _X86_INS_RDRAND = 594;;
let _X86_INS_RDSEED = 595;;
let _X86_INS_RDTSC = 596;;
let _X86_INS_RDTSCP = 597;;
let _X86_INS_ROL = 598;;
let _X86_INS_ROR = 599;;
let _X86_INS_RORX = 600;;
let _X86_INS_ROUNDPD = 601;;
let _X86_INS_ROUNDPS = 602;;
let _X86_INS_ROUNDSD = 603;;
let _X86_INS_ROUNDSS = 604;;
let _X86_INS_RSM = 605;;
let _X86_INS_RSQRTPS = 606;;
let _X86_INS_RSQRTSS = 607;;
let _X86_INS_SAHF = 608;;
let _X86_INS_SAL = 609;;
let _X86_INS_SALC = 610;;
let _X86_INS_SAR = 611;;
let _X86_INS_SARX = 612;;
let _X86_INS_SBB = 613;;
let _X86_INS_SCASB = 614;;
let _X86_INS_SCASD = 615;;
let _X86_INS_SCASQ = 616;;
let _X86_INS_SCASW = 617;;
let _X86_INS_SETAE = 618;;
let _X86_INS_SETA = 619;;
let _X86_INS_SETBE = 620;;
let _X86_INS_SETB = 621;;
let _X86_INS_SETE = 622;;
let _X86_INS_SETGE = 623;;
let _X86_INS_SETG = 624;;
let _X86_INS_SETLE = 625;;
let _X86_INS_SETL = 626;;
let _X86_INS_SETNE = 627;;
let _X86_INS_SETNO = 628;;
let _X86_INS_SETNP = 629;;
let _X86_INS_SETNS = 630;;
let _X86_INS_SETO = 631;;
let _X86_INS_SETP = 632;;
let _X86_INS_SETS = 633;;
let _X86_INS_SFENCE = 634;;
let _X86_INS_SGDT = 635;;
let _X86_INS_SHA1MSG1 = 636;;
let _X86_INS_SHA1MSG2 = 637;;
let _X86_INS_SHA1NEXTE = 638;;
let _X86_INS_SHA1RNDS4 = 639;;
let _X86_INS_SHA256MSG1 = 640;;
let _X86_INS_SHA256MSG2 = 641;;
let _X86_INS_SHA256RNDS2 = 642;;
let _X86_INS_SHL = 643;;
let _X86_INS_SHLD = 644;;
let _X86_INS_SHLX = 645;;
let _X86_INS_SHR = 646;;
let _X86_INS_SHRD = 647;;
let _X86_INS_SHRX = 648;;
let _X86_INS_SHUFPD = 649;;
let _X86_INS_SHUFPS = 650;;
let _X86_INS_SIDT = 651;;
let _X86_INS_FSIN = 652;;
let _X86_INS_SKINIT = 653;;
let _X86_INS_SLDT = 654;;
let _X86_INS_SMSW = 655;;
let _X86_INS_SQRTPD = 656;;
let _X86_INS_SQRTPS = 657;;
let _X86_INS_SQRTSD = 658;;
let _X86_INS_SQRTSS = 659;;
let _X86_INS_FSQRT = 660;;
let _X86_INS_STAC = 661;;
let _X86_INS_STC = 662;;
let _X86_INS_STD = 663;;
let _X86_INS_STGI = 664;;
let _X86_INS_STI = 665;;
let _X86_INS_STMXCSR = 666;;
let _X86_INS_STOSB = 667;;
let _X86_INS_STOSD = 668;;
let _X86_INS_STOSQ = 669;;
let _X86_INS_STOSW = 670;;
let _X86_INS_STR = 671;;
let _X86_INS_FST = 672;;
let _X86_INS_FSTP = 673;;
let _X86_INS_FSTPNCE = 674;;
let _X86_INS_SUBPD = 675;;
let _X86_INS_SUBPS = 676;;
let _X86_INS_FSUBR = 677;;
let _X86_INS_FISUBR = 678;;
let _X86_INS_FSUBRP = 679;;
let _X86_INS_SUBSD = 680;;
let _X86_INS_SUBSS = 681;;
let _X86_INS_FSUB = 682;;
let _X86_INS_FISUB = 683;;
let _X86_INS_FSUBP = 684;;
let _X86_INS_SWAPGS = 685;;
let _X86_INS_SYSCALL = 686;;
let _X86_INS_SYSENTER = 687;;
let _X86_INS_SYSEXIT = 688;;
let _X86_INS_SYSRET = 689;;
let _X86_INS_T1MSKC = 690;;
let _X86_INS_TEST = 691;;
let _X86_INS_UD2 = 692;;
let _X86_INS_FTST = 693;;
let _X86_INS_TZCNT = 694;;
let _X86_INS_TZMSK = 695;;
let _X86_INS_FUCOMPI = 696;;
let _X86_INS_FUCOMI = 697;;
let _X86_INS_FUCOMPP = 698;;
let _X86_INS_FUCOMP = 699;;
let _X86_INS_FUCOM = 700;;
let _X86_INS_UD2B = 701;;
let _X86_INS_UNPCKHPD = 702;;
let _X86_INS_UNPCKHPS = 703;;
let _X86_INS_UNPCKLPD = 704;;
let _X86_INS_UNPCKLPS = 705;;
let _X86_INS_VADDPD = 706;;
let _X86_INS_VADDPS = 707;;
let _X86_INS_VADDSD = 708;;
let _X86_INS_VADDSS = 709;;
let _X86_INS_VADDSUBPD = 710;;
let _X86_INS_VADDSUBPS = 711;;
let _X86_INS_VAESDECLAST = 712;;
let _X86_INS_VAESDEC = 713;;
let _X86_INS_VAESENCLAST = 714;;
let _X86_INS_VAESENC = 715;;
let _X86_INS_VAESIMC = 716;;
let _X86_INS_VAESKEYGENASSIST = 717;;
let _X86_INS_VALIGND = 718;;
let _X86_INS_VALIGNQ = 719;;
let _X86_INS_VANDNPD = 720;;
let _X86_INS_VANDNPS = 721;;
let _X86_INS_VANDPD = 722;;
let _X86_INS_VANDPS = 723;;
let _X86_INS_VBLENDMPD = 724;;
let _X86_INS_VBLENDMPS = 725;;
let _X86_INS_VBLENDPD = 726;;
let _X86_INS_VBLENDPS = 727;;
let _X86_INS_VBLENDVPD = 728;;
let _X86_INS_VBLENDVPS = 729;;
let _X86_INS_VBROADCASTF128 = 730;;
let _X86_INS_VBROADCASTI128 = 731;;
let _X86_INS_VBROADCASTI32X4 = 732;;
let _X86_INS_VBROADCASTI64X4 = 733;;
let _X86_INS_VBROADCASTSD = 734;;
let _X86_INS_VBROADCASTSS = 735;;
let _X86_INS_VCMPPD = 736;;
let _X86_INS_VCMPPS = 737;;
let _X86_INS_VCMPSD = 738;;
let _X86_INS_VCMPSS = 739;;
let _X86_INS_VCVTDQ2PD = 740;;
let _X86_INS_VCVTDQ2PS = 741;;
let _X86_INS_VCVTPD2DQX = 742;;
let _X86_INS_VCVTPD2DQ = 743;;
let _X86_INS_VCVTPD2PSX = 744;;
let _X86_INS_VCVTPD2PS = 745;;
let _X86_INS_VCVTPD2UDQ = 746;;
let _X86_INS_VCVTPH2PS = 747;;
let _X86_INS_VCVTPS2DQ = 748;;
let _X86_INS_VCVTPS2PD = 749;;
let _X86_INS_VCVTPS2PH = 750;;
let _X86_INS_VCVTPS2UDQ = 751;;
let _X86_INS_VCVTSD2SI = 752;;
let _X86_INS_VCVTSD2USI = 753;;
let _X86_INS_VCVTSS2SI = 754;;
let _X86_INS_VCVTSS2USI = 755;;
let _X86_INS_VCVTTPD2DQX = 756;;
let _X86_INS_VCVTTPD2DQ = 757;;
let _X86_INS_VCVTTPD2UDQ = 758;;
let _X86_INS_VCVTTPS2DQ = 759;;
let _X86_INS_VCVTTPS2UDQ = 760;;
let _X86_INS_VCVTUDQ2PD = 761;;
let _X86_INS_VCVTUDQ2PS = 762;;
let _X86_INS_VDIVPD = 763;;
let _X86_INS_VDIVPS = 764;;
let _X86_INS_VDIVSD = 765;;
let _X86_INS_VDIVSS = 766;;
let _X86_INS_VDPPD = 767;;
let _X86_INS_VDPPS = 768;;
let _X86_INS_VERR = 769;;
let _X86_INS_VERW = 770;;
let _X86_INS_VEXTRACTF128 = 771;;
let _X86_INS_VEXTRACTF32X4 = 772;;
let _X86_INS_VEXTRACTF64X4 = 773;;
let _X86_INS_VEXTRACTI128 = 774;;
let _X86_INS_VEXTRACTI32X4 = 775;;
let _X86_INS_VEXTRACTI64X4 = 776;;
let _X86_INS_VEXTRACTPS = 777;;
let _X86_INS_VFMADD132PD = 778;;
let _X86_INS_VFMADD132PS = 779;;
let _X86_INS_VFMADD213PD = 780;;
let _X86_INS_VFMADD213PS = 781;;
let _X86_INS_VFMADDPD = 782;;
let _X86_INS_VFMADD231PD = 783;;
let _X86_INS_VFMADDPS = 784;;
let _X86_INS_VFMADD231PS = 785;;
let _X86_INS_VFMADDSD = 786;;
let _X86_INS_VFMADD213SD = 787;;
let _X86_INS_VFMADD132SD = 788;;
let _X86_INS_VFMADD231SD = 789;;
let _X86_INS_VFMADDSS = 790;;
let _X86_INS_VFMADD213SS = 791;;
let _X86_INS_VFMADD132SS = 792;;
let _X86_INS_VFMADD231SS = 793;;
let _X86_INS_VFMADDSUB132PD = 794;;
let _X86_INS_VFMADDSUB132PS = 795;;
let _X86_INS_VFMADDSUB213PD = 796;;
let _X86_INS_VFMADDSUB213PS = 797;;
let _X86_INS_VFMADDSUBPD = 798;;
let _X86_INS_VFMADDSUB231PD = 799;;
let _X86_INS_VFMADDSUBPS = 800;;
let _X86_INS_VFMADDSUB231PS = 801;;
let _X86_INS_VFMSUB132PD = 802;;
let _X86_INS_VFMSUB132PS = 803;;
let _X86_INS_VFMSUB213PD = 804;;
let _X86_INS_VFMSUB213PS = 805;;
let _X86_INS_VFMSUBADD132PD = 806;;
let _X86_INS_VFMSUBADD132PS = 807;;
let _X86_INS_VFMSUBADD213PD = 808;;
let _X86_INS_VFMSUBADD213PS = 809;;
let _X86_INS_VFMSUBADDPD = 810;;
let _X86_INS_VFMSUBADD231PD = 811;;
let _X86_INS_VFMSUBADDPS = 812;;
let _X86_INS_VFMSUBADD231PS = 813;;
let _X86_INS_VFMSUBPD = 814;;
let _X86_INS_VFMSUB231PD = 815;;
let _X86_INS_VFMSUBPS = 816;;
let _X86_INS_VFMSUB231PS = 817;;
let _X86_INS_VFMSUBSD = 818;;
let _X86_INS_VFMSUB213SD = 819;;
let _X86_INS_VFMSUB132SD = 820;;
let _X86_INS_VFMSUB231SD = 821;;
let _X86_INS_VFMSUBSS = 822;;
let _X86_INS_VFMSUB213SS = 823;;
let _X86_INS_VFMSUB132SS = 824;;
let _X86_INS_VFMSUB231SS = 825;;
let _X86_INS_VFNMADD132PD = 826;;
let _X86_INS_VFNMADD132PS = 827;;
let _X86_INS_VFNMADD213PD = 828;;
let _X86_INS_VFNMADD213PS = 829;;
let _X86_INS_VFNMADDPD = 830;;
let _X86_INS_VFNMADD231PD = 831;;
let _X86_INS_VFNMADDPS = 832;;
let _X86_INS_VFNMADD231PS = 833;;
let _X86_INS_VFNMADDSD = 834;;
let _X86_INS_VFNMADD213SD = 835;;
let _X86_INS_VFNMADD132SD = 836;;
let _X86_INS_VFNMADD231SD = 837;;
let _X86_INS_VFNMADDSS = 838;;
let _X86_INS_VFNMADD213SS = 839;;
let _X86_INS_VFNMADD132SS = 840;;
let _X86_INS_VFNMADD231SS = 841;;
let _X86_INS_VFNMSUB132PD = 842;;
let _X86_INS_VFNMSUB132PS = 843;;
let _X86_INS_VFNMSUB213PD = 844;;
let _X86_INS_VFNMSUB213PS = 845;;
let _X86_INS_VFNMSUBPD = 846;;
let _X86_INS_VFNMSUB231PD = 847;;
let _X86_INS_VFNMSUBPS = 848;;
let _X86_INS_VFNMSUB231PS = 849;;
let _X86_INS_VFNMSUBSD = 850;;
let _X86_INS_VFNMSUB213SD = 851;;
let _X86_INS_VFNMSUB132SD = 852;;
let _X86_INS_VFNMSUB231SD = 853;;
let _X86_INS_VFNMSUBSS = 854;;
let _X86_INS_VFNMSUB213SS = 855;;
let _X86_INS_VFNMSUB132SS = 856;;
let _X86_INS_VFNMSUB231SS = 857;;
let _X86_INS_VFRCZPD = 858;;
let _X86_INS_VFRCZPS = 859;;
let _X86_INS_VFRCZSD = 860;;
let _X86_INS_VFRCZSS = 861;;
let _X86_INS_VORPD = 862;;
let _X86_INS_VORPS = 863;;
let _X86_INS_VXORPD = 864;;
let _X86_INS_VXORPS = 865;;
let _X86_INS_VGATHERDPD = 866;;
let _X86_INS_VGATHERDPS = 867;;
let _X86_INS_VGATHERPF0DPD = 868;;
let _X86_INS_VGATHERPF0DPS = 869;;
let _X86_INS_VGATHERPF0QPD = 870;;
let _X86_INS_VGATHERPF0QPS = 871;;
let _X86_INS_VGATHERPF1DPD = 872;;
let _X86_INS_VGATHERPF1DPS = 873;;
let _X86_INS_VGATHERPF1QPD = 874;;
let _X86_INS_VGATHERPF1QPS = 875;;
let _X86_INS_VGATHERQPD = 876;;
let _X86_INS_VGATHERQPS = 877;;
let _X86_INS_VHADDPD = 878;;
let _X86_INS_VHADDPS = 879;;
let _X86_INS_VHSUBPD = 880;;
let _X86_INS_VHSUBPS = 881;;
let _X86_INS_VINSERTF128 = 882;;
let _X86_INS_VINSERTF32X4 = 883;;
let _X86_INS_VINSERTF64X4 = 884;;
let _X86_INS_VINSERTI128 = 885;;
let _X86_INS_VINSERTI32X4 = 886;;
let _X86_INS_VINSERTI64X4 = 887;;
let _X86_INS_VINSERTPS = 888;;
let _X86_INS_VLDDQU = 889;;
let _X86_INS_VLDMXCSR = 890;;
let _X86_INS_VMASKMOVDQU = 891;;
let _X86_INS_VMASKMOVPD = 892;;
let _X86_INS_VMASKMOVPS = 893;;
let _X86_INS_VMAXPD = 894;;
let _X86_INS_VMAXPS = 895;;
let _X86_INS_VMAXSD = 896;;
let _X86_INS_VMAXSS = 897;;
let _X86_INS_VMCALL = 898;;
let _X86_INS_VMCLEAR = 899;;
let _X86_INS_VMFUNC = 900;;
let _X86_INS_VMINPD = 901;;
let _X86_INS_VMINPS = 902;;
let _X86_INS_VMINSD = 903;;
let _X86_INS_VMINSS = 904;;
let _X86_INS_VMLAUNCH = 905;;
let _X86_INS_VMLOAD = 906;;
let _X86_INS_VMMCALL = 907;;
let _X86_INS_VMOVQ = 908;;
let _X86_INS_VMOVDDUP = 909;;
let _X86_INS_VMOVD = 910;;
let _X86_INS_VMOVDQA32 = 911;;
let _X86_INS_VMOVDQA64 = 912;;
let _X86_INS_VMOVDQA = 913;;
let _X86_INS_VMOVDQU16 = 914;;
let _X86_INS_VMOVDQU32 = 915;;
let _X86_INS_VMOVDQU64 = 916;;
let _X86_INS_VMOVDQU8 = 917;;
let _X86_INS_VMOVDQU = 918;;
let _X86_INS_VMOVHLPS = 919;;
let _X86_INS_VMOVHPD = 920;;
let _X86_INS_VMOVHPS = 921;;
let _X86_INS_VMOVLHPS = 922;;
let _X86_INS_VMOVLPD = 923;;
let _X86_INS_VMOVLPS = 924;;
let _X86_INS_VMOVMSKPD = 925;;
let _X86_INS_VMOVMSKPS = 926;;
let _X86_INS_VMOVNTDQA = 927;;
let _X86_INS_VMOVNTDQ = 928;;
let _X86_INS_VMOVNTPD = 929;;
let _X86_INS_VMOVNTPS = 930;;
let _X86_INS_VMOVSD = 931;;
let _X86_INS_VMOVSHDUP = 932;;
let _X86_INS_VMOVSLDUP = 933;;
let _X86_INS_VMOVSS = 934;;
let _X86_INS_VMOVUPD = 935;;
let _X86_INS_VMOVUPS = 936;;
let _X86_INS_VMPSADBW = 937;;
let _X86_INS_VMPTRLD = 938;;
let _X86_INS_VMPTRST = 939;;
let _X86_INS_VMREAD = 940;;
let _X86_INS_VMRESUME = 941;;
let _X86_INS_VMRUN = 942;;
let _X86_INS_VMSAVE = 943;;
let _X86_INS_VMULPD = 944;;
let _X86_INS_VMULPS = 945;;
let _X86_INS_VMULSD = 946;;
let _X86_INS_VMULSS = 947;;
let _X86_INS_VMWRITE = 948;;
let _X86_INS_VMXOFF = 949;;
let _X86_INS_VMXON = 950;;
let _X86_INS_VPABSB = 951;;
let _X86_INS_VPABSD = 952;;
let _X86_INS_VPABSQ = 953;;
let _X86_INS_VPABSW = 954;;
let _X86_INS_VPACKSSDW = 955;;
let _X86_INS_VPACKSSWB = 956;;
let _X86_INS_VPACKUSDW = 957;;
let _X86_INS_VPACKUSWB = 958;;
let _X86_INS_VPADDB = 959;;
let _X86_INS_VPADDD = 960;;
let _X86_INS_VPADDQ = 961;;
let _X86_INS_VPADDSB = 962;;
let _X86_INS_VPADDSW = 963;;
let _X86_INS_VPADDUSB = 964;;
let _X86_INS_VPADDUSW = 965;;
let _X86_INS_VPADDW = 966;;
let _X86_INS_VPALIGNR = 967;;
let _X86_INS_VPANDD = 968;;
let _X86_INS_VPANDND = 969;;
let _X86_INS_VPANDNQ = 970;;
let _X86_INS_VPANDN = 971;;
let _X86_INS_VPANDQ = 972;;
let _X86_INS_VPAND = 973;;
let _X86_INS_VPAVGB = 974;;
let _X86_INS_VPAVGW = 975;;
let _X86_INS_VPBLENDD = 976;;
let _X86_INS_VPBLENDMD = 977;;
let _X86_INS_VPBLENDMQ = 978;;
let _X86_INS_VPBLENDVB = 979;;
let _X86_INS_VPBLENDW = 980;;
let _X86_INS_VPBROADCASTB = 981;;
let _X86_INS_VPBROADCASTD = 982;;
let _X86_INS_VPBROADCASTMB2Q = 983;;
let _X86_INS_VPBROADCASTMW2D = 984;;
let _X86_INS_VPBROADCASTQ = 985;;
let _X86_INS_VPBROADCASTW = 986;;
let _X86_INS_VPCLMULQDQ = 987;;
let _X86_INS_VPCMOV = 988;;
let _X86_INS_VPCMP = 989;;
let _X86_INS_VPCMPD = 990;;
let _X86_INS_VPCMPEQB = 991;;
let _X86_INS_VPCMPEQD = 992;;
let _X86_INS_VPCMPEQQ = 993;;
let _X86_INS_VPCMPEQW = 994;;
let _X86_INS_VPCMPESTRI = 995;;
let _X86_INS_VPCMPESTRM = 996;;
let _X86_INS_VPCMPGTB = 997;;
let _X86_INS_VPCMPGTD = 998;;
let _X86_INS_VPCMPGTQ = 999;;
let _X86_INS_VPCMPGTW = 1000;;
let _X86_INS_VPCMPISTRI = 1001;;
let _X86_INS_VPCMPISTRM = 1002;;
let _X86_INS_VPCMPQ = 1003;;
let _X86_INS_VPCMPUD = 1004;;
let _X86_INS_VPCMPUQ = 1005;;
let _X86_INS_VPCOMB = 1006;;
let _X86_INS_VPCOMD = 1007;;
let _X86_INS_VPCOMQ = 1008;;
let _X86_INS_VPCOMUB = 1009;;
let _X86_INS_VPCOMUD = 1010;;
let _X86_INS_VPCOMUQ = 1011;;
let _X86_INS_VPCOMUW = 1012;;
let _X86_INS_VPCOMW = 1013;;
let _X86_INS_VPCONFLICTD = 1014;;
let _X86_INS_VPCONFLICTQ = 1015;;
let _X86_INS_VPERM2F128 = 1016;;
let _X86_INS_VPERM2I128 = 1017;;
let _X86_INS_VPERMD = 1018;;
let _X86_INS_VPERMI2D = 1019;;
let _X86_INS_VPERMI2PD = 1020;;
let _X86_INS_VPERMI2PS = 1021;;
let _X86_INS_VPERMI2Q = 1022;;
let _X86_INS_VPERMIL2PD = 1023;;
let _X86_INS_VPERMIL2PS = 1024;;
let _X86_INS_VPERMILPD = 1025;;
let _X86_INS_VPERMILPS = 1026;;
let _X86_INS_VPERMPD = 1027;;
let _X86_INS_VPERMPS = 1028;;
let _X86_INS_VPERMQ = 1029;;
let _X86_INS_VPERMT2D = 1030;;
let _X86_INS_VPERMT2PD = 1031;;
let _X86_INS_VPERMT2PS = 1032;;
let _X86_INS_VPERMT2Q = 1033;;
let _X86_INS_VPEXTRB = 1034;;
let _X86_INS_VPEXTRD = 1035;;
let _X86_INS_VPEXTRQ = 1036;;
let _X86_INS_VPEXTRW = 1037;;
let _X86_INS_VPGATHERDD = 1038;;
let _X86_INS_VPGATHERDQ = 1039;;
let _X86_INS_VPGATHERQD = 1040;;
let _X86_INS_VPGATHERQQ = 1041;;
let _X86_INS_VPHADDBD = 1042;;
let _X86_INS_VPHADDBQ = 1043;;
let _X86_INS_VPHADDBW = 1044;;
let _X86_INS_VPHADDDQ = 1045;;
let _X86_INS_VPHADDD = 1046;;
let _X86_INS_VPHADDSW = 1047;;
let _X86_INS_VPHADDUBD = 1048;;
let _X86_INS_VPHADDUBQ = 1049;;
let _X86_INS_VPHADDUBW = 1050;;
let _X86_INS_VPHADDUDQ = 1051;;
let _X86_INS_VPHADDUWD = 1052;;
let _X86_INS_VPHADDUWQ = 1053;;
let _X86_INS_VPHADDWD = 1054;;
let _X86_INS_VPHADDWQ = 1055;;
let _X86_INS_VPHADDW = 1056;;
let _X86_INS_VPHMINPOSUW = 1057;;
let _X86_INS_VPHSUBBW = 1058;;
let _X86_INS_VPHSUBDQ = 1059;;
let _X86_INS_VPHSUBD = 1060;;
let _X86_INS_VPHSUBSW = 1061;;
let _X86_INS_VPHSUBWD = 1062;;
let _X86_INS_VPHSUBW = 1063;;
let _X86_INS_VPINSRB = 1064;;
let _X86_INS_VPINSRD = 1065;;
let _X86_INS_VPINSRQ = 1066;;
let _X86_INS_VPINSRW = 1067;;
let _X86_INS_VPLZCNTD = 1068;;
let _X86_INS_VPLZCNTQ = 1069;;
let _X86_INS_VPMACSDD = 1070;;
let _X86_INS_VPMACSDQH = 1071;;
let _X86_INS_VPMACSDQL = 1072;;
let _X86_INS_VPMACSSDD = 1073;;
let _X86_INS_VPMACSSDQH = 1074;;
let _X86_INS_VPMACSSDQL = 1075;;
let _X86_INS_VPMACSSWD = 1076;;
let _X86_INS_VPMACSSWW = 1077;;
let _X86_INS_VPMACSWD = 1078;;
let _X86_INS_VPMACSWW = 1079;;
let _X86_INS_VPMADCSSWD = 1080;;
let _X86_INS_VPMADCSWD = 1081;;
let _X86_INS_VPMADDUBSW = 1082;;
let _X86_INS_VPMADDWD = 1083;;
let _X86_INS_VPMASKMOVD = 1084;;
let _X86_INS_VPMASKMOVQ = 1085;;
let _X86_INS_VPMAXSB = 1086;;
let _X86_INS_VPMAXSD = 1087;;
let _X86_INS_VPMAXSQ = 1088;;
let _X86_INS_VPMAXSW = 1089;;
let _X86_INS_VPMAXUB = 1090;;
let _X86_INS_VPMAXUD = 1091;;
let _X86_INS_VPMAXUQ = 1092;;
let _X86_INS_VPMAXUW = 1093;;
let _X86_INS_VPMINSB = 1094;;
let _X86_INS_VPMINSD = 1095;;
let _X86_INS_VPMINSQ = 1096;;
let _X86_INS_VPMINSW = 1097;;
let _X86_INS_VPMINUB = 1098;;
let _X86_INS_VPMINUD = 1099;;
let _X86_INS_VPMINUQ = 1100;;
let _X86_INS_VPMINUW = 1101;;
let _X86_INS_VPMOVDB = 1102;;
let _X86_INS_VPMOVDW = 1103;;
let _X86_INS_VPMOVMSKB = 1104;;
let _X86_INS_VPMOVQB = 1105;;
let _X86_INS_VPMOVQD = 1106;;
let _X86_INS_VPMOVQW = 1107;;
let _X86_INS_VPMOVSDB = 1108;;
let _X86_INS_VPMOVSDW = 1109;;
let _X86_INS_VPMOVSQB = 1110;;
let _X86_INS_VPMOVSQD = 1111;;
let _X86_INS_VPMOVSQW = 1112;;
let _X86_INS_VPMOVSXBD = 1113;;
let _X86_INS_VPMOVSXBQ = 1114;;
let _X86_INS_VPMOVSXBW = 1115;;
let _X86_INS_VPMOVSXDQ = 1116;;
let _X86_INS_VPMOVSXWD = 1117;;
let _X86_INS_VPMOVSXWQ = 1118;;
let _X86_INS_VPMOVUSDB = 1119;;
let _X86_INS_VPMOVUSDW = 1120;;
let _X86_INS_VPMOVUSQB = 1121;;
let _X86_INS_VPMOVUSQD = 1122;;
let _X86_INS_VPMOVUSQW = 1123;;
let _X86_INS_VPMOVZXBD = 1124;;
let _X86_INS_VPMOVZXBQ = 1125;;
let _X86_INS_VPMOVZXBW = 1126;;
let _X86_INS_VPMOVZXDQ = 1127;;
let _X86_INS_VPMOVZXWD = 1128;;
let _X86_INS_VPMOVZXWQ = 1129;;
let _X86_INS_VPMULDQ = 1130;;
let _X86_INS_VPMULHRSW = 1131;;
let _X86_INS_VPMULHUW = 1132;;
let _X86_INS_VPMULHW = 1133;;
let _X86_INS_VPMULLD = 1134;;
let _X86_INS_VPMULLW = 1135;;
let _X86_INS_VPMULUDQ = 1136;;
let _X86_INS_VPORD = 1137;;
let _X86_INS_VPORQ = 1138;;
let _X86_INS_VPOR = 1139;;
let _X86_INS_VPPERM = 1140;;
let _X86_INS_VPROTB = 1141;;
let _X86_INS_VPROTD = 1142;;
let _X86_INS_VPROTQ = 1143;;
let _X86_INS_VPROTW = 1144;;
let _X86_INS_VPSADBW = 1145;;
let _X86_INS_VPSCATTERDD = 1146;;
let _X86_INS_VPSCATTERDQ = 1147;;
let _X86_INS_VPSCATTERQD = 1148;;
let _X86_INS_VPSCATTERQQ = 1149;;
let _X86_INS_VPSHAB = 1150;;
let _X86_INS_VPSHAD = 1151;;
let _X86_INS_VPSHAQ = 1152;;
let _X86_INS_VPSHAW = 1153;;
let _X86_INS_VPSHLB = 1154;;
let _X86_INS_VPSHLD = 1155;;
let _X86_INS_VPSHLQ = 1156;;
let _X86_INS_VPSHLW = 1157;;
let _X86_INS_VPSHUFB = 1158;;
let _X86_INS_VPSHUFD = 1159;;
let _X86_INS_VPSHUFHW = 1160;;
let _X86_INS_VPSHUFLW = 1161;;
let _X86_INS_VPSIGNB = 1162;;
let _X86_INS_VPSIGND = 1163;;
let _X86_INS_VPSIGNW = 1164;;
let _X86_INS_VPSLLDQ = 1165;;
let _X86_INS_VPSLLD = 1166;;
let _X86_INS_VPSLLQ = 1167;;
let _X86_INS_VPSLLVD = 1168;;
let _X86_INS_VPSLLVQ = 1169;;
let _X86_INS_VPSLLW = 1170;;
let _X86_INS_VPSRAD = 1171;;
let _X86_INS_VPSRAQ = 1172;;
let _X86_INS_VPSRAVD = 1173;;
let _X86_INS_VPSRAVQ = 1174;;
let _X86_INS_VPSRAW = 1175;;
let _X86_INS_VPSRLDQ = 1176;;
let _X86_INS_VPSRLD = 1177;;
let _X86_INS_VPSRLQ = 1178;;
let _X86_INS_VPSRLVD = 1179;;
let _X86_INS_VPSRLVQ = 1180;;
let _X86_INS_VPSRLW = 1181;;
let _X86_INS_VPSUBB = 1182;;
let _X86_INS_VPSUBD = 1183;;
let _X86_INS_VPSUBQ = 1184;;
let _X86_INS_VPSUBSB = 1185;;
let _X86_INS_VPSUBSW = 1186;;
let _X86_INS_VPSUBUSB = 1187;;
let _X86_INS_VPSUBUSW = 1188;;
let _X86_INS_VPSUBW = 1189;;
let _X86_INS_VPTESTMD = 1190;;
let _X86_INS_VPTESTMQ = 1191;;
let _X86_INS_VPTESTNMD = 1192;;
let _X86_INS_VPTESTNMQ = 1193;;
let _X86_INS_VPTEST = 1194;;
let _X86_INS_VPUNPCKHBW = 1195;;
let _X86_INS_VPUNPCKHDQ = 1196;;
let _X86_INS_VPUNPCKHQDQ = 1197;;
let _X86_INS_VPUNPCKHWD = 1198;;
let _X86_INS_VPUNPCKLBW = 1199;;
let _X86_INS_VPUNPCKLDQ = 1200;;
let _X86_INS_VPUNPCKLQDQ = 1201;;
let _X86_INS_VPUNPCKLWD = 1202;;
let _X86_INS_VPXORD = 1203;;
let _X86_INS_VPXORQ = 1204;;
let _X86_INS_VPXOR = 1205;;
let _X86_INS_VRCP14PD = 1206;;
let _X86_INS_VRCP14PS = 1207;;
let _X86_INS_VRCP14SD = 1208;;
let _X86_INS_VRCP14SS = 1209;;
let _X86_INS_VRCP28PD = 1210;;
let _X86_INS_VRCP28PS = 1211;;
let _X86_INS_VRCP28SD = 1212;;
let _X86_INS_VRCP28SS = 1213;;
let _X86_INS_VRCPPS = 1214;;
let _X86_INS_VRCPSS = 1215;;
let _X86_INS_VRNDSCALEPD = 1216;;
let _X86_INS_VRNDSCALEPS = 1217;;
let _X86_INS_VRNDSCALESD = 1218;;
let _X86_INS_VRNDSCALESS = 1219;;
let _X86_INS_VROUNDPD = 1220;;
let _X86_INS_VROUNDPS = 1221;;
let _X86_INS_VROUNDSD = 1222;;
let _X86_INS_VROUNDSS = 1223;;
let _X86_INS_VRSQRT14PD = 1224;;
let _X86_INS_VRSQRT14PS = 1225;;
let _X86_INS_VRSQRT14SD = 1226;;
let _X86_INS_VRSQRT14SS = 1227;;
let _X86_INS_VRSQRT28PD = 1228;;
let _X86_INS_VRSQRT28PS = 1229;;
let _X86_INS_VRSQRT28SD = 1230;;
let _X86_INS_VRSQRT28SS = 1231;;
let _X86_INS_VRSQRTPS = 1232;;
let _X86_INS_VRSQRTSS = 1233;;
let _X86_INS_VSCATTERDPD = 1234;;
let _X86_INS_VSCATTERDPS = 1235;;
let _X86_INS_VSCATTERPF0DPD = 1236;;
let _X86_INS_VSCATTERPF0DPS = 1237;;
let _X86_INS_VSCATTERPF0QPD = 1238;;
let _X86_INS_VSCATTERPF0QPS = 1239;;
let _X86_INS_VSCATTERPF1DPD = 1240;;
let _X86_INS_VSCATTERPF1DPS = 1241;;
let _X86_INS_VSCATTERPF1QPD = 1242;;
let _X86_INS_VSCATTERPF1QPS = 1243;;
let _X86_INS_VSCATTERQPD = 1244;;
let _X86_INS_VSCATTERQPS = 1245;;
let _X86_INS_VSHUFPD = 1246;;
let _X86_INS_VSHUFPS = 1247;;
let _X86_INS_VSQRTPD = 1248;;
let _X86_INS_VSQRTPS = 1249;;
let _X86_INS_VSQRTSD = 1250;;
let _X86_INS_VSQRTSS = 1251;;
let _X86_INS_VSTMXCSR = 1252;;
let _X86_INS_VSUBPD = 1253;;
let _X86_INS_VSUBPS = 1254;;
let _X86_INS_VSUBSD = 1255;;
let _X86_INS_VSUBSS = 1256;;
let _X86_INS_VTESTPD = 1257;;
let _X86_INS_VTESTPS = 1258;;
let _X86_INS_VUNPCKHPD = 1259;;
let _X86_INS_VUNPCKHPS = 1260;;
let _X86_INS_VUNPCKLPD = 1261;;
let _X86_INS_VUNPCKLPS = 1262;;
let _X86_INS_VZEROALL = 1263;;
let _X86_INS_VZEROUPPER = 1264;;
let _X86_INS_WAIT = 1265;;
let _X86_INS_WBINVD = 1266;;
let _X86_INS_WRFSBASE = 1267;;
let _X86_INS_WRGSBASE = 1268;;
let _X86_INS_WRMSR = 1269;;
let _X86_INS_XABORT = 1270;;
let _X86_INS_XACQUIRE = 1271;;
let _X86_INS_XBEGIN = 1272;;
let _X86_INS_XCHG = 1273;;
let _X86_INS_FXCH = 1274;;
let _X86_INS_XCRYPTCBC = 1275;;
let _X86_INS_XCRYPTCFB = 1276;;
let _X86_INS_XCRYPTCTR = 1277;;
let _X86_INS_XCRYPTECB = 1278;;
let _X86_INS_XCRYPTOFB = 1279;;
let _X86_INS_XEND = 1280;;
let _X86_INS_XGETBV = 1281;;
let _X86_INS_XLATB = 1282;;
let _X86_INS_XRELEASE = 1283;;
let _X86_INS_XRSTOR = 1284;;
let _X86_INS_XRSTOR64 = 1285;;
let _X86_INS_XSAVE = 1286;;
let _X86_INS_XSAVE64 = 1287;;
let _X86_INS_XSAVEOPT = 1288;;
let _X86_INS_XSAVEOPT64 = 1289;;
let _X86_INS_XSETBV = 1290;;
let _X86_INS_XSHA1 = 1291;;
let _X86_INS_XSHA256 = 1292;;
let _X86_INS_XSTORE = 1293;;
let _X86_INS_XTEST = 1294;;
let _X86_INS_ENDING = 1295;;

(* Group of X86 instructions *)

let _X86_GRP_INVALID = 0;;

(* Generic groups *)
let _X86_GRP_JUMP = 1;;
let _X86_GRP_CALL = 2;;
let _X86_GRP_RET = 3;;
let _X86_GRP_INT = 4;;
let _X86_GRP_IRET = 5;;

(* Architecture-specific groups *)
let _X86_GRP_VM = 128;;
let _X86_GRP_3DNOW = 129;;
let _X86_GRP_AES = 130;;
let _X86_GRP_ADX = 131;;
let _X86_GRP_AVX = 132;;
let _X86_GRP_AVX2 = 133;;
let _X86_GRP_AVX512 = 134;;
let _X86_GRP_BMI = 135;;
let _X86_GRP_BMI2 = 136;;
let _X86_GRP_CMOV = 137;;
let _X86_GRP_F16C = 138;;
let _X86_GRP_FMA = 139;;
let _X86_GRP_FMA4 = 140;;
let _X86_GRP_FSGSBASE = 141;;
let _X86_GRP_HLE = 142;;
let _X86_GRP_MMX = 143;;
let _X86_GRP_MODE32 = 144;;
let _X86_GRP_MODE64 = 145;;
let _X86_GRP_RTM = 146;;
let _X86_GRP_SHA = 147;;
let _X86_GRP_SSE1 = 148;;
let _X86_GRP_SSE2 = 149;;
let _X86_GRP_SSE3 = 150;;
let _X86_GRP_SSE41 = 151;;
let _X86_GRP_SSE42 = 152;;
let _X86_GRP_SSE4A = 153;;
let _X86_GRP_SSSE3 = 154;;
let _X86_GRP_PCLMUL = 155;;
let _X86_GRP_XOP = 156;;
let _X86_GRP_CDI = 157;;
let _X86_GRP_ERI = 158;;
let _X86_GRP_TBM = 159;;
let _X86_GRP_16BITMODE = 160;;
let _X86_GRP_NOT64BITMODE = 161;;
let _X86_GRP_SGX = 162;;
let _X86_GRP_DQI = 163;;
let _X86_GRP_BWI = 164;;
let _X86_GRP_PFI = 165;;
let _X86_GRP_VLX = 166;;
let _X86_GRP_SMAP = 167;;
let _X86_GRP_NOVLX = 168;;
let _X86_GRP_ENDING = 169;;
