# CS_ARCH_MIPS, CS_MODE_MIPS64+CS_MODE_BIG_ENDIAN, None
0x40,0xac,0x80,0x02 = dmtc0 $12, $16, 2
0x40,0xac,0x80,0x00 = dmtc0 $12, $16, 0
0x40,0x8c,0x80,0x02 = mtc0 $12, $16, 2
0x40,0x8c,0x80,0x00 = mtc0 $12, $16, 0
0x40,0x2c,0x80,0x02 = dmfc0 $12, $16, 2
0x40,0x2c,0x80,0x00 = dmfc0 $12, $16, 0
0x40,0x0c,0x80,0x02 = mfc0 $12, $16, 2
0x40,0x0c,0x80,0x00 = mfc0 $12, $16, 0
0x48,0xac,0x80,0x02 = dmtc2 $12, $16, 2
0x48,0xac,0x80,0x00 = dmtc2 $12, $16, 0
0x48,0x8c,0x80,0x02 = mtc2 $12, $16, 2
0x48,0x8c,0x80,0x00 = mtc2 $12, $16, 0
0x48,0x2c,0x80,0x02 = dmfc2 $12, $16, 2
0x48,0x2c,0x80,0x00 = dmfc2 $12, $16, 0
0x48,0x0c,0x80,0x02 = mfc2 $12, $16, 2
0x48,0x0c,0x80,0x00 = mfc2 $12, $16, 0
