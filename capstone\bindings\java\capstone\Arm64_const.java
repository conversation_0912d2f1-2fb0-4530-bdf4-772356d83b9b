// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class Arm64_const {

	// ARM64 shift type

	public static final int ARM64_SFT_INVALID = 0;
	public static final int ARM64_SFT_LSL = 1;
	public static final int ARM64_SFT_MSL = 2;
	public static final int ARM64_SFT_LSR = 3;
	public static final int ARM64_SFT_ASR = 4;
	public static final int ARM64_SFT_ROR = 5;

	// ARM64 extender type

	public static final int ARM64_EXT_INVALID = 0;
	public static final int ARM64_EXT_UXTB = 1;
	public static final int ARM64_EXT_UXTH = 2;
	public static final int ARM64_EXT_UXTW = 3;
	public static final int ARM64_EXT_UXTX = 4;
	public static final int ARM64_EXT_SXTB = 5;
	public static final int ARM64_EXT_SXTH = 6;
	public static final int ARM64_EXT_SXTW = 7;
	public static final int ARM64_EXT_SXTX = 8;

	// ARM64 condition code

	public static final int ARM64_CC_INVALID = 0;
	public static final int ARM64_CC_EQ = 1;
	public static final int ARM64_CC_NE = 2;
	public static final int ARM64_CC_HS = 3;
	public static final int ARM64_CC_LO = 4;
	public static final int ARM64_CC_MI = 5;
	public static final int ARM64_CC_PL = 6;
	public static final int ARM64_CC_VS = 7;
	public static final int ARM64_CC_VC = 8;
	public static final int ARM64_CC_HI = 9;
	public static final int ARM64_CC_LS = 10;
	public static final int ARM64_CC_GE = 11;
	public static final int ARM64_CC_LT = 12;
	public static final int ARM64_CC_GT = 13;
	public static final int ARM64_CC_LE = 14;
	public static final int ARM64_CC_AL = 15;
	public static final int ARM64_CC_NV = 16;

	// System registers

	// System registers for MRS

	public static final int ARM64_SYSREG_INVALID = 0;
	public static final int ARM64_SYSREG_MDCCSR_EL0 = 0x9808;
	public static final int ARM64_SYSREG_DBGDTRRX_EL0 = 0x9828;
	public static final int ARM64_SYSREG_MDRAR_EL1 = 0x8080;
	public static final int ARM64_SYSREG_OSLSR_EL1 = 0x808c;
	public static final int ARM64_SYSREG_DBGAUTHSTATUS_EL1 = 0x83f6;
	public static final int ARM64_SYSREG_PMCEID0_EL0 = 0xdce6;
	public static final int ARM64_SYSREG_PMCEID1_EL0 = 0xdce7;
	public static final int ARM64_SYSREG_MIDR_EL1 = 0xc000;
	public static final int ARM64_SYSREG_CCSIDR_EL1 = 0xc800;
	public static final int ARM64_SYSREG_CLIDR_EL1 = 0xc801;
	public static final int ARM64_SYSREG_CTR_EL0 = 0xd801;
	public static final int ARM64_SYSREG_MPIDR_EL1 = 0xc005;
	public static final int ARM64_SYSREG_REVIDR_EL1 = 0xc006;
	public static final int ARM64_SYSREG_AIDR_EL1 = 0xc807;
	public static final int ARM64_SYSREG_DCZID_EL0 = 0xd807;
	public static final int ARM64_SYSREG_ID_PFR0_EL1 = 0xc008;
	public static final int ARM64_SYSREG_ID_PFR1_EL1 = 0xc009;
	public static final int ARM64_SYSREG_ID_DFR0_EL1 = 0xc00a;
	public static final int ARM64_SYSREG_ID_AFR0_EL1 = 0xc00b;
	public static final int ARM64_SYSREG_ID_MMFR0_EL1 = 0xc00c;
	public static final int ARM64_SYSREG_ID_MMFR1_EL1 = 0xc00d;
	public static final int ARM64_SYSREG_ID_MMFR2_EL1 = 0xc00e;
	public static final int ARM64_SYSREG_ID_MMFR3_EL1 = 0xc00f;
	public static final int ARM64_SYSREG_ID_ISAR0_EL1 = 0xc010;
	public static final int ARM64_SYSREG_ID_ISAR1_EL1 = 0xc011;
	public static final int ARM64_SYSREG_ID_ISAR2_EL1 = 0xc012;
	public static final int ARM64_SYSREG_ID_ISAR3_EL1 = 0xc013;
	public static final int ARM64_SYSREG_ID_ISAR4_EL1 = 0xc014;
	public static final int ARM64_SYSREG_ID_ISAR5_EL1 = 0xc015;
	public static final int ARM64_SYSREG_ID_A64PFR0_EL1 = 0xc020;
	public static final int ARM64_SYSREG_ID_A64PFR1_EL1 = 0xc021;
	public static final int ARM64_SYSREG_ID_A64DFR0_EL1 = 0xc028;
	public static final int ARM64_SYSREG_ID_A64DFR1_EL1 = 0xc029;
	public static final int ARM64_SYSREG_ID_A64AFR0_EL1 = 0xc02c;
	public static final int ARM64_SYSREG_ID_A64AFR1_EL1 = 0xc02d;
	public static final int ARM64_SYSREG_ID_A64ISAR0_EL1 = 0xc030;
	public static final int ARM64_SYSREG_ID_A64ISAR1_EL1 = 0xc031;
	public static final int ARM64_SYSREG_ID_A64MMFR0_EL1 = 0xc038;
	public static final int ARM64_SYSREG_ID_A64MMFR1_EL1 = 0xc039;
	public static final int ARM64_SYSREG_MVFR0_EL1 = 0xc018;
	public static final int ARM64_SYSREG_MVFR1_EL1 = 0xc019;
	public static final int ARM64_SYSREG_MVFR2_EL1 = 0xc01a;
	public static final int ARM64_SYSREG_RVBAR_EL1 = 0xc601;
	public static final int ARM64_SYSREG_RVBAR_EL2 = 0xe601;
	public static final int ARM64_SYSREG_RVBAR_EL3 = 0xf601;
	public static final int ARM64_SYSREG_ISR_EL1 = 0xc608;
	public static final int ARM64_SYSREG_CNTPCT_EL0 = 0xdf01;
	public static final int ARM64_SYSREG_CNTVCT_EL0 = 0xdf02;
	public static final int ARM64_SYSREG_TRCSTATR = 0x8818;
	public static final int ARM64_SYSREG_TRCIDR8 = 0x8806;
	public static final int ARM64_SYSREG_TRCIDR9 = 0x880e;
	public static final int ARM64_SYSREG_TRCIDR10 = 0x8816;
	public static final int ARM64_SYSREG_TRCIDR11 = 0x881e;
	public static final int ARM64_SYSREG_TRCIDR12 = 0x8826;
	public static final int ARM64_SYSREG_TRCIDR13 = 0x882e;
	public static final int ARM64_SYSREG_TRCIDR0 = 0x8847;
	public static final int ARM64_SYSREG_TRCIDR1 = 0x884f;
	public static final int ARM64_SYSREG_TRCIDR2 = 0x8857;
	public static final int ARM64_SYSREG_TRCIDR3 = 0x885f;
	public static final int ARM64_SYSREG_TRCIDR4 = 0x8867;
	public static final int ARM64_SYSREG_TRCIDR5 = 0x886f;
	public static final int ARM64_SYSREG_TRCIDR6 = 0x8877;
	public static final int ARM64_SYSREG_TRCIDR7 = 0x887f;
	public static final int ARM64_SYSREG_TRCOSLSR = 0x888c;
	public static final int ARM64_SYSREG_TRCPDSR = 0x88ac;
	public static final int ARM64_SYSREG_TRCDEVAFF0 = 0x8bd6;
	public static final int ARM64_SYSREG_TRCDEVAFF1 = 0x8bde;
	public static final int ARM64_SYSREG_TRCLSR = 0x8bee;
	public static final int ARM64_SYSREG_TRCAUTHSTATUS = 0x8bf6;
	public static final int ARM64_SYSREG_TRCDEVARCH = 0x8bfe;
	public static final int ARM64_SYSREG_TRCDEVID = 0x8b97;
	public static final int ARM64_SYSREG_TRCDEVTYPE = 0x8b9f;
	public static final int ARM64_SYSREG_TRCPIDR4 = 0x8ba7;
	public static final int ARM64_SYSREG_TRCPIDR5 = 0x8baf;
	public static final int ARM64_SYSREG_TRCPIDR6 = 0x8bb7;
	public static final int ARM64_SYSREG_TRCPIDR7 = 0x8bbf;
	public static final int ARM64_SYSREG_TRCPIDR0 = 0x8bc7;
	public static final int ARM64_SYSREG_TRCPIDR1 = 0x8bcf;
	public static final int ARM64_SYSREG_TRCPIDR2 = 0x8bd7;
	public static final int ARM64_SYSREG_TRCPIDR3 = 0x8bdf;
	public static final int ARM64_SYSREG_TRCCIDR0 = 0x8be7;
	public static final int ARM64_SYSREG_TRCCIDR1 = 0x8bef;
	public static final int ARM64_SYSREG_TRCCIDR2 = 0x8bf7;
	public static final int ARM64_SYSREG_TRCCIDR3 = 0x8bff;
	public static final int ARM64_SYSREG_ICC_IAR1_EL1 = 0xc660;
	public static final int ARM64_SYSREG_ICC_IAR0_EL1 = 0xc640;
	public static final int ARM64_SYSREG_ICC_HPPIR1_EL1 = 0xc662;
	public static final int ARM64_SYSREG_ICC_HPPIR0_EL1 = 0xc642;
	public static final int ARM64_SYSREG_ICC_RPR_EL1 = 0xc65b;
	public static final int ARM64_SYSREG_ICH_VTR_EL2 = 0xe659;
	public static final int ARM64_SYSREG_ICH_EISR_EL2 = 0xe65b;
	public static final int ARM64_SYSREG_ICH_ELSR_EL2 = 0xe65d;

	// System registers for MSR
	public static final int ARM64_SYSREG_DBGDTRTX_EL0 = 0x9828;
	public static final int ARM64_SYSREG_OSLAR_EL1 = 0x8084;
	public static final int ARM64_SYSREG_PMSWINC_EL0 = 0xdce4;
	public static final int ARM64_SYSREG_TRCOSLAR = 0x8884;
	public static final int ARM64_SYSREG_TRCLAR = 0x8be6;
	public static final int ARM64_SYSREG_ICC_EOIR1_EL1 = 0xc661;
	public static final int ARM64_SYSREG_ICC_EOIR0_EL1 = 0xc641;
	public static final int ARM64_SYSREG_ICC_DIR_EL1 = 0xc659;
	public static final int ARM64_SYSREG_ICC_SGI1R_EL1 = 0xc65d;
	public static final int ARM64_SYSREG_ICC_ASGI1R_EL1 = 0xc65e;
	public static final int ARM64_SYSREG_ICC_SGI0R_EL1 = 0xc65f;

	// System PState Field (MSR instruction)

	public static final int ARM64_PSTATE_INVALID = 0;
	public static final int ARM64_PSTATE_SPSEL = 0x05;
	public static final int ARM64_PSTATE_DAIFSET = 0x1e;
	public static final int ARM64_PSTATE_DAIFCLR = 0x1f;

	// Vector arrangement specifier (for FloatingPoint/Advanced SIMD insn)

	public static final int ARM64_VAS_INVALID = 0;
	public static final int ARM64_VAS_8B = 1;
	public static final int ARM64_VAS_16B = 2;
	public static final int ARM64_VAS_4H = 3;
	public static final int ARM64_VAS_8H = 4;
	public static final int ARM64_VAS_2S = 5;
	public static final int ARM64_VAS_4S = 6;
	public static final int ARM64_VAS_1D = 7;
	public static final int ARM64_VAS_2D = 8;
	public static final int ARM64_VAS_1Q = 9;

	// Vector element size specifier

	public static final int ARM64_VESS_INVALID = 0;
	public static final int ARM64_VESS_B = 1;
	public static final int ARM64_VESS_H = 2;
	public static final int ARM64_VESS_S = 3;
	public static final int ARM64_VESS_D = 4;

	// Memory barrier operands

	public static final int ARM64_BARRIER_INVALID = 0;
	public static final int ARM64_BARRIER_OSHLD = 0x1;
	public static final int ARM64_BARRIER_OSHST = 0x2;
	public static final int ARM64_BARRIER_OSH = 0x3;
	public static final int ARM64_BARRIER_NSHLD = 0x5;
	public static final int ARM64_BARRIER_NSHST = 0x6;
	public static final int ARM64_BARRIER_NSH = 0x7;
	public static final int ARM64_BARRIER_ISHLD = 0x9;
	public static final int ARM64_BARRIER_ISHST = 0xa;
	public static final int ARM64_BARRIER_ISH = 0xb;
	public static final int ARM64_BARRIER_LD = 0xd;
	public static final int ARM64_BARRIER_ST = 0xe;
	public static final int ARM64_BARRIER_SY = 0xf;

	// Operand type for instruction's operands

	public static final int ARM64_OP_INVALID = 0;
	public static final int ARM64_OP_REG = 1;
	public static final int ARM64_OP_IMM = 2;
	public static final int ARM64_OP_MEM = 3;
	public static final int ARM64_OP_FP = 4;
	public static final int ARM64_OP_CIMM = 64;
	public static final int ARM64_OP_REG_MRS = 65;
	public static final int ARM64_OP_REG_MSR = 66;
	public static final int ARM64_OP_PSTATE = 67;
	public static final int ARM64_OP_SYS = 68;
	public static final int ARM64_OP_PREFETCH = 69;
	public static final int ARM64_OP_BARRIER = 70;

	// TLBI operations

	public static final int ARM64_TLBI_INVALID = 0;
	public static final int ARM64_TLBI_VMALLE1IS = 1;
	public static final int ARM64_TLBI_VAE1IS = 2;
	public static final int ARM64_TLBI_ASIDE1IS = 3;
	public static final int ARM64_TLBI_VAAE1IS = 4;
	public static final int ARM64_TLBI_VALE1IS = 5;
	public static final int ARM64_TLBI_VAALE1IS = 6;
	public static final int ARM64_TLBI_ALLE2IS = 7;
	public static final int ARM64_TLBI_VAE2IS = 8;
	public static final int ARM64_TLBI_ALLE1IS = 9;
	public static final int ARM64_TLBI_VALE2IS = 10;
	public static final int ARM64_TLBI_VMALLS12E1IS = 11;
	public static final int ARM64_TLBI_ALLE3IS = 12;
	public static final int ARM64_TLBI_VAE3IS = 13;
	public static final int ARM64_TLBI_VALE3IS = 14;
	public static final int ARM64_TLBI_IPAS2E1IS = 15;
	public static final int ARM64_TLBI_IPAS2LE1IS = 16;
	public static final int ARM64_TLBI_IPAS2E1 = 17;
	public static final int ARM64_TLBI_IPAS2LE1 = 18;
	public static final int ARM64_TLBI_VMALLE1 = 19;
	public static final int ARM64_TLBI_VAE1 = 20;
	public static final int ARM64_TLBI_ASIDE1 = 21;
	public static final int ARM64_TLBI_VAAE1 = 22;
	public static final int ARM64_TLBI_VALE1 = 23;
	public static final int ARM64_TLBI_VAALE1 = 24;
	public static final int ARM64_TLBI_ALLE2 = 25;
	public static final int ARM64_TLBI_VAE2 = 26;
	public static final int ARM64_TLBI_ALLE1 = 27;
	public static final int ARM64_TLBI_VALE2 = 28;
	public static final int ARM64_TLBI_VMALLS12E1 = 29;
	public static final int ARM64_TLBI_ALLE3 = 30;
	public static final int ARM64_TLBI_VAE3 = 31;
	public static final int ARM64_TLBI_VALE3 = 32;

	// AT operations
	public static final int ARM64_AT_S1E1R = 33;
	public static final int ARM64_AT_S1E1W = 34;
	public static final int ARM64_AT_S1E0R = 35;
	public static final int ARM64_AT_S1E0W = 36;
	public static final int ARM64_AT_S1E2R = 37;
	public static final int ARM64_AT_S1E2W = 38;
	public static final int ARM64_AT_S12E1R = 39;
	public static final int ARM64_AT_S12E1W = 40;
	public static final int ARM64_AT_S12E0R = 41;
	public static final int ARM64_AT_S12E0W = 42;
	public static final int ARM64_AT_S1E3R = 43;
	public static final int ARM64_AT_S1E3W = 44;

	// DC operations

	public static final int ARM64_DC_INVALID = 0;
	public static final int ARM64_DC_ZVA = 1;
	public static final int ARM64_DC_IVAC = 2;
	public static final int ARM64_DC_ISW = 3;
	public static final int ARM64_DC_CVAC = 4;
	public static final int ARM64_DC_CSW = 5;
	public static final int ARM64_DC_CVAU = 6;
	public static final int ARM64_DC_CIVAC = 7;
	public static final int ARM64_DC_CISW = 8;

	// IC operations

	public static final int ARM64_IC_INVALID = 0;
	public static final int ARM64_IC_IALLUIS = 1;
	public static final int ARM64_IC_IALLU = 2;
	public static final int ARM64_IC_IVAU = 3;

	// Prefetch operations (PRFM)

	public static final int ARM64_PRFM_INVALID = 0;
	public static final int ARM64_PRFM_PLDL1KEEP = 0x00+1;
	public static final int ARM64_PRFM_PLDL1STRM = 0x01+1;
	public static final int ARM64_PRFM_PLDL2KEEP = 0x02+1;
	public static final int ARM64_PRFM_PLDL2STRM = 0x03+1;
	public static final int ARM64_PRFM_PLDL3KEEP = 0x04+1;
	public static final int ARM64_PRFM_PLDL3STRM = 0x05+1;
	public static final int ARM64_PRFM_PLIL1KEEP = 0x08+1;
	public static final int ARM64_PRFM_PLIL1STRM = 0x09+1;
	public static final int ARM64_PRFM_PLIL2KEEP = 0x0a+1;
	public static final int ARM64_PRFM_PLIL2STRM = 0x0b+1;
	public static final int ARM64_PRFM_PLIL3KEEP = 0x0c+1;
	public static final int ARM64_PRFM_PLIL3STRM = 0x0d+1;
	public static final int ARM64_PRFM_PSTL1KEEP = 0x10+1;
	public static final int ARM64_PRFM_PSTL1STRM = 0x11+1;
	public static final int ARM64_PRFM_PSTL2KEEP = 0x12+1;
	public static final int ARM64_PRFM_PSTL2STRM = 0x13+1;
	public static final int ARM64_PRFM_PSTL3KEEP = 0x14+1;
	public static final int ARM64_PRFM_PSTL3STRM = 0x15+1;

	// ARM64 registers

	public static final int ARM64_REG_INVALID = 0;
	public static final int ARM64_REG_X29 = 1;
	public static final int ARM64_REG_X30 = 2;
	public static final int ARM64_REG_NZCV = 3;
	public static final int ARM64_REG_SP = 4;
	public static final int ARM64_REG_WSP = 5;
	public static final int ARM64_REG_WZR = 6;
	public static final int ARM64_REG_XZR = 7;
	public static final int ARM64_REG_B0 = 8;
	public static final int ARM64_REG_B1 = 9;
	public static final int ARM64_REG_B2 = 10;
	public static final int ARM64_REG_B3 = 11;
	public static final int ARM64_REG_B4 = 12;
	public static final int ARM64_REG_B5 = 13;
	public static final int ARM64_REG_B6 = 14;
	public static final int ARM64_REG_B7 = 15;
	public static final int ARM64_REG_B8 = 16;
	public static final int ARM64_REG_B9 = 17;
	public static final int ARM64_REG_B10 = 18;
	public static final int ARM64_REG_B11 = 19;
	public static final int ARM64_REG_B12 = 20;
	public static final int ARM64_REG_B13 = 21;
	public static final int ARM64_REG_B14 = 22;
	public static final int ARM64_REG_B15 = 23;
	public static final int ARM64_REG_B16 = 24;
	public static final int ARM64_REG_B17 = 25;
	public static final int ARM64_REG_B18 = 26;
	public static final int ARM64_REG_B19 = 27;
	public static final int ARM64_REG_B20 = 28;
	public static final int ARM64_REG_B21 = 29;
	public static final int ARM64_REG_B22 = 30;
	public static final int ARM64_REG_B23 = 31;
	public static final int ARM64_REG_B24 = 32;
	public static final int ARM64_REG_B25 = 33;
	public static final int ARM64_REG_B26 = 34;
	public static final int ARM64_REG_B27 = 35;
	public static final int ARM64_REG_B28 = 36;
	public static final int ARM64_REG_B29 = 37;
	public static final int ARM64_REG_B30 = 38;
	public static final int ARM64_REG_B31 = 39;
	public static final int ARM64_REG_D0 = 40;
	public static final int ARM64_REG_D1 = 41;
	public static final int ARM64_REG_D2 = 42;
	public static final int ARM64_REG_D3 = 43;
	public static final int ARM64_REG_D4 = 44;
	public static final int ARM64_REG_D5 = 45;
	public static final int ARM64_REG_D6 = 46;
	public static final int ARM64_REG_D7 = 47;
	public static final int ARM64_REG_D8 = 48;
	public static final int ARM64_REG_D9 = 49;
	public static final int ARM64_REG_D10 = 50;
	public static final int ARM64_REG_D11 = 51;
	public static final int ARM64_REG_D12 = 52;
	public static final int ARM64_REG_D13 = 53;
	public static final int ARM64_REG_D14 = 54;
	public static final int ARM64_REG_D15 = 55;
	public static final int ARM64_REG_D16 = 56;
	public static final int ARM64_REG_D17 = 57;
	public static final int ARM64_REG_D18 = 58;
	public static final int ARM64_REG_D19 = 59;
	public static final int ARM64_REG_D20 = 60;
	public static final int ARM64_REG_D21 = 61;
	public static final int ARM64_REG_D22 = 62;
	public static final int ARM64_REG_D23 = 63;
	public static final int ARM64_REG_D24 = 64;
	public static final int ARM64_REG_D25 = 65;
	public static final int ARM64_REG_D26 = 66;
	public static final int ARM64_REG_D27 = 67;
	public static final int ARM64_REG_D28 = 68;
	public static final int ARM64_REG_D29 = 69;
	public static final int ARM64_REG_D30 = 70;
	public static final int ARM64_REG_D31 = 71;
	public static final int ARM64_REG_H0 = 72;
	public static final int ARM64_REG_H1 = 73;
	public static final int ARM64_REG_H2 = 74;
	public static final int ARM64_REG_H3 = 75;
	public static final int ARM64_REG_H4 = 76;
	public static final int ARM64_REG_H5 = 77;
	public static final int ARM64_REG_H6 = 78;
	public static final int ARM64_REG_H7 = 79;
	public static final int ARM64_REG_H8 = 80;
	public static final int ARM64_REG_H9 = 81;
	public static final int ARM64_REG_H10 = 82;
	public static final int ARM64_REG_H11 = 83;
	public static final int ARM64_REG_H12 = 84;
	public static final int ARM64_REG_H13 = 85;
	public static final int ARM64_REG_H14 = 86;
	public static final int ARM64_REG_H15 = 87;
	public static final int ARM64_REG_H16 = 88;
	public static final int ARM64_REG_H17 = 89;
	public static final int ARM64_REG_H18 = 90;
	public static final int ARM64_REG_H19 = 91;
	public static final int ARM64_REG_H20 = 92;
	public static final int ARM64_REG_H21 = 93;
	public static final int ARM64_REG_H22 = 94;
	public static final int ARM64_REG_H23 = 95;
	public static final int ARM64_REG_H24 = 96;
	public static final int ARM64_REG_H25 = 97;
	public static final int ARM64_REG_H26 = 98;
	public static final int ARM64_REG_H27 = 99;
	public static final int ARM64_REG_H28 = 100;
	public static final int ARM64_REG_H29 = 101;
	public static final int ARM64_REG_H30 = 102;
	public static final int ARM64_REG_H31 = 103;
	public static final int ARM64_REG_Q0 = 104;
	public static final int ARM64_REG_Q1 = 105;
	public static final int ARM64_REG_Q2 = 106;
	public static final int ARM64_REG_Q3 = 107;
	public static final int ARM64_REG_Q4 = 108;
	public static final int ARM64_REG_Q5 = 109;
	public static final int ARM64_REG_Q6 = 110;
	public static final int ARM64_REG_Q7 = 111;
	public static final int ARM64_REG_Q8 = 112;
	public static final int ARM64_REG_Q9 = 113;
	public static final int ARM64_REG_Q10 = 114;
	public static final int ARM64_REG_Q11 = 115;
	public static final int ARM64_REG_Q12 = 116;
	public static final int ARM64_REG_Q13 = 117;
	public static final int ARM64_REG_Q14 = 118;
	public static final int ARM64_REG_Q15 = 119;
	public static final int ARM64_REG_Q16 = 120;
	public static final int ARM64_REG_Q17 = 121;
	public static final int ARM64_REG_Q18 = 122;
	public static final int ARM64_REG_Q19 = 123;
	public static final int ARM64_REG_Q20 = 124;
	public static final int ARM64_REG_Q21 = 125;
	public static final int ARM64_REG_Q22 = 126;
	public static final int ARM64_REG_Q23 = 127;
	public static final int ARM64_REG_Q24 = 128;
	public static final int ARM64_REG_Q25 = 129;
	public static final int ARM64_REG_Q26 = 130;
	public static final int ARM64_REG_Q27 = 131;
	public static final int ARM64_REG_Q28 = 132;
	public static final int ARM64_REG_Q29 = 133;
	public static final int ARM64_REG_Q30 = 134;
	public static final int ARM64_REG_Q31 = 135;
	public static final int ARM64_REG_S0 = 136;
	public static final int ARM64_REG_S1 = 137;
	public static final int ARM64_REG_S2 = 138;
	public static final int ARM64_REG_S3 = 139;
	public static final int ARM64_REG_S4 = 140;
	public static final int ARM64_REG_S5 = 141;
	public static final int ARM64_REG_S6 = 142;
	public static final int ARM64_REG_S7 = 143;
	public static final int ARM64_REG_S8 = 144;
	public static final int ARM64_REG_S9 = 145;
	public static final int ARM64_REG_S10 = 146;
	public static final int ARM64_REG_S11 = 147;
	public static final int ARM64_REG_S12 = 148;
	public static final int ARM64_REG_S13 = 149;
	public static final int ARM64_REG_S14 = 150;
	public static final int ARM64_REG_S15 = 151;
	public static final int ARM64_REG_S16 = 152;
	public static final int ARM64_REG_S17 = 153;
	public static final int ARM64_REG_S18 = 154;
	public static final int ARM64_REG_S19 = 155;
	public static final int ARM64_REG_S20 = 156;
	public static final int ARM64_REG_S21 = 157;
	public static final int ARM64_REG_S22 = 158;
	public static final int ARM64_REG_S23 = 159;
	public static final int ARM64_REG_S24 = 160;
	public static final int ARM64_REG_S25 = 161;
	public static final int ARM64_REG_S26 = 162;
	public static final int ARM64_REG_S27 = 163;
	public static final int ARM64_REG_S28 = 164;
	public static final int ARM64_REG_S29 = 165;
	public static final int ARM64_REG_S30 = 166;
	public static final int ARM64_REG_S31 = 167;
	public static final int ARM64_REG_W0 = 168;
	public static final int ARM64_REG_W1 = 169;
	public static final int ARM64_REG_W2 = 170;
	public static final int ARM64_REG_W3 = 171;
	public static final int ARM64_REG_W4 = 172;
	public static final int ARM64_REG_W5 = 173;
	public static final int ARM64_REG_W6 = 174;
	public static final int ARM64_REG_W7 = 175;
	public static final int ARM64_REG_W8 = 176;
	public static final int ARM64_REG_W9 = 177;
	public static final int ARM64_REG_W10 = 178;
	public static final int ARM64_REG_W11 = 179;
	public static final int ARM64_REG_W12 = 180;
	public static final int ARM64_REG_W13 = 181;
	public static final int ARM64_REG_W14 = 182;
	public static final int ARM64_REG_W15 = 183;
	public static final int ARM64_REG_W16 = 184;
	public static final int ARM64_REG_W17 = 185;
	public static final int ARM64_REG_W18 = 186;
	public static final int ARM64_REG_W19 = 187;
	public static final int ARM64_REG_W20 = 188;
	public static final int ARM64_REG_W21 = 189;
	public static final int ARM64_REG_W22 = 190;
	public static final int ARM64_REG_W23 = 191;
	public static final int ARM64_REG_W24 = 192;
	public static final int ARM64_REG_W25 = 193;
	public static final int ARM64_REG_W26 = 194;
	public static final int ARM64_REG_W27 = 195;
	public static final int ARM64_REG_W28 = 196;
	public static final int ARM64_REG_W29 = 197;
	public static final int ARM64_REG_W30 = 198;
	public static final int ARM64_REG_X0 = 199;
	public static final int ARM64_REG_X1 = 200;
	public static final int ARM64_REG_X2 = 201;
	public static final int ARM64_REG_X3 = 202;
	public static final int ARM64_REG_X4 = 203;
	public static final int ARM64_REG_X5 = 204;
	public static final int ARM64_REG_X6 = 205;
	public static final int ARM64_REG_X7 = 206;
	public static final int ARM64_REG_X8 = 207;
	public static final int ARM64_REG_X9 = 208;
	public static final int ARM64_REG_X10 = 209;
	public static final int ARM64_REG_X11 = 210;
	public static final int ARM64_REG_X12 = 211;
	public static final int ARM64_REG_X13 = 212;
	public static final int ARM64_REG_X14 = 213;
	public static final int ARM64_REG_X15 = 214;
	public static final int ARM64_REG_X16 = 215;
	public static final int ARM64_REG_X17 = 216;
	public static final int ARM64_REG_X18 = 217;
	public static final int ARM64_REG_X19 = 218;
	public static final int ARM64_REG_X20 = 219;
	public static final int ARM64_REG_X21 = 220;
	public static final int ARM64_REG_X22 = 221;
	public static final int ARM64_REG_X23 = 222;
	public static final int ARM64_REG_X24 = 223;
	public static final int ARM64_REG_X25 = 224;
	public static final int ARM64_REG_X26 = 225;
	public static final int ARM64_REG_X27 = 226;
	public static final int ARM64_REG_X28 = 227;
	public static final int ARM64_REG_V0 = 228;
	public static final int ARM64_REG_V1 = 229;
	public static final int ARM64_REG_V2 = 230;
	public static final int ARM64_REG_V3 = 231;
	public static final int ARM64_REG_V4 = 232;
	public static final int ARM64_REG_V5 = 233;
	public static final int ARM64_REG_V6 = 234;
	public static final int ARM64_REG_V7 = 235;
	public static final int ARM64_REG_V8 = 236;
	public static final int ARM64_REG_V9 = 237;
	public static final int ARM64_REG_V10 = 238;
	public static final int ARM64_REG_V11 = 239;
	public static final int ARM64_REG_V12 = 240;
	public static final int ARM64_REG_V13 = 241;
	public static final int ARM64_REG_V14 = 242;
	public static final int ARM64_REG_V15 = 243;
	public static final int ARM64_REG_V16 = 244;
	public static final int ARM64_REG_V17 = 245;
	public static final int ARM64_REG_V18 = 246;
	public static final int ARM64_REG_V19 = 247;
	public static final int ARM64_REG_V20 = 248;
	public static final int ARM64_REG_V21 = 249;
	public static final int ARM64_REG_V22 = 250;
	public static final int ARM64_REG_V23 = 251;
	public static final int ARM64_REG_V24 = 252;
	public static final int ARM64_REG_V25 = 253;
	public static final int ARM64_REG_V26 = 254;
	public static final int ARM64_REG_V27 = 255;
	public static final int ARM64_REG_V28 = 256;
	public static final int ARM64_REG_V29 = 257;
	public static final int ARM64_REG_V30 = 258;
	public static final int ARM64_REG_V31 = 259;
	public static final int ARM64_REG_ENDING = 260;

	// alias registers
	public static final int ARM64_REG_IP1 = ARM64_REG_X16;
	public static final int ARM64_REG_IP0 = ARM64_REG_X17;
	public static final int ARM64_REG_FP = ARM64_REG_X29;
	public static final int ARM64_REG_LR = ARM64_REG_X30;

	// ARM64 instruction

	public static final int ARM64_INS_INVALID = 0;
	public static final int ARM64_INS_ABS = 1;
	public static final int ARM64_INS_ADC = 2;
	public static final int ARM64_INS_ADDHN = 3;
	public static final int ARM64_INS_ADDHN2 = 4;
	public static final int ARM64_INS_ADDP = 5;
	public static final int ARM64_INS_ADD = 6;
	public static final int ARM64_INS_ADDV = 7;
	public static final int ARM64_INS_ADR = 8;
	public static final int ARM64_INS_ADRP = 9;
	public static final int ARM64_INS_AESD = 10;
	public static final int ARM64_INS_AESE = 11;
	public static final int ARM64_INS_AESIMC = 12;
	public static final int ARM64_INS_AESMC = 13;
	public static final int ARM64_INS_AND = 14;
	public static final int ARM64_INS_ASR = 15;
	public static final int ARM64_INS_B = 16;
	public static final int ARM64_INS_BFM = 17;
	public static final int ARM64_INS_BIC = 18;
	public static final int ARM64_INS_BIF = 19;
	public static final int ARM64_INS_BIT = 20;
	public static final int ARM64_INS_BL = 21;
	public static final int ARM64_INS_BLR = 22;
	public static final int ARM64_INS_BR = 23;
	public static final int ARM64_INS_BRK = 24;
	public static final int ARM64_INS_BSL = 25;
	public static final int ARM64_INS_CBNZ = 26;
	public static final int ARM64_INS_CBZ = 27;
	public static final int ARM64_INS_CCMN = 28;
	public static final int ARM64_INS_CCMP = 29;
	public static final int ARM64_INS_CLREX = 30;
	public static final int ARM64_INS_CLS = 31;
	public static final int ARM64_INS_CLZ = 32;
	public static final int ARM64_INS_CMEQ = 33;
	public static final int ARM64_INS_CMGE = 34;
	public static final int ARM64_INS_CMGT = 35;
	public static final int ARM64_INS_CMHI = 36;
	public static final int ARM64_INS_CMHS = 37;
	public static final int ARM64_INS_CMLE = 38;
	public static final int ARM64_INS_CMLT = 39;
	public static final int ARM64_INS_CMTST = 40;
	public static final int ARM64_INS_CNT = 41;
	public static final int ARM64_INS_MOV = 42;
	public static final int ARM64_INS_CRC32B = 43;
	public static final int ARM64_INS_CRC32CB = 44;
	public static final int ARM64_INS_CRC32CH = 45;
	public static final int ARM64_INS_CRC32CW = 46;
	public static final int ARM64_INS_CRC32CX = 47;
	public static final int ARM64_INS_CRC32H = 48;
	public static final int ARM64_INS_CRC32W = 49;
	public static final int ARM64_INS_CRC32X = 50;
	public static final int ARM64_INS_CSEL = 51;
	public static final int ARM64_INS_CSINC = 52;
	public static final int ARM64_INS_CSINV = 53;
	public static final int ARM64_INS_CSNEG = 54;
	public static final int ARM64_INS_DCPS1 = 55;
	public static final int ARM64_INS_DCPS2 = 56;
	public static final int ARM64_INS_DCPS3 = 57;
	public static final int ARM64_INS_DMB = 58;
	public static final int ARM64_INS_DRPS = 59;
	public static final int ARM64_INS_DSB = 60;
	public static final int ARM64_INS_DUP = 61;
	public static final int ARM64_INS_EON = 62;
	public static final int ARM64_INS_EOR = 63;
	public static final int ARM64_INS_ERET = 64;
	public static final int ARM64_INS_EXTR = 65;
	public static final int ARM64_INS_EXT = 66;
	public static final int ARM64_INS_FABD = 67;
	public static final int ARM64_INS_FABS = 68;
	public static final int ARM64_INS_FACGE = 69;
	public static final int ARM64_INS_FACGT = 70;
	public static final int ARM64_INS_FADD = 71;
	public static final int ARM64_INS_FADDP = 72;
	public static final int ARM64_INS_FCCMP = 73;
	public static final int ARM64_INS_FCCMPE = 74;
	public static final int ARM64_INS_FCMEQ = 75;
	public static final int ARM64_INS_FCMGE = 76;
	public static final int ARM64_INS_FCMGT = 77;
	public static final int ARM64_INS_FCMLE = 78;
	public static final int ARM64_INS_FCMLT = 79;
	public static final int ARM64_INS_FCMP = 80;
	public static final int ARM64_INS_FCMPE = 81;
	public static final int ARM64_INS_FCSEL = 82;
	public static final int ARM64_INS_FCVTAS = 83;
	public static final int ARM64_INS_FCVTAU = 84;
	public static final int ARM64_INS_FCVT = 85;
	public static final int ARM64_INS_FCVTL = 86;
	public static final int ARM64_INS_FCVTL2 = 87;
	public static final int ARM64_INS_FCVTMS = 88;
	public static final int ARM64_INS_FCVTMU = 89;
	public static final int ARM64_INS_FCVTNS = 90;
	public static final int ARM64_INS_FCVTNU = 91;
	public static final int ARM64_INS_FCVTN = 92;
	public static final int ARM64_INS_FCVTN2 = 93;
	public static final int ARM64_INS_FCVTPS = 94;
	public static final int ARM64_INS_FCVTPU = 95;
	public static final int ARM64_INS_FCVTXN = 96;
	public static final int ARM64_INS_FCVTXN2 = 97;
	public static final int ARM64_INS_FCVTZS = 98;
	public static final int ARM64_INS_FCVTZU = 99;
	public static final int ARM64_INS_FDIV = 100;
	public static final int ARM64_INS_FMADD = 101;
	public static final int ARM64_INS_FMAX = 102;
	public static final int ARM64_INS_FMAXNM = 103;
	public static final int ARM64_INS_FMAXNMP = 104;
	public static final int ARM64_INS_FMAXNMV = 105;
	public static final int ARM64_INS_FMAXP = 106;
	public static final int ARM64_INS_FMAXV = 107;
	public static final int ARM64_INS_FMIN = 108;
	public static final int ARM64_INS_FMINNM = 109;
	public static final int ARM64_INS_FMINNMP = 110;
	public static final int ARM64_INS_FMINNMV = 111;
	public static final int ARM64_INS_FMINP = 112;
	public static final int ARM64_INS_FMINV = 113;
	public static final int ARM64_INS_FMLA = 114;
	public static final int ARM64_INS_FMLS = 115;
	public static final int ARM64_INS_FMOV = 116;
	public static final int ARM64_INS_FMSUB = 117;
	public static final int ARM64_INS_FMUL = 118;
	public static final int ARM64_INS_FMULX = 119;
	public static final int ARM64_INS_FNEG = 120;
	public static final int ARM64_INS_FNMADD = 121;
	public static final int ARM64_INS_FNMSUB = 122;
	public static final int ARM64_INS_FNMUL = 123;
	public static final int ARM64_INS_FRECPE = 124;
	public static final int ARM64_INS_FRECPS = 125;
	public static final int ARM64_INS_FRECPX = 126;
	public static final int ARM64_INS_FRINTA = 127;
	public static final int ARM64_INS_FRINTI = 128;
	public static final int ARM64_INS_FRINTM = 129;
	public static final int ARM64_INS_FRINTN = 130;
	public static final int ARM64_INS_FRINTP = 131;
	public static final int ARM64_INS_FRINTX = 132;
	public static final int ARM64_INS_FRINTZ = 133;
	public static final int ARM64_INS_FRSQRTE = 134;
	public static final int ARM64_INS_FRSQRTS = 135;
	public static final int ARM64_INS_FSQRT = 136;
	public static final int ARM64_INS_FSUB = 137;
	public static final int ARM64_INS_HINT = 138;
	public static final int ARM64_INS_HLT = 139;
	public static final int ARM64_INS_HVC = 140;
	public static final int ARM64_INS_INS = 141;
	public static final int ARM64_INS_ISB = 142;
	public static final int ARM64_INS_LD1 = 143;
	public static final int ARM64_INS_LD1R = 144;
	public static final int ARM64_INS_LD2R = 145;
	public static final int ARM64_INS_LD2 = 146;
	public static final int ARM64_INS_LD3R = 147;
	public static final int ARM64_INS_LD3 = 148;
	public static final int ARM64_INS_LD4 = 149;
	public static final int ARM64_INS_LD4R = 150;
	public static final int ARM64_INS_LDARB = 151;
	public static final int ARM64_INS_LDARH = 152;
	public static final int ARM64_INS_LDAR = 153;
	public static final int ARM64_INS_LDAXP = 154;
	public static final int ARM64_INS_LDAXRB = 155;
	public static final int ARM64_INS_LDAXRH = 156;
	public static final int ARM64_INS_LDAXR = 157;
	public static final int ARM64_INS_LDNP = 158;
	public static final int ARM64_INS_LDP = 159;
	public static final int ARM64_INS_LDPSW = 160;
	public static final int ARM64_INS_LDRB = 161;
	public static final int ARM64_INS_LDR = 162;
	public static final int ARM64_INS_LDRH = 163;
	public static final int ARM64_INS_LDRSB = 164;
	public static final int ARM64_INS_LDRSH = 165;
	public static final int ARM64_INS_LDRSW = 166;
	public static final int ARM64_INS_LDTRB = 167;
	public static final int ARM64_INS_LDTRH = 168;
	public static final int ARM64_INS_LDTRSB = 169;
	public static final int ARM64_INS_LDTRSH = 170;
	public static final int ARM64_INS_LDTRSW = 171;
	public static final int ARM64_INS_LDTR = 172;
	public static final int ARM64_INS_LDURB = 173;
	public static final int ARM64_INS_LDUR = 174;
	public static final int ARM64_INS_LDURH = 175;
	public static final int ARM64_INS_LDURSB = 176;
	public static final int ARM64_INS_LDURSH = 177;
	public static final int ARM64_INS_LDURSW = 178;
	public static final int ARM64_INS_LDXP = 179;
	public static final int ARM64_INS_LDXRB = 180;
	public static final int ARM64_INS_LDXRH = 181;
	public static final int ARM64_INS_LDXR = 182;
	public static final int ARM64_INS_LSL = 183;
	public static final int ARM64_INS_LSR = 184;
	public static final int ARM64_INS_MADD = 185;
	public static final int ARM64_INS_MLA = 186;
	public static final int ARM64_INS_MLS = 187;
	public static final int ARM64_INS_MOVI = 188;
	public static final int ARM64_INS_MOVK = 189;
	public static final int ARM64_INS_MOVN = 190;
	public static final int ARM64_INS_MOVZ = 191;
	public static final int ARM64_INS_MRS = 192;
	public static final int ARM64_INS_MSR = 193;
	public static final int ARM64_INS_MSUB = 194;
	public static final int ARM64_INS_MUL = 195;
	public static final int ARM64_INS_MVNI = 196;
	public static final int ARM64_INS_NEG = 197;
	public static final int ARM64_INS_NOT = 198;
	public static final int ARM64_INS_ORN = 199;
	public static final int ARM64_INS_ORR = 200;
	public static final int ARM64_INS_PMULL2 = 201;
	public static final int ARM64_INS_PMULL = 202;
	public static final int ARM64_INS_PMUL = 203;
	public static final int ARM64_INS_PRFM = 204;
	public static final int ARM64_INS_PRFUM = 205;
	public static final int ARM64_INS_RADDHN = 206;
	public static final int ARM64_INS_RADDHN2 = 207;
	public static final int ARM64_INS_RBIT = 208;
	public static final int ARM64_INS_RET = 209;
	public static final int ARM64_INS_REV16 = 210;
	public static final int ARM64_INS_REV32 = 211;
	public static final int ARM64_INS_REV64 = 212;
	public static final int ARM64_INS_REV = 213;
	public static final int ARM64_INS_ROR = 214;
	public static final int ARM64_INS_RSHRN2 = 215;
	public static final int ARM64_INS_RSHRN = 216;
	public static final int ARM64_INS_RSUBHN = 217;
	public static final int ARM64_INS_RSUBHN2 = 218;
	public static final int ARM64_INS_SABAL2 = 219;
	public static final int ARM64_INS_SABAL = 220;
	public static final int ARM64_INS_SABA = 221;
	public static final int ARM64_INS_SABDL2 = 222;
	public static final int ARM64_INS_SABDL = 223;
	public static final int ARM64_INS_SABD = 224;
	public static final int ARM64_INS_SADALP = 225;
	public static final int ARM64_INS_SADDLP = 226;
	public static final int ARM64_INS_SADDLV = 227;
	public static final int ARM64_INS_SADDL2 = 228;
	public static final int ARM64_INS_SADDL = 229;
	public static final int ARM64_INS_SADDW2 = 230;
	public static final int ARM64_INS_SADDW = 231;
	public static final int ARM64_INS_SBC = 232;
	public static final int ARM64_INS_SBFM = 233;
	public static final int ARM64_INS_SCVTF = 234;
	public static final int ARM64_INS_SDIV = 235;
	public static final int ARM64_INS_SHA1C = 236;
	public static final int ARM64_INS_SHA1H = 237;
	public static final int ARM64_INS_SHA1M = 238;
	public static final int ARM64_INS_SHA1P = 239;
	public static final int ARM64_INS_SHA1SU0 = 240;
	public static final int ARM64_INS_SHA1SU1 = 241;
	public static final int ARM64_INS_SHA256H2 = 242;
	public static final int ARM64_INS_SHA256H = 243;
	public static final int ARM64_INS_SHA256SU0 = 244;
	public static final int ARM64_INS_SHA256SU1 = 245;
	public static final int ARM64_INS_SHADD = 246;
	public static final int ARM64_INS_SHLL2 = 247;
	public static final int ARM64_INS_SHLL = 248;
	public static final int ARM64_INS_SHL = 249;
	public static final int ARM64_INS_SHRN2 = 250;
	public static final int ARM64_INS_SHRN = 251;
	public static final int ARM64_INS_SHSUB = 252;
	public static final int ARM64_INS_SLI = 253;
	public static final int ARM64_INS_SMADDL = 254;
	public static final int ARM64_INS_SMAXP = 255;
	public static final int ARM64_INS_SMAXV = 256;
	public static final int ARM64_INS_SMAX = 257;
	public static final int ARM64_INS_SMC = 258;
	public static final int ARM64_INS_SMINP = 259;
	public static final int ARM64_INS_SMINV = 260;
	public static final int ARM64_INS_SMIN = 261;
	public static final int ARM64_INS_SMLAL2 = 262;
	public static final int ARM64_INS_SMLAL = 263;
	public static final int ARM64_INS_SMLSL2 = 264;
	public static final int ARM64_INS_SMLSL = 265;
	public static final int ARM64_INS_SMOV = 266;
	public static final int ARM64_INS_SMSUBL = 267;
	public static final int ARM64_INS_SMULH = 268;
	public static final int ARM64_INS_SMULL2 = 269;
	public static final int ARM64_INS_SMULL = 270;
	public static final int ARM64_INS_SQABS = 271;
	public static final int ARM64_INS_SQADD = 272;
	public static final int ARM64_INS_SQDMLAL = 273;
	public static final int ARM64_INS_SQDMLAL2 = 274;
	public static final int ARM64_INS_SQDMLSL = 275;
	public static final int ARM64_INS_SQDMLSL2 = 276;
	public static final int ARM64_INS_SQDMULH = 277;
	public static final int ARM64_INS_SQDMULL = 278;
	public static final int ARM64_INS_SQDMULL2 = 279;
	public static final int ARM64_INS_SQNEG = 280;
	public static final int ARM64_INS_SQRDMULH = 281;
	public static final int ARM64_INS_SQRSHL = 282;
	public static final int ARM64_INS_SQRSHRN = 283;
	public static final int ARM64_INS_SQRSHRN2 = 284;
	public static final int ARM64_INS_SQRSHRUN = 285;
	public static final int ARM64_INS_SQRSHRUN2 = 286;
	public static final int ARM64_INS_SQSHLU = 287;
	public static final int ARM64_INS_SQSHL = 288;
	public static final int ARM64_INS_SQSHRN = 289;
	public static final int ARM64_INS_SQSHRN2 = 290;
	public static final int ARM64_INS_SQSHRUN = 291;
	public static final int ARM64_INS_SQSHRUN2 = 292;
	public static final int ARM64_INS_SQSUB = 293;
	public static final int ARM64_INS_SQXTN2 = 294;
	public static final int ARM64_INS_SQXTN = 295;
	public static final int ARM64_INS_SQXTUN2 = 296;
	public static final int ARM64_INS_SQXTUN = 297;
	public static final int ARM64_INS_SRHADD = 298;
	public static final int ARM64_INS_SRI = 299;
	public static final int ARM64_INS_SRSHL = 300;
	public static final int ARM64_INS_SRSHR = 301;
	public static final int ARM64_INS_SRSRA = 302;
	public static final int ARM64_INS_SSHLL2 = 303;
	public static final int ARM64_INS_SSHLL = 304;
	public static final int ARM64_INS_SSHL = 305;
	public static final int ARM64_INS_SSHR = 306;
	public static final int ARM64_INS_SSRA = 307;
	public static final int ARM64_INS_SSUBL2 = 308;
	public static final int ARM64_INS_SSUBL = 309;
	public static final int ARM64_INS_SSUBW2 = 310;
	public static final int ARM64_INS_SSUBW = 311;
	public static final int ARM64_INS_ST1 = 312;
	public static final int ARM64_INS_ST2 = 313;
	public static final int ARM64_INS_ST3 = 314;
	public static final int ARM64_INS_ST4 = 315;
	public static final int ARM64_INS_STLRB = 316;
	public static final int ARM64_INS_STLRH = 317;
	public static final int ARM64_INS_STLR = 318;
	public static final int ARM64_INS_STLXP = 319;
	public static final int ARM64_INS_STLXRB = 320;
	public static final int ARM64_INS_STLXRH = 321;
	public static final int ARM64_INS_STLXR = 322;
	public static final int ARM64_INS_STNP = 323;
	public static final int ARM64_INS_STP = 324;
	public static final int ARM64_INS_STRB = 325;
	public static final int ARM64_INS_STR = 326;
	public static final int ARM64_INS_STRH = 327;
	public static final int ARM64_INS_STTRB = 328;
	public static final int ARM64_INS_STTRH = 329;
	public static final int ARM64_INS_STTR = 330;
	public static final int ARM64_INS_STURB = 331;
	public static final int ARM64_INS_STUR = 332;
	public static final int ARM64_INS_STURH = 333;
	public static final int ARM64_INS_STXP = 334;
	public static final int ARM64_INS_STXRB = 335;
	public static final int ARM64_INS_STXRH = 336;
	public static final int ARM64_INS_STXR = 337;
	public static final int ARM64_INS_SUBHN = 338;
	public static final int ARM64_INS_SUBHN2 = 339;
	public static final int ARM64_INS_SUB = 340;
	public static final int ARM64_INS_SUQADD = 341;
	public static final int ARM64_INS_SVC = 342;
	public static final int ARM64_INS_SYSL = 343;
	public static final int ARM64_INS_SYS = 344;
	public static final int ARM64_INS_TBL = 345;
	public static final int ARM64_INS_TBNZ = 346;
	public static final int ARM64_INS_TBX = 347;
	public static final int ARM64_INS_TBZ = 348;
	public static final int ARM64_INS_TRN1 = 349;
	public static final int ARM64_INS_TRN2 = 350;
	public static final int ARM64_INS_UABAL2 = 351;
	public static final int ARM64_INS_UABAL = 352;
	public static final int ARM64_INS_UABA = 353;
	public static final int ARM64_INS_UABDL2 = 354;
	public static final int ARM64_INS_UABDL = 355;
	public static final int ARM64_INS_UABD = 356;
	public static final int ARM64_INS_UADALP = 357;
	public static final int ARM64_INS_UADDLP = 358;
	public static final int ARM64_INS_UADDLV = 359;
	public static final int ARM64_INS_UADDL2 = 360;
	public static final int ARM64_INS_UADDL = 361;
	public static final int ARM64_INS_UADDW2 = 362;
	public static final int ARM64_INS_UADDW = 363;
	public static final int ARM64_INS_UBFM = 364;
	public static final int ARM64_INS_UCVTF = 365;
	public static final int ARM64_INS_UDIV = 366;
	public static final int ARM64_INS_UHADD = 367;
	public static final int ARM64_INS_UHSUB = 368;
	public static final int ARM64_INS_UMADDL = 369;
	public static final int ARM64_INS_UMAXP = 370;
	public static final int ARM64_INS_UMAXV = 371;
	public static final int ARM64_INS_UMAX = 372;
	public static final int ARM64_INS_UMINP = 373;
	public static final int ARM64_INS_UMINV = 374;
	public static final int ARM64_INS_UMIN = 375;
	public static final int ARM64_INS_UMLAL2 = 376;
	public static final int ARM64_INS_UMLAL = 377;
	public static final int ARM64_INS_UMLSL2 = 378;
	public static final int ARM64_INS_UMLSL = 379;
	public static final int ARM64_INS_UMOV = 380;
	public static final int ARM64_INS_UMSUBL = 381;
	public static final int ARM64_INS_UMULH = 382;
	public static final int ARM64_INS_UMULL2 = 383;
	public static final int ARM64_INS_UMULL = 384;
	public static final int ARM64_INS_UQADD = 385;
	public static final int ARM64_INS_UQRSHL = 386;
	public static final int ARM64_INS_UQRSHRN = 387;
	public static final int ARM64_INS_UQRSHRN2 = 388;
	public static final int ARM64_INS_UQSHL = 389;
	public static final int ARM64_INS_UQSHRN = 390;
	public static final int ARM64_INS_UQSHRN2 = 391;
	public static final int ARM64_INS_UQSUB = 392;
	public static final int ARM64_INS_UQXTN2 = 393;
	public static final int ARM64_INS_UQXTN = 394;
	public static final int ARM64_INS_URECPE = 395;
	public static final int ARM64_INS_URHADD = 396;
	public static final int ARM64_INS_URSHL = 397;
	public static final int ARM64_INS_URSHR = 398;
	public static final int ARM64_INS_URSQRTE = 399;
	public static final int ARM64_INS_URSRA = 400;
	public static final int ARM64_INS_USHLL2 = 401;
	public static final int ARM64_INS_USHLL = 402;
	public static final int ARM64_INS_USHL = 403;
	public static final int ARM64_INS_USHR = 404;
	public static final int ARM64_INS_USQADD = 405;
	public static final int ARM64_INS_USRA = 406;
	public static final int ARM64_INS_USUBL2 = 407;
	public static final int ARM64_INS_USUBL = 408;
	public static final int ARM64_INS_USUBW2 = 409;
	public static final int ARM64_INS_USUBW = 410;
	public static final int ARM64_INS_UZP1 = 411;
	public static final int ARM64_INS_UZP2 = 412;
	public static final int ARM64_INS_XTN2 = 413;
	public static final int ARM64_INS_XTN = 414;
	public static final int ARM64_INS_ZIP1 = 415;
	public static final int ARM64_INS_ZIP2 = 416;
	public static final int ARM64_INS_MNEG = 417;
	public static final int ARM64_INS_UMNEGL = 418;
	public static final int ARM64_INS_SMNEGL = 419;
	public static final int ARM64_INS_NOP = 420;
	public static final int ARM64_INS_YIELD = 421;
	public static final int ARM64_INS_WFE = 422;
	public static final int ARM64_INS_WFI = 423;
	public static final int ARM64_INS_SEV = 424;
	public static final int ARM64_INS_SEVL = 425;
	public static final int ARM64_INS_NGC = 426;
	public static final int ARM64_INS_SBFIZ = 427;
	public static final int ARM64_INS_UBFIZ = 428;
	public static final int ARM64_INS_SBFX = 429;
	public static final int ARM64_INS_UBFX = 430;
	public static final int ARM64_INS_BFI = 431;
	public static final int ARM64_INS_BFXIL = 432;
	public static final int ARM64_INS_CMN = 433;
	public static final int ARM64_INS_MVN = 434;
	public static final int ARM64_INS_TST = 435;
	public static final int ARM64_INS_CSET = 436;
	public static final int ARM64_INS_CINC = 437;
	public static final int ARM64_INS_CSETM = 438;
	public static final int ARM64_INS_CINV = 439;
	public static final int ARM64_INS_CNEG = 440;
	public static final int ARM64_INS_SXTB = 441;
	public static final int ARM64_INS_SXTH = 442;
	public static final int ARM64_INS_SXTW = 443;
	public static final int ARM64_INS_CMP = 444;
	public static final int ARM64_INS_UXTB = 445;
	public static final int ARM64_INS_UXTH = 446;
	public static final int ARM64_INS_UXTW = 447;
	public static final int ARM64_INS_IC = 448;
	public static final int ARM64_INS_DC = 449;
	public static final int ARM64_INS_AT = 450;
	public static final int ARM64_INS_TLBI = 451;
	public static final int ARM64_INS_ENDING = 452;

	// Group of ARM64 instructions

	public static final int ARM64_GRP_INVALID = 0;

	// Generic groups
	public static final int ARM64_GRP_JUMP = 1;

	// Architecture-specific groups
	public static final int ARM64_GRP_CRYPTO = 128;
	public static final int ARM64_GRP_FPARMV8 = 129;
	public static final int ARM64_GRP_NEON = 130;
	public static final int ARM64_GRP_CRC = 131;
	public static final int ARM64_GRP_ENDING = 132;
}