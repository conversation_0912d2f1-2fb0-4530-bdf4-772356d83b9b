# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_BIG_ENDIAN, None
0x78,0x1c,0x9f,0x1b = fadd.w $w28, $w19, $w28
0x78,0x3d,0x13,0x5b = fadd.d $w13, $w2, $w29
0x78,0x19,0x5b,0x9a = fcaf.w $w14, $w11, $w25
0x78,0x33,0x08,0x5a = fcaf.d $w1, $w1, $w19
0x78,0x90,0xb8,0x5a = fceq.w $w1, $w23, $w16
0x78,0xb0,0x40,0x1a = fceq.d $w0, $w8, $w16
0x79,0x98,0x4c,0x1a = fcle.w $w16, $w9, $w24
0x79,0xa1,0x76,0xda = fcle.d $w27, $w14, $w1
0x79,0x08,0x47,0x1a = fclt.w $w28, $w8, $w8
0x79,0x2b,0xcf,0x9a = fclt.d $w30, $w25, $w11
0x78,0xd7,0x90,0x9c = fcne.w $w2, $w18, $w23
0x78,0xef,0xa3,0x9c = fcne.d $w14, $w20, $w15
0x78,0x59,0x92,0x9c = fcor.w $w10, $w18, $w25
0x78,0x6b,0xcc,0x5c = fcor.d $w17, $w25, $w11
0x78,0xd5,0x13,0x9a = fcueq.w $w14, $w2, $w21
0x78,0xe7,0x1f,0x5a = fcueq.d $w29, $w3, $w7
0x79,0xc3,0x2c,0x5a = fcule.w $w17, $w5, $w3
0x79,0xfe,0x0f,0xda = fcule.d $w31, $w1, $w30
0x79,0x49,0xc9,0x9a = fcult.w $w6, $w25, $w9
0x79,0x71,0x46,0xda = fcult.d $w27, $w8, $w17
0x78,0x48,0xa1,0x1a = fcun.w $w4, $w20, $w8
0x78,0x63,0x5f,0x5a = fcun.d $w29, $w11, $w3
0x78,0x93,0x93,0x5c = fcune.w $w13, $w18, $w19
0x78,0xb5,0xd4,0x1c = fcune.d $w16, $w26, $w21
0x78,0xc2,0xc3,0x5b = fdiv.w $w13, $w24, $w2
0x78,0xf9,0x24,0xdb = fdiv.d $w19, $w4, $w25
0x7a,0x10,0x02,0x1b = fexdo.h $w8, $w0, $w16
0x7a,0x3b,0x68,0x1b = fexdo.w $w0, $w13, $w27
0x79,0xc3,0x04,0x5b = fexp2.w $w17, $w0, $w3
0x79,0xea,0x05,0x9b = fexp2.d $w22, $w0, $w10
0x79,0x17,0x37,0x5b = fmadd.w $w29, $w6, $w23
0x79,0x35,0xe2,0xdb = fmadd.d $w11, $w28, $w21
0x7b,0x8d,0xb8,0x1b = fmax.w $w0, $w23, $w13
0x7b,0xa8,0x96,0x9b = fmax.d $w26, $w18, $w8
0x7b,0xca,0x82,0x9b = fmax_a.w $w10, $w16, $w10
0x7b,0xf6,0x4f,0x9b = fmax_a.d $w30, $w9, $w22
0x7b,0x1e,0x0e,0x1b = fmin.w $w24, $w1, $w30
0x7b,0x2a,0xde,0xdb = fmin.d $w27, $w27, $w10
0x7b,0x54,0xea,0x9b = fmin_a.w $w10, $w29, $w20
0x7b,0x78,0xf3,0x5b = fmin_a.d $w13, $w30, $w24
0x79,0x40,0xcc,0x5b = fmsub.w $w17, $w25, $w0
0x79,0x70,0x92,0x1b = fmsub.d $w8, $w18, $w16
0x78,0x8f,0x78,0xdb = fmul.w $w3, $w15, $w15
0x78,0xaa,0xf2,0x5b = fmul.d $w9, $w30, $w10
0x7a,0x0a,0x2e,0x5a = fsaf.w $w25, $w5, $w10
0x7a,0x3d,0x1e,0x5a = fsaf.d $w25, $w3, $w29
0x7a,0x8d,0x8a,0xda = fseq.w $w11, $w17, $w13
0x7a,0xbf,0x07,0x5a = fseq.d $w29, $w0, $w31
0x7b,0x9f,0xff,0x9a = fsle.w $w30, $w31, $w31
0x7b,0xb8,0xbc,0x9a = fsle.d $w18, $w23, $w24
0x7b,0x06,0x2b,0x1a = fslt.w $w12, $w5, $w6
0x7b,0x35,0xd4,0x1a = fslt.d $w16, $w26, $w21
0x7a,0xcc,0x0f,0x9c = fsne.w $w30, $w1, $w12
0x7a,0xf7,0x6b,0x9c = fsne.d $w14, $w13, $w23
0x7a,0x5b,0x6e,0xdc = fsor.w $w27, $w13, $w27
0x7a,0x6b,0xc3,0x1c = fsor.d $w12, $w24, $w11
0x78,0x41,0xd7,0xdb = fsub.w $w31, $w26, $w1
0x78,0x7b,0x8c,0xdb = fsub.d $w19, $w17, $w27
0x7a,0xd9,0xc4,0x1a = fsueq.w $w16, $w24, $w25
0x7a,0xee,0x74,0x9a = fsueq.d $w18, $w14, $w14
0x7b,0xcd,0xf5,0xda = fsule.w $w23, $w30, $w13
0x7b,0xfa,0x58,0x9a = fsule.d $w2, $w11, $w26
0x7b,0x56,0xd2,0xda = fsult.w $w11, $w26, $w22
0x7b,0x7e,0xb9,0x9a = fsult.d $w6, $w23, $w30
0x7a,0x5c,0x90,0xda = fsun.w $w3, $w18, $w28
0x7a,0x73,0x5c,0x9a = fsun.d $w18, $w11, $w19
0x7a,0x82,0xfc,0x1c = fsune.w $w16, $w31, $w2
0x7a,0xb1,0xd0,0xdc = fsune.d $w3, $w26, $w17
0x7a,0x98,0x24,0x1b = ftq.h $w16, $w4, $w24
0x7a,0xb9,0x29,0x5b = ftq.w $w5, $w5, $w25
0x79,0x4a,0xa4,0x1c = madd_q.h $w16, $w20, $w10
0x79,0x69,0x17,0x1c = madd_q.w $w28, $w2, $w9
0x7b,0x49,0x92,0x1c = maddr_q.h $w8, $w18, $w9
0x7b,0x70,0x67,0x5c = maddr_q.w $w29, $w12, $w16
0x79,0x8a,0xd6,0x1c = msub_q.h $w24, $w26, $w10
0x79,0xbc,0xf3,0x5c = msub_q.w $w13, $w30, $w28
0x7b,0x8b,0xab,0x1c = msubr_q.h $w12, $w21, $w11
0x7b,0xb4,0x70,0x5c = msubr_q.w $w1, $w14, $w20
0x79,0x1e,0x81,0x9c = mul_q.h $w6, $w16, $w30
0x79,0x24,0x0c,0x1c = mul_q.w $w16, $w1, $w4
0x7b,0x13,0xa1,0x9c = mulr_q.h $w6, $w20, $w19
0x7b,0x34,0x0e,0xdc = mulr_q.w $w27, $w1, $w20
