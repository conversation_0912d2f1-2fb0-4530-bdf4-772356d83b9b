# CS_ARCH_MIPS, CS_MODE_MIPS32+CS_MODE_BIG_ENDIAN, None
0x78,0x30,0xe8,0x80 = andi.b $w2, $w29, 48
0x78,0x7e,0xb1,0x81 = bmnzi.b $w6, $w22, 126
0x79,0x58,0x0e,0xc1 = bmzi.b $w27, $w1, 88
0x7a,0xbd,0x1f,0x41 = bseli.b $w29, $w3, 189
0x7a,0x38,0x88,0x40 = nori.b $w1, $w17, 56
0x79,0x87,0xa6,0x80 = ori.b $w26, $w20, 135
0x78,0x69,0xf4,0xc2 = shf.b $w19, $w30, 105
0x79,0x4c,0x44,0x42 = shf.h $w17, $w8, 76
0x7a,0x5d,0x1b,0x82 = shf.w $w14, $w3, 93
0x7b,0x14,0x54,0x00 = xori.b $w16, $w10, 20
