# CS_ARCH_ARM64, 0, None
0x20,0x84,0x22,0x0e = add v0.8b, v1.8b, v2.8b
0x20,0x84,0x22,0x4e = add v0.16b, v1.16b, v2.16b
0x20,0x84,0x62,0x0e = add v0.4h, v1.4h, v2.4h
0x20,0x84,0x62,0x4e = add v0.8h, v1.8h, v2.8h
0x20,0x84,0xa2,0x0e = add v0.2s, v1.2s, v2.2s
0x20,0x84,0xa2,0x4e = add v0.4s, v1.4s, v2.4s
0x20,0x84,0xe2,0x4e = add v0.2d, v1.2d, v2.2d
0x20,0x84,0x22,0x2e = sub v0.8b, v1.8b, v2.8b
0x20,0x84,0x22,0x6e = sub v0.16b, v1.16b, v2.16b
0x20,0x84,0x62,0x2e = sub v0.4h, v1.4h, v2.4h
0x20,0x84,0x62,0x6e = sub v0.8h, v1.8h, v2.8h
0x20,0x84,0xa2,0x2e = sub v0.2s, v1.2s, v2.2s
0x20,0x84,0xa2,0x6e = sub v0.4s, v1.4s, v2.4s
0x20,0x84,0xe2,0x6e = sub v0.2d, v1.2d, v2.2d
0x20,0xd4,0x22,0x0e = fadd v0.2s, v1.2s, v2.2s
0x20,0xd4,0x22,0x4e = fadd v0.4s, v1.4s, v2.4s
0x20,0xd4,0x62,0x4e = fadd v0.2d, v1.2d, v2.2d
0x20,0xd4,0xa2,0x0e = fsub v0.2s, v1.2s, v2.2s
0x20,0xd4,0xa2,0x4e = fsub v0.4s, v1.4s, v2.4s
0x20,0xd4,0xe2,0x4e = fsub v0.2d, v1.2d, v2.2d
